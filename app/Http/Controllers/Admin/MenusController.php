<?php

namespace App\Http\Controllers\Admin;

use App\Models\Menu;
use Aimsoft\UserManagement\Models\Permission;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Route;
use App\Http\Requests\SaveMenuRequest;

class MenusController extends Controller
{
    protected $loggedInUser;

    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            $this->loggedInUser = Auth()->user()->user_name;

            return $next($request);
        });
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        Gate::authorize('access-menus');

        $permissions = Permission::all();
        $menus = Menu::with('parent')->get();
        $iconsFontAwesome = config('icons.fontawesome');
        $iconsPng = config('icons.png');
        $iconsList = array_merge($iconsFontAwesome,$iconsPng);

        $routeCollection = Route::getRoutes();

        $routeNames = [];

        array_push($routeNames, '#');

        foreach ($routeCollection as $route) {
            if (!is_null($route->getName())) {
                $routeNames[] = $route->getName();
            }
        }

        if ($request->ajax()) {
            $data = DB::table('menus')
                ->select('menus.id', 'menus.name as menu', 'menus.icon', 'menus.route_name', 'menus.position', 'permissions.name as permission', 'parent.name as parent')
                ->leftJoin('permissions', function ($join) {
                    $join->on('menus.permission_id', '=', 'permissions.id');
                })
                ->leftJoin('menus parent', function ($join) {
                    $join->on('menus.parent_id', '=', 'parent.id');
                });

            return DataTables::of($data)
                ->editColumn('parent', function ($data) {
                    return $data->parent !== null ? $data->parent : '<span class="label label-pill label-warning">No Parent</span>';
                })
                ->editColumn('permission', function ($data) {
                    return $data->permission !== null ? $data->permission : '<span class="label label-pill label-warning">Permission based on children</span>';
                })
                ->editColumn('icon', function ($data) {
                    $iconText = '';
                    
                    if(in_array($data->icon, config('icons.png'))) {
                        $iconText = '<span><img style="width:16px; height:16px;" src="'. '/images' . '/' . $data->icon . '.png' .'"/> '. $data->icon .'</span>';
                    }

                    if(in_array($data->icon, config('icons.fontawesome'))) {
                        $iconText = '<span><i class="' . $data->icon . '"></i> ' . $data->icon . '</span>';
                    }
                    return $iconText;
                })
                ->addColumn('action', function ($data) {
                    $btn = '';
                    if (Gate::check('edit-menu')) {
                        $btn .= '<button id="edit-menu" data-id="' . $data->id . '" class="btn btn-xs btn-primary"><i class="fa fa-pencil-square-o"></i> Edit</button>';
                        $btn .= '&nbsp;&nbsp;';
                    }
                    if (Gate::check('delete-menu')) {
                        $btn .= '<button id="delete-menu" data-id="' . $data->id . '" class="btn btn-xs btn-danger"><i class="fa fa-minus-square-o"></i> Delete</button>';
                    }

                    return $btn;
                })
                ->rawColumns(['action', 'parent', 'permission', 'icon'])
                ->make(true);
        }
        return view(
            'admin.aimsmenus',
            [
                'permissions' => $permissions,
                'menus' => $menus,
                'routeNames' => $routeNames,
                'iconsList' => $iconsList
            ]
        );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(SaveMenuRequest $request)
    {
        Gate::authorize('create-menu');

        $status = 0;

        $data = $request->validated();

        $formattedMenuName = $this->formatMenuName($data['menu_name']);

        $nextId = Menu::max('id') + 1;

        $menuIcon = trim($request->menu_icon) == null ? config('app.default_menu_icon') : trim($request->menu_icon);

        $menu_type = ($request->menu_type) ? 'R' : 'M';

        $menu = new Menu();
        $menu->id = $nextId;
        $menu->name = $formattedMenuName;
        $menu->icon = $menuIcon;
        $menu->route_name = trim($data['menu_route']);
        $menu->permission_id = $data['menu_permission'];
        $menu->parent_id = $request->menu_parent;
        $menu->position = $data['position'];
        $menu->menu_type = $menu_type;
        $menu->created_by = $this->loggedInUser;
        $menu->updated_by = $this->loggedInUser;
        $menu->save();

        $status = 1;
        
        return array('status' => $status);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Menu $menu)
    {
        Gate::authorize('edit-menu');

        $formattedMenuName = $this->formatRetrievedMenuName($menu->name);

        $menu->name = $formattedMenuName;

        return $menu;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(SaveMenuRequest $request, Menu $menu)
    {
        Gate::authorize('edit-menu');

        $status = 0;

        $data = $request->validated();

        $formattedMenuName = $this->formatMenuName($data['menu_name']);

        $menuIcon = trim($request->menu_icon) == null ? config('app.default_menu_icon') : trim($request->menu_icon);

        $menu_type = ($request->menu_type) ? 'R' : 'M';

        $menu->name = $formattedMenuName;
        $menu->icon = $menuIcon;
        $menu->route_name = trim($data['menu_route']);
        $menu->permission_id = $data['menu_permission'];
        $menu->parent_id = $request->menu_parent;
        $menu->position = $data['position'];
        $menu->menu_type = $menu_type;
        $menu->updated_by = $this->loggedInUser;
        $menu->save();

        $status = 1;

        return array('status' => $status);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Menu $menu)
    {
        Gate::authorize('delete-menu');

        $status = 0;

        $menu->delete();

        $status = 1;

        return array('status' => $status);
    }

    public function MenuList(Request $request)
    {
        $term = trim($request->term);
        $menus = DB::table('menus')
            ->selectRaw('id, name AS text')
            ->whereRaw("UPPER(name) LIKE '%" . strtoupper($term) . "%'")
            ->limit(10)
            ->get();

        $results = array(
            "results" => $menus,
        );

        return Response()->json($results);
    }

    public function ParentMenuChildrenCheck($menuId)
    {
        $childrenCount = Menu::where('parent_id', $menuId)->count();

        $status = ($childrenCount == 0) ? 1 : 0;

        return array('status' => $status);
    }

    public function formatMenuName($menuName)
    {
         // Regular expression to match words enclosed in brackets
        $pattern = '/\[(.*?)\]/';

        // Extract the words enclosed in brackets
        preg_match_all($pattern, $menuName, $matches);

        // Replace the matched words with placeholders
        $placeholders = [];

        foreach ($matches[1] as $match) {

            $placeholder = '__' . count($placeholders) . '__';

            $menuName = str_replace('[' . $match . ']', $placeholder, $menuName);

            $placeholders[$placeholder] = $match;
        }

        $formattedMenuName = trim(ucwords(Str::lower($menuName)));

        // Replace the placeholders with the original words
        $formattedMenuName = strtr($formattedMenuName, $placeholders);

        return $formattedMenuName;

    }

    private function formatRetrievedMenuName($menuName)
    {
        $words = explode(' ', $menuName);

        $processedMenuName = [];

        foreach ($words as $word) {

            if (trim(ucwords(Str::lower($word))) != trim($word)) {
                // Enclose the word with square brackets
                $processedMenuName[] = '[' . $word . ']'; 
            }
            
            if (trim(ucwords(Str::lower($word))) == trim($word)) {
                $processedMenuName[] = $word ; 
            }

        }

        $formattedMenuName = implode(' ', $processedMenuName);

        return $formattedMenuName;
    }

}
