<?php

namespace App\Http\Controllers\Admin;

use App\Models\Group;
use Illuminate\Http\Request;
use App\Models\EscalationRole;
use Ya<PERSON>ra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Gate;
use Aimsoft\UserManagement\Models\Role;
use App\Http\Requests\SaveEscalateToRequest;

class EscalationRolesController extends Controller
{
    protected $loggedInUser;

    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            $this->loggedInUser = Auth()->user()->user_name;

            return $next($request);
        });
    }

    public function index(Request $request)
    {
        Gate::authorize('access-group-escalation');

        $data = Role::select('id','name','slug');

        if ($request->ajax()) {
            return DataTables::of($data)
            ->addColumn('action',function($data){
                $btn = '';

                if (Gate::check('access-group-escalation')) {
                    $btn .= '<button id="view-escalate-roles" data-id="' . $data->id . '" class="btn btn-xs btn-default">View / Configure Roles To Escalate To</button>';
                }

                return $btn;
            })
            ->make(true);
        }

        return view('admin.escalationroles');
    }

    public function store(SaveEscalateToRequest $request)
    {
        Gate::authorize('configure-group-escalation');

        $status = 0;

        $data = $request->validated();

        $group = Group::select('id')
                    ->Where('id', $data['parentGroup'])
                    ->first();

        foreach ($data['user_groups'] as $roleId) {
            $escalateToRoles = [
                'role_id' => $group->id,
                'escalate_role_id' => $roleId,
                'created_by' => $this->loggedInUser,
                'updated_by' => $this->loggedInUser,
            ];

            EscalationRole::create($escalateToRoles);
        }

        $status = 1;

        return array('status' => $status);
    }

    public function fetchGroupName($group)
    {
        $userGroup = Role::select('name')
            ->where('id', $group)
            ->first();
        
        $groupName = $userGroup->name . " Group's".' users can escalate to : ';

        return response()->json($groupName);
    }

    public function fetchEscalateToRoles(Request $request, $group)
    {
        $data = EscalationRole::select('role_id','roles.name','roles.slug','escalation_role.escalate_role_id')
                            ->join('roles',function($join){
                                $join->on('roles.id','=','escalation_role.escalate_role_id');
                            })
                            ->where('role_id',$group)
                            ->get();

        if ($request->ajax()) {
            return DataTables::of($data)
                    ->addColumn('action',function($data){
                        $btn = '';

                        if (Gate::check('configure-group-escalation')) {
                            $btn .= '<a href="#" id="escalateTo-revoke" userGroup-id="' . $data->role_id . '" escalateGroup-id="' . $data->escalate_role_id . '" class="btn btn-xs btn-default"><i class="fa fa-times"></i> Revoke</a>';
                        }

                        return $btn;
                    })
                    ->make(true);
        }
    }

    public function fetchAllGroups(Request $request)
    {
        $confRoles = EscalationRole::select('escalate_role_id')
                                    ->where('role_id', $request->roleId)
                                    ->get()
                                    ->toArray();

        $term = trim($request->term);

        $roles = DB::table('roles')
                    ->select('id', 'name as text')
                    ->whereRaw("UPPER(name) LIKE '%" . strtoupper($term) . "%'")
                    // ->whereNotIn('id', $confRoles)
                    ->limit(10)
                    ->get();

        $results = array(
            "results" => $roles,
        );

        return Response()->json($results);
    }

    public function rekove_group($roleId,$escalateRoleId)
    {
        Gate::authorize('configure-group-escalation');

        $status = 0;

        $parentGroupName = Group::select('name')
                                ->where('id',$roleId)
                                ->first();
        
        $childGroupName = Group::select('name')
                                ->where('id',$escalateRoleId)
                                ->first();

        DB::beginTransaction();

        try {

            EscalationRole::where('role_id',$roleId)
                            ->where('escalate_role_id',$escalateRoleId)
                            ->delete();
            DB::commit();

            $status = 1;

        } catch (\Throwable $th) {
            DB::rollBack();
        }

        return [
            'status' => $status,
            'parentGroupName' => $parentGroupName->name,
            'childGroupName' => $childGroupName->name,
        ];
    }
}
