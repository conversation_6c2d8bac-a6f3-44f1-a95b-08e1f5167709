<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Services\EscalationService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Services\UserGroupLimitCheckerService;

class EscalationsController extends Controller
{
    public function fetchAllUsers()
    {
        $UserGroupLimitCheckerService = new UserGroupLimitCheckerService();

        $users = $UserGroupLimitCheckerService
                ->fetchAllUsers();

        return response()->json($users);
    }

    public function fetchUsersWithPermission(Request $request)
    {
        $permission = $request->permission;

        $UserGroupLimitCheckerService = new UserGroupLimitCheckerService();

        $users = $UserGroupLimitCheckerService
                ->setPermission($permission)
                ->fetchUsersWithPermission();

        return response()->json($users);
    }

    public function fetchUsersWithLimit(Request $request)
    {   

        $limitCategory = $request->limit_category;
        $class = $request->class;
        $amount = $request->amount;

        $UserGroupLimitCheckerService = new UserGroupLimitCheckerService();

        $users = $UserGroupLimitCheckerService
                ->setLimitCategory($limitCategory)
                ->setClass($class)
                ->setAmount($amount)
                ->fetchUsersWithLimit();

        return response()->json($users);
    }
    
    public function fetchUsersWithPermissionWithLimit(Request $request)
    {
        $limitCategory = $request->limit_category;
        $class = $request->class;
        $amount = $request->amount;
        $permission = $request->permission;
        
        $UserGroupLimitCheckerService = new UserGroupLimitCheckerService();

        $users = $UserGroupLimitCheckerService
                ->setLimitCategory($limitCategory)
                ->setClass($class)
                ->setAmount($amount)
                ->setPermission($permission)
                ->fetchUsersWithPermissionWithLimit();

        return response()->json($users);
    }

    public function fetchNextEscalationLevelUsers()
    {
        $escalationService = new EscalationService;

        $userRole = Auth::user()->role_id;

        $users = $escalationService
                ->setCurrentUserRole($userRole)
                ->fetchNextEscalationLevelUsers();

        return response()->json($users);
    }
}
