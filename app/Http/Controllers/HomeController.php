<?php

namespace App\Http\Controllers;

use Auth;
use App\Acdet;
use App\Clhmn;
use App\Clpmn;
use App\Cbmast;
//use View;
use App\Client;
use App\Certreq;

use App\MailBox;
use App\Pipcnam;
use App\Dcontrol;
use App\Approvals;
use App\Debitmast;
use App\Polmaster;
use Carbon\Carbon;
use App\Batch_list;
use App\Aimspasslog;
use App\Aimsuser_web;
use App\Escalate_pol;
use App\ObjectParams;
use App\Pass_history;
use App\Polmasterend;
use App\Approval_flow;
use App\UserAndGroups;

//use Illuminate\Support\Facades\Auth;
use App\PasswordPolicy;
use App\ObjectParamsData;
use App\workflow\Workflows;
use App\PoliciesInboxOutbox;
use Illuminate\Http\Request;
use App\workflow\Escalations;
use Ya<PERSON>ra\Datatables\Datatables;
use App\Services\DashboardService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
//use Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\View;
use Aimsoft\FlexiApproval\Enums\ApprovalRequestStatus;
use App\Http\Controllers\gb\underwriting\Cancellations;
use Aimsoft\FlexiApproval\Models\ApprovalRequestApprover;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */

    public function __construct()
    {
        $this->middleware('auth');
    }

    //change password functions
    public function showChangePasswordForm()
    {
        $signature = Auth::user()->signature;
        return view('auth.changepassword',compact('signature'));
    }

    public function changePassword(Request $request)
    {
        if (!Hash::check($request->get('current-password'), Auth::user()->password)) {
            // The passwords matches
            return redirect()
                ->back()
                ->with('error', 'Your current password does not matches with the password you provided. Please try again.');
        }

        if (strcmp($request->get('current-password'), $request->get('new-password')) == 0) {
            //Current password and new password are same
            return redirect()
                ->back()
                ->with('error', 'New Password cannot be same as your current password. Please choose a different password.');
        }

        $validatedData = $request->validate([
            'current-password' => 'required',
            'new-password' => 'required|confirmed',
        ]);

        //Change Password
        $passpolicy = PasswordPolicy::where('record_type', 0)->first();
        $temporary_acc = Auth::user()->temporary_acc;
        $expiry_days = $passpolicy->expiry_days;

        if($temporary_acc == "Y"){
          $expiry_days = $passpolicy->temporary_expiry_days;

        }
        $expiry_date = Carbon::today()->addDays($expiry_days - 1);

        $passhist = Pass_history::where("user_id",Auth::user()->user_id)
                                ->where('date_logged', '>=', now()->subMonths($passpolicy->reuse_cycle))
                                ->orderBy('date_logged','desc')
                               // ->take($passpolicy->reuse_cycle)
                                ->get();
        
        foreach ($passhist as $pass) {
            if (Hash::check($request->get('new-password'), $pass->password)) {
                $error .= "You cant Re Use a Password used within $passpolicy->reuse_cycle Months . Kindly input a different password ";
                return redirect()->back()->withInput()->with('error', $error);
                }
        }

        $user = Auth::user();
        $user->password = bcrypt($request->get('new-password'));
        $user->pass_valid_days = $expiry_days;
        $user->pass_expiry_date = $expiry_date;
        $user->start_warning_days = $passpolicy->start_warning_days;
        $user->save();

        //update password change log table
        $today = Carbon::today();
        $now = Carbon::now();

        $aimspasslog = Aimspasslog::insert([
            'user_name' => trim($user->user_name),
            'email' => trim($user->email),
            'new_password' => bcrypt($request->get('new-password')),
            'old_password' => bcrypt($request->get('current-password')),
            'pass_change_date' => $today,
            'pass_change_time' => $now,
            'pass_expiry_date' => $expiry_date,
        ]);

         ##new record to pass history table
        $next = Pass_history::max('id');
        $count = $next + 1;
        $passwd = Pass_history::insert([
            'user_name' => trim($user->user_name),
            'user_id' => trim($user->user_id),
            'id' =>$count,
            'password' => bcrypt($request->get('current-password')),
            'date_logged' => Carbon::now(),
        
        ]);

        if ($request->get('changlnxpasswd')) {
            $username = trim(Auth::user()->user_name);
            $password = trim($request->get('new-password'));
            $command = "echo '$username:$password' |sudo chpasswd ";
            shell_exec($command);
        }

        Auth::logout();
        return view('auth.login')->with('success', 'Password changed successfully !');
    }

    public function checkPassExpiry(Request $request)
    {
        $today = Carbon::today();
        $user = auth::User();
        $warning_days = $user->start_warning_days;
        $days = $today->diffInDays($user->pass_expiry_date, false);
        //dd($days);
        if ($user->pass_expiry_date < $today || $user->pass_expiry_date == $today) {
            $result = [
                'status' => 1,
                'days' => $days,
                'warning_days' => $warning_days,
            ];
        } else {
            $result = [
                'status' => 0,
                'days' => $days,
                'warning_days' => $warning_days,
            ];
        }
        return $result;
    }
    public function checkpassparams(Request $request)
    {
        // $user = auth::User();
        $passpolicyparams = PasswordPolicy::where('record_type', 0)->get();

        echo json_encode($passpolicyparams[0]);
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        # get user details
        $user = Auth::user();
        $user_name = $user->user_name;
        $user_branch = $user->branch;

        $registered_policies_count = null;
        $debited_policies_count = null;
        $debited_SI = null;
        $debited_premium = null;
        $unrenewedPolicies = null;
        $renewedPolicies = null;
        $debitedNewBusiness = null;
        $undebited_policy_transactions = collect();
        

        $users_group = UserAndGroups::where('user_id', $user->user_id)->get();

        # registered policies by the current user
        if (Gate::check('view-dash-own-registered-policies')) {
            $registered_policies_count = Dcontrol::where('user_str', $user_name)
                ->where('account_year', '=', Carbon::now()->format('Y'))
                ->count();
        }

        # all registered policies
        if (Gate::check('view-dash-total-registered-policies')) {
            $registered_policies_count = Dcontrol::where('account_year', '=', Carbon::now()->format('Y'))
                                       ->whereRaw("cancelled <> 'Y'")
                                       ->whereRaw("trans_type <> 'NIL'")
                                       ->count('policy_no');
        }

        # debited policies by the current user
        if (Gate::check('view-dash-own-debited-policies')) {
            $debited_policies_count = Debitmast::where('user_str', $user_name)
                ->where('account_year', '=', Carbon::now()->format('Y'))
                ->count();
        }

        # all debited policies
        if (Gate::check('view-dash-total-debited-policies')) {
            $debited_policies_count = Debitmast::where('account_year', '=', Carbon::now()->format('Y'))->count('policy_no');
        }

        # debited total sum insured by the current user
        if (Gate::check('view-dash-own-debited-si')) {
            $debited_SI = Debitmast::where('user_str', $user_name)
                ->where('account_year', '=', Carbon::now()->format('Y'))
                ->sum('sum_insured');
        }

        # debited total sum insured
        if (Gate::check('view-dash-total-debited-si')) {
            $debited_SI = Debitmast::where('account_year', '=', Carbon::now()->format('Y'))->sum('sum_insured');
        }

        # debited total premium by the current user
        if (Gate::check('view-dash-own-debited-prem')) {
            $debited_premium = Debitmast::where('user_str', $user_name)
                ->where('account_year', '=', Carbon::now()->format('Y'))
                ->sum('nett_amount');
        }

        # debited total premium
        if (Gate::check('view-dash-total-debited-prem')) {
            $debited_premium = Debitmast::where('account_year', '=', Carbon::now()->format('Y'))->sum('nett_amount');
        }

        

        # unprocessed policy transactions by the current user
        if (Gate::check('view-dash-own-unprocessed-policy-transactions')) {
            $undebited_policy_transactions = DB::table('Dcontrol')
                ->select('dcontrol.endt_renewal_no', 'dcontrol.sum_insured', 'dcontrol.user_str', 'client.name', 'class.motor_policy')
                ->join('client', function ($join) {
                    $join->on('client.client_number', 'dcontrol.client_number');
                })
                ->join('class', function ($join) {
                    $join->on('class.class', 'dcontrol.class');
                })
                ->leftJoin('debitmast', function ($join) {
                    $join->on('debitmast.endt_renewal_no', 'dcontrol.endt_renewal_no');
                })
                ->where('dcontrol.user_str', '=', $user_name)
                ->where('dcontrol.cancelled','<>','Y')
                ->where('dcontrol.committed','<>','Y')
                ->whereNull('debitmast.endt_renewal_no')
                ->orderBy('dcontrol.dola', 'desc')
                ->take(5)
                ->get();
        }

        # all unprocessed policy transactions
        if (Gate::check('view-dash-all-unprocessed-policy-transactions')) {
            $undebited_policy_transactions = DB::table('Dcontrol')
            ->select('dcontrol.endt_renewal_no', 'dcontrol.sum_insured', 'dcontrol.user_str', 'dcontrol.committed', 'dcontrol.cancelled', 'dcontrol.dola', 'client.name', 'class.motor_policy')
            ->join('client', 'client.client_number', '=', 'dcontrol.client_number')
            ->join('class', 'class.class', '=', 'dcontrol.class')
            ->leftJoin('debitmast', 'debitmast.endt_renewal_no', '=', 'dcontrol.endt_renewal_no')
            ->whereNull('debitmast.endt_renewal_no')
            ->where('dcontrol.cancelled', '<>', 'Y')
            ->where(function ($query) {
                $query->where('dcontrol.committed', '=', 'N')
                      ->orWhereNull('dcontrol.committed');
            })
            ->orderBy('dcontrol.dola', 'desc')
            ->take(5)
            ->get();
  
        }

        # all unprocessed policy transactions
        if (Gate::check('view-dash-all-production-statistics')) {
            $startOfMonth = Carbon::now()->startOfMonth();
            $today = Carbon::today();
            // dd(Carbon::now()->addDays(46));
                
            $unrenewedPolicies = Polmaster::select('policy_no','expiry_date','renewal_date','period_from')
                ->whereExists(function($query){
                    $query->select('*')
                        ->from('debitmast')
                        ->whereColumn('debitmast.policy_no','polmaster.policy_no');
                })
                ->whereExists(function($query){
                    $query->select('*')
                        ->from('class')
                        ->where('class.renewable','Y')
                        ->whereColumn('class.class','polmaster.class');
                })
                // ->whereBetween('renewal_date', [$startOfMonth, $today])
                ->where('status_code', 'ACT')
                ->get()
                ->filter(function($policy) use ($startOfMonth,$today){
                    $cncCtrl = new Cancellations;
                    $baseTrans = $cncCtrl->latest_baseTransaction($policy->policy_no);
                    $renewalDue = Dcontrol::select('renewal_date','expiry_date')
                        ->where('endt_renewal_no',$baseTrans->endt_renewal_no)
                        ->whereBetween('renewal_date', [$startOfMonth, $today])
                        ->count();
                    return $renewalDue;
                    
                })
                ->count();
                
            // dd($unrenewedPolicies);
            $renewedPolicies = Debitmast::select('policy_no','expiry_date','renewal_date','period_from')
                ->whereExists(function($query){
                    $query->select('*')
                        ->from('class')
                        ->where('class.renewable','Y')
                        ->whereColumn('class.class','debitmast.class');
                })
                ->whereBetween('dola', [$startOfMonth, $today])
                ->where('entry_type_descr', 'REN')
                ->count();
            $debitedNewBusiness = Debitmast::select('policy_no','expiry_date','renewal_date','period_from')
                ->whereExists(function($query){
                    $query->select('*')
                        ->from('class')
                        ->where('class.renewable','Y')
                        ->whereColumn('class.class','debitmast.class');
                })
                ->whereBetween('dola', [$startOfMonth, $today])
                ->where('entry_type_descr', 'POL')
                ->count();

        }



        # get 5 underwriters with the highest debited premium weekly
        $driver = DB::getDriverName();
        if ($driver === 'pgsql') {
            $leaderboard = DB::select(
                "SELECT d.USER_STR,a.FIRST_NAME , a.LAST_NAME, c.CURRENCY, count(d.dtrans_no) as count, sum(d.nett_amount) as total
                            FROM DEBITMAST d
                            JOIN CURRENCY c ON c.base_currency = 'Y'
                            LEFT JOIN AIMSUSERS a ON d.USER_STR = a.USER_NAME
                            WHERE DATE_TRUNC('day', d.Dola) = DATE_TRUNC('day', CURRENT_DATE)
                        -- AND EFFECTIVE_DATE >= CURRENT_TIMESTAMP - INTERVAL '7 days'
                            GROUP BY d.USER_STR,a.FIRST_NAME , a.LAST_NAME, c.currency
                            ORDER BY count(d.dtrans_no) DESC, sum(d.nett_amount) desc
                            LIMIT 5"
            );
        } else {
            $leaderboard = DB::select(
                "SELECT d.USER_STR,a.FIRST_NAME , a.LAST_NAME, c.CURRENCY, count(d.dtrans_no) as count, sum(d.nett_amount) as total
                            FROM DEBITMAST d
                            JOIN CURRENCY c ON c.base_currency = 'Y'
                            LEFT JOIN AIMSUSERS a ON d.USER_STR = a.USER_NAME
                            WHERE trunc(d.Dola) = trunc(sysdate)
                        -- AND EFFECTIVE_DATE >= CURRENT_TIMESTAMP - 7
                            GROUP BY d.USER_STR,a.FIRST_NAME , a.LAST_NAME, c.currency
                            ORDER BY count(d.dtrans_no) DESC, sum(d.nett_amount) desc
                            FETCH FIRST 5 ROWS ONLY"
            );
        }

        if ($driver === 'pgsql') {
            $monthly_registered = DB::table('Dcontrol')
                ->selectRaw("TO_CHAR(Dola,'Mon') as month,count(POLICY_NO) as pol_count")
                ->whereRaw("EXTRACT(YEAR FROM Dola) = EXTRACT(YEAR FROM CURRENT_DATE)")
                ->whereRaw("cancelled <> 'Y'")
                ->groupByRaw("EXTRACT(MONTH FROM Dola),TO_CHAR(Dola,'Mon')")
                ->orderByRaw("EXTRACT(MONTH FROM Dola)")
                ->get()
                ->toArray();

            $monthly_debited = DB::table('Debitmast')
                ->selectRaw("TO_CHAR(Dola,'Mon') as month,count(Nett_AMOUNT) as gross_amount")
                ->whereRaw("EXTRACT(YEAR FROM Dola) = EXTRACT(YEAR FROM CURRENT_DATE)")
                ->groupByRaw("EXTRACT(MONTH FROM Dola),TO_CHAR(Dola,'Mon')")
                ->orderByRaw("EXTRACT(MONTH FROM Dola)")
                ->get()
                ->toArray();
        } else {
            $monthly_registered = DB::table('Dcontrol')
                ->selectRaw("to_char((Dola),'Mon') as month,count(POLICY_NO) as pol_count")
                ->whereRaw("to_char(Dola,'yyyy') = TO_CHAR(CURRENT_DATE,'yyyy') ")
                ->whereRaw("cancelled <> 'Y'")
                ->groupByRaw("to_char((Dola),'MM'),to_char((Dola),'Mon')")
                ->orderByRaw("to_char((Dola),'MM')")
                ->get()
                ->toArray();

            $monthly_debited = DB::table('Debitmast')
                ->selectRaw("to_char(TO_DATE(Dola),'Mon') as month,count(Nett_AMOUNT) as gross_amount")
                ->whereRaw("to_char(Dola,'yyyy') = TO_CHAR(CURRENT_DATE,'yyyy')")
                ->groupByRaw("to_char(TO_DATE(Dola),'MM'),to_char(TO_DATE(Dola),'Mon')")
                ->orderByRaw("to_char(TO_DATE(Dola),'MM')")
                ->get()
                ->toArray();
        }

        $months_todate = monthsToDate();
        $total_monthly_debited = (new DashboardService())->prepareMonthlyDebitedGraphData($monthly_debited, $months_todate);
        $total_monthly_registered = (new DashboardService())->prepareMonthlyRegGraphData($monthly_registered, $months_todate);

        // debited policies in last 7 days
        if ($driver === 'pgsql') {
            $last_count_debited_policies = DB::table('Debitmast')
                ->selectRaw("TO_CHAR(DOLA,'YYYY-MM-DD') AS specific_day, TO_CHAR(DOLA,'Day') as day_name, count(policy_no) as count")
                ->whereDate('dola','<=',Carbon::today()->toDateString())
                ->whereDate('dola','>',(Carbon::today())->subDays(7)->toDateString())
                ->groupByRaw("TO_CHAR(dola,'YYYY-MM-DD'),TO_CHAR(dola,'Day')")
                ->get()->toArray();

            $last_count_reg_policies = DB::table('Dcontrol')
                ->selectRaw("TO_CHAR(DOLA,'YYYY-MM-DD') AS specific_day, TO_CHAR(DOLA,'Day') as day_name, count(policy_no) as count")
                ->whereDate('dola','<=',Carbon::today()->toDateString())
                ->whereDate('dola','>',(Carbon::today())->subDays(7)->toDateString())
                ->groupByRaw("TO_CHAR(dola,'YYYY-MM-DD'),TO_CHAR(dola,'Day')")
                ->get()->toArray();
        } else {
            $last_count_debited_policies = DB::table('Debitmast')
                ->selectRaw("TO_CHAR(DOLA,'YYYY-MM-DD') AS specific_day, to_char(TO_DATE(DOLA),'Day') as day_name, count(policy_no) as count")
                ->whereDate('dola','<=',Carbon::today()->toDateString())
                ->whereDate('dola','>',(Carbon::today())->subDays(7)->toDateString())
                ->groupByRaw("TO_CHAR(dola,'YYYY-MM-DD'),to_char(TO_DATE(dola),'Day')")
                ->get()->toArray();

            $last_count_reg_policies = DB::table('Dcontrol')
                ->selectRaw("TO_CHAR(DOLA,'YYYY-MM-DD') AS specific_day, to_char(TO_DATE(DOLA),'Day') as day_name, count(policy_no) as count")
                ->whereDate('dola','<=',Carbon::today()->toDateString())
                ->whereDate('dola','>',(Carbon::today())->subDays(7)->toDateString())
                ->groupByRaw("TO_CHAR(dola,'YYYY-MM-DD'),to_char(TO_DATE(dola),'Day')")
                ->get()->toArray();
        }

        $total_weekly_debited = (new DashboardService())->prepareWeeklyDebitedGraphData($last_count_debited_policies);
        $total_weekly_registered = (new DashboardService())->prepareWeeklyRegGraphData($last_count_reg_policies);

        $last7days = getDatesinLastNDays(6);

        $inbox_data = $this->getInboxData($user->user_id);

        // dd($inbox_data);

        $reinsurance_dashboard_data = $this->reinsuranceData($user);

        $finance_data = $this->getFinanceData($user);
        $claimsDashboardContent = $this->claimsDashboardContent($user);
        // dd($finance_data);

        $currency = DB::table('pipcnam')->select('currency.CURRENCY')
        ->join('currency', function ($join) {
            $join->on('currency.CURRENCY_CODE', 'pipcnam.CURRENCY_CODE');
        })
        ->first();

        if (Gate::check('view-dash-own-registered-policies')
        or Gate::check('view-dash-total-registered-policies')
        or Gate::check('view-dash-reinsurance-total-count')
        or Gate::check('view-dash-finance-own-count')
        or Gate::check('view-dash-own-latest-registered-claims')
        or Gate::check('view-dash-outstanding-premium-own-count')
        or Gate::check('view-dash-all-production-statistics')
        
        ) {
            return View::make('dashboard2', [
                'registered_policies' => (object) ['count' => $registered_policies_count, 'label' => 'Transactions Registered This Year'],
                'debited_policies_count' => (object) ['count' => $debited_policies_count, 'label' => 'Transactions Debited This Year'],
                'debited_SI' => (object) ['count' => $debited_SI, 'label' => 'Sum Insured Debited This Year'],
                'debited_premium' => (object) ['count' => $debited_premium, 'label' => 'Premium Debited This Year'],
                'unrenewedPolicies' => (object) ['count' => $unrenewedPolicies, 'label' => 'Unrenewed policies'],
                'renewedPolicies' => (object) ['count' => $renewedPolicies, 'label' => 'Renewed policies'],
                'debitedNewBusiness' => (object) ['count' => $debitedNewBusiness, 'label' => 'New Business (Debited)'],
                'undebited_policy_transactions' => (object) ['transactions' => $undebited_policy_transactions, 'label' => 'Unprocessed policy transactions'],

                'registered_claims' => (object) ['count' => $registered_claims_count, 'label' => 'Registered claims'],
                'processed_claims' => (object) ['count' => $processed_claims_count, 'label' => 'Processed claims'],
                'paid_claims' => (object) ['amount' => $paid_claims_amount, 'label' => 'Paid claims amount'],
                'latest_registered_claims' => (object) ['transactions' => $latest_registered_claims, 'label' => 'Latest registered claims'],
                'leaderboard' => $leaderboard,

                'monthly_pol_registered' => json_encode($total_monthly_registered),
                'monthly_pol_debited' => json_encode($total_monthly_debited),
                'months' => json_encode($months_todate),

                'last7days' => json_encode($last7days),
                'last7days_debited' => json_encode($total_weekly_debited),
                'last7days_registered' => json_encode($total_weekly_registered),
                'inbox_data' => $inbox_data,
                'reinsurance_dashboard_data' => ($reinsurance_dashboard_data),
                'financeData' =>$finance_data,
                'claims_dashboard_data' => ($claimsDashboardContent),
                'currency' => $currency->currency
            ]);
        } else {
            return view('dashboard');
        }
    }

    
    
    //!--------------------write signaturepad--------------------------
    public function signaturePad(Request $request){
        //authentication
        $user = Auth::user();
        $image_file = trim($request->signed);
        $signature = trim($request->signed);
        $type = pathinfo($signature, PATHINFO_EXTENSION);
        $data = file_get_contents($signature);
        $img_base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);

        //update/save pic name to db
        $user->signature = trim($img_base64);

        //!sign trail tb update
        $data = array(
            'username' => trim(Auth::user()->user_name),
            'dola' => Carbon::now(),
            'time' => Carbon::now()->format('H:i:s'),
            'signature' => trim($img_base64)
        );
        $keep_sign_trail = DB::table('signature_trails')->insert($data);
        $user->save();

        return back()->with('success', 'Signature Written Successifully ');

    }
        //!--------------------/write signaturepad------------------------------


    //!---------------------upload signature---------------------------------
    public function uploadSignature(Request $request){
        $this->validate($request, [
            'signature' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        if ($request->hasFile('signature')) {    
            $user = Auth::user();
            $signature = trim($request->file('signature'));
            $type = pathinfo($signature, PATHINFO_EXTENSION);
            $data = file_get_contents($signature);
            $img_base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);

            //update/save pic name to db
            $user->signature =trim($img_base64);
            //!sign trail tb update
            $data = array(
                'username' => trim(Auth::user()->user_name),
                'dola' => Carbon::now(),
                'time' => Carbon::now()->format('H:i:s'),
                'signature' => trim($img_base64)
            );
            $keep_sign_trail = DB::table('signature_trails')->insert($data);
            $user->save();

        }

        return redirect()->back()->with('success','Signature Uploaded successfully');
        
    }
    //!---------------------upload signature---------------------------------


    #----------------------------------Reinsurance-------------------------------------
    private function reinsuranceData($user){
        $year = Carbon::now()->year;
        $month = Carbon::now()->month;
        $user_name = $user->user_name;

        if (Gate::check('view-dash-reinsurance-premium')){
            if ($driver === 'pgsql') {
                $total_rein_premium = Debitmast::whereMonth('DOLA', '=', $month)
                                                ->whereYear('DOLA', '=', $year)
                                                ->sum(DB::raw('COALESCE(KR_PREMIUM,0)+COALESCE(QUOTA_PREMIUM,0)+COALESCE(SURP_1ST_PREMIUM,0)+COALESCE(SURP_2ND_PREMIUM,0)+
                                                                COALESCE(SURP_3RD_PREMIUM,0)+COALESCE(FACULT_PREMIUM,0)'));
            } else {
                $total_rein_premium = Debitmast::whereMonth('DOLA', '=', $month)
                                                ->whereYear('DOLA', '=', $year)
                                                ->sum(DB::raw('NVL(KR_PREMIUM,0)+NVL(QUOTA_PREMIUM,0)+NVL(SURP_1ST_PREMIUM,0)+NVL(SURP_2ND_PREMIUM,0)+
                                                                NVL(SURP_3RD_PREMIUM,0)+NVL(FACULT_PREMIUM,0)'));
            }
        }
        if (Gate::check('view-dash-reinsurance-recoveries')){
            if ($driver === 'pgsql') {
                $total_rein_recoveries = Clpmn::whereMonth('PAY_DATE', '=', $month)
                                                ->whereYear('PAY_DATE', '=', $year)
                                                ->whereIn('pay_type',array(10,20))
                                                ->sum(DB::raw('COALESCE(MANDATORY_RECOVERY,0)+COALESCE(QUOTA_SHARE_RECOVERY,0)+COALESCE(SURPLUS_1ST_RECOVERY,0)+
                                                                COALESCE(SURPLUS_2ND_RECOVERY,0)+COALESCE(SURPLUS_3RD_RECOVERY,0)+COALESCE(FACULT_N_RECOVERY,0)'));
            } else {
                $total_rein_recoveries = Clpmn::whereMonth('PAY_DATE', '=', $month)
                                                ->whereYear('PAY_DATE', '=', $year)
                                                ->whereIn('pay_type',array(10,20))
                                                ->sum(DB::raw('NVL(MANDATORY_RECOVERY,0)+NVL(QUOTA_SHARE_RECOVERY,0)+NVL(SURPLUS_1ST_RECOVERY,0)+
                                                                NVL(SURPLUS_2ND_RECOVERY,0)+NVL(SURPLUS_3RD_RECOVERY,0)+NVL(FACULT_N_RECOVERY,0)'));
            }
        }

        $premium_per_treaty = DB::table('Debitmast')
                            ->select('KR_PREMIUM','QUOTA_PREMIUM','SURP_1ST_PREMIUM','SURP_2ND_PREMIUM',
                                    'SURP_3RD_PREMIUM','FACULT_PREMIUM')->get()
                            ;
  
        $premium_per_treaty_result = (new DashboardService())->premiumPerTreaty($premium_per_treaty);

        $fac_requisitions = DB::table('Payreqst p')
                            ->select('c.name','p.req_no')
                            ->join('client c', function ($join) {
                                $join->on('c.CLIENT_NUMBER', 'p.CLIENT_NUMBER');
                            })
                            ->where('source_code','=','FAC')
                            ->where('payments_todate','=',0)
                            ->whereNull('cancelled')
                            ->take(5)
                            ->get();
        if(Gate::check('view-dash-reinsurance-graph')){
            if ($driver === 'pgsql') {
                $total_monthly_premium_outward = DB::select("
                    WITH months AS (
                        SELECT TO_CHAR(DATE_TRUNC('year', CURRENT_DATE) + (generate_series(0, EXTRACT(MONTH FROM CURRENT_DATE)::int - 1) || ' months')::interval, 'Mon') as month
                    )
                    SELECT sub.month, COALESCE(sum(
                        COALESCE(KR_PREMIUM,0)+
                        COALESCE(QUOTA_PREMIUM,0)+
                        COALESCE(SURP_1ST_PREMIUM,0)+
                        COALESCE(SURP_2ND_PREMIUM,0)+
                        COALESCE(SURP_3RD_PREMIUM,0)+
                        COALESCE(FACULT_PREMIUM,0)),0) as count
                    FROM months sub
                    LEFT OUTER JOIN Debitmast c ON sub.month = TO_CHAR(dola,'Mon')
                        AND EXTRACT(YEAR FROM dola) = EXTRACT(YEAR FROM CURRENT_DATE)
                    GROUP BY sub.month
                ");

                $total_recoveries_pm_query = DB::select("
                    WITH months AS (
                        SELECT TO_CHAR(DATE_TRUNC('year', CURRENT_DATE) + (generate_series(0, EXTRACT(MONTH FROM CURRENT_DATE)::int - 1) || ' months')::interval, 'Mon') as month
                    )
                    SELECT sub.month, COALESCE(sum(
                        COALESCE(MANDATORY_RECOVERY,0)
                        +COALESCE(QUOTA_SHARE_RECOVERY,0)
                        +COALESCE(SURPLUS_1ST_RECOVERY,0)
                        +COALESCE(SURPLUS_2ND_RECOVERY,0)
                        +COALESCE(SURPLUS_3RD_RECOVERY,0)
                        +COALESCE(FACULT_P_RECOVERY,0)
                        +COALESCE(XLOSS_1_RECOVERY_3,0)
                        +COALESCE(XLOSS_2_RECOVERY_3,0)
                        +COALESCE(XLOSS_3_RECOVERY_3,0)),0) as count
                    FROM months sub
                    LEFT OUTER JOIN Clpmn c ON sub.month = TO_CHAR(PAY_DATE,'Mon')
                        AND EXTRACT(YEAR FROM PAY_DATE) = EXTRACT(YEAR FROM CURRENT_DATE)
                        AND PAY_TYPE IN (10,20)
                    GROUP BY sub.month
                ");
            } else {
                $total_monthly_premium_outward = DB::select("select sub.month,COALESCE(sum(
                                                                    nvl(KR_PREMIUM,0)+
                                                                    nvl(QUOTA_PREMIUM,0)+
                                                                    nvl(SURP_1ST_PREMIUM,0)+
                                                                    nvl(SURP_2ND_PREMIUM,0)+
                                                                    nvl(SURP_3RD_PREMIUM,0)+
                                                                    nvl(FACULT_PREMIUM,0)),0) as count from
                                                                    (
                                                                    select TO_CHAR( ADD_MONTHS( TRUNC(SYSDATE,'year') , LEVEL-1  ), 'Mon' ) month
                                                                    FROM dual
                                                                    CONNECT BY LEVEL <= floor( months_between( sysdate, trunc(sysdate,'YYYY')))+1
                                                                    ) sub
                                                                    left outer join Debitmast c ON 	sub.month = to_char(dola,'Mon')
                                                                    AND (to_char(dola,'YYYY') = to_char(sysdate,'YYYY'))
                                                                    group by  sub.month");
                $total_recoveries_pm_query = DB::select("select sub.month,COALESCE(sum(
                                                                nvl(MANDATORY_RECOVERY,0)
                                                                +nvl(QUOTA_SHARE_RECOVERY,0)
                                                                +nvl(SURPLUS_1ST_RECOVERY,0)
                                                                +nvl(SURPLUS_2ND_RECOVERY,0)
                                                                +nvl(SURPLUS_3RD_RECOVERY,0)
                                                                +nvl(FACULT_P_RECOVERY,0)
                                                                +nvl(XLOSS_1_RECOVERY_3,0)
                                                                +nvl(XLOSS_2_RECOVERY_3,0)
                                                                +nvl(XLOSS_3_RECOVERY_3,0)),0) as count from
                                                                                            (
                                                                                            select TO_CHAR( ADD_MONTHS( TRUNC(SYSDATE,'year') , LEVEL-1  ), 'Mon' ) month
                                                                                            FROM dual
                                                                                            CONNECT BY LEVEL <= floor( months_between( sysdate, trunc(sysdate,'YYYY')))+1

                                                                                            ) sub
                                                                left outer join Clpmn c ON 	sub.month = to_char(PAY_DATE ,'Mon')
                                                                AND (to_char(PAY_DATE,'YYYY') = to_char(sysdate,'YYYY'))
                                                                AND PAY_TYPE IN (10,20)
                                                                group by  sub.month");
            }
            $total_recoveries_pm = (new DashboardService())->sortGraphDataByMonth($total_recoveries_pm_query);

            $monthly_premium_outward = (new DashboardService())->sortGraphDataByMonth($total_monthly_premium_outward);

        }

        if (Gate::check('view-dash-reinsurance-total-count')){
            $total_debited_premium = Debitmast::whereMonth('DOLA', '=', $month)
                                            ->whereYear('DOLA', '=', $year)
                                            ->sum('gross_amount');
            if ($driver === 'pgsql') {
                $total_rein_premium = Debitmast::whereMonth('DOLA', '=', $month)
                                                ->whereYear('DOLA', '=', $year)
                                                ->sum(DB::raw('COALESCE(KR_PREMIUM,0)+COALESCE(QUOTA_PREMIUM,0)+COALESCE(SURP_1ST_PREMIUM,0)+COALESCE(SURP_2ND_PREMIUM,0)+COALESCE(SURP_3RD_PREMIUM,0)+COALESCE(FACULT_PREMIUM,0)'));
            } else {
                $total_rein_premium = Debitmast::whereMonth('DOLA', '=', $month)
                                                ->whereYear('DOLA', '=', $year)
                                                ->sum(DB::raw('NVL(KR_PREMIUM,0)+NVL(QUOTA_PREMIUM,0)+NVL(SURP_1ST_PREMIUM,0)+NVL(SURP_2ND_PREMIUM,0)+NVL(SURP_3RD_PREMIUM,0)+NVL(FACULT_PREMIUM,0)'));
            }
        }
        $user_endt = Dcontrol::select('endt_renewal_no')->where('USER_STR','=',$user_name);

        $facultative_reqs = DB::table('payreqst')
                                ->select('*')
                                ->where('source_code','=','FAC')
                                ->where('cancelled','<>','Y')
                                ->whereNull('cancelled')
                                ->whereExists(function ($query) {
                                    $query->select(1)
                                        ->from('Dcontrol')
                                        ->whereColumn('Dcontrol.endt_renewal_no','payreqst.claim_no');
                                })->get();

        $reinsurance_data = [
            "total_rein_premium" =>(object) ['count' => $total_rein_premium, 'label' => 'Uncollected premium','cardImg'=>'debited-policies.png'],
            "total_rein_recoveries" => (object) ['count' => $total_rein_recoveries, 'label' => 'Reinsurance recoveries','cardImg'=>'reinsurance.png'],
            "premium_per_treaty" => json_decode($premium_per_treaty_result),
            "premium_outward_pm" => $monthly_premium_outward,
            "total_recoveries_pm" => $total_recoveries_pm,
            "fac_requisitions" => $fac_requisitions
        ];

        return $reinsurance_data;
    }
    #----------------------------------End of Reinsurance-------------------------------------------

    private function getFinanceData($user){
        // finance card 1 data(Outstanding Premium Debit/Credits)
        // dd("vbnjm");
        if(Gate::check('view-dash-outstanding-premium-own-count')) { 
            $total_outstanding_premium = DB::table('Acdet')
                ->where('source', '=','U/W')
                ->where('unallocated','<>', 0)
                ->where('user_1','=', $user->user_name)
                ->sum('unallocated');
                

        }
        if(Gate::check('view-dash-outstanding-premium-total-count')) { 
            $total_outstanding_premium = DB::table('Acdet')
                    ->where('source', '=','U/W')
                    ->where('unallocated','<>', 0)
                    ->sum('unallocated');
        }
        // finance card 2 data(Total Claims Outstanding)
        if(Gate::check('view-dash-outstanding-claims-own-count')) { 
            $total_claims_outstanding = DB::table('Clhmn')
            ->where('user_str','=', $user->user_name)
            ->where('curr_total_estimate','>', 0)
            ->sum('curr_total_estimate'); 
        }
        if(Gate::check('view-dash-outstanding-claims-total-count')) { 
            $total_claims_outstanding = DB::table('Clhmn')
                    ->where('curr_total_estimate','>', 0)
                    ->sum('curr_total_estimate');
        }

        // finance card 3 data(Total Receipts Curr Month)
        if(Gate::check('view-dash-receipts-curr-own-count')) { 
            $receipts_curr_month = DB::table('COMBINED_REC_VIEW')
            ->selectRaw("ABS(SUM(foreign_amount)) as Total")
            ->where('created_by','=', $user->user_name)
            ->where('account_year', '=', Carbon::now()->year)
            ->where('account_month', '=', Carbon::now()->month)
            ->value('Total');
        }

        if(Gate::check('view-dash-receipts-curr-total-count')) { 
            $receipts_curr_month = DB::table('COMBINED_REC_VIEW')
            ->selectRaw("ABS(SUM(foreign_amount)) as Total")
            ->where('account_year', '=', Carbon::now()->year)
            ->where('account_month', '=', Carbon::now()->month)
            ->value('Total');
        }

        // finance card 4 data(Total Payments Curr Month)
        if(Gate::check('view-dash-payments-curr-own-count')) { 
            $payments_curr_month = DB::table('COMBINED_PAY_VIEW')
            ->selectRaw("ABS(SUM(foreign_amount)) as Total")
            ->where('created_by','=', $user->user_name)
            ->where('account_year', '=', Carbon::now()->year)
            ->where('account_month', '=', Carbon::now()->month)
            ->value('Total');
        }

        if(Gate::check('view-dash-payments-curr-total-count')) { 
            $payments_curr_month = DB::table('COMBINED_PAY_VIEW')
            ->selectRaw("ABS(SUM(foreign_amount)) as Total")
            ->where('account_year', '=', Carbon::now()->year)
            ->where('account_month', '=', Carbon::now()->month)
            ->value('Total'); 
        }

        // finance card 5 data(Total YTD receipts)
        if(Gate::check('view-dash-receipts-ytd-total-count')) { 
            $receipts_curr_ytd = DB::table('COMBINED_REC_VIEW')
            ->selectRaw("ABS(SUM(foreign_amount)) as Total")
            ->where('account_year', '=', Carbon::now()->year)
            ->whereBetween('dola', [Carbon::now()->startOfYear(), Carbon::now()])
            ->value('Total');         
        }

        // finance card 6 data(Total YTD Payments)
        if(Gate::check('view-dash-payments-ytd-total')) { 
            $payments_curr_ytd = DB::table('COMBINED_PAY_VIEW')
            ->selectRaw("ABS(SUM(foreign_amount)) as Total")
            ->where('account_year', '=', Carbon::now()->year)
            ->whereBetween('dola', [Carbon::now()->startOfYear(), Carbon::now()])
            ->value('Total');                       
        }

        // $total_unallocated_premium = DB::table('Acdet')
        //         ->where('source', '=','U/W' )
        //         ->sum('unallocated');

        // Cash In Cash Out graph data
        if ($driver === 'pgsql') {
            $cashOut = DB::select("
                WITH months AS (
                    SELECT TO_CHAR(DATE_TRUNC('year', CURRENT_DATE) + (generate_series(0, EXTRACT(MONTH FROM CURRENT_DATE)::int - 1) || ' months')::interval, 'Mon') as month,
                           EXTRACT(MONTH FROM DATE_TRUNC('year', CURRENT_DATE) + (generate_series(0, EXTRACT(MONTH FROM CURRENT_DATE)::int - 1) || ' months')::interval)::text as mon
                )
                SELECT sub.month, sub.mon, COALESCE(sum(c.AMOUNT),0) as amount
                FROM months sub
                LEFT OUTER JOIN CBMAST c ON sub.month = TO_CHAR(TO_DATE(c.ACCOUNT_MONTH,'MM'),'Mon')
                    AND DOC_TYPE = 'REC'
                GROUP BY sub.MONTH, sub.mon
                ORDER BY sub.mon::int
            ");

            $cashIn = DB::select("
                WITH months AS (
                    SELECT TO_CHAR(DATE_TRUNC('year', CURRENT_DATE) + (generate_series(0, EXTRACT(MONTH FROM CURRENT_DATE)::int - 1) || ' months')::interval, 'Mon') as month,
                           EXTRACT(MONTH FROM DATE_TRUNC('year', CURRENT_DATE) + (generate_series(0, EXTRACT(MONTH FROM CURRENT_DATE)::int - 1) || ' months')::interval)::text as mon
                )
                SELECT sub.month, sub.mon, COALESCE(sum(c.AMOUNT),0) as amount
                FROM months sub
                LEFT OUTER JOIN CBMAST c ON sub.month = TO_CHAR(TO_DATE(c.ACCOUNT_MONTH,'MM'),'Mon')
                    AND DOC_TYPE = 'PAY'
                GROUP BY sub.MONTH, sub.mon
                ORDER BY sub.mon::int
            ");
        } else {
            $getMonth = "(select TO_CHAR( ADD_MONTHS( TRUNC(SYSDATE,'year') , LEVEL-1  ), 'Mon' ) MONTH,
            TO_CHAR( ADD_MONTHS( TRUNC(SYSDATE,'year') , LEVEL-1  ), 'MM' ) mon
            FROM dual
            CONNECT BY LEVEL <= floor( months_between( sysdate, trunc(sysdate,'YYYY')))+1
            )";

            $cashOut = DB::select("select sub.month,sub.mon, COALESCE (sum(c.AMOUNT),0)  as amount from
                              ".$getMonth." sub
                             left outer join CBMAST c ON 	sub.month = to_char(TO_DATE(c.ACCOUNT_MONTH ,'MM'),'Mon')
                             and DOC_TYPE = 'REC'
                             group by  sub.MONTH,sub.mon
                             ORDER BY sub.mon" );

            $cashIn = DB::select("select sub.month,sub.mon, COALESCE (sum(c.AMOUNT),0)  as amount from
                              ".$getMonth." sub
                             left outer join CBMAST c ON 	sub.month = to_char(TO_DATE(c.ACCOUNT_MONTH ,'MM'),'Mon')
                             and DOC_TYPE = 'PAY'
                             group by  sub.MONTH,sub.mon
                             ORDER BY sub.mon");
        }

        $cashOutMonthData = (new DashboardService())->amountByMonth($cashOut);
        $cashInMonthData = (new DashboardService())->amountByMonth($cashIn);
        
        // dd($cashInMonthData);
    
        
        $cashIn = DB::table('cbmast')
            ->selectRaw('sum(amount) total,account_month')
            ->where('doc_type','=','REC')
            ->where("account_year","=",Carbon::now()->year)
            ->groupby('account_month')
            ->orderby('account_month','asc')
            ->get();

        $premium_per_intermediary = DB::table('AGMNF as a')
            ->selectRaw("a.NAME, b.DESCRIPTION, ac.ACCOUNT_YEAR, ac.ACCOUNT_MONTH")
            ->selectRaw('COALESCE(SUM(ac.unallocated), 0) as total')
            ->leftJoin('ACDET as ac', function ($join) {
                $join->on('a.AGENT', '=', 'ac.AGENT')
                    ->on('a.branch', '=', 'ac.branch')
                    ->where('ac.SOURCE', '=', 'U/W')
                    ->where('ac.account_year', '=', Carbon::now()->year)
                    ->where('ac.account_month', '=', Carbon::now()->month);
            })
            ->join('Branch as b', 'b.BRANCH', '=', 'a.BRANCH')
            ->groupBy('a.NAME', 'b.DESCRIPTION', 'ac.ACCOUNT_YEAR', 'ac.ACCOUNT_MONTH')
            ->orderBy('total', 'DESC')
            ->take(5)
            ->get();

            $banking_balances = DB::table('nlmstbal as nb')
                ->selectRaw("nb.glhead, SUM(nb.ytd_bal) as total, gb.prdesc")
                ->join('glbanks as gb', 'nb.glhead', '=', 'gb.prsno')
                ->where('gb.dash_display_bal', 'Y')
                ->where('nb.period_year', Carbon::now()->year)
                ->where('nb.PERIOD_MONTH', Carbon::now()->month)
                ->groupBy('nb.glhead', 'gb.prdesc')
                ->orderBy('total', 'desc')
                ->take(5)
                ->get();

        $top_debtors = DB::table('ACDET as a')
            ->selectRaw("SUM(a.UNALLOCATED) AS total_unallocated, c.CLIENT_NUMBER, c.NAME")
            ->join('CLIENT as c', 'a.CLIENT_NUMBER', '=', 'c.CLIENT_NUMBER')
            ->where('a.UNALLOCATED', '>', 0)
            ->where('a.DOC_TYPE', 'DRN')
            ->where('a.SOURCE', 'U/W')
            ->groupBy('c.CLIENT_NUMBER', 'c.NAME')
            ->orderByDesc('total_unallocated')
            ->take(5)
            ->get();
        

            if ($driver === 'pgsql') {
                $debtors_buckets = DB::select("
                    WITH Bands AS (
                        SELECT '1' AS band_group
                        UNION ALL
                        SELECT '2' AS band_group
                        UNION ALL
                        SELECT '3' AS band_group
                        UNION ALL
                        SELECT '4' AS band_group
                    )
                    SELECT Bands.band_group, COALESCE(SUM(ds.OUTSTANDING_AMOUNT), 0) AS total_outstanding_amount
                    FROM Bands
                    LEFT OUTER JOIN DEBTORS_SUMMARY_BUCKETS ds ON (
                        (Bands.band_group = '1' AND ds.BAND_POSITION IN (1, 2, 3))
                        OR (Bands.band_group = '2' AND ds.BAND_POSITION IN (4, 5, 6))
                        OR (Bands.band_group = '3' AND ds.BAND_POSITION IN (7, 8, 9))
                        OR (Bands.band_group = '4' AND ds.BAND_POSITION > 9)
                    )
                    GROUP BY Bands.band_group
                    ORDER BY Bands.band_group
                ");
            } else {
                $debtors_buckets = DB::select("SELECT Bands.band_group, COALESCE(SUM(ds.OUTSTANDING_AMOUNT), 0) AS total_outstanding_amount  FROM
                        (SELECT '1' AS band_group FROM DUAL
                                    UNION ALL
                                    SELECT '2' AS band_group  FROM DUAL
                                    UNION ALL
                                    SELECT '3' AS band_group  FROM DUAL
                                    UNION ALL
                                    SELECT '4' AS band_group FROM DUAL
                                ) Bands
                        LEFT OUTER JOIN DEBTORS_SUMMARY_BUCKETS ds ON (
                            (Bands.band_group = '1' AND ds.BAND_POSITION IN (1, 2, 3))
                            OR (Bands.band_group = '2' AND ds.BAND_POSITION IN (4, 5, 6))
                            OR (Bands.band_group = '3' AND ds.BAND_POSITION IN (7, 8, 9))
                            OR (Bands.band_group = '4' AND ds.BAND_POSITION > 9)
                        )
                        GROUP BY Bands.band_group
                        ORDER BY UPPER(Bands.band_group) ASC
                ");
            }

            /*$debtors_buckets = DB::table(DB::Raw("SELECT * FROM(SELECT '1' AS band_group FROM DUAL
                            UNION ALL
                            SELECT '2' AS band_group FROM DUAL
                            UNION ALL
                            SELECT '3' AS band_group FROM DUAL
                            UNION ALL
                            SELECT '4' AS band_group FROM DUAL) Bands")->getValue(DB::connection()->getQueryGrammar()))

                ->leftJoin('DEBTORS_SUMMARY_BUCKETS ds', function ($join) {
                $join->on('Bands.band_group', '=', '1')
                    ->whereRaw('ds.BAND_POSITION IN (1, 2, 3)')
                    ->orWhere(function ($query) {
                        $query->on('Bands.band_group', '=', '2')
                            ->whereRaw('ds.BAND_POSITION IN (4, 5, 6)');
                    })
                    ->orWhere(function ($query) {
                        $query->on('Bands.band_group', '=', '3')
                            ->whereRaw('ds.BAND_POSITION IN (7, 8, 9)');
                    })
                    ->orWhere(function ($query) {
                        $query->on('Bands.band_group', '=', '4')
                            ->whereRaw('ds.BAND_POSITION > 9');
                    });
                })
                ->groupBy('Bands.band_group')
                ->orderBy(DB::raw('UPPER(Bands.band_group)')->getValue(DB::connection()->getQueryGrammar()), 'ASC')
                ->selectRaw("'Bands.band_group', COALESCE(SUM(ds.OUTSTANDING_AMOUNT), 0) AS total_outstanding_amount")
                ->get();*/

        

        $outstanding_premium_balance = DB::table('ACDET')
            ->whereIn('branch', function ($query) {
                $query->select('branch')
                    ->from('branch')
                    ->where('UW_ACTIVE', 'Y');
            })
            ->sum('NETT');

        $financeData = [
            'total_unallocated_premium' =>  (object) ['count' => $total_unallocated_premium, 'label' => 'Total unallocated premium','cardImg'=>'file-invoice-dollar-solid.svg'],
            'premium_per_intermediary' => $premium_per_intermediary,
            'total_outstanding_premium'=> (object) ['count' => $total_outstanding_premium, 'label' => 'Outstanding Premium Debits/Credits','cardImg'=>'debited-total-premium.png'],
            'total_claims_outstanding'=> (object) ['count' => $total_claims_outstanding, 'label' => 'Total Claims Outstanding','cardImg'=>'money.png'],
            'receipts_curr_month'=> (object) ['count' => $receipts_curr_month, 'label' => 'Monthly Total Receipts','cardImg'=>'receipts.png'],
            'receipts_curr_ytd'=> (object) ['count' => $receipts_curr_ytd, 'label' => 'Year to Date Total Receipts','cardImg'=>'receipts.png'],
            'payments_curr_month'=>(object) ['count' => $payments_curr_month, 'label' => 'Monthly Total Payments','cardImg'=>'recovery-receipts.png'],
            'payments_curr_ytd'=>(object) ['count' => $payments_curr_ytd, 'label' => 'Year To Date Total Payments','cardImg'=>'recovery-receipts.png'],
            'cashOutMonthData'=>$cashOutMonthData,
            'cashInMonthData'=>$cashInMonthData,
            'outstanding_premium_balance'=>(object) ['count' => $outstanding_premium_balance, 'label' => 'Outstanding Premium Balance','cardImg'=>'recovery-receipts.png'],
            'banking_balances' => $banking_balances,
            'debtors_buckets' => $debtors_buckets,
            'top_debtors' => $top_debtors
        ];
    
        return $financeData;

    //    dd($cashOut);
    }
    
    #---------------------------------- Claims -----------------------------------------------------
    
    private function claimsDashboardContent($user){
        $account_period = DB::table('Dtran0')->select('account_year','account_month')->get()->first();
        $user_name = $user->user_name;
        $latest_registered_claims = collect();

        $registered_claims_count = null;
        $processed_claims_count = null;
        $paid_claims_amount = null;
        $year = Carbon::now()->year;
        $month = Carbon::now()->month;


        if(Gate::check('view-dash-own-active-claims-count')) {
            // Active claims Count in current account month for claims associates
            $active_claims_count= Clhmn::where('user_str', $user_name)
                                ->whereNull('closed')
                                ->whereNull('rejected')
                                ->whereMonth('DATE_REG', '=', $month)
                                ->count('claim_no');
        }
        if(Gate::check('view-dash-current-own-total-outstanding')) {                   

            // Current Total Outstanding in current account month for claims associates
            $current_total_outstanding = Clhmn::where('user_str', $user_name)
                                                ->where('CURR_TOTAL_ESTIMATE','>',0)
                                                ->whereMonth('DATE_REG', '=', $month)
                                                ->whereYear('DATE_REG', '=', $year)
                                                ->sum('CURR_TOTAL_ESTIMATE');
        }
        if(Gate::check('view-dash-own-claim-payments')) {     
            // Total payments in current account month for claims associates
            $total_claim_payments = Clpmn::where('user_str', $user_name)
                                            ->where('ACCOUNT_MONTH', '=', $month)
                                            ->where('ACCOUNT_YEAR', '=', $year)
                                            ->where('PAY_TYPE','=',10)
                                            ->sum('amount');
        }
        if(Gate::check('view-dash-own-recovery-receipts')) { 
            // Total Recovery receipts in current account month for claims associates
            $total_recovery_receipts = Clpmn::where('user_str', $user_name)
                                            ->where('ACCOUNT_MONTH', '=', $month)
                                            ->where('ACCOUNT_YEAR', '=', $year)
                                            ->where('PAY_TYPE','=',20)
                                            ->sum('amount');
        }

        if(Gate::check('view-dash-total-active-claims-count')) {
            // Active claims Count in current account month for claims managers view
            $active_claims_count= Clhmn::whereNull('closed')
                                        ->whereNull('rejected')
                                        ->whereMonth('DATE_REG', '=', $month)
                                        ->count('claim_no');

        }
        if(Gate::check('view-dash-current-all-total-outstanding')) {
            // Current Total Outstanding in current account month for claims managers view
            $current_total_outstanding = Clhmn::where('CURR_TOTAL_ESTIMATE','>',0)
                                        ->whereMonth('DATE_REG', '=', $month)
                                        ->whereYear('DATE_REG', '=', $year)
                                        ->sum('CURR_TOTAL_ESTIMATE');
        }
        if(Gate::check('view-dash-total-claim-payments')) {
            // Total payments in current account month for claims managers view
            $total_claim_payments = Clpmn::where('ACCOUNT_MONTH', '=', $month)
                                        ->where('ACCOUNT_YEAR', '=', $year)
                                        ->where('PAY_TYPE','=',10)
                                        ->sum('amount');
        }
        if(Gate::check('view-dash-total-recovery-receipts')) {
            // Total Recovery receipts in current account month for claims managers view
            $total_recovery_receipts = Clpmn::where('ACCOUNT_MONTH', '=', $month)
                                            ->where('ACCOUNT_YEAR', '=', $year)
                                            ->where('PAY_TYPE','=',20)
                                            ->sum('amount');
        };
        

        if (Gate::check('view-dash-own-latest-registered-claims')) {
            $latest_registered_claims = DB::table('Clhmn as c')
                                            ->select('c.USER_STR', 'c.CLAIM_NO', 'c.DATE_REG', 'c.COST_TODATE', 'client.name')
                                            ->join('client', function ($join) {
                                                $join->on('client.client_number', 'c.client_number');
                                            })
                                            ->where('cost_todate', '=', 0)
                                            ->where('c.user_str', '=', $user_name)
                                            ->orderBy('cost_todate', 'desc')
                                            ->take(5)
                                            ->get();
        }

        if (Gate::check('view-dash-latest-registered-claims')) {
            $latest_registered_claims = DB::table('Clhmn as c')
                                            ->select('c.USER_STR', 'c.CLAIM_NO', 'c.DATE_REG', 'c.COST_TODATE', 'client.name')
                                            ->join('client', function ($join) {
                                                $join->on('client.client_number', 'c.client_number');
                                            })
                                            ->where('cost_todate', '=', 0)
                                            ->orderBy('cost_todate', 'desc')
                                            ->take(5)
                                            ->get();
        }

        # registered_claims_count by the current user
        if (Gate::check('view-dash-own-registered-claims')) {
            $registered_claims_count = Clhmn::where('user_str', $user_name)
                                            ->where('claim_year', '=', Carbon::now()->format('Y'))
                                            ->count('claim_no');
        }

        # total registered_claims_count
        if (Gate::check('view-dash-total-registered-claims')) {
            $registered_claims_count = Clhmn::where('claim_year', '=', Carbon::now()->format('Y'))->count('claim_no');
        }

        # processed_claims_count by current user
        if (Gate::check('view-dash-own-processed-claims')) {
            $processed_claims_count = Clhmn::where('user_str', $user_name)
                                            ->where('claim_year', '=', Carbon::now()->format('Y'))
                                            ->where('cost_todate', '>', 0)
                                            ->count('claim_no');
        }

        # total processed_claims_count
        if (Gate::check('view-dash-total-processed-claims')) {
            $processed_claims_count = Clhmn::where('claim_year', '=', Carbon::now()->format('Y'))
                                            ->where('cost_todate', '>', 0)
                                            ->count('claim_no');
        }

        # paid_claims_amount by current user
        if (Gate::check('view-dash-own-paid-claims-amount')) {
            $paid_claims_amount = Clhmn::where('user_str', $user_name)
                                        ->where('claim_year', '=', Carbon::now()->format('Y'))
                                        ->sum('cost_todate');
        }

        # total paid_claims_amount
        if (Gate::check('view-dash-total-paid-claims-amount')) {
            $paid_claims_amount = Clhmn::where('claim_year', '=', Carbon::now()->format('Y'))->sum('cost_todate');
        }

        # claim payments per month
        if ($driver === 'pgsql') {
            $claims_payments_pm_query = DB::select("
                WITH months AS (
                    SELECT TO_CHAR(DATE_TRUNC('year', CURRENT_DATE) + (generate_series(0, EXTRACT(MONTH FROM CURRENT_DATE)::int - 1) || ' months')::interval, 'Mon') as month
                )
                SELECT sub.month, count(c.CLAIM_NO) as count
                FROM months sub
                LEFT OUTER JOIN clpmn c ON sub.month = TO_CHAR(PAY_DATE,'Mon')
                    AND c.PAY_TYPE = 10
                    AND EXTRACT(YEAR FROM PAY_DATE) = $year
                GROUP BY sub.month
            ");
        } else {
            $claims_payments_pm_query = DB::select("select sub.month,count (c.CLAIM_NO) as count from
                                                            (
                                                            select TO_CHAR( ADD_MONTHS( TRUNC(SYSDATE,'year') , LEVEL-1  ), 'Mon' ) month
                                                            FROM dual
                                                            CONNECT BY LEVEL <= floor( months_between( sysdate, trunc(sysdate,'YYYY')))+1
                                                            ) sub
                                                            left outer join clpmn c ON 	sub.month = to_char(PAY_DATE,'Mon')
                                                            AND c.PAY_TYPE = 10
                                                            AND (to_char(PAY_DATE,'YYYY') = '$year')
                                                            group by  sub.month ");
        }

        $claims_payments_pm = (new DashboardService())->claimsPaymentsByMonth($claims_payments_pm_query);

        # claims registered per month
        if ($driver === 'pgsql') {
            $claims_reg_pm_query = DB::select("
                WITH months AS (
                    SELECT TO_CHAR(DATE_TRUNC('year', CURRENT_DATE) + (generate_series(0, EXTRACT(MONTH FROM CURRENT_DATE)::int - 1) || ' months')::interval, 'Mon') as month
                )
                SELECT sub.month, count(c.CLAIM_NO) as count
                FROM months sub
                LEFT OUTER JOIN clhmn c ON sub.month = TO_CHAR(DATE_REG,'Mon')
                    AND EXTRACT(YEAR FROM DATE_REG) = $year
                GROUP BY sub.month
            ");
        } else {
            $claims_reg_pm_query = DB::select("select sub.month,count (c.CLAIM_NO) as count from
                                                        (
                                                        select TO_CHAR( ADD_MONTHS( TRUNC(SYSDATE,'year') , LEVEL-1  ), 'Mon' ) month
                                                        FROM dual
                                                        CONNECT BY LEVEL <= floor( months_between( sysdate, trunc(sysdate,'YYYY')))+1
                                                        ) sub
                                                        left outer join clhmn c ON 	sub.month = to_char(DATE_REG,'Mon')
                                                        AND (to_char(DATE_REG,'YYYY') = '$year')
                                                        group by sub.month ");
        }

        $claims_reg_pm = (new DashboardService())->claimsRegByMonth($claims_reg_pm_query);

        # claims payments last 7 days
        if ($driver === 'pgsql') {
            $claims_payments_pw_query = DB::select("
                WITH dates AS (
                    SELECT TO_CHAR(CURRENT_DATE - generate_series(0, 7), 'YYYY-MM-DD') as dt
                )
                SELECT sub.dt, count(c.CLAIM_NO) as count
                FROM dates sub
                LEFT OUTER JOIN clpmn c ON sub.dt = TO_CHAR(PAY_DATE,'YYYY-MM-DD')
                    AND c.PAY_TYPE = 10
                GROUP BY sub.dt
                ORDER BY sub.dt
            ");
        } else {
            $claims_payments_pw_query = DB::select("select sub.dt,count (c.CLAIM_NO) as count from
                                                            (
                                                            select to_char(sysdate - (level - 1),'YYYY-MM-DD') dt from dual connect by level < 8
                                                            ) sub
                                                            left outer join clpmn c ON 	sub.dt = to_char(PAY_DATE,'YYYY-MM-DD')
                                                            AND c.PAY_TYPE = 10
                                                            group by  sub.dt
                                                            ORDER BY sub.dt");
        }

        $claims_payments_pw = (new DashboardService())->claimsPayByWeek($claims_payments_pw_query);

        # claims registered last 7 days
        if ($driver === 'pgsql') {
            $claims_reg_pw_query = DB::select("
                WITH dates AS (
                    SELECT TO_CHAR(CURRENT_DATE - generate_series(0, 7), 'YYYY-MM-DD') as dt
                )
                SELECT sub.dt, count(c.CLAIM_NO) as count
                FROM dates sub
                LEFT OUTER JOIN clhmn c ON sub.dt = TO_CHAR(DATE_REG,'YYYY-MM-DD')
                GROUP BY sub.dt
                ORDER BY sub.dt
            ");
        } else {
            $claims_reg_pw_query = DB::select("select sub.dt,count (c.CLAIM_NO) as count from
                                                    (
                                                    select to_char(sysdate - (level - 1),'YYYY-MM-DD') dt from dual connect by level < 8
                                                    ) sub
                                                    left outer join clhmn c ON 	sub.dt = to_char(DATE_REG,'YYYY-MM-DD')
                                                    group by sub.dt
                                                    ORDER BY sub.dt");
        }

        $claims_reg_pw = (new DashboardService())->claimsRegByWeek($claims_reg_pw_query);

        # top classes with highest loss
        $loss_per_class = DB::table('Clhmn as cp')
                            ->selectRaw('sum(CURR_TOTAL_ESTIMATE + COST_TODATE) as total, cp.class,c.description,cr.CURRENCY')
                            ->join('class as c', function ($join) {
                                $join->on('c.class', 'cp.class');
                                })
                            ->join('currency as cr', function ($join) {
                                $join->on('cr.CURRENCY_CODE', 'cp.CURRENCY_CODE');
                                })
                            ->groupBy('cp.class','c.description','cr.CURRENCY')
                            ->orderBy('total','DESC')
                            ->take(5)
                            ->get();
        
       
        if (Gate::check('view-dash-loss-ratio-per-class')) {

            $loss_ratio_per_class = DB::table('polexp_v as a')->select('a.UW_YEAR', 'a.CLASS', 'b.description',
                                        DB::raw('sum(BASIC_PREMIUM) as sum_basic_premium'),
                                        DB::raw('sum(EARNED_PREMIUM) as earned_premium'),
                                        DB::raw('sum(TOTAL_PREMIUM) as total_premium'),
                                        DB::raw('sum(SUM_INSURED) as sum_sum_insured'),
                                        DB::raw('sum(CLAIMS_PAID) as claims_paid'),
                                        DB::raw('sum(CLAIMS_OUT) as claims_out'))
                                ->join('class as b', 'a.CLASS', '=', 'b.CLASS')
                                ->where('a.UW_YEAR', '=', (int)$account_period->account_year)
                                ->groupBy('a.UW_YEAR', 'a.CLASS', 'b.description')
                                ->get();
            // $loss_ratio_per_class = $loss_ratio_per_class->toArray(); // Convert Eloquent collection to array
            // $loss_ratio_per_class = json_encode($loss_ratio_per_class);
            // $loss_ratio_per_class = json_decode($loss_ratio_per_class, true);

        }

        $claims_data = [
            'recovery_receipts' => (object) ['count' => $total_recovery_receipts, 'label' => 'Recovery receipts','cardImg'=>'recovery-receipts.png'],   
            "claim_payments"   => (object) ['count' => $total_claim_payments, 'label' => 'Claim payments','cardImg'=>'claims-payments.png'],  
            "active_claims_count" => (object) ['count' => $active_claims_count, 'label' => 'Active claims','cardImg'=>'active-claims.png'], 
            "current_total_outstanding" => (object) ['count' => $current_total_outstanding, 'label' => 'Total outstanding','cardImg'=>'money.png'],
            "transactions" => $latest_registered_claims,
            "loss_per_class" => $loss_per_class,
            "loss_ratio_per_class" => $loss_ratio_per_class,
            "claims_payments_pm" => $claims_payments_pm,
            "claims_reg_pm" => $claims_reg_pm,
            "claims_payments_pw" => $claims_payments_pw,
            "claims_reg_pw" => $claims_reg_pw
            ];

        return $claims_data;

    }
    #---------------------------------- End of Claims ----------------------------------------------

    protected $count = 0;

    public function fetchMessages($user_id){
                # fetch polescalations
        $polescalations = collect($this->polescalationsQuery($user_id));

        #fetch claim escalations
        $claim_escalations = collect($this->claimescalationsQuery($user_id));

        # fetch pending debiting escalations
        $pend_deb_escalations = collect($this->pendingDebitEscalationQuery($user_id));

        # fetch decline debiting escalations
        $decline_escalation = $this->declineEscalationQuery($user_id);

        # renewal escalations with policies with outstanding amount
        $renewalEscalations = $this->renewalEscalationsQuery($user_id);

        $checkCreditApproved = $this->checkCreditApprovedQuery($user_id);

        $sendreqnumber = $this->sendReqNumberQuery($user_id);


        # uw requisitions
        $uwRequsition = $this->uwRequsitionQuery($user_id);
        $batchEscalations = $this->batchEscalations();
        $claimReqApprovals = $this->claimReqApprovals();
        $claimPayReqApprovals = $this->claimPayReqApprovals();

        $batchAuthorizations = $this->batchAuthorizations();

        $certreq_query = $this->certreq_query();
        $backpostreq_query = $this->backpostreq_query();

        $recrev = $this->receipt_rev_escalations();

        $recrevapproved = $this->receipt_rev_approved();

        $recrevfrom = $this->receipt_rev_from();

        $recrevdec = $this->receipt_rev_declined();

        $rejectedreq = $this->declinedreq();

        $chq_escalations = $this->cheque_escalations();

        
        $sp_escalations = $this->sprovider_escalations();
            
        $chq_escalation_approved = $this->chq_escalation_approved();

        $pv_rev_escalations = $this->pv_rev_escalations();

        $reverse_batch = $this->reverse_batch_escalations();
            
        $reverse_batch_from = $this->remove_batchrev_from_inbox();

        $reverse_batch_declined = $this->reverse_batch_declined();

        $reverse_pv_declined = $this->reverse_pv_declined();

        $reverse_pv_approved = $this->reverse_pv_approved();

        $escalateCloseBNKRecon = $this->escalateCloseBNKRecon();

        $decescalateCloseBNKRecon = $this->decescalateCloseBNKRecon();

        $appescalateCloseBNKRecon = $this->appescalateCloseBNKRecon();

        $po_fa_escalations = $this->po_fa_escalations();
        $blacklistapproval = $this->blacklistapproval();
        $unblacklistapproval = $this->unblacklistapproval();

        $budget_approvals = $this->budget_approvals();
        $budget_editing_approvals = $this->budget_editing_approvals();
        $nil_escalation_approvals = $this->nil_endt_approvals($user_id);
        $nil_escalation_approved = $this->nil_endt_approved($user_id);
        $special_sticker_approvals = $this->special_sticker_approvals($user_id);
        $special_sticker_approved = $this->special_sticker_approved($user_id);

        $escalintcreditfloatreq = $this->escalateintcreditfloatrequest($user_id);
        $escalcltcreditfloatreq = $this->escalatecltcreditfloatrequest($user_id);
        $approverRequestsData = $this->systemApproverRequests($user_id);

        $results1 = $polescalations->concat($pend_deb_escalations);
        $results2 = $results1->concat($decline_escalation);
        $results3 = $results2->concat($renewalEscalations);
        $results4 = $results3->concat($checkCreditApproved);
        $results5 = $results4->concat($uwRequsition);
        $results6 = $results5->concat($sendreqnumber);
        $results7= $results6->concat($batchEscalations);
        $results8= $results7->concat($batchAuthorizations);
        $results9= $results8->concat($certreq_query);
        $results10= $results9->concat($recrev);
        $results11= $results10->concat($recrevapproved);
        $results12= $results11->concat($recrevfrom);
        $results13= $results12->concat($recrevdec);
        $results14= $results13->concat($rejectedreq);

        $results15= $results14->concat($chq_escalations);
        $results16= $results15->concat($chq_escalation_approved);
        $results17= $results16->concat($chq_escalation_declined);
        $results18= $results17->concat($reverse_batch);
        $results19= $results18->concat($reverse_batch_from);
        $results20= $results19->concat($reverse_batch_declined);
        $results21= $results20->concat($pv_rev_escalations);
        $results22= $results21->concat($reverse_pv_declined);
        $results23= $results22->concat($reverse_pv_approved);
        $results24= $results23->concat($escalateCloseBNKRecon);
        $results25= $results24->concat($decescalateCloseBNKRecon);
        $results26= $results25->concat($appescalateCloseBNKRecon);
        $results27= $results26->concat($po_fa_escalations);
        $results28= $results27->concat($budget_approvals);
        $results29= $results28->concat($budget_editing_approvals);
        $results30 = $results29->concat($backpostreq_query);
        $results31 = $results30->concat($blacklistapproval);
        $results32 = $results31->concat($unblacklistapproval);
        $results33 = $results32->concat($nil_escalation_approvals);
        $results34 = $results33->concat($sp_escalations);
        $results35 = $results34->concat($nil_escalation_approved);
        $results36 = $results35->concat($special_sticker_approvals);
        $results37 = $results36->concat($special_sticker_approved);
        $results38 = $results37->concat($escalintcreditfloatreq);
        $results39 = $results38->concat($escalcltcreditfloatreq);
        $results40 = $results39->concat($claim_escalations);
        $results41 = $results40->concat($approverRequestsData);
        $results42= $results41->concat($claimReqApprovals);
        $results43= $results42->concat($claimPayReqApprovals);

        #retrieve only required number of records
        
        $sorted_inbox_data = collect($results43)->sortBy('updated_at')->reverse();

        return $sorted_inbox_data;
    }
    
    public function getInboxData($user_id) {
        $inbox_length = 5;

        $messages = $this->fetchMessages($user_id);

        if (!$messages) {
        return []; // Return empty array if null
        }

        $inbox_data = $messages->slice(0,$inbox_length)->all();

        //dd($inbox_data);
        return $inbox_data;
    }

    public function viewAllInbox(Request $request) {
        // Get user_id from either route parameter or query string
        $user_id = $request->user_id ?? $request->route('user_id') ?? auth()->user()->username;
        
        // Fallback if still not found
        if (!$user_id) {
            abort(403, 'User not specified');
        }

        $messages = collect($this->fetchMessages($user_id, PHP_INT_MAX));
        
        return view('view_all_inbox', compact('messages'));
    }

    protected function polescalationsQuery($user_id)
    {
        $driver = DB::getDriverName();
        $polescalations = [];

        if ($driver === 'pgsql') {
            $polescalations = DB::select(
                "select e.endorse_no as unique_ref,e.created_at,e.user_name,e.approved,e.description,c.name,
                CASE WHEN e.APPROVED != 'Y' OR e.APPROVED IS NULL THEN 'policy_functions'
                ELSE '' END AS action_link
                from escalate_pol e
                join dcontrol f on e.endorse_no=f.endt_renewal_no
                join client c on c.client_number=e.client_name
                left join debitmast d on e.endorse_no=d.endt_renewal_no
                where d.endt_renewal_no is null
                and e.sent_to = ?
                and e.re_escalate_date is null
                and e.declined_date is null and f.cancelled <> 'Y' ",
                [$user_id]
            );
        } else {
            $polescalations = DB::select(
                "select e.endorse_no as unique_ref,e.created_at,e.user_name,e.approved,e.description,c.name,
                CASE WHEN e.APPROVED != 'Y' OR e.APPROVED IS NULL THEN 'policy_functions'
                ELSE '' END AS action_link
                from escalate_pol e
                join dcontrol f on e.endorse_no=f.endt_renewal_no
                join client c on c.client_number=e.client_name
                left join debitmast d on e.endorse_no=d.endt_renewal_no
                where d.endt_renewal_no is null
                and e.sent_to = ?
                and e.re_escalate_date is null
                and e.declined_date is null and f.cancelled <> 'Y' ",
                [$user_id]
            );
        }

        return $polescalations;
    }


    protected function claimescalationsQuery($user_id)
    {
        $driver = DB::getDriverName();
        $claim_escalations = [];

        if ($driver === 'pgsql') {
            $claim_escalations = DB::select(
                "select e.claim_no as unique_ref,e.created_at,e.user_name,e.approved,e.description,
                CASE WHEN e.APPROVED != 'Y' OR e.APPROVED IS NULL THEN 'claims'
                ELSE '' END AS action_link
                from escalate_pol e
                join clhmn ch on e.claim_no=ch.claim_no
                WHERE e.sent_to = ?
                and e.re_escalate_date is null
                and e.declined_date is NULL
                and (e.APPROVED IS NULL or e.APPROVED != 'Y')",
                [$user_id]
            );
        } else {
            $claim_escalations = DB::select(
                "select e.claim_no as unique_ref,e.created_at,e.user_name,e.approved,e.description,
                CASE WHEN e.APPROVED != 'Y' OR e.APPROVED IS NULL THEN 'claims'
                ELSE '' END AS action_link
                from escalate_pol e
                join clhmn ch on e.claim_no=ch.claim_no
                WHERE e.sent_to = ?
                and e.re_escalate_date is null
                and e.declined_date is NULL
                and (e.APPROVED IS NULL or e.APPROVED != 'Y')",
                [$user_id]
            );
        }

        return $claim_escalations;
        // dd($claim_escalations);
    }


    protected function pendingDebitEscalationQuery($user_id)
    {
        $driver = DB::getDriverName();

        if ($driver === 'pgsql') {
            $pend_deb_escalations = DB::select(
                "select e.endorse_no as unique_ref,e.created_at,e.approved_date,f.user_name as user_name,
                    e.approved,e.description,c.name,
                    CASE WHEN e.APPROVED = 'Y' THEN 'policy_functions'
                    ELSE '' END AS action_link
                    from escalate_pol e
                    join dcontrol f on  e.endorse_no=f.endt_renewal_no
                    join client c on c.client_number=e.client_name
                    left join debitmast d on e.endorse_no=d.endt_renewal_no
                    left  join aimsusers f on  e.sent_to = f.user_id
                    where d.endt_renewal_no is null and e.sent_by = ?
                    and e.re_escalate_date is null and e.approved = 'Y' and f.cancelled <> 'Y' ",
                [$user_id]
            );
        } else {
            $pend_deb_escalations = DB::select(
                "select e.endorse_no as unique_ref,e.created_at,e.approved_date,f.user_name as user_name,
                    e.approved,e.description,c.name,
                    CASE WHEN e.APPROVED = 'Y' THEN 'policy_functions'
                    ELSE '' END AS action_link
                    from escalate_pol e
                    join dcontrol f on  e.endorse_no=f.endt_renewal_no
                    join client c on c.client_number=e.client_name
                    left join debitmast d on e.endorse_no=d.endt_renewal_no
                    left  join aimsusers f on  e.sent_to = f.user_id
                    where d.endt_renewal_no is null and e.sent_by = ?
                    and e.re_escalate_date is null and e.approved = 'Y' and f.cancelled <> 'Y' ",
                [$user_id]
            );
        }

        return $pend_deb_escalations;

    }

    protected function declineEscalationQuery($user_id){
        $driver = DB::getDriverName();

        if ($driver === 'pgsql') {
            $decline_escalation = DB::select(
                "select e.endorse_no as unique_ref,e.created_at,e.user_name,e.approved,e.description,c.name
                from escalate_pol e join client c on c.client_number=e.client_name
                left join debitmast d on e.endorse_no=d.endt_renewal_no
                where d.endt_renewal_no is null and e.sent_to = ?
                and e.re_escalate_date is null and e.declined_date is not null ",
                [$user_id]
            );
        } else {
            $decline_escalation = DB::select(
                "select e.endorse_no as unique_ref,e.created_at,e.user_name,e.approved,e.description,c.name
                from escalate_pol e join client c on c.client_number=e.client_name
                left join debitmast d on e.endorse_no=d.endt_renewal_no
                where d.endt_renewal_no is null and e.sent_to = ?
                and e.re_escalate_date is null and e.declined_date is not null ",
                [$user_id]
            );
        }

        return $decline_escalation;
    }

    protected function escalateintcreditfloatrequest($user_id){

        $cred = DB::table('intermediary_credit_hist')
                                ->selectRaw("intermediary_credit_hist.*,intermediary_credit_hist.reference as unique_ref, 'INTERMEDIARY CREDIT FLOAT APPROVAL' AS description")
                                ->where('action_by',Auth::user()->user_name)
                                ->where('status','PENDING')
                                ->get()
                                ->map(function ($item) {
                                    $item->action_link = 'int_credit_float';
                                    return $item;
                                });

        // $item = DB::select(
        //     "select e.req_no as unique_ref,e.created_at,e.user_name,e.approved,e.description
        //     from escalate_pol e join intermediary_credit_hist c on c.reference=e.req_no
        //     where e.sent_to = '$user_id' 
        //     and e.re_escalate_date is NULL
        //     and e.declined_date is NULL
        //     AND c.status='PENDING'")
            // ->map(function ($item) {
            //     $item->action_link = 'int_credit_float';
            //     return $item;
            // });

        return $cred;
    }

    protected function escalatecltcreditfloatrequest($user_id){

        $cred = DB::table('client_credit_hist')
                                ->selectRaw("client_credit_hist.*,client_credit_hist.reference as unique_ref, 'CLIENT CREDIT FLOAT APPROVAL' AS description")
                                ->where('action_by',Auth::user()->user_name)
                                ->where('status','PENDING')
                                ->get()
                                ->map(function ($item) {
                                    $item->action_link = 'clt_credit_float';
                                    return $item;
                                });

        // $item = DB::select(
        //     "select e.req_no as unique_ref,c.branch,c.agent,e.created_at,e.user_name,e.approved,e.description
        //     from escalate_pol e join client_credit_hist c on c.reference=e.req_no
        //     where e.sent_to = '$user_id' 
        //     and e.re_escalate_date is NULL
        //     and e.declined_date is NULL
        //     AND c.status='PENDING'")->get();

        //dd($item);
            // ->map(function ($item) {
            //     $item->action_link = 'clt_credit_float';
            //     return $item;
            // });

        return $cred;
    }

    protected function systemApproverRequests(){

        $currentUser = Auth::user()->user_id;
            
        // selectRaw("client_credit_hist.*,client_credit_hist.reference as unique_ref,
        //  'CLIENT CREDIT FLOAT APPROVAL' AS description");
         
        $approverRequests = DB::table('approval_request_approvers')
            ->select(
            'approval_request_approvers.id',
            'approval_request_approvers.sequence as approval_step_sequence',
            'approval_types.name as approval_type',
            'approval_types.slug as approval_type_slug',
            'approval_processes.name as approval_process',
            'approval_processes.id as approval_process_id',
            'approval_steps.name as approval_step',
            'approval_request_approvers.status as approval_status_id',
            'approval_statuses.name as approval_status',
            'approval_transactions.primary_display_column',
            DB::raw("approval_types.name ||'-'||approval_transactions.primary_display_column  as unique_ref"),
            'aimsusers.name as user_name',
            'approval_request_id',
            'approval_request_approvers.updated_at',
            'approval_request_approvers.created_at',
            DB::raw("'global_approval_requests' as action_link")
        )
        ->join('approval_requests', 'approval_request_approvers.approval_request_id', '=', 'approval_requests.id')
        ->join('approval_types', 'approval_requests.approval_type_id', '=', 'approval_types.id')
        ->join('approval_processes', 'approval_requests.approval_process_id', '=', 'approval_processes.id')
        ->join('approval_steps', 'approval_request_approvers.approval_step_id', '=', 'approval_steps.id')
        ->join('approval_statuses', 'approval_request_approvers.status', '=', 'approval_statuses.id')
        ->join('approval_transactions', 'approval_requests.approval_transaction_id', '=', 'approval_transactions.id')
        ->join('aimsusers', 'approval_requests.initiator_id', '=', 'aimsusers.user_id')
        ->where('approval_request_approvers.approver_id', $currentUser)
        ->where('approval_request_approvers.status',ApprovalRequestStatus::Pending)
        ->orderBy('approval_request_approvers.approval_request_id','desc')
        ->orderBy('approval_request_approvers.sequence')
        ->get();

        // dd($approverRequests);
        return $approverRequests;
    }


    protected function renewalEscalationsQuery($user_id){
        $driver = DB::getDriverName();
        if ($driver === 'pgsql') {
            $renewalescalations = DB::select(
                "select e.policy_no as unique_ref, e.endorse_no, e.created_at, e.user_name, e.approved, e.description, c.name,\nCASE WHEN e.APPROVED != 'Y' THEN 'endorse_functions' ELSE '' END AS action_link\nfrom escalate_pol e\nleft join debitmast d on e.endorse_no = d.endt_renewal_no\nleft join client c on c.client_number = d.client_number\nwhere e.escalate_type = '1'\nand e.approved <> 'Y'\nand e.re_escalate_date is null\nand e.declined_date is null\nand e.sent_to = ?",
                [$user_id]
            );
        } else {
            $renewalescalations = DB::select(
                "select e.policy_no as unique_ref, e.endorse_no, e.created_at, e.user_name, e.approved, e.description, c.name, CASE WHEN e.APPROVED != 'Y' THEN 'endorse_functions' ELSE '' END AS action_link from escalate_pol e left join debitmast d on e.endorse_no = d.endt_renewal_no left join client c on c.client_number = d.client_number where e.escalate_type = '1' and e.approved <> 'Y' and e.re_escalate_date is null and e.declined_date is null and e.sent_to = ?",
                [$user_id]
            );
        }
        return $renewalescalations;
    }

    protected function sendReqNumberQuery($user_id){
        $driver = DB::getDriverName();
        if ($driver === 'pgsql') {
            $sendreqnumber = DB::select(
                "select distinct a.req_no, f.policyholder, a.user_name, a.sent_by, a.sent_to, c.user_name as nem, e.name, a.claim_no, a.description, a.created_at, e.cancelled, CASE WHEN (a.claim_no is null or a.claim_no = '') THEN 'policy_functions' ELSE 'claims' END AS action_link, CASE WHEN (a.claim_no is null or a.claim_no = '') THEN a.endorse_no ELSE a.claim_no END AS unique_ref from escalate_pol a join payreqst e on (trim(a.req_no) = trim(e.req_no)) join clhmn f on a.claim_no = f.claim_no join aimsusers c on (trim(a.sent_by) = trim(c.user_id)) where trim(a.sent_to) = ? and (a.type = 'REQ') and (e.cancelled is null and e.approved_by is null)",
                [$user_id]
            );
        } else {
            $sendreqnumber = DB::select(
                "select distinct a.req_no, f.policyholder, a.user_name, a.sent_by, a.sent_to, c.user_name as nem, e.name, a.claim_no, a.description, a.created_at, e.cancelled, CASE WHEN (a.claim_no is null or a.claim_no = '') THEN 'policy_functions' ELSE 'claims' END AS action_link, CASE WHEN (a.claim_no is null or a.claim_no = '') THEN a.endorse_no ELSE a.claim_no END AS unique_ref from escalate_pol a join payreqst e on (trim(a.req_no) = trim(e.req_no)) join clhmn f on a.claim_no = f.claim_no join aimsusers c on (trim(a.sent_by) = trim(c.user_id)) where trim(a.sent_to) = ? and (a.type = 'REQ') and (e.cancelled is null and e.approved_by is null)",
                [$user_id]
            );
        }
        return $sendreqnumber;
    }

    protected function checkCreditApprovedQuery($user_id){
        $driver = DB::getDriverName();
        if ($driver === 'pgsql') {
            $checkcreditapproved = DB::select(
                "select e.endorse_no, e.created_at, e.user_name, e.description, e.type from escalate_pol e where e.sent_to = ? and e.approved is null and e.type = 'CRD' and re_escalate_date is null",
                [$user_id]
            );
        } else {
            $checkcreditapproved = DB::select(
                "select e.endorse_no, e.created_at, e.user_name, e.description, e.type from escalate_pol e where e.sent_to = ? and e.approved is null and e.type = 'CRD' and re_escalate_date is null",
                [$user_id]
            );
        }
        return $checkcreditapproved;
    }

    protected function uwRequsitionQuery($user_id){
        $driver = DB::getDriverName();
        if ($driver === 'pgsql') {
            $uwreq = DB::select(
                "select distinct a.endorse_no, a.user_name, a.claim_no, a.sent_by, a.sent_to, c.user_name as nem, a.req_no, a.claim_no, a.description, a.created_at, e.cancelled, a.escalate_id from escalate_pol a join payreqst e on (trim(a.req_no) = trim(e.req_no)) join aimsusers c on (trim(a.sent_by) = trim(c.user_id)) where trim(a.sent_to) = ? and (a.type = 'U/W REQ' or a.type = 'REQ') and (e.cancelled is null and e.approved_by is null) and a.claim_no is null",
                [$user_id]
            );
        } else {
            $uwreq = DB::select(
                "select distinct a.endorse_no, a.user_name, a.claim_no, a.sent_by, a.sent_to, c.user_name as nem, a.req_no, a.claim_no, a.description, a.created_at, e.cancelled, a.escalate_id from escalate_pol a join payreqst e on (trim(a.req_no) = trim(e.req_no)) join aimsusers c on (trim(a.sent_by) = trim(c.user_id)) where trim(a.sent_to) = ? and (a.type = 'U/W REQ' or a.type = 'REQ') and (e.cancelled is null and e.approved_by is null) and a.claim_no is null",
                [$user_id]
            );
        }
        return $uwreq;
    }

    protected function batchEscalations(){
        
        $escalated_batch = DB::table('glbatch a')
        ->selectRaw("a.escalated_date, a.batch_title, a.escalted_by, a.local_batch_amount, a.batch_no as unique_ref, a.approved, a.declined, a.authorized, a.authorized_by, a.approved_by, a.escalted_to, 
        'APPROVE ESCALATED BATCH' AS description")
        ->join('aimsusers','aimsusers.user_id','=','a.escalted_to')
        ->where(['a.approved' => '0', 'a.escalted_to'=>Auth::user()->user_id])
        ->where(['a.declined' => '0'])
        ->where(['a.authorized' => '1'])
        ->get()
        ->map(function ($item) {
                $item->action_link = 'approve_batch';
                return $item;
            });
           
        return $escalated_batch;
    }


    public function claimReqApprovals() {
        $incoming = DB::table('approval_flow a')->join('approvals e', function($join)
            {
                $join->on('e.approval_id','=','a.approval_id');
            })
            ->join('aimsusers u', function($join)
            {
                $join->on('u.user_id','=','e.user_id');
            })
            ->where('a.approver_id', Auth::user()->user_id)
            ->where('a.status', 'P')
            ->where('e.type', 'REQAP')
            ->where('a.start_approval', 'Y')
            ->get([
                'a.approval_id', 'e.claim_no as unique_ref', 'u.user_name', 'e.date_created as created_at', 'e.type',
                 'e.claim_no', 'e.description','e.req_no'
            ])->map(function ($item) {
                $item->action_link = 'claims.req_process';
                return $item;
            });

        return $incoming;
    }

    public function claimPayReqApprovals() {
        $incoming = DB::table('approval_flow a')->join('approvals e', function($join)
            {
                $join->on('e.approval_id','=','a.approval_id');
            })
            ->join('aimsusers u', function($join)
            {
                $join->on('u.user_id','=','e.user_id');
            })
            ->where('a.approver_id', Auth::user()->user_id)
            ->where('a.status', 'P')
            ->where('e.type', 'PYQAP')
            ->where('a.start_approval', 'Y')
            ->get([
                'a.approval_id', 'e.claim_no as unique_ref', 'u.user_name', 'e.date_created as created_at', 'e.type',
                 'e.claim_no', 'e.description','e.req_no'
            ])->map(function ($item) {
                $item->action_link = 'payments.requisitions.details';
                return $item;
            });

        return $incoming;
    }

    protected function batchAuthorizations(){
        $authorize_batch = DB::table('glbatch a')
            ->selectRaw(" a.escalated_date, a.batch_title, a.escalted_by, a.local_batch_amount, a.batch_no as unique_ref, a.approved, a.declined, a.authorized, a.authorized_by, a.approved_by, a.escalted_to, 
                'AUTHORIZE ESCALATED BATCH' AS description")
            ->join('aimsusers g','g.user_id','=','a.authorized_by')
            ->where(['a.approved' => '0', 'a.authorized_by'=>Auth::user()->user_id])
            ->where(['a.declined' => '0'])
            ->where(['a.authorized' => '0'])
            ->get()
            ->map(function ($item) {
                $item->action_link = 'authorize_batch';
                return $item;
            });

        return $authorize_batch;
    }

    protected function certreq_query(){
        
         $certificatereqs = DB::table('certreq')
                                ->selectRaw("certreq.*,certreq.requisition_no as unique_ref, 'CERTIFICATE REQUISITION APPROVAL' AS description, raised_date as created_at")
                                ->where('escalate_to',Auth::user()->user_name)
                                ->where(function($query){
                                    $query->whereNotIn('approval_flag',['Y','y'])
                                        ->orWhereNull('approval_flag');
                                })
                                ->where(function($query){
                                    $query->whereNotIn('declined_flag',['Y','y'])
                                        ->orWhereNull('declined_flag');
                                })
                                ->where(function($query){
                                    $query->whereNotIn('cancel_flag',['Y','y'])
                                        ->orWhereNull('cancel_flag');
                                })->get()
                                ->map(function ($item) {
                                    $item->action_link = 'cert_request_view';
                                    return $item;
                                });
                                
        return $certificatereqs;
    }

    protected function backpostreq_query(){
        
        $bpreq = DB::table('backpostdate_requests')
                               ->selectRaw("backpostdate_requests.*,backpostdate_requests.requisition_no as unique_ref, 'BACKDATE/POSTDATE REQUISITION APPROVAL' AS description, requisition_date as created_at")
                               ->where('escalate_to',Auth::user()->user_name)
                               ->where('status_flag','P')
                            //    ->where(function($query){
                            //        $query->whereNotIn('status_flag',['P'])
                            //            ->orWhereNull('status_flag');
                            //    })
                               ->get()
                               ->map(function ($item) {
                                   $item->action_link = 'backpostdate_view';
                                   return $item;
                               });
          //dd($bpreq);               
       return $bpreq;
   }




    

    public function brian()
    {
        //return workflowupdate_start_no_link('221','AB4');
        return workflowupdate_subsequent('AB4');
        //return workflowupdate_subsequent('0330709032274201701');
        //john 15:13|romans 3:23
    }

    public function inbox(Request $request)
    {
        
        if($request->ajax()){
            
            try {
                $user_id = Auth::user()->user_id;
    
                $users_group = UserAndGroups::where('user_id', $user_id)->get();
    
                //$inbox = MailBox::where('user_id','=',$user_name)->where('status','=','incoming')->get();
    
                /* $sql = "SELECT MAILBOX_ESCALATIONS.*,AIMSUSER.USER_ID,AIMSUSER.USER_NAME
                FROM MAILBOX_ESCALATIONS,AIMSUSER WHERE MAILBOX_ESCALATIONS.FROM_USER_ID = AIMSUSER.USER_ID AND
                MAILBOX_ESCALATIONS.STATUS = 'incoming' AND USER_GROUP IN (SELECT GROUP_ID FROM USER_AND_GROUPS WHERE USER_ID = '$user_id')";*/
    
                //$users = DB::connection('mysql2')->select(...);
    
                $schem = schemaName();
    
                $gb = $schem['gb'];
                $gl = $schem['gl'];
                $common = $schem['common'];
    
                $sql =
                    'select m.id as mailbox_id,m.*,e2.description,u.user_name,e.reference_variable,ug.group_description,e.id,W.SEND_TO,e2.persons_responsible,e2.link_redirect as lr2 from ' .
                    $common .
                    '.mailbox m left join ' .
                    $common .
                    ".aimsusers u  on (m.from_user_id=u.user_id)
                              left join " .
                    $gb .
                    ".escalations e on (m.stage=e.code and m.business_process=e.category)
                              left join " .
                    $gb .
                    ".workflowsconfig w on (w.escalation_id=e.id)
                              left join " .
                    $gb .
                    ".escalations e2 on (e2.id=W.SEND_TO)
                              left join " .
                    $common .
                    ".Aimsgroup ug on (ug.aims_group=e2.persons_responsible)
                              where  e.code=m.stage and e.category=m.business_process  and m.status='incoming'
                              
                              order by m.date_sent,m.time_sent desc";
    
                //and ((w.yes_no=m.yes_no and w.type='condition') or w.type<>'condition')
    
                // $sql="select m.id as mailbox_id,m.*,e2.description,u.user_name,e.reference_variable,ug.name,e.id,W.SEND_TO,e2.persons_responsible,e2.link_redirect as lr2 from ".$common.".mailbox m
                //               left join ".$common.".aimsuser_web u  on (m.from_user_id=u.user_id)
                //              left join ".$gb.".escalations e on (m.stage=e.code and m.business_process=e.category)
                //               left join ".$gb.".workflowsconfig w on (w.escalation_id=e.id)
                //               left join ".$gb.".escalations e2 on (e2.id=w.send_to)
                //               left join ".$common.".user_groups ug on (ug.id=e2.persons_responsible)
                //               where  e.code=m.stage and e.category=m.business_process and m.status='incoming'
    
                //               order by m.date_sent,m.time_sent desc";
                //and ((w.yes_no=m.yes_no and w.type='condition') or w.type<>'condition')
                // where m.from_user_id='$user_id' and m.status='approved'
    
                $inbox = DB::select($sql);

    
                $mailbox = [];
    
                foreach ($inbox as $key => $mail) {
                    $client = Client::where('client_number', $mail->reference_number)->get(['name']);
    
                    $name = $client[0]->name;
    
                    $mail->client_name = $name;
    
                    foreach ($users_group as $key => $group) {
                        if ($mail->persons_responsible == $group->group_id) {
                            $x = Escalations::where('id', '=', $mail->send_to)->get();
    
                            $escalation = Escalations::where('code', '=', $mail->stage)
                                ->where('category', '=', $mail->business_process)
                                ->orderBy('code', 'asc')
                                ->first();
    
                            $object_id = $x[0]->checker_object_id;
    
                            $count = ObjectParams::where('object_id', '=', $object_id)->count();
    
                            if ($count > 0) {
                                $mail->checker = 'Y';
                            } else {
                                $mail->checker = 'N';
                            }
    
                            array_push($mailbox, $mail);
                        }
                    }
                }
                // $pols = $this->polescalationsQuery();
               
    
                $polescalations = DB::select(
                    "select e.endorse_no,e.created_at,e.user_name,e.approved,e.description,c.name from escalate_pol e
                join dcontrol f on  e.endorse_no=f.endt_renewal_no
                join client c on c.client_number=e.client_name left join debitmast d on e.endorse_no=d.endt_renewal_no where d.endt_renewal_no is null and e.sent_to = '$user_id' and e.re_escalate_date is null and e.declined_date is null and f.cancelled <> 'Y' ",
                );
                // dd($polescalations)
    
                $decline_escalation = DB::select("select e.endorse_no,e.created_at,e.user_name,e.approved,e.description,c.name from escalate_pol e join client c on c.client_number=e.client_name left join debitmast d on e.endorse_no=d.endt_renewal_no where d.endt_renewal_no is null and e.sent_to = '$user_id' and e.re_escalate_date is null and e.declined_date is not null ");
                $pend_deb_escalation = DB::select(
                    "select e.endorse_no,e.created_at,e.user_name,e.approved_date,f.user_name as names,
                                                        e.approved,e.description,c.name from escalate_pol e
                                                        join dcontrol f on  e.endorse_no=f.endt_renewal_no
                                                        join client c on c.client_number=e.client_name
                                                        left join debitmast d on e.endorse_no=d.endt_renewal_no
                                                        left  join aimsusers f on  e.sent_to = f.user_id
                                                        where d.endt_renewal_no is null and e.sent_by = '$user_id' and e.re_escalate_date is null and e.approved = 'Y' and f.cancelled <> 'Y' ",
                );
    
                
    
                $renewalinbox = DB::select(
                    "select e.policy_no,e.endorse_no,e.created_at,e.user_name,e.approved,e.description,c.name from escalate_pol e
                        left join debitmast d on e.endorse_no=d.endt_renewal_no
                        left join client c on c.client_number=d.client_number
                        where e.escalate_type ='1'
                        and e.approved <> 'Y'
                        and e.re_escalate_date is null
                        and e.declined_date is null
                        and e.sent_to = '$user_id'",
                );
               
                $sendreqnumber = DB::select(
                    "select distinct a.req_no, f.policyholder, a.user_name,a.sent_by,a.sent_to, c.user_name as nem, e.name,a.claim_no,a.description,a.created_at,e.cancelled from escalate_pol a
                join payreqst e on (trim(a.req_no)=trim(e.req_no))
                join clhmn f on a.claim_no = f.claim_no
                join aimsusers c on ( trim(a.sent_by) = trim(c.user_id) ) where trim(a.sent_to) = '$user_id' and (a.type = 'REQ')
                 and (e.cancelled is null and e.approved_by is null )",
                );

                $rejectedreq = $this->declinedreq();
                $nilescalations = $this->nil_endt_approvals($user_id);
    
                ##inbox for credit approval
                $checkcreditapproved = DB::select(
                    "select e.endorse_no,e.created_at,e.user_name,e.description,e.type from escalate_pol e
                where e.sent_to ='$user_id' and e.approved is null and e.type = 'CRD' and re_escalate_date is null",
                );
    
                // $uwreq= DB::table(''.$gb.'.escalate_pol')
                //                         ->join(''.$gb.'.dcontrol', function($join)
                //                         {
                //                             $join->on('escalate_pol.endorse_no','=','dcontrol.endt_renewal_no');
                //                         }) ->join(''.$gb.'.aimsuser_web', function($join)
                //                         {
                //                             $join->on('aimsuser_web.user_id','=','escalate_pol.sent_by');
                //                         })
                //                         ->where('escalate_pol.type','=','U/W REQ')
                //                         ->where('escalate_pol.sent_to','=',$user_id)
                //                         //->whereNull('dcontrol.approved_by')
                //                        // ->where('dcontrol.req_approved','!=','Y')
                //                         ->whereNull('dcontrol.req_approved')
                //                         ->orderBy('escalate_pol.created_at','desc')
                //                         ->get();
    
                // $ureq=DB::select( DB::raw("select a.endorse_no, a.user_name,a.claim_no,a.sent_by,a.sent_to, c.user_name as nem, a.req_no,a.claim_no,a.description,a.created_at,e.cancelled,a.escalate_id
                //                     from escalate_pol a
                //                     join payreqst e on (trim(a.req_no)=trim(e.req_no))
                //                     join aimsuser_web c on ( trim(a.sent_by) = trim(c.user_id) ) where trim(a.sent_to) = '$user_id' and (a.type = 'REQ')
                //                     and (e.cancelled is null and e.approved_by is null ) and a.claim_no is null"));
                $uwreq = DB::select(
                    "select distinct a.endorse_no, a.user_name,a.claim_no,a.sent_by,a.sent_to, c.user_name as nem, a.req_no,a.claim_no,a.description,a.created_at,e.cancelled,a.escalate_id
                                    from escalate_pol a
                                    join payreqst e on (trim(a.req_no)=trim(e.req_no))
                                    join aimsusers c on ( trim(a.sent_by) = trim(c.user_id) ) where trim(a.sent_to) = '$user_id' and (a.type = 'U/W REQ' or a.type = 'REQ' )
                                    and (e.cancelled is null and e.approved_by is null ) and a.claim_no is null",
                );

                
            $escalated_batch = $this->batchEscalations();

            // autorize batch
            $authorize_batch = $this->batchAuthorizations();
            
            // end authorize
            // $escalated_period = DB::table('period_approval')
            // ->select('*')
            // ->join('aimsuser_web','aimsuser_web.user_id','=','period_approval.escalated_to')
            // ->where(['period_approval.approved' => '0', 'period_approval.escalated_to'=>Auth::user()->user_id])
            // ->where(['period_approval.declined' => '0'])
            // // ->get()
            // ;

            $escalated_period = DB::select("select a.batch_no,a.escalated_by,a.escalated_date from period_approval a 
            where a.escalated_to = '$user_id'
            and a.approved='0' and a.declined='0' ");

            $escalated_period_from = DB::select( "select a.batch_no,a.escalated_by,a.escalated_date from period_approval a 
            where a.escalated_by = '$user_id' and a.approved='1' and a.read='0' ");

            
            $reverse_batch = $this->reverse_batch_escalations();
            
            $reverse_batch_from = $this->remove_batchrev_from_inbox();

            $escalated_batch_from = DB::select( "select a.batch_no,a.escalted_by,a.escalted_to,a.batch_title,a.approved_date,a.local_batch_amount from glbatch a 
            where a.escalted_by = '$user_id'
            and a.approved='1' and a.read='0' ");

            
            $declined_batch = DB::select("select a.batch_no,a.escalted_to,a.escalted_by,a.batch_title,a.approved_date,a.local_batch_amount from glbatch a 
            where a.escalted_by = '$user_id'
            and a.declined='1' and a.read='0' ");

            $escaledbnkreconclose = DB::select(
                "select e.req_no,e.created_at,e.type,e.user_name,e.sent_by,e.description from escalate_pol e
                JOIN GLBANKS g ON e.REQ_NO = g.BANK_ACC_CODE
                where e.sent_to ='$user_id' and e.approved is null and e.type = 'BNK RECON' and e.reescalated is NULL AND g.C_APPROVED ='N'",
            );

            #blacklistvehicles approvals
            $blacklistapproval = DB::select("select * from escalate_pol e where e.type='BLK' and e.sent_to='$user_id' and e.approved='N'");

            #unblacklistvehicles approvals
            $unblacklistapproval = DB::select("select * from escalate_pol e where e.type='UNBLK' and e.sent_to='$user_id' and e.approved='N'");

            #claim approvals
            $claim_approvals = DB::select("select * from escalate_pol e where e.type='FNT' and e.sent_to='$user_id' and e.approved='N'");

            $certificatereqs = $this->certreq_query();
            $bpdatereqs = $this->backpostreq_query();
          
            ##rec rev escalations
            $recrev = $this->receipt_rev_escalations();
            $blacklistapproval = $this->blacklistapproval();
            $unblacklistapproval = $this->unblacklistapproval();
          
                //REVERSAL APPROVED
            $recrevapproved = $this->receipt_rev_approved();

            $recrevfrom = $this->receipt_rev_from();  
            
            //     //reversal declined
            $recrevdec = $this->receipt_rev_declined(); 

            ##cheque cancellation approval
            $chq_escalations = $this->cheque_escalations();
            
            $chq_escalation_approved = $this->chq_escalation_approved();

            $chq_escalation_declined = $this->chq_escalation_declined(); 

            $pv_rev_escalations = $this->pv_rev_escalations(); 

            $reverse_pv_declined = $this->reverse_pv_declined();

            $reverse_pv_approved = $this->reverse_pv_approved();
          

            

            $reverse_batch_declined = $this->reverse_batch_declined();

            $escalintcreditfloatreq = $this->escalateintcreditfloatrequest($user_id);
            $escalcltcreditfloatreq = $this->escalatecltcreditfloatrequest($user_id);

            



                $escalations = Escalations::all();
                $workflows = Workflows::all();
                //$inbox = MailBox::where('status','=','incoming')->orWhere('status','=','complete')->get();
                // $data = [
                //     $uwreq,
                //     $mailbox,
                //     $escalations,
                //     // $ureq,
                //     $sendreqnumber,
                //     $workflows,
                //     $polescalations,
                //     $user_id,
                //     $checkcreditapproved,
                //     $checkcreditapproved,
                //     $pend_deb_escalation,
                //     $decline_escalation,
                //     $renewalinbox,
                // ];

                $data = [
                    $uwreq,$mailbox,$escalations,$ureq,$Payreq,
                    $sendreqnumber,$workflows,
                    $polescalations,$user_id,
                    $nilescalations,
                    $checkcreditapproved,$checkcreditapproved,
                    $pend_deb_escalation,$decline_escalation,
                    $renewalinbox,$showclaimescalations,
                    $poldocprintinbox,
                    //RECEIPT REVERSAL
                    $recrev,//reversal requests
                    $recrevapproved, //approved reversal
                    $recrevdec,
                    $recrevfrom,
                    //END OF RECEIPT REVERSAL
                    
                    //PV REVERSAL
                    $pvrev,//reversal requests
                    $pvrevapproved, //approved reversal
                    $pvrevdec,
                    $recepts,
                    $receipts_to,
                    $receipts_from,
                    $escalated_batch,
                    $escalated_batch_from,
                    $declined_batch,
                    $escaledbnkreconclose,
                    $escalated_period,
                    $escalated_period_from,
                    $reverse_batch,
                    $reverse_batch_from,
                    $rejectedreq,
                    
                    //END OF PV REVERSAL
                    $escalate_vendor,
                    // $vendor_creation_escalation,
                    $authorize_batch,
                    $approve_batch,
                    $vendor_creation_escalation_rejected,
                    $vendor_creation_resend,
                    $upd_vendor_escalation_rejected,
                    $uwclmreq,
                    $certificatereqs, // CERTIFICATE REQUISITIONS
                    $bpdatereqs,  //BACKDATING / POSTDATING REQUISITIONS
                    $chq_escalations,
                    $escalintcreditfloatreq,
                    $escalcltcreditfloatreq
                ];
                


                $tbody = '';
          
                if(count($escalated_batch_from) > 0 ){
                    foreach($escalated_batch_from as $batch_from){
                        $tbody .= '<tr> 
                                <td class = "color_blue"><b>APPROVED BATCH | '.$batch_from->batch_no.' | '.number_format($batch_from->local_batch_amount,2).'</td>
                                <td class = "color_blue"> '.Aimsuser_web::where('user_id', $batch_from->escalted_to)->first()->user_name.'</td>
                                <td class = "color_blue"> '.$batch_from->batch_title.'</td>
                                <td class = "color_blue"> '.substr($batch_from->approved_date,0,10).' '.substr($batch_from->approved_date,12,16).'</td>
                                <td class = "color_blue">
                                <a href="'.route('read-approved-batch',['batch_no'=>$batch_from->batch_no,]).'">Take Action </a>
                                </td>
                            </tr>';
                    }
                }
                if(count($claim_approvals) > 0 ){
                    foreach($claim_approvals as $approval){
                        $tbody .= '<tr> 
                                <td class = "color_blue"><b>CLAIM APPROVALS'.'</td>
                                <td class = "color_blue"> '.Aimsuser_web::where('user_id', $approval->sent_to)->first()->user_name.'</td>
                                <td class = "color_blue"> '.$approval->description.'</td>
                                <td class = "color_blue"> '.substr($approval->created_at,0,10).' '.substr($approval->created_at,12,16).'</td>
                                <td class = "color_blue">
                                <a href="'.route('claims.index',['id'=>$approval->claim_no,]).'">Take Action </a>
                                </td>
                            </tr>';
                    }
                }
                if(count($blacklistapproval) > 0 ){
                    foreach($blacklistapproval as $blacklist){
                        $tbody .= '<tr> 
                                <td class = "color_blue"><b>BLACKLISTED VEHICLES'.'</td>
                                <td class = "color_blue"> '.Aimsuser_web::where('user_id', $blacklist->sent_to)->first()->user_name.'</td>
                                <td class = "color_blue"> '.$blacklist->description.'</td>
                                <td class = "color_blue"> '.substr($blacklist->created_at,0,10).' '.substr($blacklist->created_at,12,16).'</td>
                                <td class = "color_blue">
                                <a href="'.route('viewblacklist',['id'=>$blacklist->blacklist_id,]).'">Take Action </a>
                                </td>
                            </tr>';
                    }
                }
                if(count($unblacklistapproval) > 0 ){
                    foreach($unblacklistapproval as $blacklist){
                        $tbody .= '<tr> 
                                <td class = "color_blue"><b>UNBLACKLISTED VEHICLES  '.'</td>
                                <td class = "color_blue"> '.Aimsuser_web::where('user_id', $blacklist->sent_to)->first()->user_name.'</td>
                                <td class = "color_blue"> '.$blacklist->description.'</td>
                                <td class = "color_blue"> '.substr($blacklist->created_at,0,10).' '.substr($blacklist->created_at,12,16).'</td>
                                <td class = "color_blue">
                                <a href="'.route('viewunblacklist',['id'=>$blacklist->blacklist_id,]).'">Take Action </a>
                                </td>
                            </tr>';
                    }
                }

                if(count($rejectedreq) > 0 ){
                    foreach($rejectedreq as $rej){
                        $tbody .= '<tr> 
                                <td class = "color_blue"><b>DECLINED REQUISITION | '.$rej->req_no.'</td>
                                <td class = "color_blue"> '.Aimsuser_web::where('user_name', $rej->declined_by)->first()->user_name.'</td>
                                <td class = "color_blue"> '.$rej->name.'</td>
                                <td class = "color_blue"> '.'</td>
                                <td class = "color_blue">
                                <a href="'.route('read-declinedReq',['req_no'=>$rej->req_no,]).'">Take Action </a>
                                </td>
                            </tr>';
                    }
                }
    
                if(count($declined_batch) > 0 ){
                    foreach($declined_batch as $batch_declined){
                        $tbody .= '<tr> 
                                <td class = "color_blue"><b>DECLINED BATCH | '.$batch_declined->batch_no.' | '.number_format($batch_declined->local_batch_amount,2).'</td>
                                <td class = "color_blue"> '.Aimsuser_web::where('user_id', $batch_declined->escalted_to)->first()->user_name.'</td>
                                <td class = "color_blue"> '.$batch_declined->batch_title.'</td>
                                <td class = "color_blue"> '.substr($batch_declined->approved_date,0,10).' '.substr($batch_declined->approved_date,12,16).'</td>
                                <td class = "color_blue">
                                <a href="'.route('read-approved-batch',['batch_no'=>$batch_declined->batch_no,]).'">Take Action </a>
                                </td>
                            </tr>';
                    }
                }
    
                if(count($escaledbnkreconclose) > 0 ){
                    foreach($escaledbnkreconclose as $escaledbnkreconclose){
                        $tbody .= '<tr> 
                                <td class = "color_blue"><b>BANK RECON CLOSURE | '.$escaledbnkreconclose->req_no.' | '.'</td>
                                <td class = "color_blue"> '.Aimsuser_web::where('user_id', $escaledbnkreconclose->sent_by)->first()->user_name.'</td>
                                <td class = "color_blue"> '.$escaledbnkreconclose->description.'</td>
                                <td class = "color_blue"> '.substr($escaledbnkreconclose->created_at,0,10).' '.substr($escaledbnkreconclose->created_at,12,16).'</td>
                                <td class = "color_blue">
                                <a href="'.route('tobnkreconc',['id'=>$escaledbnkreconclose->req_no,]).'">Take Action </a>
                                </td>
                            </tr>';
                    }
                }

    
                if(count($escalated_batch) > 0 ){
                    foreach($escalated_batch as $batch){
                        $tbody .= '<tr> 
                                <td class = "color_blue"><b>APPROVE POST BATCH | '.$batch->unique_ref.' | '.number_format($batch->local_batch_amount,2).'</td>
                                <td class = "color_blue">'.Aimsuser_web::where('user_id', $batch->authorized_by)->first()->user_name.'</td>
                                <td class = "color_blue"> '.$batch->batch_title.'</td>
                                <td class = "color_blue"> '.substr($batch->escalated_date,0,10).' '.substr($batch->escalated_date,12,16).'</td>
                                <td class = "color_blue"> <a href="'.route('authorize-batch',['batch_no'=>$batch->unique_ref,  'approve' => 'Y']).'">Approve  Batch</a></td>
                            </tr>';

                    }
                }
    
                if(count($authorize_batch) > 0 ){
                    foreach($authorize_batch as $authorize){
                        $tbody .= '<tr> 
                                <td class = "color_blue"><b>AUTHORIZE BATCH | '.$authorize->unique_ref.' | '.number_format($authorize->local_batch_amount,2).'</td>
                                <td class = "color_blue"> '.Aimsuser_web::where('user_id', $authorize->escalted_by)->first()->user_name.'</td>
                                <td class = "color_blue"> '.$authorize->batch_title.'</td>
                                <td class = "color_blue"> '.substr($authorize->escalated_date,0,10).' '.substr($authorize->escalated_date,12,16).'</td>
                                <td class = "color_blue">
                                <a href="'.route('authorize-batch',['batch_no'=>$authorize->unique_ref]).'">Authorize Batch</a>
                                </td>
                            </tr>';
                    }
                }

                
            if(count($escalated_period) > 0 ){
                foreach($escalated_period as $period){
                    $getbatch = Batch_list::where('batch_no', $period->batch_no)->first();
                    $tbody .= '<tr> 
                            <td class = "color_blue"><b>APPROVE POST FUTURE BATCH | '.$getbatch->batch_no.' | '.number_format($getbatch->local_batch_amount,2).'</td>
                            <td class = "color_blue"> '.Aimsuser_web::where('user_id', $period->escalated_by)->first()->user_name.'</td>
                            <td class = "color_blue"> '.$getbatch->batch_title.'</td>
                            <td class = "color_blue"> '.substr($period->escalated_date,0,10).' '.substr($period->escalated_date,12,16).'</td>
                            <td class = "color_blue">
                            <a href="#" data-batch="'.$getbatch->batch_no.'" class="approve-period-batch">Take Action </a>
                            </td>
                        </tr>';
                }
            }


            if(count($escalated_period_from) > 0 ){
                foreach($escalated_period_from as $period_from){
                    $getperiodbatch = Batch_list::where('batch_no', $period_from->batch_no)->first();
                    $tbody .= '<tr> 
                            <td class = "color_blue"><b>APPROVED FUTURE BATCH | '.$getperiodbatch->batch_no.' | '.number_format($getperiodbatch->local_batch_amount,2).'</td>
                            <td class = "color_blue"> '.Aimsuser_web::where('user_id', $period_from->escalated_to)->first()->user_name.'</td>
                            <td class = "color_blue"> '.$getperiodbatch->batch_title.'</td>
                            <td class = "color_blue"> '.substr($period_from->approved_date,0,10).' '.substr($period_from->approved_date,12,16).'</td>
                            <td class = "color_blue">
                            <a href="'.route('read-period-approved-batch',['batch_no'=>$getperiodbatch->batch_no,'account_year'=>$getperiodbatch->account_year,'account_month'=>$getperiodbatch->account_month]).'">remove </a>
                            </td>
                        </tr>';
                }
            }


            // end of period escalation

            // reverse batch escalation
            if(count($reverse_batch) > 0 ){
                foreach($reverse_batch as $reverse){
                    $getreversebatch = Batch_list::where('batch_no', $reverse->unique_ref)->first();
                    $tbody .= '<tr> 
                            <td class = "color_blue"><b>APPROVE BATCH  REVERSAL| '.$getreversebatch->batch_no.' | '.number_format($getreversebatch->local_batch_amount,2).'</td>
                            <td class = "color_blue"> '.Aimsuser_web::where('user_id', $reverse->escalted_by)->first()->user_name.'</td>
                            <td class = "color_blue"> '.$getreversebatch->batch_title.'</td>
                            <td class = "color_blue"> '.substr($reverse->created_at,0,10).' '.substr($reverse->created_at,12,16).'</td>
                            <td class = "color_blue">
                                <a href="#" data-batch="'.$getreversebatch->batch_no.'" class="approve-reverse-batch">Take Action </a>
                            </td>
                        </tr>';
                }
            }

            if(count($reverse_batch_from) > 0 ){
                foreach($reverse_batch_from as $reverse_from){
                    $getreversebatch_from = Batch_list::where('batch_no', $reverse_from->unique_ref)->first();
                    $tbody .= '<tr> 
                            <td class = "color_blue"><b>ESCALATION FOR REVERSAL APPROVED| '.$getreversebatch_from->batch_no.' | '.number_format($getreversebatch_from->local_batch_amount,2).'</td>
                            <td class = "color_blue"> '.Aimsuser_web::where('user_id', $reverse_from->escalted_to)->first()->user_name.'</td>
                            <td class = "color_blue"> '.$getreversebatch_from->batch_title.'</td>
                            <td class = "color_blue"> '.substr($reverse_from->approved_date,0,10).' '.substr($reverse_from->approved_date,12,16).'</td>
                            <td class = "color_blue">
                            <a href="'.route('read-reverse-approved-batch',['batch_no'=>$getreversebatch_from->batch_no,'account_year'=>$getreversebatch_from->account_year,'account_month'=>$getreversebatch_from->account_month]).'">Remove from inbox</a>
                            </td>
                        </tr>';
                }
            }

            if(count($reverse_batch_declined) > 0 ){
                foreach($reverse_batch_declined as $declined){
                    $get_batch = Batch_list::where('batch_no', $declined->unique_ref)->first();
                    $tbody .= '<tr> 
                            <td class = "color_blue"><b>ESCALATION FOR REVERSAL DECLINED| '.$get_batch->batch_no.' | '.number_format($get_batch->local_batch_amount,2).'</td>
                            <td class = "color_blue"> '.Aimsuser_web::where('user_id', $declined->escalted_to)->first()->user_name.'</td>
                            <td class = "color_blue"> '.$declined->declined_reason.'</td>
                            <td class = "color_blue"> '.substr($declined->approved_date,0,10).' '.substr($declined->approved_date,12,16).'</td>
                            <td class = "color_blue">
                            <a href="'.route('read-reverse-approved-batch',['batch_no'=>$get_batch->batch_no,'account_year'=>$get_batch->account_year,'account_month'=>$get_batch->account_month, 'decline_flag'=>'Y']).'">Take Action</a>
                            </td>
                        </tr>';
                }
            }

                
                if (count($sendreqnumber) > 0) {
                    foreach ($sendreqnumber as $sendreq) {
                        $tbody .=
                            '<tr>
                        <td class = "color_blue"><b>
                            ' .
                            $sendreq->name .
                            '
                            </b> |';
                        if ($sendreq->req_no != null) {
                            $tbody .= formatRequisitionNo($sendreq->req_no);
                        } else {
                            $tbody .= formatPolicyOrClaim($sendreq->claim_no);
                        }
                        $tbody .= '</td>';
                        $tbody .=
                            '<td class = "color_blue">' .
                            $sendreq->nem .
                            '</td>
                            <td class = "color_blue">' .
                            $sendreq->description .
                            '</td>
                            <td class = "color_blue"> ' .
                            substr($sendreq->created_at, 0, 10) .
                            ' ' .
                            substr($sendreq->created_at, 12, 16) .
                            '</td>
                            <td class = "color_blue">';
                        if (!$sendreq->claim_no || is_null($sendreq->claim_no)) {
                            $tbody .= '<a href="' . route('policy_functions', ['endt_renewal_no' => $sendreq->endorse_no]) . '">Take Action </a>';
                        } else {
                            $tbody .= '<a href="' . route('claims.index', ['claim_no' => $sendreq->claim_no]) . '">Take Actions</a>';
                        }
                        $tbody .= '</td>
                            </tr>';
                    }
                }
    
                if (count($uwreq) > 0) {
                    foreach ($uwreq as $uwreq) {
                        $tbody .=
                            '<tr>
                                <td class = "color_blue"><b>
                                    ' .
                            $uwreq->type .
                            ' </b>|' .
                            formatRequisitionNo($uwreq->endorse_no) .
                            '
                                </td>
                                <td class = "color_blue"> ' .
                            $uwreq->nem .
                            '</td>
                                <td class = "color_blue"> ' .
                            $uwreq->description .
                            ' </td>
                                <td class = "color_blue"> ' .
                            substr($uwreq->created_at, 0, 10) .
                            ' ' .
                            substr($uwreq->created_at, 12, 16) .
                            '</td>
                                <td class = "color_blue">
                                <a href=" ' .
                            route('uw_inbox', ['endt_renewal_no' => $uwreq->endorse_no, 'escalate_id' => $uwreq->escalate_id]) .
                            '">Take Action </a>
                                </td>
                            </tr>';
                    }
                }

                if (count($polescalations) > 0) {
                    foreach ($polescalations as $polescalation) {
                        $tbody .=
                            '<tr>
                        <td class = "color_blue"><b>
                        ' .      
                            $polescalation->name .
                            '
                        </b> |
                        ' .
                            formatPolicyOrClaim($polescalation->endorse_no) .
                            '</td>
                        <td "color_blue"> ' .
                            $polescalation->user_name .
                            ' </td>
                        <td "color_blue"> ' .
                            $polescalation->description .
                            ' </td>
    
                        <td "color_blue"> ' .
                            substr($polescalation->created_at, 0, 10) .
                            ' ' .
                            substr($polescalation->created_at, 12, 16) .
                            ' </td>';
                        if ($polescalation->approved != 'Y') {
                            $tbody .=
                                '<td "color_blue">
                                    <a href="' .
                                route('policy_functions', ['endt_renewal_no' => $polescalation->endorse_no]) .
                                '">Take Action</a>
                            </td>';
                        } else {
                            $tbody .= '<td>
                            </td>';
                        }
                        $tbody .= '</tr>';
                    }
                }

                if (count($decline_escalation) > 0) {
                    foreach ($decline_escalation as $decline) {
                        $tbody .=
                            '<tr>
                            <td class = ""color_blue"" ><b>
                            ' .
                            $decline->name .
                            '
                            </b> |
                            ' .
                            formatPolicyOrClaim($decline->endorse_no) .
                            '</td>
                            <td class = "color_blue">' .
                            $decline->user_name .
                            '</td>
                            <td class = "color_blue">' .
                            $decline->description .
                            '</td>
                            <td class = "color_blue">' .
                            substr($decline->created_at, 0, 10) .
                            ' ' .
                            substr($decline->created_at, 12, 16) .
                            '</td>';
                        if ($decline->approved != 'Y') {
                            $tbody .=
                                '<td>
                                <a href="' .
                                route('policy_functions', ['endt_renewal_no' => $decline->endorse_no]) .
                                '">Take Action</a>
                            </td>';
                        } else {
                            $tbody .= '<td class = "color_blue">
                                </td>';
                        }
                        $tbody .= '</tr>';
                    }
                }
    
                // <!--approved Debit -->
                if (count($pend_deb_escalation) > 0) {
                    foreach ($pend_deb_escalation as $pend_deb_esc) {
                        $tbody .=
                            '<tr>
                        <td class = "color_blue"><b>
                        ' .
                            $pend_deb_esc->name .
                            '
                        </b> |
                        ' .
                            formatPolicyOrClaim($pend_deb_esc->endorse_no) .
                            '</td>
                        <td class = "color_blue"> ' .
                            $pend_deb_esc->names .
                            ' </td>
                        <td class = "color_blue"> ' .
                            $pend_deb_esc->description .
                            ' </td>
                        <td class = "color_blue">
                            ' .
                            substr($pend_deb_esc->created_at, 0, 10) .
                            ' ' .
                            substr($pend_deb_esc->created_at, 12, 16) .
                            '</td>
                        <td class = "color_blue">
                            <a href="' .
                            route('policy_functions', ['endt_renewal_no' => $pend_deb_esc->endorse_no]) .
                            '">Take Action</a>
                        </td>
                        </tr>';
                    }
                }
                // <!--approved renewal -->
                if (count($renewalinbox) > 0) {
                    foreach ($renewalinbox as $ren) {
                        $tbody .=
                            '<tr>
                        <td class = "color_blue"><b>
                        ' .
                            $ren->name .
                            '
                        </b> |
                        ' .
                            formatPolicyOrClaim($ren->endorse_no) .
                            '</td>
                        <td class = "color_blue"> ' .
                            $ren->user_name .
                            ' </td>
                        <td class = "color_blue"> ' .
                            $ren->description .
                            ' </td>
                        <td class = "color_blue">
                            ' .
                            substr($ren->created_at, 0, 10) .
                            ' ' .
                            substr($ren->created_at, 12, 16) .
                            '</td>';
    
                        if ($ren->approved != 'Y') {
                            $tbody .=
                                '<td>
                                <a href="' .
                                route('endorse_functions', ['policy_no' => $ren->policy_no]) .
                                '">Take Action</a>
                            </td>';
                        } else {
                            $tbody .= '<td class = "color_blue">
                                </td>';
                        }
                        $tbody .= '</tr>';
                    }
                }
                // <!--credit approval inbox-->
                if (count($checkcreditapproved) > 0) {
                    foreach ($checkcreditapproved as $checkcredit) {
                        $tbody .=
                            '<tr>
                        <td class = "color_blue"><b>
                            ' .
                            $checkcredit->credit_reference .
                            '
                        </b> |
                        ' .
                            formatPolicyOrClaim($checkcredit->endorse_no) .
                            '</td>
                        <td class = "color_blue"> ' .
                            $checkcredit->user_name .
                            ' </td>
                        <td class = "color_blue"> ' .
                            $checkcredit->description .
                            ' </td>
    
                        <td class = "color_blue">' .
                            substr($checkcredit->created_at, 0, 10) .
                            ' ' .
                            substr($checkcredit->created_at, 12, 16) .
                            ' </td>
                        <td class = "color_blue">
                        <a href="' .
                            route('credits') .
                            '">Take Action</a>
                        </td>
                        </tr>';
                    }
                }

                if (count($certificatereqs) > 0) {
                    foreach ($certificatereqs as $certreq) {
                        $tbody .=
                            '<tr>
                        <td class = "color_blue"><b>
                            CERTIFICATE REQUISITION
                            </b> |';
                        if ($certreq->requisition_no != null) {
                            $tbody .= formatRequisitionNo($certreq->requisition_no);
                        }
                        $tbody .= '</td>';
                        $tbody .=
                            '<td class = "color_blue">' .
                            $certreq->raised_by .
                            '</td>
                            <td class = "color_blue">CERTIFCATE REQUISITION APPROVAL</td>
                            <td class = "color_blue"> ' .
                            substr($certreq->raised_date, 0, 10) .
                            ' ' .
                            substr($certreq->raised_date, 12, 16) .
                            '</td>
                            <td class = "color_blue">';
                        
                        $tbody .= '<a href="' . route('cert_request_view') . '">Take Action </a>';
                        
                        $tbody .= '</td>
                            </tr>';
                    }
                }
                
                if (count($bpdatereqs) > 0) {
                    foreach ($bpdatereqs as $bpdatereq) {
                        $tbody .=
                            '<tr>
                        <td class = "color_blue"><b>
                                BACKDATE / POSTDATE REQUISITION
                            </b> |';
                        if ($bpdatereq->requisition_no != null) {
                            $tbody .= formatRequisitionNo($bpdatereq->requisition_no);
                        }
                        $tbody .= '</td>';
                        $tbody .=
                            '<td class = "color_blue">' .
                            $bpdatereq->raised_by .
                            '</td>
                            <td class = "color_blue">BACKDATE / POSTDATE REQUISITION APPROVAL</td>
                            <td class = "color_blue"> ' .
                            substr($bpdatereq->raised_date, 0, 10) .
                            ' ' .
                            substr($bpdatereq->raised_date, 12, 16) .
                            '</td>
                            <td class = "color_blue">';
                        
                        $tbody .= '<a href="' . route('backpostdate_view') . '">Take Action </a>';
                        
                        $tbody .= '</td>
                            </tr>';
                    }
                }

                if(count($escalintcreditfloatreq) > 0){

                    foreach ($escalintcreditfloatreq as $intcredreq) {
                        $tbody .=
                            '<tr>
                        <td class = "color_blue"><b>
                                INTERMEDIARY CREDIT FLOAT REQUEST
                            </b> |';
                        if ($intcredreq->reference != null) {
                            $tbody .= $intcredreq->reference;
                        }
                        $tbody .= '</td>';
                        $tbody .=
                            '<td class = "color_blue">' .
                            $intcredreq->created_by .
                            '</td>
                            <td class = "color_blue">INTERMEDIARY CREDIT FLOAT REQUEST APPROVAL</td>
                            <td class = "color_blue"> ' .
                            substr($intcredreq->created_at, 0, 10) .
                            ' ' .
                            substr($intcredreq->created_at, 12, 16) .
                            '</td>
                            <td class = "color_blue">';
                        
                        $tbody .= '<a href="' . route('int_credit_float',['branch'=>$intcredreq->branch, 'agent'=>$intcredreq->agent]) . '">Take Action </a>';
                        
                        $tbody .= '</td>
                            </tr>';
                    }
                }

                if(count($escalcltcreditfloatreq) > 0){

                    foreach ($escalcltcreditfloatreq as $cltcredreq) {
                        $tbody .=
                            '<tr>
                        <td class = "color_blue"><b>
                                CLIENT CREDIT FLOAT REQUEST
                            </b> |';
                        if ($cltcredreq->reference != null) {
                            $tbody .= $cltcredreq->reference;
                        }
                        $tbody .= '</td>';
                        $tbody .=
                            '<td class = "color_blue">' .
                            $cltcredreq->created_by .
                            '</td>
                            <td class = "color_blue">CLIENT CREDIT FLOAT REQUEST APPROVAL</td>
                            <td class = "color_blue"> ' .
                            substr($cltcredreq->created_at, 0, 10) .
                            ' ' .
                            substr($cltcredreq->created_at, 12, 16) .
                            '</td>
                            <td class = "color_blue">';
                        
                        $tbody .= '<a href="' . route('clt_credit_float',['branch'=>$cltcredreq->branch, 'agent'=>$cltcredreq->agent,'client_number'=>$cltcredreq->client_number]) . '">Take Action </a>';
                        
                        $tbody .= '</td>
                            </tr>';
                    }
                }

                 //RECEIPT REVERSAL
            
            if(count($recrev) > 0 ){
                foreach($recrev as $recr){
                    // dd($recr->unique_ref,$recr->user_name,$recr->type, $recr->user_name, $recr->user_name, $recr->user_name);
                    $tbody .= '<tr>
                            <td class = "color_blue"><b>
                                '.$recr->type.' </b>|'.$recr->unique_ref.'
                            </td>
                            <td class = "color_blue"> '.$recr->user_name.'</td>
                            <td class = "color_blue"> '.$recr->description.' </td>
                            <td class = "color_blue"> '.substr($recr->created_at,0,10).' '.substr($recr->created_at,12,16).'</td>
                            <td class = "color_blue">
                            <a href=" '.route('receipts.details', ['dtrans_no' =>$recr->dtrans_no,'account_year'=>$recr->account_year,'account_month'=>$recr->account_month
                            ,'entry_type_descr'=>$recr->entry_type_descr,'offcd'=>$recr->offcd,
                            ]) .'">Take Action </a>
                            </td>
                        </tr>';
                }
            }

            if(count($recrevfrom) > 0 ){
                foreach($recrevfrom as $recr){
                    $tbody .= '<tr>
                            <td class = "color_blue"><b>
                           REC REVERSAL APPROVED </b>|'.$recr->unique_ref.'
                            </td>
                            <td class = "color_blue"> '.Aimsuser_web::where('user_id',$recr->sent_by)->first()->user_name.'</td>
                            <td class = "color_blue"> '.$recr->description.' </td>
                            <td class = "color_blue"> '.substr($recr->created_at,0,10).' '.substr($recr->created_at,12,16).'</td>
                            <td class = "color_blue">
                            <a href=" '.route('fo.receipts.remove_from_inbox', ['dtrans_no' =>$recr->dtrans_no,'type'=>$recr->type,
                            'escalate_id' =>$recr->escalate_id,
                                'sent_by' =>$recr->sent_by, 'sent_to' =>$recr->sent_to
                            ]) .'">Remove from inbox </a>
                            </td>
                        </tr>';
                }
            }

            if(count($recrevdec) > 0 ){
                foreach($recrevdec as $recr){
                    $tbody .= '<tr>
                            <td class = "color_blue"><b>
                                '.$recr->type.' </b>|'.$recr->unique_ref.'
                            </td>
                            <td class = "color_blue"> '.$recr->user_name.'</td>
                            <td class = "color_blue"> '.$recr->description.' </td>
                            <td class = "color_blue"> '.substr($recr->created_at,0,10).' '.substr($recr->created_at,12,16).'</td>
                            <td class = "color_blue">
                            <a href=" '.route('receipts.details', ['dtrans_no' =>$recr->dtrans_no,'account_year'=>$recr->account_year,'account_month'=>$recr->account_month
                            ,'entry_type_descr'=>$recr->entry_type_descr,'offcd'=>$recr->offcd,
                            ]) .'">Take Action </a>
                            </td>
                        </tr>';
                }
            }

            // //approved reversals
            
            if(count($recrevapproved) > 0 ){
                foreach($recrevapproved as $recr){
                    $tbody .= '<tr>
                            <td class = "color_blue"><b>
                                REVERSAL APPROVED </b>|'.$recr->unique_ref.'
                            </td>
                            <td class = "color_blue"> '.$recr->user_name.'</td>
                            <td class = "color_blue"> '.$recr->description.' </td>
                            <td class = "color_blue"> '.substr($recr->created_at,0,10).' '.substr($recr->created_at,12,16).'</td>
                            <td class = "color_blue">
                            <a href=" '.route('receipts.details', ['dtrans_no' =>$recr->dtrans_no,'account_year'=>$recr->account_year,'account_month'=>$recr->account_month
                            ,'entry_type_descr'=>$recr->entry_type_descr,'offcd'=>$recr->offcd,
                            ]) .'">Take Action </a>
                            </td>
                        </tr>';
                }
            }

                //PV  REVERSAL

                if(count($pv_rev_escalations) > 0 ){
                foreach($pv_rev_escalations as $recr){
                    // dd($recr->unique_ref,$recr->user_name,$recr->type, $recr->user_name, $recr->user_name, $recr->user_name);
                    $tbody .= '<tr>
                            <td class = "color_blue"><b>
                                '.$recr->type.' </b>|'.$recr->unique_ref.'
                            </td>
                            <td class = "color_blue"> '.$recr->user_name.'</td>
                            <td class = "color_blue"> '.$recr->description.' </td>
                            <td class = "color_blue"> '.substr($recr->created_at,0,10).' '.substr($recr->created_at,12,16).'</td>
                            <td class = "color_blue">
                            <a href=" '.route('payments.details', ['voucher_no' =>$recr->dtrans_no,'account_year'=>$recr->account_year,'account_month'=>$recr->account_month
                            ,'entry_type_descr'=>$recr->entry_type_descr,'offcd'=>$recr->offcd,'doc_type'=>'PAY','escalate_id'=>$recr->escalate_id
                            ]) .'">Take Action </a>
                            </td>
                        </tr>';
                }
            }

            if(count($chq_escalations) > 0 ){
                foreach($chq_escalations as $chq){
                    $tbody .= '<tr>
                            <td class = "color_blue"><b>
                                CHEQUE CANCELLATION ESCALATION </b>|'.$chq->unique_ref.'
                            </td>
                            <td class = "color_blue"> '.$chq->user_name.'</td>
                            <td class = "color_blue">APPROVE ESCALATION </td>
                            <td class = "color_blue"> '.substr($chq->created_at,0,10).' '.substr($chq->created_at,12,16).'</td>
                            <td class = "color_blue">
                                <a href="#" data-cheque_no="'.$chq->unique_ref.'" data-bank_code="'.$chq->bank_acc_code.'" data-send_by="'.$chq->user_name.'"  data-reason="'.$chq->cancel_reason.'" data-bank_name="'.$chq->bank_name.'" data-escalated_by="'.$chq->escalated_by.'" class="approve_cheq">Take Action </a>
                            </td>
                            </tr>';
                }
            }

            if(count($chq_escalation_approved) > 0 ){
                foreach($chq_escalation_approved as $chq){
                    $tbody .= '<tr>
                            <td class = "color_blue"><b>
                                CHEQUE ESCALATION APPROVED </b>|'.$chq->unique_ref.'
                            </td>
                            <td class = "color_blue"> '.$chq->user_name.'</td>
                            <td class = "color_blue">ESCALATION APPROVED </td>
                            <td class = "color_blue"> '.substr($chq->created_at,0,10).' '.substr($chq->created_at,12,16).'</td>
                            <td class = "color_blue">
                                <a href="#" data-cheque_no="'.$chq->unique_ref.'" data-bank_code="'.$chq->bank_acc_code.'"  data-reason="'.$chq->cancel_reason.'" data-bank_name="'.$chq->bank_name.'" class="cancel_chq">Take Action </a>
                            </td>
                            </tr>';
                }
            }

            if(count($chq_escalation_declined) > 0 ){
                foreach($chq_escalation_declined as $chq){
                    $tbody .= '<tr>
                            <td class = "color_blue"><b>
                                ESCALATION DECLINED </b>|'.$chq->unique_ref.'
                            </td>
                            <td class = "color_blue"> '.$chq->user_name.'</td>
                            <td class = "color_blue">'.$chq->reason_declined.' </td>
                            <td class = "color_blue"> '.substr($chq->created_at,0,10).' '.substr($chq->created_at,12,16).'</td>
                            <td class = "color_blue">
                                <a href=" '.route('chequelist', ['prsno' => $chq->prsno, 'bank_acc_code'=> $chq->bank_acc_code]) .'">Take Action </a>
                            </td>
                            </tr>';
                }
            }


                foreach ($mailbox as $inbox) {
                    $tbody .=
                        '<tr>
                        <td><b>
                            ' .
                        $inbox->client_name .
                        ' </b> |  ' .
                        formatPolicyOrClaim($inbox->endt_renewal_no) .
                        '</td>
                       
                        <td> ' .
                        $inbox->user_name .
                        ' </td>
                        <td> ' .
                        $inbox->description .
                        ' </td>
                        <td> ' .
                        substr($inbox->date_sent, 0, 10) .
                        ' ' .
                        $inbox->time_sent .
                        '</td>
                        <td>';
    
                    if ($inbox->checker == 'Y') {
                        $tbody .=
                            '<a href="' .
                            route($inbox->lr2, ['endt_renewal_no' => $inbox->endt_renewal_no, 'code' => $inbox->stage, 'workflow_id' => $inbox->business_process, 'ref' => $inbox->reference_variable, 'mailbox_serial' => $inbox->mailbox_id]) .
                            '">
                                Take action</a>';
                    } else {
                        $tbody .= 'No Checker Object';
                    }
                    $tbody .= '</td>
                    </tr>';
                }

                return response($tbody, 200);
            } catch (\Throwable $e) {
                DB::rollback();
                 dd($e);
                //  return $e;
    
                return response('Operation failed', 400);
            }
        } 

        return view('dashboard');
    }

    public function outbox()
    {
        $user_id = Auth::user()->user_id;
        //$outbox = MailBox::where('user_id','=',$user_name)->where('status','=','outgoing')->get();

        /*$sql = "SELECT MAILBOX_ESCALATIONS.*,USER_GROUPS.ID AS UGID ,USER_GROUPS.NAME AS UGNAME
         FROM MAILBOX_ESCALATIONS,USER_GROUPS WHERE MAILBOX_ESCALATIONS.PERSONS_RESPONSIBLE = USER_GROUPS.ID AND (MAILBOX_ESCALATIONS.STATUS = 'outgoing' OR MAILBOX_ESCALATIONS.STATUS = 'closed') AND USER_GROUP IN (SELECT GROUP_ID FROM USER_AND_GROUPS WHERE USER_ID = '$user_id')";*/

        $gb = $_ENV['DB_USERNAME'];
        $gl = $_ENV['DB_USERNAME1'];
        $common = $_ENV['DB_USERNAME2'];

        $driver = DB::getDriverName();
        if ($driver === 'pgsql') {
            $sql =
                'select m.*,e.description, e2.description next_activity,u.user_name,e.reference_variable,ug.name,e.id,W.SEND_TO,e2.persons_responsible from ' .
                $common .
                '.mailbox m left join ' .
                $common .
                ".aimsusers u  on (CAST(TRIM(m.from_user_id) AS INTEGER)=u.user_id)
                          left join " .
                $gb .
                ".escalations e on (CAST(TRIM(m.stage) AS INTEGER)=e.code and m.business_process=CAST(TRIM(e.category) AS INTEGER))
                          left join " .
                $gb .
                ".workflowsconfig w on (CAST(TRIM(w.escalation_id) AS INTEGER)=e.id)
                          left join " .
                $gb .
                ".escalations e2 on (e2.id=W.SEND_TO)
                          left join " .
                $common .
                ".USER_GROUPS ug on (CAST(ug.id AS TEXT)=e2.persons_responsible)
                          where m.from_user_id='$user_id' and e.code=CAST(TRIM(m.stage) AS INTEGER) and CAST(TRIM(e.category) AS INTEGER)=m.business_process and m.status='closed'
                          order by m.date_sent,m.time_sent desc";
        } else {
            $sql =
                'select m.*,e.description, e2.description next_activity,u.user_name,e.reference_variable,ug.name,e.id,W.SEND_TO,e2.persons_responsible from ' .
                $common .
                '.mailbox m left join ' .
                $common .
                ".aimsusers u  on (to_number(trim(m.from_user_id))=u.user_id)
                          left join " .
                $gb .
                ".escalations e on (to_number(trim(m.stage))=e.code and m.business_process=to_number(trim(e.category)))
                          left join " .
                $gb .
                ".workflowsconfig w on (to_number(trim(w.escalation_id))=e.id)
                          left join " .
                $gb .
                ".escalations e2 on (e2.id=W.SEND_TO)
                          left join " .
                $common .
                ".USER_GROUPS ug on (to_char(ug.id)=e2.persons_responsible)
                          where m.from_user_id='$user_id' and e.code=to_number(trim(m.stage)) and to_number(trim(e.category))=m.business_process and m.status='closed'
                          order by m.date_sent,m.time_sent desc";
        }

        // and ((w.yes_no=m.yes_no and w.type='condition') or w.type<>'condition')

        $outbox = DB::select($sql);
        $escalations = Escalations::all();
        $workflows = Workflows::all();

        foreach ($outbox as $key => $mail) {
            $client = Client::where('client_number', $mail->reference_number)->get(['name']);

            $name = $client[0]->name;

            $mail->client_name = $name;
        }

        return View::make('outbox2')
            ->with('outbox', $outbox)
            ->with('escalations', $escalations)
            ->with('workflows', $workflows);
    }

    public function closed()
    {
        $user_id = Auth::user()->user_id;
        //$outbox = MailBox::where('user_id','=',$user_name)->where('status','=','outgoing')->get();

        $gb = $_ENV['DB_USERNAME'];
        $gl = $_ENV['DB_USERNAME1'];
        $common = $_ENV['DB_USERNAME2'];

        $sql =
            'SELECT ' .
            $common .
            '.MAILBOX.*, ' .
            $gb .
            '.ESCALATIONS.CODE, ' .
            $gb .
            '.ESCALATIONS.CATEGORY, ' .
            $gb .
            ".ESCALATIONS.REFERENCE_VARIABLE
FROM " .
            $common .
            '.MAILBOX, ' .
            $gb .
            ".ESCALATIONS
WHERE " .
            $common .
            '.MAILBOX.STAGE = ' .
            $gb .
            '.ESCALATIONS.CODE AND ' .
            $common .
            '.MAILBOX.BUSINESS_PROCESS = ' .
            $gb .
            ".ESCALATIONS.CATEGORY AND
" .
            $common .
            ".MAILBOX.STATUS = 'closed' AND USER_GROUP IN (SELECT GROUP_ID FROM " .
            $common .
            ".USER_AND_GROUPS WHERE USER_ID = '$user_id')";

        $closed = DB::select($sql);
        $escalations = Escalations::all();
        $workflows = Workflows::all();

        return View::make('closed')
            ->with('closed', $closed)
            ->with('escalations', $escalations)
            ->with('workflows', $workflows);
        //return $outbox;
    }

    public function approvals_dashboard(Request $request)
    {
        $user_id = Auth::user()->user_id;
        $outgoing = Approvals::where('user_id', $user_id)->get();
        $incoming = Approval_flow::where('approver_id', $user_id)->get();

        return view('approvals', [
            'outgoing' => $outgoing,
            'incoming' => $incoming,
        ]);
    }

    public function incoming_approvals(Request $request)
    {
        $user_id = Auth::user()->user_id;
        $incoming = Approval_flow::with('approval')
            ->where('approver_id', $user_id)
            ->where('status', 'P')
            ->get();

        return Datatables::of($incoming)
            ->editColumn('approval_id', function ($level) {
                if($level->approval->type == 'PO' || $level->approval->type == 'REQ'|| $level->approval->type == 'SIR' || $level->approval->type == 'INV' || $level->approval->type == 'SCT' || $level->approval->type == 'DISP' || 
                    $level->approval->type == 'MJV' || $level->approval->type == 'MJVREV' ||  $level->approval->type == 'APINV' || $level->approval->type == 'RECREV' || $level->approval->type == 'ASTREQ' || $level->approval->type == 'BGTAPR' || $level->approval->type == 'BGTREF' || $level->approval->type == 'REPREQ' || $level->approval->type == 'REVPV' ){
                    $approval_id = $level->approval_id;
                }

                else if($level->approval->type == 'CLM'){
                    $mask = '%s%s%s%s%s%s/%s%s%s%s';
                    $approval_id = vsprintf($mask, str_split($level->approval_id));
                }
               
                return $approval_id;
            })

            ->addColumn('level', function ($row) {
                $level = $row->level->description;
                return "({$row->approval_level}){$level}";
            })
            ->addColumn('user', function ($row) {
                $user = $row->approval->user->name;
                return $user;
            })
            ->addColumn('action', function ($level) {
                $type = $level->approval->type;
                $take_action = 'Y';
                    
                if ($type == 'PO') {
                    $po_number = $level->approval->po_number;
                    $route_param = route('po.po_details',[$po_number]);
                }
                
                else if ($type == 'REQ') {
                    $req_no = $level->approval->req_no;
                    // $route_param = route('poReqHeader',[$req_no]);
                    $route_param = route('po.req_details',[$req_no]);

                }
                
                else if ($type == 'SIR') {
                    $req_no = $level->approval->req_no;
                    $route_param = route('itemReqDtl',[$req_no]);

                }
                
                else if ($type == 'INV') {
                    $invoice_no = $level->approval->invoice_no;
                    $vendor_id = $level->approval->vendor_id;
                    $po_number = $level->approval->po_number;
                    $route_param = route('po.invoice_dtls',[$invoice_no, $vendor_id, $po_number]);

                }
                else if ($type == 'SCT') {
                    $stk_take_code = $level->approval->stk_take_code;
                    $route_param = route('stkTakeDtl',[$stk_take_code]);
                }
                else if ($type == 'DISP') {
                    $disposal_no = $level->approval->claim_no;
                    $route_param = route('fa.disposal_header_dtls',['disposal_no'=>$disposal_no]);
                }
                else if ($type == 'MJV') {
                    
                    $batch_no = $level->approval->claim_no;
                    $period_year = $level->approval->account_year;
                    $period_month = $level->approval->account_month;
                    $route_param = route('batch_entry',['batch_no'=>$batch_no, 'period_year'=> $period_year, 'period_month' =>$period_month]);
                }
                else if ($type == 'MJVREV') {
                    
                    $batch_no = $level->approval->claim_no;
                    $period_year = $level->approval->account_year;
                    $period_month = $level->approval->account_month;
                    $route_param = route('batch_entry',['batch_no'=>$batch_no, 'period_year'=> $period_year, 'period_month' =>$period_month]);
                }

                else if ($type == 'APINV') {
                    $invoice_no = $level->approval->invoice_no;
                    $vendor_id = $level->approval->vendor_id;
                    $route_param = route('invoiceheaderdetails', ['invoice_no'=>$invoice_no, 'vendor_id'=>$vendor_id]);

                }

                else if ($type == 'RECREV') {
                    $dtrans_no = $level->approval->claim_no;
                    $account_year = $level->approval->account_year;
                    $account_month = $level->approval->account_month;
                    $offcd = $level->approval->invoice_no;
                    $entry_type_descr = $level->approval->po_number;
                    $route_param = route('receipts.details', ['dtrans_no'=>$dtrans_no, 'account_year'=>$account_year,'account_month'=>$account_month,'offcd'=>$offcd,'entry_type_descr'=>$entry_type_descr]);

                }

                else if ($type == 'ASTREQ' || $type == 'REPREQ') {
                    $id = $level->approval->req_no;
                    $route_param = route('fa.faacquisitiondtl', ['id'=>$id]);

                }

                else if ($type == 'BGTAPR') {
                    $id = $level->approval->req_no;
                    $route_param = route('mainbudget', ['budget_id'=>$id]);

                }

                else if ($type == 'BGTREF') {
                    $id = $level->approval->req_no;
                    $route_param = route('mainbudget', ['budget_id'=>$id]);

                }
                else if ($type == 'REVPV') {
                    $id = $level->approval->req_no;
                    $route_param = route('payments.details', ['voucher_no' =>$level->approval->claim_no, 'doc_type' => 'PAY', 'account_year' => $level->approval->account_year]);

                }

                else{
                    $claim_no = $level->approval->claim_no;
                    $route_param = route('claims.index', [
                        'claim_no' => $claim_no,
                    ]);

                    $take_action = make_approval($level);
                }
                    
                if($level->status == 'P' && $take_action == 'Y' ){
                    return '<a href="'.$route_param.'" >Take Action</a>';
                }

                else{
                    return '<a class="badge disabled" style="cursor:no-drop"><i class="fa fa-ban"></i> Not allowed</a>';
                }
            })

            ->editColumn('reference', function ($row) {
                $type = $row->approval->type;

                if ($type == 'PO') {

                    $ref = $row->approval->po_number;

                }else if ($type == 'REQ') {

                    $ref = $row->approval->req_no;

                }else if ($type == 'SIR') {

                    $ref = $row->approval->req_no;

                }else if ($type == 'SCT') {

                    $ref = $row->approval->stk_take_code;

                }else if ($type == 'INV') {

                    $ref = $row->approval->invoice_no;

                
                }else if ($type == 'DISP') {

                    $ref = $row->approval->claim_no;

                
                }else if ($type == 'MJV') {

                    $ref = $row->approval->claim_no;

          
                }else if ($type == 'MJVREV') {

                    $ref = $row->approval->claim_no;

                }

                else if ($type == 'APINV') {
                    $ref = $row->approval->invoice_no;
                }

                else if ($type == 'RECREV') {
                    $ref = $row->approval->claim_no;
                }

                else if ($type == 'ASTREQ' || $type == 'REPREQ') {
                    $ref = $row->approval->req_no;
                }
                else if ($type == 'BGTAPR') {
                    $ref = $row->approval->req_no;
                }
                else if ($type == 'BGTREF') {
                    $ref = $row->approval->req_no;
                }
                else if ($type == 'REVPV') {
                    $ref = $row->approval->req_no;
                }

                else{
                    $claim_no = $row->approval->claim_no;
                    $ref = formatPolicyOrClaim($claim_no);
                }

                return $ref;
            })

            ->rawColumns(['action'])
            ->escapeColumns([])
            ->make(true);
    }

    public function outgoing_approvals(Request $request)
    {
        $user_id = Auth::user()->user_id;
        $outgoing = Approvals::where('user_id', $user_id)
            ->where('status', 'P')
            ->get();

        return Datatables::of($outgoing)
            ->editColumn('approval_id', function ($row) {

                if ($row->type == 'DISP' || $row->type == 'MJV' || $row->type == 'MJVREV' || $row->type == 'INV' || $row->type == 'APINV' || $row->type == 'ASTREQ'|| $row->type == 'RECREV'|| $row->type == 'BGTAPR'|| $row->type == 'BGTREF'|| $row->type == 'SCT' || $row->type == 'REQ'|| $row->type == 'SCT' || $row->type == 'REPREQ' || $row->type == 'REVPV') {
                    $approval_id = $row->approval_id;
                }
                
                else{
                    $mask = '%s%s%s%s%s%s/%s%s%s%s';
                    $approval_id = vsprintf($mask, str_split($row->approval_id));
                }


                return $approval_id;
            })
            ->editColumn('reference', function ($row) {
                $claim_no = $row->claim_no;

                if ($row->type == 'DISP' || $row->type == 'MJV' || $row->type == 'MJVREV' || $row->type == 'INV' || $row->type == 'APINV' || $row->type == 'ASTREQ'|| $row->type == 'RECREV'|| $row->type == 'BGTAPR'|| $row->type == 'BGTREF'|| $row->type == 'SCT' || $row->type == 'REQ'|| $row->type == 'SCT' || $row->type == 'REPREQ' || $row->type == 'REVPV') {
                    $claim_no = $claim_no;
                }
                
                else{
                    $claim_no = formatPolicyOrClaim($claim_no);
                }
                

                return $claim_no;
            })
            ->addColumn('status', function ($row) {
                switch ($row->status) {
                    case 'P':
                        $status = 'Pending';
                        break;
                    case 'A':
                        $status = 'Approved';
                        break;
                    case 'R':
                        $status = 'Rejected';
                        break;
                }

                return '<span class="badge">' . $status . '</span>';
            })
            ->rawColumns(['status'])
            ->escapeColumns([])
            ->make(true);
    }

    protected function receipt_rev_escalations() {
        $schem = schemaName();
    
        $gb = $schem['gb'];

        return  DB::table(''.$gb.'.escalate_pol e')
                ->join(''.$gb.'.cbmast c', function($join)
                {
                    $join->on('e.req_no','=','c.dtrans_no');
                }) ->join(''.$gb.'.aimsusers', function($join)
                {
                    $join->on('aimsusers.user_id','=','e.sent_by');
                })
                ->where('c.doc_type','=','REC')
                ->where('c.entry_type_descr','!=','REC')
                ->whereNull('c.cancelled')
                ->where('e.type','=','REC REV')
                ->whereNull('c.reversal_approved')
                ->where('e.sent_to','=',Auth::user()->user_id)
                ->distinct('e.escalate_id')
                ->get(
                    ['e.req_no as unique_ref', 'c.account_year','e.description','e.type', 'aimsusers.user_name','e.created_at','c.account_month',
                        'c.offcd','c.dtrans_no','c.entry_type_descr'
                    ]
                )->map(function ($item) {
                    $item->action_link = 'receipts.details';
                    return $item;
                });

    }

    protected function receipt_rev_approved(){
        $schem = schemaName();
    
        $gb = $schem['gb'];

        return DB::table(''.$gb.'.escalate_pol e')
        ->join(''.$gb.'.cbmast c', function($join)
        {
            $join->on('e.req_no','=','c.dtrans_no');
        }) ->join(''.$gb.'.aimsusers', function($join)
        {
            $join->on('aimsusers.user_id','=','e.sent_to');
        })
        ->where('c.entry_type_descr','!=','REC')
        ->whereNull('c.cancelled')
        ->where('e.type','=','REV APP')
        ->where('e.sent_to','=',Auth::user()->user_id)
        ->distinct('e.req_no')
        ->get(
            ['e.req_no as unique_ref', 'c.account_year','e.description','e.type', 'aimsusers.user_name','e.created_at','c.account_month',
                'c.offcd','c.dtrans_no','c.entry_type_descr'
            ]
        )->map(function ($item) {
            $item->action_link = 'receipts.details';
            return $item;
        });
    }

    protected function receipt_rev_from() {
        return DB::table('escalate_pol')
                ->select('escalate_pol.req_no as unique_ref','escalate_pol.sent_to', 'escalate_pol.escalate_id', 'escalate_pol.sent_by','escalate_pol.created_at',
                'cbmast.account_year','escalate_pol.description','escalate_pol.type',
                'aimsusers.user_name','cbmast.account_month','cbmast.offcd','cbmast.dtrans_no',
                'cbmast.entry_type_descr')
                ->join('cbmast','escalate_pol.req_no','=','cbmast.dtrans_no')
                ->join('aimsusers','aimsusers.user_id','=','escalate_pol.sent_to')
                ->whereNull('cbmast.cancelled')
                ->where('escalate_pol.type','=','REC APP')
                ->where('cbmast.reversal_approved', 'Y')
                ->where('escalate_pol.sent_to','=',Auth::user()->user_id)
                ->whereNull('escalate_pol.read')
                ->distinct('escalate_pol.req_no')
                ->get()
                ->map(function ($item) {
                    $item->action_link = 'fo.receipts.remove_from_inbox';
                    return $item;
                });
    }

    protected function receipt_rev_declined() {
        $schem = schemaName();
    
        $gb = $schem['gb'];

        return DB::table(''.$gb.'.escalate_pol e')
        ->join(''.$gb.'.cbmast c', function($join)
        {
            $join->on('e.req_no','=','c.dtrans_no');
        }) ->join(''.$gb.'.aimsusers', function($join)
        {
            $join->on('aimsusers.user_id','=','e.sent_to');
        })
        ->where('c.entry_type_descr','!=','REC')
        ->whereNull('c.cancelled')
        ->where('e.type','=','REC DEC')
        ->where('c.reversal_approved','!=','Y')
        ->where('e.sent_to','=',Auth::user()->user_id)
        ->distinct('e.req_no')
        ->get(
            ['e.req_no as unique_ref', 'c.account_year','e.description','e.type', 'aimsusers.user_name','e.created_at','c.account_month',
                'c.offcd','c.dtrans_no','c.entry_type_descr'
            ]
        )
        ->map(function ($item) {
            $item->action_link = 'receipts.details';
            return $item;
        });
    }

    protected function declinedreq() {

        $declinedreq = DB::table('PAYREQST')
        ->selectRaw('PAYREQST.*,PAYREQST.req_no as unique_ref, '."'DECLINED REQUISITION' AS description".'' )
        ->whereNotNull('declined_date')
        ->whereNull('declined')
        ->distinct('req_no')
        ->get()
        ->map(function ($item) {
            $item->action_link = 'read-declinedReq';
            return $item;
        });

        return $declinedreq;
    }

    protected function cheque_escalations() {
        return DB::table('chequemast c')
        ->selectRaw("g.bank_name, c.escalation_date as created_at, b.user_name,c.cheque_no as unique_ref, c.cancel_reason,
         c.approved, c.escalated_to, c.escalated_by, c.bank_acc_code ,  'APPROVE CHEQUE ESCALATION FOR CANCELLATION' AS description")
        ->join('aimsusers a', 'a.user_id', '=', 'c.escalated_to')
        ->join('aimsusers b', 'b.user_id', '=', 'c.escalated_by')
        ->join('glbanks g', 'g.bank_acc_code', '=', 'c.bank_acc_code')
        ->where('c.cheque_cancelled', '=', 'N')
        ->where('c.escalated_to', '=', Auth::user()->user_id)
        ->whereRaw(" (c.declined = 'N' or c.declined is null) OR (c.declined = 'Y' AND  c.reescalated ='Y') and (c.approved = 'N' or c.approved is null)")
        ->get()
        ->map(function ($item) {
            $item->action_link = 'approve_chqno';
            return $item;
        });
    }

    

    
    protected function sprovider_escalations() {
        $schem = schemaName();
    
        $gb = $schem['gb'];

        return  DB::table(''.$gb.'.escalate_pol e')
                ->join(''.$gb.'.clparam c', function($join)
                {
                    $join->on('e.claimant_code','=','c.claimant_code');
                }) ->join(''.$gb.'.aimsusers', function($join)
                {
                    $join->on('aimsusers.user_id','=','e.sent_by');
                })
                ->where('c.approved','=', 'N')
                ->where('e.type','=','SP')
                ->where('e.sent_to','=',Auth::user()->user_id)
                ->distinct('e.escalate_id')
                ->get(
                    ['e.claimant_code as unique_ref','e.description','e.type', 'aimsusers.user_name','e.created_at','e.escalate_id'
                    ]
                )->map(function ($item) {
                    $item->action_link = 'view_claimant';
                    return $item;
                });

    }

    protected function chq_escalation_approved() {
        return  DB::table('chequemast c')
        ->selectRaw("g.bank_name, c.date_approved as created_at, b.user_name,c.cheque_no as unique_ref, c.cancel_reason,
         c.approved, c.escalated_to, c.escalated_by, c.bank_acc_code, 'CHEQUE ESCALATION APPROVED' AS description ")
        ->join('aimsusers a', 'a.user_id', '=', 'c.escalated_by')
        ->join('aimsusers b', 'b.user_id', '=', 'c.escalated_to')
        ->join('glbanks g', 'g.bank_acc_code', '=', 'c.bank_acc_code')
        ->where('c.approved', '=', 'Y')
        ->where('c.cheque_cancelled', '=', 'N')
        ->where('c.escalated_by', '=', Auth::user()->user_id)
        ->get()
        ->map(function ($item) {
            $item->action_link = 'cancel_chqno';
            return $item;
        });
    }

    public function nil_endt_approvals($user_id) {
        $nilapproval = [];
        $nilapproval = DB::table('escalate_pol e')
                    ->join('aimsusers u', function($join){ $join->on('u.user_id','=','e.sent_to');})
                    ->where('e.type','=','NIL')
                    ->where('e.sent_to', Auth::user()->user_id)
                    ->where('e.approved', 'N')
                    ->where('e.reescalated', '!=', 'Y')
                    ->distinct('e.endorse_no')
                    ->get(['e.endorse_no as unique_ref','e.description','e.created_at'])
                    ->map(function ($item) { $item->action_link = 'policy_functions'; return $item; });
        // dd($nilapproval);
        return $nilapproval;
    }

    public function special_sticker_approvals($user_id) {

        $special_approval = DB::table('escalate_pol e')
                    ->join('aimsusers u', function($join){ $join->on('u.user_id','=','e.sent_to');})
                    ->where('e.type','SPT')
                    ->where('e.sent_to', Auth::user()->user_id)
                    ->where('e.approved', 'N')
                    ->where('e.reescalated', 'N')
                    ->distinct('e.endorse_no')
                    ->get(['e.endorse_no as unique_ref','e.description','e.created_at'])
                    ->map(function ($item) { $item->action_link = 'policy_functions'; return $item; });
        // dd($special_approval);
        return $special_approval;
    }

    public function special_sticker_approved($user_id) {

        $special_approval = DB::table('escalate_pol e')
                    ->join('aimsusers u', function($join){ $join->on('u.user_id','=','e.sent_by');})
                    ->where('e.type','SPT')
                    ->where('e.sent_by', Auth::user()->user_id)
                    ->where('e.approved', 'Y')
                    ->where('e.reescalated', 'N')
                    ->distinct('e.endorse_no')
                    ->get(['e.endorse_no as unique_ref','e.description','e.created_at'])
                    ->map(function ($item) { $item->action_link = 'policy_functions'; return $item; });
        return $special_approval;
    }



    public function nil_endt_approved($user_id) {
        $nilapproval = [];
        $nilapproval = DB::table('escalate_pol e')
                    ->join('aimsusers u', function($join){ $join->on('u.user_id','=','e.sent_by');})
                    ->where('e.type','=','NIL')
                    ->where('e.sent_by', Auth::user()->user_id)
                    ->where('e.approved', 'Y')
                    ->where('e.reescalated', '!=', 'Y')
                    ->distinct('e.endorse_no')
                    ->get(['e.endorse_no as unique_ref','e.description','e.created_at'])
                    ->map(function ($item) { $item->action_link = 'policy_functions'; return $item; });
        return $nilapproval;
    }

    protected function chq_escalation_declined() {
        return  DB::table('chequemast c')
        ->selectRaw( "'g.prsno, g.bank_name, c.reason_declined, c.date_declined as created_at, b.user_name,c.cheque_no as unique_ref, c.cancel_reason, c.approved, c.escalated_to, c.escalated_by, c.bank_acc_code, '.'CHEQUE ESCALATION DECLINED' AS description.''")
        ->join('aimsusers a', 'a.user_id', '=', 'c.escalated_by')
        ->join('aimsusers b', 'b.user_id', '=', 'c.escalated_to')
        ->join('glbanks g', 'g.bank_acc_code', '=', 'c.bank_acc_code')
        ->where('c.declined', '=', 'Y')
        ->whereRaw(" (c.reescalated = 'N' or c.approved is null) ")
        ->where('c.cheque_cancelled', '=', 'N')
        ->where('c.escalated_by', '=', Auth::user()->user_id)
        ->get()
        ->map(function ($item) {
            $item->action_link = 'chequelist';
            return $item;
        });
    }

    protected function pv_rev_escalations() {
        $schem = schemaName();
    
        $gb = $schem['gb'];

        return  DB::table(''.$gb.'.escalate_pol e')
                ->join(''.$gb.'.cbmast c', function($join)
                {
                    $join->on('e.req_no','=','c.dtrans_no');
                }) ->join(''.$gb.'.aimsusers', function($join)
                {
                    $join->on('aimsusers.user_id','=','e.sent_by');
                })
                ->where('c.doc_type','=','PAY')
                ->whereNull('c.cancelled')
                ->where('e.type','=','PV REV')
                ->where('e.read','=','0')
                ->whereNull('c.reversal_approved')
                ->where('e.sent_to','=',Auth::user()->user_id)
                ->distinct('e.escalate_id')
                ->get(
                    ['e.req_no as unique_ref', 'c.account_year','e.description','e.type', 'aimsusers.user_name','e.created_at','c.account_month',
                        'c.offcd','c.dtrans_no','c.entry_type_descr','e.escalate_id'
                    ]
                )->map(function ($item) {
                    $item->action_link = 'payments.details';
                    return $item;
                });

    }
    protected function blacklistapproval() {
        $driver = DB::getDriverName();
        $user_id=Auth::user()->user_id;
        if ($driver === 'pgsql') {
            $data= DB::select("select * from escalate_pol e where e.type='BLK' and e.sent_to=? and e.approved='N'", [$user_id]);
        } else {
            $data= DB::select("select * from escalate_pol e where e.type='BLK' and e.sent_to=? and e.approved='N'", [$user_id]);
        }
        $data =collect($data);
        $data->map(function ($item) {
            $item->action_link = 'blacklist-vehicles';
            return $item;
        });
        return $data;
    }
    protected function unblacklistapproval() {
        $driver = DB::getDriverName();
        $user_id=Auth::user()->user_id;
        if ($driver === 'pgsql') {
            $data= DB::select("select * from escalate_pol e where e.type='UNBLK' and e.sent_to=? and e.approved='N'", [$user_id]);
        } else {
            $data= DB::select("select * from escalate_pol e where e.type='UNBLK' and e.sent_to=? and e.approved='N'", [$user_id]);
        }
        $data =collect($data);
        $data->map(function ($item) {
            $item->action_link = 'unblacklist-vehicles';
            return $item;
        });
        return $data;
    }
    protected function reverse_batch_escalations(){
       return DB::table('batch_reverse_escalation a')
        ->selectRaw( "a.batch_no unique_ref,a.escalted_to, a.escalted_by, a.escalated_date as created_at, 
            'APPROVE BATCH REVERSAL ESCALATION' AS description")
        ->join('aimsusers c', 'a.escalted_by', '=', 'c.user_id')
        ->where('a.escalted_to', '=', Auth::user()->user_id)
        ->where('a.approved', '=', '0')
        ->where('a.declined', '=', '0')
        ->get()->map(function ($item) {
            $item->action_link = 'approve_btc_reverse';
            return $item;
        });
    }

    protected function remove_batchrev_from_inbox(){
        return DB::table('batch_reverse_escalation a')
        ->selectRaw( "g.account_year, g.account_month, a.batch_no unique_ref, a.escalted_to, a.escalted_by, a.APPROVED_DATE as created_at, 
            'BATCH REVERSAL ESCALATION APPROVED' AS description ")
        ->join('aimsusers c', 'a.escalted_by', '=', 'c.user_id')
        ->join('glbatch g', 'g.batch_no', '=', 'a.batch_no')
        ->where('a.escalted_by', '=', Auth::user()->user_id)
        ->where('a.approved', '=', '1')
        ->where('a.read', '=', '0')
        ->get()->map(function ($item) {
            $item->action_link = 'remove_btc_reverse';
            return $item;
        });
    }

    protected function reverse_batch_declined(){
        return DB::table('batch_reverse_escalation a')
         ->selectRaw("a.declined_reason, a.batch_no unique_ref,a.escalted_to, a.escalted_by, a.approved_date as created_at, 
             'ESCALATION FOR REVERSAL DECLINED' AS description")
         ->join('aimsusers c', 'a.escalted_to', '=', 'c.user_id')
         ->where('a.escalted_by', '=', Auth::user()->user_id)
         ->where('a.approved', '=', '0')
         ->where('a.declined', '=', '1')
         ->whereRaw(" (a.read = '0' or a.read is null) ")
         ->get()->map(function ($item) {
             $item->action_link = 'declined_reverse_btc';
             return $item;
         });
    }

    protected function reverse_pv_declined() {
        $schem = schemaName();
    
        $gb = $schem['gb'];

        return  DB::table(''.$gb.'.escalate_pol e')
                ->join(''.$gb.'.cbmast c', function($join)
                {
                    $join->on('e.req_no','=','c.dtrans_no');
                }) ->join(''.$gb.'.aimsusers', function($join)
                {
                    $join->on('aimsusers.user_id','=','e.sent_by');
                })
                ->where('c.doc_type','=','PAY')
                ->whereNull('c.cancelled')
                ->where('e.type','=','PV REV')
                ->where('e.read','=','2')
                ->whereNotNull('c.reversal_declined_reason')
                ->where('e.sent_by','=',Auth::user()->user_id)
                ->distinct('e.escalate_id')
                ->get(
                    ['e.req_no as unique_ref', 'c.account_year','e.description','e.type', 'aimsusers.user_name','e.created_at','c.account_month',
                        'c.offcd','c.dtrans_no','c.entry_type_descr','e.escalate_id'
                    ]
                )->map(function ($item) {
                    $item->action_link = 'pvreversal.details';
                    return $item;
                });

    }

    protected function reverse_pv_approved() {
        $schem = schemaName();
    
        $gb = $schem['gb'];

        return  DB::table(''.$gb.'.escalate_pol e')
                ->join(''.$gb.'.cbmast c', function($join)
                {
                    $join->on('e.req_no','=','c.dtrans_no');
                }) ->join(''.$gb.'.aimsusers', function($join)
                {
                    $join->on('aimsusers.user_id','=','e.sent_by');
                })
                ->where('c.doc_type','=','PAY')
                ->whereNull('c.cancelled')
                ->where('e.type','=','PV REV')
                ->where('e.read','=','3')
                ->where('e.sent_by','=',Auth::user()->user_id)
                ->distinct('e.escalate_id')
                ->get(
                    ['e.req_no as unique_ref', 'c.account_year','e.description','e.type', 'aimsusers.user_name','e.created_at','c.account_month',
                        'c.offcd','c.dtrans_no','c.entry_type_descr','e.escalate_id'
                    ]
                )->map(function ($item) {
                    $item->action_link = 'pvappreversal.details';
                    return $item;
                });

    }

    protected function escalateCloseBNKRecon() {
        $schem = schemaName();
    
        $gb = $schem['gb'];

        return  DB::table(''.$gb.'.escalate_pol e')
                ->join(''.$gb.'.glbanks g', function($join)
                {
                    $join->on('e.req_no','=','g.BANK_ACC_CODE');
                }) ->join(''.$gb.'.aimsusers', function($join)
                {
                    $join->on('aimsusers.user_id','=','e.sent_by');
                })
                ->join(''.$gb.'.glbanks g', function($join)
                {
                    $join->on('e.account_month','=','g.account_month');
                })
                ->join(''.$gb.'.glbanks g', function($join)
                {
                    $join->on('e.account_year','=','g.account_year');
                })
                ->where('e.type','=','BNK RECON')
                ->where('e.read','=','0')
                ->where('e.sent_to','=',Auth::user()->user_id)
                ->where('g.escalated_to','=',Auth::user()->user_id)
                ->distinct('e.escalate_id')
                ->get(
                    ['e.req_no as unique_ref', 'g.account_year','e.description','e.type', 'aimsusers.user_name','e.created_at','g.account_month',
                        'g.BANK_ACC_CODE','e.escalate_id'
                    ]
                )->map(function ($item) {
                    $item->action_link = 'clbnkrecon.details';
                    return $item;
                });

    }

    protected function decescalateCloseBNKRecon() {
        $schem = schemaName();
    
        $gb = $schem['gb'];

        return  DB::table(''.$gb.'.escalate_pol e')
                ->join(''.$gb.'.glbanks g', function($join)
                {
                    $join->on('e.req_no','=','g.BANK_ACC_CODE');
                }) ->join(''.$gb.'.aimsusers', function($join)
                {
                    $join->on('aimsusers.user_id','=','e.sent_by');
                })
                ->join(''.$gb.'.glbanks g', function($join)
                {
                    $join->on('e.account_month','=','g.account_month');
                })
                ->join(''.$gb.'.glbanks g', function($join)
                {
                    $join->on('e.account_year','=','g.account_year');
                })
                ->where('e.type','=','DRECON')
                ->where('e.read','=','0')
                ->where('e.sent_to','=',Auth::user()->user_id)
                ->distinct('e.escalate_id')
                ->get(
                    ['e.req_no as unique_ref', 'g.account_year','e.description','e.type', 'aimsusers.user_name','e.created_at','g.account_month',
                        'g.BANK_ACC_CODE','e.escalate_id'
                    ]
                )->map(function ($item) {
                    $item->action_link = 'clbnkrecon.details';
                    return $item;
                });

    }

    protected function appescalateCloseBNKRecon() {
        $schem = schemaName();
    
        $gb = $schem['gb'];

        return  DB::table(''.$gb.'.escalate_pol e')
                ->join(''.$gb.'.glbanks g', function($join)
                {
                    $join->on('e.req_no','=','g.BANK_ACC_CODE');
                }) ->join(''.$gb.'.aimsusers', function($join)
                {
                    $join->on('aimsusers.user_id','=','e.sent_by');
                })
                ->join(''.$gb.'.glbanks g', function($join)
                {
                    $join->on('e.account_month','=','g.account_month');
                })
                ->join(''.$gb.'.glbanks g', function($join)
                {
                    $join->on('e.account_year','=','g.account_year');
                })
                ->where('e.type','=','ARECON')
                ->where('e.read','=','0')
                ->where('e.sent_to','=',Auth::user()->user_id)
                ->distinct('e.escalate_id')
                ->get(
                    ['e.req_no as unique_ref', 'g.account_year','e.description','e.type', 'aimsusers.user_name','e.created_at','g.account_month',
                        'g.BANK_ACC_CODE','e.escalate_id'
                    ]
                )->map(function ($item) {
                    $item->action_link = 'clbnkrecon.details';
                    return $item;
                });

    }
 
    public function po_fa_escalations() {
        $incoming = DB::table('approval_flow a')->join('approvals e', function($join)
            {
                $join->on('e.approval_id','=','a.approval_id');
            })
            ->join('aimsusers u', function($join)
            {
                $join->on('u.user_id','=','e.user_id');
            })
            ->where('a.approver_id', Auth::user()->user_id)
            ->where('a.status', 'P')
            ->where('a.start_approval', 'Y')
            ->get([
                'a.approval_id', 'e.claim_no as unique_ref', 'u.user_name', 'e.date_created as created_at', 'e.type', 'e.po_number',
                'e.vendor_id', 'e.invoice_no', 'e.description', 'e.stk_take_code','e.account_year','e.account_month'
            ])->map(function ($item) {
                $item->action_link = 'approve_po_fa';
                return $item;
            });

        return $incoming;
    }

    public function budget_approvals() {

        $budget = DB::table('escalate_pol e')
                    ->join('aimsusers u', function($join){ $join->on('u.user_id','=','e.sent_to');})
                    ->where('e.type','=','BDGUW')
                    ->where('e.sent_to', Auth::user()->user_id)
                    ->where('approved', null)
                    ->distinct('e.req_no')
                    ->get(['e.req_no as unique_ref','e.description','e.created_at'])
                    ->map(function ($item) { $item->action_link = 'approvebudget'; return $item; });

        return $budget;
    }

    public function budget_editing_approvals() {

        $budget = DB::table('escalate_pol e')
                    ->join('aimsusers u', function($join){ $join->on('u.user_id','=','e.sent_to');})
                    ->where('e.type','=','EBDGUW')
                    ->where('e.sent_to', Auth::user()->user_id)
                    ->where('approved', null)
                    ->distinct('e.req_no')
                    ->get(['e.req_no as unique_ref','e.description','e.created_at'])
                    ->map(function ($item) { $item->action_link = 'approvebudget'; return $item; });

        return $budget;
    }

}
