<?php

namespace App\Http\Controllers\budget;

use App\Http\Controllers\Controller;
use Auth;
use Carbon\Carbon;
use DB;
use Excel;
use File;
use Response;
use Session;
use View;
use Yajra\Datatables\Datatables;
use Throwable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

use App\BGTparams;
use App\PoReqHeader;
use App\GLBudgetParam;
use App\GLBudgetTotalsParam;
use App\GlMasterbudget;
use App\CompanyGlmasterBudget;
use App\BranchGlmasterBudget;
use App\GlBudgetLevels;
use App\GLBudgetid;
use App\GLBudgetLevel;
use App\Dist_channel;
use App\GlBudgetControl;
use App\Budgetbranches;

use App\Nlcateglevel;
use App\Nlcategory;
use App\Nlaccountcateg;
use App\Nlparams;
use App\Segments;
use App\Cbmast;
use App\Cbtrans;
use App\Currency;
use App\GLaccountstatus;
use App\Nlmst;
use App\Nlmstdtl;
use App\Nlmstbal;
use App\Nlctrl;
use App\Branch;
use App\Glclass;
use App\Aimsuser_web;
use App\Escalate_pol;
use App\SendEmail;
use App\Doctype;
use App\GlBudgetTotalParamMaster;
use Illuminate\Support\Facades\Schema;
use App\Classes\Common\FileUploadManager;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Gate;

use App\Aims_process;
use App\Process_approval_dtl;
use App\Approval_level;
use App\Approval_matrix;
use App\Approver_matrix;
use App\User;
use App\Approvals;
use App\Approval_flow;
use App\Classes\Approvals\ApprovalsMgt;
use App\Classes\Approvals\ApprovalsPo;
use App\Models\Aimsuser;
use Symfony\Component\HttpFoundation\Response as ResponseCode;


    class GLBudgetController extends Controller
    {
        
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function GLBudget_details()
    {
        $dat = array(
            'module' => 'Budget'
        );
        $refocusparam = GlBudgetControl::first() ?? 1;
        return view::make('budget.budgetheader')
            ->with(compact('dat', 'refocusparam'));
    }

    public function view_paramlinks()
    {
        $links = $this->links();

        $links1 = $this->links1();

        $dat = array(
            'main' => 'Parameters',
            'module' => 'GLBudget',
            'submodule' => 'Links List'
        );

        return view('budget.glbudget_linkslist', [
            'links' => $links,
            'links1' => $links1
        ]);
    }


    public function createdraftbudget(Request $request)
    {
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        DB::connection(env('DB_CONNECTION2'))->beginTransaction();

        try {

            $doctype = 'BGT';
            $req_serial = $this->genReqNo();
            $req_no = $doctype . STR_PAD($req_serial, 6, '0', STR_PAD_LEFT) . $request->fiscalYear;

            $attributes = DB::table('BUDGETHEADER')->insert([
                'BUDGET_ID' => $req_no,
                'FISCAL_YEAR' => $request->fiscal_year,
                'DESCRIPTION' => $request->description,
                'START_DATE' => $request->start_date,
                'END_DATE' => $request->end_date,
                'CANCELLED' => 'N',
                'CREATED_BY' => $request->created_by,
                'CREATED_ON' => now()
            ]);

            $req_serial = $this->updDoctypeSerial();



            DB::connection(env('DB_CONNECTION1'))->commit();
            DB::connection(env('DB_CONNECTION2'))->commit();

            return redirect()->back()->with('success', "Generated successfully");
        } catch (\Exception $e) {

            DB::connection(env('DB_CONNECTION1'))->rollback();
            DB::connection(env('DB_CONNECTION2'))->rollback();
            return redirect()->back()->with('error', "Something went wrong");
        }
    }

    public function createbudgetlineheader(Request $request)
    {
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        DB::connection(env('DB_CONNECTION2'))->beginTransaction();

        try {


            $doctype = 'BHL';
            $req_serial = $this->genHeaderline();
            // $req_no = $doctype . STR_PAD($req_serial, 6, '0', STR_PAD_LEFT) . $request->fiscalYear;

            $attributes = DB::table('BUDGETLINESHEADER')->insert([
                'BUDGET_ID' => $request->budget_id,
                'BUDGETLINEHEADER_ID' => $req_serial,
                // 'BUDGETTYPE' => $request->budget_type,
                // 'DEPARTMENT' => $request->department,
                'OFFCD' => $request->offcd,
                'DESCRIPTION' => $request->description,
                // 'BUDGET_AMOUNT' => $request->amount,
                'COMMENTS' => $request->comments,
                'USR_STR' => trim(Auth::user()->user_id)
            ]);

            $req_serial = $this->updHeaderLine();



            DB::connection(env('DB_CONNECTION1'))->commit();
            DB::connection(env('DB_CONNECTION2'))->commit();

            return redirect()->back()->with('success', "Generated successfully");
        } catch (\Exception $e) {
         
            DB::connection(env('DB_CONNECTION1'))->rollback();
            DB::connection(env('DB_CONNECTION2'))->rollback();
            return redirect()->back()->with('error', "Something went wrong");
        }
    }


    public function createbudgetlineitem(Request $request)
    {
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        DB::connection(env('DB_CONNECTION2'))->beginTransaction();
    
        try {
            // Get relevant budget line header
            $budgetlinesheader = DB::table('BUDGETLINESHEADER')
                ->where('BUDGET_ID', $request->budget_id)
                ->where('BUDGETLINEHEADER_ID', $request->budget_header)
                ->first();
    
            $portfolio = $request->portfolio;
            $firstPart = explode(' - ', $portfolio)[0]; // Extracts "020206"
    
            // Prepare monthly amounts
            $months = [
                1 => 'amount_jan',
                2 => 'amount_feb',
                3 => 'amount_mar',
                4 => 'amount_apr',
                5 => 'amount_may',
                6 => 'amount_jun',
                7 => 'amount_jul',
                8 => 'amount_aug',
                9 => 'amount_sep',
                10 => 'amount_oct',
                11 => 'amount_nov',
                12 => 'amount_dec',
            ];
    
            // Insert a record for each month
            foreach ($months as $month => $field) {
                $amount = str_replace(',', '', $request->input($field));
                $amount = is_numeric($amount) ? (float)$amount : 0;
    
                // Calculate ITEM_NO
                $item_no = DB::table('BUDGETLINEITEMS')
                    ->where('BUDGET_ID', $request->budget_id)
                    ->where('BUDGETLINEHEADER_ID', $request->budget_header)
                    ->count() + 1;
    
                DB::table('BUDGETLINEITEMS')->insert([
                    'OFFCD' => $budgetlinesheader->offcd,
                    'GLHEAD' => $request->glhead,
                    'PERIOD_YEAR' => date('Y'), // Adjust as needed
                    'PERIOD_MONTH' => $month,
                    'AMOUNT' => $amount,
                    'PERIOD_DEBITS' => 0, // Default value
                    'PERIOD_CREDITS' => 0, // Default value
                    'DEPT_CODE' => $request->department,
                    'PORTFOLIO' => $firstPart,
                    'BUDGETLINEHEADER_ID' => $request->budget_header,
                    'BUDGET_ID' => $request->budget_id,
                    'ITEM_NO' => $item_no,
                    'PRODUCT' => $request->product,
                    'FORECAST_AMOUNT' => $amount,
                    'TEMP_FORECAST_AMOUNT' => $amount,
                    'COMMENTS' => $request->comments,
                    'BGTTYPE' => $request->bgttype,
                ]);
            }
    
            // Commit transactions
            DB::connection(env('DB_CONNECTION1'))->commit();
            DB::connection(env('DB_CONNECTION2'))->commit();
    
            return redirect()->back()->with('success', "Added successfully");
        } catch (\Exception $e) {
            // Rollback transactions
            DB::connection(env('DB_CONNECTION1'))->rollback();
            DB::connection(env('DB_CONNECTION2'))->rollback();
    
            // Log the error
            \Log::error('Error creating budget line items: ' . $e->getMessage());
    
            return redirect()->back()->with('error', "Something went wrong");
        }
    }


    public function createbudgetlinetype(Request $request)
    {
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        DB::connection(env('DB_CONNECTION2'))->beginTransaction();
    
        try {
            // Get relevant budget line header

            $doctype = 'BHL';

            // $req_serial = $this->genHeaderType();
            // $req_no = $doctype . STR_PAD($req_serial, 6, '0', STR_PAD_LEFT) . $request->fiscalYear;

            $attributes = DB::table('BUDGETLINESHEADERTYPE')->insert([
                'BUDGET_ID' => $request->budget_id,
                'BUDGETLINEHEADER_ID' => $request->budget_header,
                'TYPE' => $request->budgettype,
                'BGTTYPE' => $request->budgettype,
                // 'DEPARTMENT' => $request->department,
                'OFFCD' => $request->offcd,
                'DESCRIPTION' => $request->description,
                // 'BUDGET_AMOUNT' => $request->amount,
                'COMMENTS' => $request->comments,
                'USR_STR' => trim(Auth::user()->user_id)
            ]);

            // $req_serial = $this->updHeaderType();

            // Commit transactions
            DB::connection(env('DB_CONNECTION1'))->commit();
            DB::connection(env('DB_CONNECTION2'))->commit();
    
            return redirect()->back()->with('success', "Added successfully");
        } catch (\Exception $e) {
            // Rollback transactions
            DB::connection(env('DB_CONNECTION1'))->rollback();
            DB::connection(env('DB_CONNECTION2'))->rollback();
    
            // Log the error
            \Log::error('Error creating budget line items: ' . $e->getMessage());
    
            return redirect()->back()->with('error', "Something went wrong");
        }
    }
    



    private function getBudgetTypeValues($bgt_type)
    {
        $op_bgt = $capex_bgt = $cash_bgt = 'N';

        if ($bgt_type == 'O') {
            $op_bgt = 'Y';
        } elseif ($bgt_type == 'X') {
            $capex_bgt = 'Y';
        } elseif ($bgt_type == 'C') {
            $cash_bgt = 'Y';
        }

        return [$op_bgt, $capex_bgt, $cash_bgt];
    }

    private function generateBudgetData($bgtformat, $level_2, $level_3, $level_4, $op_bgt, $capex_bgt, $cash_bgt, $bgtid)
    {
        $bdgtdata = [];

        if (empty($level_3) && empty($level_4)) {
            // Process level 2 only
            foreach ($bgtformat as $bgtItem) {
                foreach ($level_2 as $column2) {
                    $bdgtdata[] = [
                        'item_no' => $bgtItem->item_no,
                        'budget_id' => $bgtid,
                        'description_name' => $bgtItem->description_name,
                        'level1' => 1,
                        'level2' => $column2,
                        'level3' => null, // Set level 3 to null
                        'level4' => null, // Set level 4 to null
                        'account_grp_sec' => $bgtItem->account_grp_sec,
                        'title_flag' => $bgtItem->title_flag,
                        'total_flag' => $bgtItem->total_flag,
                        'parent_total_flag' => $bgtItem->parent_total_flag,
                        'op_bgt' => $op_bgt,
                        'capex_bgt' => $capex_bgt,
                        'cash_bgt' => $cash_bgt
                    ];
                }
            }
        } else if (!empty($level_3) && empty($level_4)) {
            // Process level 2 and level 3 only
            foreach ($bgtformat as $bgtItem) {
                foreach ($level_2 as $column2) {
                    foreach ($level_3 as $column3) {
                        $bdgtdata[] = [
                            'item_no' => $bgtItem->item_no,
                            'budget_id' => $bgtid,
                            'description_name' => $bgtItem->description_name,
                            'level1' => 1,
                            'level2' => $column2,
                            'level3' => $column3,
                            'level4' => null, // Set level 4 to null
                            'account_grp_sec' => $bgtItem->account_grp_sec,
                            'title_flag' => $bgtItem->title_flag,
                            'total_flag' => $bgtItem->total_flag,
                            'parent_total_flag' => $bgtItem->parent_total_flag,
                            'op_bgt' => $op_bgt,
                            'capex_bgt' => $capex_bgt,
                            'cash_bgt' => $cash_bgt
                        ];
                    }
                }
            }
        } else if (!empty($level_3) && !empty($level_4)) {
            // Process level 2, level 3, and level 4
            foreach ($bgtformat as $bgtItem) {
                foreach ($level_2 as $column2) {
                    foreach ($level_3 as $column3) {
                        foreach ($level_4 as $column4) {
                            $bdgtdata[] = [
                                'item_no' => $bgtItem->item_no,
                                'budget_id' => $bgtid,
                                'description_name' => $bgtItem->description_name,
                                'level1' => 1,
                                'level2' => $column2,
                                'level3' => $column3,
                                'level4' => $column4,
                                'account_grp_sec' => $bgtItem->account_grp_sec,
                                'title_flag' => $bgtItem->title_flag,
                                'total_flag' => $bgtItem->total_flag,
                                'parent_total_flag' => $bgtItem->parent_total_flag,
                                'op_bgt' => $op_bgt,
                                'capex_bgt' => $capex_bgt,
                                'cash_bgt' => $cash_bgt
                            ];
                        }
                    }
                }
            }
        }

        return $bdgtdata;
    }





    public function updateDoctype()
    {


        $new_serial = Doctype::where('doc_type', 'BGT')->first();

        return $new_serial->serial_no;

    }

    public function getAllBudgets()
    {
        // Fetch all budgets that are not cancelled, sorted by BUDGET_ID in descending order
        $data = DB::table('BUDGETHEADER')
            ->where('cancelled', 'N') // Only fetch non-cancelled budgets
            ->orderBy('BUDGET_ID', 'desc')
            ->get();

        // Debugging (optional): Check the data retrieved
 

        // Return the data to DataTables for further processing
        return DataTables::of($data)
            // If you want to display a status based on the 'approved' flag
            ->editColumn('approved', function ($data) {
                $statusLabel = $data->approved == 'Y' ? 'Approved' : 'Pending';
                $labelClass = $data->approved == 'Y' ? 'success' : 'warning';

                return '<span class="label label-pill label-' . $labelClass . '">' . $statusLabel . '</span>';
            })
            ->editColumn('amount', function ($row) {
                // Summing the 'amount' column from 'BUDGETLINEITEMS'
                $totalAmount = DB::table('BUDGETLINEITEMS')
                    ->where('budget_id', $row->budget_id)
                    ->sum('amount'); // Directly get the sum of the 'amount' column
            
                // Debugging: Uncomment this if you want to see the result for each row
             
            
                return $totalAmount ?? 0; // Return the sum for the 'budget_amount' column
            })
            ->addColumn('actual', function ($row) {
 
                // Summing the 'amount' column from 'BUDGETLINEITEMS'
                $glheads = DB::table('BUDGETLINEITEMS')
                ->where('budget_id', $row->budget_id)
                ->pluck('glhead');
        
                // Calculate the total amount from the 'NLMSTBAL' table for the retrieved GL heads
                $totalAmount = DB::table('NLMSTBAL')
                    ->whereIn('glhead', $glheads)
                    ->where('PERIOD_YEAR', $budget_header->fiscal_year)
                    ->sum('ytd_bal');
            
                // Debugging: Uncomment this if you want to see the result for each row
                
            
                return $totalAmount ?? 0; // Return the sum for the 'budget_amount' column
            })
            ->addColumn('forecast_amount', function ($row) {
                // Summing the 'amount' column from 'BUDGETLINEITEMS'
                $totalAmount = DB::table('BUDGETLINEITEMS')
                    ->where('budget_id', $row->budget_id)
                    ->sum('forecast_amount'); // Directly get the sum of the 'amount' column
            
                // Debugging: Uncomment this if you want to see the result for each row
          
            
                return $totalAmount ?? 0; // Return the sum for the 'budget_amount' column
            })
            // Add action buttons for editing and viewing details
            ->addColumn('action', function ($data) {
                $btn = '';

                // Edit button triggers the modal
                $btn .= '<a href="#" id="edit-budget" data-id="' . $data->budget_id . '" class="btn btn-xs btn-primary" data-toggle="modal" data-target="#editBudgetModal"><i class="fa fa-pencil-square-o"></i> Edit</a>';

                $btn .= '&nbsp;&nbsp;';

                // Details button - Redirects to the details page
                $detailsUrl = url('gl/budget/glcompanybudget/' . $data->budget_id);
                $btn .= '<a href="' . $detailsUrl . '" class="btn btn-xs btn-default"><i class="fa fa-info-circle"></i> Details</a>';

                return $btn;

            })
            ->rawColumns(['action', 'approved']) // Ensure 'action' and 'approved' columns are rendered as raw HTML
            ->make(true);
    }

    // Fetch budget details
    public function getBudgetDetails(Request $request)
    {
        $budget = DB::table('BUDGETHEADER')->where('BUDGET_ID', $request->budget_id)->first();
        return response()->json($budget);
    }

    // Update budget
    public function updateBudget(Request $request)
    {

        // Validate incoming data
        $request->validate([
            'editBudgetId' => 'required',
            'description' => 'required',
            'fiscal_year' => 'required',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
        ]);

        // Update the budget in the database
        DB::table('BUDGETHEADER')
            ->where('BUDGET_ID', $request->editBudgetId)
            ->update([
                'DESCRIPTION' => $request->description,
                'FISCAL_YEAR' => $request->fiscal_year,
                'START_DATE' => $request->start_date,
                'END_DATE' => $request->end_date,
                'CANCELLED' => $request->cancelled,
            ]);

        return response()->json(['success' => true]);
    }



    public function getrunningbudgets()
    {
        $data = GlMasterbudget::where('approved', 'Y')->get();
        return Datatables::Of($data)
            ->addColumn('action', function ($dt) {
                return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
            })
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function getBGTParamMenu(Request $request)
    {
        $links = BGTparams::orderBy('aims_program_type', 'aims_program_code')
            ->get();
        return Datatables::Of($links)
            ->addColumn('action', function ($lnk) {
                return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
            })
            ->editColumn('aims_program_type', function ($type) {
                if ($type->aims_program_type == 'M') {
                    return "Menu";
                } else {
                    return "Screen";
                }
            })
            ->editColumn('main_menu', function ($main) {
                if ($main->main_menu == 'G') {
                    return "General Ledger";
                } else {
                    return "Front Office";
                }
            })
            ->editColumn('parent_code', function ($par) {
                if ($par->parent_code == 0) {
                    return 'Level One Menu';
                } else {
                    $parent = BGTparams::where('aims_program_code', $par->parent_code)
                        ->get();
                    foreach ($parent as $pare) {
                        return $pare->aims_program_name;
                    }
                }
            })
            ->make(true);
    }

    // Function for Operations Budget Parameters
    public function getOperationsBudgetParam(Request $request)
    {
        // Query from the GLBUDGETPARAM table (Operations budget)
        $data = DB::table('GLBUDGETPARAM')->orderBy('item_no')->get();

        return Datatables::of($data)
            ->addColumn('action', function ($lnk) {
                return '<a class="btn btn-xs edit" data-id="' . $lnk->item_no . '" data-type="operations"><i class="glyphicon glyphicon-edit"></i></a> 
                        <a class="btn btn-xs delete" data-id="' . $lnk->item_no . '" data-type="operations"><i class="glyphicon glyphicon-minus"></i></a>';
            })
            ->make(true);
            
    }

    // Function for Expenditure Budget Parameters
    public function getExpenditureBudgetParam(Request $request)
    {
        // Query from the EXP_GLBUDGETPARAM table (Expenditure budget)
        $data = DB::table('EXP_GLBUDGETPARAM')->orderBy('item_no')->get();

        return Datatables::of($data)
            ->addColumn('action', function ($lnk) {
                return '<a class="btn btn-xs edit" data-id="' . $lnk->item_no . '" data-type="expenditure"><i class="glyphicon glyphicon-edit"></i></a> 
                        <a class="btn btn-xs delete" data-id="' . $lnk->item_no . '" data-type="expenditure"><i class="glyphicon glyphicon-minus"></i></a>';
            })
            ->make(true);
    }

    // Function for Capital Expenditure (CapEx) Budget Parameters
    public function getCapexBudgetParam(Request $request)
    {
        // Query from the CAPEX_GLBUDGETPARAM table (Capital Expenditure budget)
        $data = DB::table('CAPEX_GLBUDGETPARAM')->orderBy('item_no')->get();

        return Datatables::of($data)
            ->addColumn('action', function ($lnk) {
                return '<a class="btn btn-xs edit" data-id="' . $lnk->item_no . '" data-type="capital"><i class="glyphicon glyphicon-edit"></i></a> 
                        <a class="btn btn-xs delete" data-id="' . $lnk->item_no . '" data-type="capital"><i class="glyphicon glyphicon-minus"></i></a>';
            })
            ->make(true);
    }

    // Function for Cashflow Budget Parameters
    public function getCashflowBudgetParam(Request $request)
    {
        // Query from the CASH_GLBUDGETPARAM table (Cashflow budget)
        $data = DB::table('CASH_GLBUDGETPARAM')->orderBy('item_no')->get();

        return Datatables::of($data)
            ->addColumn('action', function ($lnk) {
                return '<a class="btn btn-xs edit" data-id="' . $lnk->item_no . '" data-type="cashflow"><i class="glyphicon glyphicon-edit"></i></a> 
                        <a class="btn btn-xs delete" data-id="' . $lnk->item_no . '" data-type="cashflow"><i class="glyphicon glyphicon-minus"></i></a>';
            })
            ->make(true);
    }

    // Function for Department Budget Parameters
    public function getDepartmentBudgetParam(Request $request)
    {
        // Query from the DEPT_GLBUDGETPARAM table (Department budget)
        $data = DB::table('DEPT_GLBUDGETPARAM')->orderBy('item_no')->get();

        return Datatables::of($data)
            ->addColumn('action', function ($lnk) {
                return '<a class="btn btn-xs edit" data-id="' . $lnk->item_no . '" data-type="department"><i class="glyphicon glyphicon-edit"></i></a> 
                        <a class="btn btn-xs delete" data-id="' . $lnk->item_no . '" data-type="department"><i class="glyphicon glyphicon-minus"></i></a>';
            })
            ->make(true);
    }



    public function getglbudgettotalparam(Request $request)
    {
        if ($request->item_no == "O") {
            $data = DB::table('GLBUDGETTOTALPARAM')
                ->select('item_no')
                ->orderBy('item_no')
                ->groupBy('item_no')
                ->get();

            return Datatables::Of($data)
                ->addColumn('description', function ($row) {
                    $item = DB::table('GLBUDGETPARAM')->where('item_no', $row->item_no)->first();
                    return $item->description_name;

                })
                ->addColumn('action', function ($lnk) {
                    return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
                })
                ->make(true);

        } else if ($request->item_no == "X") {
            $data = DB::table('CAPEXGLBUDGETTOTALPARAM')
                ->select('item_no')
                ->orderBy('item_no')
                ->groupBy('item_no')
                ->get();

            return Datatables::Of($data)
                ->addColumn('description', function ($row) {
                    $item = DB::table('CAPEX_GLBUDGETPARAM')->where('item_no', $row->item_no)->first();
                    return $item->description_name;

                })
                ->addColumn('action', function ($lnk) {
                    return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
                })
                ->make(true);

        } else {
            $data = DB::table('CASHGLBUDGETTOTALPARAM')
                ->select('item_no')
                ->orderBy('item_no')
                ->groupBy('item_no')
                ->get();

            return Datatables::Of($data)
                ->addColumn('description', function ($row) {
                    $item = DB::table('CASH_GLBUDGETPARAM')->where('item_no', $row->item_no)->first();
                    return $item->description_name;

                })
                ->addColumn('action', function ($lnk) {
                    return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
                })
                ->make(true);

        }

    }

    public function getglbudgettotalparammaster(Request $request)
    {

        $data = GLBudgetParam::whereNull('account_grp_sec')
            ->where('total_flag', 'Y')
            ->get();

        return Datatables::Of($data)
            ->addColumn('action', function ($lnk) {
                return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
            })
            ->make(true);
    }

    public function itemnodetails(Request $request)
    {
        $item_no = $request->batch_no;

        $description = (GLBudgetParam::where('item_no', $item_no)->get()[0])->description_name;

        return view::make('budget.glbudgettotalsparams')->with(compact('item_no', 'description'));
    }


    public function check_children(Request $request)
    {
        $menu1 = BGTparams::where('parent_code', $request->get('progcode'))->count();

        echo $menu1;
    }

    public function getaccountgroups()
    {
        $glh = Segments::orderBy('position', 'DESC')->first();
        $accounts = Nlparams::where('prid', $glh->segment_code)->distinct()->get(['prsno']);
        $segments = Segments::where('segment_code', '<>', $glh->segment_code)
            ->orderBy('position', 'ASC')
            ->get();
        $data = [];

        foreach ($accounts as $grp) {
            $row = [
                'seg_' . $segments[0]->position => substr($grp->prsno, 0, (int) $segments[0]->length),
                'seg_desc_' . $segments[0]->position => Nlparams::where('prid', $segments[0]->segment_code)
                    ->whereRaw("trim(prsno) = '" . substr($grp->prsno, 0, $segments[0]->length) . "'")
                    ->first()->prdesc,
            ];

            // Generate a unique key based on the row data
            $key = implode('_', $row);

            // Save the row in the $data array if it doesn't exist already
            if (!isset($data[$key])) {
                $data[$key] = $row;
            }
        }

        // Reset the array keys to ensure sequential numbering
        $data = array_values($data);

        return response()->json($data);
    }

    public function getaccountgroupsections(Request $request)
    {

        $data = Nlparams::where('prid', 'SEC')
            ->whereRaw('prsno LIKE ?', [$request->account_grp . '%'])
            ->get();

        return response()->json($data);
    }

    public function getbgtbranches()
    {

        $data = Branch::where('is_active', 'Y')
            ->get();

        return response()->json($data);
    }

    public function getbgtproducts()
    {

        $data = Glclass::whereNull('deleted')->get();

        return response()->json($data);
    }

    public function validatebgtamount(Request $request)
    {

        $companydata = CompanyGlmasterBudget::where('budget_id', $request->budgetid)
            ->where('item_no', $request->cell)
            ->get();

        $totalBudgetAmt_company = 0;

        foreach ($companydata as $data) {
            $totalBudgetAmt_company += $data->budget_amt;
        }


        $branchdata = BranchGlmasterBudget::where('budget_id', $request->budgetid)
            ->where('item_no', $request->cell)
            ->where('branch', '!=', $request->branch)
            ->get();


        $totalBudgetAmt_branch = 0;

        foreach ($branchdata as $data) {
            $totalBudgetAmt_branch += $data->budget_amt;
        }

        $budget_balance = $totalBudgetAmt_company - $totalBudgetAmt_branch;

        return response()->json($budget_balance);
    }

    public function getbgtchannels()
    {

        $data = Dist_channel::all();

        return response()->json($data);
    }

    public function viewbgtbranch(Request $request, $budget_id = null,$offcd = null)
    {
        $budget_header = DB::table('BUDGETHEADER')->where('budget_id',$budget_id)->first();

        $budget_office = DB::table('NLPARAMS')->where('prsno', $offcd)->where('prid','OFF')->first();

        return view::make('budget.glbranchbudget')->with(compact('budget_header','budget_office'));


    }

    public function viewbgtdept(Request $request, $budget_id = null,$offcd = null)
    {
        $budget_header = DB::table('BUDGETHEADER')->where('budget_id',$budget_id)->first();

        $budget_department = DB::table('NLPARAMS')->where('prsno', $offcd)->where('prid','DEP')->first();

        return view::make('budget.gldeptbudget')->with(compact('budget_header','budget_department'));


    }

    public function getbgttotalsitemno(Request $request)
    {
        if ($request->type == "O") {

            $data = GLBudgetParam::whereNull('account_grp_sec')
                ->where('total_flag', 'Y')
                ->get();

        } else if ($request->type == "X") {

            $data = DB::table('capex_glbudgetparam')->whereNull('account_grp_sec')
                ->where('total_flag', 'Y')
                ->get();

        } else {

            $data = DB::table('cash_glbudgetparam')->whereNull('account_grp_sec')
                ->where('total_flag', 'Y')
                ->get();

        }


        return response()->json($data);
    }

    public function getbgtitemtotalsitemno()
    {
        $data = GLBudgetParam::whereNull('account_grp_sec')
            ->where('total_flag', 'Y')
            ->get();

        return response()->json($data);
    }

    public function getbgtaccount_groupsesc(Request $request)
    {

        if ($request->type == "O") {

            $data = GLBudgetParam::whereNotNull('account_grp_sec')->get();

        } else if ($request->type == "X") {

            $data = DB::table('capex_glbudgetparam')->whereNotNull('account_grp_sec')->get();


        } else {

            $data = DB::table('cash_glbudgetparam')->whereNotNull('account_grp_sec')->get();

        }


        return response()->json($data);
    }

    public function xaddbgtparams(Request $request)
    {

        DB::beginTransaction();

        try {

            if ($request->budget_type == 'O') {

                $bgtparam = GLBudgetParam::create([
                    'item_no' => $request->item_no,
                    'title_flag' => $request->titleflag,
                    'description_name' => $request->description,
                    'budget_year' => $request->budgetyear,
                    'account_grp_sec' => $request->acc_grp_sec,
                    'total_flag' => $request->total_flag,
                    'parent_total_flag' => $request->parent_total_flag
                ]);


            } elseif ($request->budget_type == 'X') {

                $attributes = DB::table('capex_glbudgetparam')->insert([
                    'item_no' => $request->item_no,
                    'title_flag' => $request->titleflag,
                    'description_name' => $request->description,
                    'budget_year' => $request->budgetyear,
                    'account_grp_sec' => $request->acc_grp_sec,
                    'total_flag' => $request->total_flag,
                    'parent_total_flag' => $request->parent_total_flag
                ]);

            } else {

                $attributes = DB::table('cash_glbudgetparam')->insert([
                    'item_no' => $request->item_no,
                    'title_flag' => $request->titleflag,
                    'description_name' => $request->description,
                    'budget_year' => $request->budgetyear,
                    'account_grp_sec' => $request->acc_grp_sec,
                    'total_flag' => $request->total_flag,
                    'parent_total_flag' => $request->parent_total_flag
                ]);

            }

            DB::commit();
            return redirect()->back()->with('success', "added successfully");
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', "something went wrong");
        }
    }


    public function addbgttotalsparams(Request $request)
    {

        DB::beginTransaction();

        try {

            if ($request->type == "O") {
                $bgtparam = GLBudgetTotalsParam::create([
                    'item_no' => $request->item_no,
                    'account_grp_sec' => $request->act_grp_sec,
                    'referral_item_no' => $request->referral_item_no,
                    'add_less' => $request->add_less
                ]);

            } elseif ($request->type == "X") {
                $bgtparam = DB::table('CAPEXGLBUDGETTOTALPARAM')->insert([
                    'item_no' => $request->item_no,
                    'account_grp_sec' => $request->act_grp_sec,
                    'referral_item_no' => $request->referral_item_no,
                    'add_less' => $request->add_less
                ]);

            } else {
                $bgtparam = DB::table('CASHGLBUDGETTOTALPARAM')->insert([
                    'item_no' => $request->item_no,
                    'account_grp_sec' => $request->act_grp_sec,
                    'referral_item_no' => $request->referral_item_no,
                    'add_less' => $request->add_less
                ]);

            }


            DB::commit();
            return redirect()->back()->with('success', "added successfully");
        } catch (\Exception $e) {

            DB::rollback();
            return redirect()->back()->with('error', "something went wrong");
        }
    }

    public function addbgttotalsparammaster(Request $request)
    {

        DB::beginTransaction();

        try {
            $bgtparam = GLBudgetTotalsParam::create([
                'item_no' => $request->item_no,
                'account_grp_sec' => $request->act_grp_sec,
                'referral_item_no' => $request->referral_item_no,
                'add_less' => $request->add_less
            ]);

            DB::commit();
            return redirect()->back()->with('success', "added successfully");
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', "something went wrong");
        }
    }


    public function fetch_paramlinks(Request $request)
    {
        $param = BGTparams::where('aims_sys', $request->get('sysname'))->where('main_menu', $request->get('syscateg'))->where('aims_program_type', 'M')->get();


        echo $param;
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function glbudgetparam()
    {

        return view::make('budget.glbudgetparam');
    }

    public function glbudgetlevels()
    {
        $driver = DB::getDriverName();

        if ($driver === 'pgsql') {
            // PostgreSQL
            $sql = "
                SELECT tablename AS table_name
                FROM pg_tables
                WHERE schemaname = 'public'
                ORDER BY tablename ASC
            ";
        } elseif (in_array($driver, ['oracle', 'oci', 'oci8'])) {
            // Oracle
            $sql = "
                SELECT table_name
                FROM user_tables
                ORDER BY table_name ASC
            ";
        } else {
            throw new \Exception("Unsupported database driver: {$driver}");
        }

        $tables = DB::select($sql);

        return view('budget.glbudgetlevels', compact('tables'));
    }

    public function glbudgetlevelsDatatable()
    {
        $data = GlBudgetLevels::get();

        return Datatables::Of($data)
            ->addColumn('action', function ($lnk) {
                return '<a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i>delete</a>';
            })
            ->make(true);
    }

    public function glbudgettotalsparam()
    {

        return view::make('budget.glbudgettotalsparams');
    }

    public function glbudgettotalsparammaster()
    {

        return view::make('budget.glbudgettotalparammaster');
    }

    public function glbudgetcontrols()
    {
        $controls = GlBudgetControl::where('id', 1)->get()[0];

        return view::make('budget.glbudgetcontrols')->with(compact('controls'));
    }

    public function storeglbudgetcontrols(Request $request)
    {

        $count = GlBudgetControl::where('id', 1)->count();
        if ($count > 0) {
            GlBudgetControl::where('id', 1)
                ->update([
                    'approval' => $request->approval_Process,
                    'manual_allocation' => $request->manual_estimate,
                    'refocus' => $request->refocus_times
                ]);
            Session::flash('success', 'Controls Editted Successfully');
        } else {
            GlBudgetControl::create([
                'approval' => $request->approval_Process,
                'manual_allocation' => $request->manual_estimate,
                'refocus' => $request->refocus_times
            ]);
            Session::flash('success', 'Controls added Successfully');
        }

        return redirect()->back();

    }

    public function authorizeglbudget(Request $request)
    {


        DB::beginTransaction();

        try {
            $deactivate_other = GlMasterbudget::where('account_year', $request->authorize_budget_year)
                ->update([
                    'status' => 'I'
                ]);

            if ($deactivate_other) {
                GlMasterbudget::where('budget_id', $request->authorize_budget_id)
                    ->update([
                        'approved' => 'Y',
                        'status' => 'A'
                    ]);
            }

            DB::commit();
            return redirect()->route('glcompanybudget', ['budget_id' => $request->authorize_budget_id])->with('success', "budget approved");
        } catch (\Exception $e) {

            DB::rollback();
            return redirect()->route('glcompanybudget', ['budget_id' => $request->authorize_budget_id])->with('error', "something went wrong");
        }

    }

    public function lockglbudget(Request $request)
    {


        DB::beginTransaction();

        try {

            $parts = explode('/', $request->lock_budget_id);
            $bgid = $parts[0];

            GlMasterbudget::where('budget_id', $bgid)
                ->update([
                    'lock_budget' => 'Y'
                ]);

            DB::commit();
            return redirect()->route('gl.budget')->with('success', "budget locked");
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('gl.budget')->with('error', "something went wrong");
        }

    }

    public function glcompanybudget(Request $request, $budget_id = null)
    {



        $budget_header = DB::table('BUDGETHEADER')->where('budget_id',$budget_id)->first();

        $assumptions = DB::table('BUDGETASSUMPTIONS')->where('budget_id',$budget_id)->count();

        $departments = Nlparams::where('prid', 'DEP')->get();

        $offices = Nlparams::where('prid', 'OFF')->get();

        $headers = DB::table('BUDGETLINESHEADER')->where('budget_id',$budget_id)->get();


        //!approval 
		$user_id = trim(Auth::user()->user_id);
		$process = Aims_process::where('slug','budget-approvals')->first();
		$process_code= trim($process->process_code);
	
		$process = Aims_process::with(['process_dtl',
				'approval_levels'
			])
			->where('process_code',trim($process_code))
			->first();
			
		// fetch approvals if any
		$approval_dtl = Approvals::with('approval_flow')
									->where('req_no',trim($budget_id))
									->where('type', 'APRGLB')
									->where('utilized', 'N')
									->orderBy('date_created','DESC')
									->first();

		$check_approval = Approvals::where('req_no',trim($budget_id))
									->where('type', 'APRGLB')
                                    ->where('utilized', 'N')
									->orderBy('date_created','DESC')
									->first();


		if(isset($check_approval)){
			$requested_by = Aimsuser::where('user_id',$check_approval->user_id)->first();

			if($check_approval->status == 'A'){
				$status = 'A';
				
			}
			elseif($check_approval->status == 'R'){
				$status = 'R';
				$msg = 'Budget was rejected';
			}
			elseif($check_approval->status == 'P'){
				$status = 'P';
				$msg = 'Budget has a Pending approval';
			}
		}

		$approval_status = $status ;
		$approval_msg = $msg;


        return view::make('budget.glcompanybudget')->with(compact(
            'budget_id','budget_header','departments','offices','headers','process','approval_status','approval_msg','approval_dtl','status','assumptions'

        ));
    }

    public function getglcompanybudgetheaders(Request $request)
    {


        $data = DB::table('BUDGETLINESHEADER')->where('budget_id',$request->budget_id)->get();
        $budget_header = DB::table('BUDGETHEADER')->where('budget_id',$request->budget_id)->first();

        return Datatables::Of($data)
        ->addColumn('action', function ($lnk) {
            // Assuming `line_id` is available in the data (if not, replace with correct field)
            return '<a href="'. route('budgetlinetype', ['budget_id' => $lnk->budget_id, 'line_id' => $lnk->budgetlineheader_id]) .'" class="btn btn-xs btn-info">Details</a> 
                    <a href="javascript:void(0);" class="btn btn-xs btn-danger eddel" id="eddel" data-id="' . $lnk->budgetlineheader_id . '" data-budgetid="' . $lnk->budget_id . '">Remove</a>';
        })
        ->editColumn('offcd', function ($row) {
            $data = Nlparams::where('prid', 'OFF')->where('prsno', $row->offcd)->first();
            return $data->prdesc ?? 'N/A';
        })
        
        ->editColumn('usr_str', function ($row) {
            $data = Aimsuser_web::where('user_id', $row->usr_str)->first();
            return $data->name;

        })

        ->editColumn('budget_amount', function ($row) {
            // Summing the 'amount' column from 'BUDGETLINEITEMS'
            $totalAmount = DB::table('BUDGETLINEITEMS')
                ->where('budget_id', $row->budget_id)
                ->where('BUDGETLINEHEADER_ID', $row->budgetlineheader_id)
                ->sum('amount'); // Directly get the sum of the 'amount' column
        
            return $totalAmount ?? 0; // Return the sum for the 'budget_amount' column
        })

        ->editColumn('actual_amount', function ($row) use ($budget_header) {
            // Get the GL heads associated with the specific budget line item
            $glheads = DB::table('BUDGETLINEITEMS')
                ->where('budget_id', $row->budget_id)
                ->where('BUDGETLINEHEADER_ID', $row->budgetlineheader_id)
                ->pluck('glhead');
        
            // Calculate the total amount from the 'NLMSTBAL' table for the retrieved GL heads
            $totalAmount = DB::table('NLMSTBAL')
                ->whereIn('glhead', $glheads)
                ->where('offcd', $row->offcd)
                ->where('PERIOD_YEAR', $budget_header->FISCAL_YEAR)
                ->sum('ytd_bal');
        
            // Return the total amount or 0 if no value is found
            return $totalAmount ?: 0;
        })
        

        ->editColumn('forecast_amount', function ($row) {
            // Summing the 'amount' column from 'BUDGETLINEITEMS'
            $totalAmount = DB::table('BUDGETLINEITEMS')
                ->where('budget_id', $row->budget_id)
                ->where('BUDGETLINEHEADER_ID', $row->budgetlineheader_id)
                ->sum('forecast_amount'); // Directly get the sum of the 'amount' column
        
            return $totalAmount ?? 0; // Return the sum for the 'budget_amount' column
        })
        
        ->make(true);


    }





    public function getglcompanybudgetheadertype(Request $request)
    {
        // Validate required parameters
        if (!$request->has('budget_id') || !$request->has('budgetheaderline')) {
            return response()->json(['error' => 'Missing required parameters'], 400);
        }

        $budget_header = DB::table('BUDGETHEADER')->where('budget_id',$request->budget_id)->first();

        // Fetch grouped data
        $data = DB::table('BUDGETLINESHEADERTYPE')
            ->where('BUDGET_ID', $request->budget_id)
            ->where('BUDGETLINEHEADER_ID', $request->budgetheaderline)
            ->orderBy('BUDGETLINEHEADER_ID')
            ->get();

        $nlctrl = DB::table('NLCTRL')->first();

        // Return data for DataTable
        return Datatables::of($data)
            ->editColumn('bgttype', function ($row) {
                // Map the type to a human-readable label
                if ($row->bgttype === 'O') {
                    return 'Operational Budget';
                } elseif ($row->bgttype === 'C') {
                    return 'Capital Budget';
                } else {
                    return 'Unknown Type'; // Fallback for unrecognized types
                }
            })

            ->addColumn('amount', function ($lnk) {

                $budgetLine_amounts = DB::table('BUDGETLINEITEMS')
                ->where('budget_id',  $lnk->budget_id)
                ->where('bgttype',  $lnk->bgttype)
                ->where('offcd', '=', $lnk->offcd)
                ->sum('amount'); // Assuming we expect only one row

                return $budgetLine_amounts ?? '0';


            })
            // ->addColumn('actual', function ($lnk) use ($budget_header, $nlctrl) {

            //     // Get GLHEADs for the current line
            //     $glhead = DB::table('BUDGETLINEITEMS')
            //         ->where('BUDGET_ID', $lnk->budget_id)
            //         ->where('BUDGETLINEHEADER_ID', $lnk->budgetlineheader_id)
            //         ->where('BGTTYPE', $lnk->bgttype)
            //         ->distinct()
            //         ->pluck('glhead');
    
            //     if ($lnk->bgttype === 'O') { // Operational Budget Logic
            //         if ($budget_header->fiscal_year == date('Y')) { // Current year
            //             $actuals = DB::table('NLMSTBAL')
            //                 ->whereIn('GLHEAD', $glhead)
            //                 ->where('PERIOD_YEAR', $budget_header->fiscal_year)
            //                 ->where('PERIOD_MONTH', $nlctrl->account_month)
            //                 ->where('OFFCD', $lnk->offcd)
            //                 ->select(DB::raw('SUM(YTD_BAL) as total'))
            //                 ->groupBy('PERIOD_YEAR')
            //                 ->first();
            //         } elseif ($budget_header->fiscal_year < (date('Y'))) { // Previous year
            //             $actuals = DB::table('NLMSTBAL')
            //                 ->whereIn('GLHEAD', $glhead)
            //                 ->where('PERIOD_YEAR', $budget_header->fiscal_year)
            //                 ->where('OFFCD', $lnk->offcd)
            //                 ->where('PERIOD_MONTH', 12)
            //                 ->select(DB::raw('SUM(YTD_BAL) as total'))
            //                 ->groupBy('PERIOD_YEAR')
            //                 ->first();
            //         }
    
            //         // Check if we got any results
            //         return $actuals ? $actuals->total : 0;
            //     } elseif ($lnk->bgttype === 'C') {
            //         $pref = Nlparams::where('prid', 'GRP')->whereIn('PRDESC', ['ASSETS', 'LIABILITIES', 'EQUITY'])->pluck('prsno');
    
            //         // Fetch GLHEADs for Capital Budget, assuming similar query structure
            //         $glheads = DB::table('BUDGETLINEITEMS')
            //             ->where('BUDGET_ID', $lnk->budget_id)
            //             ->where('BUDGETLINEHEADER_ID', $lnk->budgetlineheader_id)
            //             ->where('BGTTYPE', 'C')
            //             ->distinct()
            //             ->pluck('glhead');
    
            //             if ($budget_header->fiscal_year == date('Y')) { // Current year
            //                 $actuals = DB::table('NLMSTBAL')
            //                     ->whereIn('GLHEAD', $glhead)
            //                     ->where('PERIOD_YEAR', $budget_header->fiscal_year)
            //                     ->where('PERIOD_MONTH', $nlctrl->account_month)
            //                     ->where('OFFCD', $lnk->offcd)
            //                     ->select(DB::raw('SUM(YTD_BAL) as total'))
            //                     ->groupBy('PERIOD_YEAR')
            //                     ->first();
            //             } elseif ($budget_header->fiscal_year < (date('Y'))) { // Previous year
            //                 $actuals = DB::table('NLMSTBAL')
            //                     ->whereIn('GLHEAD', $glhead)
            //                     ->where('PERIOD_YEAR', $budget_header->fiscal_year)
            //                     ->where('OFFCD', $lnk->offcd)
            //                     ->where('PERIOD_MONTH', 12)
            //                     ->select(DB::raw('SUM(YTD_BAL) as total'))
            //                     ->groupBy('PERIOD_YEAR')
            //                     ->first();
            //             }
    
            //         return $actuals ? $actuals->total : 0;
            //     }
    
            //     return 0; // Default if no conditions match
            // })


            ->addColumn('actual', function ($lnk) use ($budget_header, $nlctrl) {
                // Get required fields from BUDGETLINEITEMS
                $lineItems = DB::table('BUDGETLINEITEMS')
                    ->where('BUDGET_ID', $lnk->budget_id)
                    ->where('BUDGETLINEHEADER_ID', $lnk->budgetlineheader_id)
                    ->where('BGTTYPE', $lnk->bgttype)
                    ->distinct()
                    ->select('offcd', 'dept_code', 'portfolio', 'product', 'glhead')
                    ->get();

                $totalActuals = 0;

                // Loop through each line item and calculate the sum
                foreach ($lineItems as $item) {
                    // Prepare the query
                    $query = DB::table('NLMSTBAL')
                        ->where('GLHEAD', $item->glhead)
                        ->where('PERIOD_YEAR', $budget_header->fiscal_year)
                        ->where('OFFCD', $item->offcd)
                        ->where('DEPT_CODE', $item->dept_code)
                        ->where('PRODUCT', $item->product)
                        ->where('PORTFOLIO', $item->portfolio);
            
                    if ($budget_header->fiscal_year == date('Y')) { // Current year
                        $query->where('PERIOD_MONTH', $nlctrl->account_month);
                    } elseif ($budget_header->fiscal_year < date('Y')) { // Previous year
                        $query->where('PERIOD_MONTH', 12);
                    }
            
                    // Execute the query and get the sum
                    $actuals = $query
                        ->selectRaw('SUM(YTD_BAL) as total')
                        ->groupBy('PERIOD_YEAR')
                        ->first();
            
                    // Add to totalActuals, defaulting to 0 if no result
                    $totalActuals += $actuals ? $actuals->total : 0;
                }
            
                return $totalActuals;

            
            
                // if ($lnk->bgttype === 'O') { // Operational Budget Logic
                //     if ($budget_header->fiscal_year == date('Y')) { // Current year
                //         // $actuals = DB::table('NLMSTBAL')
                //         //     ->whereIn('GLHEAD', $glheads)
                //         //     ->where('PERIOD_YEAR', $budget_header->fiscal_year)
                //         //     ->where('PERIOD_MONTH', $nlctrl->account_month)
                //         //     ->whereIn('OFFCD', $offcds)
                //         //     ->select(DB::raw('SUM(YTD_BAL) as total'))
                //         //     ->groupBy('PERIOD_YEAR')
                //         //     ->first();
                //     } elseif ($budget_header->fiscal_year < date('Y')) { // Previous year
                //         // $actuals = DB::table('NLMSTBAL')
                //         //     ->whereIn('GLHEAD', $glheads)
                //         //     ->where('PERIOD_YEAR', $budget_header->fiscal_year)
                //         //     ->whereIn('OFFCD', $offcds)
                //         //     ->where('PERIOD_MONTH', 12)
                //         //     ->select(DB::raw('SUM(YTD_BAL) as total'))
                //         //     ->groupBy('PERIOD_YEAR')
                //         //     ->first();
                //     }
            
                //     return $actuals ? $actuals->total : 0;
                // } elseif ($lnk->bgttype === 'C') { // Capital Budget Logic
                //     $pref = Nlparams::where('prid', 'GRP')
                //         ->whereIn('PRDESC', ['ASSETS', 'LIABILITIES', 'EQUITY'])
                //         ->pluck('prsno');
            
                //     if ($budget_header->fiscal_year == date('Y')) { // Current year
                //         $actuals = DB::table('NLMSTBAL')
                //             ->whereIn('GLHEAD', $glheads)
                //             ->where('PERIOD_YEAR', $budget_header->fiscal_year)
                //             ->where('PERIOD_MONTH', $nlctrl->account_month)
                //             ->whereIn('OFFCD', $offcds)
                //             ->select(DB::raw('SUM(YTD_BAL) as total'))
                //             ->groupBy('PERIOD_YEAR')
                //             ->first();
                //     } elseif ($budget_header->fiscal_year < date('Y')) { // Previous year
                //         $actuals = DB::table('NLMSTBAL')
                //             ->whereIn('GLHEAD', $glheads)
                //             ->where('PERIOD_YEAR', $budget_header->fiscal_year)
                //             ->whereIn('OFFCD', $offcds)
                //             ->where('PERIOD_MONTH', 12)
                //             ->select(DB::raw('SUM(YTD_BAL) as total'))
                //             ->groupBy('PERIOD_YEAR')
                //             ->first();
                //     }
            
                //     return $actuals ? $actuals->total : 0;
                // }
            
                // return 0; // Default if no conditions match
            })
            
            ->addColumn('forecast_amount', function ($lnk) {

                $budgetLine_amounts = DB::table('BUDGETLINEITEMS')
                ->where('budget_id',  $lnk->budget_id)
                ->where('bgttype',  $lnk->bgttype)
                ->where('offcd', '=', $lnk->offcd)
                ->sum('forecast_amount'); // Assuming we expect only one row

                return $budgetLine_amounts ?? '0';
            })

            ->addColumn('action', function ($lnk) {

                return '<a href="'. route('budgetlineitems', ['budget_id' => $lnk->budget_id, 'line_id' => $lnk->budgetlineheader_id,'type' => $lnk->type]) .'" class="btn btn-xs btn-info">Details</a> 
                <a href="javascript:void(0);" class="btn btn-xs btn-danger eddel" id="eddel" data-id="' . $lnk->budgetlineheader_id . '" data-budgetid="' . $lnk->budget_id . '" data-type="' . $lnk->type . '">Remove</a>';

            })
            ->rawColumns(['action'])
            ->make(true);
    }


    public function getglcompanybudgetlineitems(Request $request)
    {
        // Validate required parameters
        if (!$request->has('budget_id') || !$request->has('budgetheaderline')) {
            return response()->json(['error' => 'Missing required parameters'], 400);
        }

        $nlctrl = DB::table('NLCTRL')->first();

        // Fetch grouped data
        $budgetLineItems = DB::table('BUDGETLINEITEMS')
            ->selectRaw('
                BUDGET_ID,
                BUDGETLINEHEADER_ID,
                PERIOD_YEAR,
                GLHEAD,
                COMMENTS,
                BGTTYPE,
                SUM(AMOUNT) as total_amount,
                SUM(COALESCE(PERIOD_DEBITS, 0)) as period_debits,
                SUM(COALESCE(PERIOD_CREDITS, 0)) as period_credits,
                SUM(COALESCE(FORECAST_AMOUNT, 0)) as forecast_amount,
                DEPT_CODE,
                OFFCD,
                PORTFOLIO,
                PRODUCT
            ')
            ->where('BUDGET_ID', $request->budget_id)
            ->where('BUDGETLINEHEADER_ID', $request->budgetheaderline)
            ->where('BGTTYPE', $request->bgttype)
            ->groupBy(
                'BUDGET_ID',
                'BUDGETLINEHEADER_ID',
                'PERIOD_YEAR',
                'GLHEAD',
                'COMMENTS',
                'BGTTYPE',
                'DEPT_CODE',
                'OFFCD',
                'PORTFOLIO',
                'PRODUCT'
            )
            ->orderBy('PERIOD_YEAR')
            ->get();

        // Fetch related data
        $departmentIds = $budgetLineItems->pluck('dept_code')->unique()->filter();
        $officeCodes = $budgetLineItems->pluck('offcd')->unique()->filter();

        $departments = Nlparams::where('prid', 'DEP')->whereIn('prsno', $departmentIds)->get()->keyBy('prsno');
        $offices = Nlparams::where('prid', 'OFF')->whereIn('prsno', $officeCodes)->get()->keyBy('prsno');

        // Process each item with the pre-fetched data
        $data = $budgetLineItems->map(function ($item) use ($departments, $offices) {
            $item->department_desc = $departments[$item->dept_code]->prdesc ?? 'N/A';
            $item->offcd_desc = $offices[$item->offcd]->prdesc ?? 'N/A';
            return $item;
        });

        // Return data for DataTable
        return Datatables::of($data)
            ->addColumn('action', function ($lnk) {

                return '<button type="button" class="btn btn-icon toggle-details" 
                            data-header-id="' . e($lnk->budgetlineheader_id) . '" 
                            data-budget-id="' . e($lnk->budget_id ?? '') . '" 
                            data-glhead="' . e($lnk->glhead ?? '') . '" 
                            data-dept-code="' . e($lnk->dept_code ?? '') . '" 
                            data-portfolio="' . e($lnk->portfolio ?? '') . '" 
                            data-offcd="' . e($lnk->offcd ?? '') . '">
                            <i class="fa fa-plus-circle"></i>
                        </button>';
            })
            ->addColumn('actual', function ($lnk) use ($nlctrl) {
                // dd($lnk);

                // Get GLHEADs for the current line
                // $glhead = DB::table('BUDGETLINEITEMS')
                //     ->where('BUDGET_ID', $lnk->budget_id)
                //     ->where('BUDGETLINEHEADER_ID', $lnk->budgetlineheader_id)
                //     ->where('BGTTYPE', $lnk->bgttype)
                //     ->where('OFFCD', $lnk->offcd)
                //     ->glhead('OFFCD', $lnk->glhead)
                //     ->distinct()
                //     ->pluck('glhead');
    
                if ($lnk->bgttype === 'O') { // Operational Budget Logic
                    if ($lnk->period_year == date('Y')) { // Current year
                        
                        $actuals = DB::table('NLMSTBAL')
                            ->where('GLHEAD', $lnk->glhead)
                            ->where('PERIOD_YEAR', $lnk->period_year)
                            ->where('PERIOD_MONTH', $nlctrl->account_month)
                            ->where('OFFCD', $lnk->offcd)
                            ->where('DEPT_CODE', $lnk->dept_code)
                            ->where('PRODUCT', $lnk->product)
                            ->where('PORTFOLIO', $lnk->portfolio)
                            ->selectRaw('SUM(YTD_BAL) as total')
                            ->groupBy('PERIOD_YEAR')
                            ->first();
                    } elseif ($budget_header->fiscal_year < (date('Y'))) { // Previous year
                        $actuals = DB::table('NLMSTBAL')
                            ->whereIn('GLHEAD', $lnk->glhead)
                            ->where('PERIOD_YEAR', $lnk->period_year)
                            ->where('OFFCD', $lnk->offcd)
                            ->where('DEPT_CODE', $lnk->dept_code)
                            ->where('PRODUCT', $lnk->product)
                            ->where('PORTFOLIO', $lnk->portfolio)
                            ->where('PERIOD_MONTH', 12)
                            ->selectRaw('SUM(YTD_BAL) as total')
                            ->groupBy('PERIOD_YEAR')
                            ->first();
                    }
    
                    // Check if we got any results
                    return $actuals ? $actuals->total : 0;
                } elseif ($lnk->bgttype === 'C') {
    
                    if ($lnk->period_year == date('Y')) { // Current year
                        $actuals = DB::table('NLMSTBAL')
                            ->where('GLHEAD', $lnk->glhead)
                            ->where('PERIOD_YEAR', $lnk->period_year)
                            ->where('PERIOD_MONTH', $nlctrl->account_month)
                            ->where('OFFCD', $lnk->offcd)
                            ->where('DEPT_CODE', $lnk->dept_code)
                            ->where('PRODUCT', $lnk->product)
                            ->where('PORTFOLIO', $lnk->portfolio)
                            ->selectRaw('SUM(YTD_BAL) as total')
                            ->groupBy('PERIOD_YEAR')
                            ->first();
                    } elseif ($budget_header->fiscal_year < (date('Y'))) { // Previous year
                        $actuals = DB::table('NLMSTBAL')
                            ->whereIn('GLHEAD', $lnk->glhead)
                            ->where('PERIOD_YEAR', $lnk->period_year)
                            ->where('OFFCD', $lnk->offcd)
                            ->where('DEPT_CODE', $lnk->dept_code)
                            ->where('PRODUCT', $lnk->product)
                            ->where('PORTFOLIO', $lnk->portfolio)
                            ->where('PERIOD_MONTH', 12)
                            ->selectRaw('SUM(YTD_BAL) as total')
                            ->groupBy('PERIOD_YEAR')
                            ->first();
                    }

                    return $actuals ? $actuals->total : 0;
                }
    
                return 0; // Default if no conditions match
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    public function getglcompanybudgetlineitemsdetails(Request $request, $budget_id = null, $headerId = null, $glhead = null, $bgttype = null)
    {
        try {
            // Validate required parameters
            if ((!$budget_id) || (!$headerId)) {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            // Fetch all budget line items
            $budgetLineItems = DB::table('BUDGETLINEITEMS')
                ->where('BUDGET_ID', $budget_id)
                ->where('BUDGETLINEHEADER_ID', $headerId)
                ->where('BGTTYPE', $bgttype)
                ->where('GLHEAD', $glhead)
                ->orderBy('PERIOD_MONTH')
                ->get();


            $budget_header = DB::table('BUDGETHEADER')->where('budget_id',$budget_id)->first();

            // Pre-fetch NLCTRL data
            $nlctrl = DB::table('NLCTRL')->first();
            $currentPeriodMonth = $nlctrl->account_month ?? 0;
            $currentPeriodYear = $nlctrl->account_year ?? 0;

            // Collect unique department and office codes
            $departmentIds = $budgetLineItems->pluck('dept_code')->unique()->filter();
            $officeCodes = $budgetLineItems->pluck('offcd')->unique()->filter();

            // Fetch departments and offices in bulk
            $departments = Nlparams::where('prid', 'DEP')
                ->whereIn('prsno', $departmentIds)
                ->pluck('prdesc', 'prsno'); // Use pluck for efficient mapping
            $offices = Nlparams::where('prid', 'OFF')
                ->whereIn('prsno', $officeCodes)
                ->pluck('prdesc', 'prsno');

            // Fetch all actual balances in bulk
            $actuals = DB::table('NLMSTBAL')
                ->where('GLHEAD', $glhead)
                ->whereIn('PERIOD_YEAR', $budgetLineItems->pluck('period_year')->unique())
                ->whereIn('PERIOD_MONTH', $budgetLineItems->pluck('period_month')->unique())
                ->selectRaw('PERIOD_YEAR, PERIOD_MONTH, SUM(YTD_BAL) as total')
                ->groupBy('PERIOD_YEAR', 'PERIOD_MONTH')
                ->get()
                ->keyBy(function ($item) {
                    return $item->PERIOD_YEAR . '-' . $item->PERIOD_MONTH;
                });



            // Map data and process in memory
            $data = $budgetLineItems->map(function ($item) use ($departments, $offices, $actuals, $currentPeriodMonth, $currentPeriodYear) {
                $item->department_desc = $departments[$item->dept_code] ?? 'N/A';
                $item->offcd_desc = $offices[$item->offcd] ?? 'N/A';

                // Use pre-fetched actual balances
                $key = $item->period_year . '-' . $item->period_month;
                $item->actual = $actuals[$key]->total ?? 0;

                // Disable Amend button logic
                $item->disable_amend = ($item->period_year < $currentPeriodYear) ||
                    ($item->period_year == $currentPeriodYear && $item->period_month <= $currentPeriodMonth);

                return $item;
            });

            return response()->json($data->toArray());
        } catch (\Exception $e) {
            \Log::error('Error in getglcompanybudgetlineitemsdetails: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to process the request'], 500);
        }
    }





    public function getrefocus(Request $request)
    {

        $data = GlMasterbudget::where('account_year', $request->bgtyear)
            ->max('budget_quarter') ?? 0;

        $data += 1;

        return response()->json(['data' => $data]);
    }



    public function escalaterefocus(Request $request)
    {

        $parts = explode('/', $request->get('esc_budget1'));
        $budgetid = $parts[0];

        $user = trim(Auth::user()->user_id);
        $username = trim(Auth::user()->user_name);
        try {

            $sent_to = $request->get('esc_to1');
            $policy_num = $request->get('esc_budget1');

            Escalate_pol::where('policy_no', $budgetid)->where('approved', 'N')->where('type', 'RBGT')->update(['approved' => 'Y']);

            $escalate = new Escalate_pol;
            $count = Escalate_pol::max('escalate_id');
            $next = $count + 1;

            $escalate->escalate_id = $next;
            $escalate->sent_by = $user;
            $escalate->sent_to = $sent_to;
            $escalate->policy_no = $budgetid;
            $escalate->type = 'RBGT';
            $escalate->escalate_type = 101;
            $escalate->approved = 'N';
            $escalate->description = 'BUDGET REFOCUSING APPROVAL';
            $escalate->user_name = $username;
            $escalate->created_at = Carbon::now();
            $escalate->save();
            DB::commit();
            //send email to user
            $email = Aimsuser_web::where('user_id', $sent_to)->first();
            $emailaddr = $email->email;
            $reciever = $email->first_name;
            $name = trim(Auth::user()->user_name);

            $sendemail = new SendEmail;
            $sendemail->category = 'BUDGET REFOCUSING APPROVAL';
            $sendemail->receiver = $emailaddr;
            $sendemail->message = "Kindly Approve Budget with budget number '$budgetid'  for Refocusing .
                <br> $comm. <br> Thank You. ";
            $sendemail->creator = $name;
            $sendemail->save();

            DB::commit();
            Session::flash('success', 'Approval Request sent Successfull ');


            return redirect()->route('glcompanybudget', ['budget_id' => $budgetid])->with('success', "successful");

        } catch (\Exeption $e) {
            DB::rollback();
            Session::flash('error', 'Approval request not sent sucessfully');
            return redirect()->route('glcompanybudget', ['budget_id' => $budgetid])->with('error', "not successful");

        }

    }

    public function getbudgetcolumns()
    {

        $tableName = 'companyglmasterbudget';

        $columns = DB::getSchemaBuilder()->getColumnListing($tableName);

        sort($columns);

        return $columns;

    }



    public function edit_menulink(Request $request)
    {

        DB::beginTransaction();

        try {
            if ($request->get('change_parent') == 'Y') {
                $ed = BGTparams::where('aims_program_code', $request->get('edmenucode'))->update([
                    'aims_program_name' => $request->get('edmenuname'),
                    'program' => $request->get('edroutepath'),
                    'parent_code' => $request->get('edlinkparent'),
                    'aims_sys' => $request->get('edsysname'),
                    'main_menu' => $request->get('edsyscateg')
                ]);
            } else {
                $ed = BGTparams::where('aims_program_code', $request->get('edmenucode'))->update([
                    'aims_program_name' => $request->get('edmenuname'),
                    'program' => $request->get('edroutepath')
                ]);
            }

            DB::commit();

            $links = $this->links();

            $links1 = $this->links1();

            Session::flash('success', 'Link Editted Successfully');
            return view('budget.glbudget_linkslist', [
                'links' => $links,
                'links1' => $links1
            ]);

        } catch (\Throwable $e) {

            DB::rollback();

            $links = $this->links();

            $links1 = $this->links1();


            Session::flash('error', 'Link Edit Failed');
            return view('budget.glbudget_linkslist', [
                'links' => $links,
                'links1' => $links1
            ]);

        }
    }

    public function addfrom_viewlink(Request $request)
    {
        $menu = new BGTparams;
        $num = BGTparams::orderBy('aims_program_code', 'desc')->first();

        $next_code = $num->aims_program_code + 1;

        $menu->aims_program_type = $request->input('linktype');
        $menu->aims_program_name = $request->input('menuname');
        $menu->aims_program_code = $next_code;
        $menu->program = $request->input('progroute');
        $menu->parent_code = $request->input('linkparent');
        $menu->aims_sys = $request->input('sysname');
        $menu->main_menu = $request->input('syscateg');
        //$menu->save();

        DB::beginTransaction();

        try {
            $menu->save();
            DB::commit();

            $links = $this->links();

            $links1 = $this->links1();

            Session::flash('success', 'Link Added Successfully');
            return view('budget.glbudget_linkslist', [
                'links' => $links,
                'links1' => $links1
            ]);

        } catch (\Throwable $e) {
            //    dd($e);
            DB::rollback();

            $links = $this->links();

            $links1 = $this->links1();

            Session::flash('error', 'Link Failed to Add');
            return view('budget.glbudget_linkslist', [
                'links' => $links,
                'links1' => $links1
            ]);

        }
    }

    public function delete_menulink(Request $request)
    {
        DB::beginTransaction();

        try {
            $del = BGTparams::where('aims_program_code', $request->get('delmenucode'))->delete();

            DB::commit();

            $links = $this->links();

            $links1 = $this->links1();


            Session::flash('success', 'Link Deleted Successfully');
            return view('budget.glbudget_linkslist', [
                'links' => $links,
                'links1' => $links1
            ]);

        } catch (\Throwable $e) {
            DB::rollback();

            $links = $this->links();

            $links1 = $this->links1();


            Session::flash('error', 'Link Failed to Delete');
            return view('budget.glbudget_linkslist', [
                'links' => $links,
                'links1' => $links1
            ]);
        }
    }

    public function links1()
    {
        $links1 = BGTparams::where('parent_code', '!=', 0)
            ->orderBy('aims_program_code', 'asc')->get();
        return $links1;
    }

    public function links()
    {
        $links = BGTparams::where(function ($query) {
                $query->where('main_menu', 'G')
                    ->orWhere('main_menu', 'F');
            })
            ->where('parent_code', 0)
            ->get();

        return $links;
    }

    public function budgetlevels(Request $request)
    {
        $driver = DB::getDriverName();

        if ($driver === 'pgsql') {
            // PostgreSQL
            $sql = "
                SELECT tablename AS table_name
                FROM pg_tables
                WHERE schemaname = 'public'
                ORDER BY tablename ASC
            ";
        } elseif (in_array($driver, ['oracle', 'oci', 'oci8'])) {
            // Oracle
            $sql = "
                SELECT table_name
                FROM user_tables
                ORDER BY table_name ASC
            ";
        } else {
            throw new \Exception("Unsupported database driver: {$driver}");
        }

        $tables = DB::select($sql);

        $categorys = DB::table('budget_category')->get();
        $bhlevels = DB::table('budget_hierarchy')->get();

        return view('gb.budgets.budgetparams', compact('categorys', 'bhlevels', 'tables'));
    }

    public function getcolumns(Request $request)
    {

        $table = $request->table;
        $columns = Schema::getColumnListing($table);

        sort($columns);
        return response()->json([
            'columns' => $columns
        ]);
    }

    public function deletebudgetlevel(Request $request)
    {

        if ($request->id == 1) {
            Session::Flash('error', 'level could not be deleted');
            return;
        }
        $deletelevel = DB::table('glbudgetlevel')->where('level_no', $request->id)->delete();
        Session::Flash('success', 'level deleted');
    }

    public function getseconddropdown(Request $request)
    {

        $desc = $request->description;
        $obj = DB::table('glbudgetlevel')
            ->where('table_name', $desc)
            ->select('table_name', 'column_name')
            ->first();

        $tableName = $obj->table_name;
        $columnName = $obj->column_name;

        if (!$tableName || !$columnName) {
            return response()->json([]);
        }

        if ($tableName == 'BRANCH') {
            $item = DB::table($tableName)->where('IS_ACTIVE', 'Y')->get();
        } else {
            $item = DB::table($tableName)->get();
        }


        return response()->json($item);

    }

    public function budgetdrilldtpol(Request $request)
    {

        $obj1 = $request->description1; // 2 for branch / 3 for channel / 4 for class   12 for depat

        $obj2 = $request->description2; // 
        $obj4 = $request->budget_val;
        $bdid = (int) trim($obj4, '[]"');

        $branchRecords = DB::table('branch')->where('is_active', 'Y')->get();
        $distChannelRecords = DB::table('dist_channel')->get();
        $classRecords = DB::table('dept')->get();

        $masterbudget = DB::table('glmasterbudget')
            ->where('budget_id', $bdid)
            ->first();


        $levels = GLBudgetLevel::where('level_no', '<=', $masterbudget->depth)
            ->where('level_no', '!=', 1)
            ->orderBy('level_no')
            ->get();

        $levelNos = [];
        $level_2 = [];
        $level_3 = [];
        $level_4 = [];

        foreach ($levels as $level) {
            $levelNos[] = $level->table_name . "/" . $level->level_no;

            if ($level->level_no == 2 && $level->table_name == 'BRANCH') {
                foreach ($branchRecords as $record) {
                    $level_2[] = $record->branch;
                }
            } elseif ($level->level_no == 2 && $level->table_name == 'DIST_CHANNEL') {
                foreach ($distChannelRecords as $record) {
                    $level_2[] = $record->dist_type;
                }
            }

            if ($level->level_no == 3 && $level->table_name == 'BRANCH') {
                foreach ($branchRecords as $record) {
                    $level_3[] = $record->branch;
                }
            } elseif ($level->level_no == 3 && $level->table_name == 'DIST_CHANNEL') {
                foreach ($distChannelRecords as $record) {
                    $level_3[] = $record->dist_type;
                }
            }

            if ($level->level_no == 4 && $level->table_name == 'DEPT') {
                foreach ($classRecords as $record) {
                    $level_4[] = $record->class;
                }
            }
        }



        $categoryInfo = DB::table('glbudgetlevel')->where('table_name', $obj1)->first();
        $table = $categoryInfo->table_name;
        $column = $categoryInfo->column_name;

        if ($table == "BRANCH") {
            $clmn = DB::table($table)->where('description', 'like', "%$obj2%")->select('offcd')->first();
            $mycolumn = $clmn->offcd;
        }


        $results = DB::table($table)->where('description', 'like', "%$obj2%")->select($column)->first();

        $valuesArray = array();

        foreach ($results as $key => $value) {
            $valuesArray[] = $value;
        }


        $budgetlevel = DB::table('glbudgetlevel')->where('table_name', $obj1)->first();
        $budgetcategoryInfo = DB::table('glbudgetlevel')->where('level_no', $budgetlevel->level_no)->first();



        $data = CompanyGlmasterBudget::selectRaw(
            'SUM(BUDGET_1) AS month1, 
            SUM(BUDGET_2) AS month2, 
            SUM(BUDGET_3) AS month3, 
            SUM(BUDGET_4) AS month4, 
            SUM(BUDGET_5) AS month5, 
            SUM(BUDGET_6) AS month6, 
            SUM(BUDGET_7) AS month7, 
            SUM(BUDGET_8) AS month8, 
            SUM(BUDGET_9) AS month9, 
            SUM(BUDGET_10) AS month10, 
            SUM(BUDGET_11) AS month11, 
            SUM(BUDGET_12) AS month12, 
            SUM(BUDGET_1 + BUDGET_2 + BUDGET_3 + BUDGET_4 + BUDGET_5 + BUDGET_6 + BUDGET_7 + BUDGET_8 + BUDGET_9 + BUDGET_10 + BUDGET_11 + BUDGET_12) AS total, 
            ITEM_NO, 
            DESCRIPTION_NAME, 
            ACCOUNT_GRP_SEC, 
            TITLE_FLAG, 
            TOTAL_FLAG, 
            PARENT_TOTAL_FLAG'
        )
            ->where('budget_id', $bdid)
            ->whereIn("level$budgetlevel->level_no", $valuesArray)
            ->groupBy('ITEM_NO', 'DESCRIPTION_NAME', 'ACCOUNT_GRP_SEC', 'TITLE_FLAG', 'TOTAL_FLAG', 'PARENT_TOTAL_FLAG')
            ->orderBy('ITEM_NO', 'asc')
            ->get();

        foreach ($data as $rec) {
            if ($rec->total_flag == 'Y' && $rec->parent_total_flag !== 'Y') {
                $months = array_fill(1, 12, 0); // Initialize an array for 12 months, index starting from 1
                $totxRecords = GLBudgetTotalsParam::whereNotNull('account_grp_sec')->where('item_no', $rec->item_no)->get();

                foreach ($totxRecords as $totx) {
                    for ($i = 1; $i <= 12; $i++) {
                        $monthField = "month" . $i;
                        $months[$i] += ($totx->add_less == 'L' ? -1 : 1) * $data->where('account_grp_sec', $totx->account_grp_sec)->sum($monthField);
                    }
                }

                // Assign calculated month values back to the object
                for ($i = 1; $i <= 12; $i++) {
                    $monthField = "month" . $i;
                    $rec->$monthField = $months[$i];
                }
            }
        }

        if ($masterbudget->period == 'M') {
            $current = intval($masterbudget->account_month);

            $act_account_year1 = intval($masterbudget->account_year);
            $act_account_year2 = intval($masterbudget->account_year);
            $act_account_year3 = intval($masterbudget->account_year);

            $prev1 = $current - 1;
            $prev2 = $current - 2;
            $prev3 = $current - 3;

            if ($current < 4) {
                if ($current == 3) {
                    $prev1 = 2;
                    $prev2 = 1;
                    $prev3 = 12;

                    $act_account_year3--;
                } elseif ($current == 2) {
                    $prev1 = 1;
                    $prev2 = 12;
                    $prev3 = 11;

                    $act_account_year2--;
                    $act_account_year3--;
                } elseif ($current == 1) {
                    $prev1 = 12;
                    $prev2 = 11;
                    $prev3 = 10;

                    $act_account_year1--;
                    $act_account_year2--;
                    $act_account_year3--;
                }
            }

            return Datatables::of($data)
                ->addColumn('first', function ($row) use ($masterbudget, $act_account_year3, $prev3, $mycolumn) {
                    if ($row->account_grp_sec) {
                        $result = DB::table('nlmstbal')
                            ->selectRaw('SUM(COALESCE(ytd_bal, 0)) as total')
                            ->where('period_year', '=', $act_account_year3)
                            ->where('period_month', '=', $prev3)
                            ->where('offcd', '=', $mycolumn)
                            ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
                            ->first();

                        return $result->total;
                    } else {
                        return 0; // Return 0 if account_grp_sec is empty or null
                    }
                })

                ->addColumn('second', function ($row) use ($masterbudget, $act_account_year2, $prev2, $mycolumn) {
                    if ($row->account_grp_sec) {
                        $result = DB::table('nlmstbal')
                            ->selectRaw('SUM(COALESCE(ytd_bal, 0)) as total')
                            ->where('period_year', '=', $act_account_year2)
                            ->where('period_month', '=', $prev2)
                            ->where('offcd', '=', $mycolumn)
                            ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
                            ->first();

                        return $result->total;
                    } else {
                        return 0; // Return 0 if account_grp_sec is empty or null
                    }
                })

                ->addColumn('third', function ($row) use ($masterbudget, $act_account_year1, $prev1, $mycolumn) {
                    if ($row->account_grp_sec) {
                        $result = DB::table('nlmstbal')
                            ->selectRaw('SUM(COALESCE(ytd_bal, 0)) as total')
                            ->where('period_year', '=', $act_account_year1)
                            ->where('period_month', '=', $prev1)
                            ->where('offcd', '=', $mycolumn)
                            ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
                            ->first();

                        return $result->total;
                    } else {
                        return 0; // Return 0 if account_grp_sec is empty or null
                    }
                })

                ->addColumn('action', function ($lnk) {
                    return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
                })
                ->make(true);
        } else if ($masterbudget->period == 'Q') {

            $current = intval($masterbudget->account_month);

            $act_account_year1 = intval($masterbudget->account_year);
            $act_account_year2 = intval($masterbudget->account_year);
            $act_account_year3 = intval($masterbudget->account_year);

            $prev1 = $current - 1;
            $prev2 = $current - 2;
            $prev3 = $current - 3;

            if ($current < 4) {
                if ($current == 3) {
                    $prev1 = 2;
                    $prev2 = 1;
                    $prev3 = 4;

                    $act_account_year3--;
                } elseif ($current == 2) {
                    $prev1 = 1;
                    $prev2 = 4;
                    $prev3 = 3;

                    $act_account_year2--;
                    $act_account_year3--;
                } elseif ($current == 1) {
                    $prev1 = 4;
                    $prev2 = 3;
                    $prev3 = 2;

                    $act_account_year1--;
                    $act_account_year2--;
                    $act_account_year3--;
                }
            }

            return Datatables::of($data)
                ->addColumn('first', function ($row) use ($masterbudget, $act_account_year3, $prev3, $mycolumn) {
                    if ($row->account_grp_sec) {
                        $result = DB::table('nlmstbal')
                            ->selectRaw('SUM(COALESCE(ytd_bal, 0)) as total')
                            ->where('period_year', '=', $act_account_year3)
                            ->whereIn('period_month', ($prev3 == 1) ? [1, 2, 3] : (($prev3 == 2) ? [4, 5, 6] : (($prev3 == 3) ? [7, 8, 9] : [10, 11, 12])))
                            ->where('offcd', '=', $mycolumn)
                            ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
                            ->first();

                        return $result->total;
                    } else {
                        return 0; // Return 0 if account_grp_sec is empty or null
                    }
                })

                ->addColumn('second', function ($row) use ($masterbudget, $act_account_year2, $prev2, $mycolumn) {
                    if ($row->account_grp_sec) {
                        $result = DB::table('nlmstbal')
                            ->selectRaw('SUM(COALESCE(ytd_bal, 0)) as total')
                            ->where('period_year', '=', $act_account_year2)
                            ->where('offcd', '=', $mycolumn)
                            ->whereIn('period_month', ($prev2 == 1) ? [1, 2, 3] : (($prev2 == 2) ? [4, 5, 6] : (($prev2 == 3) ? [7, 8, 9] : [10, 11, 12])))
                            ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
                            ->first();

                        return $result->total;
                    } else {
                        return 0; // Return 0 if account_grp_sec is empty or null
                    }
                })

                ->addColumn('third', function ($row) use ($masterbudget, $act_account_year1, $prev1, $mycolumn) {
                    if ($row->account_grp_sec) {
                        $result = DB::table('nlmstbal')
                            ->selectRaw('SUM(COALESCE(ytd_bal, 0)) as total')
                            ->where('period_year', '=', $act_account_year1)
                            ->where('offcd', '=', $mycolumn)
                            ->whereIn('period_month', ($prev1 == 1) ? [1, 2, 3] : (($prev1 == 2) ? [4, 5, 6] : (($prev1 == 3) ? [7, 8, 9] : [10, 11, 12])))
                            ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
                            ->first();

                        return $result->total;
                    } else {
                        return 0; // Return 0 if account_grp_sec is empty or null
                    }
                })

                ->addColumn('action', function ($lnk) {
                    return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
                })
                ->make(true);

        } else {

            $current = intval($request->current);

            $act_account_year1 = intval($masterbudget->account_year);
            $act_account_year2 = intval($masterbudget->account_year);
            $act_account_year3 = intval($masterbudget->account_year);

            $prev1 = $current - 1;
            $prev2 = $current - 2;
            $prev3 = $current - 3;


            return Datatables::of($data)
                ->addColumn('first', function ($row) use ($masterbudget, $prev3, $mycolumn) {
                    if ($row->account_grp_sec) {
                        $result = DB::table('nlmstbal')
                            ->selectRaw('SUM(COALESCE(ytd_bal, 0)) as total')
                            ->where('period_year', '=', $prev3)
                            ->where('offcd', '=', $mycolumn)
                            ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
                            ->first();

                        return $result->total;
                    } else {
                        return 0; // Return 0 if account_grp_sec is empty or null
                    }
                })

                ->addColumn('second', function ($row) use ($masterbudget, $prev2, $mycolumn) {
                    if ($row->account_grp_sec) {
                        $result = DB::table('nlmstbal')
                            ->selectRaw('SUM(COALESCE(ytd_bal, 0)) as total')
                            ->where('period_year', '=', $prev2)
                            ->where('offcd', '=', $mycolumn)
                            ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
                            ->first();

                        return $result->total;
                    } else {
                        return 0; // Return 0 if account_grp_sec is empty or null
                    }
                })

                ->addColumn('third', function ($row) use ($masterbudget, $prev1, $mycolumn) {
                    if ($row->account_grp_sec) {
                        $result = DB::table('nlmstbal')
                            ->selectRaw('SUM(COALESCE(ytd_bal, 0)) as total')
                            ->where('period_year', '=', $prev1)
                            ->where('offcd', '=', $mycolumn)
                            ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
                            ->first();

                        return $result->total;
                    } else {
                        return 0; // Return 0 if account_grp_sec is empty or null
                    }
                })

                ->addColumn('action', function ($lnk) {
                    return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
                })
                ->make(true);
        }

        // return datatables::of($data)

        // ->addColumn('january_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 1)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })
        // ->addColumn('february_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 2)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })
        // ->addColumn('march_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 3)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })
        // ->addColumn('april_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 4)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })
        // ->addColumn('may_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 5)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })
        // ->addColumn('june_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 6)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })
        // ->addColumn('july_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 7)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })
        // ->addColumn('august_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 8)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })
        // ->addColumn('september_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 9)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })
        // ->addColumn('october_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 10)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })
        // ->addColumn('november_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 11)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })
        // ->addColumn('december_actual', function ($row) use ($masterbudget,$mycolumn) {
        //     if ($row->account_grp_sec) {
        //         $result = DB::table('nlmstbal')
        //             ->select(DB::raw('SUM(COALESCE(ytd_bal, 0)) as total'))
        //             ->where('period_year', '=', $masterbudget->account_year)
        //             ->where('period_month', '=', 12)
        //             ->where('offcd', '=', $mycolumn)
        //             ->whereRaw('SUBSTR(glhead, 1, 5) = ?', [substr($row->account_grp_sec, 0, 5)])
        //             ->first();

        //         return $result->total;
        //     } else {
        //         return 0; // Return 0 if account_grp_sec is empty or null
        //     }
        // })

        // ->make(true);

    }


    public function postbudgetcategories(Request $request)
    {

        DB::beginTransaction();

        try {
            $deletelevel1 = DB::table('glbudgetlevel')->where('level_no', 1)->delete();

            $level1 = DB::table('glbudgetlevel')->insert([
                'category' => 'COMPANY',
                'level_no' => 1,
                'table_name' => 'COMPANY',
                'column_name' => 'COMPANY',
            ]);

            if ($request->level == 1) {
                Session::Flash('error', 'Invalid level could not be added');
                return;
            }

            $deletelevel2 = DB::table('glbudgetlevel')->where('level_no', $request->level)->delete();

            $level2 = DB::table('glbudgetlevel')->insert([
                'category' => $request->description,
                'level_no' => $request->level,
                'table_name' => $request->db_table,
                'column_name' => $request->db_column,
            ]);

            DB::commit();
            Session::Flash('success', 'Category Level Added Successfully');
            return redirect()->back()->with('success', "added successfully");
        } catch (\Exception $e) {
            DB::rollback();
            Session::Flash('error', 'Category Level not added');
            return redirect()->back()->with('error', "something went wrong");
        }
    }

    //main datatable
    public function maindatatable(Request $request)
    {

        $selectedValues = $request->selectedValues;

        $level1 = null;
        $level2 = null;
        $level3 = null;
        $level4 = null;
        $bgid = $request->checkbgtid;

        $budget_id = $request->checkbgtid;
        $parts = explode('/', $budget_id);
        $bgid = $parts[0];

        $masterbudget = DB::table('glmasterbudget')
            ->where('budget_id', $bgid)
            ->first();

        $levels = GLBudgetLevel::where('level_no', '<=', $masterbudget->depth)
            ->where('level_no', '!=', 1)
            ->orderBy('level_no')
            ->get();


        foreach ($selectedValues as $categoryId => $selectedOptions) {

            $parts = $selectedOptions[0];
            $level1 = 1;
            switch ($categoryId) {
                case 2:
                    $level2 = $parts;
                    break;
                case 3:
                    $level3 = $parts;
                    break;
                case 4:
                    $level4 = $parts;
                    break;
                case 5:
                    $level5 = $parts;
                    break;
            }

        }

        $cols = [];

        foreach ($levels as $item) {
            $cols[1] = "1";
            if (intval($item->level_no) !== 1) {
                $table = $item->table_name;
                $column = $item->column_name;
                $level = 'level' . $item->level_no;
                $obj1 = $$level;

                $results = DB::table($table)
                    ->whereRaw("REPLACE(description, ' ', '') LIKE ?", ["%" . str_replace(' ', '', $obj1) . "%"])
                    ->select($column)
                    ->first();

                $column = strtolower($column);

                $cols[$item->level_no] = $results->$column;

            }
        }

        $query = CompanyGlmasterBudget::selectRaw(
            'SUM(BUDGET_1) AS month1, 
            SUM(BUDGET_2) AS month2, 
            SUM(BUDGET_3) AS month3, 
            SUM(BUDGET_4) AS month4, 
            SUM(BUDGET_5) AS month5, 
            SUM(BUDGET_6) AS month6, 
            SUM(BUDGET_7) AS month7, 
            SUM(BUDGET_8) AS month8, 
            SUM(BUDGET_9) AS month9, 
            SUM(BUDGET_10) AS month10, 
            SUM(BUDGET_11) AS month11, 
            SUM(BUDGET_12) AS month12, 
            SUM(BUDGET_1 + BUDGET_2 + BUDGET_3 + BUDGET_4 + BUDGET_5 + BUDGET_6 + BUDGET_7 + BUDGET_8 + BUDGET_9 + BUDGET_10 + BUDGET_11 + BUDGET_12) AS total, 
            ITEM_NO, 
            DESCRIPTION_NAME, 
            ACCOUNT_GRP_SEC, 
            TITLE_FLAG, 
            TOTAL_FLAG, 
            PARENT_TOTAL_FLAG'
        );

        foreach ($cols as $level => $column) {
            if ($column) {
                $query->whereIn("level$level", [$column]);
            }
        }


        $query->where('budget_id', $bgid)
            ->groupBy('ITEM_NO', 'DESCRIPTION_NAME', 'ACCOUNT_GRP_SEC', 'TITLE_FLAG', 'TOTAL_FLAG', 'PARENT_TOTAL_FLAG')
            ->orderBy('ITEM_NO', 'asc');

        $data = $query->get();

        foreach ($data as $rec) {
            if ($rec->total_flag == 'Y' && $rec->parent_total_flag !== 'Y') {
                $months = array_fill(1, 12, 0); // Initialize an array for 12 months, index starting from 1
                $totxRecords = GLBudgetTotalsParam::whereNotNull('account_grp_sec')->where('item_no', $rec->item_no)->get();

                foreach ($totxRecords as $totx) {
                    for ($i = 1; $i <= 12; $i++) {
                        $monthField = "month" . $i;
                        $months[$i] += ($totx->add_less == 'L' ? -1 : 1) * $data->where('account_grp_sec', $totx->account_grp_sec)->sum($monthField);
                    }
                }

                // Assign calculated month values back to the object
                for ($i = 1; $i <= 12; $i++) {
                    $monthField = "month" . $i;
                    $rec->$monthField = $months[$i];
                }
            }
        }


        return Datatables::of($data)
            ->make(true);


    }


    


    public function UploadBudgetTemplate(Request $request)
{
    DB::connection(env('DB_CONNECTION1'))->beginTransaction();
    DB::connection(env('DB_CONNECTION2'))->beginTransaction();

    try {
        if ($request->hasFile('budget_file')) {
            $request->validate([
                'budget_file' => 'required|mimes:xlsx,xls,csv|max:2048',
            ]);

            $fileName = $request->file('budget_file')->getClientOriginalName();
            $branch_desc = str_replace(' ', '', str_replace('"', '', explode(".", $fileName)[0]));

            // Get the excel data
            $excelData = FileUploadManager::excelUpload($request->file('budget_file'));

            foreach ($excelData as $row) {
                DB::table('OPERATIONSBUDGET')->insert([
                    'ITEM_NO' => $row['item_no'] ?? null,
                    'DESCRIPTION_NAME' => $row['description'] ?? null,
                    'ACCOUNT_GRP_SEC' => $row['accountgrpsec'] ?? null,
                    'BUDGET_ID' => $request->input('upload_budget_id'),
                    'BUDGET_TYPE' => $row['budget_type'] ?? null,
                    'BUDGET_1' => is_numeric($row['january']) ? $row['january'] : null,
                    'BUDGET_2' => is_numeric($row['february']) ? $row['february'] : null,
                    'BUDGET_3' => is_numeric($row['march']) ? $row['march'] : null,
                    'BUDGET_4' => is_numeric($row['april']) ? $row['april'] : null,
                    'BUDGET_5' => is_numeric($row['may']) ? $row['may'] : null,
                    'BUDGET_6' => is_numeric($row['june']) ? $row['june'] : null,
                    'BUDGET_7' => is_numeric($row['july']) ? $row['july'] : null,
                    'BUDGET_8' => is_numeric($row['august']) ? $row['august'] : null,
                    'BUDGET_9' => is_numeric($row['september']) ? $row['september'] : null,
                    'BUDGET_10' => is_numeric($row['october']) ? $row['october'] : null,
                    'BUDGET_11' => is_numeric($row['november']) ? $row['november'] : null,
                    'BUDGET_12' => is_numeric($row['december']) ? $row['december'] : null,
                    'OFFCD' => $request->input('upload_offcd')
                ]);
            }

            DB::connection(env('DB_CONNECTION1'))->commit();
            DB::connection(env('DB_CONNECTION2'))->commit();

            return redirect("/gl/budget/viewbgtbranch/{$request->input('upload_budget_id')}/{$request->input('upload_offcd')}")->with('success', 'Processed successfully');
        } else {
            return redirect()->back()->with('error', "No file uploaded.");
        }
    } catch (\Exception $e) {
        DB::connection(env('DB_CONNECTION1'))->rollBack();
        DB::connection(env('DB_CONNECTION2'))->rollBack();
        return redirect()->back()->with('error', "Something went wrong: " . $e->getMessage());
    }
}






    public function UploadBudgetTemplater(Request $request)
    {
        DB::beginTransaction();

        try {
            if ($request->hasFile('budgettemplater')) {
                $path = $request->file('budgettemplater')->getRealPath();

                $parts = explode('/', $request->budgetuploadidr);
                $bgid = $parts[0];

                $refocusbgt = DB::table('glmasterbudget')
                    ->where('budget_id', $bgid)
                    ->increment('refocus', 1);


                $decline = Escalate_pol::where('policy_no', $bgid)
                    ->where('type', 'RBGT')
                    ->delete();

                // Load the Excel file using Maatwebsite\Excel\Facades\Excel
                $excelData = Excel::load($path)->get();
                $monthlydata = [];
                $branchdata = [];
                $channeldata = [];
                $departmentdata = [];
                $foundbranch = false;
                $foundchannel = false;
                $foundDepartment = false;
                $branches = DB::table('branch');

                foreach ($excelData as $sheet) {
                    $cleanedTitle = str_replace(' ', '', $sheet->getTitle());
                    if (!empty($cleanedTitle)) {
                        $branch = DB::table('branch')
                            ->whereRaw("REPLACE(description, ' ', '') LIKE ?", ["%{$cleanedTitle}%"])
                            ->first();

                    }

                    foreach ($sheet->toArray() as $value) {

                        if (empty(str_replace('"', '', $value['description'])) && str_replace('"', '', $value['description']) == null) {

                        } else {

                            $desc = str_replace('"', '', $value['description']);
                            $grpsec = str_replace('"', '', $value['accountgrpsec']);
                            $month1 = str_replace('"', '', $value['january']);
                            $month2 = str_replace('"', '', $value['february']);
                            $month3 = str_replace('"', '', $value['march']);
                            $month4 = str_replace('"', '', $value['april']);
                            $month5 = str_replace('"', '', $value['may']);
                            $month6 = str_replace('"', '', $value['june']);
                            $month7 = str_replace('"', '', $value['july']);
                            $month8 = str_replace('"', '', $value['august']);
                            $month9 = str_replace('"', '', $value['september']);
                            $month10 = str_replace('"', '', $value['october']);
                            $month11 = str_replace('"', '', $value['november']);
                            $month12 = str_replace('"', '', $value['december']);

                            if ($desc == 'CHANNEL') {
                                $foundbranch = false;
                                $foundDepartment = false;
                                $foundchannel = true;

                            } elseif ($desc == 'CLASS') {
                                $foundbranch = false;
                                $foundDepartment = true;
                                $foundchannel = false;

                            } elseif ($desc == 'WRITTEN PREMIUM') {
                                $foundbranch = true;
                                $foundDepartment = false;
                                $foundchannel = false;

                            }

                            if ($foundchannel) {

                                if ($grpsec !== "DIST_TYPE") {
                                    $channeldata[] = $desc . "-" . $grpsec . "-" . $month1;

                                }

                            } elseif ($foundDepartment) {

                                if ($grpsec !== "CODE") {
                                    $departmentdata[] = $desc . "-" . $grpsec . "-" . $month1;

                                }

                            } elseif ($foundbranch) {
                                if ($grpsec !== null && $grpsec !== "") {
                                    $branchdata[] = $desc . "-" . $grpsec . "-" . $month1 . "-" . $month2 . "-" . $month3 . "-" . $month4 . "-" . $month5 . "-" . $month6 . "-" . $month7 . "-" . $month8 . "-" . $month9 . "-" . $month10 . "-" . $month11 . "-" . $month12;
                                    // $level2code[] = $grpsec;
                                }

                            }

                        }



                    }

                    // $count = 0;
                    // foreach ($channeldata as $item) {
                    //     $count++;
                    //     // Explode the string using "-" as the delimiter
                    //     $explodedData = explode('-', $item);
                    //     if($explodedData[0] !== 'CHANNEL'){
                    //         if (count($explodedData) >= 2) {
                    //             $dist_type = trim($explodedData[1]);
                    //             $percent = trim($explodedData[2]);

                    //             foreach($branchdata as $bdata){
                    //                 $explodedbranchData = explode('-', $bdata);

                    //                 if ($explodedbranchData[1] !== "") {
                    //                     $braData1 = ($percent/100) * $explodedbranchData[2];
                    //                     $braData2 = ($percent/100) * $explodedbranchData[3];
                    //                     $braData3 = ($percent/100) * $explodedbranchData[4];
                    //                     $braData4 = ($percent/100) * $explodedbranchData[5];
                    //                     $braData5 = ($percent/100) * $explodedbranchData[6];
                    //                     $braData6 = ($percent/100) * $explodedbranchData[7];
                    //                     $braData7 = ($percent/100) * $explodedbranchData[8];
                    //                     $braData8 = ($percent/100) * $explodedbranchData[9];
                    //                     $braData9 = ($percent/100) * $explodedbranchData[10];
                    //                     $braData10 = ($percent/100) * $explodedbranchData[11];
                    //                     $braData11 = ($percent/100) * $explodedbranchData[12];
                    //                     $braData12 = ($percent/100) * $explodedbranchData[13];
                    //                     $level2code = $explodedbranchData[1];

                    //                     foreach($departmentdata as $ddata){
                    //                         $explodeddeptData = explode('-', $ddata);
                    //                         if ($explodeddeptData[1] !== "CODE") {
                    //                             $class = $explodeddeptData[1];
                    //                             $budget_1 = ($explodeddeptData[2]/100)*$braData1;
                    //                             $budget_2 = ($explodeddeptData[2]/100)*$braData2;
                    //                             $budget_3 = ($explodeddeptData[2]/100)*$braData3;
                    //                             $budget_4 = ($explodeddeptData[2]/100)*$braData4;
                    //                             $budget_5 = ($explodeddeptData[2]/100)*$braData5;
                    //                             $budget_6 = ($explodeddeptData[2]/100)*$braData6;
                    //                             $budget_7 = ($explodeddeptData[2]/100)*$braData7;
                    //                             $budget_8 = ($explodeddeptData[2]/100)*$braData8;
                    //                             $budget_9 = ($explodeddeptData[2]/100)*$braData9;
                    //                             $budget_10 = ($explodeddeptData[2]/100)*$braData10;
                    //                             $budget_11 = ($explodeddeptData[2]/100)*$braData11;
                    //                             $budget_12 = ($explodeddeptData[2]/100)*$braData12;
                    //                             // dd($bgid,$level2code,$branch->branch,$dist_type,$class,$budget_1);
                    //                             CompanyGlmasterBudget::where('budget_id',$bgid)
                    //                             ->where('account_grp_sec',$level2code)
                    //                             ->where('level2',$branch->branch)
                    //                             ->where('level3',$dist_type)
                    //                             ->where('level4',$class)
                    //                             ->update([
                    //                                 'budget_1' => $budget_1,
                    //                                 'budget_2' => $budget_2,
                    //                                 'budget_3' => $budget_3,
                    //                                 'budget_4' => $budget_4,
                    //                                 'budget_5' => $budget_5,
                    //                                 'budget_6' => $budget_6,
                    //                                 'budget_7' => $budget_7,
                    //                                 'budget_8' => $budget_8,
                    //                                 'budget_9' => $budget_9,
                    //                                 'budget_10' => $budget_10,
                    //                                 'budget_11' => $budget_11,
                    //                                 'budget_12' => $budget_12
                    //                             ]);
                    //                         }
                    //                     }
                    //                 }
                    //             }
                    //         }


                    //     }

                    // }

                    $updateData = [];
                    $distTypes = [];
                    $classes = [];
                    $level2codes = [];
                    $combinedAcctAndDistAndClass = [];

                    foreach ($channeldata as $item) {
                        $explodedData = explode('-', $item);
                        if ($explodedData[0] !== 'CHANNEL' && count($explodedData) >= 3) {
                            $dist_type = trim($explodedData[1]);
                            $percent = trim($explodedData[2]) / 100;

                            foreach ($branchdata as $bdata) {
                                $explodedBranchData = explode('-', $bdata);
                                if ($explodedBranchData[1] !== "") {
                                    $level2code = $explodedBranchData[1];
                                    $budgetItems = array_slice($explodedBranchData, 2, 12);
                                    $branchBudgets = array_map(function ($item) use ($percent) {
                                        return $percent * $item;
                                    }, $budgetItems);

                                    foreach ($departmentdata as $ddata) {
                                        $explodedDeptData = explode('-', $ddata);
                                        if ($explodedDeptData[1] !== "CODE") {
                                            $class = $explodedDeptData[1];

                                            $combinedAcctAndDistTypeAndClass = $level2code . '-' . $dist_type . '-' . $class;

                                            $departmentBudgets = array_map(function ($budgetItem) use ($explodedDeptData) {
                                                return ($explodedDeptData[2] / 100) * $budgetItem;
                                            }, $branchBudgets);

                                            // Prepare update data for this iteration
                                            $updateData[] = [
                                                'budget_1' => $departmentBudgets[0],
                                                'budget_2' => $departmentBudgets[1],
                                                'budget_3' => $departmentBudgets[2],
                                                'budget_4' => $departmentBudgets[3],
                                                'budget_5' => $departmentBudgets[4],
                                                'budget_6' => $departmentBudgets[5],
                                                'budget_7' => $departmentBudgets[6],
                                                'budget_8' => $departmentBudgets[7],
                                                'budget_9' => $departmentBudgets[8],
                                                'budget_10' => $departmentBudgets[9],
                                                'budget_11' => $departmentBudgets[10],
                                                'budget_12' => $departmentBudgets[11]
                                            ];

                                            // Collect distTypes and classes
                                            $distTypes[] = $dist_type;
                                            $classes[] = $class;
                                            $level2codes[] = $level2code;

                                            $combinedAcctAndDistAndClass[] = [
                                                'combinedValue' => $combinedAcctAndDistTypeAndClass,
                                                'updateData' => $updateData
                                            ];
                                        }
                                    }
                                }
                            }
                        }
                    }



                    // Extract updateData array
                    $updateDataArray = [];
                    foreach ($combinedAcctAndDistAndClass as $item) {
                        $updateDataArray[] = $item['updateData'][0];


                        $items = explode('-', $item['combinedValue']);

                        $level2 = $items[0];
                        $distType = $items[1];
                        $class = $items[2];





                        // $test= CompanyGlmasterBudget::where('budget_id', $bgid)
                        // ->where('level2', $branch->branch)
                        // ->where('account_grp_sec', $level2)
                        // ->where('level3', $distType)
                        // ->where('level4', $class)
                        // // ->get();
                        // ->update($updateDataArray[0]);



                    }

                    // Extract updateData array
                    // $updateDataArray = [];
                    // $conditions = [];

                    // foreach ($combinedAcctAndDistAndClass as $item) {
                    //     $updateDataArray[] = $item['updateData'][0];

                    //     $items = explode('-', $item['combinedValue']);
                    //     $level2 = $items[0];
                    //     $distType = $items[1];
                    //     $class = $items[2];

                    //     // Prepare conditions for update
                    //     $conditions[] = [
                    //         'budget_id' => $bgid,
                    //         'level2' => $branch->branch,
                    //         'account_grp_sec' => $level2,
                    //         'level3' => $distType,
                    //         'level4' => $class,
                    //     ];
                    // }

                    // // Perform batch update
                    // CompanyGlmasterBudget::whereIn($conditions)
                    //     ->update($updateDataArray[0]);



                }

                // Process $monthlydata and $departmentdata arrays as needed
                DB::commit();
            }


            // return 1;
            return redirect()->back()->with('success', "Uploaded successfully");
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', "Something went wrong");
        }
    }

    
    


    public function downloadExcelBudget(Request $request)
    {

        $branches = DB::table('branch')->where('is_active', 'Y')->get();

        $departments = DB::table('dept')->get();
        $channels = DB::table('dist_channel')->get();

        $parts = explode('/', $request->budget_id);
        $budget_id = $parts[0];


        $header = [
            'DESCRIPTION',
            'ACCOUNTGRPSEC',
            'JANUARY',
            'FEBRUARY',
            'MARCH',
            'APRIL',
            'MAY',
            'JUNE',
            'JULY',
            'AUGUST',
            'SEPTEMBER',
            'OCTOBER',
            'NOVEMBER',
            'DECEMBER',
        ];

        $columnWidths = [
            'DESCRIPTION' => 25,
            'ACCOUNTGRPSEC' => 15,
            'JANUARY' => 15,
            'FEBRUARY' => 15,
            'MARCH' => 15,
            'APRIL' => 15,
            'MAY' => 15,
            'JUNE' => 15,
            'JULY' => 15,
            'AUGUST' => 15,
            'SEPTEMBER' => 15,
            'OCTOBER' => 15,
            'NOVEMBER' => 15,
            'DECEMBER' => 15,
        ];

        $channelheader = [
            'CHANNEL',
            'DIST_TYPE',
            'PERCENTAGE(%)'
        ];

        $classheader = [
            'DEPARTMENT',
            'CODE',
            'PERCENTAGE(%)'
        ];

        $spreadsheet = new Spreadsheet();
        foreach ($branches as $branch) {

            // Create a new sheet for each branch
            $sheet = $spreadsheet->createSheet();

            $sheet->setTitle($branch->description); // Set sheet title using branch description

            $colIndex = 1;
            foreach ($header as $columnName) {
                $sheet->setCellValueByColumnAndRow($colIndex, 1, $columnName);
                $sheet->getStyleByColumnAndRow($colIndex, 1)->getFont()->setBold(true);
                $sheet->getColumnDimensionByColumn($colIndex)->setWidth($columnWidths[$columnName]);
                $colIndex++;
            }

            $structure = CompanyGlmasterBudget::where('budget_id', $budget_id)
                ->where('level2', $branch->branch)
                ->selectRaw('
                    DESCRIPTION_NAME,
                    ACCOUNT_GRP_SEC,
                    ITEM_NO,
                    SUM(BUDGET_1) as budget_1,
                    SUM(BUDGET_2) as budget_2,
                    SUM(BUDGET_3) as budget_3,
                    SUM(BUDGET_4) as budget_4,
                    SUM(BUDGET_5) as budget_5,
                    SUM(BUDGET_6) as budget_6,
                    SUM(BUDGET_7) as budget_7,
                    SUM(BUDGET_8) as budget_8,
                    SUM(BUDGET_9) as budget_9,
                    SUM(BUDGET_10) as budget_10,
                    SUM(BUDGET_11) as budget_11,
                    SUM(BUDGET_12) as budget_12
                ')
                ->groupBy('DESCRIPTION_NAME', 'ACCOUNT_GRP_SEC', 'ITEM_NO')
                ->orderBy('ITEM_NO')
                ->get();

            $rowIndex = 2;
            foreach ($structure as $row) {
                $colIndex = 1;
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->description_name);
                $colIndex++;

                if ($row->account_grp_sec !== null) {
                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->account_grp_sec);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_1);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_2);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_3);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_4);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_5);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_6);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_7);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_8);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_9);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_10);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_11);
                    $colIndex++;

                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->budget_12);
                    $colIndex++;

                } else {
                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->account_grp_sec);
                    $colIndex++;



                }



                $rowIndex++;
            }

            // $rowIndex++;

            $colIndex = 1;
            foreach ($header as $columnName) {
                $sheet->setCellValueByColumnAndRow($colIndex, 1, $columnName);
                $sheet->getStyleByColumnAndRow($colIndex, 1)->getFont()->setBold(true);
                $sheet->getColumnDimensionByColumn($colIndex)->setWidth($columnWidths[$columnName]);
                $colIndex++;
            }

            $rowIndex++;

            $colIndex = 1;
            foreach ($channelheader as $columnNameh) {
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $columnNameh);
                $sheet->getStyleByColumnAndRow($colIndex, $rowIndex)->getFont()->setBold(true);
                $colIndex++;
            }

            $rowIndex++;

            foreach ($channels as $row) {

                $colIndex = 1;
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->description);
                $colIndex++;

                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->dist_type);
                $colIndex++;

                $rowIndex++;
            }

            $rowIndex++;

            $colIndex = 1;
            foreach ($classheader as $columnNamec) {
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $columnNamec);
                $sheet->getStyleByColumnAndRow($colIndex, $rowIndex)->getFont()->setBold(true);
                $colIndex++;
            }

            $rowIndex++;

            foreach ($departments as $row) {
                $colIndex = 1;
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->description);
                $colIndex++;

                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->dept);
                $colIndex++;


                $rowIndex++;
            }

        }


        $spreadsheet->removeSheetByIndex(0);

        $writer = new Xlsx($spreadsheet);

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="Budget.xlsx"');
        header('Cache-Control: max-age=0');

        // Output the generated .xlsx file to the browser
        $writer->save('php://output');

        exit;
    }

    public function glfetchamount(Request $request)
    {


        $selectedValues = $request->fetchvalues['fetchvalues'];
        $result = explode("/", $request->fetchvalues['checkbgtid']);


        $level1 = null;
        $level2 = null;
        $level3 = null;
        $level4 = null;
        $bgid = $result[0];

        foreach ($selectedValues as $categoryId => $selectedOptions) {

            $parts = $selectedOptions[0];

            switch ($categoryId) {
                case 1:
                    $level1 = $parts;
                    break;
                case 2:
                    $level2 = $parts;
                    break;
                case 3:
                    $level3 = $parts;
                    break;
                case 4:
                    $level4 = $parts;
                    break;
            }

        }

        $bgtlevels = DB::table('glbudgetlevel')->orderBy('level_no')->get();

        $levels = [];

        foreach ($bgtlevels as $bgtlevel) {
            $levels[] = $bgtlevel->table_name;
        }

        if (count($levels) > 0) {
            if ($levels[1] == "BRANCH") {

                $valueData0 = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level2%'")
                    ->pluck('branch')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();

            } elseif ($levels[1] == "DIST_CHANNEL") {
                $valueData0 = DB::table('dist_channel')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level2%'")
                    ->pluck('dist_type')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();
            }
        }


        if (count($levels) > 1) {

            if ($levels[2] == "BRANCH") {

                $valueData1 = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level3%'")
                    ->pluck('branch')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();

            } elseif ($levels[2] == "DIST_CHANNEL") {
                $valueData1 = DB::table('dist_channel')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level3%'")
                    ->pluck('dist_type')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();
            } elseif ($levels[3] == "DEPT") {
                $valueData1 = DB::table('dept')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level3%'")
                    ->pluck('dept')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();

            }

        }

        if (count($levels) > 2) {

            $valueData2 = DB::table('dept')
                ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level4%'")
                ->pluck('dept')
                ->map(function ($value) {
                    return intval($value);
                })
                ->first();

        }

        $valueData2 = $valueData2 ?? null;


        $amount1a = DB::table('companyglmasterbudget')
            ->where('budget_id', $bgid)
            ->where('level2', $valueData0)
            ->selectRaw('SUM(COALESCE(BUDGET_1, 0) + COALESCE(BUDGET_2, 0) + COALESCE(BUDGET_3, 0) + COALESCE(BUDGET_4, 0) + COALESCE(BUDGET_5, 0) + COALESCE(BUDGET_6, 0) + COALESCE(BUDGET_7, 0) + COALESCE(BUDGET_8, 0) + COALESCE(BUDGET_9, 0) + COALESCE(BUDGET_10, 0) + COALESCE(BUDGET_11, 0) + COALESCE(BUDGET_12, 0)) AS total_sum1a')
            ->first();




        $amount2a = DB::table('companyglmasterbudget')
            ->where('budget_id', $bgid)
            ->where('level2', $valueData0)
            ->where('level3', $valueData1)
            ->selectRaw('SUM(COALESCE(BUDGET_1, 0) + COALESCE(BUDGET_2, 0) + COALESCE(BUDGET_3, 0) + COALESCE(BUDGET_4, 0) + COALESCE(BUDGET_5, 0) + COALESCE(BUDGET_6, 0) + COALESCE(BUDGET_7, 0) + COALESCE(BUDGET_8, 0) + COALESCE(BUDGET_9, 0) + COALESCE(BUDGET_10, 0) + COALESCE(BUDGET_11, 0) + COALESCE(BUDGET_12, 0)) AS total_sum1a')
            ->first();



        $amount3a = DB::table('companyglmasterbudget')
            ->where('budget_id', $bgid)
            ->where('level2', $valueData0)
            ->where('level3', $valueData1)
            ->where('level4', $valueData2)
            ->selectRaw('SUM(COALESCE(BUDGET_1, 0) + COALESCE(BUDGET_2, 0) + COALESCE(BUDGET_3, 0) + COALESCE(BUDGET_4, 0) + COALESCE(BUDGET_5, 0) + COALESCE(BUDGET_6, 0) + COALESCE(BUDGET_7, 0) + COALESCE(BUDGET_8, 0) + COALESCE(BUDGET_9, 0) + COALESCE(BUDGET_10, 0) + COALESCE(BUDGET_11, 0) + COALESCE(BUDGET_12, 0)) AS total_sum1a')
            ->first();

        $branch_data = [
            'branch' => $valueData0
        ];
        // dd($amount1a, $amount2a, $amount3a);

        return response()->json([$amount1a, $amount2a, $amount3a, $branch_data]);
    }

    public function approvebgtrefocus(Request $request)
    {

        $parts = explode('/', $request->id);
        $budgetid = $parts[0];
        $status = $request->status;

        $user = trim(Auth::user()->user_id);

        if ($status == 'Y') {
            $approval = Escalate_pol::where('policy_no', $budgetid)
                ->where('type', 'RBGT')
                ->where('approved', 'N')
                ->where('sent_to', $user)
                ->update([
                    'approved' => 'Y',
                    'approved_date' => Carbon::now()
                ]);

        } else {
            $decline = Escalate_pol::where('policy_no', $budgetid)
                ->where('type', 'RBGT')
                ->where('approved', 'N')
                ->where('sent_to', $user)
                ->delete();

        }




    }


    public function checkgluploadedsecondlevel(Request $request)
    {

        $branch = DB::table('branch')
            ->whereRaw("REPLACE(description, ' ', '') LIKE ?", ["%{$request->selectedUploadValues[0][0]}%"])
            ->first();

        $parts = explode('/', $request->bgtid);
        $bgtid = $parts[0];

        $branchdata = DB::table('budgetbranches')
            ->where('budget_id', $bgtid)
            ->where('branch', $branch->branch)
            ->where('status', 'Y')
            ->get();



        $distTypes = [];

        foreach ($branchdata as $data) {
            $distTypes[] = $data->dist_channel;
        }

        $desc = DB::table('dist_channel')
            ->whereIn('dist_type', $distTypes)
            ->pluck('description');



        $desc = $desc->values()->all();

        $desc = array_map(function ($value) {
            return str_replace(' ', '', $value);
        }, $desc);

        return $desc;


    }


    public function tottalparamdetailsO(Request $request)
    {


        $item = DB::table('GLBUDGETPARAM')->where('item_no', $request->item_no)->first();
        $type = 'O';

        return view::make('budget.totalparams_details', compact('item', 'data', 'type'))->with('dat', $dat);

    }

    public function tottalparamdetailsX(Request $request)
    {

        $dat = array(
            'module' => 'Budget',
        );

        $item = DB::table('CAPEX_GLBUDGETPARAM')->where('item_no', $request->item_no)->first();

        $type = 'X';

        return view::make('budget.totalparams_details', compact('item', 'data', 'type'))->with('dat', $dat);

    }

    public function tottalparamdetailsC(Request $request)
    {

        $dat = array(
            'module' => 'Budget',
        );

        $item = DB::table('CASH_GLBUDGETPARAM')->where('item_no', $request->item_no)->first();
        $type = 'C';

        return view::make('budget.totalparams_details', compact('item', 'data', 'type'))->with('dat', $dat);


    }

    public function getallglbudgettotalparam(Request $request)
    {

        if ($request->type == "O") {
            $data = DB::table('GLBUDGETTOTALPARAM')
                ->select('item_no', 'account_grp_sec')
                //    ->orderBy('item_no')
                ->get();

            return Datatables::Of($data)
                ->editColumn('item_no', function ($row) {
                    $item = DB::table('glbudgetparam')->where('account_grp_sec', $row->account_grp_sec)->first();
                    return $item->item_no;

                })
                ->addColumn('description', function ($row) {
                    $item = DB::table('glbudgetparam')->where('account_grp_sec', $row->account_grp_sec)->first();
                    return $item->description_name;

                })
                ->addColumn('action', function ($lnk) {
                    return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
                })
                ->make(true);

        } else if ($request->type == "X") {
            $data = DB::table('CAPEXGLBUDGETTOTALPARAM')
                ->select('item_no', 'account_grp_sec')
                ->get();

            return Datatables::Of($data)
                ->editColumn('item_no', function ($row) {
                    $item = DB::table('CAPEX_GLBUDGETPARAM')->where('account_grp_sec', $row->account_grp_sec)->first();
                    return $item->item_no;

                })
                ->addColumn('description', function ($row) {
                    $item = DB::table('CAPEX_GLBUDGETPARAM')->where('account_grp_sec', $row->account_grp_sec)->first();
                    return $item->description_name;

                })
                ->addColumn('action', function ($lnk) {
                    return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
                })
                ->make(true);

        } else {
            $data = DB::table('CASHGLBUDGETTOTALPARAM')
                ->select('item_no', 'account_grp_sec')
                ->get();

            return Datatables::Of($data)

                ->editColumn('item_no', function ($row) {
                    $item = DB::table('CASH_GLBUDGETPARAM')->where('account_grp_sec', $row->account_grp_sec)->first();
                    return $item->item_no;

                })
                ->addColumn('description', function ($row) {
                    $item = DB::table('CASH_GLBUDGETPARAM')->where('account_grp_sec', $row->account_grp_sec)->first();
                    return $item->description_name;

                })

                ->addColumn('action', function ($lnk) {
                    return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
                })
                ->make(true);

        }





    }

    public function pullbudget(Request $request)
    {

        $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];


        $parts = explode('/', $request->budget_id);
        $bgid = $parts[0];

        $masterbudget = DB::table('glmasterbudget')
            ->where('budget_id', $bgid)
            ->first();



        DB::beginTransaction();

        try {

            $procedureName = '' . $gb . '.glbudget_process';

            $bindings = [
                'w_budget_yr' => $masterbudget->account_year,
                'w_accyr' => $masterbudget->account_year,
                'w_accmth' => $masterbudget->account_month,
                'l_type' => $masterbudget->bgt_type,
                'w_budget_id' => $masterbudget->budget_id
            ];

            $resp = DB::executeProcedure($procedureName, $bindings);

            DB::commit();

            return 1;

        } catch (\Exception $e) {

            DB::rollback();
            return 0;

        }


    }







    function genReqNo()
    {
        $req = DocType::where('doc_type', 'BGT')->get()[0];
        $dtrans = $req->serial_no;
        return $dtrans;
    }

    ##increment dtrans number
    public function updDoctypeSerial()
    {
        $new_serial = DocType::where('doc_type', 'BGT')->increment('serial_no', 1);
        return $new_serial;

    }





    function genHeaderline()
    {
        $req = DocType::where('doc_type', 'BHL')->get()[0];
        $dtrans = $req->serial_no;
        return $dtrans;
    }

    ##increment dtrans number
    public function updHeaderLine()
    {
        $new_serial = DocType::where('doc_type', 'BHL')->increment('serial_no', 1);
        return $new_serial;

    }


    function genHeaderType()
    {
        $req = DocType::where('doc_type', 'BTY')->get()[0];
        $dtrans = $req->serial_no;
        return $dtrans;
    }

    ##increment dtrans number
    public function updHeaderType()
    {
        $new_serial = DocType::where('doc_type', 'BTY')->increment('serial_no', 1);
        return $new_serial;

    }

    
    public function getoffcd(Request $request)
    {

        $data = Nlparams::where('prid', 'OFF')->get();

        return response()->json($data);
    }
    
    public function getbgtglheads(Request $request)
    {

        $data = Nlparams::where('prid', 'GLH')->get();

        return response()->json($data);
    }
    
    public function getdept(Request $request)
    {

        $data = Nlparams::where('prid', 'DEP')->get();

        return response()->json($data);
    }
    
    public function getemployees(Request $request)
    {

        $data = Aimsuser_web::where('left_company', '<>', 'Y')->get();

        return response()->json($data);
    }
    
    public function getcontactpersons(Request $request)
    {

        $data = DB::table('BUDGETCONTACTPERSONS')->where('budget_id', $request->budget_id)->get();


            return DataTables::of($data)
                ->editColumn('name', function ($row) {
                    
                    $item = DB::table('AIMSUSER_WEB')->where('user_id', $row->usr_id)->first();
                    
                    return $item->name;
                })
                ->editColumn('off_dept', function ($row) {
                    if($row->type == 'O'){
                        $item = DB::table('NLPARAMS')->where('prsno', $row->offcd)->where('prid','OFF')->first();
                    }else{
                        $item = DB::table('NLPARAMS')->where('prsno', $row->dept)->where('prid','DEP')->first();
                    }
                    return $item->prdesc;
                })
                ->addColumn('action', function ($lnk) {
                    return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> 
                            <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
                })
                ->make(true);

    }

    public function savecontactpersons(Request $request)
    {
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        DB::connection(env('DB_CONNECTION2'))->beginTransaction();

        try {
            // Validate incoming data
            // $request->validate([
            //     'type.*' => 'required|in:office,department',
            //     'office.*' => 'nullable|exists:offices,id',
            //     'department.*' => 'nullable|exists:departments,id',
            //     'employee.*' => 'required|exists:employees,id',
            //     'budget_id' => 'required|exists:budgets,id' // Ensure the budget ID is provided
            // ]);

            // Get the budget ID from the request
            $budgetId = $request->input('budget_id'); 

            // Iterate through the form inputs and save them
            $types = $request->input('type');
            $offices = $request->input('office');
            $departments = $request->input('department');
            $employees = $request->input('employee');

            foreach ($types as $index => $type) {
                $officeId = $type === 'O' ? $offices[$index] : null;
                $departmentId = $type === 'D' ? $departments[$index] : null;
                $employeeId = $employees[$index];

                // Insert contact person into BUDGETCONTACTPERSONS table
                DB::table('BUDGETCONTACTPERSONS')->insert([
                    'BUDGET_ID' => $budgetId,
                    'USR_ID' => $employeeId,
                    'TYPE' => $type,
                    'OFFCD' => $officeId,
                    'DEPT' => $departmentId
                ]);

                if($type == 'O'){


                    DB::table('BUDGETOFFICES')->insert([
                        'BUDGET_ID' => $budgetId,
                        'USR_STR' => $employeeId,
                        'OFFCD' => $officeId
                    ]);

                }else{

                    DB::table('BUDGETDEPARTMENTS')->insert([
                        'BUDGET_ID' => $budgetId,
                        'USR_STR' => $employeeId,
                        'DEPT' => $departmentId
                    ]);

                }

   

            }

            // Commit transactions if all operations are successful
            DB::connection(env('DB_CONNECTION1'))->commit();
            DB::connection(env('DB_CONNECTION2'))->commit();

            return back()->with('success', 'Contact persons have been saved successfully.');
        } catch (\Exception $e) {
            // Rollback transactions if there's an error
            DB::connection(env('DB_CONNECTION1'))->rollBack();
            DB::connection(env('DB_CONNECTION2'))->rollBack();

            return back()->withErrors('Error saving contact persons: ' . $e->getMessage());
        }
    }

    public function saveAssumptions(Request $request)
    {
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        DB::connection(env('DB_CONNECTION2'))->beginTransaction();

        try {
            
            $item_no = DB::table('BUDGETASSUMPTIONS')->where('budget_id',$request->budgetid)->count() + 1;

      
            DB::table('BUDGETASSUMPTIONS')->insert([
                'ASSUMPTION_ID' => $item_no,
                'BUDGET_ID' => $request->budgetid,
                'ASSUMPTION_TYPE' => $request->assumptionType,
                'DESCRIPTION' => $request->description,
                'CREATED_BY' => trim(Auth::user()->user_id),
                'CREATED_ON' => Carbon::now()
            ]);



            // Commit transactions if all operations are successful
            DB::connection(env('DB_CONNECTION1'))->commit();
            DB::connection(env('DB_CONNECTION2'))->commit();

            return back()->with('success', 'Added successfully.');
        } catch (\Exception $e) {
            // Rollback transactions if there's an error
            DB::connection(env('DB_CONNECTION1'))->rollBack();
            DB::connection(env('DB_CONNECTION2'))->rollBack();

            return back()->withErrors('Error adding: ' . $e->getMessage());
        }
    }

    
    public function getbudgetoffices(Request $request)
    {

        $data = DB::table('BUDGETOFFICES')->where('budget_id', $request->budget_id)->get();



            return DataTables::of($data)
                ->editColumn('name', function ($row) {
                    
                    $item = DB::table('AIMSUSER_WEB')->where('user_id', $row->usr_str)->first();
                    
                    return $item->name;
                })
                ->editColumn('offcd', function ($row) {
                  
                    $item = DB::table('NLPARAMS')->where('prsno', $row->offcd)->where('prid','OFF')->first();
                    
                    return $item->prdesc;
                })
                ->addColumn('action', function ($data) {
                    $btn = '';
    
                    // Edit button triggers the modal
                    $btn .= '<a href="#" id="edit-budget" data-id="' . $data->budget_id . '" class="btn btn-xs btn-primary" data-toggle="modal" data-target="#editBudgetModal"><i class="fa fa-pencil-square-o"></i> Edit</a>';
    
                    $btn .= '&nbsp;&nbsp;';
    
                    // Details button - Redirects to the details page
                    $detailsUrl = url('gl/budget/viewbgtbranch/' . $data->budget_id .'/'. $data->offcd);
                    $btn .= '<a href="' . $detailsUrl . '" class="btn btn-xs btn-default"><i class="fa fa-info-circle"></i> Details</a>';
    
                    return $btn;
    
                })
                ->make(true);

    }
    
    public function getbudgetdepartment(Request $request)
    {

        $data = DB::table('BUDGETDEPARTMENTS')->where('budget_id', $request->budget_id)->get();



            return DataTables::of($data)
                ->editColumn('name', function ($row) {
                    
                    $item = DB::table('AIMSUSER_WEB')->where('user_id', $row->usr_str)->first();
                    
                    return $item->name;
                })
                ->editColumn('dept', function ($row) {
                  
                    $item = DB::table('NLPARAMS')->where('prsno', $row->dept)->where('prid','DEP')->first();
                    
                    return $item->prdesc;
                })
                ->addColumn('action', function ($data) {
                    $btn = '';
    
                    // Edit button triggers the modal
                    $btn .= '<a href="#" id="edit-budget" data-id="' . $data->budget_id . '" class="btn btn-xs btn-primary" data-toggle="modal" data-target="#editBudgetModal"><i class="fa fa-pencil-square-o"></i> Edit</a>';
    
                    $btn .= '&nbsp;&nbsp;';
    
                    // Details button - Redirects to the details page
                    $detailsUrl = url('gl/budget/viewbgtdept/' . $data->budget_id .'/'. $data->dept);
                    $btn .= '<a href="' . $detailsUrl . '" class="btn btn-xs btn-default"><i class="fa fa-info-circle"></i> Details</a>';
    
                    return $btn;
    
                })
                ->make(true);

    }
    
    public function getbudgetdepartments(Request $request)
    {

        $data = DB::table('BUDGETDEPARTMENTS')->where('budget_id', $request->budget_id)->get();


            return DataTables::of($data)
                ->editColumn('name', function ($row) {
                    
                    $item = DB::table('AIMSUSER_WEB')->where('user_id', $row->usr_id)->first();
                    
                    return $item->name;
                })
                ->editColumn('off_dept', function ($row) {
                    if($row->type == 'O'){
                        $item = DB::table('NLPARAMS')->where('prsno', $row->offcd)->where('prid','OFF')->first();
                    }else{
                        $item = DB::table('NLPARAMS')->where('prsno', $row->dept)->where('prid','DEP')->first();
                    }
                    return $item->prdesc;
                })
                ->addColumn('action', function ($lnk) {
                    return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> 
                            <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
                })
                ->make(true);

    }


    public function notifyContactPersons(Request $request)
    {
        $contactPersons = $request->input('contactPersons');
        foreach ($contactPersons as $person) {
            $name = $person['name'];
            $id = $person['id'];
            $sender = '<EMAIL>';
            $address = '<EMAIL>';
            $subject = 'Budget Reminder for ' . $name;
            Mail::send(
                'dompdf_templates.gl.sendbgtalerts',
                [
                    'address' => $address,
                    'subject' => 'Budget Submission Reminder',
                    'reference' => 'BGT-' . $id,
                    'title' => 'Budget Preparation Alert',
                    'message_body' => 'Dear ' . $name . ', please submit your budget to avoid delays.',
                    'sender' => $sender,
                ],
                function ($message) use ($address, $sender, $subject) {
                    $message->to($address);
                    $message->subject($subject);
                    $message->from($sender);
                }
            );
        }
        return redirect()->back();
    }
    
    public function saveProjectedPercentages(Request $request)
    {
        $request->validate([
            'item_no' => 'required|string',
            'projected_percentages' => 'required|array',
        ]);

        $budget_id = $request->budget_id;
        $item_no = $request->item_no;
        $offcd = $request->offcd;

        // Calculate the total projected percentage
        $totalProjectedPercentage = 0;

        foreach ($request->projected_percentages as $projected) {
            // Ensure class_name and projected_value exist in $projected
            $class_name = $projected['class_name'];
            $projected_value = $projected['projected_value'];

            // Validate the projected value to ensure it's between 0 and 100
            if ($projected_value < 0 || $projected_value > 100) {
                return response()->json(['success' => false, 'message' => "Projected percentage must be between 0 and 100 for class: $class_name"], 400);
            }

            // Add the projected value to the total
            $totalProjectedPercentage += $projected_value;
        }

        // Check if the total exceeds 100%
        if ($totalProjectedPercentage > 100) {
            return response()->json(['success' => false, 'message' => "Total projected percentages cannot exceed 100%"], 400);
        }

        // Proceed with saving the projected percentages
        foreach ($request->projected_percentages as $projected) {
            $class_name = $projected['class_name'];
            $projected_value = $projected['projected_value'];

            // Check if the record exists
            $existingRecord = DB::table('BUDGETOPERATIONSALLOCATION')
                ->where('ITEM_NO', str_pad($request->input('item_no'), 2, '0', STR_PAD_LEFT))
                ->where('CLASS', $class_name)
                ->where('BUDGET_ID', $budget_id)
                ->where('OFFCD', $offcd)
                ->first();

            if ($existingRecord) {
                // If record exists, update it
                DB::table('BUDGETOPERATIONSALLOCATION')
                    ->where('ITEM_NO', str_pad($request->input('item_no'), 2, '0', STR_PAD_LEFT))
                    ->where('CLASS', $class_name)
                    ->update(['projected_percentage' => $projected_value]);
            } else {
                // If record does not exist, insert a new one
                DB::table('BUDGETOPERATIONSALLOCATION')->insert([
                    'ITEM_NO' => str_pad($request->input('item_no'), 2, '0', STR_PAD_LEFT),
                    'CLASS' => $class_name,
                    'PROJECTED_PERCENTAGE' => $projected_value,
                    'BUDGET_ID' => $budget_id, 
                    'OFFCD' => $offcd
                ]);
            }
        }

        return response()->json(['success' => true]);
    }

    
    public function budgetlinetype(Request $request, $budget_id = null, $line_id= null)
    {

        $budget_header = DB::table('BUDGETHEADER')->where('budget_id',$budget_id)->first();

        $departments = Nlparams::where('prid', 'DEP')->get();

        $gl_classes = DB::table('GL_CLASS')->get();

        $header = DB::table('BUDGETLINESHEADER')->where('budget_id',$budget_id)->where('budgetlineheader_id',$line_id)->first();

        $nlparams = Nlparams::where('prid', 'GLH')->get();

        $office = Nlparams::where('prid', 'OFF')->where('prsno', $header->offcd)->first();

        return view::make('budget.budgettypes')->with(compact(
            'budget_id','budget_header','offices','header','gl_classes','nlparams','departments','office'

        ));
    }
    
    public function budgetlineitems(Request $request, $budget_id = null, $line_id= null,$type= null)
    {

        $budget_header = DB::table('BUDGETHEADER')->where('budget_id',$budget_id)->first();

        $departments = Nlparams::where('prid', 'DEP')->get();

        $gl_classes = DB::table('GL_CLASS')->get();

        $header = DB::table('BUDGETLINESHEADER')->where('budget_id',$budget_id)->where('budgetlineheader_id',$line_id)->first();

        $type = DB::table('BUDGETLINESHEADERTYPE')->where('budget_id',$budget_id)->where('budgetlineheader_id',$line_id)->where('bgttype',$type)->first();

        if($type->type == "O"){

            $pref = Nlparams::where('prid', 'GRP')
            ->whereIn('PRDESC', ['INCOME', 'EXPENSES'])
            ->pluck('prsno');

        }elseif(($type->type == "C")){

            $pref = Nlparams::where('prid', 'GRP')
            ->whereIn('PRDESC', ['ASSETS', 'LIABILITIES', 'EQUITY'])
            ->pluck('prsno');

        }

        
        $nlparams = Nlparams::where('prid', 'GLH')
        ->where(function ($query) use ($pref) {
            foreach ($pref as $p) {
                $query->orWhere('prsno', 'like', $p . '%');
            }
        })
        ->get();




        $office = Nlparams::where('prid', 'OFF')->where('prsno', $header->offcd)->first();

        return view::make('budget.budgetlineitems')->with(compact(
            'budget_id','budget_header','offices','header','gl_classes','nlparams','departments','office','type'

        ));
    }


    public function getbgtPortfolio(Request $request)
    {
        $glclass =  DB::table('GL_CLASS')->where('product',$request->product)->first();

        return DB::table('GL_PORTFOLIOS')->where('portfolio_code',$glclass->portfolio)->first();

    }


    public function checkBudget($account_year, $account_month, $deptcode, $glhead, $offcd, $product,$portfolio,$item_net)
    {
        $expense = 'N';
        $withinBudget = 'Y'; // Default, to be updated based on checks
    
        // Determine if it's an expense
        if (substr($glhead, 0, 3) === '500') {
            $expense = 'Y';
        }
    
        if ($expense === 'Y') {
            // Calculate GL balance for the given criteria
            $glbalance = DB::table('NLMSTBAL')
                ->where('GLHEAD', $glhead)
                ->where('PERIOD_YEAR', '=', $account_year)
                // ->where('PERIOD_MONTH', '=', $account_month)
                ->sum('YTD_BAL');

            $totalItemCost = DB::table('AP_INVOICE_DETAILS as aid')
            ->join('AP_INVOICE_HEADER as aih', 'aid.INVOICE_NO', '=', 'aih.INVOICE_NO')
            ->whereNotIn('aih.INVOICE_STATUS', ['005'])
            ->where('aih.ACCOUNT_YEAR', $account_year)
            ->where('aih.ACCOUNT_MONTH', $account_month)
            ->where('aid.PORTFOLIO', $portfolio)
            ->where('aid.PRODUCT', $product)
            ->where('aih.OFFICE_CODE', $offcd)
            ->where('aih.DEP_CODE', $deptcode)
            ->sum('aid.local_amount_payable');
            // dd($totalItemCost,2);
                

            $po_req_amounts = PoReqHeader::where('cancelled', 'N')
                ->whereRaw("EXTRACT(YEAR FROM req_date) = ?", [$account_year])
                ->whereNull("APPROVED")
                ->sum('net_amt');

            $total_expense = $glbalance + $po_req_amounts + $item_net + $totalItemCost ;

            // Fetch budget limit using the given criteria
            $budgetLine_amounts = DB::table('BUDGETLINEITEMS')
                ->where('offcd',  $offcd)
                ->where('GLHEAD', '=', $glhead)
                ->where('period_year',  $account_year)
                // ->where('PERIOD_MONTH', '=', $account_month)
                ->where('PORTFOLIO', '=', $portfolio)
                ->where('DEPT_CODE', '=', $deptcode)
                ->sum('amount'); // Assuming we expect only one row

            if((float)$total_expense > (float)$budgetLine_amounts){
                $withinBudget = 'N';
            }else{
                $withinBudget = 'Y';
            }
    
            if ($withinBudget === 'N') {
                $state = "Exceeded Budget";
                $err_msg = " - GL Balance plus Requisitions Net Amount Plus the current Item being added: " 
                . number_format($total_expense, 2) 
                . " exceeds budget limit: " 
                . number_format($budgetLine_amounts, 2) 
                . ".";
            
    
                Session::flash($err_msg);
                Session::flash('error', "Overspending detected: $err_msg. Please check your expenses.");
                throw new \Exception($state . $err_msg, ResponseCode::HTTP_NOT_ACCEPTABLE);
            } else {
                return true;
            }
        }
    
        return false; // Optional for cases where it's not an expense
    }



    public function updateBudgetLineItem(Request $request)
    {
        // dd($request->all());
        // Retrieve the budget header and budget line item
        $budgetheader = DB::table('BUDGETHEADER')->where('BUDGET_ID', $request->upd_budget_id)->first();
        
        // Convert the 'amount' and 'forecast_amount' values in the request by removing commas
        $dataToUpdate = [];
    
        // Process the amount fields
        foreach ($request->all() as $key => $value) {
            if (strpos($key, 'amount') !== false || strpos($key, 'forecast_amount') !== false) {
                // Remove commas from the amount field (if any) and convert it to a float
                if($budgetheader->approved == 'Y'){
                    $dataToUpdate['temp_forecast_amount'] = (float) str_replace(',', '', $value);
                }else{
                    $dataToUpdate['amount'] = (float) str_replace(',', '', $value);
                    $dataToUpdate['forecast_amount'] = (float) str_replace(',', '', $value);
                    $dataToUpdate['temp_forecast_amount'] = (float) str_replace(',', '', $value);
                }
                
            }
        }
    
        // Check if the budget is approved
        if ($budgetheader->approved == 'Y') {
            // Budget is approved, only update forecast amounts for future months
            $currentMonth = date('m');
            $months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    
            // Loop through the months and check if it's a future month
            foreach ($months as $monthIndex => $month) {
                $monthNumber = $monthIndex + 1;
                if ($monthNumber > $currentMonth) {
                    $forecastAmountField = 'temp_forecast_amount' . strtolower($month);
                    // If the forecast amount field exists in the request, update it
                    if ($request->has($forecastAmountField)) {
                        $dataToUpdate[$forecastAmountField] = (float) str_replace(',', '', $request->$forecastAmountField);
                    }
                }
            }
        }

    
        // If there are any amounts or forecast amounts to update, perform the update
        if (!empty($dataToUpdate)) {
            DB::table('BUDGETLINEITEMS')
                ->where('BUDGET_ID', $request->upd_budget_id)
                ->where('ITEM_NO', $request->item_no)
                ->update($dataToUpdate);
        }
    
        // Redirect with a success message
        return redirect()->route('budgetlineitems', [
            'budget_id' => $request->upd_budget_id,
            'line_id' => $request->upd_budget_header,  // Use the appropriate identifier for the line item
        ])->with('success', 'Budget item updated successfully.');
    }
    

    public function removeBudgetLine(Request $request)
    {
        DB::table('BUDGETLINESHEADER')->where('BUDGETLINEHEADER_ID', $request->id)->where('BUDGET_ID', $request->budgetid)->delete();
        DB::table('BUDGETLINEITEMS')->where('BUDGETLINEHEADER_ID', $request->id)->where('BUDGET_ID', $request->budgetid)->delete();
        return response()->json(['success' => 'Budget line removed successfully.']);
    }

    public function removeAssumption(Request $request)
    {
        DB::table('BUDGETASSUMPTIONS')->where('ASSUMPTION_ID', $request->assumption_id)->where('BUDGET_ID', $request->budgetid)->delete();
        return response()->json(['success' => 'Budget Assumption removed successfully.']);
    }

    public function assumptionsTable(Request $request)
    {

        $data = DB::table('BUDGETASSUMPTIONS')->where('budget_id', $request->budget_id)->get();

        return DataTables::of($data)
            ->editColumn('name', function ($row) {
                
                $item = DB::table('AIMSUSER_WEB')->where('user_id', $row->usr_str)->first();
                
                return $item->name;
            })
            ->editColumn('created_by', function ($row) {
                
                $item = DB::table('AIMSUSERS')->where('user_id', $row->created_by)->first();
                
                return $item->name;
            })
            ->addColumn('action', function ($row) {

                return '<a href="javascript:void(0);" class="btn btn-xs btn-danger assumpteddel" id="assumpteddel" data-assumption_id="' . $row->assumption_id . '" data-assumptbudgetid="' . $row->budget_id . '">Remove</a>';

            })
            ->make(true);

    }

    public function downloadBudgetTemplate()
    {
        // Define the CSV headers
        $headers = [
            'ACCOUNT', 'DEPT_CODE', 'PRODUCT', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12','TOTAL_AMOUNT'
        ];

        // Create a callback to generate the CSV
        $callback = function () use ($headers) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $headers); // Add headers to the file
            fclose($file);
        };

        // Set response headers for downloading a CSV file
        return response()->stream($callback, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="budget_template.csv"',
            'Cache-Control' => 'no-store, no-cache, must-revalidate',
            'Pragma' => 'no-cache',
        ]);
    }


    public function uploadBudget(Request $request)
    {
        // Validate uploaded file
        $validator = Validator::make($request->all(), [
            'budget_template' => 'required|mimes:csv,txt|max:2048', // Only CSV files
        ]);
        

        $item_no = DB::table('BUDGETLINEITEMS')
        ->where('BUDGET_ID', $request->uploadbudget_id)
        ->where('BUDGETLINEHEADER_ID', $request->uploadbudget_header)
        ->count();

        $budgetlinesheader = DB::table('BUDGETLINESHEADER')
        ->where('BUDGET_ID', $request->uploadbudget_id)
        ->where('BUDGETLINEHEADER_ID', $request->uploadbudget_header)
        ->first();



        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $file = $request->file('budget_template');
 
        $filePath = $file->getRealPath();

        // Read and process the file
        $data = [];
        if (($handle = fopen($filePath, 'r')) !== false) {
            $header = fgetcsv($handle); // Read the header row
            if ($header !== ['ACCOUNT', 'DEPT_CODE', 'PRODUCT', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', 'TOTAL_AMOUNT']) {
                return redirect()->back()->with('error', 'Invalid file format.');
            }

            while (($row = fgetcsv($handle)) !== false) {
                $account = $row[0];
                $deptCode = $row[1];
                $portfolio = $row[2];
                $monthlyAmounts = array_slice($row, 3, 12);
                $totalAmount = $row[15];
                $portfolio_data = DB::table('GL_CLASS')->where('PRODUCT', $portfolio)->first();

                $nlparams_data = DB::table('NLPARAMS')->where('prsno', $account)->where('prid','GLH')->first();


                // Prepare data for each month
                foreach ($monthlyAmounts as $monthIndex => $amount) {
                    $item_no = $item_no + 1;
                    $data[] = [
                        'OFFCD' => $budgetlinesheader->offcd, // Example office code; adjust as needed
                        'GLHEAD' => $account,
                        'PERIOD_YEAR' => now()->year,
                        'PERIOD_MONTH' => $monthIndex + 1, // Month (1 to 12)
                        'YEAR_OPENING_BAL' => 0,
                        'PERIOD_OPENING_BAL' => 0,
                        'YTD_DEBITS' => 0,
                        'YTD_CREDITS' => 0,
                        'YTD_BAL' => 0,
                        'PERIOD_DEBITS' => 0,
                        'PERIOD_CREDITS' => 0,
                        'YTD_CLOSING_BAL' => 0,
                        'PERIOD_CLOSING_BAL' => 0,
                        'DEPT_CODE' => $deptCode,
                        'PORTFOLIO' => $portfolio_data->portfolio,
                        'BUDGETLINEHEADER_ID' => $request->uploadbudget_header,
                        'BUDGET_ID' => $request->uploadbudget_id,
                        'PRODUCT' => $portfolio_data->product, // Replace as needed
                        'FORECAST_AMOUNT' => $amount,
                        'TEMP_FORECAST_AMOUNT' => $amount,
                        'COMMENTS' => $nlparams_data->prdesc,
                        'ITEM_NO' => $item_no,
                        'AMOUNT' => $amount,
                    ];
                }
            }
            fclose($handle);
        }

        // Insert data into the database
        try {
            DB::table('AIMSGLDATA.BUDGETLINEITEMS')->insert($data);
            return redirect()->back()->with('success', 'Budget uploaded successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to upload budget: ' . $e->getMessage());
        }
    }

    public function amendLineItem(Request $request)
    {
        // dd($request->all());
        // $validated = $request->validate([
        //     'line_item_id' => 'required|integer|exists:BUDGETLINEITEMS,id',
        //     'forecast_amount' => 'required|numeric',
        //     'temp_forecast_amount' => 'required|numeric',
        //     'comments' => 'nullable|string',
        // ]);

        $lineItemId = $request->line_item_id;

        // Update the line item in the database
        DB::table('BUDGETLINEITEMS')
            ->where('item_no', $lineItemId)
            ->where('budget_id', $request->amend_budget_id)
            ->where('BUDGETLINEHEADER_ID', $request->amend_budget_header)
            ->where('BGTTYPE', $request->amend_bgttype)
            ->update([
                'TEMP_FORECAST_AMOUNT' =>$request->temp_forecast_amount
            ]);

        return response()->json(['success' => true, 'message' => 'Line item amended successfully.']);
    }




    



    


}
