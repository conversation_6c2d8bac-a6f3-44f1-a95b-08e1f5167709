<?php

namespace App\Http\Controllers\Auth;

use App\PasswordPolicy;

use App\Http\Controllers\Controller;
use App\Models\Aimsuser;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */
   /**
     * lockoutTime
     *
     * @var
     */
    protected $lockoutTime;
     
    /**
     * maxLoginAttempts
     *
     * @var
     */
    protected $maxLoginAttempts;



    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
        $this->lockoutTime  = 1;    
        
        $maxpassword = PasswordPolicy::where('record_type',0)->first();
        $login_attempts = (int)$maxpassword->login_attempts;
        $this->maxLoginAttempts = $login_attempts;
    }

    public function logout(Request $request){
        session(['previous_url' => url()->previous()]);

        $this->clearsession($request);
        Auth::logout();
        return redirect('/login');

    }

    protected function hasTooManyLoginAttempts(Request $request)
    {

        return $this->limiter()->tooManyAttempts(
            $this->throttleKey($request),
            $this->maxLoginAttempts,
            $this->lockoutTime
        );
    }

    //save date each time a user logs into the system
    public function authenticated(Request $request, $user)
    {
        
        $user->last_login =  Carbon::now(); // current time to be saved in database
        //Auth::logoutOtherDevices($request->password); 
        //comment this line test
        $user->save();

    }
    protected function clearsession($request){
       $user_id = auth()->user()->user_id;
        DB::table("sessions")
            ->where('ip_address',$request->ip())
            ->where('user_id',$user_id)
            ->delete();

    }

    public function login(Request $request)
    {

        $user = Aimsuser::where('email', $request->email)->first();
        
        if(!$user){
            return redirect()->back()
                ->withInput($request->only($this->username(), 'remember'))
                ->withErrors([
                    $this->username() => 'Wrong credentials',
                ]);

        }

        if ($user->temporary_acc == 'Y' && Carbon::today()->gte(Carbon::parse($user->temporary_acc_expiry_date)->startOfDay())) {
            $user->update([
                'lock_account' => 'Y'
            ]);
            return redirect()->back()
            ->withInput($request->only($this->username(), 'remember'))
            ->withErrors([
                $this->username() => 'Your account is locked. Please contact the administrator.',
            ]);
        }

        $this->validateLogin($request);

        // If the class is using the ThrottlesLogins trait, we can automatically throttle
        // the login attempts for this application. We'll key this by the username and
        // the IP address of the client making these requests into this application.
        if (method_exists($this, 'hasTooManyLoginAttempts') &&
            $this->hasTooManyLoginAttempts($request)) {
            $this->fireLockoutEvent($request);

            return $this->sendLockoutResponse($request);
        }

        if ($this->guard()->validate($this->credentials($request))) {
            $user = $this->guard()->user();

            // If the user is locked out, redirect them back to the login form
            if ($user->lock_account == 'Y' || $user->left_company == 'Y') {
                return redirect()->back()
                    ->withInput($request->only($this->username(), 'remember'))
                    ->withErrors([
                        $this->username() => 'Your account is locked. Please contact the administrator.',
                    ]);
            }
            if ($user->permanent_disable_acc == 'Y') {
                return redirect()->back()
                    ->withInput($request->only($this->username(), 'remember'))
                    ->withErrors([
                        $this->username() => 'Your account is Permanently locked. Please contact the administrator.',
                    ]);
            }
          
            $checkip = $this->checkIpaddress($request,$user);
            if($checkip){
                return redirect()->back()
                    ->withInput($request->only($this->username(), 'remember'))
                    ->withErrors([
                        $this->username() => 'Your account is actively Logged in a Different Computer. Please logout to proceed.',
                    ]);

            }

            if ($this->attemptLogin($request)) {
                if ($request->hasSession()) {
                    $request->session()->put('auth.password_confirmed_at', time());
                }

                // Update user session information, including last activity
                   $this->updateUserSessionInfo();

    
                return $this->sendLoginResponse($request);
            }
        }

        // If the login attempt was unsuccessful we will increment the number of attempts
        // to login and redirect the user back to the login form. Of course, when this
        // user surpasses their maximum number of attempts they will get locked out.
        $this->incrementLoginAttempts($request);

        return $this->sendFailedLoginResponse($request);
    }

    /**
     * Update user session information, including last activity.
     */
    protected function updateUserSessionInfo()
    {
        $userexists = DB::table("sessions")->where('user_id',auth()->user()->user_id)->exists();
        if($userexists){
            $ipadd = DB::table("sessions")->where('user_id',auth()->user()->user_id)->first()->ip_address;
            if($ipadd == $this->getClientIpAddress() ){
                $addsess = DB::table("sessions")
                        ->where('user_id',auth()->user()->user_id)
                        ->where('ip_address',$ipadd)
                        ->update([
                            "id"=>session()->getId(),
                            "last_active"=>Carbon::now()
                        ]);

            }else {
                $addsess = DB::table("sessions")
                ->where('user_id',auth()->user()->user_id)
                ->update([
                    "id"=>session()->getId(),
                    "ip_address"=>$this->getClientIpAddress(),
                    "last_active"=>Carbon::now()
                ]);
            }
        }else{
            $addsess = DB::table("sessions")->insert([
                "id"=>session()->getId(),
                "user_id"=>auth()->user()->user_id,
                "ip_address"=>$this->getClientIpAddress(),
                "last_active"=>Carbon::now()
            ]);

        }
    }
    
    protected function getClientIpAddress()
    {
        return request()->ip(); //  client's IP address
    }

    protected function checkIpaddress($request,$user){
      
        // Check if session ID and IP match
        if ($user) {
           
             $usersess = DB::table("sessions")->where('user_id',$user->user_id)->exists();
          
             if($usersess){
                #getstoredip
                $storedrec = DB::table("sessions")->where('user_id',$user->user_id)->first();
                $passpolicy = PasswordPolicy::where('record_type', 0)->first();
                #if ip is different check session timeout
                if($storedrec->ip_address <> $request->ip()){
              
                    $lastactive =$storedrec->last_active;
                    $session_timeout = $passpolicy->session_time_out;
                     // Create a Carbon instance from the timestamp
                    $carbonTimestamp = Carbon::parse($lastactive);
                    // Subtract minutes
                    $newTime = $carbonTimestamp->addMinutes($session_timeout);

                    // Format the result
                    $newTimeFormatted = $newTime->format('Y-m-d H:i:s.u');

                     // Get the current time using Carbon
                    $currentDateTime = Carbon::now();
                    
                    // Compare the two Carbon instances
                    if ($newTime->greaterThan($currentDateTime)) {
                      return true;

                    }
                }
             }
        }
        return false;
    }


    /**
     * Send the response after the user was authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    protected function sendLoginResponse(Request $request)
    {
        $request->session()->regenerate();

        $this->clearLoginAttempts($request);

        if ($response = $this->authenticated($request, $this->guard()->user())) {
            return $response;
        }

        if(config('auth.2fa'))
        {
             auth()->user()->generateCode();
        }
  
        // return redirect()->route('2fa.index');

        return $request->wantsJson()
                    ? new JsonResponse([], 204)
                    : (config('auth.2fa') 
                        ? redirect()->intended(route('2fa.index')) 
                        : redirect()->intended($this->redirectPath())
                    );
                // : redirect()->intended(route('2fa.index'));
    }
}
