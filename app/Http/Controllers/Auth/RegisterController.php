<?php

namespace App\Http\Controllers\Auth;

use Carbon\Carbon;

use App\User;
use App\PasswordPolicy;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Auth\RegistersUsers;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:aimsuser',
            'sname' => 'required|string|max:255',
            'lname' => 'required|string|max:255',
            'password' => 'required|alpha_num|min:8|confirmed',
        ]);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\User
     */
    protected function create(array $data)
    {
        $passpolicy = PasswordPolicy::where('record_type',0)
                            ->first();
        $expiry_days = $passpolicy->expiry_days;
        $expiry_date = Carbon::today()->addDays($expiry_days-1);

        return User::create([
            'user_name' => $data['name'],
            'email' => $data['email'],
            'surname'=> $data['sname'],
            'last_name'=> $data['lname'],
            'password' => bcrypt($data['password']),
            'pass_valid_days' => $expiry_days,
            'pass_expiry_date' => $expiry_date,
            'start_warning_days' => $passpolicy->start_warning_days,
        ]);
    }
}
