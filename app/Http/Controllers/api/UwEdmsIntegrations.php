<?php

namespace App\Http\Controllers\api;

use File;
use Validator;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

use App\EDMS_Docs_GB;

class UwEdmsIntegrations extends Controller
{
    public  $company_details ;

    public function __construct(){
        $this->company_details = company_details();
    }
    

    public function integrate_GB_docs_to_edms(Request $request)
    {

            try {
                $pending_docs_count = EDMS_Docs_GB::where('integrated','<>','Y')->where('error_comments',null)->count();
               
                    
                if($pending_docs_count>0){
                    $pending_docs_to_upload = EDMS_Docs_GB::where('integrated','<>','Y')->where('error_comments',null)->get();
                    
                    foreach ($pending_docs_to_upload as $pending_doc_rec) {
                        $this->process_docs_to_edms($pending_doc_rec);
                    }
                    return response()->json(['success'=>'Documents integrated to EDMS successfully'], 200);
                }else {
                    return response()->json(['error'=>'No document pending to integrate to EDMS'], 401);
                }

            } catch (\Throwable $th) {
                // dd($th);
            }      
    }

    public function process_docs_to_edms($pending_doc_rec)
    {
        $document_label = $pending_doc_rec->document_label;
        
        
        $client = new \GuzzleHttp\Client();

        $url = 'http://***********:4040/api/v4/document_types/';
        $user = 'steve';
        $passwd = 'aims2021';						
        $response = '';
        $status="";

        $response = $client->get($url, [
            'auth' => [
                $user, 
                $passwd
            ],
            'headers' => [
                    'Content-Type'  => 'application/json'
                ],
            

        ]);
        $contents = json_decode($response->getBody()->getContents());
        $results=$contents->results;
        $next_page_count=$contents->next;
        
        foreach ($results as $key => $value) {
            if ($value->label==$document_label) {
                $document_type_id=$value->id;
            }
        }
        $page=1;
        $this->process_doc_for_upload($pending_doc_rec,$document_type_id,$document_label,$next_page_count,$page);
        
    }

    public function process_doc_for_upload($pending_doc_rec,$document_type_id,$document_label,$next_page_count,$page)
    {
        $client = new \GuzzleHttp\Client();
        $user = 'steve';
        $passwd = 'aims2021';
        //check if document type id is null
        if($document_type_id==null){
            //check if there is next page to load
            if($next_page_count==null){
                // dd('Page Limit has reached and no such document label '.$document_label.'.Last Page is No.'.$page); 
                return response()->json(['error'=>'Page Limit has reached and no such document label '.$document_label], 401);
            }else{
                //go to next page to check document label
                $page=$page + 1;
                $this->list_document_types_next_page($pending_doc_rec,$document_label,$page);
            }
            
        }else{
            ///upload document
            $this->upload_document($pending_doc_rec,$document_type_id,$document_label,$next_page_count,$page);
                    
        }
    }

    public function list_document_types_next_page($pending_doc_rec,$document_label,$page)
    {
        $client = new \GuzzleHttp\Client();
        
       
        $url = 'http://***********:4040/api/v4/document_types/?page='.$page;
        $user = 'steve';
        $passwd = 'aims2021';						

        $response = '';
        $status="";

        $response = $client->get($url, [
            'auth' => [
                $user, 
                $passwd
            ],
            'headers' => [
                    'Content-Type'  => 'application/json'
                ], 

        ]);
        $contents = json_decode($response->getBody()->getContents());
        $results=$contents->results;
        $next_page_count = $contents->next;
       
        foreach ($results as $key => $value) {
            if ($value->label==$document_label) {
                $document_type_id=$value->id;
            }
        }

        $this->process_doc_for_upload($pending_doc_rec,$document_type_id,$document_label,$next_page_count,$page);
        
    
    }
    
    public function upload_document($pending_doc_rec,$document_type_id,$document_label,$next_page_count,$page)
    {
        
        $file_to_upload = public_path('/edms/'.$pending_doc_rec->document_name);
        $file_name = $pending_doc_rec->document_name;

        $client = new \GuzzleHttp\Client();
        $url = 'http://***********:4040/api/v4/documents/upload/';
        $user = 'steve';
        $passwd = 'aims2021';

        $response = '';
        $status="";

        $response = $client->post($url, [
            'auth' => [
                $user, 
                $passwd
            ],
                'multipart' => [
                    [
                        'Content-type' => 'multipart/form-data',
                        'name' => 'file',
                        'contents' => fopen($file_to_upload, 'r'),
                        'filename' => $file_name,
                    ],
                    [ 'name' => 'document_type_id', 'contents' => $document_type_id ]
                ]
                
        ]);
        $contents = json_decode($response->getBody()->getContents());
        
        $upload_document_id = $contents->id;
        $this->get_metadata_types($pending_doc_rec,$document_type_id,$document_label,$upload_document_id);
    }

    public function get_metadata_types($pending_doc_rec,$document_type_id,$document_label,$upload_document_id)
    {
try {
    //code...

        $client = new \GuzzleHttp\Client();
        $url = 'http://***********:4040/api/v4/document_types/'.$document_type_id.'/metadata_types/';
        $user = 'steve';
        $passwd = 'aims2021';

        $response = '';
        $status="";

        $response = $client->get($url, [
            'auth' => [
                $user, 
                $passwd
            ],
        ]);

        $contents = json_decode($response->getBody()->getContents());
        $results=$contents->results;
        $meta_types_count=$contents->count;
        

        $array=[];
        $all_rules=[];
        $statusCode=null;
        foreach ($results as $key => $value) {
            $field=$value->metadata_type->name;
            $metadata_type_id=$value->metadata_type->id;

            $array[$metadata_type_id]=$field;

            $rules = array(
                'metadata_type_id' => $metadata_type_id,
                'value' =>$pending_doc_rec->{$field}
            );

            $url = 'http://***********:4040/api/v4/documents/'.$upload_document_id.'/metadata/';

                $response = $client->post($url, [
                    'auth' => [
                        $user, 
                        $passwd
                    ],
                    'headers' => [
                            'Content-Type'  => 'application/json'
                        ],
                    'json'=>$rules
                ]);

                $contents = json_decode($response->getBody()->getContents());
                $statusCode = $response->getStatusCode();
                
        }
        
            if($statusCode==201){
                $pending_docs_rec_update = EDMS_Docs_GB::where('policy_no',$pending_doc_rec->policy_no)->where('endorsement_no',$pending_doc_rec->endorsement_no)
                ->where('claim_no',$pending_doc_rec->claim_no)->where('client_number',$pending_doc_rec->client_number)->update([
                    'integrated'=>'Y',
                    'doc_uploaded'=>'Y',
                    'metadata_set'=>'Y',
                    'document_type_id'=>$document_type_id,
                    'uploaded_doc_id'=>$upload_document_id
                ]);
                return response()->json(['success'=>'Document upload integration successfully']);
            }else {
                return response()->json(['error'=>'Document upload integration has failed'], 401);
            }
    } catch (\Throwable $th) {
        dd($th);
    }

    }

    
}