<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
//use Yajra\Datatables\Datatables;
use App\Main_menu;
use App\Menus;
use App\workflow\Escalations;
use App\workflow\Workflows;
use View;
use Illuminate\Support\Facades\DB;

use Session;


class MenuController extends Controller
{

	public function index()
    {
    	$menus= Menus::all();
        $workflow = Workflows::all();
        $parents = Menus::where('menu_type','M')->get();
    	// foreach ($menus as $key => $value) {
    	// 	echo $value->workflow_id."<br/>";
    	// 	echo $value->get_escalation;
    	// }
          
    
       //return View::make('menu')->with('menus', $menus);
       return view('menu',[
         'menus'=>$menus,
         'workflow'=>$workflow,
         'parents'=>$parents
        ]);
    }

    public function add_menu(Request $request){
        $driver = DB::getDriverName();

        if ($driver === 'pgsql') {
            $sql = "select max(cast(program_code as integer)) as max from main_menu";
        } else {
            $sql = "select max(to_number(program_code)) as max from main_menu";
        }

        $d = DB::connection('oracle2')->select($sql);

        $next_val = $d[0]->max + 1;

        $lv = DB::connection('oracle2')->table('main_menu')
                  ->select("levl")
                  ->where('program_code',$request->input('parent_code'))
                  ->get();

        $lvl = $lv[0]->levl + 1;
        
        //dd($lvl);

        $menu = new Menus;
       
        $menu->program_code = $next_val;
        $menu->levl = $lvl;
        $menu->parent_code = $request->input('parent_code');
        $menu->url=$request->input('url');
        $menu->module=$request->input('module');
        $menu->submodule=$request->input('submodule');
        $menu->program_name=$request->input('program_name');
        $menu->workflow_id=$request->input('workflow_id');
        $menu->workflow_code=$request->input('workflow_code');
        $menu->menu_type=$request->input('menu_type');
        //$menu->save();

        DB::beginTransaction();

            try {
                $menu->save();
                DB::commit();


                Session::flash('success','Menu Added Successfully');
                return redirect()->route('menu_main');

            }catch (\Throwable $e) {
               
                DB::rollback();

                //dd($e);

                Session::flash('error','Menu Failed to Add');
                return redirect()->route('menu_main');

            }

        //return redirect()->route('menu_main');
    }


    public function edit_menu(Request $request){
       // dd($request);
        if($request->get('submit')){
          //  dd($request);
            $lv = DB::connection('oracle2')->table('main_menu')
                  ->select("levl")
                  ->where('program_code',$request->input('ed_parent_code'))
                  ->get();

            $lvl = $lv[0]->levl + 1;

            $menu = Menus::where('program_code',$request->get('ed_program_code'))->first();
       
           // $menu->program_code = $next_val;
            $menu->levl = $lvl;
            $menu->parent_code = $request->input('ed_parent_code');
            $menu->url=$request->input('ed_url');
            $menu->module=$request->input('ed_module');
            $menu->submodule=$request->input('ed_submodule');
            $menu->program_name=$request->input('ed_program_name');

            if($request->input('change_workflow')=='Y'){
                $menu->workflow_id=$request->input('ed_workflow_id');
                $menu->workflow_code=$request->input('ed_workflow_code');
            }

            $menu->menu_type=$request->input('ed_menu_type');
            //$menu->save();

            DB::beginTransaction();

                try {
                    $menu->save();
                    DB::commit();


                    Session::flash('success','Menu Editted Successfully');
                    return redirect()->route('menu_main');

                }catch (\Throwable $e) {
                   
                    DB::rollback();

                    dd($e);

                    Session::flash('error','Menu Failed to Edit');
                    return redirect()->route('menu_main');

                }
        }

        else if($request->get('delete')){
               DB::beginTransaction();
                try{
                    $ed = Menus::where('program_code',$request->get('ed_program_code'))->delete();

                    DB::commit();

                    Session::flash('success','Menu Deleted Successfully');
                    return redirect()->route('menu_main');

                }catch(\Throwable $e){
                    DB::rollback();

                    Session::flash('error','Menu Failed to Delete');
                    return redirect()->route('menu_main');
                }

        }
    }


    public function fetch_wcode(Request $request){

        $b = DB::connection('oracle')->table('escalations')
                ->select('*')->where('category',$request->get('id'))->get();
        //dd($b);
        echo json_encode($b);
    }

  /*  public function fetch_menus(){
       /* $menus = Menus::all();

        $menus = DB::connection('oracle')->table('binderpol')->join('class', 'binderpol.class', '=', 'class.class')
         ->join('branch', 'binderpol.branch', '=', 'branch.branch')
         //->join('agmnf', 'binderpol.branch', '=', 'agmnf.branch','binderpol.agent', '=', 'agmnf.agent')
         ->join("agmnf",function($join){
                $join->on("agmnf.branch","=","binderpol.branch")
                    ->on("agmnf.agent","=","binderpol.agent");
            })
         ->select('binderpol.*', 'class.description as cl', 'branch.description as br','agmnf.name as ag')->get();
         return $this->hasOne('App\workflow\Escalations','code','workflow_code','category','workflow_id');
       
         $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

       /*   $menus = DB::connection('oracle2')->table('main_menu')
                  ->leftjoin($gb.'.workflows','main_menu.workflow_id','=','workflows.id')
                  ->select("main_menu.*","workflows.name as wname")
                  ->get(); 
               
        $menus = Main_menu::select("main_menu.*","workflows.name as wname")
           ->leftjoin($gb.".workflows","workflows.id","=","main_menu.workflow_id")
           // ->leftjoin($gb.".escalations",function($join){
           //    $join->on("escalations.code","=","main_menu.workflow_code")
           //         ->on("escalations.category","=","main_menu.workflow_id");
           // })
            ->get();     

         return datatables::of($menus)

         /*    ->addColumn('Workflow Name', function ($fn) {
                $m = Menus()->get_workflow();
                $workflow_name=$m->name;

                if($workflow_name){

                    return $workflow_name;

                }else{

                    return '';

                }
                
            })
           ->addColumn('Restatus', function ($fn) {

              $count=Polremast::where('endt_renewal_no',$fn->endorse_no)->count();

              if($count>0){

                  return 'Reinsured';

              }else{

                return 'Not Rensured';

              }


                
            })
            ->addColumn('LossRatio', function ($fn) {

              //$gross_prem = DB::table('debitmast')->where('policy_no' '=' $fn->policy_no)->sum('gross_premium');
             // $clmpaid = DB::table('clhmn')->where('policy_no' '=' $fn->policy_no)->sum('total_payment');
             // $clmest = DB::table('clhmn')->where('policy_no' '=' $fn->policy_no)->sum('curr_total_estimate');

              $clmpaid=Clhmn::where('policy_no',$fn->policy_no)->sum('total_payment');
              $clmest=Clhmn::where('policy_no',$fn->policy_no)->sum('curr_total_estimate');
              $gross_prem=Debitmast::where('policy_no',$fn->policy_no)->sum('gross_amount');

               if($gross_prem==0){
                  $lr = 0;
               }else{
                  $lr = (($clmpaid+$clmest) / $gross_prem) * 100 ;
               }

               return round($lr,2) . ' %';
                
            })  
            ->make(true);
    }  */

    public function brian()
    {
    	workflowupdate(12345567,321,20);
    }

}