<?php

namespace App\Http\Controllers\gb\underwriting;
use View;

use App\Modtl;
use App\Pipcnam;
use App\Ptamdtl;
use App\Currency;
use App\Dcontrol;
use App\Debitdtl;
use App\Covertype;
use App\Curr_ate;
use App\Polmaster;
use App\Polremast;
use Carbon\Carbon;
use App\ClassModel;
use App\Aimsuprofgb;
use App\ComesaParam;
use App\ComesaRates;
use App\Aimsuser_web;
use App\Polmasterend;
use App\Polmisc_fees;
use App\ComesaVehicles;
use App\Aimsgrouplimits;
use App\Misc_fees_param;
use App\Models\Modtlhist;
use App\Models\Modtlmast;
use App\Models\Modtlsumm;
use App\Models\Motcvrdet;
use App\Models\Motorsect;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
// use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\gb\underwriting\Policy;
use App\Http\Controllers\gb\underwriting\MotorProcessing;

class ComesaExtension extends Controller
{
    public function applyComesaRates($endt_renewal_no){

        //get Polmaster before applying comesa rates
        $polmasterBefore = Polmaster::where('endorse_no', $endt_renewal_no)->first();
        //get dcontrol data for the endorsement
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $class = ClassModel::where('class', $dcontrol->class)->first();
        //get effective date and expiry date of comesa
        $effective_date = Carbon::parse($dcontrol->effective_date);
        $expiry_date = Carbon::parse($dcontrol->expiry_date);
        
        $month = $effective_date->month;
        $year = $effective_date->year;
        $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);
        //get difference in days
        $no_of_days = $effective_date->diffInDays($expiry_date) + 1;
        if($month == 2){
            $Months = ($no_of_days / 30);
        }else{
            $Months = ($no_of_days / $days);
        }
        //round to next month count
        $Months = ceil($Months);
        
        $coveredMonths = (int)$Months;
        //get covered vehicles
        $comesaVehicles = Debitdtl::where('endt_renewal_no', $endt_renewal_no)->get();
        $noOfTravellers = $comesaVehicles[0]->travellers;

        $TotalComesaPremium = 0;

        foreach ($comesaVehicles as $key => $comesaVehicle) {
            $modtl = Modtlmast::where('reg_no', trim($comesaVehicle->reg_no))->first();
            if($coveredMonths > 6 || $no_of_days > 60){
                $comesaRates = ComesaRates::where('full_prem', 'Y')->where('dept', $class->aimsdept)->first();
                
                $comesaPremium = round(($comesaRates->amount* $no_of_days)/60,2);   
            }
            else{
                $comesaRates = DB::table('comesarates')
                    ->where('dept', $class->aimsdept)
                    ->whereRaw("(min_days <= $no_of_days AND max_days >= $no_of_days) OR period=$coveredMonths")
                    ->first();
                $comesaPremium = $comesaRates->amount;           
            }
            
            $TotalComesaPremium = $TotalComesaPremium + $comesaPremium ;
        }
        
        $comesa = ComesaRates::where('dept', $dcontrol->dept)->get(['medical']);
        $comMedical = $comesa[0]->medical;

        //multiply by travellers
        $comesaMedical = ($comMedical * $noOfTravellers);
        //sum medical and comesa charges
        $DueTotalComesaPremium = $TotalComesaPremium + $comesaMedical ;
        //save values to polmaster
        // $polmaster_update = Polmaster::where('policy_no', $dcontrol->policy_no)->first();

        // $polmaster_update->endorse_amount = $DueTotalComesaPremium;
        // $polmaster_update->annual_premium = $polmasterBefore->annual_premium + $DueTotalComesaPremium;
        // $polmaster_update->renewal_premium = $w_total_renewal_prem;
        // $polmaster_update->items_total = $items_total_modtl;
        // $polmaster_update->prev_premium = $polmasterBefore->annual_premium;
        // $polmaster_update->pvt_prem = 0;
        // $polmaster_update->save();

        //modify vehicles
        $this->modifyCoveredVehicles($endt_renewal_no, $dcontrol);
        
    }

    public function modifyCoveredVehicles($endt_renewal_no, $dcontrol){
        $comesaVehicles = Debitdtl::where('endt_renewal_no', $endt_renewal_no)->get();
        foreach ($comesaVehicles as $key => $comesaVehicle) {
            $modtl = Modtlmast::where('policy_no', $dcontrol->policy_no)->where('reg_no', trim($comesaVehicle->reg_no))
                            ->update([
                                'endt_renewal_no' => $endt_renewal_no
                            ]);
        }

    }
    public function ApplyModtlOption(Request $request)
    {
        $policy_no =$request->policy_no;
        $no_travellers =$request->no_travellers;
        $cover =$request->cover;
        $period_from = $request->period_from;
        $period_to = $request->period_to;
        $dcontrol = Dcontrol::where('policy_no', $policy_no)->first();
        $polmaster = Polmaster::where('policy_no', $policy_no)->first();
        $class = ClassModel::where('class', $polmaster->class)->first();
        $vehicle = Modtlmast::where('policy_no', $policy_no)
                            ->where('reg_no',$request->reg_no)
                            ->first();

        if($cover != 0){

            $modtls = Modtlmast::where('policy_no', $policy_no)
                            ->where('endt_renewal_no',$polmaster->endorse_no)
                            ->where('covertype',$cover)
                            ->where('status','ACT')
                            ->take(50)
                            ->get();

        }else{

            $modtls = Modtlmast::where('policy_no', $policy_no)
                            ->where('endt_renewal_no',$polmaster->endorse_no)
                            ->where('status','ACT')
                            ->take(50)
                            ->get();

        } 

        

        $months = Carbon::parse($period_from)->diffInMonths($period_to);

        $period_from = Carbon::parse($period_from);
        $period_to = Carbon::parse($period_to);
        
        $month = $period_from->month;
        $year = $period_from->year;
        $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);
        //get difference in days
        $no_of_days = Carbon::parse($period_from)->diffInDays(Carbon::parse($period_to));
        $Months = Carbon::parse($period_from)->floatDiffInMonths(Carbon::parse($period_to)->addDay());

        $months = Carbon::parse($period_from)->diffInMonths($period_to);
        // return [$period_from, $period_to];

        // $effective_date = Carbon::parse($dcontrol->effective_date);
        // $expiry_date = Carbon::parse($dcontrol->expiry_date);
        $period_from = Carbon::parse($period_from);
        $period_to = Carbon::parse($period_to);
        
        $month = $period_from->month;
        $year = $period_from->year;
        $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);
        //get difference in days
        $no_of_days = Carbon::parse($period_from)->diffInDays(Carbon::parse($period_to));


        if ($no_of_days > 0 && $no_of_days < 21) {
            $Months =  0.67;
            $days = 0.67;
        }elseif ($no_of_days > 20 && $no_of_days < 31) {
            $Months =  1;
        }else{

            if($month == 2){
                $Months = ($no_of_days / 30);
            }else{
                $Months = ($no_of_days / $days);
            }
            $Months = ceil($Months);
        }

        
        //round to next month count
        // dd($Months);
        $coveredMonths = $Months;
        if($Months > 7 && $Months != 0.67 && $Months != 1){
            $coveredMonths = 7;
        }

        //round to next month count
        // $Months = ceil($Months); 
        //$coveredMonths = (int)$Months;

        if($Months > 12){
            $coveredMonths = 12;
        }
        $coveredPeriod = "{$Months} month(s)";

        // $comesarates = DB::table('comesarates')
        //     ->where('dept', $class->aimsdept)
        //     ->whereNotIn('motor_categ' ,['TRL','TRK'])
        //     ->whereRaw("(min_days <= $no_of_days AND max_days >= $no_of_days) OR period=$coveredMonths")
        //     ->first(); 

        // if($no_of_days > 60 and $Months <= 12){
        //     $comesarates->amount = round(($comesarates->amount* $no_of_days)/60,2);
        // }
  
        // COMESA premium for trailers and trucks
        // switch (Str::upper($request->motor_categ)) {
        //     case 'TRK':
        //     case 'TRL':
        //         $Annualcomesarates = ComesaRates::where('full_prem', 'Y')
        //             ->where('dept', $class->aimsdept)
        //             ->where('motor_categ',$request->motor_categ)
        //             ->first();

        //         if($coveredMonths == 6) {
        //                 $comesarates = $Annualcomesarates;
        //                 $comesarates->amount = (float)$Annualcomesarates->amount/2;   
        //                 $comesarates->cardfee = (float)$comesarates->cardfee/2;          
        //         }
        //         elseif($no_of_days == 365 or $no_of_days == 366){
        //                 $comesarates = $Annualcomesarates;
        //                 $comesarates->amount = (float)$Annualcomesarates->amount;
        //         }
        //     break;
        //     default:
        //         break;
        // } 

        $currency = Currency::where('currency_code', $request->currency)->first();
        $com_currency = ComesaParam::first()->default_currency;

        $comesa_currency = Currency::where('currency', $com_currency)->first();


        $dept = $class->dept;
        $comesarates  = Comesarates::where('period', $coveredMonths)
                                    ->where('dept', $dept)
                                    ->first();

        $comesacars  = ComesaVehicles::where('dept',$class->dept)->get();

        if ($com_currency != $currency->currency) {

            $rate = Curr_ate::where('currency_code', $comesa_currency->currency_code)
                    ->where('rate_date', Carbon::today())
                    ->first();

            if(is_null($rate)){
                return [
                    'code' => -1,
                    "msg" => "Currency rate not set for ".$comesa_currency->description
                ];
            }else {
                if($comesarates){
                    $comesarates->amount = $comesarates->amount * $rate->currency_rate;
                    $comesarates->cardfee = $comesarates->cardfee * $rate->currency_rate;
                    $comesarates->medical = $comesarates->medical * $rate->currency_rate;
                }

                foreach ($comesacars as $car) {
                    $car->amount = $car->amount * $rate->currency_rate;
                }
            }

        }
        // dd($comesarates);

        if(!$comesarates){
            return [
                'code' => -1,
                "msg" => "Comesa rates not set for ".$coveredPeriod."  period"
            ];
        }

        return [
            'comesarates' => $comesarates,
            'comesacars' => $comesacars,
            'modtls' => $modtls
        ];

    }

    public function getCountries(){
        
        $countries =  DB::table('countries')->where('comesa_flag', 'Y')->get();

        return $countries;

    }

    // comesa edit
    public function getComesaEditDetails(Request $request)
    {

        $endorse_no = $request->endorse_no;
        $ptamdtl = Ptamdtl::where('endt_renewal_no', $endorse_no)->where('status',1)->get();
        $countries = Ptamdtl::where('endt_renewal_no', $endorse_no)->where('status',1)->first()->countries;
        $countries = explode('_',$countries);
        $dcontrol = Dcontrol::where('endt_renewal_no', $endorse_no)->first();
        $masterDcontrol = Dcontrol::where('policy_no', $dcontrol->master_policy)->first();
        // $comesarates  = Comesarates::where('period', $months)->where('dept', $dept)->first();

        return [
            'ptamdtl' => $ptamdtl,
            'dcontrol' => $dcontrol,
            'masterDcontrol' => $masterDcontrol,
            'countries' => $countries
        ];
    }
    // comesa edit // end

    // updateComesaDetails func
    public function updateComesaDetails(Request $request)
    {
        DB::BeginTransaction();
        try {

            $policy_no = $request->pta_pol_no;
            $endorse_no = $request->pta_endt_no;
            $prev_endorse_no = $request->previous_endt_renewal_no;
            $period_from = $request->pta_period_from;
            $period_to = $request->pta_period_to;
            $basic_prem = $request->basic_prem;
            $countries = $request->countries;
            $reg_nos = $request->reg_no;
            $total_card_fee = (float) array_sum($request->card_fee);

            $dcontrol = Dcontrol::where('endt_renewal_no', $endorse_no)->where('trans_type', 'PTA')->first();
            $motorCtrl = new MotorProcessing();

            $detail = '';
            foreach($countries as $key => $country){
                $detail .= $country.'_';
            }
    
            $detail = rtrim($detail, "_");

            foreach($reg_nos as $i => $reg_no){
                $baseVeh = Modtlsumm::where('policy_no',$dcontrol->master_policy)
                    ->max('transeq_no');

                $BasemodtlPrem = Modtlsumm::where('policy_no',$dcontrol->master_policy)
                    ->where('reg_no',$reg_no)
                    ->where('transeq_no',$baseVeh)
                    ->first();

                $motorCtrl->setProps(
                    endt_renewal_no: $dcontrol->endt_renewal_no,
                    reg_no: $reg_no,
                    cls : $dcontrol->class,
                    total_sum_insured: $BasemodtlPrem->total_sum_insured
                );

                $ptamdtl_check = Ptamdtl::where('endt_renewal_no', $endorse_no)
                                        ->where('reg_no', $reg_no)
                                        ->count();

                if ($ptamdtl_check > 0) {

                    $prem_dtl = Motcvrdet::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                        ->where('reg_no',$reg_no)
                        ->first();

                    $check_upd = (int) $request->delvehicle[$i];
                    
                    if ( $check_upd == 0) {
                        $ptamdtl = Ptamdtl::where('endt_renewal_no', $endorse_no)->where('reg_no', $reg_no)->first();
                        $ptamdtl->status = 1;
                        $ptamdtl->period_from = $period_from;
                        $ptamdtl->period_to = $period_to;
                        $ptamdtl->card_no = $request->comesacard_no[$i];
                        $ptamdtl->medical = $request->med_prem[$i];
                        $ptamdtl->card_fee = $request->card_fee[$i];
                        $ptamdtl->passengers = $request->no_passengers[$i];
                        $ptamdtl->pta_prem = $basic_prem[$i];
                        $ptamdtl->endorse_amt = $basic_prem[$i] + $request->med_prem[$i];
                        $ptamdtl->countries = $detail;
                        $ptamdtl->update();

                        Modtlhist::where('endt_renewal_no', $endorse_no)
                            ->where('reg_no', $reg_no)
                            ->update([
                                'status' => 'ACT',
                            ]);

                        $section = [
                            'group' => $prem_dtl->grp_code,
                            'item' => $prem_dtl->item_code,
                            'rate_amount' => $ptamdtl->endorse_amt,
                            'risk_value' => $prem_dtl->risk_value,
                            'cancel' => 'N',
                        ];
        
                        $premium_amounts = [
                            'annual_premium' =>  $ptamdtl->endorse_amt,
                            'premium_movt' =>  $ptamdtl->endorse_amt,
                            'risk_value_movt' => $prem_dtl->sum_insured,
                            'endorse_amount' =>  $ptamdtl->endorse_amt
                        ];
                    }else{
                        
                        Ptamdtl::where('endt_renewal_no', $endorse_no)
                            ->where('reg_no', $reg_no)
                            ->update([
                                'status' => 0
                            ]); 
                        Modtlhist::where('endt_renewal_no', $endorse_no)
                            ->where('reg_no', $reg_no)
                            ->update([
                                'status' => 'CNC',
                                'updated_by' => Auth::user()->user_name,
                            ]); 
    
                        $section = [
                            'group' => $prem_dtl->grp_code,
                            'item' => $prem_dtl->item_code,
                            'rate_amount' => 0,
                            'risk_value' => $prem_dtl->risk_value,
                            'cancel' => 'Y',
                        ];
        
                        $premium_amounts = [
                            'annual_premium' =>  0,
                            'premium_movt' =>  0,
                            'risk_value_movt' => $prem_dtl->risk_value,
                            'endorse_amount' =>  0
                        ];
                    }
                    $motorCtrl->save_section_dtl($section,$premium_amounts);
                    $motorCtrl->update_motor_summary($dcontrol->endt_renewal_no,$dcontrol->class,$reg_no);
                    $motorCtrl->update_polmaster($dcontrol->endt_renewal_no);
                }else {
                    $transcation = 'update';
                    $pol = new Policy;
                    $pol->saveComesaDtl($endorse_no, $request, $transcation);
                }
            }
            
            $misc_fee = Misc_fees_param::where('comesa_flag', 'Y')->first();
            $total_card_fee = Ptamdtl::where('endt_renewal_no', $endorse_no)->where('status', 1)->sum('card_fee') ;
            $polmisc_fees_count = Polmisc_fees::where('policy_no', $policy_no)->where('endt_renewal_no', $endorse_no)->count();
            
            if ($polmisc_fees_count > 0) {
                $polmisc_fees = Polmisc_fees::where('policy_no', $policy_no)
                    ->where('endt_renewal_no', $endorse_no)
                    ->update([
                        'fee_descr' => $misc_fee->fee_descr,
                        'fee_amt' => $total_card_fee,
                        'fee_entry_type' => $misc_fee->fee_entry_type,
                    ]);
            }else{
                $polmisc_fees = Polmisc_fees::create([
                        'policy_no' => $policy_no,
                        'endt_renewal_no' => $endorse_no,
                        'fee_descr' => $misc_fee->fee_descr,
                        'fee_amt' => $total_card_fee,
                        'fee_entry_type' => $misc_fee->fee_entry_type,
                    ]);
            }
            // card fee // end
            
            DB::commit();
            Session::flash('success', 'Comesa has been updated Successfully');
            return redirect()->route('policy_functions',['endt_renewal_no'=>$endorse_no]);
        } catch (\Throwable $th) {
            DB::rollback();
            throw $th;
            Session::flash('error', 'error');
            // dd($th);
            return redirect()->route('policy_functions',['endt_renewal_no'=>$endorse_no]);
        }
    }
    // updateComesaDetails func // end


    // comesa dataTable
    public function comesa_table(Request $request)
    {
        $endt_renewal_no = $request->endt_renewal_no;
        // $polmaster = Polmaster::where('policy_no', $policy_no)->first();
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->where('trans_type', 'PTA')->first();

        // dd($dcontrol);
        $ptamdtl  = Ptamdtl::where('endt_renewal_no', $dcontrol->endt_renewal_no)->where('status', 1)->get();

        // dd($ptamdtl);

        return Datatables::of($ptamdtl)
                ->addIndexColumn()
                ->editColumn('period_from', function($data){
                    return dateUserFriendly($data->period_from);
                })
                ->editColumn('period_to', function($data){
                    return dateUserFriendly($data->period_to);
                })
                ->make(true);
                
    }
    // comesa dataTable // end


}
