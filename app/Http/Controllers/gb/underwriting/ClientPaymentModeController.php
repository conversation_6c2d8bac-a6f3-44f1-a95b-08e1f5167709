<?php

namespace App\Http\Controllers\gb\underwriting;

use App\Models\ClientPaymentMode;
use App\Models\ClientRiskProfile;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\SaveClientPaymentModeRequest;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;



class ClientPaymentModeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = DB::table('client_payment_modes')
                    ->select('id','payment_mode','created_by','updated_by');

            return DataTables::of($data)
                    ->addColumn('action',function($data){

                            $btn = '<a href="#" id="edit-clntpaymentmode" data-id="' . $data->id . '" class="btn btn-xs btn-primary"><i class="fa fa-pencil-square-o"></i> Edit</a>';

                            $btn .= '&nbsp;&nbsp';
                        
                            $btn .= '<a href="#" id="delete-clntpaymentmode" data-id="' . $data->id . '" class="btn btn-xs btn-danger"><i class="fa fa-minus-square-o"></i> Delete</a>';
                        
                        return $btn;
                    })
                    ->make(true);
        }

        return view('gb.underwriting.client_payment_mode');
    }

    

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(SaveClientPaymentModeRequest $request)
    {
        $status = 0;

        $data = $request->validated();
        $paymentModeId = ClientPaymentMode::max('id') + 1;

        DB::beginTransaction();
        try {

            $clientPaymentMode = new ClientPaymentMode();
            $clientPaymentMode->id = $paymentModeId;
            $clientPaymentMode->payment_mode = $data['payment_mode'];
            $clientPaymentMode->save();

            DB::commit();
            $status = 1;
        } catch (\Throwable $th) {

            throw $th;
            DB::rollBack();
        }

        return array('status' => $status);
    }

   

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ClientPaymentMode  $clientPaymentMode
     * @return \Illuminate\Http\Response
     */
    public function edit(ClientPaymentMode $clientPaymentMode)
    {
        return $clientPaymentMode;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ClientPaymentMode  $clientPaymentMode
     * @return \Illuminate\Http\Response
     */
    public function update(SaveClientPaymentModeRequest $request, ClientPaymentMode $clientPaymentMode)
    {
        $status = 0;

        $data = $request->validated();

        try {
            $clientPaymentMode->payment_mode = $data['payment_mode'];
            $clientPaymentMode->save();

            $status = 1;
        } catch (\Throwable $th) {
            throw $th;
        }

        return array('status' => $status);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ClientPaymentMode  $clientPaymentMode
     * @return \Illuminate\Http\Response
     */
    public function destroy(ClientPaymentMode $clientPaymentMode)
    {
        $status = 0;

        DB::beginTransaction();

        try {
            $clientPaymentMode->delete();

            DB::commit();

            $status = 1;

        } catch (\Throwable $th) {
            DB::rollBack();
        }

        return array('status' => $status);
    }

     public function paymentModeUsageCheck($clientPaymentMode)
    {
        $paymentModeCount = ClientRiskProfile::where('payment_mode_id',$clientPaymentMode)->count();

        $status = $paymentModeCount == 0 ? 1 : 0;

        return array('status' => $status);
    }
}
