<?php

namespace App\Http\Controllers\gb\underwriting;

use Auth;
use Session;
use App\Town;
use App\Agmnf;
use App\Title;
use Illuminate\Support\Facades\Route;
use App\Branch;
use App\Cities;
use App\Client;
use App\Gender;
use App\Polast;
use App\Reidoc;
use DataTables;
use App\Acctype;
use App\Bustype;
use App\Country;
use App\Pipcnam;
use App\Polsect; 
use App\Regions;
use App\Currency;
use App\Dcontrol;
use App\District;
use App\Olbranch;
use App\Bankbranches;
use App\Polmaster;
use Carbon\Carbon;
use App\ClassModel;
use App\Occupation;
use App\Olbnknames;
use App\Beneficiary;
use App\PartnerBank;
use App\Polmasterend;
use App\Postal_codes;
use App\Source_funds;
use App\Identity_type;
use App\Identification;
use App\AgmnfMasterTemp;
use App\Models\Location;
use App\Organisation_role;
use App\Models\JointMember;
use App\Models\PaymentMode;
use Illuminate\Http\Request;
use App\Beneficiary_relation;
use App\Models\CompanyModule;
use App\Models\CustomerGroup;
use App\Models\LocationLevel;
use App\Models\MaritalStatus;
use App\Models\PostalAddress;
use App\Models\ClientPaymentMode;
use App\Models\ClientRiskProfile;
use App\Http\Requests\StoreClient;
use App\Models\ClientFieldsParams;
use App\Models\Aml_classification;
use App\Models\Hobby;
use App\Models\ClientHobby;
use App\Models\PepClassification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\ClientClassification;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Response;
use App\Events\DispatchNotificationEvent;
use App\Traits\GenerateOnboardingNumber;

use App\Services\IntermediaryQueryService;

use Aimsoft\UserManagement\Models\Permission;
use App\ValueObjects\IntermediaryQueryParams;
use App\Http\Controllers\gb\underwriting\IntergrationController;

class Policy_details extends Controller
{

     protected $agents;
     protected $branches;
     protected $client_number;
     protected $types;
     protected $clients;
     protected $titles;
     protected $banks;

     use GenerateOnboardingNumber;


     public function index(){

        Gate::authorize('create-client');

        $this->clients=Client::whereNotIn('client_stopped',['Y','y'])->get(['client_number','name']);
        $this->titles=Title::all();
        $this->branches= Branch::all();
        $this->banks=Olbnknames::all();
        //$this->agents = Agmnf::all();
        $this->types=Reidoc::where('orig','U/W')->get(['doc_type','entry_type_descr','description']);

        $identification=Identification::all();
        // $idtypes=Identity_type::all();
        $identification=$identification[0];
        $country=Country::all();
        // Risk profile payment methods
        $payment_modes = ClientPaymentMode::all();
        $occupation=Occupation::all();
        $pipcnam = Pipcnam::first();
        $org_roles = Organisation_role::all();
        $postal_codes = postal_codes::all();
        $postal_addresses = PostalAddress::all();
        $client_classifications = ClientClassification::all();
        $cities=Cities::all();
        $genders = Gender::all();
        $districts = District::all();
        $regions = Regions::all();
        $source_funds = Source_funds::all();
        $beneficiary_relations = Beneficiary_relation::all();
        $selfRelationCode = Beneficiary_relation::selfRelationCode;
        $currency = Currency::all();
        $permission = Permission::where('slug','access-aml-risk-items')->first();
        $hobbies = Hobby::all();
        $pepClassifications = PepClassification::all();

        $local_country = Country::select('nationality')->where('iso',$pipcnam->country_iso)->first();

        if($pipcnam->shortened_nationality == 'Y'){

            $nationalities = [
                [
                    'nationality' => $local_country->nationality,
                    'iso' => 'L'
                ],
                [
                    'nationality' => 'Foreigner',
                    'iso' => 'F'
                ]
            ];
        }

        if($pipcnam->shortened_nationality == 'N'){
            $nationalities = Country::select('nationality', 'iso')->get()->toArray();
        }



        $approvers = $permission->users;

        
        $base_currency = Currency::where('base_currency','Y')
                                  ->first();
        $idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                  ->where('client_type', ['I'])->get();
        //corporate local id types    
        $cl_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['C'])
                                    ->where('client_status', ['L'])
                                    ->get();
        // local id types
        $l_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['I'])
                                    ->whereIn('client_status', ['L','F'])
                                    ->whereIn('identity_code', ['1', '2'])
                                    ->get();
        // foreign id types
        $f_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['I'])
                                    ->where('client_status', ['F'])
                                    ->get();
        $buyer_types = DB::table('buyer_type')->get();
        $client_types = DB::table('client_type')->get();
        // Bank details payment methods
        $payment_methods = PaymentMode::select('paymode_name','paymode_code', 'paycode_flag')->get();

        $marital_status = MaritalStatus::select('status_code','description')->get();

        $customer_groups = CustomerGroup::select('group_code','group_desc')->get();
        $company_modules = CompanyModule::select('company_module_code','company_module_name')->get();
        $clientfieldparams = ClientFieldsParams::select('field_id','mandatory_flag','disabled_flag','label_name','label_name_2','country','client_type','individual_mandatory_flag','corporate_mandatory_flag','joint_flag', 'director_flag')
                                                ->get();
        $locationlevels = LocationLevel::select('level_code','level_name','level_type', 'load_method')->where('status','A')->get();
        $locations = Location::select('loc_code','description','loc_level')->where('active', 'Y')->where('loc_level',1)->get();
        $aml_classifications = Aml_classification::all();
                                            
        
        // $idtypes = DB::select("select * from IDENTITY_TYPE a inner join regex_parameters r on a.regex_code = r.regex_code ");
         return view('gb.underwriting.add_client',[
            'branch'=>$this->branches,
          //  'agent'=>$this->agents,
            'types'=>$this->types,
            'title'=>$this->titles,
            'bank'=>$this->banks,
            'client'=>$this->clients,
            'record_data'=>$this->clients,
            'currencies'=>$currency,
            'base_currency'=>$base_currency,
            'identification'=>$identification,
            'countries'=>$country,
            'occupation'=>$occupation,
            'org_roles'=>$org_roles,
            'idtypes'=>$org_roles,
            'identity_number'=>$id_number,
            'genders'=>$genders,
            'l_idtypes'=>$l_idtypes,
            'f_idtypes'=>$f_idtypes,
            'cl_idtypes'=>$cl_idtypes,
            'Source_funds'=>$Source_funds,
            'postal_codes'=>$postal_codes,
            'postal_addresses'=>$postal_addresses,
            'cities'=>$cities,
            'districts'=>$districts,
            'regions'=>$regions,
            'idtypes'=> $idtypes,
            'pipcnam'=>$pipcnam,
            'payment_modes'=>$payment_modes,
            'nationalities'=>$nationalities,
            'source_funds'=>$source_funds,
            'beneficiary_relations'=>$beneficiary_relations,
            'approvers'=>$approvers,
            'client_types'=>$client_types,
            'buyer_types'=>$buyer_types,
            'selfRelationCode' => $selfRelationCode,
            'marital_statuses' =>$marital_status,
            'payment_methods' =>$payment_methods,
            'customer_groups' =>$customer_groups,
            'company_modules' =>$company_modules,
            'client_classifications' => $client_classifications,
            'clientfieldparams' =>$clientfieldparams,
            'locationlevels' =>$locationlevels,
            'locations' =>$locations,
            'hobbies' => $hobbies,
            'pepClassifications' => $pepClassifications,
            'aml_classifications' =>$aml_classifications,      

         ]);
         

          
     }


    //   public function edit_client(Request $request){

    //     Gate::authorize('amend-client-details');
    //     $client_number = $request->get('client_number');

    //     $client_detl = Client::where('client_number',$client_number)
    //                     ->first();
    //     $clientbanks = Clientbanks::where('client_number',$client_number)->get();

    //     $pipcnam = Pipcnam::first();

    //     $l_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
    //                                 ->where('client_type', ['I'])
    //                                 ->where('client_status', ['L'])
    //                                 ->get();

    //     $f_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
    //                                 ->where('client_type', ['I'])
    //                                 ->where('client_status', ['F'])
    //                                 ->get();

    //     foreach($clientbanks as $clientbank){
    //         if((trim($clientbank->bank_code) == trim($client_detl[0]->bank_code)) and (trim($clientbank->branch_code) == trim($client_detl[0]->branch_code)) 
    //         and (trim($clientbank->bank_account_no) == trim($client_detl[0]->bank_account))){
    //             $primary = $clientbank->bank_account_no.'Y';
    //         }
    //     }

    //     if(trim($request->get('nil_endt')) == 'Y'){
    //       $policy_no = $request->get('policy_no');
    //       $endt_renewal_no = $request->get('endt_renewal_no');
    //     }else{
    //       $policy_no = 'c';
    //       $endt_renewal_no = 'c';
    //     }

    //     if($client_detl->nationality == $picnam->country_code){
    //         $idtypes = $l_idtypes;
    //     }else{
    //         $idtypes = $f_idtypes;
    //     }

    //     $this->titles=Title::all();
    //     $this->branches= Branch::all();
    //     $this->banks=Olbnknames::all();
	// 	$this->bankbranches=Olbranch::all();
    //     $this->types=Reidoc::where('orig','U/W')->get(['doc_type','entry_type_descr','description']);

    //     $identification=Identification::all();
    //     $identification=$identification[0];
    //     $country=Country::all();
    //     $occupation=Occupation::where('category',$client_detl[0]->client_type)->get();
    //     $org_roles = Organisation_role::all();
    //     $postal_codes = postal_codes::all();
    //     $genders = Gender::all();
    //     $districts = District::where('region', $client_detl[0]->region)->get();
    //     $regions = Regions::all();
    //     $idtypes = Identity_type::all();
        

    //     return view('gb.underwriting.edit_client-bck',[
    //         'clnt'=>$client_detl,
    //         'clientbanks'=>$clientbanks,
    //         'primary'=>$primary,
    //         'branch'=>$this->branches,
    //         //'agent'=>$this->agents,
    //         'types'=>$this->types,
    //         'title'=>$this->titles,
    //         'bank'=>$this->banks,
	// 		'bankbranch'=>$this->bankbranches,
    //         'client'=>$this->clients,
    //         'identification'=>$identification,
    //         'countries'=>$country,
    //         'occupation'=>$occupation,
    //         'org_roles'=>$org_roles,
    //         'genders'=>$genders,
    //         'postal_codes'=>$postal_codes,
    //         'policy_no'=>$policy_no,
    //         'pipcnam'=>$pipcnam,
    //         'endt_renewal_no'=>$endt_renewal_no,
    //         'nil_endt'=>$request->get('nil_endt'),
    //         'districts'=>$districts,
    //         'l_idtypes'=>$l_idtypes,
    //         'f_idtypes'=>$f_idtypes,
    //         'regions'=>$regions,
    //         'idtypes'=> $idtypes
    //     ]);



    // }

    public function edit_client(Request $request){

        Gate::authorize('amend-client-details');
        $client_number = $request->get('client_number');

        $client = Client::where('client_number',$client_number)
                        ->first(); 


        $clientbanks = PartnerBank::where('partner_number',$client_number)->get();



        foreach($clientbanks as $clientbank){
            if((trim($clientbank->bank_code) == trim($client_detl[0]->bank_code)) and (trim($clientbank->branch_code) == trim($client_detl[0]->branch_code)) 
            and (trim($clientbank->bank_account_no) == trim($client_detl[0]->bank_account))){
                $primary = $clientbank->bank_account_no.'Y';
            }
        }

        if(trim($request->get('nil_endt')) == 'Y'){
          $policy_no = $request->get('policy_no');
          $endt_renewal_no = $request->get('endt_renewal_no');
        }else{
          $policy_no = 'c';
          $endt_renewal_no = 'c';
        }

        $this->titles=Title::all();
        $this->branches= Branch::all();
        $this->banks=Olbnknames::all();
		$this->bankbranches=Bankbranches::all();
        $this->types=Reidoc::where('orig','U/W')->get(['doc_type','entry_type_descr','description']);

        $identification=Identification::all();
        $identification=$identification[0];
        $country=Country::all();
        $occupation=Occupation::where('category',$client_detl[0]->client_type)->get();
        $org_roles = Organisation_role::all();
        $payment_modes = ClientPaymentMode::all();
        $postal_codes = postal_codes::all();
        $postal_addresses = PostalAddress::all();
        $cities=Cities::all();
        $genders = Gender::all();
        $districts = District::where('region', $client_detl[0]->region)->get();
        $regions = Regions::all();
        $idtypes = Identity_type::all();
        $pipcnam = Pipcnam::first();
        $riskprofile = ClientRiskProfile::where('client_number',$client_number)->first();
        $currency = Currency::all();
        $source_funds = Source_funds::all();
        $permission = Permission::where('slug','access-aml-risk-items')->first();
        $beneficiary_relations = Beneficiary_relation::all();
        // $agents  =  Agmnf::select('agent', 'name')
        //                 ->where('branch', $client->branch)
        //                 ->where(function($query) {
        //                     $query->where('stop_flag', '<>', 'Y')
        //                         ->orWhereNull('stop_flag');
        //                 })->get();   
        $intermediaryParams = new IntermediaryQueryParams([
            'branch' => $client->branch,
        ]);
        $agents  =IntermediaryQueryService::getActiveintermediaryByBranch($intermediaryParams)
                                            ->select('intermediary.intermediary_number', 'name')
                                            ->get();

        $client_classifications = ClientClassification::all();
        $pepClassifications = PepClassification::all();


        
        $approvers = $permission->users;

        $base_currency = Currency::where('base_currency','Y')
                                  ->first();
                                  //corporate local id types    
        $cl_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['C'])
                                    ->where('client_status', ['L'])
                                    ->get();
        $l_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['I'])
                                    ->whereIn('client_status', ['L','F'])
                                    ->whereIn('identity_code', ['1', '2'])
                                    ->get();
                                    

        $f_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['I'])
                                    ->where('client_status', ['F'])
                                    ->get();
        $local_country = Country::select('nationality')->where('iso',$pipcnam->country_iso)->first();

        if($pipcnam->shortened_nationality == 'Y'){

            $nationalities = [
                [
                    'nationality' => $local_country->nationality,
                    'iso' => 'L'
                ],
                [
                    'nationality' => 'Foreigner',
                    'iso' => 'F'
                ]
            ];
        }

        if($pipcnam->shortened_nationality == 'N'){
            $nationalities = Country::select('nationality', 'iso')->get()->toArray();
        }
        $buyer_types = DB::table('buyer_type')->get();
        $client_types = DB::table('client_type')->get();

        // Bank details payment methods
        $payment_methods = PaymentMode::select('paymode_name','paymode_code')->get();
        $marital_status = MaritalStatus::select('status_code','description')->get();

        $customer_groups = CustomerGroup::select('group_code','group_desc')->get();
        $company_modules = CompanyModule::select('company_module_code','company_module_name')->get();

        $clientfieldparams = ClientFieldsParams::select('field_id','mandatory_flag','disabled_flag','label_name','label_name_2','country','client_type','individual_mandatory_flag','corporate_mandatory_flag','joint_flag', 'director_flag')
                                                ->get();
        $locationlevels = LocationLevel::select('level_code','level_name','level_type', 'load_method')->where('status','A')->get();
        $locations = Location::select('loc_code','description','loc_level')->where('active', 'Y')->where('loc_level',1)->get();
        $hobbies = Hobby::all();
        $client_hobbies = ClientHobby::where('client_number', $client_number)
        ->pluck('hobbies') 
        ->toArray();
        $aml_classifications = Aml_classification::all();
        $clnt_aml_clsf = Aml_classification::where(['short_descr'=>$client->aml_classification])->value('description');

        return view('gb.underwriting.edit_client',[
            'save_method'=>'update',
            'client'=>$client,
            'record_data'=>$client,
            'clientbanks'=>$clientbanks,
            'primary'=>$primary,
            'branch'=>$this->branches,
            'agents'=>$agents,
            'types'=>$this->types,
            'title'=>$this->titles,
            'bank'=>$this->banks,
            'currencies'=>$currency,
            'riskprofile'=>$riskprofile,
            'payment_modes'=>$payment_modes,
            'base_currency'=>$base_currency,
            'approvers'=>$approvers,
			'bankbranch'=>$this->bankbranches,
            'identification'=>$identification,
            'countries'=>$country,
            'occupation'=>$occupation,
            'source_funds'=>$source_funds,
            'org_roles'=>$org_roles,
            'genders'=>$genders,
            'postal_codes'=>$postal_codes,
            'postal_addresses'=>$postal_addresses,
            'cities'=>$cities,
            'policy_no'=>$policy_no,
            'endt_renewal_no'=>$endt_renewal_no,
            'nil_endt'=>$request->get('nil_endt'),
            'districts'=>$districts,
            'regions'=>$regions,
            'idtypes'=> $idtypes,
            'pipcnam'=>$pipcnam,
            'nationalities'=>$nationalities,
            'beneficiary_relations'=>$beneficiary_relations,
            'l_idtypes'=>$l_idtypes,
            'f_idtypes'=>$f_idtypes,
            'cl_idtypes'=>$cl_idtypes,
            'buyer_types'=>$buyer_types,
            'client_types'=>$client_types,
            'marital_statuses' => $marital_status,
            'payment_methods' => $payment_methods,
            'customer_groups' => $customer_groups,
            'company_modules' => $company_modules,
            'clientfieldparams' => $clientfieldparams,
            'locations' => $locations,
            'locationlevels' => $locationlevels,
            'client_classifications' => $client_classifications,
            'hobbies' => $hobbies,
            'client_hobbies' => $client_hobbies,
            'pepClassifications' => $pepClassifications,
            'aml_classifications'=>$aml_classifications,
            'clnt_aml_clsf'=>$clnt_aml_clsf,


        ]);



    }

    public function view_client(Request $request){

        Gate::authorize('amend-client-details');
        $client_number = $request->get('client_number');

        $client = Client::where('client_number',$client_number)
                        ->first();
        // $agents  =  Agmnf::select('agent', 'name')
        //                 ->where('branch', $client->branch)
        //                 ->where(function($query) {
        //                     $query->where('stop_flag', '<>', 'Y')
        //                         ->orWhereNull('stop_flag');
        //                 })->get();
        $intermediaryParams = new IntermediaryQueryParams([
            'branch' => $client->branch,
        ]);
        $agents  =IntermediaryQueryService::getActiveintermediaryByBranch($intermediaryParams)
                                            ->select('intermediary.intermediary_number', 'name')
                                            ->get();
        $clientbanks = PartnerBank::where('partner_number',$client_number)->get();

        foreach($clientbanks as $clientbank){
            if((trim($clientbank->bank_code) == trim($client_detl[0]->bank_code)) and (trim($clientbank->branch_code) == trim($client_detl[0]->branch_code)) 
            and (trim($clientbank->bank_account_no) == trim($client_detl[0]->bank_account))){
                $primary = $clientbank->bank_account_no.'Y';
            }
        }

        if(trim($request->get('nil_endt')) == 'Y'){
          $policy_no = $request->get('policy_no');
          $endt_renewal_no = $request->get('endt_renewal_no');
        }else{
          $policy_no = 'c';
          $endt_renewal_no = 'c';
        }

        $titles=Title::all();
        $this->branches= Branch::all();
        $this->banks=Olbnknames::all();
		$this->bankbranches=Bankbranches::all();
        $this->types=Reidoc::where('orig','U/W')->get(['doc_type','entry_type_descr','description']);

        $identification=Identification::all();
        $identification=$identification[0];
        $country=Country::all();
        $occupation=Occupation::where('category',$client_detl[0]->client_type)->get();
        $org_roles = Organisation_role::all();
        $payment_modes = ClientPaymentMode::all();
        $postal_codes = postal_codes::all();
        $postal_addresses = PostalAddress::all();
        $cities = Cities::all();
        $genders = Gender::all();
        $districts = District::where('region', $client_detl[0]->region)->get();
        $regions = Regions::all();
        $idtypes = Identity_type::all();
        $pipcnam = Pipcnam::first();
        $riskprofile = ClientRiskProfile::where('client_number',$client_number)->first();
        $currency = Currency::all();
        $source_funds = Source_funds::all();
        $permission = Permission::where('slug','access-aml-risk-items')->first();
        $beneficiary_relations = Beneficiary_relation::all();
        $client_classifications = ClientClassification::all();


        
        $approvers = $permission->users;

        $base_currency = Currency::where('base_currency','Y')
                                  ->first();
                                  //corporate local id types    
        $cl_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['C'])
                                    ->where('client_status', ['L'])
                                    ->get();
        $l_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['I'])
                                    ->whereIn('client_status', ['L','F'])
                                    ->whereIn('identity_code', ['1', '2'])
                                    ->get();

        $f_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['I'])
                                    ->where('client_status', ['F'])
                                    ->get();
        $local_country = Country::select('nationality')->where('iso',$pipcnam->country_iso)->first();

        if($pipcnam->shortened_nationality == 'Y'){

            $nationalities = [
                [
                    'nationality' => $local_country->nationality,
                    'iso' => 'L'
                ],
                [
                    'nationality' => 'Foreigner',
                    'iso' => 'F'
                ]
            ];
        }

        if($pipcnam->shortened_nationality == 'N'){
            $nationalities = Country::select('nationality', 'iso')->get()->toArray();
        }
        $buyer_types = DB::table('buyer_type')->get();
        $client_types = DB::table('client_type')->get();
        $titles=Title::all();
        // $marital_status = MaritalStatus::select('status_code','description')->get();

        $payment_methods = PaymentMode::select('paymode_name','paymode_code')->get();
        $marital_status = MaritalStatus::select('status_code','description')->get();

        $customer_groups = CustomerGroup::select('group_code','group_desc')->get();
        $company_modules = CompanyModule::select('company_module_code','company_module_name')->get();
        $clientfieldparams = ClientFieldsParams::select('field_id','mandatory_flag','disabled_flag','label_name','label_name_2','country','client_type','individual_mandatory_flag','corporate_mandatory_flag','joint_flag', 'director_flag')
                                                ->get();
        $locationlevels = LocationLevel::select('level_code','level_name','level_type')->where('status','A')->get();
        $locations = Location::select('loc_code','description','loc_level')->where('active', 'Y')->where('loc_level',1)->get();
        $hobbies = Hobby::all();

        $client_hobbies = ClientHobby::where('client_number', $client_number)
        ->pluck('hobbies') 
        ->toArray();
        

       
        return view('gb.underwriting.edit_client',[
            'save_method'=>'view',
            'client'=>$client,
            'record_data'=>$client,
            'clientbanks'=>$clientbanks,
            'primary'=>$primary,
            'branch'=>$this->branches,
            'agents'=>$agents,
            'types'=>$this->types,
            'title'=>$titles,
            'bank'=>$this->banks,
            'currencies'=>$currency,
            'riskprofile'=>$riskprofile,
            'payment_modes'=>$payment_modes,
            'base_currency'=>$base_currency,
            'approvers'=>$approvers,
			'bankbranch'=>$this->bankbranches,
            'identification'=>$identification,
            'countries'=>$country,
            'occupation'=>$occupation,
            'source_funds'=>$source_funds,
            'org_roles'=>$org_roles,
            'genders'=>$genders,
            'postal_codes'=>$postal_codes,
            'postal_addresses'=>$postal_addresses,
            'cities'=>$cities,
            'policy_no'=>$policy_no,
            'endt_renewal_no'=>$endt_renewal_no,
            'nil_endt'=>$request->get('nil_endt'),
            'districts'=>$districts,
            'regions'=>$regions,
            'idtypes'=> $idtypes,
            'pipcnam'=>$pipcnam,
            'nationalities'=>$nationalities,
            'beneficiary_relations'=>$beneficiary_relations,
            'l_idtypes'=>$l_idtypes,
            'f_idtypes'=>$f_idtypes,
            'cl_idtypes'=>$cl_idtypes,
            'buyer_types'=>$buyer_types,
            'client_types'=>$client_types,
            'marital_statuses'=>$marital_status,
            'payment_methods' => $payment_methods,
            'customer_groups' => $customer_groups,
            'company_modules' => $company_modules,
            'clientfieldparams' => $clientfieldparams,
            'locationlevels' => $locationlevels,
            'locations' => $locations,
            'client_classifications' => $client_classifications,
            'hobbies' => $hobbies,
            'client_hobbies' => $client_hobbies,


        ]);



    }


    public function get_id_validate(Request $request)
    {
        Gate::authorize('create-client');

        $country = $request->country; 
        $idType = $request->idType;
        $idNumber = $request->id;
        
        $idTypes = Identity_type::select('identity_descr')
                                    ->where('identity_code',$idType)
                                    ->first();

        $idTypeDesc = $idTypes->identity_descr;

       if($idTypeDesc == 'PASSPORT'){

          $names = Client::where("id_number", $idNumber)
                            ->where('nationality', $country)
                            ->pluck('name')
                            ->toArray();

            
       }else{
           $names = Client::where("id_number", $idNumber)
                            ->pluck('name')
                            ->toArray();
       }
       

       if(count($names) > 0 ){
           return response()->json(['status'=>1, 'validate'=>$names,'idNumber'=>$idNumber]);
       }else{
            return response()->json(['status'=> 0, 'validate'=>$names,'idNumber'=>$idNumber]);
       }
        

    }

    public function get_joint_field_validate(Request $request)
    {
        Gate::authorize('create-client');
        if ($request->has('column') && $request->has('value') && $request->has('clientno') && $request->has('field') && $request->has('memberId')) {
            $column = $request->input('column');
            $value = $request->input('value');
            $clientNo = $request->input('clientno');
            $field = $request->input('field');
            $glyphicon = $request->input('glyphicon');
            $memberId = $request->input('memberId');

            // Ensure the column is a valid field name
            $validColumns = ['email', 'mobile_no', 'pin_number', 'incorporation_cert', 'identity_number'];
            if (!in_array($column, $validColumns)) {
                return response()->json(['status' => 0, 'error' => 'Invalid column name']);
            }
    
            $validate = JointMember::where("client_number", $clientNo)
                ->where($column, $value)
                ->where('id', '!=', $memberId)
                ->first();

            return response()->json([
                'status' => $validate ? 1 : 2,
                'value' => $value,
                'field' => $field,
                'glyphicon' =>$glyphicon
            ]);
        } else {
            return response()->json(['status' => 0, 'error' => 'Missing required parameters']);
        }
    }

     public function branch_agt_lnk(Request $request){
       

        $grace_period=Pipcnam::first()->intermediary_licence_grace_period ?? 0;
        $intermediaryParams = new IntermediaryQueryParams([
            'branch' => $request->get('branch'),
            'additionalFields' => ['intermediary.licence_number','intermediary.licence_expiry_date'],
        ]);
        $this->agents  =IntermediaryQueryService::getActiveintermediaryByBranch($intermediaryParams)
            // ->whereRaw("SYSDATE - licence_expiry_date <= ?", [$grace_period])
            ->get();

        echo $this->agents;


        
     }
     public function get_acctype(Request $request){
        
        $acc_type=Acctype::where('acc_type',$request->acctype)->first();

        echo $acc_type;
        
     }
     public function check_license(Request $request){
       
        $acc_type=Acctype::where('acc_type',$request->acctype)->first();
        $check_license =$acc_type->check_license;
    
        if($check_license == "Y"){
            //    $agent=Agmnf::where('branch',$request->branch)
            //                 ->where('agent',$request->agent)
            //                 ->first();
            $intermediaryParams = new IntermediaryQueryParams([
                'agentNo' => $request->agent,
                'additionalFields' => ['licence_number','licence_expiry_date']
            ]);

            $agent  =  IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
        
        $license_expiry_date=Carbon::parse($agent->licence_expiry_date);
        $today = Carbon::today();
        $grace_period_days = Pipcnam::first()->intermediary_licence_grace_period ?? 0; 
        
        if ($license_expiry_date->lt($today)) {
            $days_difference = $license_expiry_date->diffInDays($today);

            // Check if the difference is not greater than the grace period
            if ($days_difference <= $grace_period_days) {
                // Within grace period
                $resp = [
                    'status' => -2,
                    'license_expiry_date' =>$license_expiry_date,
                    'msg' => "Agent: ".$agent->name."'s license has expired but is within the grace period."
                ];
            } else {
                // Beyond grace period
                $resp = [
                    'status' => -1,
                    'license_expiry_date' =>$license_expiry_date,
                    'msg' => "Agent: ".$agent->name."'s license has expired and is beyond the grace period."
                ];
            }

        }else{
            $resp=[
                'status'=>0,
                'msg'=>'ok'
            ];
        }
        
        return $resp;

        }
        $resp=[
            'status'=>0,
            'msg'=>'ok'
        ];
        return $resp;
        
     }
     public function fetch_form_details(){
    //    $ann = Polast::where('ast_marker', 'A')->get(); 
       $curren = Currency::where('base_currency', 'Y')->get();
       $busstype = Bustype::where('type_of_bus', '1')->get();

        return json_encode([$curren,$busstype]);

     }

     public function fetch_bank_branches(Request $request){

        //$bank_code = substr($request->get('bank_code'), 1);

        //$result = Olbranches::where('bank_code',$bank_code)->get();

        $bank_code = (string)$request->get('bank_code');
        
        $olbranch_details = Bankbranches::WhereRaw("trim(bank_code)='".trim($bank_code)."'")->get();
        //echo $olbranch_details;

        echo json_encode($olbranch_details);

   }

     public function fetch_workflow_id(Request $request){

        $class = ClassModel::where('class',$request->get('class'))->get(['workflow_id']);

        echo $class[0]->workflow_id; 
     }



     public function checkDuplicate(Request $request){
        $field_value = $request->fieldvalue;
        $column_name = $request->colname;

        $duplicate = Client::where($column_name,$field_value)->exists();
        

        if($duplicate){
            return ['status'=> 0];
        }else{
            return ['status'=> 1];
        }

     }


     public function generate_client_number($type){

        Gate::authorize('create-client');

         //check client type
        $client_number = $this->generateOnboardingNumber($type);
        
        // Remove any special characters from the first name and convert it to uppercase
        return $client_number;

     }



    //  get client contact details
    public function get_client_contacts(Request $request)
    {
        $client_number = $request->client_number;
        $pageAction = $request->has('pageAction') ? ($request->pageAction === 'update' ? 'Edit' : $request->pageAction) : 'Edit';
        $client = Client::where('client_number',$client_number)->first();

        $contacts = Beneficiary::where('client_number',$client_number)
                                ->where('contact_type','C')
                                ->get(['beneficiary_id','full_name','telephone','phy_loc','region','district','positions','client_number','org_role',"location_1","location_2","location_3","location_4","location_5","location_6","loclabel_1","loclabel_2","loclabel_3","loclabel_4","loclabel_5","loclabel_6"]);
        
        return DataTables::of($contacts)
            
            
            ->editColumn('region', function ($contact) {
				if(($contact->region != "")){
					$region = Regions::where('region',$contact->region)->first();
					return $region->region_name;
				}else{
					return "N/A";
				}

			})
            ->editColumn('district', function ($contact) {
				if(($contact->district != "")){
					$district = District::where('district_code',$contact->district)->first();
					return $district->district_name;
				}else{
					return "N/A";
				}

			})
            ->editColumn('relationship', function ($contact) {
                $client = Client::where('client_number', $contact->client_number)->first();
                if ($client->client_type == 'C') {
                    if(($contact->org_role != "")){
                        $relationship = Organisation_role::where('role_id',$contact->org_role)->first();
                        return $relationship->role_description;
                    }else{
                        return "---";
                    }
                } else if($client->client_type == 'I'){
                    if(($contact->positions != "")){
                        $relationship = Beneficiary_relation::where('relation_code',$contact->positions)->first();
                        return $relationship->relation_name;
                    }else{
                        return "---";
                    }
                }else{

                }
                
                
				

			})
            ->addColumn('action',function ($contact)  use ($pageAction){
                return '
                <a href="#" id="edit-contact-client" class="btn btn-sm btn-info edit" data-contact_id="'.$contact->beneficiary_id.'">
                    <i class="fa fa-pen"></i> '.$pageAction.'
                </a>&nbsp';

            })
            ->make(true);
        
    }


    // Get intermediary contact
    public function get_intermediary_contact(Request $request)
    {
        $term=$request->get('term');

        $results = array();

        $intermediaries = AgmnfMasterTemp::select('global_intermediary_id','first_name','last_name','other_name','cell_phone','addr1','name')
                            ->whereRaw("UPPER(name) LIKE '%" . strtoupper($term) . "%'")
                            ->where(function($q){
                                $q->where('stop_flag','N')
                                  ->orWhereNull('stop_flag');
                            })
                            ->limit(10)
                            ->get();

        foreach($intermediaries as $intermediary){
            
            $results[] = [
                'value'=> $intermediary->global_intermediary_id.' - '.$intermediary->name,
                'fname' => trim($intermediary->first_name),
                'lname' => trim($intermediary->last_name),
                'oname' => trim($intermediary->other_name),
                'name' => trim($intermediary->name),
                'mobile' => trim($intermediary->cell_phone),
                'postaladdress' => trim($intermediary->addr1),
                
            ];
        }
        // return Response::Json($results);
        return response()->json($results);
    }

    //  get client director details

    public function get_client_directors(Request $request)
    {
        $client_number = $request->client_number;
        $client = Client::where('client_number',$client_number)->first();
        $pageAction = $request->has('pageAction') ? ($request->pageAction === 'update' ? 'Edit' : $request->pageAction) : 'Edit';

        $directors = Beneficiary::where('client_number',$client_number)
                                ->where('contact_type','D')
                                ->get(['client_type','beneficiary_id','full_name','telephone','e_mail','client_number']);
        
        return DataTables::of($directors)
        ->editColumn('client_type',function ($member) {
            if ($member->client_type=='I') {
                return "Individual";
            } elseif ($member->client_type=='C') {
                return "Corporate";
            } else {
                return $member->client_type;
            }
        })
    
            ->addColumn('action',function ($director)use ($pageAction) {
                return '
                <a href="#" id="edit-director-client" class="btn btn-sm btn-info edit" data-contact_id="'.$director->beneficiary_id.'">
                    <i class="fa fa-pen"></i> '.$pageAction.'
                </a>&nbsp';

            })
            ->make(true);
        
    }

    public function get_joint_members(Request $request)
    {
        $client_number = $request->client_number;
        $members = JointMember::where('client_number',$client_number)->get();
        $pageAction = $request->has('pageAction') ? ($request->pageAction === 'update' ? 'Edit' : $request->pageAction) : 'Edit';

        return DataTables::of($members)
    
            ->editColumn('client_type',function ($member) {
                if ($member->client_type=='I') {
                    return "Individual";
                } elseif ($member->client_type=='C') {
                    return "Corporate";
                } else {
                    return $member->client_type;
                }
            })
            ->editColumn('full_name',function ($member) {
                if (!empty($member->first_name)) {
                    return trim($member->first_name . ' ' . $member->last_name . ' ' . $member->other_names);
                } elseif (!empty($member->name)) {
                    return $member->name;
                } else {
                    return '--';
                }
            })
            ->addColumn('action',function ($member) use ($pageAction){
                
                return '
                <a href="#" id="edit-member-client" class="btn btn-sm btn-info edit" data-member_id="'.$member->id.'">
                    <i class="fa fa-pen"></i> '.$pageAction.'
                </a>&nbsp';

            })
            ->make(true);
        
    }

    // save new contact on edit client screen
    public function save_client_director(Request $request)
    {
        $client_no = $request->client_no;

        $director_old = Beneficiary::where('client_number',$client_no)
        ->where('beneficiary_id',trim($request->contact_id))
        ->first();

        $clientType =$request->dir_client_type;

        DB::beginTransaction();
        try{

                $beneficiary_details = new Beneficiary;

                if ($clientType == 'I') {

                $beneficiary_details->first_name = $request->dir_firstname;
                $beneficiary_details->last_name =  $request->dir_lastname;
                $beneficiary_details->other_name = $request->dir_othernames;
                $beneficiary_details->full_name =$request->dir_firstname.' '.$request->dir_lastname.' '.$request->dir_othernames;
                $beneficiary_details->title = $request->dir_title;
                $beneficiary_details->identity_type = $request->dir_id_type;
                $beneficiary_details->id_number = $request->dir_identity_number;
                $beneficiary_details->gender = $request->dir_gender;

                }elseif($clientType == 'C') {
                $beneficiary_details->full_name =$request->dir_member_name;
                $beneficiary_details->incorporation_cert = $request->dir_incorporation_cert;
                    
                }
                $beneficiary_details->client_number =  $client_no;
                $beneficiary_details->client_type = $clientType;
                $beneficiary_details->nationality = $request->dir_nationality;
                $beneficiary_details->dob = $request->dir_dob;               
                $beneficiary_details->pin_number = $request->dir_pin_number;               
                $beneficiary_details->country_code = $request->dir_country_code;
                $beneficiary_details->telephone = $request->dir_mobile_no;
                $beneficiary_details->e_mail = $request->dir_email;
                $beneficiary_details->political_exposed = $request->dir_politicalexposed;
                $beneficiary_details->aml_classification = $request->dir_amlclassification;
                $beneficiary_details->contact_type = $request->contact_type;
                $beneficiary_details->ubo = $request->dir_ubo;
                $beneficiary_details->investment = $request->dir_investment_type;
                $beneficiary_details->investment_value = $request->dir_investment_value;

                $beneficiary_details->save();

                 DB::commit();

                $changed_data = $beneficiary_details->getAttributes();
                $process_slug = 'client-addition';
                $activity_slug = 'update';
                $unique_item = $client_no;
                $old_data = $director_old;
                $ip =$request->ip();

                log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

                $client =  Client::whereRaw("trim(client_number) = '" .trim($client_no). "' ")->first();
            

                return response()->json(['status' => 1, 'message' => 'Successfully added director details for client : '.$client->name.' with client no '.trim($client_no)]);
        }catch (Exception $e) {
            dd($e);
            // An error occurred; cancel the transaction...
            DB::rollBack();
            
        }
    }


    // save new contact on edit client screen
    public function save_client_contact(Request $request)
    {
        $client_no = $request->client_no;

        $contact_old = Beneficiary::select(["first_name","last_name","other_name","full_name","positions","telephone",
        "relation_type" ,"org_role" ,"region" ,"district" ,"phy_loc" ,"political_exposed",                                     
        "aml_classification","contact_type","id_number","location_1","location_2","location_3","location_4","location_5","location_6","loclabel_1","loclabel_2","loclabel_3","loclabel_4","loclabel_5","loclabel_6"])

        ->where('client_number',$client_no)
        ->where('beneficiary_id',trim($request->edcontact_id))
        ->first();

       if($client_no) {
            $beneficiary_details = new Beneficiary;
            $beneficiary_details->client_number =  $client_no;
            $beneficiary_details->first_name = $request->edit_ben_firstname;
            $beneficiary_details->last_name =  $request->edit_ben_lastname;
            $beneficiary_details->other_name = $request->edit_ben_othernames;
            $beneficiary_details->full_name =$request->edit_ben_firstname.' '.$request->edit_ben_lastname.' '.$request->edit_ben_othernames;
            $beneficiary_details->positions =$request->ed_relationship;
            $beneficiary_details->org_role = $request->ed_ben_orgrole;
            $beneficiary_details->telephone = $request->edit_ben_telephone;
            $beneficiary_details->e_mail = $request->ed_ben_email;
            // $beneficiary_details->region = $request->edit_ben_region;
            // $beneficiary_details->district=$request->edit_ben_district;
            $beneficiary_details->phy_loc=$request->ed_address;
            $beneficiary_details->political_exposed = $request->ed_ben_politicalexposed;
            $beneficiary_details->aml_classification = $request->ed_ben_amlclassification;
            $beneficiary_details->contact_type = $request->contact_type;

            // Location fields
            $beneficiary_details->location_1   = $request->input('contact_level_1');
            $beneficiary_details->location_2   = $request->input('contact_level_2');
            $beneficiary_details->location_3 = $request->input('contact_level_3');
            $beneficiary_details->location_4  = $request->input('contact_level_4');
            $beneficiary_details->location_5  = $request->input('contact_level_5');
            $beneficiary_details->location_6   = $request->input('contact_level_6');

            // Location labels  
            $beneficiary_details->loclabel_1   = $request->input('loclevel_1');
            $beneficiary_details->loclabel_2   = $request->input('loclevel_2');
            $beneficiary_details->loclabel_3 = $request->input('loclevel_3');
            $beneficiary_details->loclabel_4  = $request->input('loclevel_4');
            $beneficiary_details->loclabel_5  = $request->input('loclevel_5');
            $beneficiary_details->loclabel_6   = $request->input('loclevel_6');
            // $beneficiary_details->updated_on= Carbon::now();
            // $beneficiary_details->updated_by= trim(Auth::user()->user_name);
            $beneficiary_details->save();
            
            $changed_data = $beneficiary_details->getAttributes();
            $process_slug = 'client-addition';
            $activity_slug = 'update';
            $unique_item = $client_no;
            $old_data = $contact_old;
            $ip =$request->ip();

            log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

            $client =  Client::whereRaw("trim(client_number) = '" .trim($client_no). "' ")->first();
           

            return response()->json(['status' => 1, 'message' => 'Successfully added contact details for client : '.$client->name.' with client no '.trim($client_no)]);
       }

       return response()->json(['status' => 0, 'message' => 'Could not correctly pass the client record.']);
    }


    public function getContactDetails(Request $request)
    {
        $contact_id = $request->contact_id;
            
        if ($contact_id) {

            $contact_rec = Beneficiary::where('beneficiary_id',trim($contact_id))->first();
            
            return response()->json(['status'=> 1, 'data'=>$contact_rec]);

        }else{
            return response()->json(['status'=> 0,'message'=> "Unprocessed entity"]);
        }
    }
    public function getMemberDetails(Request $request)
    {
        $member_id = $request->member_id;
            
        if ($member_id) {

            $member_rec = JointMember::where('id',trim($member_id))->first();
            
            return response()->json(['status'=> 1, 'data'=>$member_rec]);

        }else{
            return response()->json(['status'=> 0,'message'=> "Unprocessed entity"]);
        }
    }
    public function save_client_member(Request $request)
    {
        $joint_client_no = $request->joint_client_number;
        $client_no = $joint_client_no[0];

        $latestId = JointMember::max('id');
        $newId = $latestId ? $latestId + 1 : 1;

        $member_old=JointMember::select('*')->where('id',$request->id)->first();
        $i=0;
        $clientType = $request->joint_client_type[$i];
        
        DB::beginTransaction();
        try{
            $member = new JointMember();
            $member->id=$newId;

            if ($clientType == 'I') {
                
                $member->first_name = $request->joint_firstname[$i];
                $member->last_name = $request->joint_lastname[$i];
                $member->other_names = $request->joint_othernames[$i];
                $member->title = $request->joint_title[$i];
                $member->gender = $request->joint_gender[$i];
                $member->identity_type = $request->joint_id_type[$i];
                $member->identity_number = $request->joint_identity_number[$i];
                
            } elseif($clientType == 'C') {
                
                $member->name = $request->joint_member_name[$i];
                $member->incorporation_cert = $request->joint_incorporation_cert[$i];
                
            }
            $member->client_number = $request->joint_client_number[$i];
            $member->client_type = $request->joint_client_type[$i];
            $member->nationality = $request->joint_nationality[$i];
            $member->pin_number = $request->joint_pin_number[$i];
            $member->dob = $request->joint_dob[$i];
            $member->country_code = $request->joint_country_code[$i];
            $member->mobile_no = $request->joint_mobile_no[$i];
            $member->email = $request->joint_email[$i];

            $member->save();

            $changed_data = $member->getAttributes();
            $process_slug = 'client-addition';
            $activity_slug = 'update';
            $unique_item = $client_no;
            $old_data = $member_old;
            $ip =$request->ip();

            log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

            DB::commit();

            return response()->json([
                'status' => 1,
                'message' => 'Joint member added successfully'
            ]);
        } catch (Exception $e) {
            // An error occurred; cancel the transaction...
            DB::rollBack();
            
        }
        
    }
    public function update_client_member(Request $request)
    {
        $joint_client_no = $request->joint_client_number;
        $client_no = $joint_client_no[0];

        $member_id = $request->member_id;

        $member_old=JointMember::select('*')->where('id',$member_id)->first();
        $i=0;
        $clientType = $request->joint_client_type[$i];

        DB::beginTransaction();
        try{
            $member=JointMember::select('*')->where('id',$member_id)->first();
              
            if ($clientType == 'I') {
                
                $member->first_name = $request->joint_firstname[$i];
                $member->last_name = $request->joint_lastname[$i];
                $member->other_names = $request->joint_othernames[$i];
                $member->title = $request->joint_title[$i];
                $member->gender = $request->joint_gender[$i];
                $member->identity_type = $request->joint_id_type[$i];
                $member->identity_number = $request->joint_identity_number[$i];

                $member->incorporation_cert = '';
                
            } elseif($clientType == 'C') {
                
                $member->name = $request->joint_member_name[$i];
                $member->incorporation_cert = $request->joint_incorporation_cert[$i];
                
                $member->first_name = '';
                $member->last_name = '';
                $member->other_names = '';
                $member->title = '';
                $member->identity_type = '';
                $member->identity_number = '';
            }

            $member->client_type = $request->joint_client_type[$i];
            $member->name = $request->joint_member_name[$i];
            $member->nationality = $request->joint_nationality[$i];
            $member->pin_number = $request->joint_pin_number[$i];
            $member->dob = $request->joint_dob[$i];
            $member->country_code = $request->joint_country_code[$i];
            $member->mobile_no = $request->joint_mobile_no[$i];
            $member->email = $request->joint_email[$i];
            $member->save();

            $changed_data = $member->getAttributes();
            $process_slug = 'client-addition';
            $activity_slug = 'update';
            $unique_item = $client_no;
            $old_data = $member_old;
            $ip =$request->ip();

            log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

            DB::commit();

            return response()->json([
                'status' => 1,
                'message' => 'Joint member updated successfully'
            ]);
        } catch (Exception $e) {
            // An error occurred; cancel the transaction...
            DB::rollBack();
            
        }
        
    }
    
    public function update_client_director(Request $request)
    {
        $client_no = $request->client_no;
        $nil_endt=$request->input('dir_nil_endt');

        if (trim($nil_endt)=='Y') {
            $policy_no = $request->input('dir_policy_no');
            $endt_renewal_no = $request->input('dir_endt_renewal_no');
        }

        $director_old = Beneficiary::where('client_number',$client_no)
                                ->where('beneficiary_id',trim($request->contact_id))
                                ->first();

        $clnt_upd = Beneficiary::where('client_number',$client_no)
                                ->where('beneficiary_id',trim($request->contact_id))
                                ->first();

        $data = array();
        $client_type = $request->input('ed_dir_client_type');

        switch ($client_type){
            case 'I':

                $full_name = trim($request->input('ed_dir_firstname')).' '.trim($request->input('ed_dir_lastname')).' '.trim($request->input('ed_dir_othernames'));

                if(trim($clnt_upd->first_name) != trim($request->input('ed_dir_firstname'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$client_no,
                        'table_name'=>'Beneficiary',
                        'field_changed'=>'first name',
                        'old_value'=>$clnt_upd->first_name,
                        'new_value'=>$request->input('ed_dir_firstname'),
                        'date_changed'=>Carbon::now('EAT'),
                        'ip_address'=>$request->ip(),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->first_name = $request->input('ed_dir_firstname');
                    $clnt_upd->full_name = $full_name;

                    array_push($data, $audit1);
                }

                if(trim($clnt_upd->last_name) != trim($request->input('ed_dir_lastname'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$client_no,
                        'table_name'=>'Beneficiary',
                        'field_changed'=>'last name',
                        'old_value'=>$clnt_upd->last_name,
                        'new_value'=>$request->input('ed_dir_lastname'),
                        'date_changed'=>Carbon::now('EAT'),
                        'ip_address'=>$request->ip(),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->last_name = $request->input('ed_dir_lastname');
                    $clnt_upd->full_name = $full_name;

                    array_push($data, $audit1);
                }

                if(trim($clnt_upd->other_name) != trim($request->input('ed_dir_othernames'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$client_no,
                        'table_name'=>'Beneficiary',
                        'field_changed'=>'other name',
                        'old_value'=>$clnt_upd->other_name,
                        'new_value'=>$request->input('ed_dir_othernames'),
                        'date_changed'=>Carbon::now('EAT'),
                        'ip_address'=>$request->ip(),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->other_name = $request->input('ed_dir_othernames');
                    $clnt_upd->full_name = $full_name;

                    array_push($data, $audit1);
                }
                if(trim($clnt_upd->identity_type) != trim($request->input('ed_dir_id_type'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$client_no,
                        'table_name'=>'Beneficiary',
                        'field_changed'=>'Identity Type',
                        'old_value'=>$clnt_upd->identity_type,
                        'new_value'=>$request->input('ed_dir_id_type'),
                        'date_changed'=>Carbon::now('EAT'),
                        'ip_address'=>$request->ip(),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->identity_type = $request->input('ed_dir_id_type');

                    array_push($data, $audit1);
                }

                if(trim($clnt_upd->id_number) != trim($request->input('ed_dir_identity_number'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$client_no,
                          'table_name'=>'Beneficiary',
                          'field_changed'=>'national id',
                          'old_value'=>$clnt_upd->id_number,
                          'new_value'=>$request->input('ed_dir_identity_number'),
                          'date_changed'=>Carbon::now('EAT'),
                          'ip_address'=>$request->ip(),
                          'system_user'=>Auth::user()->user_name
                      );
        
                    $clnt_upd->id_number = $request->input('ed_dir_identity_number');
                    
                    array_push($data, $audit1);
                }
                if(trim($clnt_upd->gender) != trim($request->input('ed_dir_gender'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$client_no,
                          'table_name'=>'Beneficiary',
                          'field_changed'=>'gender',
                          'old_value'=>$clnt_upd->gender,
                          'new_value'=>$request->input('ed_dir_gender'),
                          'date_changed'=>Carbon::now('EAT'),
                          'ip_address'=>$request->ip(),
                          'system_user'=>Auth::user()->user_name
                      );
        
                    $clnt_upd->gender = $request->input('ed_dir_gender');
                    
                    array_push($data, $audit1);
                }
                if(trim($clnt_upd->title) != trim($request->input('ed_dir_title'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$client_no,
                          'table_name'=>'Beneficiary',
                          'field_changed'=>'salutation',
                          'old_value'=>$clnt_upd->title,
                          'new_value'=>$request->input('ed_dir_title'),
                          'date_changed'=>Carbon::now('EAT'),
                          'ip_address'=>$request->ip(),
                          'system_user'=>Auth::user()->user_name
                      );
        
                    $clnt_upd->title = $request->input('ed_dir_title');
                    
                    array_push($data, $audit1);
                }

                break;

            case 'C':

                if(trim($clnt_upd->full_name) != trim($request->input('ed_dir_member_name'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$client_no,
                          'table_name'=>'Beneficiary',
                          'field_changed'=>'Name',
                          'old_value'=>$clnt_upd->full_name,
                          'new_value'=>$request->input('ed_dir_member_name'),
                          'date_changed'=>Carbon::now('EAT'),
                          'ip_address'=>$request->ip(),
                          'system_user'=>Auth::user()->user_name
                      );
        
                    $clnt_upd->full_name = $request->input('ed_dir_member_name');
                    
                    array_push($data, $audit1);
                }
                if(trim($clnt_upd->incorporation_cert) != trim($request->input('ed_dir_incorporation_cert'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$client_no,
                          'table_name'=>'Incorporation Certificate',
                          'field_changed'=>'Name',
                          'old_value'=>$clnt_upd->incorporation_cert,
                          'new_value'=>$request->input('ed_dir_incorporation_cert'),
                          'date_changed'=>Carbon::now('EAT'),
                          'ip_address'=>$request->ip(),
                          'system_user'=>Auth::user()->user_name
                      );
        
                    $clnt_upd->incorporation_cert = $request->input('ed_dir_incorporation_cert');
                    
                    array_push($data, $audit1);
                }   
                
                break;


        }

        if(trim($clnt_upd->e_mail) != trim($request->input('ed_dir_email'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'email',
                  'old_value'=>$clnt_upd->e_mail,
                  'new_value'=>$request->input('ed_dir_email'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->e_mail = $request->input('ed_dir_email');
            
            array_push($data, $audit1);
        }
        if(trim($clnt_upd->nationality) != trim($request->input('ed_dir_nationality'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'nationality',
                  'old_value'=>$clnt_upd->nationality,
                  'new_value'=>$request->input('ed_dir_nationality'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->nationality = $request->input('ed_dir_nationality');
            
            array_push($data, $audit1);
        }
        if(trim($clnt_upd->number_of_shares) != trim($request->input('ed_dir_share'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'Shares',
                  'old_value'=>$clnt_upd->number_of_shares,
                  'new_value'=>$request->input('ed_dir_share'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->number_of_shares = $request->input('ed_dir_share');
            
            array_push($data, $audit1);
        }
        if(trim($clnt_upd->ubo) != trim($request->input('ed_dir_ubo'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'Ultimate Beneficial Owner',
                  'old_value'=>$clnt_upd->ubo,
                  'new_value'=>$request->input('ed_dir_ubo'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->ubo = $request->input('ed_dir_ubo');
            
            array_push($data, $audit1);
        }
        if(trim($clnt_upd->investment) != trim($request->input('ed_dir_investment_type'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'Investment Type',
                  'old_value'=>$clnt_upd->investment,
                  'new_value'=>$request->input('ed_dir_investment_type'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->investment = $request->input('ed_dir_investment_type');
            
            array_push($data, $audit1);
        }
        if(trim($clnt_upd->country_code) != trim($request->input('ed_dir_country_code'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'Country Code',
                  'old_value'=>$clnt_upd->country_code,
                  'new_value'=>$request->input('ed_dir_country_code'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->country_code = $request->input('ed_dir_country_code');
            
            array_push($data, $audit1);
        }
        if(trim($clnt_upd->pin_number) != trim($request->input('ed_dir_pin_number'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'Pin Number',
                  'old_value'=>$clnt_upd->pin_number,
                  'new_value'=>$request->input('ed_dir_pin_number'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->pin_number = $request->input('ed_dir_pin_number');
            
            array_push($data, $audit1);
        }
        if(trim($clnt_upd->dob) != trim($request->input('ed_dir_dob'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'Date',
                  'old_value'=>$clnt_upd->dob,
                  'new_value'=>$request->input('ed_dir_dob'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->dob = $request->input('ed_dir_dob');
            
            array_push($data, $audit1);
        }
        if(trim($clnt_upd->investment_value) != trim($request->input('ed_dir_investment_value'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'Investment Value',
                  'old_value'=>$clnt_upd->investment_value,
                  'new_value'=>$request->input('ed_dir_investment_value'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->investment_value = $request->input('ed_dir_investment_value');
            
            array_push($data, $audit1);
        }

        if(trim($clnt_upd->telephone) != trim($request->input('ed_dir_mobile_no'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'phone number',
                  'old_value'=>$clnt_upd->telephone,
                  'new_value'=>$request->input('ed_dir_mobile_no'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->telephone = $request->input('ed_dir_mobile_no');
            
            array_push($data, $audit1);
        }

        if(trim($clnt_upd->political_exposed) != trim($request->input('ed_dir_politicalexposed'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'politically exposed',
                  'old_value'=>$clnt_upd->political_exposed,
                  'new_value'=>$request->input('ed_dir_politicalexposed'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->political_exposed = $request->input('ed_dir_politicalexposed');
            
            array_push($data, $audit1);
        }

        if(trim($clnt_upd->aml_classification) != trim($request->input('ed_dir_amlclassification'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'aml classification',
                  'old_value'=>$clnt_upd->aml_classification,
                  'new_value'=>$request->input('ed_dir_amlclassification'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->aml_classification = $request->input('ed_dir_amlclassification');
            
            array_push($data, $audit1);
        }

        if(trim($clnt_upd->contact_type) != trim($request->input('contact_type'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$client_no,
                  'table_name'=>'Beneficiary',
                  'field_changed'=>'Contact type',
                  'old_value'=>$clnt_upd->contact_type,
                  'new_value'=>$request->input('contact_type'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->contact_type = $request->input('contact_type');
            
            array_push($data, $audit1);
        }

        $client = Client::whereRaw("trim(client_number) = '" . trim($client_no) . "'")->first();

        $changed_data = $clnt_upd->getAttributes();
        $process_slug = 'client-addition';
        $activity_slug = 'update';
        $unique_item = $client_no;
        $old_data = $director_old;
        $ip =$request->ip();

        log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);


        if($nil_endt == 'Y'){
            $clnt_upd = $clnt_upd->toArray();
            if(Beneficiary::where("beneficiary_id",trim($request->contact_id))->update($clnt_upd)){

                DB::table('risk_audit_trail')->insert($data);

                return response()->json(['status' => 1, 'message' => 'Successfully updated director details for client : '.$client->name.' with client no '.trim($client_no)]);
            }else{
                return response()->json(['status' => 0, 'message' => 'Could not correctly pass the client record.']);
            }
        }else{
            if($client_no) {

                $clnt_upd = $clnt_upd->toArray();

                Beneficiary::where("beneficiary_id",trim($request->contact_id))->update($clnt_upd);

                return response()->json(['status' => 1, 'message' => 'Successfully updated director details for client : '.$client->name.' with client no '.trim($client_no)]);
            }else{
                return response()->json(['status' => 0, 'message' => 'Could not correctly pass the client record.']);    
            }
       
            
        }

       

       
    }

    public function update_client_contact(Request $request)
    {
        $client_no = $request->edclient_no;

       $contact = $request->edcontact_id;

        $nil_endt=$request->input('ben_nil_endt');

        if (trim($nil_endt)=='Y') {
            $policy_no = $request->input('ben_policy_no');
            $endt_renewal_no = $request->input('ben_endt_renewal_no');
        }
        $contact_old = Beneficiary::select(["first_name","last_name","other_name","full_name","positions","telephone",
        "relation_type" ,"org_role" ,"region" ,"district" ,"phy_loc" ,"political_exposed",                                     
        "aml_classification","contact_type","id_number","location_1","location_2","location_3","location_4","location_5","location_6","loclabel_1","loclabel_2","loclabel_3","loclabel_4","loclabel_5","loclabel_6"])

        ->where('client_number',$client_no)
        ->where('beneficiary_id',trim($request->edcontact_id))
        ->first();

        if($contact == null){
            $beneficiary_details = new Beneficiary;
            $beneficiary_details->client_number =  $client_no;
            $beneficiary_details->first_name = $request->edit_ben_firstname;
            $beneficiary_details->last_name =  $request->edit_ben_lastname;
            $beneficiary_details->other_name = $request->edit_ben_othernames;
            $beneficiary_details->full_name =$request->edit_ben_firstname.' '.$request->edit_ben_lastname.' '.$request->edit_ben_othernames;
            $beneficiary_details->positions =$request->ed_relationship;
            $beneficiary_details->org_role = $request->ed_ben_orgrole;
            $beneficiary_details->telephone = $request->edit_ben_telephone;
            $beneficiary_details->e_mail = $request->ed_ben_email;
            // $beneficiary_details->district=$request->edit_ben_district;
            $beneficiary_details->phy_loc=$request->ed_address;
            $beneficiary_details->political_exposed = $request->ed_ben_politicalexposed;
            $beneficiary_details->aml_classification = $request->ed_ben_amlclassification;
            $beneficiary_details->contact_type = 'C';

          // Utility function to safely handle array inputs
            function convertArrayToString($value) {
                return is_array($value) ? implode(' ', $value) : $value;
            }

            // Location fields
            $beneficiary_details->location_1 = convertArrayToString($request->input('contact_level_1'));
            $beneficiary_details->location_2 = convertArrayToString($request->input('contact_level_2'));
            $beneficiary_details->location_3 = convertArrayToString($request->input('contact_level_3'));
            $beneficiary_details->location_4 = convertArrayToString($request->input('contact_level_4'));
            $beneficiary_details->location_5 = convertArrayToString($request->input('contact_level_5'));
            $beneficiary_details->location_6 = convertArrayToString($request->input('contact_level_6'));

            // Location labels  
            $beneficiary_details->loclabel_1 = convertArrayToString($request->input('loclevel_1'));
            $beneficiary_details->loclabel_2 = convertArrayToString($request->input('loclevel_2'));
            $beneficiary_details->loclabel_3 = convertArrayToString($request->input('loclevel_3'));
            $beneficiary_details->loclabel_4 = convertArrayToString($request->input('loclevel_4'));
            $beneficiary_details->loclabel_5 = convertArrayToString($request->input('loclevel_5'));
            $beneficiary_details->loclabel_6 = convertArrayToString($request->input('loclevel_6'));

            // $beneficiary_details->updated_on= Carbon::now();
            // $beneficiary_details->updated_by= trim(Auth::user()->user_name);
            // dd($beneficiary_details);
            $beneficiary_details->save();
            
            $changed_data = $beneficiary_details->getAttributes();
            $process_slug = 'client-addition';
            $activity_slug = 'update';
            $unique_item = $client_no;
            $old_data = $contact_old;
            $ip =$request->ip();

            log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

            $client =  Client::whereRaw("trim(client_number) = '" .trim($client_no). "' ")->first();
           

            return response()->json(['status' => 1, 'message' => 'Successfully added contact details for client : '.$client->name.' with client no '.trim($client_no)]);
        }else{
            
        $clnt_upd = Beneficiary::select(["first_name","last_name","other_name","full_name","positions","telephone",
        "relation_type" ,"org_role" ,"region" ,"district" ,"phy_loc" ,"political_exposed",                                     
        "aml_classification","contact_type","id_number","location_1","location_2","location_3","location_4","location_5","location_6","loclabel_1","loclabel_2","loclabel_3","loclabel_4","loclabel_5","loclabel_6" ])

        ->where('client_number',$client_no)
        ->where('beneficiary_id',trim($request->edcontact_id))
        ->first();
        


        $data = array();

        $full_name = trim($request->input('edit_ben_firstname')).' '.trim($request->input('edit_ben_lastname')).' '.trim($request->input('edit_ben_othernames'));

        if(trim($clnt_upd->first_name) != trim($request->input('edit_ben_firstname'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'first name',
        'old_value'=>$clnt_upd->first_name,
        'new_value'=>$request->input('edit_ben_firstname'),
        'date_changed'=>Carbon::now('EAT'),
        'ip_address'=>$request->ip(),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->first_name = $request->input('edit_ben_firstname');
        $clnt_upd->full_name = $full_name;

        array_push($data, $audit1);
        }  

        if(trim($clnt_upd->last_name) != trim($request->input('edit_ben_lastname'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'last name',
        'old_value'=>$clnt_upd->last_name,
        'new_value'=>$request->input('edit_ben_lastname'),
        'date_changed'=>Carbon::now('EAT'),
        'ip_address'=>$request->ip(),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->last_name = $request->input('edit_ben_lastname');
        $clnt_upd->full_name = $full_name;

        array_push($data, $audit1);
        }

        if(trim($clnt_upd->other_name) != trim($request->input('edit_ben_othernames'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'other name',
        'old_value'=>$clnt_upd->other_name,
        'new_value'=>$request->input('edit_ben_othernames'),
        'ip_address'=>$request->ip(),
        'date_changed'=>Carbon::now('EAT'),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->other_name = $request->input('edit_ben_othernames');
        $clnt_upd->full_name = $full_name;

        array_push($data, $audit1);
        }

        if(trim($clnt_upd->positions) != trim($request->input('ed_relationship'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'positions',
        'old_value'=>$clnt_upd->positions,
        'new_value'=>$request->input('ed_relationship'),
        'date_changed'=>Carbon::now('EAT'),
        'ip_address'=>$request->ip(),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->positions = $request->input('ed_relationship');

        array_push($data, $audit1);
        }

        if(trim($clnt_upd->org_role) != trim($request->input('ed_ben_orgrole'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'organisation role',
        'old_value'=>$clnt_upd->org_role,
        'new_value'=>$request->input('ed_ben_orgrole'),
        'date_changed'=>Carbon::now('EAT'),
        'ip_address'=>$request->ip(),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->org_role = $request->input('ed_ben_orgrole');

        array_push($data, $audit1);
        }

        if(trim($clnt_upd->telephone) != trim($request->input('edit_ben_telephone'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'telephone',
        'old_value'=>$clnt_upd->telephone,
        'new_value'=>$request->input('edit_ben_telephone'),
        'date_changed'=>Carbon::now('EAT'),
        'ip_address'=>$request->ip(),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->telephone = $request->input('edit_ben_telephone');

        array_push($data, $audit1);
        }

        if(trim($clnt_upd->region) != trim($request->input('ed_ben_region'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'region',
        'old_value'=>$clnt_upd->region,
        'new_value'=>$request->input('ed_ben_region'),
        'ip_address'=>$request->ip(),
        'date_changed'=>Carbon::now('EAT'),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->region = $request->input('ed_ben_region');

        array_push($data, $audit1);
        }

        if(trim($clnt_upd->district) != trim($request->input('ed_ben_district'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'district',
        'old_value'=>$clnt_upd->district,
        'new_value'=>$request->input('ed_ben_district'),
        'date_changed'=>Carbon::now('EAT'),
        'ip_address'=>$request->ip(),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->district = $request->input('ed_ben_district');

        array_push($data, $audit1);
        }

        if (trim($clnt_upd->e_mail) != trim($request->input('ed_ben_email'))) {

            $audit1 = [
                'policy_no' => $policy_no,
                'endt_renewal_no' => $endt_renewal_no,
                'risk_item' => $client_no,
                'table_name' => 'Beneficiary',
                'field_changed' => 'e_mail',
                'old_value' => $clnt_upd->e_mail,
                'new_value' => $request->input('ed_ben_email'),
                'date_changed' => Carbon::now('EAT'),
                'ip_address' => $request->ip(),
                'system_user' => Auth::user()->user_name,
            ];
        
            array_push($data, $audit1);
            $clnt_upd->e_mail = $request->input('ed_ben_email');
        }
        
        if(trim($clnt_upd->phy_loc) != trim($request->input('ed_address'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'phy_loc',
        'old_value'=>$clnt_upd->phy_loc,
        'new_value'=>$request->input('ed_address'),
        'date_changed'=>Carbon::now('EAT'),
        'ip_address'=>$request->ip(),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->phy_loc = $request->input('ed_address');

        array_push($data, $audit1);
        }

        if(trim($clnt_upd->political_exposed) != trim($request->input('ed_ben_politicalexposed'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'political_exposed',
        'old_value'=>$clnt_upd->political_exposed,
        'new_value'=>$request->input('ed_ben_politicalexposed'),
        'date_changed'=>Carbon::now('EAT'),
        'ip_address'=>$request->ip(),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->political_exposed = $request->input('ed_ben_politicalexposed');

        array_push($data, $audit1);
        }

        if(trim($clnt_upd->aml_classification) != trim($request->input('ed_ben_amlclassification'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'aml classification',
        'old_value'=>$clnt_upd->aml_classification,
        'new_value'=>$request->input('ed_ben_amlclassification'),
        'date_changed'=>Carbon::now('EAT'),
        'ip_address'=>$request->ip(),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->aml_classification = $request->input('ed_ben_amlclassification');

        array_push($data, $audit1);
        }

        function convertArrayToString($value) {
        return is_array($value) ? implode(' ', $value) : $value;
        }

        $old_location_1 = convertArrayToString($clnt_upd->location_1);
        $new_location_1 = convertArrayToString($request->contact_level_1);

        if (trim($old_location_1) != trim($new_location_1)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'location 1',
        'old_value' => $old_location_1,
        'new_value' => $new_location_1,
        'ip_address' => $request->ip(),
        'date_changed' => Carbon::now('EAT'),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->location_1 = $new_location_1;

        array_push($data, $audit1);
        }

        $old_location_2 = convertArrayToString($clnt_upd->location_2);
        $new_location_2 = convertArrayToString($request->contact_level_2);

        if (trim($old_location_2) != trim($new_location_2)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'location 2',
        'old_value' => $old_location_2,
        'new_value' => $new_location_2,
        'ip_address' => $request->ip(),
        'date_changed' => Carbon::now('EAT'),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->location_2 = $new_location_2;

        array_push($data, $audit1);
        }

        $old_location_3 = convertArrayToString($clnt_upd->location_3);
        $new_location_3 = convertArrayToString($request->contact_level_3);

        if (trim($old_location_3) != trim($new_location_3)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'location 3',
        'old_value' => $old_location_3,
        'new_value' => $new_location_3,
        'ip_address' => $request->ip(),
        'date_changed' => Carbon::now('EAT'),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->location_3 = $new_location_3;

        array_push($data, $audit1);
        }

        $old_location_4 = convertArrayToString($clnt_upd->location_4);
        $new_location_4 = convertArrayToString($request->contact_level_4);

        if (trim($old_location_4) != trim($new_location_4)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'location 4',
        'old_value' => $old_location_4,
        'new_value' => $new_location_4,
        'ip_address' => $request->ip(),
        'date_changed' => Carbon::now('EAT'),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->location_4 = $new_location_4;

        array_push($data, $audit1);
        }

        $old_location_5 = convertArrayToString($clnt_upd->location_5);
        $new_location_5 = convertArrayToString($request->contact_level_5);

        if (trim($old_location_5) != trim($new_location_5)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'location 5',
        'old_value' => $old_location_5,
        'new_value' => $new_location_5,
        'ip_address' => $request->ip(),
        'date_changed' => Carbon::now('EAT'),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->location_5 = $new_location_5;

        array_push($data, $audit1);
        }

        $old_location_6 = convertArrayToString($clnt_upd->location_6);
        $new_location_6 = convertArrayToString($request->contact_level_6);

        if (trim($old_location_6) != trim($new_location_6)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'location 6',
        'old_value' => $old_location_6,
        'new_value' => $new_location_6,
        'ip_address' => $request->ip(),
        'date_changed' => Carbon::now('EAT'),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->location_6 = $new_location_6;

        array_push($data, $audit1);
        }

        $old_loclabel_1 = convertArrayToString($clnt_upd->loclabel_1);
        $new_loclabel_1 = convertArrayToString($request->input('loclevel_1'));

        if (trim($old_loclabel_1) != trim($new_loclabel_1)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'loclevel 1',
        'old_value' => $old_loclabel_1,
        'new_value' => $new_loclabel_1,
        'date_changed' => Carbon::now('EAT'),
        'ip_address' => $request->ip(),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->loclabel_1 = $new_loclabel_1;

        array_push($data, $audit1);
        }

        $old_loclabel_2 = convertArrayToString($clnt_upd->loclabel_2);
        $new_loclabel_2 = convertArrayToString($request->input('loclevel_2'));

        if (trim($old_loclabel_2) != trim($new_loclabel_2)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'loclevel 2',
        'old_value' => $old_loclabel_2,
        'new_value' => $new_loclabel_2,
        'date_changed' => Carbon::now('EAT'),
        'ip_address' => $request->ip(),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->loclabel_2 = $new_loclabel_2;

        array_push($data, $audit1);
        }

        $old_loclabel_3 = convertArrayToString($clnt_upd->loclabel_3);
        $new_loclabel_3 = convertArrayToString($request->input('loclevel_3'));

        if (trim($old_loclabel_3) != trim($new_loclabel_3)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'loclevel 3',
        'old_value' => $old_loclabel_3,
        'new_value' => $new_loclabel_3,
        'date_changed' => Carbon::now('EAT'),
        'ip_address' => $request->ip(),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->loclabel_3 = $new_loclabel_3;

        array_push($data, $audit1);
        }

        $old_loclabel_4 = convertArrayToString($clnt_upd->loclabel_4);
        $new_loclabel_4 = convertArrayToString($request->input('loclevel_4'));

        if (trim($old_loclabel_4) != trim($new_loclabel_4)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'loclevel 4',
        'old_value' => $old_loclabel_4,
        'new_value' => $new_loclabel_4,
        'date_changed' => Carbon::now('EAT'),
        'ip_address' => $request->ip(),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->loclabel_4 = $new_loclabel_4;

        array_push($data, $audit1);
        }

        $old_loclabel_5 = convertArrayToString($clnt_upd->loclabel_5);
        $new_loclabel_5 = convertArrayToString($request->input('loclevel_5'));

        if (trim($old_loclabel_5) != trim($new_loclabel_5)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'loclevel 5',
        'old_value' => $old_loclabel_5,
        'new_value' => $new_loclabel_5,
        'date_changed' => Carbon::now('EAT'),
        'ip_address' => $request->ip(),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->loclabel_5 = $new_loclabel_5;

        array_push($data, $audit1);
        }

        $old_loclabel_6 = convertArrayToString($clnt_upd->loclabel_6);
        $new_loclabel_6 = convertArrayToString($request->input('loclevel_6'));

        if (trim($old_loclabel_6) != trim($new_loclabel_6)) {
        $audit1 = array(
        'policy_no' => $policy_no,
        'endt_renewal_no' => $endt_renewal_no,
        'risk_item' => $client_no,
        'table_name' => 'Beneficiary',
        'field_changed' => 'loclevel 6',
        'old_value' => $old_loclabel_6,
        'new_value' => $new_loclabel_6,
        'date_changed' => Carbon::now('EAT'),
        'ip_address' => $request->ip(),
        'system_user' => Auth::user()->user_name
        );

        $clnt_upd->loclabel_6 = $new_loclabel_6;

        array_push($data, $audit1);
        }        

        if(trim($clnt_upd->contact_type) != trim($request->input('edcontact_type'))){
        $audit1 = array(
        'policy_no'=>$policy_no,
        'endt_renewal_no'=>$endt_renewal_no,
        'risk_item'=>$client_no,
        'table_name'=>'Beneficiary',
        'field_changed'=>'Contact type',
        'old_value'=>$clnt_upd->contact_type,
        'new_value'=>$request->input('edcontact_type'),
        'date_changed'=>Carbon::now('EAT'),
        'ip_address'=>$request->ip(),
        'system_user'=>Auth::user()->user_name
        );

        $clnt_upd->contact_type = $request->input('edcontact_type');

        array_push($data, $audit1);
        }
        $client = Client::whereRaw("trim(client_number) = '" . trim($client_no) . "'")->first();
        
        $changed_data = $clnt_upd->getAttributes();
        $process_slug = 'client-addition';
        $activity_slug = 'update';
        $unique_item = $client_no;
        $old_data = $contact_old;
        $ip =$request->ip();

        log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

        if($nil_endt == 'Y'){
        $clnt_upd = $clnt_upd->toArray();
        if(Beneficiary::where("beneficiary_id",trim($request->edcontact_id))->update($clnt_upd)){

        DB::table('risk_audit_trail')->insert($data);

        return response()->json(['status' => 1, 'message' => 'Successfully updated contact details for client : '.$client->name.' with client no '.trim($client_no)]);
        }else{
        return response()->json(['status' => 0, 'message' => 'Could not correctly pass the client record.']);
        }
        }else{
        if($client_no) {

        $clnt_upd = $clnt_upd->toArray();

        Beneficiary::where("beneficiary_id",trim($request->edcontact_id))->update($clnt_upd);

        return response()->json(['status' => 1, 'message' => 'Successfully updated director details for client : '.$client->name.' with client no '.trim($client_no)]);
        }else{
        return response()->json(['status' => 0, 'message' => 'Could not correctly pass the client record.']);    
        }


        }

        }


    }
    public function checkBeneficiaryStatus(Request $request)
    {
        $clientNo = $request->query('client_number');

        if (!$clientNo) {
            return response()->json([
                'error' => 'Client ID is required'
            ], 400);
        }

        $beneficiary = Beneficiary::where('client_number', $clientNo)->first();

        if ($beneficiary) {
            $isPoliticallyExposed = $beneficiary->politically_exposed == 'Y';

            return response()->json([
                'client_number' => $clientNo,
                'politically_exposed' => $isPoliticallyExposed ? 'Y' : 'N'
            ]);
        } else {
            return response()->json([
                'error' => 'Beneficiary not found for the given client ID'
            ], 404);
        }
    }
     public function client_upd(Request $request){
        $clnt_no=$request->input('client_number');
        
        $nil_endt=$request->input('nil_endt');
        $alpha3_country_code = Country::where('country_code',$country)->value('abbrev');

        if (trim($nil_endt)=='Y') {
          $policy_no = $request->input('policy_no');
          $endt_renewal_no = $request->input('endt_renewal_no');
        }

        $previous_data = Client::where('client_number',$clnt_no)->first();
        $clnt_upd = Client::where('client_number',$clnt_no)->first();
        $riskprofile_upd = ClientRiskProfile::where('client_number',$clnt_no)->first();
        $joint_account = $request->input('joint_account');
        
        
        //joint account 
        $clnt_upd->joint_account=$joint_account;
        $clnt_upd->relationship=$joint_account === 'Y' ? $request->input('relation') : '';
        $clnt_upd->relationship_desc=$joint_account === 'Y' ? $request->input('relationship_desc') : '';
        
        if(empty($riskprofile_upd)){
            $riskprofile_upd = new ClientRiskProfile();
            $riskprofile_upd->client_number = $clnt_no;
        }
    
        

        $old_branch_code = $clnt_upd->branch_code;
        $old_bank_account_no = $clnt_upd->bank_account_no;
        $old_bank_code = $clnt_upd->bank_code;

        $data = array();
        $client_type = $request->input('client_type_val');        

         if(trim($clnt_upd->approved_by) != trim($request->input('approved_by'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$clnt_no,
                  'table_name'=>'client',
                  'field_changed'=>'approved_by',
                  'old_value'=>$clnt_upd->approved_by,
                  'new_value'=>$request->input('approved_by'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->approved_by = $request->input('approved_by');

            array_push($data, $audit1);
         }

         if(trim($clnt_upd->buyer_type) != trim($request->input('buyer_type_val'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$clnt_no,
                  'table_name'=>'client',
                  'field_changed'=>'buyer_type',
                  'old_value'=>$clnt_upd->buyer_type,
                  'new_value'=>$request->input('buyer_type_val'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->buyer_type = $request->input('buyer_type_val');

            array_push($data, $audit1);
         }
         
         if(trim($clnt_upd->sanction_screening) != trim($request->input('sanction_screening'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$clnt_no,
                  'table_name'=>'client',
                  'field_changed'=>'sanction_screening',
                  'old_value'=>$clnt_upd->sanction_screening,
                  'new_value'=>$request->input('sanction_screening'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->sanction_screening = $request->input('sanction_screening');

            array_push($data, $audit1);
         }
        
         if(trim($clnt_upd->source_of_funds) != trim($request->input('source_of_funds'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$clnt_no,
                  'table_name'=>'client',
                  'field_changed'=>'source_of_funds',
                  'old_value'=>$clnt_upd->source_of_funds,
                  'new_value'=>$request->input('source_of_funds'),
                  'date_changed'=>Carbon::now('EAT'),
                  'ip_address'=>$request->ip(),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->source_of_funds = $request->input('source_of_funds');

            array_push($data, $audit1);
         }

            if(trim($clnt_upd->lifecycle_status) != trim($request->input('lifecycle_status'))){
                $audit1 = array(
                    'policy_no'=>$policy_no,
                    'endt_renewal_no'=>$endt_renewal_no,
                    'risk_item'=>$clnt_no,
                    'table_name'=>'client',
                    'field_changed'=>'lifecycle_status',
                    'old_value'=>$clnt_upd->lifecycle_status,
                    'new_value'=>$request->input('lifecycle_status'),
                    'date_changed'=>Carbon::now('EAT'),
                    'ip_address'=>$request->ip(),
                    'system_user'=>Auth::user()->user_name
                );

                $clnt_upd->lifecycle_status = $request->input('lifecycle_status');

                array_push($data, $audit1);
            }

            

            if(trim($clnt_upd->expected_annual_income) != trim($request->input('annual_income'))){
                $audit1 = array(
                    'policy_no'=>$policy_no,
                    'endt_renewal_no'=>$endt_renewal_no,
                    'risk_item'=>$clnt_no,
                    'table_name'=>'client',
                    'field_changed'=>' annual_income',
                    'old_value'=>$clnt_upd-> annual_income,
                    'new_value'=>$request->input(' annual_income'),
                    'date_changed'=>Carbon::now('EAT'),
                    'ip_address'=>$request->ip(),
                    'system_user'=>Auth::user()->user_name
                );

                $clnt_upd->expected_annual_income = $request->input('annual_income');

                array_push($data, $audit1);
            }

            

            if(trim($clnt_upd->vrn) != trim($request->input('vrn'))){
                $audit1 = array(
                    'policy_no'=>$policy_no,
                    'endt_renewal_no'=>$endt_renewal_no,
                    'risk_item'=>$clnt_no,
                    'table_name'=>'client',
                    'field_changed'=>'vrn',
                    'old_value'=>$clnt_upd->vrn,
                    'new_value'=>$request->input('vrn'),
                    'date_changed'=>Carbon::now('EAT'),
                    'ip_address'=>$request->ip(),
                    'system_user'=>Auth::user()->user_name
                );

                $clnt_upd->vrn = $request->input('vrn');

                array_push($data, $audit1);
            }
            if(trim($clnt_upd->nationality) != trim($request->input('nationality'))){
                $audit1 = array(
                    'policy_no'=>$policy_no,
                    'endt_renewal_no'=>$endt_renewal_no,
                    'risk_item'=>$clnt_no,
                    'table_name'=>'client',
                    'field_changed'=>'nationality',
                    'old_value'=>$clnt_upd->nationality,
                    'new_value'=>$request->input('nationality'),
                    'date_changed'=>Carbon::now('EAT'),
                    'ip_address'=>$request->ip(),
                    'system_user'=>Auth::user()->user_name
                );

            $clnt_upd->nationality = $request->input('nationality');

            array_push($data, $audit1);
         }
         if(trim($clnt_upd->client_classification) != trim($request->input('client_classification'))){
            $audit1 = array(
                  'policy_no'=>$policy_no,
                  'endt_renewal_no'=>$endt_renewal_no,
                  'risk_item'=>$clnt_no,
                  'table_name'=>'client',
                  'field_changed'=>'client_classification',
                  'old_value'=>$clnt_upd->client_classification,
                  'new_value'=>$request->input('client_classification'),
                  'ip_address'=>$request->ip(),
                  'date_changed'=>Carbon::now('EAT'),
                  'system_user'=>Auth::user()->user_name
              );

            $clnt_upd->client_classification = $request->input('client_classification');

                array_push($data, $audit1);
            }
            if(trim($clnt_upd->aml_classification) != trim($request->input('aml_classification'))){
                $audit1 = array(
                    'policy_no'=>$policy_no,
                    'endt_renewal_no'=>$endt_renewal_no,
                    'risk_item'=>$clnt_no,
                    'table_name'=>'client',
                    'field_changed'=>'aml_classification',
                    'old_value'=>$clnt_upd->aml_classification,
                    'new_value'=>$request->input('aml_classification'),
                    'ip_address'=>$request->ip(),
                    'date_changed'=>Carbon::now('EAT'),
                    'system_user'=>Auth::user()->user_name
                );

                $clnt_upd->aml_classification = $request->input('aml_classification');

                array_push($data, $audit1);
            }
            if(trim($clnt_upd->political_exposed) != trim($request->input('political_exposed'))){
                $audit1 = array(
                    'policy_no'=>$policy_no,
                    'endt_renewal_no'=>$endt_renewal_no,
                    'risk_item'=>$clnt_no,
                    'table_name'=>'client',
                    'field_changed'=>'political_exposed',
                    'old_value'=>$clnt_upd->political_exposed,
                    'new_value'=>$request->input('political_exposed'),
                    'ip_address'=>$request->ip(),
                    'date_changed'=>Carbon::now('EAT'),
                    'system_user'=>Auth::user()->user_name
                );

                $clnt_upd->political_exposed = $request->input('political_exposed');

                array_push($data, $audit1);
            }
            if(trim($clnt_upd->pep_classification) != trim($request->input('pep_classification'))){
                $audit1 = array(
                    'policy_no'=>$policy_no,
                    'endt_renewal_no'=>$endt_renewal_no,
                    'risk_item'=>$clnt_no,
                    'table_name'=>'client',
                    'field_changed'=>'pep_classification',
                    'old_value'=>$clnt_upd->pep_classification,
                    'new_value'=>$request->input('pep_classification'),
                    'ip_address'=>$request->ip(),
                    'date_changed'=>Carbon::now('EAT'),
                    'system_user'=>Auth::user()->user_name
                );

                $clnt_upd->pep_classification = $request->input('pep_classification');

                array_push($data, $audit1);
            }
            if(trim($clnt_upd->id_number) != trim($request->input('identity_number'))){
                $audit1 = array(
                    'policy_no'=>$policy_no,
                    'endt_renewal_no'=>$endt_renewal_no,
                    'risk_item'=>$clnt_no,
                    'table_name'=>'client',
                    'field_changed'=>'id number',
                    'old_value'=>$clnt_upd->id_number,
                    'new_value'=>$request->input('identity_number'),
                    'ip_address'=>$request->ip(),
                    'date_changed'=>Carbon::now('EAT'),
                    'system_user'=>Auth::user()->user_name
                );

                $clnt_upd->id_number = $request->input('identity_number');

                array_push($data, $audit1);
            }
    
            if(trim($clnt_upd->occupation) != trim($request->input('occupation'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'client',
                        'field_changed'=>'occupation',
                        'old_value'=>$clnt_upd->occupation,
                        'new_value'=>$request->input('occupation'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->occupation = $request->input('occupation');

                    array_push($data, $audit1);
            }
            
            if(trim($clnt_upd->address1) != trim($request->input('postal_address'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'client',
                      'field_changed'=>'postal address',
                      'old_value'=>$clnt_upd->address1,
                      'new_value'=>$request->input('postal_address'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->address1 = $request->input('postal_address');

                array_push($data, $audit1);
            } 
            if(trim($clnt_upd->address2) != trim($request->input('postal_code'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'client',
                      'field_changed'=>'postal code',
                      'old_value'=>$clnt_upd->address2,
                      'new_value'=>$request->input('postal_code'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->address2 = $request->input('postal_code');

                array_push($data, $audit1);
            } 

            if(trim($clnt_upd->address3) != trim($request->input('physical_address'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'client',
                      'field_changed'=>'physical address',
                      'old_value'=>$clnt_upd->address3,
                      'new_value'=>$request->input('physical_address'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->address3 = $request->input('physical_address');

                array_push($data, $audit1);
            }

            if(trim($clnt_upd->pin_number) != trim($request->input('pin'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'client',
                      'field_changed'=>'TIN Number',
                      'old_value'=>$clnt_upd->pin_number,
                      'new_value'=>$request->input('pin_number'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->pin_number = $request->input('pin');

                array_push($data, $audit1);
            }

            if(trim($clnt_upd->mobile_no) != trim($request->input('mobile_no'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'client',
                      'field_changed'=>'Mobile Number',
                      'old_value'=>$clnt_upd->mobile_no,
                      'new_value'=>$request->input('mobile_no'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->mobile_no = $request->input('mobile_no');

                array_push($data, $audit1);
            }

            if(trim($clnt_upd->telephone) != trim($request->input('telephone'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'client',
                      'field_changed'=>'Telephone Number',
                      'old_value'=>$clnt_upd->telephone,
                      'new_value'=>$request->input('telephone'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->telephone = $request->input('telephone');

                array_push($data, $audit1);
            }

            if(trim($clnt_upd->country_code) != trim($request->input('country'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Country Code',
                      'old_value'=>$clnt_upd->country_code,
                      'new_value'=>$request->input('country'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->country_code = $request->input('country');
                $clnt_upd->alpha3_country_code = $alpha3_country_code;
                

                array_push($data, $audit1);
            }

            if(trim($clnt_upd->e_mail) != trim($request->input('email'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Email',
                      'old_value'=>$clnt_upd->e_mail,
                      'new_value'=>$request->input('email'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->e_mail = $request->input('email');

                array_push($data, $audit1);
            }

            if(trim($clnt_upd->alt_email) != trim($request->input('alt_email'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Alternate Email',
                      'old_value'=>$clnt_upd->alt_email,
                      'new_value'=>$request->input('alt_email'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->alt_email = $request->input('alt_email');

                array_push($data, $audit1);
            }

            if($clnt_upd->dob != $request->input('dob')){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'DOB',
                      'old_value'=>$clnt_upd->dob,
                      'new_value'=>$request->input('dob'),
                      'date_changed'=>Carbon::now('EAT'),
                      'ip_address'=>$request->ip(),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->dob = $request->input('dob');
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->vat_exempt) != trim($request->input('vat_exempt'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'VAT Exempt',
                      'old_value'=>$clnt_upd->vat_exempt,
                      'new_value'=>$request->input('vat_exempt'),
                      'date_changed'=>Carbon::now('EAT'),
                      'ip_address'=>$request->ip(),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->vat_exempt = $request->input('vat_exempt');
                
                array_push($data, $audit1);
            }

            if((trim($clnt_upd->bank_account_no) != trim($request->input('bank_account'))) && ($request->input('bank_account') != null)){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Bank Account Number',
                      'old_value'=>$clnt_upd->bank_account_no,
                      'new_value'=>$request->input('bank_account'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->bank_account_no = $request->input('bank_account');
                
                array_push($data, $audit1);
            }

            if((trim($clnt_upd->bank_code) != trim($request->input('bank'))) && ($request->input('bank') != null)){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Bank Code',
                      'old_value'=>$clnt_upd->bank_code,
                      'new_value'=>$request->input('bank'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->bank_code = $request->input('bank');
                
                array_push($data, $audit1);
            }
            

            if((trim($clnt_upd->branch_code) != trim($request->input('bank_branch'))) && ($request->input('bank_branch') != null)){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Branch Code',
                      'old_value'=>$clnt_upd->branch_code,
                      'new_value'=>$request->input('bank_branch'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->branch_code = $request->input('bank_branch');
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->contact_firstname) != trim($request->input('contact_firstname'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'contact firstname',
                      'old_value'=>$clnt_upd->contact_firstname,
                      'new_value'=>$request->input('contact_firstname'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->contact_firstname = $request->input('contact_firstname');
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->contact_surname) != trim($request->input('contact_surname'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'contact surname',
                      'old_value'=>$clnt_upd->contact_surname,
                      'new_value'=>$request->input('contact_surname'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->contact_surname = $request->input('contact_surname');
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->contact_othername) != trim($request->input('contact_othername'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'contact othername',
                      'old_value'=>$clnt_upd->contact_othername,
                      'new_value'=>$request->input('contact_othername'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->contact_othername = $request->input('contact_othername');
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->contact_position) != trim($request->input('contact_position'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'contact position',
                      'old_value'=>$clnt_upd->contact_position,
                      'new_value'=>$request->input('contact_position'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->contact_position = $request->input('contact_position');
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->contact_telephone) != trim($request->input('contact_telephone'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'contact telephone',
                      'old_value'=>$clnt_upd->contact_telephone,
                      'new_value'=>$request->input('contact_telephone'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->contact_telephone = $request->input('contact_telephone');
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->identity_type) != $request->id_type){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Identity Type',
                      'old_value'=>$clnt_upd->identity_type,
                      'new_value'=>$request->id_type,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->identity_type = $request->id_type;
                
                array_push($data, $audit1);
            }

            
            // branch fields
            if(trim($clnt_upd->is_direct) != $request->input('direct')){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Direct Client',
                      'old_value'=>$clnt_upd->is_direct,
                      'new_value'=>$request->input('direct'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->is_direct = $request->input('direct');
                
                array_push($data, $audit1);
            }
            


            if(trim($clnt_upd->branch) != $request->input('c_branch')){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Branch',
                      'old_value'=>$clnt_upd->branch,
                      'new_value'=>$request->input('c_branch'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->branch = $request->input('c_branch');
                
                array_push($data, $audit1);
            }

            
            if(trim($clnt_upd->agent) != $request->input('agent')){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Agent',
                      'old_value'=>$clnt_upd->agent ?? null,
                      'new_value'=>$request->input('agent'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->agent = $request->input('agent');
                
                array_push($data, $audit1);
            }

            

            // Other details fields

            if(trim($clnt_upd->consent_mkt) != $request->send_mkt_material){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Send Marketing Material',
                      'old_value'=>$clnt_upd->identity_type,
                      'new_value'=>$request->send_mkt_material,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->consent_mkt = $request->send_mkt_material;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->consent_internal) != $request->share_kyc_internally){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Share KYC internally',
                      'old_value'=>$clnt_upd->consent_internal,
                      'new_value'=>$request->share_kyc_internally,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->consent_internal = $request->share_kyc_internally;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->consent_out_ke) != $request->share_kyc_externally){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Share KYC externally',
                      'old_value'=>$clnt_upd->consent_out_ke,
                      'new_value'=>$request->share_kyc_externally,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->consent_out_ke = $request->share_kyc_externally;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->consent_child) != $request->share_child_kyc){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Share Child KYC',
                      'old_value'=>$clnt_upd->consent_child,
                      'new_value'=>$request->share_child_kyc,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->consent_child = $request->share_child_kyc;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->consent_3rd_party) != $request->kyc_details_from3rdparty){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'3rd Party KYC details',
                      'old_value'=>$clnt_upd->consent_3rd_party,
                      'new_value'=>$request->kyc_details_from3rdparty,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->consent_3rd_party = $request->kyc_details_from3rdparty;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->consent_analysis) != $request->use_kyc_for_research){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Use KYC for research',
                      'old_value'=>$clnt_upd->consent_analysis,
                      'new_value'=>$request->use_kyc_for_research,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->consent_analysis = $request->use_kyc_for_research;
                
                array_push($data, $audit1);
            }

      

              if(trim($clnt_upd->customer_group) != $request->customer_group){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'Customer Group',
                      'old_value'=>$clnt_upd->customer_group,
                      'new_value'=>$request->customer_group,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->customer_group = $request->customer_group;
                
                array_push($data, $audit1);
            }
            // End of other details tab.

            // Locations update
            if(trim($clnt_upd->location_1) != $request->level_1){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'location 1',
                      'old_value'=>$clnt_upd->location_1,
                      'new_value'=>$request->level_1,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->location_1 = $request->level_1;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->location_2) != $request->level_2){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'location 2',
                      'old_value'=>$clnt_upd->location_2,
                      'new_value'=>$request->level_2,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->location_2 = $request->level_2;
                
                array_push($data, $audit1);
            }
            if(trim($clnt_upd->location_3) != $request->level_3){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'location 3',
                      'old_value'=>$clnt_upd->location_3,
                      'new_value'=>$request->level_3,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->location_3 = $request->level_3;
                
                array_push($data, $audit1);
            }
            if(trim($clnt_upd->location_4) != $request->level_4){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'location 4',
                      'old_value'=>$clnt_upd->location_4,
                      'new_value'=>$request->level_4,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->location_4 = $request->level_4;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->location_5) != $request->level_5){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'location 5',
                      'old_value'=>$clnt_upd->location_5,
                      'new_value'=>$request->level_5,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->location_5 = $request->level_5;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->location_6) != $request->level_6){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'location 6',
                      'old_value'=>$clnt_upd->location_6,
                      'new_value'=>$request->level_6,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->location_6 = $request->level_6;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->loclabel_1) != $request->loclevel_1){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'loclabel 1',
                      'old_value'=>$clnt_upd->loclabel_1,
                      'new_value'=>$request->loclevel_1,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->loclabel_1 = $request->loclevel_1;
                
                array_push($data, $audit1);
            }
            
            if(trim($clnt_upd->loclabel_2) != $request->loclevel_2){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'loclabel 2',
                      'old_value'=>$clnt_upd->loclabel_2,
                      'new_value'=>$request->loclevel_2,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->loclabel_2 = $request->loclevel_2;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->loclabel_3) != $request->loclevel_3){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'loclabel 3',
                      'old_value'=>$clnt_upd->loclabel_3,
                      'new_value'=>$request->loclevel_3,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->loclabel_3 = $request->loclevel_3;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->loclabel_4) != $request->loclevel_4){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'loclabel 4',
                      'old_value'=>$clnt_upd->loclabel_4,
                      'new_value'=>$request->loclevel_4,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->loclabel_4 = $request->loclevel_4;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->loclabel_5) != $request->loclevel_5){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'loclabel 5',
                      'old_value'=>$clnt_upd->loclabel_5,
                      'new_value'=>$request->loclevel_5,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->loclabel_5 = $request->loclevel_5;
                
                array_push($data, $audit1);
            }

            if(trim($clnt_upd->loclabel_6) != $request->loclevel_6){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'loclabel 6',
                      'old_value'=>$clnt_upd->loclabel_6,
                      'new_value'=>$request->loclevel_6,
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );

                $clnt_upd->loclabel_6 = $request->loclevel_6;
                
                array_push($data, $audit1);
            }
            // End of locations update

            $clnt_upd->preferred_name=$request->input('joint_name');

            switch ($client_type) {

             case 'I':

             $full_name =trim($request->input('fname')).' '.trim($request->input('sname')).' '.trim($request->input('surname'));

            
                if(trim($clnt_upd->title) != trim($request->input('title'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'client',
                        'field_changed'=>'title',
                        'old_value'=>$clnt_upd->title,
                        'new_value'=>$request->input('title'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->title = $request->input('title');

                    array_push($data, $audit1);
                }

            if(trim($clnt_upd->first_name) != trim($request->input('fname'))){
                $audit1 = array(
                      'policy_no'=>$policy_no,
                      'endt_renewal_no'=>$endt_renewal_no,
                      'risk_item'=>$clnt_no,
                      'table_name'=>'Client',
                      'field_changed'=>'First Name',
                      'old_value'=>$clnt_upd->first_name,
                      'new_value'=>$request->input('fname'),
                      'ip_address'=>$request->ip(),
                      'date_changed'=>Carbon::now('EAT'),
                      'system_user'=>Auth::user()->user_name
                  );
                $clnt_upd->first_name = $request->input('fname');
                $clnt_upd->name = $full_name;

                    array_push($data, $audit1);
                }

                if(trim($clnt_upd->others) != trim($request->input('sname'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'Client',
                        'field_changed'=>'Other Names',
                        'old_value'=>$clnt_upd->others,
                        'new_value'=>$request->input('sname'),
                        'date_changed'=>Carbon::now('EAT'),
                        'ip_address'=>$request->ip(),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->others = $request->input('sname');
                    $clnt_upd->name = $full_name;

                    array_push($data, $audit1);
                }

            

                if(trim($clnt_upd->surname) != trim($request->input('surname'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'Client',
                        'field_changed'=>'Surname',
                        'old_value'=>$clnt_upd->surname,
                        'new_value'=>$request->input('surname'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->surname = $request->input('surname');
                    $clnt_upd->name = $full_name;

                    array_push($data, $audit1);
                }

            

                if(trim($clnt_upd->gender) != trim($request->input('gender'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'Client',
                        'field_changed'=>'Gender',
                        'old_value'=>$clnt_upd->gender,
                        'new_value'=>$request->input('gender'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->gender = $request->input('gender');
                    
                    array_push($data, $audit1);
                }


                if(trim($clnt_upd->id_number) != trim($request->input('identity_number'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'Client',
                        'field_changed'=>'ID Number',
                        'old_value'=>$clnt_upd->id_number,
                        'new_value'=>$request->input('identity_number'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->id_number = $request->input('identity_number');
                    
                    array_push($data, $audit1);
                }

                if(trim($clnt_upd->passport_number) != trim($request->input('passport'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'Client',
                        'field_changed'=>'Passport Number',
                        'old_value'=>$clnt_upd->passport_number,
                        'new_value'=>$request->input('passport'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->passport_number = $request->input('passport');
                    
                    array_push($data, $audit1);
                }

                if(trim($clnt_upd->dl_number) != trim($request->input('dl'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'Client',
                        'field_changed'=>'Driving Licence Number',
                        'old_value'=>$clnt_upd->dl_number,
                        'new_value'=>$request->input('dl'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->dl_number = $request->input('dl');
                    
                    array_push($data, $audit1);
                }

                
                if(trim($clnt_upd->card_number) != trim($request->input('card_number'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'Client',
                        'field_changed'=>'Card Number',
                        'old_value'=>$clnt_upd->card_number,
                        'new_value'=>$request->input('card_number'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->card_number = $request->input('card_number');
                    
                    array_push($data, $audit1);
                }

                if(trim($clnt_upd->marital_status) != trim($request->input('marital_status'))){
                    $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'marital_status',
                        'field_changed'=>'Marital Status',
                        'old_value'=>$clnt_upd->marital_status,
                        'new_value'=>$request->input('marital_status'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );

                    $clnt_upd->marital_status = $request->input('marital_status');
                    
                    array_push($data, $audit1);
                }


                 break;
             
             case 'C':
                $full_name = trim($request->input('corporate_name'));
                

                if(trim($clnt_upd->name) != trim($request->input('corporate_name'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$clnt_no,
                          'table_name'=>'Client',
                          'field_changed'=>'Corporate Name',
                          'old_value'=>$clnt_upd->name,
                          'new_value'=>$request->input('corporate_name'),
                          'ip_address'=>$request->ip(),
                          'date_changed'=>Carbon::now('EAT'),
                          'system_user'=>Auth::user()->user_name
                      );

                    $clnt_upd->name = $request->input('corporate_name');
                    
                    array_push($data, $audit1);
                }


                if(trim($clnt_upd->incorporation_cert) != trim($request->input('incorporation_cert'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$clnt_no,
                          'table_name'=>'Client',
                          'field_changed'=>'Incorporation Cert No',
                          'old_value'=>$clnt_upd->incorporation_cert,
                          'new_value'=>$request->input('incorporation_cert'),
                          'ip_address'=>$request->ip(),
                          'date_changed'=>Carbon::now('EAT'),
                          'system_user'=>Auth::user()->user_name
                      );

                    $clnt_upd->incorporation_cert = $request->input('incorporation_cert');
                    
                    array_push($data, $audit1);
                }
                

                // edit risk profile details
                

                if(trim($riskprofile_upd->payment_mode_id) != trim($request->input('payment_mode'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$clnt_no,
                          'table_name'=>'client_risk_profiles',
                          'field_changed'=>'Payment Mode',
                          'old_value'=>$riskprofile_upd->payment_mode_id,
                          'new_value'=>$request->input('payment_mode'),
                          'ip_address'=>$request->ip(),
                          'date_changed'=>Carbon::now('EAT'),
                          'system_user'=>Auth::user()->user_name
                      );

                    $riskprofile_upd->payment_mode_id = $request->input('payment_mode');
                    
                    array_push($data, $audit1);
                }

                if(trim($riskprofile_upd->government_owned) != trim($request->input('government_owned'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$clnt_no,
                          'table_name'=>'client_risk_profiles',
                          'field_changed'=>'Government owned',
                          'old_value'=>$riskprofile_upd->government_owned,
                          'new_value'=>$request->input('government_owned'),
                          'ip_address'=>$request->ip(),
                          'date_changed'=>Carbon::now('EAT'),
                          'system_user'=>Auth::user()->user_name
                      );

                    $riskprofile_upd->government_owned = $request->input('government_owned');
                    
                    array_push($data, $audit1);
                }

                if(trim($riskprofile_upd->supervisory_regulated) != trim($request->input('supervisory_regulated'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$clnt_no,
                          'table_name'=>'client_risk_profiles',
                          'field_changed'=>'Supervisory regulated',
                          'old_value'=>$riskprofile_upd->supervisory_regulated,
                          'new_value'=>$request->input('supervisory_regulated'),
                          'ip_address'=>$request->ip(),
                          'date_changed'=>Carbon::now('EAT'),
                          'system_user'=>Auth::user()->user_name
                      );

                    $riskprofile_upd->supervisory_regulated = $request->input('supervisory_regulated');
                    
                    array_push($data, $audit1);
                }

                if(trim($riskprofile_upd->supervisory_body) != trim($request->input('supervisory_body'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$clnt_no,
                          'table_name'=>'client_risk_profiles',
                          'field_changed'=>'Supervisory Body',
                          'old_value'=>$riskprofile_upd->supervisory_body,
                          'new_value'=>$request->input('supervisory_body'),
                          'ip_address'=>$request->ip(),
                          'date_changed'=>Carbon::now('EAT'),
                          'system_user'=>Auth::user()->user_name
                      );

                    $riskprofile_upd->supervisory_body = $request->input('supervisory_body');
                    
                    array_push($data, $audit1);
                }

                if(trim($riskprofile_upd->organisation_license_revoked) != trim($request->input('organisation_license_revoked'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$clnt_no,
                          'table_name'=>'client_risk_profiles',
                          'field_changed'=>'Organisation License Revoked',
                          'old_value'=>$riskprofile_upd->organisation_license_revoked,
                          'new_value'=>$request->input('organisation_license_revoked'),
                          'ip_address'=>$request->ip(),
                          'date_changed'=>Carbon::now('EAT'),
                          'system_user'=>Auth::user()->user_name
                      );

                    $riskprofile_upd->organisation_license_revoked = $request->input('organisation_license_revoked');
                    
                    array_push($data, $audit1);
                }

                
                if(trim($riskprofile_upd->license_revoked_details) != trim($request->input('license_revoked_details'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$clnt_no,
                          'table_name'=>'client_risk_profiles',
                          'field_changed'=>'Organisation License Revoked Details',
                          'old_value'=>$riskprofile_upd->license_revoked_details,
                          'new_value'=>$request->input('license_revoked_details'),
                          'ip_address'=>$request->ip(),
                          'date_changed'=>Carbon::now('EAT'),
                          'system_user'=>Auth::user()->user_name
                      );

                    $riskprofile_upd->license_revoked_details = $request->input('license_revoked_details');
                    
                    array_push($data, $audit1);
                }

                if(trim($riskprofile_upd->prior_dealings_with_us) != trim($request->input('prior_dealings_with_us'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$clnt_no,
                          'table_name'=>'client_risk_profiles',
                          'field_changed'=>'Prior dealings with us',
                          'old_value'=>$riskprofile_upd->prior_dealings_with_us,
                          'new_value'=>$request->input('prior_dealings_with_us'),
                          'ip_address'=>$request->ip(),
                          'date_changed'=>Carbon::now('EAT'),
                          'system_user'=>Auth::user()->user_name
                      );

                    $riskprofile_upd->prior_dealings_with_us = $request->input('prior_dealings_with_us');
                    
                    array_push($data, $audit1);
                }

                if(trim($riskprofile_upd->dealings_details_with_us) != trim($request->input('dealings_details_with_us'))){
                    $audit1 = array(
                          'policy_no'=>$policy_no,
                          'endt_renewal_no'=>$endt_renewal_no,
                          'risk_item'=>$clnt_no,
                          'table_name'=>'client_risk_profiles',
                          'field_changed'=>'Prior dealings details with us',
                          'old_value'=>$riskprofile_upd->dealings_details_with_us,
                          'new_value'=>$request->input('dealings_details_with_us'),
                          'ip_address'=>$request->ip(),
                          'date_changed'=>Carbon::now('EAT'),
                          'system_user'=>Auth::user()->user_name
                      );

                    $riskprofile_upd->dealings_details_with_us = $request->input('dealings_details_with_us');
                    
                    array_push($data, $audit1);
                }
                 
                     
            break;
            }
        $clientbanks = PartnerBank::where('partner_number',$clnt_no)->get();
        $clnt_updated= Client::where('client_number',$clnt_no)->first();
        
        // Updates client details on relevant tables once the client data has been updated 
        $proceed_to_update = false;

        if(count($clientbanks) > 0){
            if($request->input('bank') != null){
                foreach($clientbanks as $clientbank){
                    if((trim($clientbank->bank_code) == trim($old_bank_code)) and 
                    (trim($clientbank->branch_code) == trim($old_branch_code)) and 
                    (trim($clientbank->bank_account_no) == trim($old_bank_account_no))){

                        $clientbank = PartnerBank::where('partner_number',$request->client_number)->update([
                            'bank_code' => $request->input('bank'),
                            'branch_code' => $request->input('bank_branch'),
                            'bank_account_no' => $request->input('bank_account'),
                            'bank_account' => $request->input('bank_account'),
                            'bank_account_name' => $clnt_updated->name,
                        ]);

                    }
                }

            }

        }else{
            if($request->input('bank') != null){
                $clientbank = new PartnerBank;
                $clientbank->partner_number = $clnt_no;
                $clientbank->item_no = count($clientbanks) + 1;
                $clientbank->bank_code = $request->input('bank');
                $clientbank->branch_code = $request->input('bank_branch');
                $clientbank->bank_account_name = $clnt_updated->name;
                $clientbank->bank_account_no = $request->input('bank_account');
                $clientbank->bank_account = $request->input('bank_account');
                $clientbank->in_use = 'Y';
                $clientbank->holder = 'CLIENT';
                $clientbank->save();
            }
        }

        if($proceed_to_update == true){
            DB::table('risk_audit_trail')->insert($data);

            $pol_upd = Polmaster::where('policy_no',$policy_no)->update([
                'name' => $full_name
            ]);
            $polend_upd = Polmasterend::where('endorse_no',$endt_renewal_no)->update([
                'name' => $full_name
            ]);
            $dcon_upd = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->update([
                'name' => $full_name
            ]);
            $polsect_upd = Polsect::where('endt_renewal_no',$endt_renewal_no)->update([
                'name' => $full_name
            ]);
        }

        if($nil_endt == 'Y'){

             ###log changes to the new logs table 
             $changed_data = $clnt_upd->getAttributes();
             $process_slug = 'client-addition';
             $activity_slug = 'update';
             $unique_item = $clnt_no;
             $old_data = $previous_data;
             $ip =$request->ip();
 
             log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

            if($clnt_upd->save()){

                DB::table('risk_audit_trail')->insert($data);

                if($client_type == 'C' && $riskprofile_upd->save()){
                    $proceed_to_update = true;
                }
                if($client_type == 'I'){
                    $proceed_to_update = true;
                }
                
               

                $data = [
                    'status' => 2,
                    'message' => 'Client number '.$clnt_no.' updated successfully',
                    'endt_renewal_no' => $endt_renewal_no
                    
                ];


                


                return response()->json(['data' => $data]);


                // return redirect()->route('policy_functions', ['endt_renewal_no' => $endt_renewal_no]);
            }else{
                $data = [
                    'status' => 0,
                    'message' => 'Client number '.$clnt_no.' update failed',
                
                ];
                return response()->json(['data' => $data]);
            }

            

        }else{
            ###log changes to the new logs table 
            $changed_data = $clnt_upd->getAttributes();
            $process_slug = 'client-addition';
            $activity_slug = 'update';
            $unique_item = $clnt_no;
            $old_data = $previous_data;
            $ip =$request->ip();

            log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);
          
            if($clnt_upd->save()){
                
                if($client_type == 'C' && $riskprofile_upd->save() ){
                    $proceed_to_update = true;
                }

                if($client_type == 'I'){
                    $proceed_to_update = true;
                }
                

              
                
                $data = [
                    'status' => 1,
                    'message' => 'Client number '.$clnt_no.' updated successfully',
                    
                ];
                return response()->json(['data' => $data]);

              
            
          
            }else{
            $data = [
                'status' => 0,
                'message' => 'Client number '.$clnt_no.' update failed',
                
            ];
            return response()->json(['data' => $data]);
          }

          
        }

         
         

     }


    public function verify_id_no(Request $request){

        $id_no = $request->get('id');

        $clnt_id_exist=Client::where('id_number',$id_no)->count();


        $valid=0;

        if($clnt_id_exist>0){

          $client_rec_with_id=Client::where('id_number',$id_no)->get();

          foreach ($client_rec_with_id as $key => $clnt_rec) {

              $valid=$valid+1;
              $name = $clnt_rec['name']; 
 
          }



        }

        if($valid>0){
                 //not valid

          //$result=array('valid' =>0);
          $result=array('valid' =>0, 'name'=>$name);

        }else{
                 // valid
          $result=array('valid' =>1);

        }

        echo json_encode($result);

   }


    public function verify_passport_no(Request $request){

            $passport_no = $request->get('passport');

            $clnt_id_exist=Client::where('passport_number',$passport_no)->count();


            $valid=0;

            if($clnt_id_exist>0){

            $client_rec_with_id=Client::where('passport_number',$passport_no)->get();

            foreach ($client_rec_with_id as $key => $clnt_rec) {

                $valid=$valid+1;
                $name = $clnt_rec['name']; 
    
            }



            }

            if($valid>0){
                    //not valid

            $result=array('valid' =>0, 'name'=>$name);

            }else{
                    // valid
            $result=array('valid' =>1);

            }

            echo json_encode($result);

    }

    public function verify_driving_licence(Request $request){

        $dl_no = $request->get('dl');

        $clnt_id_exist=Client::where('dl_number',$dl_no)->count();


        $valid=0;

        if($clnt_id_exist>0){

            $client_rec_with_id=Client::where('dl_number',$dl_no)->get();

            foreach ($client_rec_with_id as $key => $clnt_rec) {

                $valid=$valid+1;
                $name = $clnt_rec['name']; 

            }



        }

        if($valid>0){
                    //not valid

            $result=array('valid' =>0, 'name'=>$name);

        }else{
                    // valid
            $result=array('valid' =>1);

        }

        echo json_encode($result);

    }



   public function verify_pin_no(Request $request){
    
        $pin_no = $request->get('pin');
       
        
        $clnt_id_exist=Client::where(trim('pin_number'),$pin_no)->count();
 
        if($clnt_id_exist > 0){

          $client_rec_with_id=Client::where(trim('pin_number'),$pin_no)
                                    ->get();
            $clientRecsWithId = Client::where(trim('pin_number'),$pin_no)
                       ->pluck('name')
                       ->toArray();


        }
        
        if(is_countable($clientRecsWithId) && count($clientRecsWithId) > 0){
            
          $result=array('valid' => 0, 'names'=>$clientRecsWithId);

        }else{
          $result=array('valid' => 1);

        }

        return json_encode($result);

   }


   public function verify_agt_pin_no(Request $request){

        $pin_no = trim($request->pin);
        $agent = trim($request->agent);
        $branch = trim($request->branch);
        $acc_type = trim($request->acc_type);

        // $agt_id_exist=Agmnf::where(trim('pin_number'),$pin_no)->count();
        // $agt_id_exist=Agmnf::where(trim('pin_number'),$pin_no)
        //                     // ->where(trim('agent'),$agent)
        //                     // ->where(trim('branch'),$branch)
        //                     ->where(trim('acc_type'),$acc_type)
        //                     ->first();
        $intermediaryParams = new IntermediaryQueryParams([
            'conditions' => function ($query) use ($pin_no, $acc_type) {
                return $query->where('pin_number', trim($pin_no))
                             ->where('acc_type', trim($acc_type));
            },
            'additionalFields' => ['pin_number', 'acc_type']
        ]);
        
        $agt_id_exist = IntermediaryQueryService::getAllActiveintermediary($intermediaryParams)->first();


        return response()->json(['status'=>1, 'agt_id_exist'=>$agt_id_exist]);


        // $valid=0;

        // if($agt_id_exist>0){

        //   $agt_rec_with_id=Agmnf::where(trim('pin_number'),$pin_no)->get();

        //   foreach ($agt_rec_with_id as $key => $agt_rec) {

        //       $valid=$valid+1;
        //       $name = $agt_rec['name']; 
 
        //   }



        // }

        // if($valid>0){
        //          //not valid

        //   $result=array('valid' =>0, 'name'=>$name);

        // }else{
        //          // valid
        //   $result=array('valid' =>1);

        // }

        // echo json_encode($result);

   }


   public function verify_incorporation_cert(Request $request){

    $incorporation_cert = $request->get('incorporation_cert');

    $clnt_id_exist=Client::where('incorporation_cert',$incorporation_cert)->count();


    $valid=0;

    if($clnt_id_exist>0){

        $client_rec_with_id=Client::where('incorporation_cert',$incorporation_cert)->get();

        foreach ($client_rec_with_id as $key => $clnt_rec) {

            $valid=$valid+1;
            $name = $clnt_rec['name']; 

        }



    }

    if($valid>0){
                //not valid

        $result=array('valid' =>0, 'name'=>$name);

    }else{
                // valid
        $result=array('valid' =>1);

    }

    echo json_encode($result);

    }


    public function fetch_occupation(Request $request){

        $categ = $request->get('type');
        $occup = Occupation::where('category',$categ)->get();
        

        echo json_encode($occup);

   }


    public function fetch_districts(Request $request){
        
        $region = $request->get('region');
        $districts = District::where('region',$region)->get();

        return $districts;

   }

   public function fetch_towns(Request $request){
        
    $county = $request->get('county');
    $towns = Town::where('county_id',$county)->get();

    return $towns;

}

    public function client(StoreClient $request){
        DB::beginTransaction();

        try{
            $validated = $request->validated();
            $type = 'C';
            $clnt_no=$this->generate_client_number($type);
            $country_id = $request->input('country_code');
            $country_code = Country::where('id',$country_id)->value('country_code');
            $alpha3_country_code = Country::where('id',$country_id)->value('abbrev');
            $selfRelationCode = Beneficiary_relation::selfRelationCode;
            $joint_account = $request->input('joint_account');
           

            switch ($request->input('client_type'))
            {
                case 'I':
                    
                        $client=new Client;
                        $client->client_number=$clnt_no;
                        $client->title=$joint_account === 'Y' ? '' : $request->input('title');
                        $client->address1=$request->input('postal_address');
                        $client->address2=$request->input('postal_code');
                        $client->address3=$request->input('physical_address');
                        //$client->vrn=$request->input('vrn');
                        $client->pin_number=$request->input('pin_number');
                        $client->mobile_no=$request->input('mobile_no');
                        $client->client_classification=$request->input('client_classification');
                        $client->telephone=$request->input('telephone');
                        $client->occupation=$request->input('occupation');
                        $client->country_code=$country_code;
                        $client->alpha3_country_code=$alpha3_country_code;
                        $client->country_id= $country_id;
                        $client->first_name=$request->input('fname');
                        $client->others=$request->input('sname');
                        $client->surname=$request->input('surname');
                        $client->name=$request->input('fname').' '.$request->input('sname').' '.$request->input('surname');
                        $client->joint_account=$joint_account;
                        $client->preferred_name=$joint_account === 'Y' ? $request->input('joint_name'): '';
                        $client->relationship=$joint_account === 'Y' ? $request->input('relation') : '';
                        $client->relationship_desc=$joint_account === 'Y' ? $request->input('relationship_desc') : '';
                        $client->group_code=$clnt_no;
                        $client->dola=Carbon::today();
                        $client->client_type=$request->input('client_type');
                        $client->gender=$request->input('gender');
                        $client->e_mail=$request->input('e_mail');
                        $client->alt_email=$request->input('alt_email');
                        $client->dob=$request->input('dob');
                        $client->vat_exempt=$request->input('vat_exempt');
                        $client->approved_by=$request->input('approved_by');
                        $client->contact_firstname=$request->input('contact_firstname');
                        $client->contact_surname=$request->input('contact_surname');
                        $client->contact_othername=$request->input('contact_othername');
                        $client->contact_telephone=$request->input('contact_telephone');
                        $client->contact_position=$request->input('contact_position');

                        $client->sanction_screening = $request->input('sanction_screening');
                        $client->source_of_funds=$request->input('source_of_funds');
                        $client->expected_annual_income=$request->input('annual_income');
                        $client->buyer_type=$request->input('buyer_type');
                        $client->nationality=$request->input('nationality');
                        $client->political_exposed = $request->input('political_exposed');
                        $client->pep_classification = $request->input('pep_classification');
                        $client->aml_classification = $request->input('aml_classification');
                        $client->card_number =$request->input('card_number');
                        $client->marital_status =$request->input('marital_status');
                        $client->is_direct = $request->input('direct');
                        $client->branch = $request->input('c_branch');
                        $client->agent = $request->input('agent');

                        // Location fields
                        $client->location_1   = $request->input('level_1');
                        $client->location_2   = $request->input('level_2');
                        $client->location_3 = $request->input('level_3');
                        $client->location_4  = $request->input('level_4');
                        $client->location_5  = $request->input('level_5');
                        $client->location_6   = $request->input('level_6');

                        // Location labels  
                        $client->loclabel_1   = $request->input('loclevel_1');
                        $client->loclabel_2   = $request->input('loclevel_2');
                        $client->loclabel_3 = $request->input('loclevel_3');
                        $client->loclabel_4  = $request->input('loclevel_4');
                        $client->loclabel_5  = $request->input('loclevel_5');
                        $client->loclabel_6   = $request->input('loclevel_6');

                        

                        $client->user_str=Auth::user()->user_name;
                        $client->identity_type=$request->input('id_type');
                        $client->id_number=$request->input('identity_number');
                        $client->pass_expiry_date=$request->input('pass_expiry_date');
                        $client->bank_account_no = $request->moreaccno[0];
                        $client->bank_code = $request->morebank[0];
                        $client->branch_code = $request->morebranch[0];

                        // Other details fields
                        $client->consent_mkt = $request->send_mkt_material;
                        $client->consent_internal = $request->share_kyc_internally;
                        $client->consent_out_ke = $request->share_kyc_externally;
                        $client->consent_child = $request->share_child_kyc;
                        $client->consent_3rd_party = $request->kyc_details_from3rdparty;
                        $client->consent_analysis  = $request->use_kyc_for_research;
                        $client->d365_fo_integration = $request->d365_fo_integration;
                        $client->company_to_integrate_fo = $request->company_to_integrate_fo;
                        $client->d365_crm_integration = $request->d365_crm_integration;
                        $client->customer_group  =  $request->customer_group;

                        $cnt_id = Client::where('id_number',$request->input('identity_number'))
                                        ->whereNotNull('id_number')->count();
                        
                        if($cnt_id>0){

                            return response()->json(['status' => -1, 'msg' => 'ID Number Already taken by another  Client ']);
                            
                        }
                        else
                        {
                            
                            $morebanks = $request->morebank;
                            foreach($morebanks as $i => $morebank){
                                if($request->morebank[$i] != null){
                                    $clientbank = new PartnerBank;
                                    $clientbank->partner_number = $clnt_no;
                                    $clientbank->item_no = $i + 1;
                                    $clientbank->bank_code = $morebank;
                                    $clientbank->branch_code = $request->morebranch[$i];
                                    // $clientbank->bank_account_name = $request->moreaccname[$i];
                                    $clientbank->bank_account_name = $client->name;
                                    if($i == 0){
                                        $clientbank -> primary_account = 'Y';
                                    }else{
                                        $clientbank -> primary_account = 'N';
                                    }
                                    $clientbank->bank_account_no = $request->moreaccno[$i];
                                    $clientbank->bank_account = $request->moreaccno[$i];
                                    $clientbank->currency_type = $request->morecurrency_type[$i];
                                    $clientbank->payment_mode = $request->morepayment_mode[$i];
                                    $clientbank->banking_phone_no = $request->morebanking_phone[$i];
                                    $clientbank->in_use = 'Y';
                                    $clientbank->holder = 'CLIENT';
                                    $clientbank->save();
                                }
                            } 
                            

                            //save beneficiaries

                            $beneficiaries = $request->moreben_firstname;
                            $relationship = $request->morerelation_type;
                           
                            // When the option of self in contact type is set 
                            if($relationship[0] == $selfRelationCode){
                                $beneficiary_details = new Beneficiary();
                                $beneficiary_details->client_number =  $clnt_no;
                                $beneficiary_details->first_name = $request->input('fname');
                                $beneficiary_details->last_name =  $request->input('surname');
                                $beneficiary_details->other_name = $request->input('sname');
                                $beneficiary_details->full_name = $joint_account === $request->input('joint_name') ? '' : $request->input('fname').' '.$request->input('sname').' '.$request->input('surname');
                                $beneficiary_details->positions = $relationship[0];
                                $beneficiary_details->org_role = null;
                                $beneficiary_details->telephone = $request->input('mobile_no');
                                $beneficiary_details->region = $request->input('region');
                                $beneficiary_details->district=$request->input('district');
                                $beneficiary_details->e_mail=$request->input('e_mail');
                                $beneficiary_details->phy_loc=$request->input('postal_address');
                                $beneficiary_details->political_exposed = $request->input('political_exposed');
                                $beneficiary_details->aml_classification = $request->input('aml_classification');
                                $beneficiary_details->contact_type = 'C';
                                $beneficiary_details->holder = 'CLIENT';

                                // Location fields
                                $beneficiary_details->location_1   = $request->input('level_1');
                                $beneficiary_details->location_2   = $request->input('level_2');
                                $beneficiary_details->location_3 = $request->input('level_3');
                                $beneficiary_details->location_4  = $request->input('level_4');
                                $beneficiary_details->location_5  = $request->input('level_5');
                                $beneficiary_details->location_6   = $request->input('level_6');

                                // Location labels  
                                $beneficiary_details->loclabel_1   = $request->input('loclevel_1');
                                $beneficiary_details->loclabel_2   = $request->input('loclevel_2');
                                $beneficiary_details->loclabel_3 = $request->input('loclevel_3');
                                $beneficiary_details->loclabel_4  = $request->input('loclevel_4');
                                $beneficiary_details->loclabel_5  = $request->input('loclevel_5');
                                $beneficiary_details->loclabel_6   = $request->input('loclevel_6');
                                
                                $beneficiary_details->save();
                            }else{
                            
                                foreach ($beneficiaries as $key => $moreben_firstname) {
                                
                                    if($request->moreben_firstname[$key] != null){
                                            $beneficiary_details = new Beneficiary();
                                            $beneficiary_details->client_number =  $clnt_no;
                                            $beneficiary_details->first_name = $request->moreben_firstname[$key];
                                            $beneficiary_details->last_name =  $request->moreben_lastname[$key];
                                            $beneficiary_details->other_name = $request->moreben_othernames[$key];
                                            $beneficiary_details->full_name =$request->moreben_firstname[$key].' '.$request->moreben_lastname[$key].' '.$request->moreben_othernames[$key];
                                            $beneficiary_details->positions =$request->morerelation_type[$key];
                                            $beneficiary_details->org_role = $request->morerelation_contactposition[$key];
                                            $beneficiary_details->telephone = $request->moreben_telephone[$key];
                                            $beneficiary_details->region = $request->moreben_region[$key];
                                            $beneficiary_details->district=$request->moreben_district[$key];
                                            $beneficiary_details->phy_loc=$request->more_address[$key];
                                            $beneficiary_details->e_mail=$request->moreben_email[$key];
                                            $beneficiary_details->political_exposed = $request->moreben_politicalexposed[$key];
                                            $beneficiary_details->aml_classification = $request->moreben_amlclassification[$key];
                                            $beneficiary_details->contact_type = $request->morecontact_type[$key];
                                            $beneficiary_details->holder = 'CLIENT';

                                            // Location fields
                                            $beneficiary_details->location_1   = $request->contact_level_1[$key];
                                            $beneficiary_details->location_2   = $request->contact_level_2[$key];
                                            $beneficiary_details->location_3 = $request->contact_level_3[$key];
                                            $beneficiary_details->location_4  = $request->contact_level_4[$key];
                                            $beneficiary_details->location_5  = $request->contact_level_5[$key];
                                            $beneficiary_details->location_6   = $request->contact_level_6[$key];

                                            // Location labels  
                                            $beneficiary_details->loclabel_1   = $request->input('loclevel_1');
                                            $beneficiary_details->loclabel_2   = $request->input('loclevel_2');
                                            $beneficiary_details->loclabel_3 = $request->input('loclevel_3');
                                            $beneficiary_details->loclabel_4  = $request->input('loclevel_4');
                                            $beneficiary_details->loclabel_5  = $request->input('loclevel_5');
                                            $beneficiary_details->loclabel_6   = $request->input('loclevel_6');
                                            
                                            $beneficiary_details->save();
                                    }
                                    
                                        
                                }       
                            }

                            //save Hobbies
                            foreach ($request->hobbies as $hobby) {
                                $latestId = ClientHobby::max('id');
                                $newId = $latestId ? $latestId + 1 : 1;
                                
                                $newHobby=ClientHobby::create([
                                    'id' =>$newId,
                                    'client_number' => $clnt_no,
                                    'hobbies' => $hobby,
                                    'created_by' =>trim(auth()->user()->user_name),
                                    'updated_by'=>trim(auth()->user()->user_name),
                                ]);
            
                            }
                        }
                            $client->save();
                            $this->updateAllocated('C',$clnt_no);
                            
                            
                            $data=$this->generateClientPartnerNo($request, $clnt_no);
                            if($data['status']==2){
    
                                return response()->json(['data' => $data]);
    
                            }

                            $notificationData = ['client_number'  =>  $client->client_number];
                            DispatchNotificationEvent::dispatch($slug = 'client-onboarding',$notificationData);
                    break;
                
                case 'C':
                    
                        $client=new Client;
                        $client->client_number=$clnt_no;
                        $client->occupation=$request->input('occupation');
                        $client->address1=$request->input('postal_address');
                        $client->address2=$request->input('postal_code');
                        $client->address3=$request->input('physical_address');
                        //$client->vrn=$request->input('vrn');
                        $client->pin_number=$request->input('pin_number');
                        $client->mobile_no=$request->input('mobile_no');
                        $client->telephone=$request->input('telephone');
                        $client->name=$request->input('corporate_name');
                        $client->joint_account=$joint_account;
                        $client->preferred_name=$joint_account === 'Y' ? $request->input('joint_name'): '';
                        $client->relationship=$joint_account === 'Y' ? $request->input('relation') : '';
                        $client->relationship_desc=$joint_account === 'Y' ? $request->input('relationship_desc') : '';
                        $client->approved_by=$request->input('approved_by');
                        $client->source_of_funds=$request->input('source_of_funds');
                        $client->expected_annual_income=$request->input('annual_income');
                        $client->buyer_type=$request->input('buyer_type');
                        $client->country_code=$country_code;
                        $client->alpha3_country_code=$alpha3_country_code;
                        $client->country_id= $country_id;
                        $client->nationality=$request->input('nationality');
                        $client->sanction_screening = $request->input('sanction_screening');
                        $client->political_exposed =$request->input('political_exposed');
                        $client->pep_classification =$request->input('pep_classification');
                        $client->aml_classification = $request->input('aml_classification');
                        $client->lifecycle_status=$request->input('lifecycle_status');
                        $client->group_code=$clnt_no;
                        $client->dola=Carbon::today();
                        $client->client_type=$request->input('client_type');
                        $client->e_mail=$request->input('e_mail');
                        $client->alt_email=$request->input('alt_email');
                        $client->dob=$request->input('dob');
                        $client->incorporation_cert=$request->input('incorporation_cert');
                        $client->identity_type='C';
                        $client->id_number=$request->input('incorporation_cert');
                        $client->is_direct = $request->input('direct');
                        $client->branch = $request->input('c_branch');
                        $client->agent = $request->input('agent');


                        $client->bank_account_no = $request->moreaccno[0];
                        $client->bank_code = $request->morebank[0];
                        $client->branch_code = $request->morebranch[0];
                        $client->vat_exempt=$request->input('vat_exempt');

                        // Location fields
                        $client->location_1   = $request->input('level_1');
                        $client->location_2   = $request->input('level_2');
                        $client->location_3 = $request->input('level_3');
                        $client->location_4  = $request->input('level_4');
                        $client->location_5  = $request->input('level_5');
                        $client->location_6   = $request->input('level_6');

                        // Location labels  
                        $client->loclabel_1   = $request->input('loclevel_1');
                        $client->loclabel_2   = $request->input('loclevel_2');
                        $client->loclabel_3 = $request->input('loclevel_3');
                        $client->loclabel_4  = $request->input('loclevel_4');
                        $client->loclabel_5  = $request->input('loclevel_5');
                        $client->loclabel_6   = $request->input('loclevel_6');

                        
                        $client->contact_firstname=$request->input('contact_firstname');
                        $client->contact_surname=$request->input('contact_surname');
                        $client->contact_othername=$request->input('contact_othername');
                        $client->contact_telephone=$request->input('contact_telephone');
                        $client->contact_position=$request->input('contact_position');
                        $client->client_classification=$request->input('client_classification');


                        // Other details fields
                        $client->consent_mkt = $request->send_mkt_material;
                        $client->consent_internal = $request->share_kyc_internally;
                        $client->consent_out_ke = $request->share_kyc_externally;
                        $client->consent_child = $request->share_child_kyc;
                        $client->consent_3rd_party = $request->kyc_details_from3rdparty;
                        $client->consent_analysis  = $request->use_kyc_for_research;
                        $client->d365_fo_integration = $request->d365_fo_integration;
                        $client->company_to_integrate_fo = $request->company_to_integrate_fo;
                        $client->d365_crm_integration = $request->d365_crm_integration;
                        $client->customer_group  =  $request->customer_group;
                        

                        $client->user_str=Auth::user()->user_name;
                        $pinno = Client::where('pin_number',$request->input('pin_number'))
                                        ->whereNotNull('pin_number')->count();

                        if($pinno>0)
                        {
                            // Session::flash('error','TIN Already in Use by Another Client');
                             return response()->json(['status' => -1, 'msg' => 'TIN Already in Use by Another Client']);
                        }
                        else
                        {
                            $morebanks = $request->morebank;

                            foreach($morebanks as $i => $morebank){
                                if($request->morebank[$i] != null){
                                    $clientbank = new PartnerBank;
                                    $clientbank->partner_number = $clnt_no;
                                    $clientbank->item_no = $i + 1;
                                    $clientbank->bank_code = $morebank;
                                    $clientbank->branch_code = $request->morebranch[$i];
                                    // $clientbank->bank_account_name = $request->moreaccname[$i];
                                    $clientbank->bank_account_name = $client->name;
                                    if($i == 0){
                                        $clientbank -> primary_account = 'Y';
                                    }else{
                                        $clientbank -> primary_account = 'N';
                                    }
                                    $clientbank->bank_account_no = $request->moreaccno[$i];
                                    $clientbank->bank_account = $request->moreaccno[$i];
                                    $clientbank->currency_type = $request->morecurrency_type[$i];
                                    $clientbank->in_use = 'Y';
                                    $clientbank->holder = 'CLIENT';
                                    
                                    $clientbank->save();
                                }
                            }

                            //save risk profile details
                            $risk_profile = new ClientRiskProfile();
                            $risk_profile->client_number = $clnt_no;
                            $risk_profile->payment_mode_id=$request->input('payment_mode');
                            $risk_profile->government_owned=$request->input('government_owned');
                            $risk_profile->supervisory_regulated=$request->input('supervisory_regulated');
                            $risk_profile->supervisory_body=$request->input('supervisory_body');
                            $risk_profile->organisation_license_revoked = $request->input('organisation_license_revoked');
                            $risk_profile->license_revoked_details = $request->input('license_revoked_details');
                            $risk_profile->prior_dealings_with_us = $request->input('prior_dealings_with_us');
                            $risk_profile->dealings_details_with_us = $request->input('dealings_details_with_us');
                            $risk_profile->save();
                            
                            
                            //save directors
                            // $directors = $request->moredir_firstname;
                            
                            // foreach ($directors as $key => $moredir_firstname) {
                               
                            //    if($request->moredir_firstname[$key] != null){
                            //         $director_details = new Beneficiary();
                            //         $director_details->client_number =  $clnt_no;
                            //         $director_details->first_name = $request->moredir_firstname[$key];
                            //         $director_details->last_name =  $request->moredir_lastname[$key];
                            //         $director_details->other_name = $request->moredir_othernames[$key];
                            //         $director_details->full_name = $request->moredir_firstname[$key].' '.$request->moredir_lastname[$key].' '.$request->moredir_othernames[$key];
                            //         $director_details->e_mail = $request->moredir_email[$key];
                            //         $director_details->id_number =$request->moredir_id[$key];
                            //         $director_details->telephone = $request->moredir_telephone[$key];
                            //         $director_details->number_of_shares = $request->moredir_shares[$key];
                            //         $director_details->political_exposed = $request->moredir_politicalexposed[$key];
                            //         $director_details->aml_classification = $request->moredir_amlclassification[$key];
                            //         $director_details->contact_type = $request->moredirector_type[$key];
                            //         $director_details->holder = 'CLIENT';

                                    
                            //         $director_details->save();
                                
                                    
                            //     }

                            
                            // }
                            $directorsClientTypes = $request->director_client_type;

                            foreach ($directorsClientTypes as $i => $clientType) {
                    
                                if ($clientType == 'I') {
                                    
                                    $directormember = new Beneficiary();
                                    $directormember->client_type = $clientType;
                                    $directormember->title = $request->director_title[$i];
                                    $directormember->first_name = $request->director_firstname[$i];
                                    $directormember->last_name = $request->director_lastname[$i];
                                    $directormember->other_name = $request->director_othernames[$i];
                                    $directormember->full_name = $request->director_firstname[$i] . ' ' . $request->director_lastname[$i] . ' ' . $request->director_othernames[$i];
                                    $directormember->gender = $request->director_gender[$i];
                                    $directormember->identity_type = $request->director_id_type[$i];
                                    $directormember->id_number = $request->director_identity_number[$i];
                                    
                                } elseif($clientType == 'C') {
                                    
                                    $directormember = new Beneficiary();
                                    $directormember->client_type = $clientType;
                                    $directormember->incorporation_cert = $request->director_incorporation_cert[$i];
                                    $directormember->full_name = $request->director_member_name[$i];                                    
                                }
                                $directormember->nationality = $request->director_nationality[$i];
                                $directormember->pin_number = $request->director_pin_number[$i];
                                $directormember->client_number =$clnt_no;
                                $directormember->dob = $request->director_dob[$i];
                                $directormember->country_code = $request->director_country_code[$i];
                                $directormember->telephone = $request->director_mobile_no[$i];
                                $directormember->e_mail = $request->director_email[$i];
                                $directormember->ubo = $request->director_ubo[$i];
                                $directormember->investment = $request->director_investment_type[$i];
                                $directormember->investment_value    = $request->director_investment_value[$i];
                                $directormember->political_exposed    = $request->moredir_politicalexposed[$i];
                                $directormember->aml_classification    = $request->moredir_amlclassification[$i];
                                $directormember->contact_type    = 'D';
                                $directormember->holder = 'CLIENT';
                                // dd($directormember);
                                
            
                                
                                $directormember->save();
                            }

                        
                            $beneficiaries = $request->moreben_firstname;
                            
                            foreach ($beneficiaries as $key => $moreben_firstname) {
                            
                               if($request->moreben_firstname[$key] != null){
                                    $beneficiary_details = new Beneficiary();
                                    $beneficiary_details->client_number =  $clnt_no;
                                    $beneficiary_details->first_name = $request->moreben_firstname[$key];
                                    $beneficiary_details->last_name =  $request->moreben_lastname[$key];
                                    $beneficiary_details->other_name = $request->moreben_othernames[$key];
                                    $beneficiary_details->full_name =$request->moreben_firstname[$key].' '.$request->moreben_lastname[$key].' '.$request->moreben_othernames[$key];
                                    $beneficiary_details->positions =$request->morerelation_type[$key];
                                    $beneficiary_details->org_role = $request->moreben_contactposition[$key];
                                    $beneficiary_details->telephone = $request->moreben_telephone[$key];
                                    $beneficiary_details->region = $request->moreben_region[$key];
                                    $beneficiary_details->district=$request->moreben_district[$key];
                                    $beneficiary_details->phy_loc=$request->more_address[$key];
                                    $beneficiary_details->political_exposed = $request->moreben_politicalexposed[$key];
                                    $beneficiary_details->aml_classification = $request->moreben_amlclassification[$key];
                                    $beneficiary_details->contact_type = $request->morecontact_type[$key];
                                    $beneficiary_details->e_mail = $request->moreben_email[$key];
                                    $beneficiary_details->holder = 'CLIENT';

                                    // Location fields
                                    $beneficiary_details->location_1   = $request->contact_level_1[$key];
                                    $beneficiary_details->location_2   = $request->contact_level_2[$key];
                                    $beneficiary_details->location_3 = $request->contact_level_3[$key];
                                    $beneficiary_details->location_4  = $request->contact_level_4[$key];
                                    $beneficiary_details->location_5  = $request->contact_level_5[$key];
                                    $beneficiary_details->location_6   = $request->contact_level_6[$key];

                                    // Location labels  
                                    $beneficiary_details->loclabel_1   = $request->input('loclevel_1');
                                    $beneficiary_details->loclabel_2   = $request->input('loclevel_2');
                                    $beneficiary_details->loclabel_3 = $request->input('loclevel_3');
                                    $beneficiary_details->loclabel_4  = $request->input('loclevel_4');
                                    $beneficiary_details->loclabel_5  = $request->input('loclevel_5');
                                    $beneficiary_details->loclabel_6   = $request->input('loclevel_6');
                                    

                                    
                                    $beneficiary_details->save();
                               }
                                
                              }


                           
                        }
                        // dd($client);
                        $client->save(); //$clnt_no, name,company_name
                        $this->updateAllocated('C',$clnt_no);  
                         
                        $data=$this->generateClientPartnerNo($request, $clnt_no);
                        if($data['status']==2){

                            return response()->json(['data' => $data]);

                        }

                        $notificationData = ['client_number'  =>  $client->client_number];
                        DispatchNotificationEvent::dispatch($slug = 'client-onboarding',$notificationData);
                       
                    break;
            }
            
            $client->preferred_name=$joint_account === 'Y' ? $request->input('joint_name'): '';

            /* //set session variable to blink user created div
            Session::flash('success','Client number '.$clnt_no.' added sucessfully');*/

            //  workflowupdate_start_no_link(221,$ref);
            // workflowupdate_subsequent($ref);

            $cnt = $clnt_no;

            
            if($request->joint_account =='Y'){
                //Store joint member details
                $jointClientTypes = $request->joint_client_type;
                $latestId = JointMember::max('id');
                $newId = $latestId ? $latestId + 1 : 1;
                $joint_client_count=0;
                foreach ($jointClientTypes as $i => $clientType) {
                    
                    if ($clientType == 'I') {
                        
                        $jointMember = new JointMember();
                        $jointMember->id=$newId+$joint_client_count;
                        $jointMember->client_type = $clientType;
                        $jointMember->title = $request->joint_title[$i];
                        $jointMember->first_name = $request->joint_firstname[$i];
                        $jointMember->last_name = $request->joint_lastname[$i];
                        $jointMember->other_names = $request->joint_othernames[$i];
                        $jointMember->gender = $request->joint_gender[$i];
                        $jointMember->identity_type = $request->joint_id_type[$i];
                        $jointMember->identity_number = $request->joint_identity_number[$i];
                        
                    } elseif($clientType == 'C') {
                        
                        $jointMember = new JointMember();
                        $jointMember->id=$newId+$joint_client_count;
                        $jointMember->client_type = $clientType;
                        $jointMember->incorporation_cert = $request->joint_incorporation_cert[$i];
                        
                    }
                    $jointMember->name = $request->joint_member_name[$i];
                    $jointMember->nationality = $request->joint_nationality[$i];
                    $jointMember->pin_number = $request->joint_pin_number[$i];
                    $jointMember->client_number =$clnt_no;
                    $jointMember->dob = $request->joint_dob[$i];
                    $jointMember->country_code = $request->joint_country_code[$i];
                    $jointMember->mobile_no = $request->joint_mobile_no[$i];
                    $jointMember->email = $request->joint_email[$i];
                    

                    
                    $jointMember->save();
                    $joint_client_count++;
                }
            }

            workflowupdate($cnt,361,20);
            $client_details=Client::where('client_number',$clnt_no)
                            ->get(['client_number','name','mobile_no','occupation','e_mail','blacklist_flag','edms_cabinet_created']);

            $no_of_policies=Polmaster::where('client_number',$this->client_number)->count();
            $process_id = 10;
            $dept_id = 0;


            //set session variable to blink user created div
            //Session::flash('success','Client number '.$clnt_no.' sent for approval');

            //return redirect()->route('clnt_home');
            
            //return redirect()->route('new_clnt');
            //return redirect('/home');
            // return Redirect::back()->withErrors(['current_password' => $message])->withInput($request->except('password'));
            if($cnt_id > 0 || $cnt_passport > 0){

                return response()->json(['status' => -1 ]);
            }else{
                //return redirect('/home');
                // Session::flash('success','Client number '.$clnt_no.' added successfully');
                DB::commit();

                $data = [
                    'status' => 1,
                    'client_details' => $client_details,
                    'no_of_pol' => $no_of_policies,
                    'process_id' =>$process_id,
                    'dept_id'=> $dept_id
                ];

                return response()->json(['data' => $data]);
               
            }   
        }
        catch(\Throwable $e){
            DB::rollBack();

            $error_msg = json_encode($e->getMessage());
            $reference = $request->client_type == 'I' ? $request->identity_type : $request->incorporation_cert;
            $route_name = Route::getCurrentRoute()->getActionName();
            log_error_details($route_name, $error_msg, $reference);
            
            Session::flash('error', 'Failed to save client');
            
            return response()->json([
                'data' => [
                    'status' => 0,
                    'error' => $e->getMessage(), 
                ]
            ]);
        }

    }

	public function editclientaccount(Request $request)
	{        
		$client_old = Client::where('client_number',$request->client_number)->first();
		$clientbank_old = PartnerBank::where('partner_number',$request->client_number)->where('item_no', $request->item_no)->first();
		$clientbank_count = PartnerBank::where('partner_number',$request->client_number)->count();

        $nil_endt=$request->input('bnk_nil_endt');

        if (trim($nil_endt)=='Y') {
            $policy_no = $request->input('bnk_policy_no');
            $endt_renewal_no = $request->input('bnk_endt_renewal_no');
        }

        $bank_upd = PartnerBank::select(["bank_code","branch_code","bank_account_no","bank_account","bank_account_name"])
                                ->where('partner_number',$request->client_number)
                                ->first();

        $clnt = $request->client_number;
              
        $data = array();

        
        if ($request->primary == 'Y') {
            PartnerBank::where('partner_number', $request->client_number)
                       ->update(['primary_account' => 'N']);
        }

		if($request->item_no == null ){
			$clientbank_count = PartnerBank::where('partner_number',$request->client_number)->max('item_no');

			$clientbank = new PartnerBank;
			$clientbank->partner_number = $request->client_number; 
			$clientbank->item_no = $clientbank_count + 1;
			$clientbank->payment_mode=$request->payment_method;
			$clientbank->bank_code=$request->bank_code;
			$clientbank->branch_code=$request->bank_branch_code;
			$clientbank->bank_account_no=trim($request->bank_account_no);
			$clientbank->bank_account=trim($request->bank_account_no);
			$clientbank->bank_account_name=$request->bank_account_name;
			$clientbank->banking_phone_no=$request->banking_phone_no;
			$clientbank->currency_type=$request->add_currency_type;
			$clientbank->primary_account=$request->primary;
			$clientbank->in_use = 'Y';
			$clientbank->holder = 'CLIENT';

            $audit1 = array(
                    'policy_no'=>$policy_no,
                    'endt_renewal_no'=>$endt_renewal_no,
                    'risk_item'=>$clnt_no,
                    'table_name'=>'partnerbank',
                    'field_changed'=>'Payment Method',
                    'old_value'=>null,
                    'new_value'=>$request->input('payment_method'),
                    'ip_address'=>$request->ip(),
                    'date_changed'=>Carbon::now('EAT'),
                    'system_user'=>Auth::user()->user_name
                );

            array_push($data, $audit1);
            
            
            
            $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'partnerbank',
                        'field_changed'=>'Bank code',
                        'old_value'=>null,
                        'new_value'=>$request->input('bank_code'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );
    
                array_push($data, $audit1);
                
             
    
            
                $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'partnerbank',
                        'field_changed'=>'Branch code',
                        'old_value'=>null,
                        'new_value'=>$request->input('bank_branch_code'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );
    
                array_push($data, $audit1);
             
    
            
                $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'partnerbank',
                        'field_changed'=>'Branch account no',
                        'old_value'=>null,
                        'new_value'=>$request->input('bank_account_no'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );
                array_push($data, $audit1);
             
    
            
                $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'partnerbank',
                        'field_changed'=>'Branch account name',
                        'old_value'=>null,
                        'new_value'=>$request->input('bank_account_name'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );
    
                array_push($data, $audit1);
             
                $audit1 = array(
                    'policy_no'=>$policy_no,
                    'endt_renewal_no'=>$endt_renewal_no,
                    'risk_item'=>$clnt_no,
                    'table_name'=>'partnerbank',
                    'field_changed'=>'Banking phone number',
                    'old_value'=>null,
                    'new_value'=>$request->input('banking_phone_no'),
                    'ip_address'=>$request->ip(),
                    'date_changed'=>Carbon::now('EAT'),
                    'system_user'=>Auth::user()->user_name
                );
    
                array_push($data, $audit1);
    
            
                $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'partnerbank',
                        'field_changed'=>'currency type',
                        'old_value'=>null,
                        'new_value'=>$request->input('add_currency_type'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );
    
                array_push($data, $audit1);
           

            
            if($request->primary == 'Y'){
                $insert_client = Client::where('client_number',$request->client_number)->update([
                    'bank_code' => $request->bank_code,
                    'branch_code' => $request->bank_branch_code,
                    'bank_account_no' => $request->bank_account_no,
                    'bank_account' => $request->bank_account_no,
                    'bank_account_name' => $request->bank_account_name
                ]);

            }

			$changed_data = $clientbank->getAttributes();
			$process_slug = 'client-addition';
			$activity_slug = 'update';
			$unique_item = $request->client_number;
			$old_data = $clientbank_old;
			$ip =$request->ip();

			log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

            if($nil_endt == 'Y'){
                if ($clientbank->save()) {

                    DB::table('risk_audit_trail')->insert($data);

                    $client =  Client::whereRaw("trim(client_number) = '" .trim($client_no). "' ")->first();
           

                    return response()->json([
                        'success' => true,
                        'message' => 'Account number ' . $request->bank_account_no . ' has been added',
                    ]);
                    // Session::flash('success','Account number '.$request->bank_account_no. ' has been added');
                    
                }else{
                    return response()->json([
                        'success' => false,
                        'message' => 'Error occurred while adding extra account number ' . $request->bank_account_no,
                    ]);
                    // Session::flash('error','Error occured while adding extra account number '.$request->bank_account_no);
                }
            }else{
                if ($clientbank->save()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Account number ' . $request->bank_account_no . ' has been added',
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'Error occurred while adding extra account number ' . $request->bank_account_no,
                    ]);
                }
            }
			
		}

		else{
            if($clientbank_count == 1 || $request->primary == 'Y'){
                $insert_client = Client::where('client_number',$request->client_number)->update([
                    'bank_code' => $request->bank_code,
                    'branch_code' => $request->bank_branch_code,
                    'bank_account_no' => $request->bank_account_no,
                    'bank_account' => $request->bank_account_no,
                    'bank_account_name' => $request->bank_account_name
                ]);

            }
            //update in client bank account details
            $clientbank = PartnerBank::where('partner_number',$request->client_number)->where('item_no', $request->item_no)->update([
                'bank_code' => $request->bank_code,
                'branch_code' => $request->bank_branch_code,
                'bank_account_no' => $request->bank_account_no,
                'bank_account' => $request->bank_account_no,
                'bank_account_name' => $request->bank_account_name,
                'currency_type' => $request->add_currency_type,
                'banking_phone_no' => $request->banking_phone_no,
                'in_use' => $request->in_use,
                'primary_account' =>$request->primary
            ]);
            // $bank_upd = Clientbanks::where('client_number',$request->client_number)->where('item_no', $request->item_no)->get();
            
            if(trim($bank_upd->bank_code) != trim($request->input('bank_code'))){
                $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'partnerbank',
                        'field_changed'=>'bank code',
                        'old_value'=>$bank_upd->bank_code,
                        'new_value'=>$request->input('bank_code'),
                        'date_changed'=>Carbon::now('EAT'),
                        'ip_address'=>$request->ip(),
                        'system_user'=>Auth::user()->user_name
                    );
    
                $bank_upd->bank_code = $request->input('bank_code');
                
    
                array_push($data, $audit1);
            }  
    
            if(trim($bank_upd->branch_code) != trim($request->input('bank_branch_code'))){
                $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'partnerbank',
                        'field_changed'=>'branch code',
                        'old_value'=>$bank_upd->branch_code,
                        'new_value'=>$request->input('bank_branch_code'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );
    
                $bank_upd->branch_code = $request->input('bank_branch_code');
                
    
                array_push($data, $audit1);
            } 
    
            if(trim($bank_upd->bank_account_no) != trim($request->input('bank_account_no'))){
                $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'partnerbank',
                        'field_changed'=>'branch account no',
                        'old_value'=>$bank_upd->bank_account_no,
                        'new_value'=>$request->input('bank_account_no'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );
    
                $bank_upd->bank_account_no = $request->input('bank_account_no');
                
    
                array_push($data, $audit1);
            } 
    
            if(trim($bank_upd->banking_phone_no) != trim($request->input('banking_phone_no'))){
                $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'partnerbank',
                        'field_changed'=>'Bank Phone Number',
                        'old_value'=>$bank_upd->banking_phone_no,
                        'new_value'=>$request->input('banking_phone_no'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );
    
                $bank_upd->banking_phone_no = $request->input('banking_phone_no');
                
    
                array_push($data, $audit1);
            } 
            if(trim($bank_upd->bank_account_name) != trim($request->input('bank_account_name'))){
                $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'partnerbank',
                        'field_changed'=>'branch account name',
                        'old_value'=>$bank_upd->bank_account_name,
                        'new_value'=>$request->input('bank_account_name'),
                        'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );
    
                $bank_upd->bank_account_name = $request->input('bank_account_name');
                
    
                array_push($data, $audit1);
            } 
    
    
            if(trim($bank_upd->currency_type) != trim($request->input('add_currency_type'))){
                $audit1 = array(
                        'policy_no'=>$policy_no,
                        'endt_renewal_no'=>$endt_renewal_no,
                        'risk_item'=>$clnt_no,
                        'table_name'=>'partnerbank',
                        'field_changed'=>'currency type',
                        'old_value'=>$bank_upd->currency_type,
                        'new_value'=>$request->input('add_currency_type'),
                       'ip_address'=>$request->ip(),
                        'date_changed'=>Carbon::now('EAT'),
                        'system_user'=>Auth::user()->user_name
                    );
    
                $bank_upd->currency_type = $request->input('add_currency_type');
                
    
                array_push($data, $audit1);
            }
            $updated_clientbank = PartnerBank::where('partner_number',$request->client_number)->where('item_no', $request->item_no)->first();

            $changed_data = $updated_clientbank->getAttributes();
			$process_slug = 'client-addition';
			$activity_slug = 'update';
			$unique_item = $request->client_number;
			$old_data = $clientbank_old;
			$ip =$request->ip();

			log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);
            
            if($nil_endt == 'Y'){
                if ($clientbank) {

                    DB::table('risk_audit_trail')->insert($data);

                    // Session::flash('success','Account has been updated to'.$request->bank_account_no);
                    return response()->json([
                        'success' => true,
                        'message' => 'Account has been updated to ' . $request->bank_account_no,
                    ]);
                    

                }else{
                    // Session::flash('error','Error occured while updating account number '.$clientbank_old->bank_account_no);
                    return response()->json([
                        'success' => false,
                        'message' => 'Error occurred while updating account number ' . $clientbank_old->bank_account_no,
                    ]);
                }
            }else{
                if ($clientbank) {
                    // Session::flash('success','Account has been updated to '.$request->bank_account_no);
                    return response()->json([
                        'success' => true,
                        'message' => 'Account has been updated to ' . $request->bank_account_no,
                    ]);
                }else{
                    // Session::flash('error','Error occured while updating account number '.$clientbank_old->bank_account_no);
                    return response()->json([
                        'success' => false,
                        'message' => 'Error occurred while updating account number ' . $clientbank_old->bank_account_no,
                    ]);
                }
            }
            
	    }
        
        return back();

	}

    public function integrateClient(Request $request){
        $client_no  = $request->clnt_no;
        $partner_group = 'cust'; //Customer partner group
        


        $client =  Client::select('client_type')->where('client_number',$client_no)->first();
        $partner_type = $client->client_type;
        $partner_code = $client_no;

        

        $getpartner = new IntergrationController;
        $resp = getPartnerData($partner_group,$partner_type,$partner_code);


        if ( $client->client_type == "I") {
            $firstname= $client->first_name;
            $middlename=$client->others;
            $surname=$client->surname;
            $companyname="";
            $id_type=$client->identity_type;
            $id_ke=$client->id_number;
            $companytin="";
            $pin_ke=$client->pin_number;
            $legalentitytype=1;
            $gender=$client->gender;

        }else {
            $firstname= "";
            $middlename="";
            $surname="";
            $companyname=$client->name;
            $id_type=*********;
            $id_ke=0;
            $companytin=$client->pin_number;
            $pin_ke="";
            $legalentitytype=2;
            $gender=3;
        }

        if(trim($client->identity_type) == 1){
            $pass_no="";
          
        }elseif ($client->identity_type == 2) {
            $pass_no=$client->id_number;
        }else{
            $pass_no="";
        }

        

        $partner_data=array(
            'country_id'=>2,//$request->country_code,
            'sourceid'=>1,
            'partytypeid'=>1,
            'legalentitytype'=>$legalentitytype,
            'phonenumber_main'=>$client->telephone,
            'phonenumber_alternate'=>$client->telephone,
            'emailaddress1'=>$client->e_mail,
            'emailaddress2'=>$client->e_mail,
            'firstname'=>$firstname,
            'middlename'=>$middlename,
            'surname'=>$surname,
            'companyname'=>$companyname,
            'residenceordomicile_countrycode'=>$client->country,
            'residenceordomicile_regionorcounty'=>"",
            'dateofbirth'=>$client->date_of_birth,
            'passport'=>$pass_no,
            'id_type'=>$id_type,
            'id_ke'=>$id_ke,
            'id_ug'=>"",
            'id_tz'=>"",
            'id_ss'=>"",
            'id_rw'=>"",
            'pin_ke'=>"",
            'pin_ug'=>"",
            'pin_rw'=>"",
            'pin_ss'=>"",
            'pin_tz'=>"",
            'gender'=>$gender,
            'companytin'=>$companytin
        );
     
        $getpartner = new IntergrationController;
        $resp = (object)$getpartner->getPatnerNo($partner_data);
       
  
       
        if($resp->statusCode == 200){
            $updatepartner = Client::whereRaw("trim(client_number) = '" . $client_no . "' ")
                ->update([
                    'partnernumber'=>$resp->message,
                    'client_status'=>"Y",
                    'partnernumber_status'=>"Y",
                    'partnernumber_error'=>""

                ]);
           
          
        }else{
            $updatepartner = Client::whereRaw("trim(client_number) = '" . $client_no . "' ")
            ->update([
                'partnernumber_error'=>$resp->message,
                'client_status'=>"N",
                'partnernumber_status'=>"N"

            ]);
        }

        if($request->mtp_flag <> "Y"){
           return redirect()->back();
        }





    }

    public function generateClientPartnerNo($request, $clnt_no){
        $id_number = $request->input('identity_number');

        $client =  Client::select('client_type')->where('client_number',$clnt_no)->first();
        $partner_type = $client->client_type;
        $partner_code = $clnt_no;
        $partner_group = 'cust';

        $getpartner = new IntergrationController;

        $resp = $getpartner->getPartnerData($partner_group,$partner_type,$partner_code);
        
        if ($resp->statusCode == 200) {
            $data = [
                'status' => 1,
                'message' => 'Partner number for: '.$clnt_no.' generated successfully'
            ];


        }elseif($resp->statusCode == 400){
            $data = [
                'status' => 2,
                'message' => 'Status Code: '.$resp->statusCode . ' Partner number for: '.$clnt_no.' failed to generate. Please try again later.'
            ];
            
        }else{
            $data = [
                'status' => 2,
                'message' => 'Partner number for: '.$clnt_no.' failed to generate. Please try again later.'
            ];
            
        }
        return $data;
        
    }

    public function fetch_branch_agents(Request $request){

        $branch = (int)$request->branch;

        if($branch){
            $intermediaryParams = new IntermediaryQueryParams([
                'branch' => $request->get('branch'),
            ]);
            $agents  =IntermediaryQueryService::getActiveintermediaryByBranch($intermediaryParams)
                                                ->select('intermediary.intermediary_number', 'name')
                                                ->get();    
            // $agents = Agmnf::select('agent', 'name')
            //             ->where('branch', $branch)
            //             // ->where(function($query) {
            //             //     $query->where('stop_flag', '<>', 'Y')
            //             //         ->orWhereNull('stop_flag');
            //             // })
            //             ->where('status','ACT')
            //             ->get();
            
            return response()->json(['status' =>  1 , 'data' => $agents]);

        }else{
            return response()->json(['status' =>  0 , 'message' => 'Branch was not parsed or has no value.']);
        }

        return response()->json(['status' =>  0 , 'message' => 'Could not process your request as parsed.']);
    }

    public function fetch_child_locations(Request $request){

        $parent_code =  $request->parent_code;

        if(!is_null($parent_code)){
            $locations = DB::table('locations')
                                ->join('location_levels', function ($join) {
                                            $join->on('locations.loc_level', '=', 'location_levels.level_code');
                                        })
                                ->select('locations.loc_code','locations.description','locations.loc_level','location_levels.level_name')
                                ->where('parent_code',$parent_code)
                                ->get();
            
            return response()->json(['status' =>  1 , 'data' => $locations]);
        }else{
            return response()->json(['status' =>  0 , 'message' => 'Location was not parsed or has no value.']);
        }

        return response()->json(['status' =>  0 , 'message' => 'Could not process your request as parsed.']);
    }
    
    public function fetch_parallel_child_locations(Request $request)
    {
        $levelCode = $request->levelCode;
        $previousLevelCode = $request->previousLevelCode;
        $previousLevelValue = $request->previousLevelValue;

        if (!empty($previousLevelCode) && !empty($previousLevelValue)) {

            $child_locations = $this->get_child_locations($previousLevelValue);

            for ($i = $previousLevelCode+1; $i < $levelCode; $i++) {
                $parent_locations=[];
                $parent_locations=$child_locations;
                $child_locations=[];

                foreach ($parent_locations as $location) {
                    $parentCode = $location->loc_code;
                    $locations = $this->get_child_locations($parentCode)->toArray();

                    $child_locations = array_merge($child_locations, $locations);

                }
            }

            return response()->json(['status' => 1, 'data' => $child_locations]);
        } else {
            
            $locations = DB::table('locations')
                            ->join('location_levels', 'locations.loc_level', '=', 'location_levels.level_code')
                            ->select('locations.loc_code', 'locations.description', 'locations.loc_level', 'location_levels.level_name')
                            ->where('locations.loc_level', $levelCode)
                            ->get();
    
            return response()->json(['status' => 1, 'data' => $locations]);
        }
    }
    public function get_child_locations($parentCode){
        $child_locations = DB::table('locations')
            ->join('location_levels', 'locations.loc_level', '=', 'location_levels.level_code')
            ->select('locations.loc_code', 'locations.description', 'locations.loc_level', 'location_levels.level_name')
            ->where('parent_code', $parentCode)
            ->get();
            return $child_locations;
    }

    public function fetch_parent_locations(Request $request)
    {
        $locationcode = $request->locationcode;
        $locationlevel = $request->locationlevel;

        if (!empty($locationcode)) {
  
            $child = DB::table('locations')
                            ->join('location_levels', 'locations.loc_level', '=', 'location_levels.level_code')
                            ->select('locations.parent_code')
                            ->where('locations.loc_code', $locationcode)
                            ->first();
                            
            $parent = DB::table('locations')
                ->join('location_levels', 'locations.loc_level', '=', 'location_levels.level_code')
                ->select('locations.loc_code', 'locations.description', 'locations.loc_level', 'location_levels.level_name')
                ->where('locations.loc_code', $child->parent_code)
                ->first();
    
            return response()->json(['status' => 1, 'data' => $parent]);
        }
    }



}

