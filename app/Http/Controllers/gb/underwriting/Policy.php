<?php

namespace App\Http\Controllers\gb\underwriting;

use App\Dept;
use DateTime;
use App\Acdet;
use App\Agmnf;
use App\Clhmn;
use App\Madtl;
use App\Ports;
use App\Title;
use App\Tran0;
use App\Branch;
use App\Cities;
use App\Client;
use App\Crmast;
use App\Dtran0;
use App\Polast;
use App\Polcmb;
use App\Polsec;
use App\Reidoc;
use App\Bustype;
use App\Classbr;
use App\Clauses;
use App\Currate;
use App\MailBox;
use App\Pipcnam;
use App\Pipstmp;
use App\Polsect;
use App\Ptamdtl;
use App\Backdate;
use App\Cbdeduct;
use App\Curr_ate;
use App\Currency;
use App\Currrate;
use App\Dcontrol;
use App\Debitdtl;
use App\Nlparams;
use App\Polsched;
use App\PPWParam;
use App\Prosched;
use App\Accessory;
use App\Covertype;
use App\Debitmast;
use App\Financier;
use App\Polclause;
use App\Polexcess;
use App\Pollimits;
use App\Polmaster;
use App\Transtype;
use App\Vat_setup;
use Carbon\Carbon;
use App\Autolimits;
use App\Classexces;
use App\ClassModel;
use App\Extensions;
use App\FacUploads;
use App\Instalplan;
use App\Motermrate;
use App\Occupation;
use App\Olbnknames;
use App\Polclauses;
use App\Polsectend;
use App\Gltaxgroups;
use App\Instalparam;
use App\Mautoclauses;
use App\Polmasterend;
use App\Polmisc_fees;
use App\Endorse_descr;
use App\Facuploadbatch;
use App\MarineUTLDebit;
use App\Marinemasterpol;
use App\Misc_fees_param;
use App\Models\Polscope;
use App\WorkflowsConfig;
use App\Marinemasterhist;
use App\Models\Condition;
use App\Models\Modtlhist;
use App\Models\Modtlmast;
use App\Models\Modtlsumm;
use App\Models\Motcvrdet;
use App\Models\Motorsect;
use App\Nil_endorsetypes;
use App\Risk_audit_trail;
use App\Sec_ext_reinsure;
use App\Branch_agent_join;
use App\Installmentstatus;
use App\Models\Disclaimer;
use App\Models\Exclusions;
use App\Models\Modtlpivot;
use App\Discounts_loadings;
use App\Models\Motorpolsec;
use App\Models\Polcoverage;
use App\Models\Polwarranty;
use App\workflow\Workflows;
use Illuminate\Support\Str;
use App\Models\Polcondition;
use App\Models\Polexclusion;
use App\Models\Productscope;
use App\Models\ProductPolWording;
use Illuminate\Http\Request;
use App\Models\Poldisclaimer;
use App\workflow\Escalations;
use App\Models\Classcondition;
use App\Models\FacultInClient;
// use App\Http\Helpers;
use App\Models\StickerRepldtl;
use App\Combined_class_members;
use App\Imports\ImportTemplate;
use App\Models\Productwarranty;
use App\Models\SmartCoverNotes;
use App\Models\SmartTaxInvoices;
use Yajra\Datatables\Datatables;
use App\Models\BackdateTransType;
use App\Models\ProformaReason;
use App\Models\Medical\Medmember;
use App\Polsect_not_in_polsectend;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Backpostdate_requests;
use App\Models\Medical\Polmedoptplan;
use Illuminate\Support\Facades\Route;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Response;
use App\Classes\Common\FileUploadManager;
use Illuminate\Support\Facades\Validator;
use App\Services\IntermediaryQueryService;
use App\ValueObjects\IntermediaryQueryParams;
use App\Classes\gb\underwriting\CombinedPolicy;
use App\Http\Controllers\gb\medical\Medicalrisk;
use App\Http\Controllers\gb\medical\ReplicateMedicals;
use \Phpoffice\PhpSpreadsheet\Shared\Date as ExcelDate;
use App\Http\Controllers\gb\underwriting\Policy_details;
use App\Http\Controllers\gb\underwriting\Policy_functions;
use App\Http\Controllers\gb\underwriting\MarineOpenProcessing;
use App\Models\PerCapitaRate;
use App\Models\UwParameter;
use Symfony\Component\HttpFoundation\Response as ResponseCode;

class Policy extends Risk
{
    protected $agents;
    protected $branches;
    protected $types;
    protected $clients;
    protected $titles;
    protected $banks;

    /*variables for policy*/
    protected $branch;
    protected $class;
    protected $endorsetype;
    protected $policy_serial;
    protected $uw_year;
    protected $uw_month;

    public function index(Request $request)
    {
       

        $trans_type = (string) $request->get('trans_type');

        switch ($trans_type) {
            case 'POL':

                return $this->Pol_transaction($request);

                break;

            case 'RNS':
                return $this->Rns_transaction($request);

                break;
            case 'EXT':
            case 'INS':

                return $this->Ext_transaction($request);

                break;

            case 'NIL':

                return $this->Ext_transaction($request);

                break;

            case 'REN':

                return $this->Ren_transaction($request);

                break;

            case 'EDIT':

                return $this->Edit_transaction($request);

                break;
        }
    }


    public function get_todays_rate(Request $request)
    {

        $selected_currency = $request->get('currency');
        $date = Carbon::today();

        $currency = Currency::where('currency_code', $selected_currency)->get(['currency', 'base_currency']);
        $currency = $currency[0];



        if ($currency->base_currency == 'Y') {

            $result = array('valid' => 2, 'rate' => 1);
            echo json_encode($result);
        } else {


            $count_curr = Curr_ate::where('currency_code', $selected_currency)
                ->where('rate_date', $date)
                ->count();


            if ($count_curr > 0) {

                $rate = Curr_ate::where('currency_code', $selected_currency)
                    ->where('rate_date', $date)
                    ->get();

                $result = array('valid' => 1, 'rate' => $rate[0]->currency_rate);
                echo json_encode($result);
            } else {


                $result = array('valid' => 0, 'short_descr' => $currency->currency);
                echo json_encode($result);
            }
        }
    }

    public function yesterdayRate(Request $request)
    {
        $jana = Carbon::yesterday()->format('Y-m-d H:i:s');;
        $query = Currrate::where('rate_date', $jana)->where('currency_code', $request->currency);
        $check = $query->count();

        if ($check == 0) {
            # Notify user to set currency rate manually
            return $check;
        } else {
            # Fetch rate
            $rate = $query->first();

            return $rate;
        }
    }


    public function set_currency(Request $request)
    {
        $currency_code = $request->get('curr_code');
        $new_rate = $request->get('rate_new');

        $todays_date = Carbon::today();

        $curr_rate = new Curr_ate;
        $curr_rate->currency_code = $currency_code;
        $curr_rate->rate_date = $todays_date;
        $curr_rate->currency_rate = $new_rate;
        $curr_rate->save();


        $result = array('rate' => $new_rate);

        echo json_encode($result);
    }


    public function get_class(Request $request)
    {

        $dept = $request->get('dept');
        $class = ClassModel::where('dept', $dept)->where('class_active', 'Y')->where('rider_class','N')->get();
        echo json_encode($class);
    }

    public function get_class_cmb(Request $request)
    {
        $dept = $request->get('dept');
        $policy_no = $request->get('policy_no');
        $endt_renewal_no = $request->get('endt_renewal_no');
        $polcmb = Polcmb::where('endt_renewal_no',$endt_renewal_no)->pluck('class');
        $class = ClassModel::where('dept', $dept)->where('class_active', 'Y')->WhereNotIn('class',$polcmb)->get();
        echo json_encode($class);
    }


    public function ta_conversion_rate(Request $request)
    {
        $class = $request->get('class');
        $rate = $request->get('rate');
        $currency = $request->get('currency');
        $travel = ClassModel::select('class_base_currency')->where('class', $class)->first();
        $foreign_curr = Currate::select('currency_rate')
                        ->where('currency_code', $travel->class_base_currency)
                        ->where(function ($query) {
                            $query->where('rate_date', Carbon::yesterday())
                                ->orWhere('rate_date', Carbon::today());
                        })->first();

        $conversion_rate = 1;

        if($travel->class_base_currency == 0 && $travel->class_base_currency != $currency) {

            $conversion_rate = 1 / $rate;

        }elseif ($travel->class_base_currency != 0 && $travel->class_base_currency != $currency) {

            $conversion_rate = $foreign_curr->currency_rate;

        }elseif ($travel->class_base_currency == $currency) {
            $conversion_rate = 1; 

        }
        return response()->json(['conversion_rate' => $conversion_rate]);
    }

    public function get_exem(Request $request)
    {
        $class = $request->get('class');
        
        $exem = ClassModel::where('class', $class)->first();

        return response()->json(['exem'=>$exem->exem, 
                                 'travel'=>$exem->travel,
                                 'shortterm_applicable' => $exem->shortterm_applicable,
                                 'open_cover' => $exem->open_cover,
                                 'class_base_currency'=>$exem->class_base_currency
                                ]);
    }

    public function verifyShortTermRate($class,$cover_days)
    {
        $request = new Request([
            'class' => $class,
            'cover_days' => $cover_days
        ]); 

        $resp = $this->getShortTermRate($request);
        $resp = json_decode($resp);

        return (int)$resp->shortTermRate;
    }

    public function getShortTermRate(Request $request)
    {
        $class = $request->get('class');
        $cover_days = $request->get('cover_days');
        $ratesSet = Motermrate::where('class', $class)->count();

        if($ratesSet > 0){

            $period = Motermrate::where('class', $class)
                                ->where('period','>=',(int)$cover_days)
                                ->orderBy('period','ASC')
                                ->take(1)
                                ->first();

        }else{

            $period = Motermrate::where('class', 0)
                                ->where('period','>=',(int)$cover_days)
                                ->orderBy('period','ASC')
                                ->take(1)
                                ->first();
            
            $ratesSet  = ($period) ? 1 : 0 ;               

        }  

        $shortTermRate = $period->rate;
        
        return json_encode(['shortTermRate' => $shortTermRate,'ratesSet' => $ratesSet]);
    }


    public function fetch_ports(Request $request){

        $country = $request->get('source');
        $port = Ports::where('country_code',$country)->get();
        // //echo $port;

        $cities = Cities::where('country_code', $country)->get();

        $data = array();
        $data['port'] = $port;
        $data['cities'] = $cities;

        echo json_encode($data);

   }

    public function getBackPostDate(Request $request)
    {
        $email = Auth::user()->email;
        $trans_type = $request->input('trans_type');

        $backpost = $this->back_post_date($email, $trans_type);

        return response()->json($backpost);
    }

   public function back_post_date($email, $trans_type){

        $backpost_policy = array();

        $transType = BackdateTransType::where('trans_type', $trans_type)->first();

        if (!$transType) {
            // Default policy dates to now()
            $backpost_policy = [
                'back_date' => Carbon::now()->subDay(),  // Yesterday
                'post_date' => Carbon::now(),  // Today
                'max_backdate_days' => Carbon::now()->subDay(),
                'max_postdate_days' => Carbon::now(),
                'warning' => 'Transaction type backdate limit not found. Default to today for booking.'
            ];
            return $backpost_policy;
        } else {
            // Default policy dates to now() if the trans type is set but user not set
            $backpost_policy = [
                'back_date' => Carbon::now()->subDay(),  // Yesterday
                'post_date' => Carbon::now(),  // Tomorrow
                'max_backdate_days' => Carbon::now()->subDays($transType->max_backdate_days)->subDay(),
                'max_postdate_days' => Carbon::now()->addDays($transType->max_postdate_days)
            ];

        }

        // Fetch user's backdate policy record
        $backpost_policy_rec = Backdate::where('user_email', $email)
            ->where('backdate_trans_type_id', $transType->id)
            ->first();

        //     if (!$backpost_policy_rec) {
        //     return response()->json(['error' => 'User not found in the transaction type backdate'], 422);
        // }

        if ($backpost_policy_rec) {
            // Adjust back date if the user's policy is more restrictive
            if ($transType->backdate_policy == 'Y' && $backpost_policy_rec->backdate_policy == 'Y') {
                $back_days = $backpost_policy_rec->number_of_days;

                if ($back_days > $transType->backdate_number_of_days) {
                    return response()->json(['error' => 'The user backdate days exceeds transaction type limit'], 422);
                }
                $backpost_policy['back_date'] = ($back_days > 0) ? Carbon::now()->subDays($back_days)->subDay() : Carbon::now()->subDay();
            }

            // Adjust post date if the user's policy allows postdating
            if ($transType->postdate_policy == 'Y' && $backpost_policy_rec->postdate_policy == 'Y') {
                $post_days = $backpost_policy_rec->postdate_no_of_days;

                if ($post_days > $transType->postdate_number_of_days) {
                    return response()->json(['error' => 'The user postdate days exceeds transaction type limit'], 422);
                }
                $backpost_policy['post_date'] = ($post_days > 0) ? Carbon::now()->addDays($post_days) : Carbon::now();
            }
        }

        return $backpost_policy;
   }

    public function getTravelcurrencies()
    {
        $travelClassCount = ClassModel::whereNotNull('class_base_currency')->where('travel', 'Y')->count();

        if($travelClassCount){

            $travelClass = ClassModel::select('class_base_currency')->where('travel', 'Y')->first();

            $baseCurrencyCode = $travelClass->class_base_currency;

            if ($baseCurrencyCode != 0) {
                $travelCurrencies = Currency::whereIn('currency_code', [0, $baseCurrencyCode])->pluck('currency_code');
                
            } else {
                $travelCurrencies = Currency::pluck('currency_code');
            }

        }
        else{

            $travelCurrencies = Currency::pluck('currency_code');
            
        }

        return $travelCurrencies;
        
    }

    public function Pol_transaction($request){
        Gate::authorize('register-policy-document');
      
        //get user email
        $email = Auth::user()->email;
        $user_branch = Auth::user()->branch;
        $trans_type = (string) $request->get('trans_type');

        //$backpost_policy = $this->back_post_date($email, $trans_type);
        $clnt_no = $request->get('client');

        //$this->clients=Client::take(100)->get();
        $this->clients = Client::where('client_number', $clnt_no)->get();
        $this->titles = Title::all();
        $this->branches = Branch::all();
        $this->banks = Olbnknames::all();
        $this->types = Reidoc::where('orig', 'U/W')->get(['doc_type', 'entry_type_descr', 'description']);

        $currency = Currency::all();


        $class = ClassModel::where('class_active', 'Y')->get();
        //$covertype=Covertype::all();
        $ast = Polast::where('active','Y')->get();
        $bus_type = Bustype::all();
        $financier = Financier::all();
        $today = date('Y');
        $department=Dept::orderBy('description', 'ASC')->get();
        $account_period = Dtran0::where('rec_no', 0)->get(['account_month', 'account_year']);
        $actual_period = $account_period[0]->account_month . '/' . $account_period[0]->account_year;
        $instalplan = Instalplan::all();

        /** VAT setup **/
        $vat_setup = Vat_setup::all();
        //dd($vat_setup);
        /** End VAT Setup **/

        $crmast = DB::select("SELECT LPAD(c.branch, 3, 0) || c.agent branch_agent, c.name FROM crmast c
            INNER JOIN acctype a ON c.account_type = a.acc_type WHERE a.facultative = 'Y'");

        $pipstmp = Pipstmp::first();
        

        $facultinClients = FacultInClient::select('facultin_client_no', 'name')->get();
        $facultinppws = PPWParam::select('ppw_code', 'ppw_name')->get();
        $travel_vat_check = Pipcnam::select('exem_vat_on_travel')->first();
        $allowedToBackdatePreviousYears = BackdateTransType::where('trans_type', $trans_type)->value('backdate_to_previous_years');
        $ProformaReason = ProformaReason::all();
        $travel_code = ClassModel::select('class')->where('travel','Y')->first();

        $uw_parameters = UwParameter::first();
        return view('gb.underwriting.policy_form', [
            'trans_type' => $trans_type,
            'incept_date' => Carbon::now()->subDays(2),
            'period_from' => Carbon::now()->subDays(2),
            'period_to' => Carbon::now()->addDays(365),
            'branch' => $this->branches,
            'user_branch' => $user_branch,
            'currencies' => $currency,
            'travel_currencies' => $this->getTravelcurrencies(),
            'agent' => $this->agents,
            'types' => $this->types,
            'title' => $this->titles,
            'bank' => $this->banks,
            'client' => $this->clients,
            'class' => $class,
            'ast' => $ast,
            'bustype' => $bus_type,
            'financier' => $financier,
            'todaysdate' => $today,
            'dept' => $department,
            'period' => $actual_period,
            'last_date' => $last_date,
            'instalplan' => $instalplan,
            'ast_marker' => '',
            'vat_setup' => $vat_setup,
            'crmast'=>$crmast,
            'pipstmp' => $pipstmp,
            // 'backpost_policy' => $backpost_policy,
            'facultinClients' => $facultinClients,
            'facultinppws' => $facultinppws,
            'travel_vat_check' =>$travel_vat_check,
            'allowedToBackdatePreviousYears' => $allowedToBackdatePreviousYears,
            'ProformaReason' => $ProformaReason,
            'uw_parameters' => $uw_parameters,
            'policywordings' => $policywordings,
            'travel_code'=>$travel_code,
        ]);
    }


    public function Ext_transaction($request)
    {
        Gate::authorize('process-endorsement');

        //get user email
        $email = Auth::user()->email;

        $old_endt_renewal_no = $request->get('endt_renewal_no');
        $clnt_no = $request->get('client');
        $trans_type = $request->get('trans_type');
        $opencover_master_flag = $request->get('open_cover_ext_master');
        $mac_endt_no = ($request->has('mac_endt_no')) ? $request->get('mac_endt_no') : 'N';
       
        //get dcontrol record for endorsement number
        $dcontrol = Dcontrol::where('endt_renewal_no', $old_endt_renewal_no)->first();
        $poldetails= Polmaster::select('status_code')->where('endorse_no', $old_endt_renewal_no)->first();
        
        $clsrec = ClassModel::where('class',$dcontrol->class)->first();
        $pipstmp = Pipstmp::first();
        $pipcnam = Pipcnam::first();
        if ($dcontrol->trans_type == 'PTA') {
            $dcontrol = Dcontrol::where('policy_no', $dcontrol->policy_no)
                                    ->where('trans_type', '<>', 'PTA')
                                    ->orderBy('dtrans_no', 'desc')
                                    ->first();

        }
        else if($clsrec->open_cover == 'Y'){

            $dcontrol = Dcontrol::where('endt_renewal_no', $request->get('mac_endt_no'))->first();
        }

        //get the number of days between period from and period_to
        $effective_date = Carbon::parse($dcontrol->effective_date);
        $period_to = Carbon::parse($dcontrol->period_to);
        $cover_days = $effective_date->diffInDays($period_to) + 1;
        // $cover_days = $dcontrol->days_covered;
        $extendfrom = Carbon::today();

        //get the backdate days
        $backdate_policy = $this->back_post_date($email, $trans_type);

        $this->clients = Client::where('client_number', $clnt_no)->get();
        $intermediaryParams = new IntermediaryQueryParams([
            'branch' => $dcontrol->branch
        ]);
        $this->agents  =IntermediaryQueryService::getActiveintermediaryByBranch($intermediaryParams)->get();
       // $this->agents = Agmnf::where('branch', $dcontrol->branch)->get();
        $this->titles = Title::all();
        $this->branches = Branch::all();
        $this->banks = Olbnknames::all();
        $this->types = Reidoc::where('orig', 'U/W')->get(['doc_type', 'entry_type_descr', 'description']);
        $currency = Currency::all();
        $instalplan = Instalplan::all();
        $dpt = (int)$dcontrol->dept;
        $class = ClassModel::where('dept',$dpt)->where('class_active','Y')->get();     
        
        //$covertype=Covertype::all();
        $ast = Polast::where('active','Y')->get();
        $bus_type = Bustype::all();
        $financier = Financier::all();
        $ProformaReason = ProformaReason::all();
        $today = date('Y/m/d');
        $year = date('Y');
        $department=Dept::orderBy('description', 'ASC')->get();
        $account_period = Dtran0::where('rec_no', 0)->get(['account_month', 'account_year']);
        $actual_period = $account_period[0]->account_month . '/' . $account_period[0]->account_year;
        $motor_pol = ClassModel::where('class',$dcontrol->class)->get();

        if($trans_type == 'NIL'){
            $pol_stat = Polmaster::where('policy_no',$dcontrol->policy_no)->get(['status_code']);
            $pol_stat = $pol_stat[0];

            if($motor_pol[0]->motor_policy == 'Y'){

                $nil_risk = Modtlmast::where('policy_no',$dcontrol->policy_no)
                    ->where('status','ACT')
                    ->get(); 

            }else{
                if($motor_pol[0]->combined == 'Y' && $motor_pol[0]->fire_loc_as_default == 'Y'){
                    $nil_risk = Polsect::where('policy_no',$dcontrol->policy_no)->where('class',$motor_pol[0]->cmb_default_loc_class)->get();
                }
                else{
                    $nil_risk = Polsect::where('policy_no',$dcontrol->policy_no)->where('deleted','<>','Y')->get();
                }
            }
            //$nil_types = Nil_endorsetypes::all();

            if($pol_stat->status_code == 'CNC'){
                $nil_types = Nil_endorsetypes::where('endt_type', 6)->get(); 
            }else{
                $nil_types = Nil_endorsetypes::whereNotIn('endt_type',[6,7])
                                            ->get();
            }

            if($pol_stat->status_code != 'CNC' && $motor_pol[0]->motor_policy == 'Y'){

                $nil_types = Nil_endorsetypes::where('endt_type','<>', 6)
                                            ->get();
            }

            // latest transtype
            $cover_period_editable = false;
            $check_REN = Dcontrol::where('policy_no',$dcontrol->policy_no)
                ->where('trans_type', 'REN')
                ->orderBy('dtrans_no','desc')
                ->where('cancelled', '<>', 'Y')
                ->first();
            if(isset($check_REN)){
                $REN_dtrans_no = $check_REN->dtrans_no;
                $EXT_after_REN = Dcontrol::where('policy_no',$dcontrol->policy_no)
                    ->where('dtrans_no','>', $REN_dtrans_no)
                    ->where('trans_type', 'EXT')
                    ->orderBy('dtrans_no','desc')
                    ->where('cancelled', '<>', 'Y')
                    ->count();
                if($EXT_after_REN == 0){
                    $cover_period_editable = true;
                }
            }
            else{
                $EXT_after_POL = Dcontrol::where('policy_no',$dcontrol->policy_no)
                    ->where('trans_type', 'EXT')
                    ->where('cancelled', '<>', 'Y')
                    ->count();
                if($EXT_after_POL == 0){
                    $cover_period_editable = true;
                }
            }
            $latest_transaction = Dcontrol::where('policy_no',$dcontrol->policy_no)
                ->orderBy('dtrans_no','desc')
                ->where('cancelled', '<>', 'Y')
                ->first();
            $nil_cover_days = $latest_transaction->days_covered;
        }else{
            $nil_types = array(0,0);
            $nil_risk = array(0,0);
            $nil_cover_days = null;
            $cover_period_editable = false;
        }

        # Installment Plan Check
        $installPlan = $dcontrol->plan;
        $planCateg = $dcontrol->plan_categ;
        $currentInstallment = $dcontrol->instal_categ;
        $nextInstallment = $currentInstallment + 1;

        if ($dcontrol->ast_marker == 'I') {
            $installParam = Instalparam::where('plan', $installPlan)
            ->where('instal_categ', $nextInstallment)
            ->exists();
    
            if (!$installParam && $trans_type == 'INS') {
                return redirect()->route('endorse_functions', ['policy_no' => $dcontrol->policy_no])->with('error', 'Installment plans for the policy have been exhausted. Kindly renew policy');
            }

            if ($trans_type == 'INS' || $trans_type == 'EXT') {

                $Previous_installment = Dcontrol::select('endt_renewal_no')
                                                ->where('policy_no', $dcontrol->policy_no)
                                                ->whereIn('trans_type',['POL','REN','INS','RNS'])
                                                ->where(function($query){
                                                    $query->where('cancelled','N')
                                                          ->orWhereNull('cancelled');
                                                })
                                                ->orderBy('dtrans_no','desc')
                                                ->first();

                $dcontrol = Dcontrol::where('endt_renewal_no',$Previous_installment->endt_renewal_no)
                                    ->first();
            }
        }

        $nextInstallmentDetails = Instalparam::where('plan', $installPlan)
        ->where('instal_categ', $nextInstallment)
        ->first();

        $vat_setup = Vat_setup::all();
        $crmast = DB::select("SELECT LPAD(c.branch, 3, 0) || c.agent branch_agent, c.name FROM crmast c
            INNER JOIN acctype a ON c.account_type = a.acc_type WHERE a.facultative = 'Y'");

        $facultinClients = FacultInClient::select('facultin_client_no', 'name')
                            ->where('facultin_client_no',$dcontrol->facultin_client_no)
                            ->get();
        $facultinppws = PPWParam::select('ppw_code', 'ppw_name')->where('ppw_code',$dcontrol->ppw_code)->get();
       ##loss ration
       $clampaid=Clhmn::where('policy_no',$dcontrol->policy_no)->sum('cost_todate');
       $clamest=Clhmn::where('policy_no',$dcontrol->policy_no)->sum('curr_total_estimate');
       $out_bal=Acdet::where('policy_no',$dcontrol->policy_no)->sum('unallocated');
       $gross_premium=Debitmast::where('policy_no',$dcontrol->policy_no)->sum('gross_amount');
       
       if($gross_premium==0 ){
           $lor = 0;
       }else{
           $lor = (($clampaid+$clamest) / $gross_premium) * 100 ;
       }
       $loss_ratio  = round($lor,2) . ' %';
       $travel_vat_check = Pipcnam::select('exem_vat_on_travel')->first();


       ##end loss atio
       $allowedToBackdatePreviousYears = BackdateTransType::where('trans_type', $trans_type)->value('backdate_to_previous_years');
       $travel_code = ClassModel::select('class')->where('travel','Y')->first();

       $policy_wordings = ProductPolWording::where('class', $dcontrol->class)
       ->join('policydocuments', 'product_pol_wordings.policydocid', '=', 'policydocuments.id')
       ->select('product_pol_wordings.heading', 'policydocuments.document_description', 'product_pol_wordings.policydocid')
       ->get();
    //    dd($policy_wordings);

       
        return view('gb.underwriting.policy_form', [
            'trans_type' => $trans_type,
            'incept_date' => $dcontrol->incept_date,
            'period_from' => $dcontrol->period_from,
            'period_to' => $dcontrol->period_to,
            'old_endt_trans' => $dcontrol,
            'poldetails' => $poldetails,
            'cover_days' => $cover_days,
            'nil_cover_days' => $nil_cover_days,
            'currencies' => $currency,
            'travel_currencies' => $this->getTravelcurrencies(),
            'branch' => $this->branches,
            'agent' => $this->agents,
            'types' => $this->types,
            'title' => $this->titles,
            'bank' => $this->banks,
            'bank_code'=>$dcontrol->bank_code,
            'bank_branch'=>$dcontrol->bank_branch_code,
            'client' => $this->clients,
            'class' => $class,
            'ast' => $ast,
            'bustype' => $bus_type,
            'financier' => $financier,
            'todaysdate' => $today,
            'risk_note_no'=>$dcontrol->risk_note_no,
            'external_pol_no'=>$dcontrol->external_pol_no,
            'year' => $year,
            'dept' => $department,
            'period' => $actual_period,
            'last_date' => $dcontrol->period_from,
            'instalplan' => $instalplan,
            'ast_marker' => $dcontrol->ast_marker,
            'nil_types' => $nil_types,
            'nil_risk' => $nil_risk,
            'motor_pol' => $motor_pol[0]->motor_policy,
            'vat_setup' => $vat_setup,
            'cover_period_editable' =>  $cover_period_editable,
            'crmast'=>$crmast,
            'pipstmp' => $pipstmp,
            'facultinClients' => $facultinClients,
            'facultinppws' => $facultinppws,
            'lor'=>$loss_ratio,
            'opencover_master_flag' => $opencover_master_flag,
            'backpost_policy'=> $backdate_policy,
            'opencover_master_flag' => $opencover_master_flag,
            'mac_endt_no' => $mac_endt_no,
            'travel_vat_check' =>$travel_vat_check,
            'installmentstatus' =>  $installmentstatus,
            'travel_code' => $travel_code,
            'ProformaReason' => $ProformaReason,
            'policy_wordings' => $policy_wordings,

            # Extend Installment
            // 'nextInstallmentDetails' => $nextInstallmentDetails,
            'nextInstallment' => $nextInstallment,
            'allowedToBackdatePreviousYears' => $allowedToBackdatePreviousYears,
        ]);
    }

    public function lapsePoliciesPastLapseDays(){

        $pipcnam = Pipcnam::where('record_type',0)->first();

        if ($pipcnam->auto_lapse !== 'Y') {
            return; // Exit the function if auto_lapse is not enabled
        }

        $autoLapseDays=$pipcnam->auto_lapse_days;
        
        $currentDate = Carbon::today();
        $lapseDate = $currentDate->subDays($autoLapseDays);

        // Query to get policies that are past the lapse date
        $policiesPastLapseDate = Polmaster::where('expiry_date', '<', $lapseDate)
                ->where(function ($query) {
                    $query->where('status_code', '<>', 'LPS')
                        ->where('status_code', '<>', 'CNC')
                        ->orWhereNull('status_code');
                })
                ->whereIn('endorse_no', function ($query) {
                    $query->select('endt_renewal_no')
                        ->from('debitmast');
                })
                ->get();

        $data=[];

        //lapse each policy
        foreach ($policiesPastLapseDate as $policy) {
            $dcontrol = Dcontrol::where('endt_renewal_no', $policy->endorse_no)->get();
            $dcontrol=$dcontrol[0];
            $pol_no=$dcontrol->policy_no;
            
            if ($dcontrol) {
                $data['type']="NIL";
                $data['previous_endt_renewal_no']=$dcontrol->endt_renewal_no;
                $data['insured']=$dcontrol->client_number;
                $data['branchpol']=$dcontrol->branch;
                $data['agentpol']=$dcontrol->agent;
                $data['vat_charged']=$dcontrol->vat_code;
                $data['department']=$dcontrol->dept;
                $data['class']=$dcontrol->class;
                $data['ast']=$dcontrol->ast_marker;
                $data['currency']=$dcontrol->currency;
                $data['today_currency']=$dcontrol->currency_rate;
                $data['bustype']=$dcontrol->type_of_bus;
                $data['nil_endorse_type']="9";
                $data['nil_reason']="automatic policy lapse";
                $data['nil_cover_days']=$dcontrol->days_covered;
                $data['nil_period_from']=$dcontrol->period_from;
                $data['nil_period_to']=$dcontrol->period_to;
                $data['nil_t_period_from']=$dcontrol->cov_period_from;
                $data['nil_t_period_to']=$dcontrol->cov_period_to;
                $data['period_from']=$dcontrol->period_from;
                $data['period_to']=$dcontrol->period_to;
                $data['effective_date']=$dcontrol->expiry_date;
                $data['lapse_date']=Carbon::today()->toDateString();
                $data['who_lapsed']="system";

                $effective_date = Carbon::parse($data['effective_date']);
                $period_to = Carbon::parse($data['period_to']);
                $cover_days = $effective_date->diffInDays($period_to) + 1;
                $data['cover_days']=$cover_days;

                $data['system_generated']='Y';

                // Create nil endorsement to lapse policy
                $request = new Request($data);
                $result = $this->create($request);

                //create escalation for nil endorsement
                $new_endorse_no = Polmaster::where('policy_no', $pol_no)->pluck('endorse_no')->first();
                $new_dcontrol= Dcontrol::where('endt_renewal_no', $new_endorse_no)->get();
                $new_dcontrol= $new_dcontrol[0];

                if ($result === 1) {
                    //commit endorsement
                    $commit_data['endt_renewal_no']=$new_dcontrol->endt_renewal_no;
                    $commit_request = new Request($commit_data);

                    $policyFunctionsController = new Policy_functions();
                    $policyFunctionsController->confirm_commit($commit_request);              
                } 
            }


        }


    }


    public function Rns_transaction($request) {

        Gate::authorize('reinstate-policy');
        
        $old_endt_renewal_no = $request->get('rns_endt_no');
        $clnt_no = $request->get('insured');
        $trans_type = $request->get('trans_type');

        //get dcontrol record for endorsement number
        $dcontrol = Dcontrol::where('endt_renewal_no', $old_endt_renewal_no)->first();

        //get the number of days between period from and period_to
        $effective_date = Carbon::parse($dcontrol->effective_date);
        $period_to = Carbon::parse($dcontrol->period_to);

        $cover_days = $effective_date->diffInDays($period_to) + 1;
        $extendfrom = Carbon::today();

        $this->clients = Client::where('client_number', $clnt_no)->get();
        $intermediaryParams = new IntermediaryQueryParams([
            'branch' => $dcontrol->branch
        ]);
        $this->agents  =IntermediaryQueryService::getActiveintermediaryByBranch($intermediaryParams)->get();
      // $this->agents = Agmnf::where('branch', $dcontrol->branch)->get();
        $this->titles = Title::all();
        $this->branches = Branch::all();
        $this->banks = Olbnknames::all();
        $this->types = Reidoc::where('orig', 'U/W')->get(['doc_type', 'entry_type_descr', 'description']);
        $currency = Currency::all();
        $instalplan = Instalplan::all();
        $dpt = (int)$dcontrol->dept;
        $class = ClassModel::where('dept',$dpt)->where('class_active','Y')->get();     
        //$covertype=Covertype::all();
        $ast = Polast::where('active','Y')->get();
        $bus_type = Bustype::all();
        $financier = Financier::all();
        $today = date('Y/m/d');
        $year = date('Y');
        $department=Dept::orderBy('description', 'ASC')->get();
        $account_period = Dtran0::where('rec_no', 0)->get(['account_month', 'account_year']);
        $actual_period = $account_period[0]->account_month . '/' . $account_period[0]->account_year;
        $motor_pol = ClassModel::where('class',$dcontrol->class)->get();

        if($trans_type == 'NIL'){
            

            if($motor_pol[0]->motor_policy == 'Y'){
                $nil_risk = Modtlmast::where('policy_no',$dcontrol->policy_no)->get();
            }else{
                if($motor_pol[0]->combined == 'Y' && $motor_pol[0]->fire_loc_as_default == 'Y'){
                    $nil_risk = Polsect::where('policy_no',$dcontrol->policy_no)->where('class',$motor_pol[0]->cmb_default_loc_class)->get();
                }
                else{
                    $nil_risk = Polsect::where('policy_no',$dcontrol->policy_no)->get();
                }
            }
            $nil_types = Nil_endorsetypes::all();
            // latest transtype
            $cover_period_editable = false;
            $check_REN = Dcontrol::where('policy_no',$dcontrol->policy_no)
                ->where('trans_type', 'REN')
                ->orderBy('dtrans_no','desc')
                ->first();
            if(isset($check_REN)){
                $REN_dtrans_no = $check_REN->dtrans_no;
                $EXT_after_REN = Dcontrol::where('policy_no',$dcontrol->policy_no)
                    ->where('dtrans_no','>', $REN_dtrans_no)
                    ->where('trans_type', 'EXT')
                    ->orderBy('dtrans_no','desc')
                    ->count();
                if($EXT_after_REN == 0){
                    $cover_period_editable = true;
                }
            }
            else{
                $EXT_after_POL = Dcontrol::where('policy_no',$dcontrol->policy_no)
                    ->where('trans_type', 'EXT')
                    ->count();
                if($EXT_after_POL == 0){
                    $cover_period_editable = true;
                }
            }
            $latest_transaction = Dcontrol::where('policy_no',$dcontrol->policy_no)
                ->orderBy('dtrans_no','desc')
                ->first();
            $nil_cover_days = $latest_transaction->days_covered;
        }else{
            $nil_types = array(0,0);
            $nil_risk = array(0,0);
            $nil_cover_days = null;
            $cover_period_editable = false;
        }

        $vat_setup = Vat_setup::all();
        $crmast = DB::select("SELECT LPAD(c.branch, 3, 0) || c.agent branch_agent, c.name FROM crmast c
            INNER JOIN acctype a ON c.account_type = a.acc_type WHERE a.facultative = 'Y'");
        $pipstmp = Pipstmp::first();

        $facultinClients = FacultInClient::select('facultin_client_no', 'name')
                                        ->where('facultin_client_no',$dcontrol->facultin_client_no)
                                        ->get();

        $facultinppws = PPWParam::select('ppw_code', 'ppw_name')->where('ppw_code',$dcontrol->ppw_code)->get();

        $allowedToBackdatePreviousYears = BackdateTransType::where('trans_type', $trans_type)->value('backdate_to_previous_years');
        $travel_code = ClassModel::select('class')->where('travel','Y')->first();
        

        return view('gb.underwriting.reinstatement_policy_form', [
            'trans_type' => $trans_type,
            'incept_date' => $dcontrol->incept_date,
            'period_from' => $dcontrol->period_from,
            'period_to' => $dcontrol->period_to,
            'old_endt_trans' => $dcontrol,
            'cover_days' => $cover_days,
            'nil_cover_days' => $nil_cover_days,
            'currencies' => $currency,
            'travel_currencies' => $this->getTravelcurrencies(),
            'branch' => $this->branches,
            'agent' => $this->agents,
            'types' => $this->types,
            'title' => $this->titles,
            'bank' => $this->banks,
            'bank_code'=>$dcontrol->bank_code,
            'bank_branch'=>$dcontrol->bank_branch_code,
            'client' => $this->clients,
            'class' => $class,
            'ast' => $ast,
            'bustype' => $bus_type,
            'financier' => $financier,
            'todaysdate' => $today,
            'risk_note_no'=>$dcontrol->risk_note_no,
            'external_pol_no'=>$dcontrol->external_pol_no,
            'year' => $year,
            'dept' => $department,
            'period' => $actual_period,
            'last_date' => $dcontrol->period_from,
            'instalplan' => $instalplan,
            'ast_marker' => $dcontrol->ast_marker,
            'nil_types' => $nil_types,
            'nil_risk' => $nil_risk,
            'motor_pol' => $motor_pol[0]->motor_policy,
            'vat_setup' => $vat_setup,
            'cover_period_editable' =>  $cover_period_editable,

            'sys_rns_amt' =>  $request->sys_rns_amt,
            'rns_amt' =>  $request->rns_amt,
            'rns_reason' =>  $request->rns_reason,
            'rns_endt_no' =>  $request->rns_endt_no,
            'rns_pol_no' =>  $request->rns_pol_no,
            'crmast'=>$crmast,
            'pipstmp' => $pipstmp,
            'facultinClients' => $facultinClients,
            'facultinppws' => $facultinppws,
            'allowedToBackdatePreviousYears' => $allowedToBackdatePreviousYears,
            'ProformaReason' => $ProformaReason,
        ]);
    }


    public function Check_endt_pending(Request $request)
    {
        $polno = $request->get('policy_no');
        $username = Auth::user()->user_name;

        $gt_pol = Polmaster::where('policy_no', $polno)->first();

        $cls = ClassModel::where('class', $gt_pol->class)->first();

        if ($gt_pol->trans_type != 'NIL') {
            $count_debitmast = Debitmast::where('endt_renewal_no', $gt_pol->endorse_no)->count();

            if($cls->open_cover == 'Y' && $count_debitmast==0){

                //$count_debitmast= MarineUTLDebit::where('endt_renewal_no', $gt_pol->endorse_no)->groupBy('endt_renewal_no')->count();
                $rec = DB::select("SELECT COUNT(*) as CNT FROM (SELECT ENDT_RENEWAL_NO,COUNT(*) FROM MARINE_UTL_DEBITS WHERE ENDT_RENEWAL_NO='".$gt_pol->endorse_no."' GROUP BY ENDT_RENEWAL_NO)");
                $count_debitmast = (int)$rec[0]->cnt;

                if($count_debitmast == 0){

                    $marine = Marinemasterpol::where('policy_no', $gt_pol->policy_no)->first();

                    $count_debitmast = ($marine->commit_transaction == 'Y' && in_array($marine->opentype_code, ['CMC','ZPR']) && $marine->endt_renewal_no == $gt_pol->endorse_no) ? 1 : $count_debitmast ;

                }
                
            }

            echo $count_debitmast;
            // echo $endorsement;
        } else {
            $count_debitmast = 1;
            // echo $endorsement;

            echo $count_debitmast;
        }
    }

    public function Ren_transaction($request)
    {
        Gate::authorize('renew-policy');
        
        $email = Auth::user()->email;
        $trans_type = (string) $request->get('trans_type');
        $pipcnam = Pipcnam::where('record_type',0)->first();

        //check if user can backdate policy
        $transTypeId = BackdateTransType::where('trans_type', $trans_type)->value('id');

        $renewalPostdateDays = BackdateTransType::where('trans_type', $trans_type)->value('max_postdate_days') ?? 120;

        $backdate_count = Backdate::where('user_email', $email)->where('backdate_trans_type_id', $transTypeId)->count();
        
        if ($backdate_count > 0) {

            $backdate = Backdate::where('user_email', $email)->where('backdate_trans_type_id', $transTypeId)->get();
            $backdate = $backdate[0];

            $last_date = Carbon::now()->subDays($backdate->number_of_days);
        } else {

            $last_date = Carbon::now();
        }

        $backpost_policy = $this->back_post_date($email, $trans_type);

        $endt_renewal_no = $request->get('endt_renewal_no');
        $clnt_no = $request->get('client');

        //get dcontrol record for endorsement number
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $dcontrol_pol = Dcontrol::where('endt_renewal_no', $dcontrol->policy_no)->first();

        $cls = ClassModel::where('class',$dcontrol->class)->first();

        # Cover Days
        $effective_date = Carbon::parse($dcontrol_pol->effective_date);
        $period_to = Carbon::parse($dcontrol_pol->period_to);

        # Get AST Marker
        switch ($dcontrol->ast_marker) {
            case 'A':
                # Check If Renewal Year is Leap Year or Not
                $renewalDate = Carbon::parse($dcontrol_pol->renewal_date);
                $isLeapYear = $renewalDate->format('L'); 
                $month = $renewalDate->format('m');

                if ($isLeapYear == 0) {
                    $cover_days = 365;
                } else {
                    if ($month > 02) {
                        $cover_days = 365;
                    } else {
                        $cover_days = 366;
                    }
                }
            break;

            case 'T':
                # Check If Renewal Year is Leap Year or Not
                $renewalDate = Carbon::parse($dcontrol_pol->renewal_date);
                $isLeapYear = $renewalDate->format('L'); 
                $month = $renewalDate->format('m');

                if ($isLeapYear == 0) {
                    $cover_days = 365;
                } else {
                    if ($month > 02) {
                        $cover_days = 365;
                    } else {
                        $cover_days = 366;
                    }
                }
            break;

            case 'S':
                if ($dcontrol_pol->short_term_method == 'P') {
                    $cover_days = $effective_date->diffInDays($period_to);
                    $cover_days = $dcontrol_pol->days_covered;
                } else {
                    # Check If Renewal Year is Leap Year or Not
                    $renewalDate = Carbon::parse($dcontrol_pol->renewal_date);
                    $isLeapYear = $renewalDate->format('L'); 
                    $month = $renewalDate->format('m');

                    if ($isLeapYear == 0) {
                        $cover_days = 365;
                    } else {
                        if ($month > 02) {
                            $cover_days = 365;
                        } else {
                            $cover_days = 366;
                        }
                    }
                }
            break;
            
            default:
                $cover_days = $effective_date->diffInDays($period_to);
                $cover_days = $dcontrol_pol->days_covered;
            break;
        }

        $this->clients = Client::where('client_number', $clnt_no)->get();
       // $this->agents = Agmnf::where('branch', $dcontrol->branch)->get();
        $intermediaryParams = new IntermediaryQueryParams([
            'branch' => $dcontrol->branch
        ]);
        $this->agents  =IntermediaryQueryService::getActiveintermediaryByBranch($intermediaryParams)->get();
        $this->titles = Title::all();
        $this->branches = Branch::all();
        $this->banks = Olbnknames::all();
        $this->types = Reidoc::where('orig', 'U/W')->get(['doc_type', 'entry_type_descr', 'description']);
        $currency = Currency::all();
        $dpt = (int)$dcontrol->dept;
        $class = ClassModel::where('dept',$dpt)->where('class_active','Y')->get();     

        //$covertype=Covertype::all();
        $ast = Polast::where('active','Y')->get();
        $bus_type = Bustype::all();
        $financier = Financier::all();
        $today = date('Y');
        $department=Dept::orderBy('description', 'ASC')->get();
        $account_period = Dtran0::where('rec_no', 0)->get(['account_month', 'account_year']);
        $actual_period = $account_period[0]->account_month . '/' . $account_period[0]->account_year;

        $new_period_from = Carbon::parse($dcontrol->renewal_date);

        if($cls->exem == "Y"){
            $exem_cover_days = (string)($cover_days - 1);      
            $renewal_period_to = $new_period_from->addDays($exem_cover_days);
        } else{
            $renewal_period_to = $new_period_from->addDays($cover_days);
        }
        
        $instalplan = Instalplan::all();

        $vat_setup = Vat_setup::all();
        $crmast = DB::select("SELECT LPAD(c.branch, 3, 0) || c.agent branch_agent, c.name FROM crmast c
            INNER JOIN acctype a ON c.account_type = a.acc_type WHERE a.facultative = 'Y'");

        /** CHECK RENEWAL POST DATING **/
        //$pipcnam->postdate_ren_days-1
        $ren_post_date_cap = Carbon::parse($dcontrol->renewal_date)->addDays($renewalPostdateDays);

        /** END CHECK RENEWAL POST DATING **/

        $facultinClients = FacultInClient::select('facultin_client_no', 'name')
                                        ->where('facultin_client_no', $dcontrol->facultin_client_no)
                                        ->get();
         $facultinppws = PPWParam::select('ppw_code', 'ppw_name')->where('ppw_code',$dcontrol->ppw_code)->get();
        ##loss ration
        $clampaid=Clhmn::where('policy_no',$dcontrol->policy_no)->sum('cost_todate');
        $clamest=Clhmn::where('policy_no',$dcontrol->policy_no)->sum('curr_total_estimate');
        $out_bal=Acdet::where('policy_no',$dcontrol->policy_no)->sum('unallocated');
        $gross_premium=Debitmast::where('policy_no',$dcontrol->policy_no)->sum('gross_amount');
        
        if($gross_premium==0 ){
            $lor = 0;
        }else{
            $lor = (($clampaid+$clamest) / $gross_premium) * 100 ;
        }
        $loss_ratio  = round($lor,2) . ' %';
        $travel_vat_check = Pipcnam::select('exem_vat_on_travel')->first();
        $travel_code = ClassModel::select('class')->where('travel','Y')->first();

        ##end loss atio
        $allowedToBackdatePreviousYears = BackdateTransType::where('trans_type', $trans_type)->value('backdate_to_previous_years');

        $policy_wordings = ProductPolWording::where('class', $dcontrol->class)
        ->join('policydocuments', 'product_pol_wordings.policydocid', '=', 'policydocuments.id')
        ->select('product_pol_wordings.heading', 'policydocuments.document_description', 'product_pol_wordings.policydocid')
        ->get();
    
        return view('gb.underwriting.policy_form', [
            'trans_type' => $trans_type,
            'incept_date' => $dcontrol->incept_date,
            'period_from' => $dcontrol->period_from,
            'period_to' => $dcontrol->period_to,
            'renewal_period_to' => $renewal_period_to,
            'old_endt_trans' => $dcontrol,
            'cover_days' => $cover_days,
            'currencies' => $currency,
            'travel_currencies' => $this->getTravelcurrencies(),
            'branch' => $this->branches,
            'agent' => $this->agents,
            'types' => $this->types,
            'title' => $this->titles,
            'bank' => $this->banks,
            'client' => $this->clients,
            'class' => $class,
            'ast' => $ast,
            'bustype' => $bus_type,
            'risk_note_no'=>$dcontrol->risk_note_no,
            'external_pol_no'=>$dcontrol->external_pol_no,
            'financier' => $financier,
            'todaysdate' => $today,
            'dept' => $department,
            'period' => $actual_period,
            'last_date' => $last_date,
            'instalplan' => $instalplan,
            'ast_marker' => $dcontrol->ast_marker,
            'vat_setup' => $vat_setup, 
            'crmast'=> $crmast,
            'postdate_ren'=> $pipcnam->postdate_ren,
            'ren_post_date_cap'=> $ren_post_date_cap,
            'backpost_policy' => $backpost_policy,
            'facultinClients' => $facultinClients,
            'facultinppws' => $facultinppws,
            'lor'=>$loss_ratio,
            'backpost_policy' => $backpost_policy,
            'travel_vat_check' =>$travel_vat_check,
            'travel_code' => $travel_code,
            'allowedToBackdatePreviousYears' => $allowedToBackdatePreviousYears,
            'ProformaReason' => $ProformaReason,
            'policy_wordings' =>$policy_wordings

            
        ]);
    }
    public function Edit_transaction($request)
    {

        $old_endt_renewal_no = $request->get('endt_renewal_no');
        $clnt_no = $request->get('client');
        $trans_type = (string) $request->get('trans_type');
        

        //get dcontrol record for endorsement number
        $dcontrol = Dcontrol::where('endt_renewal_no', $old_endt_renewal_no)->get();
        $dcontrol = $dcontrol[0];


        //get the number of days between period from and period_to
        // $cover_days = $dcontrol->days_covered;
        $effective_date = Carbon::parse($dcontrol->effective_date);
        $period_to = Carbon::parse($dcontrol->period_to);
        $cover_days = $effective_date->diffInDays($period_to);
        
        $new_period_from = new Carbon($dcontrol->renewal_date);
        $cls = ClassModel::where('class',$dcontrol->class)->first();

        if($cls->exem == "Y"){
            $cover_days = (string)($cover_days - 1);      
        }
        
        // $diff = date_diff(date_create($dcontrol->period_from), date_create($dcontrol->period_to));
        // $cover_days = $diff->days;


        $this->clients = Client::where('client_number', $clnt_no)->get();
       // $this->agents = Agmnf::where('branch', $dcontrol->branch)->get();
        $intermediaryParams = new IntermediaryQueryParams([
            'branch' => $dcontrol->branch
        ]);
        $this->agents  =IntermediaryQueryService::getActiveintermediaryByBranch($intermediaryParams)->get();
        $this->titles = Title::all();
        $this->branches = Branch::all();
        $this->banks = Olbnknames::all();
        $this->types = Reidoc::where('orig', 'U/W')->get(['doc_type', 'entry_type_descr', 'description']);
        $currency = Currency::all();
        $instalplan = Instalplan::all();

        $class = ClassModel::all();
        //$covertype=Covertype::all();
        $annual = Polast::where('ast_marker', 'A')->get();
        $ast = Polast::where('active','Y')->get();
        $bus_type = Bustype::all();
        $financier = Financier::all();
        $today = date('Y');
        $department=Dept::orderBy('description', 'ASC')->get();
        $account_period = Dtran0::where('rec_no', 0)->get(['account_month', 'account_year']);
        $actual_period = $account_period[0]->account_month . '/' . $account_period[0]->account_year;

        $vat_setup = Vat_setup::all();
        $crmast = DB::select("SELECT LPAD(c.branch, 3, 0) || c.agent branch_agent, c.name FROM crmast c
            INNER JOIN acctype a ON c.account_type = a.acc_type WHERE a.facultative = 'Y'");
        $dcon_no = Dcontrol::where('policy_no', $dcontrol->policy_no)
            ->where('class', $dcontrol->class)
            ->where('endt_renewal_no', '<>', $old_endt_renewal_no)
            ->whereNull('delete_str')
            ->max('dcon_no');

        $dcontrolOld = Dcontrol::where('policy_no', $dcontrol->policy_no)
                ->where('class', $dcontrol->class)
                ->where('dcon_no', $dcon_no)
                ->first();
        $pipstmp = Pipstmp::first();
        //return $this->branches;
        //return $dcontrol->bank_branch_code;

        $facultinClients = FacultInClient::select('facultin_client_no', 'name')
            ->where('facultin_client_no', $dcontrol->facultin_client_no)
            ->get();

        $facultinppws = PPWParam::select('ppw_code', 'ppw_name')->where('ppw_code',$dcontrol->ppw_code)->get();
        $travel_vat_check = Pipcnam::select('exem_vat_on_travel')->first();
        
        return view('gb.underwriting.policy_form', [
            'trans_type' => $trans_type,
            'incept_date' => $dcontrol->incept_date,
            'period_from' => $dcontrol->period_from,
            'period_to' => $dcontrol->period_to,
            'old_endt_trans' => $dcontrol,
            'prev_endt_trans' => $dcontrolOld,
            'cover_days' => $cover_days,
            'currencies' => $currency,
            'travel_currencies' => $this->getTravelcurrencies(),
            'external_pol_no' => $dcontrol->external_pol_no,
            'risk_note_no' => $dcontrol->risk_note_no,
            'branch' => $this->branches,
            'agent' => $this->agents,
            'bank' => $dcontrol->bank_code,
            'bank_branch_code' => $dcontrol->bank_branch_code,
            'types' => $this->types,
            'title' => $this->titles,
            'bank' => $this->banks,
            'client' => $this->clients,
            'class' => $class,
            'ast' => $ast,
            'annual' => $annual,
            'bustype' => $bus_type,
            'financier' => $financier,
            'todaysdate' => $today,
            'dept' => $department,
            'period' => $actual_period,
            'last_date' => $dcontrol->period_from,
            'instalplan' => $instalplan,
            'ast_marker' => $dcontrol->ast_marker,
            'vat_setup' => $vat_setup,
            'old_pol'=>$dcontrol->renew_old_policy,
            'onboard_pol'=>$dcontrol->onboard_old_policy,
            'old_pol_no'=>$dcontrol->old_policy_no,
            'apply_admin_fees'=>$dcontrol->apply_admin_fees,
            'admin_fees_rate'=>$dcontrol->admin_fees_rate,
            'crmast'=>$crmast,
            'pipstmp' => $pipstmp,
            'facultinClients' => $facultinClients,
            'facultinppws' => $facultinppws,
            'travel_vat_check' =>$travel_vat_check,
            'ProformaReason' => $ProformaReason,
        ]);
    }


    public function Edit_policy(Request $request)
    {

        DB::beginTransaction();
        try {
            $old_endt_renewal_no = $request->get('previous_endt_renewal_no');
            $dcontrol = Dcontrol::where('endt_renewal_no', $old_endt_renewal_no)->first();
            $cls = ClassModel::where('class', $request->input('class'))->first();
            $transaction_type = $dcontrol->trans_type;
            $facult_in=Bustype::where('type_of_bus',$request->bustype)->get('facult_in');
            $ppw = PPWParam::where('ppw_code',$request->facinppw)->first();
            if($transaction_type=='POL' || $transaction_type == 'EXT' || $transaction_type == 'REN' || $transaction_type == 'RNS'){
                $validate = Validator::make($request->all(), [
                    'insured'           => 'required',
                    'branchpol'         => 'required',
                    'agentpol'          => 'required',
                    'bustype'           => 'required',
                    'currency'          => 'required',
                    'ast'               => 'required',
                    'co_ins_share'      => 'required',
                    'cover_days'        => 'required',
                    'incept_date'       => 'required|date|date_format:Y-m-d|before:period_to',
                    'effective_date'    => 'required|date|date_format:Y-m-d|before:period_to',
                    'period_from'       => 'required|date|date_format:Y-m-d|before:period_to',
                    'ext_from'          => 'required|date|date_format:Y-m-d|before:period_to',
                    'period_to'         => 'required|date|date_format:Y-m-d|after:period_from',
                    'ext_to'            => 'required|date|date_format:Y-m-d|after:period_from',
                ]);
                if ($validate->fails()){
                    return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all required fields')->withErrors($validate);
                }

                if ($request->ast == 'T') {
                    # Time on Risk Validation
                    $validate = Validator::make($request->all(), [
                        't_period_from'   => 'required',
                        't_period_to'      => 'required |date|date_format:Y-m-d|before:period_to',
                        't_cover_days'           => 'required',
                    ]);

                    if ($validate->fails()){
                        return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all time on risk required fields')->withErrors($validate);
                    }
                } else if ($request->ast == 'S' && $request->prem_method == 'S') {
                    # Short Term Percentage Method Validation
                    $validate = Validator::make($request->all(), [
                        't_period_from'   => 'required',
                        't_period_to'     => 'required',
                        't_cover_days'    => 'required',    
                    ]);

                    if ($validate->fails()){
                        return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all short term percentage method required fields')->withErrors($validate);
                    }
                } else if ($request->facin_flag == 'Y' && $cls->combined != 'Y') {
                    # Facultative Inward Validation
                    $validate = Validator::make($request->all(), [
                        'facin_total_sum_insured'   => 'required',
                        'all_premium'     => 'required',
                        'our_share'    => 'required',
                        'facin_comm_rate'    => 'required' ,    
                        'facin_sum_insured'    => 'required',    
                        'facin_premium'    => 'required',    
                        'facin_comm_amt'    => 'required' 
                    ]);
    
                    if ($validate->fails()){
                        return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all facultaive inward required fields');
                    }
                }
            }

            $clnt_no = $request->input('insured');

            $bustype_curr = Bustype::where('type_of_bus', trim($request->input('bustype')))->first();

            $charge_vat = Pipcnam::where('record_type', 0)->get(['charge_vat']);
            $charge_vat = $charge_vat[0]->charge_vat;

            if($charge_vat == 'Y'){
                $get_vat_setup = Vat_setup::where('vat_code',(int)$request->input('vat_charged'))->get();
                $get_vat_setup = $get_vat_setup[0];
            }

            $dcontrol->branch = $request->input('branchpol');
            $dcontrol->agent = $request->input('agentpol');
            $dcontrol->apply_admin_fees = $request->apply_admin_fees;
            $dcontrol->admin_fees_rate = $request->admin_rate;
            $dcontrol->renew_old_policy = $request->input('edit_renew_old_pol');
            $dcontrol->old_policy_no = $request->input('edit_old_pol');
            // $dcontrol->class=$request->input('class');
            $dcontrol->user_str = (string) $request->User()->user_name;
            if($cls->exem == "Y"){
                $renewal = new DateTime((string) $request->input('period_to'));
                $renewal->modify('+1 day');
            }
            else{
                $renewal = new DateTime((string) $request->input('period_to'));
            }
            
            if($transaction_type == 'POL') {
                $dcontrol->incept_date = $request->input('incept_date');
                
            }
            switch ($transaction_type) {
                case 'POL':
                case 'REN':
                case 'RNS':
                case 'PTA':
                    $dcontrol->cov_period_from=$request->input('period_from');
                    $dcontrol->cov_period_to=$request->input('period_to');
                    $dcontrol->expiry_date = $request->input('period_to');
                    $dcontrol->renewal_date = $renewal;
                    break;
                default:
                    
                    break;
            }
            if(trim($request->input('ast')) == 'T'){
                $dcontrol->period_from = $request->input('t_period_from');
                $dcontrol->period_to =$request->input('t_period_to');
                $dcontrol->cov_period_from = $request->input('period_from');
                $dcontrol->cov_period_to = $request->input('period_to');
                $dcontrol->endt_days = $request->input('t_cover_days');
            }
            else if(trim($request->input('ast')) == 'S' && trim($request->input('prem_method')) == 'S'){
                $dcontrol->period_from=$request->input('t_period_from');
                $dcontrol->period_to=$request->input('t_period_to');
                $dcontrol->cov_period_from=$request->input('period_from');
                $dcontrol->cov_period_to=$request->input('period_to');
                $dcontrol->endt_days = $request->input('t_cover_days');
            }else{
                $dcontrol->period_from=$request->input('period_from');
                $dcontrol->period_to=$request->input('period_to');
            }
  
            $dcontrol->days_covered = $request->input('cover_days');
            $dcontrol->effective_date = $request->input('effective_date');
            $dcontrol->endorse_date = $request->input('effective_date');
            $dcontrol->branch_code = str_pad($request->input('branchpol'), 3, "0", STR_PAD_LEFT);
            $dcontrol->co_insure = $request->input('co_ins');
            $dcontrol->company_share = $request->input('co_ins_share');
            $dcontrol->co_ins_rate = $request->input('co_ins_rate');
            $dcontrol->co_ins_base = $request->input('co_ins_base');
            $dcontrol->type_of_bus = $request->input('bustype');
            // $dcontrol->dept=$request->input('department');
            $dcontrol->actual_period_from = $request->input('period_from');
            $dcontrol->actual_period_to = $request->input('period_to');
            $dcontrol->financed = $request->input('financed');
            $dcontrol->ipf = $request->input('ipf');
            $dcontrol->onboard_old_policy = $request->edit_onboard_old_pol;      
            $pipcnam_plan = Pipcnam::first()->predebit_plan;

            if ($pipcnam_plan == 'Y') {
                $dcontrol->apply_payment_plan =$request->input('payment_plan');
            }

            $dcontrol->ast_marker = $request->input('ast');
            $dcontrol->items_total = 1;
            $dcontrol->branch_cod = $request->input('branchpol');
            $dcontrol->ext_from = $request->input('ext_from');
            $dcontrol->ext_to = $request->input('ext_to');
            $dcontrol->currency = $request->input('currency');
            $dcontrol->currency_rate = str_replace(',','',$request->input('today_currency'));
            if($bustype_curr->facult_in == 'Y'){
                $get_name = DB::select("SELECT LPAD(c.branch, 3, 0) || c.agent branch_agent, c.name FROM crmast c
                    INNER JOIN acctype a ON c.account_type = a.acc_type WHERE a.facultative = 'Y'
                    AND LPAD(c.branch, 3, 0) || c.agent = $request->facin_lead_agent")[0];
                $dcontrol->facin_lead_agent = $get_name->name;
                $dcontrol->facin_branch_agent = $request->facin_lead_agent;
                $dcontrol->facin_comm_amt = str_replace(',','',$request->facin_comm_amt);
                $dcontrol->facin_total_sum_insured = str_replace(',','',$request->facin_total_sum_insured);
                $dcontrol->facin_sum_insured = str_replace(',','',$request->facin_sum_insured);
                $dcontrol->facin_premium = str_replace(',','',$request->facin_premium);
                $dcontrol->facin_comm_rate = $request->facin_comm_rate;
                $dcontrol->facin_premium_rate = $request->our_share;
                $dcontrol->company_share = $request->our_share;
                $dcontrol->facin_total_premium = str_replace(',','',$request->all_premium);
                $dcontrol->facultin_client_no = $request->facinclient;
                $dcontrol->ppw_code = $request->facinppw;
                $dcontrol->ppw_days = $ppw->ppw_days;
            }
            if($bustype_curr->fronting == 'Y'){
                $dcontrol->ira_rate = $request->ira_rate;
                $dcontrol->ipec_levy_loading_amount = $request->ipec_levy_loading_amount;
                $dcontrol->fronting_rate = $request->fronting_rate;
                $dcontrol->reinsurer_fronting_rate = $request->reinsurer_fronting_rate;
                $dcontrol->fronting_stampduty_basis = $request->fronting_stampduty_basis;
                $dcontrol->comm_rate = $request->fronting_comm_rate;
            }
            if ($request->input('financed') == 'Y') {
                $dcontrol->financed_code = $request->input('financier');
            } else {
                $dcontrol->financed_code = '';
            }

            if($cls->travel == 'Y'){
                $dcontrol->conv_rate = $request->input('curr_conv_rate');
            };

            if($charge_vat == 'Y'){
                $dcontrol->vat_type = $get_vat_setup->vat_type;
                $dcontrol->vat_description = $get_vat_setup->vat_description;
                $dcontrol->vat_rate = $get_vat_setup->vat_rate;
                $dcontrol->vat_code = $request->input('vat_charged');
            }else{
                $dcontrol->vat_rate = 0;
            }

            $dcontrol->save();

            $dcntrl = Dcontrol::where('endt_renewal_no', $old_endt_renewal_no)->get();
            //validate cover periods
            $validCover = $this->validateCovPeriod($dcntrl[0],'EDIT');
            

            // foreach()
            switch($transaction_type){
                case 'RFN':
                case 'CNC':
                case 'PTA':
                case 'CXT':
                case 'NIL':
                    $pol_uw =Polmaster::where('policy_no',$dcntrl[0]->policy_no)->first();
                    $uw_year = $pol_uw->uw_year;
                    $this->uw_year = $uw_year;
                    break;
                default:
                    $uw_year = date('Y', strtotime((string) $request->input('period_from')));
                    $this->uw_year = $uw_year;
                    break;
                    
            }

            $this->modify_polmaster((string) $old_endt_renewal_no, $uw_year);

            //return redirect()->route('endorse_functions');

            $policy_no = $dcntrl[0]->policy_no;

            $polmaster = Polmaster::where('policy_no', $policy_no)->get();
            $client = Client::where('client_number', $polmaster[0]->client_number)->get();

            $all_endorsements = Dcontrol::where('policy_no', $policy_no)->get();

            $class = ClassModel::where('class', $polmaster[0]->class)->get();

        
            switch ($class[0]->motor_policy) {

                case 'Y':
                    $motor = 'Y';
                    break;

                default:
                    $motor = 'N';
                    break;
            }

            DB::commit();
            return redirect()->route('endorse_functions', ['policy_no' => $polmaster[0]->policy_no, 'uw_year' => $uw_year])->with('success', 'The endorsement '. formatPolicyOrClaim($dcontrol->endt_renewal_no) ." was successfully edited");
        }
        catch(\Throwable $e){
            DB::rollback();
            // dd($e);
            if($e->getCode() == ResponseCode::HTTP_EXPECTATION_FAILED){
                Session::Flash('error', $e->getMessage());
            }
            else{
                Session::Flash('error', 'Failed to edit endorsement '. formatPolicyOrClaim($dcontrol->endt_renewal_no));
            }
            return redirect()->back();
        }


        /* return view('gb.underwriting.policy_form',[
            'trans_type'=>'EDIT',
            'incept_date'=>$dcontrol->incept_date,
            'period_from'=>$dcontrol->period_from,
            'period_to'=>$dcontrol->period_to,
            'old_endt_trans'=>$dcontrol,
            'cover_days'=>$cover_days,
            'currencies'=>$currency,
            'branch'=>$this->branches,
            'agent'=>$this->agents,
            'types'=>$this->types,
            'title'=>$this->titles,
            'bank'=>$this->banks,
            'client'=>$this->clients,
            'class'=>$class,
            'ast'=>$ast,
            'bustype'=>$bus_type,
            'financier'=>$financier,
            'todaysdate'=>$today,
            'dept'=>$department,
            'period'=>$actual_period,
            'last_date'=>$dcontrol->period_from,
            'instalplan'=>$instalplan,
            'ast_marker'=>$dcontrol->ast_marker

            ]); */
    }



    public function Cnc_transaction($request)
    { }

    public function Rfn_transaction($request)
    { }

    public function insured_branch_agents(Request $request)
    {

        $clnt_no = $request->get('client');
        $branch_code = Client::where('client_number', $clnt_no)->get(['branch']);

        $branch_agents = Branch_agent_join::where('branch', $branch_code[0]->branch)->get();

        echo $branch_agents;
    }

    public function generate_pol($branch, $bus_class, $endt_type, $year, $month)
    {
        $policy_code = '';
        $serial = '';
        $serial_string = '';

        $comp_class = ClassModel::where('class', $bus_class)->first()->company_class_code;
        $branch = Branch::where('branch', $branch)->first()->branch_code;        
        $pipcnam = Pipcnam::where('record_type', 0)->first();
        $classbr = Classbr::where('class', $comp_class)->first();

        switch ($endt_type) {
            case 'POL':
                
                $policy_code = $pipcnam->policy_code;
                $serial = $classbr->policy_serial;
                $serial_string = 'policy_serial';
                
                break;

            case 'EXT':
            case 'CXT':
            case 'STK':
                
                $policy_code = $pipcnam->extra_code;
                $serial = $classbr->extra_serial;
                $serial_string = 'extra_serial';
                
                break;
            
            case 'NIL':
                
                $policy_code = $pipcnam->nil_code;
                $serial = $classbr->nil_serial;
                $serial_string = 'nil_serial';
                
                break;

            case 'RFN':
                
                $policy_code = $pipcnam->refund_code;
                $serial = $classbr->refund_serial;
                $serial_string = 'refund_serial';
                
                break;

            case 'CNC':
                
                $policy_code = $pipcnam->cancel_code;
                $serial = $classbr->cancel_serial;
                $serial_string = 'cancel_serial';
                
                break;

            case 'INS':
            case 'REN':

                $policy_code = $pipcnam->renewal_code;
                $serial = $classbr->renewal_serial;
                $serial_string = 'renewal_serial';
                
                break;
            case 'PTA':

                $policy_code = $pipcnam->pta_code;
                $serial = $classbr->pta_serial;
                $serial_string = 'pta_serial';

                break;
            case 'RNS':

                $policy_code = $pipcnam->reins_code;
                $serial = $classbr->reins_serial;
                $serial_string = 'reins_serial';

                break;
            case 'MAC':

                $policy_code = $pipcnam->mac_code;
                $serial = $classbr->mac_serial;
                $serial_string = 'mac_serial';

                break;

        }

        $new_serial = Classbr::where('class', $comp_class)->increment($serial_string, (int) '1');

        $branch = str_pad($branch, 3, "0", STR_PAD_LEFT);
        $class = str_pad($bus_class, 3, "0", STR_PAD_LEFT);
        $month = str_pad($month, 2, "0", STR_PAD_LEFT);
        $serial = str_pad($serial, 6, "0", STR_PAD_LEFT);

        $policy_number = $branch . $class . $policy_code . $serial . $year . $month;

        return $policy_number;
    }

    public function create(Request $request)
    {
        // dd($request->all());
        $cls = ClassModel::where('class', $request->input('class'))->first();
        $pip = Pipcnam::where('record_type', 0)->first();
        
        if((string)$request->input('type')=='POL' || (string)$request->input('type') == 'EXT' || (string)$request->input('type') == 'INS' || (string)$request->input('type') == 'REN' || (string)$request->input('type') == 'RNS'){
            $validate = Validator::make($request->all(), [
                'insured'           => 'required',
                'branchpol'         => 'required',
                'agentpol'          => 'required',
                'bustype'           => 'required',
                'currency'          => 'required',
                'department'        => 'required',
                'class'             => 'required',
                'ast'               => 'required',
                'co_ins_share'      => 'required',
                'cover_days'        => 'required',
                'incept_date'       => 'required|date|date_format:Y-m-d|before:period_to',
                'effective_date'    => 'required|date|date_format:Y-m-d|before:period_to',
                'period_from'       => 'required|date|date_format:Y-m-d|before:period_to',
                'ext_from'          => 'required|date|date_format:Y-m-d|before:period_to',
                'period_to'         => 'required|date|date_format:Y-m-d|after:period_from',
                'ext_to'            => 'required|date|date_format:Y-m-d|after:period_from',
                //'bp_request_no'     => 'required_if:check_for_backpost_date,Y'
               

            ]);

            if ($validate->fails()){
                return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all required fields')->withErrors($validate);
            }

            if($cls->travel == 'Y'){

                $validate = Validator::make($request->all(), [
                    'curr_conv_rate'   => 'required',
                ]);
                
                if ($validate->fails()){
                    return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all time on risk required fields')->withErrors($validate);
                }
            }

            if ($request->ast == 'T') {
                # Time on Risk Validation
                $validate = Validator::make($request->all(), [
                    't_period_from'   => 'required',
                    't_period_to'      => 'required',
                    't_cover_days'           => 'required',
                ]);

                if ($validate->fails()){
                    return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all time on risk required fields')->withErrors($validate);
                }
            } else if ($request->ast == 'S' && $request->prem_method == 'S') {
                # Short Term Percentage Method Validation
                $validate = Validator::make($request->all(), [
                    't_period_from'   => 'required',
                    't_period_to'     => 'required',
                    't_cover_days'    => 'required',
                    'prem_percent'    => 'required'     
                ]);

                if ($validate->fails()){
                    return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all short term percentage method required fields')->withErrors($validate);
                }
            }else if ($request->facin_flag == 'Y' && $cls->combined != 'Y') {
                # Facultative Inward Validation
                $validate = Validator::make($request->all(), [
                    'facin_total_sum_insured'   => 'required',
                    'all_premium'     => 'required',
                    'our_share'    => 'required',
                    'facin_comm_rate'    => 'required' ,    
                    'facin_sum_insured'    => 'required',    
                    'facin_premium'    => 'required',    
                    'facin_comm_amt'    => 'required',
                    'facinppw'   => 'required',

                ]);

                if ($validate->fails()){
                    return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all facultaive inward required fields');
                }
            }

            if ($request->bustype == 4) {
                if ($pip->facultative_inward_client !== 'N') {
                    $validate = Validator::make($request->all(), [
                        'facinclient'   => 'required',
                    ]);

                    if ($validate->fails()) {
                        return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly select the facultative inwards client to proceed')->withErrors($validate);
                    }
                }
            }

            if($request->input('check_for_backpost_date') == 'Y'){
                $check_bp = Validator::make($request->all(), [
                    'bp_request_no'   => 'required'   
                ]);

                if ($check_bp->fails()){
                    return redirect()->back()->with('error', 'Backdating / Postdating beyond Limit requires approved request')->withErrors($validate);
                }
                
            }
        }
 
        //MAC Validator
        if((string)$request->input('type')=='MAC'){
            $validate = Validator::make($request->all(), [
                    'mac_period_from'   => 'required',
                    'mac_period_to'     => 'required',
                    'mac_cover_days'    => 'required'    
                ]);

                if ($validate->fails()){
                    return redirect()->back()->with('error', 'Failed to create endorsement. Some required fields are missing')->withErrors($validate);
                }
        }

        //end policy form validator //end
        

        //CHECK UNDEBITED TRANSACTIONS
        if((string)$request->input('type') != 'POL'){

            $polno = Dcontrol::where('endt_renewal_no',$request->previous_endt_renewal_no)->first();

            $cnt_dcon = Dcontrol::where('policy_no',$polno->policy_no)
                            ->where('trans_type','<>','NIL')
                            ->whereNull('delete_str')
                            ->count();

            $cnt_marine_cmc = Marinemasterhist::where('policy_no',$polno->policy_no)
                                              ->whereIn('opentype_code',['CMC','ZPR'])
                                              ->where('commit_transaction','Y')
                                              ->where('status','ACT')
                                              ->count();

            $cnt_dbt = Debitmast::where('policy_no',$polno->policy_no)
                            ->count();

            $marine_count = DB::select("select count(*) as cnt from (select endt_renewal_no, count(*) from MARINE_UTL_DEBITS where policy_no='$polno->policy_no' group by endt_renewal_no)");

            $cnt_dcon = $cnt_dcon - $cnt_marine_cmc;

            $total_dbt = (int)$marine_count[0]->cnt + $cnt_dbt;
            
            $pipcnamrec = Pipcnam::where('record_type', 0)->first();
            $cnc_proforma_on_lps = $pipcnamrec->cnc_proforma_on_lps;
            
            if($cnt_dcon != $total_dbt && $cnc_proforma_on_lps=='Y' && $request->nil_endorse_type ==9){
                $polno = Dcontrol::where('endt_renewal_no',$request->previous_endt_renewal_no)->first();
                $polrec = Polmaster::where('policy_no',$polno->policy_no)->first();
                
                $cnc_data['endt_renewal_no']=$polno->endt_renewal_no;
                $cnc_data['policy_no']=$polno->policy_no;
                $cnc_data['policy_uw_year']=$polrec->uw_year;
                $cnc_data['cancellationReason']="Cancellation of proforma on lapse";

                $cnc_request = new Request($cnc_data);

                $policyFunctionsController = new Policy_functions();
                $policyFunctionsController->deleteEndorsement($cnc_request);  

            }elseif($cnt_dcon != $total_dbt){
                
                //return redirect()->back()->with('error', 'Cannot proceed. There is an undebited transaction.');

                Session::flash('error', 'Cannot proceed. There is an undebited transaction.');

                return redirect()->route('endorse_functions', ['policy_no' => $polno->policy_no]);

            }
        }

        //CHECK IF INTERMEDIARY IS EXPIRED AND PAST GRACE PERIOD for(POL, REN, RNS)
        if((string)$request->input('type')=='POL' || (string)$request->input('type') == 'REN' || (string)$request->input('type') == 'RNS'){
            $licence_data['acctype']=$request->acctype;
            $licence_data['branch']=$request->branchpol;
            $licence_data['agent']=$request->agentpol;
            $licence_request = new Request($licence_data);

            $policyDetailsController = new Policy_details();
            $resp=$policyDetailsController->check_license($licence_request);  
            
            if($resp['status']===-1){
                return redirect()->back()->with('error', $resp['msg']);
            }
        }

   
        //END CHECK UNDEBITED TRANSACTIONS
        DB::beginTransaction();
        try {
        
        $schem = schemaName();
        
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
        
        $username =  Auth::user()->user_name;

        $endorsement_no = $request->endt_renewal_no;
        //get the transaction type
        $transaction_type = (string)$request->input('type');


        $policy_no1 = $request->cnc_pol_no;

        $cancellation = DB::table('pollimits')->select('pollimits.endt_renewal_no')
        ->join('dcontrol', 'pollimits.endt_renewal_no', '=', 'dcontrol.endt_renewal_no')
       
        ->where('dcontrol.policy_no', $policy_no1)->latest('dola')->first()->endt_renewal_no;

        // $charge_vat = Pipcnam::where('record_type', 0)->get(['charge_vat']);
        $pipcnam = Pipcnam::where('record_type', 0)->first();
        $charge_vat = $pipcnam->charge_vat;
      

        if ($transaction_type == 'CNC') {

            $validate = Validator::make($request->all(), [
                'cnc_effective_date' => 'required'
            ]);
        }elseif($transaction_type == 'PTA') {
            $validate = Validator::make($request->all(), [
                'pta_period_from' => 'required',
                'pta_period_to' => 'required',
                'pta_currency_code' => 'required',
                'pta_currency_rate' => 'required'
            ]);

        }else{

            $validate = Validator::make($request->all(), [
                'effective_date' => 'required'
            ]);
        }

        // dd($transaction_type);
        if ($transaction_type == 'EXT' || $transaction_type == 'INS' || $transaction_type == 'NIL' || $transaction_type == 'CXT' || $transaction_type == 'STK') {

            // dd ($request->input('class'));

            //get previous endt_renewal_no
            $previous_endt_renewal_no = $request->input('previous_endt_renewal_no');
            
            $dcontrol_rec_before_update = Dcontrol::where('endt_renewal_no', $previous_endt_renewal_no)->get();
            $endt_renewal_no = $request->input('previous_endt_renewal_no');
            $dcontrol_prev_record = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
            $dcontrol_prev_record = $dcontrol_prev_record[0];


            $dcontrol_rec_before_update = $dcontrol_rec_before_update[0];

            //dd($dcontrol_rec_before_update);
        } else if ($transaction_type == 'REN'){

            //get previous endt_renewal_no
            $endt_renewal_no = $request->input('previous_endt_renewal_no');
            $dcontrol_prev_record = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
            $dcontrol_prev_record = $dcontrol_prev_record[0];

            if($pipcnam->postdate_ren == 'Y'){

                $ren_p_from = Carbon::parse($request->input('period_from'))->format('Y-m-d');
                $ren_post_date_cap = Carbon::parse($dcontrol_prev_record->renewal_date)->addDays($pipcnam->postdate_ren_days-1)->format('Y-m-d');

                if($ren_p_from > $ren_post_date_cap){

                     return redirect()->back()->with('error', 'Renewal Period from cannot be greater than the set period from cap');

                }
            }
            
        } elseif ($transaction_type == 'RFN') {

            //dd($request);
            //get the refund type
            $refund_type = $request->input('type_rfn');

            $refund_amount=$request->input('rfn_amt');

        
            $refund_amount = str_replace(',','',$refund_amount);
            
            $sys_calc_refund_amount = $request->input('sys_calc_rfn_amt'); 

            $sys_calc_refund_amount =  str_replace(',','',$sys_calc_refund_amount);

            $debit_amount = $request->input('debit_amount'); 

            $debit_amount =  str_replace(',','',$debit_amount);

            // return ($refund_amount);

            if($cls->open_cover=='Y'){
                //get previous endt_renewal_no
                $mac_endt = trim($request->input('ext_endt'));
                $endt_renewal_no = Dcontrol::where('mac_endt_no', $mac_endt)
                                            ->where(function($query){
                                                $query->where('cancelled','<>','Y')->orWhereNull('cancelled');
                                            })
                                            ->orderBy('dcon_no','desc')
                                            ->first()->endt_renewal_no;

                $dcontrol_prev_record = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();

            }else{

                //get previous endt_renewal_no
                $endt_renewal_no = $request->input('endt_renewal_no');
                $dcontrol_prev_record = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
            }
            
            /*if ($veh_count == $veh_fully_refunded && $veh_count>0) {

                $polmaster = Polmaster::where('policy_no', $dcontrol_prev_record->policy_no)->get();
                $polmaster = $polmaster[0];

                Session::flash('success', 'This Policy has already been fully refunded');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            } */

            if ((float)$debit_amount <= 0) {

                $polmaster = Polmaster::where('policy_no', $dcontrol_prev_record->policy_no)->get();
                $polmaster = $polmaster[0];

                Session::flash('error', 'This Policy has already been fully refunded');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            }

            if((float)$refund_amount > 0){
                Session::flash('error', 'Refund amount cannot be positive');

                return redirect()->route('endorse_functions', ['policy_no' => $dcontrol_prev_record->policy_no]);
            }

            if((float)$refund_amount * -1 > (float)$debit_amount){
                Session::flash('error', 'Refund amount cannot be greater than Debit Amount');

                return redirect()->route('endorse_functions', ['policy_no' => $dcontrol_prev_record->policy_no]);
            }
        } else if ($transaction_type == 'CNC') {
            //get previous endt_renewal_no

                //dd($request);

            $cnc_amount = str_replace(',','',$request->input('cnc_amt'));
            $cnc_debit_amount = str_replace(',','',$request->input('cnc_debit_amount'));
            
            $sys_calc_refund_amount = $request->input('sys_calc_rfn_amt'); 

            $endt_renewal_no = $request->input('endt_renewal_no');

            $dcontrol_prev_record = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
            $dcontrol_prev_record = $dcontrol_prev_record[0];

            $polmaster = Polmaster::where('policy_no', $dcontrol_prev_record->policy_no)->get();
            $polmaster = $polmaster[0];

            if ((float)$cnc_amount == 0) {
                Session::flash('error', 'Cannot cancel with zero refund amount. Raise NIL Endorsement to Flag as Cancelled');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            }
            else if ((float)$cnc_amount > 0) {
                Session::flash('error', 'Cancellation Amount Cannot be Positive');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            }
            else if (((float)$cnc_amount * -1) > (float)$cnc_debit_amount) {
                Session::flash('error', 'Cancellation Amount Cannot be greater than Debit Amount');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            }

            //check if policy is cancelled
            if ($polmaster->status_code == 'CNC') {
                Session::flash('success', 'This policy has been cancelled');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            }

            //check if endorsement is cancelled
            else if ($dcontrol_prev_record->cancelled == 'Y') {
                Session::flash('success', 'This endorsement has been cancelled');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            }

        } else if ($transaction_type == 'PTA') {
            $endt_renewal_no = $request->input('pta_endt_no');
            $policy_no = $request->input('pta_pol_no');
            $dcontrol_prev_record = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
            $dcontrol_prev_record = $dcontrol_prev_record[0];
            // dd($dcontrol_prev_record);
        } else if($transaction_type == 'RNS'){
            $endt_renewal_no = $request->input('rns_endt_no');
            $dcontrol_prev_record = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
            $dcontrol_prev_record = $dcontrol_prev_record[0];

            $polmaster = Polmaster::where('policy_no', $dcontrol_prev_record->policy_no)->get();
            $polmaster = $polmaster[0];

            if ((float)$request->input('rns_amt') == 0) {
                Session::flash('error', 'Cannot reinstate with zero amount.');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            }

            if ((float)$request->input('rns_amt') < 0) {
                Session::flash('error', 'Reinstate Amount Cannot be Negative');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            }

           /* if ((float)$request->input('rns_amt') > (float)$request->input('sys_rns_amt')) {
                Session::flash('error', 'Reinstate Amount Cannot be Greater than the Cancelled Amount');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            } */

            //check if policy is cancelled
            if ($polmaster->status_code != 'CNC') {
                Session::flash('success', 'This policy has already been reinstated');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            }

            //check if endorsement is cancelled
            /*else if ($dcontrol->cancelled == 'Y') {
                Session::flash('success', 'This endorsement has been cancelled');

                return redirect()->route('endorse_functions', ['policy_no' => $polmaster->policy_no]);
            }*/
        }


        /*dcon_no */
        $transaction_no = Tran0::where('rec_no', 0)->get(['tran_no']);
        $tran_no = $transaction_no[0]->tran_no;
        $tran0 = Tran0::where('rec_no', 0)->increment('tran_no', (int) '1');
        
        /* get dtrans_no and period */
        $doc_trans = Dtran0::where('rec_no', 0)->get(['dtran_no', 'account_month', 'account_year']);
        $dtran_no = $doc_trans[0]->dtran_no;

        //USE CURRENT DATE TO GET ACCOUNT PERIOD
        //$account_month = $doc_trans[0]->account_month;
        //$account_year = $doc_trans[0]->account_year;
        
        $account_month = Carbon::now()->month;
        $account_year = Carbon::now()->year;
        $dtran0 = Dtran0::where('rec_no', 0)->increment('dtran_no', (int) '1');


        if ($transaction_type == 'RFN' || $transaction_type == 'CNC' /*  || $transaction_type == 'RNS' */) {

            /*uw_year*/
            $now = new DateTime();
            $year = $now->format("Y");

            /*renewal date*/
            $renewal = $dcontrol_prev_record->renewal_date;


            //generate endorsement number
            $new_endt_renewal_no = $this->generate_pol(
                $dcontrol_prev_record->branch,
                $dcontrol_prev_record->class,
                $transaction_type,
                $account_year,
                $account_month
            );

            switch ($transaction_type) {
                case 'CNC':
                    $prev_ri_endt = $this->previousEndorsement($new_endt_renewal_no,$polmaster->policy_no);

                    $pol_uw =Polmasterend::where('endorse_no',$prev_ri_endt)->first();
                    $uw_year = $pol_uw->uw_year;
                    break;
                default:
                    $uw_year = $year;
                    break;
            }
            /*get currency*/
            $currency_code = $dcontrol_prev_record->currency_code;

            /*get current currency rate*/
            $currency_rate = $dcontrol_prev_record->currency_rate;


            /*get class department*/
            $department = ClassModel::where('class', $dcontrol_prev_record->class)->get(['dept']);
            $dept = (string) $department[0]->dept;

            /*get number of days of cover*/
            $days_of_cover = $dcontrol_prev_record->days_covered;
        }elseif($transaction_type == 'PTA') {
            /*uw_year*/
            $now = new DateTime();
            $year = $now->format("Y");
            $uw_year = $year;

            /*renewal date*/
            $renewal = $dcontrol_prev_record->renewal_date;

            //generate endorsement number
            $new_endt_renewal_no = $this->generate_pol(
                $dcontrol_prev_record->branch,
                $request->class,
                $transaction_type,
                $account_year,
                $account_month
            );

            /*get currency*/
            $currency_code = $dcontrol_prev_record->currency_code;

            /*get current currency rate*/
            $currency_rate = $dcontrol_prev_record->currency_rate;

            /*get class department*/
            $department = ClassModel::where('class', $dcontrol_prev_record->class)->get(['dept']);
            $dept = (string) $department[0]->dept;

            

        }elseif($transaction_type == 'STK') {
            /*uw_year*/

            $pol_uw = Polmaster::where('policy_no',$dcontrol_prev_record->policy_no)->first(['uw_year']);
            $uw_year = $pol_uw->uw_year;
            $doc_type = 'DRN';

            /*renewal date*/
            $renewal = $dcontrol_prev_record->renewal_date;

            //generate endorsement number
            $new_endt_renewal_no = $this->generate_pol(
                $dcontrol_prev_record->branch,
                $request->class,
                $transaction_type,
                $account_year,
                $account_month
            );

            /*get currency*/
            $currency_code = $request->currency;
            $currency_rate = $request->currency_rate;

            /*get class department*/
            $department = ClassModel::where('class', $dcontrol_prev_record->class)->first(['dept']);
            $dept = (string) $department->dept;
            $days_of_cover = $dcontrol_prev_record->days_covered;
        }
        else if($transaction_type == 'CXT'){
            $cls = ClassModel::where('class', $request->input('class'))->first();
            /*uw_year*/
            //$uw_year = date('Y', strtotime((string) $request->input('exc_ext_from')));
            $pol_uw = Polmaster::where('policy_no',$dcontrol_rec_before_update->policy_no)->get(['uw_year'])[0];
            $uw_year = $pol_uw->uw_year;
            $doc_type = 'DRN';
            /*renewal date*/
            // $renewal = new DateTime((string) $request->input('exc_ext_to'));
            // $renewal->modify('+1 day');

            if($cls->exem == "Y"){
                $renewal = new DateTime((string) $request->input('period_to'));
                $renewal->modify('+1 day');
            }
            else{
                $renewal = new DateTime((string) $request->input('period_to'));
            }
            //dd($request->input('class'));
            //generate endorsement number
            

            $new_endt_renewal_no = $this->generate_pol(
                $request->input('branchpol'),
                $request->input('class'),
                $request->input('type'),
                $account_year,
                $account_month
            );
           // dd($new_endt_renewal_no);
            /*get class department*/
            $department = ClassModel::where('class', $request->input('class'))->get(['dept']);
            $dept = (string) $department[0]->dept;

            /*get number of days of cover*/
            $cover_from = Carbon::parse($request->input('exc_ext_from'));
            $cover_to = Carbon::parse($request->input('exc_ext_to'));

            $days_of_cover = $cover_from->diffInDays($cover_to);
            $class = ClassModel::where('class', $request->input('class'))->first();

            $orig_dcontrol = Dcontrol::where('endt_renewal_no',$dcontrol_prev_record->policy_no)->first();
            
            # Get Cover Days from orig record
            $days_of_cover = $orig_dcontrol->days_covered;
        }else if($transaction_type == 'NIL'){
            $cls = ClassModel::where('class', $request->input('class'))->first();
            /*uw_year*/
            //$uw_year = date('Y', strtotime((string) $request->input('exc_ext_from')));
            $pol_uw = Polmaster::where('policy_no',$dcontrol_rec_before_update->policy_no)->get(['uw_year'])[0];
            $uw_year = $pol_uw->uw_year;
            //$doc_type = 'DRN';
            /*renewal date*/
            // $renewal = new DateTime((string) $request->input('period_to'));
            // $renewal->modify('+1 day');

            if($cls->exem == "Y"){
                $renewal = new DateTime((string) $request->input('nil_period_to'));
                $renewal->modify('+1 day');
            }
            else{
                $renewal = new DateTime((string) $request->input('nil_period_to'));
            }
            
            //dd($request->input('class'));
            //generate endorsement number
            
            
            $new_endt_renewal_no = $this->generate_pol(
                $request->input('branchpol'),
                $request->input('class'),
                $request->input('type'),
                $account_year,
                $account_month
            );

            $department = ClassModel::where('class', $request->input('class'))->get(['dept']);
            $dept = (string) $department[0]->dept;

            /*get number of days of cover*/
            $cover_from = Carbon::parse($request->input('effective_date'));
            $cover_to = Carbon::parse($request->input('period_to'));

            //$days_of_cover = $cover_from->diffInDays($cover_to);
            $class = ClassModel::where('class', $request->input('class'))->first();
            
            # Get Cover Days From Input Field
            $days_of_cover = $request->input('cover_days');

            /*get currency*/
            $currency = $request->input('currency');                                            //Currency::where('base_currency','Y')->get(['currency_code']);
            $currency_code = $currency; //$currency[0]->currency_code;

            /*get current currency rate*/
            $currency_rate =str_replace(',','',$request->input('today_currency'));
            $nil_endorse_type = $request->input('nil_endorse_type');
            $nilType = Nil_endorsetypes::where('endt_type',$nil_endorse_type)->first();
        }else if($transaction_type == 'MAC'){

            # For MAC
            $cls = ClassModel::where('class', $request->input('class'))->first();

            $endt_renewal_no = $request->input('endt_renewal_no');

            $previous_endt_renewal_no = $request->input('previous_endt_renewal_no');
            
            $dcontrol_rec_before_update = Dcontrol::where('endt_renewal_no', $previous_endt_renewal_no)->first();

            $dcontrol_prev_record = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();

            $marinemaster = Marinemasterpol::where('policy_no', $dcontrol_prev_record->policy_no)->first();
            
            /*uw_year*/
            //$uw_year = date('Y', strtotime((string) $request->input('mac_period_from')));
            $uw_year = $marinemaster->uw_year;

            /*renewal date*/
            // $renewal = new DateTime((string) $request->input('period_to'));
            // $renewal->modify('+1 day');

            if($cls->exem == "Y"){
                $renewal = new DateTime((string) $request->input('mac_period_to'));
                $renewal->modify('+1 day');
            }
            else{
                $renewal = new DateTime((string) $request->input('mac_period_to'));
            }

            $currency_rate = str_replace(',','',$request->input('today_currency'));
            
            $new_endt_renewal_no = $this->generate_pol(
                $request->input('branchpol'),
                $request->input('class'),
                $request->input('type'),
                $account_year,
                $account_month
            );

            # Get Cover Days From Input Field
            $days_of_cover = (int)$request->input('mac_cover_days');
        }
        else {
            # For POL < REN < RNS < EXT
            $cls = ClassModel::where('class', $request->input('class'))->first();
            
            /*uw_year*/
            $uw_year = date('Y', strtotime((string) $request->input('period_from')));

            /*renewal date*/
            // $renewal = new DateTime((string) $request->input('period_to'));
            // $renewal->modify('+1 day');

            if($cls->exem == "Y"){
                $renewal = new DateTime((string) $request->input('period_to'));
                $renewal->modify('+1 day');
            }
            else{
                $renewal = new DateTime((string) $request->input('period_to'));
            }
            //dd($request->input('class'));
            //generate endorsement number
            
            $new_endt_renewal_no = $this->generate_pol(
                $request->input('branchpol'),
                $request->input('class'),
                $request->input('type'),
                $account_year,
                $account_month
            );

            /*get currency*/
            $currency = $request->input('currency');                                            //Currency::where('base_currency','Y')->get(['currency_code']);
            $currency_code = $currency; //$currency[0]->currency_code;

            /*get current currency rate*/
            $currency_rate = str_replace(',','',$request->input('today_currency'));

            /*get class department*/
            $department = ClassModel::where('class', $request->input('class'))->get(['dept']);
            $dept = (string) $department[0]->dept;

            /*get number of days of cover*/
            $cover_from = Carbon::parse($request->input('effective_date'));
            $cover_to = Carbon::parse($request->input('period_to'));

            $days_of_cover = $cover_from->diffInDays($cover_to);
            $class = ClassModel::where('class', $request->input('class'))->first();

            # Get Cover Days From Input Field
            $days_of_cover = $request->input('cover_days');

            // dd($cover_from, $cover_to, $days_of_cover, $request->input('cover_days'));
        }
        $this->uw_year = $uw_year;

        
        /*dprop_no*/
        if ($transaction_type == 'POL' || $transaction_type == 'MAC') {

            $prop_number = Classbr::where('class', $request->input('class'))->get(['prop_serial']);
            $prop_no = $prop_number[0]->prop_serial;
            $dprop_no = Classbr::where('class', $request->input('class'));

            $dprop_no = Classbr::where('class', $request->input('class'))->increment('prop_serial', (int) '1');
        }

        $seq_no = Dcontrol::generateTranseqNumber($request->input('type'),$polno->policy_no);
        /*insured array*/

        $insured = Client::where('client_number', $request->input('insured'))->get(['name', 'client_number', 'client_type']);

        /*get doc type */
        $document_type = Transtype::where('descr', $request->input('type'))->get(['doc_type']);
        $doc_type = $document_type[0]->doc_type;

        //type of bus
        $bustype_curr = Bustype::where('type_of_bus', trim($request->input('bustype')))->first();

        if($charge_vat == 'Y'){
            $get_vat_setup = Vat_setup::where('vat_code',(int)$request->input('vat_charged'))->first();
        }

        /******* REDIRECT IF MARINE OPEN COVER *******/

        if($cls->open_cover == 'Y' && $pipcnam->mac_as_pol =='Y' && ($transaction_type == 'POL' || $transaction_type == 'REN')){

            $marine_data =  [
                                'client_number'   => $insured[0]->client_number,
                                'insured'         => $insured[0]->name,
                                'days_covered'    => $days_of_cover,
                                'account_year'    => $account_year,
                                'account_month'   => $account_month,
                                'currency'        => $currency_code,
                                'currency_rate'   => $currency_rate,
                                'renewal_date'    => $renewal,
                                'vat_type'        => $get_vat_setup->vat_type,
                                'vat_description' => $get_vat_setup->vat_description,
                                'uw_year'         => $uw_year
                            ];
            
            $marine_open_cover = new MarineOpenProcessing();
            
            $marine_open_cover->setProps($new_endt_renewal_no,
                                         $new_endt_renewal_no,
                                         $request->input('class'),
                                         $marine_data,
                                         $request);

            if($transaction_type=='POL'){
                $marine_status = $marine_open_cover->marine_masterpol_processing();
            }else {
                $marine_status = $marine_open_cover->process_renewal($dcontrol_prev_record->policy_no,$new_endt_renewal_no);
            }

            if($marine_status == true){
                $this->autoPopulateDisclaimer($new_endt_renewal_no);
                $this->autoPopulateCoverage($new_endt_renewal_no);
                $this->autoPopulateWarranty($new_endt_renewal_no);
                $this->autoPopulateConditions($new_endt_renewal_no);
                $this->autoPopulateScope($new_endt_renewal_no);
                $this->autoPopulateExclusions($new_endt_renewal_no);


                DB::commit();
                return redirect()->route('marine_open_dtl',['endt_renewal_no'=> $new_endt_renewal_no])->with('success','Marine Open Cover Policy '.$new_endt_renewal_no.' generated successfully');

            }
            else{

                DB::rollback();
                return redirect()->back()->with('error','Failed to generate Marine open cover');

            }  

        }
        
        if($cls->open_cover == 'Y' && $transaction_type != 'POL' && $transaction_type != 'REN' && $transaction_type != 'RNS'){

            $marineopenendt = Marinemasterpol::where('policy_no', $dcontrol_prev_record->policy_no)->first();
            $marineopenpol = Marinemasterhist::where('policy_no', $dcontrol_prev_record->policy_no)
                                             ->whereIn('trans_type', ['POL','REN'])
                                             ->where('status', 'ACT')
                                             ->orderBy('transeq_no', 'desc')
                                             ->first();

            $marine_policy_no = $marineopenpol->endt_renewal_no;
            $marine_endt_no = $marineopenendt->endt_renewal_no;
        }
        else{
            $marine_policy_no = $new_endt_renewal_no;
            $marine_endt_no = $new_endt_renewal_no;
        }

        $ppw = PPWParam::where('ppw_code',$request->facinppw)->first();

        /******* END REDIRECT IF MARINE OPEN COVER ******/

        //add new record to dcontrol
        $dcontrol = new Dcontrol;
        $dcontrol->dcon_no = $tran_no;
        $dcontrol->transeq_no = $seq_no;
        $dcontrol->doc_type = strtoupper($doc_type);

        
        switch ($request->input('ast')) {
            case 'I':
                $dcontrol->plan = $request->input('plan');
                $dcontrol->plan_categ = $request->input('plan_categ');
                $dcontrol->instal_categ = $request->input('instal_categ');
            break;
        }

        $get_pol = $transaction_type == "POL" ? $new_endt_renewal_no : $dcontrol_prev_record->policy_no;

        $pol_ren = $this->pol_ren($transaction_type, $get_pol);
        $dcontrol->pol_ren = $pol_ren;

        switch ($transaction_type) {
            case 'POL':
                $dcontrol->dprop_no=$prop_no;
                $dcontrol->policy_no=(string)$new_endt_renewal_no;
                $dcontrol->prop_date=Carbon::now();
                $dcontrol->branch=$request->input('branchpol');
                $dcontrol->agent=$request->input('agentpol');
                $dcontrol->class=$request->input('class');
                $dcontrol->user_str=(string)$request->User()->user_name;

                $dcontrol->period_from=$request->input('period_from');
                $dcontrol->period_to=$request->input('period_to');
                $dcontrol->old_policy_no = $request->input('old_pol');
                $dcontrol->renew_old_policy = $request->input('renew_old_pol');
                $dcontrol->onboard_old_policy = $request->input('onboard_old_pol');
                $dcontrol->apply_admin_fees = $request->apply_admin_fees;
                $dcontrol->admin_fees_rate = $request->admin_rate;

                $dcontrol->doc_type = 'DRN';

                if(trim($request->input('ast')) == 'T'){
                    $dcontrol->period_from = $request->input('t_period_from');
                    $dcontrol->period_to =$request->input('t_period_to');
                    $dcontrol->cov_period_from = $request->input('period_from');
                    $dcontrol->cov_period_to = $request->input('period_to');
                    $dcontrol->endt_days = $request->input('t_cover_days');
                }
                else if(trim($request->input('ast')) == 'S' && trim($request->input('prem_method')) == 'S'){
                    $dcontrol->period_from=$request->input('t_period_from');
                    $dcontrol->period_to=$request->input('t_period_to');
                    $dcontrol->cov_period_from=$request->input('period_from');
                    $dcontrol->cov_period_to=$request->input('period_to');
                    $dcontrol->endt_days = $request->input('t_cover_days');
                    
                    $shortTermRate = $this->verifyShortTermRate($request->input('class'),$request->input('t_cover_days'));
 
                }else if($request->ast == 'I'){
                    $yearLength = Carbon::parse($request->period_from)->isLeapYear() ? 366 : 365;
                    $inst_cov_to = Carbon::parse($request->period_from)->addDays($yearLength);
                    $instal_days = Carbon::parse($request->period_from)->diffInDays(Carbon::parse($request->period_to));
                    $dcontrol->period_from=$request->input('period_from');
                    $dcontrol->period_to=$request->input('period_to');
                    $dcontrol->cov_period_from=$request->input('period_from');
                    $dcontrol->cov_period_to= $inst_cov_to;
                    $dcontrol->endt_days = $instal_days;
                }else{
                    $dcontrol->period_from=$request->input('period_from');
                    $dcontrol->period_to=$request->input('period_to');
                    $dcontrol->cov_period_from=$request->input('period_from');
                    $dcontrol->cov_period_to=$request->input('period_to');
                }

                $dcontrol->short_term_method=$request->input('prem_method');
                $dcontrol->short_term_percent=$shortTermRate;
                
                $dcontrol->effective_date=$request->input('effective_date');
                $dcontrol->branch_code= str_pad($request->input('branchpol'), 3,"0",STR_PAD_LEFT);
                $dcontrol->co_insure=$request->input('co_ins');

                $dcontrol->ext_from = $dcontrol->period_from;
                $dcontrol->ext_to = $dcontrol->period_to;

                if($dcontrol->co_insure=='Y' && $pipcnam->coins_rate_per_sec!='Y'){
                    $dcontrol->co_ins_rate=$request->input('co_ins_rate');
                    $dcontrol->co_ins_base=$request->input('co_ins_base');
                }else{
                    $dcontrol->co_ins_rate=0;
                    $dcontrol->co_ins_base=0;
                }
                
                $dcontrol->type_of_bus=$request->input('bustype');
                $dcontrol->dept=$request->input('department');
                $dcontrol->actual_period_from=$request->input('period_from');
                $dcontrol->actual_period_to=$request->input('period_to');
                $dcontrol->financed=$request->input('financed');
                $dcontrol->ipf=$request->input('ipf');
                $dcontrol->ast_marker=$request->input('ast');  
                $dcontrol->items_total=1;
                $dcontrol->branch_cod=$request->input('branchpol');
                $dcontrol->currency=$currency_code;
                $dcontrol->ipf_repayment_date=$request->input('ipf_repayment_date');
                

                if($request->input('financed')=='Y'){

                    $dcontrol->financed_code=$request->input('financier');
                }else{
                    $dcontrol->financed_code='';
                }

                if ($request->input('co_ins') == 'Y') {

                    $dcontrol->company_share = $request->input('co_ins_share');
                } else {
                    $dcontrol->company_share = 100;  //must be 100 for debit and reinsurances to work if no co insurance is done
                }

                if($charge_vat == 'Y'){
                    $dcontrol->vat_type = $get_vat_setup->vat_type;
                    $dcontrol->vat_description = $get_vat_setup->vat_description;
                    $dcontrol->vat_rate = $get_vat_setup->vat_rate;
                    $dcontrol->vat_code = $request->input('vat_charged');
                }else{
                    $dcontrol->vat_rate = 0;
                }

                if($bustype_curr->fronting == 'Y'){
                    $dcontrol->ira_rate = $request->input('ira_rate');
                    $dcontrol->ipec_levy_loading_amount = $request->input('ipec_levy_loading_amount');
                    $dcontrol->fronting_rate = $request->input('fronting_rate');
                    $dcontrol->reinsurer_fronting_rate = $request->input('reinsurer_fronting_rate');
                    $dcontrol->fronting_stampduty_basis = $request->input('fronting_stampduty_basis');
                    $dcontrol->comm_rate = $request->input('fronting_comm_rate');
                }
                else{
                    $dcontrol->ira_rate = 0;
                    $dcontrol->ipec_levy_loading_amount = 0;
                    $dcontrol->fronting_rate = 0;
                    $dcontrol->reinsurer_fronting_rate = 0;
                    $dcontrol->comm_rate = 0;
                }
            

                if ($pipcnam->predebit_plan == 'Y') {
                    $dcontrol->apply_payment_plan =$request->input('payment_plan');
                }

                $dcontrol->bpdate_request_no = $request->input('bp_request_no');

                $dcontrol->master_policy = $new_endt_renewal_no;
                $dcontrol->master_endt_no = $new_endt_renewal_no;
                $dcontrol->instruction_date = $request->input('instruction_date');
                $dcontrol->proforma_reason = $request->input('proforma_reason');
                if($cls->travel == 'Y'){
                    $dcontrol->conv_rate = $request->input('curr_conv_rate');
                };
                $dcontrol->sales_channel = $request->input('sales_channel');
                $dcontrol->policy_wording = $request->input('policy_wording');

            break;

            case 'EXT':
                $dcontrol->dprop_no = 0;
                $dcontrol->policy_no = $dcontrol_rec_before_update->policy_no;
                $dcontrol->prop_date = $dcontrol_rec_before_update->prop_date;
                $dcontrol->branch = $request->input('branchpol');
                $dcontrol->agent = $request->input('agentpol');
                $dcontrol->apply_admin_fees = $request->apply_admin_fees;
                $dcontrol->admin_fees_rate = $request->admin_rate;
                $dcontrol->class = $request->input('class');
                $dcontrol->user_str = (string) $request->User()->user_name;
                $dcontrol->proforma_reason = $request->input('proforma_reason');
                $dcontrol->policy_wording = $request->input('policy_wording');

                # Master Policy No For 'Slave' Classes => Employer's Liability
                $dcontrol->master_policy = $dcontrol_rec_before_update->master_policy;
                $dcontrol->master_endt_no = $dcontrol_rec_before_update->master_endt_no;

                if(trim($request->input('ast')) == 'T'){
                    $dcontrol->period_from = $request->input('t_period_from');
                    $dcontrol->period_to =$request->input('t_period_to');
                    $dcontrol->cov_period_from = $request->input('period_from');
                    $dcontrol->cov_period_to = $request->input('period_to');
                    $dcontrol->endt_days = $request->input('t_cover_days');
                }
                else if(trim($request->input('ast')) == 'S' && $dcontrol_prev_record->short_term_method == 'S'){
                    $dcontrol->period_from=$request->input('t_period_from');
                    $dcontrol->period_to=$request->input('t_period_to');
                    $dcontrol->cov_period_from=$request->input('period_from');
                    $dcontrol->cov_period_to=$request->input('period_to');
                    $dcontrol->endt_days = $request->input('t_cover_days');

                    $shortTermRate = $this->verifyShortTermRate($request->input('class'),$request->input('t_cover_days'));
                }else if($request->ast == 'I'){
                    $instal_days = Carbon::parse($request->ext_from)->diffInDays(Carbon::parse($request->ext_to));
                    $dcontrol->period_from=$request->input('ext_from');
                    $dcontrol->period_to=$request->input('ext_to');
                    $dcontrol->cov_period_from=$dcontrol_rec_before_update->cov_period_from;
                    $dcontrol->cov_period_to= $dcontrol_rec_before_update->cov_period_to;
                    $dcontrol->endt_days = $instal_days;
                }else{
                    $dcontrol->period_from=$request->input('period_from');
                    $dcontrol->period_to=$request->input('period_to');
                    $dcontrol->cov_period_from=$request->input('period_from');
                    $dcontrol->cov_period_to=$request->input('period_to');
                }

                if($cls->open_cover == 'Y'){
                    $dcontrol->cov_period_from = $marineopenpol->cov_period_from;
                    $dcontrol->cov_period_to = $marineopenpol->cov_period_to;
                }
                

                $dcontrol->short_term_method = $dcontrol_prev_record->short_term_method;
                $dcontrol->short_term_percent = $shortTermRate;

                $dcontrol->ext_from = $dcontrol->period_from;
                $dcontrol->ext_to = $dcontrol->period_to;

                $dcontrol->effective_date = $request->input('effective_date');
                $dcontrol->branch_code = str_pad($request->input('branchpol'), 3, "0", STR_PAD_LEFT);
                $dcontrol->co_insure = $request->input('co_ins');
                $dcontrol->co_ins_rate = $request->input('co_ins_rate');
                $dcontrol->co_ins_base = $request->input('co_ins_base');
                $dcontrol->type_of_bus = $request->input('bustype');
                $dcontrol->dept = $request->input('department');
                $dcontrol->actual_period_from = $request->input('period_from');
                $dcontrol->actual_period_to = $request->input('period_to');
                $dcontrol->financed = $request->input('financed');
                $dcontrol->ipf=$request->input('ipf');
                $dcontrol->ast_marker = $request->input('ast');
                $dcontrol->items_total = 1;
                $dcontrol->branch_cod = $request->input('branchpol');
                $dcontrol->currency = $currency_code;
                $dcontrol->currency_rate = $currency_rate;
                $dcontrol->ipf_repayment_date=$request->input('ipf_repayment_date');
                $dcontrol->mac_endt_no = ($request->has('mac_endt_no')) ? $request->input('mac_endt_no') : 'N';
                if($cls->travel == 'Y'){
                    
                    $dcontrol->conv_rate = $request->input('curr_conv_rate');
                };
                
                if ($request->input('financed') == 'Y') {
                    $dcontrol->financed_code = $request->input('financier');
                } else {
                    $dcontrol->financed_code = '';
                }

                if ($request->input('co_ins') == 'Y') {

                    $dcontrol->company_share = $request->input('co_ins_share');
                } else {
                    $dcontrol->company_share = 100;  //must be 100 for debit and reinsurances to work if no co insurance is done
                }

                if($charge_vat == 'Y'){
                    $dcontrol->vat_type = $get_vat_setup->vat_type;
                    $dcontrol->vat_description = $get_vat_setup->vat_description;
                    $dcontrol->vat_rate = $get_vat_setup->vat_rate;
                    $dcontrol->vat_code = $request->input('vat_charged');
                }else{
                    $dcontrol->vat_rate = 0;
                }
                
                if($bustype_curr->fronting == 'Y'){
                    $dcontrol->ira_rate = $request->input('ira_rate');
                    $dcontrol->ipec_levy_loading_amount = $request->input('ipec_levy_loading_amount');
                    $dcontrol->fronting_rate = $request->input('fronting_rate');
                    $dcontrol->reinsurer_fronting_rate = $request->input('reinsurer_fronting_rate');
                    $dcontrol->fronting_stampduty_basis = $request->input('fronting_stampduty_basis');
                    $dcontrol->comm_rate = $request->input('fronting_comm_rate');
                }
                else{
                    $dcontrol->ira_rate = 0;
                    $dcontrol->ipec_levy_loading_amount = 0;
                    $dcontrol->fronting_rate = 0;
                    $dcontrol->comm_rate = 0;
                }
            break;

            case 'NIL':
                $dcontrol->dprop_no = 0;
                $dcontrol->policy_no = $dcontrol_rec_before_update->policy_no;
                $dcontrol->prop_date = $dcontrol_rec_before_update->prop_date;
                $dcontrol->branch = $request->input('branchpol');
                $dcontrol->agent = $request->input('agentpol');
                $dcontrol->class = $request->input('class');
                $dcontrol->user_str = (string) $request->User()->user_name;

                # Master Policy No For 'Slave' Classes => Employer's Liability
                $dcontrol->master_policy = $dcontrol_rec_before_update->master_policy;
                $dcontrol->master_endt_no = $dcontrol_rec_before_update->master_endt_no;

                if(trim($dcontrol_rec_before_update->ast_marker) == 'T'){
                    if($nilType->slug == 'update-wrong-cover-period-captured'){
                        $dcontrol->period_from=$request->input('nil_period_from');
                        $dcontrol->period_to=$request->input('nil_period_to');
                        $dcontrol->cov_period_from=$request->input('nil_t_period_from');
                        $dcontrol->cov_period_to=$request->input('nil_t_period_to');   
                    }
                    else{
                        $dcontrol->period_from=$dcontrol_rec_before_update->period_from;
                        $dcontrol->period_to=$dcontrol_rec_before_update->period_to;
                        $dcontrol->cov_period_from=$dcontrol_rec_before_update->cov_period_from;
                        $dcontrol->cov_period_to=$dcontrol_rec_before_update->cov_period_to;
                    }
                }else{
                    if($nilType->slug == 'update-wrong-cover-period-captured'){
                        $dcontrol->period_from=$request->input('nil_period_from');
                        $dcontrol->period_to=$request->input('nil_period_to');
                        $dcontrol->cov_period_from=$request->input('nil_period_from');
                        $dcontrol->cov_period_to=$request->input('nil_period_to');
                    }else{
                        $dcontrol->period_from=$dcontrol_rec_before_update->period_from;
                        $dcontrol->period_to=$dcontrol_rec_before_update->period_to;
                        $dcontrol->cov_period_from=$dcontrol_rec_before_update->cov_period_from;
                        $dcontrol->cov_period_to=$dcontrol_rec_before_update->cov_period_to;
                    }
                }

                $dcontrol->short_term_method = $dcontrol_rec_before_update->short_term_method;
                $dcontrol->short_term_percent = $dcontrol_rec_before_update->short_term_percent;

                $dcontrol->endt_days = $dcontrol_rec_before_update->endt_days;
                $dcontrol->ext_from = $dcontrol->period_from;
                $dcontrol->ext_to = $dcontrol->period_to;

                $dcontrol->effective_date = $request->input('effective_date');
                $dcontrol->branch_code = str_pad($request->input('branchpol'), 3, "0", STR_PAD_LEFT);
                $dcontrol->co_insure = $request->input('co_ins');
                $dcontrol->co_ins_rate = $request->input('co_ins_rate');
                $dcontrol->co_ins_base = $request->input('co_ins_base');
                $dcontrol->type_of_bus = $request->input('bustype');
                $dcontrol->dept = $request->input('department');
                $dcontrol->actual_period_from = $request->input('period_from');
                $dcontrol->actual_period_to = $request->input('period_to');
                $dcontrol->proforma_reason = $request->input('proforma_reason');
                $dcontrol->policy_wording = $request->input('policy_wording');


                if($nilType->slug == 'update-financier-details'){
                    $dcontrol->financed = $request->input('financed_nil');

                    if ($request->input('financed_nil') == 'Y') {
                        $dcontrol->financed_code = $request->input('financier_nil');
                    } 
                    else {
                        $dcontrol->financed_code = '';
                    }
                }else{
                    $dcontrol->financed = $dcontrol_rec_before_update->financed;
                    $dcontrol->financed_code = $dcontrol_rec_before_update->financed_code;
                }

                $dcontrol->ipf=$request->input('ipf');
                $dcontrol->ipf_repayment_date=$request->input('ipf_repayment_date');
                $dcontrol->ast_marker = $request->input('ast');
                $dcontrol->items_total = 1;
                $dcontrol->branch_cod = $request->input('branchpol');
                $dcontrol->currency = $currency_code;
                $dcontrol->currency_rate = $currency_rate;

                if ($request->input('co_ins') == 'Y') {

                    $dcontrol->company_share = $request->input('co_ins_share');
                } else {
                    $dcontrol->company_share = 100;  //must be 100 for debit and reinsurances to work if no co insurance is done
                }
                
                $dcontrol->nil_code = $nilType->endt_type;
                $dcontrol->nil_reason = $request->input('nil_reason');

                if($charge_vat == 'Y'){
                    $dcontrol->vat_type = $get_vat_setup->vat_type;
                    $dcontrol->vat_description = $get_vat_setup->vat_description;
                    $dcontrol->vat_rate = $get_vat_setup->vat_rate;
                    $dcontrol->vat_code = $request->input('vat_charged');
                }else{
                    $dcontrol->vat_rate = 0;
                }

                # Uncommitted NIL transactions default to N
                $dcontrol->committed = 'N';
                $dcontrol->ira_rate = $dcontrol_rec_before_update->ira_rate;
                $dcontrol->ipec_levy_loading_amount = $dcontrol_rec_before_update->ipec_levy_loading_amount;
                $dcontrol->fronting_rate = $dcontrol_rec_before_update->fronting_rate;
                $dcontrol->reinsurer_fronting_rate = $dcontrol_rec_before_update->reinsurer_fronting_rate;
                $dcontrol->fronting_stampduty_basis = $dcontrol_rec_before_update->fronting_stampduty_basis;
                $dcontrol->comm_rate = $dcontrol_rec_before_update->fronting_comm_rate;

            break;

            case 'CXT':
                $dcontrol->dprop_no = 0;
                $dcontrol->policy_no = $dcontrol_rec_before_update->policy_no;
                $dcontrol->prop_date = $dcontrol_rec_before_update->prop_date;
                $dcontrol->branch = $dcontrol_rec_before_update->branch;
                $dcontrol->agent = $dcontrol_rec_before_update->agent;
                $dcontrol->class = $dcontrol_rec_before_update->class;
                $dcontrol->user_str = (string) $request->User()->user_name;

                $dcontrol->period_from = $request->input('exc_ext_from');
                $dcontrol->period_to = $request->input('exc_ext_to');
                $dcontrol->cov_period_from = $dcontrol_rec_before_update->cov_period_from;
                $dcontrol->cov_period_to = $request->input('exc_ext_to');

                $dcontrol->effective_date = $request->input('exc_ext_from');
                $dcontrol->branch_code = $dcontrol_rec_before_update->branch_code;
                $dcontrol->co_insure = $dcontrol_rec_before_update->co_insure;
                $dcontrol->co_ins_rate = $dcontrol_rec_before_update->co_ins_rate;
                $dcontrol->co_ins_base = $dcontrol_rec_before_update->co_ins_base;
                $dcontrol->type_of_bus = $dcontrol_rec_before_update->type_of_bus;
                $dcontrol->dept = $dcontrol_rec_before_update->dept;
                $dcontrol->actual_period_from = $request->input('exc_ext_from');
                $dcontrol->actual_period_to = $request->input('exc_ext_to');
                $dcontrol->financed = $dcontrol_rec_before_update->financed;
                $dcontrol->ast_marker = $dcontrol_rec_before_update->ast_marker;
                $dcontrol->items_total = 1;
                $dcontrol->branch_cod = $dcontrol_rec_before_update->branch_cod;
                $dcontrol->currency = $dcontrol_rec_before_update->currency;
                $dcontrol->currency_rate=$dcontrol_rec_before_update->currency_rate;
                $dcontrol->financed_code = $dcontrol_rec_before_update->financed_code;
                $dcontrol->company_share = $dcontrol_rec_before_update->company_share;
                $dcontrol->vat_type = $dcontrol_rec_before_update->vat_type;
                $dcontrol->vat_description = $dcontrol_rec_before_update->vat_description;
                $dcontrol->vat_rate = $dcontrol_rec_before_update->vat_rate;
                $dcontrol->vat_code = $dcontrol_rec_before_update->vat_code;
                $dcontrol->prorate_cxt = $request->input('prorate_cxt');
                $dcontrol->cxt_load_rate = (float)$request->input('cxt_load_rate');

                $dcontrol->short_term_method = $request->input('prorate_cxt');
                $dcontrol->short_term_percent = $request->input('cxt_short_term_percent');

                $currency_rate=$dcontrol_rec_before_update->currency_rate;
                $dcontrol->ext_from = $dcontrol->period_from;
                $dcontrol->ext_to = $dcontrol->period_to;

                # Master Policy No For 'Slave' Classes => Employer's Liability
                $dcontrol->master_policy = $dcontrol_rec_before_update->master_policy;
                $dcontrol->master_endt_no = $dcontrol_rec_before_update->master_endt_no;
                $dcontrol->ira_rate = $dcontrol_rec_before_update->ira_rate;
                $dcontrol->ipec_levy_loading_amount = $dcontrol_rec_before_update->ipec_levy_loading_amount;
                $dcontrol->fronting_rate = $dcontrol_rec_before_update->fronting_rate;
                $dcontrol->reinsurer_fronting_rate = $dcontrol_rec_before_update->reinsurer_fronting_rate;
                $dcontrol->fronting_stampduty_basis = $dcontrol_rec_before_update->fronting_stampduty_basis;
                $dcontrol->comm_rate = $dcontrol_rec_before_update->fronting_comm_rate;
            break;

            case 'INS':
            case 'REN':
                $dcontrol->dprop_no = 0;
                $dcontrol->policy_no = $dcontrol_prev_record->policy_no;
                $dcontrol->prop_date = $dcontrol_prev_record->prop_date;
                $dcontrol->branch = $request->input('branchpol');
                $dcontrol->agent = $request->input('agentpol');
                $dcontrol->apply_admin_fees = $request->apply_admin_fees;
                $dcontrol->admin_fees_rate = $request->admin_rate;
                $dcontrol->class = $request->input('class');
                $dcontrol->user_str = (string) $request->User()->user_name;
                //$dcontrol->period_from = $request->input('period_from');
                //$dcontrol->period_to = $request->input('period_to');

                # Master Policy No For 'Slave' Classes => Employer's Liability
                $dcontrol->master_policy = $dcontrol_prev_record->master_policy;
                $dcontrol->master_endt_no = $dcontrol_prev_record->master_endt_no;

                if(trim($request->input('ast')) == 'T'){
                    $dcontrol->period_from = $request->input('t_period_from');
                    $dcontrol->period_to =$request->input('t_period_to');
                    $dcontrol->cov_period_from = $request->input('period_from');
                    $dcontrol->cov_period_to = $request->input('period_to');
                    $dcontrol->endt_days = $request->input('t_cover_days');
                }
                else if(trim($request->input('ast')) == 'S' && $dcontrol_prev_record->short_term_method == 'S'){
                    $dcontrol->period_from=$request->input('t_period_from');
                    $dcontrol->period_to=$request->input('t_period_to');
                    $dcontrol->cov_period_from=$request->input('period_from');
                    $dcontrol->cov_period_to=$request->input('period_to');
                    $dcontrol->endt_days = $request->input('t_cover_days');

                    $shortTermRate = $this->verifyShortTermRate($request->input('class'),$request->input('t_cover_days'));
                }else if($request->ast == 'I'){
                    
                    $instal_days = Carbon::parse($request->period_from)->diffInDays(Carbon::parse($request->period_to));
                    $dcontrol->period_from=$request->input('period_from');
                    $dcontrol->period_to=$request->input('period_to');
                    $dcontrol->cov_period_from=$dcontrol_rec_before_update->cov_period_from;
                    $dcontrol->cov_period_to= $dcontrol_rec_before_update->cov_period_to;
                    $dcontrol->endt_days = $instal_days;
                }else{
                    $dcontrol->period_from=$request->input('period_from');
                    $dcontrol->period_to=$request->input('period_to');
                    $dcontrol->cov_period_from=$request->input('period_from');
                    $dcontrol->cov_period_to=$request->input('period_to');
                }

                $dcontrol->short_term_method = $dcontrol_prev_record->short_term_method;
                $dcontrol->short_term_percent = $shortTermRate;

                $dcontrol->ext_from = $dcontrol->period_from;
                $dcontrol->ext_to = $dcontrol->period_to;
                $dcontrol->doc_type = 'DRN';

                $dcontrol->effective_date = $request->input('effective_date');
                $dcontrol->branch_code = str_pad($request->input('branchpol'), 3, "0", STR_PAD_LEFT);
                $dcontrol->co_insure = $request->input('co_ins');
                $dcontrol->co_ins_rate = $request->input('co_ins_rate');
                $dcontrol->co_ins_base = $request->input('co_ins_base');
                $dcontrol->type_of_bus = $request->input('bustype');
                $dcontrol->dept = $request->input('department');
                $dcontrol->actual_period_from = $request->input('period_from');
                $dcontrol->actual_period_to = $request->input('period_to');
                $dcontrol->financed = $request->input('financed');
                $dcontrol->ipf=$request->input('ipf');
                $dcontrol->ipf_repayment_date=$request->input('ipf_repayment_date');
                $dcontrol->ast_marker = $request->input('ast');
                $dcontrol->items_total = 1;
                $dcontrol->branch_cod = $request->input('branchpol');
                $dcontrol->currency = $currency_code;
                $dcontrol->proforma_reason = $request->input('proforma_reason');
                if($cls->travel == 'Y'){
                    
                    $dcontrol->conv_rate = $request->input('curr_conv_rate');
                };
                $dcontrol->policy_wording = $request->input('policy_wording');


                if ($request->input('financed') == 'Y') {
                    $dcontrol->financed_code = $request->input('financier');
                } else {
                    $dcontrol->financed_code = '';
                }

                if ($request->input('co_ins') == 'Y') {

                    $dcontrol->company_share = $request->input('co_ins_share');
                } else {
                    $dcontrol->company_share = 100;  //must be 100 for debit and reinsurances to work if no co insurance is done
                }

                if($charge_vat == 'Y'){
                    $dcontrol->vat_type = $get_vat_setup->vat_type;
                    $dcontrol->vat_description = $get_vat_setup->vat_description;
                    $dcontrol->vat_rate = $get_vat_setup->vat_rate;
                    $dcontrol->vat_code = $request->input('vat_charged');
                }else{
                    $dcontrol->vat_rate = 0;
                }
                
                if($bustype_curr->fronting == 'Y'){
                    $dcontrol->ira_rate = $request->input('ira_rate');
                    $dcontrol->ipec_levy_loading_amount = $request->input('ipec_levy_loading_amount');
                    $dcontrol->fronting_rate = $request->input('fronting_rate');                    
                    $dcontrol->reinsurer_fronting_rate = $request->input('reinsurer_fronting_rate');
                    $dcontrol->fronting_stampduty_basis = $request->input('fronting_stampduty_basis');
                    $dcontrol->comm_rate = $request->input('fronting_comm_rate');
                }
                else{
                    $dcontrol->ira_rate = 0;
                    $dcontrol->ipec_levy_loading_amount = 0;
                    $dcontrol->fronting_rate = 0;
                    $dcontrol->comm_rate = 0;
                }

                $dcontrol->bpdate_request_no = $request->input('bp_request_no');

            break;

            case 'RFN':
                $dcontrol->dprop_no = 0;
                $dcontrol->policy_no = $dcontrol_prev_record->policy_no;
                $dcontrol->prop_date = $dcontrol_prev_record->prop_date;
                $dcontrol->branch = $dcontrol_prev_record->branch;
                $dcontrol->agent = $dcontrol_prev_record->agent;
                $dcontrol->class = $dcontrol_prev_record->class;
                $dcontrol->client_number = $dcontrol_prev_record->client_number;
                $dcontrol->user_str = $username;
                // $dcontrol->user_str = $dcontrol_prev_record->user_str;
                $dcontrol->period_from = $dcontrol_prev_record->period_from;
                $dcontrol->period_to = $dcontrol_prev_record->period_to;
                $dcontrol->cov_period_from = $dcontrol_prev_record->cov_period_from;
                $dcontrol->cov_period_to = $dcontrol_prev_record->cov_period_to;
                $dcontrol->apply_admin_fees = $dcontrol_prev_record->apply_admin_fees;
                $dcontrol->admin_fees_rate = $dcontrol_prev_record->admin_fees_rate;

                $dcontrol->effective_date = $request->input('refund_date');
                $dcontrol->endorse_date = $request->input('refund_date');
                $dcontrol->branch_code = $dcontrol_prev_record->branch_code;
                $dcontrol->co_insure = $dcontrol_prev_record->co_insure;
                $dcontrol->co_ins_rate = $dcontrol_prev_record->co_ins_rate;
                $dcontrol->co_ins_base = $dcontrol_prev_record->co_ins_base;
                $dcontrol->type_of_bus = $dcontrol_prev_record->type_of_bus;
                $dcontrol->dept = $dcontrol_prev_record->dept;
                $dcontrol->actual_period_from = $dcontrol_prev_record->actual_period_from;
                $dcontrol->actual_period_to = $dcontrol_prev_record->actual_period_to;
                $dcontrol->financed = $dcontrol_prev_record->financed;
                $dcontrol->ast_marker = $dcontrol_prev_record->ast_marker;
                $dcontrol->short_term_method = $dcontrol_prev_record->short_term_method;
                $dcontrol->short_term_percent = $dcontrol_prev_record->short_term_percent;
                $dcontrol->items_total = 1;
                $dcontrol->branch_cod = $dcontrol_prev_record->branch_cod;
                $dcontrol->financed_code = $dcontrol_prev_record->financed_code;
                $dcontrol->currency = $dcontrol_prev_record->currency;

                //$dcontrol->cnc_rfn_endt = $dcontrol_prev_record->endt_renewal_no;
                $dcontrol->cnc_rfn_endt = $request->input('rfn_endt_no');
               
                $dcontrol->vat_type = $dcontrol_prev_record->vat_type;
                $dcontrol->vat_description = $dcontrol_prev_record->vat_description;
                $dcontrol->vat_rate = $dcontrol_prev_record->vat_rate;
                $dcontrol->vat_code = $dcontrol_prev_record->vat_code;
                $dcontrol->ipf_flag = $request->ipf_policy;

                if($request->ipf_policy == 'Y'){
                    $dcontrol->auto_allocate = 'N';
                }else{
                    $dcontrol->auto_allocate = $request->auto_allocate;
                }
                $dcontrol->refund_type = $request->type_rfn;

                $dcontrol->ext_from = $dcontrol->period_from;
                $dcontrol->ext_to = $dcontrol->period_to;

                # Master Policy No For 'Slave' Classes => Employer's Liability
                $dcontrol->master_policy = $dcontrol_prev_record->master_policy;
                $dcontrol->master_endt_no = $dcontrol_prev_record->master_endt_no;

                $dcontrol->facin_premium = $dcontrol_prev_record->facin_premium;
                $dcontrol->facin_sum_insured = $dcontrol_prev_record->facin_sum_insured;
                $dcontrol->facin_comm_rate = $dcontrol_prev_record->facin_comm_rate;
                $dcontrol->ira_rate = $dcontrol_prev_record->ira_rate;
                $dcontrol->ipec_levy_loading_amount = $dcontrol_prev_record->ipec_levy_loading_amount;
                $dcontrol->fronting_rate = $dcontrol_prev_record->fronting_rate;
                $dcontrol->reinsurer_fronting_rate = $dcontrol_prev_record->reinsurer_fronting_rate;
                $dcontrol->fronting_stampduty_basis = $dcontrol_prev_record->fronting_stampduty_basis;
                $dcontrol->comm_rate = $dcontrol_prev_record->fronting_comm_rate;
                $dcontrol->facultin_client_no = $dcontrol_prev_record->facultin_client_no;
                $dcontrol->cancel_mac = ($request->has('cancel_mac')) ? $request->input('cancel_mac') : 'N';
                $dcontrol->mac_endt_no = ($cls->open_cover=='Y') ? $request->input('ext_endt') : 'N';

            break;

            case 'CNC':
                $dcontrol->dprop_no = 0;
                $dcontrol->policy_no = $dcontrol_prev_record->policy_no;
                $dcontrol->prop_date = $dcontrol_prev_record->prop_date;
                $dcontrol->branch = $dcontrol_prev_record->branch;
                $dcontrol->agent = $dcontrol_prev_record->agent;
                $dcontrol->class = $dcontrol_prev_record->class;
                $dcontrol->client_number = $dcontrol_prev_record->client_number;
                $dcontrol->user_str = $username;
                // $dcontrol->user_str = $dcontrol_prev_record->user_str;
                $dcontrol->period_from = $dcontrol_prev_record->period_from;
                $dcontrol->period_to = $dcontrol_prev_record->period_to;
                $dcontrol->cov_period_from = $dcontrol_prev_record->cov_period_from;
                $dcontrol->cov_period_to = $dcontrol_prev_record->cov_period_to;
                
                $dcontrol->effective_date = $request->input('cnc_effective_date');
                $dcontrol->endorse_date = $request->input('cnc_effective_date');
                $dcontrol->branch_code = $dcontrol_prev_record->branch_code;
                $dcontrol->co_insure = $dcontrol_prev_record->co_insure;
                $dcontrol->co_ins_rate = $dcontrol_prev_record->co_ins_rate;
                $dcontrol->co_ins_base = $dcontrol_prev_record->co_ins_base;
                $dcontrol->type_of_bus = $dcontrol_prev_record->type_of_bus;
                $dcontrol->apply_admin_fees = $dcontrol_prev_record->apply_admin_fees;
                $dcontrol->admin_fees_rate = $dcontrol_prev_record->admin_fees_rate;
                $dcontrol->dept = $dcontrol_prev_record->dept;
                $dcontrol->actual_period_from = $dcontrol_prev_record->actual_period_from;
                $dcontrol->actual_period_to = $dcontrol_prev_record->actual_period_to;
                $dcontrol->financed = $dcontrol_prev_record->financed;
                $dcontrol->ast_marker = $dcontrol_prev_record->ast_marker;
                $dcontrol->short_term_method = $dcontrol_prev_record->short_term_method;
                $dcontrol->short_term_percent = $dcontrol_prev_record->short_term_percent;
                $dcontrol->items_total = 1;
                $dcontrol->branch_cod = $dcontrol_prev_record->branch_cod;
                $dcontrol->financed_code = $dcontrol_prev_record->financed_code;
                $dcontrol->currency = $dcontrol_prev_record->currency;
                $dcontrol->expiry_date = $dcontrol_prev_record->expiry_date;
                //$dcontrol->cnc_rfn_endt = $dcontrol_prev_record->endt_renewal_no;
                $dcontrol->cnc_rfn_endt = $request->input('cnc_endt_no');

                $dcontrol->vat_type = $dcontrol_prev_record->vat_type;
                $dcontrol->vat_description = $dcontrol_prev_record->vat_description;
                $dcontrol->vat_rate = $dcontrol_prev_record->vat_rate;
                $dcontrol->vat_code = $dcontrol_prev_record->vat_code;
                $dcontrol->auto_allocate = $request->auto_allocate;
                $dcontrol->refund_type = $request->calc_method;

                # Set Co insurance to the same share before cancellation
                $dcontrol->company_share = $dcontrol_prev_record->company_share;

                $dcontrol->ext_from = $dcontrol->period_from;
                $dcontrol->ext_to = $dcontrol->period_to;

                # Master Policy No For 'Slave' Classes => Employer's Liability
                $dcontrol->master_policy = $dcontrol_prev_record->master_policy;
                $dcontrol->master_endt_no = $dcontrol_prev_record->master_endt_no;

                $dcontrol->facin_premium = $dcontrol_prev_record->facin_premium;
                $dcontrol->facin_sum_insured = $dcontrol_prev_record->facin_sum_insured;
                $dcontrol->facin_comm_rate = $dcontrol_prev_record->facin_comm_rate;
                $dcontrol->ira_rate = $dcontrol_prev_record->ira_rate;
                $dcontrol->ipec_levy_loading_amount = $dcontrol_prev_record->ipec_levy_loading_amount;
                $dcontrol->fronting_rate = $dcontrol_prev_record->fronting_rate;
                $dcontrol->reinsurer_fronting_rate = $dcontrol_prev_record->reinsurer_fronting_rate;
                $dcontrol->fronting_stampduty_basis = $dcontrol_prev_record->fronting_stampduty_basis;
                $dcontrol->comm_rate = $dcontrol_prev_record->fronting_comm_rate;
                $dcontrol->facultin_client_no = $dcontrol_prev_record->facultin_client_no;
            break;

            case 'PTA':

                $dcontrol->dprop_no = 0;
                $dcontrol->policy_no = (string)$new_endt_renewal_no;
                $dcontrol->prop_date = $dcontrol_prev_record->prop_date;
                $dcontrol->branch = $dcontrol_prev_record->branch;
                $dcontrol->agent = $dcontrol_prev_record->agent;
                $dcontrol->class = $request->class;
                $dcontrol->user_str = $username;
                $dcontrol->period_from = $request->pta_period_from;
                $dcontrol->period_to = $request->pta_period_to;
                $dcontrol->cov_period_from = $request->pta_period_from;
                $dcontrol->cov_period_to = $request->pta_period_to;
                $dcontrol->effective_date = $request->pta_period_from;
                $dcontrol->endorse_date = $request->pta_period_from;
                $dcontrol->branch_code = $dcontrol_prev_record->branch_code;
                $dcontrol->co_insure = $dcontrol_prev_record->co_insure;
                $dcontrol->co_ins_rate = $dcontrol_prev_record->co_ins_rate;
                $dcontrol->co_ins_base = $dcontrol_prev_record->co_ins_base;
                $dcontrol->type_of_bus = $dcontrol_prev_record->type_of_bus;
                $dcontrol->dept = $dcontrol_prev_record->dept;
                $dcontrol->actual_period_from = $request->pta_period_from;
                $dcontrol->actual_period_to = $request->pta_period_to;
                $dcontrol->financed = $dcontrol_prev_record->financed;
                $dcontrol->ast_marker = $dcontrol_prev_record->ast_marker;
                $dcontrol->short_term_method = $dcontrol_prev_record->short_term_method;
                $dcontrol->short_term_percent = $dcontrol_prev_record->short_term_percent;
                $dcontrol->items_total = 1;
                $dcontrol->branch_cod = $dcontrol_prev_record->branch_cod;
                $dcontrol->financed_code = $dcontrol_prev_record->financed_code;
                
                $dcontrol->currency = $request->pta_currency_code;
                $dcontrol->currency_rate = $request->pta_currency_rate;

                $dcontrol->vat_type = $get_vat_setup->vat_type;
                $dcontrol->vat_description = $get_vat_setup->vat_description;
                $dcontrol->vat_rate = $get_vat_setup->vat_rate;
                $dcontrol->vat_code = $get_vat_setup->vat_code;
                // $dcontrol->expiry_date = $dcontrol_prev_record->expiry_date;
                
                // compute medical and basic premium
                #code
                // compute medical and basic premium // end


                // comesa Re-insurance
                #code
                // retain 70% of the prem and ceed out 30%

                // compute commision of 5% of the ceeded prem

                // comesa Re-insurance // end

                $dcontrol->ext_from = $dcontrol->period_from;
                $dcontrol->ext_to = $dcontrol->period_to;

                # Master Policy No For 'Slave' Classes => Employer's Liability
                $dcontrol->master_policy = $dcontrol_prev_record->policy_no;
                $dcontrol->master_endt_no = $dcontrol_prev_record->endt_renewal_no;

                $currency_rate = $request->pta_currency_rate;

                $dcontrol->ira_rate = $dcontrol_prev_record->ira_rate;
                $dcontrol->ipec_levy_loading_amount = $dcontrol_prev_record->ipec_levy_loading_amount;
                $dcontrol->fronting_rate = $dcontrol_prev_record->fronting_rate;
                $dcontrol->reinsurer_fronting_rate = $dcontrol_prev_record->reinsurer_fronting_rate;
                $dcontrol->fronting_stampduty_basis = $dcontrol_prev_record->fronting_stampduty_basis;
                $dcontrol->comm_rate = $dcontrol_prev_record->fronting_comm_rate;
                
            break;

            # New Reinstatement Process
            case 'RNS':
                $dcontrol->dprop_no         = $prop_no;
                $dcontrol->policy_no        = $dcontrol_prev_record->policy_no;
                $dcontrol->prop_date        = Carbon::now();
                $dcontrol->branch           = $request->input('branchpol');
                $dcontrol->agent            = $request->input('agentpol');
                $dcontrol->apply_admin_fees = $request->apply_admin_fees;
                $dcontrol->admin_fees_rate = $request->admin_rate;
                $dcontrol->class            = $dcontrol_prev_record->class;
                $dcontrol->dept             = $dcontrol_prev_record->dept;
                $dcontrol->user_str         = (string)$request->User()->user_name;

                $dcontrol->period_from      = $request->input('period_from');
                $dcontrol->period_to        = $request->input('period_to');
                $dcontrol->old_policy_no    = $request->input('old_pol');
                $dcontrol->renew_old_policy = $request->input('renew_old_pol');
                $dcontrol->ast_marker       = $request->input('ast'); 

                if(trim($request->input('ast')) == 'T'){
                    $dcontrol->period_from      = $request->input('t_period_from');
                    $dcontrol->period_to        = $request->input('t_period_to');
                    $dcontrol->cov_period_from  = $request->input('period_from');
                    $dcontrol->cov_period_to    = $request->input('period_to');
                    $dcontrol->endt_days        = $request->input('t_cover_days');
                } else if(trim($request->input('ast')) == 'S' && trim($request->input('prem_method')) == 'S'){
                    $dcontrol->period_from      = $request->input('t_period_from');
                    $dcontrol->period_to        = $request->input('t_period_to');
                    $dcontrol->cov_period_from  = $request->input('period_from');
                    $dcontrol->cov_period_to    = $request->input('period_to');
                    $dcontrol->endt_days        = $request->input('t_cover_days');

                    $shortTermRate = $this->verifyShortTermRate($dcontrol_prev_record->class,$request->input('t_cover_days'));
                }else{
                    $dcontrol->period_from      = $request->input('period_from');
                    $dcontrol->period_to        = $request->input('period_to');
                    $dcontrol->cov_period_from  = $request->input('period_from');
                    $dcontrol->cov_period_to    = $request->input('period_to');
                }

                $dcontrol->client_number    = $dcontrol_prev_record->client_number;
                $dcontrol->name             = $dcontrol_prev_record->name;

                $dcontrol->short_term_method    = $request->input('prem_method');
                $dcontrol->short_term_percent   = $shortTermRate;

                $dcontrol->ext_from = $dcontrol->period_from;
                $dcontrol->ext_to = $dcontrol->period_to;
                
                $dcontrol->effective_date       = $request->input('effective_date');
                $dcontrol->branch_code          = str_pad($request->input('branchpol'), 3,"0",STR_PAD_LEFT);
                $dcontrol->co_insure            = $request->input('co_ins');

                if($dcontrol->co_insure == 'Y' && $pipcnam->coins_rate_per_sec != 'Y'){
                    $dcontrol->co_ins_rate      = $request->input('co_ins_rate');
                    $dcontrol->co_ins_base      = $request->input('co_ins_base');
                }else{
                    $dcontrol->co_ins_rate      = 0;
                    $dcontrol->co_ins_base      = 0;
                }

                $dcontrol->type_of_bus          = $request->input('bustype');
                $dcontrol->actual_period_from   = $request->input('period_from');
                $dcontrol->actual_period_to     = $request->input('period_to');
                $dcontrol->financed             = $request->input('financed');
                $dcontrol->ipf                  = $request->input('ipf');
                $dcontrol->cnc_rfn_endt         = $dcontrol_prev_record->endt_renewal_no;
                $dcontrol->branch_cod           = $request->input('branchpol');
                $dcontrol->currency             = $currency_code;
                $dcontrol->items_total          = 1;
                $dcontrol->proforma_reason = $request->input('proforma_reason');
                $dcontrol->policy_wording = $request->input('policy_wording');


                if($request->input('financed') == 'Y'){
                    $dcontrol->financed_code    = $request->input('financier');
                } else {
                    $dcontrol->financed_code    = '';
                }

                if ($request->input('co_ins') == 'Y') {
                    $dcontrol->company_share = $request->input('co_ins_share');
                } else {
                    $dcontrol->company_share = 100;  
                    // must be 100 for debit and reinsurances to work if no co insurance is done
                }

                if ($charge_vat == 'Y') {
                    $dcontrol->vat_type         = $get_vat_setup->vat_type;
                    $dcontrol->vat_description  = $get_vat_setup->vat_description;
                    $dcontrol->vat_rate         = $get_vat_setup->vat_rate;
                    $dcontrol->vat_code         = $request->input('vat_charged');
                } else {
                    $dcontrol->vat_rate         = 0;
                }

                $dcontrol->vat_type         = $dcontrol_prev_record->vat_type;
                $dcontrol->vat_description  = $dcontrol_prev_record->vat_description;
                $dcontrol->vat_rate         = $dcontrol_prev_record->vat_rate;
                $dcontrol->vat_code         = $dcontrol_prev_record->vat_code;

                # Master Policy No For 'Slave' Classes => Employer's Liability
                $dcontrol->master_policy = $dcontrol_prev_record->master_policy;
                $dcontrol->master_endt_no = $dcontrol_prev_record->master_endt_no;
                
                if($bustype_curr->fronting == 'Y'){
                    $dcontrol->ira_rate = $request->input('ira_rate');
                    $dcontrol->ipec_levy_loading_amount = $request->input('ipec_levy_loading_amount');
                    $dcontrol->fronting_rate = $request->input('fronting_rate');
                    $dcontrol->reinsurer_fronting_rate = $request->input('reinsurer_fronting_rate');
                    $dcontrol->fronting_stampduty_basis = $request->input('fronting_stampduty_basis');
                    $dcontrol->comm_rate = $request->input('fronting_comm_rate');
                }
                else{
                    $dcontrol->ira_rate = 0;
                    $dcontrol->ipec_levy_loading_amount = 0;
                    $dcontrol->fronting_rate = 0;
                    $dcontrol->comm_rate = 0;
                }
            break;

            # Marine Certificates
            case 'MAC':
                $dcontrol->dprop_no=$prop_no;
                $dcontrol->policy_no = $dcontrol_prev_record->policy_no;
                $dcontrol->prop_date = Carbon::now();
                $dcontrol->branch = $dcontrol_prev_record->branch;
                $dcontrol->agent = $dcontrol_prev_record->agent;
                $dcontrol->class = $dcontrol_prev_record->class;
                $dcontrol->user_str = Auth::User()->user_name;

                $dcontrol->period_from = $request->input('mac_period_from');
                $dcontrol->period_to = $request->input('mac_period_to');
                //$dcontrol->cov_period_from = $request->input('mac_period_from');
                //$dcontrol->cov_period_to = $request->input('mac_period_to');
                $dcontrol->cov_period_from = $marineopenpol->cov_period_from;
                $dcontrol->cov_period_to = $marineopenpol->cov_period_to;
                $dcontrol->expiry_date = $request->input('mac_period_to');
                $dcontrol->effective_date = $request->input('mac_period_from');
                $dcontrol->endorse_date = $request->input('mac_period_from');
                $dcontrol->branch_code = $dcontrol_prev_record->branch;
                $dcontrol->co_insure = $dcontrol_prev_record->co_insure;
                $dcontrol->co_ins_rate = $dcontrol_prev_record->co_ins_rate;
                $dcontrol->co_ins_base = $dcontrol_prev_record->co_ins_base;
                $dcontrol->type_of_bus = $dcontrol_prev_record->type_of_bus;
                $dcontrol->dept = $dcontrol_prev_record->dept;
                $dcontrol->actual_period_from = $request->input('mac_period_from');
                $dcontrol->actual_period_to = $request->input('mac_period_to');
                $dcontrol->financed = $dcontrol_prev_record->financed; //($request->input('financier') != '') ? 'Y' : 'N';
                $dcontrol->ast_marker = $dcontrol_prev_record->ast_marker;
                $dcontrol->items_total = 1;
                $dcontrol->branch_cod = $dcontrol_prev_record->branch;
                $dcontrol->ext_from = $request->input('mac_period_from');
                $dcontrol->ext_to = $request->input('mac_period_to');
                $dcontrol->currency = $dcontrol_prev_record->currency;
                //$dcontrol->currency_rate=  $request->input('today_currency');
                
                $dcontrol->financed_code = $dcontrol_prev_record->financed_code; //$request->input('financier');
                $dcontrol->company_share = $dcontrol_prev_record->company_share;

                //$dcontrol->vat_type = $get_vat_setup->vat_type;
                //$dcontrol->vat_description = $get_vat_setup->vat_description;
                //$dcontrol->vat_rate = $get_vat_setup->vat_rate;
                //$dcontrol->vat_code = $request->input('vat_charged');

                $dcontrol->vat_type         = $dcontrol_prev_record->vat_type;
                $dcontrol->vat_description  = $dcontrol_prev_record->vat_description;
                $dcontrol->vat_rate         = $dcontrol_prev_record->vat_rate;
                $dcontrol->vat_code         = $dcontrol_prev_record->vat_code;

                $dcontrol->ipf_repayment_date= $dcontrol_prev_record->ipf_repayment_date; //$request->input('ipf_repayment_date');
                
                //$dcontrol->workflow_id=1;
                $currency_rate = $dcontrol_prev_record->currency_rate;
                $dcontrol->mac_endt_no = $new_endt_renewal_no;

            break;
            case 'STK':
                $dcontrol->dprop_no = 0;
                $dcontrol->policy_no = $dcontrol_prev_record->policy_no;
                $dcontrol->prop_date = $dcontrol_prev_record->prop_date;
                $dcontrol->branch = $dcontrol_prev_record->branch;
                $dcontrol->agent = $dcontrol_prev_record->agent;
                $dcontrol->class = $dcontrol_prev_record->class;
                $dcontrol->client_number = $dcontrol_prev_record->client_number;
                $dcontrol->user_str = $username;
                // $dcontrol->user_str = $dcontrol_prev_record->user_str;
                $dcontrol->period_from = $dcontrol_prev_record->period_from;
                $dcontrol->period_to = $dcontrol_prev_record->period_to;
                $dcontrol->cov_period_from = $dcontrol_prev_record->cov_period_from;
                $dcontrol->cov_period_to = $dcontrol_prev_record->cov_period_to;

                $dcontrol->effective_date = $request->input('endorse_date');
                $dcontrol->endorse_date = $request->input('endorse_date');
                $dcontrol->branch_code = $dcontrol_prev_record->branch_code;
                $dcontrol->co_insure = $dcontrol_prev_record->co_insure;
                $dcontrol->co_ins_rate = $dcontrol_prev_record->co_ins_rate;
                $dcontrol->co_ins_base = $dcontrol_prev_record->co_ins_base;
                $dcontrol->type_of_bus = $dcontrol_prev_record->type_of_bus;
                $dcontrol->dept = $dcontrol_prev_record->dept;
                $dcontrol->actual_period_from = $dcontrol_prev_record->actual_period_from;
                $dcontrol->actual_period_to = $dcontrol_prev_record->actual_period_to;
                $dcontrol->financed = $dcontrol_prev_record->financed;
                $dcontrol->ast_marker = $dcontrol_prev_record->ast_marker;
                $dcontrol->short_term_method = $dcontrol_prev_record->short_term_method;
                $dcontrol->short_term_percent = $dcontrol_prev_record->short_term_percent;
                $dcontrol->items_total = 1;
                $dcontrol->branch_cod = $dcontrol_prev_record->branch_cod;
                $dcontrol->financed_code = $dcontrol_prev_record->financed_code;
                $dcontrol->currency = $request->currency;
                $currency_rate = $request->currency_rate;
                $dcontrol->currency_rate = $currency_rate;
               
                $dcontrol->vat_type = $request->vat_type;
                $dcontrol->vat_description = $get_vat_setup->vat_description;
                $dcontrol->vat_rate = $get_vat_setup->vat_rate;
                $dcontrol->vat_code = $get_vat_setup->vat_code;

                $dcontrol->ext_from = $dcontrol->period_from;
                $dcontrol->ext_to = $dcontrol->period_to;

                # Master Policy No For 'Slave' Classes => Employer's Liability
                $dcontrol->master_policy = $dcontrol_prev_record->master_policy;
                $dcontrol->master_endt_no = $dcontrol_prev_record->master_endt_no;

                $dcontrol->facin_premium = $dcontrol_prev_record->facin_premium;
                $dcontrol->facin_sum_insured = $dcontrol_prev_record->facin_sum_insured;
                $dcontrol->facin_comm_rate = $dcontrol_prev_record->facin_comm_rate;
                $dcontrol->ira_rate = $dcontrol_prev_record->ira_rate;
                $dcontrol->ipec_levy_loading_amount = $dcontrol_prev_record->ipec_levy_loading_amount;
                $dcontrol->fronting_rate = $dcontrol_prev_record->fronting_rate;
                $dcontrol->reinsurer_fronting_rate = $dcontrol_prev_record->reinsurer_fronting_rate;
                $dcontrol->fronting_stampduty_basis = $dcontrol_prev_record->fronting_stampduty_basis;
                $dcontrol->comm_rate = $dcontrol_prev_record->fronting_comm_rate;
                $dcontrol->facultin_client_no = $dcontrol_prev_record->facultin_client_no;

            break;
        }

        $dcontrol->endt_renewal_no = (string) $new_endt_renewal_no;
        $dcontrol->dtrans_no = $dtran_no;
        $dcontrol->insured = (string) $insured[0]->name;
        $dcontrol->trans_type = $transaction_type;
        $dcontrol->dola = Carbon::now();
        $dcontrol->sum_insured = 0;
        $dcontrol->location = 0;
        $dcontrol->time = Carbon::now();

        switch ($request->input('ast')) {
            case 'I':
                $dcontrol->plan = $request->input('plan');
                $dcontrol->plan_categ = $request->input('plan_categ');
                $dcontrol->instal_categ = $request->input('instal_categ');

                if ($transaction_type == "PTA" || $transaction_type == "NIL" || $transaction_type == "CNC" || $transaction_type == "CXT") {
                    // INS details
                    $dcontrol->plan = $dcontrol_prev_record->plan;
                    $dcontrol->plan_categ = $dcontrol_prev_record->plan_categ;
                    $dcontrol->instal_categ = $dcontrol_prev_record->instal_categ;
                }

                // create installmentstatus to track the plan that has been used 
                ($this->saveInstallmentstatus($dcontrol)); 
            break;
        }

        if($transaction_type == "PTA"){
            $dcontrol->expiry_date = $request->input('pta_period_to');
            $dcontrol->trans_type = $transaction_type;
        }else if($transaction_type == "NIL"){
            $dcontrol->expiry_date = $request->input('nil_period_to');
        }else if($transaction_type == "MAC"){
            $dcontrol->expiry_date = $request->input('mac_period_to');
        }
        else{
            $dcontrol->expiry_date = $request->input('period_to');
        }

        $dcontrol->pin_no = 'Y'; //$insured[0]->pin_number;
        $dcontrol->client_number = (string) $insured[0]->client_number;
        $dcontrol->surname = (string) $insured[0]->surname;
        $dcontrol->others = (string) $insured[0]->others;
        $dcontrol->first_name = (string) $insured[0]->first_name;
        $dcontrol->client_type = $insured[0]->client_type;

        if($transaction_type == 'POL') {

            $dcontrol->incept_date = $request->input('incept_date');
        }else if( $transaction_type == 'NIL'){
            if($nilType->slug == 'update-wrong-cover-period-captured'){
                $ch_incept = Dcontrol::where('policy_no',$dcontrol_rec_before_update->policy_no)
                                        ->whereRaw("TRANS_TYPE IN ('REN','RNS','CXT', 'EXT')")->count();
                if($ch_incept > 0){
                    $dcontrol->incept_date = $dcontrol_rec_before_update->incept_date;
                }else{
                   $dcontrol->incept_date = $request->input('nil_period_from'); 
                }
                
            }else{
                $dcontrol->incept_date = $dcontrol_rec_before_update->incept_date;
            }
        }else if ($transaction_type == 'EXT') {

            $dcontrol->incept_date = $dcontrol_rec_before_update->incept_date;
        }else if ($transaction_type == 'MAC') {

            $dcontrol->incept_date = $dcontrol_rec_before_update->incept_date;
        }
        else{
            $dcontrol->incept_date = $dcontrol_prev_record->incept_date;
        }
       
        $dcontrol->company_class_code = $request->input('class');
        $dcontrol->account_year = $account_year;
        $dcontrol->account_month = $account_month;
        $dcontrol->name = trim($insured[0]->name);
        //$dcontrol->pol_cert=
        $dcontrol->cancelled = 'N';
        $dcontrol->reg_no = '';
        $dcontrol->source = 'U/W';
        
        $dcontrol->currency_rate = $currency_rate;

      /*  if ($dept == '7' or $dept == '8') {

            $dcontrol->sticker_amount = 50;
        } else {

            $dcontrol->sticker_amount = 0;
        } */

        // copy amd add function


        //$dcontrol->committed=
        //$dcontrol->binder_no2=
        //$dcontrol->consult_code=
        //$dcontrol->consult_name=
        //$dcontrol->dispatched=
        //$dcontrol->dispatch_date=
        //$dcontrol->ceeding_branch=
        //$dcontrol->bond_no=

        //$dcontrol->prev_endt_renewal_no=
        //$dcontrol->authority_narration=
        //$dcontrol->authorizing_officer
        //$dcontrol->authority_flag=
        //$dcontrol->authorize_date=
        $dcontrol->binder_flag = 'N';
        //$dcontrol->policy_no_bind=
        $dcontrol->line_no = 0;
        $dcontrol->renewal_date = $renewal;

        $dcontrol->risk_note_no = $request->input('risk_note');
        $dcontrol->external_pol_no = $request->input('external_pol_number');
        //$taxInv = SmartTaxInvoices::where('customer_tax_invoice',$dcontrol->external_pol_no)->first();
        //$verification_code = $taxInv->verification_code;
        //$dcontrol->tax_invoice_no = $verification_code;

        $dcontrol->special_policy = $request->input('special_class');
        //scheme and binder policy
        if ($request->input('binder') == 'Y' || $request->input('binder') == 'S' || $dcontrol_rec_before_update->binder == "Y" || $dcontrol_rec_before_update->binder == "S" || $dcontrol_prev_record->binder == "Y" || $dcontrol_prev_record->binder == "S") {
            
            if($transaction_type == "POL") {
                $dcontrol->binder_pol_no = $request->input('binderpol');
                $dcontrol->bank_code = $request->input('bank');
                $dcontrol->bank_branch_code = $request->input('bank_branch');  
                $dcontrol->binder = $request->input('binder');
            }else{
                $dcontrol->binder_pol_no = $dcontrol_prev_record->binder_pol_no;
                $dcontrol->bank_code = $dcontrol_prev_record->bank_code;
                $dcontrol->bank_branch_code = $dcontrol_prev_record->bank_branch_code;
                //$dcontrol->risk_note_no = $dcontrol_prev_record->risk_note_no;
                //$dcontrol->external_pol_no = $dcontrol_prev_record->external_pol_no;
                $dcontrol->binder = $dcontrol_prev_record->binder;
            }
        }
      
        
        if($transaction_type == 'NIL'){

            if($nilType->slug == 'update-wrong-cover-period-captured'){
                if($request->input('ast') == 'A'){
                    $nil_period_from = Carbon::parse($request->input('nil_period_from'));
                    $nil_period_to = $nil_period_from->copy()->addYear();
                    // $nil_period_to = $nil_period_to->subDay();
    
                    $days_of_cover = $nil_period_from->diffInDays($nil_period_to);
                }else{
                    
                    $days_of_cover = $dcontrol_prev_record->days_covered;
                  
                }

            }else {
                $days_of_cover = $dcontrol_prev_record->days_covered;
            }
        }

        // dd($days_of_cover, $transaction_type, $nilType->slug);

        /***** MARINE OPEN COVER ****/
        if($cls->open_cover == 'Y'){
            $dcontrol->master_policy = $marine_policy_no;
            $dcontrol->master_endt_no = $marine_endt_no;
        }
        /***** MARINE OPEN COVER ****/
        
        $dcontrol->fleet = 'N';
        $dcontrol->days_covered = $days_of_cover;
        $dcontrol->pvt_cover = ''; //$request->input('pvt');

        if($transaction_type != 'CNC' && $transaction_type != 'RFN' && $transaction_type != 'RNS' & $transaction_type != 'MAC'){
            $dcontrol->endorse_date = $request->effective_date;
        }
        $dcontrol->global_account_comm_rate = $request->global_account_comm_rate;
        //if($dcontrol->type_of_bus == '4'){
        if (trim($bustype_curr->facult_in) == 'Y'){
            $get_name = DB::select("SELECT LPAD(c.branch, 3, 0) || c.agent as AS branch_agent, c.name 
            FROM crmast c INNER JOIN acctype a ON c.account_type = a.acc_type WHERE a.facultative = 'Y' 
            AND LPAD(c.branch, 3, 0) || c.agent = :facin_lead_agent", ['facin_lead_agent' => $request->facin_lead_agent])[0];  
            $dcontrol->facin_lead_agent = $get_name->name;
            $dcontrol->facin_branch_agent = $request->facin_lead_agent;
            $dcontrol->facin_comm_amt = str_replace(',','',$request->facin_comm_amt);
            $dcontrol->facin_total_sum_insured = str_replace(',','',$request->facin_total_sum_insured);
            $dcontrol->facin_sum_insured = str_replace(',','',$request->facin_sum_insured);
            $dcontrol->facin_premium = str_replace(',','',$request->facin_premium);
            $dcontrol->facin_comm_rate = $request->facin_comm_rate;
            $dcontrol->facin_premium_rate = $request->our_share;
            $dcontrol->company_share = $request->our_share;
            $dcontrol->facin_total_premium =str_replace(',','',$request->all_premium);
            $dcontrol->facultin_client_no = $request->facinclient;
            $dcontrol->ppw_code = $request->facinppw;
            $dcontrol->ppw_days = $ppw->ppw_days;
        }

        $policy_created = $dcontrol->save();


        //validate cover period before save
        $nilEndorseType = $request->input('nil_endorse_type');

        $validCover = $this->validateCovPeriod($dcontrol, $transaction_type);
       // $this->update_external_coverNote($dcontrol);
         
        //insert narration details

        if ($transaction_type == 'CNC') {
            $this->debitdtl_create($new_endt_renewal_no, $request->cnc_reason, $request->other_cnc_reason,$request->sticker_returned);
        }else if($transaction_type == 'RFN'){
            $this->debitdtl_create($new_endt_renewal_no, $request->rfn_reason_option, $request->rfn_reason,'N');
        }else if($transaction_type == 'NIL'){
            $this->debitdtl_create($new_endt_renewal_no, $request->nil_endorse_type, $request->nil_reason,$request->sticker_returned);
        }else if($transaction_type == 'RNS'){
            //$this->debitdtl_create($new_endt_renewal_no, '', $request->rns_reason,'N');
        }
        else if($transaction_type == 'STK'){
            $this->debitdtl_create($new_endt_renewal_no, '', $request->replacement_reason,'N');
        }

        // return ($transaction_type);
        /*add to polmaster*/

        $replicate_medical = new ReplicateMedicals;


        switch ($transaction_type) {
            case 'POL':
                $bustype_curr = Bustype::where('type_of_bus', trim($dcontrol->type_of_bus))->first();

                $this->add_polmaster((string) $new_endt_renewal_no, $uw_year);

                //add to workflows
                $this->add_to_workflow($new_endt_renewal_no, $request->input('workflow_id'), $request->input('pid'));

                if ($dcontrol->binder != 'Y' && $transaction_type != 'MAC') {
                    if ($bustype_curr->facult_in != 'Y'){
                        # Auto Populate Clauses

                        $classModel = ClassModel::where('class', $dcontrol->class)->first();
                        
                        if ($classModel->combined != 'Y') {
                            # Auto Populate Clauses
                            $this->autoPopulateClauses($new_endt_renewal_no);

                            $this->autoPopulateDisclaimer($new_endt_renewal_no);
                            $this->autoPopulateCoverage($new_endt_renewal_no);
                            $this->autoPopulateWarranty($new_endt_renewal_no);
                            $this->autoPopulateConditions($new_endt_renewal_no);
                            $this->autoPopulateScope($new_endt_renewal_no);
                            $this->autoPopulateExclusions($new_endt_renewal_no);
                        }

                        # Auto Populate Excesses
                        $this->autoLoadExcess($new_endt_renewal_no);

                        # Auto Populate Limits
                        $this->autoLoadLimits($new_endt_renewal_no); 
                    }
                }

                $this->update_bpdate_request($request->input('check_for_backpost_date'), $request->input('bp_request_no'), $new_endt_renewal_no);

                //set session variable to blink user created div
                Session::flash('success', 'Policy number ' . $new_endt_renewal_no . ' added sucessfully');

                // dd('stop here');

            break;

            case 'MAC':
                //add to workflows
                $this->add_to_workflow($new_endt_renewal_no, $request->input('workflow_id'), $request->input('pid'));

                # Replicate Polclause Details
                $this->replicate_polclause($new_endt_renewal_no,  $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Policy Limits
                $this->replicate_limits($new_endt_renewal_no,  $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Policy Excess
                $this->replicate_excess($new_endt_renewal_no,  $dcontrol_rec_before_update->endt_renewal_no);

                //modify polmaster
                $this->modify_polmaster((string) $new_endt_renewal_no, $uw_year);

                $this->replicate_DL($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                $this->update_bpdate_request($request->input('check_for_backpost_date'), $request->input('bp_request_no'), $new_endt_renewal_no);

            break;

            case 'EXT':
            case 'CXT':
            case 'STK':
                //modify modtl
              /*  if($transaction_type == 'CXT'){
                    $uw_year = $dcontrol_prev_record->uw_year;
                } */

                
                $this->modify_modtl($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                //modify polmaster
                $this->modify_polmaster((string) $new_endt_renewal_no, $uw_year);
                //set session variable to blink user created div

                $this->add_to_workflow($new_endt_renewal_no, $request->input('workflow_id'), $request->input('pid'));

                if($request->input('open_cover_ext_master') != 'Y'){

               
                    # Modify Proshed
                    $this->replicate_prosched($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                    # Delete Removed Records From Previous Endorsment Before Replicate
                    $this->delete_polsec($dcontrol_rec_before_update->endt_renewal_no);

                    # Replicate Polsec Details
                    $this->replicate_polsec($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                    # Replicate Sec_ext_reinsure Details
                    $this->replicate_secExtReinsure($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                    # Delete Removed Records From Previous Endorsment Before Replicate
                    $this->delete_polsched($dcontrol_rec_before_update->endt_renewal_no);

                    # Replicate Polsched Details
                    $this->replicate_polsched($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                    
                    # Replicate Marine Details
                    $this->replicate_madtl($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                    # Replicate Discounts and Loadings
                    $this->replicate_DL($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no); 

                    # Replicate Extensions
                    $this->replicate_extensions($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                    // Replicate accessories
                    $this->replicate_accessories($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);


                    # Replicate per capita and employee details
                    $this->replicate_FG_details($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                }

                # Replicate Polclause Details
                $this->replicate_polclause($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Policy Limits
                $this->replicate_limits($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Policy Excess
                $this->replicate_excess($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                if($request->input('open_cover_ext_master') != 'Y'){

                    # Update Polsect Details
                    $this->modify_polsect($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                
                    $this->saveStickerReplacementDtl($new_endt_renewal_no, $request);


                    $replicate_medical->replicateMedmember($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                    $replicate_medical->replicateMeddependants($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                    $replicate_medical->replicatePolmedprem($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                    $replicate_medical->replicatePolmedsec($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                    $replicate_medical->replicatePolmedlimit($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                    $replicate_medical->replicatePolmedoptplan($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                

                    # Update Endorse Amount to 0
                    $locations = Polsect::where('endt_renewal_no', $new_endt_renewal_no)->get();
                    $renDcontrol = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->first();

                    foreach ($locations as $location) {
                        # Access Risk Controller and Update Polsec
                        $c_cls = ClassModel::where('class', $location->class)->first();

                        if (/* $location->class != 102 && */ $c_cls->motor_policy != 'Y') {
                            $this->updatePolsec($renDcontrol->policy_no, $location->location, $renDcontrol->endt_renewal_no);
                            $this->update_polsect($renDcontrol->policy_no, $location->location, $renDcontrol->endt_renewal_no);
                            $this->update_polmaster($renDcontrol->endt_renewal_no);
                        }

                    }
                }
                if (/* $location->class != 102 && */ $c_cls->motor_policy == 'Y') 
                {
                    $mtrCtrl = new MotorProcessing;
                    $mtrCtrl->update_polmaster($new_endt_renewal_no);
                }

                $this->update_bpdate_request($request->input('check_for_backpost_date'), $request->input('bp_request_no'), $new_endt_renewal_no);

                Session::flash('success', 'New endorsement number ' . $new_endt_renewal_no . ' added sucessfully for policy number ' . $dcontrol_rec_before_update->policy_no);

            break;

            case 'PTA':
                $this->add_polmaster((string) $new_endt_renewal_no, $uw_year);
                $transaction = 'create';
                
                $this->saveComesaDtl($new_endt_renewal_no, $request, $transaction);

                $this->add_to_workflow($new_endt_renewal_no, $request->input('workflow_id'), $request->input('pid'));

                $this->update_bpdate_request($request->input('check_for_backpost_date'), $request->input('bp_request_no'), $new_endt_renewal_no);

                Session::flash('success', 'New endorsement number ' . $new_endt_renewal_no . ' added sucessfully for policy number ' . $dcontrol_rec_before_update->policy_no);

            break;

            case 'NIL':
                //modify modtl
                $this->modify_modtl($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);                

                //modify polmaster
                $this->modify_polmaster((string) $new_endt_renewal_no, $uw_year);
                //set session variable to blink user created div

                $this->add_to_workflow($new_endt_renewal_no, $request->input('workflow_id'), $request->input('pid'));

                # Modify Proshed
                $this->replicate_prosched($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Delete Removed Records From Previous Endorsment Before Replicate
                $this->delete_polsec($dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Polsec Details
                $this->replicate_polsec($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Sec_ext_reinsure Details
                $this->replicate_secExtReinsure($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Polclause Details
                $this->replicate_polclause($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Delete Removed Records From Previous Endorsment Before Replicate
                $this->delete_polsched($dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Polsched Details
                $this->replicate_polsched($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                
                # Replicate Marine Details
                $this->replicate_madtl($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Discounts and Loadings
                $this->replicate_DL($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Extensions
                $this->replicate_extensions($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                // Replicate accessories
                $this->replicate_accessories($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate per capita and employee details
                $this->replicate_FG_details($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Policy Limits
                $this->replicate_limits($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Policy Excess
                $this->replicate_excess($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Update Polsect Details
                $this->modify_polsect($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                // add naration details

                
                $replicate_medical->replicateMedmember($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                $replicate_medical->replicateMeddependants($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                $replicate_medical->replicatePolmedprem($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                $replicate_medical->replicatePolmedsec($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                $replicate_medical->replicatePolmedlimit($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);
                $replicate_medical->replicatePolmedoptplan($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                // cancel vehicles
                $dcontrol_curr = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->first();

                if($dcontrol_curr->nil_code == 3){
                    $this->cancelTransVehicle($dcontrol_prev_record->policy_no,$new_endt_renewal_no);
                    
                }

                if($dcontrol_curr->nil_code == 7){

                    $nilData = [
                        'cls' => $request->nil_loc_class,
                        'reg_no' => $request->nil_risk_item,
                        'endt_renewal_no' => $new_endt_renewal_no,
                        'policy_no' => $dcontrol_prev_record->policy_no,
                        'zero_refund' => 'Y',
                        'cncreason' => $request->nil_reason,
                    ];

                    $this->cancelMotorVehicleNil($nilData);
                    
                }

                $this->update_bpdate_request($request->input('check_for_backpost_date'), $request->input('bp_request_no'), $new_endt_renewal_no);

                Session::flash('success', 'New endorsement number ' . $new_endt_renewal_no . ' added sucessfully for policy number ' . $dcontrol_rec_before_update->policy_no);

            break;

            case 'INS':
            case 'REN':

                $renDcontrol = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->first();
                $check_cls = ClassModel::where('class', $renDcontrol->class)->first();

                if($check_cls->open_cover != 'Y'){
                    //modify modtl
                    $this->modify_modtl($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                }
                $this->add_to_workflow($new_endt_renewal_no, $request->input('workflow_id'), $request->input('pid'));
                //modify polmaster
                $this->modify_polmaster((string) $new_endt_renewal_no, $uw_year);
                //set session variable to blink user created div

                if($check_cls->open_cover != 'Y'){
                    # Modify Proshed
                    $this->replicate_prosched($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                    # Delete Removed Records From Previous Endorsment Before Replicate
                    $this->delete_polsec($dcontrol_prev_record->endt_renewal_no);

                    # Replicate Polsec Details
                    $this->replicate_polsec($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                    # Replicate Sec_ext_reinsure Details
                    $this->replicate_secExtReinsure($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                    # Delete Removed Records From Previous Endorsment Before Replicate
                    $this->delete_polsched($dcontrol_prev_record->endt_renewal_no);

                    # Replicate Polsched Details
                    $this->replicate_polsched($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                    # Replicate Marine Details
                    $this->replicate_madtl($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                    # Replicate Discounts and Loadings
                    $this->replicate_DL($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                    # Replicate Extensions
                    $this->replicate_extensions($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                    // Replicate accessories
                    $this->replicate_accessories($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                    # Replicate per capita and employee details
                    $this->replicate_FG_details($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                }

                # Replicate Polclause Details
                $this->replicate_polclause($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Policy Limits
                $this->replicate_limits($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Policy Excess
                $this->replicate_excess($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                if($check_cls->open_cover != 'Y'){
                    # Update Polsect Details
                    $this->modify_polsect($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                    
                    $replicate_medical->replicateMedmember($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                    $replicate_medical->replicateMeddependants($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                    $replicate_medical->replicatePolmedprem($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                    $replicate_medical->replicatePolmedsec($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                    $replicate_medical->replicatePolmedlimit($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                    $replicate_medical->replicatePolmedoptplan($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                    
                    # Calculate Renewal Premium and SI
                    $locations = Polsect::where('endt_renewal_no', $new_endt_renewal_no)->get();
                    $vehicles = Modtlmast::where('endt_renewal_no', $new_endt_renewal_no)->get();
                    $renDcontrol = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->first();
                    $check_cls = ClassModel::where('class', $renDcontrol->class)->first();

                    if($class->medical == 'Y'){
                        $med_risk = new Medicalrisk;

                        $med_risk->computeMedicalPremium($new_endt_renewal_no, $renDcontrol->policy_no);

                    }    

                    if ($renDcontrol->ast_marker == 'T') {
                        # Recalculate Time on Risk Premium
                        $this->timeOnRiskPremium($new_endt_renewal_no);
                    } else if ($renDcontrol->ast_marker == 'S' && $renDcontrol->short_term_method == 'S') {
                        # Recalculate Short Term Premium
                        $this->shortTermPremium($new_endt_renewal_no);
                    }else if ($renDcontrol->ast_marker == 'I') {
                        # Recalculate installment Premium
                        $this->installmentPremium($new_endt_renewal_no);
                    } else {
                        foreach ($locations as $location) {
                            # Access Risk Controller and Update Polsec
                            $c_cls = ClassModel::where('class', $location->class)->first();
        
                            if ($c_cls->motor_policy == 'Y') {                           

                                $this->update_polsect_motor($renDcontrol->policy_no, $location->location, $location->plot_no, $renDcontrol->endt_renewal_no);
                            }
        
                            if (/* $location->class != 102 && */ $c_cls->motor_policy != 'Y' && $c_cls->open_cover != 'Y') {
                                $this->updatePolsec($renDcontrol->policy_no, $location->location, $renDcontrol->endt_renewal_no);
                                $this->update_polsect($renDcontrol->policy_no, $location->location, $renDcontrol->endt_renewal_no);
                                $this->update_polmaster($renDcontrol->endt_renewal_no);
                            }
                        }

                        foreach ($vehicles as $veh) {

                            $motorCtrl = new MotorProcessing();
                            $motorCtrl->setProps(
                                endt_renewal_no: $dcontrol->endt_renewal_no,
                                reg_no: $veh->reg_no,
                                cls : $veh->class,
                                total_sum_insured : $veh->premium_dtl->total_sum_insured
                            );

                            $transSections = Motcvrdet::with('motor_group')
                                ->leftJoin('motorsect',function($join){
                                    $join->on('motorsect.grp_code','motcvrdet.grp_code');
                                    $join->on('motorsect.item_code','motcvrdet.item_code');
                                })
                                ->where('cancelled','<>','Y')
                                ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                                ->where('reg_no',$veh->reg_no)
                                ->get(['motcvrdet.*','motorsect.basis']);

                            foreach ($transSections as $transect) {
                                $section = [
                                    'group' => $transect->grp_code,
                                    'item' => $transect->item_code,
                                    'rate_amount' => $transect->basis == 'R' ? $transect->rate : $transect->annual_premium,
                                    'risk_value' => $transect->risk_value,
                                    'cancel' => 'N',
                                ];
                                
                                $section['rate_amount'] = $motorCtrl->get_minRateAmt($section);
                                $premium_amounts = $motorCtrl->compute_motor_premium($section,$dcontrol->ast_marker);
                                $resp = $motorCtrl->save_section_dtl($section,$premium_amounts);
                            }   
                            $motorCtrl->update_motor_summary($dcontrol->endt_renewal_no,$veh->class,$veh->reg_no);
                            $motorCtrl->update_polmaster($dcontrol->endt_renewal_no);
                        }
                    }
                }
                else{
                    $del = Polsect::where('policy_no',$dcontrol->policy_no)->delete();
                }

                $this->update_bpdate_request($request->input('check_for_backpost_date'), $request->input('bp_request_no'), $new_endt_renewal_no);

                Session::flash('success','Renewal number '.$new_endt_renewal_no.' added sucessfully for policy number '.$dcontrol_prev_record->policy_no);
            
            break;

            case 'RFN':
              //dd($dcontrol_prev_record);
                $this->add_to_workflow($new_endt_renewal_no, $request->input('workflow_id'), $request->input('pid'));

                $this->modify_modtl($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                
                //modify polmaster
                $this->modify_polmaster((string) $new_endt_renewal_no, $uw_year);
                //set session variable to blink user created div

                # Modify Proshed
                $this->replicate_prosched($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Delete Removed Records From Previous Endorsment Before Replicate
                $this->delete_polsec($dcontrol_prev_record->endt_renewal_no);

                # Replicate Polsec Details
                $this->replicate_polsec($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Sec_ext_reinsure Details
                $this->replicate_secExtReinsure($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Polclause Details
                $this->replicate_polclause($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Delete Removed Records From Previous Endorsment Before Replicate
                $this->delete_polsec($dcontrol_prev_record->endt_renewal_no);

                # Replicate Polsched Details
                $this->replicate_polsched($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Marine Details
                $this->replicate_madtl($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Discounts and Loadings
                $this->replicate_DL($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Extensions
                $this->replicate_extensions($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate per capita and employee details
                $this->replicate_FG_details($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Policy Limits
                $this->replicate_limits($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Policy Excess
                $this->replicate_excess($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Update Polsect Details
                $this->modify_polsect($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                
                $replicate_medical->replicateMedmember($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicateMeddependants($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedprem($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedsec($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedlimit($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedoptplan($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                $this->update_bpdate_request($request->input('check_for_backpost_date'), $request->input('bp_request_no'), $new_endt_renewal_no);

                $dcontrol_curr = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->get();

                $dcontrol_curr = $dcontrol_curr[0];


                 //update polmaster endorse_amount
                 $pol_upd=Polmaster::where('policy_no',$dcontrol_curr->policy_no)->update([
                    'endorse_amount'=>$refund_amount,
                    'status_date' => Carbon::parse($request->refund_date),
                    'sys_cnc_amt'=>$sys_calc_refund_amount
                  ]);
                  
            break;

            case 'CNC':
                $polno=$dcontrol_prev_record->policy_no;
                
                //flag certificate for DMVIC cancellation
                $cert_info = DB::select("select * from CERTALLOC where trim(POLICY_NO)='".$polno."'  and cert_status=1");
                $cert_info_count =count($cert_info);
                if ($cert_info_count > 0){
                  $this->cancelCert($cert_info);  
                  
                }
                $this->add_to_workflow($new_endt_renewal_no, $request->input('workflow_id'), $request->input('pid'));
                
                $this->modify_modtl($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                
                //modify polmaster
                $this->modify_polmaster((string) $new_endt_renewal_no, $uw_year);
                //set session variable to blink user created div

                # Modify Proshed
                $this->replicate_prosched($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Delete Removed Records From Previous Endorsment Before Replicate
                $this->delete_polsec($dcontrol_prev_record->endt_renewal_no);

                # Replicate Polsec Details
                $this->replicate_polsec($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Sec_ext_reinsure Details
                $this->replicate_secExtReinsure($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                $this->modify_secEXT($dcontrol_prev_record->policy_no,$new_endt_renewal_no);

                # Replicate Polclause Details
                $this->replicate_polclause($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Delete Removed Records From Previous Endorsment Before Replicate
                $this->delete_polsec($dcontrol_prev_record->endt_renewal_no);

                # Replicate Polsched Details
                $this->replicate_polsched($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Marine Details
                $this->replicate_madtl($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Discounts and Loadings
                $this->replicate_DL($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Extensions
                $this->replicate_extensions($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate per capita and employee details
                $this->replicate_FG_details($new_endt_renewal_no, $dcontrol_rec_before_update->endt_renewal_no);

                # Replicate Policy Limits
                $this->replicate_limits($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Policy Excess
                $this->replicate_excess($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Update Polsect Details
                $this->modify_polsect($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                // cancel vehicles
                $this->cancelTransVehicle($dcontrol_prev_record->policy_no,$new_endt_renewal_no);

                
                $replicate_medical->replicateMedmember($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicateMeddependants($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedprem($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedsec($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedlimit($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedoptplan($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                $this->update_bpdate_request($request->input('check_for_backpost_date'), $request->input('bp_request_no'), $new_endt_renewal_no);

                $dcontrol_curr = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->get();

                $dcontrol_curr = $dcontrol_curr[0];

                //cancel polmaster
                $pol_upd = Polmaster::where('policy_no', $dcontrol_curr->policy_no)->update([
                    'endorse_amount' => str_replace(",", "", $request->cnc_amt),
                    'status_code' => 'CNC',
                    'status_date' => Carbon::parse($request->cnc_effective_date),
                    'sys_cnc_amt' => str_replace(",", "", $request->sys_cnc_amt)
                ]);

            break;

            case 'RNS':
                $this->modify_modtl($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                
                //modify polmaster
                $this->modify_polmaster((string) $new_endt_renewal_no, $uw_year);
                //set session variable to blink user created div

                # Modify Proshed
                $this->replicate_prosched($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Delete Removed Records From Previous Endorsment Before Replicate
                $this->delete_polsec($dcontrol_prev_record->endt_renewal_no);

                # Replicate Polsec Details
                $this->replicate_polsec($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Sec_ext_reinsure Details
                $this->replicate_secExtReinsure($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Convert Sec_Ext_reinsure Endorse Amount to +Ve
                $secExtData = Sec_ext_reinsure::where('policy_no', $dcontrol_prev_record->policy_no)
                ->where('endt_renewal_no', $new_endt_renewal_no)
                ->get();

                foreach ($secExtData as $secExt) {
                    $secExtUpdate = Sec_ext_reinsure::where('policy_no', $dcontrol_prev_record->policy_no)
                    ->where('endt_renewal_no', $new_endt_renewal_no)
                    ->where('location', $secExt->location)
                    ->where('section_no', $secExt->section_no)
                    ->update([
                        'endorse_amount' => abs($secExt->endorse_amount),
                    ]);
                }

                # Replicate Polclause Details
                $this->replicate_polclause($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Delete Removed Records From Previous Endorsment Before Replicate
                $this->delete_polsec($dcontrol_prev_record->endt_renewal_no);

                # Replicate Polsched Details
                $this->replicate_polsched($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Marine Details
                $this->replicate_madtl($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Discounts and Loadings
                $this->replicate_DL($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Extensions
                $this->replicate_extensions($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate accessories
                $this->replicate_accessories($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate per capita and employee details
                $this->replicate_FG_details($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Policy Limits
                $this->replicate_limits($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Replicate Policy Excess
                $this->replicate_excess($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                # Update Polsect Details
                $this->modify_polsect($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                $replicate_medical->replicateMedmember($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicateMeddependants($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedprem($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedsec($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedlimit($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);
                $replicate_medical->replicatePolmedoptplan($new_endt_renewal_no, $dcontrol_prev_record->endt_renewal_no);

                $this->update_bpdate_request($request->input('check_for_backpost_date'), $request->input('bp_request_no'), $new_endt_renewal_no);

                $dcontrol_curr = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->get();
                $dcontrol_curr = $dcontrol_curr[0];

                # Update Polmaster Status
                $pol_upd = Polmaster::where('policy_no', $dcontrol_curr->policy_no)
                ->update([
                    'status_code' => 'ACT',
                    'status_date' => Carbon::parse($request->rns_effective_date),
                ]);

            break;
        }

        //access current policy;
        $dcontrol_curr = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->first();
        
        $bustype_curr = Bustype::where('type_of_bus', trim($dcontrol_curr->type_of_bus))->first();

        $class = ClassModel::where('class', $dcontrol_curr->class)->first();

        $department = Dept::where('dept', $class->dept)->get();
        $department = $department[0];

        /*************** REPLICATION OF COMBINED CLASSES ***************/

        if($class->combined == 'Y' && $transaction_type != 'POL'){
            
            $combinedPolicy = new CombinedPolicy($dcontrol_curr->policy_no,$new_endt_renewal_no);

            $combinedPolicy->replicate_polcmb();

            if($transaction_type == 'CNC' || $transaction_type == 'RFN' || $transaction_type=='RNS'){

                //$combinedPolicy->distribute_refund_amt();
            }

            if($transaction_type == 'REN'){

                $combinedPolicy->update_current_polcmb();
            }
            
        }
        
        /*************** END REPLICATION OF COMBINED CLASSES *************/

        // code here
        if ($transaction_type == "CNC" || $transaction_type == "RFN" ) {
            $cancellation = DB::table('pollimits')->select('pollimits.endt_renewal_no')
                ->join('dcontrol', 'pollimits.endt_renewal_no', '=', 'dcontrol.endt_renewal_no')
            
                ->where('dcontrol.policy_no', $policy_no1)->latest('dola')->first()->endt_renewal_no;

            $schem = schemaName();
            // dd($cancellation);

            $gb = $schem['gb'];
            $gl = $schem['gl'];
            $common = $schem['common'];

            $old_endorsement = $cancellation;

            $new_endorsement =$new_endt_renewal_no;

            $user_name = trim($username);

            //Compute refund amount for discounts
            $rfn_dl = (new Risk)->compute_dl_refund_amt($new_endt_renewal_no, $dcontrol_curr->cnc_rfn_endt);

            if($class->motor_policy == 'Y'){
                $motorCtrl = new MotorProcessing();
                $resp = $motorCtrl->cancel_motorSects($new_endt_renewal_no, $dcontrol_curr->cnc_rfn_endt);

                $cnc_rfn_endt = Dcontrol::where('endt_renewal_no',$dcontrol_curr->cnc_rfn_endt)->first('trans_type');
                $feesTransTypes = [
                    'STK',
                ];
                
                if(in_array($cnc_rfn_endt->trans_type,$feesTransTypes))
                {
                    // Polmaster::where('policy_no', $dcontrol_curr->policy_no)->update([
                    //     'endorse_amount' => $sys_calc_refund_amount,
                    // ]);
                }
                else 
                {

                }

            }else if ($class->medical == 'Y') {
                $med_risk = new Medicalrisk;
                $med_risk->cancelMedicals($new_endt_renewal_no, $dcontrol_curr->cnc_rfn_endt);
            }
            else if($class->open_cover == 'Y'){
                     
                $this->cancel_certificate($dcontrol_curr, $refund_amount);

             }
            else{ 

                $resp = (new Risk)->refund_nonmotorSects($dcontrol_curr->endt_renewal_no, $dcontrol_curr->cnc_rfn_endt);
                //allocate cmb rfn amount to individual classes
                // $procedureName = ''.$gb.'.allocate_cnc_refund';
                // $bindings = [
                // 'pol_no'  =>  $dcontrol_curr->policy_no,
                // 'endorsement_no'=>$dcontrol_curr->endt_renewal_no
                // ];

                // $resp= DB::executeProcedure($procedureName, $bindings);
                
            }
        } else if ($transaction_type == "RNS") {
            $schem = schemaName();

            $gb = $schem['gb'];
            $gl = $schem['gl'];
            $common = $schem['common'];

            $old_endorsement = $cancellation;
            $new_endorsement =$new_endt_renewal_no;
            $user_name = trim($username);

            if ($class->medical == 'Y') {
                $med_risk = new Medicalrisk();

                $med_risk->computeMedicalPremium($dcontrol_curr->endt_renewal_no, $dcontrol_curr->policy_no);
            }

            if($class->motor_policy == 'Y'){
                $motorCtrl = new MotorProcessing();

                $motorCtrl->set_initial_rns(true);
                
                $motorSect = Motcvrdet::where('policy_no',$dcontrol->policy_no)
                                    ->where('endt_renewal_no',$dcontrol_curr->endt_renewal_no)
                                    ->where('deleted','<>','Y')
                                    ->get();
                    
                foreach($motorSect as $sect){
                    $customRequest = new Request([
                        'cls' => $sect->class,
                        'grp_code' => $sect->grp_code,
                        'item_code' => $sect->item_code,
                        'reg_no' => $sect->reg_no,
                        'endt_renewal_no' => $dcontrol_curr->endt_renewal_no,
                        'actionType' => 'RNS'
                    ]);

                    $motorCtrl->cancelReinstate_section($customRequest);
                }
            }
            else{
                $procedureName = ''.$gb.'.allocate_reinstate_amt';
                $bindings = [
                    'pol_no'         => $dcontrol_curr->policy_no,
                    'endorsement_no' => $dcontrol_curr->endt_renewal_no
                ];

                $resp = DB::executeProcedure($procedureName, $bindings);


                # Update Endorse Amount to 0
                $locations = Polsect::where('endt_renewal_no', $new_endt_renewal_no)->get();
                $rnsDcontrol = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->first();

                foreach ($locations as $location) {
                    # Access Risk Controller and Update Polsec
                    $c_cls = ClassModel::where('class', $location->class)->first();

                    if (/* $location->class != 102 && */ $c_cls->motor_policy != 'Y') {
                        $this->updatePolsec($rnsDcontrol->policy_no, $location->location, $rnsDcontrol->endt_renewal_no);
                        $this->update_polsect($rnsDcontrol->policy_no, $location->location, $rnsDcontrol->endt_renewal_no);
                        $this->update_polmaster($rnsDcontrol->endt_renewal_no);
                    }

                }
            }
        }

        DB::commit();
    }
    catch(\Throwable $e){
        
            DB::rollback();
            report($e);
            dd($e);
            if($e->getCode() == ResponseCode::HTTP_EXPECTATION_FAILED){
                Session::Flash('error', $e->getMessage());
            }
            else{
                Session::Flash('error', 'Failed to create endorsement ');
            }
            $error_msg = $e->getMessage();
            $reference = "New endt_renewal_no: {$new_endt_renewal_no}";
            $module = __METHOD__;
            $route_name = Route::getCurrentRoute()->getActionName();

            log_error_details($route_name,$error_msg,$reference,$module);
            
            return redirect()->route('endorse_functions', ['policy_no' => $dcontrol->policy_no]);
    }

    /******** MARINE OPEN COVER THAT USES ONE POLICY NO ************/
    if($cls->open_cover == 'Y' && $pipcnam->mac_as_pol =='N' && ($transaction_type == 'POL' || $transaction_type == 'REN' || ($request->input('open_cover_ext_master') == 'Y'))){

        $marine_data =  [
                            'client_number'   => $insured[0]->client_number,
                            'insured'         => $insured[0]->name,
                            'days_covered'    => $days_of_cover,
                            'account_year'    => $account_year,
                            'account_month'   => $account_month,
                            'currency'        => $currency_code,
                            'currency_rate'   => $currency_rate,
                            'renewal_date'    => $renewal,
                            'vat_type'        => $get_vat_setup->vat_type,
                            'vat_description' => $get_vat_setup->vat_description,
                            'uw_year'         => $uw_year
                        ];

        $marine_open_cover = new MarineOpenProcessing();

        $marine_open_cover->setProps($dcontrol_curr->policy_no,
                                     $new_endt_renewal_no,
                                     $request->input('class'),
                                     $marine_data,
                                     $request);

        $marine_status = $marine_open_cover->marine_masterpol_processing();

        if($marine_status == true){
            // $this->autoPopulateDisclaimer($new_endt_renewal_no);
            // $this->autoPopulateCoverage($new_endt_renewal_no);
            // $this->autoPopulateWarranty($new_endt_renewal_no);
            // $this->autoPopulateConditions($new_endt_renewal_no);
            // $this->autoPopulateScope($new_endt_renewal_no);
            // $this->autoPopulateExclusions($new_endt_renewal_no);

            DB::commit();
            return redirect()->route('marine_open_dtl',['endt_renewal_no'=> $new_endt_renewal_no])->with('success','Marine Open Cover Policy '.$new_endt_renewal_no.' generated successfully');

        }
        else{

            DB::rollback();
            return redirect()->back()->with('error','Failed to generate Marine open cover');

        }  

    }
    /*******************/

        switch ($transaction_type) {
            case 'RFN':
                Session::flash('success','Successful, continue with the process');
                return redirect()->action(
                    'gb\underwriting\Policy_functions@index', ['endt_renewal_no' => $dcontrol_curr->endt_renewal_no]);

                break;
            case 'PTA':
                return redirect()->route('policy_functions',['endt_renewal_no'=>$dcontrol_curr->endt_renewal_no]);

                break;

            case 'POL':

                if ($class->combined == 'Y') {
                    Session::flash('success','Successful, you continue with the process');
                    $this->auto_add_combined_classes($dcontrol_curr->policy_no,$dcontrol_curr->endt_renewal_no,$dcontrol_curr->class);
                    
                    $this->autoPopulateCMBClauses($dcontrol_curr->endt_renewal_no);

                    return redirect()->route('risk', ['policy_no' => $dcontrol_curr->policy_no, 'endt_renewal_no' => $dcontrol_curr->endt_renewal_no, 'motor' => $department->motor, 'cmb' => 'Y','facult_in'=>trim($bustype_curr->facult_in)]);
                }

                $bustype_curr = Bustype::where('type_of_bus', trim($dcontrol_curr->type_of_bus))->get();
                $bustype_curr = $bustype_curr[0];

                $loc_serial = $this->location_serial($dcontrol_curr->policy_no);

                if ($class->bypass_location == 'Y'  && trim($bustype_curr->facult_in) !='Y') {
                    $this->add_polsect((string)$new_endt_renewal_no, (int)$dcontrol_curr->class, $loc_serial);
                    $this->add_polsectend((string)$new_endt_renewal_no, (int)$dcontrol_curr->class, $loc_serial);

                    Session::flash('success','Successful, you continue with the process');
                    return redirect()->route('polsched',['endorsement'=>$dcontrol_curr->endt_renewal_no,'location'=>1,'cls'=>$dcontrol_curr->class, 'bond_upd'=>$class->bond]);
                } 

                if (trim($bustype_curr->facult_in) == 'Y' && trim($bustype_curr->auto_add_location) =='Y') {
                    $this->add_polsect((string)$new_endt_renewal_no,(int)$dcontrol_curr->class, $loc_serial);
                    $this->add_polsectend((string)$new_endt_renewal_no,(int)$dcontrol_curr->class, $loc_serial);

                    Session::flash('success','Successful, you continue with the process');
                    return redirect()->route('policy_functions',['endt_renewal_no'=>$dcontrol_curr->endt_renewal_no]);
                }
                /*   if($dcontrol->dept != 2 && $dcontrol->dept != 3 && $dcontrol->dept != 4 && $dcontrol->dept != 7 && $dcontrol->dept != 8 && $dcontrol->dept != 12){
                    $this->add_polsect((string)$new_endt_renewal_no);

                    //{{Form::open(['url' => '/uw/polsched','id'=>'form_add_sections','method'=>'GET']) }}

                    return redirect()->route('polsched',['endorsement'=>$dcontrol->endt_renewal_no,'location'=>1,'cls'=>$dcontrol->class]);
                }
             */
                Session::flash('success','Successful');
                return redirect()->route('risk', ['policy_no' => $dcontrol_curr->policy_no, 'endt_renewal_no' => $dcontrol_curr->endt_renewal_no, 'motor' => $department->motor, 'cls' => $dcontrol_curr->class, 'cmb' => 'N','facult_in'=>trim($bustype_curr->facult_in)]);
                break;

            case 'MAC':
                $polmaster_prev = Polmaster::where('policy_no', $dcontrol_curr->policy_no)->update([
                    'total_premium' => 0,
                    'annual_premium' => 0,
                    'endorse_amount' => 0,
                    'sum_insured' =>0,
                    'total_sum_insured' =>0
                ]);

                $bustype_curr = Bustype::where('type_of_bus', trim($dcontrol_curr->type_of_bus))->get();
                $bustype_curr = $bustype_curr[0];

                $loc_serial = $this->location_serial($dcontrol_curr->policy_no);

                if ($class->bypass_location == 'Y'  && trim($bustype_curr->facult_in) !='Y') {
                    $this->add_polsect((string)$new_endt_renewal_no, (int)$dcontrol_curr->class, $loc_serial);
                    $this->add_polsectend((string)$new_endt_renewal_no, (int)$dcontrol_curr->class, $loc_serial);

                    Session::flash('success','Successful, you continue with the process');
                    return redirect()->route('polsched',['endorsement'=>$dcontrol_curr->endt_renewal_no,'location'=>1,'cls'=>$dcontrol_curr->class, 'bond_upd'=>$class->bond]);
                } 

                if (trim($bustype_curr->facult_in) == 'Y' && trim($bustype_curr->auto_add_location) =='Y') {
                    $this->add_polsect((string)$new_endt_renewal_no,(int)$dcontrol_curr->class, $loc_serial);
                    $this->add_polsectend((string)$new_endt_renewal_no,(int)$dcontrol_curr->class, $loc_serial);

                    Session::flash('success','Successful, you continue with the process');
                    return redirect()->route('policy_functions',['endt_renewal_no'=>$dcontrol_curr->endt_renewal_no]);
                }
                Session::flash('success','Successful');
                return redirect()->route('risk', ['policy_no' => $dcontrol_curr->policy_no, 'endt_renewal_no' => $dcontrol_curr->endt_renewal_no, 'motor' => $department->motor, 'cls' => $dcontrol_curr->class, 'cmb' => 'N','facult_in'=>trim($bustype_curr->facult_in)]);
                
            break;

            case 'EXT':
            case 'INS':
            case 'REN':
            case 'CXT':

                if (trim($class->motor_policy) == 'Y') {
                    if ($transaction_type == 'EXT') {
                        $polmaster_prev = Polmaster::where('policy_no', $dcontrol_curr->policy_no)->update([
                            'endorse_amount' => 0
                        ]);

                        
                        $polmaster_m = Polmaster::where('policy_no', $dcontrol_curr->policy_no)->first();
                        
    
                        $endt_no = trim($polmaster_m->endorse_no);
                    }
    
                }

                // dd($check_cls->motor_policy, 'ren endorse', $dcontrol_curr);




                if ($class->combined == 'Y') {

                    //dd($dcontrol_curr->endt_renewal_no);
                    // $polcmb_upd = Polcmb::where('policy_no', $dcontrol_curr->policy_no)->update([
                    //                 'endorse_amount' => 0
                    //               ]);

                    return redirect()->route('risk', ['policy_no' => $dcontrol_curr->policy_no, 'endt_renewal_no' => $dcontrol_curr->endt_renewal_no, 'motor' => $department->motor, 'cmb' => 'Y','facult_in'=>trim($bustype_curr->facult_in)]);
                }

                $bustype_curr = Bustype::where('type_of_bus', trim($dcontrol_curr->type_of_bus))->get();
                $bustype_curr = $bustype_curr[0];
                if ($class->bypass_location == 'Y' && trim($bustype_curr->facult_in) !='Y') {
                    // $this->add_polsect((string)$new_endt_renewal_no,(int)$dcontrol_curr->class);

                    return redirect()->route('polsched',['endorsement'=>$dcontrol_curr->endt_renewal_no,'location'=>1,'cls'=>$dcontrol_curr->class, 'bond_upd'=>'P']);
                }
        
                if (trim($bustype_curr->facult_in) == 'Y' && trim($bustype_curr->auto_add_location) =='Y') {
                    //$this->add_polsect((string)$new_endt_renewal_no,(int)$dcontrol_curr->class);

                    //Session::flash('success','Successful, you continue with the process');
                    return redirect()->route('policy_functions',['endt_renewal_no'=>$dcontrol_curr->endt_renewal_no]);
                }

                /*   if($dcontrol->dept != 2 && $dcontrol->dept != 3 && $dcontrol->dept != 4 && $dcontrol->dept != 7 && $dcontrol->dept != 8 && $dcontrol->dept != 12){
                    $this->add_polsect((string)$new_endt_renewal_no);

                    //{{Form::open(['url' => '/uw/polsched','id'=>'form_add_sections','method'=>'GET']) }}

                    return redirect()->route('polsched',['endorsement'=>$dcontrol->endt_renewal_no,'location'=>1,'cls'=>$dcontrol->class]);
                }
             */

                return redirect()->route('risk', ['policy_no' => $dcontrol_curr->policy_no, 'endt_renewal_no' => $dcontrol_curr->endt_renewal_no, 'motor' => $department->motor, 'cls' => $dcontrol_curr->class, 'cmb' => 'N','facult_in'=>trim($bustype_curr->facult_in)]);

                // return redirect()->route('risk',['policy_no' =>$dcontrol_curr->policy_no,'endt_renewal_no' =>$new_endt_renewal_no,'motor'=>$department->motor]);
                break;

            case 'NIL':
                //dd($request->input('nil_loc_class'));
                try {

                    //clone polmaster to polmasterend
                    $procedureName = ''.$gb.'.clone_polmaster_to_polmasteren';
                    $bindings = [
                        'policy_number' => $dcontrol_curr->policy_no
                    ];
                    
                    $resp= DB::executeProcedure($procedureName, $bindings);

                    DB::commit();

                }catch (\Throwable $e) {
                    //dd($e);
                    DB::rollback();

                }

                if($nilType->slug == 'update-risk-details'){
                  $nil_cls = ClassModel::where('class',(int)$request->input('nil_loc_class'))->first();

                  if($nil_cls->motor_policy == 'Y'){
                    return redirect()->route('nil_risk_amend', ['policy_no' => $dcontrol_curr->policy_no, 'old_endt_renewal_no'=>$dcontrol_curr->endt_renewal_no, 'endt_renewal_no'=>$new_endt_renewal_no, 'nil_endorse_type'=>$request->get('nil_endorse_type'), 'nil_risk_item'=>$request->get('nil_risk_item'),'motor_policy'=>'Y','cls'=>$request->input('nil_loc_class')]);
                  }else{
                    return redirect()->route('nil_risk_amend', ['policy_no' => $dcontrol_curr->policy_no, 'old_endt_renewal_no'=>$dcontrol_curr->endt_renewal_no, 'endt_renewal_no'=>$new_endt_renewal_no, 'nil_endorse_type'=>$request->get('nil_endorse_type'), 'nil_risk_item'=>$request->get('nil_risk_item'),'motor_policy'=>'N','cls'=>$request->input('nil_loc_class')]);
                  }

                }else if($nilType->slug == 'update-customer-details'){
                 //dd($dcontrol_curr->client_number);
                 return redirect()->route('edit_client',['client_number'=>$dcontrol_curr->client_number,'policy_no' => $dcontrol_curr->policy_no, 'endt_renewal_no'=>$dcontrol_curr->endt_renewal_no,'nil_endt'=>'Y']);
              
                }
                else if($nilType->slug == 'cancel-policy-with-zero-refund'){

                  $pol_upd = Polmaster::where('policy_no', $dcontrol_curr->policy_no)->update([
                                'endorse_amount' => 0,
                                'status_code' => 'CNC',
                                'status_date' => Carbon::parse($request->effective_date),
                                'sys_cnc_amt' => 0
                            ]);
 
                  return redirect()->action(
                    'gb\underwriting\Policy_functions@index',
                    ['endt_renewal_no' => $dcontrol_curr->endt_renewal_no]
                  );
                }
                else if($nilType->slug == 'modify-limits-clauses-and-excesses'){

                  return redirect()->action(
                    'gb\underwriting\Policy_functions@index',
                    ['endt_renewal_no' => $dcontrol_curr->endt_renewal_no]
                  );
                  
                }
                else if($nilType->slug == 'cancel-vehicle-with-zero-refund'){

                  Polmaster::where('policy_no', $dcontrol_curr->policy_no)
                            ->update([
                                    'endorse_amount' => 0,
                                    'sys_cnc_amt' => 0,
                                    'sys_endorse_amount' => 0
                                ]);
 
                  return redirect()->action(
                    'gb\underwriting\Policy_functions@index',
                    ['endt_renewal_no' => $dcontrol_curr->endt_renewal_no]
                  );
                }
                else if($nilType->slug == 'reverse-nil-policy-cancellation'){

                  $pol_upd = Polmaster::where('policy_no', $dcontrol_curr->policy_no)->update([
                                'endorse_amount' => 0,
                                'status_code' => 'ACT',
                                'status_date' => Carbon::parse($request->effective_date),
                                'sys_cnc_amt' => 0
                            ]);
 
                  return redirect()->action(
                    'gb\underwriting\Policy_functions@index',
                    ['endt_renewal_no' => $dcontrol_curr->endt_renewal_no]
                  );
                }
                else if($nilType->slug == 'update-financier-details'){
                    
                    $nil_audit = new Risk_audit_trail;

                    $nil_audit->policy_no = $dcontrol_curr->policy_no;
                    $nil_audit->endt_renewal_no = $dcontrol_curr->endt_renewal_no;
                    $nil_audit->risk_item = 'financier details';
                    $nil_audit->table_name = 'polmaster';
                    $nil_audit->field_changed = 'financed';
                    $nil_audit->old_value = $request->input('old_financed_nil');
                    $nil_audit->new_value = $request->input('financed_nil');
                    $nil_audit->date_changed = Carbon::now('EAT');
                    $nil_audit->ip_address=$request->ip();
                    
                    $nil_audit->system_user = Auth::user()->user_name;

                    $nil_audit->save();

                    $gt_financier = Financier::where('finance_code',$request->input('old_financier_nil'))->get()[0];
                    $gt_financier1 = Financier::where('finance_code',$request->input('financier_nil'))->get()[0];

                    $nil_audit = new Risk_audit_trail;

                    $nil_audit->policy_no = $dcontrol_curr->policy_no;
                    $nil_audit->endt_renewal_no = $dcontrol_curr->endt_renewal_no;
                    $nil_audit->risk_item = 'financier details';
                    $nil_audit->table_name = 'polmaster';
                    $nil_audit->field_changed = 'finance_code';
                    $nil_audit->old_value = $request->input('old_financier_nil').' - '.$gt_financier->name;
                    $nil_audit->new_value = $request->input('financier_nil').' - '.$gt_financier1->name;
                    $nil_audit->date_changed = Carbon::now('EAT');
                    $nil_audit->ip_address=$request->ip();

                    $nil_audit->system_user = Auth::user()->user_name;

                    $nil_audit->save();

                  /*  $audit1 = array(
                                  'policy_no'=>$policy_no,
                                  'endt_renewal_no'=>$endt_renewal_no,
                                  'risk_item'=>$location,
                                  'table_name'=>'polmaster',
                                  'field_changed'=>'financed',
                                  'old_value'=>$request->input('financed'),
                                  'new_value'=>$request->input('financed_nil'),
                                  'date_changed'=>Carbon::today(),
                                  'system_user'=>Auth::user()->user_name
                                );

                    $audit2 = array(
                                  'policy_no'=>$policy_no,
                                  'endt_renewal_no'=>$endt_renewal_no,
                                  'risk_item'=>$location,
                                  'table_name'=>'polmaster',
                                  'field_changed'=>'financed',
                                  'old_value'=>$request->input('financier'),
                                  'new_value'=>$request->input('financier_nil'),
                                  'date_changed'=>Carbon::today(),
                                  'system_user'=>Auth::user()->user_name
                                );

                    array_push($data, $audit1);
                    array_push($data, $audit2);

                    DB::table('risk_audit_trail')->insert($data); */
                      
                      return redirect()->action(
                        'gb\underwriting\Policy_functions@index',
                        ['endt_renewal_no' => $dcontrol_curr->endt_renewal_no]
                      );
                }
                else if($nilType->slug == 'update-wrong-cover-period-captured'){
                    $prev_endorse = Dcontrol::where('policy_no', $dcontrol_curr->policy_no)
                        ->where('endt_renewal_no','!=',$dcontrol_curr->endt_renewal_no)
                        ->orderBy('dtrans_no','desc')
                        ->first();
                    $REN_POL = Dcontrol::where('policy_no', $dcontrol_curr->policy_no)
                        ->where('endt_renewal_no','!=',$dcontrol_curr->endt_renewal_no)
                        ->whereIn('trans_type',['POL','REN'])
                        ->orderBy('dtrans_no','desc')
                        ->first();
                    // update debitmast
                    Debitmast::where('endt_renewal_no',$REN_POL->endt_renewal_no)
                        ->update([
                            'period_from' => $request->input('nil_period_from'),
                            'period_to' => $request->input('nil_period_to'),
                            'effective_date' => $request->input('effective_date')
                        ]);
                    // update acdet
                    Acdet::where('endt_renewal_no',$REN_POL->endt_renewal_no)
                        ->where('source','U/W')
                        ->update([
                            'date_effective' => $request->input('effective_date')
                        ]);
                    $policy_no = $dcontrol_curr->policy_no;
                    $old_endt_renewal_no = $prev_endorse->endt_renewal_no;
                    $endt_renewal_no =$dcontrol_curr->endt_renewal_no;
    
                    $this->nil_cover_period_trail($policy_no,$old_endt_renewal_no,$endt_renewal_no);
                    return redirect()->action(
                        'gb\underwriting\Policy_functions@index',
                        ['endt_renewal_no' => $dcontrol_curr->endt_renewal_no]
                    );
                }
                else if($nilType->slug == 'change-vehicle-value'){
                    return redirect()->route('motorprocessing.nilVehicleValue',['endt_renewal_no' =>$dcontrol_curr->endt_renewal_no]);
                }
                else if($nilType->slug == 'lapse-policy'){
                    DB::beginTransaction();

                    try {
                        $pol_upd = Polmaster::where('policy_no', $dcontrol_curr->policy_no)->update([
                            'lapse_date' => $request->input('lapse_date'),
                            'who_lapsed' => $request->who_lapsed? $request->who_lapsed: auth()->user()->user_name,
                            'why_lapsed' => $request->nil_reason,
                            'status_code' => 'LPS',
                            'status_date' => $request->input('effective_date'),
                            'endorse_date' => $request->input('effective_date'),
                        ]);

                        DB::commit();
                        if($request->system_generated =='Y'){
                            return 1;
                        }

                        return redirect()->action(
                            'gb\underwriting\Policy_functions@index',
                            ['endt_renewal_no' => $dcontrol_curr->endt_renewal_no]
                        );
                    } catch (\Exception $e) {
                        DB::rollback();

                        return back()->withError('Error updating lapse policy: ' . $e->getMessage())->withInput();
                    }
                }
                else if($nilType->slug == 'activate-lapsed-policy'){
                    // Activate lapsed Policy
                  
                    DB::beginTransaction();

                    try {
                        $pol_upd = Polmaster::where('policy_no', $dcontrol_curr->policy_no)->update([
                            'lapse_date' => '',
                            'who_lapsed' => '',
                            'why_lapsed' => '',
                            'status_code' => 'ACT',
                            'endorse_date' => $request->input('effective_date'),
                            'status_date' =>$request->input('effective_date'),
                        ]);

                        DB::commit();

                        if ($request->system_generated=='Y') {
                            return 1;
                        }

                        return redirect()->action(
                            'gb\underwriting\Policy_functions@index',
                            ['endt_renewal_no' => $dcontrol_curr->endt_renewal_no]
                        );
                    } catch (\Exception $e) {
                        DB::rollback();

                        return back()->withError('Error updating lapse policy: ' . $e->getMessage())->withInput();
                    }
                   
                }
                break;

            case 'CNC':
                Session::flash('success','Policy Cancelled Successfully');
                return redirect()->action(
                    'gb\underwriting\Policy_functions@index',
                    ['endt_renewal_no' => $dcontrol_curr->endt_renewal_no]
                );

                break;

            case 'RNS':
                //dd($dcontrol_curr->endt_renewal_no);
                Session::flash('success','Policy Reinstated Successfully');
                // return redirect()->action(
                //     'gb\underwriting\Policy_functions@index',
                //     ['endt_renewal_no' => $dcontrol_curr->endt_renewal_no]
                // );
                
                // go to risk details screen
                return redirect()->route('risk', ['policy_no' => $dcontrol_curr->policy_no, 'endt_renewal_no' => $dcontrol_curr->endt_renewal_no, 'motor' => $department->motor, 'cls' => $dcontrol_curr->class, 'cmb' => 'N','facult_in'=>trim($bustype_curr->facult_in)]);

                break;
            case 'STK':
                Session::flash('success','Sticker Replacement Transaction Was Successful');
                return [
                    'status' => 1,
                    'message' => 'Sticker Replacement Transaction Was Successful',
                    'endt_renewal_no' => $dcontrol_curr->endt_renewal_no,
                ];

                break;
            
        }
}

    public function double_insurance_check(Request $request){
        $dcontrolPol = Dcontrol::where('endt_renewal_no', $request->previous_endt_renewal_no)->first();
        $double_not_allowed = ['REN', 'POL'];

        $status = 1;
        $message = '';
        $trans_type = $request->type;

        $dcontrolQuery = Dcontrol::Where('client_number',$request->insured)
                                    ->Where('branch',$request->branchpol)
                                    ->Where('agent',$request->agentpol)
                                    ->Where('class',$request->class)
                                    ->Where('trans_type',$trans_type)
                                    ->Where('period_from',$request->period_from)
                                    ->Where('period_to',$request->period_to)
                                    ->Where('cancelled','N');

        // dd($request->previous_endt_renewal_no,$request->insured,$request->branchpol,$request->agentpol,$request->class,$trans_type,$request->period_from,$request->period_to,$dcontrolCount,$dcontrolPol);

        if ($trans_type === 'REN' && $dcontrolPol) {
            $dcontrolQuery->Where('policy_no', $dcontrolPol->policy_no);
        }

        $dcontrolCount = $dcontrolQuery->count();
        
        if(in_array($trans_type, $double_not_allowed) && $dcontrolCount > 0) {
            $status = 0 ;
            $message = 'Double insurance detected! A similar transaction exists with matching Insured, class, intermediary and cover period Exists !!';
        }

        return [
                'status' => $status,
                'message' => $message,
                'trans_type' => $trans_type
            ];

    }


    public function update_polsect_motor($policy_no, $location,$plot_no, $endt_renewal_no)
    {
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $polsect = Polsect::where('endt_renewal_no', $endt_renewal_no)
                            ->where('plot_no', $plot_no)
                            ->first();

        $prorated_amount = $prorated_amount = $this->compute_short_term_motor($dcontrol->endt_renewal_no, $polsect->annual_premium);
                            
        $polsect->endorse_amount =  $polsect->prorated_amount;
        $polsect->save();
                          
    }
    public function debitdtl_create($endorsement_no, $detail_code, $other_reason,$cert_returned){
        $dcontrol = Dcontrol::where('endt_renewal_no',$endorsement_no)
                            ->get();

        
      
  
        $dcontrol = $dcontrol[0];

        if($dcontrol->trans_type == "NIL"){
            $endorse_descr = Nil_endorsetypes::where('endt_type',$detail_code)->get()[0];
        }else{
            $endorse_descr = Endorse_descr::where('descr_code',$detail_code)->get()[0];
        } 


        if($dcontrol->doc_type == 'DRN'){
          $dr_cr = 'D';
        }
        else if($dcontrol->doc_type == 'CRN'){
          $dr_cr = 'C';
        }
        else{
          $dr_cr = "";
        }
        $line_no = Debitdtl::where('policy_no', $dcontrol->policy_no)->count();
        $new_line_no = (int)$line_no + 1;
        
        if($dcontrol->trans_type == "NIL"){
            $detail = $endorse_descr->endt_descr;
          }else{
            $detail  = $endorse_descr->descr_name;
          }
        $debitdtl = Debitdtl::create([
          'policy_no'=>$dcontrol->policy_no,
          'detail'=>$detail,
          'line_no' => $new_line_no,
          'endt_renewal_no'=>$dcontrol->endt_renewal_no,
          'dtrans_no'=>$dcontrol->dtrans_no,
          'dr_cr'=>$dr_cr,
          'dola'=>Carbon::today(),
          'effective_date'=>$dcontrol->effective_date,
          'detail_code'=>$detail_code,
          'detail2'=>$other_reason,
          'doc_type'=>$dcontrol->doc_type,
          'sticker_returned'=>$cert_returned
        ]);
      }

    public function add_polsect($endt_renewal_no, $cls, $loc)
    {

        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol = $dcontrol[0];

        $bustype = Bustype::where('type_of_bus', trim($dcontrol->type_of_bus))->get();
        $bustype = $bustype[0];

        $count_p = Polsect::where('policy_no', $dcontrol->policy_no)->count();

        $clsrec = ClassModel::where('class', $cls)->first();

        //$loc = $count_p + 1;

        $polsect = new Polsect;

        $polsect->policy_no = $dcontrol->policy_no;
        $polsect->proposal_no = (string) $dcontrol->dprop_no;
        $polsect->location = $loc;
        $polsect->class = $cls;
        $polsect->town = '';
        $polsect->street = '';
        $polsect->endt_renewal_no = $dcontrol->endt_renewal_no;
        //$polsect->endt_renewal_no = ;
        //$polsect->endt_renewal_no = ;
        $polsect->effective_date = Carbon::now();

        //$polsect->save();

        $polsect->policy_no = $dcontrol->policy_no;
        $polsect->proposal_no = $dcontrol->dprop_no;
        $polsect->dtrans_no = $dcontrol->dtrans_no;
        $polsect->line_no = $loc;
        $polsect->endt_renewal_no = $dcontrol->endt_renewal_no;
        //$polsect->class=$cls;
        //$polsect->location=1;
        $polsect->plot_no = ($clsrec->marine == 'Y') ? 'PENDING' : $dcontrol->client_number;
        $polsect->effective_date = $dcontrol->effective_date;
        $polsect->town = '';
        $polsect->street = '';
        $polsect->section_sum_insured_1 = 0;
        $polsect->section_sum_insured_2 = 0;
        $polsect->section_sum_insured_3 = 0;
        $polsect->section_sum_insured_4 = 0;
        $polsect->section_sum_insured_5 = 0;
        $polsect->section_sum_insured_6 = 0;
        $polsect->section_liab_1 = 0;
        $polsect->section_liab_2 = 0;
        $polsect->section_liab_3 = 0;
        $polsect->section_liab_4 = 0;
        $polsect->section_liab_5 = 0;
        $polsect->section_liab_6 = 0;
        $polsect->section_premium_1 = 0;
        $polsect->section_premium_2 = 0;
        $polsect->section_premium_3 = 0;
        $polsect->section_premium_4 = 0;
        $polsect->section_premium_5 = 0;
        $polsect->section_premium_6 = 0;
        $polsect->total_premium = 0;
        $polsect->total_sum_insured = 0;
        $polsect->renewal_premium = 0;
        $polsect->first_premium = 0;
        $polsect->premium_todate = 0;
        $polsect->content_first_rate = 0;
        $polsect->period = 0;
        $polsect->basic_rate = 0;
        $polsect->discount_base = 0;
        $polsect->first_sum = 0;
        $polsect->fire_premium = 0;
        $polsect->quake_premium = 0;
        $polsect->zone = 0;
        $polsect->area = 0;
        $polsect->quake_rate_base = 0;
        $polsect->quake_rate = 0;
        $polsect->first_fire_premium = 0;
        $polsect->first_quake_premium = 0;
        $polsect->endorse_amount = 0;
        $polsect->prev_premium = 0;
        $polsect->fire_endorse_amt = 0;
        $polsect->quake_endorse_amt = 0;
        $polsect->prev_fire_premium = 0;
        $polsect->prev_quake_premium = 0;
        $polsect->company_class_code = $cls;
        $polsect->escalate_rate_base = 100;
        $polsect->declare_rate_base = 100;
        $polsect->total_quake_premium = 0;
        $polsect->total_fire_premium = 0;
        $polsect->todate_fire_premium = 0;
        $polsect->todate_quake_premium = 0;
        $polsect->sum_insured = 0;
        $polsect->total_first_premium = 0;
        $polsect->incept_date = $dcontrol->incept_date;
        $polsect->name = ($clsrec->marine == 'Y') ? 'PENDING' : $dcontrol->name;
        $polsect->installment_premium = 0;
        $polsect->multiple_of_salary = 0;
        $polsect->annual_premium = 0;
        $polsect->prev_annual = 0;
        $polsect->prev_total = 0;
        $polsect->total_liability_amt = 0;
        $polsect->liability_amt = 0;
        $polsect->none_proratable = 0;
        $polsect->prev_none_proratable = 0;
        $polsect->annual_monthly_salary = 0;
        $polsect->total_discounts = 0;
        $polsect->total_loadings = 0;
        $polsect->total_premium_short = 0;
        $polsect->owner_lib = 0;
        $polsect->occupier_lib = 0;
        $polsect->no_units = 0;
        $polsect->currency_code = $dcontrol->currency;
        $polsect->currency_rate = $dcontrol->currency_rate;
        $polsect->no_of_lifes_endorsed = 0;
        //$polsect->line_no=$location;                
        $polsect->pvt_prem = 0;
        $polsect->pvt_prev_total = 0;
        $polsect->pvt_prev_premium = 0;

        if (trim($bustype->facult_in) == 'Y' && trim($bustype->auto_add_location) =='Y') {
            $polsect->section_sum_insured_1 = $dcontrol->facin_sum_insured;
            $polsect->section_premium_1 =$dcontrol->facin_premium;
            $polsect->total_premium = $dcontrol->facin_premium;
            $polsect->total_sum_insured = $dcontrol->facin_sum_insured;
            $polsect->renewal_premium = $dcontrol->facin_premium;
            $polsect->sum_insured = $dcontrol->facin_sum_insured;
            $polsect->total_first_premium = $dcontrol->facin_premium;
            $polsect->endorse_amount = $dcontrol->facin_premium;
            $polsect->fire_endorse_amt = $dcontrol->facin_premium;
            $polsect->annual_premium = $dcontrol->facin_premium;
        }

        $polsect->mac_endt_no = $dcontrol->mac_endt_no;

        $polsect->save();
    }

    public function add_polmaster($endt_renewal_no, $uw_year)
    {

        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol = $dcontrol[0];

        $class = Classmodel::where('class', $dcontrol->class)->first();

        $bustype = Bustype::where('type_of_bus', trim($dcontrol->type_of_bus))->get();
        $bustype = $bustype[0];

        $polmaster = new Polmaster;
        $polmaster->policy_no = (string) $dcontrol->policy_no;
        $polmaster->endorse_no = (string) $dcontrol->endt_renewal_no;
        $polmaster->proposal_no = (string) $dcontrol->dprop_no;
        $polmaster->name = (string) $dcontrol->name;
        $polmaster->type_of_bus = $dcontrol->type_of_bus;
        $polmaster->incept_date = (string) $dcontrol->incept_date;
        $polmaster->period_from = (string) $dcontrol->period_from;
        $polmaster->period_to = (string) $dcontrol->period_to;
        $polmaster->cov_period_from = (string) $dcontrol->cov_period_from;
        $polmaster->cov_period_to = (string) $dcontrol->cov_period_to;
        $polmaster->expiry_date = (string) $dcontrol->expiry_date;
        $polmaster->renewal_date = (string) $dcontrol->renewal_date;
        $polmaster->endorse_date = (string) $dcontrol->endorse_date;
        $polmaster->plan = (string) $dcontrol->plan;
        $polmaster->plan_categ = (string) $dcontrol->plan_categ;
        $polmaster->instal_categ = (string) $dcontrol->instal_categ;
        $polmaster->dola = Carbon::now();
        $polmaster->period = (int) $dcontrol->account_year.str_pad($dcontrol->account_month,2,"0",STR_PAD_LEFT);
        $polmaster->ast_marker = (string) $dcontrol->ast_marker;
        $polmaster->tran_no = $dcontrol->tran_no;
        $polmaster->client_number = (string) $dcontrol->client_number;
        $polmaster->uw_year = $uw_year;
        $polmaster->annual_premium = 0;
        $polmaster->first_premium = 0;
        $polmaster->fire_premium = 0;
        $polmaster->first_fire_premium = 0;
        $polmaster->quake_premium = 0;
        $polmaster->first_quake_premium = 0;
        $polmaster->premium_todate = 0;
        $polmaster->renewal_premium = 0;
        $polmaster->endorse_amount = 0;
        $polmaster->fire_endorse_amt = 0;

        $polmaster->prev_premium = 0;
        $polmaster->total_sum_insured = 0;
        $polmaster->total_premium = 0;
        $polmaster->total_liability_amt = 0;
        $polmaster->company_share = 0;
        $polmaster->sum_insured = 0;

        if ($dcontrol->co_insure == 'Y') {

            $polmaster->company_share = $dcontrol->company_share;
        }
        

        if (trim($bustype->facult_in) == 'Y' && trim($bustype->auto_add_location) =='Y') {
            $polmaster->annual_premium = $dcontrol->facin_premium;
            $polmaster->first_premium = $dcontrol->facin_premium;
            $polmaster->renewal_premium = $dcontrol->facin_premium;
            $polmaster->endorse_amount = $dcontrol->facin_premium;
            $polmaster->total_sum_insured = $dcontrol->facin_sum_insured;
            $polmaster->total_premium = $dcontrol->facin_premium;
            $polmaster->sum_insured = $dcontrol->facin_sum_insured ;
        }
        $polmaster->liability_amt = 0;
        $polmaster->fleet_amount = 0;
        $polmaster->ncd_amount = 0;
        $polmaster->windscreen = 0;
        $polmaster->items_total = $dcontrol->items_total;
        $polmaster->branch = $dcontrol->branch;
        $polmaster->agent_no = $dcontrol->agent;
        $polmaster->class = (string) $dcontrol->class;
        $polmaster->dtrans_no = $dcontrol->dtrans_no;
        $polmaster->created_by = (string) trim($dcontrol->user_str);
        if ($dcontrol->trans_type == 'POL') {
            $polmaster->time_created = Carbon::now();
        }
        $polmaster->company_class_code = (string) $dcontrol->class;
        $polmaster->finance_code = $dcontrol->financed_code;
        $polmaster->financed = $dcontrol->financed;
        $polmaster->currency_code = $dcontrol->currency;
        $polmaster->combined = $class->combined;
        $polmaster->premium_method = $dcontrol->ast_marker;
        $polmaster->trans_type = (string) $dcontrol->trans_type;
        $polmaster->total_loadings = 0;
        $polmaster->total_discounts = 0;
        $polmaster->sticker_amount = $dcontrol->sticker_amount;
        $polmaster->binder_pol_no = $dcontrol->binder_pol_no;
        $polmaster->bank_code = $dcontrol->bank_code;
        $polmaster->bank_branch_code = $dcontrol->bank_branch_code;
        $polmaster->risk_note_no = $dcontrol->risk_note_no;
        $polmaster->binder = $dcontrol->binder;
        $polmaster->pvt_cover = 'N';
        $polmaster->pvt_prem = 0;
        $polmaster->status_code = 'ACT';

        $polmaster->save();
    }



    public function modify_polmaster($endt_renewal_no, $uw_year)
    {

        //find dcontrol record
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol = $dcontrol[0];
        $polmaster_curr = Polmaster::where('policy_no', $dcontrol->policy_no)->first();

        $class_motor = ClassModel::where('class', trim($polmaster_curr->class))->first();

        $bustype = Bustype::where('type_of_bus', trim($dcontrol->type_of_bus))->get();
        $bustype = $bustype[0];

        if (trim($bustype->facult_in) == 'Y' && trim($bustype->auto_add_location) =='Y') {
            $annual_premium = $dcontrol->facin_premium;
            $first_premium = $dcontrol->facin_premium;
            $renewal_premium = $dcontrol->facin_premium;
            $endorse_amount = $dcontrol->facin_premium;
            $total_sum_insured = $dcontrol->facin_sum_insured;
            $total_premium = $dcontrol->facin_premium;
            $sum_insured = $dcontrol->facin_sum_insured;
            if ($dcontrol->trans_type == 'RNS') {
                $endorse_amount = abs($dcontrol->facin_premium);
            }

        }
       
        else{
            $annual_premium = $polmaster_curr->annual_premium;
            $first_premium = $polmaster_curr->first_premium;
            $renewal_premium = $polmaster_curr->renewal_premium;
            $endorse_amount = $polmaster_curr->endorse_amount;
            $total_sum_insured = $polmaster_curr->total_sum_insured;
            $total_premium = $polmaster_curr->total_premium;
            $sum_insured = $polmaster_curr->sum_insured;
            if ($dcontrol->trans_type == 'RNS') {
                $endorse_amount = abs($polmaster_curr->endorse_amount);
            }
        }

        if ($dcontrol->trans_type == 'REN') {

            if (trim($class_motor->motor_policy) == 'Y') {
                $endorse_amount = $polmaster_curr->renewal_premium;
            }
        }

        $cov_period_from = ($dcontrol->trans_type == 'MAC') ? $polmaster_curr->cov_period_from : $dcontrol->cov_period_from;
        $cov_period_to = ($dcontrol->trans_type == 'MAC') ? $polmaster_curr->cov_period_to : $dcontrol->cov_period_to;
        $incept_date = ($dcontrol->trans_type == 'MAC') ? $polmaster_curr->incept_date : $dcontrol->incept_date;
        $expiry_date = ($dcontrol->trans_type == 'MAC') ? $polmaster_curr->expiry_date : $dcontrol->expiry_date;
        $renewal_date = ($dcontrol->trans_type == 'MAC') ? $polmaster_curr->renewal_date : $dcontrol->renewal_date;

       


        // dd($endorse_amount, $polmaster_curr->trans_type);

        //find polmaster record for update
        $polmaster = Polmaster::where('policy_no', $dcontrol->policy_no)
        ->update([
            'endorse_no' => $dcontrol->endt_renewal_no,
            'name' => (string)$dcontrol->name,
            'type_of_bus' => $dcontrol->type_of_bus,
            'period_from' => (string)$dcontrol->period_from,
            'period_to' => (string)$dcontrol->period_to,
            'ext_from' => (string)$dcontrol->ext_from,
            'ext_to' => (string)$dcontrol->ext_to,
            'cov_period_from' => (string) $cov_period_from,
            'cov_period_to' => (string) $cov_period_to,
            'incept_date' => (string)$incept_date,
            'expiry_date' => (string)$expiry_date,
            'renewal_date' => (string)$renewal_date,
            'endorse_date' => (string)$dcontrol->endorse_date,
            'dola' => Carbon::today(),
            'period' => (int) $dcontrol->account_year.str_pad($dcontrol->account_month,2,"0",STR_PAD_LEFT),
            'ast_marker' => (string)$dcontrol->ast_marker,
            'tran_no' => $dcontrol->tran_no,
            'client_number' => (string)$dcontrol->client_number,
            'uw_year' => $uw_year,
            'prev_premium' => 0,
            'branch' => $dcontrol->branch,
            'agent_no' => $dcontrol->agent,
            'class' => (string)$dcontrol->class,
            'dtrans_no' => $dcontrol->dtrans_no,
            'created_by' => (string) trim($dcontrol->user_str),
            'finance_code' => $dcontrol->financed_code,
            'company_class_code' => (string) $dcontrol->class,
            'financed' => $dcontrol->financed,
            'currency_code' => $dcontrol->currency_code,
            'premium_method' => $dcontrol->ast_marker,
            'trans_type' => $dcontrol->trans_type,
            'sticker_amount' => $dcontrol->sticker_amount,
            'annual_premium' => $annual_premium,
            'first_premium'=> $first_premium,
            'renewal_premium' => $renewal_premium,
            'endorse_amount' => $endorse_amount,
            'total_sum_insured' => $total_sum_insured,
            'total_premium' => $total_premium,
            'sum_insured' => $sum_insured 
        ]);
        /*
            // $polmaster = Polmaster::where('policy_no', $dcontrol->policy_no)->first();
            // $polmaster->endorse_no = $dcontrol->endt_renewal_no;
            // $polmaster->name = (string) $dcontrol->name;
            // $polmaster->type_of_bus = $dcontrol->type_of_bus;
            // $polmaster->period_from = (string) $dcontrol->period_from;
            // $polmaster->period_to = (string) $dcontrol->period_to;
            // $polmaster->expiry_date = (string) $dcontrol->expiry_date;
            // $polmaster->renewal_date = (string) $dcontrol->renewal_date;
            // $polmaster->endorse_date = (string) $dcontrol->endorse_date;
            // $polmaster->dola = Carbon::today();
            // $polmaster->period = $dcontrol->account_month . $dcontrol->account_year;
            // $polmaster->ast_marker = (string) $dcontrol->ast_marker;
            // $polmaster->tran_no = $dcontrol->tran_no;
            // $polmaster->client_number = (string) $dcontrol->client_number;
            // $polmaster->uw_year = $uw_year;
            // //$polmaster->annual_premium=0;
            // //$polmaster->first_premium=0;
            // //$polmaster->fire_premium=0;
            // //$polmaster->first_fire_premium=0;
            // //$polmaster->quake_premium=0;
            // //$polmaster->first_quake_premium=0;
            // //$polmaster->premium_todate=0;
            // //$polmaster->renewal_premium=0;
            // //$polmaster->endorse_amount=0;
            // //$polmaster->fire_endorse_amt=0;

            // $polmaster->prev_premium = 0;
            // //$polmaster->total_sum_insured=0;
            // //$polmaster->total_premium=0;
            // //$polmaster->sum_insured=0;
            // //$polmaster->windscreen=0;
            // //$polmaster->items_total=$dcontrol->items_total;
            // $polmaster->branch = $dcontrol->branch;
            // $polmaster->agent_no = $dcontrol->agent;
            // $polmaster->class = (string) $dcontrol->class;
            // $polmaster->dtrans_no = $dcontrol->dtrans_no;
            // $polmaster->created_by = (string) trim($dcontrol->user_str);
            // //$polmaster->time_created=Carbon::now();
            // $polmaster->company_class_code = (string) $dcontrol->class;
            // $polmaster->finance_code = $dcontrol->financier;
            // $polmaster->financed = $dcontrol->financed;
            // $polmaster->currency_code = $dcontrol->currency_code;
            // $polmaster->premium_method = $dcontrol->ast_marker;
            // $polmaster->trans_type = (string) $dcontrol->trans_type;
            // $polmaster->sticker_amount = $dcontrol->sticker_amount;
            // //$polmaster->pvt_cover='N';
            // //$polmaster->pvt_prem=0;
            // $polmaster->save();
        */
    }


    public function apply_std_limits($new_endt_renewal_no)
    {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {

            //insert standard limits by executing oracle procedure apply_std_limits
            $procedureName = '' . $gb . '.apply_std_limits';
            $bindings = [
                'endt_renewal_no'  =>  $new_endt_renewal_no
            ];
            $resp = DB::executeProcedure($procedureName, $bindings);
            DB::commit();
        } catch (\Throwable $e) {

            DB::rollback();
        }

    }
    public function apply_std_excess($new_endt_renewal_no)
    {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {

            //insert standard excesses  by executing oracle procedure apply_std_limits
            $procedureName = '' . $gb . '.apply_std_excess';
            $bindings = [
                'endt_renewal_no'  =>  $new_endt_renewal_no
            ];
            $resp = DB::executeProcedure($procedureName, $bindings);
            DB::commit();
        } catch (\Throwable $e) {

            DB::rollback();
        }

    }
    public function modify_modtl($new_endt_renewal_no, $prev_endt_renewal_no)
    {


        // declarations for procedures

        //access dcontrol with new_endt_renewal_no
        $dcontrol = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->first();
        $pipcnam = Pipcnam::first();

        $class = ClassModel::where('class', $dcontrol->class)->first();

        if($class->motor_policy == 'Y' || $class->combined == 'Y'){

            $compute_prem = 'N';
            $deleted = 'N';

            $motorCtrl = new MotorProcessing();

            
            $vehicles = Modtlmast::where('policy_no',$dcontrol->policy_no)
                                 ->where('endt_renewal_no',$prev_endt_renewal_no)
                                 ->get();

            foreach($vehicles as $veh){
                // move to history table
                $veh_hist = $veh->toArray();
                Modtlhist::create($veh_hist);

                $motorRequest = new Request([
                    'endt_renewal_no' => $dcontrol->endt_renewal_no,
                    'effective_date' => $dcontrol->effective_date,
                    'reg_no' => $veh->reg_no,
                ]);
                $motorStatus = $motorCtrl->verify_reg_no($motorRequest);
                $reg_valid = (int) json_decode($motorStatus)->valid;

                $status = $veh->status;

                if($reg_valid == 0 && in_array($dcontrol->trans_type,['REN','RNS']))//check if vehicle already exists when renewing/reinstating
                {
                    $status = 'CNC';
                    $deleted = 'Y';
                }
                elseif($dcontrol->trans_type == 'RNS')
                {
                    $deleted = ($veh->deleted == 'Y') ? 'Y' : 'N';
                }
                else
                {
                    $deleted = ($veh->status == 'CNC') ? 'Y' : 'N';
                }
                
                Modtlmast::where('policy_no',$dcontrol->policy_no)
                            ->where('reg_no',$veh->reg_no)
                            ->update([
                                'transeq_no' =>  $dcontrol->transeq_no,
                                'endt_renewal_no' =>  $dcontrol->endt_renewal_no,
                                'updated_by' =>  Auth::user()->user_name,
                                'deleted' => $deleted,
                                'status' => $status
                            ]);
                
                $motorprems = Modtlsumm::where('endt_renewal_no',$prev_endt_renewal_no)
                                        ->where('reg_no',$veh->reg_no)
                                        ->first();
                    
                $newMotor = $motorprems->replicate();
                $newMotor->transeq_no = $dcontrol->transeq_no;
                $newMotor->endt_renewal_no = $dcontrol->endt_renewal_no;
                $newMotor->created_by = Auth::user()->user_name;
                $newMotor->endorse_amount = 0;
                $newMotor->save();
            }
            
            // motor sections
            $motorSect = Motcvrdet::where('policy_no',$dcontrol->policy_no)
                                ->where('endt_renewal_no',$prev_endt_renewal_no)
                                ->get();

            $deleted = 'N';  

            foreach($motorSect as $sect){
                if($dcontrol->trans_type == 'RNS'){
                    $deleted = ($sect->deleted == 'Y') ? 'Y' : 'N';
                }
                else{
                    $deleted = ($sect->cancelled == 'Y') ? 'Y' : 'N';
                }

                $vehicle = Modtlmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$sect->reg_no)
                    ->first();

                $cancelled = $sect->cancelled;
                if($vehicle->status != 'ACT' && in_array($dcontrol->trans_type,['REN','RNS']))//check if vehicle already exists when renewing/reinstating
                {
                    $cancelled = 'Y';
                    $deleted = 'Y';
                }

                $newSect = $sect->replicate();
                $newSect->transeq_no = $dcontrol->transeq_no;
                $newSect->endt_renewal_no = $dcontrol->endt_renewal_no;
                $newSect->created_by = Auth::user()->user_name;
                $newSect->deleted = $deleted;
                $newSect->cancelled = $cancelled;
                $newSect->save();
            }

            // replicate transactional table for renewals && Update motor status
            switch($dcontrol->trans_type){
                case 'RNS':
                    Modtlmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                             ->where('deleted','<>','Y')
                             ->update([
                                'status' => 'ACT',
                                'updated_by' => Auth::user()->user_name
                             ]);

                    Motcvrdet::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                             ->where('deleted','<>','Y')
                             ->update([
                                'cancelled' => 'N',
                                'updated_by' => Auth::user()->user_name,

                             ]);
                break;
                case 'CNC':
                    Modtlmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                        ->update([
                            'status' => 'CNC',
                            'updated_by' => Auth::user()->user_name
                        ]);

                    Motcvrdet::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                        ->update([
                            'cancelled' => 'Y',
                            'updated_by' => Auth::user()->user_name
                        ]);
                break;
            }
        }

    }
    public function cancelCert($cert_info){
        foreach ($cert_info as $cert_inform) {
            $cert_no= trim($cert_inform->aki_cert_no);
            $policy_no=trim($cert_inform->policy_no);
            $cert_cancellation_rsn="Policy cancellation";
            //dd($cert_no);
            
        
            $rec=DB::table('certalloc')->where('policy_no',$policy_no)
                      ->where('aki_cert_no',$cert_no)
                                           ->update([
                                                        'cancellationreason' => $cert_cancellation_rsn,
                                                        'cancellationflag' => 'Y',
                                                        
                                                    ]);
            
        }

    }



    public function add_to_workflow($endt_renewal_no, $id, $process_id)
    {
        
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)
            ->get();
        $dcontrol = $dcontrol[0];

        //check availability in mailbox
        $mailbox_chck = MailBox::where('endt_renewal_no', $dcontrol->endt_renewal_no)
            ->where('policy_no', $dcontrol->policy_no)
            ->where('process_id', $process_id)
            ->count();
        $mailboxId = MailBox::max('id');
        
        //if process id is not in mailbox add it else ignore it
        if ($mailbox_chck == 0 && !empty($process_id)) {
            
            $workflow = Workflows::where('id', $id)->first();
            
            $escalations = Escalations::where('category', $workflow->id)
                ->orderBy('code', 'Asc')
                ->take(1)
                ->first();
                
            $mail_box_current_stage_cnt = MailBox::where('business_process', $workflow->id)
                ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                ->where('policy_no', $dcontrol->policy_no)
                ->count();
                
            if ($mail_box_current_stage_cnt > 0) {
                $mail_box_current_stage = MailBox::where('business_process', $workflow->id)
                    ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                    ->where('policy_no', $dcontrol->policy_no)
                    ->orderBy('stage', 'Desc')
                    ->first();
                    
                $previous_stage = $mail_box_current_stage->stage;

                $escalations_rec = Escalations::where('code', $previous_stage)
                    ->where('category', $workflow->id)
                    ->first();

                $workflow_config = WorkflowsConfig::where('workflow_id', $id)
                    ->where('escalation_id', $escalations_rec->id)
                    ->first();
                    
                /*   if($workflow_config[0]->type=='condition'){

                    $curr_mailbox=MailBox::where('business_process',$workflow->id)
                               ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                               ->where('policy_no',$dcontrol->policy_no)
                               ->orderBy('stage','Desc')
                               ->take(1)
                               ->get();
                
                    $workflow_chosen=[];

                    foreach ($workflow_config as $key => $workflowconfig) {
               
                        if($workflowconfig->type=='condition'){

                            if($workflowconfig->yes_no==$curr_mailbox[0]->yes_no){

                                array_push($workflow_chosen, $workflowconfig);
                            }

                        }

                    }


                    $workflow_config=$workflow_chosen[0];



                }else{ */


                //  }

                $escalations = Escalations::where('id', $workflow_config->send_to)
                    ->where('category', $id)
                    ->first();
                $next_stage = $escalations->code;

            } else {

                $next_stage = $escalations->code;
            }

            $mail = new MailBox;
            $mail->id = $mailboxId + 1;
            $mail->policy_no = $dcontrol->policy_no;
            $mail->endt_renewal_no = $dcontrol->endt_renewal_no;
            $mail->reference_number = $dcontrol->client_number;
            $mail->workflow_id = $workflow->name;
            $mail->stage = $next_stage;
            
            if (trim($escalations->activity_type) == 'End' || trim($escalations->activity_type) == 'end') {
                //if(empty($next_stage)){
                $mail->status = 'closed';
            } else {
                $mail->status = 'incoming';
            }
            
            $mail->location = 1;
            $mail->date_sent = Carbon::today();
            //$mail->date_sent=date('d-m-Y');
            $mail->time_sent = Carbon::now()->toTimeString();
            //$mail->time_sent=date('H:i:s');
            $mail->user_group = $escalations->escalate_to;
            $mail->link_redirect = $escalations->link_redirect;
            $mail->business_process = $workflow->id;
            $mail->from_user_id = Auth::user()->user_id;
            $mail->process_id = $process_id;
            $mail->save();
            
            //update the previous stage to closed;
            if ($mail_box_current_stage_cnt > 0) {
                
                $mailbox_latest = MailBox::where('business_process', $workflow->id)
                    ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                    ->where('policy_no', $dcontrol->policy_no)
                    ->where('stage', $next_stage)
                    ->first(['stage']);
                    
                $mailbox_previous = MailBox::where('business_process', $workflow->id)
                    ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                    ->where('policy_no', $dcontrol->policy_no)
                    ->where('stage', '<', $mailbox_latest->stage)
                    ->orderBy('stage', 'Desc')
                    ->first();
                    
                $previous_upd = MailBox::where('id', $mailbox_previous->id)->update([
                    'status' => 'closed'
                ]);

                /*  $check_end = Escalations::where('category',$workflow->id)->where('code',$mailbox_latest[0]->stage)->get(['activity_type']);

                if($check_end[0]->activity_type == 'End' || $check_end[0]->activity_type == 'end'){

                    $upd_end=MailBox::where('id',$mailbox_latest[0]->id)->update([
                                    'status'=>'closed'
                                ]);
                } */
            }

            //check if next stage is approval and insert
            // $check_appr = Escalations::where('code','>',$next_stage)->orderBy('code','asc')->take(1)->get();

            //  if($check_appr[0]->link_redirect == 'approve_stage'){

            //     $this->add_approval_mailbox($endt_renewal_no,$workflow->id,$check_appr[0]->code,6);

            //  }
            //end check if next is approval  

        }
    }

    public function add_approval_mailbox($endt_renewal_no, $workflow_id, $next_stage, $process_id)
    {

        // dd($workflow_id." ".$next_stage." ".$process_id);

        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)
            ->get();
        $dcontrol = $dcontrol[0];

        $workflows = Workflows::where('id', $workflow_id)->get();
        $workflow = $workflows[0];

        $escalations = Escalations::where('category', $workflow->id)
            ->where('code', $next_stage)
            // ->orderBy('code', 'Asc')
            // ->take(1)
            ->get();


        $escalations = $escalations[0];

        $mail = new MailBox;
        $mail->policy_no = $dcontrol->policy_no;
        $mail->endt_renewal_no = $dcontrol->endt_renewal_no;
        $mail->reference_number = $dcontrol->client_number;
        $mail->workflow_id = $workflow->name;
        $mail->stage = $next_stage;
        $mail->status = 'incoming';
        $mail->location = 1;
        $mail->date_sent = Carbon::today();
        //$mail->date_sent=date('d-m-Y');
        $mail->time_sent = Carbon::now()->toTimeString();
        //$mail->time_sent=date('H:i:s');
        $mail->user_group = $escalations->escalate_to;
        $mail->link_redirect = $escalations->link_redirect;
        $mail->business_process = $workflow->id;
        $mail->from_user_id = Auth::user()->user_id;
        $mail->process_id = $process_id;

        $x = $mail->save();

        // dd($x);

        //update the previous stage to closed;


        $mailbox_latest = MailBox::where('business_process', $workflow->id)
            ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
            ->where('policy_no', $dcontrol->policy_no)
            ->where('stage', $next_stage)
            ->get(['stage']);

        $mailbox_previous = MailBox::where('business_process', $workflow->id)
            ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
            ->where('policy_no', $dcontrol->policy_no)
            ->where('stage', '<', $mailbox_latest[0]->stage)
            ->orderBy('stage', 'Desc')
            ->take(1)
            ->get();


        $previous_upd = MailBox::where('id', $mailbox_previous[0]->id)->update([
            'status' => 'closed'
        ]);
    }


    public function modify_polsect($new_endt_renewal_no, $previous_endt_renewal_no) {
        $dcontrol = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->get();
        $dcontrol = $dcontrol[0];

        $class = ClassModel::where('class', $dcontrol->class)->first();

        //$polsec = Polsec::where('endt_renewal_no', $new_endt_renewal_no)->get();

        //foreach ($polsec as $key => $curr_modtl) {

        if($class->open_cover == 'Y'){

            $polsect = Polsect::where('policy_no', $dcontrol->policy_no)
                               ->where('mac_endt_no', $dcontrol->mac_endt_no)
                               ->update([
                                    'endt_renewal_no' => $dcontrol->endt_renewal_no,
                                    'dtrans_no' => $dcontrol->dtrans_no,
                                    'effective_date' => $dcontrol->effective_date,
                                    'incept_date' => $dcontrol->incept_date,
                                    'period_to' => $dcontrol->period_to,
                                    //'deleted' => 'N',
                                    //'cancelled' => 'N',
                               ]);

        }
        else{

            $polsect = Polsect::where('policy_no', $dcontrol->policy_no)
                //->where('location', $curr_modtl->location)
                    ->update([
                        'endt_renewal_no' => $dcontrol->endt_renewal_no,
                        'dtrans_no' => $dcontrol->dtrans_no,
                        'effective_date' => $dcontrol->effective_date,
                        'incept_date' => $dcontrol->incept_date,
                        'period_to' => $dcontrol->period_to,
                        //'deleted' => 'N',
                        //'cancelled' => 'N',
                    ]);
        }

        

        if ( $class->motor_policy == 'Y') {
            Polsect::where('policy_no', $dcontrol->policy_no)
                //->where('location', $curr_modtl->location)
                ->update([
                    'endorse_amount' => 0
                ]);
        }
        //}
        
        # Duplicate PolsectEnd Details
        $polsectCount = Polsect::where('endt_renewal_no', $new_endt_renewal_no)->where('policy_no', $dcontrol->policy_no)->count();
        $polsectendCount = Polsectend::where('endt_renewal_no', $new_endt_renewal_no)->where('policy_no', $dcontrol->policy_no)->count();

        if ($polsectCount != $polsectendCount) {
            $this->clone_polsect_to_polsectend($new_endt_renewal_no);
        }

        switch ($dcontrol->trans_type) {
            case 'RNS':
                # Do Not Delete Previously Cancelled Locations
            break;

            default:
                # Permanently Remove Deleted Locations
                $delPolsect = Polsect::where('policy_no', $dcontrol->policy_no)
                ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                ->where('deleted', 'Y')
                ->delete();

                $delPolsect = Polsectend::where('policy_no', $dcontrol->policy_no)
                ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                ->where('deleted', 'Y')
                ->delete();
            break;
        }
    }

    public function cancel_certificate($dcon, $refund_amount){

        if($dcon->cancel_mac == 'Y'){

            $macrec = Madtl::where('endt_renewal_no', $dcon->cnc_rfn_endt)->select('location','total_sum_insured')->get();

            foreach($macrec as $m){

                $loc_sum = ((float)$m->total_sum_insured / (float)$macrec->sum('total_sum_insured')) * $refund_amount;

                //Update refund amount
                $upd = Polsect::where('endt_renewal_no',$dcon->endt_renewal_no)
                               ->where('location',$m->location)
                               ->update(['endorse_amount' => $loc_sum, 'cancelled'=>'Y', 'deleted'=>'Y']);

                $upd = Polsectend::where('endt_renewal_no',$dcon->endt_renewal_no)
                                 ->where('location',$m->location)
                                 ->update(['endorse_amount' => $loc_sum, 'cancelled'=>'Y', 'deleted'=>'Y' ]);

                $upd = Polsec::where('endt_renewal_no',$dcon->endt_renewal_no)
                             ->where('location',$m->location)
                             ->update(['endorse_amount' => $loc_sum, 'deleted'=>'Y' ]);

                $del_polsched = Polsched::where('policy_no',$dcon->policy_no)
                                        ->where('endorse_no',$dcon->endt_renewal_no)
                                        ->where('location',$m->location)
                                        ->update(['delete_str'=>'Y']);

                $upd = Madtl::where('endt_renewal_no',$dcon->endt_renewal_no)
                            ->where('location', $m->location)
                            ->update(['endorse_amount' => $loc_sum, 'deleted'=>'Y' ]);

            }

        }
        
    }

    /**

    * Display policy number only if consolidate flag is set to "Y" 

    * and policy number not the same as main policy number

    * @var string $main_pol_no Main policy number

    */
    public function getpolicy_numbers(Request $request){


        $pol =  $request->get('term');
        $main_pol_no =  (string)$request->get('main_pol_no');
        $main_dept =  (string)$request->get('dept');
        $polmaster = Polmaster::select('endorse_no')->where('policy_no',$main_pol_no)->first();
        $dcontrol = Dcontrol::select('endt_renewal_no')->where('endt_renewal_no',$polmaster->endorse_no)->first();
        $class = ClassModel::select('combined')->where('class',$dcontrol->class)->first();

        $policies = DB::select("SELECT p.endorse_no value FROM polmaster p 
            INNER JOIN class c ON p.class=c.class 
            WHERE p.policy_no like '%{$pol}%' AND c.consolidate_ret='Y' AND c.dept='{$main_dept}'
            AND ( p.status_code !='CNC'  or p.status_code is null) 
            AND p.client_number = '$request->client_number'
            AND not exists ( 
                SELECT * FROM DEBITMAST d WHERE p.endorse_no = d.endt_renewal_no 
                )
            AND CASE WHEN c.combined ='Y' then 1 else 0 end =1
            ");

       return Response::Json($policies);
    }

    public function replicate_prosched($new_endt_renewal_no, $previous_endt_renewal_no) {
        $prosched = Prosched::where('endorse_no', $previous_endt_renewal_no)->get();
        $today = Carbon::today();

        $dcontrol = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->first();

        foreach ($prosched as $value) {
            # Replicate Prosched
            $replicate = new Prosched;

            $replicate->endorse_no = $new_endt_renewal_no;
            $replicate->policy_no = $value->policy_no;
            $replicate->quantity = $value->quantity;
            $replicate->class = $value->class;
            $replicate->location = $value->location;

            $replicate->proposal_no = $value->proposal_no;
            $replicate->delete_str = $value->delete_str;
            $replicate->location_name = $value->location_name;
            $replicate->dola = $today;
            $replicate->endorse_date = $dcontrol->effective_date;
            
            $replicate->head1 = $value->head1;
            $replicate->amounta = $value->amounta;
            $replicate->amount2 = $value->amount2;
            $replicate->manu_date = $value->manu_date;
            $replicate->age = $value->age;
            $replicate->manu_year = $value->manu_year;

            $replicate->detail_line = $value->detail_line;
            $replicate->detail_line2 = $value->detail_line2;
            $replicate->categ_class = $value->categ_class;
            $replicate->multiple_of_salary = $value->multiple_of_salary;
            $replicate->old_s_code = $value->old_s_code;

            $replicate->user_filler = $value->user_filler;
            $replicate->passport = $value->passport;
            $replicate->duration = $value->duration;
            $replicate->plan = $value->plan;
            $replicate->dob = $value->dob;
            $replicate->departure_date = $value->departure_date;
            $replicate->s_code = $value->s_code;

            $replicate->section_no  = $value->section_no;
            $replicate->classgrp    = $value->classgrp;
            $replicate->save();
        }

    }

    public function replicate_polsec($new_endorsement, $old_endorsement) {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {
            $polsecProcedure = '' . $gb . '.replicate_polsec';
            $bindings = [
                'old_endorsement' => $old_endorsement,
                'new_endorsement' => $new_endorsement,
            ];

            $resp = DB::executeProcedure($polsecProcedure, $bindings);
            DB::commit();

            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollback();

            $resp = false;
            $warning = 'Error. Section already duplicated.';

           // dd($th);
        }
    }

    public function replicate_polclause($new_endorsement, $old_endorsement) {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {
            $polclauseProcedure = '' . $gb . '.replicate_polclause';
            $bindings = [
                'old_endorsement' => $old_endorsement,
                'new_endorsement' => $new_endorsement,
            ];

            $resp = DB::executeProcedure($polclauseProcedure, $bindings);
            DB::commit();

            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollback();

            $resp = false;
            $warning = 'Error. Policy clauses duplication failed::  ' .$th->getMessage();
            
            dd($th);
        }
    }

    public function replicate_polsched($new_endorsement, $old_endorsement) {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {
            $polschedProcedure = '' . $gb . '.replicate_polsched';
            $bindings = [
                'old_endorsement' => $old_endorsement,
                'new_endorsement' => $new_endorsement,
            ];

            $resp = DB::executeProcedure($polschedProcedure, $bindings);
            DB::commit();

            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollback();

            $resp = false;
            $warning = 'Error. Section already duplicated.';

           // dd($th);
        }
    }

    public function replicate_madtl($new_endorsement, $old_endorsement) {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {
            $procedure = '' . $gb . '.replicate_madtl';
            $bindings = [
                'old_endorsement' => $old_endorsement,
                'new_endorsement' => $new_endorsement,
            ];

            $resp = DB::executeProcedure($procedure, $bindings);
            DB::commit();

            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollback();

            $resp = false;
            $warning = 'Error. Madtl data already duplicated.';

            //dd($th);
        }
    }

    public function replicate_DL($new_endorsement, $old_endorsement) {
        # Replicate Discounts and Loadings
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {
            $procedure = '' . $gb . '.replicate_DL';
            $bindings = [
                'old_endorsement' => $old_endorsement,
                'new_endorsement' => $new_endorsement,
            ];

            $resp = DB::executeProcedure($procedure, $bindings);
            DB::commit();

            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollback();

            $resp = false;
            $warning = 'Error. Discounts and Loadings data already duplicated.';

            
        }
    }

    public function replicate_accessories($new_endorsement, $old_endorsement) {
        # Replicate Extensions
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {
            $procedure = '' . $gb . '.replicate_accessories';
            $bindings = [
                'old_endorsement' => $old_endorsement,
                'new_endorsement' => $new_endorsement,
            ];

            $resp = DB::executeProcedure($procedure, $bindings);
            DB::commit();

            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollback();

            $resp = false;
            $warning = 'Error. Accessories data already duplicated.';

            // dd($th);
        }
    }

    public function replicate_extensions($new_endorsement, $old_endorsement) {
        # Replicate Extensions
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {
            $procedure = '' . $gb . '.replicate_extensions';
            $bindings = [
                'old_endorsement' => $old_endorsement,
                'new_endorsement' => $new_endorsement,
            ];

            $resp = DB::executeProcedure($procedure, $bindings);
            DB::commit();

            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollback();

            $resp = false;
            $warning = 'Error. Extensions data already duplicated.';

            // dd($th);
        }
    }

    public function replicate_FG_details($new_endorsement, $old_endorsement) {
        # Replicate Extensions
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {
            $procedure = '' . $gb . '.replicate_fg_details';
            $bindings = [
                'old_endorsement' => $old_endorsement,
                'new_endorsement' => $new_endorsement,
            ];

            $resp = DB::executeProcedure($procedure, $bindings);
            DB::commit();

            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollback();

            $resp = false;
            $warning = 'Error. Extensions data already duplicated.';

            // dd($th);
        }
    }

    public function replicate_limits($new_endorsement, $old_endorsement) {
        # Replicate Policy Limits
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {
            $procedure = '' . $gb . '.replicate_limits';
            $bindings = [
                'old_endorsement' => $old_endorsement,
                'new_endorsement' => $new_endorsement,
            ];

            $resp = DB::executeProcedure($procedure, $bindings);
            DB::commit();

            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollback();

            $resp = false;
            $warning = 'Error. Policy Limits are already duplicated.';

            // dd($th);
        }
    }

    public static function autoPopulateCMBClauses($endt_renewal_no)
    {
        $dcontrol = Dcontrol::where('policy_no', $endt_renewal_no)
                        ->orderBy('dtrans_no','desc')
                        ->first();

        
        $class = ClassModel::where('class', $dcontrol->class)->first();
        
        $cmb_classes = Polcmb::where('policy_no',$dcontrol->policy_no)
                            ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                            ->where('class_cmb',$dcontrol->class)
                            ->get();
        
        try{
            foreach ($cmb_classes as $cmb) {

                $cls = ClassModel::where('class', $cmb->class)->first();

                
                // dd($cmb->class);

                if ($cls->motor_policy != 'Y' && $cls->travel != 'Y') {
                 $pipcnam = Pipcnam::first();
                 $clause_flag = $pipcnam->clauses_flag; 
 
                    if ($clause_flag == 'D') {
                        # Use Dept
                        $autoClauses = Mautoclauses::where('dept', $cls->dept)->where('auto_gen', 'Y')->get();
                        
                        $existing_clauses = Polclause::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                        ->where('dept',$cls->dept)
                        ->exists();

                    } else if ($clause_flag == 'C') {
                        # Use Class 
                        $autoClauses = Mautoclauses::where('class', $cls->class)->where('auto_gen', 'Y')->get();

                        $existing_clauses = Polclause::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                        ->where('comb_class',$cls->class)
                        ->exists();
                    } 
                    if(!$existing_clauses){
                        
                        $clause_count=1;
                        foreach($autoClauses as $autoClause) {
                            $clause = Clauses::whereRaw("TRIM(clause) = ?", [$autoClause->clause])
                                ->where('dept', $cls->dept)
                                ->first();  
                            
                                
                        
                            
                            $polclause = Polclause::create([
                                'policy_no' => $dcontrol->policy_no,
                                'location' => '1',
                                'dept' => $cls->dept,
                                'dept_fil' => $clause->dept_fill,
                                'clause' => trim($clause->clause),
                                's_code' => 0,
                                'description' => $clause->description,
                                'serial' => $clause_count,
                                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                                'class' => $class->class,
                                'comb_class' => $cls->class,
                            ]);
                            
                            $polclauses = Polclauses::create([
                                'policy_no' => $dcontrol->policy_no,
                                'dept' => $cls->dept,
                                'clause' => trim($clause->clause),
                                'clause_no' => trim($clause->clause),
                                's_code' => 0,
                                'description' => $clause->description,
                                'serial' => $clause_count,
                                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                                'class' => $class->class,
                                'comb_class' => $cls->class,
                            ]);
                            $clause_count=  $clause_count + 1;
                        }
                    }
                }
            } 
        }catch (\Throwable $th) {
            $resp = false;
            $warning = 'Error. Policy Clauses have not been auto-set.';
        }
    }

    public function autoPopulateClauses($endt_renewal_no) {
        $dcontrol = Dcontrol::where('policy_no', $endt_renewal_no)->first();
        $class = ClassModel::where('class', $dcontrol->class)->first();
       

        if ($class->clauses_applicable != 'N') {
            try {
                # Check clause flag
                /* $pipcnam = Pipcnam::first();
                $clause_flag = $pipcnam->clauses_flag; */

                /* if ($clause_flag == 'D') {
                    # Use Dept
                    $clauses = Clauses::where('dept', $class->dept)->get();
                } else if ($clause_flag == 'C') {
                    # Use Class 
                    $clauses = Clauses::where('class', $dcontrol->class)->get();
                } */

                $autoClauses = Mautoclauses::where('class', $dcontrol->class)->get();
                $agent=$dcontrol->agent;
                $branch=$dcontrol->branch;
                $client_no=$dcontrol->client_number;
                // dd($autoClauses,$branch,$agent);
                # Add Clauses to Polclause
               
                foreach($autoClauses as $autoClause) {
                    $apply_basis=$autoClause->apply_basis;
                    $clause = Clauses::whereRaw("TRIM(clause) = ?", [$autoClause->clause])
                    ->where('class', $class->class)
                        ->when($apply_basis == 'I', function ($query) use ($agent, $branch) {
                                                        return $query->where('agent', $agent)->where('branch', $branch);
                                                    })
                        ->when($apply_basis == 'C', function ($query) use ($client_no) {
                                                    return $query->where('client_no', $client_no);
                                            })
                        ->when($apply_basis == 'P', function ($query){

                            return $query;

                    })->first();

                  if(isset($clause)){

                    Polclause::create([
                        'policy_no' => $dcontrol->policy_no,
                        'location' => '1',
                        'dept' => $class->dept,
                        'dept_fil' => $clause->dept_fill,
                        'clause' => $clause->clause,
                        's_code' => $clause->s_code,
                        'description' => $clause->description,
                        'serial' => $clause->serial?$clause->serial:0,
                        'endt_renewal_no' => $dcontrol->endt_renewal_no,
                        'class' => $class->class,
                        'comb_class' => $class->class,
                    ]);

                  }
                   
                   
                   
                }
              
            } catch (\Throwable $th) {
              
                // dd($th);
                $resp = false;
                $warning = 'Error. Policy Clauses have not been auto-set.';

                // dd($th);
            }
        }
    }

    public function autoPopulateScope($endt_renewal_no) {
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        if (is_null($dcontrol)) {
            $dcontrol = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->first();
        }
        $class = ClassModel::where('class', $dcontrol->class)->first();

            try {

                $autoScopes = Productscope::where('class', $dcontrol->class)->first();

                $polrecord = Polscope::create([
                    'policy_no' => $dcontrol->policy_no,
                    'scope' => $autoScopes->description,
                    'endt_renewal_no' => $endt_renewal_no,
                    'class' => $class->class,
                    'cmb_class' => $class->class,
                    'updated_by'=>Auth::user()->user_name
                ]);
            } catch (\Throwable $th) {
                $resp = false;
                $warning = 'Error. Policy scope has not been auto-set.';
            }
    }

    public function autoPopulateWarranty($endt_renewal_no) {
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        if (is_null($dcontrol)) {
            $dcontrol = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->first();
        }
        $class = ClassModel::where('class', $dcontrol->class)->first();

            try {

                $warranty = Productwarranty::where('class', $dcontrol->class)->first();

                $polrecord = Polwarranty::create([
                    'policy_no' => $dcontrol->policy_no,
                    'warranty' => $warranty->description,
                    'endt_renewal_no' => $endt_renewal_no,
                    'class' => $class->class,
                    'cmb_class' => $class->class,
                    'updated_by'=>Auth::user()->user_name
                ]);
            } catch (\Throwable $th) {
                $resp = false;
                $warning = 'Error. Policy warranty has not been auto-set.';
            }
    }

    public function autoPopulateExclusions($endt_renewal_no) {
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        if (is_null($dcontrol)) {
            $dcontrol = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->first();
        }
        $class = ClassModel::where('class', $dcontrol->class)->first();

            try {

                $exlusion = Exclusions::where('class', $dcontrol->class)->first();

                

                $polrecord = Polexclusion::create([
                    'policy_no' => $dcontrol->policy_no,
                    'exclusion' => $exlusion->description,
                    'endt_renewal_no' => $endt_renewal_no,
                    'class' => $class->class,
                    'cmb_class' => $class->class,
                    'updated_by'=>Auth::user()->user_name
                ]);
            } catch (\Throwable $th) {
                $resp = false;
                $warning = 'Error. Policy exclusions has not been auto-set.';
            }
    }

    public function autoPopulateDisclaimer($endt_renewal_no) {
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        if (is_null($dcontrol)) {
            $dcontrol = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->first();
        }
        $class = ClassModel::where('class', $dcontrol->class)->first();

            try {

                $disclaimer = Disclaimer::first();

                $polrecord = Poldisclaimer::create([
                    'policy_no' => $dcontrol->policy_no,
                    'disclaimer' => $disclaimer->description,
                    'endt_renewal_no' => $endt_renewal_no,
                    'class' => $class->class,
                    'cmb_class' => $class->class,
                    'updated_by'=>Auth::user()->user_name
                ]);
            } catch (\Throwable $th) {
                $resp = false;
                $warning = 'Error. Policy disclaimer has not been auto-set.';
            }
    }

    

    public function autoPopulateCoverage($endt_renewal_no) {
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        if (is_null($dcontrol)) {
            $dcontrol = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->first();
        }
        $class = ClassModel::where('class', $dcontrol->class)->first();

            try {

                $polrecord = Polcoverage::create([
                    'policy_no' => $dcontrol->policy_no,
                    'coverage' => $class->geocoverage,
                    'endt_renewal_no' => $endt_renewal_no,
                    'class' => $class->class,
                    'cmb_class' => $class->class,
                    'updated_by'=>Auth::user()->user_name
                ]);
            } catch (\Throwable $th) {
                $resp = false;
                $warning = 'Error. Policy geographical coverage has not been auto-set.';
            }
    }

    
    public function autoPopulateConditions($endt_renewal_no) {
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        if (is_null($dcontrol)) {
            $dcontrol = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->first();
        }
        $class = ClassModel::where('class', $dcontrol->class)->first();

            try {

                $condition = Classcondition::where('class', $dcontrol->class)->first();

                
                $cnd = Condition::where("id", $condition->condition)
                        ->first();
                $polrecord = Polcondition::create([
                    'policy_no' => $dcontrol->policy_no,
                    'condition' => $cnd->description,
                    'endt_renewal_no' => $endt_renewal_no,
                    'class' => $class->class,
                    'cmb_class' => $class->class,
                    'updated_by'=>Auth::user()->user_name
                ]);

            } catch (\Throwable $th) {
                $resp = false;
                $warning = 'Error. Policy conditions has not been auto-set.';
            }
    }

    public function replicate_excess($new_endorsement, $old_endorsement) {
        # Replicate Policy Excess
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {
            $procedure = '' . $gb . '.replicate_excess';
            $bindings = [
                'old_endorsement' => $old_endorsement,
                'new_endorsement' => $new_endorsement,
            ];

            $resp = DB::executeProcedure($procedure, $bindings);
            DB::commit();

            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollback();

            $resp = false;
            $warning = 'Error. Policy Excesses are already duplicated.';

            
        }
    }

    public function cancelTransVehicle($policy_no,$new_endt_renewal_no) {
        # Replicate Policy Excess
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        try {
            $procedure = '' . $gb . '.CANCEL_TRANS_VEHICLE';
            $bindings = [
                'w_policy_no' => $policy_no
            ];

            $resp = DB::executeProcedure($procedure, $bindings);


            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;


            $resp = false;
            $warning = 'Error. Policy Excesses are already duplicated.';

            // dd($th);
        }
    }
    
    public function cancelMotorVehicleNil(array $nilData) 
    {
        $nilCncVehicleRequest = new Request($nilData);

        $motorProcessingClass = new MotorProcessing();

        $motorProcessingClass->cancelMotorVehicle($nilCncVehicleRequest);
        
    }

    public function autoLoadExcess($endt_renewal_no) {
        $dcontrol = Dcontrol::where('policy_no', $endt_renewal_no)->first();
        $class = ClassModel::where('class', $dcontrol->class)->first();

        $classexcess = Classexces::where('class', $dcontrol->class)->where('automatic', 'Y')->get();
        $count = 1;

        if ($class->motor_policy != 'Y' && $class->travel != 'Y') {
            foreach($classexcess as $excess) {
                # Save to Polexcess 
                $polexcess = Polexcess::create([
                    'policy_no' => $dcontrol->policy_no,
                    'endt_renewal_no' => $dcontrol->endt_renewal_no,
                    'class' => $dcontrol->class,
                    'comb_class' => $dcontrol->class,
                    'location' => 1,
                    'item_no' => $excess->item_no,
                    'item_no1'  => $count,
                    'description' => $excess->description,
                    'load_discount' => 'N',
                    'based_on' => $excess->based_on,
                    'rate' => $excess->rate,
                    'base' => $excess->base,
                    'amount' => 0,
                    'basic_amount' => 0,
                    'basic_rate' => 0,
                    'balance_amount' => 0,
                    'balance_rate' => 0,
                    'electrical' => 'N',
                    'excess_per' => $excess->rate,
                    'minimum_excess' => $excess->minimum/$dcontrol->currency_rate,
                    'maximum_excess' => $excess->maximum/$dcontrol->currency_rate,
                    'curr' => 0,
                    'user_fill' => Auth::user()->user_name,
                ]);

                $count+1;
            }
        }
    }

    public function autoLoadLimits($endt_renewal_no) {
        $dcontrol = Dcontrol::where('policy_no', $endt_renewal_no)->first();
        $class = ClassModel::where('class', $dcontrol->class)->first();
        $client_no=$dcontrol->client_number;
        $agent=$dcontrol->agent;
        $branch=$dcontrol->branch;
       

        if ($class->combined == "Y") {
            // $combClass 
        }

        // $combClass 

        $autolimits = Autolimits::where('class', $dcontrol->class)
                                
                                ->get();

                                $autolimits = Autolimits::where(function ($query) use ($apply_basis, $class, $client_no,$branch,$agent) {
                                    $query->where(function ($query) use ($apply_basis, $class,$branch) {
                                       
                                        $query->where('apply_basis', 'P')->where('class', $class->class)->where('auto_gen', 'Y');
                                    })->orWhere(function ($query) use ($apply_basis,$agent,$branch,$class) {
                                  
                                        $query->where('apply_basis', 'I')->where('class', $class->class)->where('agent', $agent)->where('branch', $branch)->where('auto_gen', 'Y');
                                   
                                    })
                                    ->orWhere(function ($query) use ($apply_basis,$client_no,$class) {
                                  
                                        $query->where('apply_basis', 'C')->where('class', $class->class)->where('client_no', $client_no)->where('auto_gen', 'Y');
                                   
                                    });
                                })->get();
      
        $count = 1;

        // if ($class->motor_policy != 'Y' && $class->travel != 'Y' && isset($autolimits)) {
        if (isset($autolimits)) {
            # Do Only For Non-motor
            foreach ($autolimits as $limit) {
                $pollimits = Pollimits::create([
                    'policy_no' => $dcontrol->policy_no,
                    'endt_renewal_no' => $dcontrol->endt_renewal_no,
                    'location' => $count,
                    'detail_line' => $limit->description,
                    'sec_no' => $count,
                    'limit_no' => $limit->limit_no,
                    'amount' => $limit->amount/$dcontrol->currency_rate,
                    'comb_class'=>$dcontrol->class,
                    'user_name' => Auth::user()->user_name,

                ]);

                $count++;
            }
        }
    }

    public function auto_add_combined_classes($policy_no,$endt_renewal_no,$comb_cls){
        $cnt = Combined_class_members::where('class_cmb',$comb_cls)->count();

        if($cnt > 0){
            $comb = Combined_class_members::where('class_cmb',$comb_cls)->get();

            foreach ($comb as $cmb) {
                $cls_desc = ClassModel::where('class',$cmb->class)->first();
                $polcmb = new Polcmb;

                $polcmb->policy_no = $policy_no;
                $polcmb->class = $cmb->class;
                $polcmb->class_cmb = $cmb->class_cmb;
                $polcmb->total_sum_insured = 0;
                $polcmb->sum_insured = 0;
                $polcmb->total_premium = 0;
                $polcmb->premium = 0;
                $polcmb->effective_premium = 0;
                $polcmb->endt_renewal_no = $endt_renewal_no;
                $polcmb->description = $cls_desc->description;
                $polcmb->company_share = 100;
                $polcmb->endorse_amount = 0;

                $polcmb->save();
            }
        }

    }

    public function saveComesaDtl($new_endt_renewal_no, $request, $transcation){

        $dcontrol = Dcontrol::where('endt_renewal_no',$new_endt_renewal_no)->first();

        $countries = $request->countries;
        $detail = '';
        foreach($countries as $key => $country){
            $detail .= $country.'_';
        }

        $detail = rtrim($detail, "_");

        // loop cars insertin them $ their details array_sum($request->total_prem)
        $total_basic_premium = (float) array_sum($request->total_prem);
        $total_card_fee = (float) array_sum($request->card_fee);
        $motorCtrl = new MotorProcessing();
        // insert into modtlhist
        $newVehicles = Modtlmast::where('policy_no',$dcontrol->master_policy)
            ->whereIn('reg_no',$request->reg_no)
            ->whereNotExists(function($query) use ($dcontrol){
                $query->select('*')
                    ->from('modtlhist')
                    ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->whereColumn('modtlmast.reg_no','modtlhist.reg_no');
            })
            ->get();

        foreach($newVehicles as $veh){
            $modtl = Modtlmast::where('policy_no',$dcontrol->master_policy)
                ->where('reg_no',$veh->reg_no)
                ->first()
                ->toArray();

            $max_item_no = (int)Modtlhist::where('policy_no',$dcontrol->policy_no)
                ->max('item_no');
            $item_no = $max_item_no+1;

            $modtl['policy_no'] = $dcontrol->policy_no;
            $modtl['endt_renewal_no'] = $dcontrol->endt_renewal_no;
            $modtl['class'] = $dcontrol->class;
            $modtl['transeq_no'] = $dcontrol->transeq_no;
            $modtl['item_no'] = $item_no;
            $modtl['created_by'] = Auth::user()->user_name;

            Modtlhist::create($modtl);
        }

        for ($i=0; $i < count($request->comesacard_no); $i++) { 
            $baseTrans = Modtlmast::where('policy_no',$dcontrol->master_policy)->first();

            $BasemodtlPrem = Modtlsumm::where('policy_no',$dcontrol->master_policy)
                ->where('reg_no',$request->reg_no[$i])
                ->where('endt_renewal_no',$baseTrans->endt_renewal_no)
                ->first();

            if($request->id[$i] == '00'){
                $line_no = Ptamdtl::where('endt_renewal_no', $dcontrol->endt_renewal_no)->max('ln_no');
                $new_line_no = $line_no + 1;
                $pta_dtl = new Ptamdtl;
                $pta_dtl->endt_renewal_no = $dcontrol->endt_renewal_no;
                $pta_dtl->policy_no = $dcontrol->policy_no;
                $pta_dtl->period_from = $request->pta_period_from;
                $pta_dtl->period_to = $request->pta_period_to;
                $pta_dtl->no_of_days = Carbon::parse($request->pta_period_from)->diffInDays(Carbon::parse($request->pta_period_to));
                $pta_dtl->class = $dcontrol->class;
                $pta_dtl->ln_no = $new_line_no;
                $pta_dtl->card_no = $request->comesacard_no[$i];
                $pta_dtl->medical = $request->med_prem[$i];
                $pta_dtl->med_per_pass = $request->medical;
                $pta_dtl->card_fee = $request->card_fee[$i];
                $pta_dtl->reg_no = $request->reg_no[$i];
                $pta_dtl->created_at = Carbon::today();;
                $pta_dtl->countries = $detail;
                $pta_dtl->passengers = $request->no_passengers[$i];
                $pta_dtl->travellers = $request->insured;
                $pta_dtl->pta_prem = $request->basic_prem[$i];
                $pta_dtl->endorse_amt = $request->total_prem[$i];
                $pta_dtl->pta_rate = $request->pta_rate;
                $pta_dtl->dept = $request->pta_dept;
                $pta_dtl->client_number = $dcontrol->client_number;
                $pta_dtl->motor_categ = $request->motor_categ;
                $pta_dtl->save();

                $motorate = Motorsect::where('class',$dcontrol->class)
                    ->where('grp_code','BSP')
                    ->firstOrFail();

                $section = [
                    'group' => $motorate->grp_code,
                    'item' => $motorate->item_code,
                    'rate_amount' => $request->rate_amount,
                    //'risk_value' => $BasemodtlPrem->sum_insured / $dcontrol->currency_rate,
                    'risk_value' => 0,
                    'cancel' => 'N',
                ];

                $premium_amounts = [
                    'annual_premium' => $pta_dtl->endorse_amt ?? 0,
                    'premium_movt' => $pta_dtl->endorse_amt ?? 0,
                    //'risk_value_movt' => $BasemodtlPrem->sum_insured / $dcontrol->currency_rate,
                    'risk_value_movt' => 0,
                    'endorse_amount' => $pta_dtl->endorse_amt ?? 0
                ];

                $motorCtrl->setProps(
                    endt_renewal_no: $dcontrol->endt_renewal_no,
                    reg_no: $request->reg_no[$i],
                    cls : $dcontrol->class,
                    //total_sum_insured: $BasemodtlPrem->total_sum_insured / $dcontrol->currency_rate,
                    total_sum_insured: 0
                );
                $motorCtrl->save_section_dtl($section,$premium_amounts);
                $motorCtrl->update_motor_summary($new_endt_renewal_no,$dcontrol->class,$request->reg_no[$i]);
            }
        }
        $motorCtrl->update_polmaster($new_endt_renewal_no);

        // if ($transcation == 'create') {
        //                 // card fee
        //     $misc_fee = Misc_fees_param::where('comesa_flag', 'Y')->first();
        //     $polmisc_fees = Polmisc_fees::create([
        //             'policy_no' => $dcontrol->policy_no,
        //             'endt_renewal_no' => $dcontrol->endt_renewal_no,
        //             'fee_descr' => $misc_fee->fee_descr,
        //             'fee_amt' => $total_card_fee,
        //             'fee_entry_type' => $misc_fee->fee_entry_type,
        //         ]);
        //     // card fee // end
        // }
    }

    public function add_polsectend($endt_renewal_no, $cls, $loc) {
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();

        $bustype = Bustype::where('type_of_bus', trim($dcontrol->type_of_bus))->first();

        $count_p = Polsectend::where('policy_no', $dcontrol->policy_no)->count();

        $clsrec = ClassModel::where('class', $cls)->first();

        if (trim($bustype->facult_in) == 'Y' && trim($bustype->auto_add_location) == 'Y') {
            $polsectend = Polsectend::create([
                'policy_no' => $dcontrol->policy_no,
                'proposal_no' => (string) $dcontrol->dprop_no,
                'location' => $loc,
                'class' => $cls,
                'town' => '',
                'street' => '',
                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                'effective_date' => Carbon::now(),
                'policy_no' => $dcontrol->policy_no,
                'proposal_no' => $dcontrol->dprop_no,
                'dtrans_no' => $dcontrol->dtrans_no,
                'line_no' => $loc,
                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                'plot_no' => ($clsrec->marine == 'Y') ? 'PENDING' : $dcontrol->client_number,
                'effective_date' => $dcontrol->effective_date,
                'town' => '',
                'street' => '',
                'section_sum_insured_1' => 0,
                'section_sum_insured_2' => 0,
                'section_sum_insured_3' => 0,
                'section_sum_insured_4' => 0,
                'section_sum_insured_5' => 0,
                'section_sum_insured_6' => 0,
                'section_liab_1' => 0,
                'section_liab_2' => 0,
                'section_liab_3' => 0,
                'section_liab_4' => 0,
                'section_liab_5' => 0,
                'section_liab_6' => 0,
                'section_premium_1' => 0,
                'section_premium_2' => 0,
                'section_premium_3' => 0,
                'section_premium_4' => 0,
                'section_premium_5' => 0,
                'section_premium_6' => 0,
                'total_premium' => 0,
                'total_sum_insured' => 0,
                'renewal_premium' => 0,
                'first_premium' => 0,
                'premium_todate' => 0,
                'content_first_rate' => 0,
                'period' => 0,
                'basic_rate' => 0,
                'discount_base' => 0,
                'first_sum' => 0,
                'fire_premium' => 0,
                'quake_premium' => 0,
                'zone' => 0,
                'area' => 0,
                'quake_rate_base' => 0,
                'quake_rate' => 0,
                'first_fire_premium' => 0,
                'first_quake_premium' => 0,
                'endorse_amount' => 0,
                'prev_premium' => 0,
                'fire_endorse_amt' => 0,
                'quake_endorse_amt' => 0,
                'prev_fire_premium' => 0,
                'prev_quake_premium' => 0,
                'company_class_code' => $cls,
                'escalate_rate_base' => 100,
                'declare_rate_base' => 100,
                'total_quake_premium' => 0,
                'total_fire_premium' => 0,
                'todate_fire_premium' => 0,
                'todate_quake_premium' => 0,
                'sum_insured' => 0,
                'total_first_premium' => 0,
                'incept_date' => $dcontrol->incept_date,
                'name' => ($clsrec->marine == 'Y') ? 'PENDING' : $dcontrol->name,
                'installment_premium' => 0,
                'multiple_of_salary' => 0,
                'annual_premium' => 0,
                'prev_annual' => 0,
                'prev_total' => 0,
                'total_liability_amt' => 0,
                'liability_amt' => 0,
                'none_proratable' => 0,
                'prev_none_proratable' => 0,
                'annual_monthly_salary' => 0,
                'total_discounts' => 0,
                'total_loadings' => 0,
                'total_premium_short' => 0,
                'owner_lib' => 0,
                'occupier_lib' => 0,
                'no_units' => 0,
                'currency_code' => $dcontrol->currency,
                'currency_rate' => $dcontrol->currency_rate,
                'no_of_lifes_endorsed' => 0,
                'pvt_prem' => 0,
                'pvt_prev_total' => 0,
                'pvt_prev_premium' => 0,
                'section_sum_insured_1' => $dcontrol->facin_sum_insured,
                'section_premium_1' =>$dcontrol->facin_premium,
                'total_premium' => $dcontrol->facin_premium,
                'total_sum_insured' => $dcontrol->facin_sum_insured,
                'renewal_premium' => $dcontrol->facin_premium,
                'sum_insured' => $dcontrol->facin_sum_insured,
                'total_first_premium' => $dcontrol->facin_premium,
                'endorse_amount' => $dcontrol->facin_premium,
                'fire_endorse_amt' => $dcontrol->facin_premium,
                'annual_premium' => $dcontrol->facin_premium,
                'mac_endt_no' => $dcontrol->mac_endt_no
            ]);
        } else {
            $polsectend = Polsectend::create([
                'policy_no' => $dcontrol->policy_no,
                'proposal_no' => (string) $dcontrol->dprop_no,
                'location' => $loc,
                'class' => $cls,
                'town' => '',
                'street' => '',
                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                'effective_date' => Carbon::now(),
                'policy_no' => $dcontrol->policy_no,
                'proposal_no' => $dcontrol->dprop_no,
                'dtrans_no' => $dcontrol->dtrans_no,
                'line_no' => $loc,
                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                'plot_no' => ($clsrec->marine == 'Y') ? 'PENDING' : $dcontrol->client_number,
                'effective_date' => $dcontrol->effective_date,
                'town' => '',
                'street' => '',
                'section_sum_insured_1' => 0,
                'section_sum_insured_2' => 0,
                'section_sum_insured_3' => 0,
                'section_sum_insured_4' => 0,
                'section_sum_insured_5' => 0,
                'section_sum_insured_6' => 0,
                'section_liab_1' => 0,
                'section_liab_2' => 0,
                'section_liab_3' => 0,
                'section_liab_4' => 0,
                'section_liab_5' => 0,
                'section_liab_6' => 0,
                'section_premium_1' => 0,
                'section_premium_2' => 0,
                'section_premium_3' => 0,
                'section_premium_4' => 0,
                'section_premium_5' => 0,
                'section_premium_6' => 0,
                'total_premium' => 0,
                'total_sum_insured' => 0,
                'renewal_premium' => 0,
                'first_premium' => 0,
                'premium_todate' => 0,
                'content_first_rate' => 0,
                'period' => 0,
                'basic_rate' => 0,
                'discount_base' => 0,
                'first_sum' => 0,
                'fire_premium' => 0,
                'quake_premium' => 0,
                'zone' => 0,
                'area' => 0,
                'quake_rate_base' => 0,
                'quake_rate' => 0,
                'first_fire_premium' => 0,
                'first_quake_premium' => 0,
                'endorse_amount' => 0,
                'prev_premium' => 0,
                'fire_endorse_amt' => 0,
                'quake_endorse_amt' => 0,
                'prev_fire_premium' => 0,
                'prev_quake_premium' => 0,
                'company_class_code' => $cls,
                'escalate_rate_base' => 100,
                'declare_rate_base' => 100,
                'total_quake_premium' => 0,
                'total_fire_premium' => 0,
                'todate_fire_premium' => 0,
                'todate_quake_premium' => 0,
                'sum_insured' => 0,
                'total_first_premium' => 0,
                'incept_date' => $dcontrol->incept_date,
                'name' => ($clsrec->marine == 'Y') ? 'PENDING' : $dcontrol->name,
                'installment_premium' => 0,
                'multiple_of_salary' => 0,
                'annual_premium' => 0,
                'prev_annual' => 0,
                'prev_total' => 0,
                'total_liability_amt' => 0,
                'liability_amt' => 0,
                'none_proratable' => 0,
                'prev_none_proratable' => 0,
                'annual_monthly_salary' => 0,
                'total_discounts' => 0,
                'total_loadings' => 0,
                'total_premium_short' => 0,
                'owner_lib' => 0,
                'occupier_lib' => 0,
                'no_units' => 0,
                'currency_code' => $dcontrol->currency,
                'currency_rate' => $dcontrol->currency_rate,
                'no_of_lifes_endorsed' => 0,
                'pvt_prem' => 0,
                'pvt_prev_total' => 0,
                'pvt_prev_premium' => 0,
                'mac_endt_no' => $dcontrol->mac_endt_no
            ]);
        }

    }

    public function getWibaRiderClass(Request $request){
        
        $dept = $request->input('department');
        $wiba_rider = ClassModel::where('wiba_rider', 'Y')->where('dept',$dept)->get();

        return response()->json($wiba_rider);

    }

    public function createSlavePolicy(Request $request) {
        $username =  Auth::user()->user_name;
        $transaction_type = 'POL';
        $policy_no = $request->input('master_policy');
        
        # WIBA POLICY DETAILS
        $polmaster = Polmaster::where('policy_no', $policy_no)->first();
        $masterPolicy = Dcontrol::where('policy_no', $policy_no)->where('endt_renewal_no', $polmaster->endorse_no)->first();

        $pipcnam = Pipcnam::where('record_type', 0)->first();
        $charge_vat = $pipcnam->charge_vat;

        $validate = Validator::make($request->all(), [
            'el_effective_date' => 'required'
        ]);

        $transaction_no = Tran0::where('rec_no', 0)->get(['tran_no']);
        $tran_no = $transaction_no[0]->tran_no;
        $tran0 = Tran0::where('rec_no', 0)->increment('tran_no', (int) '1');
        
        /* get dtrans_no and period */
        $doc_trans = Dtran0::where('rec_no', 0)->get(['dtran_no', 'account_month', 
        'account_year']);
        
        $dtran_no = $doc_trans[0]->dtran_no;
        $account_month = $doc_trans[0]->account_month;
        $account_year = $doc_trans[0]->account_year;

        $dtran0 = Dtran0::where('rec_no', 0)->increment('dtran_no', (int) '1');

        /*uw_year*/
        $uw_year = date('Y', strtotime((string) $request->input('period_from')));

        /*renewal date*/
        $renewal = new DateTime((string) $request->input('period_to'));
        $renewal->modify('+1 day');

        $new_endt_renewal_no = $this->generate_pol(
            $masterPolicy->branch,
            $request->input('class'),
            $transaction_type,
            $account_year,
            $account_month
        );

        // dd($new_endt_renewal_no ,$request);

        /*get currency*/
        $currency = $masterPolicy->currency;
        $currency_code = $currency;

        /*get current currency rate*/
        $currency_rate = $masterPolicy->currency_rate;

        /*get class department*/
        $department = ClassModel::where('class', $request->input('class'))->get(['dept']);
        $dept = (string) $department[0]->dept;

        /*get number of days of cover*/
        $cover_from = Carbon::parse($request->input('el_effective_date'));
        $cover_to = Carbon::parse($request->input('period_to'));

        $days_of_cover = $cover_from->diffInDays($cover_to);
        $class = ClassModel::where('class', $request->input('class'))->first();
        $days_of_cover = $days_of_cover + 1;

        $prop_number = Classbr::where('class', $request->input('class'))->get(['prop_serial']);
        $prop_no = $prop_number[0]->prop_serial;
        $dprop_no = Classbr::where('class', $request->input('class'));

        $dprop_no = Classbr::where('class', $request->input('class'))->increment('prop_serial', (int) '1');

        /*insured array*/
        $insured = Client::where('client_number', $polmaster->client_number)->get(['name', 'client_number', 'client_type']);

        /*get doc type */
        $document_type = Transtype::where('descr', $transaction_type)->get(['doc_type']);
        $doc_type = $document_type[0]->doc_type;

        //type of bus
        $bustype_curr = Bustype::where('type_of_bus', trim($request->input('bustype')))->get();
        $bustype_curr = $bustype_curr[0];
        $seq_no = Dcontrol::generateTranseqNumber($transaction_type);
        /**********************
                DCONTROL
        ***********************/
        $dcontrol = new Dcontrol;
        $dcontrol->dcon_no = $tran_no;
        $dcontrol->transeq_no = $seq_no;

        if($charge_vat == 'Y'){
            $get_vat_setup = Vat_setup::where('vat_code', $masterPolicy->vat_charged)->get();
            $get_vat_setup = $get_vat_setup[0];
        }

        switch ($masterPolicy->ast) {
            case 'I':
                $dcontrol->plan = $masterPolicy->plan;
                $dcontrol->plan_categ = $masterPolicy->plan_categ;
                $dcontrol->instal_categ = $masterPolicy->instal_categ;
            break;
        }

        $dcontrol->dprop_no=$prop_no;
        $dcontrol->policy_no=(string)$new_endt_renewal_no;
        $dcontrol->prop_date=Carbon::now();
        $dcontrol->branch = $masterPolicy->branch;
        $dcontrol->agent = $masterPolicy->agent;
        $dcontrol->class = $request->input('class');
        $dcontrol->user_str =(string) $request->User()->user_name;

        $dcontrol->period_from=$request->input('period_from');
        $dcontrol->period_to=$request->input('period_to');
        

        if(trim($masterPolicy->ast) == 'T'){
            $dcontrol->period_from = $masterPolicy->period_from;
            $dcontrol->period_to = $masterPolicy->period_to;
            $dcontrol->cov_period_from = $masterPolicy->period_from;
            $dcontrol->cov_period_to = $masterPolicy->period_to;
        }else{
            $dcontrol->period_from = $masterPolicy->period_from;
            $dcontrol->period_to = $masterPolicy->period_to;
            $dcontrol->cov_period_from = $masterPolicy->period_from;
            $dcontrol->cov_period_to = $masterPolicy->period_to;
        }

        $dcontrol->effective_date=$request->input('el_effective_date');
        $dcontrol->branch_code= str_pad($masterPolicy->branchpol, 3,"0",STR_PAD_LEFT);
        $dcontrol->co_insure=$masterPolicy->co_ins;

        if($dcontrol->co_insure=='Y' && $pipcnam->coins_rate_per_sec!='Y'){
            $dcontrol->co_ins_rate = $masterPolicy->co_ins_rate;
            $dcontrol->co_ins_base = $masterPolicy->co_ins_base;
        }else{
            $dcontrol->co_ins_rate = 0;
            $dcontrol->co_ins_base = 0;
        }

        $dcontrol->type_of_bus = $masterPolicy->type_of_bus;
        $dcontrol->dept = $masterPolicy->dept;
        $dcontrol->actual_period_from = $request->input('period_from');
        $dcontrol->actual_period_to = $request->input('period_to');
        $dcontrol->financed = $masterPolicy->financed;
        $dcontrol->ast_marker = $masterPolicy->ast_marker;  
        $dcontrol->items_total = 1;
        $dcontrol->branch_cod = $masterPolicy->branchpol;
        $dcontrol->ext_from = $masterPolicy->ext_from;
        $dcontrol->ext_to = $masterPolicy->ext_to;
        $dcontrol->currency = $currency_code;

        if($masterPolicy->financed == 'Y'){
            $dcontrol->financed_code = $masterPolicy->financier;
        }else{
            $dcontrol->financed_code='';
        }

        if ($masterPolicy->co_ins == 'Y') {
            $dcontrol->company_share = $masterPolicy->co_ins_share;
        } else {
            $dcontrol->company_share = 100;
        }

        if($charge_vat == 'Y'){
            $dcontrol->vat_type = $get_vat_setup->vat_type;
            $dcontrol->vat_description = $get_vat_setup->vat_description;
            $dcontrol->vat_rate = $get_vat_setup->vat_rate;
            $dcontrol->vat_code = $masterPolicy->vat_code;
        }else{
            $dcontrol->vat_rate = 0;
        }

        $dcontrol->endt_renewal_no = (string) $new_endt_renewal_no;
        $dcontrol->dtrans_no = $dtran_no;
        $dcontrol->insured = (string) $insured[0]->name;

        $dcontrol->trans_type = $transaction_type;
        $dcontrol->dola = Carbon::now();

        $dcontrol->sum_insured = 0;
        $dcontrol->location = 0;
        
        $dcontrol->time = Carbon::now();
        $dcontrol->expiry_date = $masterPolicy->expiry_date;

        $dcontrol->pin_no = 'Y'; //$insured[0]->pin_number;
        $dcontrol->client_number = (string) $insured[0]->client_number;
        $dcontrol->surname = (string) $insured[0]->surname;
        $dcontrol->others = (string) $insured[0]->others;
        $dcontrol->first_name = (string) $insured[0]->first_name;
        $dcontrol->client_type = $insured[0]->client_type;

        $dcontrol->incept_date = $masterPolicy->incept_date;
        $dcontrol->company_class_code = $masterPolicy->company_class_code;
        $dcontrol->account_year = $account_year;
        $dcontrol->account_month = $account_month;
        $dcontrol->name = trim($insured[0]->name);

        $dcontrol->cancelled = 'N';
        $dcontrol->reg_no = '';
        $dcontrol->source = 'U/W';
        $dcontrol->doc_type = strtoupper($doc_type);
        $dcontrol->currency_rate = $currency_rate;

        $dcontrol->line_no = 0;
        $dcontrol->renewal_date = $renewal;
        $dcontrol->binder_flag = $dcontrol->binder_flag;

        if ($dcontrol->binder == 'Y') {
            $dcontrol->binder_pol_no = $masterPolicy->binder_pol_no;
            $dcontrol->bank_code = $masterPolicy->bank_code;
            $dcontrol->bank_branch_code = $masterPolicy->bank_branch_code;
            $dcontrol->risk_note_no = $masterPolicy->risk_note_no;
            $dcontrol->external_pol_no = $masterPolicy->external_pol_no;
            $dcontrol->binder = $masterPolicy->binder;
        }

        # Scheme Policy
        if($masterPolicy->binder == 'S'){
            $dcontrol->binder_pol_no = $masterPolicy->binder_pol_no;
            $dcontrol->bank_code = $masterPolicy->bank_code;
            $dcontrol->bank_branch_code = $masterPolicy->bank_branch_code;
            $dcontrol->risk_note_no = $masterPolicy->risk_note_no;
            $dcontrol->external_pol_no = $masterPolicy->external_pol_no;
            $dcontrol->binder = $masterPolicy->binder;
        }

        $dcontrol->fleet = 'N';
        $dcontrol->days_covered = $days_of_cover;
        $dcontrol->pvt_cover = '';
        $dcontrol->endorse_date = Carbon::today();
        $dcontrol->global_account_comm_rate = $request->global_account_comm_rate;

        if (trim($bustype_curr->facult_in) == 'Y'){
            $dcontrol->facin_lead_agent = $masterPolicy->facin_lead_agent;
            $dcontrol->facin_comm_amt = $masterPolicy->facin_comm_amt;
            $dcontrol->facin_total_sum_insured = $masterPolicy->facin_total_sum_insured;
            $dcontrol->facin_sum_insured = $masterPolicy->facin_sum_insured;
            $dcontrol->facin_premium = $masterPolicy->facin_premium;
            $dcontrol->facin_comm_rate = $masterPolicy->facin_comm_rate;
            $dcontrol->facin_premium_rate = $masterPolicy->facin_premium_rate;
            $dcontrol->company_share = $masterPolicy->facin_premium_rate;
            $dcontrol->facin_total_premium = $masterPolicy->facin_total_premium;
            $dcontrol->facultin_client_no = $masterPolicy->facultin_client_no;
        }

        # Master Policy's Endorsement No
        $dcontrol->master_policy = $masterPolicy->master_policy;
        $dcontrol->master_endt_no = $masterPolicy->master_endt_no;

        $dcontrol->save();

        $bustype_curr = Bustype::where('type_of_bus', trim($dcontrol->type_of_bus))->get();
        $bustype_curr = $bustype_curr[0];

        $this->add_polmaster((string) $new_endt_renewal_no, $uw_year);

        //add to workflows
        $this->add_to_workflow($new_endt_renewal_no, $request->input('workflow_id'), $request->input('pid'));

        if ($dcontrol->binder != 'Y' ) {
            if ($bustype_curr->facult_in != 'Y'){
                # Auto Populate Clauses
                $this->autoPopulateClauses($new_endt_renewal_no);

                if($class->combined == 'Y'){
                    $this->autoPopulateCMBClauses($new_endt_renewal_no);
                } else {
                    # Auto Populate Clauses
                    $this->autoPopulateClauses($new_endt_renewal_no);

                    
                    $this->autoPopulateDisclaimer($new_endt_renewal_no);
                    $this->autoPopulateCoverage($new_endt_renewal_no);
                    $this->autoPopulateWarranty($new_endt_renewal_no);
                    $this->autoPopulateConditions($new_endt_renewal_no);
                    $this->autoPopulateScope($new_endt_renewal_no);
                    $this->autoPopulateExclusions($new_endt_renewal_no);
                }

                # Auto Populate Limits
                $this->autoLoadLimits($new_endt_renewal_no); 
            }
        }

        $dcontrol_curr = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->get();
        $dcontrol_curr = $dcontrol_curr[0];

        $bustype_curr = Bustype::where('type_of_bus', trim($dcontrol_curr->type_of_bus))->get();
        $bustype_curr = $bustype_curr[0];

        $class = ClassModel::where('class', $dcontrol_curr->class)->get();
        $class = $class[0];

        $department = Dept::where('dept', $class->dept)->get();
        $department = $department[0];
        
        if ($class->combined == 'Y') {
            Session::flash('success', 'Policy Creation Successful, you can continue with the process');

            return redirect()->route('risk', ['policy_no' => $dcontrol_curr->policy_no, 'endt_renewal_no' => $dcontrol_curr->endt_renewal_no, 'motor' => $department->motor, 'cmb' => 'Y','facult_in'=>trim($bustype_curr->facult_in)]);
        }


        $loc_serial = $this->location_serial($dcontrol_curr->policy_no);

        if ($class->bypass_location == 'Y' && trim($bustype_curr->facult_in) !='Y') {
           
            $this->add_polsect((string)$new_endt_renewal_no, (int)$dcontrol_curr->class, $loc_serial);
            $this->add_polsectend((string)$new_endt_renewal_no,(int)$dcontrol_curr->class, $loc_serial);

            Session::flash('success','Policy Creation Successful, you can continue with the process');
            return redirect()->route('polsched',['endorsement'=>$dcontrol_curr->endt_renewal_no,'location'=>1,'cls'=>$dcontrol_curr->class, 'bond_upd'=>'P']);
        }


        if (trim($bustype_curr->facult_in) == 'Y' && trim($bustype_curr->auto_add_location) =='Y') {

            $this->add_polsect((string)$new_endt_renewal_no, (int)$dcontrol_curr->class, $loc_serial);
            $this->add_polsectend((string)$new_endt_renewal_no, (int)$dcontrol_curr->class, $loc_serial);

            Session::flash('success','Policy Creation Successful, you continue with the process');
            return redirect()->route('policy_functions',['endt_renewal_no'=>$dcontrol_curr->endt_renewal_no]);
        }
        Session::flash('success', 'Policy number ' . $new_endt_renewal_no . ' added sucessfully');
        return redirect()->route('risk', ['policy_no' => $dcontrol_curr->policy_no, 'endt_renewal_no' => $dcontrol_curr->endt_renewal_no, 'motor' => $department->motor, 'cls' => $dcontrol_curr->class, 'cmb' => 'N','facult_in'=>trim($bustype_curr->facult_in)]);
    }

    public function clone_polsect_to_polsectend($endt_renewal_no, $location = 0) {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        //insert missing locations to polsectend from oracle view polsect_not_in_polsectend
        $polsect_not_in_polsectend = Polsect_not_in_polsectend::where('endt_renewal_no', $endt_renewal_no)->get();

        foreach ($polsect_not_in_polsectend as $polsect_not_in_polsectend) {
            //sql to clone modtl record to modtlend 
            
            DB::beginTransaction();
            try {
                $procedureName = '' . $gb . '.clone_polsect_to_polsectend';
                $bindings = [
                    'endt_renewal_no'  =>  $endt_renewal_no,
                    'location' => $polsect_not_in_polsectend->location
                ];

                $resp = DB::executeProcedure($procedureName, $bindings);
                DB::commit();
            } catch (\Throwable $e) {
                DB::rollback();
            }
        }
    }

    public function delete_polsec($endt_renewal_no) {
        $polsecDel = Polsec::where('endt_renewal_no', $endt_renewal_no)
        ->where('deleted', 'Y')
        ->delete();
    }

    public function delete_polsched($endt_renewal_no) {
        $polschedDel = Polsched::where('endorse_no', $endt_renewal_no)
        ->where('delete_str', 'Y')
        ->delete();
    }
    
    public function replicate_secExtReinsure($new_endorsement, $old_endorsement) {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {
            $polsecProcedure = '' . $gb . '.replicate_secExtReinsure';
            $bindings = [
                'old_endorsement' => $old_endorsement,
                'new_endorsement' => $new_endorsement,
            ];

            $resp = DB::executeProcedure($polsecProcedure, $bindings);
            DB::commit();

            // return $resp;
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollback();

            $resp = false;
            $warning = 'Error. Section already duplicated.';

           // dd($th);
        }
    }
        
    public function nil_cover_period_trail($policy_no,$old_endt_renewal_no,$endt_renewal_no){
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $old_dcontrol = Dcontrol::where('endt_renewal_no', $old_endt_renewal_no)->first();
        $REN_POL = Dcontrol::where('policy_no', $policy_no)
            ->whereIn('trans_type',['REN','POL'])
            ->orderBy('dtrans_no','desc')
            ->first();
        
        $fields = [
            'I' => 'incept_date',
            'E' => 'effective_date',
            'PF' => 'period_from',
            'PT' => 'period_to',
            'CF' => 'cov_period_from',
            'CT' => 'cov_period_to',
            'EF' => 'ext_from',
            'ET' =>'ext_to',
            'R' => 'renewal_date',
            'EX' => 'expiry_date',
        ];

        $tables =[
            'I' => 'dcontrol,polmaster,polsect,modtl,debitmast',
            'E' => 'dcontrol,polsect,modtl,debitmast,acdet',
            'PF' => 'dcontrol,polmaster,modtl,debitmast',
            'PT' => 'dcontrol,polmaster,polsect,modtl,debitmast',
            'CF' => 'dcontrol,polmaster',
            'CT' => 'dcontrol,polmaster',
            'EF' => 'dcontrol,polmaster',
            'ET' => 'dcontrol,polmaster',
            'R' => 'dcontrol,polmaster',
            'EX' => 'dcontrol,polmaster',
        ];
        
        if($dcontrol->ast_marker == 'A'){
            $fields['DC'] = 'days_covered';
            $tables['DC'] = 'dcontrol';
        }

        foreach($fields as $key => $field){
            $audit_trail = Risk_audit_trail::create([
                'risk_item' => 'cover period for '.$REN_POL->endt_renewal_no,
                'policy_no'=>$policy_no,
                'endt_renewal_no'=>$endt_renewal_no,
                'table_name'=>$tables[$key],
                'field_changed'=>$field,
                'old_value'=>$old_dcontrol->$field,
                'new_value'=> $dcontrol->$field,
                'date_changed'=>Carbon::now('EAT'),

                'system_user'=>Auth::user()->user_name
            ]);
        }
    }

    public function extendPolicyTOR(Request $request) {
       
        $username =  Auth::user()->user_name;
        $transaction_type = 'EXT';
        $policy_no = $request->input('master_policy');
        $cls = ClassModel::where('class', $request->input('class'))->first();

        if((string)$request->input('type')=='POL' || (string)$transaction_type == 'EXT' || (string)$request->input('type') == 'REN' || (string)$request->input('type') == 'RNS'){
            $validate = Validator::make($request->all(), [
                'department'        => 'required',
                'class'             => 'required',
                'cover_days'        => 'required',
                'incept_date'       => 'required|date|date_format:Y-m-d|before:period_to',
                'el_effective_date'    => 'required|date|date_format:Y-m-d|before:period_to',
                'period_from'       => 'required|date|date_format:Y-m-d|before:period_to',
                'period_to'         => 'required|date|date_format:Y-m-d|after:period_from',
            ]);

            if ($validate->fails()){
                return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all required fields');
            }

            if ($request->ast == 'T') {
                # Time on Risk Validation
                $validate = Validator::make($request->all(), [
                    'cov_period_from'   => 'required',
                    'cov_period_to'      => 'required',
                    't_cover_days'     => 'required',
                ]);

                if ($validate->fails()){
                    return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all time on risk required fields');
                }
            } else if ($request->ast == 'S' && $request->prem_method == 'S') {
                # Short Term Percentage Method Validation
                $validate = Validator::make($request->all(), [
                    't_period_from'   => 'required',
                    't_period_to'     => 'required',
                    't_cover_days'    => 'required',
                    'prem_percent'    => 'required'     
                ]);

                if ($validate->fails()){
                    return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all short term percentage method required fields');
                }
            } else if ($request->facin_flag == 'Y' && $cls->combined != 'Y') {
                # Facultative Inward Validation
                $validate = Validator::make($request->all(), [
                    'facin_total_sum_insured'   => 'required',
                    'all_premium'     => 'required',
                    'our_share'    => 'required',
                    'facin_comm_rate'    => 'required' ,    
                    'facin_sum_insured'    => 'required',    
                    'facin_premium'    => 'required',    
                    'facin_comm_amt'    => 'required' 
                ]);

                if ($validate->fails()){
                    return redirect()->back()->with('error', 'Failed to create policy/endorsement. Kindly fill all facultaive inward required fields');
                }
            }
        }
        //end policy form validator //end
        try{
        $schem = schemaName();
    
        $gb = $schem['gb'];
        
        # MASTER POLICY DETAILS
        $polmaster = Polmaster::where('policy_no', $policy_no)->first();
        $masterPolicy = Dcontrol::where('policy_no', $policy_no)
        ->where('endt_renewal_no', $polmaster->endorse_no)
        ->first();

        $validate = Validator::make($request->all(), [
            'el_effective_date' => 'required'
        ]);

        $transaction_no = Tran0::where('rec_no', 0)->get(['tran_no']);
        $tran_no = $transaction_no[0]->tran_no;
        $tran0 = Tran0::where('rec_no', 0)->increment('tran_no', (int) '1');
        
        /* get dtrans_no and period */
        $doc_trans = Dtran0::where('rec_no', 0)->get(['dtran_no', 'account_month', 
        'account_year']);
        
        $dtran_no = $doc_trans[0]->dtran_no;

        $dtran0 = Dtran0::where('rec_no', 0)->increment('dtran_no', (int) '1');

        $account_month = Carbon::now()->month;
        $account_year = Carbon::now()->year;

        /*uw_year*/
        // $uw_year = date('Y', strtotime((string) $request->input('cov_period_from')));
        $uw_year = $polmaster->uw_year;

        /*renewal date*/
        // $renewal = new DateTime((string) $request->input('period_to'));
        $renewal = $masterPolicy->renewal_date;

        $new_endt_renewal_no = $this->generate_pol(
            $masterPolicy->branch,
            $request->input('class'),
            $transaction_type,
            $account_year,
            $account_month
        );

        /*get currency*/
        $currency = $masterPolicy->currency;
        $currency_code = $currency;

        /*get current currency rate*/
        $currency_rate = $masterPolicy->currency_rate;

        /*get class department*/
        $department = ClassModel::where('class', $request->input('class'))->get(['dept']);
        $dept = (string) $department[0]->dept;

        /*get number of days of cover*/
        $days_of_cover = $request->input('cover_days');

        $class = ClassModel::where('class', $request->input('class'))->first();
        $prop_number = Classbr::where('class', $request->input('class'))->get(['prop_serial']);
        $prop_no = $prop_number[0]->prop_serial;

        $dprop_no = Classbr::where('class', $request->input('class'))
        ->increment('prop_serial', (int) '1');

        /*insured array*/
        $insured = Client::where('client_number', $polmaster->client_number)
        ->get(['name', 'client_number', 'client_type']);

        /*get doc type */
        $document_type = Transtype::where('descr', $transaction_type)
        ->get(['doc_type']);
        $doc_type = $document_type[0]->doc_type;

        //type of bus
        $bustype_curr = Bustype::where('type_of_bus', trim($request->input('bustype')))->get();
        $bustype_curr = $bustype_curr[0];
        $seq_no = Dcontrol::generateTranseqNumber($transaction_type,$polmaster->policy_no);
        /**********************
                DCONTROL
        ***********************/

        $dcontrol = new Dcontrol;
        $dcontrol->dcon_no = $tran_no;
        $dcontrol->transeq_no = $seq_no;

        $dcontrol->dprop_no=$prop_no;
        $dcontrol->policy_no = $masterPolicy->policy_no;
        $dcontrol->prop_date=Carbon::now();
        $dcontrol->branch = $masterPolicy->branch;
        $dcontrol->agent = $masterPolicy->agent;
        $dcontrol->class = $request->input('class');
        $dcontrol->user_str =(string) $request->User()->user_name;

        $dcontrol->period_from=$request->input('period_from');
        $dcontrol->period_to=$request->input('period_to');
        $dcontrol->ast_marker = $masterPolicy->ast_marker;  

        switch ($masterPolicy->ast_marker) {
            case 'I':
                $dcontrol->plan = $masterPolicy->plan;
                $dcontrol->plan_categ = $masterPolicy->plan_categ;
                $dcontrol->instal_categ = $masterPolicy->instal_categ;
            break;
        }

        if($masterPolicy->ast_marker == 'T') {
            $dcontrol->period_from      = $request->input('cov_period_from');
            $dcontrol->period_to        = $request->input('cov_period_to');
            $dcontrol->cov_period_from  = $request->input('period_from');
            $dcontrol->cov_period_to    = $request->input('period_to');
            $dcontrol->endt_days        = $request->input('t_cover_days');
        } else if($masterPolicy->ast_marker == 'S' && $masterPolicy->short_term_method == 'S'){
            $dcontrol->period_from      = $request->input('cov_period_from');
            $dcontrol->period_to        =$request->input('cov_period_to');
            $dcontrol->cov_period_from  = $request->input('period_from');
            $dcontrol->cov_period_to    = $request->input('period_to');
            $dcontrol->endt_days        = $request->input('t_cover_days');

            $shortTermRate = $this->verifyShortTermRate($request->input('class'),$request->input('t_cover_days'));

            $dcontrol->short_term_method = $masterPolicy->short_term_method;
            $dcontrol->short_term_percent = $shortTermRate;
        } else{
            $dcontrol->period_from      =$request->input('period_from');
            $dcontrol->period_to        =$request->input('period_to');
            $dcontrol->cov_period_from  =$request->input('period_from');
            $dcontrol->cov_period_to    =$request->input('period_to');
        }

        $dcontrol->ext_from = $dcontrol->period_from;
        $dcontrol->ext_to = $dcontrol->period_to;

        $dcontrol->effective_date=$request->input('el_effective_date');
        $dcontrol->branch_code= str_pad($masterPolicy->branchpol, 3,"0",STR_PAD_LEFT);
        $dcontrol->co_insure=$masterPolicy->co_ins;

        if($dcontrol->co_insure=='Y' && $pipcnam->coins_rate_per_sec!='Y'){
            $dcontrol->co_ins_rate = $masterPolicy->co_ins_rate;
            $dcontrol->co_ins_base = $masterPolicy->co_ins_base;
        }else{
            $dcontrol->co_ins_rate = 0;
            $dcontrol->co_ins_base = 0;
        }

        $dcontrol->type_of_bus = $masterPolicy->type_of_bus;
        $dcontrol->dept = $masterPolicy->dept;
        $dcontrol->actual_period_from = $request->input('cov_period_from');
        $dcontrol->actual_period_to = $request->input('cov_period_to');
        $dcontrol->financed = $masterPolicy->financed;
        $dcontrol->items_total = 1;
        $dcontrol->branch_cod = $masterPolicy->branchpol;
        $dcontrol->currency = $currency_code;

        if($masterPolicy->financed == 'Y'){
            $dcontrol->financed_code = $masterPolicy->financier;
        }else{
            $dcontrol->financed_code='';
        }

        if ($masterPolicy->co_ins == 'Y') {
            $dcontrol->company_share = $masterPolicy->co_ins_share;
        } else {
            $dcontrol->company_share = 100;
        }

        $dcontrol->vat_type = $masterPolicy->vat_type;
        $dcontrol->vat_description = $masterPolicy->vat_description;
        $dcontrol->vat_rate = $masterPolicy->vat_rate;
        $dcontrol->vat_code = $masterPolicy->vat_code;

        $dcontrol->endt_renewal_no = (string) $new_endt_renewal_no;
        $dcontrol->dtrans_no = $dtran_no;
        $dcontrol->insured = (string) $insured[0]->name;

        $dcontrol->trans_type = $transaction_type;
        $dcontrol->dola = Carbon::now();

        $dcontrol->sum_insured = 0;
        $dcontrol->location = 0;
        
        $dcontrol->time = Carbon::now();
        $dcontrol->expiry_date = $masterPolicy->expiry_date;

        $dcontrol->pin_no = 'Y'; //$insured[0]->pin_number;
        $dcontrol->client_number = (string) $insured[0]->client_number;
        $dcontrol->surname = (string) $insured[0]->surname;
        $dcontrol->others = (string) $insured[0]->others;
        $dcontrol->first_name = (string) $insured[0]->first_name;
        $dcontrol->client_type = $insured[0]->client_type;

        $dcontrol->incept_date = $masterPolicy->incept_date;
        $dcontrol->company_class_code = $masterPolicy->company_class_code;
        $dcontrol->account_year = $account_year;
        $dcontrol->account_month = $account_month;
        $dcontrol->name = trim($insured[0]->name);

        $dcontrol->cancelled = 'N';
        $dcontrol->reg_no = '';
        $dcontrol->source = 'U/W';
        $dcontrol->doc_type = strtoupper($doc_type);
        $dcontrol->currency_rate = $currency_rate;

        $dcontrol->line_no = 0;
        $dcontrol->renewal_date = $masterPolicy->renewal_date;;
        $dcontrol->binder_flag = $dcontrol->binder_flag;

        $dcontrol->risk_note_no = $request->input('risk_note');
        $dcontrol->external_pol_no = $request->input('external_pol_number');

        if ($dcontrol->binder == 'Y') {
            $dcontrol->binder_pol_no = $masterPolicy->binder_pol_no;
            $dcontrol->bank_code = $masterPolicy->bank_code;
            $dcontrol->bank_branch_code = $masterPolicy->bank_branch_code;
            
            $dcontrol->binder = $masterPolicy->binder;
        }

        # Scheme Policy
        if($masterPolicy->binder == 'S'){
            $dcontrol->binder_pol_no = $masterPolicy->binder_pol_no;
            $dcontrol->bank_code = $masterPolicy->bank_code;
            $dcontrol->bank_branch_code = $masterPolicy->bank_branch_code;
           // $dcontrol->risk_note_no = $masterPolicy->risk_note_no;
           // $dcontrol->external_pol_no = $masterPolicy->external_pol_no;
            $dcontrol->binder = $masterPolicy->binder;
        }

        $dcontrol->fleet = 'N';
        $dcontrol->days_covered = $days_of_cover;
        $dcontrol->pvt_cover = '';
        $dcontrol->endorse_date = Carbon::today();
        $dcontrol->global_account_comm_rate = $request->global_account_comm_rate;

        if (trim($bustype_curr->facult_in) == 'Y'){
            $dcontrol->facin_lead_agent = $masterPolicy->facin_lead_agent;
            $dcontrol->facin_comm_amt = $masterPolicy->facin_comm_amt;
            $dcontrol->facin_total_sum_insured = $masterPolicy->facin_total_sum_insured;
            $dcontrol->facin_sum_insured = $masterPolicy->facin_sum_insured;
            $dcontrol->facin_premium = $masterPolicy->facin_premium;
            $dcontrol->facin_comm_rate = $masterPolicy->facin_comm_rate;
            $dcontrol->facin_premium_rate = $masterPolicy->facin_premium_rate;
            $dcontrol->company_share = $masterPolicy->facin_premium_rate;
            $dcontrol->facin_total_premium = $masterPolicy->facin_total_premium;
            $dcontrol->facultin_client_no = $masterPolicy->facultin_client_no;
        }

        # Master Policy's Endorsement No
        $dcontrol->master_policy = $masterPolicy->master_policy;
        $dcontrol->master_endt_no = $masterPolicy->master_endt_no;

        $validCover = $this->validateCovPeriod($dcontrol, $transaction_type);
        $dcontrol->save();
    }
    catch(\Throwable $e){
        if($e->getCode() == ResponseCode::HTTP_EXPECTATION_FAILED){
            Session::Flash('error', $e->getMessage());
        }
        else{
            Session::Flash('error', 'Failed to extend policy ');
        }
        return redirect()->back();
    }
        # Modify Tables and Replicate Data
        //modify modtl
        /*  if($transaction_type == 'CXT'){
            $uw_year = $dcontrol_prev_record->uw_year;
        } */

        if($class->motor_policy == 'Y'){
            $this->modify_modtl($new_endt_renewal_no, $masterPolicy->endt_renewal_no);
    
        }
        
        //modify polmaster
        $this->modify_polmaster((string) $new_endt_renewal_no, $uw_year);
        //set session variable to blink user created div

        try {
              DB::beginTransaction();

              $procedureName = '' . $gb . '.clone_polmaster_to_polmasteren';
              $bindings = [
                'policy_number'  =>  $masterPolicy->policy_no
              ];
              $resp = DB::executeProcedure($procedureName, $bindings);
              DB::commit();
        } catch (\Throwable $e) {
              // dd($e);
              DB::rollback();
        }

        $this->add_to_workflow($new_endt_renewal_no, $request->input('workflow_id'), $request->input('pid'));

        # Modify Proshed
        $this->replicate_prosched($new_endt_renewal_no, $masterPolicy->endt_renewal_no);

        # Delete Removed Records From Previous Endorsment Before Replicate
        $this->delete_polsec($masterPolicy->endt_renewal_no);

        # Replicate Polsec Details
        $this->replicate_polsec($new_endt_renewal_no, $masterPolicy->endt_renewal_no);

        # Replicate Sec_ext_reinsure Details
        $this->replicate_secExtReinsure($new_endt_renewal_no, $masterPolicy->endt_renewal_no);

        # Replicate Polclause Details
        $this->replicate_polclause($new_endt_renewal_no, $masterPolicy->endt_renewal_no);

        # Delete Removed Records From Previous Endorsment Before Replicate
        $this->delete_polsched($masterPolicy->endt_renewal_no);

        # Replicate Polsched Details
        $this->replicate_polsched($new_endt_renewal_no, $masterPolicy->endt_renewal_no);
        
        # Replicate Marine Details
        $this->replicate_madtl($new_endt_renewal_no, $masterPolicy->endt_renewal_no);

        # Replicate Extensions
        $this->replicate_extensions($new_endt_renewal_no, $masterPolicy->endt_renewal_no);

        // Replicate accessories
        $this->replicate_accessories($new_endt_renewal_no, $masterPolicy->endt_renewal_no);
 
        # Replicate per capita and employee details
        $this->replicate_FG_details($new_endt_renewal_no, $masterPolicy->endt_renewal_no);

        # Replicate Policy Limits
        $this->replicate_limits($new_endt_renewal_no, $masterPolicy->endt_renewal_no);

        # Replicate Policy Excess
        $this->replicate_excess($new_endt_renewal_no, $masterPolicy->endt_renewal_no);

        # Update Polsect Details
        $this->modify_polsect($new_endt_renewal_no, $masterPolicy->endt_renewal_no);

        if ($masterPolicy->ast_marker == 'T') {
            # Recalculate Time on Risk Premium
            $this->timeOnRiskPremium($new_endt_renewal_no);
        } else if ($masterPolicy->ast_marker == 'S') {
            # Recalculate Short Term Premium
            $this->shortTermPremium($new_endt_renewal_no);
        }

        $dcontrol_curr = Dcontrol::where('endt_renewal_no', $new_endt_renewal_no)->get();
        $dcontrol_curr = $dcontrol_curr[0];

        $bustype_curr = Bustype::where('type_of_bus', trim($dcontrol_curr->type_of_bus))->get();
        $bustype_curr = $bustype_curr[0];

        $class = ClassModel::where('class', $dcontrol_curr->class)->get();
        $class = $class[0];

        $department = Dept::where('dept', $class->dept)->get();
        $department = $department[0];
        
        if ($class->combined == 'Y') {

            /*************** REPLICATION OF COMBINED CLASSES ***************/
            
            $combinedPolicy = new CombinedPolicy($masterPolicy->policy_no, $new_endt_renewal_no);

            $combinedPolicy->replicate_polcmb();

            $combinedPolicy->update_current_polcmb();
        
            /*************** END REPLICATION OF COMBINED CLASSES *************/

            Session::flash('success', 'Policy Creation Successful, you can continue with the process');

            return redirect()->route('risk', ['policy_no' => $dcontrol_curr->policy_no, 'endt_renewal_no' => $dcontrol_curr->endt_renewal_no, 'motor' => $department->motor, 'cmb' => 'Y','facult_in'=>trim($bustype_curr->facult_in)]);
        }

        $bustype_curr = Bustype::where('type_of_bus', trim($dcontrol_curr->type_of_bus))->get();
        $bustype_curr = $bustype_curr[0];
        
        if ($class->bypass_location == 'Y' && trim($bustype_curr->facult_in) !='Y') {
            Session::flash('success','Policy Creation Successful, you can continue with the process');
            return redirect()->route('polsched',['endorsement'=>$dcontrol_curr->endt_renewal_no,'location'=>1,'cls'=>$dcontrol_curr->class, 'bond_upd'=>'P']);
        }


        if (trim($bustype_curr->facult_in) == 'Y' && trim($bustype_curr->auto_add_location) =='Y') {
            Session::flash('success','Policy Creation Successful, you continue with the process');
            return redirect()->route('policy_functions',['endt_renewal_no'=>$dcontrol_curr->endt_renewal_no]);
        }

        Session::flash('success', 'Policy number ' . $new_endt_renewal_no . ' added sucessfully');
        return redirect()->route('risk', ['policy_no' => $dcontrol_curr->policy_no, 'endt_renewal_no' => $dcontrol_curr->endt_renewal_no, 'motor' => $department->motor, 'cls' => $dcontrol_curr->class, 'cmb' => 'N','facult_in'=>trim($bustype_curr->facult_in)]);
    }

    public function modify_secEXT($policy_no,$endt_renewal_no){
        // get latest POL/REN/RNS
        $latest_rec = Debitmast::where('policy_no',$policy_no)
                        ->whereRaw("(entry_type_descr='POL' or entry_type_descr='REN' or entry_type_descr='RNS')")
                        ->orderBy('dtrans_no','DESC')
                        ->limit(1)
                        ->get()[0];
        $dcontrol_dtrans = Dcontrol::where("endt_renewal_no",$latest_rec->endt_renewal_no)->get(['dtrans_no'])[0];
        
        // GET ALL TRANSACTION THAT HAPPENEND AFTER POL/REN/RNS
        $prev_recs = Dcontrol::where('policy_no',$policy_no)
                    ->whereRaw("to_number(dtrans_no)>=".(int)$dcontrol_dtrans->dtrans_no)
                    ->where('cancelled','<>','Y')
                    ->where('endt_renewal_no','<>',$endt_renewal_no)
                    ->pluck('endt_renewal_no');
        # Convert Sec_Ext_reinsure Endorse Amount to -Ve
        $secExtData = Sec_ext_reinsure::where('policy_no', $policy_no)
            ->where('endt_renewal_no', $endt_renewal_no)
            ->get();

        foreach ($secExtData as $secExt) {

            $sec_class = ClassModel::where('class', $secExt->class)->first();

            if($sec_class->motor_policy == 'Y'){
                // sum up endorse amount
                $sec_sum = Sec_ext_reinsure::where('policy_no', $policy_no)
                    ->whereIn('endt_renewal_no',$prev_recs)
                    ->where('reg_no',$secExt->reg_no)
                    ->where('ext_code',$secExt->ext_code)
                    ->sum('endorse_amount');

                $secExtUpdate = Sec_ext_reinsure::where('policy_no', $policy_no)
                    ->where('endt_renewal_no', $endt_renewal_no)
                    ->where('reg_no',$secExt->reg_no)
                    ->where('ext_code',$secExt->ext_code)
                    ->update([
                        'endorse_amount' => abs($sec_sum) * -1,
                    ]);
            }
            else{

                $sec_sum = Sec_ext_reinsure::where('policy_no', $policy_no)
                            ->whereIn('endt_renewal_no',$prev_recs)
                            ->where('location',$secExt->location)
                            ->where('section_no',$secExt->section_no)
                            ->sum('endorse_amount');

                $secExtUpdate = Sec_ext_reinsure::where('policy_no', $policy_no)
                ->where('endt_renewal_no', $endt_renewal_no)
                ->where('location',$secExt->location)
                ->where('section_no',$secExt->section_no)
                ->update([
                    'endorse_amount' => abs($sec_sum) * -1,
                ]);
            }

        }

    }
    
    public function fetchCapitaDropdwn(Request $request){
        
        $occup_code = trim($request->occupation_code);

        // $selected_occup_code =  Occupation::whereRaw("trim(occup_code)='".$occup_code."'")->first();
        // $other_occup_code = Occupation::whereNotIn(trim('occup_code'), [$occup_code]) -> get();
        $selected_occup_code =  PerCapitaRate::whereRaw("trim(category_code) = ?", [trim($occup_code)])->first();
        $other_occup_code = PerCapitaRate::whereNotIn('category_code', [$occup_code])->get();
        // dd($selected_occup_code, $other_occup_code);    
        
        return response()->json([
            'selected_occup_code' => $selected_occup_code ,
            'other_occup_code' => $other_occup_code,
            ]);

    }

    public function validateCovPeriod($dcontrol, $transaction_type='N')
    {
        $valid = 'Y';
        $msg = 'valid';

        $incept_date = Carbon::parse($dcontrol->incept_date);
        $effective_date = Carbon::parse($dcontrol->effective_date);
        $period_from = Carbon::parse($dcontrol->period_from);
        $cov_period_from = Carbon::parse($dcontrol->cov_period_from);
        $period_to = Carbon::parse($dcontrol->period_to);
        $cov_period_to = Carbon::parse($dcontrol->cov_period_to);

        if ($incept_date->gt($period_to)) {
            $valid ='N';
            $msg = 'Inception Date cannot be greater than Period To';
        } 
        elseif($effective_date->gt($period_to)) {
            $valid ='N';
            $msg = 'Effective Date cannot be greater than Period To';
        } 
        elseif($period_from->gt($period_to)) {
            $valid ='N';
            $msg = 'Period From cannot be greater than Period To';
        }
        elseif($period_from->gt($cov_period_to)) {
            $valid ='N';
            $msg = 'Period From cannot be greater than Cover Period To';
        } 
        elseif($period_to->gt($cov_period_to) && $transaction_type != 'MAC') {
            $valid ='N';
            $msg = 'Period To cannot be greater than Cover Period To';
        }
 
        elseif($cov_period_from->gt($cov_period_to)) {
            $valid ='N';
        }
        
        if ($valid == 'N'){
            //throw new \Exception('Period from cannot be greater than period to',ResponseCode::HTTP_EXPECTATION_FAILED);
            throw new \Exception($msg, ResponseCode::HTTP_EXPECTATION_FAILED);
            
        }
        return true;
        
    }

        ##uw requisition enquiries
        public function showuwRequisitions(Request $request)
        {
            $branch = str_pad(Auth::user()->branch, 3, '0', STR_PAD_LEFT);
    
            $today = Carbon::today();
            $office = Nlparams::where('prid', 'OFF')
                ->whereRaw("trim(prsno)='" . $branch . "'")
                ->get();
    
            $cbdeduct = Cbdeduct::where('doc_type', 'PAY')->get();
    
            $dat = array(
                'main' => 'Front Office',
                'module' => 'Payments',
                'submodule' => 'Requisitions',
            );

            $tax_groups = Gltaxgroups::all();
            return view('gb.underwriting.uw_requisitions', compact('today', 'office', 'cbdeduct', 'tax_groups'))
                ->with('dat', $dat);
        }
    
        ## function for refund requisitions
        public function rfn_reqs(Request $request)
        {
            Gate::authorize('raise-uw-payment-requisition');
    
            return View('gb.underwriting.rfn_req_enquiry', compact('aimsuser'));
        }
    
        ### function for checking rfn requisitions
        public function pendcheckrequisitiondat(Request $request)
        {
            $code = 'CLM';
            $othercode = 'CB';
    
            $requisition = DB::table('payreqst')
                ->select('payreqst.*', 'polmaster.name as insured')
                ->join('polmaster', 'polmaster.policy_no', '=', 'payreqst.policy_no')
                ->where('payments_todate', 0)
                ->whereRaw("trim(source_code)!='" . $code . "'")
                ->whereRaw("trim(entry_type_descr) ='RFP'")
                ->where("workflow_id", 624)
                ->where('cancelled', null)
                ->get();
            //  $requisition = Payreqst::where('payments_todate',0)
            //                          ->whereRaw("trim(source_code)!='".$code."'")
            //                          ->whereRaw("trim(entry_type_descr) ='RFP'")
            //                          ->where("workflow_id",624)
            //                          ->where('cancelled',null)->get();
            return Datatables::of($requisition)
                ->editColumn('req_no', function ($req) {
                    return formatRequisitionNo($req->req_no);
                })
                ->editColumn('amount', function ($amt) {
                    return number_format($amt->amount, 2);
    
                })
                ->editColumn('approved_date', function ($date) {
    
                    return [
                        'display' => formatDate($date->approved_date),
                        'timestamp' => $date->approved_date,
                    ];
    
                })
                ->make(true);
        }
    
        ### function for authorize rfn requisitions
        public function pendrefauthreq(Request $request)
        {
            $code = 'CLM';
            $othercode = 'CB';
            $requisition = DB::table('payreqst')
                ->select('payreqst.*', 'polmaster.name as insured')
                ->join('polmaster', 'polmaster.policy_no', '=', 'payreqst.policy_no')
                ->where('payments_todate', 0)
                ->whereRaw("trim(source_code)!='" . $code . "'")
                ->whereRaw("trim(entry_type_descr) ='RFP'")
                ->where("workflow_id", 625)
                ->where('cancelled', null)
                ->get();
            //  $requisition = Payreqst::where('payments_todate',0)
            //                          ->whereRaw("trim(source_code)!='".$code."'")
            //                          ->whereRaw("trim(entry_type_descr) ='RFP'")
            //                          ->where("workflow_id",625)
            //                          ->where('cancelled',null)->get();
            return Datatables::of($requisition)
                ->editColumn('req_no', function ($req) {
                    return formatRequisitionNo($req->req_no);
                })
                ->editColumn('amount', function ($amt) {
                    return number_format($amt->amount, 2);
    
                })
                ->editColumn('approved_date', function ($date) {
    
                    return [
                        'display' => formatDate($date->approved_date),
                        'timestamp' => $date->approved_date,
                    ];
    
                })
                ->make(true);
        }
    
        ### function for recommend rfn requisitions
        public function pendrefrecommreq(Request $request)
        {
            $code = 'CLM';
            $othercode = 'CB';
            $requisition = DB::table('payreqst')
                ->select('payreqst.*', 'polmaster.name as insured')
                ->join('polmaster', 'polmaster.policy_no', '=', 'payreqst.policy_no')
                ->where('payments_todate', 0)
                ->whereRaw("trim(source_code)!='" . $code . "'")
                ->whereRaw("trim(entry_type_descr) ='RFP'")
                ->where("workflow_id", 626)
                ->where('cancelled', null)
                ->get();
            //  $requisition = Payreqst::where('payments_todate',0)
            //                          ->whereRaw("trim(source_code)!='".$code."'")
            //                          ->whereRaw("trim(entry_type_descr) ='RFP'")
            //                          ->where("workflow_id",626)
            //                          ->where('cancelled',null)->get();
            return Datatables::of($requisition)
                ->editColumn('req_no', function ($req) {
                    return formatRequisitionNo($req->req_no);
                })
                ->editColumn('amount', function ($amt) {
                    return number_format($amt->amount, 2);
    
                })
                ->editColumn('approved_date', function ($date) {
    
                    return [
                        'display' => formatDate($date->approved_date),
                        'timestamp' => $date->approved_date,
                    ];
    
                })
                ->make(true);
        }
    
        ### function for approved rfn requisitions
        public function pendrefapprovereq(Request $request)
        {
            $code = 'CLM';
            $othercode = 'CB';
            $requisition = DB::table('payreqst')
                ->select('payreqst.*', 'polmaster.name as insured')
                ->join('polmaster', 'polmaster.policy_no', '=', 'payreqst.policy_no')
                ->where('payments_todate', 0)
                ->whereRaw("trim(source_code)!='" . $code . "'")
                ->whereRaw("trim(entry_type_descr) ='RFP'")
                ->where("workflow_id", 627)
                ->where('cancelled', null)
                ->get();
            //  $requisition = Payreqst::where('payments_todate',0)
            //                          ->whereRaw("trim(source_code)!='".$code."'")
            //                          ->whereRaw("trim(entry_type_descr) ='RFP'")
            //                          ->where("workflow_id",627)
            //                          ->where('cancelled',null)->get();
            return Datatables::of($requisition)
                ->editColumn('req_no', function ($req) {
                    return formatRequisitionNo($req->req_no);
                })
                ->editColumn('amount', function ($amt) {
                    return number_format($amt->amount, 2);
    
                })
                ->editColumn('approved_date', function ($date) {
    
                    return [
                        'display' => formatDate($date->approved_date),
                        'timestamp' => $date->approved_date,
                    ];
    
                })
                ->make(true);
        }
    
        ### function for declined rfn requisitions
        public function declinedrfn(Request $request)
        {
            $code = 'CLM';
            $othercode = 'CB';
            $requisition = DB::table('payreqst')
                ->select('payreqst.*', 'polmaster.name as insured')
                ->join('polmaster', 'polmaster.policy_no', '=', 'payreqst.policy_no')
                ->where('payments_todate', 0)
                ->whereRaw("trim(source_code)!='" . $code . "'")
                ->whereRaw("trim(entry_type_descr) ='RFP'")
                ->where('cancelled', 'Y')
                ->get();
            //  $requisition = Payreqst::where('payments_todate',0)
            //                          ->whereRaw("trim(source_code)!='".$code."'")
            //                          ->whereRaw("trim(entry_type_descr) ='RFP'")
            //                          ->where('cancelled','Y')->get();
            return Datatables::of($requisition)
                ->editColumn('req_no', function ($req) {
                    return formatRequisitionNo($req->req_no);
                })
                ->editColumn('amount', function ($amt) {
                    return number_format($amt->amount, 2);
    
                })
                ->editColumn('approved_date', function ($date) {
    
                    return [
                        'display' => formatDate($date->approved_date),
                        'timestamp' => $date->approved_date,
                    ];
    
                })
                ->make(true);
        }
    
        ## function for refund requisitions
        public function fac_reqs(Request $request)
        {
            Gate::authorize('raise-uw-payment-requisition');
    
            return View('gb.underwriting.fac_req_enquiry', compact('aimsuser'));
        }
    
        ### function for checking rfn requisitions
        public function fpendcheckrequisitiondat(Request $request)
        {
            $code = 'CLM';
            $othercode = 'CB';
            $requisition = DB::table('payreqst')
                ->select('payreqst.*', 'polmaster.name as insured')
                ->join('polmaster', 'polmaster.policy_no', '=', 'payreqst.policy_no')
                ->where('payments_todate', 0)
                ->whereRaw("trim(source_code)!='" . $code . "'")
                ->whereRaw("trim(entry_type_descr) ='FAC'")
                ->where("workflow_id", 624)
                ->where('cancelled', null)
                ->get();
    
            //  $requisition = Payreqst::where('payments_todate',0)
            //                          ->whereRaw("trim(source_code)!='".$code."'")
            //                          ->whereRaw("trim(entry_type_descr) ='FAC'")
            //                          ->where("workflow_id",624)
            //                          ->where('cancelled',null)->get();
            return Datatables::of($requisition)
                ->editColumn('req_no', function ($req) {
                    return formatRequisitionNo($req->req_no);
                })
                ->editColumn('amount', function ($amt) {
                    return number_format($amt->amount, 2);
    
                })
                ->editColumn('approved_date', function ($date) {
    
                    return [
                        'display' => formatDate($date->approved_date),
                        'timestamp' => $date->approved_date,
                    ];
    
                })
                ->make(true);
        }
    
        ### function for authorize rfn requisitions
        public function fpendrefauthreq(Request $request)
        {
            $code = 'CLM';
            $othercode = 'CB';
            $requisition = DB::table('payreqst')
                ->select('payreqst.*', 'polmaster.name as insured')
                ->join('polmaster', 'polmaster.policy_no', '=', 'payreqst.policy_no')
                ->where('payments_todate', 0)
                ->whereRaw("trim(source_code)!='" . $code . "'")
                ->whereRaw("trim(entry_type_descr) ='FAC'")
                ->where("workflow_id", 625)
                ->where('cancelled', null)
                ->get();
            //  $requisition = Payreqst::where('payments_todate',0)
            //                          ->whereRaw("trim(source_code)!='".$code."'")
            //                          ->whereRaw("trim(entry_type_descr) ='FAC'")
            //                          ->where("workflow_id",625)
            //                          ->where('cancelled',null)->get();
            return Datatables::of($requisition)
                ->editColumn('req_no', function ($req) {
                    return formatRequisitionNo($req->req_no);
                })
                ->editColumn('amount', function ($amt) {
                    return number_format($amt->amount, 2);
    
                })
                ->editColumn('approved_date', function ($date) {
    
                    return [
                        'display' => formatDate($date->approved_date),
                        'timestamp' => $date->approved_date,
                    ];
    
                })
                ->make(true);
        }
    
        ### function for recommend rfn requisitions
        public function fpendrefrecommreq(Request $request)
        {
            $code = 'CLM';
            $othercode = 'CB';
            $requisition = DB::table('payreqst')
                ->select('payreqst.*', 'polmaster.name as insured')
                ->join('polmaster', 'polmaster.policy_no', '=', 'payreqst.policy_no')
                ->where('payments_todate', 0)
                ->whereRaw("trim(source_code)!='" . $code . "'")
                ->whereRaw("trim(entry_type_descr) ='FAC'")
                ->where("workflow_id", 626)
                ->where('cancelled', null)
                ->get();
            //  $requisition = Payreqst::where('payments_todate',0)
            //                          ->whereRaw("trim(source_code)!='".$code."'")
            //                          ->whereRaw("trim(entry_type_descr) ='FAC'")
            //                          ->where("workflow_id",626)
            //                          ->where('cancelled',null)->get();
            return Datatables::of($requisition)
                ->editColumn('req_no', function ($req) {
                    return formatRequisitionNo($req->req_no);
                })
                ->editColumn('amount', function ($amt) {
                    return number_format($amt->amount, 2);
    
                })
                ->editColumn('approved_date', function ($date) {
    
                    return [
                        'display' => formatDate($date->approved_date),
                        'timestamp' => $date->approved_date,
                    ];
    
                })
                ->make(true);
        }
    
        ### function for approved rfn requisitions
        public function fpendrefapprovereq(Request $request)
        {
            $code = 'CLM';
            $othercode = 'CB';
            $requisition = DB::table('payreqst')
                ->select('payreqst.*', 'polmaster.name as insured')
                ->join('polmaster', 'polmaster.policy_no', '=', 'payreqst.policy_no')
                ->where('payments_todate', 0)
                ->whereRaw("trim(source_code)!='" . $code . "'")
                ->whereRaw("trim(entry_type_descr) ='FAC'")
                ->where("workflow_id", 627)
                ->where('cancelled', null)
                ->get();
            //  $requisition = Payreqst::where('payments_todate',0)
            //                          ->whereRaw("trim(source_code)!='".$code."'")
            //                          ->whereRaw("trim(entry_type_descr) ='FAC'")
            //                          ->where("workflow_id",627)
            //                          ->where('cancelled',null)->get();
            return Datatables::of($requisition)
                ->editColumn('req_no', function ($req) {
                    return formatRequisitionNo($req->req_no);
                })
                ->editColumn('amount', function ($amt) {
                    return number_format($amt->amount, 2);
    
                })
                ->editColumn('approved_date', function ($date) {
    
                    return [
                        'display' => formatDate($date->approved_date),
                        'timestamp' => $date->approved_date,
                    ];
    
                })
                ->make(true);
        }
    
        ### function for declined rfn requisitions
        public function fdeclined(Request $request)
        {
            $code = 'CLM';
            $othercode = 'CB';
            $requisition = DB::table('payreqst')
                ->select('payreqst.*', 'polmaster.name as insured')
                ->join('polmaster', 'polmaster.policy_no', '=', 'payreqst.policy_no')
                ->where('payments_todate', 0)
                ->whereRaw("trim(source_code)!='" . $code . "'")
                ->whereRaw("trim(entry_type_descr) ='FAC'")
            // ->where("workflow_id",624)
                ->where('cancelled', 'Y')
                ->get();
            //  $requisition = Payreqst::where('payments_todate',0)
            //                          ->whereRaw("trim(source_code)!='".$code."'")
            //                          ->whereRaw("trim(entry_type_descr) ='FAC'")
            //                          ->where('cancelled','Y')->get();
            return Datatables::of($requisition)
                ->editColumn('req_no', function ($req) {
                    return formatRequisitionNo($req->req_no);
                })
                ->editColumn('amount', function ($amt) {
                    return number_format($amt->amount, 2);
    
                })
                ->editColumn('approved_date', function ($date) {
    
                    return [
                        'display' => formatDate($date->approved_date),
                        'timestamp' => $date->approved_date,
                    ];
    
                })
                ->make(true);
        }
     
    public function view_smartPolicyCoverNotes(Request $request)
    {
        if($request->ajax()){
            $covernotes = SmartCoverNotes::query();

            return Datatables::of($covernotes)
                ->editColumn('aims_policy',function($data){
                    return formatPolicyOrClaim($data->aims_policy);
                })
                ->make(true);
        }
        else{
            return view('gb.underwriting.smartcovernotes');
        }
    }
    public function smartPolicyCoverNotes(Request $request)
    {
        // dd($request->all());
        $request->validate([
            'date_received' => 'required',
            'covernotes' => 'required|file',
            'taxinvoices' => 'required|file',
        ]);
        
        DB::beginTransaction();
        try{
            $covernoteData = $request->file('covernotes');
            $invoiceData = $request->file('taxinvoices');
        
            $cvrExtension = $covernoteData->getClientOriginalExtension();
            $invExtension = $invoiceData->getClientOriginalExtension();
            
            $errorsCount = 0;
            $errorsList = array();
            //check and verify uploaded file type
            if($cvrExtension == "csv"  || $cvrExtension == "xlsm" || $cvrExtension == "xls" || $cvrExtension == "xlsx"){
                $coverNotes = FileUploadManager::excelUpload($request->file('covernotes'));
            }
            else{
                $errorsCount++;
                $errorsList[$errorsCount] = 'Uploaded Cover Notes File Type Not Supported, Use a CSV, XLS,XLSM or XLSX file! ';
                
            }
            
            if($invExtension == "csv" || $invExtension == "xlsm" || $invExtension == "xls" || $invExtension == "xlsx"){
                $taxInvoices = FileUploadManager::excelUpload($request->file('taxinvoices'));
            }
            else{
                $errorsCount++;
                $errorsList[$errorsCount] = 'Uploaded Tax Invoices File Type Not Supported, Use a CSV, XLS,XLSM or XLSX file! ';
            }
            
            if (is_null($coverNotes)) {
                $errorsCount++;
                $errorsList[$errorsCount] = 'No data found in the covernote file uploaded';
                               
            }
            if (is_null($taxInvoices)) {
                $errorsCount++;
                $errorsList[$errorsCount] = 'No data found in the tax invoice file uploaded';
                               
            }

            if($errorsCount > 0){
                Session::flash('errorsList', $errorsList);
                return redirect()->route('view_smartPolicyCoverNotes');
            }

            $batch_no = (int)SmartTaxInvoices::max('batch_no')+1;

            foreach ($taxInvoices as $row) {
                $row = (object) $row->toArray();
                $receipt_time = null;
                $receipt_date = null;

                if(isset($row->receipt_date)){
                    $receipt_date = Carbon::createFromFormat('d-m-Y',$row->receipt_date);
                }
                if(isset($row->receipt_time)){
                    $receipt_time = Carbon::createFromFormat('d-m-Y H:i:s',"{$row->receipt_date} {$row->receipt_time}");
                }

                $smartTaxInvoices = new SmartTaxInvoices;
                $smartTaxInvoices->batch_no = $batch_no;
                $smartTaxInvoices->debit_note = ltrim(Str::upper($row->debit_note),'DN-');
                $smartTaxInvoices->client_vrn = $row->client_vrn;
                $smartTaxInvoices->client_name = $row->client_name;
                $smartTaxInvoices->policy_date = ExcelDate::excelToDateTimeObject($row->policy_date);
                $smartTaxInvoices->intermediary = $row->intermediary;
                $smartTaxInvoices->client_tin = $row->client_tin;
                $smartTaxInvoices->customer_tax_invoice = $row->customer_tax_invoice;
                $smartTaxInvoices->customer_tax_invoice_date = ExcelDate::excelToDateTimeObject($row->customer_tax_invoice_date);
                $smartTaxInvoices->receipt_code = $row->receipt_code;
                $smartTaxInvoices->verification_code = $row->verification_code;
                $smartTaxInvoices->receipt_date = $receipt_date;
                $smartTaxInvoices->receipt_time = $receipt_time;
                $smartTaxInvoices->total_amount = $row->total_amount;
                $smartTaxInvoices->paid_amount = $row->paid_amount;
                $smartTaxInvoices->currency = $row->currency;
                $smartTaxInvoices->date_received = Carbon::parse($request->date_received);
                $smartTaxInvoices->uploaded_on = Carbon::now();
                $smartTaxInvoices->uploaded_by = Auth::user()->user_name;
                // dd($smartTaxInvoices);
                $smartTaxInvoices->save();
            }

            foreach ($coverNotes as $row) {
                $row = (object) $row->toArray();

                $smartCoverNote = new SmartCoverNotes;
                $smartCoverNote->batch_no = $batch_no;
                $smartCoverNote->risk_note = $row->risk_note_nb;
                $smartCoverNote->created_on = ExcelDate::excelToDateTimeObject($row->date);
                $smartCoverNote->debit_note = $row->debit_note_nb;
                $smartCoverNote->cover_note = $row->cover_note;
                $smartCoverNote->sticker_no = $row->sticker_nb;
                // $smartCoverNote->file_nb = $row->file_nb;
                $smartCoverNote->created_by = $row->created_by;
                $smartCoverNote->insured = $row->insured_name;
                $smartCoverNote->client_name = $row->client_name;
                $smartCoverNote->insurance_type_desc = $row->insurance_type_desc;
                $smartCoverNote->policy_category = $row->policy_category;
                $smartCoverNote->insurance_class_desc = $row->insurance_class_desc;
                $smartCoverNote->reg_no = $row->vehicle_reg_no;
                $smartCoverNote->make = $row->vehicle_make;
                $smartCoverNote->veh_type = $row->vehicle_type;
                $smartCoverNote->engine_no = $row->engine_no;
                $smartCoverNote->chassis_no = $row->chassis_no;
                $smartCoverNote->man_year = $row->year;
                $smartCoverNote->cc = $row->cc;
                $smartCoverNote->color = $row->color;
                $smartCoverNote->period_from = ExcelDate::excelToDateTimeObject($row->period_from_date);
                $smartCoverNote->period_to = ExcelDate::excelToDateTimeObject($row->period_to_date);
                $smartCoverNote->sum_insured = $row->sum_insured;
                $smartCoverNote->gross_premium = $row->gross_premium;
                $smartCoverNote->vat_amt = $row->vat_amount;
                $smartCoverNote->total_premium = $row->total_premium;
                $smartCoverNote->commission_rate = $row->commission_rate;
                $smartCoverNote->commission_amt = $row->broker_commission;
                $smartCoverNote->insurer_settlement_amount = $row->insurer_settlement_amount;
                $smartCoverNote->currency = $row->currency;
                $smartCoverNote->receipt_no = $row->receipt_noref_no;
                $smartCoverNote->policy_no = $row->policy_no;
                $smartCoverNote->loan_account_number = $row->loan_account_number;
                $smartCoverNote->loan_issue_date = $row->loan_issue_date;
                $smartCoverNote->period_in_months = $row->period_in_months;
                $smartCoverNote->region = $row->region;
                $smartCoverNote->branch = $row->branch;
                $smartCoverNote->zone = $row->zone;
                $smartCoverNote->date_received = Carbon::parse($request->date_received);
                $smartCoverNote->uploaded_on = Carbon::now();
                $smartCoverNote->uploaded_by = Auth::user()->user_name;
                $smartCoverNote->save();
            }
            DB::commit();
            Session::flash('success','Successfully uploaded');
            return redirect()->route('view_smartPolicyCoverNotes');
        }
        catch(\Throwable $e){
            DB::rollback();
            //  dd($e);
            Session::flash('error','An error occured');
            return redirect()->route('view_smartPolicyCoverNotes');
        }

    }

    public function smartTaxInvoices(Request $request)
    {
        $taxInvoices = SmartTaxInvoices::query();

        return Datatables::of($taxInvoices)
            ->editColumn('aims_policy',function($data){
                return formatPolicyOrClaim($data->aims_policy);
            })
            ->editColumn('total_amount',function($data){
                return number_format($data->total_amount);
            })
            ->make(true);   
    }

    public function fetchExternalCoverNote(Request $request)
    {
        $q = $request->get('term');
        $taxInvoices = SmartTaxInvoices::whereRaw("customer_tax_invoice like UPPER('%{$q}%') OR client_name like UPPER('%{$q}%')")
            ->where('integrated','N')
            ->get();
        $results = [];
        $results = $taxInvoices->filter(function($taxInvoice){
            $invoice_integrated = Dcontrol::where('external_pol_no',$taxInvoice->customer_tax_invoice)
                ->where('delete_str',null)
                ->exists();

            return !$invoice_integrated;

        })->map(function($taxInvoice){
            $covernote = SmartCoverNotes::where('debit_note',$taxInvoice->debit_note)
                ->where('batch_no',$taxInvoice->batch_no)
                ->first();
            
            $period_from = $covernote->period_from;
            $period_to = $covernote->period_to;
            $policy_days = $period_from->diffInDays($period_to->addDay());
            $ast = 'A';
            if($policy_days < 365){
                $ast = 'S';
            }
            elseif($policy_days > 366){
                $ast = 'T';
            }

            return [
                'value' => $taxInvoice->customer_tax_invoice,
                'label' => "{$taxInvoice->customer_tax_invoice}-{$taxInvoice->client_name}",
                'tax_invoice' => $taxInvoice->customer_tax_invoice,
                'ast_marker' => $ast,
                'period_from' => formatDateRFC3339($covernote->period_from),
                'period_to' => formatDateRFC3339($covernote->period_to),
            ];
        });

        return json_encode($results);
    }

    public function update_external_coverNote($dcontrol)
    {
        $taxInv = SmartTaxInvoices::where('customer_tax_invoice',$dcontrol->external_pol_no)->first();

        if($taxInv->integrated == 'N'){
            SmartTaxInvoices::where('customer_tax_invoice',$dcontrol->external_pol_no)
                ->update([
                    'integrated' => 'Y',
                    'aims_policy' => $dcontrol->endt_renewal_no,
                ]);

            SmartCoverNotes::where('batch_no',$taxInv->batch_no)
                ->where('debit_note',$taxInv->debit_note)
                ->where('client_name',$taxInv->client_name)
                ->update([
                    'integrated' => 'Y',
                    'aims_policy' => $dcontrol->endt_renewal_no,
                ]);
        }
    }

    public function pol_ren($trans_type, $pol_no){

        if($trans_type == 'POL'){
            return 'POL';
        }

        if($trans_type == 'REN'){
            return 'REN';
        }

        if($trans_type != "POL" && $trans_type !='REN'){

            $transaction = Polmasterend::where('policy_no', $pol_no)
                                    ->where(function($status){
                                        $status->where('status', '!=', 'Y')
                                               ->orWhereNull('status');
                                    })
                                    ->where(function($ren){
                                        $ren->where('trans_type', 'POL')
                                            ->orWhere('trans_type', 'REN');
                                    })
                                    ->orderBy('period_from', 'DESC')
                                    ->orderBy('proposal_no', 'DESC')
                                    ->first();

            return $transaction->trans_type;

        }

    }


    public function redirect_nil_endorsement(Request $request){

      
        $endt_renewal_no = $request->endt_renewal_no;
        $nil_type = $request->nil_code;
        $dcontrol = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();
        $nil_endorse_type = Nil_endorsetypes::where('endt_type',$nil_type)->first();

          if(($nil_endorse_type->slug == 'update-customer-details') && ($nil_endorse_type->show_edit_screens == 'Y'  )){

            // dd(1);
             $route =  route('edit_client',['client_number'=>$dcontrol->client_number,'policy_no' => $dcontrol->policy_no, 
                            'endt_renewal_no'=>$dcontrol->endt_renewal_no,'nil_endt'=>'Y']);

        
          }else{
            $route = '';

          }

          return response()->json(['route' => $route]);
    }

    public function update_bpdate_request($check_flag, $request_no, $endt_renewal_no){

        if($check_flag == 'Y'){

            $upd = Backpostdate_requests::where('requisition_no',$request_no)
                ->update([
                    'endt_renewal_no' => $endt_renewal_no
                ]);

        }

    }

    ##uw fac_uploads enquiries
    public function facuploads(Request $request)
    {

        $dat = array(
            'main' => 'U/W',
            'module' => 'Payments',
            'submodule' => 'Facultative CRNs',
        );

        $batch_no = $request->batch_no;

        return view('gb.underwriting.facul_uploads_details', compact('today', 'office','batch_no'))
            ->with('dat', $dat);
    }
    ##uw fac_uploads enquiries
    public function facdatatable(Request $request)
    {

        $branch = str_pad(Auth::user()->branch, 3, '0', STR_PAD_LEFT);

        $today = Carbon::today();
        $office = Nlparams::where('prid', 'OFF')
            ->whereRaw("trim(prsno)='" . $branch . "'")
            ->get();

        $items = FacUploads::where('batch_no',$request->batch_no)->get();

        return Datatables::of($items)
        ->addColumn('action',function($items){
            return '<a class="btn btn-xs" id="" style="color:blue;" >View</a>';
        })
        ->rawColumns(['cancel','action'])
            ->make(true); 


    }

    public function saveStickerReplacementDtl($endt_renewal_no,$request)
    {
        try {
            $reg_nos = $request->reg_no;
            $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();

            foreach ($reg_nos as $reg_no) {
                $dtl = new StickerRepldtl;
                $dtl->policy_no = $dcontrol->policy_no;
                $dtl->endt_renewal_no = $dcontrol->endt_renewal_no;
                $dtl->class = $dcontrol->class;
                $dtl->reg_no = $reg_no;
                $dtl->dola = Carbon::now();
                $dtl->reason = $request->replacement_reason;
                $dtl->save();

            }
        } catch (\Throwable $e) {
            throw $e;
        }
    }
    public function get_rate_allowance(Request $request){
        $currency =(int) str_replace(",","",$request->currency);
        $rate = (float)str_replace(",","",$request->rate);
        $today = Carbon::today();

      
        $currecord =Currency::where("currency_code",$currency)->first();

        $currency_allowance =$currecord->rate_allowance;
        $today_currate =Currate::where("currency_code",$currency)
                                ->whereDate('rate_date', $today)
                                ->first();
        $maxrate =($today_currate->currency_rate) +$currency_allowance;
      
        if($rate > $maxrate){
            $formatted =number_format($maxrate);
            return [
                "status"=>-1,
                 "msg" =>"rate is above the allowable rate of  $formatted in the system"
            ];
        }
        return [
            "status"=>1
        ];


    }

    public function saveInstallmentstatus($dcontrol){

        // call our selected plan
        $plan = Instalparam::where('plan', $dcontrol->plan)
        ->where('plan_categ', $dcontrol->plan_categ)    
        ->where('instal_categ', $dcontrol->instal_categ) 
        ->first();   

        $next_plan = Instalparam::where('plan', $dcontrol->plan)
        ->where('plan_categ', (int) $dcontrol->plan_categ + 1)    
        ->where('instal_categ', (int) $dcontrol->instal_categ + 1) 
        ->first();  

        // check if record exists
        $data = false;
        $check_data = Installmentstatus::where('endt_renewal_no', $dcontrol->endt_renewal_no)->get();

        if (count($check_data) > 0) {
            $data = Installmentstatus::where('endt_renewal_no', $dcontrol->endt_renewal_no)
            ->update([
                "policy_no" => $dcontrol->policy_no,
                "trans_type" => $dcontrol->trans_type,
                "ast_marker" => $dcontrol->ast_marker,
                "plan" => $dcontrol->plan,
                "install_categ" => $dcontrol->instal_categ,
                "next_install_categ" => $next_plan->instal_categ,
                "plan_categ" => $dcontrol->plan_categ,
                "next_plan_categ" => $next_plan->plan_categ,
                "instal_rate" => $plan->instal_rate,
                "next_instal_rate" => $next_plan->instal_rate,
            ]);
        }else {
            $data = Installmentstatus::create([
                "policy_no" => $dcontrol->policy_no,
                "endt_renewal_no" => $dcontrol->endt_renewal_no,
                "trans_type" => $dcontrol->trans_type,
                "ast_marker" => $dcontrol->ast_marker,
                "plan" => $dcontrol->plan,
                "install_categ" => $dcontrol->instal_categ,
                "next_install_categ" => $next_plan->instal_categ,
                "plan_categ" => $dcontrol->plan_categ,
                "next_plan_categ" => $next_plan->plan_categ,
                "instal_rate" => $plan->instal_rate,
                "next_instal_rate" => $next_plan->instal_rate,
            ]);
            
        } 

        // check if data was saved
        if ($data) {
            return [
                'code' => 1,
                'msg' => 'Data successfully saved' 
            ];
        }


        return [
            'code' => -1,
            'msg' => 'Error while saving data' 
        ];


    }

    public function get_special_class(Request $request)
    {
        $classId = $request->input('class');

        $class = ClassModel::find($classId);

        return response()->json($class->special_class);
    }
    
    public function getSalesChannels()
    {
        $salesChannels = DB::table('sales_channel')->select('id', 'description')->where('active', 'Y')->get();

        return response()->json($salesChannels);  
    }

    public function get_policy_wordings(Request $request)
    {
        $classId = $request->input('class');

        $class = ClassModel::find($classId);

        return response()->json($class->policy_wordings);
    }

    public function getPolicyWording(Request $request)
    {
        $class = $request->input('class');

        // Use a join or a relationship to fetch document_description
        $policyWordings = ProductPolWording::where('class', $class)
                            ->join('policydocuments', 'product_pol_wordings.policydocid', '=', 'policydocuments.id')
                            ->select('product_pol_wordings.heading', 'policydocuments.document_description','product_pol_wordings.policydocid' )
                            ->get();

        return response()->json($policyWordings);
    }

    public function getOccupationsByClassGroup(Request $request){
        $classgrp = $request->input('classgrp');
        $occupations = PerCapitaRate::where('classgrp', $classgrp)->get();

        return response()->json($occupations);
    }
}

