<?php

namespace App\Http\Controllers\gb\underwriting;

use App\Dept;
use App\Ibnr;
use App\Acdet;
use App\Agcom;
use App\Agmnf;
use App\Clhmn;
use App\Madtl;
use App\Modtl;
use Throwable;
use App\Client;
use App\Crmast;
use App\Dtran0;
use App\Months;
use App\Period;
use App\Polcmb;
use App\Polsec;
use App\Reidoc;
use App\Bustype;
use App\Clauses;
use App\Currate;
use App\Pipcnam;
use App\Pipstmp;
use App\Polsect;
use App\Cbcredit;
use App\Certmast;
use App\Certtype;
use App\Coinpart;
use App\Currency;
use App\Dcontrol;
use App\Debitdtl;
use App\Modtlend;
use App\Nlparams;
use App\Olbranch;
use App\Payreqst;
use App\Polredtl;
use App\Tax_code;
use App\VatSetup;
use App\Accessory;
use App\Binderpol;
use App\Certalloc;
use App\Classsect;
use App\Classyear;
use App\Covertype;
use App\Debitmast;
use App\Polclause;
use App\Polexcess;
use App\Pollimits;
use App\Polmaster;
use App\Polremast;
use App\Polrepart;
use App\Reinscons;
use App\Reinsetup;
use App\SendEmail;
use App\Vat_setup;
use Carbon\Carbon;
use App\Autolimits;
use App\Classexces;
use App\ClassModel;
use App\Creditmast;
use App\Endtrepart;
use App\Extensions;
use App\Olbnknames;
use App\Polclauses;
use App\Polsectend;
use App\Agcom_categ;
use App\Binderlimit;
use App\ComesaParam;
use App\Instalparam;
use App\Mgt_expense;
use App\Userprofile;
use App\Aimsuser_web;
use App\Binderclause;
use App\Binderexcess;
use App\Escalate_pol;
use App\Mautoclauses;
use App\Policy_notes;
use App\Polmasterend;
use App\Polmisc_fees;
use App\Polremastend;
use App\Endorse_descr;
use App\MarineUTLDebit;
use App\Aimsgrouplimits;
use App\Firstloss_rates;
use App\Marinemasterpol;
use App\Misc_fees_param;
use App\Models\Aimsuser;
use App\Models\Polscope;
use App\Makersubmissions;
use App\Marinemasterhist;
use App\Models\Modtlmast;
use App\Models\Modtlsumm;
use App\Models\Motcvrdet;
use App\Motor_cert_setup;
use App\Nil_endorsetypes;
use App\Risk_audit_trail;
use App\Models\Modtlpivot;
use App\Discounts_loadings;
use App\MarineUTLDebitmast;
use App\Models\Motorpolsec;
use App\Models\Polcoverage;
use App\Models\Polwarranty;
use App\Models\Polcondition;
use App\Models\Polexclusion;
use Illuminate\Http\Request;
use App\Models\NarrationType;
use App\Models\Poldisclaimer;
use App\DiscountLoadingparams;
use App\Modtl_not_in_modtlend;
use Yajra\Datatables\Datatables;
use App\Cert_serials_unallocated;
use App\Models\Medical\Polmedsec;
use App\Models\PremiumPaymentPlan;
use App\Polsect_not_in_polsectend;
use Illuminate\Support\Facades\DB;
use Aimsoft\Tiramis\Models\Tradata;
use App\Models\Medical\Polmedlimit;
use App\Models\RecommendationTypes;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Response;;
use App\Models\CreditFacility\CreditSetup;
use App\Services\IntermediaryQueryService;
use Aimsoft\UserManagement\Models\Permission;
use App\ValueObjects\IntermediaryQueryParams;
use App\Http\Controllers\gb\underwriting\Risk;
use App\Services\UserGroupLimitCheckerService;
use App\Classes\gb\underwriting\CombinedPolicy;
use App\Clparam;
use App\Http\Controllers\gb\medical\Medicalrisk;
use App\Http\Controllers\gb\underwriting\Policy;
use App\Http\Requests\SpecialCertApprovalRequest;
use App\Http\Controllers\gb\underwriting\Reinsurance;
use App\Http\Controllers\gb\underwriting\ComesaExtension;
use App\Http\Controllers\gb\underwriting\Extra_endorsements;
use App\Http\Controllers\gb\underwriting\MarineOpenProcessing;
use App\Models\UwParameter;
use App\Debitmastsnap;


# Extend Risk Controller To Call Functions Specified in Delete Endorsements
class Policy_functions extends Risk
{ 


    public function index(Request $request)
    {   


        $schem = schemaName();
        $username = trim(Auth::user()->user_name);
        $aimsuser_web = Aimsuser_web::whereRaw("trim(user_name)='" . $username . "'")->get()[0];

        $checkgroup = $aimsuser_web->aims_group; 
     

        $debbit = Gate::check('debit-policy');
        $reinsurance = Gate::check('process-reinsurance');
        $printpolicydoc = Gate::check('print-policy-document');

        $check_clause = Polclause::where('endt_renewal_no', $request->get('endt_renewal_no'))->count();
        
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];


        // $getpolmast_amount = Polmaster::where('endorse_no', $request->get('endt_renewal_no'))->get()[0];
        $dcontrol_doc = Dcontrol::where('endt_renewal_no',$request->get('endt_renewal_no'))->get()[0];
        $debitmast_rec = Debitmast::whereRaw("trim(endt_renewal_no)='" . trim($dcontrol_doc->endt_renewal_no) ."'")->first();
        $debitmastsnap = Debitmastsnap::where('endt_renewal_no', trim($dcontrol_doc->endt_renewal_no))->where('predebit_type', 'PROFORMA')->first();
        if(isset($debitmastsnap) || isset($debitmast_rec)){
            $print_proforma_sched = 'Y';
        }
        $intermediaryParams = new IntermediaryQueryParams([
            'agentNo' => $debitmast_rec->agent_no,
        ]);
        $intemediaryTaxes  = IntermediaryQueryService::getIntermediaryWithTaxes($intermediaryParams);

        # Check if Endorsement Was Deleted
        // if ($dcontroloc->delete_str == 'Y') {
        //     $polmaster = Polmaster::where('policy_no', $dcontrol_doc->policy_no)->first();

        //     $request['policy_no'] = $dcontrol_doc->policy_no;
        //     $request['policy_uw_year'] = $polmaster->uw_year;

        //     return redirect()->route('endorse_functions', ['policy_no' => $request->policy_no, 'uw_year' => $request->uw_year])->with('success', 'The endorsement '. formatPolicyOrClaim($dcontrol_doc->endt_renewal_no) ." you're trying to acces has been cancelled");
        // }


        if ($request->isMethod('post')) {

            $endt_renewal_no = $request->post('endt_renewal_no');
            $process_id = $request->post('pid');
        }

        if ($request->isMethod('get')) {

            $endt_renewal_no = $request->get('endt_renewal_no');
            $process_id = null;
        }
        
        // check if its pol or another endorsement


        $dcontrol_cls = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol_cls = $dcontrol_cls[0];

        $certalloc_cls = Certalloc::where('endt_renewal_no', $endt_renewal_no)->where('cert_status', 1)->orderBy('period_to', 'DESC')->get();
        $certalloc = $certalloc_cls[0];

        $class = ClassModel::where('class', $dcontrol_cls->class)->get();

        if($dcontrol_cls->trans_type == 'CNC'){
            $active_modtl = Modtl::where('policy_no', $dcontrol_cls->policy_no)->whereRaw("cancelled<>'Y'")->count();

        }else{
            $active_modtl = Modtl::where('endt_renewal_no', $endt_renewal_no)->whereRaw("cancelled<>'Y'")->count();
        }


        $classInstance = ClassModel::where('class', $dcontrol_cls->class)->first();

        if($classInstance->polsched_route != null){
            $polsched_route=$classInstance->polsched_route;
        }
              
        
        $clean_polsched_route=str_replace('/{endorse_no}','',$polsched_route);

        ##loss ration
          $getpercentlossratio=Pipcnam::all();
          $clampaid=Clhmn::where('client_number',$dcontrol_cls->client_number)->sum('local_cost_todate');
          $clamest=Clhmn::where('client_number',$dcontrol_cls->client_number)->sum('local_curr_total_estimate');
          $out_bal=Acdet::where('client_number',$dcontrol_cls->client_number)->sum('unallocated');
          $gross_premium=Debitmast::where('client_number',$dcontrol_cls->client_number)->sum('gross_amount');
          
          if($gross_premium==0 ){
              $lor = 0;
          }else{
              $lor = (($clampaid+$clamest) / $gross_premium) * 100 ;
        }
         ##end loss atio
      
        if($classInstance->policy_doc_route != null){
            $doc_route=$classInstance->policy_doc_route;
        }

        $clean_doc_route=str_replace('/{endorse_no}','',$doc_route);
        
        if($dcontrol_cls->trans_type == 'EXT' || $dcontrol_cls->trans_type == 'CXT' || $dcontrol_cls->trans_type == 'RNS'){
            $endt_endorse_amount = Modtlend::where('endt_renewal_no', $endt_renewal_no)->whereRaw("cancelled<>'Y'")->where('type', 'E')->sum('endorse_amount');
            if($endt_endorse_amount == 0){
                $endt_endorse_amount = Modtl::where('endt_renewal_no', $endt_renewal_no)->where('type', 'D')->count();
            }
            $endt_sum_insured = Modtlend::where('endt_renewal_no', $endt_renewal_no)->whereRaw("cancelled<>'Y'")->where('type', 'E')->sum('sum_insured');

            $cancelled_modtl = Modtl::where('endt_renewal_no', $endt_renewal_no)->where('cancelled' ,'Y')->where('type', 'E')->get();
        }

        $endt_endorse_amount = Modtlsumm::where('endt_renewal_no', $endt_renewal_no)->sum('endorse_amount');
        $endt_sum_insured = Modtlsumm::where('endt_renewal_no', $endt_renewal_no)->sum('sum_insured');
        // dd($endt_endorse_amount);
        
        $pipcnam = Pipcnam::where('record_type',0)->get();
        $pipcnam = $pipcnam[0];

        $limit_dept =  $class[0]->dept;
        $limitgroup = Aimsgrouplimits::where('group_id', $checkgroup)->where('dept', $limit_dept)->get()[0];

        // $checkexist_escalation = DB::select(DB::raw("select * from escalate_pol where endorse_no = '$endt_renewal_no' and re_escalate_date is null and (type = 'DRN' or type = 'CRN') order by created_at desc fetch next 1 rows only"));
        
        $checkexist_escalation = Escalate_pol::where('endorse_no', $endt_renewal_no)
        ->where(function($query){
            $query->where('type','DRN')
                ->orWhere('type','CRN')
                ->orWhere('type','NIL');
        })
        ->where('re_escalate_date', null)
        ->orderBy('created_at','desc')
        ->first();

        $checkexist_nil_escalation = Escalate_pol::where('endorse_no', $endt_renewal_no)
        ->where(function($query){
            $query->where('type','NIL');
        })
        ->where('re_escalate_date', null)
        ->where('approved','!=', 'D')
        ->orderBy('created_at','desc')
        ->count();


        $checkdmc_escalation = Escalate_pol::where('endorse_no', $endt_renewal_no)
        ->where('type','SPT')
        ->where('re_escalate_date', null)
        ->orderBy('created_at','desc')
        ->first();

        $checkexistcredit = Cbcredit::where('claim_no',$endt_renewal_no)
                                        ->get();
      
        $motor = 'Y';

        switch ($class[0]->motor_policy) {
            
            case 'Y':
                $motor = 'Y';
               
                break;

            default:
                $motor = 'N';
                break;
        }
        // dd($dcontrol_cls);
        //check for 
        switch ($dcontrol_cls->trans_type) {
            case 'POL':
            case 'MAC':
                
                $polmaster_count = Polmaster::where('endorse_no', $endt_renewal_no)->count();
                $polmasterend_count = Polmasterend::where('endorse_no', $endt_renewal_no)->count();

                if ($polmaster_count > 0) {

                    $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->get();
                    
                } else if ($polmasterend_count > 0) {

                    $polmaster = Polmasterend::where('endorse_no', $endt_renewal_no)->take(1)->get();
                    
                } else {

                    $polmaster = Polmaster::where('policy_no', $dcontrol_cls->policy_no)->get();

                }

                $count_polsect = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('policy_no', $dcontrol_cls->policy_no)->count();
                $count_polsectend = Polsectend::where('endt_renewal_no', $endt_renewal_no)->where('policy_no', $dcontrol_cls->policy_no)->count();

                if ($count_polsect != $count_polsectend) {
                    $this->clone_polsect_to_polsectend($endt_renewal_no);
                    
                }

                $limit_count = Pollimits::where('endt_renewal_no', $dcontrol_cls->endt_renewal_no)->count();
                $excess_count = Polexcess::where('endt_renewal_no', $dcontrol_cls->endt_renewal_no)->count();
                $clause_count = Polclauses::where('policy_no', $dcontrol_cls->policy_no)->where('endt_renewal_no', $endt_renewal_no)->count();
                
                if($dcontrol_cls->binder == "Y"||$dcontrol_cls->binder == "S"){
                    //apply binder clauses 
                    if($clause_count == 0 ){
                        $this->apply_binder_clauses($endt_renewal_no);
                    }
                    //apply binder limits 
                    if($limit_count == 0 ){
                        $this->apply_binder_limits($endt_renewal_no);
                    }
                    //apply binder excesses
                    if($excess_count == 0 ){
                        $this->apply_binder_excess($endt_renewal_no);
                    }
                }else{
                    //apply standard clauses 
                    if($clause_count == 0 ){
                        // $this->apply_clauses_new($endt_renewal_no);
                        $this->autoPopulateStdClauses($endt_renewal_no, $dcontrol_cls->policy_no);
                    }
                    //apply standard limits 
                    if($limit_count == 0 ){
                        //$this->apply_std_limits($endt_renewal_no);
                    }
                    //apply standard excesses
                    if($excess_count == 0 ){
                        $this->apply_std_excess($endt_renewal_no);
                    }
                }

               
                break;

            case 'EXT':
            case 'CXT':
            case 'STK':
                //case for extra endorsements

                $polmaster_count = Polmaster::where('endorse_no', $endt_renewal_no)->count();
                $polmasterend_count = Polmasterend::where('endorse_no', $endt_renewal_no)->count();

                if ($polmaster_count > 0) {

                    $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->get();
                } else if ($polmasterend_count > 0) {

                    $polmaster = Polmasterend::where('endorse_no', $endt_renewal_no)->take(1)->get();
                } else {

                    $polmaster = Polmaster::where('policy_no', $dcontrol_cls->policy_no)->get();
                }

                $count_polsect = Polsect::where('endt_renewal_no', $endt_renewal_no)
                                        ->where('policy_no', $dcontrol_cls->policy_no)
                                        ->count();

                $count_polsectend = Polsectend::where('endt_renewal_no', $endt_renewal_no)->where('policy_no', $dcontrol_cls->policy_no)
                                                ->count();

                if ($count_polsect != $count_polsectend) {
                    $this->clone_polsect_to_polsectend($endt_renewal_no);                    
                }
                
                break;                                
            case 'NIL':
                //case for nil endorsements
                $polmaster_count = Polmaster::where('endorse_no', $endt_renewal_no)->count();
                $polmasterend_count = Polmasterend::where('endorse_no', $endt_renewal_no)->count();

                if ($polmaster_count > 0) {

                    $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->get();
                } else if ($polmasterend_count > 0) {

                    $polmaster = Polmasterend::where('endorse_no', $endt_renewal_no)->get();
                } else {

                    $polmaster = Polmaster::where('policy_no', $dcontrol_cls->policy_no)->get();
                }

                break;
            case 'PTA':
                //case for comesa endorsements
                // $ptaComesa = new ComesaExtension;
                // $pta = $ptaComesa->applyComesaRates($endt_renewal_no);

                $noOfComesaCars = Modtlmast::where('endt_renewal_no', $endt_renewal_no)->count();

                $polmaster_count = Polmaster::where('endorse_no', $endt_renewal_no)->count();
                $polmasterend_count = Polmasterend::where('endorse_no', $endt_renewal_no)->count();

                if ($polmaster_count > 0) {

                    $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->get();
                } else if ($polmasterend_count > 0) {

                    $polmaster = Polmasterend::where('endorse_no', $endt_renewal_no)->get();
                } else {

                    $polmaster = Polmaster::where('policy_no', $dcontrol_cls->policy_no)->get();
                }
    
                // Clone Polsect to Polsectend
                $count_polsect = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('policy_no', $dcontrol_cls->policy_no)->count();
                $count_polsectend = Polsectend::where('endt_renewal_no', $endt_renewal_no)->where('policy_no', $dcontrol_cls->policy_no)->count();

                if ($count_polsect != $count_polsectend) {
                    $this->clone_polsect_to_polsectend($endt_renewal_no);
                }

            break;

            case 'REN':
            case 'INS':
                //case for renewals

                $polmaster_count = Polmaster::where('endorse_no', $endt_renewal_no)->count();
                $polmasterend_count = Polmasterend::where('endorse_no', $endt_renewal_no)->count();
                if ($polmaster_count > 0) {

                    // $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->get();

                    /*Change polmaster endorse_amount to be renewal_premium(very important)*/

                    $polmaster_rec = Polmaster::where('endorse_no', $endt_renewal_no)->get();
                    $renewal_prem = $polmaster_rec[0]->renewal_premium;

                    //check if polmaster renewal premium= polmaster endorse amount

                    if ($polmaster_rec[0]->endorse_amount != $renewal_prem) {

                        //change polmaster endorse amount

                        // $polmaster_amend = Polmaster::where('policy_no', $polmaster_rec[0]->policy_no)
                        //     ->update(['endorse_amount' => $renewal_prem]);
                    }

                    $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->get();

                    /*End of change polmaster endorse_amount to be renewal_premium*/
                } else if ($polmasterend_count > 0) {

                    $polmaster = Polmasterend::where('endorse_no', $endt_renewal_no)->take(1)->get();
                } else {

                    $polmaster = Polmaster::where('policy_no', $dcontrol_cls->policy_no)->get();
                }

                # Clone Polsect to Polsectend
                $count_polsect = Polsect::where('endt_renewal_no', $endt_renewal_no)
                    ->where('policy_no', $dcontrol_cls->policy_no)
                    ->count();

                $count_polsectend = Polsectend::where('endt_renewal_no', $endt_renewal_no)->where('policy_no', $dcontrol_cls->policy_no)
                    ->count();

                if ($count_polsect != $count_polsectend) {
                    $this->clone_polsect_to_polsectend($endt_renewal_no);
                }


                //ensure clauses,limits and excesses are added to respective tables for renewal
                $dcontrol_user = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get(['user_str']);
                $dcontrol_user = $dcontrol_user[0];

                $limit_count = Pollimits::where('endt_renewal_no', $dcontrol_cls->endt_renewal_no)->count();
                $excess_count = Polexcess::where('endt_renewal_no', $dcontrol_cls->endt_renewal_no)->count();
                $clause_count = Polclause::where('policy_no', trim($dcontrol_cls->policy_no))->where('endt_renewal_no', trim($endt_renewal_no))->count();
                // dd($clause_count);
                if($dcontrol_cls->binder == "Y"||$dcontrol_cls->binder == "S"){

                    //apply binder clauses 
                    if($clause_count == 0 ){
                        $this->apply_binder_clauses($endt_renewal_no);
                    }
                    //apply binder limits 
                    if($limit_count == 0 ){
                        $this->apply_binder_limits($endt_renewal_no);
                    }
                    //apply binder excesses
                    if($excess_count == 0 ){
                        $this->apply_binder_excess($endt_renewal_no);
                    }
                }else{

                    //apply standard clauses 
                    if($clause_count == 0 ){
                        // $this->apply_clauses_new($endt_renewal_no);
                       // $this->autoPopulateStdClauses($endt_renewal_no, $dcontrol_cls->policy_no);
                    }
                    //apply standard limits 
                    if($limit_count == 0 ){
                        //$this->apply_std_limits($endt_renewal_no);
                    }
                    //apply standard excesses
                    if($excess_count == 0 ){
                        $this->apply_std_excess($endt_renewal_no);
                    }
                }




                break;

            case 'RFN':
            case 'CNC':
            case 'RNS':

                $dcontrol_rfn = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
                $dcontrol_rfn = $dcontrol_rfn[0];

               // $polmaster = Polmaster::where('policy_no', $dcontrol_rfn->policy_no)->get();

                $polmaster_count = Polmaster::where('endorse_no', $endt_renewal_no)->count();
                $polmasterend_count = Polmasterend::where('endorse_no', $endt_renewal_no)->count();

                if ($polmaster_count > 0) {

                    $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->get();
                } else if ($polmasterend_count > 0) {

                    $polmaster = Polmasterend::where('endorse_no', $endt_renewal_no)->take(1)->get();
                } else {

                    $polmaster = Polmaster::where('policy_no', $dcontrol_cls->policy_no)->get();
                }



                break;
        } //end switch endo-type


        $debitmast_records_count = Debitmast::where('endt_renewal_no', $endt_renewal_no)->count();
        $MarineUTLDebit = MarineUTLDebit::where('endt_renewal_no', $endt_renewal_no)->count();
        $debited = 'N';

        if ($debitmast_records_count > 0 || $MarineUTLDebit > 0) {

            $debited = 'Y';
        }

        $debitmast = Debitmast::where('endt_renewal_no', $endt_renewal_no)->get();

        // $polremastend = Polremastend::where('endt_renewal_no', $endt_renewal_no)->get();

         // $polremastend =  DB::table('polremastend')->select('polremastend.*','class.description as class_description')
        //                     ->join('class', 'class.class', '=', 'polremastend.comb_class')
        //                     ->where(['endt_renewal_no' => $endt_renewal_no])->get();
        $polremastend = Polremastend::where('polremastend.endt_renewal_no', $endt_renewal_no)
            ->join('class', 'class.class', '=', 'polremastend.comb_class')
            ->when($classInstance->motor_policy == 'Y', function($query){
                $query->leftJoin('modtlpivot', function($join) {
                    $join->on('polremastend.endt_renewal_no', '=', 'modtlpivot.endt_renewal_no')
                    ->on('polremastend.location', '=', 'modtlpivot.item_no');
                });
            })
            ->when($classInstance->motor_policy =='N', function($query){
                $query->leftJoin('polsectend', function($join) {
                    $join->on('polremastend.endt_renewal_no', '=', 'polsectend.endt_renewal_no')
                        ->on('polremastend.location', '=', 'polsectend.location');
                    });
            })
            ->when($classInstance->motor_policy =='Y', function($query){
                $query->select('polremastend.*','modtlpivot.reg_no as risk_description','class.description as class_description');
            })
            ->when($classInstance->motor_policy =='N', function($query){
                $query->select('polremastend.*', DB::raw('TRIM(polsectend.name) as risk_description'),'class.description as class_description');
            })
            ->get();
        
        switch($dcontrol_cls->trans_type) {
            case 'POL':
            case 'MAC':
                $prevRemast = null;
                break;
            default:
                # Get Previous Endt
                $prev_endt_no = $this->previousEndorsement($dcontrol_cls->endt_renewal_no);

                $prevRemast =  DB::table('polremastend')
                    ->select('polremastend.*', 'class.description as class_description')
                    ->join('class', 'class.class', '=', 'polremastend.comb_class')
                    ->where(['endt_renewal_no' => $prev_endt_no])
                    ->get();
                break;
                
        }
        $polremast = Polremast::where('endt_renewal_no', $endt_renewal_no)->get();

        $class = ClassModel::where('class', $polmaster[0]->class)->get();

        //get currency code from dcontrol
        $dcontrol = Dcontrol::where('endt_renewal_no', $polmaster[0]->endorse_no)->get(); 
        //dd($dcontrol);
        //dd($dcontrol[0]->delete_str == 'Y');

        // FETCH LATEST POL/REN/RNS
        $latest_rec = Debitmast::where("policy_no",$dcontrol[0]->policy_no)
            ->whereRaw("(entry_type_descr='POL' or entry_type_descr='REN' or entry_type_descr='RNS')")
            ->orderBy('dtrans_no','DESC')
            ->limit(1)
            ->first();
        //get contents to calculate debit preview details
        // $agmnf = Agmnf::where('branch', $polmaster[0]->branch)->where('agent', $polmaster[0]->agent_no)->get();
        $intermediaryParams = new IntermediaryQueryParams([
            'agentNo' => $polmaster[0]->agent_no,
            'branch' => $polmaster[0]->branch 
           // 'additionalFields'=>['intermediary_attribute.value'],
            // 'conditions' => function($query){
            //     return $query->where('intermediary_attribute.slug','commission-category');
            // }
        ]);
        $agmnf  =IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();

        $agmnf_attr = DB::table('intermediary_attribute')
                     ->select('intermediary_number', 'slug', 'value')
                     ->where('intermediary_number',  $agmnf->agent)
                     ->where('intermediary_attribute.slug','commission-category')
                     ->first();


       $agcom_check = Agcom::where('class', $polmaster[0]->class)->where('commission', $agmnf_attr->value)->count();

        switch($dcontrol_cls->trans_type){
            case 'POL':

            case 'REN':
            case 'RNS':
            case 'INS':
            case 'MAC':
                if($agcom_check > 0){
                            $agcom = Agcom::where('class', $polmaster[0]->class)->where('commission', $agmnf_attr->value)->get();
                            if($agcom[0]->percent == 0){
                                $agcom_categ = Agcom_categ::where('ag_type',$agmnf->value)->get();
                                $comm_rate = $agcom_categ[0]->default_rate;
                            }else{
                                $comm_rate = $agcom[0]->percent;
                            }
                }else{
                          $agcom_categ = Agcom_categ::where('ag_type',$agmnf_attr->value)->get();
                            $comm_rate = $agcom_categ[0]->default_rate;
                }
                
                break;
            default:
                // GET RATE FROM MOST RECENT POL/REN/RNS
                $comm_rate = $latest_rec->comm_rate;
                break;
        }

        // $tax_code = Tax_code::where('tax_code', $agmnf->tax_code)->get();
        $tax_code = $intemediaryTaxes->where('intermediarytaxgroupdtl.tax_type','WHT')->first();
        //stamp duty
        $class = ClassModel::where('class', $polmaster[0]->class)->get();

        $madtl = Madtl::where('endt_renewal_no', $endt_renewal_no)->get();

        //$check_marine = Madtl::where('endt_renewal_no',$endt_renewal_no)->get();

        $stamp_duty = $class[0]->stamp_duty;

        $check_travel = $class[0]->travel;


        if($dcontrol[0]->trans_type=='CNC'){
            $cnc_reason_code = Debitdtl::where('endt_renewal_no',$endt_renewal_no)->get()[0];
            $cnc_full_refund = Endorse_descr::where('descr_code',$cnc_reason_code->detail_code)->get()[0];
        }
        else if($dcontrol[0]->trans_type=='RNS'){
            $rns_reason_code = Debitdtl::where('endt_renewal_no',$dcontrol[0]->cnc_rfn_endt)->get()[0];
            $rns_full_refund = Endorse_descr::where('descr_code',$rns_reason_code->detail_code)->get()[0];
        }


        //PIPSTMP
        $pipstmp = Pipstmp::where('key','02')->get();
        /*** STAMP DUTY FOR NEW BUSINESS ***/

        if($dcontrol[0]->trans_type=='POL' || $dcontrol[0]->trans_type=='MAC'){
            // if($class[0]->marine=="Y" && $madtl[0]->mode_sea=='Y'){

            //     if($madtl[0]->sea_rate <= $pipstmp[0]->marine_sea_stamp_duty_cap){
            //         $stamp_duty = $pipstmp[0]->marine_sea_min_stamp_duty;

            //     }
            //     else if($madtl[0]->sea_rate > $pipstmp[0]->marine_sea_stamp_duty_cap){
            //         $stamp_duty = ($pipstmp[0]->marine_sea_stamp_duty * ($madtl[0]->sum_insured / $dcontrol[0]->currency_rate)) / 100;

            //     }
        
            // }
            if($pipcnam->calculate_per_fleet == 'Y' && $class[0]->motor_policy=='Y'){

                $stamp_duty = $active_modtl * ($stamp_duty  / $dcontrol[0]->currency_rate);
               // $sticker_fees = $active_modtl * ($class[0]->sticker_fees / $dcontrol[0]->currency_rate);
            }else{
                
                $stamp_duty = $class[0]->stamp_duty / $dcontrol[0]->currency_rate;
                //$sticker_fees = $class[0]->sticker_fees / $dcontrol[0]->currency_rate;
            }
        }
        else if($dcontrol[0]->trans_type=='CNC' || $dcontrol[0]->trans_type=='RFN'){
            $stamp_duty = 0;
            if($cnc_full_refund->refund_stamp_duty == 'Y'){
                $latest_rec_rfn = Debitmast::whereRaw("policy_no='".$dcontrol[0]->policy_no."' and (entry_type_descr='POL' or entry_type_descr='REN' or entry_type_descr='RNS')")->orderBy('dtrans_no','DESC')->limit(1)->get()[0];


                $dcontrol_dtrans = Dcontrol::where("endt_renewal_no",$latest_rec_rfn->endt_renewal_no)->get(['dtrans_no'])[0];

                $rfn_dcon_rec = Dcontrol::whereRaw("policy_no='".$dcontrol[0]->policy_no."' and to_number(dtrans_no)>=".(int)$dcontrol_dtrans->dtrans_no)->get();

                foreach ($rfn_dcon_rec as $rfn_dcon) {
                    $sd1 = Debitmast::where("endt_renewal_no",$rfn_dcon->endt_renewal_no)->sum('foreign_stamp_duty');
                    
                    $stamp_duty = $stamp_duty + (float)$sd1;
                }

                $stamp_duty = $stamp_duty * -1;
            }else{
                $stamp_duty = 0;
            }

        }
        else if($dcontrol[0]->trans_type=='RNS'){
            $get_sd = Debitmast::whereRaw("policy_no='".$dcontrol[0]->policy_no."' and entry_type_descr='CNC'")->orderBy('dtrans_no','DESC')->limit(1)->get()[0];

            $stamp_duty = $get_sd->foreign_stamp_duty * -1;

           /* if($rns_full_refund->full_refund == 'Y'){
                $get_sd = Debitmast::where('endt_renewal_no',$dcontrol[0]->cnc_rfn_endt)->get()[0];
                $stamp_duty = $get_sd->foreign_stamp_duty * -1;
            }else{
                $stamp_duty = 0;
            }*/
        }
        else if($dcontrol[0]->trans_type=='EXT'){
            
            if($pipcnam->charge_stamp_duty_on_ext == 'Y'){

                if($class[0]->marine=="Y" && $madtl[0]->mode_sea=='Y'){

                    if($madtl[0]->sea_rate <= $pipstmp[0]->marine_sea_stamp_duty_cap){
                        $stamp_duty = $pipstmp[0]->marine_sea_min_stamp_duty;

                    }
                    else if($madtl[0]->sea_rate > $pipstmp[0]->marine_sea_stamp_duty_cap){
                        $stamp_duty = ($pipstmp[0]->marine_sea_stamp_duty * ($madtl[0]->sum_insured / $dcontrol[0]->currency_rate)) / 100;

                    }
            
                }
                else if($pipcnam->calculate_per_fleet == 'Y' && $class[0]->motor_policy=='Y'){

                    $stamp_duty = $active_modtl * ($stamp_duty  / $dcontrol[0]->currency_rate);
                   // $sticker_fees = $active_modtl * ($class[0]->sticker_fees / $dcontrol[0]->currency_rate);
                }else{
                    
                    $stamp_duty = $class[0]->stamp_duty / $dcontrol[0]->currency_rate;
                    //$sticker_fees = $class[0]->sticker_fees / $dcontrol[0]->currency_rate;
                }
            }
            else{
                $stamp_duty = 0;
            }

        }
        else if($dcontrol[0]->trans_type=='REN' || $dcontrol[0]->trans_type=='INS' || $dcontrol[0]->trans_type=='CXT'){
            if($pipcnam->charge_stamp_duty_on_ren == 'Y'){

                if($class[0]->marine=="Y" && $madtl[0]->mode_sea=='Y'){

                    if($madtl[0]->sea_rate <= $pipstmp[0]->marine_sea_stamp_duty_cap){
                        $stamp_duty = $pipstmp[0]->marine_sea_min_stamp_duty;

                    }
                    else if($madtl[0]->sea_rate > $pipstmp[0]->marine_sea_stamp_duty_cap){
                        $stamp_duty = ($pipstmp[0]->marine_sea_stamp_duty * ($madtl[0]->sum_insured / $dcontrol[0]->currency_rate)) / 100;

                    }
            
                }
                else if($pipcnam->calculate_per_fleet == 'Y' && $class[0]->motor_policy=='Y'){

                    $stamp_duty = $active_modtl * ($stamp_duty  / $dcontrol[0]->currency_rate);
                   // $sticker_fees = $active_modtl * ($class[0]->sticker_fees / $dcontrol[0]->currency_rate);
                }else{
                    
                    $stamp_duty = $class[0]->stamp_duty / $dcontrol[0]->currency_rate;
                    //$sticker_fees = $class[0]->sticker_fees / $dcontrol[0]->currency_rate;
                }
            }
            else{
                $stamp_duty = 0;
            }

        }
        else{
            $stamp_duty = 0;
        }

        //stamp duty for binders and scheme policies
        if($dcontrol[0]->binder == 'Y' || $dcontrol[0]->binder == 'S'){
            $count_binder_pol = Dcontrol::where('binder_pol_no',$dcontrol[0]->binder_pol_no)->count();

            if($count_binder_pol > 1){
                $stamp_duty = 0;
            }
        }
        //end stamp duty for binders and scheme policies


        //Stamp duty for renewals from old system 
        if($dcontrol[0]->renew_old_policy == 'Y'){

            $stamp_duty = 0;

        }
        // End stamp duty for renewals from old system 
        
        /**** END STAMP DUTY CALCULAION *****/

        /**** STICKER FEES ******/
        if($dcontrol[0]->trans_type=='POL' || $dcontrol[0]->trans_type=='REN' || $dcontrol[0]->trans_type=='REN' || $dcontrol[0]->trans_type=='MAC'){
            if($pipcnam->calculate_per_fleet == 'Y' && $class[0]->motor_policy=='Y'){

                $sticker_fees = $active_modtl * ($class[0]->sticker_fees / $dcontrol[0]->currency_rate);
            }else{
                
                $sticker_fees = $class[0]->sticker_fees / $dcontrol[0]->currency_rate;
            }
        }else if($dcontrol[0]->trans_type == 'CNC' or $dcontrol[0]->trans_type == 'RFN'){

            if($cnc_reason_code->sticker_returned == 'Y'){
                $latest_rec = Debitmast::whereRaw("policy_no='".$dcontrol[0]->policy_no."' and (entry_type_descr='POL' or entry_type_descr='REN' or entry_type_descr='RNS')")->orderBy('dtrans_no','DESC')->limit(1)->get()[0];

                $dcontrol_dtrans = Dcontrol::where("endt_renewal_no",$latest_rec->endt_renewal_no)->get(['dtrans_no'])[0];

                $rfn_dcon_rec = Dcontrol::whereRaw("policy_no='".$dcontrol[0]->policy_no."' and to_number(dtrans_no)>=".(int)$dcontrol_dtrans->dtrans_no)->get();

                $sticker_fees = 0;
                
                foreach ($rfn_dcon_rec as $rfn_dcon) {
                    $rfn_sticker_fees = Debitmast::where("endt_renewal_no",$rfn_dcon->endt_renewal_no)->sum('foreign_sticker_amount');

                    $sticker_fees = $sticker_fees + $rfn_sticker_fees;

                }

                $sticker_fees = $sticker_fees * -1;
                
            }else{
                $sticker_fees = 0;
            } 


        }else if($dcontrol[0]->trans_type == 'RNS'){
            $get_sd = Debitmast::whereRaw("policy_no='".$dcontrol[0]->policy_no."' and entry_type_descr='CNC'")->orderBy('dtrans_no','DESC')->limit(1)->get()[0];

                $sticker_fees = $get_sd->foreign_sticker_amount * -1;
        }
        else if($dcontrol[0]->trans_type=='EXT' || $dcontrol[0]->trans_type=='CXT'){
            
            if($pipcnam->charge_sticker_fees_on_ext == 'Y'){
                if($pipcnam->calculate_per_fleet == 'Y' && $class[0]->motor_policy=='Y'){
                   $sticker_fees = $active_modtl * ($class[0]->sticker_fees / $dcontrol[0]->currency_rate);
                }else{
                    $sticker_fees = $class[0]->sticker_fees / $dcontrol[0]->currency_rate;
                }
            }
            else{
                $sticker_fees = 0;
            }

        }

        /**** END STICKER FEES *****/

      /*  if($dcontrol[0]->trans_type=='POL' || $dcontrol[0]->trans_type=='REN' || ($dcontrol[0]->trans_type=='CNC' && $cnc_full_refund->full_refund=='Y')){

            if($class[0]->marine=="Y" && $check_marine[0]->mode_sea=='Y'){

                $stamp_duty = (($pipstmp[0]->marine_sea_stamp_duty / $dcontrol[0]->currency_rate) * $check_marine[0]->sum_insured) / 100;
        
            }
            else if($pipcnam->calculate_per_fleet == 'Y' && $class[0]->motor_policy=='Y'){

                $stamp_duty = $active_modtl * ($stamp_duty  / $dcontrol[0]->currency_rate);
                $sticker_fees = $active_modtl * ($class[0]->sticker_fees / $dcontrol[0]->currency_rate);
            }else{
                
                $stamp_duty = $class[0]->stamp_duty / $dcontrol[0]->currency_rate;
                $sticker_fees = $class[0]->sticker_fees / $dcontrol[0]->currency_rate;
            }
            

        }else{
            //dd($cnc_full_refund->full_refund);
            if($dcontrol[0]->trans_type != 'CNC'){
                $stamp_duty = 0;
                $sticker_fees = 0;     
            }else{
                if($pipcnam->calculate_per_fleet == 'Y' && $class[0]->motor_policy=='Y'){
                    $sticker_fees = $active_modtl * ($class[0]->sticker_fees / $dcontrol[0]->currency_rate);
                }else{
                    $sticker_fees = $class[0]->sticker_fees / $dcontrol[0]->currency_rate;
                }

            }
           
        }

        if($dcontrol[0]->trans_type == 'CNC'){
            if($cnc_full_refund->full_refund == 'Y'){
                $stamp_duty = $stamp_duty * -1;
            }else{
                $stamp_duty = 0;
            }

            if($cnc_reason_code->sticker_returned == 'Y'){
                $sticker_fees = $sticker_fees * -1;
            }else{
                $sticker_fees = 0;
            }

            
        } */

        //get cancellation reasons
        $narration = Endorse_descr::orderBy('descr_code')->get();

        //training levy
        $pipstmp = Pipstmp::whereRaw("trim(key) = '02'")->get();
        $training = ($pipstmp[0]->levy_rate * $polmaster[0]->endorse_amount) / 100;
        $t_levy_rate = $pipstmp[0]->levy_rate;

        //policy fund
        $phcf = ($pipstmp[0]->policy_fund_rate * $polmaster[0]->endorse_amount) / 100;
        $phcf_rate = $pipstmp[0]->policy_fund_rate;

        //gross commission
        $gross_com = ($comm_rate * $polmaster[0]->endorse_amount) / 100; 

        //withholding tax
        $withholding = ($gross_com * $tax_code->tax_rate) / 100;
        $with_rate = $tax_code->tax_rate;

        //ira tax amount
        $ira_tax_rate = $dcontrol[0]->ira_rate;
        $fronting_rate = $dcontrol[0]->fronting_rate;

        if($dcontrol[0]->ira_tax_rate != '' || $dcontrol[0]->ira_tax_rate != 0){
            $ira_tax_amt = ($dcontrol[0]->ira_tax_rate * $polmaster[0]->endorse_amount) / 100;
        }else{
            $ira_tax_amt = 0;
        }

        $coins_service_fee = $pipstmp[0]->coins_service_fee;
        

        /*** VAT Calculation ***/
        $vat_rate = $dcontrol[0]->vat_rate;
        $vat_tax = ($polmaster[0]->endorse_amount + $training + $sticker_fees) * ($vat_rate / 100);
        $vat_desc = $dcontrol[0]->vat_type.' - '.$dcontrol[0]->vat_description;
        
        /*** End VAT Calculation ***/


        $follower = Bustype::where('type_of_bus',$dcontrol[0]->type_of_bus)->get();

  

        if($follower[0]->charge_stamp_duty != 'Y'){
            $stamp_duty = 0;
        }


        //miscellaneous fees
        $check_misc_fees = Polmisc_fees::where('endt_renewal_no',$dcontrol[0]->endt_renewal_no)->count();

        if($check_misc_fees > 0){
            $misc_fees = Polmisc_fees::where('endt_renewal_no',$dcontrol[0]->endt_renewal_no)->sum('fee_amt');
        }else{
            $misc_fees = 0;
        }        

       if ($dcontrol[0]->trans_type == 'POL' || $dcontrol[0]->trans_type == 'INS' || $dcontrol[0]->trans_type == 'REN' || ($dcontrol[0]->trans_type=='CNC' && $cnc_full_refund->full_refund=='Y') || $dcontrol[0]->trans_type == 'RNS') {

            if($follower[0]->facult_in == 'Y'){
                $phcf = 0;
                $sticker_fees = 0;
                $misc_fees = 0;
                $nett = ($polmaster[0]->endorse_amount);
                $training = 0;
                $stamp_duty = 0;
                $vat_tax = 0;
                $ira_tax_amt = 0;
                 $withholding = 0;

            }else if($follower[0]->leader == 'Y' && $dcontrol[0]->co_insure=='Y'){
                $nett = ($polmaster[0]->endorse_amount + $training + $phcf + $stamp_duty + $sticker_fees + $vat_tax + $misc_fees);

            }else if($follower[0]->leader != 'Y' && $dcontrol[0]->co_insure=='Y'){
                $nett = ($polmaster[0]->endorse_amount + $training + $phcf + $sticker_fees + $vat_tax + $ira_tax_amt + $misc_fees);

                $stamp_duty = 0;
            }else{
                $nett = ($polmaster[0]->endorse_amount + $training + $phcf + $stamp_duty + $sticker_fees + $vat_tax + $ira_tax_amt + $misc_fees);
            }

            
            
        } else {

            //net amount to be debitted
            if($follower[0]->facult_in == 'Y'){
                $phcf = 0;
                $sticker_fees = 0;
                $misc_fees = 0;
                $nett = ($polmaster[0]->endorse_amount);
                $training = 0;
                 $stamp_duty = 0;
                 $vat_tax = 0;
                 $ira_tax_amt = 0;
                 $withholding = 0;

            }else if($follower[0]->leader == 'Y'){
                $nett = ($polmaster[0]->endorse_amount + $training + $phcf + $stamp_duty + $sticker_fees + $vat_tax + $ira_tax_amt + $misc_fees);

            }else{
                $nett = ($polmaster[0]->endorse_amount + $training + $phcf + $sticker_fees + $vat_tax + $ira_tax_amt + $misc_fees);

            }

            //$training = 0;
            //$phcf = 0;
            //$stamp_duty = 0;
            // $training = 0;
            // $phcf = 0;
            // $stamp_duty = 0;
        }


        //number of debitmast records
        $debit_count = Debitmast::where('endt_renewal_no', $endt_renewal_no)->count();
        $polremast_count = Polremastend::where('endt_renewal_no', $endt_renewal_no)->count();
        
       $fully_reinsured = (new Reinsurance)->fully_reinsured($endt_renewal_no,$polmaster[0]->class);


        if ($polmaster[0]->endorse_amount < 0) {

            $document_type = 'CRN';
        } else {

            $document_type = 'DRN';
        }

        if($motor == "Y"){
            $pvt_cover = $polmaster[0]->pvt_cover;
            $comb_pvt_class = array();
            foreach($polremast as $key => $comb_pvt){
                $pvt_comb_class = ClassModel::where('class', $polremast[$key]->comb_class)->first();
                array_push($comb_pvt_class, $pvt_comb_class);
            }
        }

        //get certificate types
        $certtypes = Certtype::all(['type', 'description']);

        //variable to check payment of premiums
        $fully_paid = 0;

        //check if the endorsement has been paid for
        $receipted = Acdet::where('doc_type', 'REC')->where('endt_renewal_no', $endt_renewal_no)->count();

        if ($receipted > 0) {
            //check amount receipted
            $amount = Acdet::where('doc_type', 'REC')
                ->where('endt_renewal_no', $endt_renewal_no)->sum('nett');
            $fully_paid = $amount;
        }

        $vehicles = Modtlmast::where('policy_no', $polmaster[0]->policy_no)
                                ->where(function($query){
                                    $query->whereIn('stickernumber',[0])
                                        ->orWhereNull('stickernumber');
                                })
                                ->get();

        $comprehensiveVehicles = Modtlmast::where('policy_no', $polmaster[0]->policy_no)->where('covertype', '1')->whereNot('deleted', 'Y')->get();

        $no_of_vehicles_to_cert = Modtlpivot::where('policy_no', $polmaster[0]->policy_no)
                                            ->where('endt_renewal_no',$dcontrol[0]->endt_renewal_no)
                                            ->where('status','ACT')
                                            ->whereNotIn(DB::raw('TRIM(reg_no)'), Certalloc::where('policy_no',$polmaster[0]->policy_no)
                                            // ->whereNotIn(DB::raw('TRIM(reg_no)')->getValue(DB::connection()->getQueryGrammar()), Certalloc::where('policy_no',$polmaster[0]->policy_no)
                                                                    ->whereBetween('period_from', [$dcontrol[0]->period_from, $dcontrol[0]->period_to])
                                                                        ->pluck('reg_no')->map(function ($value) {
                                                                                                return trim($value);
                                                                                            })
                                            )
                                            ->get();



        $new_motor_proc_vehicles = Modtlpivot::where('policy_no', $polmaster[0]->policy_no)
                                            ->where('endt_renewal_no',$dcontrol[0]->endt_renewal_no)
                                            ->get();


        $currency = Currency::where('currency_code', $dcontrol[0]->currency)->get();
        $currency = $currency[0];
        $client_no=$dcontrol[0]->client_number;
        $agent=$dcontrol[0]->agent;
        $branch=$dcontrol[0]->branch;
        
    
        if(trim($pipcnam->clauses_flag) == 'C'){
            $clauses = Clauses::where(function ($query) use ($apply_basis, $class, $client_no,$agent,$branch) {
                $query->where(function ($query) use ($apply_basis, $class) {
                    $query->where('apply_basis', 'P')->where('class', $class[0]->class);
                })->orWhere(function ($query) use ($apply_basis, $client_no,$class) {
                    $query->where('apply_basis', 'C')->where('client_no', $client_no)->where('class', $class[0]->class);
               
                })->orWhere(function ($query) use ($apply_basis, $client_no,$class,$agent,$branch) {
                    $query->where('apply_basis', 'I')->where('agent', $agent)->where('branch', $branch)->where('class', $class[0]->class);
               
                });
            })->get();

            $limits = Autolimits::where(function ($query) use ($apply_basis, $class, $client_no,$agent,$branch) {
                $query->where(function ($query) use ($apply_basis, $class) {
                    $query->where('apply_basis', 'P')->where('class', $class[0]->class);
                })->orWhere(function ($query) use ($apply_basis, $client_no,$class) {
                    $query->where('apply_basis', 'C')->where('client_no', $client_no)->where('class', $class[0]->class);
               
                })->orWhere(function ($query) use ($apply_basis, $client_no,$class,$agent,$branch) {
                    $query->where('apply_basis', 'I')->where('agent', $agent)->where('branch', $branch)->where('class', $class[0]->class);
               
                });
            })->get();
           // dd($limits);
        }
        else{
            $clauses = Clauses::where(function ($query) use ($apply_basis, $class, $client_no,$agent,$branch) {
                $query->where(function ($query) use ($apply_basis, $class) {
                    $query->where('apply_basis', 'P')->where('dept', $class[0]->dept);
                })->orWhere(function ($query) use ($apply_basis, $client_no,$class) {
                    $query->where('apply_basis', 'C')->where('client_no', $client_no)->where('class', $class[0]->class);
               
                })->orWhere(function ($query) use ($apply_basis, $agent,$branch,$class) {
                    $query->where('apply_basis', 'I')->where('agent', $agent)->where('branch', $branch)->where('class', $class[0]->class);
               
                });
            })->get();
            $limits = Autolimits::where(function ($query) use ($apply_basis, $class, $client_no) {
                $query->where(function ($query) use ($apply_basis, $class) {
                    $query->where('apply_basis', 'P')->where('dept', $class[0]->dept);
                })->orWhere(function ($query) use ($apply_basis, $client_no,$class) {
                    $query->where('apply_basis', 'C')->where('client_no', $client_no)->where('class', $class[0]->class);
               
                })->orWhere(function ($query) use ($apply_basis, $client_no,$class) {
                    $query->where('apply_basis', 'I')->where('agent', $agent)->where('branch', $dcontrol[0]->branch)->where('class', $class[0]->class);
               
                });
            })->get();
        }
        

        $clsexcess = Classexces::where('class', $class[0]->class)->get();

        $set_limits = Pollimits::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->get(['limit_no']);
        $set_excess = Polexcess::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->get(['item_no']);
        // $set_excess = Polexcess::where('policy_no', $dcontrol[0]->policy_no)->get(['item_no']);
        $set_clauses = Polclause::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->get(['clause']);

        $all_set_limits = array();
        $all_set_excess = array();
        $all_set_clauses = array();
        foreach($set_limits as $key => $set_limit){
            $set_lmt = $set_limit->limit_no;
            array_push($all_set_limits, $set_lmt);
        }
        $all_set_limits = json_encode($all_set_limits);

        foreach($set_excess as $key => $set_exces){
            $set_exes = $set_exces->item_no;
            array_push($all_set_excess, $set_exes);
        }
        $all_set_excess = json_encode($all_set_excess);
        
        foreach($set_clauses as $key => $set_clause){
            $set_claus = $set_clause->clause;
            array_push($all_set_clauses, $set_claus);
        }
        $all_set_clauses = json_encode($all_set_clauses);

        $xcov = $new_motor_proc_vehicles[0]->cover_type;
        
        // if($motor == "Y"){
        //     // $limits = Autolimits::where('dept', $class[0]->dept)->where('class', $class[0]->class)->where('classtype', $xcov)->get();
        //     $limits = Autolimits::where('dept', $class[0]->dept)->where('class', $class[0]->class)->get();
        // }else{
        //     $limits = Autolimits::where('dept', $class[0]->dept)->where('class', $class[0]->class)->get();

        // }
        //$limits = Autolimits::where('dept', $class[0]->dept)->where('class', $class[0]->class)->get();
        
        ///certificates
        switch ($class[0]->motor_policy) {
            case 'Y':

                $brnch = substr($dcontrol[0]->endt_renewal_no, 0, 3);
                $brnch = (int) $brnch;
                //GET USER STATUS
                // $cert_status = Auth::user()->user_certificate;
                $user_name = Auth::user()->user_name;
                $user_name = trim($user_name);
        
                
                $cert_status = "N";

                if(Gate::check('issue-motor-certificate')){
                    $cert_status = "Y";
                }			
                // }
                //CHECK USER STATUS
                if ($cert_status == 'Y') {
                    $certificates = $this->fetch_certificates($class[0], $dcontrol[0], $brnch);
                }

                $serials = Cert_serials_unallocated::whereRaw("trim(cert_type)='" . $class[0]->cert_type . "'")->where('branch', $brnch)
                                                    ->take(30)->get(['cert_no', 'branch', 'agent', 'cert_type']);
                break;

            default:

                $serials = array('type' => 'Z', 'description' => 'invalid', 'cert_no' => '0');

                break;
        }

       // $Crmast = Crmast::where('branch', 95)
            //   ->where('agent',$dcontrol[0]->agent)
        //    ->get();
        $Crmast = Crmast::all();

        /////////////////////////////add transaction to workflow mailbox///////////
        $policy = new Policy;
        $next_code = $policy->add_to_workflow($dcontrol[0]->endt_renewal_no, workflow_id(), $process_id);

        ///////////get user profile and determine if they are allowed to debit/credit
        $user = Auth::user();
        $userprofile = Userprofile::where('user_id', $user->user_id)->get();
        $userprofile = $userprofile[0];
        $debit_limit = $userprofile->debit_limit;
        $credit_limit = $userprofile->credit_limit;

        switch ($dcontrol[0]->trans_type) {
            case 'POL':
            case 'EXT':
            case 'REN':
            case 'INS':
            case 'CXT':
            case 'RNS':
            case 'MAC':
                $debit_amount = $polmaster[0]->sum_insured;
                $credit_amount = 0;
                break;

            default:
                $credit_amount = $polmaster[0]->sum_insured;
                $debit_amount = 0;
                break;
        }

        if ($debit_amount > $debit_limit || $credit_amount > $credit_limit) {

            $allow_debit_credit = 'N';
        } else {

            $allow_debit_credit = 'Y';
        }

        ////check if maker submitted to checker
        $maker_submission = Makersubmissions::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->count();
        if ($maker_submission > 0) {

            $submitted_to_checker = 'Y';
            $checker_id = Makersubmissions::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)
                ->get();


            $checker_user_id = $checker_id[0]->checker_user_id;

            $aimsuser = Aimsuser::where('user_id', $checker_user_id)->get();
            $aimsuser = $aimsuser[0];
            $checker_names = $aimsuser->name . ' ' . $aimsuser->last_name;
        } else {

            $submitted_to_checker = 'N';
            $checker_names = '';
        }

        ##check if existence of discount/loading
        $checkdiscount = Discounts_loadings::where('endt_renewal_no',$dcontrol[0]->endt_renewal_no)
                                            ->where('type','D')
                                            ->where('deleted','<>','Y')
                                            ->count();

        $checkers = Userprofile::where('complex', 'Y')->get();

        $coinpart_share = $coinpart = Coinpart::where('policy_no', $polmaster[0]->policy_no)->sum('reice');
        $coinpart = Coinpart::where('policy_no', $polmaster[0]->policy_no)->get();

        $btyp = Bustype::where('type_of_bus',$dcontrol[0]->type_of_bus)->get();

        $btyp = $btyp[0];
        $class_year = Classyear::where('class', $polmaster[0]->class)
            ->where('uw_year', $polmaster[0]->uw_year)
            ->get();

        $reins = Reinsetup::where('class',$class_year[0]->reinclass)->where('uw_year',$polmaster[0]->uw_year)->count();

        if($dcontrol[0]->trans_type == 'NIL'){
            $nil_type = Nil_endorsetypes::where('endt_type',$dcontrol[0]->nil_code)->get();
            $nil_descr = $nil_type[0]->endt_descr;

            $nil_audit = Risk_audit_trail::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$dcontrol[0]->endt_renewal_no)->get();
            
        }else{
            $nil_descr = '';
            $nil_audit = array(0,0);
        }

        $cls_yearx = Classyear::where('class', $polmaster[0]->class)
            ->where('uw_year', $polmaster[0]->uw_year)
            ->get(['reinclass', 'treaty_type']);

        $reinsetup = Reinsetup::where('class',$cls_yearx[0]->reinclass)
                            ->where('uw_year',$polmaster[0]->uw_year)
                            ->get();


        $polcmb =  DB::table('polcmb')->select('polcmb.class', 'polcmb.sum_insured', 'polcmb.premium', 'class.description')
                        ->join('class', 'class.class', '=', 'polcmb.class')->where(['policy_no' => $polmaster[0]->policy_no])->where(['endt_renewal_no' => $dcontrol[0]->endt_renewal_no])->get();

        $polcmb_checked =  DB::table('polcmb')->select('polcmb.class', 'polcmb.sum_insured', 'polcmb.premium', 'polcmb.endorse_amount', 'class.description','class.consolidate')
                                        ->join('class', 'class.class', '=', 'polcmb.class')
                                        ->where(['polcmb.policy_no' => $polmaster[0]->policy_no])
                                        ->where('polcmb.endt_renewal_no', $dcontrol[0]->endt_renewal_no)
                                        ->where('polcmb.endorse_amount','<>',0)
                                        ->get();
        
        if($class[0]->motor_policy == 'Y'){
            if($dcontrol[0]->trans_type == 'PTA'){
                $misc_fees_params = Misc_fees_param::where('motor_fee','Y')->where('comesa_flag','Y')->get();
            }
            else{
                $misc_fees_params = Misc_fees_param::where('motor_fee','Y')->get();
            }
            
        }else{
            $misc_fees_params = Misc_fees_param::where('motor_fee','N')->get();
        }

        // Ensure narration is mandatory 
        $types = Pipcnam::first()->narration_endt_required;
        $allowed_types = explode(',',$types);

        $narration = Debitdtl::where('endt_renewal_no',$endt_renewal_no)
                                ->count();

        $trans_type = Dcontrol::where('endt_renewal_no',$endt_renewal_no)
                             ->first()->trans_type;

        if (in_array($trans_type,$allowed_types) && $narration < 1) {
            $req_narration ='Please Add Narration for this transaction.';
        }

        // facultative inward
        if($follower[0]->facult_in == 'Y'){
            $comm_rate = $dcontrol[0]->facin_comm_rate;
            $gross_com = $dcontrol[0]->facin_comm_amt; 
        }
        
        if($follower[0]->global_account == 'Y'){
            $comm_rate = $dcontrol[0]->global_account_comm_rate;
            $gross_com = ($comm_rate * $polmaster[0]->endorse_amount) / 100;
        }

        $user = Aimsuser_web::whereRaw("trim(user_name) = '".trim($dcontrol[0]->user_str)."'")->first();
        $branch = str_pad(Auth::user()->branch, 3,'0',STR_PAD_LEFT);

        $today = Carbon::today();
        $office = Nlparams::where('prid','OFF')
                        ->whereRaw("trim(prsno)='".$branch."'")
                        ->get();
               
        $req_details=Payreqst::where('policy_no',$dcontrol[0]->policy_no)
            ->where('claim_no',$dcontrol[0]->endt_renewal_no)
        ->get();


        $users=Aimsuser_web::where('user_name','!=',Auth::user()->user_name)->get();
        $u_id  = Auth::user()->user_id;
        $aimsusers = DB::select("select * from aimsusers where (aims_group = 'GRP004' or aims_group = 'GRP003') and left_company <> 'Y' and user_id <> '$u_id' ");
        $aimsusers_reqs = DB::select("select * from aimsusers a join aimsuprofgb b on a.user_name = b.aims_user where b.approve_rfn_req = 'Y' and a.left_company <> 'Y' and a.user_id <> '$u_id' ");
        $aimsusers_credits = DB::select("select * from aimsusers where left_company <> 'Y' and user_id <> '$u_id' AND user_name IN (SELECT aims_user FROM aimsuprof WHERE approve_credit = 'Y')");

        $approve_surplus_referral = Permission::where('slug','approve-surplus-referral')->first();
        $surplus_ref_approvers = $approve_surplus_referral->users;
        $reinsurance_approval = Escalate_pol::where('type','R/I')
            ->where('endorse_no',$dcontrol[0]->endt_renewal_no)
            ->whereNull('re_escalate_date')
            ->join('aimsusers','aimsusers.user_id','=','escalate_pol.sent_to')
            ->orderBy('escalate_pol.created_at','DESC')
            ->first(['escalate_pol.*','aimsusers.name']);
        $reinController = new Reinsurance;
        $surplus_referral_limit = $reinController->surplus_referral_limit($dcontrol[0]->class,$endt_renewal_no);
        $ri_cons = Reinscons::where('endt_renewal_no',$dcontrol[0]->endt_renewal_no)->first();
        
//        $users=Aimsuser_web::all();
        //GET ACDET DETAILS
        // $credit_note=Acdet

        
        if($debited=='Y'){

            $credit_note= Acdet::where('endt_renewal_no',$dcontrol[0]->endt_renewal_no)
            ->where('source','U/W')
            ->where('unallocated','<',0)
            ->where('doc_type','CRN')
            ->get()[0];


            // return $dcontrol[0]->endt_renewal_no;

            // return $credit_note;
         
            $unallocated=$credit_note->unallocated;
            
            //get Raised Requisitions

            $sum_of_raised_requisitions=Payreqst::where('policy_no','=',$dcontrol[0]->policy_no)
                                                    ->where('claim_no',$dcontrol[0]->endt_renewal_no)
                                                    ->whereRaw("trim(cancelled)!='Y'")
                                                        ->where('CANCELLED_BY','!=',null)
                                                    ->get()->sum('amount');
             
                   //return $sum_of_raised_requisitions;            
                    
            
        }
        if($unallocated<0){
            $unallocated=abs($unallocated);
        }
        
        if($class[0]->burglary == 'Y'){
            $polsect_floss_sum = Polsect::where('endt_renewal_no', $endt_renewal_no)->sum('total_first_loss_sum');
        }
        else{
            $polsect_floss_sum = 0;
        }

        $tradata = Tradata::where('source','U/W')
            ->where('endt_renewal_no', $endt_renewal_no)
            ->first();


        $polmaster_cnc = Polmaster::where("policy_no",$dcontrol[0]->policy_no)
                                    ->select('status_code')->get();

        $motor_certs = DB::select("SELECT
                MOTOR_CERT_SETUP.CLASS,
                MOTOR_CERT_SETUP.CERT_TYPE,
                CERTTYPE.TYPE,
                CERTTYPE.DESCRIPTION
            FROM
                MOTOR_CERT_SETUP
                INNER JOIN CERTTYPE ON TRIM(MOTOR_CERT_SETUP.CERT_TYPE) = TRIM(CERTTYPE.TYPE)
            WHERE
                MOTOR_CERT_SETUP.CLASS ='".$dcontrol[0]->class."'
        AND MOTOR_CERT_SETUP.TYPE_ACTIVE = 'Y'");
        $disclaimer = Poldisclaimer::where('endt_renewal_no', $endt_renewal_no)->first();
        $exclusions = Polexclusion::where('endt_renewal_no', $endt_renewal_no)->first();
        $warranty = Polwarranty::where('endt_renewal_no', $endt_renewal_no)->first();
        $scope = Polscope::where('endt_renewal_no', $endt_renewal_no)->first();
        $conditions = Polcondition::where('endt_renewal_no', $endt_renewal_no)->first();
        $coverage = Polcoverage::where('endt_renewal_no', $endt_renewal_no)->first();
        $cncapprovals=DB::table('efris_approval_records')
                        ->where('endt_renewal_no', $endt_renewal_no)
                        ->get();

        $UserGroupLimitCheckerService = new UserGroupLimitCheckerService();

        $nil_approvers = $UserGroupLimitCheckerService
                ->setPermission('approve-nil-endorsement')
                ->fetchUsersWithPermission();

        $special_cert_approvers = $UserGroupLimitCheckerService
                ->setPermission('approve-special-certs')
                ->fetchUsersWithPermission();

        if ($class[0]->medical == 'Y') {
            $medical_obje = new Medicalrisk;                           
            $med_risk = $medical_obje->checkFullMedicalSchedule($endt_renewal_no);
            if (!$med_risk) {
                $med_risk = 'N';
            }else{
                $med_risk = 'Y';  
            } 
        }

        $disable_reins_marine_utilization='N';
        $Marinemasterhist = Marinemasterhist::where('endt_renewal_no', $dcontrol[0]->master_endt_no)->first();
        $marine_cert_count = Madtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->count();
        $marine_master_count = Marinemasterhist::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->count();
        $marineutldebitmast = MarineUTLDebitmast::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->get();
        $marineutldebit_count = MarineUTLDebitmast::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->count();

        if($class[0]->open_cover=='Y'  && $marine_cert_count > 0 && $marine_master_count == 0 && $Marinemasterhist->opentype_code=='UTL'){
            $disable_reins_marine_utilization='Y'; 
            $fully_reinsured='Y';
        }
        if($class[0]->open_cover=='Y' && $marine_cert_count == 0 && $marine_master_count > 0 && $Marinemasterhist->opentype_code=='ZPR'){
            $fully_reinsured='Y';
        }
        $commit_marine_master = (($Marinemasterhist->opentype_code == 'CMC' || ($Marinemasterhist->opentype_code == 'ZPR' && !in_array($Marinemasterhist->trans_type,['POL','REN'])) ) && $marine_master_count > 0) ? 'Y' : 'N';

        $creditsetup = CreditSetup::where('record_type',0)->first();
        // dd('reinsured'.$class[0]->open_cover.'-'.$dcontrol[0]->trans_type.'-'.$Marinemasterhist->opentype_code.'-'.$dcontrol[0]->master_endt_no);

        $debited = ($commit_marine_master == 'Y' && $dcontrol[0]->committed == 'Y') ? 'Y' : $debited;

        $narrationTypes = NarrationType::orderBy('id', 'asc')->where('status', 'active')->get(['id','type']);
        $pay_plan_amount = PremiumPaymentPlan::where('endt_renewal_no', $endt_renewal_no)->sum('amount_due');
        $uw_parameters = UwParameter::first();
        $misc_fees = Polmisc_fees::where(['endt_renewal_no'=>$endt_renewal_no])->get();

        
        
        return view('gb.underwriting.policy_functions', [
            'sum_of_raised_requisitions'=>$sum_of_raised_requisitions,
            'unallocated'=>$unallocated,
            'debitmast_rec'=> $debitmast_rec,
            'req_details'=>$req_details,
            'users'=>$users,
            'office'=>$office,
            'req_narration' => $req_narration,
            'user' => $user,
            'pipcnam' => $pipcnam,
            'debited' => $debited,
            'count_reins'=>$reins,
            'reinsetup'=>$reinsetup[0],
            'policy_no' => $polmaster[0]->policy_no,
            'dcontrol' => $dcontrol[0],
            'polmaster' => $polmaster,
            'debitmast' => $debitmast,
            'marineutldebitmast' => $marineutldebitmast,
            'debit_count' => $debit_count,
            'marineutldebit_count' => $marineutldebit_count,
            'prevRemast' => $prevRemast,
            'polremast' => $polremast,
            'polremastend' => $polremastend,
            'polremast_count' => $polremast_count,
            'gross_comm' => $gross_com,
            'comm_rate' => $comm_rate,
            'with_tax' => $withholding,
            'with_rate' => $with_rate,
            'stamp_duty' => round($stamp_duty,2),
            'sticker_fees' => $sticker_fees,
            't_levy' => $training,
            't_levy_rate' => $t_levy_rate,
            'class' => $class,
            'comb_pvt_class' => $comb_pvt_class,
            'pvt_cover' => $pvt_cover,
            'phcf' => $phcf,
            'phcf_rate' => $phcf_rate,
            'vat_tax' => $vat_tax,
            'vat_rate' => $vat_rate,
            'vat_desc' => $vat_desc,
            'creditsetup' => $creditsetup,
            'charge_vat' => $pipcnam->charge_vat,
            'country_descr' => $pipcnam->country_descr,
            'nett' => $nett,
            'doc_type' => $document_type,
            'cert_types' => $certtypes,
            'receipted_prem' => $fully_paid,
            'vehicle' => $vehicles,
            'currency' => $currency,
            'motor' => $motor,
            'certificates' => $certificates,
            'serials' => $serials,
            'agmnf' => $agmnf,
            'modtl_endorsed' => $no_of_vehicles_to_cert,
            'new_motor_proc_vehicles'=>$new_motor_proc_vehicles,
            'previous_pid' => $process_id,
            'clauses' => $clauses,
            'limits' => $limits,
            'aimsusers' => $aimsusers, 
            'aimsusers_reqs' => $aimsusers_reqs, 
            'aimsusers_credits' => $aimsusers_credits, 
            'clsexcess' => $clsexcess,
            'xcov' => $xcov,
            'certalloc' => $certalloc,
            'narration' => $narration,
            'crmast' => $Crmast,
            //'polall' => $polall,
            'cert_status' => $cert_status,
            'fully_reinsured' => $fully_reinsured,
            'allow_debit_credit' => $allow_debit_credit,
            'checkers' => $checkers,
            'submitted_to_checker' => $submitted_to_checker,
            'checker_names' => $checker_names,
            'coinpart' => $coinpart,
            'coinpart_share' => $coinpart_share,
            'btyp' => $btyp,
            'debbit' => $debbit,
            'check_clause' => $check_clause,
            'dcontrol_doc' => $dcontrol_doc,
            'aimsuser_web' => $aimsuser_web,
            // 'getpolmast_amount' => $getpolmast_amount,
            'reinsurance'=>$reinsurance,
            'printpolicydoc'=>$printpolicydoc,
            'ira_tax_rate'=>$ira_tax_rate,
            'ira_tax_amt'=>$ira_tax_amt,
            'fronting_rate'=>$fronting_rate,
            'nil_descr'=>$nil_descr,
            'nil_audit'=>$nil_audit,
            'polcmb'=>$polcmb,
            'polcmb_checked'=>$polcmb_checked,
            'check_comb'=>$class[0],
            'limit_dept'=>$limit_dept,
            'limitgroup'=>$limitgroup,
            'all_set_limits'=>$all_set_limits,
            'all_set_excess'=>$all_set_excess,
            'all_set_clauses'=>$all_set_clauses,
            'misc_fees_params'=>$misc_fees_params,
            'check_misc_fees'=>$check_misc_fees,
            'endt_sum_insured'=>$endt_sum_insured,
            'endt_endorse_amount'=>$endt_endorse_amount,
            'misc_fees'=>$misc_fees,
            'check_travel'=>$check_travel,
            'doc_route' => $clean_doc_route,
            'pol_sched_route' => $clean_polsched_route,
            'bustype' => $follower[0],
            'noOfComesaCars' => $noOfComesaCars,
            'polsect_floss_sum' => $polsect_floss_sum,
            'checkdiscount' => $checkdiscount,
            'getpercentlossratio' => $getpercentlossratio,
            'lor' => $lor,
            'checkexist_escalation' => $checkexist_escalation,
            'checkexist_nil_escalation'=>$checkexist_nil_escalation,
            'checkexistcredit' => $checkexistcredit,
            'coins_service_fee'=>$coins_service_fee, //coins_service_fee
            'tradata' => $tradata,
            'surplus_referral' => $surplus_referral_limit,
            'surplus_ref_approvers' => $surplus_ref_approvers,
            'reinsurance_approval' => $reinsurance_approval,
            'motor_certs' => $motor_certs,
            'is_combined_policy' => $class[0]->combined,
            'attachments_base_class' => $dcontrol[0]->class,
            'nil_type'=>$nil_type[0],
            'ri_cons' => $ri_cons,
            'polmaster_cnc'=>$polmaster_cnc[0],
            'disclaimer' => $disclaimer,
            'exclusions' => $exclusions,
            'conditions' => $conditions,
            'scope' => $scope,
            'coverage' => $coverage,
            'warranty' => $warranty,
            'cncapprovals'=>$cncapprovals,
            'discountparams'=>$discountparams,
            'nil_approvers'=>$nil_approvers,
            'disable_reins_marine_utilization'=>$disable_reins_marine_utilization,
            'certalloc_cls'=>$certalloc_cls, 
            'checkdmc_escalation'=>$checkdmc_escalation,
            'special_cert_approvers'=>$special_cert_approvers,
            'commit_marine_master' => $commit_marine_master,
            'narrationTypes' => $narrationTypes,
            'pay_plan_amount' => $pay_plan_amount,
            'uw_parameters' => $uw_parameters,
            'comprehensiveVehicles' => $comprehensiveVehicles,
            'print_proforma_sched'=>$print_proforma_sched,
            'misc_fees'=> $misc_fees,

        ]);

    }

    public function banksBankbranches(Request $request)
    {
        // return 'dfgfd';
        $banks=Olbnknames::all();
        $bank_branches = Olbranch::all();
        return [
            'banks' => $banks,
            'bank_branches' => $bank_branches
        ];
    }


    public function getOutstandingPrem($endt_no)
	{
		$acdet = Acdet::select('nett','allocated','unallocated')
						->where('doc_type','DRN')
						->where('endt_renewal_no',$endt_no)
						->first();
		return $acdet;
	}

    public function fetchCredits($endt_no)
	{
		return Cbcredit::select('cancelled','manager')
							->where('claim_no',$endt_no)
							->where('entry_type_descr','PRM')
							->orderBy('created_time','DESC')
							->first();
	}

    public function checkCashAndCarry(Request $request){

        $dcon = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->first();
        $cls = ClassModel::where('class', $dcon->class)->first();

        $debited = Debitmast::where('endt_renewal_no', $request->endt_renewal_no)->count();
        $marine_count = Marinemasterhist::where('endt_renewal_no', $request->endt_renewal_no)->count();

        if($debited == 0 && $marine_count > 0 && $cls->open_cover == 'Y'){
            $master = Marinemasterhist::where('endt_renewal_no', $request->endt_renewal_no)->first();

            $debited = ($master->commit_transaction == 'Y') ? 1 : $debited ;
        }

        $pipcnam_rec = Pipcnam::select('cash_and_carry')->first();

        $credits = $this->fetchCredits($request->endt_renewal_no);

        $acdet_rec = $this->getOutstandingPrem($request->endt_renewal_no);

        if($debited == 0)
        {
            return [
                'status' => 0,
                'message' => 'Due to cash and carry policy, you are not allowed to view this document because the policy is not debited'
            ];
        }

        if($acdet_rec->unallocated !=  0  

            && $pipcnam_rec->cash_and_carry == 'Y'
            && ($credits != null
                && $credits->manager == null 
                && $credits->cancelled !='Y')
            )

        {
        return [
            'status' => 0,
            'message' => 'Due to cash and carry policy, you are not allowed to view this document because the premium is not fully paid. There is a pending Credit request....Consult with the Manager For Approval '
        ];
       }

       if ($acdet_rec->unallocated !=  0 && $credits != null && $credits->cancelled =='Y'){
        return [
            'status' => 0,
            'message' => 'Due to cash and carry policy, you are not allowed to view this document because the premium is not fully paid!. There is a cancelled Credit request....Consult with the Manager For Explanation'
        ];
       }


        if($acdet_rec->unallocated !=  0
            && $credits == null
            && $pipcnam_rec->cash_and_carry == 'Y' 
            && $debited == 1
            )
        {
            return [
                'status' => 0,
                'message' => 'Due to cash and carry policy, you are not allowed to view this document because the premium is not fully paid!'
            ];
        }
        
        return [
            'status' => 1,
        ];

    }
    
    public function getusergroup(Request $request)
    {
        $schem = schemaName();
        $username = trim(Auth::user()->user_name);
        $aimsuser_web = Aimsuser_web::whereRaw("trim(user_name)='" . $username . "'")->get()[0];

        $sumin = (int)$request->get('sumin');
        $department = $request->get('department');

      
     
        
        if($sumin >= 1 ){
      
            $grouplimit = DB::select( "select * from AIMSGROUPLIMITS where D_LIMIT >= '$sumin' and ROLE_TYPE = 'U' and DEPT = '$department' or (ROLE_TYPE = 'E' and dept = '$department' and d_limit >= '$sumin')ORDER BY d_limit" ) ;
        }  else{
            $grouplimit = DB::select( "select * from AIMSGROUPLIMITS where C_LIMIT <= '$sumin' and ROLE_TYPE = 'U' and DEPT = '$department' or (ROLE_TYPE = 'E' and dept = '$department' and c_limit <= '$sumin')ORDER BY c_limit" ) ;
  
        } 

        // echo json_encode($grouplimit);          
        echo json_encode($grouplimit[0]);          
    }

    public function getusernames(Request $request)
    {
    
        $usergrp = trim(Auth::user()->aims_group);
        $userid = trim(Auth::user()->user_id);

        $sumin = (int)$request->get('sumin');
        $department = $request->get('department');
        $checkescalation = Aimsgrouplimits::where('group_id',$usergrp )->first();

        if($sumin >= 1 ){

            // if($checkescalation->escalateto_ceo == 'Y'){
                $limits = DB::select( "select a.user_id,a.name from aimsgrouplimits b join aimsusers a 
                on a.aims_group = b.group_id where b.d_limit >='$sumin' and b.dept = '$department' and (b.role_type = 'E'or b.role_type = 'U')
                and a.left_company <> 'Y' and a.user_id <> '$userid' ");
            
            // }else{
                
            //     $limits = DB::select( DB::raw("select a.user_id,a.name from aimsgrouplimits b 
            //     join aimsuser_web a on a.aims_group = b.group_id where b.d_limit >='$sumin' and b.dept = '$department'
            //      and b.role_type = 'U' and a.left_company <> 'Y'  and a.user_id <> '$userid' "));
           
            // }
        
            
        }  else{

            $dcontrol1 = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->first();

            if ($dcontrol1->doc_type == 'DRN') {
                # Escalation for debits with 0 sum insured
                if($checkescalation->escalateto_ceo == 'Y'){
                    $limits = DB::select( "select a.user_id,a.name from aimsgrouplimits b join aimsusers a on a.aims_group = b.group_id where b.d_limit >='$sumin' and b.dept = '$department' and (b.role_type = 'E'or b.role_type = 'U') and a.left_company <> 'Y'  and a.user_id <> '$userid' ");
                }else{
                    $limits = DB::select( "select a.user_id,a.name from aimsgrouplimits b join aimsusers a on a.aims_group = b.group_id where b.d_limit >='$sumin' and b.dept = '$department' and b.role_type = 'U' and a.left_company <> 'Y'  and a.user_id <> '$userid' ");
                }
            } else {
                #negate sum in
                # Escalation for credit only
                if($checkescalation->escalateto_ceo == 'Y'){
                    $limits = DB::select( "select a.user_id,a.name from aimsgrouplimits b join aimsusers a on a.aims_group = b.group_id where Abs(b.c_limit) >='$sumin' and b.dept = '$department' and b.role_type = 'E' and a.left_company <> 'Y'  and a.user_id <> '$userid' ");
                }else{
                    $limits = DB::select( "select a.user_id,a.name from aimsgrouplimits b join aimsusers a on a.aims_group = b.group_id where Abs(b.c_limit) >='$sumin' and b.dept = '$department' and  b.role_type = 'U' and a.left_company <> 'Y'  and a.user_id <> '$userid' ");
                }
            }
        } 
       // dd($checkescalation->escalateto_ceo,$sumin,$department,$usergrp,$limits);
        echo json_encode($limits);
    }


    public function confirmedDcontrolclauses(Request $request)
    {
        $clause_on_pipcnam = Pipcnam::where('record_type', 0)->first();
        $check_travel = $request->get('travel_check');

        $dcontrol = Dcontrol::where('endt_renewal_no', trim($request->endt_no))->first();
        $bustype = Bustype::where('type_of_bus',$dcontrol->type_of_bus)->first();

        if($bustype->facult_in == 'Y'){
            return 'Y';
        }
        if($check_travel == 'Y'){
            return 'Y';
        }
        else if($clause_on_pipcnam->confirm_clauses_b4_debit == 'N'){
            return 'Y';
        }
        
        else if ($dcontrol->confirm_clauses == 'Y') {
            
            return 'Y';
        }else{
            return 'N';    
        }
        
    }

    public function checkPolclauses(Request $request)
    {
        $endt_no = $request->endt_no;
        $dcontrol_cls = Dcontrol::where('endt_renewal_no', $endt_no)->get();
        $dcontrol_cls = $dcontrol_cls[0];
        $clauses =  Polclause::where('endt_renewal_no', $endt_no)->get();
        return $clauses;
    }

    public function confirmPolclauses(Request $request)
    {

        $dcontrol = Dcontrol::where('endt_renewal_no', trim($request->endt_no))->update([
            'confirm_clauses' => 'Y'
        ]);

        if ($dcontrol) {
            return 'confirmed';
        }

        return 'not_confirmed';
    }

    

    public function updatePolconditions(Request $request)
    {
        $exists = Polcondition::where('endt_renewal_no', trim($request->endt_no))->exists();
        if ($exists) {
            if($request->condition == null || $request->condition == ''){

                $delete = Polcondition::where('endt_renewal_no', trim($request->endt_no))->delete();

            }else{
                $update = Polcondition::where('endt_renewal_no', trim($request->endt_no))->update([
                    'condition' => $request->condition,
                    'updated_by' => Auth::user()->user_name
                ]);

            }

        } else {
            $dcontrol = Dcontrol::where('endt_renewal_no', trim($request->endt_no))->first();
            if (is_null($dcontrol)) {
                $dcontrol = Marinemasterpol::where('endt_renewal_no', $request->endt_no)->first();
            }
            $class = Classmodel::where('class', $dcontrol->class)->first();
            $update = Polcondition::create([
                'policy_no' => $dcontrol->policy_no,
                'condition' => $request->condition,
                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                'class' => $class->class,
                'cmb_class' => $class->class,
                'updated_by'=>Auth::user()->user_name
            ]);
        }
        

        if ($update) {
            return 1;
        }else if($delete){

            return 2;

        }

        return 0;
    }
    
    public function updatePolexclusions(Request $request)
    {

        $exists = Polexclusion::where('endt_renewal_no', trim($request->endt_no))->exists();
        if ($exists) {
            if($request->exclusion == null || $request->exclusion == ''){
                $delete = Polexclusion::where('endt_renewal_no', trim($request->endt_no))->delete();

            }else{
                $update = Polexclusion::where('endt_renewal_no', trim($request->endt_no))->update([
                    'exclusion' => $request->exclusion,
                    'updated_by' => Auth::user()->user_name
                ]);
            }
 
        } else {
            $dcontrol = Dcontrol::where('endt_renewal_no', trim($request->endt_no))->first();
            if (is_null($dcontrol)) {
                $dcontrol = Marinemasterpol::where('endt_renewal_no', $request->endt_no)->first();
            }
            $class = Classmodel::where('class', $dcontrol->class)->first();
            $update = Polexclusion::create([
                'policy_no' => $dcontrol->policy_no,
                'exclusion' => $request->exclusion,
                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                'class' => $class->class,
                'cmb_class' => $class->class,
                'updated_by'=>Auth::user()->user_name
            ]);
        }

        if ($update) {
            return 1;
        }else if($delete){
            return 2;
        }

        return 0;
    }
    
    
    public function updatePolwarranty(Request $request)
    {
        $exists = Polwarranty::where('endt_renewal_no', trim($request->endt_no))->exists();
        if ($exists) {
            if($request->warranty == null || $request->warranty == ''){

                $delete = Polwarranty::where('endt_renewal_no', trim($request->endt_no))->delete();
                
            }else{
                $update = Polwarranty::where('endt_renewal_no', trim($request->endt_no))->update([
                    'warranty' => $request->warranty,
                    'updated_by' => Auth::user()->user_name
                ]);
            }
            
        } else {
            $dcontrol = Dcontrol::where('endt_renewal_no', trim($request->endt_no))->first();
            if (is_null($dcontrol)) {
                $dcontrol = Marinemasterpol::where('endt_renewal_no', $request->endt_no)->first();
            }
            $class = Classmodel::where('class', $dcontrol->class)->first();
            $update = Polwarranty::create([
                'policy_no' => $dcontrol->policy_no,
                'warranty' => $request->warranty,
                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                'class' => $class->class,
                'cmb_class' => $class->class,
                'updated_by'=>Auth::user()->user_name
            ]);
        }

        if ($update) {
            return 1;
        }else if($delete){
            return 2;
        }

        return 0;
    }
    
    public function updatePolcoverage(Request $request)
    {
        $exists = Polcoverage::where('endt_renewal_no', trim($request->endt_no))->exists();
        if ($exists) {

            if($request->coverage == null || $request->coverage == ''){

                $delete = Polcoverage::where('endt_renewal_no', trim($request->endt_no))->delete();

            }else{

                $update = Polcoverage::where('endt_renewal_no', trim($request->endt_no))->update([
                    'coverage' => $request->coverage,
                    'updated_by' => Auth::user()->user_name
                ]);

            }
            
       
        } else {
            $dcontrol = Dcontrol::where('endt_renewal_no', trim($request->endt_no))->first();
            if (is_null($dcontrol)) {
                $dcontrol = Marinemasterpol::where('endt_renewal_no', $request->endt_no)->first();
            }
            $class = Classmodel::where('class', $dcontrol->class)->first();
            $update = Polcoverage::create([
                'policy_no' => $dcontrol->policy_no,
                'coverage' => $request->coverage,
                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                'class' => $class->class,
                'cmb_class' => $class->class,
                'updated_by'=>Auth::user()->user_name
            ]);
        }
        

        if ($update) {
            return 1;
        }else if($delete){

            return 2;

        }

        return 0;
    }
    
    public function updatePolscope(Request $request)
    {
        $exists = Polscope::where('endt_renewal_no', trim($request->endt_no))->exists();
        if ($exists) {
            if($request->scope == null || $request->scope == ''){
                $delete = Polscope::where('endt_renewal_no', trim($request->endt_no))->delete();

            }else{
                $update = Polscope::where('endt_renewal_no', trim($request->endt_no))->update([
                    'scope' => $request->scope,
                    'updated_by' => Auth::user()->user_name
                ]);
            }

        } else {
            $dcontrol = Dcontrol::where('endt_renewal_no', trim($request->endt_no))->first();
            if (is_null($dcontrol)) {
                $dcontrol = Marinemasterpol::where('endt_renewal_no', $request->endt_no)->first();
            }
            $class = Classmodel::where('class', $dcontrol->class)->first();
            $update = Polscope::create([
                'policy_no' => $dcontrol->policy_no,
                'scope' => $request->scope,
                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                'class' => $class->class,
                'cmb_class' => $class->class,
                'updated_by'=>Auth::user()->user_name
            ]);
        }


        if ($update) {
            return 1;

        }else if($delete){ 
            return 2;

        }
        return 0;
    }

    public function check_motor_policy(Request $request){
        $class = $request->get('cls');

        $cls = ClassModel::where('class',$class)->get();

        echo $cls[0]->motor_policy;

    }


    public function fin_combined(Request $request)
    {
        $endt_renewal_no = $request->endt_renewal_no;
        $cls = $request->cls;
        //$pid=$request->post('pid');
        $pid = 3;


        $class = (int) substr($endt_renewal_no, 3, 3);

        $class_md = ClassModel::where('class', $class)->get();

        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol = $dcontrol[0];

        $uw_parameters = UwParameter::first();

        // Check for missing valuation or valuer during REN
        if ($uw_parameters->comprehensive_valuation_mandatory == 'Y'){
            $modtlmastVehicles = Modtlmast::where('endt_renewal_no', $endt_renewal_no)->where('covertype', '1')->whereNot('deleted', 'Y')->get();

            $invalidVehicles = [];

            if ($modtlmastVehicles->isNotEmpty()) {
                foreach ($modtlmastVehicles as $vehicle) {
                    if (is_null($vehicle->valuation) || is_null($vehicle->valuer)) {
                        $invalidVehicles[] = $vehicle->reg_no;
                    }
                }
            }

            if (!empty($invalidVehicles)) {
                $regNoList = implode(', ', $invalidVehicles);
                Session::Flash('error', 'Please ensure that both Valuation and Valuer fields are filled for the following vehicles: '. $regNoList);

                return redirect()->back();
                        
            }

        }

        if($class_md[0]->workflow_id > 0 || $class_md[0]->workflow_id != null || $class_md[0]->workflow_id != ''){
            $workflow = new Policy;

            $workflow->add_to_workflow($endt_renewal_no, $class_md[0]->workflow_id, $pid);
        }

        
        $count_debit = Debitmast::where('endt_renewal_no',$endt_renewal_no)->count();

        if($count_debit < 1){

            $prem_tot = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('endorse_amount');

            $prem_tot_mot = Modtlsumm::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('endorse_amount');

            $prem_cmb = Polcmb::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('endorse_amount');

            $tot_prem = $prem_tot + $prem_tot_mot;
        }

        //if($class_md[0]->combined == 'Y' && $request->post('fin_cmb') != 'Y'){

        if ($class_md[0]->combined == 'Y') {

            if($count_debit < 1){ 
                
                if($tot_prem != $prem_cmb){

                    $upd_polcmb = new CombinedPolicy($dcontrol->policy_no, $endt_renewal_no);

                    $upd_polcmb->update_current_polcmb(); 

                }

            }


            // $si_tot = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('total_sum_insured');

            // $prem_tot = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('endorse_amount');

            // $annual_prem = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('total_premium');


            // DB::beginTransaction();

            // $polcmb = Polcmb::where('policy_no', $dcontrol->policy_no)->where('class', $cls)->update([
            //         'sum_insured' => $si_tot,
            //         'premium' => $annual_prem,
            //         'endt_renewal_no' => $endt_renewal_no,
            //         'endorse_amount'=>$prem_tot
            //     ]); 

            // DB::commit();

            //return redirect()->route('policy_functions', ['endt_renewal_no' => $endt_renewal_no]);

            if ($request->post('fin_cmb') == 'Y') {
                return redirect()->route('policy_functions', ['endt_renewal_no' => $endt_renewal_no]);
            } else {
                return redirect()->route('risk', ['endt_renewal_no' => $endt_renewal_no, 'cmb' => 'Y']);
            } 
        }
        else if($class_md[0]->combined == 'Y' && $request->post('fin_cmb') == 'Y'){

            // $count_debit = Debitmast::where('endt_renewal_no',$endt_renewal_no)->count();

            // if($count_debit < 1){
            //     $si_tot = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('total_sum_insured');

            //     $prem_tot = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('endorse_amount');

            //     $annual_prem = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('total_premium');


            //     DB::beginTransaction();

            //     $polcmb = Polcmb::where('policy_no', $dcontrol->policy_no)->where('class', $cls)->update([
            //         'sum_insured' => $si_tot,
            //         'premium' => $annual_prem,
            //         'endt_renewal_no' => $endt_renewal_no,
            //         'endorse_amount'=>$prem_tot
            //     ]); 

            //     DB::commit();
            // }

            if($count_debit < 1){

                if($tot_prem != $prem_cmb){

                    $upd_polcmb = new CombinedPolicy($dcontrol->policy_no, $endt_renewal_no);

                    $upd_polcmb->update_current_polcmb(); 

                }
            }

            //return redirect()->route('policy_functions', ['endt_renewal_no' => $endt_renewal_no]);

            if ($request->post('fin_cmb') == 'Y') {

                return redirect()->route('policy_functions', ['endt_renewal_no' => $endt_renewal_no]);    
                
            } else {
                return redirect()->route('risk', ['endt_renewal_no' => $endt_renewal_no, 'cmb' => 'Y']);
            } 
        }
        // else if($class_md[0]->combined == 'Y' && $request->post('fin_cmb') == 'Y'){

        //     $count_debit = Debitmast::where('endt_renewal_no',$endt_renewal_no)->count();

        //     if($count_debit < 1){
        //         $si_tot = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('total_sum_insured');

        //         $prem_tot = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('endorse_amount');

        //         $annual_prem = Polsect::where('endt_renewal_no', $endt_renewal_no)->where('class', $cls)->sum('total_premium');


        //         DB::beginTransaction();

        //         $polcmb = Polcmb::where('policy_no', $dcontrol->policy_no)->where('class', $cls)->update([
        //             'sum_insured' => $si_tot,
        //             'premium' => $annual_prem,
        //             'endt_renewal_no' => $endt_renewal_no,
        //             'endorse_amount'=>$prem_tot
        //         ]); 

        //         DB::commit();
        //     }

        //    /* $si_tot = Polsect::where('endt_renewal_no',$endt_renewal_no)->where('class',$dcontrol->class)->sum('total_sum_insured');

        //     $prem_tot = Polsect::where('endt_renewal_no',$endt_renewal_no)->where('class',$dcontrol->class)->sum('endorse_amount');


        //     DB::beginTransaction();

        //     $polcmb = Polcmb::where('policy_no',$endt_renewal_no)->where('class',$dcontrol->class)->update([
        //                     'sum_insured'=> $si_tot,
        //                     'premium'=> $prem_tot
        //                 ]);

        //     $dcon = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->update([
        //                     'class'=> $class,
        //                     'dept'=> $class_md[0]->dept
        //                 ]);

        //     //DB::commit();
        //     DB::commit();*/

        //     $dc = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->get();
        //     $cls = ClassModel::where('class',$dc[0]->class)->get();

        //     return redirect()->route('policy_functions', ['endt_renewal_no'=>$endt_renewal_no]);
       
        // }
        else {
            $count_debit = Debitmast::where('endt_renewal_no',$endt_renewal_no)->count();
            
            if($count_debit < 1){
                $dc = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();

                $cls = ClassModel::where('class', $dc[0]->class)->get();

                $pol = Polmaster::where('policy_no',$dc[0]->policy_no)->get()[0];
                static::extraToRefundEndorsement($endt_renewal_no);
                if($dc[0]->trans_type =='REN'){

                    // call update_polmaster in Risk controller()
                    // $risk_controller = app('App\Http\Controllers\gb\underwriting\Risk');
                    // $risk_controller->update_polmaster($endt_renewal_no);
                }
            } 
            return redirect()->route('policy_functions', ['endt_renewal_no' => $endt_renewal_no]);

        }
    }

    public function non_motor_index_bak(Request $request)
    {
        $schem = schemaName();
        $username = trim(Auth::user()->user_name);
        $debbit = Gate::check('debit-policy');
        $reinsurance = Gate::check('process-reinsurance');
        $printpolicydoc = Gate::check('print-policy-document');

        //check if clause exists before debitting
        $check_clause = Polclause::where('endt_renewal_no', $request->get('endt_renewal_no'))->count();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
        // return($check_clause);

        if ($request->isMethod('post')) {
            $endt_renewal_no = $request->post('endt_renewal_no');
        }

        if ($request->isMethod('get')) {
            $endt_renewal_no = $request->get('endt_renewal_no');
        }

        $dcontrol_cls = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol_cls = $dcontrol_cls[0];

        //check if its pol or another endorsement
        $endo_type = (int) substr($endt_renewal_no, 6, 1);

        switch ($dcontrol_cls->trans_type) {
            case 'POL':
                $polmaster_count = Polmaster::where('endorse_no', $endt_renewal_no)->count();

                if ($polmaster_count > 0) {

                    $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->first();
                } else {

                    $polmaster = Polmasterend::where('endorse_no', $endt_renewal_no)->first();
                }


                //access dcontrol 
                $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
                $dcontrol = $dcontrol[0];

                //ensure polsect records are added to polsectend;
                $count_polsect = Polsect::where('endt_renewal_no', $endt_renewal_no)
                    ->where('policy_no', $dcontrol->policy_no)
                    ->count();

                $count_polsectend = Polsectend::where('endt_renewal_no', $endt_renewal_no)->where('policy_no', $dcontrol->policy_no)
                    ->count();

                $count_polmasterend = Polmasterend::where('endorse_no', $endt_renewal_no)
                    ->count();

                if ($count_polmasterend < 1) {

                    //insert missing polmaster record to polmasterend from oracle view polmaster_not_in_polmasteren

                    // $polmaster_not_in_polmasterend=Polmaster_not_in_polmasteren::where('endt_renewal_no',$endt_renewal_no)->get();

                    //  foreach ($polsect_not_in_polsectend as $polsect_not_in_polsectend) {
                    //sql to clone modtl record to modtlend 
                    /*
                            DB::beginTransaction();
                            try {

                                $procedureName = ''.$gb.'.clone_polmaster_to_polmasteren';
                                $bindings = [
                                    'policy_no'  =>  $dcontrol->policy_no
                                ];
                                $resp= DB::executeProcedure($procedureName, $bindings);
                                DB::commit();

                           }

                            catch (\Throwable $e) {

                                DB::rollback();
                                        
                            }

                    */

                    //  }

                }

                if ($count_polsect != $count_polsectend) {
                    $this->clone_polsect_to_polsectend($endt_renewal_no);
                }


                //ensure clauses,limits and excesses are added to respective tables for renewal
                $limit_count = Pollimits::where('endt_renewal_no', $dcontrol_cls->endt_renewal_no)->count();
                $excess_count = Polexcess::where('endt_renewal_no', $dcontrol_cls->endt_renewal_no)->count();
                $clause_count = Polclauses::where('policy_no', $dcontrol_cls->policy_no)->where('endt_renewal_no', $endt_renewal_no)->count();

                if($dcontrol_cls->binder == "Y"){
                    //apply binder clauses 
                    if($clause_count == 0 ){
                        $this->apply_binder_clauses($endt_renewal_no);
                    }
                    //apply binder limits 
                    if($limit_count == 0 ){
                        $this->apply_binder_limits($endt_renewal_no);
                    }
                    //apply binder excesses
                    if($excess_count == 0 ){
                        $this->apply_binder_excess($endt_renewal_no);
                    }
                }else{
                    //apply standard clauses 
                    if($clause_count == 0 ){
                        // $this->apply_clauses_new($endt_renewal_no);
                        $this->autoPopulateStdClauses($endt_renewal_no, $dcontrol_cls->policy_no);
                    }
                    //apply standard limits 
                    if($limit_count == 0 ){
                        $this->apply_std_limits($endt_renewal_no);
                    }
                    //apply standard excesses
                    if($excess_count == 0 ){
                        $this->apply_std_excess($endt_renewal_no);
                    }
                }


                break;


            case 'EXT':
            case 'NIL':

                $polmaster_count = Polmaster::where('endorse_no', $endt_renewal_no)->count();

                if ($polmaster_count > 0) {

                    $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->get();
                } else {

                    $polmaster = Polmasterend::where('endorse_no', $endt_renewal_no)->get();
                }

                //access dcontrol 
                $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
                $dcontrol = $dcontrol[0];

                //ensure polsect records are added to polsectend;
                $count_polsect = Polsect::where('endt_renewal_no', $endt_renewal_no)
                    ->where('policy_no', $dcontrol->policy_no)
                    ->count();

                $count_polsectend = Polsectend::where('endt_renewal_no', $endt_renewal_no)->where('policy_no', $dcontrol->policy_no)
                    ->count();


                if ($count_polsect != $count_polsectend) {
                    $this->clone_polsect_to_polsectend($endt_renewal_no);
                }

                /////////add /////////////

                break;
        }



        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        //$polmaster=$dcontrol;
        $dcontrol = $dcontrol[0];

        $class = ClassModel::where('class', $dcontrol->class)->get();

        $motor = 'Y';


        switch ($class[0]->motor_policy) {
            case 'Y':
                $motor = 'Y';
                break;

            default:
                $motor = 'N';
                break;
        }



        $polmaster = Polmaster::where('policy_no', $dcontrol->policy_no)->get();
        //$polmaster=Polmasterend::where('endorse_no',$dcontrol->endt_renewal_no)->get();

        $polsect = Polsect::where('policy_no', $dcontrol->policy_no)->get();



        $debitmast_records_count = Debitmast::where('endt_renewal_no', $endt_renewal_no)->count();
        $MarineUTLDebit = MarineUTLDebit::where('endt_renewal_no', $endt_renewal_no)->count();
        $debited = 'N';

        if ($debitmast_records_count > 0 || $MarineUTLDebit > 0) {

            $debited = 'Y';
        }


        $debitmast = Debitmast::where('endt_renewal_no', $endt_renewal_no)->get();


        //$polremast=Polremast::where('endt_renewal_no',$endt_renewal_no)->get();


        $polremast =  DB::table('polremast')
            ->select('polremast.*', 'class.description as class_description')
            ->join('class', 'class.class', '=', 'polremast.comb_class')
            ->where(['endt_renewal_no' => $endt_renewal_no])
            ->get();



        $class = ClassModel::where('class', $polmaster[0]->class)->get();


        //get contents to calculate debit preview details
        $agmnf = Agmnf::where('branch', $polmaster[0]->branch)
            ->where('agent', $polmaster[0]->agent_no)
            ->get();

        //     $intermediaryParams = new IntermediaryQueryParams([
        //      'agentNo' => $polmaster[0]->agent_no
        
        //      ]);
        
         $agmnf  =  IntermediaryQueryService::getIntermediaryWithAttributeDetails($intermediaryParams)->get();


        $agcom = Agcom::where('class', $polmaster[0]->class)
            ->where('commission', $agmnf[0]->commission)
            ->get();



        $tax_code = Tax_code::where('tax_code', $agmnf[0]->tax_code)->get();

        //stamp duty  
        $class = ClassModel::where('class', $polmaster[0]->class)->get();
        $clauses = Clauses::where('dept', $class[0]->dept)->get();

        $narration = Endorse_descr::orderBy('descr_code')->get();

        //training levy
        $pipstmp = Pipstmp::where('key', '02')->get();
        $training = ($pipstmp[0]->levy_rate * $polmaster[0]->endorse_amount) / 100;

        //policy fund
        $phcf = ($pipstmp[0]->policy_fund_rate * $polmaster[0]->endorse_amount) / 100;

        //gross commission
        $gross_com = ($agcom[0]->percent * $polmaster[0]->endorse_amount) / 100;

        //withholding tax
        $withholding = ($gross_com * $tax_code[0]->tax_rate) / 100;

        $vat_tax = ($polmaster[0]->endorse_amount + $training + $class[0]->sticker_fees) * ($pipstmp[0]->vat_rate / 100);


        //get currency code from dcontrol

        if ($dcontrol->trans_type == 'POL') {

            //net amount to be debitted
            // $nett=($polmaster[0]->endorse_amount+$withholding+$training+$phcf+$class[0]->stamp_duty+$class[0]->sticker_fees+$vat_tax)+$gross_com;
            $nett = ($polmaster[0]->endorse_amount + $training + $phcf + $class[0]->stamp_duty + $class[0]->sticker_fees + $vat_tax);

            $stamp_duty = $class[0]->stamp_duty;
        } else {

            //net amount to be debitted
            //$nett=($polmaster[0]->endorse_amount+$withholding+$training+$phcf+$class[0]->sticker_fees+$vat_tax)+$gross_com;
            $nett = ($polmaster[0]->endorse_amount + $training + $phcf + $class[0]->sticker_fees + $vat_tax);
            $training = 0;
            $phcf = 0;
            $stamp_duty = 0;
        }



        //number of debitmast records
        $debit_count = Debitmast::where('endt_renewal_no', $endt_renewal_no)->count();

        $fully_reinsured = (new Reinsurance)->fully_reinsured($endt_renewal_no,$polmaster[0]->class);



        if ($polmaster[0]->endorse_amount < 0) {

            $document_type = 'CRN';
        } else {

            $document_type = 'DRN';
        }



        //variable to check payment of premiums
        $fully_paid = 0;

        //check if the endorsement has been paid for
        $receipted = Acdet::where('doc_type', 'REC')
            ->where('endt_renewal_no', $endt_renewal_no)->count();


        if ($receipted > 0) {

            //check amount receipted
            $amount = Acdet::where('doc_type', 'REC')
                ->where('endt_renewal_no', $endt_renewal_no)->sum('nett');
            $fully_paid = $amount;
        }

        $currency = Currency::where('currency_code', $dcontrol->currency)->get();
        $currency = $currency[0];



        //get reinsurance class from classyear

        $cls_year = Classyear::where('class', $polmaster[0]->class)
            ->where('uw_year', $polmaster[0]->uw_year)
            ->get(['reinclass', 'treaty_type']);


        $check_comb = ClassModel::where('class', $polmaster[0]->class)->get();
        $check_comb = $check_comb[0];

        // if($check_comb->combined == 'Y'){
        //$polcmb = Polcmb::where('policy_no',$polmaster[0]->policy_no)->get();

        $polcmb =  DB::table('polcmb')
            ->select('polcmb.class', 'polcmb.sum_insured', 'polcmb.premium', 'class.description')
            ->join('class', 'class.class', '=', 'polcmb.class')
            ->where(['policy_no' => $polmaster[0]->policy_no])
            ->where(['endt_renewal_no' => $dcontrol->endt_renewal_no])
            ->get();

    $polcmb_checked =  DB::table('polcmb')
                        ->select('polcmb.class', 'polcmb.sum_insured', 'polcmb.premium', 'class.description')
                        ->join('class', 'class.class', '=', 'polcmb.class')
                        ->where('polcmb.premium','>',0)
                        ->where(['polcmb.policy_no' => $polmaster[0]->policy_no])
                        ->where('polcmb.endt_renewal_no', $dcontrol->endt_renewal_no)
                        ->where('polcmb.premium','>',0)
                        ->get();

        //$polcmb = $polcmb[0];
        // }


        $reinsetup = Reinsetup::where('class', $cls_year[0]->reinclass)
            ->where('uw_year', $polmaster[0]->uw_year)
            ->get();

        $no_locations = Polsect::where('policy_no', $polmaster[0]->policy_no)->count();

        $debit_accounts = Nlparams::where('prid', 'BNK')->get();

        $Crmast = Crmast::where('branch', 95)
            //   ->where('agent',$dcontrol[0]->agent)
            ->get();

      //  $co_ins = Agmnf::where('branch', '60')->get();

        $intermediaryParams = new IntermediaryQueryParams([
           'branch' => '60'
          
        
        ]);
    
        $co_ins = IntermediaryQueryService::getActiveintermediaryByBranch($intermediaryParams)->get();
       
        $coinpart = Coinpart::where('policy_no', $polmaster[0]->policy_no)->get();

        // $polall = Polmaster::all();

        $reins = Reinsetup::where('class',$class[0]->reinclass)
                            ->where('uw_year',$polmaster[0]->uw_year)
                            ->count();

        $bustype = Bustype::where('type_of_bus', trim($dcontrol->type_of_bus))->get();
        $bustype = $bustype[0];
        $disable_reins_marine_utilization='N';
        $Marinemasterhist = Marinemasterhist::where('endt_renewal_no', $dcontrol->master_endt_no)->first();
        $marine_cert_count = Madtl::where('endt_renewal_no', $dcontrol->endt_renewal_no)->count();
        $marine_master_count = Marinemasterhist::where('endt_renewal_no', $dcontrol->endt_renewal_no)->count();
        $marineutldebitmast = MarineUTLDebitmast::where('endt_renewal_no', $dcontrol->endt_renewal_no)->get();
        $marineutldebit_count = MarineUTLDebitmast::where('endt_renewal_no', $dcontrol->endt_renewal_no)->count();

            if($class[0]->open_cover=='Y'  && $marine_cert_count > 0 && $marine_master_count == 0 && $Marinemasterhist->opentype_code='UTL'){
                $disable_reins_marine_utilization='Y';
                $fully_reinsured='Y';
            }

            if($class[0]->open_cover=='Y' && $marine_cert_count == 0 && $marine_master_count > 0 && $Marinemasterhist->opentype_code=='ZPR'){
                $fully_reinsured='Y';
            }

            $creditsetup = CreditSetup::where('record_type',0)->first();

        $pay_plan_amount = PremiumPaymentPlan::where('endt_renewal_no', $endt_renewal_no)->sum('amount_due');

        return view('gb.underwriting.policy_functions', [
            'reinsetup' => $reinsetup[0],
            'debited' => $debited,
            'count_reins'=>$reins,
            'dcontrol' => $dcontrol,
            'policy_no' => $polmaster[0]->policy_no,
            'uw_year' => $polmaster[0]->uw_year,
            'facult_reice' => $reinsetup[0]->facult_p_reice,
            'facult_comm_rate' => $reinsetup[0]->facult_p_comm_per,
            'polmaster' => $polmaster,
            'debitmast' => $debitmast,
            'marineutldebitmast' => $marineutldebitmast,
            'debit_count' => $debit_count,
            'marineutldebit_count' => $marineutldebit_count,
            'polremast' => $polremast,
            'polremast_count' => $polremast_count,
            'gross_comm' => $gross_com,
            'with_tax' => $withholding,
            'stamp_duty' => $stamp_duty,
            't_levy' => $training,
            'class' => $class,
            'phcf' => $phcf,
            'vat_tax' => $vat_tax,
            'creditsetup' => $creditsetup,
            'nett' => $nett,
            'doc_type' => $document_type,
            'cert_types' => [],
            'receipted_prem' => $fully_paid,
            'currency' => $currency,
            'motor' => $motor,
            'agmnf' => $agmnf[0],
            'comm_rate' => $agcom[0]->percent,
            'fully_reinsured' => $fully_reinsured,
            'no_locations' => $no_locations,
            'clauses' => $clauses,
            'narration' => $narration,
            'debit_accounts' => $debit_accounts,
            'co_ins' => $co_ins,
            'crmast' => $Crmast,
            // 'polall' => $polall,
            'check_comb' => $check_comb,
            'polcmb' => $polcmb,
            'polcmb_checked'=>$polcmb_checked,
            'coinpart' => $coinpart,
            'check_clause' => $check_clause,
            'debbit' => $debbit,
            'reinsurance'=>$reinsurance,
            'printpolicydoc'=>$printpolicydoc,
            'bustype' =>$bustype,
            'disable_reins_marine_utilization'=>$disable_reins_marine_utilization,
            'pay_plan_amount' => $pay_plan_amount

        ]);
    }


    public function cancel_proforma(Request $request){
        // return "true";
                $pro_endorse_no = $request->pro_endorse_no;
        $dcontrol = Dcontrol::where('endt_renewal_no',$pro_endorse_no)->first();
        $polsect = Polsect::where('endt_renewal_no',$pro_endorse_no)->first();
        $modtl = Modtl::where('policy_no',$dcontrol->policy_no)->first();

                DB::beginTransaction();

                try {
        if($dcontrol != null){
            $dcontrol->update([
                'cancelled'=>'Y'
            ]);
        }
        if($modtl != null){
            DB::table('modtl')->where('policy_no', $dcontrol->policy_no)->update(['cancelled'=>'Y']);
        }

        if($polsect != null){
            DB::table('polsect')->where('endt_renewal_no', $pro_endorse_no)->update(['cancelled'=>'Y']);
            // $polsect->update([
            //     'cancelled'=>'Y'
            // ]);
        }
                    
                    DB::commit();
                    return response()->json(['status'=>1]);
                    // all good
                } catch (\Exception $e) {
                    //dd($e);
                    DB::rollback();
                    return response()->json(['status'=>0]);
                }

    }

    public function endorsements(Request $request)
    {
        $policy_no = $request->post('policy_no');

        if(!isset($policy_no) || $policy_no == null){

            $policy_no = $request->get('policy_no');

            
        }

        // loss ration
        $getpercentlossratio=Pipcnam::all();
        $clampaid=Clhmn::where('policy_no',$policy_no)->sum('cost_todate');
        $clamest=Clhmn::where('policy_no',$policy_no)->sum('curr_total_estimate');
        $out_bal=Acdet::where('policy_no',$policy_no)->sum('unallocated');
        $gross_premium=Debitmast::where('policy_no',$policy_no)->sum('gross_amount');
        
        if($gross_premium==0 ){
            $lor = 0;
        }else{
            $lor = (($clampaid+$clamest) / $gross_premium) * 100 ;
        }
        // end loss atio
        $polmaster = Polmaster::where('policy_no', $policy_no)->get();
        $client = Client::where('client_number', $polmaster[0]->client_number)->get();
        $all_endorsements = Dcontrol::where('policy_no', $policy_no)
        ->where('doc_type', 'DRN')
        ->where('trans_type','<>', 'CNC')
        ->get();

        //get cancellation reasons
        $narration = Endorse_descr::orderBy('descr_code')->get();

        $class = ClassModel::where('class', $polmaster[0]->class)->get();
      // $intmgt = Agmnf::where('branch', $polmaster[0]->branch)->where('agent', $polmaster[0]->agent_no)->get();
        $intermediaryParams = new IntermediaryQueryParams([
            'agentNo' => $polmaster[0]->agent_no,
          
        
        ]);
    
        $intmgt = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
        $check_dbt = Debitmast::where('endt_renewal_no', $polmaster[0]->endorse_no)->count();
        $check_cnc = Dcontrol::where('policy_no', $policy_no)->where('endt_renewal_no', $polmaster[0]->endorse_no)->get(['trans_type'])[0];
        if($check_cnc->trans_type == 'NIL'){
            $nil_committed = Dcontrol::where('policy_no', $policy_no)->where('endt_renewal_no', $polmaster[0]->endorse_no)->first();
            $committed = $nil_committed->committed;
        }
        else{
            $committed = 'Y';
        }
        $check_pol_cnc = $polmaster[0]->status_code;

        switch ($class[0]->motor_policy) {

            case 'Y':
                $motor = 'Y';
                break;

            default:
                $motor = 'N';
                break;
        }

        $claim_count =  Clhmn::where('policy_no', $policy_no)->count();
        $username = trim(Auth::user()->user_name);
        $registerclaims =Gate::check('register-claim');


        $aimsgroup = Aimsuser_web::whereRaw("trim(user_name)='" . $username . "'")->get()[0];
        $checkgroup = $aimsgroup->aims_group; 
        $limitgroup = Aimsgrouplimits::where('group_id', $checkgroup)->get()[0];
        $bustype = Bustype::all();
        $businesstype = Bustype::where('type_of_bus',$polmaster[0]->type_of_bus)->first();
        $limit_dept =  $class[0]->dept;
        $out_bal = number_format($out_bal,2);
        $loss_ratio  = round($lor,2) . ' %';
        $renewable = $class[0]->renewable;
        // dd($loss_ratio);

        # Employer Liability Dependants
        $class = $class[0];
        $wibaClass = ClassModel::where('dept', $class->dept)->where('wiba_policy', 'Y')->get();
        $wiba_depts = DB::select("select distinct(a.dept),a.description  from aimsdept a join class c on a.dept = c.dept where c.wiba_rider = 'Y'");
        
        $comesaClass = ClassModel::where('classaims', 76)->first();

        $todaysdate = Carbon::today();

        $user = Aimsuser_web::whereRaw("trim(user_name) = '".trim($polmaster[0]->created_by)."'")->first();

        $dcontrol = Dcontrol::where("endt_renewal_no", trim($polmaster[0]->endorse_no))->first();
        if($class->classaims == 76){
            $latest_masterPolicy = Debitmast::whereRaw("policy_no='".$dcontrol->master_policy."' and (entry_type_descr='POL' or entry_type_descr='REN' or entry_type_descr='RNS')")
            ->orderBy('dtrans_no','DESC')
            ->take(1)
            ->first();

            $masterDcontrol = Dcontrol::where("endt_renewal_no", $latest_masterPolicy->endt_renewal_no)->first();
            $masterPolm = Polmasterend::where("endorse_no", $latest_masterPolicy->endt_renewal_no)->first();
        }
        else{
            $masterDcontrol = $dcontrol;
            $masterPolm = $polmaster[0];
        }

        // check if policy has any debited endorsement in order to allow making a claim
        if($dcontrol->ast_marker == 'T'){
            $pta_period_from = Carbon::parse($masterDcontrol->cov_period_from)->addDay();
            $pta_period_to = Carbon::parse($masterDcontrol->cov_period_to)->addDay();
        }else{
            $pta_period_from = Carbon::parse($masterPolm->period_from)->addDay();
            $pta_period_to = Carbon::parse($masterPolm->period_to)->addDay();
        }
        $modtls = (new Extra_endorsements)->get_endtl_modtl(new Request([
            'policy_no' => $masterPolm->policy_no
        ]));
        $comesaCountries = (new ComesaExtension)->getCountries();
        // return $pta_period_from;
        $can_claim = Debitmast::where('policy_no', $polmaster[0]->policy_no)->count();

        /** Find refund amount **/

        $rfn_amount = 0;
        $count_debits = Debitmast::where('endt_renewal_no',$polmaster[0]->endorse_no)->count();
        $mac_debited = MarineUTLDebit::where('endt_renewal_no', $polmaster[0]->endorse_no)->count();
        // check for NIL
        if($dcontrol->trans_type == 'NIL'){
            $count_debits = 1;
        } 

        $rns_rec = array(0,0);

        if($count_debits > 0 || $mac_debited > 0){

                $latest_rec_rfn = Debitmast::whereRaw("policy_no='".$polmaster[0]->policy_no."' and (entry_type_descr='POL' or entry_type_descr='REN' or entry_type_descr='RNS')")->orderBy('dtrans_no','DESC')->limit(1)->get()[0];

                $dcontrol_dtrans = Dcontrol::where("endt_renewal_no",$latest_rec_rfn->endt_renewal_no)->get(['dtrans_no'])[0];

                $rfn_endts = Dcontrol::whereRaw("policy_no='".$polmaster[0]->policy_no."' and to_number(dtrans_no)>=".(int)$dcontrol_dtrans->dtrans_no)->select('endt_renewal_no')->get();

                $rfn_sums = Debitmast::whereIn('endt_renewal_no', $rfn_endts)
                                              ->selectRaw('SUM(foreign_premium) as rfn_amount, SUM(foreign_sticker_amount) as rfn_sticker')
                                              ->first();

                $rfn_amount = (float)$rfn_sums->rfn_amount;
                $rfn_sticker_amt = (float)$rfn_sums->rfn_sticker;
                
                if($dcontrol->trans_type == 'NIL' and $dcontrol->nil_code == 3){
                    $rns_rec = Dcontrol::where('endt_renewal_no',$polmaster[0]->endorse_no)->get()[0];
                }
                else{
                    $rns_rec = Debitmast::where('endt_renewal_no',$polmaster[0]->endorse_no)->get()[0];
                }

                $endtDcontrol = Dcontrol::where('endt_renewal_no', $polmaster[0]->endorse_no)->first();

                if ($endtDcontrol->ast_marker == 'S' and $endtDcontrol->short_term_method == 'S') {
                    $allocated_percent = Dcontrol::where('policy_no', $polmaster[0]->policy_no)
                    ->sum('short_term_percent');
                }
                if ($endtDcontrol->ast_marker == 'T') {
                    $endtDcontrol = Dcontrol::where('endt_renewal_no', $latest_rec_rfn->endt_renewal_no)->first();
                }
        }
        $currencies = Currency::all();
        $svc_trans = Reidoc::where('trans_type','SVC')->where('tied_to_policy','Y')->get();
        $vat = VatSetup::all();

        # In the event that a policy endorsement has been deleted from the system
        # Do not show the details. Redirect to Home
        $dcontrolCount = Dcontrol::where('policy_no', $polmaster[0]->policy_no)
        ->count();

        // if ($dcontrolCount == 1) {
        //     # Check if policy was cancelled
        //     $dcontrol0 = Dcontrol::where('endt_renewal_no', $polmaster[0]->policy_no)->first();
        //     if ($dcontrol0->delete_str == 'Y') {
        //         return redirect()->route('home')->with('error', "The policy you're trying to access was cancelled");
        //     }
        // }
        
        /*** End Find Refund Amount ***/

        $check_renewal_ready = Debitmast::leftjoin('dcontrol', function($join_dcon){
                                  $join_dcon->on('debitmast.endt_renewal_no', '=', 'dcontrol.endt_renewal_no');
                                })
                                ->where('debitmast.policy_no',$polmaster[0]->policy_no)

                                ->where('dcontrol.cov_period_from','<=',$todaysdate->format('Y-m-d'))
                                ->where('dcontrol.cov_period_to','>=',$todaysdate->format('Y-m-d'))
                                ->whereRaw("debitmast.entry_type_descr IN ('POL','REN','RNS','INS','CXT')")
                                ->orderBy('debitmast.dtrans_no','desc')   
                                ->first();

        if(!empty($check_renewal_ready)){

           $count_future_ren = Dcontrol::where('policy_no',$polmaster[0]->policy_no)
                                ->where('cov_period_from','>=',$check_renewal_ready->cov_period_to)
                                ->where('trans_type','REN')
                                ->where('cancelled','<>','Y')  
                                //->whereRaw("delete_str is not null && delete_str<>'Y'") 
                                ->count();
                   
        }
        else{
            $count_future_ren = 0;
        }

        ####check complete instalment process
        
        $installment_status = 1;
        $instalment = $polmaster[0]->plan;
        if (!is_null($instalment)) {
            $prev_install = Instalparam::where('plan', $instalment)->orderBy('instal_categ', 'DESC')->first();

            if ($prev_install->instal_categ != $polmaster[0]->plan) {
                $installment_status = 0;
            }else{
                $installment_status = 1;
            }
        }


        ##writeoff or stolen
        $checkwriteoff = 0 ;
        $vehicle_reg_with_claim = [];
        if($class->motor_policy == 'Y'){
            
            $checkwriteoff = $this->checksalvage($dcontrol->policy_no);

            // get vehicle_reg for vehicles with claims
            $vehicle_reg_with_claim = DB::table('modtlmast')
                ->whereIn('reg_no', function ($query) use ($policy_no) {
                    $query->select('REG_NO')
                        ->from('CLHMN')
                        ->where('POLICY_NO', $policy_no)
                        ->whereNull('REJECTED')
                        ->where(function ($query) {
                            $query->whereNull('CLOSED')->orWhere('CLOSED', 'N');
                        });
                })
                ->where('status', 'ACT')
                ->pluck('reg_no')
                ->toArray();
            
        }

        /**** COMESA CURRENCY *****/

        /* Check default PTA currency */
        $comesaparam = ComesaParam::first();

        $pta_curr_det = Currency::where('currency',$comesaparam->default_currency)->first();

        $pta_currency_rate = Currate::where('currency_code',$pta_curr_det->currency_code)
                                     ->where('rate_date',$todaysdate->format('Y-m-d'))
                                     ->first();
                                     //dd($pta_currency_rate, $todaysdate->format('Y-m-d'));

        $pta_currency = [   
                            'currency_code' => $pta_curr_det->currency_code,
                            'currency' => $comesaparam->default_currency,
                            'currency_rate' => $pta_currency_rate->currency_rate
                        ];

        /**** END COMESA CURRENCY *****/

        /*** MARINE OPEN COVER ***/

        if($class->open_cover == 'Y'){
            $marinemaster = Marinemasterpol::where('policy_no', $dcontrol->policy_no)->first();
            $master_committed = (($marinemaster->commit_transaction == 'Y' && $marinemaster->opentype_code == 'CMC') || $marinemaster->opentype_code != 'CMC') ? 'Y' : 'N';
        }else{
            $marinemaster = Dcontrol::where('endt_renewal_no', $dcontrol->endt_renewal_no)->first();
            $master_committed = 'Y';
        }
        /*** END MARINE OPEN COVER **/

        $uw_parameters = UwParameter::first();
        return view('gb.underwriting.endorsement_details', [
            'user' => $user,
            'polmaster' => $polmaster,
            'client' => $client,
            'endorsements' => $all_endorsements,
            'motor' => $motor,
            'narration' => $narration,
            'agent_name' => $intmgt->name,
            'check_dbt' => $check_dbt,
            'registerclaims' => $registerclaims,
            'check_cnc' => $check_cnc,
            'committed' => $committed,
            'check_pol_cnc' => $check_pol_cnc,
            'installment_status' => $installment_status,
            'limitgroup' => $limitgroup,
            'limit_dept' => $limit_dept,
            'can_claim' => $can_claim,
            'bustype' => $bustype,
            'claim_count' => $claim_count,
            'out_bal'=>$out_bal,
            'lor'=>  $loss_ratio,
            'getpercentlossratio'=>  $getpercentlossratio,
            'sticker_fees'=>$class[0]->sticker_fees,
            'dcontrol'=>$dcontrol,
            'masterDcontrol' => $masterDcontrol,
            'modtls' => $modtls,
            'comesaCountries' => $comesaCountries,
            'renewable'=>$renewable,
            'businesstype'=>$businesstype,
            
            # EL 
            'class' => $class,
            'wibaClass' => $wibaClass,
            'wiba_depts'=>$wiba_depts,
            'comesaClass' => $comesaClass,
            'todaysdate' => $todaysdate,
            'endtDcontrol' => $endtDcontrol,
            'allocated_percent' => $allocated_percent,

            // pta dates
            'pta_period_from' => $pta_period_from,
            'pta_period_to' => $pta_period_to,
            // pta dates // end
            'rfn_amount' => $rfn_amount,
            'rns_rec' => $rns_rec,
            'count_debits'=>$count_debits,
            'count_future_ren'=>$count_future_ren,
            'currencies'=>$currencies,
            'svc_trans'=>$svc_trans,
            'vat'=>$vat,
            'checkwriteoff'=>$checkwriteoff,
            'pta_currency'=>$pta_currency,
            'rfn_sticker_amt' => $rfn_sticker_amt,
            'vehicle_reg_with_claim'=>$vehicle_reg_with_claim,
            'marinemaster' => $marinemaster,
            'master_committed' => $master_committed,
            'uw_parameters' => $uw_parameters
        ]);
    }

    public function risk_datatable(Request $request)
    {
        $endt_no = $request->get('endt_renewal_no');
        $regis = $request->get('reg_no');

        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_no)->get();
        $dcontrol = $dcontrol[0];

        $polmaster = Polmaster::where('policy_no', $dcontrol->policy_no)->get();
        $polmaster = $polmaster[0];

        $modtl_query = Modtlend::query()
                                ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                                // ->where('cancelled' , '<>','Y')
                                ->get();

        $debited = Debitmast::where('endt_renewal_no', $endt_no)->count();

        $GLOBALS['dcontrol'] = $dcontrol;
        $GLOBALS['endt_no'] = $endt_no;

        $count = 0;
        if($dcontrol->trans_type != 'NIL'){

            return datatables::of($modtl_query)

            ->addColumn('view', function ($modtl_query) {

                $xx = $modtl_query->reg_no;
                $dcontrol = $GLOBALS['dcontrol'];
                $endt_no = $GLOBALS['endt_no'];

                $vehikl = trim($modtl_query->reg_no);

                $clhmn = Clhmn::whereRaw("trim(reg_no) = '".$vehikl."'")->orderBy('date_notified', 'DESC')->first();
                $motorVehikl = Modtl::where('reg_no', $vehikl)->where('endt_renewal_no', $endt_no)->first();

                $isWrittenOff = $clhmn->written_off;
                $vehicklManDate = $motorVehikl->man_date;
                
                $thisYear = Carbon::now()->year;
                $vehicklAge = $thisYear - $vehicklManDate;

                // if($isWrittenOff == 'Y' && $dcontrol->policy_no != $clhmn->policy_no) {
                //     $extraDoc = '<a class="btn btn-sm-default modalreport" style="padding: 0;"> <i class="fa fa-file-o"> </i></a>';
                // } else if ($vehicklAge >= 15) {
                //     # If Vehikl is older than 15 years
                //     $extraDoc = '<a class="btn btn-sm-default modalreport" style="padding: 0;"> <i class="fa fa-file-o"> </i></a>';
                // }

                if ($motorVehikl->mechanical_report == 'Y' || $motorVehikl->valuation == 'Y') {
                    $extraDoc = '<a class="btn btn-sm-default modalreport" style="padding: 0;"> <i class="fa fa-file-o"> </i></a>';
                }

                return '<a class="btn btn-sm-default submit-riskdet" title="Edit Risk Details" style="padding: 0; margin-right: 10px;"><i title="Edit Risk Details" class="glyphicon glyphicon-edit"> </i></a>
                        <a class="btn btn-sm-default modalviewrisk " title="View Risk Details" style="padding: 0; margin-right: 10px;""> <i title="View Risk Details" class="fa fa-eye"> </i></a> </a><a title="Print Motor Certificate" class="btn btn-sm-default modalprintrisk "> <i class="fa fa-print"> </i></a>' . $extraDoc;
            })
            ->addColumn('endorse_amount', function ($modtl_rec) use ($dcontrol, $polmaster) {

                if($dcontrol->trans_type == 'EXT'){
                    $risk = new Risk();
                    //get previous endorsement number
                    $dcontrolprev = Dcontrol::where('policy_no', $dcontrol->policy_no)->orderBy('dola', 'desc')->get();
                    $prevendorse = $dcontrolprev[1]->endt_renewal_no;
                    $prev_endorse_count = Modtlend::where('endt_renewal_no', $prevendorse)->where('reg_no', $modtl_rec->reg_no)->count();
                    $endorsed_count = Modtl::where('endt_renewal_no', $dcontrol->endt_renewal_no)->where('reg_no', $modtl_rec->reg_no)->where('type', 'E')->get();
        
                    $prev_endorse_motor = Modtlend::where('endt_renewal_no', $prevendorse)->where('reg_no', $modtl_rec->reg_no)->first();
                    $prev_endorse_amount = $prev_endorse_motor->nett;
                    $prev_cover = $prev_endorse_motor->cover_type;
        
                    // $prev_endorse_amount = Modtlend::where('policy_no', $dcontrol->policy_no)->where('endt_renewal_no' ,$prevendorse)->where('reg_no',$modtl_rec->reg_no)->whereRaw("cancelled<>'Y'")->sum('nett');
                    $current_endorse_amount = Modtl::where('policy_no', $dcontrol->policy_no)->where('endt_renewal_no', $dcontrol->endt_renewal_no)->where('reg_no',$modtl_rec->reg_no)->whereRaw("cancelled<>'Y'")->sum('nett');
                    $prev_ext_amount = Modtlend::where('policy_no', $dcontrol->policy_no)->where('endt_renewal_no', $prevendorse)->where('reg_no',$modtl_rec->reg_no)->whereRaw("cancelled<>'Y'")->sum('extensions_prem');
                    $current_ext_amount = Modtl::where('policy_no', $dcontrol->policy_no)->where('endt_renewal_no', $dcontrol->endt_renewal_no)->where('reg_no',$modtl_rec->reg_no)->whereRaw("cancelled<>'Y'")->sum('extensions_prem');
                    $w_pvt_premium = Modtl::where('endt_renewal_no', $dcontrol->endt_renewal_no)->where('reg_no',$modtl_rec->reg_no)->whereRaw("cancelled<>'Y'")->sum('pvt_amt');
                    $extprem = ($current_ext_amount - $prev_ext_amount);
                    if($extprem < 0){
                      $extprem = 0;
                    }
                    $p_endorse_amount = (int)$current_endorse_amount - (int)$prev_endorse_amount;
                    $prorated_endorse_amount = 0 ;
                    if ($endorsed_count > 0) {
                        if($modtl_rec->cover_type != $prev_cover){
                            if($modtl_rec->cover_type == 3){
                                $prorated_endorse_amount = $risk->compute_tpodowngrade($dcontrol->endt_renewal_no, $current_endorse_amount, 
                                $prev_endorse_amount,$prev_endorse_amount) + $extprem;
                            }
                            else{
                                $prorated_endorse_amount = $risk->compute_tpoupgrade($dcontrol->endt_renewal_no,$prev_endorse_amount,
                                $current_endorse_amount, $prev_endorse_amount) + $extprem;
                                // $prorated_endorse_amount = 12;     
                            }
                        }else{
                        $p_endorse_amount = $current_endorse_amount - $prev_endorse_amount;
                        $prorated_endorse_amount = $risk->prorate($dcontrol->endt_renewal_no, $p_endorse_amount) + $extprem;
                        }
                    }
                    if($prorated_endorse_amount != 0){
                        // return $prorated_endorse_amount;
                        // return $p_endorse_amount;
                        return $modtl_rec->endorse_amount;
                    }
                    else{
                        return $modtl_rec->endorse_amount;
                    }      
                }
                else{
                    return $modtl_rec->endorse_amount;
                }
            })
            ->addColumn('status', function($row){
                if ($row->cancelled == 'Y') {
                    return 'Cancelled';
                }
                return 'Active';
            })
            ->escapeColumns([])


            ->make(true);

          }else{

            return datatables::of($modtl_query)

            ->addColumn('view', function ($modtl_query) {

                $xx = $modtl_query->reg_no;
                $dcontrol = $GLOBALS['dcontrol'];
                $endt_no = $GLOBALS['endt_no'];

                $vehikl = trim($modtl_query->reg_no);

                $clhmn = Clhmn::whereRaw("trim(reg_no) = '".$vehikl."'")->orderBy('date_notified', 'DESC')->first();
                $motorVehikl = Modtl::where('reg_no', $vehikl)->where('endt_renewal_no', $endt_no)->first();

                $isWrittenOff = $clhmn->written_off;
                $vehicklManDate = $motorVehikl->man_date;
                
                $thisYear = Carbon::now()->year;
                $vehicklAge = $thisYear - $vehicklManDate;

                // if($isWrittenOff == 'Y' && $dcontrol->policy_no != $clhmn->policy_no) {
                //     $extraDoc = '<a class="btn btn-sm-default modalreport" style="padding: 0;"> <i class="fa fa-file-o"> </i></a>';
                // } else if ($vehicklAge >= 15) {
                //     # If Vehikl is older than 15 years
                //     $extraDoc = '<a class="btn btn-sm-default modalreport" style="padding: 0;"> <i class="fa fa-file-o"> </i></a>';
                // }

                if ($motorVehikl->mechanical_report == 'Y' || $motorVehikl->valuation == 'Y') {
                    $extraDoc = '<a class="btn btn-sm-default modalreport" style="padding: 0;"> <i class="fa fa-file-o"> </i></a>';
                }

                return '<a class="btn btn-sm-default submit-riskdet" title="Edit Risk Details" style="padding: 0; margin-right: 10px;"><i title="Edit Risk Details" class="glyphicon glyphicon-edit"> </i></a>
                        <a class="btn btn-sm-default modalviewrisk " title="View Risk Details" style="padding: 0; margin-right: 10px;""> <i class="fa fa-eye" title="View Risk Details"> </i></a>' . $extraDoc;

            })
            ->addColumn('status', function($row){
                if ($row->cancelled == 'Y') {
                    return 'Cancelled';
                }
                return 'Active';
            })

            //  ->addColumn ('watch',function ($modtl_query) {

            //         $xx = $modtl_query[0]->endt_renewal_no;

            //    return '<a class="btn btn-default modalviewrisk "> <i class="fa fa-eye"> </i></a>';
            //     })

            ->escapeColumns([])


            ->make(true);

          }
    }

    public function check_co_ins(Request $request){
        $bustype = $request->get('bus_typ');
        $rec = Bustype::where('type_of_bus', $bustype)->first();

        echo json_encode($rec);

    }
    public function get_allocated_certs_per_vehicle(Request $request){
        // return 'kjf';
        $endorsement = trim($request->get('endorsement_no'));
        $reg_no = $request->get('reg_no');

        // return $request;
        
        $rec = Certalloc::whereRaw("trim(endt_renewal_no)=".$endorsement)->whereRaw("trim(reg_no)='".$reg_no."'")->whereRaw("cert_status<>99")->get();
        return $rec;
    }
    
    public function get_allocated_certs(Request $request){
       // return $request;
        $rec_aki_cert_no = trim($request->get('aki_cert_no'));
        //$reg_no = $request->get('reg_no');

        // return $request;
        
        //$rec = Certalloc::whereRaw("trim(endt_renewal_no)=".$endorsement)->whereRaw("trim(reg_no)='".$reg_no."'")->whereRaw("cert_status<>99")->get()[0];


        //if($rec){

            //$response = array('status' => 1,'cert_no'=>$rec->aki_cert_no);
            $aki_cert_no = array(
                'CertificateNumber' => $rec_aki_cert_no
            );

            $client = new \GuzzleHttp\Client();
            $aki_url = 'GetCertificate';
						
            $tokenpipcnam = Pipcnam::where('record_type',0)->get()[0];
            $url = $tokenpipcnam->aki_base_url. $aki_url;
            $aki_user = $tokenpipcnam->aki_user;
            $aki_passwd = $tokenpipcnam->aki_passwd;						

            $response = '';
            $status="";
            //$url = "http://**********:9005/SBProject/Services/".$aki_url;
            
            $response = $client->post($url, [
                'auth' => [
                    $aki_user, 
                    $aki_passwd
                ],
                'headers' => [
                        'Content-Type'  => 'application/json',
                        'clientID' =>'D9138895-95E8-4492-B41F-7B86D9AA892F'
                    ],
                'json'=>$aki_cert_no

            ]);
            $contents = json_decode($response->getBody()->getContents());

            $status=$contents->success;
            return $contents->callbackObj->URL;

            // if($status){
            //     return $contents->callbackObj->URL;
            // }

            // return 
							
						

        // }
        // else{

        //     $response = array('status' => 0 );
        // }          
        return -1;
    }
    public function getpolicynumbers(Request $request){

        $pol =  $request->get('term');
       $policy_no = Polmaster::whereRaw("policy_no like '%".$pol."%'")->get();

       $results = array();

       foreach($policy_no as $policy){

           $results[] = ['value'=>$policy->policy_no];
       }

       return Response::Json($results);
    }


    public function coins_datatable(Request $request)
    {
        $policy_no = $request->get('policy_no');
        $query = Coinpart::query()->where('policy_no', $policy_no);

        return datatables::of($query)

            /*->editColumn('policy_no', function ($fn) {
                return formatPolicyOrClaim($fn->policy_no) ;
            })*/

            ->editColumn('reice', function ($fn) {
                return $fn->reice . " %";
            })

            ->editColumn('sum_insured', function ($fn) {
                return number_format($fn->sum_insured);
            })

            ->editColumn('premium', function ($fn) {
                return number_format($fn->premium);
            })

            ->escapeColumns([])
            //->withTrashed()
            ->make(true);
    }

    public function notes_datatable(Request $request)
    {
        $endt_renewal_no = $request->get('endt_renewal_no');
        $query = Policy_notes::query()->where('reference_no', $endt_renewal_no);
    
        return datatables::of($query)
        ->addColumn('action', function ($src) {
        
                return '<a class="btn btn-xs" id="btn-note-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-note-delete"><i class="glyphicon glyphicon-minus"></i></a>';
            
            
        })->make(true);
    }
    


    public function nil_audit_datatable(Request $request)
    {
        $policy_no = $request->get('policy_no');
        $endt_renewal_no = $request->get('endt_renewal_no');

        $audit = Risk_audit_trail::where('policy_no',$policy_no)
                                ->where('endt_renewal_no',$endt_renewal_no);

        return datatables::of($audit)
            ->make(true);
    }


    ##reverse reinsurance details

    public function reverse_reinsurance(Request $request){

        DB::beginTransaction();
        try{
            $count_Endtrepart = Endtrepart::where('endt_renewal_no',$request->endorsement)
                                                ->where('class',$request->class_comb)
                                                // ->where('location',$request->reverse_location)  
                                                // ->where('doc_type','CRN') 
                                                ->where('reverse','<>','Y') 
                                                ->count();
                                                    
            $dcontrol = Dcontrol::where('endt_renewal_no',$request->endorsement)->first();
            $polmaster = Polmaster::where('policy_no',$dcontrol->policy_no)->first();
            $count = 1;
            $trans_type = $dcontrol->trans_type;
            $prev_reinmast_exist = false;

            if($trans_type != 'POL'){
                $prev_reinmast_exist = true;
                $prev_endt_no = $this->previousEndorsement($request->endorsement);
                $prev_dcontrol = Dcontrol::where('endt_renewal_no',$prev_endt_no)->first();
            }

            $endtrepart = Endtrepart::where('endt_renewal_no',$request->endorsement)
                ->where('class',$request->class_comb)
                // ->where('location',$request->reverse_location)  
                // ->where('doc_type','CRN') 
                ->where('reverse','<>','Y') 
                ->get();

            $reinController = new Reinsurance;

            foreach($endtrepart as $part){
                $branch = $part->branch;
                $agent = $part->agent;
                $dtrans_no = $part->dtran_no;

                $resp = $reinController->single_fac_reversal($request->endorsement,$branch,$agent,$dtrans_no);
            }

            $deleted_remast = Polremast::where('endt_renewal_no',$request->endorsement)
                ->where('comb_class',$request->class_comb)
                ->where('section',$request->reverse_section)
                ->where('location',$request->reverse_location)
                ->delete();

            $deleted_remastend = Polremastend::where('endt_renewal_no',$request->endorsement)
                ->where('comb_class',$request->class_comb)
                ->where('section',$request->reverse_section)
                ->where('location',$request->reverse_location)
                ->delete();

            if($prev_reinmast_exist){
                $prev_polremastend = Polremastend::where('endt_renewal_no',$prev_dcontrol->endt_renewal_no)
                        ->where('comb_class',$request->class_comb)
                        ->where('section',$request->reverse_section)
                        ->where('location',$request->reverse_location)
                        ->first();
                        
                $prev_remastend = DB::select( "SELECT * FROM polremastend WHERE endt_renewal_no='$prev_dcontrol->endt_renewal_no'
                    AND comb_class='$request->class_comb' and location='$request->reverse_location' AND section='$request->reverse_section'")[0];

                if(isset($prev_remastend)){
                    $prev_remastend = get_object_vars($prev_remastend);

                    DB::table('polremast')->insert( $prev_remastend);
                }
            }
            DB::commit();

            Session::flash('success','Reinsurance Detail Reversed Succesfully');
            return redirect()->route('policy_functions', ['endt_renewal_no' => $request->endorsement]);
            // return redirect()->back();
        }
        catch(\Throwable $e){
            DB::rollback();
            // dd($e);
            Session::flash('error','Reinsurance Detail Failed to Reverse');
            return redirect()->route('policy_functions', ['endt_renewal_no' => $request->endorsement]);
        }

    }

    public function getescalation(Request $request){
        $user = trim(Auth::user()->user_id);
        
        

        $usergroups = DB::select("select distinct b.user_id,b.name from aimsgrouplimits a 
                                    left join aimsusers b on a.group_id = b.aims_group
                                     where a.approve_renewal = 'Y' and b.user_id <> '$user' and b.left_company <> 'Y' ");
   
        echo  json_encode($usergroups);
    }


    public function approverenewal(Request $request){
       
        $end_no = $request->app_endorse_no;
        $policy_num = $request->policy_no;
        $comm = $request->comment;
        $user = trim(Auth::user()->user_id);
        $username = trim(Auth::user()->user_name);

        $get_sent_by = Escalate_pol::where('policy_no',$policy_num)
                                // ->where('description' , 'RENEWAL APPROVAL')
                                    ->where('endorse_no' ,$end_no)
                                    ->where('approved','N')
                                    ->where('re_escalate_date',null)
                                    ->where('escalate_type',1)
                                    ->first();
        
       
        if( $request->approve == true){

            $updateescalate = Escalate_pol::where('policy_no',$policy_num)
                                            ->where(function($query){
                                                $query->where('escalate_type',1)
                                                    ->orWhere('escalate_type',4);
                                            })
                                            ->where('re_escalate_date',null)
                                            ->where('endorse_no' ,$end_no)
                                            ->where('approved','N')
                                            ->update([
                                                // 're_escalate_date' => Carbon::now(),
                                                'approved' =>'Y',
                                                'approved_by' =>$user,
                                                'approved_date' => Carbon::now()
                                            ]);
            $email = Aimsuser_web::where('user_id', $get_sent_by->sent_by)->first();
            $emailaddr = $email->email;
            $reciever = $email->first_name;
            $name = trim(Auth::user()->user_name);

            $sendemail = new Sendemail;
            $sendemail->category = 'RENEWAL APPROVED';
            $sendemail->receiver =$emailaddr;
            $sendemail->message ="Renewal with Policy number '$policy_num' and Endorsement number '$end_no' Has been Approved.
                <br> $comm. <br> Thank You. ";
            $sendemail->creator = $name;
            $sendemail->save();

            DB::commit();
            Session::flash('success','Success ');
            
            return redirect()->action(
                'gb\underwriting\Policy_functions@endorsements',
                ['policy_no' => $policy_num]
            );
        }
        else if(  $request->decline =='decline'){
            
           
            $updateescalate = Escalate_pol::where('policy_no',$policy_num)
                        ->where('escalate_type' , 1)
                        ->where('endorse_no' ,$end_no)
                        ->where('approved','N')
                        ->where('re_escalate_date',null)
                        ->update([
                            'declined_date' => Carbon::now(),
                            'comments'=> $comm,
                            'approved' =>'D',
                            'approved_by' => $user,
                            // 'approved_date' => Carbon::now(),
                        ]);
                $escalate = new Escalate_pol ;
                $count = Escalate_pol::max('escalate_id');
                $next = $count + 1;
                $escalate->policy_no = $policy_num;
                $escalate->endorse_no =$end_no;
                $escalate->escalate_id = $next;
                $escalate->escalate_type = 4;
                $escalate->client_name = $client_number;
                $escalate->sent_by =$user;
                $escalate->sent_to =$get_sent_by->sent_by;
                $escalate->type = 'DRN';
                $escalate->description = $status;
                $escalate->user_name = $username;
                $escalate->comments = $comm;
                $escalate->approved = 'D';
                $escalate->approved_by= $user;
                $escalate->approved_date= Carbon::now();
                $escalate->description= 'RENEWAL Declined';
                $escalate->approver_remarks =$request->comment;
                $escalate->created_at =  Carbon::now();
        
                $escalate->save();
                     
                $email = Aimsuser_web::where('user_id', $get_sent_by->sent_by)->first();
                $emailaddr = $email->email;
                $reciever = $email->first_name;
                $name = trim(Auth::user()->user_name);

                $sendemail = new Sendemail;
                $sendemail->category = 'RENEWAL DECLINED';
                $sendemail->receiver =$emailaddr;
                $sendemail->message ="Renewal with Policy number '$policy_num' and Endorsement number '$end_no' Has been Declined.
                <br> $comm. <br> Thank You. ";
                $sendemail->creator = $name;
                $sendemail->save();

                DB::commit();
                Session::flash('success',' Successfully Declined ');

                return redirect()->action(
                'gb\underwriting\Policy_functions@endorsements',
                ['policy_no' => $policy_num]
                );

        }
    }



    public function approveNil(Request $request){
       
        $end_no = $request->app_endorse_no;
        $policy_num = $request->policy_no;
        $comm = $request->comment;
        $user = $request->system_user ? $request->system_user : trim(Auth::user()->user_id);
        $username =$request->system_username ? $request->system_username : trim(Auth::user()->user_name);

        $get_sent_by = Escalate_pol::where('policy_no',$policy_num)
                                    ->where('endorse_no' ,$end_no)
                                    ->where('approved','N')
                                    ->where('re_escalate_date',null)
                                    ->where('escalate_type',17)
                                    ->first();
        
        if( $request->approve == 'approve'){

            Escalate_pol::where('endorse_no',$end_no)
                          ->where(function($query){
                                $query->where('escalate_type',17);
                            })
                                ->where('re_escalate_date',null)
                                ->where('endorse_no' ,$end_no)
                                ->where('approved','N')
                                ->update([
                                    'approved' =>'Y',
                                    'approved_by' =>$user,
                                    'description' => 'NIL APPROVED',
                                    'approved_date' => Carbon::now()
                            ]);



            $email = Aimsuser_web::where('user_id', $get_sent_by->sent_by)->first();
            $emailaddr = $email->email;
            $reciever = $email->first_name;
            $name = $request->system_generated?'system' : trim(Auth::user()->user_name);

            $sendemail = new Sendemail;
            $sendemail->category = 'NIL APPROVED';
            $sendemail->receiver =$emailaddr;
            $sendemail->message ="Nil with Policy number '$policy_num' and Endorsement number '$end_no' Has been Approved.
                <br> $comm. <br> Thank You. ";
            $sendemail->creator = $name;
            $sendemail->save();

            DB::commit();  
            Session::flash('success','Escalation Approval Successfull ');

            return redirect()->route('policy_functions', ['endt_renewal_no' => $end_no]);
        }
        else if(  $request->decline =='decline'){
            
           Escalate_pol::where('policy_no',$policy_num)
                        ->where('escalate_type' , 17)
                        ->where('endorse_no' ,$end_no)
                        ->where('approved','N')
                        ->where('re_escalate_date',null)
                        ->update([
                            'declined_date' => Carbon::now(),
                            'comments'=> $comm,
                            'approved' =>'D',
                            'approved_by' => $user,
                        ]);


                $escalate = new Escalate_pol ;
                $count = Escalate_pol::max('escalate_id');
                $next = $count + 1;
                $escalate->policy_no = $policy_num;
                $escalate->endorse_no =$end_no;
                $escalate->escalate_id = $next;
                $escalate->escalate_type = 4;
                $escalate->client_name = $client_number;
                $escalate->sent_by =$user;
                $escalate->sent_to =$get_sent_by->sent_by;
                $escalate->type = 'NIL';
                $escalate->description = $status;
                $escalate->user_name = $username;
                $escalate->comments = $comm;
                $escalate->approved = 'D';
                $escalate->approved_by= $user;
                $escalate->approved_date= Carbon::now();
                $escalate->description= 'Nil Declined';
                $escalate->approver_remarks =$request->comment;
                $escalate->created_at =  Carbon::now();
        
                $escalate->save();
                     
                $email = Aimsuser_web::where('user_id', $get_sent_by->sent_by)->first();
                $emailaddr = $email->email;
                $reciever = $email->first_name;
                $name = trim(Auth::user()->user_name);

                $sendemail = new Sendemail;
                $sendemail->category = 'NIL DECLINED';
                $sendemail->receiver =$emailaddr;
                $sendemail->message ="Nil with Policy number '$policy_num' and Endorsement number '$end_no' Has been Declined.
                <br> $comm. <br> Thank You. ";
                $sendemail->creator = $name;
                $sendemail->save();

                DB::commit();

                Session::flash('success','Escalation Approval Declined ');

                return redirect()->route('policy_functions', ['endt_renewal_no' => $end_no]);

        }
    }


    public function fetchSpecialDmc(Request $request){
        $endorse_no = trim($request->endt_no);
        $escalation_count = Escalate_pol::where('endorse_no', $endorse_no)
                                    ->where('type', 'SPT')
                                    ->where('escalate_type', 9)
                                    ->whereNull('re_escalate_date')
                                    ->whereNull('approved_date')
                                    ->whereNull('declined_date')
                                    ->count();

        $doc = Dcontrol::Select('policy_no','endt_renewal_no','insured')
                        ->Where('endt_renewal_no',$endorse_no)
                        ->first();

        return [
            'escalation_count' => $escalation_count,
            'document' => $doc
        ];

    }

    // public function storeSpecialDmc(Request $request) {       
    public function storeSpecialDmc(SpecialCertApprovalRequest $request) {       
        $user = trim(Auth::user()->user_id);
        $username = trim(Auth::user()->user_name);
        $status = 0;

        $doc_type = 'SPT';
        $type = 'SPECIAL CERT APPROVAL';
        $endt_no = $request->dmc_end_no;
        $dcontrol = Dcontrol::Select('policy_no','insured')
                            ->Where('endt_renewal_no',$endt_no)
                            ->first();

        DB::beginTransaction();
        try{
            if($request->dmc_escal == 2) {
                ##update previous esclate dmc
                $update_escalate = Escalate_pol::where('endorse_no',$endt_no)
                                ->where('escalate_type',9)
                                ->whereNull('re_escalate_date')
                                ->update([
                                    're_escalate_date' => Carbon::now(),
                                    'reescalated' => 'Y',
                                    'description' =>$type
                                    ]);
            }
                              
            $escalate = new Escalate_pol;
            $count = Escalate_pol::max('escalate_id');
            $next = $count + 1;

            $escalate->policy_no = $dcontrol->policy_no;
            $escalate->endorse_no = $endt_no;
            $escalate->escalate_id = $next;
            $escalate->escalate_type = 9;
            $escalate->approved = 'N';
            $escalate->reescalated = 'N';
            $escalate->client_name = $dcontrol->insured;
            $escalate->sent_by = $user;
            $escalate->sent_to = $request->escalate_to;
            $escalate->type = $doc_type;
            $escalate->description = $type;
            $escalate->user_name = $username;
            $escalate->comments = $request->dmc_comment;
            $escalate->created_at = Carbon::now();
            $escalate->save();
            
            DB::commit();

            $status = 1;

        }catch(\Exeption $e){
            dd($e);

            DB::rollback();

            $status = 0;
        }

        return array('status' => $status);
    }

    public function approve_decline_dmc(SpecialCertApprovalRequest $request){
        $status = 0;
        $message = '';
        $user = trim(Auth::user()->user_id);
        $name = trim(Auth::user()->user_name);
        $comm = $request->dmc_comment;
        $endt_no = $request->endt_no;
        $dcontrol = Dcontrol::Select('policy_no','insured')
                            ->Where('endt_renewal_no',$endt_no)
                            ->first();
        $pol_no = $dcontrol->policy_no;

        $getrequestUser = Escalate_pol::Select('user_name','sent_by')
                                    ->where('endorse_no', $endt_no)
                                    ->where('escalate_type',9)
                                    ->orderBy('created_at', 'ASC')
                                    ->first();
        $useraims = Aimsuser_web::where('user_id', $getrequestUser->sent_by)->first();
        $emailaddr = $useraims->email;



        DB::beginTransaction();
        try {
            if($request->approve_decline == 'D'){
                $category = 'SPECIAL CERT DECLINED';
                $message = "Special request to issue certificate on policy number '$pol_no' and endorsement number '$endt_no' has been declined. $comm. Thank You. ";
                $feedback = 'Request declined successfully';
                $update_pol = Escalate_pol::where('endorse_no',$endt_no)
                                                ->where('escalate_type',9)
                                                ->wherenull('re_escalate_date')
                                                ->whereNull('approved_date')
                                                ->whereNull('declined_date')
                                                ->update([
                                                        'approved'=> 'D',
                                                        'approved_by'=> $user,
                                                        'declined_date' => Carbon::now(),
                                                        'approver_remarks' =>$comm,
                                                     ]);
            }
    
           if($request->approve_decline == 'A'){
                $category = 'SPECIAL CERT APPROVED';
                $message = "Special request to issue certificate on policy number '$pol_no' and endorsement number '$endt_no' has been approved. <br> $comm. Thank You. ";
                $feedback = 'Request approved successfully';
                $update_pol = Escalate_pol::where('endorse_no',$endt_no)
                                        ->where('escalate_type',9)
                                        ->wherenull('re_escalate_date')
                                        ->whereNull('approved_date')
                                        ->whereNull('declined_date')
                                        ->update([
                                                'approved'=> 'Y',
                                                'approved_by'=> $user,
                                                'description'=> $category,
                                                'approved_date'=> Carbon::now(),
                                                'approver_remarks' => $comm,
                                            ]);
            }
            //send email to user
            if($update_pol) {
                $sendemail = new Sendemail;
                $sendemail->category = $category;
                $sendemail->receiver = $emailaddr;
                $sendemail->message = $message;
                $sendemail->creator = $name;
                $sendemail->save();
            }

            DB::commit();
            $status = 1;
        }catch(\Exception $e){
            DB::rollback();
            $status = 0;
        }

        return [
            'status' => $status,
            'message' => $feedback
        ];

    }





    public function credit_reescalation(Request $request){
        
            try{
                $end = $request->endorsement;
                $credit_ref = $request->credit_ref;
                $comment = $request->comment;
                $sent_to = $request->escalate_credit;
                $username = trim(Auth::user()->user_name);

            $updatecbcredit = Cbcredit::where('claim_no',$end)->where('credit_reference',$credit_ref)
                        ->update([
                                'escalate_to'=>$request->escalate_credit,
                                'narration'=>$comment,
                                'dola'=>Carbon::now()
                            ]);

            $updateescalate = Escalate_pol::
                    where('description' , 'CREDIT APPROVAL')
                    ->where('endorse_no' ,$end)
                    ->where('type','CRD')
                    ->update([
                        'comments'=> $comment,
                        'sent_to' =>$sent_to,
                        'sent_by' =>$user,
                        'created_at' => Carbon::now(),
                        'user_name' => $username,
                    ]);
    
                $email = Aimsuser_web::where('user_id', $sent_to)->first();
                $emailaddr = $email->email;
                $reciever = $email->first_name;
                $name = trim(Auth::user()->user_name);
    
                $sendemail = new Sendemail;
                $sendemail->category = 'CREDIT APPROVAL';
                $sendemail->receiver =$emailaddr;
                $sendemail->message ="Kindly Approve the Credit with Reference no. '$credit_ref' and Endorsement number '$end_no' .
                    <br> $comment. <br> Thank You. ";
                $sendemail->creator = $name;
                $sendemail->save();
                DB::commit();
                Session::flash('success',' Success');
                
                return redirect()->route('policy_functions', ['endt_renewal_no' => $end]);
               
                }catch (\Exception $e) {
                DB::rollback();
                Session::flash('error','Escalation not done Successfully ');
                }
            
    }

    public function recheck_escalation(Request $request){
        $endorse_no = trim($request->endt_no);
        $escalateexist = Escalate_pol::where('endorse_no',$endorse_no)
                                        ->where(function($query){
                                            $query->where('type','DRN')
                                                ->orWhere('type','CRN');
                                        })
                                   ->where('escalate_type',2)
                                   ->where('re_escalate_date',null)
                                    ->count();  
       echo $escalateexist;

    }
    public function checkescalate_pol(Request $request){
        $endorse_no = trim($request->endt_no);
       $checkendorse = Escalate_pol::where('endorse_no',$endorse_no)
                                    ->where('type','DRN')
                                    ->where('escalate_type',1)
                                    ->where('re_escalate_date',null)
                                    ->first();  
       
       echo $checkendorse;

    }


    public function escalate_pol(Request $request){

        // dd($request->all());
       
        $user = $request->system_user ? $request->system_user : trim(Auth::user()->user_id);
        $username = $request->system_username ? $request->system_username : trim(Auth::user()->user_name);

        if ($request->escalate_nil == 'Escalate'){

            try{
                $sent_to = $request->get('escalate_doc');
                $policy_num = $request->get('policy');
                $end_no = $request->get('endorsement');
                $comm = $request->comments;
                $escalate = new Escalate_pol ;
                $count = Escalate_pol::max('escalate_id');
                $next = $count + 1;
                $escalate->policy_no = $policy_num;
                $escalate->endorse_no =$end_no;
                $escalate->escalate_id = $next;
                $escalate->sent_by =$user;
                $escalate->sent_to =$sent_to;
                $escalate->type = 'NIL';
                $escalate->escalate_type = 17;
                $escalate->approved = 'N';
                $escalate->description = 'NIL APPROVAL';
                $escalate->user_name = $username;
                $escalate->comments = $comm;
                $escalate->created_at =  Carbon::now();
                $escalate->reescalated = 'N';
                $escalate->save();

                DB::commit();
                //send email to user
                $email = Aimsuser_web::where('user_id', $sent_to)->first();
                $emailaddr = $email->email;
                $name = trim(Auth::user()->user_name);
                $sendemail = new Sendemail;
                $sendemail->category = 'NIL APPROVAL';
                $sendemail->receiver =$emailaddr;
                $sendemail->message ="Kindly Approve the NIL Endorsement with Endorsement number '$end_no' .
                    <br> $comm. <br> Thank You. ";
                $sendemail->creator = $name;
                $sendemail->save();
                    
                DB::commit();
                Session::flash('success','Escalation Successfull ');

                return redirect()->route('policy_functions', ['endt_renewal_no' => $end_no]);

                }catch (\Exception $e) {
                    DB::rollback();
                    $success = 0;
                    return  $success;
                    Session::flash('error','Escalation not done Successfully ');
                  
                }

        } else if($request->escalate_nil == 'Re-Escalate'){

            $sent_to = $request->get('escalate_doc');
            $policy_num = $request->get('policy');
            $end_no = $request->get('endorsement');
            $comm = $request->comments;
            try{
                Escalate_pol::where('endorse_no',$end_no)
                                    ->where(function($query){
                                        $query->where('escalate_type',17);
                                    })
                                    ->where('re_escalate_date',null)
                                    ->update([
                                        're_escalate_date' => Carbon::now(),
                                        'reescalated' =>'Y'
                                    ]);

                ##create new transaction
                $escalate = new Escalate_pol ;
                $count = Escalate_pol::max('escalate_id');
                $next = $count + 1;

                $escalate->policy_no = $policy_num;
                $escalate->reescalated = 'N';
                $escalate->endorse_no =$end_no;
                $escalate->escalate_id = $next;
                $escalate->sent_by =$user;
                $escalate->sent_to =$sent_to;
                $escalate->type = 'NIL';
                $escalate->escalate_type = 17;
                $escalate->approved = 'N';
                $escalate->description = 'NIL APPROVAL';
                $escalate->user_name = $username;
                $escalate->comments = $comm;
                $escalate->created_at =  Carbon::now();
                $escalate->save();

                $email = Aimsuser_web::where('user_id', $sent_to)->first();
                $emailaddr = $email->email;
                $reciever = $email->first_name;
                $name = trim(Auth::user()->user_name);

                $sendemail = new Sendemail;
                $sendemail->category = 'NIL APPROVAL';
                $sendemail->receiver =$emailaddr;
                $sendemail->message ="Kindly Approve the NIL Endorsement with Endorsement number '$end_no' .
                    <br> $comm. <br> Thank You. ";
                $sendemail->creator = $name;
                $sendemail->save();
                DB::commit();
                Session::flash('success',' Re-escalation done succesfully');
                
                return redirect()->route('policy_functions', ['endt_renewal_no' => $end_no]);
           
            }catch (\Exception $e) {
                DB::rollback();
                Session::flash('error','Escalation not done Successfully ');
            
            }

        }

        else if ($request->renewalescalation == 'Escalate'){

            try{
                $sent_to = $request->get('escalate_doc');
                $policy_num = $request->get('policy');
                $end_no = $request->get('endorsement');
                $comm = $request->comments;

                $escalate = new Escalate_pol ;
                $count = Escalate_pol::max('escalate_id');
                $next = $count + 1;

                $escalate->policy_no = $policy_num;
                $escalate->endorse_no =$end_no;
                $escalate->escalate_id = $next;
                $escalate->sent_by =$user;
                $escalate->sent_to =$sent_to;
                $escalate->type = 'DRN';
                $escalate->escalate_type = 1;
                $escalate->approved = 'N';
                $escalate->description = 'RENEWAL APPROVAL';
                $escalate->user_name = $username;
                $escalate->comments = $comm;
                $escalate->created_at =  Carbon::now();
                $escalate->save();
                DB::commit();
                //send email to user
                $email = Aimsuser_web::where('user_id', $sent_to)->first();
                $emailaddr = $email->email;
                $reciever = $email->first_name;
                $name = trim(Auth::user()->user_name);

                $sendemail = new Sendemail;
                $sendemail->category = 'RENEWAL APPROVAL';
                $sendemail->receiver =$emailaddr;
                $sendemail->message ="Kindly Approve the Renewal with Policy number '$policy_num' and Endorsement number '$end_no' .
                    <br> $comm. <br> Thank You. ";
                $sendemail->creator = $name;
                $sendemail->save();
                    
                DB::commit();
                Session::flash('success','Escalation Successfull ');
                
                return redirect()->action(
                    'gb\underwriting\Policy_functions@endorsements',
                    ['policy_no' => $policy_num]
                );

                }catch (\Exception $e) {
                    DB::rollback();
                    $success = 0;
                    return  $success;
                    Session::flash('error','Escalation not done Successfully ');
                  
                }

        } else if($request->re_escalaterenewal == 'Re-Escalate'){

            $sent_to = $request->get('escalate_doc');
            $policy_num = $request->get('policy');
            $end_no = $request->get('endorsement');
            $comm = $request->comments;
            try{
                $updateescalate = Escalate_pol::where('policy_no',$policy_num)
                                                ->where(function($query){
                                                    $query->where('escalate_type',1)
                                                        ->orWhere('escalate_type',4);
                                                })
                                                ->where('re_escalate_date',null)
                                                ->update([
                                                    're_escalate_date' => Carbon::now()
                                                ]);
                ##create new transaction
                $escalate = new Escalate_pol ;
                $count = Escalate_pol::max('escalate_id');
                $next = $count + 1;

                $escalate->policy_no = $policy_num;
                $escalate->endorse_no =$end_no;
                $escalate->escalate_id = $next;
                $escalate->sent_by =$user;
                $escalate->sent_to =$sent_to;
                $escalate->type = 'DRN';
                $escalate->escalate_type = 1;
                $escalate->approved = 'N';
                $escalate->description = 'RENEWAL APPROVAL';
                $escalate->user_name = $username;
                $escalate->comments = $comm;
                $escalate->created_at =  Carbon::now();
                $escalate->save();



                $email = Aimsuser_web::where('user_id', $sent_to)->first();
                $emailaddr = $email->email;
                $reciever = $email->first_name;
                $name = trim(Auth::user()->user_name);

                $sendemail = new Sendemail;
                $sendemail->category = 'RENEWAL APPROVAL';
                $sendemail->receiver =$emailaddr;
                $sendemail->message ="Kindly Approve the Renewal with Policy number '$policy_num' and Endorsement number '$end_no' .
                    <br> $comm. <br> Thank You. ";
                $sendemail->creator = $name;
                $sendemail->save();
                DB::commit();
                Session::flash('success',' Success');
                
                return redirect()->action(
                    'gb\underwriting\Policy_functions@endorsements',
                    ['policy_no' => $policy_num]
                );
           
            }catch (\Exception $e) {
                DB::rollback();
                Session::flash('error','Escalation not done Successfully ');
            
            }

        }else if ($request->escal == 1){
               
            try{
                $sent_to = $request->get('Associates');
                $policy_num = $request->get('trans_pol_no');
                $end_no = $request->get('trans_end_no');
                $client_number = $request->get('client_number');

                $doc_type = $request->get('trans_doc_type');
                if($doc_type == 'DRN'){
                    $type = 'DEBIT APPROVAL';
                }else{
                    $type = 'REFUND APPROVAL';
                }

                $comm = $request->comment;

                $escalate = new Escalate_pol ;
                $count = (int)Escalate_pol::max('escalate_id');
                $next = $count['escalate_id'] + 1;

                $escalate->policy_no = $policy_num;
                $escalate->endorse_no =$end_no;
                $escalate->escalate_id = $next;
                $escalate->escalate_type = 2;
                $escalate->usergrp_id = $request->get('associate_group');
                $escalate->client_name = $client_number;
                $escalate->sent_by =$user;
                $escalate->sent_to =$sent_to;
                $escalate->type = $request->get('trans_doc_type');
                $escalate->amount = $request->get('sum_ins');
                $escalate->description = $type;
                $escalate->user_name = $username;
                $escalate->comments = $comm;
                $escalate->created_at =  Carbon::now();
                // return $end_no;
                $escalate->save();
                DB::commit();

                //send email to user
                $email = Aimsuser_web::where('user_id', $sent_to)->first();
                $emailaddr = $email->email;
                $reciever = $email->first_name;
                $name = trim(Auth::user()->user_name);

                $sendemail = new Sendemail;
                $sendemail->category = $type;
                $sendemail->receiver =$emailaddr;
                $sendemail->message ="Kindly Approve the Policy with Policy number '$policy_num' and Endorsement number '$end_no' .
                    Comments: $comm. Thank You. ";
                $sendemail->creator = $name;
                $sendemail->save();
                    DB::commit();
                    // Session::flash('success','Escalation Successfull '.$polmaster[0]->name);
                    // return redirect()->route('policy_functions', ['endt_renewal_no' => $end_no]);
                    $success = 1;
                    return $success;
            }catch (\Exception $e) {
                    DB::rollback();
                    // dd($e);
                    // report($e);
                    
                    // Session::flash('error','Escalation not done Successfully '.$polmaster[0]->name);

                    // return redirect()->route('policy_functions', ['endt_renewal_no' => $end_no]);

                    $success = 0;
                    return $success;
            }
        } 
        else if($request->escal == 2){

            try{
                $sent_to = $request->get('Associates');
                $policy_num = $request->get('trans_pol_no');
                $end_no = $request->get('trans_end_no');
                $client_number = $request->get('client_number');
                $comm = $request->comment;

                ##update previous esclate pol
                $update_escalate = Escalate_pol::where('endorse_no',$end_no)
                                ->where(function($query){
                                    $query->where('escalate_type',2)
                                        ->orWhere('escalate_type',3);
                                })
                                ->where('re_escalate_date',null)
                                ->update([
                                    're_escalate_date' => Carbon::now()
                                ]);

                $doc_type = $request->get('trans_doc_type');
                if($doc_type == 'DRN'){
                    $type = 'DEBIT APPROVAL';
                }else{
                    $type = 'REFUND APPROVAL';
                }

                ##create new transaction
                $escalate = new Escalate_pol ;
                $count = (int)Escalate_pol::max('escalate_id');
                $next = $count + 1;
                $escalate->policy_no = $policy_num;
                $escalate->endorse_no =$end_no;
                $escalate->escalate_id = $next;
                $escalate->escalate_type = 2;
                $escalate->usergrp_id = $request->get('associate_group');
                $escalate->client_name = $client_number;
                $escalate->sent_by =$user;
                $escalate->sent_to =$sent_to;
                $escalate->type = $request->get('trans_doc_type');
                $escalate->amount = $request->get('sum_ins');
                $escalate->description = $type;
                $escalate->user_name = $username;
                $escalate->comments = $comm;
                $escalate->created_at =  Carbon::now();
                // return $end_no;
                $escalate->save();
                DB::commit();

                //send email to user
                $email = Aimsuser_web::where('user_id', $sent_to)->first();
                $emailaddr = $email->email;
                $reciever = $email->first_name;
                $name = trim(Auth::user()->user_name);

                $sendemail = new Sendemail;
                $sendemail->category = $type;
                $sendemail->receiver =$emailaddr;
                $sendemail->message ="Kindly Approve the Policy with Policy number '$policy_num' and Endorsement number '$end_no' .
                    Comments: $comm. Thank You. ";
                $sendemail->creator = $name;
                $sendemail->save();
                    DB::commit();
                    // Session::flash('success','Escalation Successfull '.$polmaster[0]->name);
                    // return redirect()->route('policy_functions', ['endt_renewal_no' => $end_no]);
                    $success = 1;
                    return $success;
            }catch (\Exception $e) {
                    DB::rollback();
                    // dd($e);
                    // report($e);
                    // Session::flash('error','Escalation not done Successfully '.$polmaster[0]->name);

                    // return redirect()->route('policy_functions', ['endt_renewal_no' => $end_no]);
                    $success = 0;
                    return $success;
                    
            }
                   
        }

    }

    public function ri_escalate_pol(Request $request)
    {
        // dd($request->all());
        DB::beginTransaction();
        try {
            $username =  Auth::user()->user_name;
            $user_id =  Auth::user()->user_id;

            // approver
            $approver_id = $request->get('Associates');

            $count = Escalate_pol::where('sent_by', $user_id)->max('escalate_id');
            $next = $count + 1;

            $endt_renewal_no = $request->get('trans_end_no');
            $policy_num = $request->get('trans_pol_no');
            $client_number = $request->get('client_number');
            $sum_insured = $request->sum_ins;
            $company_ret = $request->company_ret_limit;
            $surp_limit = $request->get('3rd_surp_limit') != 0 ? $request->get('3rd_surp_limit') : 
                ($request->get('2nd_surp_limit') != 0 ? $request->get('2nd_surp_limit') : $request->get('1st_surp_limit'));
            $surp_referral_lines = $request->surp_referral_lines;
            $surp_referral_limit = $request->surp_referral_lines_limit;
            $comm = $request->comment;
            $category = 'Surplus referral lines utilization';

            if($request->approve_decline == 'Y'){
                $escalation = Escalate_pol::where('endorse_no',$endt_renewal_no)
                    ->where('type','R/I')
                    ->where('re_escalate_date',null)
                    ->first();
                $email = Aimsuser_web::where('user_id', $escalation->sent_by)->first();
                $emailaddr = $email->email;

                switch ($request->ri_action) {
                    case 'A':
                        Escalate_pol::where('endorse_no',$endt_renewal_no)
                        ->where('type','R/I')
                        ->where('re_escalate_date',null)
                        ->update([
                            'approved'=> 'Y',
                            'approved_by'=> $user_id,
                            'approved_date'=> Carbon::now(),
                            'description'=>  'Surplus referral lines utlization approved',
                            'approver_remarks' =>$request->approver_comments,
                        ]);

                        $mess = "Utilization of surplus referral lines has been Approved.<br>
                        <tr>
                            <td><b style='margin-right:30px;'>Policy Number</b></td> 
                            <td>{$policy_num}</td>
                        <tr>
                            <td><b style='margin-right:30px;'>Endorsement Number</b></td> 
                            <td>{$endt_renewal_no}</td>
                        </tr>
                        <tr>
                            <td><b>Additional comments:</b></td>
                            <td>{$comm}</td>
                        </tr>";
                    break;
                    
                    case 'D':
                        Escalate_pol::where('endorse_no',$endt_renewal_no)
                            ->where('type','R/I')
                            ->where('re_escalate_date',null)
                            ->update([
                                'declined_date' => Carbon::now(),
                                'approver_remarks' =>$request->approver_comment,
                            ]);

                            $mess = "Utilization of surplus referral lines has been Rejected.<br>
                                <b>Additional comments:</b> <br>{$comm}<br>";
                        break;
                }
            }
            else{
                $email = Aimsuser_web::where('user_id', $approver_id)->first();
                $emailaddr = $email->email;

                if($request->re_escalate == 'Y'){
                    $updateescalate = Escalate_pol::where('endorse_no',$endt_renewal_no)
                    ->where('type','R/I')
                    ->where('re_escalate_date',null)
                    ->update([
                        're_escalate_date' => Carbon::now()
                    ]);
                }

                $escalate = new Escalate_pol;
                $escalate->escalate_id = $next;
                $escalate->user_name = $username;
                $escalate->created_at =  Carbon::now();

                $escalate->policy_no = $policy_num;
                $escalate->endorse_no =$endt_renewal_no;
                $escalate->escalate_id = $next;
                $escalate->escalate_type = 5;
                $escalate->usergrp_id = $request->get('associate_group');
                $escalate->client_name = $client_number;
                $escalate->sent_by =$user_id;
                $escalate->sent_to =$approver_id;
                $escalate->type = 'R/I';
                $escalate->amount = str_replace(',','',$request->get('sum_ins'));
                $escalate->description = 'Surplus referral lines utilization';
                $escalate->comments = $comm;
                $escalate->created_at =  Carbon::now();

                $escalate->save();

                $mess = "Please approve this request to utilize surplus referral lines.
                    <tr>
                        <td><b style='margin-right:30px;'>Policy Number</b></td> 
                        <td>{$policy_num}</td>
                    <tr>
                        <td><b style='margin-right:30px;'>Endorsement Number</b></td> 
                        <td>{$endt_renewal_no}</td>
                    </tr>
                    <tr>
                        <td><b style='margin-right:30px;'>Sum Insured</b></td> 
                        <td>{$sum_insured}</td>
                    </tr>
                    <tr>
                        <td><b style='margin-right:30px;'>Company Retention</b> </td>
                        <td>{$company_ret}</td>
                    </tr>
                    <tr>
                        <td><b style='margin-right:30px;'>Surplus Limit</b> </td>
                        <td>{$surp_limit}</td>
                    </tr>
                    <tr>
                        <td><b style='margin-right:30px;'>Surplus Referral Lines</b> </td>
                        <td>{$surp_referral_lines}</td>
                    </tr>
                    <tr>
                        <td><b style='margin-right:30px;'>Surplus Referral Lines Limit</b> </td>
                        <td>{$surp_referral_limit}</td>
                    </tr>
                    <tr>
                        <td><b style='margin-right:30px;'>Additional comments</b> </td>
                        <td>{$comm}</td>
                    </tr>";

            }
            $sendemail = new Sendemail;
            $sendemail->category = $category;
            $sendemail->receiver = $emailaddr;
            $sendemail->message = $mess;
            $sendemail->creator = $username;

            $sendemail->save();

            DB::commit();
            return 1;
        } catch (Throwable $e) {
            DB::rollBack();
            // dd($e);
            return 0;
        }
    }

    public function aprove_decline(Request $request){


        $user = trim(Auth::user()->user_id);
        $username = trim(Auth::user()->user_name);
        // $sent_to = $request->get('Associates');
        $policy_num = $request->get('aptrans_pol_no');
        $end_no = $request->get('aptrans_end_no');
        $client_number = $request->get('client_number');
        $dotype = $request->get('doctype');

        // $comm = $request->comment;

        $get_sent_id = Escalate_pol::whereRaw("trim(policy_no)='" . $policy_num. "'")->whereRaw("trim(endorse_no)='" . $end_no. "'")->orderBy('created_at', 'ASC')->get();
        $us = $get_sent_id[0]->user_name;
        $reciv = $get_sent_id[0]->sent_by;

        if($request->declined == true){


                    $update_escalate = Escalate_pol::where('endorse_no',$end_no)
                    // ->where(function($query){
                    //     $query->where('escalate_type',2)
                    //         ->orWhere('escalate_type',3);
                    // })
                    ->where('escalate_type',2)
                    ->where('re_escalate_date',null)
                    ->update([
                        'declined_date' => Carbon::now()
                    ]);
            if($dotype == 'DRN'){
                $status = 'DEBIT REJECTED';
            }else{
                $status = 'REFUND REJECTED';
            }
           
            $messagge = "Policy number '$policy_num' and Endorsement number '$end_no' Has Been Declined. $comm. Thank You. ";

            $comm = $request->comment;
            $escalate = new Escalate_pol ;
    
            $count = Escalate_pol::where('sent_by', $user )->max('escalate_id');
            $next = $count + 1;
    
            $escalate->policy_no = $policy_num;
            $escalate->endorse_no =$end_no;
            $escalate->escalate_id = $next;
            $escalate->escalate_type = 3;
            $escalate->client_name = $client_number;
            $escalate->sent_by =$user;
            $escalate->sent_to =$reciv;
            $escalate->type = $dotype;
            // $escalate->amount = $request->get('sum_ins');
            $escalate->description = $status;
            $escalate->user_name = $username;
            $escalate->comments = $comm;
            $escalate->approved = 'D';
            $escalate->approved_by= $user;
            $escalate->approved_date= Carbon::now();
            $escalate->description= 'Debit Declined';
            $escalate->approver_remarks =$request->comment;
            $escalate->created_at =  Carbon::now();
    
            $escalate->save();

        }
        else if($request->approved == true){

            // return $request;
            $signature = DB::table('aimsusers')->where('user_name', $username)->first()->signature;
            if($dotype == 'DRN'){
                $status = 'DEBIT APPROVED';
            }else{
                $status = 'REFUND APPROVED';
            };
            $update_pol = Escalate_pol::whereRaw("trim(policy_no)='" . $policy_num. "'")
                                    ->whereRaw("trim(endorse_no)='" . $end_no. "'")
                                    ->where("escalate_type" ,2)
                                    ->where("re_escalate_date" ,null)
                                    ->update([
                                        'approved'=> 'Y',
                                        'approved_by'=> $user,
                                        'approved_date'=> Carbon::now(),
                                        'description'=>  $status,
                                        'approver_remarks' =>$request->comment,
                                        // 'user_name'=>$username
                                    ]);

                Dcontrol::whereRaw("trim(policy_no)='" . $policy_num. "'")
                ->whereRaw("trim(endt_renewal_no)='" . $end_no. "'")
                ->update([
                    'debit_approved'=> 'Y',
                    'debit_approved_by'=> $username,
                    'debit_approved_date'=> Carbon::now(),
                    'debit_approver_signature'=> $signature
                ]);
                

        
            $messagge = "Policy number '$policy_num' and Endorsement number '$end_no' Has Been Approved. <br> $comm. Thank You. ";
        }
 
        
       
        DB::commit();

        //send email to user
        $email = Aimsuser_web::where('user_id', $reciv)->first();
        $emailaddr = $email->email;
        $reciever = $email->surname;
        $name = trim(Auth::user()->user_name);
      

        $sendemail = new Sendemail;
        $sendemail->category = $status;
        $sendemail->receiver =$emailaddr;
        $sendemail->message =$messagge;
        $sendemail->creator = $name;
  
     
        try{
            $sendemail->save();
            
            DB::commit();
            Session::flash('success','Message Sent'.$polmaster[0]->name);
            return redirect()->route('policy_functions', ['endt_renewal_no' => $end_no]);


        }catch (\Exception $e) {
            DB::rollback();
            // dd($e);
            // report($e);
            
            Session::flash('error','Message not Sent'.$polmaster[0]->name);

            return redirect()->route('policy_functions', ['endt_renewal_no' => $end_no]);

        }

    }


    public function risk_detail_form(Request $request)
    {
        // $moto = Accessory::where('endt_renewal_no', $request->get('end_num'))->where('reg_no', $request->get('car_num'))
        //     ->get();
        $reg_no = str_replace(' ', '',$request->get('car_num'));
        $accessories = Accessory::where('endt_renewal_no', $request->get('end_num'))
                                ->whereRaw("replace(reg_no, ' ')= '".$reg_no."'")
                                ->where('compute_prem', 'Y')
                                ->get();


        // $modtl = Modtl::where('endt_renewal_no', $request->get('end_num'))->where('reg_no', $request->get('car_num'))
        //     ->get();
        $modtl = Modtlend::where('endt_renewal_no', $request->get('end_num'))
                        ->whereRaw("replace(reg_no, ' ')= '".$reg_no."'")
                        ->first();

        $cover_type = Covertype::where('cover', $modtl->cover_type)->first();
        // $exten = Extensions::where('endt_renewal_no', $request->get('end_num'))->where('reg_no', $request->get('car_num'))
        //     ->get();


        $exten = Extensions::where('endt_renewal_no', $request->get('end_num'))
                            ->whereRaw("replace(reg_no, ' ')= '".$reg_no."'")
                            ->where('compute_prem', 'Y')
                            ->get();

        $endt_no = $request->get('endt_renewal_no');

        // $discounts = Discounts_loadings::where('endt_renewal_no', $request->get('end_num'))->where('type', 'D')->where('reg_no', $request->get('car_num'))->get();
        $discounts = Discounts_loadings::where('endt_renewal_no', $request->get('end_num'))
                                        ->where('type', 'D')
                                        ->whereRaw("replace(reg_no, ' ')= '".$reg_no."'")
                                        ->where('compute_prem', 'Y')
                                        ->get();


        // $loadings = Discounts_loadings::where('endt_renewal_no', $request->get('end_num'))->where('type', 'L')->where('reg_no', $request->get('car_num'))->get();
        $loadings = Discounts_loadings::where('endt_renewal_no', $request->get('end_num'))
                                        ->where('type', 'L')
                                        ->whereRaw("replace(reg_no, ' ')= '".$reg_no."'")
                                        ->where('compute_prem', 'Y')
                                        ->get();

        return [
            'Accessory' => $accessories,
            'Extensions' => $exten,
            'Modtl' => $modtl,
            // 'Motend' => $modtl_query,
            'Discounts' => $discounts,
            'Loadings' => $loadings,
            'cover_type' => $cover_type
        ];
    }

    public function fetch_vehicle_details(Request $request)
    {
        try{
            $reg_no = str_replace(' ', '',$request->get('reg_no'));
            $sections =  Motorpolsec::with('motor_group')
                ->join('motorsect', function($join){
                    $join->on('motorpolsec.class','motorsect.class');
                    $join->on('motorpolsec.grp_code','motorsect.grp_code');
                    $join->on('motorpolsec.item_code','motorsect.item_code');
                })
                ->where('endt_renewal_no', $request->endt_renewal_no)
                ->where('reg_no', $reg_no)
                ->get();

            $groupPremiums =  DB::table('motorpolsec')
                ->where('endt_renewal_no', $request->endt_renewal_no)
                ->where('reg_no', $reg_no)
                ->groupBy('endt_renewal_no','reg_no','grp_code')
                ->sum('endorse_amount');

            $modtl = Modtlpivot::with('cover_type','class_type')
                ->where('endt_renewal_no', $request->endt_renewal_no)
                ->where('reg_no',$reg_no)
                ->first();
            $modtlsumm = Modtlsumm::where('endt_renewal_no', $request->endt_renewal_no)
                ->where('reg_no',$reg_no)
                ->first();

            $discounts = Discounts_loadings::where('endt_renewal_no', $request->endt_renewal_no)
                ->where('type', 'D')
                ->where('deleted', 'N')
                ->where('reg_no',$reg_no)
                ->get();

            $discountLoadings = Discounts_loadings::where('endt_renewal_no', $request->endt_renewal_no)
                ->where('reg_no',$reg_no)
                ->where('deleted', 'N')
                ->get();

        
            $loadings = Discounts_loadings::where('endt_renewal_no', $request->endt_renewal_no)
                ->where('type', 'L')
                ->where('deleted', 'N')
                ->where('reg_no',$reg_no)
                ->get();

            return [
                'status' => 1,
                'message' => 'Successful',
                'data' => [
                    'modtl' => $modtl,
                    'modtlsumm' => $modtlsumm,
                    'sections' => $sections,
                    'groupPremiums' => $groupPremiums,
                    'discounts' => $discounts,
                    'loadings' => $loadings,
                    'discountLoadings' => $discountLoadings,
                ]
            ];
        }catch(\Throwable $e){
            // dd($e);
            return [
                'status' => 0,
                'message' => 'Failed to load vehicle details'
            ];
        }
    }


    public function limits_datatable(Request $request)
    {

        $endt_renewal_no = $request->get('endt_renewal_no');
        $binder = 'N';

        if($request->has('open_cover') && $request->get('open_cover') == 'Y'){

            $trans = Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)->first();

        }
        else{

            $trans = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
            $binder = $trans->binder;

        }

        $limit_query = Pollimits::where('endt_renewal_no', $endt_renewal_no);

        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $class = ClassModel::where('class', $dcontrol->class)->first(); 
        if ($class->medical == 'Y') {
            $limit_query = Polmedsec::select('section_code as limit_no', 'description as detail_line', 'coverage as amount', 'member_no as location')
            ->where('endt_renewal_no', $endt_renewal_no);
        }
       
        if($binder != 'Y'){
            return datatables::of($limit_query)
                ->addColumn('action', function ($src) {
                
                        return '<a class="btn btn-xs" id="btn-limit-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-limit-delete"><i class="glyphicon glyphicon-minus"></i></a>';
                         
                })->addColumn('empty', function ($src) {
                    return '';
                })
                ->make(true);
        }else{
            return datatables::of($limit_query)
            ->addColumn('action', function ($src) {
              
                    return '';
                
            })->make(true);
        }
    }
    public function fetch_certificates($class, $dcontrol, $brnch)
    {
        $username = trim(Auth::user()->user_name);
        //CHECK CERT MASTER WHERE USERNAME
        $certificates = Certmast::whereRaw("trim(cert_user)='" . $username . "' AND (cert_status <> 99 AND cert_status <> 1 )")
                                ->whereRaw("trim(cert_type)='" . $class->cert_type . "'")->orderBy('cert_no')->take(50)->get();

        //AGENT
        if ( count($certificates)<1) {
            $certificates = Certmast::where('agent', $dcontrol[0]->agent)->whereRaw("trim(cert_type)='" . $class->cert_type . "'AND 
            (cert_status <> 99 AND cert_status <> 1 )")->where('BRANCH', $brnch)->orderBy('cert_no')->take(100)->get();
        }
        //BRANCH 
        if ( count($certificates)<1) {

            $certificates = Certmast::where('BRANCH', $brnch)->whereRaw("trim(cert_type)='" . $class->cert_type . "'AND 
            (cert_status <> 99 AND cert_status <> 1 )")->orderBy('cert_no')->take(250)->get();
        }

        //COMPANY
        if ( count($certificates)<1) {

            $certificates = Certmast::whereRaw("trim(cert_type)='" . $class->cert_type . "' AND (cert_status <> 99 or cert_status <> 1 )")->orderBy('cert_no')->take(500)->get();
        }

        return $certificates;
    }
    public function autocomplete_certificates(Request $request)
    {
        $username = trim(Auth::user()->user_name);

        $cert_endt = $request->get('cert_endt');
        $serial_no = $request->get('serial_no');

        $dcontrol_cls = Dcontrol::where('endt_renewal_no', $cert_endt)->get();
        $dcontrol_cls = $dcontrol_cls[0];

        $brnch = substr($dcontrol_cls->endt_renewal_no, 0, 3);
        $brnch = (int) $brnch;

        $class = ClassModel::where('class', $dcontrol_cls->class)->get();
        $class = $class[0];

        //CHECK CERT MASTER WHERE USERNAME
        $certificates = Certmast::whereRaw("trim(cert_user)='" . $username . "' AND (cert_status <> 99 AND cert_status <> 1 )")
                                ->whereRaw("trim(cert_type)='" . $class->cert_type . "'")->whereRaw("trim(cert_no) like '%".$serial_no."%'")
                                ->orderBy('cert_no')->take(50)->get();
        //AGENT
        if ( count($certificates)<1) {
            $certificates = Certmast::where('agent', $dcontrol_cls->agent)->whereRaw("trim(cert_type)='" . $class->cert_type . "'AND 
            (cert_status <> 99 AND cert_status <> 1 )")->whereRaw("trim(cert_no) like '%".$serial_no."%'")->where('BRANCH', $brnch)->orderBy('cert_no')->take(100)->get();
        }
        //BRANCH 
        if ( count($certificates)<1) {

            $certificates = Certmast::where('BRANCH', $brnch)->whereRaw("trim(cert_type)='" . $class->cert_type . "'AND 
            (cert_status <> 99 AND cert_status <> 1 )")->whereRaw("trim(cert_no) like '%".$serial_no."%'")->orderBy('cert_no')->take(250)->get();
        }

        //COMPANY
        if ( count($certificates)<1) {

            $certificates = Certmast::whereRaw("trim(cert_type)='" . $class->cert_type . "' AND (cert_status <> 99 or cert_status <> 1 )")
                                    ->whereRaw("trim(cert_no) like '%".$serial_no."%'")
                                    ->orderBy('cert_no')->take(500)->get();
                                    // dd($class->cert_type,$serial_no);

                                }
        
        $results = array();
        foreach($certificates as $certificate){
           $results[] = [
                            'value'=>$certificate->cert_no 
                        ];
       }

        return Response::Json($results);
    }

    public function clauses_datatable(Request $request)
    {
        $endt_no = $request->get('endt_renewal_no');
        $binder = 'N';

        if($request->has('open_cover') && $request->get('open_cover') == 'Y'){

            $trans = Marinemasterhist::where('endt_renewal_no', $endt_no)->first();

        }
        else{

            $trans = Dcontrol::where('endt_renewal_no', $endt_no)->first();
            $binder = $trans->binder;

        }

        $class = ClassModel::where('class',$trans->class)->first();
        $limit_query = Polclause::where('endt_renewal_no', $trans->endt_renewal_no);



        if($binder !='Y'){
            return datatables::of($limit_query)
                ->addColumn('action', function ($src) {
                  
                    return '<a class="btn btn-xs" id="btn-clause-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-clause-delete"><i class="glyphicon glyphicon-minus"></i></a>';
                    
                })->addColumn('empty', function ($src) {
                  
                   return ' ';
                
            })
                
                
                
                ->make(true);



            }else{
                return datatables::of($limit_query)
                ->addColumn('action', function ($limit_query) {
                  
                        return '';
                    
                    
                })->addColumn('empty', function ($src) {
                  
                    return ' ';
                 
             })
                ->make(true);
            }
    }

    public function excess_datatable(Request $request)
    {
        $pol_no = $request->get('policy_no');
        $endt_renewal_no = $request->get('endt_renewal_no');
        $binder = 'N';

        if($request->has('open_cover') && $request->get('open_cover') == 'Y'){

            $trans = Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)->first();

        }
        else{

            $trans = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
            $binder = $trans->binder;

        }

        $excess_query = Polexcess::where('endt_renewal_no', $trans->endt_renewal_no);
        
        if($binder != 'Y'){
            return datatables::of($excess_query)
                ->addColumn('action', function ($src) {
                  
                    return '<a class="btn btn-xs" id="btn-excess-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-excess-delete"><i class="glyphicon glyphicon-minus"></i></a>';
                    
                })->make(true);
            }else{
                return datatables::of($excess_query)
                ->addColumn('action', function ($src) {
                  
                        return '';
                    
                    
                })->make(true);
            }
    }


    public function narration_datatable(Request $request)
    {
        $endt_no = $request->get('endt_renewal_no');

        //$dcontrol=Dcontrol::where('endt_renewal_no',$endt_no)->get();
        //$dcontrol=$dcontrol[0];

        $limit_query = Debitdtl::where('endt_renewal_no', $endt_no)->get();



        if($dcontrol_cls->binder!='Y'){
            return datatables::of($limit_query)
                ->addColumn('action', function ($src) {
                  
                        return '<a class="btn btn-xs" id="btn-narration-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-narration-delete"><i class="glyphicon glyphicon-minus"></i></a>';
                    
                    
                })->make(true);
            }else{
                return datatables::of($limit_query)
                ->addColumn('action', function ($src) {
                  
                        return '';
                    
                    
                })->make(true);
            }
    }

    public function get_limit_details(Request $request)
    {
        $polmaster = Polmaster::where('endorse_no', $request->endt_no)->first();        
        $autolimit = Autolimits::where('class', $polmaster->class)->where('limit_no', $request->limit_no)->first();
        
        return $autolimit;

    }
    public function add_limit(Request $request)
    {
        
        if($request->limit_open_cover == 'Y'){

            $marine = new MarineOpenProcessing();

            $marine->add_marine_limit($request);

            return;

        }
        $combined = $request->combined;
        $limit_nos = $request->limit_descr_add;
        $polmaster = Polmasterend::where('endorse_no', $request->limit_endt_add)->first();
        $dcontrol = Dcontrol::where('endt_renewal_no', $request->limit_endt_add)->first();
        $comb_lmt_class = $request->comb_lmt_class;
        $endt_renewal_no = $dcontrol->endt_renewal_no;

        $docReg = Dcontrol::select('policy_no','trans_type','nil_code')
                            ->where('endt_renewal_no',$endt_renewal_no)
                            ->first();

        $data = array();

        if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

            $audit1 = [
                'policy_no'=>$docReg->policy_no,
                'endt_renewal_no'=>$endt_renewal_no,
                'risk_item'=> 'Policy limits',
                'table_name'=>'Pollimits',
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'system_user'=>Auth::user()->user_name
            ];

        }

        switch($combined) {
            case 'Y':
                DB::beginTransaction();

                try {
                    for ($i = 0; $i < count($limit_nos); $i++) {
                        if ($limit_nos[$i] != null) {
                            $countpollimits = Pollimits::where('endt_renewal_no', $endt_renewal_no)->count();
                            $pollimits = new Pollimits;
                            $count = 0;

                            if ($countpollimits > 0) {
                                $count = Pollimits::where('endt_renewal_no', $endt_renewal_no)->max('location');
                            }
                            
                            $autolimit = Autolimits::where('class', $comb_lmt_class[$i])
                            ->where('limit_no', $limit_nos[$i])
                            ->first();
                            
                            $next = (int)$count + 1;

                            $pollimits->policy_no       = $request->limit_pol_add;
                            $pollimits->endt_renewal_no = $endt_renewal_no;
                            $pollimits->detail_line     = $autolimit->description;
                            $pollimits->location        = $next;
                            $pollimits->sec_no          = $next;
                            $pollimits->limit_no        = $autolimit->limit_no;
                            $pollimits->amount          = str_replace(',','',$request->limit_amt_add[$i]);

                            # Add Class Details
                            //$pollimits->class           = $dcontrol->class;
                            $pollimits->comb_class      = $comb_lmt_class[$i];
                            //$pollimits->new_limit       = 'Y';
                            $pollimits->user_name = Auth::user()->user_name;
                            
                            $pollimits->save();

                            if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

                                if(!is_null(trim($autolimit->description))){
                                    $audit1['field_changed'] = 'Limit description';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim($autolimit->description);
                            
                                    array_push($data, $audit1);
                                }
                            
                                if(!is_null(trim(str_replace(',','',$request->limit_amt_add[$i])))){
                                    $audit1['field_changed'] = 'Limit amount';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim(str_replace(',','',$request->limit_amt_add[$i]));
                            
                                    array_push($data, $audit1);
                                }

                                DB::table('risk_audit_trail')->insert($data);

                            }
                        }
                    }

                    DB::commit();
                }catch(Exception $e){
                    // return json_encode(['response'=>2]);
                    DB::rollback();
                }
            break;

            default:

                            
                $limit_nos = $request->limit_descr_add;
                $polmaster = Polmaster::where('endorse_no', $request->limit_endt_add)->first();

                // class
                DB::beginTransaction();
                try{
                    foreach ($limit_nos as $key => $limit_no) {
                        if($limit_no != null){
                            $pollimits = new Pollimits;
                            $count = 0;
                            $countpollimits = Pollimits::where('endt_renewal_no', $request->limit_endt_add)->count();
                            if($countpollimits > 0){
                                $count = Pollimits::where('endt_renewal_no', $request->limit_endt_add)->max('location');
                            }
                            $autolimit = Autolimits::where('class', $polmaster->class)->where('limit_no', $limit_no)->first();
                            $next = (int)$count + 1;

                            $pollimits->policy_no = $request->limit_pol_add;
                            $pollimits->endt_renewal_no = $request->limit_endt_add;
                            $pollimits->detail_line = $autolimit->description;
                            $pollimits->location = $next;
                            $pollimits->sec_no = $next;
                            $pollimits->limit_no = $autolimit->limit_no;
                            $pollimits->amount = str_replace(',','',$request->limit_amt_add[$key]);
                            $pollimits->user_name = Auth::user()->user_name;
                            $pollimits->comb_class      = $polmaster->class;
                            
                            $pollimits->save();

                            if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

                                if(!is_null(trim($autolimit->description))){
                                    $audit1['field_changed'] = 'Limit description';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim($autolimit->description);
                            
                                    array_push($data, $audit1);
                                }
                            
                                if(!is_null(trim(str_replace(',','',$request->limit_amt_add[$key])))){
                                    $audit1['field_changed'] = 'Limit amount';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim(str_replace(',','',$request->limit_amt_add[$key]));
                            
                                    array_push($data, $audit1);
                                }

                                DB::table('risk_audit_trail')->insert($data);

                            }
                        }
                    }

                    DB::commit();
                }catch(Exception $e){
                    // return json_encode(['response'=>2]);
                    DB::rollback();
                }
                    break;
                }
    }

    public function add_new_limit(Request $request){
        if($request->limit_open_cover == 'Y'){

            $marine = new MarineOpenProcessing();

            $marine->add_marine_limit($request);

            return;

        }
        $combined = $request->combined;
        $limit_nos = $request->limit_descr_add;
        $polmaster = Polmasterend::where('endorse_no', $request->limit_endt_add)->first();
        $dcontrol = Dcontrol::where('endt_renewal_no', $request->limit_endt_add)->first();
        $comb_lmt_class = $request->comb_lmt_class;
        $endt_renewal_no = $dcontrol->endt_renewal_no;

        $docReg = Dcontrol::select('policy_no','trans_type','nil_code')
                            ->where('endt_renewal_no',$endt_renewal_no)
                            ->first();

        $data = array();

        if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

            $audit1 = [
                'policy_no'=>$docReg->policy_no,
                'endt_renewal_no'=>$endt_renewal_no,
                'risk_item'=> 'Policy limits',
                'table_name'=>'Pollimits',
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'system_user'=>Auth::user()->user_name
            ];

        }

        $customLimitStart = (int)Pollimits::where('endt_renewal_no',$endt_renewal_no)->max('limit_no')+1;

        switch($combined) {
            case 'Y':
                DB::beginTransaction();

                try {
                    for ($i = 0; $i < count($limit_nos); $i++) {
                        if ($limit_nos[$i] != null) {
                            $countpollimits = Pollimits::where('endt_renewal_no', $endt_renewal_no)->count();
                            $pollimits = new Pollimits;
                            $count = 0;

                            if ($countpollimits > 0) {
                                $count = Pollimits::where('endt_renewal_no', $endt_renewal_no)->max('location');
                            }
                            
                            // $autolimit = Autolimits::where('class', $comb_lmt_class[$i])
                            // ->where('limit_no', $limit_nos[$i])
                            // ->first();
                            
                            $next = (int)$count + 1;

                            $pollimits->policy_no       = $request->limit_pol_add;
                            $pollimits->endt_renewal_no = $endt_renewal_no;
                            $pollimits->detail_line     = trim($request->limit_descr_add[$i]);
                            $pollimits->location        = $next;
                            $pollimits->sec_no          = $next;
                            $pollimits->limit_no        = $customLimitStart;
                            $pollimits->amount          = str_replace(',','',$request->limit_amt_add[$i]);

                            # Add Class Details
                            //$pollimits->class           = $dcontrol->class;
                            $pollimits->comb_class      = $comb_lmt_class[$i];
                            //$pollimits->new_limit       = 'Y';
                            $pollimits->user_name = Auth::user()->user_name;
                            
                            $pollimits->save();

                            if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

                                if(!is_null(trim($autolimit->description))){
                                    $audit1['field_changed'] = 'Limit description';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim($request->limit_descr_add[$i]);
                            
                                    array_push($data, $audit1);
                                }
                            
                                if(!is_null(trim(str_replace(',','',$request->limit_amt_add[$i])))){
                                    $audit1['field_changed'] = 'Limit amount';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim(str_replace(',','',$request->limit_amt_add[$i]));
                            
                                    array_push($data, $audit1);
                                }

                                DB::table('risk_audit_trail')->insert($data);

                            }
                        }
                    }

                    DB::commit();
                }catch(Exception $e){
                    // return json_encode(['response'=>2]);
                    DB::rollback();
                }
            break;

            default:

                            
                $limit_nos = $request->limit_descr_add;
                $polmaster = Polmaster::where('endorse_no', $request->limit_endt_add)->first();
                

                // class
                DB::beginTransaction();
                try{
                    foreach ($limit_nos as $key => $limit_no) {
                        if($limit_no != null){
                            // $customLimitStart = 70000;
                            $pollimits = new Pollimits;
                            $count = 0;
                            $countpollimits = Pollimits::where('endt_renewal_no', $request->limit_endt_add)->count();

                            if($countpollimits > 0){
                                $count = Pollimits::where('endt_renewal_no', $request->limit_endt_add)->max('location');
                            }

                            $next = (int)$count + 1;

                            $pollimits->policy_no = $request->limit_pol_add;
                            $pollimits->endt_renewal_no = $request->limit_endt_add;
                            $pollimits->detail_line = trim($request->limit_descr_add[$key]);
                            $pollimits->location = $next;
                            $pollimits->sec_no = $next;
                            $pollimits->limit_no = $customLimitStart;
                            $pollimits->amount = str_replace(',','',$request->limit_amt_add[$key]);
                            $pollimits->user_name = Auth::user()->user_name;
                            $pollimits->comb_class = $polmaster->class;
                            
                            $pollimits->save();

                            if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

                                if(!is_null(trim($autolimit->description))){
                                    $audit1['field_changed'] = 'Limit description';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim($autolimit->description);
                            
                                    array_push($data, $audit1);
                                }
                            
                                if(!is_null(trim(str_replace(',','',$request->limit_amt_add[$key])))){
                                    $audit1['field_changed'] = 'Limit amount';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim(str_replace(',','',$request->limit_amt_add[$key]));
                            
                                    array_push($data, $audit1);
                                }

                                DB::table('risk_audit_trail')->insert($data);

                            }
                        }
                    }

                    DB::commit();
                }catch(Exception $e){
                    // return json_encode(['response'=>2]);
                    DB::rollback();
                    // dd($e);
                }
                    break;
                }
    }

    //addbinder limit
    public function addbinderlimit(Request $request){

        $count= DB::table('binderlimit')->where('policy_no',$request->binderpolno)->max('limit_no')+1;

        
        try{
         DB::table('binderlimit')->insert(
            [
            'policy_no' => $request->binderpolno, 
            'detail_line' => $request->limit_descr_add,
            'limit_no' => $count, 
            'amount' =>str_replace(',','',$request->limit_amt_add), 
            'user_name' => trim(Auth::user()->user_name), 
            
            ]
            );
         return json_encode(['response'=>1]);
     }catch(Exception $e){
        return json_encode(['response'=>2]);
     }

    }


    public function update_limit(Request $request)
    {
        $endt_renewal_no = $request->limit_endt; 
        $location = $request->limit_location; 
        $limit_no = $request->limit_item_no;
        $limit_cls = $request->limit_cls;

        $pollimit = array();

        $pollimit['amount'] = trim($request->get('limit_amt'));
        $pollimit['detail_line'] = trim($request->get('limit_descr'));

        $docReg = Dcontrol::select('policy_no','trans_type','nil_code')
                            ->where('endt_renewal_no',$endt_renewal_no)
                            ->first();

        if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

            $data = array();

            $pollimits = Pollimits::where('endt_renewal_no', $endt_renewal_no)
                                    ->where('location', $location)
                                    ->where('limit_no', $limit_no)
                                    ->first();
    
            $audit1 = [
                'policy_no'=>$docReg->policy_no,
                'endt_renewal_no'=>$endt_renewal_no,
                'risk_item'=> 'Policy limits',
                'table_name'=>'Pollimits',
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'system_user'=>Auth::user()->user_name
            ];
        
            if(trim($pollimits->detail_line) != trim($request->get('limit_descr'))){
                
                $audit1['field_changed'] = 'Limit description';
                $audit1['old_value'] = trim($pollimits->detail_line);
                $audit1['new_value'] = trim($request->get('limit_descr'));
    
                array_push($data, $audit1);
        
            }
            
            if(trim($pollimits->amount) != trim($request->get('limit_amt'))){
                
                $audit1['field_changed'] = 'Limit amount';
                $audit1['old_value'] = trim($pollimits->amount);
                $audit1['new_value'] = trim($request->get('limit_amt'));
    
                array_push($data, $audit1);
        
            }

        }
        
        DB::transaction(function () use ($endt_renewal_no, $location, $limit_no, $limit_cls, $data, $pollimit, $docReg) {

            if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {
                DB::table('risk_audit_trail')->insert($data);
            }
    
            if (!empty($pollimit)) {
                Pollimits::where('endt_renewal_no', $endt_renewal_no)
                        ->where('location', $location)
                        ->where('limit_no', $limit_no)
                        ->where('comb_class', $limit_cls)
                        ->update($pollimit);
            }
        });


    }
    public function update_binder_limit(Request $request){

            DB::table('binderlimit')->where('policy_no',$request->policyno)->where('limit_no', $request->limit_item_no)
                                    ->update([
                                        'amount'=>str_replace(',','',$request->limit_amt),
                                        'detail_line'=>$request->limit_descr
                                    ]);
    }

    public function delete_limit(Request $request)
    {
        $endt_renewal_no = $request->limit_endt_del; 
        $location = $request->limit_location_del; 
        $limit_no = $request->limit_item_no_del;
        $limit_cls = $request->limit_cls_del;
        $limit_cmb_del = $request->limit_cmb_del;


        $docReg = Dcontrol::select('policy_no','trans_type','nil_code')
                            ->where('endt_renewal_no',$endt_renewal_no)
                            ->first();

        $pollimits = Pollimits::where('endt_renewal_no', $endt_renewal_no)
                                    ->where('location', $location)
                                    ->where('limit_no', $limit_no)
                                    ->where('comb_class', $limit_cls)
                                    ->first();
    
        $data = array();

        if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

            $audit1 = [
                'policy_no'=>$docReg->policy_no,
                'endt_renewal_no'=>$endt_renewal_no,
                'risk_item'=> 'Policy limits',
                'table_name'=>'Pollimits',
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'system_user'=>Auth::user()->user_name
            ];

            if(!is_null(trim($pollimits->detail_line))){
                $audit1['field_changed'] = 'Limit description';
                $audit1['old_value'] = trim($pollimits->detail_line);
                $audit1['new_value'] = '';
        
                array_push($data, $audit1);
            }
        
            if(!is_null(trim($pollimits->amount))){
                $audit1['field_changed'] = 'Limit amount';
                $audit1['old_value'] = trim($pollimits->amount);
                $audit1['new_value'] = '';
        
                array_push($data, $audit1);
            }

        }

        

    
        DB::transaction(function () use ($endt_renewal_no, $location, $limit_no, $limit_cls, $data, $docReg, $pollimits) {

            if ($docReg->trans_type == 'NIL' 
                && $docReg->nil_code == 8
                && (!empty($pollimits))
                ) {
                DB::table('risk_audit_trail')->insert($data);
            }
    
            Pollimits::where('endt_renewal_no', $endt_renewal_no)
                    ->where('location', $location)
                    ->where('limit_no', $limit_no)
                    ->where('comb_class', $limit_cls)
                    ->delete();
        });
        
    }

    public function deletebinderlimit(Request $request){
        DB::table('binderlimit')->where('policy_no',$request->limitpolicy_no)
                                ->where('limit_no',$request->limitno)
                                ->delete();
    }


    //clauses for policy

    public function addpolclause(Request $request)
    {
            $class = Binderpol::where('binder_pol_no', $request->clause_endt_add)->first()->class; 
            $dept = Classmodel::where('class', $class)
                    ->first()->dept;

            $policy_no=$request->clause_endt_add;
            $clauses = $request->clause_add;

            // remove any blanks that come with the request
            $clauses = array_filter($clauses, function($clause){
                return $clause != "" and isset($clause );
            });
            $clauses = implode(',',$clauses);
            
            $selected_clauses = Clauses::where('dept',$dept)
                ->whereRaw("trim(clause) IN ($clauses)")
                ->get();
            
            $countMe = 0;
            $countX = '';


            foreach ($selected_clauses as $clause) {

                DB::table('binderclause')->insert([
                        'policy_no'=>$policy_no,
                        'dept_fil'=>$dept,
                        'clause'=>trim($clause->clause),
                        'description'=>$clause->description,
                        'user_str'=>trim(Auth::user()->user_name),
                        'class'=>$class,
                ]);
            }
    }




    //update
     public function updatepolclause(Request $request)
    {

        DB::table('binderclause')->where('policy_no',$request->clause_endt)
                                ->where('clause',$request->clause_code_ed)
                                ->update(['description'=>$request->clause_descr]);
    }


    //delete
     public function deletepolclause(Request $request)
    {
        
          return DB::table('binderclause')->where('policy_no',$request->clause_endt_del)
                                ->where('clause',$request->clause_code_del)
                                ->delete(); 
    
    }

    public function new_binder_clause(Request $request){
        $policy_no=$request->clause_endt_add;
        $clause_id = DB::table('binderclause')
            ->where('class', $request->classModel)
            ->where('policy_no', $policy_no)
            ->max('clause') + 1;
        $class=DB::table('class')
            ->where('class', $request->classModel)
            ->first();
        
        DB::table('binderclause')->insert([
            'policy_no'=>$policy_no,
            'dept_fil'=>$class->dept,
            'clause'=>$clause_id,
            'description'=>$request->add_new_clause,
            'user_str'=>trim(Auth::user()->user_name),
            'class'=>$class->class,
        ]);
    }
    public function fetchclauses(Request $request){
        $dept=DB::table('class')
                ->where('class',$request->classModel)
                ->first()->dept;

               

        return Clauses::where('dept', $dept)
                ->selectRaw('TRIM(description) as description, clause, apply_basis')
                ->get();


    }

    //end of clauses for policy

    public function addpolexcess(Request $request)
    {
        try {
            $count = DB::table('binderexcess')
                ->where('policy_no', $request->excess_pol_add)
                ->max('item_no') + 1;

            if (is_array($request->excess_item_no)) {
                foreach ($request->excess_item_no as $key => $item_no) {
                    $clsexcess_get = Classexces::whereRaw('trim(item_no) = ?', [$item_no])->first();

                    if (!$clsexcess_get) {
                        continue;
                    }

                    $rate = isset($request->excess_rate_add[$key]) ? (float) str_replace(',', '', $request->excess_rate_add[$key]) : 0;
                    $maxExcess = isset($request->excess_max_amt[$key]) ? (float) str_replace(',', '', $request->excess_max_amt[$key]) : 0;
                    $minExcess = isset($request->excess_min_amt[$key]) ? (float) str_replace(',', '', $request->excess_min_amt[$key]) : 0;

                    DB::table('binderexcess')->insert([
                        'policy_no'       => $request->excess_pol_add,
                        'DESCRIPTION'     => $clsexcess_get->description,
                        'load_discount'   => 'L',
                        'item_no'         => $count++,
                        'item_no1'        => $item_no,
                        'based_on'        => $request->excess_basis[$key] ?? null,
                        'rate'            => $rate,
                        'base'            => $request->excess_base_add[$key] ?? null,
                        'basic_amount'    => 0,
                        'basic_rate'      => 0,
                        'balance_amount'  => 0,
                        'balance_rate'    => 0,
                        'excess_per'      => 0,
                        'electrical'      => 'N',
                        'maximum_excess'  => $maxExcess,
                        'minimum_excess'  => $minExcess,
                        'curr'            => 0,
                        'user_fill'       => 0,
                    ]);
                }
            }

            return response()->json(['message' => 'Excess entries added successfully']);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while adding the excess entries.',
                'error'   => $e->getMessage(),
            ], 500);
        }
    }  

    public function updatepolexcess(Request $request){

        

         DB::table('binderexcess')
                            ->where('policy_no',$request->excess_pol_ed)
                            ->where('item_no',$request->excess_itemno_ed)->
                            update([
                                    'rate'=>$request->excess_rate_ed,
                                    'base'=>$request->excess_base_ed,
                                    'minimum_excess'=>$request->excess_minc_ed,
                                    'DESCRIPTION'=>$request->excess_descr_ed,
                                    'maximum_excess'=>$request->excess_maxc_ed

                            ]);
    }

    public function deletepolexcess(Request $request){
        DB::table('binderexcess')
                            ->where('policy_no',$request->excess_pol_del)
                            ->where('item_no',$request->excess_item_no1_del)
                            ->delete();
        
    }

    // auto-confirm clauses when adding clauses for the first time
    public function confirm_clause($endt_renewal_no){
        Dcontrol::where('endt_renewal_no', $endt_renewal_no)
        ->update([
            'confirm_clauses'   => 'Y',
        ]);
    }


    //end of excess

    public function add_clause(Request $request)
    {
        try {
               // dd($request);
            $clause_add = $request->clause_add;
            
            DB::beginTransaction();
            //$new_clause_add_text = array_map('trim', explode(',', $request->clause_add_text));

            //$array_clause = array_combine($request->clause_add, $new_clause_add_text);

            $count_polclause = Polclause::where('endt_renewal_no', $request->clause_endt_add)->count();


            

            if($request->clause_open_cover == 'Y'){

                $rec = Marinemasterhist::where('endt_renewal_no', $request->clause_endt_add)->first();

            }
            else{

                $rec = Dcontrol::where('endt_renewal_no', $request->clause_endt_add)->first();

            }

            //$countMe = 0;
            $serial = 1;
            $countX = '';
            $classModel = ClassModel::where('class', $rec->class)->first();

            if ($classModel->combined == 'Y') {
    
                $combClass = $request->comb_class;
            } else {

                $combClass = $rec->class;
            }

            $endt_renewal_no = $request->clause_endt_add;

            $docReg = Dcontrol::select('policy_no','trans_type','nil_code')
                            ->where('endt_renewal_no',$endt_renewal_no)
                            ->first();

            $data = array();

            if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

                $audit1 = [
                    'policy_no'=>$docReg->policy_no,
                    'endt_renewal_no'=>$endt_renewal_no,
                    'risk_item'=> 'Policy clauses',
                    'table_name'=>'Polclause',
                    'date_changed'=>Carbon::now('EAT'),
                    'ip_address'=>$request->ip(),
                    'system_user'=>Auth::user()->user_name
                ];

            }

            //foreach ($array_clause as $c_add_key => $c_add_text) {
            foreach($clause_add as $cls_add){
                $clause_det = explode('->',$cls_add);

                #skip if exists
                $ch_d = Polclause::where('endt_renewal_no', $request->clause_endt_add)
                                  ->where('comb_class',$combClass)
                                  ->whereRaw("trim(clause) ='".trim($clause_det[0])."'")
                                  ->exists();
                
                if($ch_d){
                   
                    continue ;
                    
                }

                $pcode = new Polclause;
                $pcode->policy_no = $request->clause_pol_add;
                $pcode->location = 0;
                $pcode->dept = $classModel->dept;
                $pcode->dept_fil = 0;
                $pcode->clause = trim($clause_det[0]);
                $pcode->s_code = 0;
                $pcode->description = trim($clause_det[1]);
                $pcode->serial = $serial;
                $pcode->user_str = auth()->user()->user_name;
                $pcode->endt_renewal_no = $request->clause_endt_add;
                $pcode->class = $rec->class;
                $pcode->comb_class = $combClass;
                //$countMe ++;
                $serial++;
                $countX .= 'Entry '. $serial . ' has been saved';

                $pcode->save();


                if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

                    if(!is_null(trim($c_add_text))){

                        $audit1['field_changed'] = 'Clause description';
                        $audit1['old_value'] = '';
                        $audit1['new_value'] = trim($clause_det[1]);
                
                        array_push($data, $audit1);

                    }
                
                }
            }

            if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {
                DB::table('risk_audit_trail')->insert($data);
            }

            DB::commit();

           
            if ($count_polclause == 0){
                $this->confirm_clause($request->clause_endt_add);
            }

            return $countX;
        } catch (\Exception $e) {
            DB::rollback();
            
            return $e->getMessages();
        }
    }

    # fetch excesses belonging to a combined class member
    public function combinedClassExcesses(Request $request) {

        $cls = $request->classModel;
        $endt_renewal_no = $request->endt_renewal_no;

        $existing_excess = Polexcess::select('item_no')->where('endt_renewal_no',$endt_renewal_no)->where('comb_class',$cls)->get()->toArray();

        $excesses = Classexces::where('class', $cls)->whereNotIn('item_no',$existing_excess)->get();

        return response()->json($excesses, 200);
    }

   

    public function add_new_clause(Request $request) {
        try {
            $combined = $request->combined;
            switch($combined) {
                case 'Y':
                    DB::beginTransaction();
    
                    try {
                        $new_clause_add_text = $request->add_new_clause;
                        $rec = Dcontrol::where('endt_renewal_no', $request->clause_endt_add)->first();
                        $count=(int)Polclause::where('endt_renewal_no',$request->clause_endt_add)->max('clause')+1;
    
                        $pcode = new Polclause;
                        $pcode->policy_no = $request->clause_pol_add_new;
                        $pcode->location = 0;
                        $pcode->dept = $rec->dept;
                        $pcode->dept_fil = 0;
                        $pcode->clause = $count;
                        $pcode->s_code = 0;
                        $pcode->description = $new_clause_add_text;
                        $pcode->serial = 4;
                        $pcode->user_str = auth()->user()->user_name;
                        $pcode->endt_renewal_no = $request->clause_endt_add;
                        $pcode->class = $rec->class;
                        $pcode->comb_class = $request->comb_clause_new;
    
                        $countMe ++;                
                        $pcode->save();
                        DB::commit();
                    }catch(Exception $e){
                        // return json_encode(['response'=>2]);
                        DB::rollback();
                    }
                break;
    
                default:
                    try {
                        DB::beginTransaction();
                        $new_clause_add_text = $request->add_new_clause;
                        $rec = Dcontrol::where('endt_renewal_no', $request->clause_endt_add)->first();
                        $count=(int)Polclause::where('endt_renewal_no',$request->clause_endt_add)->max('clause')+1;

                        $pcode = new Polclause;
                        $pcode->policy_no = $request->clause_pol_add_new;
                        $pcode->location = 0;
                        $pcode->dept = $rec->dept;
                        $pcode->dept_fil = 0;
                        $pcode->clause = $count;
                        $pcode->s_code = 0;
                        $pcode->description = $new_clause_add_text;
                        $pcode->serial = 4;
                        $pcode->user_str = auth()->user()->user_name;
                        $pcode->endt_renewal_no = $request->clause_endt_add;
                        $pcode->class = $rec->class;

                        $countMe ++;                
                        $pcode->save();
                        DB::commit();

                        $count_polclause = Polclause::where('endt_renewal_no', $request->clause_endt_add)->count();
                        if ($count_polclause == 1){
                            $this->confirm_clause($request->clause_endt_add);
                        }
                    } catch (\Throwable $e) {
                        //throw $th;
                        DB::rollback();     
                    }
            }

            return $countX;
        } catch (\Exception $e) {
            DB::rollback();
            report($e);

            return $e->getMessages();
        }
    }

    public function update_clause(Request $request)
    {
        $endt_renewal_no = $request->clause_endt; 
        $clause_cls = $request->clause_cls_ed;

        $policyclause = array();

        $policyclause['description'] = trim($request->clause_descr);

        $docReg = Dcontrol::select('policy_no','trans_type','nil_code')
                            ->where('endt_renewal_no',$endt_renewal_no)
                            ->first();

        if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

            $data = array();

            $polclause = Polclause::where('endt_renewal_no', $endt_renewal_no)
                                  ->where('comb_class',$clause_cls)
                                  ->whereRaw("trim(clause)='" . $request->clause_code_ed . "'")
                                  ->first();
    
            $audit1 = [
                'policy_no'=>$docReg->policy_no,
                'endt_renewal_no'=>$endt_renewal_no,
                'risk_item'=> 'Policy clauses',
                'table_name'=>'Polclause',
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'system_user'=>Auth::user()->user_name
            ];
        
            if(trim($polclause->description) != trim($request->get('clause_descr'))){
                
                $audit1['field_changed'] = 'Clause description';
                $audit1['old_value'] = trim($polclause->description);
                $audit1['new_value'] = trim($request->clause_descr);
    
                array_push($data, $audit1);
        
            }
        }
        
        DB::transaction(function () use ($endt_renewal_no,$data,$clause_cls,$policyclause, $docReg, $request) {

            if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {
                DB::table('risk_audit_trail')->insert($data);
            }
    
            if (!empty($policyclause)) {
               $pol = polclause::where('endt_renewal_no', $endt_renewal_no)
                        //  ->where('comb_class',$clause_cls)
                        ->where(function($query) use ($clause_cls){
                            $query->where('comb_class', $clause_cls)
                                    ->orWhereNull('comb_class');
                        })
                         ->whereRaw("trim(clause)='" . $request->clause_code_ed . "'")
                         ->update($policyclause);
                        
            }
        });

    }

    

    # fetch limits belonging to a combined class member
    public function combinedClassLimits(Request $request) {
        $cls = $request->classModel;
        $endt_renewal_no = $request->endt_renewal_no;

        $existing_limits = Pollimits::select('limit_no')->where('endt_renewal_no',$endt_renewal_no)->where('comb_class',$cls)->get()->toArray();

        $class = ClassModel::where('class',$cls)->first();

        $limits = Autolimits::where('class', $class->class)->whereNotIn('limit_no',$existing_limits)->get();
        
        return response()->json($limits, 200);
    }

    public function delete_clause(Request $request)
    {
        $clause_cls_del = $request->clause_cls_del;
        $docReg = Dcontrol::select('policy_no','trans_type','nil_code')
                            ->where('endt_renewal_no',$request->clause_endt_del)
                            ->first();

        $polclause = Polclause::where('endt_renewal_no', $request->clause_endt_del)
                            ->whereRaw("trim(clause)='" . $request->clause_code_del . "'")
                            ->first();

        $data = array();

        if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

            $audit1 = [
                'policy_no'=>$docReg->policy_no,
                'endt_renewal_no'=>$request->clause_endt_del,
                'risk_item'=> 'Policy clauses',
                'table_name'=>'Polclause',
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'system_user'=>Auth::user()->user_name
            ];

            if(!is_null(trim($polclause->description))){
                $audit1['field_changed'] = 'Clause description';
                $audit1['old_value'] = trim($polclause->description);
                $audit1['new_value'] = '';
        
                array_push($data, $audit1);
            }

        }
    
        DB::transaction(function () use ($request, $docReg,$data, $polclause, $clause_cls_del) {

            if ($docReg->trans_type == 'NIL' 
                && $docReg->nil_code == 8
                && (!empty($polclause))
                ) {
                DB::table('risk_audit_trail')->insert($data);
            }

    
            Polclause::where('endt_renewal_no', $request->clause_endt_del)
                    // ->where('comb_class', $request->clause_cls_del)
                    ->where(function($query) use ($clause_cls_del){
                        $query->where('comb_class', $clause_cls_del)
                                ->orWhereNull('comb_class');
                    })
                    ->whereRaw("trim(clause)='" . $request->clause_code_del . "'")
                    ->delete();
        });
        

        $count_polclause = Polclause::where('endt_renewal_no', $request->clause_endt_del)
                                    ->count();

        if ($count_polclause < 1) {
            Dcontrol::where('endt_renewal_no', trim($request->clause_endt_del))->update([
                'confirm_clauses' => 'N'
            ]);

            return 'empty';
        }
    }

    public function fetch_excessrates(Request $request)
    {
        
        if($request->has('open_cover')){

            $rec = Marinemasterhist::where('endt_renewal_no', $request->endt_no)->first();

        }else{

            $rec = Polmaster::where('policy_no', $request->polno)->first();

        }
        
        $class = ClassModel::where('class', $rec->class)->first();

        // if($class->motor_policy == 'Y'){
        //     $excessdetails = Classexces::where('class', $rec->class)->where('item_no', $request->item_no)->where('cover_type', $request->covtype)->get();
        // }
        // else{
        //     $excessdetails = Classexces::where('class', $rec->class)->where('item_no', $request->item_no)->get();
        // }
        
        $excessdetails = Classexces::where('class', $rec->class)->where('item_no', $request->item_no)->get();

        echo json_encode($excessdetails);
        
    }
    public function fetch_polexcessrates(Request $request)
    {
        
        $class=DB::table('class')
                ->where('description', 'like', '%'.$request->classdesc.'%')
                ->first()->class;
             
        $upd = Classexces::where('class',$class)->get();
       
        echo  json_encode($upd);
    }
    public function binder_polexcessrates(Request $request)
    {
        
        $class=DB::table('class')
                ->where('class',$request->classModel)
                ->first()->class;
        // return $request;
             
        $upd = Classexces::where('class',$class)->get();
       
        echo  json_encode($upd);
    }
    public function add_excess(Request $request){

        $policy_no = $request->excess_pol_add;

        if($request->excess_open_cover == 'Y'){

            $marine = new MarineOpenProcessing();

            $marine->add_marine_excess($request);

            return;

        }

        $rec = Polmaster::select('endorse_no','policy_no','class')
                        ->where('policy_no', $policy_no)
                        ->first();

        $docReg = Dcontrol::select('policy_no','trans_type','nil_code','endt_renewal_no')
                            ->where('endt_renewal_no',$rec->endorse_no)
                            ->first();

        $polmaster = Polmasterend::where('policy_no', $policy_no)
                                ->where('endorse_no', $docReg->endt_renewal_no)
                                ->first();

        if($polmaster->class==null){
            $polmasterx = Polmaster::where('policy_no', $policy_no)->first();
            $class=$polmasterx->class;
        }else{
            $class=$polmaster->class;
        }
    
        $class = ClassModel::where('class', $class)->first();
        $excess_item_nos = $request->excess_item_no;

        $dcontrol = Dcontrol::where('endt_renewal_no', $docReg->endt_renewal_no)->first();

        $endt_renewal_no = $dcontrol->endt_renewal_no;

        $data = array();

        if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

            $audit1 = [
                'policy_no'=>$docReg->policy_no,
                'endt_renewal_no'=>$endt_renewal_no,
                'risk_item'=> 'Policy excess',
                'table_name'=>'Polexcess',
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'system_user'=>Auth::user()->user_name
            ];

        }

        if ($class->combined == 'Y') {
            DB::beginTransaction();
            $combinedClass = $request->comb_lmt_class;
            try {
                foreach ($excess_item_nos as $key => $excess_item_no) {
                    if($excess_item_no != null){
                        $rec = Polmaster::where('policy_no', $policy_no)->get();
                        $countexcess = Polexcess::where('endt_renewal_no', $endt_renewal_no)
                                                ->where('class', $rec[0]->class)
                                                ->count();

                        $checkExcess = Polexcess::where('endt_renewal_no', $endt_renewal_no)
                                                ->where('comb_class', $combinedClass[$key])
                                                ->where('item_no', $excess_item_no)
                                                ->count();

                        if($countexcess > 0){
                            $count = Polexcess::where('endt_renewal_no', $endt_renewal_no)
                                                ->where('class', $rec[0]->class)
                                                ->max('item_no1');
                        }else{
                            $count = 0;
                        }

                        $next = (int)$count + 1;
                        $polexcess = new Polexcess;

                        $polexcess->policy_no       = $policy_no;
                        $polexcess->class           = $rec[0]->class;
                        $polexcess->comb_class      = $combinedClass[$key];
                        $polexcess->endt_renewal_no = $endt_renewal_no;
                        $polexcess->location        = 1;
                        $polexcess->item_no1        = $next;
                        $polexcess->item_no         = $excess_item_no;
                        $polexcess->description     = $request->excess_item_descr[$key];
                        $polexcess->load_discount   = "L";
                        $polexcess->based_on        = $request->excess_basis[$key];
                        $polexcess->rate            = $request->excess_rate_add[$key];
                        $polexcess->base            = $request->excess_base_add[$key];
                        $polexcess->minimum_excess  = str_replace(',','',$request->excess_min_amt[$key]);
                        $polexcess->excess_per      = $request->excess_rate_add[$key];
                        $polexcess->amount          = 0;
                        $polexcess->basic_amount    = 0;
                        $polexcess->basic_rate      = 0;
                        $polexcess->balance_amount  = 0;
                        $polexcess->balance_rate    = 0;
                        $polexcess->electrical      = 0;
                        $polexcess->curr            = 0;
                        $polexcess->user_fill       = Auth::user()->user_name;
                        $polexcess->maximum_excess  = str_replace(',','',$request->excess_max_amt[$key]);

                        if($checkExcess == 0){

                            $polexcess->save();

                            if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

                                if(!empty(trim($request->excess_item_descr[$key]))){
                                    $audit1['field_changed'] = 'Excess description';
                                    $audit1['old_value'] = trim($request->excess_item_descr[$key]);
                                    $audit1['new_value'] = '';
                            
                                    array_push($data, $audit1);
                                }
                                
                                if(!is_null(trim($request->excess_rate_add[$key]))){
                                    $audit1['field_changed'] = 'Excess rate';
                                    $audit1['old_value'] = trim($request->excess_rate_add[$key]);
                                    $audit1['new_value'] = '';
                            
                                    array_push($data, $audit1);
                                }
                                
                                if(!is_null(trim($request->excess_base_add[$key]))){
                                    $audit1['field_changed'] = 'Excess base';
                                    $audit1['old_value'] = trim($request->excess_base_add[$key]);
                                    $audit1['new_value'] = '';
                            
                                    array_push($data, $audit1);
                                }
                                
                                if(!empty(trim($request->excess_basis[$key]))){
                                    $audit1['field_changed'] = 'Excess based_on';
                                    $audit1['old_value'] = trim($request->excess_basis[$key]);
                                    $audit1['new_value'] = '';
                            
                                    array_push($data, $audit1);
                                }
                                
                                if(!is_null(trim(str_replace(',','',$request->excess_min_amt[$key])))){
                                    $audit1['field_changed'] = 'Excess minimum_excess';
                                    $audit1['old_value'] = trim(str_replace(',','',$request->excess_min_amt[$key]));
                                    $audit1['new_value'] = '';
                            
                                    array_push($data, $audit1);
                                }
                                
                                if(!is_null(trim(str_replace(',','',$request->excess_max_amt[$key])))){
                                    $audit1['field_changed'] = 'Excess maximum_excess';
                                    $audit1['old_value'] = trim(str_replace(',','',$request->excess_max_amt[$key]));
                                    $audit1['new_value'] = '';
                            
                                    array_push($data, $audit1);
                                }

                                DB::table('risk_audit_trail')->insert($data);

                                $checkExcess = 0;

                            }
                        }
                    }
                }

                DB::commit();
            } catch (\Throwable $e) {
                //dd($e);
                DB::rollback();
            }
        } else {

            $excess_item_nos = $request->excess_item_no;
            
            DB::beginTransaction();
            try{
                foreach ($excess_item_nos as $key => $excess_item_no) {
                    if($excess_item_no != null){
                        $polexcess = new Polexcess;
                        $rec = Polmaster::where('policy_no', $request->excess_pol_add)->get();
                        $countexcess = Polexcess::where('endt_renewal_no', $rec[0]->endorse_no)->where('class', $rec[0]->class)->count();
                        $checkExcess = Polexcess::where('endt_renewal_no', $rec[0]->endorse_no)->where('class', $rec[0]->class)->where('item_no', $excess_item_no)->count();
                        if($countexcess > 0){
                            $count = Polexcess::where('endt_renewal_no', $rec[0]->endorse_no)->where('class', $rec[0]->class)->max('item_no1');
                        }else{
                            $count = 0;
                        }
                        $next = (int)$count + 1;
                        $polexcess->policy_no = $request->excess_pol_add;
                        $polexcess->class = $rec[0]->class;
                        $polexcess->comb_class = $rec[0]->class;
                        $polexcess->endt_renewal_no = $rec[0]->endorse_no;
                        $polexcess->location = 1;
                        $polexcess->item_no1 = $next;
                        $polexcess->item_no = $request->excess_item_no[$key];
                        $polexcess->description = $request->excess_item_descr[$key];
                        $polexcess->load_discount = "L";
                        $polexcess->based_on = $request->excess_basis[$key];
                        $polexcess->rate = $request->excess_rate_add[$key];
                        $polexcess->base = $request->excess_base_add[$key];
                        $polexcess->minimum_excess = $request->excess_min_amt[$key];
                        $polexcess->excess_per = $request->excess_rate_add[$key];
                        $polexcess->amount = 0;
                        $polexcess->basic_amount = 0;
                        $polexcess->basic_rate = 0;
                        $polexcess->balance_amount = 0;
                        $polexcess->balance_rate = 0;
                        $polexcess->electrical = 0;
                        $polexcess->curr = 0;
                        $polexcess->user_fill = Auth::user()->user_name;
                        $polexcess->maximum_excess = str_replace(',','',$request->excess_max_amt[$key]);

                        if($checkExcess == 0){

                            $polexcess->save();

                            if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

                                if(!empty(trim($request->excess_item_descr[$key]))){
                                    $audit1['field_changed'] = 'Excess description';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim($request->excess_item_descr[$key]);
                            
                                    array_push($data, $audit1);
                                }
                                
                                if(!is_null(trim($request->excess_rate_add[$key]))){
                                    $audit1['field_changed'] = 'Excess rate';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim($request->excess_rate_add[$key]);
                            
                                    array_push($data, $audit1);
                                }
                                
                                if(!is_null(trim($request->excess_base_add[$key]))){
                                    $audit1['field_changed'] = 'Excess base';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim($request->excess_base_add[$key]);
                            
                                    array_push($data, $audit1);
                                }

                                if(!empty(trim($request->excess_basis[$key]))){
                                    $audit1['field_changed'] = 'Excess based_on';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim($request->excess_basis[$key]);
                            
                                    array_push($data, $audit1);
                                }
                                
                                if(!is_null(trim(str_replace(',','',$request->excess_min_amt[$key])))){
                                    $audit1['field_changed'] = 'Excess minimum_excess';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim(str_replace(',','',$request->excess_min_amt[$key]));
                            
                                    array_push($data, $audit1);
                                }
                                
                                if(!is_null(trim(str_replace(',','',$request->excess_max_amt[$key])))){
                                    $audit1['field_changed'] = 'Excess maximum_excess';
                                    $audit1['old_value'] = '';
                                    $audit1['new_value'] = trim(str_replace(',','',$request->excess_max_amt[$key]));
                            
                                    array_push($data, $audit1);
                                }

                                DB::table('risk_audit_trail')->insert($data);

                            }
                        }
                    }
                }

                DB::commit();
            }catch (\Throwable $e) {
                //dd($e);
                DB::rollback();
            }
        }
    }


    public function update_excess(Request $request)
    {
        $status = -1;

        if($request->excess_open_cover_ed == 'Y'){

            $marine = new MarineOpenProcessing();

            $status = $marine->update_marine_excess($request);

            return $status;

        }

        $excess_cls = $request->excess_cls_ed;

        $rec = Polmaster::select('endorse_no','policy_no','class')
                        ->where('policy_no', $request->excess_pol_ed)
                        ->first();

        $docReg = Dcontrol::select('policy_no','trans_type','nil_code','class','endt_renewal_no')
                            ->where('endt_renewal_no',$rec->endorse_no)
                            ->first();

        $policyexcess = array();

        $policyexcess['description'] = trim($request->get('excess_descr_ed'));
        $policyexcess['rate'] = trim($request->get('excess_rate_ed'));
        $policyexcess['base'] = trim($request->get('excess_base_ed'));
        $policyexcess['based_on'] = trim($request->get('excess_basis_ed'));
        $policyexcess['minimum_excess'] = str_replace(',','',$request->excess_minc_ed);
        $policyexcess['maximum_excess'] = str_replace(',','',$request->excess_maxc_ed);

        if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

            $data = array();

            $polexcess = Polexcess::where('endt_renewal_no', $docReg->endt_renewal_no)
                                  ->where('class', $docReg->class)
                                  ->where('comb_class', $excess_cls)
                                  ->where('item_no1', $request->excess_itemno_ed)
                                  ->first();


            $audit1 = [
                'policy_no'=>$docReg->policy_no,
                'endt_renewal_no'=> $rec->endorse_no,
                'risk_item'=> 'Policy excess',
                'table_name'=>'Polexcess',
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'system_user'=>Auth::user()->user_name
            ];

            if(trim($polexcess->description) != $policyexcess['description']){
                $audit1['field_changed'] = 'Excess description';
                $audit1['old_value'] = trim($polexcess->description);
                $audit1['new_value'] = $policyexcess['description'];
        
                array_push($data, $audit1);
            }
            
            if(trim($polexcess->rate) != $policyexcess['rate']){
                $audit1['field_changed'] = 'Excess rate';
                $audit1['old_value'] = trim($polexcess->rate);
                $audit1['new_value'] = $policyexcess['rate'];
        
                array_push($data, $audit1);
            }
            
            if(trim($polexcess->base) != $policyexcess['base']){
                $audit1['field_changed'] = 'Excess base';
                $audit1['old_value'] = trim($polexcess->base);
                $audit1['new_value'] = $policyexcess['base'];
        
                array_push($data, $audit1);
            }
            
            if(trim($polexcess->based_on) != $policyexcess['based_on']){
                $audit1['field_changed'] = 'Excess based_on';
                $audit1['old_value'] = trim($polexcess->based_on);
                $audit1['new_value'] = $policyexcess['based_on'];
        
                array_push($data, $audit1);
            }
            
            if(trim($polexcess->minimum_excess) != $policyexcess['minimum_excess']){
                $audit1['field_changed'] = 'Excess minimum_excess';
                $audit1['old_value'] = trim($polexcess->minimum_excess);
                $audit1['new_value'] = $policyexcess['minimum_excess'];
        
                array_push($data, $audit1);
            }
            
            if(trim($polexcess->maximum_excess) != $policyexcess['maximum_excess']){
                $audit1['field_changed'] = 'Excess maximum_excess';
                $audit1['old_value'] = trim($polexcess->maximum_excess);
                $audit1['new_value'] = $policyexcess['maximum_excess'];
        
                array_push($data, $audit1);
            }

        }
    
        DB::transaction(function () use ($request, $docReg,$data, $excess_cls, &$status,$policyexcess) {

            if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) 
            {
                DB::table('risk_audit_trail')->insert($data);
            }

            if (!empty($policyexcess)) {

                Polexcess::where('endt_renewal_no', $docReg->endt_renewal_no)
                        ->where('class', $docReg->class)
                        ->where('comb_class', $excess_cls)
                        ->where('item_no1', $request->excess_itemno_ed)
                        ->update($policyexcess);

            }

            $status = 1;

        });
        
        return [
                'status' => $status
            ];
        
    }

    public function delete_excess(Request $request)
    {
        $status = -1;

        if($request->excess_open_cover_del == 'Y'){

            $marine = new MarineOpenProcessing();

            $status = $marine->delete_marine_excess($request);

            return $status;

        }

        $excess_cls = $request->excess_cls_del;

        $rec = Polmaster::select('endorse_no','policy_no','class')
                        ->where('policy_no', $request->excess_pol_del)
                        ->first();

        $docReg = Dcontrol::select('policy_no','trans_type','nil_code','class','endt_renewal_no')
                            ->where('endt_renewal_no',$rec->endorse_no)
                            ->first();

        $polexcess = Polexcess::where('endt_renewal_no', $rec->endorse_no)
                                ->where('class', $rec->class)
                                ->where('comb_class', $excess_cls)
                                ->where('item_no1', $request->excess_item_no1_del)
                                ->first();

        $data = array();

        if ($docReg->trans_type == 'NIL' && $docReg->nil_code == 8) {

            $audit1 = [
                'policy_no'=>$docReg->policy_no,
                'endt_renewal_no'=> $rec->endorse_no,
                'risk_item'=> 'Policy excess',
                'table_name'=>'Polexcess',
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'system_user'=>Auth::user()->user_name
            ];

            if(!is_null(trim($polexcess->description))){
                $audit1['field_changed'] = 'Excess description';
                $audit1['old_value'] = trim($polexcess->description);
                $audit1['new_value'] = '';
        
                array_push($data, $audit1);
            }
            
            if(!is_null(trim($polexcess->rate))){
                $audit1['field_changed'] = 'Excess rate';
                $audit1['old_value'] = trim($polexcess->rate);
                $audit1['new_value'] = '';
        
                array_push($data, $audit1);
            }
            
            if(!is_null(trim($polexcess->base))){
                $audit1['field_changed'] = 'Excess base';
                $audit1['old_value'] = trim($polexcess->base);
                $audit1['new_value'] = '';
        
                array_push($data, $audit1);
            }
            
            if(!is_null(trim($polexcess->based_on))){
                $audit1['field_changed'] = 'Excess based_on';
                $audit1['old_value'] = trim($polexcess->based_on);
                $audit1['new_value'] = '';
        
                array_push($data, $audit1);
            }
            
            if(!is_null(trim($polexcess->minimum_excess))){
                $audit1['field_changed'] = 'Excess minimum_excess';
                $audit1['old_value'] = trim($polexcess->minimum_excess);
                $audit1['new_value'] = '';
        
                array_push($data, $audit1);
            }
            
            if(!is_null(trim($polexcess->maximum_excess))){
                $audit1['field_changed'] = 'Excess maximum_excess';
                $audit1['old_value'] = trim($polexcess->maximum_excess);
                $audit1['new_value'] = '';
        
                array_push($data, $audit1);
            }

        }
    
        DB::transaction(function () use ($request, $docReg,$data, $excess_cls, $polexcess, &$status) {

            if ($docReg->trans_type == 'NIL' 
                && $docReg->nil_code == 8
                && (!empty($polexcess))
                ) {
                DB::table('risk_audit_trail')->insert($data);
            }

            Polexcess::where('endt_renewal_no', $docReg->endt_renewal_no)
                    ->where('class', $docReg->class)
                    ->where('comb_class', $excess_cls)
                    ->where('item_no1', $request->excess_item_no1_del)
                    ->delete();

            $status = 1;

        });
        
        return [
                'status' => $status
            ];
    }

    public function add_note(Request $request){
        $endt_renewal_no = $request->note_endt_add;
        $notes = $request->note_descr_add;
        $count = Policy_notes::select('line_no')->where('endt_renewal_no', $endt_renewal_no)
        ->max('line_no');
        $next = $count + 1;

        $pol_note = new Policy_notes;

        $pol_note->endt_renewal_no = $endt_renewal_no;
        $pol_note->notes = $notes;
        $pol_note->line_no = $next;

        $pol_note->save();

        DB::commit();

        return [
            'status'=> 1, 
            'message'=>"Note added successfully"
       ];

    }

    public function update_note(Request $request)
    {
        Policy_notes::where('reference_no', $request->note_endt_ed)
                    ->where('note_id',$request->note_line_no_ed)
                    ->update([
                        'notes' => $request->notes_ed
                    ]);

        return [
            'status'=> 1, 
            'message'=>"Note updated successfully"
        ];
            
    }
    

    public function delete_note(Request $request)
    {
        Policy_notes::where('reference_no', $request->note_endt_del)
                    ->where('note_id', $request->note_line_no_del)
                    ->delete();

        return [
            'status'=> 1, 
            'message'=>"Note deleted successfully"
        ];

        
    }


    public function add_narration(Request $request)
    {
        $policy_no = $request->narration_pol_add;
        $endt_renewal_no = $request->narration_endt_add;

        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $classModel = ClassModel::where('class', $dcontrol->class)->first();
        $narration_reason = NarrationType::where('id', $request->narration_type)->pluck('type')->first();

        if ($classModel->combined == 'Y') {
            switch($dcontrol->trans_type) {
                case 'CNC':
                    $count = Debitdtl::where('endt_renewal_no', $endt_renewal_no)
                    ->max('line_no');
                    $combinedClasses = Polcmb::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                    ->get();
                    
                    $next = $count['line_no'] + 1;
                    $narration = $request->narration;
                    
                    foreach ($combinedClasses as $key => $cmbClass) {
                        $debitdbtl = new Debitdtl;
        
                        $debitdbtl->policy_no       = $policy_no;
                        $debitdbtl->endt_renewal_no = $endt_renewal_no;
                        $debitdbtl->line_no         = $next;
                        $debitdbtl->narration_type  = $request->narration_type;
                        $debitdbtl->detail          = $narration_reason;
                        $debitdbtl->detail2         = $narration;
        
                        # Set class
                        $debitdbtl->class           = $dcontrol->class;
                        $debitdbtl->comb_class      = $cmbClass;
        
                        $debitdbtl->save();
                        DB::commit();
                        $next = $next + 1;
                    }
                break;

                default:
                    $count = Debitdtl::where('endt_renewal_no', $endt_renewal_no)
                    ->max('line_no');
                    $next = intval($count) + 1;
                    $narrations = $request->narration;
                    $narrationClasses = $request->class;
        
                    foreach ($narrations as $key => $narration) {
                        $debitdbtl = new Debitdtl;
        
                        $debitdbtl->policy_no       = $policy_no;
                        $debitdbtl->endt_renewal_no = $endt_renewal_no;
                        $debitdbtl->line_no         = $next;
                        $debitdbtl->narration_type  = $request->narration_type;
                        $debitdbtl->detail          = $narration_reason;
                        $debitdbtl->detail2         = $narration;
        
                        # Set class
                        $debitdbtl->class           = $dcontrol->class;
                        $debitdbtl->comb_class      = $narrationClasses[$key];
        
                        $debitdbtl->save();
                        DB::commit();
                        $next = $next + 1;
                    }
                break;
            }
        } else {
        
            $pcode = new Debitdtl;

            $count = Debitdtl::where('endt_renewal_no', $request->narration_endt_add)->orderBy('line_no', 'desc')->first();


            $next = $count['line_no'] + 1;

            $pcode->policy_no = $request->narration_pol_add;
            $pcode->endt_renewal_no = $request->narration_endt_add;
            $pcode->line_no = $next;
            $pcode->narration_type  = $request->narration_type;
            $pcode->detail = $narration_reason;
            $pcode->detail2 = $request->narration_descr_add;

            $pcode->save();
            DB::commit();
        }
    }


    public function update_narration(Request $request)
    {
        $narration_reason = NarrationType::where('id', $request->narration_type_ed)->pluck('type')->first();

        $upd = Debitdtl::where('endt_renewal_no', $request->narration_endt_ed)
                       ->where('line_no', $request->narration_line_no_ed)
                       ->update([
                          'narration_type' => $request->narration_type_ed,
                          'detail' => $narration_reason,
                          'detail2' => $request->narration_descr_ed
        ]);
    }

    public function delete_narration(Request $request)
    {
        
        $upd = Debitdtl::where('endt_renewal_no', $request->narration_endt_del)
                       ->where('line_no', $request->narration_line_no_del)->delete();
    }



    /******* Miscellaneous Fees ***********/
    public function misc_fees_datatable(Request $request)
    {
        $endt_no = $request->get('endt_renewal_no');

        $misc_query = Polmisc_fees::where('endt_renewal_no', $endt_no)->get();
        
        return datatables::of($misc_query)
            ->addColumn('action', function ($src) {
                return 
                '<a class="btn btn-xs" id="btn-miscfee-edit" onclick="editMiscFee(`'. $src->endt_renewal_no.'`, `'. $src->fee_descr .'`, `'. $src->fee_amt .'`)">
                    <i class="glyphicon glyphicon-edit"></i>
                </a>
                <a class="btn btn-xs" id="btn-miscfee-delete"  onclick="delMiscFee(`'. $src->endt_renewal_no.'`, `'. $src->fee_descr.'`)">
                    <i class="glyphicon glyphicon-minus"></i>
                </a>';
            })
            ->make(true);
    }

    public function addMiscFees(Request $request)
    {
        parse_str($request->data, $data);

        $fee_type = (int) $data['fee_type'];
        $misc_fee = Misc_fees_param::where('fee_id', $fee_type)->first();

        $check_fee_exists = Polmisc_fees::where('endt_renewal_no', $data['endt_no'])
                            ->where('fee_entry_type',$misc_fee->fee_entry_type)->count();
        if ($check_fee_exists > 0) {
            Session::Flash('error', 'error while adding Miscellaneous Fee');
            return [
                'code' => 2,
                'message' => "exists"
            ];
        }
        $polmisc_fees = Polmisc_fees::create([
                'policy_no' => $data['policy_no'],
                'endt_renewal_no' => $data['endt_no'],
                'fee_descr' => $misc_fee->fee_descr,
                'fee_amt' => str_replace(',','',$data['fee_amt']),
                'fee_entry_type' => $misc_fee->fee_entry_type,
            ]);
        
        if($polmisc_fees){
            Session::Flash('success', 'Miscellaneous Fee was added successfuly');
            return [
                'code' => 1,
                'message' => "success"
            ];
        }else{
            Session::Flash('error', 'error while adding Miscellaneous Fee');
            return [
                'code' => -1,
                'message' => "error"
            ];
        }
    }


    /**Mgt Expense */


    
	    public function defmgt_expense(Request $request)
        {
            $expense = Mgt_expense::all();
            $expense = Mgt_expense::all();
            $months = Months::all();

            // return $expense;
            return view('gb.underwriting.mgt_expense',[
                
                'expense'=>$expense,
                'months'=>$months,
                'ex'=>$ex
            ]);
        }


	     public function fetchmgtyear(Request $request)
        {
            $yr = $request->get('year_search');


            $ex = Mgt_expense::where('acc_year',$yr )->get();
            $expense = Mgt_expense::all();
            $months = Months::all();
            $getmnths = DB::select( "select e.months,e.code from months e join mgt_expense d on d.acc_mnth = e.code where d.acc_year = '$yr'");



            if(count($ex)>0){
                return view('gb.underwriting.mgt_expense',[
                    'expense'=>$expense,
                    'months'=>$months,
                    'getmnths'=>$getmnths,
                    'ex'=>$ex
            
                ]);
            }
            else{
                Session::flash('error','Year Does not Exist');
                return view('gb.underwriting.mgt_expense',[
                    'expense'=>$expense,
                    'months'=>$months,
                    'getmnths'=>$getmnths,
                    'ex'=>$ex
                ]);

            }
            // return $expense;
           
        }


    public function addmgt_expense(Request $request)
    {
            $username = trim(Auth::user()->user_name);



            if($request->get('submit')){

                
                try{


                for($i=0 ; $i < count($request->countable); $i++){
                  
                    $expense = new Mgt_expense;
                    $expense->acc_mnth = $request->month[$i];
                    $expense->acc_year = $request->get('uwyear');
                    $expense->mgt_amnt = $request->amount[$i];
                    $expense->created_on = Carbon::now();
                    $expense->user_str = $username;                  
                    $expense->save();

                
                }

                DB::commit();
                Session::flash('success','Expense Added Successfully');
                return redirect()->back();
                }catch (\Throwable $e) {
                DB::rollback();
                $expense = Mgt_expense::all();
                Session::flash('error','Expense Not Added Successfully');
                return redirect()->back();
            }
              
                   
             
             
            }
            else if($request->get('amendtr')){

                // return $request;

                try{
                    for($i=0 ; $i < count($request->countable); $i++){
                      
                        $expense = new Mgt_expense;
                        $expense->acc_mnth = $request->month[$i];
                        $expense->acc_year = $request->get('uwyear');
                        $expense->mgt_amnt = $request->amount[$i];
                        $expense->created_on = Carbon::now();
                        $expense->user_str = $username;                  
                        $expense->save();  
                          }
                        DB::commit();
                        Session::flash('success','Expense Added Successfully');
                        return redirect()->back();
                    }catch (\Throwable $e) {
                        // dd($e); 
                    DB::rollback();
                        $expense = Mgt_expense::all();
                        Session::flash('error','Expense Not Added Successfully');
                        return redirect()->back();
                    }
        }
    }


    public function editmgt_expense(Request $request){
        $username = trim(Auth::user()->user_name);
        $check_acc = Period::where('account_year',$request->get('acyear'))->where('account_month',$request->get('mon') )->get();
        if($check_acc[0]->g_closed != 'Y'){
            try{
                // return $request->get('acyear');
                $ibnr = Mgt_expense::where('acc_year',$request->get('acyear') )
                ->where('acc_mnth',$request->get('mon') )
                ->update([
                    'mgt_amnt'=>$request->get('amounts'),
                    'updated_by' => $username,
                    'updated_on'=> Carbon::now()
                ]);
               Session::flash('success','Amounts Updated Successfully');
               return redirect()->back();
                }catch (\Throwable $e) {
                    // dd($e); 
                DB::rollback();
                    $ibnr = Ibnr::all();
                    Session::flash('error','Record Not  Updated');
                    return redirect()->back();
                }
  
        }else{
            Session::flash('error','Account period Has been Closed');
            return redirect()->back();
        }

    }

    /**IBNR */

    public function fetchibnrdet(Request $request){

        $yr = $request->get('acyr');
            $mnth = $request->get('acmnth');
            $dept = $request->get('dept');

        $ibnrdet = Ibnr::where('acc_year',$yr )
                    ->where('acc_mnth',$mnth )
                    ->where('dept',$dept )
                    ->first();

        echo json_encode($ibnrdet);


    }
    public function fetchmgtmnth(Request $request){

        $yr = $request->get('acyr');
            $mnth = $request->get('mon');

        $ibnrdet = Mgt_expense::where('acc_year',$yr )
                        ->where('acc_mnth', $mnth)
                        ->first();
        echo json_encode($ibnrdet);
    }
    
	  public function def_ibnr(Request $request)
        {
           
            $months = Months::all();
            $depts = Dept::all();

            // return $months;
            return view('gb.underwriting.ibnr',[
              
                'months'=>$months,     
                'depts'=>$depts
            ]);
        }

        public function fetchibnr(Request $request)
        {
            $yr = $request->get('year_search');
            $mnth = $request->get('month');
            // $dept = $request->get('department');


            $ibnr = Ibnr::where('acc_year',$yr )->where('acc_mnth',$mnth )->get();
            $gross_ibnr = Ibnr::where('acc_year',$yr )->where('acc_mnth',$mnth )->sum('gross_ibnr');
            $ri_ibnr = Ibnr::where('acc_year',$yr )->where('acc_mnth',$mnth )->sum('ri_ibnr');
            $net_amt = Ibnr::where('acc_year',$yr )->where('acc_mnth',$mnth )->sum('net_amt');
                                
      
            $getdepartments = DB::select( " select e.dept, e.description from dept e join ibnr c on e.dept = c.dept where acc_year = '$yr' and acc_mnth = '$mnth'");
            // return $getdepartments;
            
            $expense = Ibnr::all();
            $months = Months::all();
            $depts = Dept::all();


            if(count($ibnr)>0){
                return view('gb.underwriting.ibnr',[
                    'expense'=>$expense,
                    'months'=>$months,
                    'gross_ibnr'=>$gross_ibnr,
                    'ri_ibnr'=>$ri_ibnr,
                    'net_amt'=>$net_amt,
                    'getdepartments'=>$getdepartments,
                    'depts'=>$depts,
                    'ibnr'=>$ibnr
            
                ]);
            }
            else{
                Session::flash('error','Records Do not Exist');
                return view('gb.underwriting.ibnr',[
                    'expense'=>$expense,
                    'gross_ibnr'=>$gross_ibnr,
                    'months'=>$months,
                    'ri_ibnr'=>$ri_ibnr,
                    'net_amt'=>$net_amt,
                    'depts'=>$depts,
                ]);

            }
            // return $expense;
        }  

    public function add_ibnr(Request $request)
    {
        $username = trim(Auth::user()->user_name);

        if($request->get('submit')){
            
            try{
            for($i=0 ; $i < count($request->countable); $i++){

                $ibnr = new Ibnr;
                $ibnr->acc_mnth = $request->get('month');
                $ibnr->acc_year = $request->get('uwyear');
                $ibnr->dept = $request->dept[$i];
                $ibnr->gross_ibnr = $request->gamount[$i];
                $ibnr->ri_ibnr = $request->riamount[$i];
                $ibnr->net_amt = $request->gamount[$i] - $request->riamount[$i];
                $ibnr->created_date = Carbon::now();
                $ibnr->created_by = $username;
                $ibnr->save();
            }
                DB::commit();
                Session::flash('success','IBNR Amounts Saved Successfully');
                return redirect()->back();
            }catch (\Throwable $e) {
                // dd($e); 
               DB::rollback();
                $ibnr = Ibnr::all();
                Session::flash('error','IBNR Amounts Not  saved');
                return redirect()->back();
            }

        }
        elseif($request->get('amendtr')){
            // $check_acc = Period::where('account_year',$request->get('edacc_yr'))->where('account_month',$request->get('edacc_mnth') )->get();
            // return $request;

            try{
                for($i=0 ; $i < count($request->countable); $i++){
    
                    $ibnr = new Ibnr;
                    $ibnr->acc_mnth = $request->get('month');
                    $ibnr->acc_year = $request->get('uwyear');
                    $ibnr->dept = $request->dept[$i];
                    $ibnr->gross_ibnr = $request->gamount[$i];
                    $ibnr->ri_ibnr = $request->riamount[$i];
                    $ibnr->net_amt = $request->gamount[$i] - $request->riamount[$i];
                    $ibnr->created_date = Carbon::now();
                    $ibnr->created_by = $username;
                    $ibnr->save();
                }
                    DB::commit();
                    Session::flash('success','IBNR Amounts Saved Successfully');
                    return redirect()->back();
                }catch (\Throwable $e) {
                    //dd($e); 
                   DB::rollback();
                    $ibnr = Ibnr::all();
                    Session::flash('error','IBNR Amounts Not  saved');
                    return redirect()->back();
                }
        }


       
    }

    public function edit_ibnr(Request $request){
        $username = trim(Auth::user()->user_name);
        $check_acc = Period::where('account_year',$request->get('acyear'))->where('account_month',$request->get('accmonth') )->get();
        if($check_acc[0]->g_closed != 'Y'){
            try{
                $ibnr = Ibnr::where('acc_year',$request->get('acyear') )
                ->where('acc_mnth',$request->get('accmonth') )
                ->where('dept',$request->get('deptartments') )
                ->update([
                    'gross_ibnr'=>$request->get('gramount'),
                    'ri_ibnr'=>$request->get('reiamount'),
                    'net_amt' =>$request->get('gramount') -  $request->get('reiamount'),
                    'changed_by' => $username,
                    'changed_date'=> Carbon::now()
                ]);
               Session::flash('success','IBNR Amounts Updated Successfully');
               return redirect()->back();
                }catch (\Throwable $e) {
                    // dd($e); 
                DB::rollback();
                    $ibnr = Ibnr::all();
                    Session::flash('error','Record Not  Updated');
                    return redirect()->back();
                }
  
        }else{
            Session::flash('error','Account Month Has been Closed');
            return redirect()->back();
        }

    }

/**end of IBNR  */


    public function editMiscFees(Request $request)
    {
        parse_str($request->data, $data);
        // return $data['fee_amt'];
        $fee_type = (int) $data['fee_type'];
        $misc_fee = Misc_fees_param::where('fee_id', $fee_type)->first();
        if (!empty($misc_fee)) {
            $polmisc_fees = Polmisc_fees::where('endt_renewal_no', $data['endt_no'])
                                        ->where('fee_descr', $misc_fee->fee_descr)
                                        ->first();
            // return $polmisc_fees;
            if (!empty($polmisc_fees)) {
                $polmisc_fees2 = Polmisc_fees::where('endt_renewal_no', $data['endt_no'])
                            ->where('fee_descr', $misc_fee->fee_descr)
                            ->update([                    
                                'fee_amt' => (int) $data['fee_amt']
                            ]);
        
                if($polmisc_fees2){
                    Session::Flash('success', 'Miscellaneous Fee was edited successfully');
                    return [
                        'code' => 1,
                        'message' => "success"
                    ];
                }else{
                    Session::Flash('error', 'Oops... error while editing Miscellaneous Fee');
                    return [
                        'code' => -1,
                        'message' => "error"
                    ];
                }
            }
        }
        return [
            'code' => -1,
            'message' => "error"
        ];
    }

    public function delMiscFees(Request $request)
    {
        $endt = $request->endt_renewal_no;
        $fee_descr = $request->fee_descr;
        try {
            $polmisc_fees = Polmisc_fees::where('endt_renewal_no', $endt)->where('fee_descr', $fee_descr)->delete();
            // return $endt;

            if($polmisc_fees){
                Session::Flash('success', 'Miscellaneous Fee was deleted successfully');
                return [
                    'code' => 1,
                    'polmisc_fees' => $polmisc_fees,
                    'codeg' => $polmisc_fees,
                    'codeed' => $endt,
                    'codeg' => $fee_descr,
                    'message' => "success"
                ];
            }else{
                Session::Flash('error', 'error while deleted Miscellaneous Fee');
                return [
                    'code' => -1,
                    'polmisc_fees' => $polmisc_fees,
                    'codeed' => $endt,
                    'codeg' => $fee_descr,
                    'message' => $polmisc_fees
                ];
            }
        } catch (\Exception $e) {
            report($e);
            return $e->getMessage();
        }
        
    }

    public function getMiscFee(Request $request)
    {
        $id = (int) $request->id;
        $misc_fee = Misc_fees_param::where('fee_id', $id)->first();
        if($misc_fee){
            return [
                'code' => 1,
                'message' => $misc_fee 
            ];
        }else{
            return [
                'code' => -1,
                'message' => "error"
            ];
        }
    }

    /******* Miscellaneous Fees ***********/

    public function maker_submission(Request $request)
    {

        $user = Auth::user();
        $aimsuser = Aimsuser::where('user_id', $request->get('checker'))->get();
        $aimsuser = $aimsuser[0];
        $endt_no = $request->get('checker_endt_no');
        $submissions = new Makersubmissions;
        $submissions->endt_renewal_no = $endt_no;
        $submissions->maker_name = $user->name . ' ' . $user->last_name;
        $submissions->maker_user_id = $user->user_id;
        $submissions->checker_name = $aimsuser->name . ' ' . $aimsuser->last_name;
        $submissions->checker_user_id = $aimsuser->user_id;
        $submissions->status_complete = 'N';
        $submissions->dola = Carbon::today();
        $submissions->save();

        ////////function to send email to checker 

        Mail::send('emails.welcome', $data, function ($message) {
            $message->from($user->email);
            $message->to($aimsuser->email);
            $message->subject('Further action for endorsement number ' . $endt_no);
        });

        $response = array('status' => 'Endorsement number ' . $endt_no . ' has been  forwarded to ' . $aimsuser->name . ' ' . $aimsuser->last_name . ' for further action',);
        echo json_encode($response);
    }

    public function check_print_policy(Request $request)
    {
        
        $username = trim(Auth::user()->user_name);
        $printmotsticker = Gate::check('print-motor-certificate');
        $printpolicydocument = Gate::check('print-policy-document');
        return (["status" => 1, 'printpolicydocument' => $printpolicydocument, 'printmotsticker' => $printmotsticker]);
    }



    // First Loss Rates

    // view
    public function FirstLossRatesView(Request $request){
            return view('gb.underwriting.first_loss_rates');
    }

    // datatable
    public function FirstLossRatesDatatable(Request $request){
        $loss_rates = Firstloss_rates::all();
        return datatables::of($loss_rates)
            ->addColumn('action', function ($row) {
                return '<a class="btn btn-xs" id="btn-limit-edit" 
                            onclick="editLossRates('.$row->rate_id.')">
                            <i class="glyphicon glyphicon-edit"></i>
                        </a>
                        <a class="btn btn-xs" id="btn-limit-delete" 
                            onclick="delLossRates('.$row->rate_id.')" style="padding-left:15px; color:red">
                            <i class="fa fa-trash" style="font-size: large;"></i>
                        </a>';
            })
            ->make(true);
    }

    // Create
    public function FirstLossRatesCreate(Request $request)
    {
        try {
            DB::beginTransaction();

            $rates_create = [];
            parse_str($request->data, $rates_create);

            $rate_id = Firstloss_rates::all()->max('rate_id') + 1;

            // return $rates_create;

             $row_lossrate = Firstloss_rates::create([
                'rate_id'=> $rate_id,
                'min_rate' => (int) $rates_create['min_rate'],
                'max_rate' => (int) $rates_create['max_rate'],
                'rate_to_apply' => (int) $rates_create['applied_rate']
             ]);

            DB::commit();
            return [
                'code' => 1,
                'message' => 'data was saved successfully'
            ];

        } catch (\Exception $e) {
            DB::rollback();
            report($e);

            return [
                'code' => -1,
                'message' => $e->getMessage()
            ];
        }
    }

    // Get a row
    public function FirstLossRatesArow(Request $request)
    {
        try {
            DB::beginTransaction();
            $rate_id = $request->id;

            $row_lossrate = Firstloss_rates::where('rate_id', $rate_id)->first();

            // return $row_lossrate;

            DB::commit();
            return [
                'code' => 1,
                'message' => $row_lossrate
            ];

        } catch (\Exception $e) {
            DB::rollback();
            report($e);

            return [
                'code' => -1,
                'message' => $e->getMessage()
            ];
        }
    }

     // Update
     public function FirstLossRatesUpdate(Request $request)
     {
         try {
             DB::beginTransaction();
             $rates_update = [];

             parse_str($request->data, $rates_update);

            //  return json_encode( (int) $rates_update['rate_id']);
            
             $row_lossrate = Firstloss_rates::where('rate_id', (int) $rates_update['rate_id'])->update([
                 'min_rate' => (int) $rates_update['min_rate'],
                 'max_rate' => (int) $rates_update['max_rate'],
                 'rate_to_apply' => (int) $rates_update['applied_rate']
             ]);

            //  check if data was committed
            if ($row_lossrate) {
                DB::commit();
                return [
                    'code' => 1,
                    'message' => 'data was saved successfully'
                ];
            }

            return [
                'code' => -1,
                'message' => 'somethng went wrong'
            ];
 
 
         } catch (\Exception $e) {
             DB::rollback();
             report($e);
 
             return [
                 'code' => -1,
                 'message' => $e->getMessage()
             ];
         }
     }

    // Delete
    public function FirstLossRatesDelete(Request $request)
    {
        try {
            DB::beginTransaction();

            $rate_id = (int) $request->id;

            $row_lossrate = Firstloss_rates::where('rate_id', $rate_id)->delete();

            DB::commit();
            return [
                'code' => 1,
                'message' => 'data was saved successfully'
            ];

        } catch (\Exception $e) {
            DB::rollback();
            report($e);

            return [
                'code' => -1,
                'message' => $e->getMessage()
            ];
        }
    }

    // First Loss Rates.end

    public function mechanicalValuationReports(Request $request) {
        $endt_renewal_no = $request->endt_renewal_no;
        $reg_no = $request->reg_no;

        $mech_report = $request->mech_report;
        $valuation_report = $request->valuation_report;

        if ($mech_report == 'Y' && $valuation_report == 'Y') {
            # Save both mechanical and valuation 

            $reportSave = Modtl::where('endt_renewal_no', $endt_renewal_no)->where('reg_no', $reg_no)
                ->update([
                    'mech_receive_date' => new Carbon($request->mech_receive),
                    'mech_inspection_date' => new Carbon($request->mech_inspection),
                    'valuation_receive_date' => new Carbon($request->valuation_receive),
                    'valuation_inspection_date' => new Carbon($request->valuation_inspection)
                ]);
        } else if ($mech_report == 'Y') {
            $reportSave = Modtl::where('endt_renewal_no', $endt_renewal_no)->where('reg_no', $reg_no)
                ->update([
                    'mech_receive_date' => new Carbon($request->mech_receive),
                    'mech_inspection_date' => new Carbon($request->mech_inspection)
                ]);
        } else if ($valuation_report == 'Y') {
            $reportSave = Modtl::where('endt_renewal_no', $endt_renewal_no)->where('reg_no', $reg_no)
                ->update([
                    'valuation_receive_date' => new Carbon($request->valuation_receive),
                    'valuation_inspection_date' => new Carbon($request->valuation_inspection)
                ]);
        }

        Session::flash('Success', 'Report Details for '.$reg_no.' Captured Successfully');
        return redirect()->back();
    }

    public function checkReportStatus(Request $request) {
        $endt_renewal_no = $request->endt_renewal_no;
        $reg_no = $request->reg_no;
        
        $statusReport = Modtl::where('endt_renewal_no', $endt_renewal_no)->where('reg_no', $reg_no)->first();

        return $statusReport;
    }
    public function confirm_commit(Request $request) {
        $endt_renewal_no = $request->endt_renewal_no;
        $dcon = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $nil_code = (int)$dcon->nil_code;
        $pol_no = $dcon->policy_no;
    
        $cert_infom = DB::select("select * from CERTALLOC where trim(POLICY_NO)='".$pol_no."'  and cert_status=1  ORDER BY ISSUE_TIME DESC");
        
        $cert_count = count($cert_infom);
            if ($cert_count > 0 && $nil_code == 3) {
              
                foreach ($cert_infom as $cert_info) {
                    $cert_status = app('App\Http\Controllers\gb\underwriting\Debit')->postCertCnc($cert_info);
                    // dd($cert_status);
                    $msg=$cert_status['msg'];
                    $code=(int)$cert_status['code'];
                    //dd($msg);
            
                    if($code == 0){
                        Session::flash('warning',$msg);
                    }elseif($code == 1){
                        Session::flash('info',$msg);
                    }
                }
                //end api call
                

            }
         
                
        $commit = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->update([
                            'committed' => 'Y'
                        ]);

        $commit_marinemasterpol = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->update([
                                                    'commit_transaction' => 'Y',
                                                    'trans_committed_by' => Auth::user()->user_name,
                                                    'status' => 'ACT'
                                                ]);

        $commit_marinemasterhist = Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)->update([
                                                        'commit_transaction' => 'Y',
                                                        'trans_committed_by' => Auth::user()->user_name,
                                                        'status' => 'ACT'
                                                    ]);


        if($commit){
            return [
                'status' => 1,
            ];
        }
        else{
            return [
                'status' => 0,
            ];

        }
    }
    public function apply_std_limits($endt_renewal_no)
    {

        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
        
        DB::beginTransaction();

        try {

            //insert standard limits by executing oracle procedure apply_std_limits
            $procedureName = '' . $gb . '.apply_std_limits';
            $bindings = [
                'endt_renewal_no'  =>  $endt_renewal_no
            ];
            $resp = DB::executeProcedure($procedureName, $bindings);
            DB::commit();
        } catch (\Throwable $e) {

            DB::rollback();
        }

    }
    public function apply_std_excess($endt_renewal_no)
    {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        DB::beginTransaction();

        try {

            //insert standard excesses  by executing oracle procedure apply_std_excess
            $procedureName = '' . $gb . '.apply_std_excess';
            $bindings = [
                'endt_renewal_no'  =>  $endt_renewal_no
            ];
            $resp = DB::executeProcedure($procedureName, $bindings);
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();
            // dd($e);
        }

    }
    public function apply_clauses_new($endt_renewal_no)
    {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $dcontrol_cls = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol_cls = $dcontrol_cls[0];

        //ensure clauses,limits and excesses are added to respective tables for renewal
        DB::beginTransaction();

        try {

            //insert clauses by executing oracle procedure clauses_new
            $procedureName = '' . $gb . '.clauses_new';
            $bindings = [
                'endt_renewal_no'  =>  $endt_renewal_no,
                'g_user_name' => $dcontrol_cls->user_str
            ];
            $resp = DB::executeProcedure($procedureName, $bindings);
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();
        }

    }

    public function apply_binder_clauses($endt_renewal_no)
    {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $dcontrol_cls = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol_cls = $dcontrol_cls[0];

        //ensure clauses,limits and excesses are added to respective tables for renewal
        DB::beginTransaction();

        try {

            //insert clauses by executing oracle procedure clauses_new
            $procedureName = '' . $gb . '.apply_binder_clauses';
            $bindings = [
                'endt_renewal_no'  =>  $endt_renewal_no,
                'g_user_name' => $dcontrol_cls->user_str
            ];
            $resp = DB::executeProcedure($procedureName, $bindings);
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();
        }

    }
    public function apply_binder_limits($endt_renewal_no)
    {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $dcontrol_cls = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol_cls = $dcontrol_cls[0];

        //ensure clauses,limits and excesses are added to respective tables for renewal
        DB::beginTransaction();

        try {

            //insert vinder limits by executing oracle procedure apply_binder_limits
            $procedureName = '' . $gb . '.apply_binder_limits';
            $bindings = [
                'endt_renewal_no'  =>  $endt_renewal_no
            ];
            $resp = DB::executeProcedure($procedureName, $bindings);
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();
        }

    }
    public function apply_binder_excess($endt_renewal_no)
    {
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $dcontrol_cls = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol_cls = $dcontrol_cls[0];

        //ensure clauses,limits and excesses are added to respective tables for renewal
        DB::beginTransaction();

        try {

            //insert binder excesses  by executing oracle procedure apply_binder_excess
            $procedureName = '' . $gb . '.apply_binder_excess';
            $bindings = [
                'endt_renewal_no'  =>  $endt_renewal_no
            ];
            $resp = DB::executeProcedure($procedureName, $bindings);
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollback();
        }

    }
    public function clone_polsect_to_polsectend($endt_renewal_no, $location = 0){
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
        //insert missing locations to polsectend from oracle view polsect_not_in_polsectend
        $polsect_not_in_polsectend = Polsect_not_in_polsectend::where('endt_renewal_no', $endt_renewal_no)->get();

        foreach ($polsect_not_in_polsectend as $polsect_not_in_polsectend) {
            //sql to clone modtl record to modtlend 
            
            DB::beginTransaction();
            try {

                $procedureName = '' . $gb . '.clone_polsect_to_polsectend';
                $bindings = [
                    'endt_renewal_no'  =>  $endt_renewal_no,
                    'location' => $polsect_not_in_polsectend->location
                ];
                $resp = DB::executeProcedure($procedureName, $bindings);
                
                DB::commit();
            } catch (\Throwable $e) {

                DB::rollback();
            }
            
        }

    }
    public function clone_modtl_to_modtlend($endt_renewal_no){
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
        //insert missing vehicles to modtlend from oracle view modtl_not_in_modtlend
        $modtl_not_in_modtlend = Modtl_not_in_modtlend::where('endt_renewal_no', $endt_renewal_no)->get();

        foreach ($modtl_not_in_modtlend as $modtl) {
            //sql to clone modtl record to modtlend 

            try {

                $procedureName = '' . $gb . '.clone_modtl_to_modtlend';
                $bindings = [
                    'endt_renewal_no'  =>  $endt_renewal_no,
                    'reg_no' => $modtl->reg_no
                ];
                $resp = DB::executeProcedure($procedureName, $bindings);
        
            } catch (\Throwable $e) {
                // dd($e);
                
            }
        }
    }

    public function  inbox(Request $request){
        Escalate_pol::where('endorse_no',$request->endt_renewal_no)
                    ->where('escalate_id',$request->escalate_id);

                    return redirect()->route('policy_functions', ['endt_renewal_no' => $request->endt_renewal_no]);    
        
    }


    public function autoPopulateStdClauses($endt_renewal_no, $policy_no)
    {
        //get dcontrol

        $dcontrol = Dcontrol::where('policy_no', $policy_no)
            ->where('endt_renewal_no', $endt_renewal_no)
            ->first();
        //get vehicle in modtl
        $modtl_count = Modtlmast::where('endt_renewal_no', $endt_renewal_no)
            ->where('status','ACT')
            ->count();
            // ->get(['class', 'cover_type','policy_no', 'item_no', 'endt_renewal_no']);

        $cls = ClassModel::where('class', $dcontrol->class)->first();
        
        if($cls->combined == 'Y'){
            
            $p = new Policy;
            $p->autoPopulateCMBClauses($dcontrol->endt_renewal_no);

        }
        else if ($modtl_count > 0) {
            //check if fleet
            
            //do fleet operations
            #1.CHECK FOR COMPREHENSIVE
            $comprehensivecount = Modtlmast::where('endt_renewal_no', $endt_renewal_no)
                ->where('class', $dcontrol->class)//comprehensive classtype
                ->where('covertype', 1)//comprehensive covertype
                ->where('policy_no', $dcontrol->policy_no)->count();

            #2.CHECK FOR THIRD FIRE AND THEFT
            $thirdparyfirentheft = Modtlmast::where('endt_renewal_no', $endt_renewal_no)
                ->where('class', $dcontrol->class)//thirdparty classtype
                ->where('covertype', 2)//thirdparty covertype
                ->where('policy_no', $dcontrol->policy_no)->count();
            #3.CHECK FOR THIRD PARTY ONLY
            $thirdpartyonly = Modtlmast::where('endt_renewal_no', $endt_renewal_no)
                ->where('class', $dcontrol->class)//thirdparty classtype
                ->where('covertype', 3)//thirdparty only covertype
                ->where('policy_no', $dcontrol->policy_no)->count();

            $clause_per_covertype = Mautoclauses::where('class', $dcontrol->class)
                ->whereNull('cover_type')
                ->count();

            if($clause_per_covertype > 0){
                $clauses = Mautoclauses::where('class', $dcontrol->class)->get();
            }
            elseif ($comprehensivecount > 0) {
                $clauses = Mautoclauses::where('class', $dcontrol->class)
                    ->where('cover_type', 1)
                    ->get();

                    
            } elseif ($thirdparyfirentheft > 0) {
                $clauses = Mautoclauses::where('class', $dcontrol->class)
                    ->where('cover_type', 2)
                    ->get();
            } elseif ($thirdpartyonly > 0){
                //third party only
                $clauses = Mautoclauses::where('class', $dcontrol->class)
                    ->where('cover_type', 3)
                    ->get();
            }else{
                $clauses = Mautoclauses::where('class', $dcontrol->class)
                    ->get();
            }

            try {
                foreach ($clauses as $autoClause) {
                    
                    $clause = Clauses::whereRaw("trim(clause) ='".trim($autoClause->clause)."'")
                                        ->where('dept', $autoClause->dept)
                                        ->first();
                    
                    // dd($clause);
                    $counter = 1;
                    $polclause = Polclause::create([
                        'policy_no' => $dcontrol->policy_no,
                        'location' => $counter,
                        'dept' => $clause->dept,
                        'dept_fil' => $clause->dept_fill,
                        'clause' => $clause->clause,
                        's_code' => $clause->s_code,
                        'description' => $clause->description,
                        'serial' => $clause->serial,
                        'endt_renewal_no' => $dcontrol->endt_renewal_no,
                        'class' => $dcontrol->class,
                        'comb_class' => $dcontrol->class
                    ]);

                    $polclauses = Polclauses::create([
                        'policy_no' => $dcontrol->policy_no,
                        'clause' => $clause->clause,
                        'clause_no' => $clause->clause,
                        's_code' => $clause->s_code,
                        'description' => $clause->description,
                        'serial' => $clause->serial,
                        'dept' => $clause->dept,
                        'endt_renewal_no' => $dcontrol->endt_renewal_no,
                        'class' => $dcontrol->class,
                        'comb_class' => $dcontrol->class
                    ]);

                    $counter++;
                }
            } catch (\Throwable $e) {
                //dd($e);
            }
        }

    }
    public function cedits_table(Request $request)
    {
        $endt_renewal_no = $request->endt_renewal_no;
        $getcredit = Cbcredit::where('claim_no',$endt_renewal_no)
                                ->where('entry_type_descr','PRM')
                    ->get();
              
        return Datatables::of($getcredit)
                // ->addIndexColumn()

                ->editColumn('stat', function($getcredit){
                    if($getcredit->manager != null ){
                        return 'Approved';
                    }else if(($getcredit->manager !=null && $getcredit->cancelled == 'Y')||($getcredit->manager == null && $getcredit->cancelled == 'Y')){
                        return 'Cancelled';
                    }else{
                        return 'Pending Approval';
                    }
                   
                })
                ->editColumn('created_time', function($getcredit){
                    return ($getcredit->created_time);
                })
                ->editColumn('out_cred_amt', function($getcredit){
                    return $getcredit->out_cred_amt;
                })
                ->addColumn('re-escalate', function($getcredit){
					if(($getcredit->manager == null && $getcredit->cancelled != 'Y')||($getcredit->manager == null )){
                        return    '<a class="btn btn-md btn-success" id = "escalatecredit"
                                   
                                    data-credit="'. $getcredit->credit_reference .'"
                                    data-endorsement = "'. $getcredit->claim_no .'"
                                    data-toggle="modal"
                                    data-target="#re_esc_credit"
                                    >
                                    Re-Escalate
                                </a>';
					}else{
                        return '<a class="btn btn-md  btn-success"
                        disabled= `disabled` ">
                        Re-Escalate
                    </a>';
                      
					}
					
				})
                -> addColumn('upload-documents', function ($getcredit) {
                    return '<a class="btn btn-xs bg-info" data-reference = "'. $getcredit->credit_reference .'" id="upload_document"><i class="fa fa-plus" aria-hidden="true"></i>Documents</a>';
                }) 

                ->rawColumns(['re-escalate','upload-documents'])
                ->make(true);
                
    }

    public function getcbcreditdetails( Request $request){
        $endt_renewal_no = $request->endorse;
        $credit = $request->credit;
        $getcredit = Cbcredit::where('claim_no',$endt_renewal_no)
                    ->where('credit_reference',$credit)
                    ->first();
        
    }

    public function deleteEndorsement(Request $request) {
        $endt_renewal_no = $request->endt_renewal_no;
    
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();

        $cls = ClassModel::where('class', $dcontrol->class)->first();

        $cancelled_by = trim(Auth::user()->user_name);
        $cancelled_at = Carbon::now()->format('Y-m-d H:i:s');
        $cancellation_reason = $request->input('cancellationReason');
        $mac_endt_no = 'N';

        # Check if Endorsement is debited
        $debitMastCheck = Debitmast::where('policy_no', $dcontrol->policy_no)
        ->where('endt_renewal_no', $endt_renewal_no)
        ->exists();

        if ($debitMastCheck) {
            return redirect()->back()->with('error', 'Cannot cancel endorsement '. formatPolicyOrClaim($endt_renewal_no) . ' since it has been debited.');
        }

        DB::beginTransaction();

        try {
            $prev_endt = 0;
            if ($dcontrol->trans_type != 'POL' && $dcontrol->trans_type != 'PTA') {

                if($cls->open_cover == 'Y'){

                    # Get Previous Debited/Committed Endt
                    $lastDebitedTransaction = DB::select("select policy_no, trans_type, committed, endt_renewal_no from dcontrol 
                    where policy_no = '$dcontrol->policy_no' and cancelled = 'N' and (committed is null or committed = 'Y') and (mac_endt_no = '$dcontrol->mac_endt_no' or mac_endt_no='N') and
                    (
                        endt_renewal_no != '$dcontrol->endt_renewal_no' and endt_renewal_no not in 
                            (select endorse_no from escalate_pol where policy_no = '$dcontrol->policy_no' and endorse_no not in 
                                (select debitmast.endt_renewal_no from debitmast where policy_no = '$dcontrol->policy_no' )
                            )
                    ) order by dola desc FETCH FIRST 1 ROWS ONLY");

                }
                else{

                    # Get Previous Debited/Committed Endt
                    $lastDebitedTransaction = DB::select("select policy_no, trans_type, committed, endt_renewal_no from dcontrol 
                    where policy_no = '$dcontrol->policy_no' and cancelled = 'N' and (committed is null or committed = 'Y') and 
                    (
                        endt_renewal_no != '$dcontrol->endt_renewal_no' and endt_renewal_no not in 
                            (select endorse_no from escalate_pol where policy_no = '$dcontrol->policy_no' and endorse_no not in 
                                (select debitmast.endt_renewal_no from debitmast where policy_no = '$dcontrol->policy_no' )
                            )
                    ) order by dola desc FETCH FIRST 1 ROWS ONLY");

                }

                

                $prev_endt = $lastDebitedTransaction[0]->endt_renewal_no;
            }

            // dd($prev_endt);

            # Call Single Class Procedure
            $schem = schemaName();
            $gb = $schem['gb'];

            $procedure = '' . $gb . '.delete_endorsement_single_class';
            $bindings = [
                'deleted_endt_no' => $dcontrol->endt_renewal_no,
                'debited_endt_no' => $prev_endt,
                'cancelled_by' => $cancelled_by,
                'cancelled_at' => $cancelled_at,
                'why_cancelled'=>$cancellation_reason
            ];
           
            $resp = DB::executeProcedure($procedure, $bindings);

            $polremast = Polremast::where('endt_renewal_no',$dcontrol->endt_renewal_no)->get();

            foreach ($polremast as $reinsurance){
                $new_request = new Request([
                    'endorsement' => $reinsurance->endt_renewal_no, 
                    'class_comb' =>$reinsurance->comb_class ,
                    'reverse_section' =>$reinsurance->section,
                    'reverse_location'=>$reinsurance->location,
                ]);

                $this->reverse_reinsurance($new_request);
            }
        
            if ($dcontrol->trans_type != 'POL' && $dcontrol->trans_type != 'PTA') {
                $polsectRecs = Polsect::where('endt_renewal_no', $dcontrolOld->endt_renewal_no)
                ->where('deleted', 'N')
                ->get();

                foreach($polsectRecs as $polsect) {
                    # Update Section Premium and Sum Insured
                    $this->update_polsect($dcontrol->policy_no, $polsect->location, $dcontrol->endt_renewal_no);
                    
                    # Check if Combined
                    $classModel = ClassModel::where('class', $dcontrol->class)->first();

                    if ($classModel->combined == 'Yb') {                
                        $si_tot = Polsect::where('endt_renewal_no', $prev_endt)
                        ->where('class', $polsect->class)
                        ->sum('total_sum_insured');

                        $endorse_tot = Polsect::where('endt_renewal_no', $prev_endt)
                        ->where('class', $polsect->class)
                        ->sum('endorse_amount');

                        $prem_tot = Polsect::where('endt_renewal_no', $prev_endt)
                        ->where('class', $polsect->class)
                        ->sum('total_premium');

                        DB::beginTransaction();

                        // $polcmb = Polcmb::where('policy_no', $dcontrol->policy_no)
                        //                 ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                        //                 ->where('class', $polsect->class)
                        //                 ->update([
                        //                     // 'sum_insured'       => $si_tot,
                        //                     // 'premium'           => $prem_tot,
                        //                     // 'endt_renewal_no'   => $prev_endt,
                        //                     // 'endorse_amount'    => $endorse_tot,

                        //                 ]); 

                        DB::commit();
                    }
                }

                # Update Polmaster
                $this->update_polmaster($dcontrol->endt_renewal_no);
            }
            DB::commit();
            if($dcontrol->mtp <> "Y"){
                if ($dcontrol->trans_type == 'POL' || $dcontrol->trans_type == 'PTA') {
                    # Redirect to Home
                    return redirect()->route('home')->with('success', 'Policy endorsement '. formatPolicyOrClaim($dcontrol->endt_renewal_no) . ' has been cancelled from the systems');
                } else {
                    # Redirect to Policy Details Page
                    return redirect()->route('endorse_functions', ['policy_no' => $request->policy_no, 'uw_year' => $request->uw_year])->with('success', 'Endorsement '. formatPolicyOrClaim($dcontrol->endt_renewal_no) .' has been cancelled from the system');
                }

            }else{
                return [
                    "status"=>1
                ];

            }
           
        } catch (\Throwable $th) {
            DB::rollback();
            dd($th);

            report($th);


            $resp = false;
            $warning = 'Internal Server Error 500.';
            return $warning;
        }
    }
    

    // get clauses belonging to a combined class member
    public function cmb_class_clauses(Request $request)
    {
        $cls = $request->classModel;
        $class = ClassModel::where('class',$cls)->get();
        if(trim($pipcnam->clauses_flag) == 'C'){
            $clauses = Clauses::where('class', $class[0]->class)->get();
        }
        else{
            $clauses = Clauses::where('dept', $class[0]->dept)->get();
        }

        return response()->json($clauses, 200);
    }

    public static function extraToRefundEndorsement($endt_renewal_no)
    {
        $dc = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();
        $pol = Polmaster::where('endorse_no',$endt_renewal_no)->first();
        $cls = ClassModel::where('class', $dc->class)->first();

        $cancelController = new Cancellations();
        
        if($dc->trans_type=='EXT' && $pol->endorse_amount < 0){
            $pol = Polmaster::where('policy_no',$dc->policy_no)->update([
                        'trans_type'=>'RFN'
                    ]);

            // attach RFN to a debit note with amount greater than or equal to Refund amount
            $latest_rec = $cancelController->latest_baseTransaction($dc->policy_no);

            $cnc_rfn_endt = null;
            $req = new Request([
                'policy_no' => $dc->policy_no
            ]);

            if($cls->open_cover == 'Y'){

                $cnc_rfn_endt = $dc->mac_endt_no;

            }else{

                $cancellations = new Cancellations();
                $resp= $cancellations->fetch_ext($req);
                $resp = $resp->getOriginalContent();
                $unrefunded_exts = $resp['exts'];

                foreach($unrefunded_exts as $ext){
                    if(abs($pol->endorse_amount) <= (int)$ext['amount']){
                        $cnc_rfn_endt = $ext['endt_renewal_no'];
                        break;
                    }
                }
                
                // if refund amount is greater than all EXT amounts or no unrefunded EXT found, attach refund to recent POL,REN r RNS
                if(is_null($cnc_rfn_endt)){
                    $cnc_rfn_endt = $latest_rec->endt_renewal_no;
                }

            }
            
            $dc_upd = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->update([
                        'trans_type'=>'RFN',
                        'doc_type'=>'CRN',
                        'dr_cr'=>'C',
                        'auto_allocate'=>'Y',
                        'ext_to_rfn'=>'Y',
                        'cnc_rfn_endt' => $cnc_rfn_endt
                      ]);
        }
        else if(($dc->trans_type=='RFN' && $pol->endorse_amount > 0)){
            $pol = Polmaster::where('policy_no',$dc->policy_no)->update([
                        'trans_type'=>'EXT'
                    ]);

            $dc_upd = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->update([
                        'trans_type'=>'EXT',
                        'doc_type'=>'DRN',
                        'dr_cr'=>'D',
                        'ext_to_rfn'=>'N',
                      ]);
        }

        return [
            'status' => 1,
            'message' => 'Successful'
        ];
    }


    public function checksalvage($policy_no){

        $countvehicle = Modtlmast::where('policy_no',$policy_no)
                            ->where(function($query){
                                $query->where('status','WRO')
                                    ->orWhere('status','STL');
                            })
                            ->count();
            
        $countactive = Modtlmast::where('policy_no',$policy_no)
                    ->count();
        if($countvehicle == $countactive ){
            return 1;
        }else{
            return 0;
        }


    }

    public function fetchTransDetails(Request $request)
    {
        $endt_renewal_no = $request->endt_no;

        $data = Dcontrol::select('dcontrol.policy_no','dcontrol.endt_renewal_no','dcontrol.client_number','dcontrol.branch','dcontrol.agent','client.name as clientname','agmnf.name as intermediary_name')
                        ->join('client',function($join){
                            $join->on('dcontrol.client_number','=','client.client_number');
                        })
                        ->join('agmnf',function($join){
                            $join->on('dcontrol.branch','=','agmnf.branch')
                                 ->on('dcontrol.agent','=','agmnf.agent');
                        })
                        ->where('dcontrol.endt_renewal_no',$endt_renewal_no)
                        ->first();

        return $data;
    }
    public function stagecreditnote(Request $request){
       
        $endt_renewal_no=$request->endt_renewal_no;
        $foreign_premium =str_replace(',', '', $request->foreign_premium);
        $foreign_stamp_duty =str_replace(',', '', $request->foreign_stamp_duty);
        $foreign_training_levy =str_replace(',', '', $request->foreign_training_levy);
        $foreign_vat_amount =str_replace(',', '', $request->foreign_vat_amount);
        $sticker_fees =str_replace(',', '', $request->sticker_fees);

        $dcontrol_rec=Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();
        $exists=DB::table('efris_approval_records')->where('endt_renewal_no',$endt_renewal_no)->exists();
      
        if(!$exists){
            DB::table('efris_approval_records')->insert([
                "endt_renewal_no"=>$endt_renewal_no,
                "account_year"=>$dcontrol_rec->account_year,
                "account_month"=>$dcontrol_rec->account_month,
                "currency_rate"=>$dcontrol_rec->currency_rate,
                "currency_code"=>$dcontrol_rec->currency,
                "foreign_net"=>$foreign_premium,
                "foreign_premium"=>$foreign_premium,
                "foreign_stamp_duty"=>$foreign_stamp_duty?$foreign_stamp_duty:0,
                "foreign_levy_amount"=>$foreign_training_levy,
                "foreign_vat_amount"=>$foreign_vat_amount,
                "sticker_fees"=>$sticker_fees?$sticker_fees:0,
                "vat_rate"=>$dcontrol_rec->vat_rate,
                 "invoiceapplycategorycode"=>101,
                 "created_at"=>Carbon::now(),
                 "updated_at"=>Carbon::now(),
    
    
    
            ]);

        }
       
         $request = new Request();
         $request->merge(['endorsement_no' => $endt_renewal_no]);
         $efris_res= (object)(new EnfrisController())->intergrate($request);
        return $efris_res;

       
        
    }
    public function get_vat_code(Request $request){
        
        $class = ClassModel::where('class', $request->class)->first();
        $optional_vat=$class->optional_vat;
        $vat_code = $class->vat_code;
       
        $data = collect([(object)['vat_code' => $vat_code,'default'=>'Y']]);
        
        if($optional_vat == "Y"){
            $optional_data=DB::table("applicable_ratings")->where("dept",$class->dept)->get('vat_code');
            $optional_data->push((object)['vat_code' => $vat_code,'default'=>'Y']);
            $data=$optional_data;
          
        }

        return $data;

     
      

    }

    function is_facult_in(Request $request){
        
        $endorsement = $request->endorsement;
        $dcontrol=Dcontrol::where('endt_renewal_no',$endorsement)->first();
        $type_of_bus = Bustype::where('type_of_bus',$dcontrol->type_of_bus)->first();
        if($type_of_bus->facult_in =='Y'){
            $facult_in='Y';
        }else {
            $facult_in='N';
        }

        return $facult_in;
    }


    public function policyRecommendations()                     
        {
            $users= Aimsuser::where('left_company','N')
                    ->get();
            return view('gb.underwriting.policy_recommendations',[ 'users'=>$users]);
        }

    public function recommendationsDatatable(Request $request, $source)
    {
        try {
            $query = DB::table('POLICY_NOTES')
                ->select('title', 'status', 'created_at', 'notes', 'addressed_to', 'assigned_to', 'note_id', 'created_by', 'source', 'policy_no', 'reference_no', 'slug', 'reconote_type', 'date_assigned')
                ->whereNotNull('addressed_to')
                ->orderBy('created_at', 'DESC');
                
            if ($source === 'UW') {
                $query->where('source', 'U/W');
            } elseif ($source === 'CLM') {
                $query->where('source', 'CLM');
            } else {
                
            }
    
            $recommendations = $query->get();
            
            $userName = trim(Auth::user()->user_name);
    
            $recommendations = $recommendations->map(function($recommendation) use ($userName) {
                $recotype = RecommendationTypes::where('source', $recommendation->source)
                    ->where('reco_type', $recommendation->reconote_type)
                    ->first();
    
                if ($recotype) {
                    $addressedTo = trim($recotype->addressed_to);
                    $action_permission = DB::table('permissions')
                        ->select('id', 'name', 'slug')
                        ->where('id', $recotype->assignee_permission)
                        ->first();
    
                    $recommendation->permissions = [
                        'raise_recommendation_permission' => Auth::user()->user_name == $recommendation->created_by ? Gate::check('raise-recommendation') : false,
                        'action_on_recommendation_permission' => Auth::user()->user_id == $recommendation->assigned_to
                            ? ($action_permission ? Gate::check(trim($action_permission->slug)) : false)
                            : false,
                        'addressed_to' => $userName == $addressedTo,
                    ];
                } else {
                    $recommendation->permissions = [
                        'raise_recommendation_permission' => false,
                        'action_on_recommendation_permission' => false,
                        'addressed_to' => false,
                    ];
                }
    
                return $recommendation;
            });
    
            return Datatables::of($recommendations)
            ->editColumn('created_at', function ($reconotes) {
                return formatDate($reconotes->created_at);
            })
            ->editColumn('updated_at', function ($reconotes) {
                return formatDate($reconotes->updated_at);
            })
            ->addColumn('assigned_to', function ($recommendations) {
                return Aimsuser::where('user_id', $recommendations->assigned_to)->value('user_name');
            })
            ->addColumn('action', function ($row) {
                $buttons = '<button class="btn btn-xs btn-primary view-btn" style="margin-right: 5px;"><i class="fa fa-eye"></i> View</button>';
        
                $permissions = $row->permissions;
                $status = $row->status;
        
                if ($status !== 'cancelled' && $status !== 'closed') {
                    if ($permissions['raise_recommendation_permission'] && $permissions['addressed_to'] && $permissions['action_on_recommendation_permission']) {
                        $buttons .= '<button class="btn btn-xs btn-success update-btn" style="margin-right: 5px;"><i class="fa fa-pencil"></i> Edit</button>';
                        $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                        $buttons .= $status === 'assigned'
                            ? '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Re-assign</button>'
                            : '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Assign</button>';
                        $buttons .= '<button class="btn btn-xs btn-info action-btn" style="margin-right: 5px;"><i class="fa fa-check"></i> Action</button>';
                    } elseif ($permissions['raise_recommendation_permission'] && $permissions['addressed_to']) {
                        $buttons .= '<button class="btn btn-xs btn-success update-btn" style="margin-right: 5px;"><i class="fa fa-pencil"></i> Edit</button>';
                        $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                        $buttons .= $status === 'assigned'
                            ? '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Re-assign</button>'
                            : '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Assign</button>';
                    } elseif ($permissions['raise_recommendation_permission'] && $permissions['action_on_recommendation_permission']) {
                        $buttons .= '<button class="btn btn-xs btn-success update-btn" style="margin-right: 5px;"><i class="fa fa-pencil"></i> Edit</button>';
                        $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                        $buttons .= $status === 'assigned'
                            ? '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Re-assign</button>'
                            : '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Assign</button>';
                        $buttons .= '<button class="btn btn-xs btn-info action-btn" style="margin-right: 5px;"><i class="fa fa-check"></i> Action</button>';
                        } elseif ($permissions['raise_recommendation_permission']) {
                        $buttons .= '<button class="btn btn-xs btn-success update-btn" style="margin-right: 5px;"><i class="fa fa-pencil"></i> Edit</button>';
                        $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                    } elseif ($permissions['addressed_to'] && $permissions['action_on_recommendation_permission']) {
                        $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                        $buttons .= $status === 'assigned'
                            ? '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Re-assign</button>'
                            : '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Assign</button>';
                        $buttons .= '<button class="btn btn-xs btn-info action-btn" style="margin-right: 5px;"><i class="fa fa-check"></i> Action</button>';
                    } elseif ($permissions['addressed_to']) {
                        $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                        $buttons .= $status === 'assigned'
                            ? '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Re-assign</button>'
                            : '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Assign</button>';
                    } elseif ($permissions['action_on_recommendation_permission']) {
                        $buttons .= '<button class="btn btn-xs btn-info action-btn" style="margin-right: 5px;"><i class="fa fa-check"></i> Action</button>';
                    }
                }
        
                return $buttons;
            })
            ->escapeColumns([])
            ->make(true);
        } catch (\Exception $e) {
            return response()->json(['error' => 'An error occurred while processing your request.'], 500);
        }
    }

    public function getComprehensiveVehiclesDocuments(Request $request){
        $endt_renewal_no = trim($request->endt_renewal_no);
        $comprehensiveVehicles = Modtlmast::where('endt_renewal_no', $endt_renewal_no)->where('covertype', 1)->whereNot('deleted', 'Y')->get();

        return DataTables::of($comprehensiveVehicles)
            ->editColumn('valuer', function ($comprehensiveVehicles) {
                return Clparam::where('claimant_code', $comprehensiveVehicles->valuer)->value('name');
            })
            ->addColumn('inspection_doc', function ($modtl_rec) {

                if ($modtl_rec->covertype == 1 && $modtl_rec->valuation == 'N' && !is_null($modtl_rec->valuer)) {
                    $encrypted_endorse_no = encrypt($modtl_rec->endt_renewal_no);

                    $btn = '<a href="/vehicleValuationInspection/' . $encrypted_endorse_no . '/' . $modtl_rec->reg_no . '" target="_blank" class="btn btn-sm-default view-veh-inspection" title="View Letter for Valuation"> 
                        <i class="fa fa-file-pdf-o" title="View Inspection Letter"> </i>
                    </a>';
                } else {
                    $btn = '<span>No Valuation Letter</span>';
                }

                return $btn;
            })
            
            ->escapeColumns([])
            ->make(true);

    }

    
    public function getKYCDetails(Request $request) {
        $client_number = $request->get('client_number');
        $client = Client::where('client_number', $client_number)->first();
    
        return response()->json([
            'client_type' => $client->client_type,
            'id_number' => $client->id_number,
            'incorporation_cert' => $client->incorporation_cert,
            'name' => $client->name,
            'email' => $client->e_mail,
            'mobile_no' => $client->mobile_no
        ]);
    }
}

