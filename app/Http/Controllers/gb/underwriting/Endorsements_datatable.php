<?php

namespace App\Http\Controllers\gb\underwriting;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Dcontrol;
use App\Debitmast;
use App\Polremast;
use App\Polremastend;
use App\MarineUTLDebit;
use App\Marinemasterhist;
use Yajra\Datatables\Datatables;


class Endorsements_datatable extends Controller
{
    public function show_endorsements_for(Request $request){

    	$policy_no=$request->get('policy_no');
        $query = Dcontrol::query()
        ->where('policy_no',$policy_no)
        ->whereNull('delete_str');

        // dd($qeury);
	     return datatables::of($query)

            /*->editColumn('policy_no', function ($fn) {
                return formatPolicyOrClaim($fn->policy_no) ;
            })*/
             ->editColumn('endt_renewal_no', function ($fn) {
                return formatPolicyOrClaim($fn->endt_renewal_no) ;
            })

            ->editColumn('endorse_date', function ($fn) {
                return formatDate($fn->endorse_date) ;
            })

             ->editColumn('period_from', function ($fn) {
                return formatDate($fn->period_from) ;
            })

            ->editColumn('expiry_date', function ($fn) {
                return formatDate($fn->expiry_date) ;
            })
            ->editColumn('renewal_date', function ($fn) {
                return formatDate($fn->renewal_date) ;
            })
            ->addColumn('Status', function ($fn) {
                
                if($fn->trans_type == 'NIL'){
                    if($fn->committed == 'Y'){
                        return 'Committed';
                    }
                    else{
                        return 'Not Committed';
                    }
                }
                else{
                    $marine = Marinemasterhist::where('endt_renewal_no',$fn->endt_renewal_no)->first();
                    $count=Debitmast::where('endt_renewal_no',$fn->endt_renewal_no)->count();
                    $mac_count=MarineUTLDebit::where('endt_renewal_no',$fn->endt_renewal_no)->count();

                    if(!is_null($marine)){
                        if($count > 0 || $mac_count > 0){
                            return 'Debited';
                        }
                        else if($marine->commit_transaction == 'Y' && ($marine->opentype_code == 'CMC' || ($marine->opentype_code == 'ZPR' && !in_array($marine->trans_type,['POL','REN'])))){
                            return 'Committed';
                        }
                        else if($marine->commit_transaction != 'Y' && ($marine->opentype_code == 'CMC' || ($marine->opentype_code == 'ZPR' && !in_array($marine->trans_type,['POL','REN'])))){
                            return 'Not Committed';
                        }
                        else{
                            return 'Not Debited';
                        }
                    }
                    else{
                        if($count > 0){
                            return 'Debited';
                        }
                        else{
                            return 'Not Debited';
                        }

                    }
                    
                }                
            })
            ->addColumn('Restatus', function ($fn) {
                if($fn->trans_type == 'NIL'){
                    if($fn->committed == 'Y'){
                        return 'Committed';
                    }
                    else{
                        return 'Not Committed';
                    }
                }
                else{
                    $count=Polremastend::where('endt_renewal_no',$fn->endt_renewal_no)->count();
                    $mac_count=MarineUTLDebit::where('endt_renewal_no',$fn->endt_renewal_no)->count();
                    $marine = Marinemasterhist::where('endt_renewal_no',$fn->endt_renewal_no)->first();

                    if(!is_null($marine)){
                        if($count > 0 || $mac_count > 0){
                            return 'Reinsured';
                        }
                        else if($marine->commit_transaction == 'Y' && $marine->opentype_code == 'CMC'){
                            return 'Committed';
                        }
                        else if($marine->commit_transaction != 'Y' && $marine->opentype_code == 'CMC'){
                            return 'Not Committed';
                        }
                        else{
                            return 'Not Reinsured';
                        }
                    }
                    else{
                        if($count > 0){
                            return 'Reinsured';
                        }else{
                            return 'Not Rensured';
                        }

                    }
                }               
            })
            ->addColumn('edit', function ($fn) {             

                if ($fn->trans_type == 'PTA') {
                    $check_debit = Debitmast::where('endt_renewal_no',$fn->endt_renewal_no)->count();
                    return '<i class="fa fa-pencil-square-o" onclick="editComesa(`'.$fn->endt_renewal_no.'`, `'.$check_debit.'`)"></i>';
                }else{
                    return '_';
                }
                
            })
            ->escapeColumns([])
            //->withTrashed()
            ->make(true);

    }
}

