<?php

namespace App\Http\Controllers\gb\underwriting;


use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use App\Client;
use App\PartnerBank;
use App\Branch;
use App\MailBox;
use Illuminate\Support\Facades\Gate;

class Client_datatable extends Controller
{
	//
	public function getAClient(Request $request)
	{
		$client = Client::where('client_number', $request->clnt_no)->first();
		$query = "SELECT a.bank_code, a.branch_code, b.bank_code, a.BANK_ACCOUNT_NO as account_no, b.description as bankname,
					a.bank_account_name as holdername, c.branch as branchname FROM partnerbank a 
					LEFT JOIN olbnknames b on TRIM(a.BANK_CODE) = TRIM(b.BANK_CODE)
					LEFT JOIN olbranch c ON TRIM(c.BANK_CODE) = TRIM(b.BANK_CODE) and TRIM(c.<PERSON>ANCH_CODE) = TRIM(a.<PERSON>ANCH_CODE) 
					WHERE TRIM(a.partner_number)='$request->clnt_no'";
		// $clientbanks = PartnerBank::where('partner_number',$request->clnt_no)->get();
		$clientbanks = DB::select($query);

		$data = [
					'client'=> $client,
					'clientbanks'=> $clientbanks
				];
		return $data;
	}
    public function index()
	{

		//$client=Client::where('approved','Y')->get();

		//$client=Client::query()->where('approved','Y');


        //		$client = Client::select(['name','client_number','id_number','pin_number','telephone'],
        $client = Client::selectRaw("name,(CASE WHEN client.client_type = 'I' then 'INDIVIDUAL' else 'CORPORATE' END) AS client_type1,client_number,partnernumber,(CASE WHEN client.client_type = 'I' then identity_type.identity_descr else 'CERTIFICATE OF INCORPORATION' End) as identity_descr,(CASE WHEN client.client_type = 'I' then id_number else INCORPORATION_CERT END) AS id_number,pin_number,telephone,mobile_no")
		                  ->LEFTJOIN('identity_type', 'client.identity_type', '=', 'identity_type.identity_code');
		
	    return Datatables::of($client)
		->addColumn('process',function ($fn) {

			return '<a class="process_client btn btn-sm btn-default"
						style="margin-top: -4px;" 
						onclick="processClient(`' . $fn->client_number . '`)">
						<i class="fa fa-plus"></i> Policies
					</a>';

		})
	    ->addColumn('edit',function ($fn) {
			$btn = '';
			if (Gate::check('amend-client-details')) {
				$btn .= '<a href="'.route('edit_client',['client_number'=>$fn->client_number,'nil_endt'=>'N']) .'"
						<i class="glyphicon glyphicon-edit" style="color: #D12953;"></i>
					</a>';
			}
			return $btn;

		})
		->addColumn('view',function ($fn) {
			$btn = '';
			if (Gate::check('access-client')) {
				// $btn .= '<a onclick="clientDetails(`' . $fn->client_number . '`)" 
				// 		<i class="fa fa-eye" style="color: #3D3DB8;"></i>
				// 	</a>';
				// 	'
          

				$btn .= '<a href="'.route('view_client',['client_number'=>$fn->client_number,'nil_endt'=>'Y']) .'"
						<i class="fa fa-eye" style="color: #3D3DB8;"></i>
					</a>';
			}
			return $btn;
		})
		->addColumn('documents',function ($fn) {

			return '<a href="#" class="documents btn btn-sm btn-default" data-client_number="'.$fn->client_number.'" style="margin-top: -4px;"><i class="fa fa-plus"></i>
				documents
					</a>';

		})
		->escapeColumns(['client_type1'])
	    ->make(true);
	}

}
