<?php

namespace App\Http\Controllers\gb\underwriting;
use Auth;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\gb\underwriting\Risk;
use App\Modtl;
use App\Modtlend;
use App\Polsect;
use App\Polsectend;
use App\Reintray;
use App\Dcontrol   ;
use App\Covertype   ;
use App\ClassModel;
use App\Classtype;
use App\Clparam;
use App\Make   ;
use App\Veh_Model   ;
use App\Vehcol   ;
use App\Bodytype   ;
use App\Classsect   ;
use App\Extparams   ;
use App\Accessory_param;
use App\Accessory_make;
use App\Accessory;
use App\Extensions;
use App\Debitmast;
use App\Bustype;
use App\Polmaster;
use Session;
use App\Vehiclemodelyear;
use App\Motorate;
use App\Valuers;
use App\Discounts_loadings;
use App\Pipcnam;
use Carbon\Carbon;
use App\Certalloc;
use App\Models\Modtlmast;

class Extra_endorsements extends Controller
{
    public function index(Request $request){

      
      $old_endt_renewal_no=$request->post('endt_renewal_no');

      //dd($old_endt_renewal_no);

      return view('gb.underwriting.extra_form');

    }

    public function get_debit(Request $request){

     /* $endt_renewal_no=$request->get('endt_renewal_no');
      $debit=Debitmast::where('endt_renewal_no',$endt_renewal_no)->get();

      if(($debit[0]->cnc_rfn_endt == $endt_renewal_no) && ($debit[0]->doc_type == 'RFN')){
            $sum = Sum($debit->gross_amount);

      }
      $diff=$debit[0]->gross_amount + $sum;
      echo $diff; */


      $endt_renewal_no=$request->get('endt_renewal_no');
      $debit=Debitmast::where('endt_renewal_no',$endt_renewal_no)->get();
      
      $latest_rec = Debitmast::whereRaw("policy_no='".$debit[0]->policy_no."' and (entry_type_descr='POL' or entry_type_descr='REN')")->orderBy('effective_date','DESC')->limit(1)->get()[0];


      if ($latest_rec->entry_type_descr == 'POL') {
        $debitmast = Debitmast::whereRaw("policy_no='".$latest_rec->policy_no."' and effective_date>='".$latest_rec->effective_date."'")->sum('gross_amount');
      }
      else if($latest_rec->entry_type_descr == 'REN'){
        $debitmast = Debitmast::whereRaw("policy_no='".$latest_rec->policy_no."' and effective_date>='".$latest_rec->effective_date."'")->sum('gross_amount');
      }

      echo $debitmast;
    }

    public function modify(Request $request){


      $endt_renewal_no=$request->get('endt_renewal_no');
      $cls=$request->get('cls');

      $old_endt_renewal_no=$request->get('old_endt_renewal_no');

      $reg_no=$request->get('registration');

      $modtl=Modtl::where('endt_renewal_no',$old_endt_renewal_no)->where('reg_no',$reg_no)->get();
      $modtl=$modtl[0];


      $dcontrol_new=Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();

      $dcontrol = Dcontrol::where('endt_renewal_no', $old_endt_renewal_no)->get();
      $type_of_bus = Bustype::where('type_of_bus',$dcontrol[0]->type_of_bus)->first();
      $lead_share = $type_of_bus->leader;
      $polmaster = Polmaster::where('endorse_no', $old_endt_renewal_no)->get();

      if($dcontrol[0]->company_share != null or $dcontrol[0]->company_share > 0){
        $co_sum_insured = ($modtl->sum_insured * 100) / $dcontrol[0]->company_share;
      }else{
        $co_sum_insured = $modtl->sum_insured;
      }

        //check if the endorsement has been debitted
        $debitmast=Debitmast::where('endt_renewal_no',$endt_renewal_no)->count();
        if($debitmast>0){

              Session::flash('warning','Endorsement already debited.Kindly raise an extra endorsement to modify the details for this policy');

              //redirect to risk page
                 return redirect()->route('risk',['endt_renewal_no' =>$dcontrol[0]->endt_renewal_no,'policy_no'=>$dcontrol[0]->policy_no,'cls'=>$cls,'cmb'=>'N']);
  
        }else{

        //check if vehicle has been refunded
        $modtl_refunded=Modtl::where('policy_no',$dcontrol_new[0]->policy_no)->where('reg_no',$reg_no)->get();

        if($modtl_refunded[0]->full_refund=='Y'){

          Session::flash('warning','Cannot make changes to a vehicle that has fully been refunded.');

          //redirect to risk page
          return redirect()->route('risk',['endt_renewal_no' =>$dcontrol[0]->endt_renewal_no,'policy_no'=>$dcontrol[0]->policy_no,'cls'=>$cls,'cmb'=>'N']);

        }else{

          $cover_types=Covertype::all();
          $classtype=Classtype::where('class',$cls)->get();
          $make=Make::all();
          $model=Veh_Model::all();
          $color=Vehcol::all();
          // $bodytype=Bodytype::all();
          $classsect=Classsect::where('class',$cls)->whereIn('section_no',array(4, 5, 6,7))->get(['section_no','section_description','section_code']);
        
          $extensions=Extparams::where('class',$cls)->get();
                
          $motorate = Motorate::where('class',$cls)->where('cover',$modtl->cover_type)->where('classtype',$modtl->classtype)->get();
               //dd($motorate);
          $motorate=$motorate[0];
          
          //get accessories for the vehicle
          $all_accessories=Accessory_param::all();
          $all_makes=Accessory_make::all();
          // $valuers=Clparam::where('record_type', '9')->where('valuer', 'Y')->get();
          $valuers = Clparam::where('valuer', 'Y')->where('status','ACTIVE')->get();

          $accessory=Accessory::where('endt_renewal_no',$old_endt_renewal_no)->where('reg_no',$modtl->reg_no)->get();

          $total_acc_prem=Accessory::where('endt_renewal_no',$old_endt_renewal_no)
                                    // ->where('compute_prem','Y')
                                    ->where('reg_no',$modtl->reg_no)
                                    ->sum('endorse_amount');


          //get extensions for the vehicle
          $vehicle_extensions=Extensions::where('endt_renewal_no',$old_endt_renewal_no)
                                        ->where('reg_no',$modtl->reg_no)
                                        // ->where('compute_prem','Y')
                                        ->get();

          $total_ext_prem=Extensions::where('endt_renewal_no',$old_endt_renewal_no)->where('reg_no',$modtl->reg_no)
                                    // ->where('compute_prem','Y')
                                    ->sum('endorse_amount');

          $total_disc=Discounts_loadings::where('endt_renewal_no',$old_endt_renewal_no)
                                        ->where('reg_no',$modtl->reg_no)
                                        ->where('type','D')
                                        // ->where('compute_prem','Y')
                                        ->sum('endorse_amount');

          $total_loading=Discounts_loadings::where('endt_renewal_no',$old_endt_renewal_no)
                                            ->where('reg_no',$modtl->reg_no)
                                            // ->where('compute_prem','Y')
                                            ->where('type','L')
                                            ->sum('endorse_amount');

          $total_disc_load = (int)$total_loading + (int)$total_disc;
          //sessions to store total_acc_prem and total_ext_prem

          session(['total_acc_prem' => $total_acc_prem]);

          session(['total_ext_prem' => $total_ext_prem]);

          session(['total_loading' => $total_loading]);

          session(['total_disc' => $total_disc]);

          session(['total_disc_load' => $total_disc_load]);

          $motor_trade = ClassModel::where('class',$cls)->get(['motor_trade']);
          $fetched_class = ClassModel::where('class',$cls)->first();

          $motor_trade = $motor_trade[0]->motor_trade;

          $vehiclemodelyear=Vehiclemodelyear::where('year','>',2000)->distinct()->orderBy('year', 'DESC')->get(['year']);

          $vehiclemodelyear_make=Vehiclemodelyear::distinct()->orderBy('make', 'DESC')->get(['make']);

          $vehiclemodelyear_model=Vehiclemodelyear::distinct()->orderBy('model', 'DESC')->get(['model']);
          
          $bodytype=Vehiclemodelyear::distinct()->orderBy('bodytype', 'DESC')->get(['bodytype']);
          $pipcnam = Pipcnam::where('record_type', 0)->first();

          

          //return $request->old_endt_renewal_no;
          if(!$endt_renewal_no){
            $endt_renewal_no=$old_endt_renewal_no;  
          }

          $binder = app('App\Http\Controllers\gb\underwriting\Risk')->getBinderRate($dcontrol[0]);

          // dd($motorate);

          return view('gb.underwriting.motor_dtl_ext',[
            'dcontrol'=>$dcontrol[0],
            'dcontrol_new'=>$dcontrol_new[0],
            'polmaster'=>$polmaster[0],
            'binder'=>$binder,
            'modtl_rec'=>$modtl,
            'sum_insured'=>$co_sum_insured,
            'lead_share'=>$lead_share,
            'accessory'=>$accessory,
            'all_accessories'=>$all_accessories,
            'accessory_types'=>$all_accessories,
            'all_makes'=>$all_makes,
            'total_acc_prem'=>$total_acc_prem,
            'total_ext_prem'=>$total_ext_prem,
            'class'=>$cls,
            'classModel'=>$fetched_class,
            'motor_trade'=>$motor_trade,
            'cover_type'=>$cover_types,
            'classtype'=>$classtype,
            'make'=>$make,
            'model'=>$model,
            'bodytype'=>$bodytype,
            'color'=>$color,
            'valuers'=>$valuers,
            'class_sections'=>$classsect,
            'endt_renewal_no'=>$endt_renewal_no,
            'extensions'=>$extensions,
            'old_endt_renewal_no'=>$old_endt_renewal_no,
            'vehicle_extensions'=>$vehicle_extensions,
            'vehiclemodelyear'=>$vehiclemodelyear,
            'vehiclemodelyear_make'=>$vehiclemodelyear_make,
            'vehiclemodelyear_model'=>$vehiclemodelyear_model,
            'motorate'=>$motorate,
            'total_disc'=>$total_disc,
            'total_loading'=>$total_loading,
            'pipcnam'=>$pipcnam
          ]);

        }

      } 

    }
    public function remove(Request $request){

      // decrale variables to use
      $cancel_mode = $request->cancel_mode;
      $del_car = $request->del_car;
      $endt_renewal_no=$request->get('endt_renewal_no');
      $cls=$request->get('cls');
      $old_endt_renewal_no=$request->get('old_endt_renewal_no');
      $reg_no=$request->get('registration');
      $cert_cancellation_rsn=trim($request->get('cncreason'));
     

      // process for cancellation begins
      $modtl = Modtl::where('endt_renewal_no',$old_endt_renewal_no)->where('reg_no',$reg_no)->get();

      $modtl=$modtl[0];

      //new record
      $dcontrol=Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
      $policy_no=trim($dcontrol[0]->policy_no);
     // dd($policy_no);
      //old record
      $dcontrol_old=Dcontrol::where('endt_renewal_no', $old_endt_renewal_no)->get();
      //check if certificate was issued
    
      $cert_info = DB::select("select * from CERTALLOC where trim(POLICY_NO)='".$policy_no."' and trim(reg_no)='".$reg_no."' and cert_status=1 ORDER BY ISSUE_TIME DESC FETCH NEXT 1 ROWS ONLY");
      //dd($cert_info);
      $cert_count= count($cert_info);
      $cert_status=null;
      if($cert_count > 0 ){
        $cert_info=$cert_info[0];
         $cert_no= trim($cert_info->aki_cert_no);
        //dd($cert_no);
        
    
        $rec=DB::table('certalloc')->where('policy_no',$policy_no)
                  ->where('aki_cert_no',$cert_no)
                                       ->update([
                                                    'cancellationreason' => $cert_cancellation_rsn,
                                                    'cancellationflag' => 'Y',
                                                    
                                                ]);
      //dd($rec);
       
      }

     //dd($cert_status['msg']);

      # Trans type check when cancelling a vehicle
      switch ($dcontrol[0]->trans_type) {
        case 'POL':
        case 'REN':
          # For policies and renewals
          $del = DB::select("select count(*) count from modtlend where endt_renewal_no = '".$modtl->endt_renewal_no."' and reg_no = '".$modtl->reg_no."' and reg_no not in (select reg_no from modtlend where endt_renewal_no = '".$prev_endt_no->endt_renewal_no."')")[0];

          if ((int) $del->count == 1) {
            $modtlDeletion = Modtl::where('policy_no', $modtl->policy_no)
            ->where('endt_renewal_no', $modtl->endt_renewal_no)
            ->where('reg_no', $reg_no)
            ->delete();

            $modtlendDeletion = Modtlend::where('policy_no', $modtl->policy_no)
            ->where('endt_renewal_no', $modtl->endt_renewal_no)
            ->where('reg_no', $reg_no)
            ->delete();

            $polsectDeletion = Polsect::where('policy_no', $modtl->policy_no)
            ->where('endt_renewal_no', $modtl->endt_renewal_no)
            ->where('plot_no', $reg_no)
            ->delete();
            
            $polsectendDeletion = Polsectend::where('policy_no', $modtl->policy_no)
            ->where('endt_renewal_no', $modtl->endt_renewal_no)
            ->where('plot_no', $reg_no)
            ->delete();

            $reinTrayDeletion = Reintray::where('endt_renewal_no', $modtl->endt_renewal_no)
            ->where('line_no', $modtl->ln_no)
            ->delete();

            Session::flash('success', 'Vehicle removed from the system');
          }
          
          return redirect()->route('risk',['endt_renewal_no' =>$dcontrol[0]->endt_renewal_no,'policy_no'=>$dcontrol[0]->policy_no,'cls'=>$dcontrol[0]->class,'cmb'=>'N']);
        break;
        
        default:
          # Proceed for all other transtypes
        break;
      }

      // check if check mode was selected
      if(!$cancel_mode ){
        Session::flash('warning','select the mode of cancelling vehicle to proceed');

        return redirect()->route('risk',['endt_renewal_no' =>$dcontrol[0]->endt_renewal_no,'policy_no'=>$dcontrol[0]->policy_no,'cls'=>$dcontrol[0]->class,'cmb'=>'N']);
      }
      
      $prev_endt_no = DB::select(DB::raw("select endt_renewal_no 
            from dcontrol where policy_no='".$modtl->policy_no."' ORDER BY dtrans_no desc offset 1 rows"))[0];

      // just confirm for del_car if its 1
      $del = DB::select(DB::raw("select count(*) count
                            from modtlend 
                            where endt_renewal_no = '".$modtl->endt_renewal_no."' and reg_no = '".$modtl->reg_no."' and reg_no
                            not in (select reg_no from modtlend where endt_renewal_no = '".$prev_endt_no->endt_renewal_no."')"))[0];


      
      if ((int) $del->count == 1) {
        $modtlend_marked=Modtlend::where('policy_no',$modtl->policy_no)->where('endt_renewal_no',$modtl->endt_renewal_no)
                                        ->where('reg_no',$reg_no)->delete();

        $modtlend_marked=Modtl::where('policy_no',$modtl->policy_no)->where('endt_renewal_no',$modtl->endt_renewal_no)
                                        ->where('reg_no',$reg_no)->delete();


        Polsect::where('policy_no',$modtl->policy_no)->where('endt_renewal_no',$modtl->endt_renewal_no)
                ->where('plot_no',$reg_no)->delete();
                
        Polsectend::where('policy_no',$modtl->policy_no)->where('endt_renewal_no',$modtl->endt_renewal_no)
                ->where('plot_no',$reg_no)->delete();

        Reintray::where('endt_renewal_no',$modtl->endt_renewal_no)
            ->where('line_no', $modtl->ln_no)
            ->delete();  

        Session::flash('success','Vehicle Cancelled From The system');

        return redirect()->route('risk',['endt_renewal_no' =>$dcontrol[0]->endt_renewal_no,'policy_no'=>$dcontrol[0]->policy_no,'cls'=>$dcontrol[0]->class,'cmb'=>'N']);
      }

       // prev vals
      //get new endorsement values
      $prev_total_sum_insured = Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' , '<>','Y')->sum('sum_insured');
      $prev_total_endorse_amount = Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' , '<>','Y')->sum('endorse_amount');
      $prev_total_items = Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' , '<>','Y')->count();
      $prev_total_premium = Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' , '<>','Y')->sum('annual_premium');
      $prev_total_renewal_prem = Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' , '<>','Y')->sum('first_premium');
      $prev_total_endorse_amt = Modtlend::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->sum('gross');

      

      if($dcontrol[0]->company_share != null or $dcontrol[0]->company_share > 0){
        $co_sum_insured = ($modtl->sum_insured * 100) / $dcontrol[0]->company_share;
      }else{
        $co_sum_insured = $modtl->sum_insured;
      }
      //check if the endorsement has been debitted
      $debitmast=Debitmast::where('endt_renewal_no',$endt_renewal_no)->count();
      if($debitmast>0){
            Session::flash('warning','Endorsement already debited.Kindly raise an extra endorsement to modify the details for this policy');
                return redirect()->route('risk',['endt_renewal_no' =>$dcontrol[0]->endt_renewal_no,'policy_no'=>$dcontrol[0]->policy_no,'cls'=>$cls,'cmb'=>'N']);
      }else{
        //check if vehicle has been refunded
        $modtl_refunded=Modtl::where('policy_no',$dcontrol[0]->policy_no)->where('reg_no',$reg_no)->get();

        if($modtl_refunded[0]->full_refund=='Y'){
          Session::flash('warning','Cannot make changes to a vehicle that has fully been refunded.');
          return redirect()->route('risk',['endt_renewal_no' =>$dcontrol[0]->endt_renewal_no,'policy_no'=>$dcontrol[0]->policy_no,'cls'=>$cls,'cmb'=>'N']);

        }else{
          //access the modtl row removed above
          $modtl_removed = Modtl::where('endt_renewal_no', $dcontrol_old[0]->endt_renewal_no)->where('reg_no', $reg_no)->get();
          $modtl_removed = $modtl_removed[0];
          
          $modtl_count=Modtl::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)->where('reg_no',$reg_no)->count();
          $modtlend_count=Modtlend::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)->where('reg_no',$reg_no)->count();
          $polsect_count=Polsect::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)->where('plot_no',$reg_no)->count();
          $polsectend_count=Polsectend::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)->where('plot_no',$reg_no)->count();

          // call Risk controller for proration
          $risk_class = new Risk();

          
          // dd($request, $del, $modtl_count);
          if($modtl_count > 0){
            if($dcontrol[0]->trans_type == "POL"){
              $modtl_marked=Modtl::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)
                                  ->where('reg_no',$reg_no)->delete();
              $resp = 1;                                  
            }
            else{
              // fetch data in modtl for computation
              $modtl_marked_info=Modtl::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)
                                  ->where('reg_no',$reg_no)->first();

              // cancel using proration
              $prorated_fig = $risk_class->prorate( $old_endt_renewal_no, $modtl_marked_info->gross);

              // Cancel using short term
              // ???

              $modtl_marked=Modtl::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)
                                  ->where('reg_no',$reg_no)->update([
                                      'endorse_amount' => abs($prorated_fig) * -1,
                                      'temp_pol_amt' => abs($prorated_fig) * -1,
                                      'cancelled' => 'Y',
                                      'type' => 'D'
                                     ]);

            $modtl_marked=Modtlend::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)
                                     ->where('reg_no',$reg_no)->update([
                                         'endorse_amount' => abs($prorated_fig) * -1,
                                         'temp_pol_amt' => abs($prorated_fig) * -1,
                                         'cancelled' => 'Y',
                                         'type' => 'D'
                                        ]);
              $resp = 1;
            }
          }
          
          if($modtlend_count > 0){
            if($dcontrol[0]->trans_type == "POL"){
              $modtlend_marked=Modtlend::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)
                                        ->where('reg_no',$reg_no)->delete();
              $respend = 1;
            }
            else{
              $modtlend_marked=Modtlend::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)
                                        ->where('reg_no',$reg_no)->update([
                                          'cancelled' => 'Y',
                                          'type' => 'D'
                                          ]);
              $respend = 1;
            }

          }

          if($resp == 1 || $respend == 1){

            if($polsect_count > 0){
              if($dcontrol[0]->trans_type == "POL"){
                $polsect_marked=Polsect::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)->where('plot_no',$reg_no)->where('location', $modtl_removed->location)->delete();
              }
              else{
                $polsect_marked=Polsect::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)
                                        ->where('plot_no',$reg_no)->where('location', $modtl_removed->location)->update(['deleted'=>"Y"]);
              }
            }
            if($polsectend_count > 0){
              if($dcontrol[0]->trans_type == "POL"){
                $polsectend_marked=Polsectend::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)
                                              ->where('plot_no',$reg_no)->where('location', $modtl_removed->location)->delete();
              }
              else{
                $polsectend_marked=Polsectend::where('policy_no',$dcontrol[0]->policy_no)->where('endt_renewal_no',$old_endt_renewal_no)
                                              ->where('plot_no',$reg_no)->where('location', $modtl_removed->location)->update(['deleted'=>"Y"]);
              }
            }

            if($dcontrol[0]->trans_type == "POL"){
              //check for discounts
              $discounts_count = Discounts_loadings::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('reg_no', $reg_no)->where('type', 'D')->count();
              if($discounts_count > 0){
                $discounts_remove = Discounts_loadings::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('reg_no', $reg_no)->where('type', 'D')->delete();
              }
              //check for loadings
              $loadings_count = Discounts_loadings::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('reg_no', $reg_no)->where('type', 'L')->count();
              if($loadings_count > 0){
                $loadings_remove = Discounts_loadings::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('reg_no', $reg_no)->where('type', 'L')->delete();
              }
              //check for accessories
              $accessory_count = Accessory::where('policy_no', $dcontrol[0]->policy_no)->where('reg_no', $reg_no)->count();
              if($accessory_count > 0){
                $accessory_remove = Accessory::where('policy_no', $dcontrol[0]->policy_no)->where('reg_no', $reg_no)->delete();
              }
              //check for extensions
              $extensions_count = Extensions::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('reg_no', $reg_no)->count();
              if($extensions_count > 0){
                $extensions_remove = Extensions::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('reg_no', $reg_no)->delete();
              }
            }

            //get new endorsement values
            $w_total_sum_insured = (float)Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' , '<>','Y')->sum('sum_insured');
            $w_total_items = (float)Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' , '<>','Y')->count();
            $w_total_premium = (float)Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' , '<>','Y')->sum('annual_premium');
            $w_total_gross = (float)Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' , '<>','Y')->sum('gross');
            $w_total_renewal_prem =(float) Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' , '<>','Y')->sum('first_premium');

            //  $w_total_sum_insured = (float) $w_total_sum_insured - $prev_total_sum_insured;
            //  $w_total_premium = (float) $w_total_premium - $prev_total_premium;
            //  $w_total_renewal_prem = (float) $w_total_renewal_prem - $prev_total_renewal_prem;

            // dd($prev_total_sum_insured, $prev_total_endorse_amount, $prev_total_premium, $prev_total_renewal_prem, '/////',
            // $w_total_sum_insured, $w_total_premium, $w_total_renewal_prem, $s_total_premium
            //   );
            // dd($dcontrol[0]->trans_type);
            if($dcontrol[0]->trans_type == "EXT" || $dcontrol[0]->trans_type == "RFN" ){
              //instantiate risk class
              $risk = new Risk();
              //get prev endorsement
              $dcontrolprev = Dcontrol::where('policy_no', $dcontrol[0]->policy_no)->orderBy('dola', 'desc')->get();
              $prevendorse = $dcontrolprev[1]->endt_renewal_no;
              //modify cancelled vehicle
              $risk->modify_modtl($dcontrol[0]->endt_renewal_no, $dcontrol_old[0]->endt_renewal_no, $reg_no);

              $EndorsedVehiclesCount = Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' ,'Y')->where('type', 'D')->count();
              
              // Extensions
              $w_total_extensions_prem = Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' ,'Y')->where('type', 'D')->where('reg_no', $reg_no)->sum('extensions_prem');

              // windscreen
              $w_total_windscreen_prem = Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' ,'Y')->where('type', 'D')->where('reg_no', $reg_no)->sum('windscreen');

              // Accessories
              $w_total_accessories_prem = Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' ,'Y')->where('type', 'D')->where('reg_no', $reg_no)->sum('accessories');
              
              // current nett amount
              $w_total_endorse_prem = Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' ,'Y')->where('reg_no', $reg_no)->sum('nett');

              $new_endorse_amt = $w_total_gross - $prev_total_endorse_amt;
              //get amount to prorate less extensions
              $p_endorse_amount = $w_total_endorse_prem - ($w_total_extensions_prem + $w_total_windscreen_prem + $w_total_accessories_prem);
              //prorate
              $prorated_endorse_amount = $risk->prorate($dcontrol[0]->endt_renewal_no, $p_endorse_amount);

            }else{
              $w_total_endorse_amount = Modtl::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('cancelled' , '<>','Y')->sum('endorse_amount');
            }
            

            //update dcontrol
            $dcontrol_update = Dcontrol::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->first();
            $dcontrol_update->sum_insured = $w_total_sum_insured;
            $dcontrol_update->items_total = $w_total_items;
            $dcontrol_update->save();
  
            //access previous polmaster values for same policy
            $polmaster_prev = Polmaster::where('policy_no', $dcontrol[0]->policy_no)->get();
            $polmaster_prev = $polmaster_prev[0];
  
            //update polmaster            
            $polmaster_update = Polmaster::where('policy_no', $dcontrol[0]->policy_no)->first();
            $polmaster_update->sum_insured = $w_total_sum_insured;

            
            if ($EndorsedVehiclesCount == 0) {
              $polmaster_amount = 0 ;
            }
            else{  
              $polmaster_amount = (float)$polmaster_prev->endorse_amount;
            }

            if($dcontrol[0]->trans_type == "EXT" || $dcontrol[0]->trans_type == "RFN"){
              if($polmaster_amount == 0 ){
                $w_total_endorse_amount = (float)$polmaster_amount  - (float)$prorated_endorse_amount;
              }else{
                $w_total_endorse_amount = (float)$polmaster_amount  - (float)$prorated_endorse_amount;
              }
              // dd($w_total_endorse_amount, $polmaster_amount, $prorated_endorse_amount, $request->fleet);
            }else{
                $w_total_endorse_amount = $w_total_endorse_amount;
            }
            
            $polmaster_update->endorse_amount = $w_total_endorse_amount;
            $polmaster_update->renewal_premium = $w_total_renewal_prem;
            $polmaster_update->annual_premium = $w_total_premium;
            $polmaster_update->items_total = $w_total_items;
            $polmaster_update->save();

            
            // dd($prorated_endorse_amount, $w_total_endorse_prem, $new_endorse_amt, '//', $w_total_gross, $prev_total_endorse_amt, $w_total_endorse_amount, $polmaster_amount, $polmaster_prev);
  
            //remove values in reintray
            if($dcontrol[0]->trans_type == "POL"){
              $reintray = Reintray::where('endt_renewal_no', $dcontrol[0]->endt_renewal_no)->where('location', $modtl_removed->location)
                                  ->where('line_no', $modtl_removed->location)->delete();
            }
            $msg=$cert_status['msg'];
            $code=(int)$cert_status['code'];
            //dd($msg);
            Session::flash('success','Vehicle Marked As Removed From Endoresment Risks List');
            if($code == 0){
              Session::flash('warning',$msg);
            }elseif($code == 1){
              Session::flash('info',$msg);
            }
         
          }
          else{
            Session::flash('warning','Vehicle Not Marked As Removed From Endoresment Risks List');
          }
          if($request->fleet == 'Y'){
            return redirect()->route('fleet_form', ['endorsement' => $dcontrol[0]->endt_renewal_no, 'cls' => $dcontrol[0]->class]);
          }
          else{
            return redirect()->route('risk',['endt_renewal_no' =>$dcontrol[0]->endt_renewal_no,'policy_no'=>$dcontrol[0]->policy_no,'cls'=>$dcontrol[0]->class,'cmb'=>'N']);
          }
        }
      }
    }
    public function postCertCnc($cert_info,$cert_cancellation_rsn){
     
      //test data end
        $cert_info=$cert_info[0];
        $endorsement = trim($cert_info->endt_renewal_no);
        $cert_no =trim($cert_info->aki_cert_no);
        
        $cancellationreason=$cert_cancellation_rsn;
        $today = Carbon::now();
        $canceldate = date("d/m/Y H:i",strtotime($today));
       // dd($canceldate);
        $user_name = Auth::user()->user_name;
    	   $user_name = trim($user_name);
        //  /dd($cert_no);

          //$certcncreason = DB::table("certcncreason")->where('reason_id',$Cancellationid)->first()->reason_desc;
    
          $pipcnam = Pipcnam::where('record_type', 0)->first();
     
                          $array = array( 
                            "CertificateNumber"=>$cert_no,
                            "CancellationReason"=>$cancellationreason,
                            "Canceldate"=> $canceldate
                       );
          
          //dd($array,$CancellationReason);
          
              $client = new \GuzzleHttp\Client();
              $url = $pipcnam->aki_base_url.'CancelCertificate';
            
             
              $aki_user = $pipcnam->aki_user;
              $aki_passwd = $pipcnam->aki_passwd;
              // $token=aki_token();
              

              $response = '';
              $status="";

              // dd($url);
              
              if($pipcnam->digital_cert == 'Y'){
                    $response = $client->post($url, [
                      'auth' => [
                        $aki_user, 
                        $aki_passwd
                      ],
                      'headers' => [
                          'Content-Type'  => 'application/json',
                          'clientID' =>'D9138895-95E8-4492-B41F-7B86D9AA892F'
                        ],
                      'json'=>$array
        
                    ]);
                  $contents = json_decode($response->getBody()->getContents());
                 // dd($contents);
                  $status=$contents->success;
                   //dd($status);
                  if($status == true){
                      
                      // $update_certmast = Certmast::where('endt_renewal_no',$endorsement)
                      //                            ->where('aki_cert_no', $cert_no)
                      //                            ->update([
                      //                                     'who_cancelled' => $user_name,
                      //                                     'why_cancelled' => $cancellationreason,
                      //                                     'date_cancelled' => $canceldate,
                      //                                     'cert_status'=>99
                      //                                 ]);
                      $update_certalloc = Certalloc::where('endt_renewal_no',$endorsement)
                                                   ->where('aki_cert_no', $cert_no)
                                                   ->update([
                                                              'who_cancelled' => $user_name,
                                                              'why_cancelled' => $cancellationreason,
                                                              'date_cancelled' => $canceldate,
                                                              'cert_status'=>99
                                                           ]);

                      
                  

                      return [
                          'code' => 1,
                          'msg' => 'Certificate '.$CertificateNumber.' cancelled successfully'
                      ];
                      // return ['Certificate '.$CertificateNumber.' cancelled successfully'];
                  }else{
                      return [
                          'code' => 0,
                          'msg' => $contents->Error[0]->errorText
                      ];
                      // return json_encode($contents->Error[0]->errorText);
                  }
                 
              }
              
          // dd($contents);
         
  }

    public function refund(Request $request){

      $endt_renewal_no=$request->get('endt_renewal_no');

      $policy_no=$request->get('policy_no');

      $polmaster=Polmaster::where('policy_no',$policy_no)->get();

      $polmaster=$polmaster[0];

      $dcontrol=Dcontrol::where('endt_renewal_no',$polmaster->endorse_no)->get();

      $dcontrol=$dcontrol[0];

      $reg_no=$request->get('registration');

      $modtl_upd=Modtl::where('policy_no',$policy_no)
                     ->where('reg_no',$reg_no)
                     ->get();

     $debitmast_count=Debitmast::where('endt_renewal_no',$polmaster->endorse_no)->count();

     if($debitmast_count>0){

      Session::flash('Error','Cannot Refund vehicle.Endorsement has already been debited');

          return redirect()->route('risk',['endt_renewal_no' =>$endt_renewal_no,'policy_no'=>$policy_no]);

     }else{

      $modtl_upd=$modtl_upd[0];

      if($modtl_upd->full_refund=='Y'){

         Session::flash('warning','Cannot Refund vehicle.Vehicle has already been refunded the full amount');

          return redirect()->route('risk',['endt_renewal_no' =>$endt_renewal_no,'policy_no'=>$policy_no]);


      }else{

        $modtl=Modtl::where('policy_no',$policy_no)
                  ->where('reg_no',$reg_no)
                  ->where('ln_no',$modtl_upd->location)
                  ->update([
                    'endorse_amount'=>($modtl_upd->first_premium * -1),
                    'full_refund'=>'Y',
                    'endt_renewal_no'=>$polmaster->endorse_no,
                    'dtrans_no'=>$dcontrol->dtrans_no
                  ]);

        Session::flash('success','Vehicle refunded successfully');

          return redirect()->route('risk',['endt_renewal_no' =>$endt_renewal_no,'policy_no'=>$policy_no]);

      }

     }

      
  
    }
    public function get_endtl_modtl(Request $request){
      $modtls = Modtlmast::with('cover_type')->where('policy_no',$request->policy_no)
        ->where('status','ACT')
        ->get()
        ->toJson();
        $modtls = str_replace("'", "\\'",$modtls);
        // dd($modtls);
      return $modtls;
    }
}
