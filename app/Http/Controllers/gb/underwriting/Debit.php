<?php

namespace App\Http\Controllers\gb\underwriting;

use App\Madtl;
use App\Client;
use App\Reidoc;
use App\Pipcnam;
use App\Dcontrol;
use App\Certalloc;
use App\Debitmast;
use App\Polmaster;
use Carbon\Carbon;
use App\ClassModel;
use App\MarineUTLDebit;
use App\Marinemasterhist;
use App\Models\Modtlsumm;
use Illuminate\Http\Request;
use App\Classes\AddToSendEmail;
use App\Services\PreDebitService;
use App\Events\ProcessTreatyEvent;
use App\Models\DebitedPolDispatch;
use Illuminate\Support\Facades\DB;
use App\Models\External_api_status;
use App\Events\TiraIntegrationEvent;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Jobs\DispatchNotificationJob;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use App\Events\DispatchNotificationEvent;
use App\Models\SendEndtNotificationControl;
use App\Http\Controllers\gb\reinsurance\TreatyProcessing;
use App\Http\Controllers\gb\underwriting\MarineOpenProcessing;


class Debit extends Controller
{
    public function express_debit(Request $request)
    {
        
        DB::beginTransaction();
        try {
            $schem = schemaName();
            $gb = $schem['gb'];
            $gl = $schem['gl'];
            $common = $schem['common'];

            $endt_renewal_no = $request->get('endt_renewal_no');
            $reinsured = $request->get('interactive');
            $pre_debit = $request->get('pre_debit');

            $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
            
            $polamster = Polmaster::where('endorse_no', $endt_renewal_no)->first();

            $result = array('status' => 0);

            $debited = Debitmast::where('endt_renewal_no', $endt_renewal_no)->count();

            $class = ClassModel::where('class', $dcontrol->class)->first();

            $workflow_id = $class->workflow_id;
            $pid = 5;
            $user_name = is_null($request->username) ? trim(Auth::user()->user_name) : $request->username;
            
            $mac_debited = MarineUTLDebit::where('endt_renewal_no', $dcontrol->endt_renewal_no)->count();
            $marine_cert_count = Madtl::where('endt_renewal_no', $dcontrol->endt_renewal_no)->count();
            $marine_master_count = Marinemasterhist::where('endt_renewal_no', $dcontrol->endt_renewal_no)->count();
            $marine_utilized = Marinemasterhist::where('endt_renewal_no',$dcontrol->master_endt_no)->get()[0]['opentype_code'];

            if ($debited < 1 || $mac_debited < 1 ) {
               
                    if($class->open_cover == 'Y' && $marine_cert_count > 0 && $marine_master_count==0 && $marine_utilized == 'UTL'){
                        throw_if($mac_debited > 0,"RuntimeException",...["Endorsement $endt_renewal_no already debited"]);
                        $marine = new MarineOpenProcessing($endt_renewal_no);
                        $debitresp = $marine->debit_opencover_utilization($endt_renewal_no,$user_name,$pre_debit);
                        // dd($debitresp);
                    }else{
                        $debitresp = $this->debit_endorsement($endt_renewal_no,$user_name,$reinsured,$pre_debit);
                    }

                    if ($debitresp->status == 1) {

                            $result = array('status' => 1);

                            if($pre_debit == 'Y'){
                                if($class->open_cover=='Y' && $marine_cert_count > 0 && $master_count==0 && $marine_utilized=='UTL'){
                                    $pre_debit_dtl = DB::select("SELECT policy_no,endt_renewal_no,foreign_endorse_amount foreign_premium,foreign_endorse_amount foreign_net,
                                    0 foreign_comm_amount,0 foreign_vat_amount,0 foreign_levy_amount,0 foreign_stamp_duty,0 foreign_policy_fund,
                                    0 foreign_ira_tax_amt,0 foreign_fronting_amt,0 foreign_misc_fees,0 foreign_sticker_amount,0 tax_amount,
                                    0 levy_rate,0 policy_fund_rate,0 ira_tax_rate,0 fronting_rate,0 comm_rate,0 tax_rate, 0 reinsurer_fronting_rate,
                                    0 foreign_fund_guarantee_amt,0 fund_guarantee_rate,0 foreign_tpl_comm_amt, 0 foreign_comp_comm_amt,0 tpl_comm_rate,0 comp_comm_rate,0 foreign_reinsurer_fronting_amt
                                    FROM marine_utl_debitsum_snap WHERE endt_renewal_no='$endt_renewal_no'")[0];
                                    // dd($endt_renewal_no);
                                }else {
                                    $pre_debit_dtl = DB::select("SELECT policy_no,endt_renewal_no,foreign_premium,foreign_net,total_premium,total_sum_insured,
                                    foreign_comm_amount,foreign_vat_amount,foreign_levy_amount,foreign_stamp_duty,stamp_duty,foreign_policy_fund,
                                    foreign_ira_tax_amt,foreign_fronting_amt,foreign_misc_fees,foreign_sticker_amount,tax_amount,foreign_admin_fees,
                                    levy_rate,policy_fund_rate,ira_tax_rate,fronting_rate,comm_rate,tax_rate,gross_amount,levy_amount,comm_amount,
                                    foreign_fund_guarantee_amt,fund_guarantee_rate,foreign_tpl_comm_amt,foreign_comp_comm_amt,tpl_comm_rate,comp_comm_rate,
                                    agent_admin_fee,foreign_agent_admin_fee,agent_admin_fee_rate, reinsurer_fronting_rate, foreign_reinsurer_fronting_amt
                                    FROM DEBITMASTSNAP WHERE endt_renewal_no='$endt_renewal_no' AND predebit_type='PREDEBIT'")[0];
                                }
                                
                                // dd($pre_debit_dtl,$endt_renewal_no);
                                $result['pre_debit_dtl'] = $pre_debit_dtl;
                            }
                            else{
                                    //check trans type(RFN, CNC) and also for motor policy 
                                    $trans_type=trim($dcontrol->trans_type); 
                                    $doc_type=trim($dcontrol->doc_type); 

                                    $policy_no=trim($dcontrol->policy_no);  
                                    if ($trans_type=="RFN" || $trans_type=="CNC") {
                                        //add the cert cancel check 
                                        //check cancellation flag on certalloc
                                        $cert_infom = DB::select("select * from CERTALLOC where trim(POLICY_NO)='".$policy_no."'  and cert_status=1 and trim(cancellationflag)='Y' ORDER BY ISSUE_TIME DESC");
                                        $cert_count = count($cert_infom);
                                        //if exists call cert cancellation api
                                        if ($cert_count > 0) {
                                            foreach ($cert_infom as $cert_info) {
                                                $cert_status = $this->postCertCnc($cert_info);
                                                // dd($cert_status);
                                                $msg=$cert_status['msg'];
                                                $code=(int)$cert_status['code'];
                                        
                                                if($code == 0){
                                                    Session::flash('warning',$msg);
                                                }elseif($code == 1){
                                                    Session::flash('info',$msg);
                                                }
                                            }
                                        }
                                    }       
                                    $workflow = new Policy;
                                    $workflow_resp = $workflow->add_to_workflow($endt_renewal_no, $workflow_id, $pid);

                                    if($class->motor_policy == 'Y'){
                                        $source = 'MOTOR';
                                    }else{
                                        $source = 'NON_MOTOR';
                                    }
                                    
                                    if($class->open_cover == 'Y'){
                                        
                                        $marine = new MarineOpenProcessing($endt_renewal_no);

                                        $marine->update_opencover_utilised($endt_renewal_no);

                                    }

                                    // save RI amounts per treaty
                                    ProcessTreatyEvent::dispatch($source='U/W',$endt_renewal_no);

                                    
                                    #intergrate to Efris
                                    $efrisAPIStatus = External_api_status::where('id','EFRIS')->first();

                                    if($efrisAPIStatus->enabled == 'Y')
                                    {
                                        if($doc_type == 'DRN'){

                                                if($class->open_cover == 'Y' && $marine_cert_count > 0 && $marine_master_count==0 && $marine_utilized == 'UTL' ){

                                                    //Session::flash('warning', "Efris Status: MAC on Utilization method not integrated ");

                                                }elseif($class->open_cover == 'Y' && ($dcontrol->trans_type =='POL' || $dcontrol->trans_type =='REN' ) && $marine_utilized == 'ZPR'){

                                                    //Session::flash('warning', "Efris Status: NEW/Renewal on Zero Marine Open Cover method not integrated ");

                                                }else{
                                                    $request = new Request();
                                                    $request->merge(['endorsement_no' => $endt_renewal_no]);
                                                    $efris_res= (object)(new EnfrisController())->intergrate($request);
                                                    if($efris_res->status ==1){
                                                        Session::flash('success', "Efris Status: $efris_res->msg");
                                                    }else{
                                                        Session::flash('error', "Efris Status: $efris_res->msg");
                                                    }

                                                        #intergrate to Efris
                                                    if($doc_type == 'DRN'){
                                                            $request = new Request();
                                                            $request->merge(['endorsement_no' => $endt_renewal_no]);
                                                            $efris_res= (object)(new EnfrisController())->intergrate($request);
                                                            if($efris_res->status ==1){
                                                                Session::flash('success', "Efris Status: $efris_res->msg");
                                                            }else{
                                                                Session::flash('error', "Efris Status: $efris_res->msg");
                                                            }

                                                    }elseif ($doc_type == 'CRN') {
                                                            $efris_res=(new EnfrisController())->approvecnc($dcontrol);
                                                            
                                                    }
                                                }
                                        }
                                    }
                            }
                    }
                    
                    // notification
                    if($pre_debit == 'N'){
                      $notificationData = ['endt_renewal_no'=> $endt_renewal_no];

                      //policy schedule/document attachments
                      $clientRecord = Client::where('client_number',$polamster->client_number)->first();
                      saveDebitedPolDispatch([
                        'policy_no' => $polamster->policy_no,
                        'endt_renewal_no' => $endt_renewal_no,
                        'client_no' =>$polamster->client_number,
                        'e_mail' =>$clientRecord->e_mail,
                        'mobile_no' =>$clientRecord->mobile_no
                      ]);

                      if($clientRecord->e_mail && $dcontrol->onboard_old_policy == 'N'){
                        DispatchNotificationEvent::dispatch($slug = 'debit-endorsement',$notificationData);
                      }else{
                        //update the above inserted record
                        $debitedPolDispatch = DebitedPolDispatch::where([
                          'policy_no' => $polamster->policy_no,
                          'endt_renewal_no' => $endt_renewal_no,
                          'client_no' => $polamster->client_number,
                        ])->first();
                       
                    
                        if ($debitedPolDispatch) {
                            $debitedPolDispatch->failed_dispatch_reason = 'Missing email or Policy Migrated from Old System';
                            $debitedPolDispatch->save();
                        }
                      }
                      // Check if there exists comprehensive vehicles with no valuation yet appointed a valuer
                      $comprehensiveVehiclesAppointedValuerCount = $this->getComprehensiveVehiclesCount($endt_renewal_no);

                      if ($comprehensiveVehiclesAppointedValuerCount > 0) {
                          DispatchNotificationEvent::dispatch('motor-valuation-letter', $notificationData);
                      }
                    }
                    
                    DB::commit();
                     
                    echo json_encode($result);
                    
                    
                    
            }
        }
        catch (\Throwable $e) {
            DB::rollback();
            //  dd($e);
            Session::flash('error', 'Failed to Debit');

            $error_msg = json_encode($e->getMessage());
            $reference = "Endt_renewal_no: {$endt_renewal_no}";
            $module = __METHOD__;
            $route_name = Route::getCurrentRoute()->getActionName();

            log_error_details($route_name,$error_msg,$reference,$module);

            $data = [
                'error' => 'Failed to debit',
            ];
            return response()->json($data, 500);
        }

    }
    public function debit_endorsement($endt_renewal_no,$user_name,$reinsured,$pre_debit){
        // dd($endt_renewal_no,$user_name,$reinsured,$pre_debit);
        try {
            $schem = schemaName();
            $gb = $schem['gb'];
            $gl = $schem['gl'];
            $common = $schem['common'];
            $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
            $debited = Debitmast::where('endt_renewal_no', $endt_renewal_no)->count();
        
            throw_if($debited > 0,"RuntimeException",...["Endorsement $endt_renewal_no already debited"]);
             
            switch ($reinsured) {
                    //case 'Y':
                case 'N':
                    /*execute oracle procedure interactive_reinsure*/

                        $procedureName = '' . $gb . '.interactive_reinsure_precomput';
                        $bindings = [
                            'endt_renewal_no' => $endt_renewal_no,
                            'grading' => 'N',
                            'grade_down' => 'Z',
                            'grading_descr_code' => null,
                            'eml' => 100,
                            'facultative_reice' => 0,
                            'gfacultative_comm_reice' => 0,
                            'per_location' => 'N',
                            'glocation' => 0,
                            'per_section' => 'N',
                            'gsection' => 0,
                            'do_debit' => 'Y',
                            'per_combined'  => 'N',
                            'gcombined' => $dcontrol->class,
                            'final_reinsure'  => 'Y',
                            'consolidate' => 'N',
                            'w_effective_sum' => 0,
                            'recon' => 'N',
                            'g_user' => $user_name,
                            'surplus_referral' => 'N'
                        ];
                        $resp = DB::executeProcedure($procedureName, $bindings);

                    break;

                default:

                        $procedureName = '' . $gb . '.debit_endorsement';
                        $bindings = [
                            'endt_renewal_no'  =>  $endt_renewal_no,
                            'debited_by' => $user_name,
                            'pre_debit' => $pre_debit
                        ];

                        $resp = DB::executeProcedure($procedureName, $bindings);
                    break;
            }
            
            return(object)[
                "status"=>1,
                "resp"=>$resp,
                "msg"=>"Debit Success"
            ];
            
        } catch (\Throwable $th) {
            throw $th;
        }

    }
    public function pvt_reinsure($endt_renewal_no, $gb, $gl){
        /*execute oracle procedure reinsurance on class 44 pvt*/
        DB::beginTransaction();
        try {
            $procedureName = '' . $gb . '.interactive_reinsure_precomput';
            $bindings = [
                'endt_renewal_no' => $endt_renewal_no,
                'grading' => 'N',
                'grade_down' => 'Z',
                'eml' => 100,
                'facultative_reice' => 0,
                'gfacultative_comm_reice' => 0,
                'per_location' => 'N',
                'glocation' => 0,
                'per_section' => 'N',
                'gsection' => 0,
                'do_debit' => 'N',
                'per_combined'  => 'N',
                // 'gcombined' => $dcontrol->class,
                'gcombined' => '44',
                'final_reinsure'  => 'Y',
                'consolidate' => 'N',
                'w_effective_sum' => 0,
                'recon' => 'N',
                'surplus_referral' => 'N'

            ];
            $pvt_debit_reinsure = DB::executeProcedure($procedureName, $bindings);
            
        }catch (\Throwable $e) {

            DB::rollback();
        }

        
        return $pvt_debit_reinsure;

        
    }


    public function updateStickerStamp($policy)
    {
        $modtl = Modtl::where('policy_no', trim($policy))->update(['stamp_sticker_flag', 'Y']);
        $modtlend = Modtlend::where('policy_no', trim($policy))->update(['stamp_sticker_flag', 'Y']);
    }

    
    public function postCertCnc($cert_info){
     
        //test data end
          //$cert_info=$cert_info[0];
          $cert_cancellation_rsn=$cert_info->cancellationreason;
          $endorsement = trim($cert_info->endt_renewal_no);
          $cert_no =trim($cert_info->aki_cert_no);
          
          $cancellationreason=$cert_cancellation_rsn;
          if(is_null($cancellationreason))
          {
             $cancellationreason="Policy Cancellation"; 
          }
          $today = Carbon::now();
          $canceldate = date("d/m/Y H:i",strtotime($today));
         // dd($canceldate);
          $user_name = Auth::user()->user_name;
             $user_name = trim($user_name);
          //  /dd($cert_no);
  
            //$certcncreason = DB::table("certcncreason")->where('reason_id',$Cancellationid)->first()->reason_desc;
      
            $pipcnam = Pipcnam::where('record_type', 0)->first();
       
                            $array = array( 
                              "CertificateNumber"=>$cert_no,
                              "CancellationReason"=>$cancellationreason,
                              "Canceldate"=> $canceldate
                         );
            
            //dd($array,$CancellationReason);
            
                $client = new \GuzzleHttp\Client();
                $url = $pipcnam->aki_base_url.'CancelCertificate';
              
               
                $aki_user = $pipcnam->aki_user;
                $aki_passwd = $pipcnam->aki_passwd;
                // $token=aki_token();
                
  
                $response = '';
                $status="";
  
                // dd($url);
                
                if($pipcnam->digital_cert == 'Y'){
                      $response = $client->post($url, [
                        'auth' => [
                          $aki_user, 
                          $aki_passwd
                        ],
                        'headers' => [
                            'Content-Type'  => 'application/json',
                            'clientID' =>'********-95E8-4492-B41F-7B86D9AA892F'
                          ],
                        'json'=>$array
          
                      ]);
                    $contents = json_decode($response->getBody()->getContents());
                   
                    $status=$contents->success;
                    $DMVICRefNo=$contents->DMVICRefNo;
                    // dd($DMVICRefNo,$contents);
                    if($status == true){
                        
                        // $update_certmast = Certmast::where('endt_renewal_no',$endorsement)
                        //                            ->where('aki_cert_no', $cert_no)
                        //                            ->update([
                        //                                     'who_cancelled' => $user_name,
                        //                                     'why_cancelled' => $cancellationreason,
                        //                                     'date_cancelled' => $canceldate,
                        //                                     'cert_status'=>99
                        //                                 ]);
                        $update_certalloc = Certalloc::where('aki_cert_no', $cert_no)
                                                     ->update([
                                                                'who_cancelled' => $user_name,
                                                                'why_cancelled' => $cancellationreason,
                                                                'date_cancelled' => $canceldate,
                                                                'cert_status'=>99,
                                                                'cancellationcode'=>$DMVICRefNo
                                                             ]);
  
                        
                    
  
                        return [
                            'code' => 1,
                            'msg' => 'Certificate '.$CertificateNumber.' cancelled successfully'
                        ];
                        
                    }else{
                        $update_certalloc = Certalloc::where('aki_cert_no', $cert_no)
                                                     ->update([
                                                                
                                                                'cancellationcode'=>$DMVICRefNo
                                                             ]);
                        return [
                            'code' => 0,
                            'msg' => $contents->Error[0]->errorText
                        ];
                      
                    }
                   
                }
                
        
           
    }

    public function pre_debit_check($endt_renewal_no, PreDebitService $preDebitService, Request $request)
    {
        $results = $preDebitService
                    ->setEndtRenewalNo($endt_renewal_no)
                    ->setInteractiveDebit($request->interactiveDebit)
                    ->preDebitCheck();

        return response()->json($results);
    }

    public function getComprehensiveVehiclesCount($endt_renewal_no){
        $vehicles = Modtlsumm::join('modtlmast', function ($join) {
            $join->on('modtlsumm.endt_renewal_no', '=', 'modtlmast.endt_renewal_no');
        })
        ->join('covertype', function ($join) {
            $join->on('modtlmast.covertype', '=', 'covertype.cover');
        })
        ->select('modtlmast.covertype', 'modtlmast.manufacture_year', 'covertype.max_age', 'covertype.type_of_cover', 'modtlmast.reg_no', 'modtlmast.valuation', 'modtlmast.valuer')
        ->where('modtlsumm.endt_renewal_no', $endt_renewal_no)
        ->where('modtlsumm.endorse_amount', '>', 0)
        ->groupBy('modtlmast.covertype', 'modtlmast.manufacture_year', 'covertype.max_age', 'covertype.type_of_cover', 'modtlmast.reg_no', 'modtlmast.valuation', 'modtlmast.valuer')
        ->get();

        $comprehensiveVehiclesAppointedValuerCount = $vehicles->where('type_of_cover', 'C')
                                            ->where('valuation', 'N')
                                            ->whereNotNull('valuer')
                                            ->count();

        return $comprehensiveVehiclesAppointedValuerCount;
    }

}
