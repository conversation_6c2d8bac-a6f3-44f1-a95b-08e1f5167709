<?php

namespace App\Http\Controllers\gb\underwriting;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Client;
use App\Polmaster;
use App\Occupation;
use App\Agmnf;
use App\Pipcnam;
use App\Marinemasterpol;
use App\Beneficiary;

class Client_home extends Controller
{
	protected $client_number;


    public function index(){
        
    	return view('gb.underwriting.client_list');
    }

    public function display_client(Request $request){

        
        $this->client_number=$request->client_no;

    	$client_details=Client::where('client_number',$this->client_number)
    					->get(['client_number','name','edms_cabinet_created','mobile_no','telephone','occupation','e_mail','blacklist_flag']);
        // dd($client_details);
        $no_of_policies=Polmaster::where('client_number',$this->client_number)->count();

        $process_id = 10;
        $dept_id = 0;

        $openCoverCount = Marinemasterpol::where('client_number', $this->client_number)->count();
        $enable_open_cover_tab = Pipcnam::first()->enable_open_cover_tab;

        $client_classification = Client::where('client_number', $this->client_number)
        ->where('client_classification', 'HIGH')
            ->exists();
        
       // $occupe_code=trim($client_details[0]->occupation);
      
       // $occupation=Occupation::where('occup_code', $occupe_code)->get();
          
         // $occupation=$occupation[0];
       
    	return view('gb.underwriting.policy_details',[
            'client'=>$client_details,
            'process_id' => $process_id,
            'dept_id'=>$dept_id,
            //'occupation'=>$occupation->description,
            'no_of_pol'=>$no_of_policies,
            'openCoverCount' => $openCoverCount,
            'enable_open_cover_tab' => $enable_open_cover_tab,
            'client_classification' => $client_classification,

        ]);

    }

   
}
