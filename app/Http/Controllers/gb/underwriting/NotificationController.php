<?php

namespace App\Http\Controllers\gb\underwriting;

// use Session;
use DateTime;
use Response;
use App\Acdet;
use App\Agmnf;
use App\Clhmn;
use App\Branch;
use App\Client;

use App\Dtran0;
use App\Reidoc;
use ZipArchive;
use App\Pipstmp;
use App\Ptamdtl;
use App\Certmast;
use App\Currency;
use App\Dcontrol;
use App\Modtlend;
use App\Certalloc;
use App\Debitmast;
use App\Polmaster;
use App\Vat_setup;
use Carbon\Carbon;
use App\ClassModel;
use App\Models\User;
use App\Renewal_notice;
use App\Misc_fees_param;
use App\Models\Modtlmast;
use App\Models\Fees_debit;
use App\Models\Modtlpivot;
use App\Mail\sendRenewalzip;
use Illuminate\Http\Request;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use App\Models\Renewalnotice_setup;
use App\Classes\RenewalNotification;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Models\DiscountLoadingExtExc;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use App\Models\RenewalNoticeLoadingDiscount;
use Aimsoft\UserManagement\Models\Permission;

use App\Services\IntermediaryQueryService;
use App\ValueObjects\IntermediaryQueryParams;



class NotificationController extends Controller
{
        public function viewNotifications()
        {  
                $policies = Renewal_notice::all();
                // $agents   = Agmnf::all();
                // $agmnf = DB::select("SELECT * FROM agmnf a JOIN BRANCH b 
                //  ON a.BRANCH =b.BRANCH WHERE b.UW_ACTIVE ='Y'");
                // $agents = collect($agmnf)->map(function ($item) {
                //         return Agmnf::hydrate((array) $item);
                // });

                $intermediaryParams = new IntermediaryQueryParams([

                        'additionalFields'=>['intermediary_branch.status'],
                        'conditions' => function($query){
                                return $query->where('branch.uw_active','Y');
                            }
                    ]);
                $agents  =IntermediaryQueryService::getActiveintermediaryWithBranchDetails($intermediaryParams)->get();

                $clients = Client::all();
                $branches = Branch::all();
                $loadingParams = RenewalNoticeLoadingDiscount::all();

                $discLoadExtExcs = DB::table('disc_load_ext_excs')->get();
                $prmTaxes = DB::table('RENEWAL_NOTICE_PREMIUM_TAXES')->where('active', 'Y')->get();
                return view('gb.underwriting.renewal_notices', compact('policies', 'agents', 'branches', 'clients', 'loadingParams', 'discLoadExtExcs', 'prmTaxes'));
        }
        public function fetchRenewalPolocies(Request $request)
        {
                $validated = $request->validate([
                        'fetch_by' => 'required',
                        'due_in' => 'required'
                ]);

                $now = Carbon::now()->format('Y-m-d');

                $due_in = $request->due_in;
                $fetch_by = $request->fetch_by;

                if ($fetch_by == 'agent' && $request->branch_id == null) {
                        Session::flash('error', 'Branch should not be blank');
                        return redirect()->route('renewal_prelist_index');
                }

                DB::beginTransaction();

                try {

                        if ($fetch_by == 'agent') {
                                $policies = DB::select("select * from polmaster WHERE PERIOD_TO >= to_date('$now', 'YYYY-MM-DD' ) - '$due_in' AND PERIOD_TO <= to_date('$now', 'YYYY-MM-DD' ) + '$due_in' AND branch = '$request->branch_id' AND  agent_no = '$request->fetch_id'");
                        } elseif ($fetch_by == 'client') {
                                $policies = DB::select("select * from polmaster WHERE PERIOD_TO >= to_date('$now', 'YYYY-MM-DD' ) - '$due_in' AND PERIOD_TO <= to_date('$now', 'YYYY-MM-DD' ) + '$due_in' AND client_number = '$request->fetch_id'");
                        }

                        if ($policies != null) {

                                return view('gb.underwriting.renewal_notices', compact('policies'));

                        } else {
                                Session::flash('info', 'No Policies were found');

                                return redirect()->route('renewal_prelist_index');
                        }

                        return redirect()->route('renewal_prelist_index');


                } catch (\Throwable $th) {

                        Session::flash('error', 'While trying to retrieve data');

                        return redirect()->route('viewnotification');

                }

        }

        public function edit_renewal_premium(Request $request)
        {
          // dd($request->all());
            $renewal_notice = Renewal_notice::where('endt_renewal_no', trim($request->endorse_no))->first();

            if ($renewal_notice->renewal_notice_status !== 'PENDING') {
              return response()->json(['status'=> 0, 'message' => 'Renewal Notice has already been reviewed']);
            }

            DB::beginTransaction();

            // Validate incoming request data
            $request->validate([
                'renewal_premium' => 'required',
                'endorse_no' => 'required',
                'stamp_duty' => 'sometimes',
                'levy' => 'sometimes',
                'vat_amount' => 'sometimes',
                'total' => 'required',
                'type' => 'sometimes',
                'description' => 'sometimes',
                'amount' => 'sometimes',
            ]);

            $endorse_no = $request->endorse_no;
            $renewal_premium = $request->renewal_premium;

            if ($endorse_no && $renewal_premium != 0) { // if true, proceed to do review

                // Check if the renewal notice status is approved
                if ($renewal_notice->renewal_notice_status != 'APPROVED') {
                  $permissionId = DB::table('permissions')->where('slug', 'approve-renewal-notices')->pluck('id');

                  if ($permissionId) {
                    $roleIds = DB::table('permission_role')->where('permission_id',$permissionId)->pluck('role_id');
                    
                    if($roleIds->isNotEmpty()){
                      $usersWithRole = DB::table('aimsusers')
                            ->whereIn('role_id', $roleIds)
                            ->pluck('user_id');

                      $userId = auth()->user()->user_id;
                      $isAuthorized = $usersWithRole->contains($userId);
                      
                    }else{
                      $isAuthorized = false;
                      return response()->json(['status' => 0, 'message' => 'Unauthorized action']);
                    }
                  }else{
                    $isAuthorized = false;
                    return response()->json(['status' => 0, 'message' => 'Permission not found']);
                  }

                }

                // Determine the new status based on the current status and the request
                if ($renewal_notice->renewal_notice_status == 'PENDING' && $request->approved == "Y") {
                    $renewal_notice_status = 'APPROVED';
                } else if ($renewal_notice->renewal_notice_status == 'APPROVED' && $request->approved == "Y") {
                    $renewal_notice_status = 'APPROVED';
                } else if ($renewal_notice->renewal_notice_status == 'APPROVED' && $request->approved == "N") {
                    $renewal_notice_status = 'DECLINED';
                } else if ($renewal_notice->renewal_notice_status == 'PENDING' && $request->approved == "N") {
                    $renewal_notice_status = 'DECLINED';
                } else if ($renewal_notice->renewal_notice_status == 'DECLINED' && $request->approved == "N") {
                    $renewal_notice_status = 'DECLINED';
                } else if ($renewal_notice->renewal_notice_status == 'DECLINED' && $request->approved == "Y") {
                    $renewal_notice_status = 'APPROVED';
                } else {
                    return response()->json(['status' => 0, 'message' => 'Kindly approve or decline.']);
                }

                try {
                    $this->updateRenewalNotice($request, $renewal_notice_status);
                    $this->insertNewDiscLoadRecord($request);

                    DB::commit();
                    return response()->json(['status' => 1, 'message' => 'Successfully reviewed renewal notice']);
                } catch (\Throwable $th) {
                    // dd($th);
                    DB::rollback();
                    return response()->json(['status' => 0, 'message' => 'An error occurred while reviewing the renewal notice.']);
                }
            } else {
                return response()->json(['status' => 0, 'message' => "Endorsement number and renewal premium are missing. Cannot proceed to review"]);
            }
        }

        public function getdetails(Request $request)
        {
                $notice = Renewal_notice::where('endt_renewal_no', trim($request->endorse_no))->first();
                return $notice;
        }
        public function getpendingrenewals(Request $request)
        {

                $fetch_id = $request->fetch_id;
                $branch_id = $request->branch_id;
                $fetch_by = $request->fetch_by;
                $due_in = (int) $request->due_in;
                if (!is_null($fetch_by) && !is_null($fetch_id) && !is_null($due_in)) {
                        $now = Carbon::now()->format('Y-m-d');

                        if ($fetch_by == 'agent') {
                                //$policies =  DB::select("select * from renewal_notice WHERE PERIOD_TO >= to_date('$now', 'YYYY-MM-DD' ) - '$due_in' AND PERIOD_TO <= to_date('$now', 'YYYY-MM-DD' ) + '$due_in' AND branch = '$request->branch_id' AND  agent_no = '$request->fetch_id'");
                                $policies = Renewal_notice::where('renewal_date', '=', Carbon::now()->addDays($due_in)->toDateString())
                                        ->where('branch', $request->branch_id)
                                        ->where('agent_no', $request->fetch_id)
                                        ->get();

                        } elseif ($fetch_by == 'client') {

                                // $policies =  DB::select("select * from renewal_notice WHERE PERIOD_TO >= to_date('$now', 'YYYY-MM-DD' ) - '$due_in' AND PERIOD_TO <= to_date('$now', 'YYYY-MM-DD' ) + '$due_in' AND client_number = '$request->fetch_id'");
                                $policies = Renewal_notice::where('renewal_date', '=', Carbon::now()->addDays($due_in)->toDateString())
                                        ->where('client_number', $request->fetch_id)
                                        ->get();
                        }


                } else {
                        $policies = Renewal_notice::where('renewal_notice_status', 'PENDING')->get();

                }







                return Datatables::of($policies)
                        ->addColumn('loss_ratio', function ($row) {
                                $clampaid = Clhmn::where('policy_no', $row->policy_no)->sum('cost_todate');
                                $clamest = Clhmn::where('policy_no', $row->policy_no)->sum('curr_total_estimate');
                                $out_bal = Acdet::where('policy_no', $row->policy_no)->sum('unallocated');
                                $gross_premium = Debitmast::where('policy_no', $row->policy_no)->sum('gross_amount');


                                if ($gross_premium == 0) {
                                        $lor = 0;
                                } else {

                                        $lor = round((($clampaid + $clamest) / $gross_premium) * 100, 2);
                                }
                                return "$lor%";

                        })
                        ->addColumn('phone_number', function ($row) {
                                $clientphone = Client::where('client_number', trim($row->client_number))->first()->telephone;
                                return $clientphone ?? 'missing';

                        })
                        ->addColumn('e_mail', function ($row) {
                                $clientemail = Client::where('client_number', trim($row->client_number))->first()->e_mail;
                                return $clientemail;
                        })
                        ->addColumn('action', function ($row) {
                          $permissionId = DB::table('permissions')->where('slug', 'approve-renewal-notices')->pluck('id');

                          if ($permissionId) {
                            $roleIds = DB::table('permission_role')->where('permission_id',$permissionId)->pluck('role_id');
                            
                            if($roleIds->isNotEmpty()){
                              $usersWithRole = DB::table('aimsusers')
                                    ->whereIn('role_id', $roleIds)
                                    ->pluck('user_id');

                              $userId = auth()->user()->user_id;
                              $isAuthorized = $usersWithRole->contains($userId);
                            }else{
                              $isAuthorized = false;

                            }
                          }else{
                            $isAuthorized = false;
                          }
                          $authorizedAttr = $isAuthorized ? 'true' : 'false';

                          $buttonText = in_array($row->renewal_notice_status, ['DECLINED', 'APPROVED']) ? 'Reviewed' : 'Review';
                          $buttonClass = $buttonText !== 'PENDING' ? 'btn-secondary' : 'btn-info';
                          
                          return '<a class="btn btn-xs btn-primary edit_premium_preview" id="" data-authorized="' . $authorizedAttr . '">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>  Review
                            </a>';

                        })
                        ->rawColumns(['action'])
                        ->addIndexColumn()
                        ->make(true);

        }
        public function previewDoc($endorse_no)
        {
          $renewalNotice = Renewal_notice::where('endt_renewal_no', $endorse_no)->first();
    
          if ($renewalNotice) {
              $status = $renewalNotice->renewal_notice_status;

              $view = '';
      
              switch ($status) {
                  case 'APPROVED':
                      $view = 'renewal_notice_templates.renewal_notice_letter';
                      break;
                  case 'DECLINED':
                      $view = 'emails.decline_letter';
                      break;
                  
              }
      
              $notice_letter = new RenewalNotification();
              $notice_letter->generateClientPDF($endorse_no, $view, $open_file = 'Y');
          } else {
              return response()->json(['error' => 'Document not found'], 404);
          }


        }
        public function zipdownload(Request $request)
        {

                $fetch_id = $request->fetch_id;
                $branch_id = $request->branch_id;
                $fetch_by = $request->fetch_by;
                $due_in = $request->due_in;
                $sendmail = $request->sendmail;
                $zip = new ZipArchive;
                $notice_letter = new RenewalNotification();

                if (!is_null($fetch_by) && !is_null($fetch_id) && !is_null($due_in)) {
                        $now = Carbon::now()->format('Y-m-d');

                        if ($fetch_by == 'agent') {

                                //$policies =  DB::select("select * from renewal_notice WHERE approved='Y' and PERIOD_TO >= to_date('$now', 'YYYY-MM-DD' ) - '$due_in' AND PERIOD_TO <= to_date('$now', 'YYYY-MM-DD' ) + '$due_in' AND branch = '$request->branch_id' AND  agent_no = '$request->fetch_id'");

                                $policies = Renewal_notice::where('renewal_date', '=', Carbon::now()->addDays($due_in)->toDateString())
                                        ->where('branch', $request->branch_id)
                                        ->where('agent_no', $request->fetch_id)
                                        ->where('renewal_notice_status', '<>', 'SENT')
                                        ->where('approved', 'Y')
                                        ->get();

                               // $agents = Agmnf::where('branch', $request->branch_id)->where('agent', $request->fetch_id)->first();

                               $intermediaryParams = new IntermediaryQueryParams([
                                         'agentNo' => $request->fetch_id
                                    
                                    ]);
                                
                                 $agents  =  IntermediaryQueryService ::getIntermediaryByAgentNo($intermediaryParams)->first();

                                $zipFileName = $agents->name . "-" . $now . ".zip";

                                $mail = trim($agents->email);
                                $receiver = trim($agents->name);

                        } elseif ($fetch_by == 'client') {

                                $policies = Renewal_notice::where('renewal_date', '=', Carbon::now()->addDays($due_in)->toDateString())
                                        ->where('approved', 'Y')
                                        ->where('client_number', $request->fetch_id)
                                        ->get();

                                $client = Client::where('client_number', trim($request->fetch_id))->first();
                                $zipFileName = $client->name . "-" . $now . ".zip";

                                $mail = trim($client->e_mail);
                                $receiver = trim($client->name);


                        }

                }
                if (count($policies) > 0) {

                        $zip = new ZipArchive;

                        if ($zip->open(storage_path('app/public/' . $zipFileName), ZipArchive::CREATE | ZipArchive::OVERWRITE)) {

                                foreach ($policies as $policy) {
                                        $client_no = Dcontrol::where('endt_renewal_no', $policy->endt_renewal_no)->first()->client_number;
                                        $clientdata = Client::where('client_number', trim($client_no))->first();
                                        $pdfContent = $notice_letter->generateClientPDF($policy->endt_renewal_no, $open_file = 'N');

                                        // Add the generated PDF content to the zip archive
                                        $zip->addFromString($clientdata->name . "-" . $policy->endt_renewal_no . '.pdf', $pdfContent);
                                        if ($sendmail == "Y") {
                                                Renewal_notice::where('endt_renewal_no', trim($policy->endt_renewal_no))
                                                        ->where('renewal_notice_status', 'APPROVED')
                                                        ->where('policy_no', trim($policy->policy_no))->update([
                                                                        'renewal_notice_status' => 'SENT',
                                                                        'sent_date' => Carbon::now()->format('Y-m-d')
                                                                ]);
                                        }

                                }
                                $zip->close();

                                if ($sendmail == "Y") {
                                        $renewal_setup = Renewalnotice_setup::get()[0];
                                        $title = 'RENEWAL NOTIFICATION NOTICE';
                                        $subject = 'RENEWAL NOTIFICATION NOTICE';
                                        $user = trim(Auth::user()->name);
                                        $createdate = formatDateRFC3339(Carbon::now()->format('d-m-Y'));
                                        $pdf_attachment = $zipFileName;


                                        $data = array(
                                                'receiver' => $receiver,
                                                'message' => $renewal_setup->email_message,
                                        );
                                        Mail::to(trim($mail))->cc([trim($renewal_setup->cc_email)])->send(new sendRenewalzip($data, $title, $createdate, $subject, $user, $pdf_attachment));
                                        Storage::delete("public/{$pdf_attachment}");
                                        if (Mail::failures()) {

                                                return redirect()->back()->with('error', "An error Occured while sending the mail");

                                        } else {
                                                // Renewal_notice::where('endt_renewal_no',trim($policy->endt_renewal_no))
                                                // ->where('renewal_notice_status','APPROVED')
                                                // ->where('policy_no',trim($policy->policy_no))->update([
                                                //         'renewal_notice_status' => 'SENT',
                                                //         'sent_date' => Carbon::now()->format('Y-m-d')
                                                // ]);
                                                return redirect()->back()->with('success', "Mail successfully send to $mail");


                                        }

                                } else {
                                        return response()->download(storage_path('app/public/' . $zipFileName))->deleteFileAfterSend(true);

                                }






                        } else {
                                // Handle the case where the zip file couldn't be opened
                                return redirect()->back()->with('error', "Unable to open the zip file");

                        }
                } else {
                        return redirect()->back()->with('error', "No policy record found");


                }


        }

        public function getaaprovedrenewals(Request $request)
        {
                $fetch_id = $request->fetch_id;
                $branch_id = $request->branch_id;
                $fetch_by = $request->fetch_by;
                $due_in = $request->due_in;

                if (!is_null($fetch_by) && !is_null($fetch_id) && !is_null($due_in)) {
                        $now = Carbon::now()->format('Y-m-d');

                        if ($fetch_by == 'agent') {
                                $policies = Renewal_notice::where('renewal_date', '=', Carbon::now()->addDays($due_in)->toDateString())
                                        ->where('branch', $request->branch_id)
                                        ->where('agent_no', $request->fetch_id)
                                        ->where('renewal_notice_status', 'APPROVED')
                                        ->where('approved', 'Y')
                                        ->get();
                        } elseif ($fetch_by == 'client') {

                                $policies = Renewal_notice::where('renewal_date', '=', Carbon::now()->addDays($due_in)->toDateString())
                                        ->where('client_number', $request->fetch_id)
                                        ->where('approved', 'Y')
                                        ->where('renewal_notice_status', 'APPROVED')
                                        ->get();
                        }



                } else {
                        $policies = Renewal_notice::where('approved', 'Y')
                                ->where('renewal_notice_status', 'APPROVED')
                                ->get();

                }


                return Datatables::of($policies)
                        ->addColumn('phone_number', function ($row) {
                                $clientphone = Client::where('client_number', trim($row->client_number))->first()->telephone;
                                return $clientphone ?? 'missing';

                        })
                        ->addColumn('e_mail', function ($row) {
                                $clientemail = Client::where('client_number', trim($row->client_number))->first()->e_mail;
                                return $clientemail;
                        })
                        ->addColumn('action', function ($row) {
                          $permissionId = DB::table('permissions')->where('slug', 'approve-renewal-notices')->pluck('id');

                          if ($permissionId) {
                            $roleIds = DB::table('permission_role')->where('permission_id',$permissionId)->pluck('role_id');
                            
                            if($roleIds->isNotEmpty()){
                              $usersWithRole = DB::table('aimsusers')
                                    ->whereIn('role_id', $roleIds)
                                    ->pluck('user_id');

                              $userId = auth()->user()->user_id;
                              $isAuthorized = $usersWithRole->contains($userId);
                            }else{
                              $isAuthorized = false;

                            }
                          }else{
                            $isAuthorized = false;
                          }
                          $authorizedAttr = $isAuthorized ? 'true' : 'false';
                          
                          $buttonText = in_array($row->renewal_notice_status, ['DECLINED', 'APPROVED']) ? 'Reviewed' : 'Review';
                          $buttonClass = $buttonText !== 'PENDING' ? 'btn-primary' : 'btn-info';
                          $isDisabled = !in_array($row->status, ['PENDING']);
                          // $disabledClass = $isDisabled ? 'disabled' : '';
                          
                          return '
                          <a href="' . route('previewDoc', $row->endt_renewal_no ? $row->endt_renewal_no : $row->endorse_no) . '" class="btn btn-xs bg-info preview" id="preview" target="_blank">
                              <i class="fa fa-eye" aria-hidden="true"></i> Preview
                          </a>';

                        })
                        ->rawColumns(['action'])
                        ->addIndexColumn()
                        ->make(true);

        }
        public function getsendenewals(Request $request)
        {
          $fetch_id = $request->fetch_id;
          $branch_id = $request->branch_id;
          $fetch_by = $request->fetch_by;
          $due_in = $request->due_in;

          if (!is_null($fetch_by) && !is_null($fetch_id) && !is_null($due_in)) {
                  $now = Carbon::now()->format('Y-m-d');

                  if ($fetch_by == 'agent') {
                          $policies = Renewal_notice::where('renewal_date', '=', Carbon::now()->addDays($due_in)->toDateString())
                                  ->where('branch', $branch_id)
                                  ->where('agent_no', $request->fetch_id)
                                  ->where('renewal_notice_status', 'SENT')
                                  ->whereNotNull('sent_date')
                                  ->get();
                  } elseif ($fetch_by == 'client') {

                          $policies = Renewal_notice::where('renewal_date', '=', Carbon::now()->addDays($due_in)->toDateString())
                                  ->where('client_number', $request->fetch_id)
                                  ->where('renewal_notice_status', 'SENT')
                                  ->whereNotNull('sent_date')
                                  ->get();
                  }



          } else {
                  $policies = Renewal_notice::where('renewal_notice_status', 'SENT')
                    ->whereNotNull('sent_date')
                    ->get();

          }


          return Datatables::of($policies)
                  ->addColumn('phone_number', function ($row) {
                          $clientphone = Client::where('client_number', trim($row->client_number))->first()->telephone;
                          return $clientphone ?? 'missing';

                  })
                  ->addColumn('e_mail', function ($row) {
                          $clientemail = Client::where('client_number', trim($row->client_number))->first()->e_mail;
                          return $clientemail;
                  })
                  ->addIndexColumn()
                  ->make(true);

        }

        public function getDeclinedRenewals(Request $request)
        {
          $fetch_id = $request->fetch_id;
          $branch_id = $request->branch_id;
          $fetch_by = $request->fetch_by;
          $due_in = $request->due_in;

          if (!is_null($fetch_by) && !is_null($fetch_id) && !is_null($due_in)) {
                  $now = Carbon::now()->format('Y-m-d');

                  if ($fetch_by == 'agent') {
                          $policies = Renewal_notice::where('renewal_date', '=', Carbon::now()->addDays($due_in)->toDateString())
                                  ->where('branch', $branch_id)
                                  ->where('agent_no', $request->fetch_id)
                                  ->where('renewal_notice_status', 'DECLINED')
                                  ->where('approved', 'N')
                                  ->get();
                  } elseif ($fetch_by == 'client') {

                          $policies = Renewal_notice::where('renewal_date', '=', Carbon::now()->addDays($due_in)->toDateString())
                                  ->where('client_number', $request->fetch_id)
                                  ->where('approved', 'N')
                                  ->where('renewal_notice_status', 'DECLINED')
                                  ->get();
                  }



          } else {
                  $policies = Renewal_notice::where('approved', 'N')
                          ->where('renewal_notice_status', 'DECLINED')
                          ->get();

          }


          return Datatables::of($policies)
                  ->addColumn('phone_number', function ($row) {
                          $clientphone = Client::where('client_number', trim($row->client_number))->first()->telephone;
                          return $clientphone ?? 'missing';

                  })
                  ->addColumn('e_mail', function ($row) {
                          $clientemail = Client::where('client_number', trim($row->client_number))->first()->e_mail;
                          return $clientemail;
                  })
                  ->addColumn('action', function ($row) {
                    $permissionId = DB::table('permissions')->where('slug', 'approve-renewal-notices')->pluck('id');

                    if ($permissionId) {
                      $roleIds = DB::table('permission_role')->where('permission_id',$permissionId)->pluck('role_id');
                      
                      if($roleIds->isNotEmpty()){
                        $usersWithRole = DB::table('aimsusers')
                              ->whereIn('role_id', $roleIds)
                              ->pluck('user_id');

                        $userId = auth()->user()->user_id;
                        $isAuthorized = $usersWithRole->contains($userId);
                      }else{
                        $isAuthorized = false;

                      }
                    }else{
                      $isAuthorized = false;
                    }
                    $authorizedAttr = $isAuthorized ? 'true' : 'false';

                    $buttonText = in_array($row->renewal_notice_status, ['DECLINED', 'APPROVED']) ? 'Reviewed' : 'Review';
                    $buttonClass = $buttonText !== 'PENDING' ? 'btn-primary' : 'btn-info';
                    $isDisabled = !in_array($row->renewal_notice_status, ['PENDING']);
                    // $disabledClass = $isDisabled ? 'disabled' : '';
                    // <a class="btn btn-xs ' . $buttonClass . ' edit_premium_preview '  . $disabledClass . '" id="" style="margin-bottom: 2px;" data-authorized="' . $authorizedAttr . '">
                    //           <i class="fa fa-pencil-square-o" aria-hidden="true"></i> ' . $buttonText . '
                    //       </a>
                    return '
                      <a href="' . route('previewDoc', $row->endt_renewal_no ? $row->endt_renewal_no : $row->endorse_no) . '" class="btn btn-xs bg-info preview" id="preview" target="_blank">
                          <i class="fa fa-eye" aria-hidden="true"></i> Preview
                      </a>';
                  })
                  ->rawColumns(['action'])
                  ->addIndexColumn()
                  ->make(true);

        }

        //get all reviewed RN
        public function getReviewedRN()
        {
          $statuses = ['DECLINED', 'APPROVED'];
          $notices = Renewal_notice::whereIn('renewal_notice_status', $statuses)->get();

          return Datatables::of($notices)
              ->addColumn('phone_number', function ($row) {
                $clientphone = Client::where('client_number', trim($row->client_number))->first()->telephone;
                return $clientphone ?? 'missing';

              })
              ->addColumn('e_mail', function ($row) {
                      $clientemail = Client::where('client_number', trim($row->client_number))->first()->e_mail;
                      return $clientemail;
              })
              ->addColumn('action', function ($row) {
                $permissionId = DB::table('permissions')->where('slug', 'approve-renewal-notices')->pluck('id');

                if ($permissionId) {
                  $roleIds = DB::table('permission_role')->where('permission_id',$permissionId)->pluck('role_id');
                  
                  if($roleIds->isNotEmpty()){
                    $usersWithRole = DB::table('aimsusers')
                          ->whereIn('role_id', $roleIds)
                          ->pluck('user_id');

                    $userId = auth()->user()->user_id;
                    $isAuthorized = $usersWithRole->contains($userId);
                  }else{
                    $isAuthorized = false;

                  }
                }else{
                  $isAuthorized = false;
                }
                $authorizedAttr = $isAuthorized ? 'true' : 'false';

                $buttonText = in_array($row->renewal_notice_status, ['DECLINED', 'APPROVED']) ? 'Reviewed' : 'Review';
                $buttonClass = $buttonText !== 'PENDING' ? 'btn-primary' : 'btn-info';
                $isDisabled = !in_array($row->renewal_notice_status, ['PENDING']);
                $disabledClass = $isDisabled ? 'disabled' : '';
                
                return '
                  <a href="' . route('previewDoc', $row->endt_renewal_no ? $row->endt_renewal_no : $row->endorse_no) . '" class="btn btn-xs bg-info preview" id="preview" target="_blank">
                      <i class="fa fa-eye" aria-hidden="true"></i> Preview
                  </a>';
              })
              ->rawColumns(['action'])
              ->addIndexColumn()
              ->make(true);
        }

        // update Renewal Notice during review
        private function updateRenewalNotice(Request $request, $status)
        {
          // dd($request->all());
          $total_load_ext_disc = $request->exc_ext_amount;
          $existing_dep_total_amount = Renewal_notice::where('endt_renewal_no', trim($request->endorse_no))->first()->dep_total_amount;
          // dd($existing_total_dep_amount , $total_load_ext_disc, $existing_total_dep_amount + ($total_load_ext_disc));  
          Renewal_notice::where('endt_renewal_no', trim($request->endorse_no))->update(
              [
                'reviewed_renewal_premium' => (float) str_replace(',', '', $request->renewal_premium),
                'renewal_premium' => (float) str_replace(',', '', $request->renewal_premium),
                'reviewed_premium' => 'Y',
                'reviewed_notice_status' => 'Y',
                'approved_by' => Auth::user()->user_name,
                'approved_date' => Carbon::now(),
                'approved' => trim($request->approved),
                'renewal_notice_status' => $status,
                'stamp_duty' => $request->stamp_duty,
                'sticker_fee' => $request->sticker_fees,
                'levy' => $request->levy,
                'vat_amount' => $request->vat_amount,
                'phcf' => $request->phcf,
                'admin_fees' => $request->admin_fees,
                'fund_guarantee' => $request->fund_guarantee,
                'total_amout' => $request->total,
                'dep_total_amount' => $existing_dep_total_amount + ($total_load_ext_disc)
              ]
            );
          
        }

        private function insertNewDiscLoadRecord($request) {
          $endorse_no = $request->endorse_no;
          $polmaster = DB::table('polmaster')->where('endorse_no', $endorse_no)->select('class', 'endorse_no')->first();
          $class = DB::table('class')->where('class', $polmaster->class)->select('class', 'description', 'motor_policy')->first();
          $types = $request->type ?? [];
          
          $markedForRemoval = $request->markedForRemoval ?? [];
          $idsToRemove = json_decode($markedForRemoval, true);
        
          foreach ($types as $index => $type) {
              if ($type !== null && $request->description[$index] !== null && $request->amount[$index] !== null) {
               
                $description = $request->description[$index];

                  // Check if a record with the same type already exists
                  $existingRecord = DiscountLoadingExtExc::where('type', $type)
                      ->where('param_id', $description)
                      ->where('renewal_notice_id', $request->endorse_no)
                      ->first();
      
                  if ($existingRecord) {
                      // Update existing record
                      $existingRecord->amount = str_replace(',', '', $request->amount[$index]);
                      $existingRecord->updated_by = auth()->user()->user_name;
      
                      // Set add_deduct based on type
                      $existingRecord->add_deduct = ($type === 'DISC' || $type === 'EXC') ? 'D' : 'A';
      
                      $existingRecord->save();
                  } else {
                      // Insert new record
                      $newRecord = new DiscountLoadingExtExc();
                      $newRecord->type = $type;
                      // $newRecord->description = $description;
                      $newRecord->amount = str_replace(',', '', $request->amount[$index]);
                      $newRecord->created_by = auth()->user()->user_name;
                      $newRecord->renewal_notice_id = $request->endorse_no;
                      $newRecord->code_id = DB::table('renewalnotice_loading_discount')->where('code', $type)->first()->id;
      
                      // Set add_deduct based on type
                      $newRecord->add_deduct = ($type === 'DISC' || $type === 'EXC') ? 'D' : 'A';
                      
                      $newRecord->param_id = $description;

                      // Set description
                      if($type == 'EXT'){
                        if($class->motor_policy == 'Y'){
                        $desc = DB::table('motorsect')
                                  ->where('item_code', $description)
                                  ->where('grp_code', 'EXT')
                                  ->value('description');
                        $newRecord->description = $desc;
                        }else{
                          $desc = DB::table('extparams')
                                    ->where('ext_code', $description)
                                    ->where('class', $class->class)
                                    ->value('ext_description');
                          $newRecord->description = $desc;
                        }
                      }else{
                        $newRecord->description = DB::table('DISCOUNTS_LOADINGS_PARAMS')->where('id', $description)->value('description');
                      }
      
                      $newRecord->save();
                  }
              }
          }
      
          // Delete marked records
          DiscountLoadingExtExc::whereIn('id', $idsToRemove)->delete();
        }
      


        public function showrenewalnoticeRemarks(Request $request)
        {
               //dd($request->all());
               $remarks = Renewal_notice::where('policy_no', $request->policy_no)
                                ->where('renewal_notice_status', 'SENT')
                                ->whereNotNull('remarks')
                                ->get();
                    
                return Datatables::of($remarks)
                ->editColumn('created_at',function($remarks){
			return formatDate($remarks->created_at);
			})
                ->editColumn('updated_at',function($remarks){
                        return formatDate($remarks->created_at);
                        })
                ->editColumn('remarks', function ($remarks) {
                                return strip_tags($remarks->remarks);
                        })
		->make(true);      
                        

        }
        public function saveRenewalnoticeRemarks(Request $request)
                {
                
                        $request->validate([
                        'policy_no' => 'required',
                        'endt_renewal_no' => 'required',
                        'remarks' => 'required',
                        ]);

                        try {
                        $user = Auth::user();
                        $existingRemarks = Renewal_notice::where('policy_no', $request->policy_no)
                                ->where('endt_renewal_no', $request->endt_renewal_no)
                                ->first();

                                if ($existingRemarks) {
                                        if ($existingRemarks->remarks === null) {
                                       
                                        Renewal_notice::where('policy_no', $request->policy_no)
                                            ->where('endt_renewal_no', $request->endt_renewal_no)
                                            ->update([
                                                'remarks' => $request->remarks,
                                                'created_at' => Carbon::now(),
                                                'created_by' => $user->user_name,
                                                
                                            ]);
                            
                                        return response()->json(['success' => true]);
                                    } else {
                                        
                                        return response()->json(['success' => false, 'error' => 'Remarks already exist and cannot be updated.'], 400);
                                    }
                                } else {
                                    
                                    return response()->json(['success' => false, 'error' => 'No record found for the given policy number and renewal number.'], 404);
                                }
                            } catch (\Exception $e) {

                                return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
                            }
                }

                public function viewRenewalnoticeRemarks(Request $request)
                { 
                       
                
                    $remarks = Renewal_notice::where('policy_no', $request->policy_no)
                                             ->where('endt_renewal_no', $request->endt_renewal_no)
                                             ->where('renewal_notice_status', 'SENT')
                                             ->select('remarks')
                                             ->get();
                                             
                 
                    return response()->json($remarks);
                }

          
                public function updateRenewalnoticeRemarks(Request $request)
                {
                    $request->validate([
                        'policy_no' => 'required',
                        'endt_renewal_no' => 'required',
                        'update_remarks' => 'required',
                    ]);
                
                    try {
                       
                        $user = Auth::user();
                
                      
                        $existingRemarks = Renewal_notice::where('policy_no', $request->policy_no)
                            ->where('endt_renewal_no', $request->endt_renewal_no)
                            ->first();
                
                       
                        if ($existingRemarks) {
                            
                            $existingRemarks->update([
                                'remarks' => $request->update_remarks,
                                'updated_at' => Carbon::now(),
                                'updated_by' => $user->user_name,
                            ]);
                
                            return 1;
                        } else {
                          
                            return 0;
                        }
                    } catch (\Exception $e) {
                        dd( $e);
                        return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
                    }
                }


                public function  deleteRenewalnoticeRemarks(Request $request)
                {  
                       
                    $request->validate([
                        'policy_no' => 'required',
                        'endt_renewal_no' => 'required',
                       
                    ]);
                
                    try {
                       
                        $user = Auth::user();
                
                        Renewal_notice::where('policy_no', $request->policy_no)
                        ->where('endt_renewal_no', $request->endt_renewal_no)
                        ->update([
                           
                            'notice_remarks_deleted_by' => $user->user_name,
                            'remarks'=>null
                        ]);
                            return 1;
                        } catch (\Exception $e) {
                    
                        return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
                    }
                }

      // get loading/discount/ext/exc for a renewal notice
      public function getLoadingDiscExtExc($id)
      {
        try {
          // $loadings = DB::table('disc_load_ext_excs')->where('renewal_notice_id', $id)->get();
          $loadings = DB::table('disc_load_ext_excs')
          ->where('renewal_notice_id', $id)
          ->join('renewalnotice_loading_discount', 'disc_load_ext_excs.code_id', '=', 'renewalnotice_loading_discount.id')
          ->select(
              'disc_load_ext_excs.id', 
              'disc_load_ext_excs.type', 
              'disc_load_ext_excs.description', 
              'disc_load_ext_excs.amount', 
              'disc_load_ext_excs.param_id', 
              'renewalnotice_loading_discount.type as code_type' 
          )
          ->get();
          
          return response()->json($loadings);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch data', 'message' => $e->getMessage()], 500);
        }
      }

      public function fetchRNcommand()
      {
        Artisan::call('renewal:notices');
        return 'Renewal notices fetched successfully!';
      }

      public function sendRNmailCommand()
      {
        Artisan::call('notification:mail-cron');
        Artisan::call('notification:send-email');
        Artisan::call('app:send-notifications');
        return 'Renewal notices mail sent successfully!';
      }

               

}
   