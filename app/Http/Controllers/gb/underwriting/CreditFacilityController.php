<?php

namespace App\Http\Controllers\gb\underwriting;

use App\Acdet;
use App\Agmnf;
use App\Branch;
use App\Client;
use App\Cbtrans;
use App\Doctype;
use App\Cbcredit;
use App\Dcontrol;
use App\SendEmail;
use Carbon\Carbon;
use App\Aimsuser_web;

use App\Escalate_pol;
use App\Models\Aimsuser;
use Illuminate\Http\Request;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\MessageBag;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;

use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;

use App\Events\DispatchNotificationEvent;
use App\Models\Intermediary\Intermediary;
use Illuminate\Support\Facades\Validator;
use App\Models\CreditFacility\CreditSetup;
use App\Services\IntermediaryQueryService;
use App\Models\CreditFacility\ClientCredit;
use Aimsoft\UserManagement\Models\Permission;
use App\ValueObjects\IntermediaryQueryParams;
use App\Models\CreditFacility\ClientCreditHist;
use App\Models\CreditFacility\CreditUtilization;
use App\Models\CreditFacility\IntermediaryCredit;
use App\Models\CreditFacility\IntermediaryCreditHist;


class CreditFacilityController extends Controller
{
    private $intermediary_type;
    private $client_type;
    public function __construct()
    {
        $this->intermediary_type = 'CRDINTFRQ';
        $this->client_type = 'CRDCLTFRQ';
    }

    public function intermediary_credit_onboarding(Request $request)
    {

        Gate::authorize('access-credit-facility-module');

        $branch = Branch::all();
        
        return view('gb.creditfacility.credit_onboard', [
            'branch' => $branch,
        ]);

    }

    public function int_credit_float_setup(Request $request){

        $branch = $request->branch;
        $agent = $request->agent;

        // $agt = Agmnf::where('branch', $branch)->where('agent', $agent)->first();
        $intermediaryParams = new IntermediaryQueryParams([
            'branch' => $branch,
            'agentNo' => $agent
        ]);
        $agt  =IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();


        $brnch = Branch::where('branch', $branch)->first();

        $intcred_rec = IntermediaryCredit::where('branch', $branch)->where('agent', $agent)->first();
        $onboarded_by_name = Aimsuser::where('user_name', $intcred_rec->created_by)->first()->name;

        $permission = Permission::where('slug','approve-credit-float-request')->first();

        $users = $permission->users;

        $check_pending_float = IntermediaryCreditHist::where('branch',$branch)
                                                    ->where('agent',$agent)
                                                    ->where('status','PENDING')
                                                    ->count();
        
        return view('gb.creditfacility.int_credit_float', [
                        'intcred_rec' => $intcred_rec,
                        'agt' => $agt,
                        'brnch' => $brnch,
                        'onboarded_by_name' => $onboarded_by_name,
                        'auto_utilize_method' => $this->auto_utilize_descr($intcred_rec->auto_utilize),
                        'users' => $users,
                        'date_created' => Carbon::parse($intcred_rec->created_at)->format('d/m/Y h:m:s a'),
                        'check_pending_float' => $check_pending_float
                    ]);
        
    }

    public function clt_credit_float_setup(Request $request){

        $branch = $request->branch;
        $agent = $request->agent;
        // dd($agent);
        $client_number = $request->client_number;

        // $agt = Agmnf::where('branch', $branch)->where('agent', $agent)->first();
        $intermediaryParams = new IntermediaryQueryParams([
            'branch' => $branch,
            'agentNo' => $agent
        ]);
        $agt  =IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();
        // dd($agt);

        $brnch = Branch::where('branch', $branch)->first();

        $client = Client::where('client_number', $client_number)->first();

        $cred_rec = ClientCredit::where('client_number',$client_number)->where('branch', $branch)->where('agent', $agent)->first();
        $onboarded_by_name = Aimsuser::where('user_name', $cred_rec->created_by)->first()->name;

        $permission = Permission::where('slug','approve-credit-float-request')->first();

        $users = $permission->users;

        $check_pending_float = ClientCreditHist::where('branch',$branch)
                                                ->where('agent',$agent)
                                                ->where('client_number',$client_number)
                                                ->where('status','PENDING')
                                                ->count();
        
        return view('gb.creditfacility.clt_credit_float', [
                        'intcred_rec' => $cred_rec,
                        'agt' => $agt,
                        'brnch' => $brnch,
                        'client' => $client,
                        'onboarded_by_name' => $onboarded_by_name,
                        'auto_utilize_method' => $this->auto_utilize_descr($intcred_rec->auto_utilize),
                        'users' => $users,
                        'date_created' => Carbon::parse($intcred_rec->created_at)->format('d/m/Y h:m:s a'),
                        'check_pending_float' => $check_pending_float
                    ]);
        
    }

    public function auto_utilize_descr($auto_utilize){

        if($auto_utilize == 'Y'){
            $descr = 'CREDIT CONTROL CONFIRMATION';
        }else{
            $descr = 'SYSTEM AUTO CONFIRMATION';
        }

        return $descr;
    }

    public function intermediary_credit_datatable(Request $request){

        //$int = IntermediaryCredit::all();

        return datatables::of(IntermediaryCredit::query())
                ->addColumn('intermediary_name', function($int){
                    $intermediaryParams = new IntermediaryQueryParams([
                        'branch' => $int->branch,
                        'agentNo' => $int->agent
                    ]);
                    $int_name = IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();
        
                    return $int_name ? $int_name->name : '';
                    // $int_name = Agmnf::where('branch', $int->branch)->where('agent', $int->agent)->first()->name;

                    // return $int_name;
                })
                ->addColumn('branch_name', function($int){
                    $branch_name = Branch::where('branch', $int->branch)->first()->description;

                    return $branch_name;
                })
                ->addColumn('amount_formated', function($int){

                    return number_format($int->amount);
                    
                })
                ->editColumn('created_at', function($int){

                    return Carbon::parse($int->created_at)->format('d/m/Y h:m:s a');
                })
                ->addColumn('view', function ($int) {

                    return '<a href="'.route('int_credit_float',['branch'=>$int->branch,'agent'=>$int->agent]).'" class="btn btn-sm-default modalviewrisk " title="View Risk Details" style="padding: 0; margin-right: 10px;""> <i class="fa fa-eye" title="View Risk Details"> </i></a>';

                })
                
                ->escapeColumns([])

                ->make(true);

    }

    public function client_credit_datatable(Request $request){

        //$int = IntermediaryCredit::all();

        return datatables::of(ClientCredit::query())
                ->addColumn('client_name', function($int){
                    $clt_name = Client::where('client_number', $int->client_number)->first()->name;

                    return $clt_name;
                })
                ->addColumn('intermediary_name', function($int){
                    $intermediaryParams = new IntermediaryQueryParams([
                        'branch' => $int->branch,
                        'agentNo' => $int->agent
                    ]);
                    $int_name = IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();
        
                    return $int_name ? $int_name->name : '';
                    // $int_name = Agmnf::where('branch', $int->branch)->where('agent', $int->agent)->first()->name;

                    // return $int_name;
                })
                ->addColumn('branch_name', function($int){
                    $branch_name = Branch::where('branch', $int->branch)->first()->description;

                    return $branch_name;
                })
                ->editColumn('created_at', function($int){

                    return Carbon::parse($int->created_at)->format('d/m/Y h:m:s a');
                })
                ->addColumn('amount_formated', function($int){

                    return number_format($int->amount);
                    
                })
                ->addColumn('view', function ($int) {

                    return '<a href="'.route('clt_credit_float',['branch'=>$int->branch,'agent'=>$int->agent,'client_number'=>$int->client_number]).'" class="btn btn-sm-default modalviewrisk " title="View Risk Details" style="padding: 0; margin-right: 10px;""> <i class="fa fa-eye" title="View Risk Details"> </i></a>';

                })
                
                ->escapeColumns([])

                ->make(true);

    }

    public function int_credit_float_datatable(Request $request){

        if($request->type == 'APPR'){

            $float = IntermediaryCreditHist::where('branch',$request->branch)
                                           ->where('agent',$request->agent)
                                           ->where('action_by', Auth::user()->user_name)
                                           ->where('status','PENDING');

        }
        else{

            $float = IntermediaryCreditHist::where('branch',$request->branch)->where('agent',$request->agent);

        }

        return datatables::of($float)
                ->addColumn('client_name', function($float){
                    $clt_name = Client::where('client_number', $int->client_number)->first()->name;

                    return $clt_name;
                })
                ->addColumn('intermediary_name', function($float){
                    $intermediaryParams = new IntermediaryQueryParams([
                        'branch' => $int->branch,
                        'agentNo' => $int->agent
                    ]);
                    $int_name = IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();
        
                    return $int_name ? $int_name->name : '';

                    // $int_name = Agmnf::where('branch', $int->branch)->where('agent', $int->agent)->first()->name;

                    // return $int_name;
                })
                ->addColumn('branch_name', function($float){
                    $branch_name = Branch::where('branch', $int->branch)->first()->description;

                    return $branch_name;
                })
                ->addColumn('formated_amount', function($float){
                    
                    return number_format($float->amount);
                })
                ->editColumn('created_at', function($float){

                    return Carbon::parse($int->created_at)->format('d/m/Y h:m:s a');
                })
                ->addColumn('view', function ($float) {
                    $links = '<a class="btn btn-sm-default viewrequest" title="View" style="padding: 0; margin-right: 10px;"><i title="View" class="fa fa-eye"> </i></a>';
                    
                    if($float->status == 'PENDING'){

                        $links .= '<a class="btn btn-sm-default editrequest" title="Edit" style="padding: 0; margin-right: 10px;"><i title="Edit" class="glyphicon glyphicon-edit"> </i></a><br/>
                        <a class="btn btn-sm-default re-escalaterequest " title="Re-escalate" style="padding: 0; margin-right: 10px;""> <i title="Re-Escalate" class="fa fa-share"></i></a>
                        <a class="btn btn-sm-default cancelrequest " title="Cancel" style="padding: 0; margin-right: 10px;""> <i title="Cancel" class="fa fa-trash"></i></a>';
                    }
                    else{
                        $links .= '';
                    }

                    return $links;

                })
                ->addColumn('view_appr', function ($float) {

                    $links = '<a class="btn btn-sm-default viewrequest" title="View" style="padding: 0; margin-right: 10px;"><i title="View" class="fa fa-eye"> </i></a>';

                    if($float->status == 'PENDING'){

                        $links .= '<a class="btn btn-sm-default actionrequest" title="Approve/Decline" style="padding: 0; margin-right: 10px;""> <i title="Approve" class="fa fa-check"></i></a>';
                    }
                    else{
                        $links .= '';
                    }

                    return $links;

                })
                ->escapeColumns([])

                ->make(true);

    }

    public function clt_credit_float_datatable(Request $request){

        if($request->type == 'APPR'){

            $float = ClientCreditHist::where('client_number',$request->client_number)
                                     ->where('branch',$request->branch)
                                     ->where('agent',$request->agent)
                                     ->where('action_by', Auth::user()->user_name)
                                     ->where('status','PENDING');

        }
        else{

            $float = ClientCreditHist::where('client_number',$request->client_number)
                                     ->where('branch',$request->branch)
                                     ->where('agent',$request->agent);

        }

        return datatables::of($float)
                ->addColumn('client_name', function($float){
                    $clt_name = Client::where('client_number', $float->client_number)->first()->name;

                    return $clt_name;
                })
                ->addColumn('intermediary_name', function($float){
                    $intermediaryParams = new IntermediaryQueryParams([
                        'branch' => $float->branch,
                        'agentNo' => $float->agent
                    ]);
                    $int_name = IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();
        
                    return $int_name ? $int_name->name : '';

                    // $int_name = Agmnf::where('branch', $float->branch)->where('agent', $float->agent)->first()->name;

                    // return $int_name;
                })
                ->addColumn('branch_name', function($float){
                    $branch_name = Branch::where('branch', $float->branch)->first()->description;

                    return $branch_name;
                })
                ->addColumn('formated_amount', function($float){
                    
                    return number_format($float->amount);
                })
                ->editColumn('created_at', function($float){

                    return Carbon::parse($int->created_at)->format('d/m/Y h:m:s a');
                })
                ->addColumn('view', function ($float) {
                    $links = '<a class="btn btn-sm-default viewrequest" title="View" style="padding: 0; margin-right: 10px;"><i title="View" class="fa fa-eye"> </i></a>';
                    
                    if($float->status == 'PENDING'){

                        $links .= '<a class="btn btn-sm-default editrequest" title="Edit" style="padding: 0; margin-right: 10px;"><i title="Edit" class="glyphicon glyphicon-edit"> </i></a><br/>
                        <a class="btn btn-sm-default re-escalaterequest " title="Re-escalate" style="padding: 0; margin-right: 10px;""> <i title="Re-Escalate" class="fa fa-share"></i></a>
                        <a class="btn btn-sm-default cancelrequest " title="Cancel" style="padding: 0; margin-right: 10px;""> <i title="Cancel" class="fa fa-trash"></i></a>';
                    }
                    else{
                        $links .= '';
                    }

                    return $links;

                })
                ->addColumn('view_appr', function ($float) {

                    $links = '<a class="btn btn-sm-default viewrequest" title="View" style="padding: 0; margin-right: 10px;"><i title="View" class="fa fa-eye"> </i></a>';

                    if($float->status == 'PENDING'){

                        $links .= '<a class="btn btn-sm-default actionrequest" title="Approve/Decline" style="padding: 0; margin-right: 10px;""> <i title="Approve" class="fa fa-check"></i></a>';
                    }
                    else{
                        $links .= '';
                    }

                    return $links;

                })
                
                ->escapeColumns([])

                ->make(true);

    }

    public function branch_agt_credit(Request $request){

        $agents = Intermediary::leftJoin('intermediary_branch', function ($join) {
            $join->on('intermediary.intermediary_number', '=', 'intermediary_branch.intermediary_number');
        })
        ->leftJoin('intermediary_credit', function ($join) {
            $join->on('intermediary_credit.branch', '=', 'intermediary_branch.branch')
                 ->on('intermediary_credit.agent', '=', 'intermediary.intermediary_number');
        })
        ->where('intermediary_branch.branch', $request->get('branch'))
        ->where('intermediary.status', IntermediaryAccountStatus::active)
        ->where(function ($query) {
            $query->whereNull('intermediary_credit.branch')
                  ->whereNull('intermediary_credit.agent');
        })
        ->get([
            'intermediary.name',
            'intermediary.intermediary_number',
            'intermediary_branch.branch',
            'intermediary.acc_type'
        ]);

        echo $agents;

    }

    public function post_int_onboard_credit(Request $request){

        if(!Gate::check('onboard-intermediary-to-credit-facility')){

            return redirect()->back()->with('error', 'You do not have permissions to onboard intermediary');
        }

        $validate = Validator::make($request->all(), [
            'branch'        => 'required',
            'intermediary'  => 'required',
            'auto_utilize'  => 'required'
        ]);

        if ($validate->fails()){
            
            return redirect()->back()->with('error', 'Failed to onboard Intermediary. Kindly confirm the details and re-submit')->withErrors($validate);
        }

        $check_exists = IntermediaryCredit::where('branch',$request->get('branch'))->where('agent',$request->get('intermediary'))->exists();

        if($check_exists){
            return redirect()->back()->with('error', 'Intermediary already exists in the credit facility module. Kindly proceed to request float for them');
        }

        DB::beginTransaction();
        try{
            $Arr = array(); 

            $Arr = [
                'branch' => $request->get('branch'),
                'agent'  => $request->get('intermediary'),
                'amount' => 0,
                'amount_utilized' => 0,
                'auto_utilize' => $request->get('auto_utilize'),
                'created_by'   => Auth::user()->user_name,
                'created_at'   => Carbon::now()
            ];

            $int_onboard = IntermediaryCredit::insert($Arr);

            DB::commit();

            //return redirect()->route('int_credit_setup')->with('success','Intermediary onboarded successfully');
            return redirect()->route('int_credit_float',['branch'=>$request->get('branch'),'agent'=>$request->get('intermediary')])->with('success','Intermediary onboarded successfully');

        }
        catch(\Throwable $e){

            DB::rollback();

            return redirect()->back()->with('error', 'Failed to onboard intermediary. Please try again');
            
        }
    }

    public function post_clt_onboard_credit(Request $request){

        if(!Gate::check('onboard-client-to-credit-facility')){

            return redirect()->back()->with('error', 'You do not have permissions to onboard client');
        }
        
        $validate = Validator::make($request->all(), [
            'branch'        => 'required',
            'intermediary'  => 'required',
            'client_number'  => 'required',
            'auto_utilize'  => 'required'
        ]);

        if ($validate->fails()){
            
            return redirect()->back()->with('error', 'Failed to onboard Client. Kindly confirm the details and re-submit')->withErrors($validate);
        }

        $check_exists = ClientCredit::where('branch',$request->get('branch'))
                                    ->where('agent',$request->get('intermediary'))
                                    ->where('client_number',$request->get('client_number'))
                                    ->exists();

        if($check_exists){

            return redirect()->back()->with('error', 'Client already exists in the credit facility module under the said intermediary. Kindly proceed to request float for them');

        }

        DB::beginTransaction();
        try{
            $Arr = array(); 

            $Arr = [
                'client_number'   => $request->get('client_number'),
                'branch'          => $request->get('branch'),
                'agent'           => $request->get('intermediary'),
                'amount'          => 0,
                'amount_utilized' => 0,
                'auto_utilize'    => $request->get('auto_utilize'),
                'created_by'      => Auth::user()->user_name,
                'created_at'      => Carbon::now()
            ];

            $int_onboard = ClientCredit::insert($Arr);

            DB::commit();

            return redirect()->route('clt_credit_float',['branch'=>$request->get('branch'),'agent'=>$request->get('intermediary'),'client_number'=>$request->get('client_number')])->with('success','Client onboarded successfully');

        }
        catch(\Throwable $e){

            DB::rollback();

            return redirect()->back()->with('error', 'Failed to onboard client. Please try again');
            
        }
    }

    public function postCreditRequest_new(Request $request)
    {
        DB::Transaction(function() use ($request){
            $today = Carbon::today();
            $time = Carbon::now();

            $client_float = 0;
            $client_utilized = 0;
            $cur_client_utilized =0;
            $client_auto_approve = 'Y';

            $agency_float = 0;
            $agency_utilized = 0;
            $cur_agency_utilized = 0;
            $agency_auto_approve = 'Y';

            $branch = Branch::where('branch',$request->credit_branch)
                            ->first();
            $user = Auth::user()->user_name;
            $user = trim($user);
            $due_date = Carbon::today()->addDays($branch->branch_credit_days-1);

            $cbtrans = Cbtrans::where('doc_type','REC')
                                ->where('descr',$request->credit_entry_type)
                                ->first();
            $endt_no = removePolicyOrClaimFormat($request->credit_endt);
            $dcontrol = Dcontrol::where('endt_renewal_no',$endt_no)
                                ->first();
            $doctype = Doctype::where('doc_type',$request->credit_doc)
                                ->first();
            $dtrans_no = $doctype->credit_serial_no;
            $credit_reference = STR_PAD($dtrans_no,6,'0',STR_PAD_LEFT).$today->format('Y');
            

            $offcd = STR_PAD($request->credit_branch,3,'0',STR_PAD_LEFT);
            
            $w_line_no = Acdet::where('doc_type',$request->credit_doc)
                                ->where('reference',$credit_reference)
                                ->count();
            $w_line_no = $w_line_no+1;

            $clientcredit = ClientCredit::where('client_number',$dcontrol->client_number)
                                   ->where('branch',$dcontrol->branch)
                                   ->where('agent',$dcontrol->agent)
                                   ->first();
           
           $agmnfcredit = IntermediaryCredit::where('branch',$dcontrol->branch)
                                       ->where('agent',$dcontrol->agent)
                                       ->first();

           if($clientcredit){
                $client_auto_approve=$clientcredit->auto_utilize;
               $client_float = $clientcredit->amount;

                   if($request->credit_amount > $clientcredit->amount){
                       $cur_client_utilized = $clientcredit->amount; 
                       }else{
                       $cur_client_utilized = $request->credit_amount;
                       }
               $client_utilized = $clientcredit->amount_utilized + $cur_client_utilized;
               }

               if($agmnfcredit){

                   $agency_float = $agmnfcredit->amount;
                   $imeruka_client = $request->credit_amount-$client_float;
                   $agency_auto_approve = $agmnfcredit->auto_utilize;

                    if($request->credit_amount > $client_float && $imeruka_client <= $agency_float){
                        $cur_agency_utilized = $imeruka_client;
                    }else{
                        $cur_agency_utilized = 0;
                    }

                   $agency_utilized = $agmnfcredit->amount_utilized + $cur_agency_utilized;
                }

               if($client_auto_approve=='Y' && $agency_auto_approve=='Y'){
                       $manager = 'system';
                       $approved_by = 'system';
                       // $narration = 'Authorized during Float Approval';
                   }else {
                       $manager =null;
                       $approved_by =null;
                       // $narration =null;
                   }
           
            $cbcredit_data = [
                'doc_type'=>$request->credit_doc,
                'effective_date'=>$today,
                'dtrans_no'=>$dtrans_no,
                'credit_reference'=>$credit_reference,
                'offcd'=>$offcd,
                'account_year'=>$today->format('Y'),
                'account_month'=>$today->format('m'),
                'line_no'=>0,
                'entry_type_descr'=>$request->credit_entry_type,
                'debit_account'=>$cbtrans->debit_account,
                'credit_account'=>$cbtrans->credit_account,
                'source_code'=>$cbtrans->source_code,
                'sltype'=>$cbtrans->subledger_code,
                'trans_type'=>$cbtrans->trans_type,
                'client_number'=>$request->client_no,
                'receipt_date'=>$today,
                'credit_due_date'=>$due_date,
                'slhead'=>$request->slhead,
                'on_account'=>'P',
                'claim_no'=>$dcontrol->endt_renewal_no,
                'policy_no'=>$dcontrol->policy_no,
                'class'=>$dcontrol->class,
                'prepared_by'=>$dcontrol->user_1,
                'branch'=>$dcontrol->branch,
                'financed'=>$dcontrol->financed,
                'finance_code'=>$dcontrol->financed_code,
                'agent_no'=>str_pad($dcontrol->agent,5,'0',STR_PAD_LEFT),
                'name'=>$request->credit_name,
                'amount'=>$request->credit_amount,
                'orig_cred_amt'=>$request->credit_amount,
                'out_cred_amt'=>$request->credit_amount,
                'amount_in_words'=>$request->amount_words,
                'user_name'=>trim($user),
                'batch_no'=>STR_PAD($dtrans_no,6,'0',STR_PAD_LEFT).'CR',
                'created_date'=>$today,
                'created_time'=>$time,
                'dola'=>$today,
                'ln_no'=>$w_line_no,
                'narration'=>$request->narration,
                'escalate_to'=>$request->escalate_credit,
                'postdated_cheque'=>$request->plan_postdated_cheque,
                'postdated_cheque_no'=>$request->plan_cheque_no,
                'postdated_cheque_date'=>$request->plan_cheque_date,
                'postdated_cheque_amt'=>$request->plan_cheque_amount,
               'approved_by'=>$approved_by,
               'manager'=>$manager,

               
            ];	
            $create_cbcredit = Cbcredit::insert($cbcredit_data);

            if($create_cbcredit){


           //update next doc type serial
           $next_serial = Doctype::where('doc_type',$request->credit_doc)
           ->update(['credit_serial_no'=>$doctype->credit_serial_no+1]);

                   ##escalate to Team leader/UW manager
                   if( $request->escalate_credit != null && ($client_auto_approve=='N' || $agency_auto_approve=='N')){

                       $sender_id =  Auth::user()->user_id;
                       $name =  Auth::user()->user_name;
                       $user_id = $request->escalate_credit;
                       
                       $count = Escalate_pol::where('sent_by',$sender_id)->max('escalate_id');
                       $next = $count + 1;
                       $recieverdet = Aimsuser_web::where('user_id',$user_id)->first();
                       
                       $reciever = trim($recieverdet->user_name);
                       $sent_id = trim($recieverdet->user_id);
                       $emailaddr  = $recieverdet->email;
                       $amnts = number_format($request->credit_amount);

                       $escalate = new Escalate_pol ;
                       $escalate->escalate_id = $next;
                       $escalate->type = 'CRD';
                       $escalate->description = 'CREDIT APPROVAL';
                       $mess = "Kindly Approve the Credit Request  of Amount <strong>'$amnts'</strong> with Ref no. '$credit_reference' .Thank You. ";
                       $category = 'POLICY CREDIT APPROVAL';
                       
                       $escalate->req_no =$request->$credit_reference;
                       $escalate->endorse_no =removePolicyOrClaimFormat($request->credit_endt);
                       $escalate->policy_no =removePolicyOrClaimFormat($request->credit_policy);
                       $escalate->sent_to =$sent_id;
                       $escalate->sent_by =$sender_id;
                       $escalate->credit_reference =$credit_reference;
                       $escalate->user_name = $name;
                       $escalate->created_at =  Carbon::now();
                       $escalate->save();
           
                       $sendemail = new SendEmail;
                       $sendemail->category = $category ;
                       $sendemail->receiver =$emailaddr;
                       $sendemail->message =$mess;
                       $sendemail->creator = $name;
                       $sendemail->save();
                   }

                //update client float
                
               if($clientcredit){
                   if($cur_client_utilized>0){

                       $this->insertCreditUtilization($credit_reference,$offcd,$request->credit_doc,$request->credit_entry_type,'CLIENT',$cur_client_utilized);

                       $client_upd = ClientCredit::where('client_number',$client->client_number)
                                               ->where('branch',$dcontrol->branch)
                                               ->where('agent',$dcontrol->agent)
                                               ->update([
                                                   'amount_utilized'=>$client_utilized
                                                   ]);
                   

                   }							
               }
                //update intermediary float

               if($agmnfcredit){
                   if($cur_agency_utilized > 0 ){

                       $this->insertCreditUtilization($credit_reference,$offcd,$request->credit_doc,$request->credit_entry_type,'INTERMEDIARY',$cur_agency_utilized);

                     $agmnf_upd = IntermediaryCredit::where('branch',$dcontrol->branch)
                                       ->where('agent',$dcontrol->agent)
                                       ->update([
                                           'amount_utilized'=>$agency_utilized
                                       ]);
                   }
               }
           if( $request->escalate_credit != null){
            Session::Flash('success','Credit authorization '.formatReference($credit_reference).' Escalated successfully');
           }else {
               Session::Flash('success','Credit reference '.formatReference($credit_reference).' successfully generated and Auto-Authorized');
               $clientRecord = Client::where('client_number',$dcontrol->client_number)->first();
               $recordExists = DB::table('debited_pol_dispatch')
                                ->where('policy_no', $dcontrol->policy_no)
                                ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                                ->exists();
              if(!$recordExists){
                saveDebitedPolDispatch([
                  'policy_no' => $dcontrol->policy_no,
                  'endt_renewal_no' => $dcontrol->endt_renewal_no,
                  'client_no' =>$dcontrol->client_number,
                  'e_mail' =>$clientRecord->e_mail,
                  'mobile_no' =>$clientRecord->mobile_no
                ]);
    
                $notificationData = [
                  'pol_sched_doc_data'=>['endt_renewal_no'=> $dcontrol->endt_renewal_no]
                ];
                DispatchNotificationEvent::dispatch($slug = 'policy-schedule-doc-crqst',$notificationData);
              }
           }

            }
        });	
    }
    
    function insertCreditUtilization($credit_reference,$offcd,$doctype,$entry_type_descr,$credit_source,$utilize_amount){

        $cbcredit = Cbcredit::where('offcd',$offcd)
                            ->where('doc_type',$doctype)
                            ->where('entry_type_descr',$entry_type_descr)
                            ->where('credit_reference',$credit_reference)
                            ->first();
        $user = Auth::user()->user_name;
        $today = Carbon::today();

            if($cbcredit){
                $agency_utilization_data = [
                    'credit_reference'=>$credit_reference,
                    'branch' =>$cbcredit->branch,
                    'agent' =>$cbcredit->agent_no,
                    'client_number' =>$cbcredit->client_number,
                    'credit_source' =>$credit_source,
                    'entry_type_descr' =>$entry_type_descr,
                    'amount' =>$utilize_amount,
                    'created_by' =>trim($user),
                    'created_at' =>$today,
                    ];
                
                $create_credit_utilize = CreditUtilization::insert($agency_utilization_data);
            }
    }

    public function intermediary_float_request(Request $request){

        if(!Gate::check('onboard-intermediary-to-credit-facility')){

            return redirect()->back()->with('error', 'You do not have permission to request credit float for intermediary');

        }
        
        $validate = Validator::make($request->all(), [
            'int_float_branch'          => 'required',
            'int_float_agent'           => 'required',
            'int_float_topup'           => 'required',
            'int_float_escalate_to'     => 'required',
            'int_float_request_comment' => 'required'
        ]);

        if ($validate->fails()){
            
            return redirect()->back()->with('error', 'Failed to capture intermediary float. Kindly confirm the details and re-submit')->withErrors($validate);

        }

        $branch = $request->get('int_float_branch');
        $agent = $request->get('int_float_agent');
        $topup = str_replace(',','',$request->get('int_float_topup'));

        $check_active = $this->check_active_intermediary($branch, $agent);

        if(!$check_active){

            return redirect()->back()->with('error', 'Intermediary has been stopped. You cannot request for float');

        }

        $check_pending_float = IntermediaryCreditHist::where('branch',$branch)
                                                     ->where('agent',$agent)
                                                     ->where('status','PENDING')
                                                     ->count();

        if($check_pending_float > 0){

            return redirect()->back()->with('error', 'Cannot request new float. There is a pending float request not actioned');

        }

        DB::beginTransaction();

        try{

            $int_rec = IntermediaryCredit::where('branch',$branch)
                                        ->where('agent',$agent)
                                        ->first();
                            
            

            $new_amount = (float)$int_rec->amount + (float)$topup;

            $Arr = array(); 

            $Arr = [
                        'reference'       =>  str_pad(CreditSetup::generateIntermediaryRef(),6,"0",STR_PAD_LEFT).date('Y'),
                        'branch'          =>  $branch,
                        'agent'           =>  $agent,
                        'amount'          =>  $new_amount,
                        'created_by'      =>  Auth::user()->user_name,
                        'created_at'      =>  Carbon::now(),
                        'status'          =>  'PENDING',
                        'action_by'       =>  $request->get('int_float_escalate_to'),
                        'action_at'       =>  '',
                        'action_comment'  =>  '',
                        'request_comment' =>  $request->get('int_float_request_comment')                
                    ];

            $int_onboard = IntermediaryCreditHist::insert($Arr);

            $Arr['type'] = 'INT';

            $this->send_to_approver($Arr);

            DB::commit();

            return redirect()->route('int_credit_float',['branch'=>$branch,'agent'=>$agent])->with('success','Intermediary float request raised successfully');

        }
        catch(\Throwable $e){

            DB::rollback();

            return redirect()->back()->with('error', 'Failed to capture intermediary float. Please try again');
            
        }

    }

    public function client_float_request(Request $request){

        if(!Gate::check('onboard-client-to-credit-facility')){

            return redirect()->back()->with('error', 'You do not have permission to request credit float for intermediary');

        }
        
        $validate = Validator::make($request->all(), [
            'clt_float_client_number' => 'required',
            'clt_float_branch'       => 'required',
            'clt_float_branch'       => 'required',
            'clt_float_agent'        => 'required',
            'clt_float_topup'        => 'required',
            'clt_float_escalate_to'  => 'required',
            'clt_float_request_comment' => 'required'
        ]);

        if ($validate->fails()){
            
            return redirect()->back()->with('error', 'Failed to capture intermediary float. Kindly confirm the details and re-submit')->withErrors($validate);

        }

        $branch = $request->get('clt_float_branch');
        $agent = $request->get('clt_float_agent');
        $client_number = $request->get('clt_float_client_number');
        $topup = str_replace(',','',$request->get('clt_float_topup'));

        $check_active = $this->check_active_intermediary($branch, $agent);

        if(!$check_active){

            return redirect()->back()->with('error', 'Intermediary has been stopped. You cannot request for float');

        }

        $check_pending_float = ClientCreditHist::where('client_number',$client_number)
                                                ->where('branch',$branch)
                                                ->where('agent',$agent)
                                                ->where('status','PENDING')
                                                ->count();

        if($check_pending_float > 0){

            return redirect()->back()->with('error', 'Cannot request new float. There is a pending float request not actioned');

        }

        DB::beginTransaction();

        try{

            $clt_rec = ClientCredit::where('client_number',$client_number)
                                    ->where('branch',$branch)
                                    ->where('agent',$agent)
                                    ->first();
                            
            
            $new_amount = (float)$clt_rec->amount + (float)$topup;

            $Arr = array(); 

            $Arr = [
                        'reference'       =>  str_pad(CreditSetup::generateClientRef(),6,"0",STR_PAD_LEFT).date('Y'),
                        'client_number'   =>  $client_number,
                        'branch'          =>  $branch,
                        'agent'           =>  $agent,
                        'amount'          =>  $new_amount,
                        'created_by'      =>  Auth::user()->user_name,
                        'created_at'      =>  Carbon::now(),
                        'status'          =>  'PENDING',
                        'action_by'       =>  $request->get('clt_float_escalate_to'),
                        'action_at'       =>  '',
                        'action_comment'  =>  '',
                        'request_comment' =>  $request->get('clt_float_request_comment')                
                    ];

            $clt_onboard = ClientCreditHist::insert($Arr);

            $Arr['type'] = 'CLT';

            $this->send_to_approver($Arr);

            DB::commit();

            return redirect()->route('clt_credit_float',['branch'=>$branch,'agent'=>$agent,'client_number'=>$client_number])->with('success','Client float request raised successfully');

        }
        catch(\Throwable $e){

            DB::rollback();

            return redirect()->back()->with('error', 'Failed to capture Client float. Please try again');
            
        }

    }

    public function action_float_request(Request $request){

        if(!Gate::check('approve-credit-float-request')){

            return redirect()->back()->with('error', 'You do not have permissions to action float requests');

        }

        $type = $request->input('appr_type');

        if($type == 'INT_FLOAT'){
            $response = $this->action_intermediary_float($request);
        }
        else if($type == 'CLT_FLOAT'){
            $response = $this->action_client_float($request);
        }

        return $response;

    }

    public function action_intermediary_float($request){

        $validate = Validator::make($request->all(), [
                            'appr_reference'      => 'required',
                            'appr_branch'         => 'required',
                            'appr_agent'          => 'required',
                            'appr_action'         => 'required',
                            'appr_action_comment' => 'required'
                        ]);

        if ($validate->fails()){
            
            return redirect()->back()->with('error', 'Failed to action intermediary float. Kindly confirm the details and re-submit')->withErrors($validate);

        }

        $branch = $request->get('appr_branch');
        $agent = $request->get('appr_agent');
        $reference = $request->get('appr_reference');
        $approver = Auth::user()->user_name;
        $approver_det = Aimsuser::where('user_name', $approver)->first();
        $action = $request->get('appr_action');
        $action_comment = $request->get('appr_action_comment');

        //Check if intermediary is active
        if(!$this->check_active_intermediary($branch, $agent)){

            return redirect()->back()->with('error', 'Intermediary has been stopped. You cannot action');

        }

        $float_rec = IntermediaryCreditHist::where('branch',$branch)
                                            ->where('agent',$agent)
                                            ->where('reference',$reference)
                                            ->first();

        if(in_array($float_rec->status, ["REJECTED","CANCELLED","DECLINED","APPROVED"])){

            return redirect()->back()->with('error', 'Cannot action an already actioned request.');

        }

        DB::beginTransaction();

        try{

            $upd_float_appr = IntermediaryCreditHist::where('branch',$branch)
                                                    ->where('agent',$agent)
                                                    ->where('reference',$reference)
                                                    ->update([
                                                        'status'         => $action,
                                                        'action_by'      =>  $approver,
                                                        'action_at'      =>  Carbon::now(),
                                                        'action_comment' =>  $action_comment,
                                                        'updated_by'              => Auth::user()->user_name,
                                                        'updated_at'              => Carbon::now()
                                                    ]);
                    

            if($upd_float_appr && $action == 'APPROVED'){

                $escalupd = Escalate_pol::where('req_no',$reference)
                                        ->where('endorse_no',$reference)
                                        ->where(function($query){
                                            $query->whereNotIn('reescalated',['Y','y'])
                                                ->orWhereNull('reescalated');
                                        })
                                        ->where('type',$this->intermediary_type)
                                        ->update([
                                            'approved' => 'Y',
                                            'approved_by' => $approver_det->user_id,
                                            'approved_date' => Carbon::now(),
                                            'approver_remarks' => $action_comment,
                                        ]);

                $upd_float_appr = IntermediaryCredit::where('branch',$branch)
                                            ->where('agent',$agent)
                                            ->update([
                                                'amount'                  => $float_rec->amount,
                                                'last_approved_reference' => $float_rec->reference,
                                                'updated_by'              => Auth::user()->user_name,
                                                'updated_at'              => Carbon::now()
                                            ]);

            }

            $Arr = array(); 

            $Arr = [
                'ref'             =>  $reference,
                'user_name'       =>  $float_rec->created_by,
                'type'            =>  $action            
            ];

            $this->email_request($Arr);

            DB::commit();

            return redirect()->route('int_credit_float',['branch'=>$branch,'agent'=>$agent])->with('success','Intermediary float request '.$action.' successfully');

        }
        catch(\Throwable $e){

            DB::rollback();
            
            return redirect()->back()->with('error', 'Failed to action intermediary float request. Please try again');
            
        }
    }

    public function action_client_float($request){

        $validate = Validator::make($request->all(), [
                            'appr_reference'      => 'required',
                            'appr_client_number'  => 'required',
                            'appr_branch'         => 'required',
                            'appr_agent'          => 'required',
                            'appr_action'         => 'required',
                            'appr_action_comment' => 'required'
                        ]);

        if ($validate->fails()){
            
            return redirect()->back()->with('error', 'Failed to action intermediary float. Kindly confirm the details and re-submit')->withErrors($validate);

        }

        $branch = $request->get('appr_branch');
        $agent = $request->get('appr_agent');
        $client_number = $request->get('appr_client_number');
        $reference = $request->get('appr_reference');
        $approver = Auth::user()->user_name;
        $approver_det = Aimsuser::where('user_name', $approver)->first();
        $action = $request->get('appr_action');
        $action_comment = $request->get('appr_action_comment');

        //Check if intermediary is active
        if(!$this->check_active_intermediary($branch, $agent)){

            return redirect()->back()->with('error', 'Intermediary has been stopped. You cannot action');

        }

        $float_rec = ClientCreditHist::where('client_number',$client_number)
                                      ->where('branch',$branch)
                                      ->where('agent',$agent)
                                      ->where('reference',$reference)
                                      ->first();

        if(in_array($float_rec->status, ["REJECTED","CANCELLED","DECLINED","APPROVED"])){

            return redirect()->back()->with('error', 'Cannot action an already actioned request.');

        }

        DB::beginTransaction();

        try{

            $upd_float_appr = ClientCreditHist::where('client_number',$client_number)
                                               ->where('branch',$branch)
                                               ->where('agent',$agent)
                                               ->where('reference',$reference)
                                               ->update([
                                                        'status'         => $action,
                                                        'action_by'      =>  $approver,
                                                        'action_at'      =>  Carbon::now(),
                                                        'action_comment' =>  $action_comment,
                                                        'updated_by'     => Auth::user()->user_name,
                                                        'updated_at'     => Carbon::now()
                                                ]);

            if($upd_float_appr && $action == 'APPROVED'){

                $escalupd = Escalate_pol::where('req_no',$reference)
                                        ->where('endorse_no',$reference)
                                        ->where(function($query){
                                            $query->whereNotIn('reescalated',['Y','y'])
                                                ->orWhereNull('reescalated');
                                        })
                                        ->where('type',$this->client_type)
                                        ->update([
                                            'approved' => 'Y',
                                            'approved_by' => $approver_det->user_id,
                                            'approved_date' => Carbon::now(),
                                            'approver_remarks' => $action_comment,
                                        ]);

                $upd_float_appr = ClientCredit::where('client_number',$client_number)
                                               ->where('branch',$branch)
                                               ->where('agent',$agent)
                                               ->update([
                                                    'amount'                  => $float_rec->amount,
                                                    'last_approved_reference' => $float_rec->reference,
                                                    'updated_by'              => Auth::user()->user_name,
                                                    'updated_at'              => Carbon::now()
                                                ]);

            }

            $Arr = array(); 

            $Arr = [
                'ref'             =>  $reference,
                'user_name'       =>  $float_rec->created_by,
                'type'            =>  $action            
            ];

            $this->email_request($Arr);

            DB::commit();

            return redirect()->route('clt_credit_float',['branch'=>$branch,'agent'=>$agent,'client_number'=>$client_number])->with('success','Client float request '.$action.' successfully');

        }
        catch(\Throwable $e){

            DB::rollback();
            
            return redirect()->back()->with('error', 'Failed to action Client float request. Please try again');
            
        }
    }

    public function amend_credit_float(Request $request){

        // if(!Gate::check('approve-credit-float-request')){

        //     return redirect()->back()->with('error', 'You do not have permissions to action float requests');

        // }

        $type = $request->input('trans_type');
        //$amend_type = $request->input('amend_type');

        if($type == 'INT'){
            $response = $this->amend_intermediary_float($request);
        }
        else if($type == 'CLT'){
            $response = $this->amend_client_float($request);
        }

        return $response;

    }

    public function amend_intermediary_float($request){

        $amend_type = $request->input('amend_type');
        $branch = $request->get('ed_float_branch');
        $agent = $request->get('ed_float_agent');
        $reference = $request->get('ed_reference');
        $topup = str_replace(',','',$request->get('ed_float_topup'));
        $Arr = array(); 

        if(!$this->check_active_intermediary($branch, $agent)){

            return redirect()->back()->with('error', 'Intermediary has been stopped. You cannot perform action');

        }

        $curr_float = IntermediaryCreditHist::where('branch',$branch)
                                                ->where('agent',$agent)
                                                ->where('reference',$reference)
                                                ->first();

        if(trim($curr_float->created_by) != trim(Auth::user()->user_name)){

            return redirect()->back()->with('error', 'Cannot perform action. You are not the one who raised the request');
                                        
        }

        DB::beginTransaction();

        try{

            if($amend_type == 'AMEND'){

                $validate = Validator::make($request->all(), [
                    'ed_float_branch'       => 'required',
                    'ed_float_agent'        => 'required',
                    'ed_float_topup'        => 'required',
                    'ed_reference'          => 'required',
                    'ed_float_comment'      => 'required'
                ]);
        
                if ($validate->fails()){
                    
                    return redirect()->back()->with('error', 'Failed to capture intermediary float. Kindly confirm the details and re-submit')->withErrors($validate);
        
                }
    
    
                $int_rec = IntermediaryCredit::where('branch',$branch)
                                                ->where('agent',$agent)
                                                ->first();
    
                $new_amount = (float)$int_rec->amount + (float)$topup;
    
                $Arr = [
                        'amount'          =>  $new_amount,
                        'updated_by'      =>  Auth::user()->user_name,
                        'updated_at'      =>  Carbon::now(),
                        'status'          =>  'PENDING',
                        'request_comment' =>  trim($request->input('ed_float_comment'))             
                        ];
    
                $int_onboard = IntermediaryCreditHist::where('branch',$branch)
                                                        ->where('agent',$agent)
                                                        ->where('reference',$reference)
                                                        ->update($Arr);
    
                $success_msg = 'Intermediary float request amended successfully';
                    
                $fail_msg = 'Failed to amend intermediary float. Please try again';
                    
            }
            else if($amend_type == 'RE-ESCALATE'){

                $validate = Validator::make($request->all(), [
                    'ed_float_branch'       => 'required',
                    'ed_float_agent'        => 'required',
                    'ed_reference'          => 'required',
                    'ed_escalate_to'      => 'required'
                ]);
        
                if ($validate->fails()){
                    
                    return redirect()->back()->with('error', 'Failed to re-escalate intermediary float. Kindly confirm the details and re-submit')->withErrors($validate);
        
                }
    
    
                $Arr = [
                          'action_by'       =>  $request->input('ed_escalate_to'),
                          'updated_by'      =>  Auth::user()->user_name,
                          'updated_at'      =>  Carbon::now(),
                          'status'          =>  'PENDING'            
                       ];
    
                $int_onboard_ed = IntermediaryCreditHist::where('branch',$branch)
                                                        ->where('agent',$agent)
                                                        ->where('reference',$reference)
                                                        ->update($Arr);
    
                $Arr += array(
                                    'reference'       =>  $curr_float->reference,
                                    'branch'          =>  $branch,
                                    'agent'           =>  $agent,
                                    'amount'          =>  $new_amount,
                                    'created_by'      =>  $curr_float->created_by,
                                    'created_at'      =>  $curr_float->created_at,
                                    'status'          =>  'PENDING',
                                    'request_comment' =>   $curr_float->request_comment,
                                    'type'            => 'INT'              
                        );
    
                $this->send_to_approver($Arr);
    
                $success_msg = 'Intermediary float request re-escalated successfully';
                    
                $fail_msg = 'Failed to re-escalate intermediary float. Please try again';  
    
            }
            else if($amend_type == 'CANCEL'){

                $validate = Validator::make($request->all(), [
                    'ed_float_branch'       => 'required',
                    'ed_float_agent'        => 'required',
                    'ed_reference'          => 'required',
                ]);
        
                if ($validate->fails()){
                    
                    return redirect()->back()->with('error', 'Failed to cancel intermediary float. Kindly confirm the details and re-submit')->withErrors($validate);
        
                }

                $Arr = [
                    'action_by'       =>  Auth::user()->user_name,
                    'action_at'       =>  Carbon::now(),
                    'updated_by'      =>  Auth::user()->user_name,
                    'updated_at'      =>  Carbon::now(),
                    'status'          =>  'CANCELLED',
                    'action_comment'  =>  'CANCELLED'           
                 ];

                $int_onboard_ed = IntermediaryCreditHist::where('branch',$branch)
                                                        ->where('agent',$agent)
                                                        ->where('reference',$reference)
                                                        ->update($Arr);
                                    

                $success_msg = 'Intermediary float request cancelled successfully';
                    
                $fail_msg = 'Failed to cancel intermediary float. Please try again'; 

            }

            DB::commit();

            return redirect()->route('int_credit_float',['branch'=>$branch,'agent'=>$agent])->with('success', $success_msg);

        }
        catch(\Throwable $e){

            DB::rollback();
            
            return redirect()->back()->with('error', $fail_msg);

        }
        
    }

    public function amend_client_float($request){

        $amend_type = $request->input('amend_type');
        $client_number = $request->get('ed_float_client_number');
        $branch = $request->get('ed_float_branch');
        $agent = $request->get('ed_float_agent');
        $reference = $request->get('ed_reference');
        $topup = str_replace(',','',$request->get('ed_float_topup'));
        $Arr = array(); 

        if(!$this->check_active_intermediary($branch, $agent)){

            return redirect()->back()->with('error', 'Intermediary has been stopped. You cannot perform action');

        }
        
        $curr_float = ClientCreditHist::where('client_number',$client_number)
                                        ->where('branch',$branch)
                                        ->where('agent',$agent)
                                        ->where('reference',$reference)
                                        ->first();
                

        if(trim($curr_float->created_by) != trim(Auth::user()->user_name)){

            return redirect()->back()->with('error', 'Cannot perform action. You are not the one who raised the request');
                                        
        }

        DB::beginTransaction();

        try{

            if($amend_type == 'AMEND'){

                $validate = Validator::make($request->all(), [
                    'ed_float_client_number' => 'required',
                    'ed_float_branch'        => 'required',
                    'ed_float_agent'         => 'required',
                    'ed_float_topup'         => 'required',
                    'ed_reference'           => 'required',
                    'ed_float_comment'       => 'required'
                ]);
        
                if ($validate->fails()){
                    
                    return redirect()->back()->with('error', 'Failed to capture client float. Kindly confirm the details and re-submit')->withErrors($validate);
        
                }
    
    
                $clt_rec = ClientCredit::where('client_number',$client_number)
                                        ->where('branch',$branch)
                                        ->where('agent',$agent)
                                        ->first();
    
                $new_amount = (float)$clt_rec->amount + (float)$topup;
    
                $Arr = [
                        'amount'          =>  $new_amount,
                        'updated_by'      =>  Auth::user()->user_name,
                        'updated_at'      =>  Carbon::now(),
                        'status'          =>  'PENDING',
                        'request_comment' =>  trim($request->input('ed_float_comment'))             
                        ];
    
                $int_onboard = ClientCreditHist::where('client_number',$client_number)
                                                ->where('branch',$branch)
                                                ->where('agent',$agent)
                                                ->where('reference',$reference)
                                                ->update($Arr);
    
                $success_msg = 'Client float request amended successfully';
                    
                $fail_msg = 'Failed to amend client float. Please try again';
                    
            }
            else if($amend_type == 'RE-ESCALATE'){

                $validate = Validator::make($request->all(), [
                    'ed_float_client_number' => 'required',
                    'ed_float_branch'        => 'required',
                    'ed_float_agent'         => 'required',
                    'ed_reference'           => 'required',
                    'ed_escalate_to'         => 'required'
                ]);
        
                if ($validate->fails()){
                    
                    return redirect()->back()->with('error', 'Failed to re-escalate client float. Kindly confirm the details and re-submit')->withErrors($validate);
        
                }
    
    
                $Arr = [
                          'action_by'       =>  $request->input('ed_escalate_to'),
                          'updated_by'      =>  Auth::user()->user_name,
                          'updated_at'      =>  Carbon::now(),
                          'status'          =>  'PENDING'            
                       ];
    
                $int_onboard_ed = ClientCreditHist::where('client_number',$client_number)
                                                    ->where('branch',$branch)
                                                    ->where('agent',$agent)
                                                    ->where('reference',$reference)
                                                    ->update($Arr);
    
                $Arr += array(
                                    'reference'       =>  $curr_float->reference,
                                    'client_number'   =>  $client_number,
                                    'branch'          =>  $branch,
                                    'agent'           =>  $agent,
                                    'amount'          =>  $new_amount,
                                    'created_by'      =>  $curr_float->created_by,
                                    'created_at'      =>  $curr_float->created_at,
                                    'status'          =>  'PENDING',
                                    'request_comment' =>   $curr_float->request_comment,
                                    'type'            => 'CLT'              
                        );
    
                $this->send_to_approver($Arr);
    
                $success_msg = 'Client float request re-escalated successfully';
                    
                $fail_msg = 'Failed to re-escalate client float. Please try again';  
    
            }
            else if($amend_type == 'CANCEL'){

                $validate = Validator::make($request->all(), [
                    'ed_float_client_number' => 'required',
                    'ed_float_branch'        => 'required',
                    'ed_float_agent'         => 'required',
                    'ed_reference'           => 'required',
                ]);
        
                if ($validate->fails()){
                    
                    return redirect()->back()->with('error', 'Failed to cancel client float. Kindly confirm the details and re-submit')->withErrors($validate);
        
                }

                $Arr = [
                    'action_by'       =>  Auth::user()->user_name,
                    'action_at'       =>  Carbon::now(),
                    'updated_by'      =>  Auth::user()->user_name,
                    'updated_at'      =>  Carbon::now(),
                    'status'          =>  'CANCELLED',
                    'action_comment'  =>  'CANCELLED'           
                 ];

                $clt_onboard_ed = ClientCreditHist::where('client_number',$client_number)
                                                    ->where('branch',$branch)
                                                    ->where('agent',$agent)
                                                    ->where('reference',$reference)
                                                    ->update($Arr);
                                    

                $success_msg = 'Client float request cancelled successfully';
                    
                $fail_msg = 'Failed to cancel client float. Please try again'; 

            }

            DB::commit();

            return redirect()->route('clt_credit_float',['branch'=>$branch,'agent'=>$agent,'client_number'=>$client_number])->with('success', $success_msg);

        }
        catch(\Throwable $e){

            DB::rollback();
            
            return redirect()->back()->with('error', $fail_msg);

        }
        
    }

    public function check_active_intermediary($branch, $agent){

        // $stop_flag = Agmnf::where('branch',$branch)
        //                                   ->where('agent',$agent)
        //                                   ->first()->stop_flag;

        // $active = ($stop_flag == 'N' || $stop_flag == null) ? true : false;

        // return $active;

        $intermediaryParams = new IntermediaryQueryParams([
            'branch' => $branch,
            'agentNo' => $agent
        ]);

        $active  =IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->get();

        return $active;

    }

    public function send_to_approver($arr)
    {

        $user = trim(Auth::user()->user_id);
        $username = trim(Auth::user()->user_name);

        $sent_to_rec = Aimsuser::where('user_name', $arr['action_by'])->first();

        try{
            $sent_to = $sent_to_rec->user_id;
            $ref = $arr['reference'];
            $comm = $arr['request_comment'];
            
            $type = ($arr['type'] == 'INT') ? $this->intermediary_type : $this->client_type;
            //$type = 'CREDITFRQ';

            $type_descr = ($arr['type'] == 'INT') ? 'INTERMEDIARY CREDIT FLOAT APPROVAL' : 'CLIENT CREDIT FLOAT APPROVAL';

            $escalate = new Escalate_pol ;

            //$count = Escalate_pol::max('escalate_id');
            $refq = Escalate_pol::where('sent_by',$user)
                                ->where('type', $type)
                                ->where('req_no',$ref)
                                ->update([
                                    'reescalated' => 'Y',
                                    'reescalated_to' => $sent_to,
                                    're_escalate_date' => Carbon::now()
                                ]);

            $count = Escalate_pol::where('sent_by',$user)->where('type', $type)->max('escalate_id');
            $next = $count + 1;

            $escalate->policy_no = $ref;
            $escalate->endorse_no =$ref;
            $escalate->escalate_id = $next;
            $escalate->sent_by =$user;
            $escalate->sent_to =$sent_to;
            $escalate->type = $type;
            $escalate->req_no = $ref;
            $escalate->escalate_type = 9;
            $escalate->approved = 'N';
            $escalate->description = $type_descr;
            $escalate->user_name = $username;
            $escalate->comments = $comm;
            $escalate->created_at =  Carbon::now();
            $escalate->save();

            //DB::commit();
            //send email to user
            $email = Aimsuser::where('user_id', $sent_to)->first();
            $emailaddr = $email->email;
            $reciever = $email->first_name;
            $name = trim(Auth::user()->user_name);

            $sendemail = new SendEmail;
            $sendemail->category = $type_descr;
            $sendemail->receiver =$emailaddr;
            $sendemail->message ="Kindly approve credit float request with reference no '$ref'.
                <br>Request Comments: $comm. <br> Thank You. ";
            $sendemail->creator = $name;
            $sendemail->save();

        }catch (\Exeption $e) {
            
        }
        
    }

    public function email_request($arr){

		$approver = Aimsuser::where('user_name',$arr['user_name'])->first();

		if($arr['type'] == 'APPROVED'){
			$category = 'CREDIT FLOAT REQUEST APPROVED';
			$message = "Your credit float request of reference ".$arr['ref']." has been approved";

		}
		else if($arr['type'] == 'REJECTED' || $arr['type'] == 'DECLINED'){
			$category = 'CREDIT FLOAT REQUEST DECLINED';
			$message = "Your credit float request of reference ".$arr['ref']." has been declined";
		}
		else if($arr['type'] == 'CANCELLED'){
			$category = 'CREDIT FLOAT REQUEST CANCELLATION';
			$message = "Your request of reference ".$arr['ref']." has been cancelled";
		}
		
        $email = $approver->email;
        $name = $approver->name;
    
        //$this->send_email($category,$email,$message,$name);
	
		$sendemail = new SendEmail;

        $sendemail->category = $category ;
        $sendemail->receiver = $email;
        $sendemail->message =$message;
        $sendemail->creator = $name;
		$sendemail->status = 'SEND';

        $sendemail->save();

	}
}
