<?php

namespace App\Http\Controllers\gb\underwriting;

use App\Unit;
use App\Branch;
use App\Cities;
use App\Credit;
use App\Crmast;
use App\Gender;
use App\Acctype;
use App\Country;
use App\Pipcnam;
use App\Regions;
use App\Currency;
use App\District;
use App\Olbranch;
use App\Tax_code;
use Carbon\Carbon;
use App\Olbnknames;
use App\Agcom_categ;
use App\Beneficiary;
use App\PartnerBank;
use App\Dist_channel;
use App\Postal_codes;
use App\Identity_type;
use App\Models\Aimsuser;
use App\Models\Location;
use App\Models\SubAgent;
use App\Organisation_role;
use App\Models\PaymentMode;
use App\Models\VendorGroup;
use Illuminate\Http\Request;
use App\Beneficiary_relation;
use App\Models\CompanyModule;
use App\Models\LocationLevel;
use App\Models\PartnerStatus;
use Ya<PERSON>ra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Gate;
use App\Enums\IntermediaryAccountStatus;
use App\Models\PartnerConsentFieldParam;
use App\Traits\GenerateOnboardingNumber;
use App\Models\Intermediary\Intermediary;
use App\Models\Intermediary\PartnerConsent;
use App\Http\Requests\SaveAgntBranchRequest;
use App\Models\Intermediary\IntermediaryBranch;
use App\Models\Intermediary\IntermediaryTaxType;
use App\Models\Intermediary\IntermediaryTaxGroup;
use App\Models\Intermediary\IntermediaryAttribute;
use App\Models\Intermediary\PartnerBankFieldParam;
use App\Http\Controllers\parameters\gb\Mainparam_gb;
use App\Models\Intermediary\IntermediaryFieldsParam;
use App\Models\Intermediary\PartnerContactFieldParam;
use App\Models\Intermediary\IntermediaryAttributeType;
use App\Models\Intermediary\IntermediaryStatusHistory;
use App\Models\Intermediary\PartnerIdentificationDocuments;

use App\Models\Intermediary\IntermediaryBranchStatusHistory;
use App\Http\Controllers\gb\underwriting\IntergrationController;

class IntermediaryController extends Controller
{
    use GenerateOnboardingNumber;
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //Select field values
        $company_modules = CompanyModule::select('company_module_code', 'company_module_name')->get()->toArray();
        $payment_methods = PaymentMode::select('paymode_code', 'paymode_name')->get()->toArray();
        $banks=Olbnknames::select('bank_code', 'description')->get()->toArray();
        $currencies = Currency::select('currency_code', 'currency')->get()->toArray();
        $acctype = Acctype::select('acc_type', 'description')
            ->where('TREATY_FAC_ACCOUNT', 'N')
            ->get()
            ->map(function ($actyp) {
                $actyp->description = "{$actyp->acc_type} - {$actyp->description}";
                return $actyp;
            })->toArray();
        $branch = Branch::select('branch', 'description')->where('TREATY_FAC_BRANCH', 'N')->get()->toArray();
	    $country1 = Country::select('country_code', 'name')->orderBy('name','ASC')->get()->toArray();
        $units = Unit::select('unit_id', 'description')->get()->toArray();
        $sub_agents = SubAgent::select('name')->get()->map(function ($subAgent) {
            return ['name' => $subAgent->name, 'name_duplicate' => $subAgent->name];
        })->toArray();
        $aims_users = Aimsuser::select('user_name', 'name')->get()->toArray();
        $cities = Cities::select('id', 'city_name')->get()->toArray();
        $client_types = DB::table('client_type')->select('code', 'description')->get()->toArray();
        $gender = Gender::select('gender_code', 'description')->get()->toArray();
        $postal_codes = postal_codes::select('postal_code', 'code_location')->get()->map(function ($postal_code) {
            $postal_code->code_location = "{$postal_code->postal_code} - {$postal_code->code_location}";
            return $postal_code;
          })->toArray();

        $beneficiary_relations = Beneficiary_relation::select('relation_code', 'relation_name')->get()->toArray();
        $org_roles = Organisation_role::select('role_id', 'role_description')->get()->toArray();
        $regions = Regions::select('region', 'region_name')->get()->toArray();
        $vendor_group = VendorGroup::select('vendor_group_code','vendor_group_name')->get()->toArray();
        //Individual type data
        $i_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                  ->where('client_type', ['I'])->get();
        //Corporate id types    
        $c_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['C'])
                                    ->get();

        //Parameterized Fields
        $PartnerConsentFieldParam = PartnerConsentFieldParam::where("partner_type","A")->get();
		$locationlevels = LocationLevel::select('level_code','level_name','level_type','load_method')->where('status','A')->get();
        $locations = Location::select('loc_code','description','loc_level')->where('active', 'Y')->where('loc_level',1)->get();

        $intermediaryfieldparam = IntermediaryFieldsParam::all();
        $bankFieldParams = PartnerBankFieldParam::where('holder', 'AGNT')->get();
        $contactFieldParams = PartnerContactFieldParam::where('holder', 'AGNT')->get();
        $attributetypes= IntermediaryAttributeType::all();

        $pipcnam = Pipcnam::first();
        $selfRelationCode = Beneficiary_relation::selfRelationCode;
        

        return view('gb.underwriting.intermediary_onboarding.agnt_form',[
            'save_method'=>'add',
            'pipcnam'=>$pipcnam,
            'PartnerConsentFieldParam'=>$PartnerConsentFieldParam,
            'company_modules'=>$company_modules,
            'bankFieldParams'=>$bankFieldParams,
            'contactFieldParams'=>$contactFieldParams,
            'intermediaryfieldparam'=>$intermediaryfieldparam,
            'attributetypes'=>$attributetypes,
            'payment_methods'=>$payment_methods,
            'banks'=>$banks,
            'acctype'=>$acctype,
            'client_types'=>$client_types,
            'branch'=>$branch,
            'country1'=>$country1,
            'units'=>$units,
            'sub_agents'=>$sub_agents,
            'aims_users'=>$aims_users,
            'currencies'=>$currencies,
            'locationlevels'=>$locationlevels,
            'locations'=>$locations,
            'selfRelationCode'=>$selfRelationCode,
            'org_roles'=>$org_roles,
            'beneficiary_relations'=>$beneficiary_relations,
            'regions'=>$regions,
            'i_idtypes'=>$i_idtypes,
            'c_idtypes'=>$c_idtypes,
            'vendor_group'=>$vendor_group,
            'gender'=>$gender,
            'postal_codes'=>$postal_codes,
                      
         ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function save_agnt(Request $request)
    {

        //Process TO save new intermediary

        //validate
        if(Gate::check('create-agency')){
            //Validation is done front end

            /****  Save Intermediary****/
            //Generate on boarding number
            $response=$this->generateOnboardingNumber('I');

            $newId='';

            if (isset($response['error'])) {
                return response()->json(['error' => $response['error']], 400);
            }
            $newId=$response;

            //Generate on boarding number
            try {
                $loggedInUser = Auth()->user()->user_name;
                $currentDateTime = date('Y-m-d H:i:s');

                $selfRelationCode = Beneficiary_relation::selfRelationCode;

                DB::beginTransaction();

                $intermediary = new Intermediary;

                $intermediary->intermediary_number = $newId;
                $intermediary->acc_type = $request->input('acc_type');
                $intermediary->intermediary_type = $request->input('intermediary_type');
                $intermediary->name = $request->name;
                $intermediary->first_name = $request->input('first_name');
                $intermediary->last_name = $request->input('last_name');
                $intermediary->other_name = $request->input('other_name');
                $intermediary->dob = $request->input('dob');
                $intermediary->country_code = $request->input('country_phone_code');
                $intermediary->cell_phone = $request->input('cell_phone');
                $intermediary->email = $request->input('email');
                $intermediary->gender = $request->input('gender');
                $intermediary->id_type = $request->input('id_type');
                $intermediary->identity_number = $request->input('identity_number');
                $intermediary->vrn_no = $request->input('vrn_no');
                $intermediary->pin_number = $request->input('pin_number');
                $intermediary->unit = $request->input('unit');
                $intermediary->old_account = $request->input('old_account');
                $intermediary->client_number = $request->input('client_number');
                $intermediary->appointed_date = $request->input('appointed_date');
                $intermediary->licence_number = $request->input('licence_number');
                $intermediary->licence_start_date = $request->input('licence_start_date');
                $intermediary->licence_expiry_date = $request->input('licence_expiry_date');
                $intermediary->subagent_code = $request->input('subagent_code');
                $intermediary->principal_officer = $request->input('principal_officer');
                $intermediary->political_exposed = $request->input('politicalexposed');
                $intermediary->aml_classification = $request->input('amlclassification');
                
                $intermediary->postal_code = $request->input('postal_code');
                $intermediary->postal_address = $request->input('postal_address');
                $intermediary->physical_address = $request->input('physical_address');
                $intermediary->status = IntermediaryAccountStatus::Active;
                $intermediary->vendor_group = $request->input('vendor_group');

                // Location fields
                $intermediary->location_1   = $request->input('level_1');
                $intermediary->location_2   = $request->input('level_2');
                $intermediary->location_3 = $request->input('level_3');
                $intermediary->location_4  = $request->input('level_4');
                $intermediary->location_5  = $request->input('level_5');
                $intermediary->location_6   = $request->input('level_6');

                // Location labels  
                $intermediary->loclabel_1   = $request->input('loclevel_1');
                $intermediary->loclabel_2   = $request->input('loclevel_2');
                $intermediary->loclabel_3 = $request->input('loclevel_3');
                $intermediary->loclabel_4  = $request->input('loclevel_4');
                $intermediary->loclabel_5  = $request->input('loclevel_5');
                $intermediary->loclabel_6   = $request->input('loclevel_6');

                

                // // Save the intermediary details
                $intermediary->save();

                if ($intermediary) {
                    $status_history=IntermediaryStatusHistory::create([
                        'intermediary_number' => $intermediary->intermediary_number,
                        'status' => $intermediary->status,
                        'reason' => 'Intermediary Activated on Account Creation',
                        'created_at' => now(), 
                        'created_by'=> trim(auth()->user()->user_name),
                    ]);
                }
                // Partner Consent fields
                $partnerConsents = [];

                $partnerConsentFieldParams = PartnerConsentFieldParam::where("partner_type", "A")->get();

                foreach ($partnerConsentFieldParams as $pc) {
                    $field = $pc->slug;

                    $latestId = PartnerConsent::max('id');
                    $newId = $latestId ? $latestId + 1 : 1;
                    
                    // Dynamically set the field value from the request
                    if ($request->has($field)) {
                        $partnerConsent = new PartnerConsent();
                        $partnerConsent->partner_number = $intermediary->intermediary_number;

                        $partnerConsent->id=$newId;
                        $partnerConsent->slug = $field;
                        $partnerConsent->value=$request->$field?$request->$field:'N';
                        $partnerConsent->holder='AGNT';
                        $partnerConsent->created_by = $loggedInUser;
                        $partnerConsent->updated_by = $loggedInUser;
                        $partnerConsent->created_at = $currentDateTime;
                        $partnerConsent->updated_at = $currentDateTime;
                        
                        // Save the PartnerConsent instance
                        $partnerConsent->save();
                    }
                    
                    $partnerConsents[] = $partnerConsent;
                }

                // SAVE ATTRIBUTE DETAILS

                foreach ($request->attribute_type as $key => $attribute_type) {
					if($request->attribute_type[$key] != null){
                        $latestId = IntermediaryAttribute::max('id');
                        $newId = $latestId ? $latestId + 1 : 1;
                    
						$intermediaryAttribute = new IntermediaryAttribute;
						
						$intermediaryAttribute->id=$newId;
                        $intermediaryAttribute->intermediary_number = $intermediary->intermediary_number;
                        $intermediaryAttribute->slug = $request->attribute_type[$key];
                        $intermediaryAttribute->value=$request->attribute_value[$key];
                        $intermediaryAttribute->created_by = $loggedInUser;
                        $intermediaryAttribute->updated_by = $loggedInUser;
                        $intermediaryAttribute->created_at = $currentDateTime;
                        $intermediaryAttribute->updated_at = $currentDateTime;

						$intermediaryAttribute->save();

					}
				}
                //Save Contact Details
                $beneficiaries = $request->contact_firstname;
                $relationship = $request->contact_relation_type;
                
                // When the option of self in contact type is set 
                if($relationship[0] == $selfRelationCode){
                    $beneficiary_details = new Beneficiary();
                    $beneficiary_details->client_number =  $intermediary->intermediary_number;
                    $beneficiary_details->first_name = $request->input('first_name');
                    $beneficiary_details->last_name =  $request->input('last_name');
                    $beneficiary_details->other_name = $request->input('other_name');
                    $beneficiary_details->e_mail = $request->input('email');
                    $beneficiary_details->full_name =  $request->name[0];
                    $beneficiary_details->positions = $relationship[0];
                    $beneficiary_details->org_role = null;
                    $beneficiary_details->telephone = $request->input('cell_phone');
                    $beneficiary_details->phy_loc=$request->input('postal_address');
                    $beneficiary_details->contact_type = 'C';

                    // Location fields
                    $beneficiary_details->location_1   = $request->input('level_1');
                    $beneficiary_details->location_2   = $request->input('level_2');
                    $beneficiary_details->location_3 = $request->input('level_3');
                    $beneficiary_details->location_4  = $request->input('level_4');
                    $beneficiary_details->location_5  = $request->input('level_5');
                    $beneficiary_details->location_6   = $request->input('level_6');

                    // Location labels  
                    $beneficiary_details->loclabel_1   = $request->input('loclevel_1');
                    $beneficiary_details->loclabel_2   = $request->input('loclevel_2');
                    $beneficiary_details->loclabel_3 = $request->input('loclevel_3');
                    $beneficiary_details->loclabel_4  = $request->input('loclevel_4');
                    $beneficiary_details->loclabel_5  = $request->input('loclevel_5');
                    $beneficiary_details->loclabel_6   = $request->input('loclevel_6');
                    $beneficiary_details->holder = 'AGNT';
                        
                    $beneficiary_details->save();
                }else{
                
                    foreach ($beneficiaries as $key => $moreben_firstname) {
                    
                        if($request->contact_firstname[$key] != null){
                                                                
                                $beneficiary_details = new Beneficiary();
                                $beneficiary_details->client_number =  $intermediary->intermediary_number;
                                $beneficiary_details->first_name = $request->contact_firstname[$key];
                                $beneficiary_details->last_name =  $request->contact_lastname[$key];
                                $beneficiary_details->other_name = $request->contact_othernames[$key];
                                $beneficiary_details->e_mail = $request->contact_email[$key];
                                $beneficiary_details->full_name =$request->contact_firstname[$key].' '.$request->contact_lastname[$key].' '.$request->contact_othernames[$key];
                                $beneficiary_details->positions =$request->contact_relation_type[$key];
                                $beneficiary_details->org_role = $request->contact_position[$key];
                                $beneficiary_details->telephone = $request->contact_telephone[$key];
                                $beneficiary_details->region = $request->contact_region[$key];
                                $beneficiary_details->district=$request->contact_district[$key];
                                $beneficiary_details->phy_loc=$request->contact_address[$key];
                                $beneficiary_details->political_exposed = $request->contact_politicalexposed[$key];
                                $beneficiary_details->aml_classification = $request->contact_amlclassification[$key];
                                $beneficiary_details->contact_type = 'C';
                                $beneficiary_details->holder = 'AGNT';
                                
                                // Location fields
                                $beneficiary_details->location_1   = $request->contact_level_1[$key];
                                $beneficiary_details->location_2   = $request->contact_level_2[$key];
                                $beneficiary_details->location_3 = $request->contact_level_3[$key];
                                $beneficiary_details->location_4  = $request->contact_level_4[$key];
                                $beneficiary_details->location_5  = $request->contact_level_5[$key];
                                $beneficiary_details->location_6   = $request->contact_level_6[$key];

                                // Location labels  
                                $beneficiary_details->loclabel_1   = $request->input('loclevel_1');
                                $beneficiary_details->loclabel_2   = $request->input('loclevel_2');
                                $beneficiary_details->loclabel_3 = $request->input('loclevel_3');
                                $beneficiary_details->loclabel_4  = $request->input('loclevel_4');
                                $beneficiary_details->loclabel_5  = $request->input('loclevel_5');
                                $beneficiary_details->loclabel_6   = $request->input('loclevel_6');
                                    
                                $beneficiary_details->save();
                        }
                        
                            
                    }       
                }
                //Save Bank Details
                foreach ($request->bank_code as $key => $bank_code) {
					if($request->bank_code[$key] != null){
						$intermediarybank = new PartnerBank;
						$intermediarybank->partner_number = $intermediary->intermediary_number; 
						$intermediarybank->item_no = $key + 1;
						$intermediarybank->payment_mode=$request->payment_mode[$key];
						$intermediarybank->bank_code=$request->bank_code[$key];
						$intermediarybank->branch_code=$request->bank_branch_code[$key];
						$intermediarybank->bank_account_no=trim($request->bank_account_no[$key]);
						$intermediarybank->bank_account=trim($request->bank_account_no[$key]);
						$intermediarybank->bank_account_name=$request->bank_account_name[$key];
						$intermediarybank->banking_phone_no=$request->bank_contact[$key];
						$intermediarybank->currency_type=$request->currency_type[$key];
						$intermediarybank->primary_account=$request->default_bank[$key];
						$intermediarybank->in_use = 'Y';
						$intermediarybank->holder = 'AGNT';
						$intermediarybank->save();
					}
				}

                //CREATE INTERMEDIARY BRANCHES
                foreach ($request->branches as $branch) {
                    $latestId = IntermediaryBranch::max('id');
                    $newBranchId = $latestId ? $latestId + 1 : 1;
                    
                    $newBranch=IntermediaryBranch::create([
                        'id' =>$newBranchId,
                        'intermediary_number' => $intermediary->intermediary_number,
                        'branch' => $branch,
                        'status' => $intermediary->status,
                        'created_by' =>trim(auth()->user()->user_name),
                        'updated_by'=>trim(auth()->user()->user_name),
                    ]);
                    if($newBranch && $intermediary){
                        IntermediaryBranchStatusHistory::create([
                            'intermediary_number' => $intermediary->intermediary_number,
                            'branch' => $branch,
                            'status' => $intermediary->status,
                            'reason' => 'Branch Activated on Intermediary Creation',
                            'created_at' => now(), 
                            'created_by'=> trim(auth()->user()->user_name),
                        ]);
                    }

                }

                $agnt_no=$intermediary->intermediary_number;
                $data=$this->generateAgentPartnerNo($request, $agnt_no);
                if($data['status']==2){
    
                    return response()->json(['data' => $data]);

                }

                

                DB::commit();
                
                //Mark onboarding number as allocated
                $this->updateAllocated('I',$intermediary->intermediary_number);

                $data = [
                    'status' => 1,
                    'title'=>'Intermediary Created',
                    'message'=>'Intermediary Created Successfully'
                ];
                return response()->json(['data' => $data]);
    

            } catch (\Throwable $th) {
                throw $th;
                DB::rollBack();

                return response()->json(['status' => 0 ]);

            }
            
            
            
        }
        else{
            Session::flash('error','You do not have Rights to Create an intermediary account');
            return redirect()->back();
        }

    }
    public function generateAgentPartnerNo($request, $agnt_no){
        // $id_number = $request->input('identity_number');

        $agent =  Intermediary::select('intermediary_type')->where('intermediary_number',$agnt_no)->first();
        $partner_type = $agent->intermediary_type;
        $partner_code = $agnt_no;
        $partner_group = 'agnt';

        $getpartner = new IntergrationController;

        $resp = $getpartner->getPartnerData($partner_group,$partner_type,$partner_code);

        if ($resp->statusCode == 200) {
            $data = [
                'status' => 1,
                'message' => 'Partner number for: '.$agnt_no.' generated successfully'
            ];


        }elseif($resp->statusCode == 400){
            $data = [
                'status' => 2,
                'message' => 'Status Code: '.$resp->statusCode . ' Partner number for: '.$agnt_no.' failed to generate. Please try again later.'
            ];
            
        }else{
            $data = [
                'status' => 2,
                'message' => 'Partner number for: '.$agnt_no.' failed to generate. Please try again later.'
            ];
            
        }
        return $data;
        
    }

    public function agnt_list(Request $request){

        if ($request->ajax()) {
            $data = Intermediary::select('intermediary_number', 'name', 'acc_type', 'status');
    
            return DataTables::of($data)
                    ->addColumn('action', function($data) {
                        $btn = '';

                        $btn  = '<a style=" href="#" id="view-agnt" data-id="' . $data->intermediary_number . '" class="btn btn-xs btn-default"><i class="fa fa-eye"></i> View</a>';
                    
                        $btn .= '&nbsp;&nbsp';
                        
                        $btn .= '<a style=" href="#" id="edit-agnt" data-id="' . $data->intermediary_number . '" class="btn btn-xs btn-primary"><i class="fa fa-pencil-square-o"></i> Edit</a>';
                        
                        $btn .= '&nbsp;&nbsp';
                       
                        $btn .= '<a style=" href="#" id="update-agnt-status" data-id="' . $data->intermediary_number . '" class="btn btn-xs btn-default"><i class="fa fa-pencil-square-o"></i> Update Status</a>';
                    
                        $btn .= '&nbsp;&nbsp';

                        $btn .= '<a href="#" class="documents btn btn-xs btn-default" data-intermediary_number="'.$data->intermediary_number.'"><i class="fa fa-plus"></i> documents</a>';

                        $btn .= '&nbsp;&nbsp';

                        return $btn;
                    })
                    ->editColumn('acc_type', function($data) {
                        return $data->acctype ? $data->acctype->description : '--';
                    })
                    ->editColumn('status', function($data) {
                        $partner_status = PartnerStatus::where('slug',$data->status)->first();
                        return $partner_status ? $partner_status->description: '--';
                    })
                    ->make(true);
        }
        
        $statuses = PartnerStatus::all();

        $dat=array(
            'main' => 'Underwriting', 
            'module' => 'Intermediary'
            );

        return view('gb.underwriting.intermediary_onboarding.agnt_list',compact('statuses'))->with('dat',$dat);
    }
    public function update_agnt(Request $request){
        if(Gate::check('amend-agency')){ 

            try {
                DB::beginTransaction();
                $loggedInUser = Auth()->user()->user_name;
                $currentDateTime = date('Y-m-d H:i:s');

                $intermediary_number = $request->intermediary_number;
                $agent_old = Intermediary::where('intermediary_number', $request->input('intermediary_number'))->first();

                $agent = Intermediary::where('intermediary_number', $request->input('intermediary_number'))
                    ->update([
                        'name' => $request->input('name'),
                        'first_name' => $request->input('first_name'),
                        'last_name' => $request->input('last_name'),
                        'other_name' => $request->input('other_name'),
                        'dob' => $request->input('dob'),
                        'country_code' => $request->input('country_phone_code'),
                        'cell_phone' => $request->input('cell_phone'),
                        'email' => $request->input('email'),
                        'gender' => $request->input('gender'),
                        'id_type' => $request->input('id_type'),
                        'identity_number' => $request->input('identity_number'),
                        'vrn_no' => $request->input('vrn_no'),
                        'pin_number' => $request->input('pin_number'),
                        'unit' => $request->input('unit'),
                        'old_account' => $request->input('old_account'),
                        'client_number' => $request->input('client_number'),
                        'appointed_date' => $request->input('appointed_date'),
                        'licence_number' => $request->input('licence_number'),
                        'licence_start_date' => $request->input('licence_start_date'),
                        'licence_expiry_date' => $request->input('licence_expiry_date'),
                        'subagent_code' => $request->input('subagent_code'),
                        'principal_officer' => $request->input('principal_officer'),
                        'political_exposed'=> $request->input('politicalexposed'),
                        'aml_classification'=> $request->input('amlclassification'),
                        'postal_code' => $request->input('postal_code'),
                        'postal_address' => $request->input('postal_address'),
                        'physical_address' => $request->input('physical_address'),
                        'vendor_group' => $request->input('vendor_group'),
                        'location_1' => $request->input('level_1'),
                        'location_2' => $request->input('level_2'),
                        'location_3' => $request->input('level_3'),
                        'location_4' => $request->input('level_4'),
                        'location_5' => $request->input('level_5'),
                        'location_6' => $request->input('level_6'),
                        'loclabel_1' => $request->input('loclevel_1'),
                        'loclabel_2' => $request->input('loclevel_2'),
                        'loclabel_3' => $request->input('loclevel_3'),
                        'loclabel_4' => $request->input('loclevel_4'),
                        'loclabel_5' => $request->input('loclevel_5'),
                        'loclabel_6' => $request->input('loclevel_6'),
                    ]);
                    
                // SAVE ATTRIBUTE DETAILS
                $existingAttributes = IntermediaryAttribute::where('intermediary_number', $request->intermediary_number)->get();

                // Iterate through the attributes in the request
                foreach ($request->attribute_type as $key => $attribute_type) {
                    if ($attribute_type != null) {
                        // Check if the attribute exists in the database
                        $intermediaryAttribute = $existingAttributes->firstWhere('slug', $attribute_type);
                
                        if ($intermediaryAttribute) {
                            // Update the existing attribute
                            $intermediaryAttribute->value = $request->attribute_value[$key];
                            $intermediaryAttribute->updated_by = $loggedInUser;
                            $intermediaryAttribute->updated_at = $currentDateTime;
                        } else {
                            // Create a new attribute
                            $latestId = IntermediaryAttribute::max('id');
                            $newId = $latestId ? $latestId + 1 : 1;
                
                            $intermediaryAttribute = new IntermediaryAttribute;
                            $intermediaryAttribute->id = $newId;
                            $intermediaryAttribute->intermediary_number = $request->intermediary_number;
                            $intermediaryAttribute->slug = $attribute_type;
                            $intermediaryAttribute->value = $request->attribute_value[$key];
                            $intermediaryAttribute->created_by = $loggedInUser;
                            $intermediaryAttribute->updated_by = $loggedInUser;
                            $intermediaryAttribute->created_at = $currentDateTime;
                            $intermediaryAttribute->updated_at = $currentDateTime;
                        }
                
                        $intermediaryAttribute->save();
                    }
                }
                
                // Handle removed attributes
                $existingSlugs = $existingAttributes->pluck('slug')->toArray();
                $requestSlugs = $request->attribute_type;
                $slugsToRemove = array_diff($existingSlugs, $requestSlugs);
                
                if (!empty($slugsToRemove)) {
                    IntermediaryAttribute::where('intermediary_number', $request->intermediary_number)
                        ->whereIn('slug', $slugsToRemove)
                        ->delete();
                }
                // END OF SAVE ATTRIBUTE DETAILS

                $agent_updated = Intermediary::where('intermediary_number', $request->input('intermediary_number'))->first();

                $changed_data = $agent_updated->getAttributes();
                $process_slug = 'intermediary-onboarding';
                $activity_slug = 'update';
                $unique_item = $intermediary_number;
                $old_data = $agent_old;
                $ip =$request->ip();

                log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

                $partnerConsentFieldParams = PartnerConsentFieldParam::where("partner_type", "A")->get();

                foreach ($partnerConsentFieldParams as $pc) {
                    $field = $pc->slug;

                    $partnerConsent = PartnerConsent::where('partner_number', $request->intermediary_number)
                        ->where('slug',$field)
                        ->where('holder','AGNT')
                        ->first();

                    if ($partnerConsent) {
                        // Check if the field exists in the request and update it
                        if ($request->has($field)) {
                            $partnerConsent->value=$request->$field;
                            $partnerConsent->updated_by = $loggedInUser;
                            $partnerConsent->updated_at = $currentDateTime;
                            $partnerConsent->save();
                        }
                    }else {
                        $latestId = PartnerConsent::max('id');
                         $newId = $latestId ? $latestId + 1 : 1;

                        // Dynamically set the field value from the request
                        if ($request->has($field)) {
                            $partnerConsent = new PartnerConsent();
                            $partnerConsent->partner_number = $request->intermediary_number;

                        
                            $partnerConsent->id=$newId;
                            $partnerConsent->slug = $field;
                            $partnerConsent->value=$request->$field;
                            $partnerConsent->holder='AGNT';
                            $partnerConsent->created_by = $loggedInUser;
                            $partnerConsent->updated_by = $loggedInUser;
                            $partnerConsent->created_at = $currentDateTime;
                            $partnerConsent->updated_at = $currentDateTime;
                        
                            // Save the PartnerConsent instance
                            $partnerConsent->save();
                        }
                    }
                }
                
                DB::commit();

                $data = [
                    'status' => 1,
                    'title'=>'Intermediary Updated',
                    'message'=>'Intermediary Updated Successfully'
                ];
                return response()->json(['data' => $data]);
            } catch (\Throwable $th) {
                throw $th;
                DB::rollBack();
            }
                   
        }
        else{
            Session::flash('error','You do not have Rights to Update an intermediary account');
            return redirect()->back();
        }
    }

    public function edit_agnt(Request $request)
    {
        $agnt = Intermediary::where('intermediary_number',$request->agnt_no)->first();
        
        //Select field values
        $payment_methods = PaymentMode::select('paymode_code', 'paymode_name')->get()->toArray();
        $company_modules = CompanyModule::select('company_module_code', 'company_module_name')->get()->toArray();
        $banks=Olbnknames::select('bank_code', 'description')->get()->toArray();
        $currencies = Currency::select('currency_code', 'currency')->get()->toArray();
        $acctype = Acctype::select('acc_type', 'description')
            ->where('TREATY_FAC_ACCOUNT', 'N')
            ->get()
            ->map(function ($actyp) {
                $actyp->description = "{$actyp->acc_type} - {$actyp->description}";
                return $actyp;
            })->toArray();
        $branch = Branch::select('branch', 'description')->where('TREATY_FAC_BRANCH', 'N')->get()->toArray();
        $country1 = Country::select('country_code', 'name')->orderBy('name','ASC')->get()->toArray();
        $units = Unit::select('unit_id', 'description')->get()->toArray();
        $sub_agents = SubAgent::select('name')->get()->map(function ($subAgent) {
            return ['name' => $subAgent->name, 'name_duplicate' => $subAgent->name];
        })->toArray();
        $aims_users = Aimsuser::select('user_name', 'name')->get()->toArray();
        $cities = Cities::select('id', 'city_name')->get()->toArray();
        $client_types = DB::table('client_type')->select('code', 'description')->get()->toArray();
        $gender = Gender::select('gender_code', 'description')->get()->toArray();
        $postal_codes = postal_codes::select('postal_code', 'code_location')->get()->map(function ($postal_code) {
            $postal_code->code_location = "{$postal_code->postal_code} - {$postal_code->code_location}";
            return $postal_code;
        })->toArray();

        $beneficiary_relations = Beneficiary_relation::select('relation_code', 'relation_name')->get()->toArray();
        $org_roles = Organisation_role::select('role_id', 'role_description')->get()->toArray();
        $regions = Regions::select('region', 'region_name')->get()->toArray();
        $vendor_group = VendorGroup::select('vendor_group_code','vendor_group_name')->get()->toArray();
        //Individual type data
        $i_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                ->where('client_type', ['I'])->get();
        //Corporate id types    
        $c_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['C'])
                                    ->get();

        //Parameterized Fields
        $PartnerConsentFieldParam = PartnerConsentFieldParam::where("partner_type","A")->get();
        $locationlevels = LocationLevel::select('level_code','level_name','level_type','load_method')->where('status','A')->get();
        $locations = Location::select('loc_code','description','loc_level')->where('active', 'Y')->where('loc_level',1)->get();

        $intermediaryfieldparam = IntermediaryFieldsParam::select('slug','mandatory','disabled','name','country','intermediary_type','input_type','category')
            ->get();
        $bankFieldParams = PartnerBankFieldParam::where('holder', 'AGNT')->get();
        $contactFieldParams = PartnerContactFieldParam::where('holder', 'AGNT')->get();

        $pipcnam = Pipcnam::first();
        $selfRelationCode = Beneficiary_relation::selfRelationCode;

        //Partner consent field data
        $partnerConsentData = PartnerConsent::where('partner_number',$request->agnt_no)
                        ->where('holder','AGNT') 
                        ->get();

        $attributetypes= IntermediaryAttributeType::where('disabled','N')->get();
        $attributeData= IntermediaryAttribute::where('intermediary_number',$request->agnt_no)->get();
		
		return view('gb.underwriting.intermediary_onboarding.agnt_form',[
            'save_method'=>'update',
            'agnt'=>$agnt,
            'pipcnam'=>$pipcnam,
            'attributetypes'=>$attributetypes,
            'attributeData'=>$attributeData,
            'PartnerConsentFieldParam'=>$PartnerConsentFieldParam,
            'company_modules'=>$company_modules,
            'payment_methods'=>$payment_methods,
            'bankFieldParams'=>$bankFieldParams,
            'contactFieldParams'=>$contactFieldParams,
            'intermediaryfieldparam'=>$intermediaryfieldparam,
            'banks'=>$banks,
            'acctype'=>$acctype,
            'client_types'=>$client_types,
            'branch'=>$branch,
            'country1'=>$country1,
            'units'=>$units,
            'sub_agents'=>$sub_agents,
            'aims_users'=>$aims_users,
            'currencies'=>$currencies,
            'locationlevels'=>$locationlevels,
            'locations'=>$locations,
            'selfRelationCode'=>$selfRelationCode,
            'org_roles'=>$org_roles,
            'beneficiary_relations'=>$beneficiary_relations,
            'regions'=>$regions,
            'i_idtypes'=>$i_idtypes,
            'c_idtypes'=>$c_idtypes,
            'vendor_group'=>$vendor_group,
            'gender'=>$gender,
            'postal_codes'=>$postal_codes,
            'partnerConsentData'=>$partnerConsentData,
                      
         ]);
        
    }
    public function agnt_bank_accounts(Request $request)
	{
		$agntaccounts = PartnerBank::where('partner_number', $request->agnt_code)->get();
        $saveMethod=$request->saveMethod;

			return Datatables::of($agntaccounts)
			->editColumn('bank_code', function ($agntaccount) {

			$bank = Olbnknames::whereRaw("trim(bank_code)='".$agntaccount->bank_code."'")->first();
			return trim($bank->bank_code).'-'.$bank->description;

			})
			->editColumn('branch_code', function ($agntaccount) {

			$branch = Olbranch::whereRaw("trim(bank_code)='".$agntaccount->bank_code."'")->whereRaw("trim(branch_code)='".$agntaccount->branch_code."'")->first();
			return trim($branch->branch_code).'-'.$branch->branch;

			})
			->editColumn('currency_type', function ($agntaccount) {

				$currency = Currency::where('currency_code',$agntaccount->currency_type)->first();
				return trim($currency->description);

			})
			->editColumn('in_use', function ($agntaccount) {
				if($agntaccount->in_use == "Y"){
					return "ACTIVE";
				}elseif($agntaccount->in_use == "N"){
					return "INACTIVE";
				}else{
					return "N/A";
				}

			})
			->addColumn('action', function ($agntaccount) use($saveMethod) {
				$icon = $saveMethod === 'view' 
                    ? '<i class="glyphicon glyphicon-eye-open"></i>' // Eye icon for view
                    : '<i class="glyphicon glyphicon-edit"></i>';   // Edit icon for edit

                    return '<span data-toggle="modal" id="edit_agnt_bank" data-itemno="' . $agntaccount->item_no . '">' . $icon . '</span>';

			})

			->make(true);
		

	}
    public function getaccountdetails(Request $request)
	{
		$agntbank =  PartnerBank::where('partner_number',trim($request->client_number))->where('item_no',trim($request->itemno))->where('holder',trim($request->holder))->first();
		
        return [
			'agntbank'=> $agntbank
		];
	}

    public function update_agnt_bank(Request $request){
        
        try {
            DB::beginTransaction();
            $intermediary_number = $request->intermediary_number;
            $agntbank_old = PartnerBank::where('partner_number',$request->intermediary_number)->where('item_no', $request->item_no)->first();
            $agntbank_count = PartnerBank::where('partner_number',$request->intermediary_number)->count();
            if($request->item_no == null){
                //ADD BANK
                $agntbank_count = PartnerBank::where('partner_number',$request->intermediary_number)->max('item_no');

                if($request->bank_code != null){
                    $agntbank = new PartnerBank;
                    $agntbank->partner_number = $request->intermediary_number; 
                    $agntbank->item_no = $agntbank_count + 1;
                    $agntbank->payment_mode=$request->payment_mode;
                    $agntbank->bank_code=$request->bank_code;
                    $agntbank->branch_code=$request->bank_branch_code;
                    $agntbank->bank_account_no=trim($request->bank_account_no);
                    $agntbank->bank_account=trim($request->bank_account_no);
                    $agntbank->bank_account_name=$request->bank_account_name;
                    $agntbank->currency_type=$request->currency_type;
					$agntbank->banking_phone_no=$request->bank_contact;

                    $agntbank->in_use = 'Y';
                    $agntbank->holder = 'AGNT';
                    $agntbank->save();
                }

                if($request->primary == 'Y' && $agntbank){

                    // Update the primary account column
                    PartnerBank::where('partner_number', $request->intermediary_number)
                        ->where('item_no', $agntbank->item_no)
                        ->update(['primary_account' => 'Y']);
                
                    // Set the primary_account to 'N' for all other banks of the same claimant code
                    PartnerBank::where('partner_number', $request->intermediary_number)
                        ->where('item_no', '!=', $agntbank->item_no)
                        ->update(['primary_account' => 'N']);
                    
                }
                DB::commit();

                $changed_data = $agntbank->getAttributes();
                $process_slug = 'intermediary-onboarding';
                $activity_slug = 'update';
                $unique_item = $intermediary_number;
                $old_data = $agntbank_old;
                $ip =$request->ip();

                log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);


                if ($agntbank->save()) {
                    return response()->json(['success' => true, 'message' => 'Account number '.$request->bank_account_no. ' has been added']);
                    
                }else{
                    return response()->json(['error' => true, 'message' => 'Error occured while adding extra account number '.$request->bank_account_no]);
        
                }
            }
            else{
                //UPDATE BANK
                if($agntbank_count == 1 || $request->primary == 'Y'){
                    // Update the primary account column
                    PartnerBank::where('partner_number', $request->intermediary_number)
                        ->where('item_no', $request->item_no)
                        ->update(['primary_account' => 'Y']);
                
                    // Set the primary_account to 'N' for all other banks of the same claimant code
                    PartnerBank::where('partner_number', $request->intermediary_number)
                        ->where('item_no', '!=', $request->item_no)
                        ->update(['primary_account' => 'N']);

                }
                //update in client bank account details
                $agntbank = PartnerBank::where('partner_number',$request->intermediary_number)->where('item_no', $request->item_no)->update([
                    'payment_mode' => $request->payment_mode,
                    'bank_code' => $request->bank_code,
                    'branch_code' => $request->bank_branch_code,
                    'bank_account_no' => $request->bank_account_no,
                    'bank_account' => $request->bank_account_no,
                    'bank_account_name' => $request->bank_account_name,
                    'in_use' => $request->in_use,
                    'currency_type' => $request->currency_type,
                    'banking_phone_no'=>$request->bank_contact,
                ]);
                DB::commit();

                $agntbank_updated = PartnerBank::where('partner_number',$request->intermediary_number)->where('item_no', $request->item_no)->first();

                $changed_data = $agntbank_updated->getAttributes();
                $process_slug = 'intermediary-onboarding';
                $activity_slug = 'update';
                $unique_item = $intermediary_number;
                $old_data = $agntbank_old;
                $ip =$request->ip();

                log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);
                
                if ($agntbank) {
                    return response()->json(['success' => true, 'message' => 'Account has been updated to ' . $request->bank_account_no]);
                    
                }else{
                    return response()->json(['error' => true, 'message' => 'Error occured while updating account number ' . $request->bank_account_no]);

                }
            }

        } catch (\Throwable $th) {
            //throw $th;
            DB::rollBack();
        }
    }

    public function update_rein_bank(Request $request){
        
        try {
            DB::beginTransaction();
            $intermediary_number = $request->intermediary_number;
            $agntbank_old = PartnerBank::where('partner_number',$request->intermediary_number)->where('item_no', $request->item_no)->first();
            $agntbank_count = PartnerBank::where('partner_number',$request->intermediary_number)->count();
            if($request->item_no == null){
                //ADD BANK
                $agntbank_count = PartnerBank::where('partner_number',$request->intermediary_number)->max('item_no');

                if($request->bank_code != null){
                    $agntbank = new PartnerBank;
                    $agntbank->partner_number = $request->intermediary_number; 
                    $agntbank->item_no = $agntbank_count + 1;
                    $agntbank->payment_mode=$request->payment_mode;
                    $agntbank->bank_code=$request->bank_code;
                    $agntbank->branch_code=$request->bank_branch_code;
                    $agntbank->bank_account_no=trim($request->bank_account_no);
                    $agntbank->bank_account=trim($request->bank_account_no);
                    $agntbank->bank_account_name=$request->bank_account_name;
                    $agntbank->currency_type=$request->currency_type;
					$agntbank->banking_phone_no=$request->bank_contact;

                    $agntbank->in_use = 'Y';
                    $agntbank->holder = 'REIN';
                    $agntbank->save();
                }

                if($request->primary == 'Y' && $agntbank){

                    // Update the primary account column
                    PartnerBank::where('partner_number', $request->intermediary_number)
                        ->where('item_no', $agntbank->item_no)
                        ->update(['primary_account' => 'Y']);
                
                    // Set the primary_account to 'N' for all other banks of the same claimant code
                    PartnerBank::where('partner_number', $request->intermediary_number)
                        ->where('item_no', '!=', $agntbank->item_no)
                        ->update(['primary_account' => 'N']);
                    
                }
                DB::commit();

                $changed_data = $agntbank->getAttributes();
                $process_slug = 'intermediary-onboarding';
                $activity_slug = 'update';
                $unique_item = $intermediary_number;
                $old_data = $agntbank_old;
                $ip =$request->ip();

                log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);


                if ($agntbank->save()) {
                    return response()->json(['success' => true, 'message' => 'Account number '.$request->bank_account_no. ' has been added']);
                    
                }else{
                    return response()->json(['error' => true, 'message' => 'Error occured while adding extra account number '.$request->bank_account_no]);
        
                }
            }
            else{
                //UPDATE BANK
                if($agntbank_count == 1 || $request->primary == 'Y'){
                    // Update the primary account column
                    PartnerBank::where('partner_number', $request->intermediary_number)
                        ->where('item_no', $request->item_no)
                        ->update(['primary_account' => 'Y']);
                
                    // Set the primary_account to 'N' for all other banks of the same claimant code
                    PartnerBank::where('partner_number', $request->intermediary_number)
                        ->where('item_no', '!=', $request->item_no)
                        ->update(['primary_account' => 'N']);

                }
                //update in client bank account details
                $agntbank = PartnerBank::where('partner_number',$request->intermediary_number)->where('item_no', $request->item_no)->update([
                    'payment_mode' => $request->payment_mode,
                    'bank_code' => $request->bank_code,
                    'branch_code' => $request->bank_branch_code,
                    'bank_account_no' => $request->bank_account_no,
                    'bank_account' => $request->bank_account_no,
                    'bank_account_name' => $request->bank_account_name,
                    'in_use' => $request->in_use,
                    'currency_type' => $request->currency_type,
                    'banking_phone_no'=>$request->bank_contact,
                ]);
                DB::commit();

                $agntbank_updated = PartnerBank::where('partner_number',$request->intermediary_number)->where('item_no', $request->item_no)->first();

                $changed_data = $agntbank_updated->getAttributes();
                $process_slug = 'intermediary-onboarding';
                $activity_slug = 'update';
                $unique_item = $intermediary_number;
                $old_data = $agntbank_old;
                $ip =$request->ip();

                log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);
                
                if ($agntbank) {
                    return response()->json(['success' => true, 'message' => 'Account has been updated to ' . $request->bank_account_no]);
                    
                }else{
                    return response()->json(['error' => true, 'message' => 'Error occured while updating account number ' . $request->bank_account_no]);

                }
            }

        } catch (\Throwable $th) {
            //throw $th;
            DB::rollBack();
        }
    }

    // Contact Datatable
    public function agnt_contacts(Request $request)
	{
            $agnt = Intermediary::where('intermediary_number',$request->agnt_code)->first();
            $agntcontacts = Beneficiary::where('client_number', $request->agnt_code)
                            ->where('contact_type','C')
                            ->get();
            $saveMethod=$request->saveMethod;

			return Datatables::of($agntcontacts)
                ->editColumn('region', function ($contact) {
                    if(($contact->region != "")){
                        $region = Regions::where('region',$contact->region)->first();
                        return $region->region_name;
                    }else{
                        return "N/A";
                    }
    
                })
                ->editColumn('district', function ($contact) {
                    if(($contact->district != "")){
                        $district = District::where('district_code',$contact->district)->first();
                        return $district->district_name;
                    }else{
                        return "--";
                    }
    
                })
                ->editColumn('e_mail', function ($contact) {
                    return $contact->e_mail?$contact->e_mail:'--';
    
                })
                ->editColumn('relationship', function ($contact)use ($agnt) {

                    if ($agnt->intermediary_type == 'C') {
                        if(($contact->org_role != "")){
                            $relationship = Organisation_role::where('role_id',$contact->org_role)->first();
                            return $relationship->role_description;
                        }else{
                            return "---";
                        }
                    } else if($agnt->intermediary_type == 'I'){
                        if(($contact->positions != "")){
                            $relationship = Beneficiary_relation::where('relation_code',$contact->positions)->first();
                            return $relationship->relation_name;
                        }else{
                            return "---";
                        }
                    }else{
                        return'---';
    
                    }
                    
                })
                ->addColumn('action', function ($contact) use($saveMethod){
                    $icon = $saveMethod === 'view' 
                        ? '<i class="glyphicon glyphicon-eye-open"></i>' // Eye icon for view
                        : '<i class="glyphicon glyphicon-edit"></i>';   // Edit icon for edit

                    return '<span data-toggle="modal" id="edit_agnt_contact" data-contact_id="' . $contact->beneficiary_id . '">' . $icon . '</span>';

                })
                
			->make(true);
		

	}

    public function update_agnt_contact(Request $request){
        
        try {
            DB::beginTransaction();
            $agnt_no = $request->intermediary_number;
            $contact_id= $request->contact_id;

            $beneficiary_old = Beneficiary::where('beneficiary_id', trim($contact_id))->first();
            if($agnt_no) {

                if ($contact_id == null) {
                    $beneficiary_details = new Beneficiary;
                    $beneficiary_details->client_number =  $agnt_no;
                    $beneficiary_details->first_name = $request->contact_firstname;
                    $beneficiary_details->last_name =  $request->contact_lastname;
                    $beneficiary_details->other_name = $request->contact_othernames;
                    $beneficiary_details->full_name =$request->contact_firstname.' '.$request->contact_lastname.' '.$request->contact_othernames;
                    $beneficiary_details->positions =$request->contact_relation_type;
                    $beneficiary_details->org_role = $request->contact_position;
                    $beneficiary_details->telephone = $request->contact_telephone;
                    $beneficiary_details->e_mail = $request->contact_email;
                    $beneficiary_details->region = $request->contact_region;
                    $beneficiary_details->district=$request->contact_district;
                    $beneficiary_details->phy_loc=$request->contact_address;
                    $beneficiary_details->political_exposed = $request->contact_politicalexposed;
                    $beneficiary_details->aml_classification = $request->contact_amlclassification;
                    $beneficiary_details->contact_type = $request->contact_type;

                    // Location fields
                    $beneficiary_details->location_1   = $request->contact_level_1[0];
                    $beneficiary_details->location_2   = $request->contact_level_2[0];
                    $beneficiary_details->location_3 = $request->contact_level_3[0];
                    $beneficiary_details->location_4  = $request->contact_level_4[0];
                    $beneficiary_details->location_5  = $request->contact_level_5[0];
                    $beneficiary_details->location_6   = $request->contact_level_6[0];

                    // Location labels  
                    $beneficiary_details->loclabel_1   = $request->input('loclevel_1');
                    $beneficiary_details->loclabel_2   = $request->input('loclevel_2');
                    $beneficiary_details->loclabel_3 = $request->input('loclevel_3');
                    $beneficiary_details->loclabel_4  = $request->input('loclevel_4');
                    $beneficiary_details->loclabel_5  = $request->input('loclevel_5');
                    $beneficiary_details->loclabel_6   = $request->input('loclevel_6');
                    
                    $beneficiary_details->save();
                    DB::commit();

                    $changed_data = $beneficiary_details->getAttributes();
                    $process_slug = 'intermediary-onboarding';
                    $activity_slug = 'update';
                    $unique_item = $agnt_no;
                    $old_data = $beneficiary_old;
                    $ip =$request->ip();

                    log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);
            
                    return response()->json(['success' => true, 'message' => 'Contact '.$beneficiary_details->full_name. ' has been added']);
                    
                }else{
                    $update_beneficiary = Beneficiary::where('beneficiary_id', trim($contact_id))
                    ->update([
                        'first_name' => $request->contact_firstname,
                        'last_name' => $request->contact_lastname,
                        'other_name' => $request->contact_othernames,
                        'full_name' => $request->contact_firstname . ' ' . $request->contact_lastname . ' ' . $request->contact_othernames,
                        'positions' => $request->contact_relation_type,
                        'org_role' => $request->contact_position,
                        'telephone' => $request->contact_telephone,
                        'region' => $request->contact_region,
                        'district' => $request->contact_district,
                        'phy_loc' => $request->contact_address,
                        'e_mail' => $request->contact_email,
                        'political_exposed' => $request->contact_politicalexposed,
                        'aml_classification' => $request->contact_amlclassification,
                        'contact_type' => $request->contact_type,
                        'location_1' => $request->contact_level_1[0],
                        'location_2' => $request->contact_level_2[0],
                        'location_3' => $request->contact_level_3[0],
                        'location_4' => $request->contact_level_4[0],
                        'location_5' => $request->contact_level_5[0],
                        'location_6' => $request->contact_level_6[0],
                        'loclabel_1' => $request->input('loclevel_1'),
                        'loclabel_2' => $request->input('loclevel_2'),
                        'loclabel_3' => $request->input('loclevel_3'),
                        'loclabel_4' => $request->input('loclevel_4'),
                        'loclabel_5' => $request->input('loclevel_5'),
                        'loclabel_6' => $request->input('loclevel_6'),
                    ]);
                    DB::commit();

                    $beneficiary_updated = Beneficiary::where('beneficiary_id', trim($contact_id))->first();

                    $changed_data = $beneficiary_updated->getAttributes();
                    $process_slug = 'intermediary-onboarding';
                    $activity_slug = 'update';
                    $unique_item = $agnt_no;
                    $old_data = $beneficiary_old;
                    $ip =$request->ip();

                    log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);
        
                    return response()->json(['success' => true, 'message' => 'Contact '.$beneficiary_details->full_name. ' has been edited']);
                    
                }
                
            }else{
                
                return response()->json(['error' => true, 'message' => 'Error occured while adding extra contact '.$beneficiary_details->full_name]);

            }

        } catch (\Throwable $th) {
            //throw $th;
            DB::rollBack();
        }

    }

    public function get_agnt_contact(Request $request){

        $contact_id = $request->contact_id;
                
        if ($contact_id) {

            $contact_rec = Beneficiary::where('beneficiary_id',trim($contact_id))->first();
            
            return response()->json(['status'=> 1, 'data'=>$contact_rec]);

        }else{
            return response()->json(['status'=> 0,'message'=> "Unprocessed entity"]);
        }

    }
    //Branch Datatable

    public function agnt_branches(Request $request)
	{
        
            $agnt = Intermediary::where('intermediary_number',$request->agnt_code)->first();
            $agntbranches = IntermediaryBranch::where('intermediary_number', $request->agnt_code)
                            ->get();
            $saveMethod=$request->saveMethod;

            //branch, branch name, agent no, agent name,, account type, status, action
			return Datatables::of($agntbranches)
                ->addColumn('name', function ($data) {
                    
                    return $data->name ;
                })
                ->editColumn('branch_name', function ($data) {
                    $branch = Branch::where('branch',$data->branch)->first();
                    return $branch->description? $branch->description:'--';
                })
                ->editColumn('status', function($data) {
                    $partner_status = PartnerStatus::where('slug',$data->status)->first();
                    return $partner_status ? $partner_status->description: '--';
                })
                ->addColumn('action', function ($data) use($saveMethod) {
                    $btn = ''; 

                    $icon = $saveMethod === 'view' 
                        ? '<i class="glyphicon glyphicon-eye-open"></i>'
                        : '<i class="glyphicon glyphicon-edit"></i>'; 

                    $btn .= '<span data-toggle="modal" id="edit_agnt_branch" data-branch_id="' . $data->branch . '" data-agent_id="' . $data->intermediary_number . '">' . $icon . '</span>';

                    $btn .= '
                    <div class="dropdown" style="display:inline-block; margin-left: 5px;">
                        <button class="btn-icon btn btn-ghost btn-sm rounded-circle dropdown-toggle" type="button" id="dropdownMenuButton' . $data->branch . '" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton' . $data->branch . '">';

                    if ($saveMethod === 'update') {
                        $btn .= '<li><a href="#" id="update-agnt-status" data-id="' . $data->intermediary_number . '" data-branch_id="' . $data->branch . '" class="dropdown-item">
                                    <i class="fa fa-pencil-square-o"></i> Update Status
                                </a></li>';
                    }

                    $btn .= '<li><a href="#" id="view-status-history" data-id="' . $data->intermediary_number . '" data-branch_id="' . $data->branch . '" 
                            data-toggle="modal" data-target="#branchStatusHistoryModal" class="dropdown-item">
                                <i class="fa fa-history"></i>Status History
                            </a></li>';

                    $btn .= '</ul>
                    </div>';

                    return $btn;
                })
			->make(true);
		

	}

    public function update_agnt_branch(SaveAgntBranchRequest $request){
        try {
            DB::beginTransaction();
            $intermediary_branch_id = $request->intermediary_branch_id;
            $intermediary_number = $request->intermediary_number;
            $new_branch=$request->branch;

            $intemediary_old = IntermediaryBranch::where('id', $intermediary_branch_id)
                        ->where('intermediary_number',$intermediary_number)->first();
            if($intermediary_number) {

                //get intermediary
                $intermediary= Intermediary::where('intermediary_number', $intermediary_number)->first();

                if($intermediary && $intermediary_branch_id ===null){
                    //Create new intermediary branch
                    $latestId = IntermediaryBranch::max('id');
                    $newBranchId = $latestId ? $latestId + 1 : 1;
                    
                    $intemediary_updated = IntermediaryBranch::create([
                        'id' =>$newBranchId,
                        'intermediary_number' => $intermediary->intermediary_number,
                        'branch' => $new_branch,
                        'status' => $intermediary->status,
                        'created_by' =>trim(auth()->user()->user_name),
                        'updated_by'=>trim(auth()->user()->user_name),
                    ]);

                    DB::commit();

                    $changed_data = $intemediary_updated->getAttributes();
                    $process_slug = 'intermediary-onboarding';
                    $activity_slug = 'update';
                    $unique_item = $intermediary_number;
                    $old_data = $intemediary_old;
                    $ip =$request->ip();

                    log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

                    $data = [
                        'status' => 1,
                        'title'=>'Branch Added',
                        'message'=>'Branch Added Successfully'
                    ];
                    return response()->json(['data' => $data]);                    
                }else if ($intermediary && $intermediary_branch_id){
                    
                    $update_intemediary = IntermediaryBranch::where('id', $intermediary_branch_id)
                        ->where('intermediary_number',$intermediary_number)
                        ->update([
                            'branch' => (int) $new_branch,
                        ]);

                    DB::commit();

                    $intemediary_updated = IntermediaryBranch::where('id', $intermediary_branch_id)
                    ->where('intermediary_number',$intermediary_number)->first();

                    $changed_data = $intemediary_updated->getAttributes();
                    $process_slug = 'intermediary-onboarding';
                    $activity_slug = 'update';
                    $unique_item = $intermediary_number;
                    $old_data = $intemediary_old;
                    $ip =$request->ip();

                    log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

                    $data = [
                        'status' => 1,
                        'title'=>'Branch Updated',
                        'message'=>'Branch Updated Successfully'
                    ];
                    return response()->json(['data' => $data]);
                }
                
            }else{
                
                return response()->json(['error' => true, 'message' => 'Error occured while adding branch '.$beneficiary_details->full_name]);

            }
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollBack();
        }

    }

    public function get_agnt_branch(Request $request){

        $intermediary_number = $request->intermediary_number;
        $branch_id = $request->branch_id;
                
        if ($intermediary_number && $branch_id) {
            $agnt = IntermediaryBranch::select('id','intermediary_number', 'branch')
                            ->where('branch', $branch_id)
                            ->where('intermediary_number',$intermediary_number)
                            ->first();
            return response()->json(['status'=> 1, 'data'=>$agnt]);

        }else{
            return response()->json(['status'=> 0,'message'=> "Unprocessed entity"]);
        }

    }
    //View Agent
    public function view_agnt(Request $request)
    {
        $agnt = Intermediary::where('intermediary_number',$request->agnt_no)->first();
        
        //Select field values
        $payment_methods = PaymentMode::select('paymode_code', 'paymode_name')->get()->toArray();
        $company_modules = CompanyModule::select('company_module_code', 'company_module_name')->get()->toArray();
        $banks=Olbnknames::select('bank_code', 'description')->get()->toArray();
        $currencies = Currency::select('currency_code', 'currency')->get()->toArray();
        $acctype = Acctype::select('acc_type', 'description')
            ->where('TREATY_FAC_ACCOUNT', 'N')
            ->get()
            ->map(function ($actyp) {
                $actyp->description = "{$actyp->acc_type} - {$actyp->description}";
                return $actyp;
            })->toArray();
        $branch = Branch::select('branch', 'description')->where('TREATY_FAC_BRANCH', 'N')->get()->toArray();
        $country1 = Country::select('country_code', 'name')->orderBy('name','ASC')->get()->toArray();
        $units = Unit::select('unit_id', 'description')->get()->toArray();
        $sub_agents = SubAgent::select('name')->get()->map(function ($subAgent) {
            return ['name' => $subAgent->name, 'name_duplicate' => $subAgent->name];
        })->toArray();
        $aims_users = Aimsuser::select('user_name', 'name')->get()->toArray();
        $cities = Cities::select('id', 'city_name')->get()->toArray();
        $client_types = DB::table('client_type')->select('code', 'description')->get()->toArray();
        $gender = Gender::select('gender_code', 'description')->get()->toArray();
        $postal_codes = postal_codes::select('postal_code', 'code_location')->get()->map(function ($postal_code) {
            $postal_code->code_location = "{$postal_code->postal_code} - {$postal_code->code_location}";
            return $postal_code;
        })->toArray();

        $beneficiary_relations = Beneficiary_relation::select('relation_code', 'relation_name')->get()->toArray();
        $org_roles = Organisation_role::select('role_id', 'role_description')->get()->toArray();
        $regions = Regions::select('region', 'region_name')->get()->toArray();
        $vendor_group = VendorGroup::select('vendor_group_code','vendor_group_name')->get()->toArray();
        //Individual type data
        $i_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                ->where('client_type', ['I'])->get();
        //Corporate id types    
        $c_idtypes = Identity_type::select('identity_code','identity_descr','regex_code')
                                    ->where('client_type', ['C'])
                                    ->get();

        //Parameterized Fields
        $PartnerConsentFieldParam = PartnerConsentFieldParam::where("partner_type","A")->get();
        $locationlevels = LocationLevel::select('level_code','level_name','level_type','load_method')->where('status','A')->get();
        $locations = Location::select('loc_code','description','loc_level')->where('active', 'Y')->where('loc_level',1)->get();

        $intermediaryfieldparam = IntermediaryFieldsParam::select('slug','mandatory','disabled','name','country','intermediary_type','input_type','category')
            ->get();
        $bankFieldParams = PartnerBankFieldParam::where('holder', 'AGNT')->get();
        $contactFieldParams = PartnerContactFieldParam::where('holder', 'AGNT')->get();

        $pipcnam = Pipcnam::first();
        $selfRelationCode = Beneficiary_relation::selfRelationCode;
        $partnerConsentData = PartnerConsent::where('partner_number',$request->agnt_no)
        ->where('holder','AGNT') 
        ->get();
        $attributetypes= IntermediaryAttributeType::all();
        $attributeData= IntermediaryAttribute::where('intermediary_number',$request->agnt_no)->get();

		return view('gb.underwriting.intermediary_onboarding.agnt_form',[
            'save_method'=>'view',
            'agnt'=>$agnt,
            'pipcnam'=>$pipcnam,
            'attributetypes'=>$attributetypes,
            'attributeData'=>$attributeData,
            'PartnerConsentFieldParam'=>$PartnerConsentFieldParam,
            'company_modules'=>$company_modules,
            'payment_methods'=>$payment_methods,
            'bankFieldParams'=>$bankFieldParams,
            'contactFieldParams'=>$contactFieldParams,
            'intermediaryfieldparam'=>$intermediaryfieldparam,
            'banks'=>$banks,
            'acctype'=>$acctype,
            'client_types'=>$client_types,
            'branch'=>$branch,
            'country1'=>$country1,
            'units'=>$units,
            'sub_agents'=>$sub_agents,
            'aims_users'=>$aims_users,
            'currencies'=>$currencies,
            'locationlevels'=>$locationlevels,
            'locations'=>$locations,
            'selfRelationCode'=>$selfRelationCode,
            'org_roles'=>$org_roles,
            'beneficiary_relations'=>$beneficiary_relations,
            'regions'=>$regions,
            'i_idtypes'=>$i_idtypes,
            'c_idtypes'=>$c_idtypes,
            'vendor_group'=>$vendor_group,
            'gender'=>$gender,
            'postal_codes'=>$postal_codes,
            'partnerConsentData'=>$partnerConsentData,
                      
         ]);
        
    }


    //Intermediary status
    public function update_intermediary_status(Request $request){
        // dd($request->all());

        try {
            DB::beginTransaction();



                $agent_old = Intermediary::where('intermediary_number', $request->input('intermediary_number'))->first();

                $agent = Intermediary::where('intermediary_number', $request->input('intermediary_number'))
                    ->update([
                        'status' => $request->input('status'),
                        'fraud_status' => $request->input('fraud_status'),
                    ]);
                    
                $agent_updated = Intermediary::where('intermediary_number', $request->input('intermediary_number'))->first();

                $changed_data = $agent_updated->getAttributes();
                $process_slug = 'intermediary-onboarding';
                $activity_slug = 'update';
                $unique_item = $request->input('intermediary_number');
                $old_data = $agent_old;
                $ip =$request->ip();

                log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);
                     
                //Status History Log
                if ($agent) {
                    $status_history=IntermediaryStatusHistory::create([
                        'intermediary_number' => $request->input('intermediary_number'),
                        'status' => $request->input('status'),
                        'reason' => $request->input('status_reason'),
                        'fraud_status' => $request->input('fraud_status'),
                        'created_at' => now(), 
                        'created_by'=> trim(auth()->user()->user_name),
                    ]);
                }

                //Update Intermediary branch
                //Don't activate associated branches automatically
                if($request->status !==IntermediaryAccountStatus::Active){
                    $branches = IntermediaryBranch::where('intermediary_number', $request->input('intermediary_number'))->get();
                    // Update the status of each branch
                    foreach ($branches as $branch) {
                        $branch->update([
                            'status' => $request->input('status'),
                            'fraud_status' => $request->input('fraud_status'),
                        ]);

                        // Add each update to history
                        IntermediaryBranchStatusHistory::create([
                            'intermediary_number' => $request->input('intermediary_number'),
                            'branch' => $branch->branch,
                            'status' => $request->input('status'),
                            'reason' => $request->input('status_reason'),
                            'fraud_status' => $request->input('fraud_status'),
                            'created_at' => now(), 
                            'created_by'=> trim(auth()->user()->user_name),
                        ]);
                    }
                }
                
                DB::commit();
                $data = [
                    'status' => 1,
                    'title'=>'Success',
                    'message'=>'Intermediary Status Updated Successfully'
                ];
                return response()->json(['data' => $data]);
        } catch (\Throwable $th) {
            // throw $th;
            DB::rollBack();
            $data = [
                'status' => 0,
                'title'=>'Failed',
                'message'=>'Failed to update intermediary status'
            ];
            return response()->json(['data' => $data]);

        }
    }

    public function update_intermediary_branch_status(Request $request){
        
        try {

            $agent = Intermediary::select('status')->where('intermediary_number', $request->input('intermediary_number'))->first();
            $agent_branch_old = IntermediaryBranch::where('intermediary_number', $request->input('intermediary_number'))
                ->where('branch', $request->input('intermediary_branch'))->first();
            $agent_branch = IntermediaryBranch::where('intermediary_number', $request->input('intermediary_number'))
                ->where('branch', $request->input('intermediary_branch'))->first();
            if($agent_branch->status ==$request->input('status')){
                $data = [
                    'status' => 0,
                    'title'=>'Error',
                    'message'=>'The current status already matches the new status'
                ];
            }
             elseif($agent->status ==IntermediaryAccountStatus::Active){
                DB::beginTransaction();

                //Update IntermediaryBranch
                $agent_branch = IntermediaryBranch::where('intermediary_number', $request->input('intermediary_number'))
                ->where('branch', $request->input('intermediary_branch'))
                ->update([
                    'status' => $request->input('status'),
                    'fraud_status' => $request->input('fraud_status'),
                ]);

                if($agent_branch){
                    IntermediaryBranchStatusHistory::create([
                        'intermediary_number' => $request->input('intermediary_number'),
                        'branch' => $request->input('intermediary_branch'),
                        'status' => $request->input('status'),
                        'reason' => $request->input('status_reason'),
                        'fraud_status' => $request->input('fraud_status'),
                        'created_at' => now(), 
                        'created_by'=> trim(auth()->user()->user_name),
                    ]);
                }

                DB::commit();

                $agent_branch_updated = IntermediaryBranch::where('intermediary_number', $request->input('intermediary_number'))
                ->where('branch', $request->input('intermediary_branch'))->first();
                
                $changed_data = $agent_branch_updated->getAttributes();
                $process_slug = 'intermediary-onboarding';
                $activity_slug = 'update';
                $unique_item = $request->input('intermediary_number');
                $old_data = $agent_branch_old;
                $ip =$request->ip();

                log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);
                $data = [
                    'status' => 1,
                    'title'=>'Success',
                    'message'=>'Agent status updated successfully'
                ];
            }else{
                $data = [
                    'status' => 0,
                    'title'=>'Error',
                    'message'=>'Intermediary Account is not active'
                ];
            }
                    
            return response()->json(['data' => $data]);
        } catch (\Throwable $th) {
            //throw $th;
            DB::rollBack();
            $data = [
                'status' => 0,
                'title'=>'Failed',
                'message'=>'Failed to update intermediary status'
            ];
            return response()->json(['data' => $data]);

        }
        
    }

    public function getAvailableStatuses(Request $request)
    {
        $intermediaryId = $request->input('id');
        $branchId=$request->input('branch');
        $holder = $request->input('holder');
        
        
        // Fetch all statuses
        $allStatuses = PartnerStatus::select('slug','description')->get();

        if ($branchId) {
            // Fetch the intermediary branch with its current status
            $intermediary = IntermediaryBranch::select("status")
            ->where('branch', $branchId)
            ->where('intermediary_number',$intermediaryId)
            ->first();
        }else if($holder === 'REIN'){
            $intermediary = Crmast::select('name','status')->where('agent',$intermediaryId)->first();
        }else{
            // Fetch the intermediary with its current status
            $intermediary = Intermediary::select("status")->where('intermediary_number',$intermediaryId)->first();
        }
        
        if (!$intermediary) {
            return response()->json(['error' => 'Intermediary not found'], 404);
        }

        $currentStatus = $intermediary->status;
        

        $availableStatuses = $allStatuses->filter(function ($status) use ($currentStatus) {
            return $status->slug !== $currentStatus;
        });

        

        return response()->json(['statuses' => $availableStatuses->values()]);
    } 
    
    //Unique ID Number Check
    public function intermediary_id_validate(Request $request)
    {
        $validate = Intermediary::where("identity_number", $request->id)
            ->where('id_type', $request->id_type)
            ->first();

        return response()->json(['status'=>1, 'validate'=>$validate]);
    }

    public function agnt_status_history(Request $request)
	{
        $branch= $request->branch_id;

        if($branch){
            //INTERMEDIARY BRANCH HISTORY
            $branch_status_history = IntermediaryBranchStatusHistory::where('intermediary_number', $request->intermediary_number)
                ->where('branch',$branch)
                ->get();
            $saveMethod=$request->saveMethod;
    
                return Datatables::of($branch_status_history)
                ->editColumn('status', function ($data) {
                    // $data->partnerStatus->description;
    
                    $status = PartnerStatus::where('slug',$data->status)->first();
                    return $status->description;
    
                })
    
                ->make(true);
        }else{
            //INTERMEDIARY HISTORY
            $status_history = IntermediaryStatusHistory::where('intermediary_number', $request->intermediary_number)->get();
            $saveMethod = $request->saveMethod;
    
                return Datatables::of($status_history)
                ->editColumn('status', function ($data) {
                    // $data->partnerStatus->description;
    
                    $status = PartnerStatus::where('slug',$data->status)->first();
                    return $status->description;
    
                })
    
                ->make(true);
        }
		
		

	}

    public function fetch_intermediary_attribute_data(Request $request) {
        $slug = $request->slug;
        $data = [];
        $intermediaryNumber = $request->intermediaryNumber; 
        $acc_type=$request->acc_type;

        if($intermediaryNumber){
            $selectedValue = IntermediaryAttribute::where('intermediary_number', $intermediaryNumber)
                ->where('slug', $slug)
                ->pluck('value')
                ->first();
        }
        
        // Default value for selectedValue if not found
        $selectedValue = $selectedValue ?? '';

        if($acc_type){

            $acc_data['acctype']=$acc_type;
            $acc_request = new Request($acc_data);
            $Mainparam_gb = new Mainparam_gb();
            $acctype_dtls = $Mainparam_gb->get_acctype_dtls($acc_request);   
       
            if($acctype_dtls){

                $acctype0 = $acctype_dtls['acctype'];
                $prm_slheads = $acctype_dtls['prm_slheads'];
                $comm_slheads = $acctype_dtls['comm_slheads'];
                $comm_glhead = $acctype_dtls['comm_glhead'];
                $glhead = $acctype_dtls['glhead'];
                $flag = $acctype_dtls['flag'];

                switch ($slug) {
                    case 'commission-glhead':
                        $data[] = [
                            'value' => $comm_glhead['prsno'],
                            'description' => $comm_glhead['prdesc'],
                            'selectedValue' => $selectedValue ?? ''
                        ];
                        break;
            
                    case 'premium-glhead':
                        $data[] = [
                            'value' => $glhead['prsno'],
                            'description' => $glhead['prdesc'],
                            'selectedValue' => $selectedValue ?? ''
                        ];
                        break;
            
                    case 'commission-slhead':
                        if ($flag == 'A'){
                            foreach ($comm_slheads as $comm_slhead) {
                                $data[] = [
                                    'value' => $comm_slhead['slhead'],
                                    'description' => $comm_slhead['slhead'] . ' ' . $comm_slhead['name'],
                                    'selectedValue' => $acctype0['comm_slhead']
                                ];
                            }
                        }else {
                            foreach ($comm_slheads as $comm_slhead) {
                                $data[] = [
                                    'value' => $comm_slhead['slhead'],
                                    'description' => $comm_slhead['slhead'] . ' ' . $comm_slhead['name'],
                                    'selectedValue' => $selectedValue ?? ''
                                ];
                            }
                        }
                        
                        break;
            
                    case 'premium-slhead':
                        if ($flag == 'A'){
                            foreach ($prm_slheads as $prm_slhead) {
                                $data[] = [
                                    'value' => $prm_slhead['slhead'],
                                    'description' => $prm_slhead['slhead'] . ' ' . $prm_slhead['name'],
                                    'selectedValue' => $acctype0['prm_slhead']
                                ];
                            }
                        }else{
                            foreach ($prm_slheads as $prm_slhead) {
                                $data[] = [
                                    'value' => $prm_slhead['slhead'],
                                    'description' => $prm_slhead['slhead'] . ' ' . $prm_slhead['name'],
                                    'selectedValue' => $selectedValue ?? ''
                                ];
                            }
                        }
                        break;
            
                    default:
                        $data = [
                            ['value' => '', 'description' => '--No Value Found--', 'selectedValue'=>$selectedValue]
                        ];
                        break;
                }
            }
            
        }else{
            switch ($slug) {
                case 'commission-category':
                    $data = Agcom_categ::all()->map(function($item)  use ($selectedValue) {
                        return [
                            'value' => $item->ag_type,
                            'description' => $item->type_desc,
                            'selectedValue'=>$selectedValue
                        ];
                    });
                    break;
                case 'tax-group':
                    $data = IntermediaryTaxGroup::all()->map(function($item)  use ($selectedValue){
                        return [
                            'value' => $item->group_code,
                            'description' => $item->group_description,
                            'selectedValue'=>$selectedValue
                        ];
                    });
                    break;
                case 'distribution-channel':
                    $data = Dist_channel::all()->map(function($item)  use ($selectedValue){
                        return [
                            'value' => $item->dist_type,
                            'description' => $item->description,
                            'selectedValue'=>$selectedValue
                        ];
                    });
                    break;
                case 'credit-term':
                    $data = Credit::all()->map(function($item)  use ($selectedValue){
                        return [
                            'value' => trim($item->credit_term),
                            'description' => $item->description,
                            'selectedValue'=>$selectedValue
                        ];
                    });
                    break;
                case 'automatic-allocations':
                    $data = [
                        ['value' => 'Y', 'description' => 'YES', 'selectedValue'=>$selectedValue],
                        ['value' => 'N', 'description' => 'NO', 'selectedValue'=>$selectedValue],
                    ];
                    break;
                case 'net-of-commisions':
                    $data = [
                        ['value' => 'Y', 'description' => 'YES', 'selectedValue'=>$selectedValue],
                        ['value' => 'N', 'description' => 'NO', 'selectedValue'=>$selectedValue],
                    ];
                    break;
                default:
                    $data = [
                        ['value' => '', 'description' => '--No Value Found--', 'selectedValue'=>$selectedValue]
                    ];
                    break;
            }
        }
        
        
    
        return response()->json($data);
    }
}
