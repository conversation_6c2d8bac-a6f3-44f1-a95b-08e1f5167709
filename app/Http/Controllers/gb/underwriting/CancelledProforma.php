<?php

namespace App\Http\Controllers\gb\underwriting;

use App\Bustype;
use App\ClassModel;
use App\Http\Controllers\Controller;
use App\Dcontrol;
use App\Debitmastsnap;
use Illuminate\Http\Request;
use Ya<PERSON>ra\Datatables\Datatables;
use Illuminate\Support\HtmlString;

class CancelledProforma extends Controller
{
    public function showCancelledProforma(Request $request)
    {
        //DISPLAY FROM DEBITMASTSNAP
        if ($request->ajax()) {

            $data = Dcontrol::select('policy_no','endt_renewal_no','insured', 'type_of_bus', 'class')
                ->where('delete_str', 'Y');

            return DataTables::of($data)
                ->editColumn('type_of_bus', function ($fn) {
                    $t = Bustype::where('type_of_bus', $fn->type_of_bus)->first();
                    return $t->business;
                })
                ->editColumn('class_desc', function ($fn) {
                    $class_desc = ClassModel::where('class', $fn->class)->first();

                    return $class_desc->description;
                })
                ->make(true);
        }

        return view('gb.underwriting.cancelled_profoma');
    }

    public function getProformaInvoiceDocuments(Request $request)
    {
        $endt_renewal_no = trim($request->endt_renewal_no);

        $debitmastSnap = DebitmastSnap::where('endt_renewal_no', $endt_renewal_no)
            ->where('predebit_type', 'PROFORMA')
            ->select(
                'policy_no',
                'predebit_type',
                'proforma_invoice',
                'endt_renewal_no',
                'sum_insured',
                'total_sum_insured',
                'gross_amount',
                'nett_amount',
                'effective_sum',
                'total_premium',
                'user_str',
                'user_1',
                'dola'
            )->get();

            return DataTables::of($debitmastSnap)
                ->editColumn('sum_insured', function ($fn){
                    return number_format($fn->sum_insured, 2);
                })
                ->editColumn('gross_amount', function ($fn) {
                    return number_format($fn->gross_amount, 2);
                })
                ->editColumn('nett_amount', function ($fn) {
                    return number_format($fn->nett_amount, 2);
                })
                ->addColumn('action', function ($fn) {
                    $proformaUrl = url('/proformaDoc/' . $fn->endt_renewal_no . '/' . $fn->proforma_invoice);
                    return '<a href="' . $proformaUrl . '" target="_blank" class="printpolicydocument">
                                <button type="button" id="proforma_btnx" class="btn btn-primary printpolicydocument">
                                    Proforma Invoice &nbsp;
                                </button>
                            </a>';
                })  
                ->addColumn('quote_print', function ($fn) {
                    $quotationUrl = url('/quotationDoc/' . $fn->endt_renewal_no . '/' . $fn->proforma_invoice);
                    return new HtmlString('<a href="' . $quotationUrl . '" target="_blank" class="printquotation">
                                                <button type="button" id="quotation_btnx" class="btn btn-primary printquotation">
                                                    Quotation &nbsp;
                                                </button>
                                            </a>');
                })       
                ->make(true);
    }
}
