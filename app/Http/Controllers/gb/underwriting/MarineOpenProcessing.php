<?php

namespace App\Http\Controllers\gb\underwriting;

use Exception;
use Throwable;
use App\Branch;
use App\Bustype;
use App\Clauses;
use App\Country;
use App\Dtran0;
//use Yajra\DataTables\Facades\DataTables;
use App\Pipcnam;
use App\Currency;

use App\Agmnf;
use App\Madtl;
use App\Polsect;

use App\Currrate;
use App\Dcontrol;
use App\Financier;
use App\Polclause;
use App\Polexcess;
use App\Pollimits;
use App\Polmaster;
use App\Polmasterend;
use App\Vat_setup;
use Carbon\Carbon;
use App\Autolimits;
use App\Classexces;
use App\ClassModel;
use App\Aimsuser_web;
use App\Policystatus;
use App\Marinemasterpol;
use App\Marinemasterpolsched;
use App\MarineUTLDebit;
use App\MarineUTLDebitmast;
use App\MarineUTLDebitSum_SNAP;
use App\Models\Polscope;
use App\Marinemasterhist;
use App\Regex_parameters;
use App\Models\Polcoverage;
use App\Models\Polwarranty;
use App\workflow\Documents;
use App\Models\Polcondition;
use App\Models\Polexclusion;
use App\OpencoverType;
use App\Debitmast;
use App\Margroup;
use App\MarineMasterParam;
use App\DeclareOpenCargo;
use App\Models\Aimsuser;

use Illuminate\Http\Request;

use App\Models\Poldisclaimer;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\MessageBag;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\gb\underwriting\Policy;
use App\Http\Controllers\gb\underwriting\Risk;
use App\Services\MarinePremService;


use App\Services\IntermediaryQueryService;
use App\ValueObjects\IntermediaryQueryParams;

class MarineOpenProcessing extends Controller
{

    private $policy_no;
    private $endt_renewal_no;
    private $cls;
    private $marine_data;
    private $estimated_annual_limit;
    private $request;

    public function __construct($curr_endt_renewal_no = null)
    {
        $this->endt_renewal_no      =  $curr_endt_renewal_no;
        
    }

    public function setProps(
        string $policy_no,
        string $endt_renewal_no,
        string $cls,
        array $marine_data,
        Request $request,
        int $estimated_annual_limit = 0,
        
    )
    {
        $this->policy_no = $policy_no;
        $this->endt_renewal_no = $endt_renewal_no;
        $this->cls = $cls;
        $this->insured = $insured;
        $this->marine_data = $marine_data;
        $this->request = $request;
        
        $this->estimated_annual_limit = $estimated_annual_limit;
    }

    public function index(Request $request)
    {
        
        $marinehist = Marinemasterhist::where('endt_renewal_no',$request->get('endt_renewal_no'))->first();

        $business_type = Bustype::where('type_of_bus',$marinehist->type_of_bus)->first()->business;

        $currency = Currency::where('currency_code',$marinehist->currency)->first()->description;

        // $agent_name = Agmnf::where('branch',$marinehist->branch)
        //               ->where('agent',$marinehist->agent)
        //               ->first()->name;

         $intermediaryParams = new IntermediaryQueryParams([
            'agentNo' => $marinehist->agent
        
        ]);
    
        $agent_name  =  IntermediaryQueryService ::getIntermediaryByAgentNo($intermediaryParams)->first()->name;

        $source = Country::all();

        $limit_area_name = Country::where('iso',$marinehist->limit_of_area)->first()->nicename;

        $pipcnam = Pipcnam::select('debit_open_cover','declare_cargo_on_master','clauses_flag','help_link')->first();

        $class = ClassModel::where('class',$marinehist->class)->first();

        $opencover_types = OpencoverType::where('is_active', 'Y')->get();

        // $margroup = Margroup::where('class', $marinehist->class)->get();
        $allmargroup = Margroup::where('class', $marinehist->class)->pluck('description', 'group_code');

        //get group codes specific to this endorsement number
        $existingGroupCodes = DeclareOpenCargo::where('endt_renewal_no', $marinehist->endt_renewal_no)
                                            ->pluck('group_code')->toArray();

        $count_declared_cargo = DeclareOpenCargo::where('endt_renewal_no', $marinehist->endt_renewal_no)
                                                ->where(function($query){
                                                    $query->whereNotIn('deleted',['Y','y'])
                                                        ->orWhereNull('deleted');
                                                })
                                                ->count();

        $margroup = Margroup::where('class', $marinehist->class)->get();
                            // ->whereNotIn('group_code', $existingGroupCodes)
                            // ->get();

        $marine_rates = MarineMasterParam::first();

        //CLAUSES
        if(trim($pipcnam->clauses_flag) == 'C'){
            $clauses = Clauses::where('class', $class->class)->get();
        }
        else{
            $clauses = Clauses::where('dept', $class->dept)->get();
        }

        $clsexcess = Classexces::where('class', $marinehist->class)->get();

        $set_limits = Pollimits::where('endt_renewal_no', $marinehist->endt_renewal_no)->get(['limit_no']);
        $set_excess = Polexcess::where('endt_renewal_no', $marinehist->endt_renewal_no)->get(['item_no']);
       
        $set_clauses = Polclause::where('endt_renewal_no', $marinehist->endt_renewal_no)->get(['clause']);

        $all_set_limits = array();
        $all_set_excess = array();
        $all_set_clauses = array();

        foreach($set_limits as $key => $set_limit){
            $set_lmt = $set_limit->limit_no;
            array_push($all_set_limits, $set_lmt);
        }

        $all_set_limits = json_encode($all_set_limits);

        foreach($set_excess as $key => $set_exces){
            $set_exes = $set_exces->item_no;
            array_push($all_set_excess, $set_exes);
        }

        $all_set_excess = json_encode($all_set_excess);
        
        foreach($set_clauses as $key => $set_clause){
            $set_claus = $set_clause->clause;
            array_push($all_set_clauses, $set_claus);
        }

        $all_set_clauses = json_encode($all_set_clauses);

        $limits = Autolimits::where('dept', $class->dept)->where('class', $class->class)->get();

        if($pipcnam->debit_open_cover == 'N'){

            $disclaimer = Poldisclaimer::where('endt_renewal_no', $marinehist->endt_renewal_no)->first();
            $exclusions = Polexclusion::where('endt_renewal_no', $marinehist->endt_renewal_no)->first();
            $warranty = Polwarranty::where('endt_renewal_no', $marinehist->endt_renewal_no)->first();
            $scope = Polscope::where('endt_renewal_no', $marinehist->endt_renewal_no)->first();
            $conditions = Polcondition::where('endt_renewal_no', $marinehist->endt_renewal_no)->first();
            $coverage = Polcoverage::where('endt_renewal_no', $marinehist->endt_renewal_no)->first();
            $debit_count = 0;

        }
        else{
            $debit_count = Debitmast::where('endt_renewal_no', $marinehist->endt_renewal_no)->count();
        }

        $opentype = OpencoverType::where('opentype_code', $marinehist->opentype_code)->first();

          
        return view('gb.underwriting.marine_open_cover', [
            'marine' => $marinehist,
            'class' => $class,
            'business_type' => $business_type,
            'currency' => $currency,
            'agent_name' => $agent_name,
            'source'  => $source,
            'limit_area_name' => $limit_area_name,
            'clauses' => $clauses,
            'clsexcess' => $clsexcess,
            'limits' => $limits,
            'all_set_limits' => $all_set_limits,
            'all_set_excess' => $all_set_excess,
            'all_set_clauses' => $all_set_clauses,
            'attachments_base_class' =>$marinehist->class,
            'disclaimer' => $disclaimer,
            'exclusions' => $exclusions,
            'conditions' => $conditions,
            'scope' => $scope,
            'coverage' => $coverage,
            'warranty' => $warranty,
            'pipcnam' => $pipcnam,
            'opencover_types' => $opencover_types,
            'debit_count' => $debit_count,
            'allmargroup' =>$allmargroup,
            'margroup' => $margroup,
            'marine_rates' => $marine_rates,
            'opentype' => $opentype,
            'count_declared_cargo' => $count_declared_cargo
        ]);

    }

    public function open_covers_master(){

        return view('gb.underwriting.open_cover_listing');

    }

    public function get_latest_cover($endt_renewal_no){

        $marine = Marinemasterpol::where('endt_renewal_no',$endt_renewal_no)
                                     ->orderBy('transeq_no','desc')
                                     ->first();

        return $marine;

    }

    public function marine_endorse_functions(Request $request)
    {
        
        $marinehist = Marinemasterpol::where('policy_no',$request->get('policy_no'))
                                     //->where('status', 'ACT')
                                     ->orderBy('transeq_no','desc')
                                     ->first();

        $business_type = Bustype::where('type_of_bus',$marinehist->type_of_bus)->first()->business;

        $currency = Currency::where('currency_code',$marinehist->currency)->first();

        $currate = Currrate::where('currency_code',$marinehist->currency)->where('rate_date', Carbon::today())->first();
        $currate_count = Currrate::where('currency_code',$marinehist->currency)->where('rate_date', Carbon::today())->count();

        $query = "SELECT count(*) FROM dcontrol WHERE master_endt_no='".$marinehist->endt_renewal_no."' AND endt_renewal_no IN (SELECT endt_renewal_no FROM debitmast)";

        $count_debitted = DB::select($query);

        //$query = "SELECT count(*) FROM madtl WHERE open_cover_no='".$marinehist->endt_renewal_no."' AND endt_renewal_no IN (select endt_renewal_no from dcontrol where (cancelled<>'Y' or cancelled is null))";
        $query = "SELECT count(*) FROM dcontrol WHERE master_endt_no='".$marinehist->endt_renewal_no."' AND (cancelled<>'Y' or cancelled is null)";

        $count_undebitted = DB::select($query);

        $undebitted = ($count_debitted == $count_undebitted) ? 'N' : 'Y';

        $count_uncommitted = Marinemasterhist::where('policy_no',$request->get('policy_no'))
                                             ->where('commit_transaction','<>' ,'Y')
                                             ->count();

        $effective_date = Carbon::today()->format('d-m-Y');

        $vat_setup = Vat_setup::all();

        $financier = Financier::all();

        $postdate_ren_days = Pipcnam::first()->postdate_ren_days;
        $postdate_ren = Pipcnam::first()->postdate_ren;

        /**** GET RENEWAL DATA ******/
          # Check If Renewal Year is Leap Year or Not
        $renewalDate = Carbon::parse($marinehist->renewal_date);
        $isLeapYear = $renewalDate->format('L'); 
        $month = $renewalDate->format('m');

        if ($isLeapYear == 0) {
            $renew_cover_days = 365;
        } else {
            if ($month > 02) {
                $renew_cover_days = 365;
            } else {
                $renew_cover_days = 366;
            }
        }
        
       // $agents = Agmnf::where('branch', $marinehist->branch)->get();
        $intermediaryParams = new IntermediaryQueryParams([
            'branch' => $marinehist->branch
        
        ]);
    
        $agents =  IntermediaryQueryService ::getActiveintermediaryByBranch($intermediaryParams)->get();
        $branch = Branch::where('branch', $marinehist->branch)->get(); 
        
        $cls = ClassModel::where('class',$marinehist->class)->first();

        if($cls->exem == "Y"){
            $exem_cover_days = (string)($renew_cover_days - 1);      
            $renewal_period_to = $renewalDate->addDays($exem_cover_days);
            $next_renewal_date = $renewal_period_to->addDays('1');
        } else{
            $renewal_period_to = $renewalDate->addDays($renew_cover_days);
            $next_renewal_date = $renewal_period_to;
        }

        $ren_post_date_cap = Carbon::parse($marinehist->renewal_date)->addDays($postdate_ren_days-1);

        /**** END RENEWAL DATA *****/

            
        return view('gb.underwriting.marine_open_endorse', [
            'marine' => $marinehist,
            'business_type' => $business_type,
            'currency' => $currency,
            'undebitted' => $undebitted,
            'vat_setup' => $vat_setup,
            'financier' => $financier,
            'currate' => $currate,
            'currate_count' => $curr_count,
            'agents' => $agents,
            'branch' => $branch,
            'renewalDate' => $renewalDate,
            'renewal_period_to' => $renewal_period_to,
            'renew_cover_days' => $renew_cover_days,
            'exem' => $cls->exem,
            'next_renewal_date' => $next_renewal_date,
            'ren_post_date_cap' => $ren_post_date_cap,
            'postdate_ren_days' => $postdate_ren_days,
            'postdate_ren' => $postdate_ren,
            'count_uncommitted' => $count_uncommitted,
            'effective_date'  => $effective_date
        ]);

    }

    public function marine_endt_datatable(Request $request){

        $marinehist = Marinemasterhist::where('policy_no',$request->get('policy_no'))->orderBy('transeq_no', 'desc');


        return datatables::of($marinehist)

            ->addColumn('view', function ($marinehist) {

                return '<a href="'.route('marine_open_dtl',['endt_renewal_no'=>$marinehist->endt_renewal_no]).'" class="btn btn-sm-default modalviewrisk " title="View Risk Details" style="padding: 0; margin-right: 10px;""> <i class="fa fa-eye" title="View Risk Details"> </i></a>';

            })
            ->editColumn('policy_no', function($marinehist){

                return formatPolicyOrClaim($marinehist->policy_no);
            })
            ->editColumn('endt_renewal_no', function($marinehist){

                return formatPolicyOrClaim($marinehist->endt_renewal_no);
            })
            ->editColumn('cov_period_from', function ($marinehist) {
                
                return Carbon::parse($marinehist->cov_period_from)->format('d/m/Y');

            })
            ->editColumn('cov_period_to', function ($marinehist) {
                
                return Carbon::parse($marinehist->cov_period_to)->format('d/m/Y');

            })
            ->editColumn('created_on', function($marinehist){

                return Carbon::parse($marinehist->created_on)->format('d/m/Y');

            })
            ->addColumn('status', function($marinehist){

                return $marinehist->policystatus->description;

            })

            ->escapeColumns([])

            ->make(true);

    }

    public function marine_certs_datatable(Request $request){

        

        if($request->get('type') == 'ALL'){

            $endorsements = Dcontrol::where('master_policy', $request->get('policy_no'))
                                    ->select('master_endt_no')
                                    ->get()
                                    ->toArray();

            $cert = Madtl::whereIn('open_cover_no',$endorsements)->orderBy('date_issue','desc');

        }
        else{

            $cert = Madtl::where('open_cover_no',$request->get('endt_renewal_no'))->orderBy('date_issue','desc');

        }

        return datatables::of($cert)

            ->addColumn('view', function ($cert) {

                return '<a href="'.route('policy_functions',['endt_renewal_no'=>$cert->endt_renewal_no]).'" class="btn btn-sm-default modalviewrisk " title="View Risk Details" style="padding: 0; margin-right: 10px;""> <i class="fa fa-eye" title="View Risk Details"> </i></a>';

            })
            ->editColumn('open_cover_no', function($cert){

                return formatPolicyOrClaim($cert->open_cover_no);
            })
            ->editColumn('cert_no', function($cert){

                return $cert->cert_no;
            })
            ->addColumn('trans_type', function ($cert) {
                $trans_type = Dcontrol::where('endt_renewal_no',$cert->endt_renewal_no)->first()->trans_type;
                
                return $trans_type;

            })
            ->editColumn('period_from', function ($cert) {
                
                return Carbon::parse($cert->period_from)->format('d/m/Y');

            })
            ->editColumn('period_to', function ($cert) {
                
                return Carbon::parse($cert->period_to)->format('d/m/Y');

            })
            ->editColumn('proforma_invoice_date', function ($cert) {
                
                return Carbon::parse($cert->proforma_invoice_date)->format('d/m/Y');

            })
            ->escapeColumns([])

            ->make(true);

    }

    public function marine_policies_datatable(){

        $query = "SELECT marinemasterpol.*
                FROM marinemasterpol
                INNER JOIN (
                    SELECT policy_no, MAX(transeq_no) as highest_record_id
                    FROM marinemasterpol
                    GROUP BY policy_no
                ) max_records ON marinemasterpol.policy_no = max_records.policy_no AND marinemasterpol.transeq_no = max_records.highest_record_id order by marinemasterpol.dola desc";
        
        $marinepol = DB::select($query);
        
        return datatables::of($marinepol)

            ->addColumn('intermediary', function ($marinepol) {

               // $intm = Agmnf::where('branch',$marinepol->branch)->where('agent',$marinepol->agent)->first();
                $intermediaryParams = new IntermediaryQueryParams([
                    'agentNo' => $marinepol->agent
                
                ]);
            
                $intm  =  IntermediaryQueryService ::getIntermediaryByAgentNo($intermediaryParams)->first();

                return $intm->name;

            })
            ->addColumn('view', function ($marinepol) {

                return '<a href="'.route('marine_endorse_functions',['policy_no'=>$marinepol->policy_no]).'" class="btn btn-sm-default modalviewrisk " title="View Risk Details" style="padding: 0; margin-right: 10px;""> <i class="fa fa-eye"> </i></a>';

            })
            ->editColumn('policy_no', function($marinepol){

                return formatPolicyOrClaim($marinepol->policy_no);
            })
            ->editColumn('endt_renewal_no', function($marinepol){

                return formatPolicyOrClaim($marinepol->endt_renewal_no);
            })
            ->editColumn('effective_date', function($marinepol){

                return Carbon::parse($marinepol->effective_date)->format('d/m/Y');
            })
            ->editColumn('expiry_date', function($marinepol){

                return Carbon::parse($marinepol->expiry_date)->format('d/m/Y');
            })
            ->editColumn('renewal_date', function($marinepol){

                return Carbon::parse($marinepol->renewal_date)->format('d/m/Y');
            })
            ->editColumn('type_of_bus', function($marinepol){

                $bs = Bustype::where('type_of_bus',$marinepol->type_of_bus)->first()->business;

                return $bs;
            })
            ->editColumn('trans_type', function($marinepol){

                if ($marinepol->trans_type == 'POL') {

                    return 'NEW POLICY';

                 }
                 else if($marinepol->trans_type == 'REN'){

                    return 'RENEWAL';

                 }else{

                    return '-';

                 }

            })
            ->addColumn('status', function($marinepol){
                
                $st = Policystatus::where('status_code',$marinepol->status)->first()->description;

                return $st;

           })
            ->editColumn('incept_date', function($marinepol){

                return Carbon::parse($marinepol->incept_date)->format('d/m/Y');

            })
            ->editColumn('cov_period_from', function($marinepol){

                return Carbon::parse($marinepol->cov_period_from)->format('d/m/Y');

            })
            ->editColumn('cov_period_to', function($marinepol){

                return Carbon::parse($marinepol->cov_period_to)->format('d/m/Y');

            })
            ->editColumn('created_on', function($marinepol){

                return Carbon::parse($marinepol->created_on)->format('d/m/Y');

            })

            ->escapeColumns([])

            ->make(true);

    }

    public function open_cover_policies_datatable(Request $request){

        //$marinepol = Marinemasterpol::where('client_number',$request->get('client_no'));

        $query = "SELECT * FROM marinemasterpol WHERE client_number='".$request->get('client_no')."' AND transeq_no IN (SELECT MAX(transeq_no) FROM marinemasterpol WHERE  client_number='".$request->get('client_no')."' GROUP BY policy_no) order by dola desc";

        $marinepol = DB::select($query);
        //$marinepol = Marinemasterpol::where('client_number',$request->get('client_no'))->where('status','ACT')->orderBy('dola','desc');

        return datatables::of($marinepol)

            ->addColumn('view', function ($marinepol) {

                return '<a href="'.route('marine_endorse_functions',['policy_no'=>$marinepol->policy_no]).'" class="btn btn-sm-default modalviewrisk " title="View Risk Details" style="padding: 0; margin-right: 10px;""> <i class="fa fa-eye"> </i></a>';

            })
            ->editColumn('policy_no', function($marinepol){

                return formatPolicyOrClaim($marinepol->policy_no);
            })
            ->editColumn('endt_renewal_no', function($marinepol){

                return formatPolicyOrClaim($marinepol->endt_renewal_no);
            })
            ->editColumn('type_of_bus', function($marinepol){

                $bs = Bustype::where('type_of_bus',$marinepol->type_of_bus)->first()->business;

                return $bs;
            })
            ->editColumn('trans_type', function($marinepol){

                if ($marinepol->trans_type == 'POL') {

                    return 'NEW POLICY';

                 }
                 else if($marinepol->trans_type == 'REN'){

                    return 'RENEWAL';

                 }else{

                    return '-';

                 }

            })
            ->addColumn('status', function($marinepol){

                 $description = Policystatus::where('status_code',$marinepol->status)->first()->description;

                 return $description;

            })
            ->editColumn('incept_date', function($marinepol){

                return Carbon::parse($marinepol->incept_date)->format('d/m/Y');

            })
            ->editColumn('cov_period_from', function($marinepol){

                return Carbon::parse($marinepol->cov_period_from)->format('d/m/Y');

            })
            ->editColumn('cov_period_to', function($marinepol){

                return Carbon::parse($marinepol->cov_period_to)->format('d/m/Y');

            })

            ->escapeColumns([])

            ->make(true);

    }

    public function marine_fresh_master(){

        $master = [
                    "BRANCH"                => $this->request->input('branchpol'),
                    "AGENT"                 => $this->request->input('agentpol'),
                    "POLICY_NO"             => $this->policy_no,
                    "ENDT_RENEWAL_NO"       => $this->endt_renewal_no,
                    "TRANSEQ_NO"            => Marinemasterhist::getMarineTranseqNumber($this->request->input('type'),$this->policy_no),
                    "CLIENT_NUMBER"         => $this->marine_data['client_number'],
                    "INSURED"               => $this->marine_data['insured'],
                    "DEPT"                  => $this->request->input('department'),
                    "CLASS"                 => $this->request->input('class'),
                    "TRANS_TYPE"            => $this->request->input('type'),
                    "STATUS"                => 'PND',
                    "AST_MARKER"            => $this->request->input('ast'),
                    "DOLA"                  => Carbon::now(),
                    "EFFECTIVE_DATE"        => $this->request->input('effective_date'),
                    "COV_PERIOD_FROM"       => $this->request->input('period_from'),
                    "COV_PERIOD_TO"         => $this->request->input('period_to'),
                    "EXPIRY_DATE"           => $this->request->input('period_to'),
                    "RENEWAL_DATE"          => $this->marine_data['renewal_date'],
                    "DAYS_COVERED"          => $this->marine_data['days_covered'],
                    "CO_INSURE"             => $this->request->input('co_ins'),  
                    "INCEPT_DATE"           => $this->request->input('incept_date'),
                    "ACCOUNT_YEAR"          => $this->marine_data['account_year'],
                    "ACCOUNT_MONTH"         => $this->marine_data['account_month'],
                    "CURRENCY"              => $this->marine_data['currency'],
                    "COMPANY_SHARE"         => $this->request->input('co_ins_share'),
                    "TYPE_OF_BUS"           => $this->request->input('bustype'),
                    "CURRENCY_RATE"         => $this->marine_data['currency_rate'],
                    "FINANCED"              => $this->request->input('financed'), 
                    "FINANCED_CODE"         => $this->request->input('financier'),
                    "RISK_NOTE_NO"          => $this->request->input('risk_note'), 
                    "VAT_TYPE"              => $this->marine_data['vat_type'],
                    "VAT_DESCRIPTION"       => $this->marine_data['vat_description'],
                    "VAT_CODE"              => $this->request->input('vat_charged'),
                    "EXPECTED_ANNUAL_LIMIT" => 0,
                    "AIR_RATE_LIMIT"        => 0,
                    "SEA_RATE_LIMIT"        => 0,
                    "ROAD_RAIL_RATE_LIMIT"  => 0,
                    "CREATED_BY"            => Auth::user()->user_name,
                    "CREATED_ON"            => Carbon::now(),
                    "UW_YEAR"               => $this->marine_data['uw_year'],
                    "ANNUAL_TRANSIT_LIMIT"  => 'N',
                    "DEPOSIT_PREMIUM"       => 'N',
                    "DEPOSIT_PREMIUM_AMT"   => 0,
                    "TRANSIT_LIMIT"         => 'N',
                    "TRANSIT_LIMIT_AMT"     => 0,
                    "AREA_LIMIT"            => 'N',
                    "LIMIT_OF_AREA"         => '',
                    "COMMIT_TRANSACTION"    => 'N',
                    "LIMITS_SET_BY"         => '',
                    "TRANS_COMMITTED_BY"    => '',
                    "UTILISED_ANNUAL_LIMIT" => 0,
                    "UTILISED_ANNUAL_PREMIUM" => 0,
                    "ENDORSE_AMOUNT"        => 0,
                    "CREATED_BY_SIGNATURE"  => Auth::user()->signature
                ];

        return $master;

    }

    public function replicate_master(){

        $master = [
            "ENDT_RENEWAL_NO"       => $this->endt_renewal_no,
            "TRANSEQ_NO"            => Marinemasterhist::getMarineTranseqNumber($this->request->input('type'),$this->policy_no),
            "TRANS_TYPE"            => $this->request->input('type'),
            "STATUS"                => 'PND',
            "DOLA"                  => Carbon::now(),
            "EFFECTIVE_DATE"        => $this->request->input('effective_date'),
            "COV_PERIOD_FROM"       => $this->request->input('period_from'),
            "COV_PERIOD_TO"         => $this->request->input('period_to'),
            "EXPIRY_DATE"           => $this->request->input('period_to'),
            "RENEWAL_DATE"          => $this->marine_data['renewal_date'],
            "DAYS_COVERED"          => $this->marine_data['days_covered'],
            "ACCOUNT_YEAR"          => $this->marine_data['account_year'],
            "ACCOUNT_MONTH"         => $this->marine_data['account_month'],
            "FINANCED"              => $this->request->input('financed'), 
            "FINANCED_CODE"         => $this->request->input('financier'),
            "RISK_NOTE_NO"          => $this->request->input('risk_note'),
            "COMMIT_TRANSACTION"    => 'N',
            "TRANS_COMMITTED_BY"    => '',
            "CREATED_BY"            => Auth::user()->user_name,
            "CREATED_ON"            => Carbon::now(),
            "UW_YEAR"               => $this->marine_data['uw_year'],
            "CREATED_BY_SIGNATURE"  => Auth::user()->signature,
            "ENDORSE_AMOUNT"        => 0
        ];

        return $master;

    }

    public function marine_masterpol_processing(){

        $trans_type = $this->request->input('type');

        switch($trans_type){
            case 'POL':
            case 'REN':

                $master = $this->marine_fresh_master();

                break;

            default:

                $master = $this->replicate_master();

                break;
        }

        /** ASSIGN VALUES **/

        $status  = $this->save_marine_master($master);

        return $status;

    }
function renew_masterpol($policy_no,$new_endt_renewal_no){
    // $marinerep = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->first();
    $marinerep = Marinemasterpol::where('policy_no',$policy_no)
    ->where('status', 'ACT')
    ->orderBy('transeq_no','desc')
    ->first();

    $marinerep->setTable('marinemasterhist');
    
    $account_month = Carbon::now()->month;
    $account_year = Carbon::now()->year;

    // $policy = new Policy();

    $marinerep->endt_renewal_no = $new_endt_renewal_no;
    // $marinerep->effective_date = $request->input('effective_date');
    // $marinerep->transeq_no = Marinemasterhist::getMarineTranseqNumber('EXT', $marinerep->policy_no);
    $marinerep->dola = Carbon::now();
    $marinerep->trans_type = 'EXT';
    $marinerep->status = 'PND';
    $marinerep->account_year = $account_year;
    $marinerep->account_month = $account_month;
    $marinerep->created_by = Auth::user()->user_name;
    $marinerep->created_on = Carbon::now();
    $marinerep->commit_transaction = 'N';
    $marinerep->trans_committed_by = '';

    Marinemasterhist::insert($marinerep->toArray());

    $data = [
        'new_endt_renewal_no' => $new_endt_renewal_no
    ];

    return $data;

}
    public function renew_masterpol_old($request){

        $marine = Marinemasterpol::where('policy_no',$request->input('policy_no'))
                                 ->where('status', 'ACT')
                                 ->orderBy('transeq_no','desc')
                                 ->first();

        $policy = new Policy();

        $account_month = Carbon::now()->month;
        $account_year = Carbon::now()->year;

        $new_endt_renewal_no = $policy->generate_pol(
                                            $marine->branch,
                                            $marine->class,
                                            'REN',
                                            $account_year,
                                            $account_month
                                        );

        $get_vat_setup = Vat_setup::where('vat_code',(int)$request->input('vat_renewed'))->first();

        $uw_year = date('Y', strtotime((string) $request->input('ren_period_from')));

        $master = [
                        "BRANCH"                => $marine->branch,
                        "AGENT"                 => $request->input('agentpol'),
                        "POLICY_NO"             => $marine->policy_no,
                        "ENDT_RENEWAL_NO"       => $new_endt_renewal_no,
                        "TRANSEQ_NO"            => Marinemasterhist::getMarineTranseqNumber('REN', $marine->policy_no),
                        "CLIENT_NUMBER"         => $marine->client_number,
                        "INSURED"               => $marine->insured,
                        "DEPT"                  => $marine->dept,
                        "CLASS"                 => $marine->class,
                        "TRANS_TYPE"            => 'REN',
                        "STATUS"                => 'PND',
                        "AST_MARKER"            => 'A',
                        "DOLA"                  => Carbon::now(),
                        "EFFECTIVE_DATE"        => $request->input('ren_effective_date'),
                        "COV_PERIOD_FROM"       => $request->input('ren_period_from'),
                        "COV_PERIOD_TO"         => $request->input('ren_period_to'),
                        "EXPIRY_DATE"           => $request->input('ren_period_to'),
                        "RENEWAL_DATE"          => $request->input('ren_renewal_date'),
                        "DAYS_COVERED"          => $request->input('ren_cover_days'),
                        "CO_INSURE"             => $marine->co_insure,  
                        "INCEPT_DATE"           => $marine->incept_date,
                        "ACCOUNT_YEAR"          => $account_year,
                        "ACCOUNT_MONTH"         => $account_month,
                        "CURRENCY"              => $request->input('ren_currency'),
                        "COMPANY_SHARE"         => $marine->company_share,
                        "TYPE_OF_BUS"           => $marine->type_of_bus,
                        "CURRENCY_RATE"         => $request->input('ren_today_currency'),
                        "FINANCED"              => ($request->filled('financier_renew')) ? 'Y' : 'N', 
                        "FINANCED_CODE"         => $request->input('financier_renew'),
                        "RISK_NOTE_NO"          => $request->input('risk_note_no'), 
                        "VAT_TYPE"              => $get_vat_setup->vat_type,
                        "VAT_DESCRIPTION"       => $get_vat_setup->vat_description,
                        "VAT_CODE"              => $get_vat_setup->vat_code,
                        "EXPECTED_ANNUAL_LIMIT" => 0,
                        "AIR_RATE_LIMIT"        => 0,
                        "SEA_RATE_LIMIT"        => 0,
                        "ROAD_RAIL_RATE_LIMIT"  => 0,
                        "CREATED_BY"            => Auth::user()->user_name,
                        "CREATED_ON"            => Carbon::now(),
                        "UW_YEAR"               => $uw_year,
                        "ANNUAL_TRANSIT_LIMIT"  => 'N',
                        "DEPOSIT_PREMIUM"       => 'N',
                        "DEPOSIT_PREMIUM_AMT"   => 0,
                        "TRANSIT_LIMIT"         => 'N',
                        "TRANSIT_LIMIT_AMT"     => 0,
                        "AREA_LIMIT"            => 'N',
                        "LIMIT_OF_AREA"         => '',
                        "COMMIT_TRANSACTION"    => 'N',
                        "LIMITS_SET_BY"         => '',
                        "TRANS_COMMITTED_BY"    => '',
                        "UTILISED_ANNUAL_LIMIT" => 0,
                        "CREATED_BY_SIGNATURE"  => Auth::user()->signature
                 ];

        
        $status  = $this->save_marine_master($master);

        $data = [
            'new_endt_renewal_no' => $new_endt_renewal_no,
            'status' => $status
        ];

        return $data;

    }

    public function process_renewal($policy_no,$new_endt_renewal_no){

        DB::beginTransaction();

        try{

            $data = $this->renew_masterpol($policy_no,$new_endt_renewal_no);
            return true;
            // DB::commit();
            
            // return redirect()->route('marine_open_dtl',['endt_renewal_no'=> $data['new_endt_renewal_no']])->with('success','Open Cover Policy Renewed successfully');

        }
        catch(\Throwable $e){
            return false;
            // DB::rollback();

            // return redirect()->back()->with('error', 'Failed to renew cover. Kindly try again.')->withErrors($validate);
            
        }

    }

    public function process_endorsement(Request $request){

        $endt_renewal_no = $request->input('endt_renewal_no');
        $type = $request->input('type');

        DB::beginTransaction();
        try{

            $marinerep = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->first();

            $marinerep->setTable('marinemasterhist');
            
            $account_month = Carbon::now()->month;
            $account_year = Carbon::now()->year;

            $policy = new Policy();

            $new_endt_renewal_no = $policy->generate_pol(
                                                $marinerep->branch,
                                                $marinerep->class,
                                                'EXT',
                                                $account_year,
                                                $account_month
                                            );

            $marinerep->endt_renewal_no = $new_endt_renewal_no;
            $marinerep->effective_date = $request->input('effective_date');
            $marinerep->transeq_no = Marinemasterhist::getMarineTranseqNumber('EXT', $marinerep->policy_no);
            $marinerep->dola = Carbon::now();
            $marinerep->trans_type = 'EXT';
            $marinerep->status = 'PND';
            $marinerep->account_year = $account_year;
            $marinerep->account_month = $account_month;
            $marinerep->created_by = Auth::user()->user_name;
            $marinerep->created_on = Carbon::now();
            $marinerep->commit_transaction = 'N';
            $marinerep->trans_committed_by = '';

            Marinemasterhist::insert($marinerep->toArray());

                # Replicate Polclause Details
            $policy->replicate_polclause($new_endt_renewal_no, $endt_renewal_no);

                # Replicate Policy Limits
            $policy->replicate_limits($new_endt_renewal_no, $endt_renewal_no);

                # Replicate Policy Excess
            $policy->replicate_excess($new_endt_renewal_no, $endt_renewal_no);

            DB::commit();
            
            return redirect()->route('marine_open_dtl',['endt_renewal_no'=> $new_endt_renewal_no])->with('success','Endorsement Number generated successfully. Proceed to set limits');

        }
        catch(\Throwable $e){

            DB::rollback();
            
            return redirect()->back()->with('error','Unable to generate endorsement');
            
        }
    }

    public function save_marine_master($master){

        //DB::beginTransaction();
        try{

            if(!in_array($master['TRANS_TYPE'], ['POL','REN','RNS'])){

                $this->replicate_cargotypes($this->policy_no, $this->endt_renewal_no);

            }

            if($master['TRANS_TYPE'] == 'POL'){
                Marinemasterhist::insert($master);
                Marinemasterpol::insert($master);

            }
            else if($master['TRANS_TYPE'] == 'REN'){
                Marinemasterhist::insert($master);
                $masterpol = Marinemasterpol::where('policy_no', $this->policy_no)->delete();
                Marinemasterpol::insert($master);
            }
            else{

                Marinemasterpol::where('policy_no', $this->policy_no)->update($master);
                $masterpol = Marinemasterpol::where('policy_no', $this->policy_no)->first()->toArray();
                Marinemasterhist::insert($masterpol);

            }

            // $prevDcon = Dcontrol::previous_endorsement($master['endt_renewal_no']);

            // $master_policy = (in_array($master['trans_type'], ['POL','REN'])) ? $master['endt_renewal_no'] : $prevDcon->master_policy;
            // $master_endt = $master['endt_renewal_no'] ;

            // $upd = Dcontrol::where('endt_renewal_no', $master['endt_renewal_no'])->update([
            //                     'master_policy' => $master_policy,
            //                     'master_endt_no' => $master_endt             
            //                 ]);

            //DB::commit();
            
            return true;

        }
        catch(\Throwable $e){
            //DB::rollback();
            
            return false;
            
        }

    }

    public function update_policy_limits(Request $request){

        $debit_open_cover = Pipcnam::first()->debit_open_cover;
        $commited = 'N';
        $debitted = 0;

        if($debit_open_cover == 'N'){

            $committed = Marinemasterhist::where('endt_renewal_no', $request->get('open_endt_no'))
                                    ->first()->commit_transaction;
        }
        else{

            $debitted = Debitmast::where('endt_renewal_no', $request->get('open_endt_no'))->count();

        }

        if($committed == 'Y'){
            return redirect()->back()->with('error', 'This transaction has already been committed. Limits cannot be amended');
        }

        if($debitted > 0){
            return redirect()->back()->with('error', 'This transaction has already been debitted. Limits cannot be amended');
        }

        $validate = Validator::make($request->all(), [
            'open_endt_no'          => 'required',
            'annual_transit_limit'  => 'required',
            'deposit_premium'       => 'required',
            'transit_limit'         => 'required',
            'area_limit'            => 'required',
            'opencover_type'        => [$debit_open_cover == 'Y' ? 'required' : 'nullable']
        ]);

        $validate->sometimes('annual_transit_limit_amt', 'gt:0', function ($input) use($debit_open_cover){
            return $input->annual_transit_limit == 'Y' || $debit_open_cover == 'Y';
        });

        $validate->sometimes('deposit_premium_amt', 'gt:0', function ($input) {
            return $input->deposit_premium == 'Y';
        });

        $validate->sometimes('transit_limit_amt', 'gt:0', function ($input) {
            return $input->transit_limit == 'Y';
        });

        $validate->sometimes('limit_of_area', 'required', function ($input) {
            return $input->area_limit == 'Y';
        });

        if ($validate->fails()){
            
            return redirect()->back()->with('error', 'Failed to set limits. Kindly fill all required fields')->withErrors($validate);
        }

        DB::beginTransaction();
        try{

            $trans_type                = $request->get('type');
            $policy_no                 = $request->get('policy_no');
            $endt_renewal_no           = $request->get('open_endt_no');
            $annual_transit_limit      = $request->get('annual_transit_limit');
            $deposit_premium           = $request->get('deposit_premium');
            $transit_limit             = $request->get('transit_limit');
            $area_limit                = $request->get('area_limit');
            $annual_transit_limit_amt  = $request->get('annual_transit_limit_amt');
            $deposit_premium_amt       = $request->get('deposit_premium_amt');
            $transit_limit_amt         = $request->get('transit_limit_amt');
            $limit_of_area             = $request->get('limit_of_area');
            $opentype_code             = $request->get('opencover_type');

            $marineupdhist = Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)
                                            ->update([
                                                'annual_transit_limit' => $annual_transit_limit,
                                                'deposit_premium' => $deposit_premium ,
                                                'transit_limit' => $transit_limit,
                                                'area_limit' => $area_limit,
                                                'expected_annual_limit' => ($annual_transit_limit == 'Y') ? $annual_transit_limit_amt : 0,
                                                'deposit_premium_amt' => ($deposit_premium == 'Y') ? $deposit_premium_amt : 0,
                                                'transit_limit_amt' => ($transit_limit == 'Y') ? $transit_limit_amt : 0,
                                                'limit_of_area' => ($area_limit == 'Y') ? $limit_of_area : '',
                                                'limits_set_by' => Auth::user()->user_name,
                                                'opentype_code' => $opentype_code
                                            ]);


            DB::commit();

            return redirect()->route('marine_open_dtl',['endt_renewal_no'=> $endt_renewal_no])->with('success','Open Cover Policy Limits set successfully');

        }
        catch(\Throwable $e){
            DB::rollback();
            
            return redirect()->back()->with('error', 'Failed to set limits. Kindly fill all required fields');
            
        }

        
    }

    public function update_policy_limitsdbt(Request $request){

        $debit_open_cover = Pipcnam::first()->debit_open_cover;
        $commited = 'N';
        $debitted = 0;

        if($debit_open_cover == 'N'){

            $committed = Marinemasterhist::where('endt_renewal_no', $request->get('open_endt_no'))
                                    ->first()->commit_transaction;
        }
        else{

            $debitted = Debitmast::where('endt_renewal_no', $request->get('open_endt_no'))->count();

        }

        if($committed == 'Y'){
            return redirect()->back()->with('error', 'This transaction has already been committed. Limits cannot be amended');
        }

        if($debitted > 0){
            return redirect()->back()->with('error', 'This transaction has already been debitted. Limits cannot be amended');
        }

        $validate = Validator::make($request->all(), [
            'policy_no'             => 'required',
            'open_endt_no'          => 'required',
            'limits_basis_type'     => 'required',
            'basis_opencover_type'  => [$debit_open_cover == 'Y' ? 'required' : 'nullable'],
            'basis_annual_transit_limit'  => [$input->limits_basis_type == 'COVER' && $input->basis_opencover_type == 'UTL' ? 'required' : 'nullable'],
            'basis_limit_per_transit'  => [$input->limits_basis_type == 'COVER' && $input->basis_opencover_type == 'UTL' ? 'required' : 'nullable']
        ]);

        if ($validate->fails()){
            
            return redirect()->back()->with('error', 'Failed to set limits. Kindly fill all required fields')->withErrors($validate);
        }

        DB::beginTransaction();
        try{

            $policy_no                 = $request->get('policy_no');
            $endt_renewal_no           = $request->get('open_endt_no');
            $basis_opencover_type      = $request->get('basis_opencover_type');
            $limits_basis_type           = $request->get('limits_basis_type');
            $basis_annual_transit_limit = (float) str_replace(',','',$request->get('basis_annual_transit_limit'));
            $basis_limit_per_transit    = (float) str_replace(',','',$request->get('basis_limit_per_transit'));
            $master = Marinemasterpol::where('endt_renewal_no',$endt_renewal_no)->first();

            if($limits_basis_type == 'COVER' && $basis_opencover_type =='UTL' && $basis_annual_transit_limit <= 0){
                return redirect()->back()->with('error', 'Annual Transit Limit Cannot be zero or less than zero');
            }

            if($limits_basis_type == 'COVER' && $basis_opencover_type =='UTL' && $basis_limit_per_transit <= 0){
                return redirect()->back()->with('error', 'Annual Limit Per Transit Cannot be zero or less than zero');
            }

            if($limits_basis_type == 'COVER' && $basis_opencover_type =='UTL' && $basis_annual_transit_limit < $master->utilised_annual_limit){
                return redirect()->back()->with('error', 'Cannot Reduce Limit below the utilised Annual Limit');
            }

            $updvals =  [
                            'annual_transit_limit' => ($limits_basis_type == 'COVER') ? 'Y' : 'N',
                            'deposit_premium' => 'N',
                            'transit_limit' => ($limits_basis_type == 'COVER') ? 'Y' : 'N',
                            'expected_annual_limit' => ($limits_basis_type == 'COVER') ? $basis_annual_transit_limit : 0,
                            'deposit_premium_amt' => 0,
                            'transit_limit_amt' => ($limits_basis_type == 'COVER') ? $basis_limit_per_transit : 0,
                            'limits_set_by' => Auth::user()->user_name,
                            'opentype_code' => $basis_opencover_type,
                            'limits_basis' => $limits_basis_type
                        ];

            $updpol = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)
                                     ->update($updvals);

            $updhist = Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)
                                       ->update($updvals);

            $count_cargo = DeclareOpenCargo::where('endt_renewal_no', $endt_renewal_no)->where('deleted','N')->count();

            
            $val = $this->endorse_cover_limitsdbt($endt_renewal_no);

            DB::commit();

            return redirect()->route('marine_open_dtl',['endt_renewal_no'=> $endt_renewal_no])->with('success','Open Cover Policy Limits set successfully');

        }
        catch(\Throwable $e){
            DB::rollback();
            
            return redirect()->back()->with('error', 'Failed to set limits. Kindly fill all required fields');
            
        }

        
    }

    public function update_master_on_commit($endt_renewal_no){

        $upd = Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)
                                ->first();

        $endorsements = array($endt_renewal_no);
                    
        if($upd->trans_type == 'EXT'){
                    
            $latest = Marinemasterpol::where('policy_no', $upd->policy_no)->where('status','ACT')->orderBy('transeq_no', 'desc')->first();
                    
            array_push($endorsements, $latest->endt_renewal_no);
                    
        }
                    
        $marineupd = Marinemasterpol::whereIn('endt_renewal_no', $endorsements)
                                    ->update([
                                        'status' => 'ACT',
                                        'commit_transaction' => 'Y',
                                        'trans_committed_by' => Auth::user()->user_name 
                                    ]);
                    
        $marineupdhist = Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)
                                    ->update([
                                        'status' => 'ACT',
                                        'commit_transaction' => 'Y',
                                        'trans_committed_by' => Auth::user()->user_name 
                                    ]);
                    
                    
        if($upd->trans_type == 'REN'){
                    
            $m =  Marinemasterpol::where('policy_no', $upd->policy_no)
                                ->where('endt_renewal_no','<>', $endt_renewal_no) 
                                ->update([
                                    'status' => 'LPS'
                                ]);
                                                    
            $m1 = Marinemasterhist::where('policy_no', $upd->policy_no)
                                      ->where('endt_renewal_no','<>', $endt_renewal_no)
                                      ->update([
                                           'status' => 'LPS'
                                      ]);
                                                    
        }

    }

    public function confirm_open_commit(Request $request){
       
        $upd = Marinemasterhist::where('endt_renewal_no', $request->input('endt_renewal_no'))
                                ->first();

                               

        if($upd->commit_transaction == 'Y'){
            //return 5;

            return redirect()->back()->with('error', 'Endorsement already committed!!');
        }

        if(!isset($upd->annual_transit_limit) && !isset($upd->transit_limit) && !isset($upd->deposit_premium) && !isset($upd->area_limit)){
            
            //return 3;

            return redirect()->back()->with('error', 'Cannot commit. Open Cover Limits have not been set!!');

        }

        if(!isset($upd->limits_set_by) || $upd->limits_set_by == '' || $upd->limits_set_by == null){
            
            //return 3;

            return redirect()->back()->with('error', 'Cannot commit. Open Cover Limits have not been set!!');

        }

        $validate = Validator::make($request->all(), [
            'endt_renewal_no'  => 'required'

        ]);

        if ($validate->fails()){
            
            //return -1;
            return redirect()->back()->with('error', 'Failed to commit transaction!!');
        }

        DB::beginTransaction();
        try{

            $endt_renewal_no = $request->input('endt_renewal_no');

            $endorsements = array($endt_renewal_no);

            if($upd->trans_type == 'EXT'){

                $latest = Marinemasterpol::where('policy_no', $upd->policy_no)->where('status','ACT')->orderBy('transeq_no', 'desc')->first();

                array_push($endorsements, $latest->endt_renewal_no);

            }

            $marineupd = Marinemasterpol::whereIn('endt_renewal_no', $endorsements)
                                        ->update([
                                            'annual_transit_limit' => $upd->annual_transit_limit,
                                            'deposit_premium' => $upd->deposit_premium ,
                                            'transit_limit' => $upd->transit_limit,
                                            'area_limit' => $upd->area_limit,
                                            'expected_annual_limit' => ($upd->annual_transit_limit == 'Y') ? $upd->expected_annual_limit : 0,
                                            'deposit_premium_amt' => ($upd->deposit_premium == 'Y') ? $upd->deposit_premium_amt : 0,
                                            'transit_limit_amt' => ($upd->transit_limit == 'Y') ? $upd->transit_limit_amt : 0,
                                            'limit_of_area' => ($upd->area_limit == 'Y') ? $upd->limit_of_area : '',
                                            'status' => 'ACT',
                                            'commit_transaction' => 'Y',
                                            'trans_committed_by' => Auth::user()->user_name 
                                        ]);

            $marineupdhist = Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)
                                            ->update([
                                                'status' => 'ACT',
                                                'commit_transaction' => 'Y',
                                                'trans_committed_by' => Auth::user()->user_name 
                                            ]);


            if($upd->trans_type == 'REN'){

                $m =  Marinemasterpol::where('policy_no', $upd->policy_no)
                                     ->where('endt_renewal_no','<>', $endt_renewal_no) 
                                     ->update([
                                        'status' => 'LPS'
                                     ]);
                                
                $m1 = Marinemasterhist::where('policy_no', $upd->policy_no)
                                      ->where('endt_renewal_no','<>', $endt_renewal_no)
                                      ->update([
                                        'status' => 'LPS'
                                      ]);
                                
            }

            DB::commit();

            //return 1;
            return redirect()->route('marine_open_dtl',['endt_renewal_no'=> $endt_renewal_no])->with('success','Transaction Committed successfully');

        }
        catch(\Throwable $e){
            DB::rollback();
            
            //return -1;
            return redirect()->back()->with('error', 'Failed to commit transaction!!');
            
        }

        
    }

    public function cancel_open_proforma(Request $request){

        $upd = Marinemasterhist::where('endt_renewal_no', $request->input('endt_renewal_no'))
                                ->first();                    

        if($upd->commit_transaction == 'Y'){

            return redirect()->back()->with('error', 'Cannot Cancel proforma. Endorsement already committed!!');

        }

        $validate = Validator::make($request->all(), [
            'endt_renewal_no'  => 'required'

        ]);

        if ($validate->fails()){
           
            return redirect()->back()->with('error', 'Failed to cancel proforma!!');

        }

        DB::beginTransaction();
        try{

            $endt_renewal_no = $request->input('endt_renewal_no');


            $del =  Marinemasterpol::where('policy_no', $upd->policy_no)
                                    ->where('endt_renewal_no', $endt_renewal_no) 
                                    ->delete();
                                
            $del1 = Marinemasterhist::where('policy_no', $upd->policy_no)
                                    ->where('endt_renewal_no', $endt_renewal_no) 
                                    ->delete();

            DB::commit();

            return redirect()->route('marine_endorse_functions', ['policy_no'=> $upd->policy_no])->with('success','Transaction Deleted successfully');

        }
        catch(\Throwable $e){
            DB::rollback();
            
            return redirect()->back()->with('error', 'Failed to commit transaction!!');
            
        }

    }

    public function update_opencover_utilised($endt_renewal_no){

        $include_endts = array();
        $endts = array();

        $dcon = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();

        $master = Marinemasterhist::where('endt_renewal_no', $dcon->master_endt_no)->first();

        $utilised_annual_limit = 0;
        $utilised_annual_premium = 0;
        
        if($master->opentype_code == 'ZPR' || $master->opentype_code == 'CMC'){

            $declared_cargo = DeclareOpenCargo::where('endt_renewal_no', $master->endt_renewal_no)->get();
            $declared_cargo_count = DeclareOpenCargo::where('endt_renewal_no', $master->endt_renewal_no)->count();

            if($declared_cargo_count > 0){

                foreach($declared_cargo as $declared){

                    // $madtl_rec = Madtl::selectRaw('group_code,SUM(total_sum_insured) AS total_sum, SUM(total_premium) as total_prem')
                    //                    ->where('group_code', $declared->group_code)
                    //                    ->where('endt_renewal_no', $endt_renewal_no)
                    //                    ->groupBy('group_code')
                    //                    ->first();

                    $madtl_rec = Madtl::select('group_code')
                                       ->selectRaw('SUM(total_sum_insured) AS total_sum')
                                       ->selectRaw('SUM(total_premium) as total_prem')
                                       ->where('group_code', $declared->group_code)
                                       ->where('endt_renewal_no', $endt_renewal_no)
                                       ->groupBy('group_code')
                                       ->first();

                    $util_limit_sum = ($dcon->trans_type == 'MAC') ? $declared->utilised_annual_limit : 0;
                    $util_limit_prem = ($dcon->trans_type == 'MAC') ? $declared->utilised_annual_premium : 0;
    
                    $upd_rec = DeclareOpenCargo::where('group_code', $declared->group_code)
                                                ->where('endt_renewal_no', $declared->endt_renewal_no)
                                                ->update([
                                                    'utilised_annual_limit'    => $madtl_rec->total_sum,
                                                    'utilised_annual_premium'  => $madtl_rec->total_prem
                                                    // 'utilised_annual_limit'    => $util_limit_sum + $madtl_rec->total_sum,
                                                    // 'utilised_annual_premium'  => $util_limit_prem + $madtl_rec->total_prem
                                                ]);
    
                }
    
                $total_amt_rec = DeclareOpenCargo::selectRaw('SUM(utilised_annual_limit) AS total_sum, SUM(utilised_annual_premium) as total_prem')
                                                 ->where('endt_renewal_no', $declared->endt_renewal_no)->where('deleted', 'N')
                                                 ->first();

                // $total_amt_rec = DB::select("select SUM(utilised_annual_limit) AS total_sum, SUM(utilised_annual_premium) as total_prem from declare_open_cargo where deleted='N' and endt_renewal_no='$dcon->endt_renewal_no'");
                // $total_amt_rec = collect($total_amt_rec)->first();
                
                $utilised_annual_limit = $total_amt_rec->total_sum;
                $utilised_annual_premium = $total_amt_rec->total_prem;

            }else{

                // $total_amt_rec = Madtl::selectRaw('SUM(total_sum_insured) AS total_sum,SUM(total_premium) as total_prem')
                //                        //->selectRaw('SUM(total_premium) as total_prem')
                //                        ->where('endt_renewal_no', $dcon->endt_renewal_no)
                //                        ->first();

                $endts = Dcontrol::where('policy_no',$dcon->policy_no)
                                     ->where('master_policy',$dcon->master_policy)
                                     ->select('endt_renewal_no')
                                     ->get()
                                     ->toArray();

                $endts = array_column($endts,'endt_renewal_no');
                $include_endts = array_merge($include_endts, $endts);

                $total_amt_rec = Polsect::selectRaw('SUM(total_sum_insured) AS total_sum,SUM(total_premium) as total_prem')
                                        //->selectRaw('SUM(total_premium) as total_prem')
                                         //->where('endt_renewal_no', $dcon->endt_renewal_no)
                                         ->whereIn('endt_renewal_no', $include_endts)
                                         ->Where(function($query){
                                                $query->Where('cancelled','<>','Y')
                                                      ->OrWhereNull('cancelled');
                                         })
                                         ->first();

               // $total_amt_rec = DB::select("select SUM(total_sum_insured) AS total_sum, SUM(total_premium) as total_prem from madtl where endt_renewal_no='$dcon->endt_renewal_no'");
               // $total_amt_rec = collect($total_amt_rec)->first();

                $utilised_annual_limit = $total_amt_rec->total_sum;
                $utilised_annual_premium = $total_amt_rec->total_prem;

            }
            
            $upda = Marinemasterpol::where('endt_renewal_no', $master->endt_renewal_no)
                                    ->update([
                                        'utilised_annual_limit'    => $utilised_annual_limit,
                                        'utilised_annual_premium'  => $utilised_annual_premium
                                    ]);

            $updb = Marinemasterhist::where('endt_renewal_no', $master->endt_renewal_no)
                                     ->update([
                                        'utilised_annual_limit'    => $utilised_annual_limit,
                                        'utilised_annual_premium'  => $utilised_annual_premium
                                     ]);

                                     
            $lmt = $this->update_marine_polsched($endt_renewal_no);
                                    
        }
       
    }


    /******* CLAUSES LIMITS EXCESS************/
    public function add_marine_limit(Request $request)
    {

        $limit_nos = $request->limit_descr_add;
        $rec = Marinemasterhist::where('endt_renewal_no', $request->limit_endt_add)->first();
                  
        $limit_nos = $request->limit_descr_add;
                
        DB::beginTransaction();
        try{
            foreach ($limit_nos as $key => $limit_no) {
                if($limit_no != null){
                    $pollimits = new Pollimits;
                    $count = 0;
                    $countpollimits = Pollimits::where('endt_renewal_no', $rec->endt_renewal_no)->count();
                    if($countpollimits > 0){
                        $count = Pollimits::where('endt_renewal_no', $rec->endt_renewal_no)->max('location');
                    }
                    $autolimit = Autolimits::where('class', $rec->class)->where('limit_no', $limit_no)->first();
                    $next = (int)$count + 1;
                    
                    $pollimits->policy_no = $rec->policy_no;
                    $pollimits->endt_renewal_no = $rec->endt_renewal_no;
                    $pollimits->detail_line = $autolimit->description;
                    $pollimits->location = $next;
                    $pollimits->sec_no = $next;
                    $pollimits->limit_no = $autolimit->limit_no;
                    $pollimits->amount = str_replace(',','',$request->limit_amt_add[$key]);
                    $pollimits->user_name = Auth::user()->user_name;
                    $pollimits->comb_class = $rec->class;
                            
                    $pollimits->save();
                    
                }
            }

            DB::commit();

        }catch(Exception $e){
            
            DB::rollback();
        }
               
    }


    public function add_marine_excess(Request $request){

        $policy_no = $request->excess_pol_add;
        $endt_renewal_no = $request->excess_endt_add;

        $rec = Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)->first();

        $excess_item_nos = $request->excess_item_no;

        DB::beginTransaction();

        try{
            foreach ($excess_item_nos as $key => $excess_item_no) {
                
                if($excess_item_no != null){

                    $polexcess = new Polexcess;
                    
                    $countexcess = Polexcess::where('endt_renewal_no', $rec->endt_renewal_no)->where('class', $rec->class)->where('location', 1)->count();
                    $checkExcess = Polexcess::where('endt_renewal_no', $rec->endt_renewal_no)->where('class', $rec->class)->where('location', 1)->where('item_no', $excess_item_no)->count();
                    
                    if($countexcess > 0){
                        $count = Polexcess::where('endt_renewal_no', $rec->endt_renewal_no)->where('class', $rec->class)->where('location', 1)->max('item_no1');
                    }else{
                        $count = 0;
                    }

                    $next = (int)$count + 1;

                    $polexcess->policy_no = $rec->policy_no;
                    $polexcess->class = $rec->class;
                    $polexcess->endt_renewal_no = $rec->endt_renewal_no;
                    $polexcess->location = 1;
                    $polexcess->item_no1 = $next;
                    $polexcess->item_no = $request->excess_item_no[$key];
                    $polexcess->description = $request->excess_item_descr[$key];
                    $polexcess->load_discount = "L";
                    $polexcess->based_on = $request->excess_basis[$key];
                    $polexcess->rate = $request->excess_rate_add[$key];
                    $polexcess->base = $request->excess_base_add[$key];
                    $polexcess->minimum_excess = $request->excess_min_amt[$key];
                    $polexcess->excess_per = $request->excess_rate_add[$key];
                    $polexcess->amount = 0;
                    $polexcess->basic_amount = 0;
                    $polexcess->basic_rate = 0;
                    $polexcess->balance_amount = 0;
                    $polexcess->balance_rate = 0;
                    $polexcess->electrical = 0;
                    $polexcess->curr = 0;
                    $polexcess->user_fill = Auth::user()->user_name;
                    $polexcess->maximum_excess = str_replace(',','',$request->excess_max_amt[$key]);
                    $polexcess->comb_class = $rec->class;

                    if($checkExcess == 0){

                        $polexcess->save();

                    }
                }
            }

                DB::commit();

            }catch (\Throwable $e) {
               
                DB::rollback();
            }
        
    }


    public function update_marine_excess(Request $request)
    {
        $rec = Marinemasterhist::where('endt_renewal_no', $request->excess_endt_ed)->first();

        
        $policyexcess = array();

        $policyexcess['description'] = trim($request->get('excess_descr_ed'));
        $policyexcess['rate'] = trim($request->get('excess_rate_ed'));
        $policyexcess['base'] = trim($request->get('excess_base_ed'));
        $policyexcess['based_on'] = trim($request->get('excess_basis_ed'));
        $policyexcess['minimum_excess'] = str_replace(',','',$request->excess_minc_ed);
        $policyexcess['maximum_excess'] = str_replace(',','',$request->excess_maxc_ed);
    
        DB::beginTransaction();

        try{

            if (!empty($policyexcess)) {

                Polexcess::where('endt_renewal_no', $rec->endt_renewal_no)
                        ->where('class', $rec->class)
                        ->where('location', 1)
                        ->where('item_no1', $request->excess_itemno_ed)
                        ->update($policyexcess);

            }

            DB::commit();

            return [
                'status' => 1
            ];

        }catch (\Throwable $e) {
        
            DB::rollback();

            return [
                'status' => -1
            ];
        }

        return [
            'status' => -1
        ];
        
    }

    public function delete_marine_excess(Request $request)
    {
       
        $rec = Marinemasterhist::where('endt_renewal_no', $request->excess_endt_del)->first();

        $polexcess = Polexcess::where('endt_renewal_no', $rec->endt_renewal_no)
                                ->where('class', $rec->class)
                                ->where('location', 1)
                                ->where('item_no1', $request->excess_item_no1_del)
                                ->count();
                                
        DB::beginTransaction();

        try{

            if ($polexcess > 0) {
                Polexcess::where('endt_renewal_no', $rec->endt_renewal_no)
                        ->where('class', $rec->class)
                        ->where('location', 1)
                        ->where('item_no1', $request->excess_item_no1_del)
                        ->delete();
            }

            DB::commit();

            return [
                'status' => 1
            ];

        }catch (\Throwable $e) {
            
            DB::rollback();

            return [
                'status' => -1
            ];

        }

        return [
            'status' => -1
        ];
    }

    function get_opencover_premium(Request $request){

        $sum_insured = (float)str_replace(',','',$request->get('sum_insured'));
        $mode_air = $request->get('mode_air');
        $mode_sea = $request->get('mode_sea');
        $mode_rail = $request->get('mode_rail');
        $mode_road = $request->get('mode_road');

        $air_rate = $request->get('air_rate');
        $sea_rate = $request->get('sea_rate');
        $road_rail_rate = $request->get('roadrail_rate');

        $marine_service = new MarinePremService(/*$mode_air, $mode_sea, $mode_rail, $mode_road*/);

        $val =   $marine_service->setSumInsured($sum_insured)
                                //->setIcc($cover_mode)
                                //->setWar($war)
                                //->setSrcc($srcc)
                                ->setModeAir($mode_air)
                                ->setModeSea($mode_sea)
                                ->setModeRail($mode_rail)
                                ->setModeRoad($mode_road)
                                ->setAirRate($air_rate)
                                ->setSeaRate($sea_rate)
                                ->setRoadRailRate($road_rail_rate)
                                ->fetch_marine_details();
                               
        return $val;

    }

    function debit_opencover_utilization($endt_renewal_no,$user_name,$pre_debit) {
        
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $master_rec = Marinemasterhist::where('endt_renewal_no', $dcontrol->master_endt_no)->first();
        $dtran0 = Dtran0::All()->first();
        // DB::beginTransaction();
        
        
        try{
            if($master_rec->opentype_code == 'UTL'){

                $MarineUTLDebitSum_SNAP = MarineUTLDebitSum_SNAP::where('endt_renewal_no', $endt_renewal_no)
                ->delete();

                if($pre_debit =='Y' or $pre_debit =='P'){
                   
                    $madtlsum = Madtl::selectRaw('SUM(nvl(sum_insured,0)) as sum_insured, SUM(nvl(endorse_amount,0)) as endorse_amount, SUM(nvl(total_premium,0)) as total_premium')
                    ->where('endt_renewal_no', $endt_renewal_no)
                    ->first();

                        $sum_insured = $madtlsum->sum_insured;
                        $total_premium = $madtlsum->total_premium;
                        $endorse_amount = $madtlsum->endorse_amount;
                
                        $marineutl = new MarineUTLDebitSum_SNAP;
                        $marineutl->policy_no = $dcontrol->policy_no;
                        $marineutl->endt_renewal_no = $dcontrol->endt_renewal_no;
                        $marineutl->master_endt_no = $dcontrol->master_endt_no;
                        $marineutl->master_policy = $dcontrol->master_policy;
                        $marineutl->sum_insured = $sum_insured * $dcontrol->currency_rate;
                        $marineutl->premium_amount = $total_premium * $dcontrol->currency_rate;
                        $marineutl->endorse_amount = $endorse_amount * $dcontrol->currency_rate;
                        $marineutl->account_year = $dtran0->account_year;
                        $marineutl->account_month = $dtran0->account_month;
                        $marineutl->created_date = Carbon::now();
                        $marineutl->created_time = Carbon::now();
                        $marineutl->created_by = $user_name;
                        $marineutl->foreign_sum_insured = $sum_insured;
                        $marineutl->foreign_premium = $total_premium;
                        $marineutl->foreign_endorse_amount = $endorse_amount;
                        $marineutl->currency_code = $dcontrol->currency;
                        $marineutl->currency_rate = $dcontrol->currency_rate;
                        $marineutl->effective_date = $dcontrol->effective_date;
                        $marineutl->period_from = $dcontrol->period_from;
                        $marineutl->period_to = $dcontrol->period_to;
                        $marineutl->opentype_code = $master_rec->opentype_code;
                        $marineutl->save();
                    
                }else{
                    $madtls = Madtl::where('endt_renewal_no',$endt_renewal_no)->where('endorse_amount','<>',0)->get();

                    foreach ($madtls as $madtl) {
                        $marineutl = new MarineUTLDebit;
                        $marineutl->policy_no = $madtl->policy_no; //main policy which doesn't change
                        $marineutl->endt_renewal_no = $madtl->endt_renewal_no; //endorsement of the certificate
                        $marineutl->master_endt_no = $dcontrol->master_endt_no; //endorsement of master which certificate is tied on, can be POL/REN/EXT.
                        $marineutl->master_policy = $dcontrol->master_policy;  //this should be either POL/REN so that utilization is done for all within the cover
                        $marineutl->certificate_no = $madtl->cert_no;
                        $marineutl->location = $madtl->location;
                        $marineutl->sum_insured = $madtl->sum_insured * $dcontrol->currency_rate;
                        $marineutl->premium_amount = $madtl->total_premium * $dcontrol->currency_rate;
                        $marineutl->endorse_amount = $madtl->endorse_amount * $dcontrol->currency_rate;
                        $marineutl->account_year = $dtran0->account_year;
                        $marineutl->account_month = $dtran0->account_month;
                        $marineutl->created_date = Carbon::now();
                        $marineutl->created_time = Carbon::now();
                        $marineutl->created_by = $user_name;
                        $marineutl->foreign_sum_insured = $madtl->sum_insured;
                        $marineutl->foreign_premium = $madtl->total_premium;
                        $marineutl->foreign_endorse_amount = $madtl->endorse_amount;
                        $marineutl->currency_code = $dcontrol->currency;
                        $marineutl->currency_rate = $dcontrol->currency_rate;
                        $marineutl->effective_date = $madtl->date_issue;
                        $marineutl->period_from = $madtl->period_from;
                        $marineutl->period_to = $madtl->period_to;
                        $marineutl->utilization_serial_no = $dtran0->marine_utilization_serial;
                        $marineutl->applied_rate = $madtl->final_prem_rate;
                        $marineutl->opentype_code = $master_rec->opentype_code;
                        $marineutl->group_code = $madtl->group_code;
                        $marineutl->save();

                        $Marinemasterpolsched_count = Marinemasterpolsched::where('policy_no',$madtl->policy_no)->where('location',$madtl->location)->count();
                        
                        if($Marinemasterpolsched_count > 0){

                            $marinepolsched = Marinemasterpolsched::where('policy_no',$madtl->policy_no)->where('location',$madtl->location)->update([
                                                            'endt_renewal_no' => $madtl->endt_renewal_no, //endorsement of the certificate
                                                            'master_endt_no' => $dcontrol->master_endt_no, //endorsement of master which certificate is tied on, can be POL/REN/EXT.
                                                            'master_policy' => $dcontrol->master_policy,  //this should be either POL/REN so that utilization is done for all within the cover
                                                            'certificate_no' => $madtl->cert_no,
                                                            'sum_insured' => $madtl->sum_insured * $dcontrol->currency_rate,
                                                            'premium_amount' => $madtl->total_premium * $dcontrol->currency_rate,
                                                            'endorse_amount' => $madtl->endorse_amount * $dcontrol->currency_rate,
                                                            'account_year' => $dtran0->account_year,
                                                            'account_month' => $dtran0->account_month,
                                                            'created_date' => Carbon::now(),
                                                            'created_time' => Carbon::now(),
                                                            'created_by' => $user_name,
                                                            'foreign_sum_insured' => $madtl->sum_insured,
                                                            'foreign_premium' => $madtl->total_premium,
                                                            'foreign_endorse_amount' => $madtl->endorse_amount,
                                                            'currency_code' => $dcontrol->currency,
                                                            'currency_rate' => $dcontrol->currency_rate,
                                                            'effective_date' => $madtl->date_issue,
                                                            'period_from' => $madtl->period_from,
                                                            'period_to' => $madtl->period_to,
                                                            'utilization_serial_no' => $dtran0->marine_utilization_serial,
                                                            'applied_rate' => $madtl->final_prem_rate,
                                                            'opentype_code' => $master_rec->opentype_code,
                                                            'group_code' => $madtl->group_code,
                                                    ]);


                        }else{

                                $marinepolsched = new Marinemasterpolsched;
                                $marinepolsched->policy_no = $madtl->policy_no; //main policy which doesn't change
                                $marinepolsched->endt_renewal_no = $madtl->endt_renewal_no; //endorsement of the certificate
                                $marinepolsched->master_endt_no = $dcontrol->master_endt_no; //endorsement of master which certificate is tied on, can be POL/REN/EXT.
                                $marinepolsched->master_policy = $dcontrol->master_policy;  //this should be either POL/REN so that utilization is done for all within the cover
                                $marinepolsched->certificate_no = $madtl->cert_no;
                                $marinepolsched->location = $madtl->location;
                                $marinepolsched->sum_insured = $madtl->sum_insured * $dcontrol->currency_rate;
                                $marinepolsched->premium_amount = $madtl->total_premium * $dcontrol->currency_rate;
                                $marinepolsched->endorse_amount = $madtl->endorse_amount * $dcontrol->currency_rate;
                                $marinepolsched->account_year = $dtran0->account_year;
                                $marinepolsched->account_month = $dtran0->account_month;
                                $marinepolsched->created_date = Carbon::now();
                                $marinepolsched->created_time = Carbon::now();
                                $marinepolsched->created_by = $user_name;
                                $marinepolsched->foreign_sum_insured = $madtl->sum_insured;
                                $marinepolsched->foreign_premium = $madtl->total_premium;
                                $marinepolsched->foreign_endorse_amount = $madtl->endorse_amount;
                                $marinepolsched->currency_code = $dcontrol->currency;
                                $marinepolsched->currency_rate = $dcontrol->currency_rate;
                                $marinepolsched->effective_date = $madtl->date_issue;
                                $marinepolsched->period_from = $madtl->period_from;
                                $marinepolsched->period_to = $madtl->period_to;
                                $marinepolsched->utilization_serial_no = $dtran0->marine_utilization_serial;
                                $marinepolsched->applied_rate = $madtl->final_prem_rate;
                                $marinepolsched->opentype_code = $master_rec->opentype_code;
                                $marinepolsched->group_code = $madtl->group_code;
                                $marinepolsched->save();

                        }

                        $utilised_sum = MarineUTLDebit::where('master_policy',$dcontrol->master_policy)->where('group_code', $madtl->group_code)->sum('foreign_sum_insured');
                        $utilised_prem = MarineUTLDebit::where('master_policy',$dcontrol->master_policy)->where('group_code', $madtl->group_code)->sum('foreign_endorse_amount');

                        DeclareOpenCargo::where('endt_renewal_no',$master_rec->endt_renewal_no)->where('group_code',$madtl->group_code)->update([
                            'utilised_annual_limit' => $utilised_sum,
                            'utilised_annual_premium' => $utilised_prem,
                        ]);

                    }

                    $madtlsum = Madtl::selectRaw('SUM(nvl(sum_insured,0)) as sum_insured, SUM(nvl(endorse_amount,0)) as endorse_amount, SUM(nvl(total_premium,0)) as total_premium')
                                    ->where('endt_renewal_no', $endt_renewal_no)
                                    ->first();

                    $marineutl = new MarineUTLDebitmast;
                    $marineutl->policy_no = $dcontrol->policy_no; //main policy which doesn't change
                    $marineutl->endt_renewal_no = $dcontrol->endt_renewal_no; //endorsement of the certificate
                    $marineutl->master_endt_no = $dcontrol->master_endt_no; //endorsement of master which certificate is tied on, can be POL/REN/EXT.
                    $marineutl->master_policy = $dcontrol->master_policy;  //this should be either POL/REN so that utilization is done for all within the cover
                    $marineutl->sum_insured = $madtlsum->sum_insured * $dcontrol->currency_rate;
                    $marineutl->premium_amount = $madtlsum->total_premium * $dcontrol->currency_rate;
                    $marineutl->endorse_amount = $madtlsum->endorse_amount * $dcontrol->currency_rate;
                    $marineutl->account_year = $dtran0->account_year;
                    $marineutl->account_month = $dtran0->account_month;
                    $marineutl->created_date = Carbon::now();
                    $marineutl->created_time = Carbon::now();
                    $marineutl->created_by = $user_name;
                    $marineutl->foreign_sum_insured = $madtlsum->sum_insured;
                    $marineutl->foreign_premium = $madtlsum->total_premium;
                    $marineutl->foreign_endorse_amount = $madtlsum->endorse_amount;
                    $marineutl->currency_code = $dcontrol->currency;
                    $marineutl->currency_rate = $dcontrol->currency_rate;
                    $marineutl->effective_date = $dcontrol->effective_date;
                    $marineutl->period_from = $dcontrol->period_from;
                    $marineutl->period_to = $dcontrol->period_to;
                    $marineutl->utilization_serial_no = $dtran0->marine_utilization_serial;
                    // $marineutl->applied_rate = $madtl->final_prem_rate;
                    $marineutl->opentype_code = $master_rec->opentype_code;
                    $marineutl->save();

                    //Update utilization to marinemasterhist where master policy in dcontrol is same. 
                    // Master policy in dcontrol should be either POL or REN
                    $utilised_sum = MarineUTLDebit::where('master_policy',$dcontrol->master_policy)->sum('foreign_sum_insured');
                    $utilised_prem = MarineUTLDebit::where('master_policy',$dcontrol->master_policy)->sum('foreign_endorse_amount');

                    $upd_utilise_hist = Marinemasterhist::where('endt_renewal_no', $dcontrol->master_endt_no)->update([
                        'utilised_annual_limit' => (int)$utilised_sum,
                        'utilised_annual_premium' => (int)$utilised_prem,
                    ]);

                    $upd_utilise_pol = Marinemasterpol::where('policy_no', $dcontrol->master_policy)->update([
                        'utilised_annual_limit' => (int)$utilised_sum,
                        'utilised_annual_premium' => (int)$utilised_prem,
                    ]);

                    //update marine_utilization_serial to next serial by adding 1
                    $dtran0 = Dtran0::where('rec_no', 0)->increment('marine_utilization_serial', (int) '1');

                }
            }

            $this->update_master_on_commit($endt_renewal_no);

            return(object)[
                "status"=>1,
                "resp"=>$marineutl,
                "msg"=>"Debit Success"
            ];

            // DB::commit();

        }catch (\Throwable $e) {
           
            // DB::rollback();
         
            throw $e;
        }
    }
    /******************/

    function update_marine_polsched($endt_renewal_no){

        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $master_rec = Marinemasterhist::where('endt_renewal_no', $dcontrol->master_endt_no)->first();

        $madtls = Madtl::where('endt_renewal_no',$endt_renewal_no)->where('endorse_amount','<>',0)->get();

        foreach ($madtls as $madtl) {

            $Marinemasterpolsched_count = Marinemasterpolsched::where('policy_no',$madtl->policy_no)->where('location',$madtl->location)->count();
                       
            if($Marinemasterpolsched_count > 0){

                $marinepolsched = Marinemasterpolsched::where('policy_no',$madtl->policy_no)->where('location',$madtl->location)
                                                      ->update([
                                                            'endt_renewal_no' => $madtl->endt_renewal_no, //endorsement of the certificate
                                                            'master_endt_no' => $dcontrol->master_endt_no, //endorsement of master which certificate is tied on, can be POL/REN/EXT.
                                                            'master_policy' => $dcontrol->master_policy,  //this should be either POL/REN so that utilization is done for all within the cover
                                                            'certificate_no' => $madtl->cert_no,
                                                            'sum_insured' => $madtl->sum_insured * $dcontrol->currency_rate,
                                                            'premium_amount' => $madtl->total_premium * $dcontrol->currency_rate,
                                                            'endorse_amount' => $madtl->endorse_amount * $dcontrol->currency_rate,
                                                            'account_year' => $dtran0->account_year,
                                                            'account_month' => $dtran0->account_month,
                                                            'created_date' => Carbon::now(),
                                                            'created_time' => Carbon::now(),
                                                            'created_by' => $user_name,
                                                            'foreign_sum_insured' => $madtl->sum_insured,
                                                            'foreign_premium' => $madtl->total_premium,
                                                            'foreign_endorse_amount' => $madtl->endorse_amount,
                                                            'currency_code' => $dcontrol->currency,
                                                            'currency_rate' => $dcontrol->currency_rate,
                                                            'effective_date' => $madtl->date_issue,
                                                            'period_from' => $madtl->period_from,
                                                            'period_to' => $madtl->period_to,
                                                            'utilization_serial_no' => $dtran0->marine_utilization_serial,
                                                            'applied_rate' => $madtl->final_prem_rate,
                                                            'opentype_code' => $master_rec->opentype_code,
                                                            'group_code' => $madtl->group_code,
                                                        ]);


            }else{
               
                    $marinepolsched = new Marinemasterpolsched;
                    $marinepolsched->policy_no = $madtl->policy_no; //main policy which doesn't change
                    $marinepolsched->endt_renewal_no = $madtl->endt_renewal_no; //endorsement of the certificate
                    $marinepolsched->master_endt_no = $dcontrol->master_endt_no; //endorsement of master which certificate is tied on, can be POL/REN/EXT.
                    $marinepolsched->master_policy = $dcontrol->master_policy;  //this should be either POL/REN so that utilization is done for all within the cover
                    $marinepolsched->certificate_no = $madtl->cert_no;
                    $marinepolsched->location = $madtl->location;
                    $marinepolsched->sum_insured = $madtl->sum_insured * $dcontrol->currency_rate;
                    $marinepolsched->premium_amount = $madtl->total_premium * $dcontrol->currency_rate;
                    $marinepolsched->endorse_amount = $madtl->endorse_amount * $dcontrol->currency_rate;
                    $marinepolsched->account_year = $dtran0->account_year;
                    $marinepolsched->account_month = $dtran0->account_month;
                    $marinepolsched->created_date = Carbon::now();
                    $marinepolsched->created_time = Carbon::now();
                    $marinepolsched->created_by = $user_name;
                    $marinepolsched->foreign_sum_insured = $madtl->sum_insured;
                    $marinepolsched->foreign_premium = $madtl->total_premium;
                    $marinepolsched->foreign_endorse_amount = $madtl->endorse_amount;
                    $marinepolsched->currency_code = $dcontrol->currency;
                    $marinepolsched->currency_rate = $dcontrol->currency_rate;
                    $marinepolsched->effective_date = $madtl->date_issue;
                    $marinepolsched->period_from = $madtl->period_from;
                    $marinepolsched->period_to = $madtl->period_to;
                    $marinepolsched->utilization_serial_no = $dtran0->marine_utilization_serial;
                    $marinepolsched->applied_rate = $madtl->final_prem_rate;
                    $marinepolsched->opentype_code = $master_rec->opentype_code;
                    $marinepolsched->group_code = $madtl->group_code;

                    $marinepolsched->save();

            }

        }

    }

    /***Check Form Purpose (create/ update) ***/ 
    function check_form_purpose(Request $request){
        if($request->form_purpose =='create'){

            $this->process_cargotypes($request);

        }
        
        elseif($request->form_purpose =='update'){

            $this->amend_cargotypes($request);
                          
        }

        return redirect()->back();
    }
    /*** POST OPEN COVER CARGO TYPES ***/
    function process_cargotypes(Request $request){

        $marine = Marinemasterhist::where('endt_renewal_no', $request->input('endt_renewal_no'))->first();

        $risk_obj = new Risk;

        $opencover_type = $marine->opentype_code;
        $dec_limits_basis = $marine->limits_basis;
        $group_code = $request->input('group_code');
        $endt_renewal_no = $request->input('endt_renewal_no');

        $total_annual_limit = 0;
        $total_limit_per_transit = 0;
        $total_premium = 0;

        $eachArray = array();
        $finalArray = array();

        $validate = Validator::make($request->all(), [
            'policy_no'                  => 'required',
            'endt_renewal_no'            => 'required',
            'group_code.*'               => 'required',
            'total_annual_transit_limit' => 'required',
            'final_rate'                 => [$dec_limits_basis == 'CARGO' && $opencover_type == 'UTL' ? 'required' : 'nullable'],
            'total_annual_premium'       => [$dec_limits_basis == 'COVER' && $opencover_type == 'UTL' ? 'required' : 'nullable'],
            'annual_transit_limit.*'     => [$dec_limits_basis == 'CARGO' ? 'required' : 'nullable'],
            'air.*'                      => [$opencover_type == 'UTL' ? 'required_without:sea.*' : 'nullable'],
            'sea.*'                      => [$opencover_type == 'UTL' ? 'required_without:air.*' : 'nullable'],
            'rail.*'                     => [$opencover_type == 'UTL' ? 'required_without:road.*' : 'nullable'],
            'road.*'                     => [$opencover_type == 'UTL' ? 'required_without:rail.*' : 'nullable'],
            'opencover_type'             => [$opencover_type == 'UTL' ? 'required' : 'nullable'],
            'limit_per_transit.*'        => [$opencover_type == 'UTL' ? 'required' : 'nullable'],
            'premium.*'                  => [$opencover_type == 'UTL' ? 'required' : 'nullable'],
        ]);

        if ($validate->fails()){
            return redirect()->back()->with('error', 'Failed to Capture cargo types. Confirm your data and try again.')->withErrors($validate);
        }

        DB::beginTransaction();

        try{

            if(isset($group_code)){

                $final_rate = (float)$request->input('final_rate');

                //$declare_cargo_sum = DeclareOpenCargo::where('endt_renewal_no', $endt_renewal_no)->where('deleted', 'N')->sum([''])
                $declare_cargo_sum = DeclareOpenCargo::selectRaw('SUM(expected_annual_limit) AS existing_annual_limit, SUM(annual_premium) AS existing_annual_premium')
                                                      ->where('endt_renewal_no', $endt_renewal_no)->where('deleted', 'N')
                                                      ->first();
                if($marine->limits_basis == 'COVER'){
                    $total_annual_limit      = (float)$marine->expected_annual_limit;
                    $total_limit_per_transit = (float)$marine->transit_limit_amt;
                    $expected_annual_limit   =  (float)$marine->expected_annual_limit;
                    $limit_per_transit       = (float)$marine->transit_limit_amt;
                    $total_premium           = (float)str_replace(',','', $request->input('total_annual_premium'));
                }

                for($i=0; $i <count($group_code) ; $i++){


                    if($marine->limits_basis == 'CARGO'){
                        $expected_annual_limit = (float)str_replace(',','', $request->input('annual_transit_limit')[$i]);
                        $limit_per_transit = (float)str_replace(',','', $request->input('limit_per_transit')[$i]);
                    }
                    
                    $premium = (float)str_replace(',','', $request->input('premium')[$i]);

                    if(!in_array($marine->trans_type,['POL','REN','RNS'])){

                        $endorse_amount = $risk_obj->prorate($marine->endt_renewal_no, $premium);

                    }else{
                        $endorse_amount = $premium;
                    }

                    $eachArray = [
                                    'policy_no' => $request->input('policy_no'),
                                    'endt_renewal_no' => $request->input('endt_renewal_no'),
                                    'group_code' => trim($request->input('group_code')[$i]),
                                    'mode_air' =>  $request->input('mode_air')[$i],
                                    'mode_sea' =>  $request->input('mode_sea')[$i],
                                    'mode_rail' => $request->input('mode_rail')[$i],
                                    'mode_road' => $request->input('mode_road')[$i],
                                    'air_rate' => $request->input('air_rate')[$i],
                                    'sea_rate' => $request->input('sea_rate')[$i],
                                    'roadrail_rate' => $request->input('roadrail_rate')[$i],
                                    'expected_annual_limit' => ($marine->limits_basis == 'CARGO') ? $expected_annual_limit : 0,
                                    'limit_per_transit'     => ($marine->limits_basis == 'CARGO') ? $limit_per_transit : 0,
                                    'annual_premium'        => $premium,
                                    'endorse_amount'        => ($marine->limits_basis == 'COVER') ? 0 : $endorse_amount,
                                    'created_by'            => Auth::user()->user_name,
                                    'created_on'            => Carbon::now(),
                                    'updated_by'            => Auth::user()->user_name,
                                    'updated_on'            => Carbon::now()
                                ];

                    array_push($finalArray, $eachArray);
                    
                    $total_annual_limit += $expected_annual_limit;
                    $total_limit_per_transit += $limit_per_transit;
                    $total_premium += $premium;
                    
                }

                $insrt = DeclareOpenCargo::insert($finalArray);
            }

            if($marine->limits_basis == 'COVER'){

                $declare_cargo_cover = DeclareOpenCargo::selectRaw('MAX(annual_premium) AS max_annual_premium, max(GREATEST(AIR_RATE,SEA_RATE,ROADRAIL_RATE)) as max_rate')
                                                        ->where('endt_renewal_no', $endt_renewal_no)->where('deleted', 'N')
                                                        ->first();

                $total_annual_limit = $marine->expected_annual_limit;
                $total_limit_per_transit = $marine->transit_limit_amt;
                $total_premium = $declare_cargo_cover->max_annual_premium;
                $final_rate = $declare_cargo_cover->max_rate;

                if(!in_array($marine->trans_type,['POL','REN','RNS'])){

                    $endorse_amount = $risk_obj->prorate($marine->endt_renewal_no, $total_premium);

                }else{
                    $endorse_amount = $total_premium;
                }

            }else{

                $declare_cargo_sum = DeclareOpenCargo::selectRaw('sum(GREATEST(AIR_RATE,SEA_RATE,ROADRAIL_RATE)) as sum_rate, count(*) as count_rate')
                                                    ->where('endt_renewal_no', $endt_renewal_no)->where('deleted', 'N')
                                                    ->first();

                $total_premium = $total_premium + $declare_cargo_sum->existing_annual_premium;
                $total_annual_limit = $total_annual_limit + $declare_cargo_sum->existing_annual_limit;
                $final_rate = ((float)$declare_cargo_sum->sum_rate / (float)$declare_cargo_sum->count_rate);
                $endorse_amount = $total_premium;

                if(!in_array($marine->trans_type,['POL','REN','RNS'])){

                    $endorse_amount = $risk_obj->prorate($marine->endt_renewal_no, $total_premium);

                }else{
                    $endorse_amount = $total_premium;
                }

            }

            $dat =  [
                        'endt_renewal_no'    => $endt_renewal_no,
                        'total_annual_limit' => $total_annual_limit,
                        'total_premium'      => $total_premium,
                        'endorse_amount'     => $endorse_amount,
                        'limits_basis'       => $marine->limits_basis,
                        'final_rate'         => $final_rate
                    ];

            $this->update_master_tables($dat);

            DB::commit();

            return redirect()->route('marine_open_dtl',['endt_renewal_no'=> $endt_renewal_no])->with('success','Open Cover Policy Limits set successfully');
        }catch(Throwable $e){
            
            DB::rollback();

            return redirect()->back()->with('error', 'Failed to add cargo types. Confirm your data and try again.')->withErrors($validate);

        }
    }

    public function amend_cargotypes(Request $request){

        $marine = Marinemasterhist::where('endt_renewal_no', $request->input('endt_renewal_no'))->first();

        $opencover_type = $marine->opentype_code;
        $dec_limits_basis = $marine->limits_basis;

        $validate = Validator::make($request->all(), [
            'policy_no'                    => 'required',
            'endt_renewal_no'              => 'required',
            'group_code.*'                 => 'required',
            'total_annual_transit_limit'   => 'required',
            'final_rate'                   => 'required',
            'total_annual_premium'         => [$dec_limits_basis == 'COVER' && $opencover_type == 'UTL' ? 'required' : 'nullable'],
            'annual_transit_limit.*'       => [$dec_limits_basis == 'CARGO' ? 'required' : 'nullable'],
            'air.*'                        => [$opencover_type   == 'UTL' ? 'required_without:sea.*' : 'nullable'],
            'sea.*'                        => [$opencover_type   == 'UTL' ? 'required_without:air.*' : 'nullable'],
            'rail.*'                       => [$opencover_type   == 'UTL' ? 'required_without:road.*' : 'nullable'],
            'road.*'                       => [$opencover_type   == 'UTL' ? 'required_without:rail.*' : 'nullable'],
            'opencover_type'               => [$opencover_type   == 'UTL' ? 'required' : 'nullable'],
            'limit_per_transit.*'          => [$opencover_type   == 'UTL' ? 'required' : 'nullable'],
            'premium.*'                    => [$opencover_type   == 'UTL' ? 'required' : 'nullable'],
        ]);
    
        if ($validate->fails()){
            return redirect()->back()
                ->with('error', 'Failed to update cargo types. Confirm your data and try again.')
                ->withErrors($validate);
        }

        DB::beginTransaction();

        try{

            $endt_renewal_no = $request->input('endt_renewal_no');
            $group_code = $request->input('group_code')[0];

            $risk_obj = new Risk;

            $total_annual_limit = 0;
            $limit_per_transit = 0;
            $annual_premium = 0;
            $endorse_amount = 0;
            $cover_premium = 0;

            $final_rate = (float)$request->input('final_rate');

            $curr_cargo_type = DeclareOpenCargo::where('endt_renewal_no', $endt_renewal_no)->where('group_code', $group_code)->first();

            $trans_type = $marinemaster->trans_type;

            if($dec_limits_basis == 'COVER'){

                $expected_annual_limit = 0;
                $limit_per_transit = 0;
                $premium = (float)str_replace(',', '', $request->input('premium')[0]);
                $cover_premium = (float)$marine->deposit_premium_amt;
                $new_cover_premium = $premium;

            }else{

                $expected_annual_limit = (float)str_replace(',', '', $request->input('annual_transit_limit')[0]);
                $limit_per_transit = (float)str_replace(',', '', $request->input('limit_per_transit')[0]);
                $premium = (float)str_replace(',', '', $request->input('premium')[0]);

            }

            $updateArray =  [
                                'mode_air' =>  $request->input('mode_air')[0],
                                'mode_sea' =>  $request->input('mode_sea')[0],
                                'mode_rail' => $request->input('mode_rail')[0],
                                'mode_road' => $request->input('mode_road')[0],
                                'air_rate' => $request->input('air_rate')[0],
                                'sea_rate' => $request->input('sea_rate')[0],
                                'roadrail_rate' => $request->input('roadrail_rate')[0],
                                'expected_annual_limit' => $expected_annual_limit,
                                'limit_per_transit' => $limit_per_transit,
                                'annual_premium'    => $premium,
                                'endorse_amount'    => 0,
                                'updated_by'        => Auth::user()->user_name,
                                'updated_on'        => Carbon::now()
                            ];
                
            switch($trans_type){
                case 'POL':
                case 'REN':
                case 'RNS':

                    $updateArray['endorse_amount'] = ($marine->limits_basis == 'COVER') ? 0 : $updateArray['annual_premium'];

                    break;

                default:

                    $prevEndt = Marinemasterhist::where('policy_no', $marinemaster->policy_no)
                                                ->whereNotIn('status', ['CNC','LPS','PND'])
                                                ->where('endt_renewal_no','<>',$endt_renewal_no)
                                                ->orderBy('transeq_no','desc')
                                                ->first();

                    //check if cargo exists in previous endorsement
                    $count_prevCargo = DeclareOpenCargo::where('endt_renewal_no', $prevEndt->endt_renewal_no)
                                                        ->where('group_code', $group_code)
                                                        ->where('deleted','N')
                                                        ->count();
                    
                    if($count_prevCargo == 0){
                        //Does not exist in previous endorsment hence delete it
                        $updateArray['endorse_amount'] = ($marine->limits_basis == 'COVER') ? 0 : $risk_obj->prorate($endt_renewal_no, $updateArray['annual_premium']);

                    }else{

                        //Exists in previous endorsement hence we need to find the refund amout
                        $prevCargo_rec = DeclareOpenCargo::where('endt_renewal_no', $prevEndt->endt_renewal_no)
                                                          ->where('group_code', $group_code)
                                                          ->where('deleted','N')
                                                          ->first();

                        $annual_premium = $updateArray['annual_premium'] - $prevCargo_rec->annual_premium;
                        $endorse_amount = $risk_obj->prorate($endt_renewal_no, $annual_premium);

                        $updateArray['endorse_amount'] = ($marine->limits_basis == 'COVER') ? 0 : $endorse_amount;

                    }

                    break;
            }

            $update=DeclareOpenCargo::where('endt_renewal_no', $endt_renewal_no)
                                    ->where('group_code', trim($group_code))
                                    ->update($updateArray);

            $declare_cargo_sum = DeclareOpenCargo::selectRaw('SUM(expected_annual_limit) AS existing_annual_limit, sum(limit_per_transit) as existing_limit_per_transit, SUM(annual_premium) AS existing_annual_premium,sum(GREATEST(AIR_RATE,SEA_RATE,ROADRAIL_RATE)) as sum_rate,count(*) as count_rate')
                                                        ->where('endt_renewal_no', $endt_renewal_no)->where('deleted', 'N')
                                                        ->first();

            $endorse_amount = DeclareOpenCargo::where('endt_renewal_no', $endt_renewal_no)->sum('endorse_amount');

            if($marine->limits_basis == 'COVER'){

                $declare_cargo_cover = DeclareOpenCargo::selectRaw('MAX(annual_premium) AS max_annual_premium,MAX(endorse_amount) as max_endorse_amount, max(GREATEST(AIR_RATE,SEA_RATE,ROADRAIL_RATE)) as max_rate')
                                                        ->where('endt_renewal_no', $endt_renewal_no)->where('deleted', 'N')
                                                        ->first();

                $total_annual_limit = $marine->expected_annual_limit;
                $limit_per_transit = $marine->transit_limit_amt;
                $new_cover_premium = (float)$declare_cargo_cover->max_annual_premium;
                $final_rate = (float)$declare_cargo_cover->max_rate;

                if((float)$prevEndt->annual_premium < $new_cover_premium){
                    $annual_premium = $new_cover_premium;
                    $endt_amt = $annual_premium - (float)$prevEndt->annual_premium;
                    $endorse_amount = $risk_obj->prorate($endt_renewal_no, $endt_amt);
                }
                else{
                    $annual_premium = $marine->deposit_premium_amt;
                    $endorse_amount = 0;
                }

            }else{

                $total_annual_limit = $declare_cargo_sum->existing_annual_limit;
                $limit_per_transit = $declare_cargo_sum->existing_limit_per_transit;
                $annual_premium = $declare_cargo_sum->existing_annual_premium;
                $final_rate = ((float)$declare_cargo_sum->sum_rate / (float)$declare_cargo_sum->count_rate);

            }

            $dat =  [
                        'endt_renewal_no'         => $endt_renewal_no,
                        'total_annual_limit'      => $total_annual_limit,
                        'total_premium'           => $annual_premium,
                        'total_limit_per_transit' => $limit_per_transit,
                        'endorse_amount'          => $endorse_amount,
                        'opencover_type'          => $marinemaster->opentype_code,
                        'limits_basis'            => $marinemaster->limits_basis,
                        'final_rate'              => $final_rate
                    ];

            $this->update_master_tables($dat);

            DB::commit();

            return redirect()->route('marine_open_dtl', ['endt_renewal_no' => $endt_renewal_no])
                ->with('success', 'Cargo type updated successfully');
        } catch(\Throwable $e){
            DB::rollback();
            
            return redirect()->back()
                ->with('error', 'Failed to update cargo type. Confirm your data and try again.')
                ->withErrors($validate);
        }

    }

    public function delete_opcargotype(Request $request){

        $validate = Validator::make($request->all(), [
            'endt_renewal_no' => 'required',
            'group_code' => 'required'
        ]);
    
        if ($validate->fails()){
            return redirect()->back()
                ->with('error', 'Failed to delete cargo type. Confirm your data and try again.')
                ->withErrors($validate);
        }

        DB::beginTransaction();

        try{
            $endt_renewal_no = $request->input('endt_renewal_no');
            $group_code = $request->input('group_code');
            $final_rate = 0;

            $risk_obj = new Risk;

            $total_annual_limit = 0;
            $limit_per_transit = 0;
            $annual_premium = 0;
            $endorse_amount = 0;

            //current endorsement
            $marinemaster = Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)->first();

            $curr_cargo_type = DeclareOpenCargo::where('endt_renewal_no', $endt_renewal_no)->where('group_code', $group_code)->first();

            $trans_type = $marinemaster->trans_type;

            switch($trans_type){
                case 'POL':
                case 'REN':
                case 'RNS':

                    $declare_cargo_sum = DeclareOpenCargo::where('endt_renewal_no', $endt_renewal_no)->where('group_code', $group_code)->delete();

                    break;

                default:

                    //check if cargo exists in previous endorsement
                    $prevEndt = Marinemasterhist::where('policy_no', $marinemaster->policy_no)
                                                ->whereNotIn('status', ['CNC','LPS'])
                                                ->where('endt_renewal_no','<>',$endt_renewal_no)
                                                ->orderBy('transeq_no','desc')
                                                ->first();

                    $count_prevCargo = DeclareOpenCargo::where('endt_renewal_no', $prevEndt->endt_renewal_no)
                                                        ->where('group_code', $group_code)
                                                        ->where('deleted','N')
                                                        ->count();
                    
                    if($count_prevCargo == 0){
                        //Does not exist in previous endorsment hence delete it
                        $del_prevCargo = DeclareOpenCargo::where('endt_renewal_no', $endt_renewal_no)
                                                        ->where('group_code', $group_code)
                                                        ->delete();

                    }else{

                        //Exists in previous endorsement hence we need to find the refund amout
                        $prevCargo_rec = DeclareOpenCargo::where('endt_renewal_no', $prevEndt->endt_renewal_no)
                                                        ->where('group_code', $group_code)
                                                        ->where('deleted','N')
                                                        ->first();

                        $annual_premium = $curr_cargo_type->annual_premium;
                        $endorse_amount = $risk_obj->prorate($endt_renewal_no, $annual_premium) * -1;

                        $del_prevCargo = DeclareOpenCargo::where('endt_renewal_no', $endt_renewal_no)
                                                        ->where('group_code', $group_code)
                                                        ->update([
                                                            'deleted'=> 'Y',
                                                            'endorse_amount'   => $endorse_amount,
                                                            'updated_by'       => Auth::user()->user_name,
                                                            'updated_on'       => Carbon::now()
                                                        ]);

                    }

                    break;
            }

            if($marinemaster->limits_basis == 'COVER'){

                $declare_cargo_cover = DeclareOpenCargo::selectRaw('MAX(annual_premium) AS max_annual_premium,MAX(endorse_amount) as max_endorse_amount,max(GREATEST(AIR_RATE,SEA_RATE,ROADRAIL_RATE)) as max_rate')
                                                        ->where('endt_renewal_no', $endt_renewal_no)->where('deleted', 'N')
                                                        ->first();

                $total_annual_limit = $marinemaster->expected_annual_limit;
                $limit_per_transit = $marinemaster->transit_limit_amt;
                $new_cover_premium = (float)$declare_cargo_cover->max_annual_premium;
                $final_rate = (float)$declare_cargo_cover->max_rate;

                if((float)$prevEndt->annual_premium < $new_cover_premium){
                    $annual_premium = $new_cover_premium;
                    $endt_amt = $annual_premium - (float)$prevEndt->annual_premium;
                    $endorse_amount = ($endt_amt != 0) ? $risk_obj->prorate($endt_renewal_no, $endt_amt) : 0;
                }
                else{
                    $annual_premium = $marine->deposit_premium_amt;
                    $endorse_amount = 0;
                }

            }else{

                $declare_cargo_sum = DeclareOpenCargo::selectRaw('SUM(expected_annual_limit) AS existing_annual_limit, sum(limit_per_transit) as existing_limit_per_transit, SUM(annual_premium) AS existing_annual_premium,sum(GREATEST(AIR_RATE,SEA_RATE,ROADRAIL_RATE)) as sum_rate,count(*) as count_rate')
                                                        ->where('endt_renewal_no', $endt_renewal_no)->where('deleted', 'N')
                                                        ->first();

                $endorse_amount = DeclareOpenCargo::where('endt_renewal_no', $endt_renewal_no)->sum('endorse_amount');

                $total_annual_limit = $declare_cargo_sum->existing_annual_limit;
                $limit_per_transit = $declare_cargo_sum->existing_limit_per_transit;
                $annual_premium = $declare_cargo_sum->existing_annual_premium;
                $final_rate = ((float)$declare_cargo_sum->sum_rate / (float)$declare_cargo_sum->count_rate);
            }

            $dat = [
                'endt_renewal_no'         => $endt_renewal_no,
                'total_annual_limit'      => $total_annual_limit,
                'total_premium'           => $annual_premium,
                'total_limit_per_transit' => $limit_per_transit,
                'endorse_amount'          => $endorse_amount,
                'opencover_type'          => $marinemaster->opentype_code,
                'limits_basis'            => $marinemaster->limits_basis,
                'final_rate'              => $final_rate
            ];

            $this->update_master_tables($dat);

            DB::commit();

            return redirect()->route('marine_open_dtl', ['endt_renewal_no' => $endt_renewal_no])
                ->with('success', 'Cargo type deleted successfully');
        } catch(\Throwable $e){
            DB::rollback();

            return redirect()->back()
                ->with('error', 'Failed to delete cargo type. Confirm your data and try again.')
                ->withErrors($validate);
        }

    }

    public function update_master_tables($dat){

        if($dat['limits_basis'] == 'CARGO'){

            $upd =  [
                        'annual_transit_limit' => ($dat['total_annual_limit'] > 0) ? 'Y' : 'N',
                        'expected_annual_limit' => $dat['total_annual_limit'],
                        'transit_limit' => ($dat['total_limit_per_transit'] > 0) ? 'Y' : 'N',
                        'transit_limit_amt' => $dat['total_limit_per_transit'],
                        'deposit_premium' => ($dat['total_premium'] > 0) ? 'Y' : 'N',
                        'deposit_premium_amt' => $dat['total_premium'],
                        'endorse_amount' => $dat['endorse_amount'],
                        'prem_rate'      => $dat['final_rate']
                    ];

        }else{

            $upd =  [
                'deposit_premium'     => ($dat['total_premium'] > 0) ? 'Y' : 'N',
                'deposit_premium_amt' => $dat['total_premium'],
                'endorse_amount'      => $dat['endorse_amount'],
                'prem_rate'           => $dat['final_rate']
            ];

        }

        $upd_pol = Marinemasterpol::where('endt_renewal_no', $dat['endt_renewal_no'])->update($upd);

        $upd_hist = Marinemasterhist::where('endt_renewal_no', $dat['endt_renewal_no'])->update($upd);

        $upd_polmaster = Polmaster::where('endorse_no', $dat['endt_renewal_no'])
                                ->update([
                                    'total_sum_insured' => $dat['total_annual_limit'],
                                    'sum_insured' => $dat['total_annual_limit'],
                                    'annual_premium' => $dat['total_premium'],
                                    'renewal_premium' => $dat['total_premium'],
                                    'fire_premium' => $dat['total_premium'],
                                    'endorse_amount' => $dat['endorse_amount'],
                                    'sys_endorse_amount' => $dat['endorse_amount'],
                                ]);

        $upd_polmasterend = Polmasterend::where('endorse_no', $dat['endt_renewal_no'])
                                ->update([
                                    'total_sum_insured' => $dat['total_annual_limit'],
                                    'sum_insured' => $dat['total_annual_limit'],
                                    'annual_premium' => $dat['total_premium'],
                                    'renewal_premium' => $dat['total_premium'],
                                    'fire_premium' => $dat['total_premium'],
                                    'endorse_amount' => $dat['endorse_amount'],
                                    'sys_endorse_amount' => $dat['endorse_amount'],
                                ]);

        $this->update_trans_type($dat['endt_renewal_no']);

    }

    public function update_trans_type($endt_renewal_no){

        $upd_pol = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->first();

        if($upd_pol->trans_type == 'EXT' && $upd_pol->endorse_amount < 0){

            Dcontrol::where('endt_renewal_no', $endt_renewal_no)->update(['trans_type' => 'RFN','ext_to_rfn' => 'Y', 'doc_type' => 'CRN']);
            POlmaster::where('endorse_no', $endt_renewal_no)->update(['trans_type' => 'RFN']);
            Polmasterend::where('endorse_no', $endt_renewal_no)->update(['trans_type' => 'RFN']);
            Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->update(['trans_type' => 'RFN']);
            Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)->update(['trans_type' => 'RFN']);


        }
        else if($upd_pol->trans_type == 'RFN' && $upd_pol->endorse_amount > 0){

            Dcontrol::where('endt_renewal_no', $endt_renewal_no)->update(['trans_type' => 'EXT','ext_to_rfn' => 'N', 'doc_type' => 'DRN']);
            POlmaster::where('endorse_no', $endt_renewal_no)->update(['trans_type' => 'EXT']);
            Polmasterend::where('endorse_no', $endt_renewal_no)->update(['trans_type' => 'EXT']);
            Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)->update(['trans_type' => 'EXT']);
            Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)->update(['trans_type' => 'EXT']);

        }

    }

    public function opencover_cargo_types(Request $request){

        $cargodeclare = DeclareOpenCargo::where('endt_renewal_no',$request->get('endt_renewal_no'));

        return datatables::of($cargodeclare)

            ->addColumn('view', function ($cargodeclare) {

                return '<a class="btn btn-xs btn-edit" id="edit-cargodeclare" data-policy_no="'.$cargodeclare->policy_no.'" data-endt_renewal_no="'.$cargodeclare->endt_renewal_no.'" data-group_code="'.$cargodeclare->group_code.'">
                    <i class="glyphicon glyphicon-edit"></i>
                </a>
                <a class="btn btn-sm-default" id="delete-cargodeclare" style="padding: 0; margin-left: 10px;color:red;">
                    <i class="fa fa-trash" title="View Risk Details"> </i>
                </a>';

            })
            ->addColumn('format_endt_renewal_no', function($cargodeclare){

                return formatPolicyOrClaim($cargodeclare->endt_renewal_no);
            })
            ->addColumn('cargo_descr', function ($cargodeclare) {
                $m = Marinemasterhist::where('endt_renewal_no', $cargodeclare->endt_renewal_no)->first();
                $descr = Margroup::where('group_code', $cargodeclare->group_code)->where('class', $m->class)->first();

                return $descr->description;
            })
            ->addColumn('opencover_basis', function ($cargodeclare) {
                $m = Marinemasterhist::where('endt_renewal_no', $cargodeclare->endt_renewal_no)->first()->opentype_code;

                return $m;
            })
            ->addColumn('status', function ($cargodeclare) {
                
                return ($cargodeclare->deleted == 'Y') ? 'Cancelled' : 'Active';

            })
            ->addColumn('conveyance_modes', function ($cargodeclare) {
                $arr = array();

                if($cargodeclare->mode_air == 'Y')  { array_push($arr,'Air'); }
                if($cargodeclare->mode_sea  =='Y')  { array_push($arr,'Sea'); }
                if($cargodeclare->mode_rail == 'Y') { array_push($arr,'Rail'); }
                if($cargodeclare->mode_road == 'Y') { array_push($arr,'Road'); }

                $commaSeparatedString = implode(',', $arr);
                
                return $commaSeparatedString;

            })
            ->addColumn('created_on_date', function($cargodeclare){

                return Carbon::parse($cargodeclare->created_on)->format('d/m/Y');

            })
            ->addColumn('created_by_name', function($cargodeclare){
                $nem = Aimsuser::where('user_name', $cargodeclare->created_on)->first();

                return $nem->name;

            })
            ->escapeColumns([])
            ->make(true);

    }
    /*** POST OPEN COVER CARGO TYPES ***/
    public function getCargoTypeDetails(Request $request)
    {
        $policy_no = $request->input('policy_no');
        $endt_renewal_no = $request->input('endt_renewal_no');
        $group_code = $request->input('group_code');

        // Retrieve the record based on the provided parameters
        $record = DeclareOpenCargo::where('policy_no', $policy_no)
            ->where('endt_renewal_no', $endt_renewal_no)
            ->where('group_code', $group_code)
            ->first();

        return $record;
    }

    public function replicate_cargotypes($policy_no, $new_endt_renewal_no) {

        $prevEndt = Marinemasterhist::where('policy_no',$policy_no)
                                    ->where('endt_renewal_no','<>',$new_endt_renewal_no)
                                    ->where('status','ACT')
                                    ->orderBy('transeq_no','desc')
                                    ->first();

        $declareopencargo = DeclareOpenCargo::where('endt_renewal_no', $prevEndt->endt_renewal_no)->where('deleted','N')->get();
        $today = Carbon::today();

        foreach ($declareopencargo as $value) {
            # Replicate declareopencargo
            $replicate = new DeclareOpenCargo;

            $replicate->policy_no = $value->policy_no;
            $replicate->endt_renewal_no = $new_endt_renewal_no;
            $replicate->group_code = $value->group_code;
            $replicate->mode_air = $value->mode_air;
            $replicate->mode_sea = $value->mode_sea;
            $replicate->mode_road = $value->mode_road;
            $replicate->mode_rail = $value->mode_rail;
            $replicate->air_rate = $value->air_rate;
            $replicate->sea_rate = $value->sea_rate;
            $replicate->roadrail_rate = $value->roadrail_rate;
            $replicate->expected_annual_limit = $value->expected_annual_limit;
            $replicate->limit_per_transit = $value->limit_per_transit;
            $replicate->annual_premium = $value->annual_premium;
            $replicate->endorse_amount = 0;
            $replicate->deleted = 'N';
            $replicate->created_by = auth()->user()->user_name;
            $replicate->created_on = $today;
            $replicate->updated_by = auth()->user()->user_name;
            $replicate->updated_on = $today;
            $replicate->utilised_annual_limit = $value->utilised_annual_limit;
            $replicate->utilised_annual_premium = $value->utilised_annual_premium;

            $replicate->save();

        }
    }

    public function endorse_cover_limitsdbt($endt_renewal_no){
        
        try{
            $risk_obj = new Risk;

            $master = Marinemasterhist::where('endt_renewal_no',$endt_renewal_no)->first();
            $mastercargo = DeclareOpenCargo::where('endt_renewal_no', $master->endt_renewal_no)->get();
            $mastercargo_count = DeclareOpenCargo::where('endt_renewal_no', $master->endt_renewal_no)->count();
            $prevEndt = $this->previous_endorsement($master->policy_no,$master->endt_renewal_no);

            $computedPrem = array();
            $premium = 0;
            $largestPrem = 0;
            $endorse_amount = 0;

            if($mastercargo_count > 0){
                foreach($mastercargo as $cargo){

                    $max_rate = max($cargo->air_rate, $cargo->sea_rate,$cargo->roadrail_rate);

                    $premium = ($master->expected_annual_limit * $max_rate) / 100;

                    array_push($computedPrem, $premium);

                    $upd = DeclareOpenCargo::where('endt_renewal_no', $cargo->endt_renewal_no)
                                            ->where('group_code', $cargo->group_code)
                                            ->update(['annual_premium' => $premium]);

                }

                $largestPrem = max($computedPrem);

                switch($marine->trans_type){
                    case 'POL':
                    case 'REN':
                    case 'RNS':

                        $endorse_amount = $largestPrem;

                        break;
                    default:
                   
                        $diff_prem = $largestPrem - $prevEndt->deposit_premium_amt;
                        $endorse_amount = $risk_obj->prorate($master->endt_renewal_no, $diff_prem);
                        
                        break;

                }
            }
            
            $dat =  [
                'endt_renewal_no'         => $master->endt_renewal_no,
                'total_annual_limit'      => $master->expected_annual_limit,
                'total_premium'           => $largestPrem,
                'total_limit_per_transit' => $master->transit_limit_amt,
                'endorse_amount'          => $endorse_amount,
                'opencover_type'          => $master->opentype_code,
                'limits_basis'            => $master->limits_basis,
                'final_rate'              => $master->prem_rate
            ];

            $this->update_master_tables($dat);

            // $updpol = Marinemasterpol::where('endt_renewal_no', $endt_renewal_no)
            //                          ->update(['deposit_premium_amt' => $largestPrem, 'endorse_amount' => $endorse_amount]);

            // $updhist = Marinemasterhist::where('endt_renewal_no', $endt_renewal_no)
            //                            ->update(['deposit_premium_amt' => $largestPrem, 'endorse_amount' => $endorse_amount]);

            //DB::commit();

            return 0;

        }
        catch(\Throwable $e){
           // DB::rollback();
            
            return 0;
            
        }

        
    }

    public function previous_endorsement($policy_no,$endt_renewal_no){

        $prevEndt = Marinemasterhist::where('policy_no', $policy_no)
                                    ->whereNotIn('status', ['CNC','LPS','PND'])
                                    ->where('endt_renewal_no','<>',$endt_renewal_no)
                                    ->orderBy('transeq_no','desc')
                                    ->first();

        return $prevEndt;
    }
    

}
