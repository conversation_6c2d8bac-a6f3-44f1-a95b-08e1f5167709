<?php
namespace App\Http\Controllers\gb\underwriting;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Identity_type;
use App\Pipcnam;


class IntergrationController extends Controller
{
    public function getPartnerData($partner_group,$partner_type,$partner_code){
        $partner_data = [];

        $country_id = 2;
        $sourceid = 1;
        $partytypeid = 3;
        $legalentitytype = 2;
        $phonenumber_main = "";
        $phonenumber_alternate = "";
        $emailaddress1 = "";
        $emailaddress2 = "";
        $firstname = "";
        $middlename = "";
        $surname = "";
        $companyname = "";
        $residenceordomicile_countrycode = "";
        $residenceordomicile_regionorcounty = "";
        $dateofbirth = "";
        $passport = "";
        $id_type = *********;
        $id_ke= 0;
        $id_ug ="";
        $id_tz = "";
        $id_ss = "";
        $id_rw = "";
        $pin_ke = "";
        $pin_ug = "";
        $pin_rw = "";
        $pin_ss = "";
        $pin_tz = "";
        $pin_ke = "";
        $gender = 3;
        $companytin = "";

        $country = DB::SELECT("select country_iso from pipcnam")[0]->country_iso;

        switch ($partner_group) {
            case 'cust':
                $client = DB::table('client')
                         ->where('client_number',$partner_code)
                         ->first();
                
                    $partytypeid = 1;
                    $legalentitytype = $partner_type == 'C' ? 2: 1 ;
                    $phonenumber_main = $client->telephone;
                    $phonenumber_alternate = $client->telephone;
                    $emailaddress1 = $client->e_mail;
                    $emailaddress2 = $client->e_mail;
                    $firstname = $partner_type == 'I' ? $client->first_name: "";
                    $middlename = $partner_type == 'I' ? $client->others:"";
                    $surname = $partner_type == 'I' ? $client->surname: "";
                    $companyname = $partner_type == 'C' ? $client->name: "" ;
                    $residenceordomicile_countrycode = $client->country;
                    $residenceordomicile_regionorcounty = "";
                    $dateofbirth = $client->date_of_birth;
                    $passport = $client->identity_type == 2 ? $client->id_number : "" ;
                    $id_type = $partner_type == 'I' ? $client->identity_type: *********;
                    $id_ke= $partner_type == 'I' ? $client->id_number: 0;
                    $pin_ke = $client->pin_number ? $client->pin_number: "";
                    $gender = $partner_type == 'I' ? $client->gender: 3;
                    $companytin = $partner_type == 'C' ? $client->pin_number: "";
                break;
            case 'agnt':

                $intermediary = DB::table('intermediary')
                         ->where('intermediary_number',$partner_code)
                         ->first();

                $partytypeid = 3;
                $legalentitytype = $partner_type == 'C' ? 2: 1 ;
                $phonenumber_main = $intermediary->cell_phone;
                $phonenumber_alternate = $intermediary->cell_phone;
                $emailaddress1 = $intermediary->email;
                $emailaddress2 = $intermediary->email;
                $firstname = $partner_type == 'I' ? $intermediary->first_name: "";
                $middlename = $partner_type == 'I' ? $intermediary->other_name:"";
                $surname = $partner_type == 'I' ? $intermediary->last_name: "";
                $companyname = $partner_type == 'C' ? $intermediary->name: "" ;
                $residenceordomicile_countrycode = $intermediary->country_code;
                $residenceordomicile_regionorcounty = "";
                $dateofbirth = $intermediary->dob;
                $passport = $intermediary->id_type == 2 ? $intermediary->identity_number : "" ;
                $id_type = $partner_type == 'I' ? $intermediary->identity_type: *********;
                $id_ke= $partner_type == 'I' ? $intermediary->identity_number: 0;
                $pin_ke = $intermediary->pin_number ? $intermediary->pin_number: "";
                $gender = $partner_type == 'I' ? $intermediary->gender: 3;
                $companytin = $partner_type == 'C' ? $intermediary->pin_number: "";
                
                break;
    

            case 'sp':

                $sp_rec = DB::table('clparam')
                            ->whereRaw("TRIM(claimant_code) = ?", [$partner_code])
                            ->get();
                
                
                if(count($sp_rec) < 1){
                    return 'Record not found in Clparam' ;
                }else{
                    $sp_rec=$sp_rec[0];

                }

                $checkpartnernumber = $sp_rec->partnernumber;
                if (!is_null($checkpartnernumber) && strlen(trim($checkpartnernumber)) > 1 ) {
                    $errormsg ='Partner number Already Exist for this record';
                    $updatepartner = DB::table('clparam')->whereRaw("TRIM(claimant_code) = ?", [$partner_code])
                                    ->update([
                                        'partnernumber_error'=>$errormsg
                                    ]);
         
                }else{
                    $legalentitytype = $partner_type == 'C' ? 2: 1 ;
                    $phonenumber_main = $sp_rec->addr5;
                    $phonenumber_alternate = $sp_rec->addr5;
                    $emailaddress1 = trim($sp_rec->e_mail);
                    $emailaddress2 = trim($sp_rec->e_mail);
                    $firstname = $partner_type == 'I' ? $sp_rec->name: "";
                    $companyname =  $partner_type == 'C' ? trim($sp_rec->name): "" ;
                    $residenceordomicile_countrycode = $country;
                    $residenceordomicile_regionorcounty = trim($sp_rec->addr2);
                    $dateofbirth = $sp_rec->dob;
                    $passport = $sp_rec->identity_type == 2 ? $sp_rec->id_number : "" ;
                    $id_type = $partner_type == 'I' ? $sp_rec->identity_type: *********;
                    $id_ke= $partner_type == 'I' ? $sp_rec->id_number: 0;
                    $companytin = $partner_type == 'C' ? trim($sp_rec->pin_number): "";

                }
                break;

            case 'rein':
                

                $rein_rec =DB::table('crmast ')->where('agent',$partner_code)->first();
                
                $partner_data=array(
                    $country_id =>1,
                    $phonenumber_main = trim($rein_rec->mobile_no),
                    $phonenumber_alternate = trim($rein_rec->mobile_no),
                    $emailaddress1 = trim($rein_rec->e_mail),
                    $emailaddress2 = trim($rein_rec->e_mail),
                    $companyname = trim($rein_rec->name),
                    $residenceordomicile_countrycode = trim($rein_rec->country_code),
                    $residenceordomicile_regionorcounty = trim($rein_rec->region),
                    $companytin = trim($rein_rec->pin_number)
                );

                break;
                
            default:
            break;
            
            // case 'claimant':
            //         $claimant = DB::table('clparam')
            //                     ->whereRaw("TRIM(claimant_code) = ?", [$partner_code])
            //                     ->get();
                    
                    
    
            //         if(count($claimant) < 1){
            //             return 'Record not found in Clparam' ;
            //         }else{
            //             $claimant=$claimant[0];
    
            //         }
    
            //         $checkpartnernumber = $claimant->partnernumber;
            //         if (!is_null($checkpartnernumber) && strlen(trim($checkpartnernumber)) > 1 ) {
            //             $errormsg ='Partner number Already Exist for this record';
            //             $updatepartner = DB::table('clparam')->whereRaw("TRIM(claimant_code) = ?", [$partner_code])
            //                             ->update([
            //                                 'partnernumber_error'=>$errormsg
            //                             ]);
             
            //         }else{
            //             $legalentitytype = $partner_type == 'C' ? 2: 1 ;
            //             $phonenumber_main = $claimant->addr5;
            //             $phonenumber_alternate = $claimant->addr5;
            //             $emailaddress1 = trim($claimant->e_mail);
            //             $emailaddress2 = trim($claimant->e_mail);
            //             $firstname = $partner_type == 'I' ? $claimant->name: "";
            //             $companyname =  $partner_type == 'C' ? trim($claimant->name): "" ;
            //             $residenceordomicile_countrycode = $country;
            //             $residenceordomicile_regionorcounty = trim($claimant->addr2);
            //             $dateofbirth = $claimant->dob;
            //             $passport = $claimant->identity_type == 2 ? $claimant->id_number : "" ;
            //             $id_type = $partner_type == 'I' ? $claimant->identity_type: *********;
            //             $id_ke= $partner_type == 'I' ? $claimant->id_number: 0;
            //             $companytin = $partner_type == 'C' ? trim($claimant->pin_number): "";
    
            //         }
            //         break;
            
        }
         $partner_data=array(
            'country_id' => $country_id,
            'partytypeid' => $partytypeid,
            'legalentitytype' => $legalentitytype,
            'phonenumber_main'=> $phonenumber_main,
            'phonenumber_alternate'=> $phonenumber_alternate,
            'emailaddress1' => $emailaddress1,
            'emailaddress2' => $emailaddress2,
            'firstname' => $firstname,
            'middlename'=> $middlename,
            'surname' => $surname,
            'companyname' => $companyname,
            'residenceordomicile_countrycode' => $residenceordomicile_countrycode,
            'residenceordomicile_regionorcounty' => $residenceordomicile_regionorcounty,
            'dateofbirth' => $dateofbirth,
            'passport' => $passport,
            'id_type' => $id_type,
            'id_ke' => $id_ke,
            'id_ug' => $id_ug,
            'id_tz' => $id_tz,
            'id_ss' => $id_ss,
            'id_rw' => $id_rw,
            'pin_ke' => $pin_ke,
            'pin_ug' => $pin_ug,
            'pin_rw' => $pin_rw,
            'pin_ss' => $pin_ss,
            'pin_tz' => $pin_tz,
            'gender' => $gender,
            'companytin' => $companytin,
            'partner_group' => $partner_group,
            'partner_code' => $partner_code
            
        );
        
        $resp = (object)$this->getPatnerNo($partner_data);
        return $resp;

    }

    public function intergruateReinsurer($branch,$agent){

            $resp =(object)$this->getPatnerNo($partner_data);
            
        
            if($resp->statusCode == 200){
                
                    $updatepartner = DB::table('crmast ')->where('branch',$branch)->where('agent',$agent)
                                        ->update([
                                            'partnernumber'=>$resp->message,
                                            'partnernumber_status'=>"Y",
                                            'partnernumber_error'=>""

                                        ]);
                
                
            }else {
                $updatepartner = DB::table('crmast ')->where('branch',$branch)->where('agent',$agent)
                                        ->update([
                                            'partnernumber_status'=>"N",
                                            'partnernumber_error'=>$resp->message
                                        ]);
            }
    }
       
        

    

    public function getPatnerNo($partner_data){        
        $url = DB::SELECT("select partner_url from pipcnam")[0]->partner_url;
        $key = DB::SELECT("select partner_key from pipcnam")[0]->partner_key;
        $partner_group = $partner_data['partner_group'];
        $partner_code = $partner_data['partner_code'];
        $partner_resp = [];
    
        $pip = Pipcnam::select('generate_partner_no')->first();
        if ($pip->generate_partner_no === 'N') {
            $partner_resp = [
                'statusCode' => 200,
            ];
        } else {
            $data = [
                "country_id" => $partner_data['country_id'],
                "sourceid" => 101102,
                "partytypeid" => $partner_data['partytypeid'],
                "legalentitytype" => $partner_data['legalentitytype'],
                "phonenumber_main" => $partner_data['phonenumber_main'] ?? "",
                "phonenumber_alternate" => $partner_data['phonenumber_main'] ?? "",
                "firstname" => "",
                "middlename" => "",
                "surname" => "",
                "companyname" => "",
                "emailaddress1" => $partner_data['emailaddress1'] ?? "",
                "emailaddress2" => $partner_data['emailaddress1'] ?? "",
                "residenceordomicile_countrycode" => "UG",
                "residenceordomicile_regionorcounty" => $partner_data['residenceordomicile_regionorcounty'],
                "dateofbirth" => "",
                "passport" => (string)$partner_data['passport'],
                "id_type" => (int)$partner_data['id_type'],
                "id_ke" => 0,
                "id_ug" => (string)$partner_data['id_ke'],
                "id_tz" => "",
                "id_ss" => "",
                "id_rw" => "",
                "pin_ke" => "",
                "pin_ug" => (string)$partner_data['pin_ke'],
                "pin_rw" => "",
                "pin_ss" => "",
                "pin_tz" => "",
                "gender" => 0,
                "currencycode" => "",
                "nationality" => "",
                "maritalstatus" => 1,
                "branchnumber" => 110101,
                "sendmarketingmaterials" => 0,
                "sharekycinfointernally" => 0,
                "amlriskcategory" => 0,
                "pep" => 0,
                "town" => "",
                "companytin" => $partner_data['companytin'],
                "physicaladdress" => "",
                "paymentmethod" => 0,
                "bankaccountholder" => "",
                "bankaccountnumber" => "",
                "bankbranchcode" => "",
                "bankname" => "",
                "cellphonepankingnumber" => "",
                "mainagentorbroker_partnernumber" => 0,
                "salutation" => *********,
                "occupation" => "",
                "postaladdress" => "",
                "postalcode" => "",
                "customergroup" => *********,
                "vendorgroup" => *********,
                "usekycdetailsforresearch" => 0,
                "thirdpartieskycdetails" => 0,
                "directaccount" => 0,
                "pensionpersontype" => 0,
                "industrycode" => 0,
                "d365fo_company" => 0
            ];
    
            $client = new \GuzzleHttp\Client();
    
            try {
                $response = $client->post($url, [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Ocp-Apim-Subscription-Key' => $key,
                    ],
                    'json' => $data
                ]);
    
                $contents = json_decode($response->getBody()->getContents());
                $status_code = $contents->Code;
    
                if ($status_code == 200) {
                    $partnerNumber = $contents->partnerNumber;
                    $partner_resp = [
                        'statusCode' => $status_code,
                        'message' => $partnerNumber,
                    ];
                } else {
                    $partner_resp = [
                        'statusCode' => 409,
                        'message' => 'Api Error',
                    ];
                }
    
                $resp = $this->updatePartnerDetails($partner_resp, $partner_group, $partner_code);    
                return $resp;
    
            } catch (\GuzzleHttp\Exception\ServerException $e) {
                if ($e->hasResponse()) {
                    $response = $e->getResponse();
                    $error = json_decode($response->getBody()->getContents());
    
                    if (isset($error->code)) {
                        $partner_resp = [
                            'statusCode' => 400,
                            'message' => $error->Description,
                        ];
                    } elseif (isset($error->error->code)) {
                        $partner_resp = [
                            'statusCode' => 400,
                            'message' => $error->error->message,
                        ];
                    }
                } else {
                    $partner_resp = [
                        'statusCode' => 400,
                        'message' => $e->getMessage(),
                    ];
                }
            } catch (\GuzzleHttp\Exception\RequestException $e) {
                if ($e->hasResponse()) {
                    $response = $e->getResponse();
                    $error = json_decode($response->getBody()->getContents());
    
                    if (isset($error->code)) {
                        $partner_resp = [
                            'statusCode' => 400,
                            'message' => $error->Description,
                        ];
                    } elseif (isset($error->error->code)) {
                        $partner_resp = [
                            'statusCode' => 400,
                            'message' => $error->error->message,
                        ];
                    }
                } else {
                    $partner_resp = [
                        'statusCode' => 400,
                        'message' => $e->getMessage(),
                    ];
                }
            }
        }
    
        return $partner_resp;
    }
    


    public function updatePartnerDetails($partner_resp,$partner_group,$partner_code){
        $status = 0;

        $resp = $partner_resp;
        

        switch ($partner_group) {
            case 'cust': 
                if($resp['statusCode'] == 200){
                    $updatepartner = DB::table('client')->where("client_number", $partner_code)
                        ->update([
                            'partnernumber'=>$resp['message'],
                            'client_status'=>"Y",
                            'partnernumber_status'=>"Y",
                            'partnernumber_error'=>""

                        ]);
                        
                        return ['status' => 1];
                }else{
                    $updatepartner = DB::table('client')->where("client_number", $partner_code)
                                    ->update([
                                        'partnernumber_error'=>$resp['message'],
                                        'client_status'=>"N",
                                        'partnernumber_status'=>"N"

                                    ]);
                    return ['status' => 0];
                }
            
            break;

            case 'sp':                
                if($resp['statusCode'] == 200){
                    $updatepartner = DB::table('clparam')->where('claimant_code',$partner_code)
                                    ->update([
                                        'partnernumber'=>$resp['message'],
                                        'partnernumber_status'=>"Y",
                                        'partnernumber_error'=>""

                                    ]);
                    return ['status' => 1];
                
                }else {
                    $updatepartner = DB::table('clparam')->where('claimant_code',$partner_code)
                                    ->update([
                                        'partnernumber_status'=>"N",
                                        'partnernumber_error'=>$resp['message']

                                    ]);
                    return ['status' => 0];
                }
            break;

            case 'rein':
                if($resp['statusCode'] == 200){
                  
                        $updatepartner = DB::table('crmast ')->where('agent',$partner_code)
                                            ->update([
                                                'partnernumber'=>$resp['message'],
                                                'partnernumber_status'=>"Y",
                                                'partnernumber_error'=>""

                                            ]);

                        return ['status' => 1];
                  
                }else {
                    $updatepartner = DB::table('crmast ')->where('agent',$partner_code)
                                            ->update([
                                                'partnernumber_status'=>"N",
                                                'partnernumber_error'=>$resp['message']
                                            ]);
                    return ['status' => 0];

                }
            break;

            case 'agnt': 
                if($resp['statusCode'] == 200){
                    $updatepartner = DB::table('intermediary')->where("intermediary_number", $partner_code)
                        ->update([
                            'partnernumber'=>$resp['message'],
                            'partnernumber_status'=>"Y",
                            'partnernumber_error'=>""

                        ]);
                        
                        return ['status' => 1];
                }else{
                    $updatepartner = DB::table('intermediary')->where("intermediary_number", $partner_code)
                                    ->update([
                                        'partnernumber_error'=>$resp['message'],
                                        'partnernumber_status'=>"N"

                                    ]);
                    return ['status' => 0];
                }
            
            break;

            default:
                return 0;
            break;

        }

    }
    public function fetchclientDtls(Request $request){

        $id_value=trim($request->id_value);
        $id_type=trim($request->id_type);
        $fname=trim($request->fname);
        $surname=trim($request->surname);
        $midname=trim($request->midname);
        $dob=trim($request->dob);
        $documentId=trim($request->documentId);

        $id_desc =Identity_type::WhereRaw("trim(identity_code) ='".trim($id_type)."'")->first();
        //dd($id_desc);
        // if($id_desc->can_integrate_with_hub <> 1){
        //     return  response()->json(['status'=>519,"message"=>$id_desc->name. " Type is not supported for intergration. Kindly contact Admin"]);

        // }
        if($id_desc <> null &&  $id_desc->identity_descr <> null){
            $type = strtolower($id_desc->identity_descr);

        }else{
            return  response()->json(['status'=>520,"message"=>"Id Type not correctly set. Kindly contact Admin"]);

        }
       
        try {
           
                $contents = $this->getIprsDetails($id_value,$id_type,$fname,$surname,$dob,$midname,$documentId);
            
            
                $status=(int)$contents[0]->responseCode;
                $cardStatus=$contents[0]->cardStatus;
                $matchingStatus=$contents[0]->matchingStatus;

                if($matchingStatus == false && $midname !=null){
                    $fname=trim($fname." ".$midname);
                    $midname=null;
                    $contents = $this->getIprsDetails($id_value,$id_type,$fname,$surname,$dob,$midname,$documentId);
    
    
                    $status=(int)$contents[0]->responseCode;
                    $cardStatus=$contents[0]->cardStatus;
                    $matchingStatus=$contents[0]->matchingStatus;
                }
          
                if ($status == 200) {
                    if($matchingStatus == true){
                        return [
                             'status'=>1,
                             'msg'=> ''
                           ];

                    }else{
                        return [
                            'status'=>-1,
                            'msg'=> $contents[0]->errormessage ? $contents[0]->errormessage:"Failed to match"
                          ];
                        
                    }
                    
               

                }elseif($status == 201){
                    return response()->json(['status'=>$status,"data"=>$contents[0]->errormessage]);

                }else{
                    return response()->json(['status'=>$status,"message"=>"An error occured"]);
                }
        } catch (\Exception $e) {
          dd($e);
                
            
            return $e->getMessage();
        
        }
    }
    public function generateToken()
    {
        $consumer_key="nira_aims";
        $consumer_secret = hash('sha256', $consumer_key);
        $url="http://************:87/niraapi/Token/Authenticate";
        $data=array(
            "consumerkey"=>$consumer_key,
            "consumersecret"=>$consumer_secret
        );
        $client = new \GuzzleHttp\Client();
        $response = $client->post($url, [
          
            'headers' => [
                    'Content-Type'  => 'application/json',
                    
                ],
            'json'=>$data

        ]);
        $contents = json_decode($response->getBody()->getContents());
        if($contents){
            return $contents[0]->access_token;
        }
        return 0;
    
       


     
    }

    public function getIprsDetails($id_value,$id_type,$fname,$surname,$dob,$midname,$documentId){

            // $id_desc =Idtype::WhereRaw("trim(code) ='".trim($id_type)."'")->first();

            // if($id_desc <> null &&  $id_desc->name <> null){
            //     $type = strtolower($id_desc->name);

            // }
          
     
        $url ="http://************:87/niraapi/VerifyPersonInformation";
        $data=array(
            "dateOfBirth"=>$dob,
            "documentId"=>$documentId,
            "givenNames"=>$fname,//trim($fname." ".$midname),
            "nationalId"=>trim($id_value),
            "otherNames"=>$midname,
            "surname"=>trim($surname)

        );
       
        $client = new \GuzzleHttp\Client();
        $response = $client->post($url, [
                
            'headers' => [
                    'Content-Type'  => 'application/json',
                    'Authorization' => 'Bearer ' . $this->generateToken()
                ],
            'json'=>$data

        ]);

        $contents = json_decode($response->getBody()->getContents());

        return  $contents;


        
    }
}