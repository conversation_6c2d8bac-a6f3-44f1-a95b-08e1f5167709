<?php

namespace App\Http\Controllers\gb\underwriting;
use DB;
use Session;
use DateTime;
use Response;
use App\Acdet;
use App\Agcom;
use App\Agmnf;
use App\Tran0;
use App\Branch;
use App\Client;
use App\Dtran0;
use App\Mtpana;
use App\Bustype;
use App\Classbr;
use App\Pipcnam;
use App\Pipstmp;
use App\Certmast;
use App\Currency;
use App\Dcontrol;
use DateInterval;
use App\Certalloc;
use App\Covertype;
use App\Polmaster;
use App\Transtype;
use App\Vat_setup;
use Carbon\Carbon;
use App\ClassModel;
use App\Debitmast;  
use App\Regex_parameters;
use App\Models\Mtpagent;
use App\Models\Modtlmast;
use App\Models\Modtlsumm;
use App\Models\Motcvrdet;
use App\Models\Motorsect;
use App\Models\Mtpmaster;
use App\Models\MtpReport;
use App\Models\Modtlpivot;
use App\Models\Mtpagentdtl;
use App\Models\MtpreportDtl;
use Illuminate\Http\Request;
use App\Models\IceCashMaster;
use App\Models\IceCash;
use App\Models\MotorReporting;
use Yajra\Datatables\Datatables;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\pdfController;
use App\Classes\Common\FileUploadManager;
use App\Models\Intermediary\Intermediary;
use App\Services\IntermediaryQueryService;
use App\ValueObjects\IntermediaryQueryParams;
use App\Http\Controllers\gb\underwriting\Risk;
use App\Models\Intermediary\IntermediaryBranch;
use App\Http\Controllers\gb\underwriting\Policy;
use App\Http\Controllers\gb\underwriting\Policy_details;
use App\Http\Controllers\gb\underwriting\Policy_functions;
use App\Http\Controllers\gb\underwriting\IntergrationController;


class mtpController extends Controller
{
    public function create()
    {
        $intermediaryParams = new IntermediaryQueryParams([

            'additionalFields'=>['intermediary_branch.status'],
            'conditions' => function($query){
                    return $query->where('branch.uw_active','Y');
                }
        ]);
        $agents  =IntermediaryQueryService::getActiveintermediaryWithBranchDetails($intermediaryParams)->get();
        
      
        return view('gb/underwriting/mtpana',compact('agents'));
    }
    public function mtpdetailstable(Request $request)
    {

        $batchserial = $request->batch;
    
        $mtpana = IceCash::where('batch_Serial', $batchserial)
                        ->leftJoin('client', 'icecash.national_id', '=', 'client.id_number')
                        ->select('icecash.*', 'client.client_number', 'client.partnernumber')
                        ->get();
    
        
   
                        
        return Datatables::of($mtpana)
        ->addColumn('agent_name', function($row){
            $intermediaryParams = new IntermediaryQueryParams([
                'branch' => $row->branch,
                'agentNo' => $row->intermediary
            ]);
            $agent = IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();

            return $agent ? $agent->name : '';
            // $agent=  Agmnf::where('agent',$row->intermediary)->where('branch',$row->branch)->first()->name;;
            // return $agent;

        })
         ->addIndexColumn()
         ->make(true);
    }
    public function cancelMtp(Request $request){
        $batch_serial =trim($request->batch_serial);
      try {
      
        $mtmast= IceCashMaster::where('batch_serial',$batch_serial)->update([
            'cancelled'=>'Y'
        ]);
        $mtmast= IceCash::where('batch_serial',$batch_serial)->update([
            'cancelled'=>'Y'
        ]);
        return[
            'status'=>1,
            'msg'=>"Cancellation Successful"
        ];
      } catch (\Throwable $th) {
        throw $th;
      }

    }

    public function Pendingprocess(Request $request)
    {


        
        // $mtpana = DB::raw(DB::select("select * from mtpana where cancelled <> 'Y' and created = 'Y' order by id desc"));
        $mtpana = IceCashMaster::where(function ($query) {
            $query->whereIn('cancelled',['N','n'])->orWhereNull('cancelled');                  
        })
                         ->where('created', 'Y')
                        ->where('debited', "N")
                         ->where('proforma_processed', "N")
                        ->orderBy('id', 'desc')
                        ->get();

                    //    dd($mtpana);
                        
        return Datatables::of($mtpana)
        ->addColumn('agent_name', function($row){
            $intermediaryParams = new IntermediaryQueryParams([
                'branch' => $row->branch,
                'agentNo' => $row->intermediary
            ]);
            $agent = IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();

            return $agent ? $agent->name : '';
            // $agent=  Agmnf::where('agent',$row->intermediary)
            // ->where('branch',$row->branch)
            // ->first()->name;
            // // dd($agent,$row->intermediary);
            // return $agent;


        })
        ->addColumn('action', function ($row) use ($debited) {
            return '
            <div style="display: flex; gap: 5px;">
                <button class="btn btn-xs btn-primary create_proforma">
                    <i class="fa fa-check" aria-hidden="true"></i> Create Proforma
                </button>
                <button class="btn btn-xs btn-primary view">
                    <i class="fa fa-eye" aria-hidden="true"></i> View
                </button>
                <button class="btn btn-xs btn-default cancel_proforma">
                    <i class="fa fa-times" aria-hidden="true"></i> Cancel Batch
                </button>
            </div>';
        })
        
        
        ->rawColumns(['action'])
         ->addIndexColumn()
            ->make(true);
    } 
    public function Pendingdebit(Request $request)
    {


        
        // $mtpana = DB::raw(DB::select("select * from mtpana where cancelled <> 'Y' and created = 'Y' order by id desc"));
        $mtpana = IceCashMaster::where(function ($query) {
            $query->whereIn('cancelled',['N','n'])->orWhereNull('cancelled');                  
        })
                         ->where('created', 'Y')
                         ->where('debited', 'N')
                         ->where('proforma_processed', "Y")
                        ->orderBy('id', 'desc')
                        ->get();

                       // dd($mtpana);
                        
        return Datatables::of($mtpana)
        ->addColumn('agent_name', function($row){
            $intermediaryParams = new IntermediaryQueryParams([
                'branch' => $row->branch,
                'agentNo' => $row->intermediary
            ]);
            $agent = IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();
            return $agent ? $agent->name : '';
            // $agent=  Agmnf::where('agent',$row->intermediary)
            // ->where('branch',$row->branch)
            // ->first()->name;
            // return $agent;

        })
        ->addColumn('action', function ($row) use ($debited) {
            return '
            <div style="display: flex; gap: 5px;">
                <button class="btn btn-xs btn-primary show_summary">
                    <i class="fa fa-check" aria-hidden="true"></i> Debit Batch
                </button>
                <button class="btn btn-xs btn-info view">
                    <i class="fa fa-eye" aria-hidden="true"></i> View
                </button>
                <button class="btn btn-xs btn-danger cancel_proforma">
                    <i class="fa fa-eye" aria-hidden="true"></i> Cancel Proforma
                </button>
            </div>';
        })
        
        ->rawColumns(['action'])
         ->addIndexColumn()
            ->make(true);
    } 
    public function cancelproformabatch(Request $request){
      
       
      try {
        $allrecs =IceCash::where("batch_serial",$request->batch_serial)->get();
        foreach ($allrecs as $key => $value) {
            $newreq = New Request;
            $newreq->merge([
               "endt_renewal_no"=>$value->endt_renewal_no,
               "cancellationReason"=>"Cancellation of mtp batch"
            ]);
           $polfct =  new Policy_functions;

           $cancelrec = $polfct->deleteEndorsement($newreq);
           
        }
        
        $mtmast= IceCashMaster::where('batch_serial',$request->batch_serial)->first();
        $mtmast->cancelled ="Y";
        $mtmast->save();
 
        return [
            "status"=>1
        ];
      } catch (\Throwable $th) {
        //throw $th;
      }

    }
    public function debited(Request $request)
    {

        
        // $mtpana = DB::raw(DB::select("select * from mtpana where cancelled <> 'Y' and created = 'Y' order by id desc"));
        $mtpana = IceCashMaster::where(function ($query) {
            $query->whereIn('cancelled',['N','n'])->orWhereNull('cancelled');                  
        })
                         ->where('created', 'Y')
                         ->where('debited', "Y")
                         ->where('proforma_processed', "Y")
                        ->orderBy('id', 'desc')
                        ->get();

                       // dd($mtpana);
                        
        return Datatables::of($mtpana)
        ->addColumn('agent_name', function($row){
            $intermediaryParams = new IntermediaryQueryParams([
                'branch' => $row->branch,
                'agentNo' => $row->intermediary
            ]);
            $agent = IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();

            return $agent ? $agent->name : '';
            // $agent=  Agmnf::where('agent',$row->intermediary)
            // ->where('branch',$row->branch)
            // ->first()->name;
            // return $agent;

        })
        ->addColumn('action', function ($row) use ($debited, $processed) {
            if ($row->receipted == 'Y') {
                return '<div class="d-flex align-items-center">
                <button class="btn btn-xs btn-primary view d-inline-block mx-2">
                    <i class="fa fa-eye" aria-hidden="true"></i> View
                </button>
                <div class="dropdown d-inline-block mx-2">
                    <button class="btn btn-xs btn-info dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        Documents
                    </button>
                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        <a href="' . route('downloaddebitbatch', ['batch_no' => $row->batch_serial]) . '" class="dropdown-item" target="_blank">
                            <i class="fa fa-eye" aria-hidden="true"></i> Debit Note
                        </a>
                        <hr>
                        <a href="' . route('downloadschedbatch', ['batch_no' => $row->batch_serial]) . '" class="dropdown-item" target="_blank">
                            <i class="fa fa-eye" aria-hidden="true"></i> Policy Schedule
                        </a>
                        <hr>
                        <a href="' . route('downloadcombinedbatch', ['batch_no' => $row->batch_serial]) . '" class="dropdown-item" target="_blank">
                            <i class="fa fa-eye" aria-hidden="true"></i> Combined Document
                        </a>
                    </div>
                </div>
            </div>';
    
            } else {
             
                return '<div class="d-flex align-items-center">
                <button class="btn btn-xs btn-primary view d-inline-block mx-2">
                    <i class="fa fa-eye" aria-hidden="true"></i> View
                </button>
                <div class="dropdown d-inline-block mx-2">
                    <button class="btn btn-xs btn-info dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        Documents
                    </button>
                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        <a href="' . route('downloaddebitbatch', ['batch_no' => $row->batch_serial]) . '" class="dropdown-item" target="_blank">
                            <i class="fa fa-eye" aria-hidden="true"></i> Debit Note
                        </a>
                        <hr>
                        <a href="' . route('downloadschedbatch', ['batch_no' => $row->batch_serial]) . '" class="dropdown-item" target="_blank">
                            <i class="fa fa-eye" aria-hidden="true"></i> Policy Schedule
                        </a>
                        <hr>
                        <a href="' . route('downloadcombinedbatch', ['batch_no' => $row->batch_serial]) . '" class="dropdown-item" target="_blank">
                            <i class="fa fa-eye" aria-hidden="true"></i> Combined Document
                        </a>
                    </div>
                </div>
            </div>';
    
    
            }
        })
        
        
        ->rawColumns(['action'])
         ->addIndexColumn()
            ->make(true);
    }
    public function downloaddebitbatch($batch_no){
       
        $recs = IceCash::whereRaw("trim(batch_serial) = '" . $batch_no . "'")->pluck('endt_renewal_no')->toArray();
       
        $pdf = new pdfController;
        $request = new Request;
        return $pdf->debitNoteDocument($request, $recs, $base64 = null);
        

    }
    public function downloadcombinedbatch($batch_no){
        $recs = IceCash::whereRaw("trim(batch_serial) = '" . $batch_no . "'")->pluck('endt_renewal_no')->toArray();
       
        $pdf = new pdfController;
        $request = new Request;
        return $pdf->printAllMotorDoc($request, $recs, $base64 = null);


    }
    public function downloadschedbatch($batch_no){
       
        $recs = IceCash::whereRaw("trim(batch_serial) = '" . $batch_no . "'")->pluck('endt_renewal_no')->toArray();
       
        $pdf = new pdfController;
        $request = new Request;
        return $pdf->policyScheduleDocument($request, $recs);
        

    }

    public function viewbatch($batchserial){
       // dd($batchserial);

        return view('gb/underwriting/mtpanadetails',compact('batchserial'));


    }
    public function verifyMtpBatch(Request $request)
    {

        $batch_serial = $request->batch_serial;
        $count_batch = IceCash::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")->count();
        $batch_prem = IceCash::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")->sum('premium');
        $sticker_fees = IceCash::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")->sum('sticker_fees');
        $stamp_duty = IceCash::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")->sum('stamp_duty');
        $levyamt = IceCash::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")->sum('levy');
        $vat = IceCash::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")->sum('vat_amount');
      


        $batch_records = IceCash::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")->get();

        // //training levy
        // $pipstmp = Pipstmp::where('key', '02')->get();
        // $training = 0;
        // $phcf = 0;
        $gross_com = 0;
        $agent_admin_fee = 0;
        // $sticker_fees = 0;
        // $vat_amount = 0;
        foreach ($batch_records as $batch_record) {
            $polmaster = Polmaster::where('endorse_no', $batch_record->endt_renewal_no)->get();
            $dcontrol = Dcontrol::where('endt_renewal_no', $batch_record->endt_renewal_no)->get();
            //get contents to calculate debit preview details
            $intermediaryParams = new IntermediaryQueryParams([
                'branch' => $polmaster[0]->branch,
                'agentNo' => $polmaster[0]->agent_no,
                'conditions' => function ($query) {
                    return $query->where('intermediary_attribute_types.slug', 'commission-category');
                }
            ]);
            $agmnf  =IntermediaryQueryService::getIntermediaryWithAttributeDetails($intermediaryParams)
            ->get();
    
            // $agmnf = Agmnf::where('branch', $polmaster[0]->branch)
            //     ->where('agent', $polmaster[0]->agent_no)
            //     ->get();

            $agcom = Agcom::where('class', $batch_records[0]->class)
                ->where('commission', $agmnf[0]->value)
                ->get();
            $levy = ($pipstmp[0]->levy_rate * ($polmaster[0]->endorse_amount + $batch_record->system_charge)) / 100;
            if($batch_record->training_levy_flag == "N"){
                $levy =0;
            }
            $training +=$levy ;
            //policy fund
            $phcf += ($pipstmp[0]->policy_fund_rate * $polmaster[0]->endorse_amount) / 100;
            //gross commission
            $gross_com += ($agcom[0]->percent * $polmaster[0]->endorse_amount) / 100;
            $agent_admin_fee += ($dcontrol[0]->admin_fees_rate * $polmaster[0]->endorse_amount) / 100;
            //    sticker fees
            $stickerfee=ClassModel::where('class', $polmaster[0]->class)->first()->sticker_fees;
            //$sticker_fees +=$stickerfee;
            $stamp_dut=ClassModel::where('class', $polmaster[0]->class)->first()->stamp_duty;
            if($batch_record->stamp_duty_flag == "N"){
                $stamp_dut =0;
            }
           // $stamp_duty +=$stamp_dut;

           $get_vat_setup = Vat_setup::where('vat_code', (int)$batch_record->vat)->first();
           $rate = $get_vat_setup->vat_rate;
           $vat_amt = $rate/100 *($batch_record->premium +$batch_record->system_charge + $levy  + $stickerfee);
           //$vat_amount +=  $vat_amt;
 
        }
        $vat_amount = (float)round($vat,2);

        $total_premium = $batch_prem + $sticker_fees + $vat_amount + $stamp_duty + $levyamt;

        return [
            'items' => $count_batch,
            'data' => $batch_records,
            'batch_prem' => $batch_prem,
            'sticker_fees' => $sticker_fees,
            'stamp_duty' =>$stamp_duty,
            'levy' => $levyamt,
            'phcf' => 0,
            'gross_com' => $gross_com,
            'agent_admin_fee' => $agent_admin_fee,
            'vat' =>$vat_amount,
            'total_premium' => $total_premium,
        ];
        #rate/100 * (premium +system_charge + Levy + sticker fees)
    }
    public function processMtp(Request $request){
        $risk = new Risk;
        $policy_details = new Policy_details;
        $policy = new Policy;
        $motorCtrl = new MotorProcessing();
        $processed="Y";
        $batch_serial =trim($request->batch_serial);
        $batch_records_count = IceCash::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")->count();
        $batch_records_processed = IceCash::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")
        ->whereRaw("trim(proforma_processed) = '" . $processed . "'")
        ->count();
      
        if($batch_records_count < 1){
            return [
                'status'=>-1,
                'msg'=>"Records for batch serial $batch_serial do not exist"

            ];
        }
        if($batch_records_processed > 0){
            return [
                'status'=>-1,
                'msg'=>"Records for batch serial $batch_serial already processed"

            ];
        }
        $batch_records = IceCash::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")->get();
        $pipcnam = Pipcnam::where('record_type', 0)->first();


    
        try {
            DB::beginTransaction();
            foreach ($batch_records as $mtp_data) {
              
                    $schem = schemaName();
                    $gb = $schem['gb'];
                    $gl = $schem['gl'];
                    $common = $schem['common'];
                    $insured_arr = explode(" ", $mtp_data->insured);
                    $classModel = ClassModel::where('class', $mtp_data->class)->first();
                   
                    $reg_exist = $motorCtrl->verify_reg_no(new Request([
                        'chassis_no' => $mtp_data->reg_no,
                        'effective_date' =>$mtp_data->period_from
                    ]));
                $reg_valid = (int) json_decode($reg_exist)->valid;

                    if ($reg_valid == 0) {
                        return [
                            'status'=>-1,
                            'msg'=>"Registration number $mtp_data->reg_no is  active in the system"
            
                        ];
                    }

                    #check tin
                    $count_check = Client::whereRaw("trim(id_number) = '" . $mtp_data->national_id . "'")->count();
                    #if no client record exists
                    if ($count_check < 1) 
                    {  
                      //    ----- ADD NEW CLIENT ---
                        $client = new Client;
                        $client_ln_no = Client::max('ln_no') + 1;
                       
                        // $client_no = $policy_details->generate_client_number(new Request([
                        //     'client_type' => $mtp_data->client_type,
                        //     'corporate_name' =>str_replace(' ', '', $mtp_data->insured),
                        //     'fname'=>str_replace(' ', '', $mtp_data->insured)
                        // ]));
                        $client_no = $policy_details->generate_client_number('C');
                      
                        $client->client_number = $client_no;
                        $client->mobile_no = $mtp_data->phone_no;
                        $client->telephone = $mtp_data->phone_no;
                        $client->country_code = $pipcnam->country_code;
                        $client->first_name = $insured_arr[0];
                        $client->others = $insured_arr[1];
                        $client->surname = $insured_arr[2];
                        $client->name = $mtp_data->insured;
                        $client->group_code = $client_no;
                        $client->pin_number = $mtp_data->tin_number;
                        $client->ln_no = $client_ln_no;
                        //$client->contact=$request->contact');
                        $client->dola = Carbon::today();
                        $client->client_type = $mtp_data->client_type;

                        $client->identity_type = 1;
                        $client->id_number = $mtp_data->national_id;

                        // if (!is_null($mtp_data->tin_number)) {
                        //     $client->id_number = $mtp_data->tin_number;
                        //     $client->identity_type = 'I';
                        // } else if (!is_null($mtp_data->passport)) {
                        //     $client->passport_number = $passport;
                        //     $client->identity_type = 'P';
                        // } else {
                        //     $client->dl_number = $driving_license;
                        //     $client->identity_type = 'D';
                        // }

                     

                        // $partnerdata = new IntergrationController;
                        // $partner_group="cust";
                        // $partner_type="C";
                        // $partner_code=$client_no;                        
                        $client->save();
                        // $res =$partnerdata->getPartnerData($partner_group,$partner_type,$partner_code);
                     



                        
                    }
                    
                 
                    $insured_client = Client::whereRaw("trim(id_number) = '" . $mtp_data->national_id . "'")->get(['name', 'client_number', 'client_type']);
                    $checklen = strlen($insured_client[0]->client_number);
                    #check if already created policy for fleet
                    $dperiodFrom = Carbon::parse($mtp_data->period_from)->format('Y-m-d H:i:s');
                    $dperiodTo = Carbon::parse($mtp_data->period_to)->format('Y-m-d H:i:s');
                    $checkpol= IceCash::where('batch_serial',$batch_serial)
                                        ->where('national_id',$mtp_data->national_id)
                                        ->where('branch',$mtp_data->branch)
                                        ->where('intermediary',$mtp_data->intermediary)
                                        ->whereDate('period_from','=',$dperiodFrom)
                                        ->whereDate('period_to','=',$dperiodTo)
                                        ->whereNotNull('policy_no')
                                        ->count();
                    if ($checklen > 0) {
                        if($checkpol > 0){
                          
                            $checkpoldata= IceCash::where('batch_serial',$batch_serial)
                                            ->where('national_id',$mtp_data->national_id)
                                            ->where('branch',$mtp_data->branch)
                                            ->where('intermediary',$mtp_data->intermediary)
                                            ->whereDate('period_from','=',$dperiodFrom)
                                            ->whereDate('period_to','=',$dperiodTo)
                                            ->whereNotNull('policy_no')
                                            ->first();
                               
                           $m_policy_no=$checkpoldata->policy_no; 
                           $m_endt_renewal_no=$checkpoldata->endt_renewal_no; 

                           $dcontrol=Dcontrol::where('policy_no',$m_policy_no)->first();


                        }else{
                                // _________________ DOCUMENTS _____________________________________________
                            $transaction_type = 'POL';
                            $old_pol = $renew_old_pol = $ira_rate = $fronting_rate = null;
                            $branchpol = "900"; // d
                            $agentpol = $mtp_data->intermediary;
                            /*dcon_no */
                            $transaction_no = Tran0::where('rec_no', 0)->get(['tran_no']);
                            $tran_no = $transaction_no[0]->tran_no;
                            $tran0 = Tran0::where('rec_no', 0)->increment('tran_no', (int) '1');

                            /* get dtrans_no and period */
                            $doc_trans = Dtran0::where('rec_no', 0)->get(['dtran_no', 'account_month', 'account_year']);
                            $dtran_no = $doc_trans[0]->dtran_no;
                            //$account_month = $doc_trans[0]->account_month;
                            //$account_year = $doc_trans[0]->account_year;
                            $account_month = Carbon::now()->month;
                            $account_year = Carbon::now()->year;
                            $dtran0 = Dtran0::where('rec_no', 0)->increment('dtran_no', (int) '1');
                            $prop_number = Classbr::where('class', $mtp_data->class)->first()->prop_serial;
                            $dprop_no = Classbr::where('class', $mtp_data->class)->increment('prop_serial', (int) '1');
                            $document_type = Transtype::where('descr', $transaction_type)->get();
                            $doc_type = $document_type[0]->doc_type;
                            $workflow_id = $classModel->workflow_id;
                            $pid = "002";

                            //type of bus
                            $bustype_curr = Bustype::where('type_of_bus', '1')->get();
                            $bustype_curr = $bustype_curr[0];
                              //add new record to dcontrol
                              $dcontrol = new Dcontrol;
                              $dcontrol->dcon_no = $tran_no;
                              $charge_vat = $pipcnam->charge_vat;
                              if ($charge_vat == 'Y') {
                                  $get_vat_setup = Vat_setup::where('vat_code', (int)$mtp_data->vat)->get();
                                  $get_vat_setup = $get_vat_setup[0];
                              }
                              $uw_year = date('Y', strtotime((string) $mtp_data->period_from));
                              $period_from =$mtp_data->period_from;  
                              $period_to =$mtp_data->period_to;
                              $find_days_period_from = $period_from;
                    
                             $period_to_date = new DateTime($period_to);
                             $renewal_date = new DateTime($period_to);

                             $period_from_date = new DateTime($period_from);

                             $renewal_date = $renewal_date->add(new DateInterval('P1D')); // Add one day to the DateTime object
                             $interval = $period_from_date->diff($period_to_date);
                            // Get the difference in days
                            $days_of_cover = $interval->days + 1;
                            // $days_of_cover = $period_to_date->diffInDays($period_from_date);
                            if($days_of_cover == 365 ||$days_of_cover == 366 ){
                                $ast_marker="A";

                            }else{
                                $ast_marker="S";
                                $short_term_method="P";
                                $percentage=null;
                            }

                            $intermediaryParams = new IntermediaryQueryParams([
                                'branch' => $mtp_data->branch,
                                'agentNo' => $mtp_data->intermediary,
                            ]);
                            $agmnf  =IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();
                            $currency_used= Currency::where('currency', $mtp_data->currency)->first();

                            // $agmnf = Agmnf::where('branch', $mtp_data->branch)
                            //     ->where('agent', $mtp_data->intermediary)
                            //     ->get()[0];

                            $new_endt_renewal_no = $policy->generate_pol(
                                $mtp_data->branch,
                                $mtp_data->class,
                                $transaction_type,
                                $account_year,
                                $account_month
                            );
                            $seq_no = Dcontrol::generateTranseqNumber($transaction_type,$new_endt_renewal_no);
                            switch ($transaction_type) {
                                case 'POL':
                                    $dcontrol->dprop_no = $prop_number;
                                    $dcontrol->transeq_no = $seq_no;
                                    $dcontrol->policy_no = (string)$new_endt_renewal_no;
                                    $dcontrol->prop_date = Carbon::now();
                                    $dcontrol->branch = $mtp_data->branch;
                                    $dcontrol->agent = $mtp_data->intermediary;
                                    $dcontrol->class = $mtp_data->class;
                                    $dcontrol->prov_premium = $mtp_data->premium;
                                    $dcontrol->user_str = (string) Auth::user()->user_name;

                                    $dcontrol->period_from = $period_from;
                                    $dcontrol->period_to = $period_to;
                                    $dcontrol->old_policy_no = $old_pol;
                                    $dcontrol->renew_old_policy = $renew_old_pol;

                                    $dcontrol->ira_rate = $ira_rate;
                                    // dd($ira_rate);
                                    $dcontrol->fronting_rate = $fronting_rate;


                                    if (trim($ast_marker) == 'S') {
                                        $dcontrol->period_from = $period_from;
                                        $dcontrol->period_to = $period_to;
                                        $dcontrol->cov_period_from = $period_from;
                                        $dcontrol->cov_period_to = $period_to;
                                        $dcontrol->endt_days = $days_of_cover;
                                        $dcontrol->short_term_method = $short_term_method;
                                        $dcontrol->short_term_percent = $percentage;
                                    } else {
                                        $dcontrol->period_from = $period_from;
                                        $dcontrol->period_to = $period_to;
                                        $dcontrol->cov_period_from = $period_from;
                                        $dcontrol->cov_period_to = $period_to;
                                    }

                                    $dcontrol->effective_date = $period_from;
                                    // $dcontrol->branch_code = str_pad($request->branchpol, 3, "0", STR_PAD_LEFT);
                                    $dcontrol->co_insure = $request->co_ins;
                                    $dcontrol->co_ins_rate = 0;
                                    $dcontrol->co_ins_base = 0;
                                    $dcontrol->type_of_bus = '1';
                                    $dcontrol->dept = $classModel->dept;
                                    $dcontrol->actual_period_from = $period_from;
                                    $dcontrol->endorse_date = $period_from;
                                    $dcontrol->actual_period_to = $period_to;
                                    $dcontrol->renewal_date = $renewal_date;
                                    $dcontrol->financed = $request->financed;
                                    $dcontrol->ast_marker = $ast_marker;
                                    $dcontrol->items_total = 1;
                                    $dcontrol->branch_code = $mtp_data->branch;
                                    $dcontrol->ext_from = $period_from;
                                    $dcontrol->ext_to = $period_to;
                                    $dcontrol->currency = $currency_used->currency_code;

                                    if ($request->financed == 'Y') {
                                        $dcontrol->financed_code = $request->financier;
                                    } else {
                                        $dcontrol->financed_code = '';
                                    }

                                    if ($request->co_ins == 'Y') {

                                        $dcontrol->company_share = $request->co_ins_share;
                                    } else {
                                        $dcontrol->company_share = 100;  //must be 100 for debit and reinsurances to work if no co insurance is done
                                    }

                                    if ($charge_vat == 'Y') {
                                        $dcontrol->vat_type = $get_vat_setup->vat_type;
                                        $dcontrol->vat_description = $get_vat_setup->vat_description;
                                        $dcontrol->vat_rate = $get_vat_setup->vat_rate;
                                        $dcontrol->vat_code = (int)$mtp_data->vat;
                                        // $dcontrol->apply_vat_on_comm = $agmnf->vat_on_comm;
                                    } else {
                                        $dcontrol->vat_rate = 0;
                                    }

                                    $dcontrol->endt_renewal_no = (string) $new_endt_renewal_no;
                                    $dcontrol->dtrans_no = $dtran_no;

                                    $dcontrol->insured = (string) $mtp_data->insured;
                                    //$dcontrol->tran_no=

                                    $dcontrol->trans_type = $transaction_type;
                                    $dcontrol->dola = Carbon::now();
                                    $dcontrol->location = 0;
                                    $dcontrol->time = Carbon::now();
                                    $dcontrol->expiry_date = $period_to;
                                    $dcontrol->pin_no = 'Y'; //$insured[0]->pin_number;
                                    $dcontrol->client_number = (string)trim($insured_client[0]->client_number);
                                    $dcontrol->surname = (string) $insured_arr[3];
                                    $dcontrol->others = (string) $insured_arr[1];
                                    $dcontrol->first_name = (string) $insured_arr[0];
                                    $dcontrol->client_type = $mtp_data->client_type;
                                    $dcontrol->incept_date = $period_from;
                                    $dcontrol->endorse_date = $period_from;
                                    $dcontrol->company_class_code = $mtp_data->class;
                                    $dcontrol->account_year = $account_year;
                                    $dcontrol->account_month = $account_month;
                                    $dcontrol->name = trim($insured_client[0]->name);
                                    $dcontrol->cancelled = 'N';
                                    $dcontrol->source = 'U/W';
                                    $dcontrol->doc_type = strtoupper($doc_type);
                                    $dcontrol->currency_rate =$mtp_data->currency_rate;
                                    $dcontrol->binder_flag = 'N';
                                    $dcontrol->line_no = 0;
                                    $dcontrol->cover_type = $mtp_data->cover_type;
                                    $dcontrol->fleet = 'Y';
                                    $dcontrol->mtp = 'Y';
                                    $dcontrol->days_covered = $days_of_cover;
                                    $dcontrol->endt_days = $days_of_cover;
                                    $dcontrol->apply_admin_fees = $mtp_data->apply_admin_fees; 
                                    $dcontrol->admin_fees_rate = $mtp_data->admin_fees_rate;
                                    $dcontrol->save();

                                break;
                            }
                          
                            #add polmaster
                            $policy->add_polmaster((string) $new_endt_renewal_no, $uw_year);

                            //add to workflows
                            $policy->add_to_workflow($new_endt_renewal_no, $workflow_id, $pid);
                        }
                            IceCash::where('id', $mtp_data->id)
                            ->where('reg_no', $mtp_data->reg_no)
                            ->where('batch_serial', $mtp_data->batch_serial)
                            ->update([
                                'policy_no' => $dcontrol->policy_no,
                                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                                'created' => 'Y',
                                'proforma_processed'=>'Y',
                                'debited_by'=>Auth::user()->user_name
                            ]);

                            $motorate = Motorsect::basicSection((int)$dcontrol->class,(int)$dcontrol->cover_type,(int)$mtp_data->subclass,$tarrif=null)[0];
                          
                            $item_no = Modtlmast::next_serial($dcontrol->policy_no);
                            

                            // begin persisting to database
                           $modmast = Modtlmast::create([
                                'item_no' => $item_no,
                                'policy_no' => $dcontrol->policy_no,
                                'transeq_no' => $dcontrol->transeq_no,
                                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                                'client_number' => $dcontrol->client_number,
                                'class' => $dcontrol->class,
                                'reg_no' => $mtp_data->reg_no,
                                'usage' => $mtp_data->subclass,
                                'owner' => $dcontrol->insured,
                                'covertype' => $mtp_data->cover_type ,
                                'chassis_no' => trim($mtp_data->chassis_no),
                                 'model' => trim($mtp_data->model),
                                 'make' => trim($mtp_data->make), 
                                'seat_cap' => trim($mtp_data->seat_cap),
                                'insured_seats' => trim($mtp_data->seat_cap),
                                'tonnage' => trim($mtp_data->tonnage),
                                'created_by' => Auth::user()->user_name,
                            ]);
                           
                            $motorCtrl->setProps(
                                endt_renewal_no: $dcontrol->endt_renewal_no,
                                reg_no: trim($mtp_data->reg_no),
                                cls : $dcontrol->class,
                                total_sum_insured : 0
                            );
                            $section = [
                                'group' => $motorate->grp_code,
                                'item' => $motorate->item_code,
                                'rate_amount' => $mtp_data->premium,
                                'risk_value' => 0,
                                'cancel' => 'N',
                            ];
                         
                           
                            $section['rate_amount'] = $motorCtrl->get_minRateAmt($section);
                            //set TOR and short term prem to icecash figures provided
                            if(in_array($dcontrol->ast_marker,['S','T'])){
                                 $section['rate_amount'] = $mtp_data->premium;

                            }
                            $premium_amounts = $motorCtrl->compute_motor_premium($section,$dcontrol->ast_marker);
                           
                            $resp = $motorCtrl->save_section_dtl($section,$premium_amounts);
                            $motorCtrl->update_motor_summary($dcontrol->endt_renewal_no,$class,trim($mtp_data->reg_no));
                         
                            $motorCtrl->update_polmaster($dcontrol->endt_renewal_no);
        
                         

                    }
    

            }#end for each
           $mtmast= IceCashMaster::where('batch_serial',$batch_serial)->update([
                'proforma_processed'=>'Y',
                'processed_by'=>Auth::user()->user_name
            ]);
             DB::commit();
            return[
                'status'=>1,
                'msg'=>"Proforma for batch serial $batch_serial generated successfully"
            ];
         

        } catch (\Throwable $th) {
            DB::rollback();
            //  dd($th);
            return[
                'status'=>-1,
                'msg'=>$th->getMessage()
            ];
        }

    }

    function validate_ids($id, $client_type) {

        if (strtoupper($client_type) === 'C') {
            return true;
        }

        $id_regex_patterns = Regex_parameters::where('regex_code', 'ID')->pluck('regex_string')->toArray();
        $id = trim($id);
    
        foreach ($id_regex_patterns as $pattern) {
            if (preg_match($pattern, $id)) {
                return true;
                break;
            }
        }
        return false; 
    }
 
    public function stageMtp(Request $request)
    {
        DB::beginTransaction();
        try {

            $fileData = $request->file('mtp_fleet');
            $fileExtension = $fileData->getClientOriginalExtension();
            //check and verify uploaded file type
            if ($fileExtension == "csv" || $fileExtension == "xls" || $fileExtension == "xlsx") {
                $excel_data = FileUploadManager::excelUpload($fileData);
            } else {
                Session::flash('error', 'Uploaded File Type Not Supported, Use a CSV, XLS or XLSX file! ');

                return redirect()->back();
            }
            #variable declarations
            $insured   = null;
            $vehicle_chassis_no = [];
            $risk = new Risk;
            $motorCtrl = new MotorProcessing();
            //$pipcnam = Pipcnam::where('record_type', 0)->first();
            //$currency = Currency::where('base_currency', "Y")->first();
            $cover_type = 4;
           

            $branchagent = explode("/", $request->intermediary);
            $agent = $branchagent[1];
            $branch = $branchagent[0];
            $apply_admin_fee = $request->apply_admin_fees;
            $admin_fee_rate = $request->admin_rate;
            $errorsCount = 0;
            $errorsList = array();
            $row = 1;
           
            foreach ($excel_data as $value) {


                $row++;
                if (is_null($request->intermediary)) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Select an  intermediary from the dropdowm ';
                } else {
                    $params = new IntermediaryQueryParams([
                        'branch' => $branch,
                        'agentNo' => $agent
                    ]);

                    $check_agent = IntermediaryQueryService::getIntermediaryByBranchAndAgent($params)->count();

                    if ($check_agent < 1) {
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'The intermediary provided  could not be found in the system';
                    }
                }

                if (is_null($value['period_from']) || empty($value['period_from'])) {

                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter period from at row ' . $row;
                }

                if (is_null($value['period_to']) || empty($value['period_to'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter period_to at row ' . $row;
                }
                $findSlashes_period_to = strstr($value['period_to'], '-');
                $findSlashes_period_from = strstr($value['period_from'], '-');

                if (!$findSlashes_period_to || !$findSlashes_period_from) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Please enter the dates in this format YYYY-MM-DD at row ' . $row;
                }

                if (is_null($value['national_id']) || strlen(trim($value['national_id'])) < 2 || !$this->validate_ids($value['national_id'], $value['client_type'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter a valid National ID Number at row ' . $row;
                }

                if (is_null($value['insured']) || empty($value['insured'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter insured at row ' . $row;
                }
                if (is_null($value['class']) || empty($value['class'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter class at row ' . $row;
                }
                if (is_null($value['make']) || empty($value['make'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter make at row ' . $row;
                }
                if (is_null($value['model']) || empty($value['model'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter model at row ' . $row;
                }
                if (is_null($value['vehicle_type']) || empty($value['vehicle_type'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter vehicle type at row ' . $row;
                }
                #check if subclass exists in setup
                
                if (is_null($value['client_type']) || empty($value['client_type'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter client_type at row ' . $row;
                }
               
                
                if (is_null($value['reg_no']) || empty($value['reg_no'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter registration no at row ' . $row;
                }
                $validreg = $risk->validateRegex($value['reg_no'], 'VEH');
                if (!$validreg) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Wrong Registration number Format specified  at row ' . $row;
                }
                $reg_exist = $motorCtrl->verify_reg_no(new Request([
                    'reg_no' => $value['reg_no'],
                    'effective_date' => $value['period_from']
                ]));

                $reg_valid = (int) json_decode($reg_exist)->valid;
                if ($reg_valid == 0) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Vehicle with registration No. ' . $value['reg_no'] . ' at row' . $row . ' seems to be active in the system, Cancel or use another Chassis No';
                }

                if (is_null($value['phone_no']) || empty($value['phone_no'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter phone_no at row ' . $row;
                }
                if (is_null($value['policy_cover']) || empty($value['policy_cover'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter policy cover at row ' . $row;
                }
                if (is_null($value['sub_class']) || empty($value['sub_class'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter sub class at row ' . $row;
                }
                if (is_null($value['cover_type']) || empty($value['cover_type'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter covertype at row ' . $row;
                }

                
                if (is_null($value['icecash_policy_no']) || empty($value['icecash_policy_no'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter ICECASH Policy Number at row ' . $row;
                }
                
                #check if its an icecash class
                $cls = trim($value['class']);
                $ismtp = ClassModel::where('class', $cls)
                                    ->where('icecash', 'Y')
                                    ->count();

                if ($ismtp < 1) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = "Class $cls at row $row is not mtp class";
                }
            
                if (is_null($value['stamp_duty']) || empty($value['stamp_duty'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter stamp duty  at row ' . $row;
                }
                if (is_null($value['currency']) || empty($value['currency'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter currency  at row ' . $row;
                }else {
                    $currency_exists = Currency::where('currency', $value['currency'])->exists();
                    if(!$currency_exists){
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Currency  not set in the system at row ' . $row;

                    }
                    
                }
                if (is_null($value['currency_rate']) || empty($value['currency_rate'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Currency Rate  at row ' . $row;
                }
                if (is_null($value['government_levy']) || empty($value['government_levy'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter government levy  at row ' . $row;
                }
                if (is_null($value['total_premium']) || empty($value['total_premium'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter total premium at row ' . $row;
                }
               
            }
            #return back incase there is any error
            if (sizeof($errorsList) > 0) {
                $response = json_encode($errorsList);
                
                Session::flash('errorsList', $errorsList);
                return redirect()->back()->with(['errorsList' => $errorsList]);
            }
            $counter = 0;
            $batch_no = IceCash::max('batch_no') + 1;
            $batch_serial  = sprintf('%05d', $batch_no);

            #generate batch serial 
            $batch_serial = (string) $branch . '' . $agent . '' . $batch_serial;
            #insert to mtpmaster
            $mtpmaster =  new IceCashMaster;
            $mtpmaster->batch_serial = $batch_serial;
            $mtpmaster->uploaded_by = $username;
            $mtpmaster->save();



            #insert data
            foreach ($excel_data as $mtp_data) {

                $schem = schemaName();
                $gb = $schem['gb'];
                $gl = $schem['gl'];
                $common = $schem['common'];

                $class = trim($mtp_data['class']);
                $subclass = 1;
                $client_type = trim($mtp_data['client_type']);
                $client = null;
                $classModel = ClassModel::where('class', $class)->first();
                $national_id = trim($mtp_data['national_id']);
                $insured = trim($mtp_data['insured']);
                $period_from = $mtp_data['period_from'];
                $period_to = $mtp_data['period_to'];
                $premiumAmount = trim(str_replace(',', '', $mtp_data['policy_cover']));
        




                $mtpana = new IceCash;

                $mtpana->insured = $insured;
                $mtpana->reg_no = trim($mtp_data['reg_no']);
                $mtpana->make = trim($mtp_data['make']);
                $mtpana->model = trim($mtp_data['model']);
                $mtpana->period_from = $period_from;
                $mtpana->period_to = $period_to;
                $mtpana->phone_no = trim($mtp_data['phone_no']);
                $mtpana->icecash_policy_no = trim($mtp_data['icecash_policy_no']);
                $mtpana->car_type = trim($mtp_data['vehicle_type']);
                $mtpana->national_id = $national_id;
                $mtpana->batch_no = $batch_no;
                $mtpana->batch_serial = $batch_serial;
                $mtpana->class = $class;
                $mtpana->subclass = $mtp_data['sub_class'];
                $mtpana->intermediary = trim($agent);
                $mtpana->branch = $branch;
                $mtpana->client_type = $client_type;
                $mtpana->cover_type = $mtp_data['cover_type'];
                $mtpana->currency =$mtp_data['currency'];
                $mtpana->currency_rate =$mtp_data['currency_rate'];
                $mtpana->stamp_duty = trim($mtp_data['stamp_duty']);
                $mtpana->levy =$mtp_data['government_levy'];
                $mtpana->premium = $premiumAmount;
                $mtpana->total_premium = $mtp_data['total_premium'];
                $mtpana->total_prem = $mtp_data['total_premium'];
                $mtpana->apply_admin_fees = $apply_admin_fee;
                $mtpana->admin_fees_rate = $admin_fee_rate;
                $mtpana->save();
            } #end for each
            #update mtpmaster
            $total_prem = IceCash::where('batch_serial', $batch_serial)->sum('total_premium');
            $count = IceCash::where('batch_serial', $batch_serial)->count();
            IceCashMaster::where('batch_serial', $batch_serial)->update([
                'batch_count' => $count,
                'total_premium' => $total_prem,
                'created' => "Y",
                'intermediary' => trim($agent),
                'branch' => $branch
            ]);


            DB::commit();
            Session::flash('success', 'Batch Upload was successful');
            return redirect()->back();
        } catch (\Throwable $th) {
            // dd($th);
            DB::rollback();
            throw $th;
        }
    }
    public function viewbatchagent($batchserial){
        // dd($batchserial);
        $agents=   Intermediary::where("status","active")->get(['intermediary_number','name']);
        $branch = Branch::all();
 
         return view('gb/underwriting/mtpagentdetails',compact('batchserial','agents','branch'));
 
 
     }

     /**
     * 
     *   Convert CSV file to an array
     * 
     */
    public function readCSV($csvFile)
    {
        // get the file to a variable
        $csv = array_map('str_getcsv', file($csvFile));

        // loop converting this to an assoc array
        array_walk($csv, function (&$a) use ($csv) {
            $a = array_combine($csv[0], $a);
            $csv = array_change_key_case($csv, CASE_LOWER);
        });

        # remove column header
        array_shift($csv);

        return $csv;
    }
    public function mtpTemplate(Request $request)
    {
       
        $file = public_path() . "/downloads/icecash.csv";

        return Response::download($file);
    }
    public function mtpagenttable(Request $request)
    {


        
        // $mtpana = DB::raw(DB::select("select * from mtpana where cancelled <> 'Y' and created = 'Y' order by id desc"));
        $mtpagent = Mtpagent::where(function ($query) {
            $query->whereIn('cancelled',['N','n'])->orWhereNull('cancelled');                  
        })
        ->where("processed","N")
         ->orderBy('id', 'desc')
        ->get();

                    //    dd($mtpagent);
                        
        return Datatables::of($mtpagent)
       
        ->addColumn('action', function ($row){
            $disableProcessBatch = $row->completed === 'N' ? 'disabled' : '';
            
            return '<div style="display: flex; justify-content: space-around;">
                        <button class="btn btn-primary process-data btn-sm" '.$disableProcessBatch.'><i class="fa fa-check" aria-hidden="true"></i> Process Batch</button>
                        <button class="btn btn-xs btn-outline-primary viewagent btn-sm"><i class="fa fa-eye" aria-hidden="true"></i> View Batch</button>

                    </div>';
        })
        
        ->rawColumns(['action'])
         ->addIndexColumn()
        ->make(true);
    } 
    public function mtpagentrectable(Request $request)
    {
        $batchserial = $request->batch;
        
        // $mtpana = DB::raw(DB::select("select * from mtpana where cancelled <> 'Y' and created = 'Y' order by id desc"));
        $mtpana = Mtpagentdtl::where('batch_Serial', $batchserial)->get();
                        
        return Datatables::of($mtpana)
        ->addIndexColumn()
        ->addColumn('agent_name', function($row){
            if($row->intermediary <> null){
                $intermediaryParams = new IntermediaryQueryParams([
                    'branch' => $row->branch,
                    'agentNo' => $row->intermediary
                ]);
                $agent = IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();
                return $agent->name;
            }
            return "-";
           

        })
        -> addColumn('action', function ($row){
            return '<button class="btn btn-primary amend btn-sm"><i class="fa fa-check" aria-hidden="true"></i></i> Amend</button>';
            
        }) 
        ->rawColumns(['action'])
         ->addIndexColumn()
         ->make(true);
    }
    public function processmtprec(Request $request){
      
        DB::beginTransaction();
        
            try {
                $batch_seriall=$request->batch_serial;
                $batchmaster =Mtpagent::where('batch_serial',$batch_seriall)->first();
                $mtpdatas = Mtpagentdtl::where('batch_serial',$batch_seriall)->get();
                      $batch_no = Mtpana::max('batch_no') + 1;
                    $batch_serial  = sprintf('%05d', $batch_no);
                   
                     #generate batch serial 
                     $batch_serial = (string) "BN" . $batch_serial;
                    #insert to mtpmaster
                    $mtpmaster =  new Mtpmaster;
                    $mtpmaster->batch_serial = $batch_serial;
                    $mtpmaster->uploaded_by =$batchmaster->uploaded_by;
                    $mtpmaster->save();
                    foreach ($mtpdatas as $mtpdata) {
                            $mtpana = new Mtpana;
        
                            $mtpana->insured = $mtpdata->insured;
                            $mtpana->reg_no = $mtpdata->reg_no;
                            $mtpana->seat_cap = $mtpdata->seat_cap;
                            $mtpana->tonnage = $mtpdata->tonnage;
                            $mtpana->make = $mtpdata->make;
                            $mtpana->model =$mtpdata->model;
                            $mtpana->chassis_no = $mtpdata->chassis_no;
                            $mtpana->premium = $mtpdata->premium;
                            $mtpana->period_from =$mtpdata->period_from;
                            $mtpana->period_to =$mtpdata->period_to;
                            $mtpana->phone_no =$mtpdata->phone_no;
                            //$mtpana->national_id = $national_id;
                            $mtpana->batch_no = $batch_no;
                            $mtpana->batch_serial = $batch_serial;
                            //$mtpana->passport = $passport;
                            $mtpana->class = $mtpdata->class;
                            $mtpana->subclass = $mtpdata->subclass;
                            //$mtpana->driving_license = $driving_license;
                            $mtpana->intermediary =$mtpdata->intermediary;
                            $mtpana->branch=$mtpdata->branch;
                            $mtpana->sticker_serial = $mtpdata->sticker_serial;
                            $mtpana->tin_number = $mtpdata->tin_number;
                            $mtpana->pin_number = $mtpdata->tin_number;
                            $mtpana->client_type = $mtpdata->client_type;
                            $mtpana->cover_type = 4;
                            $mtpana->system_vat = $mtpdata->system_vat;
                            $mtpana->system_charge =$mtpdata->system_charge;
                            $mtpana->cert_type = $mtpdata->cert_type;
                            $mtpana->vat = $mtpdata->vat;
                            $mtpana->vat_amount = $mtpdata->vat_amount;
                            $mtpana->levy = $mtpdata->levy;
                            $mtpana->stamp_duty = $mtpdata->stamp_duty;
                            $mtpana->sticker_fees = $mtpdata->sticker_fees;
                            $mtpana->training_levy_flag = $mtpdata->training_levy_flag;
                            $mtpana->stamp_duty_flag =  $mtpdata->stamp_duty_flag;
                            $mtpana->total_premium = $mtpdata->total_premium;
                            $mtpana->save();
                           
                            
                    }
        
                    #update mtpmaster
        
                    $total_prem = Mtpana::where('batch_serial',$batch_serial)->sum('total_premium');
                    $count = Mtpana::where('batch_serial',$batch_serial)->count();
                    Mtpmaster::where('batch_serial',$batch_serial)->update([
                        'batch_count'=>$count,
                        'total_premium'=>$total_prem,
                        'created'=>"Y",
                    ]);
                    Mtpagent::where('batch_serial',$batch_seriall)->update([
                        "processed"=>"Y"
                    ]);
                  
                    DB::commit();
                    return[
                        "status"=>1
                    ];

               
            } catch (\Throwable $th) {
                //throw $th;
                DB::rollback();
                dd($th);
               // \Log::debug('MTP error',$th->getMessage());
                return $th->getMessage();
                //return response()->json(['error' => $th->getMessage()], 500);
                //Log::info('User logged in successfully.');
               

                //dd($th);

            }


            
            


    }
    public function amendagent(Request $request){
    
     
        $agent=$request->agent;
        $branch=$request->branch;
   
        Mtpagentdtl::where('batch_no',$request->batchno)
        ->where('reg_no',$request->reg_no)
        ->update([
           "intermediary"=>$agent,
           "branch"=>$branch,
           "error_log"=>null,
           'completed'=>"Y"
        ]);
        $allrec = Mtpagentdtl::where('batch_no',$request->batchno)->count();
        $allreccomplete = Mtpagentdtl::where('batch_no',$request->batchno)->where('completed',"Y")->count();
      
        if($allrec= $allreccomplete){
            $rec = Mtpagentdtl::where('batch_no',$request->batchno)->first();

            Mtpagent::where("batch_serial",$rec->batch_serial)->update([
                'completed'=>"Y"
                
            ]);

        }
        return[
            "status"=>1
        ];
    }
    public function processMtpStickers(Request $request)
    {
        $batch_serial = $request->batch_serial;
       
        $sticker_res = $this->issueMTPStickers($batch_serial);

        return $sticker_res;
        
    }
    public function issueMTPStickers($batch_serial)
    {

      //$batch_serial=$batch_serial->batch_serial;
        // get records with the batch serial
        $batch_records = Mtpana::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")->get();
        $username =  Auth::user()->user_name;
  
        
        // check if there is any record before we loop
        if (count($batch_records) > 0) {
            try {
                DB::beginTransaction();
                // issue certs
                $res = [];
                foreach ($batch_records as $batch_record) {
    
                    // fetch the models required for the process
                    $dcontrol = Dcontrol::where('endt_renewal_no', $batch_record->endt_renewal_no)->first();
                    
                    $classModel = ClassModel::where('class', $dcontrol->class)->first();
                    // $modtl = Modtlend::where('endt_renewal_no', $batch_record->endt_renewal_no)->where('reg_no', trim($batch_record->reg_no))->first();
                    $modtl = Modtlpivot::where('policy_no',$batch_record->policy_no)
							   ->where('endt_renewal_no',$batch_record->endt_renewal_no)
							   ->where('reg_no', trim($batch_record->reg_no))
							   ->first();

			        $modtlsumm = Modtlsumm::where('policy_no',$batch_record->policy_no)
								  ->where('endt_renewal_no',$batch_record->endt_renewal_no)
								  ->where('reg_no', trim($batch_record->reg_no))
								  ->first();

                    $sticker_serial = (int)$batch_record->sticker_serial;
    
                    // certificate sticker addition
                    $cert_type = "C";
                   
                    $certificates =  Certmast::whereRaw("trim(cert_type)='" . trim($cert_type) . "' AND cert_status =0   AND agent ='".(int)$dcontrol->agent."' AND branch ='".(int)$dcontrol->branch."' AND cert_no ='".$sticker_serial."'")->get();
                   
                    
                    // check if there are certs in certmast with that arguments 
                    if (count($certificates) > 0) {
                        //get acdet record
                        $certificates=$certificates[0];
                        $most_update_acdet_record = Acdet::where('doc_type', 'DRN')
                            ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                            ->orderBy('dola', 'DESC')
                            ->orderBy('time', 'DESC')
                            ->take(1)
                            ->get();
                        $most_update_acdet_record = $most_update_acdet_record[0];
    
                        //add record to certalloc
                        $certalloc = new Certalloc;
                        $certalloc->cert_type = $cert_type;
    
                        $certalloc->cert_no = $certificates->cert_no;
                        $certalloc->branch = $dcontrol->branch;
                        $certalloc->agent = $dcontrol->agent;
                        $certalloc->sum_insured=$modtlsumm->sum_insured;
						$certalloc->total_premium=$modtlsumm->endorse_amount;
                        $certalloc->date_issued = Carbon::today();
                        $certalloc->insured = $dcontrol->insured;
                        $certalloc->reg_no = $modtl->reg_no;
                        $certalloc->cover_type = (int)$batch_record->cover_type;
                        $certalloc->eng_no = $modtl->eng_no;
                        $certalloc->chassis_no = $modtl->chassis_no;
                        $certalloc->period_from = $dcontrol->period_from;
                        $certalloc->period_to = $dcontrol->period_to;
                        $certalloc->policy_no = $dcontrol->policy_no;
                        $certalloc->branch_btch = $certificates->branch_btch;
                        $certalloc->agent_btch = $certificates->agent_btch;
                        $certalloc->dola = Carbon::today();
                        $certalloc->trans_type = $dcontrol->trans_type;
                        $certalloc->endt_renewal_no = $dcontrol->endt_renewal_no;
                        $certalloc->class = $modtl->class;
                        $certalloc->add_premium = ($most_update_acdet_record->nett * -1);
                        $certalloc->sum_insured = $modtl->sum_insured;
                        $certalloc->total_premium = 0;
                        $certalloc->user_name = $username;
                        $certalloc->issue_time = Carbon::now();
                        $certalloc->make = $modtl->make;
                        $certalloc->capacity = $modtl->capacity;
                        $certalloc->carry_cap = $modtl->carry_cap;
                        //$certalloc->space1=
                        $certalloc->colour = $modtl->colour;
                        $certalloc->man_date = $modtl->man_date;
                        $certalloc->itp_injury_person = 0;
                        $certalloc->itp_injury_event = 0;
                        $certalloc->itp_property = 0;
                        $certalloc->body_type = $modtl->body_type;
                        $certalloc->model = $modtl->model;
                        $certalloc->receipt_ref = $most_update_acdet_record->reference; //to be confirmed
    
                        $certalloc->location = $modtl->location;
                        $certalloc->receipt_no = $most_update_acdet_record->reference;
                        $certalloc->cert_print_yn = 'N';
                        $certalloc->ln_no = $modtl->ln_no;
                        $certalloc->credit_amount = 0;
                        $certalloc->rec_amount = $most_update_acdet_record->allocated;
                        $certalloc->crm_flag = 'N';
                        $certalloc->cert_status = 1;
    
    
                        //UPDATE CERT MAST
                        //$certificates->cert_status=1;
                        $cert_type=trim($certificates->cert_type);
                   
                        
                       $dd = Certmast::where('cert_no',$certificates->cert_no)
                                                ->whereRaw("trim(cert_type)='".$cert_type."'")
                                                ->update([
                                    'cert_status'=>1,
                                    'reg_no'=>$modtl->reg_no,
                                    'endt_renewal_no'=>$dcontrol->endt_renewal_no,
                                    'insured'=>$dcontrol->insured,
                                    'cover_type'=>(int)$batch_record->cover_type,
                                    'eng_no'=>$modtl->eng_no,
                                    'period_from'=>$request->date_from,
                                    'period_to'=>$request->cert_expiry,
                                    'policy_no'=>$dcontrol->policy_no,
                                    'class'=>$dcontrol->class,   
                                    'carry_cap'=>$modtl->carry_cap,
                                    'capacity'=>$modtl->capacity,
                                    'add_premium'=>($most_update_acdet_record->nett * -1)
                                ]);
    
    
                        $certalloc->save();

                        if (!$certalloc->save()) {
                            $msg = 'Error saving certificate '.$certificates->cert_no.' for Reg_no '.$modtl->reg_no;
                        } else {
                            $msg = 'Success saving certificate '.$certificates->cert_no.' for Reg_no '.$modtl->reg_no;
                        }

                        array_push($res, $msg);                        

                        // certificate sticker addition // end
                    } else {
                         $certificates_issued =  Certmast::whereRaw("trim(cert_type)='" . trim($cert_type) . "' AND cert_status =1   AND agent ='".(int)$dcontrol->agent."' AND branch ='".(int)$dcontrol->branch."' AND cert_no ='".$sticker_serial."'")->count();

                        if($certificates_issued > 0){
                            $msg = "Sticker  $sticker_serial Already issued for agent $dcontrol->agent and branch $dcontrol->branch" ;
                            array_push($res, $msg);

                        }else{
                            $msg = "Sticker  $sticker_serial not found in the system for agent $dcontrol->agent and branch $dcontrol->branch" ;
                            array_push($res, $msg);

                        }
                                                    

                            
                    }
                    
    
                    
                }
                DB::commit();
                // end foreach
                return [
                    'code' => 1,
                    'msg' => $res       
                ];
            } catch (\Throwable $th) {
                DB::rollback();
                throw $th;
                return [
                    'code' => -1,
                    'msg' => 'Fatal error',
                    'data' => $th,
                ];
            }
        }else {
            return [
                'code' => -1,
                'msg' => 'No records found with the given batch serial',
                'data' => null,
            ];
        }
        
    }
    public function debitMtpBatch(Request $request)
    {
    
        $batch_serial = $request->batch_serial;
        // dd(1);
        DB::beginTransaction();
        try {
            $batch_records = IceCash::whereRaw("trim(batch_serial) = '" . $batch_serial . "'")->get();
            if (count($batch_records) < 1) {
                return [
                    'status' => 0,
                    'msg' => 'The record was not found',
                ];
            }
    
            $debit = new Debit;
            $policy = new Policy;
            $pipcnam = Pipcnam::where('record_type', 0)->first();
            $debit_feedback = [];
            $len = count($batch_records);  
          
            foreach ($batch_records as $batch_record) {
                // do an express debit
                $endt_renewal_no = $batch_record->endt_renewal_no;
                $debited = Debitmast::where('endt_renewal_no',$endt_renewal_no)->count();
                // $batch_record->update(['debited' => 'Y']);
                $user_name = trim(Auth::user()->user_name);
                $reinsured="N";
                $pre_debit="N";
                
                // dd($efris_api);
                $success_debit='N';
                 $debitrequest = new Request;
                 $debitrequest->merge([
                    "endt_renewal_no"=>$endt_renewal_no,
                    "interactive"=>$reinsured,
                    "pre_debit"=>$pre_debit,
                    "user_name"=>$user_name
                 ]);
               
            if($debited < 1){
                $debit_state = $debit->debit_endorsement($endt_renewal_no,$user_name,$reinsured,$pre_debit);
                //dd($debit_state);
                // response messages for debit process
             
                
                if ($debit_state->status == 1) {
                  
                    $success_debit='Y';
                    $msg = "Success Debit note generated for Policy $endt_renewal_no";
                    IceCash::whereRaw("trim(endt_renewal_no) = '" . $endt_renewal_no . "'")
                    ->update([
                        "debited"=>"Y"
                    ]);
                }else {
                    $msg = "Failed to generate Debit Note   for Policy $endt_renewal_no"; 
                    $success_debit='N';
                   
                }


             }else{
                $msg = "Policy $endt_renewal_no Already Debited"; 
                $success_debit='N';
             }
                
             
                
             
                array_push($debit_feedback, $msg);
    
            }
            
            
            $debited_records = DB::SELECT("SELECT *  FROM DEBITMAST d WHERE ENDT_RENEWAL_NO IN (SELECT ENDT_RENEWAL_NO  FROM icecash WHERE BATCH_SERIAL ='$batch_serial')");
            // if(count($debited_records)  == $len){
            //     IceCashMaster::where('batch_serial',$batch_serial)->update([
            //         'debited'=>"Y",
            //         'debited_by'=>Auth::user()->user_name
                   
            //     ]);
            //     DB::commit();


            // }else{
            //     DB::rollback();

            // }    
            IceCashMaster::where('batch_serial',$batch_serial)->update([
                        'debited'=>"Y",
                        'debited_by'=>Auth::user()->user_name
                       
                    ]);
            DB::commit();
          
            return  $debit_feedback;
        }catch (\Throwable $th) {
            DB::rollback();
            throw $th;

        }
        


    }
    public function mtpRecs()
    {
      
        return view('gb/underwriting/mtpreport');
    }
    public function mtpreportTemplate(Request $request)
    {
       
        $file = public_path() . "/downloads/mtpreport_template.csv";

        return Response::download($file);
    }
    public function stageMtpReport(Request $request){
        DB::beginTransaction();
     
        try {
            $fileData = $request->file('mtp_report');
            $fileExtension = $fileData->getClientOriginalExtension();
            if($fileExtension == "csv" || $fileExtension == "xls" || $fileExtension == "xlsx"){
                // $excel_data = (readCSV($FileData));
                $excel_data = FileUploadManager::excelUpload($fileData);

            }else {
                Session::flash('error','Uploaded File Type Not Supported, Use a CSV, XLS or XLSX file! ');
                
                return redirect()->back();
            }
            $errorsCount = 0;
            $errorsList = array();
            $vehicle_reg_nos = [];
            $row = 1;
            foreach ($excel_data as $value) {
              //dd($value);
               // $value = array_change_key_case($value, CASE_LOWER);
                $row++;

                if (is_null($value['policy_no']) || empty($value['policy_no'])) {

                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter policy Number at row ' . $row;
                }
                
                if (is_null($value['period_from']) || empty($value['period_from'])) {

                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter period from at row ' . $row;
                }
          
                if (is_null($value['period_to'])|| empty($value['period_to'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter period_to at row ' . $row;
                }
                if (is_null($value['period_to'])|| empty($value['period_to'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter period_to at row ' . $row;
                }
                if (is_null($value['reg_no'])|| empty($value['reg_no'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter registration no at row ' . $row;
                }
                if (!in_array($value['reg_no'], $vehicle_reg_nos)) {
                    array_push($vehicle_reg_nos, $value['reg_no']);
                } else {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Duplicate Registration  number in excel document at Row ' . $row;
                }
                
                $findSlashes_period_to = strstr($value['period_to'], '-');
                $findSlashes_period_from = strstr($value['period_from'], '-');
               
                $validpol =Debitmast::where('policy_no',$value['policy_no'])->exists();
                $validreg =Motcvrdet::where('reg_no',$value['reg_no'])->where('cancelled','N')->exists();
                if(!$validreg){
                    $errorsCount++;
                    $errorsList[$errorsCount] = "Registration number ". $value['reg_no'] ." at $row  does not exist in the system";
                }

                if(!$validpol){
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Policy '.$value['policy_no']. "at  $row does not exist in the system";
                }else{
                    if (!$findSlashes_period_to || !$findSlashes_period_from) {
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Please enter the dates in this format YYYY-MM-DD at row ' . $row;
                    }else{
                        $policy_no=$value['policy_no'];
                        $period_from=$value['period_from'];
                        $period_to=$value['period_to'];
                        $reg_no=$value['reg_no'];
                        
                        $vehcheck =(object)$this->checkpolicy($policy_no,$period_from,$period_to,$reg_no);
                        
 
                        if(!$vehcheck->polexist){
                            $errorsCount++;
                            $errorsList[$errorsCount] = "Policy Number $policy_no at $row does not exists in the system for the period $period_from to $period_to";
    
                        }else{
                            if(!$vehcheck->vehexist){
                                $errorsCount++;
                                $errorsList[$errorsCount] = "Vehicle With Registation number $reg_no at $row does not exists in the system for policy $policy_no";
            
                            } 
                        }
    
                    }

                }
                
                
                if (is_null($value['cover_type'])|| empty($value['cover_type'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Cover Type at row ' . $row;
                }
                if (is_null($value['sticker_number'])|| empty($value['sticker_number'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Sticker number at row ' . $row;
                }
                if (is_null($value['third_party_premium'])|| empty($value['third_party_premium'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Premium at row ' . $row;
                }
                
                
                                
                



            }
            #return back incase there is any error
            if (sizeof($errorsList) > 0) {
                $response = json_encode($errorsList);
                $response_array = json_decode($response);
                //IF ERRORS RETURN BACK
                Session::flash('errorsList', $errorsList);
                return redirect()->back()->with(['errorsList' => $errorsList]);
            }
             #insert to master table\
             $username =  Auth::user()->user_name;
             $batch_no = MtpReport::max('id') + 1;
             $batch_serial  = sprintf('%05d', $batch_no);
            
              #generate batch serial 
              $batch_serial = (string) "MTPR$batch_serial";
             #insert to mtpmaster
             $mtpreport =  new MtpReport;
             $mtpreport->batch_serial = $batch_serial;
             $mtpreport->uploaded_by = $username;
             $mtpreport->save();
    
             foreach ($excel_data as $mtprep_data) {
               

                #insert to details table
                $mtp_rec_dtl = new MtpreportDtl;
                $mtp_rec_dtl->batch_serial=$batch_serial;
                $mtp_rec_dtl->policy_no=$mtprep_data['policy_no'];
                $mtp_rec_dtl->period_from=$mtprep_data['period_from'];
                $mtp_rec_dtl->period_to=$mtprep_data['period_to'];
                $mtp_rec_dtl->reg_no=$mtprep_data['reg_no'];
                $mtp_rec_dtl->cover_type=$mtprep_data['cover_type'];
                $mtp_rec_dtl->sticker_number=$mtprep_data['sticker_number'];
                $mtp_rec_dtl->premium=$mtprep_data['third_party_premium'];
                $mtp_rec_dtl->dola=Carbon::now();
                $mtp_rec_dtl->save();
                
             }
      
             $count = MtpreportDtl::where('batch_serial',$batch_serial)->count();
             MtpReport::where('batch_serial',$batch_serial)->update([
                'batch_count'=>$count
            ]);
            DB::commit();
            Session::flash('success', 'Batch Upload was successful');
            return redirect()->back();


        } catch (\Throwable $th) {
           
            DB::rollback();
            throw $th;

        }
    }
    public function mtpreportlist(Request $request)
    {



        $mtpana = MtpReport::where(function ($query) {
            $query->whereIn('cancelled',['N','n'])->orWhereNull('cancelled');                  
        })
                         ->where('processed', "N")
                        ->orderBy('id', 'desc')
                        ->get();

                    //    dd($mtpana);
                        
        return Datatables::of($mtpana)
    
        -> addColumn('action', function ($row){
            return '<button class="btn btn-xs btn-primary create_proforma"><i class="fa fa-check" aria-hidden="true"></i></i> Process batch</button>
            <button class="btn btn-xs btn-primary view"><i class="fa fa-eye" aria-hidden="true"></i></i> View</button> <button class="btn btn-xs btn-default cancel_proforma"><i class="fa fa-times" aria-hidden="true"></i></i> Cancel Batch</button>';
            
        }) 
        ->rawColumns(['action'])
         ->addIndexColumn()
            ->make(true);
    } 
    public function viewmtpdtls($batchserial){
        // dd($batchserial);
 
         return view('gb/underwriting/mtpreportdetails',compact('batchserial'));
 
 
     }
     public function mtpreportdetailstable(Request $request)
    {

        $batchserial = $request->batch;
        
        // $mtpana = DB::raw(DB::select("select * from mtpana where cancelled <> 'Y' and created = 'Y' order by id desc"));
        $mtpdtls = MtpreportDtl::where('batch_Serial', $batchserial)->get();
                        
        return Datatables::of($mtpdtls)
         ->addIndexColumn()
         ->make(true);
    }
    public function processmtpReport(Request $request){

                 DB::beginTransaction();
            try {
                $batch_serial = $request->batch_serial;
                $mtpdtls = MtpreportDtl::where('batch_serial', $batch_serial)->get();
                    
                foreach ($mtpdtls as $value) {
                    
                        $latest_rec = Debitmast::select('endt_renewal_no','policy_no','period_from','period_to')
                                            ->where('policy_no',$value->policy_no)
                                            ->where('period_from' ,$value->period_from) 
                                            ->where('period_to' ,$value->period_to)
                                            ->whereIn('entry_type_descr',['POL','REN','RNS','INS'])
                                            ->orderBy('dtrans_no','desc')
                                            ->first();
                        
                        $dcontrol = Dcontrol::where('endt_renewal_no',$latest_rec->endt_renewal_no)
                                            ->first();
                    

                        $active_endorse_no =Dcontrol::where('policy_no',$latest_rec->policy_no)
                                                        ->where('cancelled','<>','Y')
                                                        ->where('dtrans_no','>=',$dcontrol->dtrans_no)
                                                        ->orderBy('dcon_no','desc')       
                                                        ->first()->endt_renewal_no;
                                                    

                        $motcvrdet=Motcvrdet::where('endt_renewal_no',$active_endorse_no)
                                            ->where('reg_no',$value->reg_no)
                                            ->where('cancelled','N')
                                            ->where('grp_code','BSP')
                                            ->first();
                                        

                        $annual_prem=$motcvrdet->annual_premium;
                        
                        switch ($dcontrol->ast_marker) {
                            case 'S':
                                $endorse_amount = ($dcontrol->endt_days / $dcontrol->days_covered) * $annual_prem;
                                if ($dcontrol->short_term_method == 'S') {
                                    $endorse_amount = ($dcontrol->short_term_percent * $annual_prem)/100;
                                }
                                break;
                            case 'T':
                                $endorse_amount = ($dcontrol->endt_days / $dcontrol->days_covered) * $annual_prem;
                                break;
                            
                            default:
                                $endorse_amount = $annual_prem;
                                break;
                        }
                        
                        #premium Breakdowns
                        $third_party_prem = $value->premium;

                        $own_damage_prem = $endorse_amount-$third_party_prem;
                    $del =MotorReporting::where('policy_no',$value->policy_no)
                                            ->where('reg_no',$value->reg_no)
                                            ->where('period_from' ,$value->period_from) 
                                            ->where('period_to' ,$value->period_to)
                                            ->delete();

                        #populate repoting table
                        MotorReporting::insert([
                        "policy_no"=>$value->policy_no,
                        "period_from"=>$value->period_from,
                        "period_to"=>$value->period_to,
                        "reg_no"=>$value->reg_no,
                        "cover_type"=>$value->cover_type,
                        "sticker_number"=>$value->sticker_number,
                        "third_party_prem"=>$third_party_prem,
                        "own_damage_prem"=>$own_damage_prem,
                        "total_prem"=>$endorse_amount,
                        "processed_by"=>Auth::user()->user_name,
                        "processed_date"=>Carbon::now()

                        ]);
                    MtpreportDtl::where('batch_serial',$batch_serial)
                                 ->where('reg_no',$value->reg_no)
                                 ->update([
                                   'processed'=>'Y'
                                 ]);

                
                                                

                }
                MtpReport::where('batch_serial',$batch_serial)
                ->update([
                  'processed'=>'Y',
                  'processed_by'=>Auth::user()->user_name
                ]);
                DB::commit();
                return[
                    "status"=>1,
                    "msg"=>"Batch Processed Succesfully"
                ];
            } catch (\Throwable $th) {
                DB::rollback();
                dd($th);
                return[
                    "status"=>-1,
                    "msg"=>$th
                ];
            }


    }
    public function processedtrans(Request $request)
    {
        
    
        $motorreport= MotorReporting::all();

                
                        
        return Datatables::of($motorreport)
        ->addColumn('cov_type', function($row){
            $cov=  Covertype::where('cover',$row->cover_type)
            ->first()->cover_description;
            return $cov;

        })
         ->addIndexColumn()
            ->make(true);
    } 
    public function checkpolicy($policy_no,$period_from,$period_to,$reg_no){
        $polexist = Debitmast::select('endt_renewal_no','policy_no','period_from','period_to')
                                            ->where('policy_no',$policy_no)
                                            ->where('period_from' ,$period_from) 
                                            ->where('period_to' ,$period_to)
                                            ->whereIn('entry_type_descr',['POL','REN','RNS','INS'])
                                            ->orderBy('dtrans_no','desc')
                                            ->get();
          if(count($polexist) < 1){
                return [
                    'polexist'=>false
                ];
          }
          $latest_rec=$polexist[0];              
      
          $dcontrol = Dcontrol::where('endt_renewal_no',$latest_rec->endt_renewal_no)
                                            ->first();
                    

          $active_endorse_no =Dcontrol::where('policy_no',$latest_rec->policy_no)
                                        ->where('cancelled','<>','Y')
                                        ->where('dtrans_no','>=',$dcontrol->dtrans_no)
                                        ->orderBy('dcon_no','desc')       
                                        ->first()->endt_renewal_no;
                                                    

        $motcvrdet=Motcvrdet::where('endt_renewal_no',$active_endorse_no)
                                            ->where('reg_no',$reg_no)
                                            ->where('cancelled','N')
                                            ->where('grp_code','BSP')
                                            ->exists();
        if($motcvrdet){
            return [
                'polexist'=>true,
                'vehexist'=>true
            ];

        }else{
            return [
                'polexist'=>true,
                'vehexist'=>false
            ];
        }
    }
    public function cancelMtprpt(Request $request){
        $batch_serial =trim($request->batch_serial);
      try {
      
        $mtmast= MtpReport::where('batch_serial',$batch_serial)->update([
            'cancelled'=>'Y'
        ]);
        $mtmast= MtpReportDtl::where('batch_serial',$batch_serial)->update([
            'cancelled'=>'Y'
        ]);
        return[
            'status'=>1,
            'msg'=>"Cancellation Successful"
        ];
      } catch (\Throwable $th) {
        throw $th;
      }

    }




}