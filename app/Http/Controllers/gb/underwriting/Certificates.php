<?php
namespace App\Http\Controllers\gb\underwriting;
use Auth;
use App\User;
use App\Acdet;
use App\Agmnf;

use App\Modtl;
use App\Client;
use App\Certreq;
use App\Doctype;
use App\Pipcnam;
use App\Cbcredit;
use App\Certmast;
use App\Certtype;
use App\Dcontrol;
use App\Certalloc;
use App\Covertype;
use App\Debitmast;
use App\Polmaster;
use App\SendEmail;
use Carbon\Carbon;
use App\CertStatus;
use App\ClassModel;
use App\Aimsuprofgb;
use App\Certtypedtl;
use App\Aimsuser_web;
use App\Escalate_pol;
use App\Certificate_log;
use App\Models\Modtlhist;
use App\Models\Modtlmast;
use App\Models\Modtlsumm;
use App\Models\Modtlpivot;
use Illuminate\Http\Request;
use Yajra\Datatables\Datatables;
use App\Cert_serials_unallocated;
use Illuminate\Support\Facades\DB;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Gate;
use App\Services\IntermediaryQueryService;
use Aimsoft\UserManagement\Models\Permission;
use App\ValueObjects\IntermediaryQueryParams;
use App\Models\UwParameter;

class Certificates extends Controller{

	public function checkCertIssue(Request $request)
	{
		$status = 0;
		$message = '';

    	$allowed = "N";

		if(Gate::check('issue-motor-certificate')){
			$allowed = "Y";
			$status = 1;
		}
		
		if(!Gate::check('issue-motor-certificate')){
			$allowed = "N";
			$status = 0;
			$message = 'You do not have the rights to issue Certificate';
		}
	    		
	    return [
			'status' => $status,
			'rights' => $allowed,
			'message' => $message
		];
	}


	public function fetchOutstandingPrem(Request $request)
	{
		$endt_no = $request->get('endt_no');

		$acdet = Acdet::select('nett','allocated','unallocated')
						->where('doc_type','DRN')
						->where('endt_renewal_no',$endt_no)
						->first();
		return $acdet;
	}

	public function getOutstandingPrem(Request $request)
	{


		$endt_no = $request->get('endt_no');

		$acdet = Acdet::where('doc_type','DRN')
						->where('endt_renewal_no',$endt_no)
						->get();
		// return $endt_no;
						
		// if(sizeof($acdet)>0){
		// 	return $acdet[0];
		// }
		return $acdet;
		

		
	}
	public function getCertCreditStatus(){

		//check credit status
		$certStatus= Pipcnam::first()->get(['cert_partial_payment','min_cert_percentage'])[0];

		return $certStatus;
	}
	
	public function getCredits(Request $request)
	{
		$endt_no = $request->get('endt_no');
		$cbcredit = Cbcredit::where('claim_no',$endt_no)
							->where('entry_type_descr','PRM')
							->orderBy('created_time','DESC')
							// ->where('cancelled_reference',null)
							->get();
			// dd($cbcredit);
		echo ($cbcredit);
	}

	public function fetchCredits(Request $request)
	{
		$endt_no = $request->get('endt_no');
		$cbcredit = Cbcredit::where('claim_no',$endt_no)
							->where('entry_type_descr','PRM')
							->orderBy('created_time','DESC')
							// ->where('cancelled_reference',null)
							->first();
		
		return $cbcredit;
	}


	public function getCertApproval($endt_no)
	{
		$cert_approval = Escalate_pol::select('approved')
											->where('endorse_no', $endt_no)
											->where('escalate_type',9)
											->orderBy('created_at', 'DESC')
											->first();
		return  $cert_approval;
	}


	public function PreCertIssueChecks(Request $request)
	{
		$endt_no = $request->endt_no;
		$debited = Debitmast::where('endt_renewal_no',$endt_no)->count();
		//check user rights
		$cert_issue = $this->checkCertIssue($request);
		// dd($cert_issue);
		if($cert_issue['status'] == 0){
			return [
				'status' => $cert_issue['status'],
				'message' => $cert_issue['message']
			];
		}
		
		//Stop if user has no rights
		if($cert_issue['rights'] == 'N'){
			return [
				'status' => 0,
				'message' => $cert_issue['message'],
			];
		}
		// check special approval
		$special_approval = $this->getCertApproval($endt_no);

		//check outstanding prem
		$acdet_os = $this->fetchOutstandingPrem($request);
		//check cert credit status
		$creditStatus = $this->getCertCreditStatus();
		//Get credit
		$credits = $this->fetchCredits($request);
		//Check is partial is allowed
		$partial = $creditStatus->cert_partial_payment;

		//calculate percentage payed


		if($acdet_os->nett == 0 || $acdet_os->nett == null){

		    $percentpayed = 0;

		}

		if(!($acdet_os->nett == 0 || $acdet_os->nett == null)){

		    $percentpayed = ($acdet_os->allocated/$acdet_os->nett)*100;

		}

		if($special_approval != null && $special_approval->approved == 'D' && $debited == 0 
		   && (($credits != null && $credits->cancelled =='Y') || $credits == null )){

			return [
				'status' => 0,
				'message' => 'Special certificate approval request was declined!'
			];
		}

		if($debited == 1 && $acdet_os->unallocated > 0){
			
			if($partial == 'Y' && $credits == null && $acdet_os->allocated > 0){
				if($percentpayed < $creditStatus->min_cert_percentage){
					return [
						'status' => 0,
						'message' => 'Payment is below '. $creditStatus->min_cert_percentage .' %'
					];
				}
			}
		}

		if($credits!= null && $credits->manager == null && $credits->cancelled !='Y'){
			return [
				'status' => 0,
				'message' => 'Credit Booked But Not Approved....Forward to Manager For Approval'
			];
		}
		
		if($credits != null && $credits->cancelled == 'Y' && $special_approval == null ){
			return [
				'status' => 0,
				'message' => 'Credit booked but declined....Kindly consult manager for explanation'
			];
		}

		if($credits != null && $credits->cancelled == 'Y' && $special_approval != null && $special_approval->approved == null ){
			return [
				'status' => 0,
				'message' => 'Credit booked but declined and Special certificate issue requested but not approved/declined!'
			];
		}

		if(($debited == 0 && $credits == null && $special_approval->approved == null) || 
		   ($debited == 1 && $credits == null && $acdet_os->allocated == 0)) {
			return [
				'status' => 0,
				'message' => 'No Special approval NOR Receipt Done NOR Credit Authority To Issue Certificate!'
			];
		}


		return [
			'status' => 1
		];

	}
 
	public function issue_bak(Request $request){
 		//dd($request->all());
		$endt_renewal_no=$request->input('cert_endt');
		$cert_no_from=$request->input('serial_from');
		$cert_no_to=$request->input('serial_to');
		//$cert_type=$request->input('cert_type');
		$reg_no=$request->input('reg_no');
		$geo_limit=$request->input('geo_limit');
		$receipt_amt=$request->input('cert_receipt');
		$credit_amt=$request->input('credit_amt');
		$no_of_veh=$request->input('no_of_veh');
		$no_of_certs=$request->input('no_of_certs');
		$apply_certs=$request->apply;

		$dcontrol=Dcontrol::where('endt_renewal_no',$endt_renewal_no)->get()[0];

		//client
		$client = Client::where('client_number',$dcontrol->client_number)->get()[0];

		// check if telephone is 9 digits
		// if (strlen(trim($client->telephone)) != 10) {
		// 	return [
		// 		"code" => -1,
		// 		"msg" => "Set Telephone Number of this Client in this format 0700000000. current telephone".(trim($client->telephone))
		// 	];
		// }


		// check if there is vehicles checked to apply certs
		
		$selected_cars = 0;
		
		if(isset($apply_certs)){
			for($i=0; $i <count($apply_certs) ; $i++){
				if ($apply_certs[$i] == 1) {
					$selected_cars += 1; 
				}
			}
		}
		
		
		if ($selected_cars < 1) {
			return [
				"code" => -1,
				"msg" => 'Please check atleast one vehicle to issue cert on apply checkbox' 
			];
		}



		$cert_no_current=$cert_no_from;

        //error/success message array
		$error=array();
        
		
		$cert_status=Auth::user()->user_certificate;

		//    dd($cert_status);

		
		$class_checkType=ClassModel::where('class', $dcontrol->class)
					->get()[0];

		//test Internet connectivity with a website
		$domain="www.google.com";

		//verify whether the internet is working or not
		if ($this->check_internet($domain)){
			if($class_checkType->cert_type != null && isset($apply_certs)){
				
				for($i=0; $i <count($apply_certs) ; $i++){
					
					// take the checked vehicles
					if ((int)$apply_certs[$i] == 1) {
						
						// GET CERTIFICATES
						//get certificate type from class
						$modtl=Modtlpivot::where('policy_no',$dcontrol->policy_no)
										 ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
										 ->where('reg_no',$reg_no[$i])
										 ->get();

						$modtlsumm=Modtlsumm::where('policy_no',$dcontrol->policy_no)
										 ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
										 ->where('reg_no',$reg_no[$i])
										 ->first();
	
	
	
						$class=ClassModel::where('class',$modtl[0]->class)
										->get(['cert_type']);
	
						$cert_type=$class[0]->cert_type;

						
			
						//CHECK USER STATUS
						$certificates = null;
					
						$username=trim(Auth::user()->user_name);

						if($cert_status=='Y'){
							
			
							
							$class=ClassModel::where('class',$modtl[0]->class)
								->get(['cert_type']);
			
							$cert_sub_type=ClassModel::where('class',$modtl[0]->class)
								->get(['cert_sub_type']);
			
								$cert_no_c = (int)$cert_no_current -1 ;
								
							$cert_type=$class[0]->cert_type;
			
								
									
							$certificates=Certmast::whereRaw("trim(cert_user)='".$username."' AND cert_status<> 99 and cert_status <> 1")
								->where('cert_no','>',$cert_no_c)->whereRaw("trim(cert_type)='".$cert_type."'")->take(count($reg_no))->get();

								dd($certificates, $cert_status,$cert_no_current, isset($certificates), count($certificates),"Q");
			
						}
			
						//AGENT
						//dd(isset($certificates));

						if(!isset($certificates) || count($certificates)<1){
							
							$cert_no_c = (int)$cert_no_current -1 ;
						
							$certificates=Certmast::where('agent',$dcontrol->agent)->whereRaw("trim(cert_type)='".$class[0]->cert_type."' AND (cert_status <> 99 and cert_status <> 1 ) AND cert_no >'".$cert_no_c."'")
													->where('BRANCH',$dcontrol->branch)
													->take(count($reg_no))
													->get();

													//dd($certificates, $cert_status,$cert_no_current, isset($certificates), count($certificates),"A");
						}
			
			
			
						//  //BRANCH   
						if(isset($certificates) && count($certificates)<1){
							$cert_no_c = (int)$cert_no_current -1 ;
							
							$certificates=Certmast::where('BRANCH',$dcontrol->branch)
												->whereRaw("trim(cert_type)='".$class[0]->cert_type."' AND (cert_status <> 99 and cert_status <> 1 ) AND cert_no > '".$cert_no_c."'")
												->take(count($reg_no))
												->get();	
												
												//dd($certificates, $cert_status,$cert_no_current, isset($certificates), count($certificates), "A2");
						}

						//COMPANY
						if(isset($certificates) && count($certificates)<1){
							$cert_no_c = (int)$cert_no_current -1 ;
							
							$certificates=Certmast::whereRaw("trim(cert_type)='".$class[0]->cert_type."' AND (cert_status <> 99 and cert_status <> 1 ) AND cert_no > '".$cert_no_c."'")->take(count($reg_no))->get();               
							
							//dd($certificates, $cert_status,$cert_no_current, isset($certificates), count($certificates),"B");
						
						}
						// END OF CERTIFICATES
			//dd($certificates, $cert_status,$cert_no_current, isset($certificates), count($certificates), "C");
	
						$count_cert_alloc=Certalloc::where('cert_no',$certificates[$i]->cert_no)
								->where('cert_type',$cert_type)
								->count();
			
						$cert_alloc_res=Certalloc::where('cert_no',$certificates[$i]->cert_no)
								->where('cert_type',$cert_type)
								->get();
			
						$pipcnam = Pipcnam::all()[0];
			
						if($count_cert_alloc > 0 && $pipcnam->digital_cert != 'Y'){
	
							$pipcnam = Pipcnam::all()[0];
							
	
							// if($pipcnam->digital_cert == 'Y'){
							// 	// dd($cert_alloc_res[$i]);
							// 	$message='Digital Certificate No '.$certificates[$i]->aki_cert_no.' has already been issued for vehicle '.$cert_alloc_res[$i]->reg_no;
							// }else{
							// 	$message='Certificate No '.$certificates[$i]->cert_no.' has already been issued for vehicle '.$cert_alloc_res[$i]->reg_no;
							// }
	
							if($pipcnam->digital_cert != 'Y'){
								$message='Certificate No '.$certificates[$i]->cert_no.' has already been issued for vehicle '.$cert_alloc_res[$i]->reg_no;
							}
	
	
							//$result=array('status' =>$message);
	
							array_push($error,$message);
							// break;
	
	
						}else{
							
	
							//access dcontrol rec
							$dcontrol=Dcontrol::where('endt_renewal_no',$endt_renewal_no)->get();
							$dcontrol=$dcontrol[0];
	
							//access modtl record
							$modtl=Modtlmast::where('policy_no',$dcontrol->policy_no)
											->where('reg_no',$reg_no[$i])
											->first();
	
							//get acdet record
							$most_update_acdet_record=Acdet::where('doc_type','DRN')
										->where('endt_renewal_no',$endt_renewal_no)
										->orderBy('dola','DESC')
										->orderBy('time','DESC')
										->take(1)
										->get();
							$most_update_acdet_record=$most_update_acdet_record[0];
	
							//agent
							// $agmnf = Agmnf::where('branch',$dcontrol->branch)
							// 				->where('agent',$dcontrol->agent)
							// 				->get();
	
							// $agmnf=$agmnf[0];
							$intermediaryParams = new IntermediaryQueryParams([
								'agentNo' => $dcontrol->agent,
							]);
							$agmnf  =IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();

							//covertype
							$cover_type = Covertype::where('cover',$modtl->covertype)
													->first();
										
							/* if($class[0]->cert_type=='A' || $class[0]->cert_type=='D') {
								$pipcnam = Pipcnam::all()[0];
	
								if($pipcnam->digital_cert == 'Y'){
									$cert_sub_type=ClassModel::where('class',$dcontrol->class)->first()->cert_sub_type;
															
									$certtype=Certtypedtl::whereRaw("trim(type) = '".trim( $class[0]->cert_type) ."' and trim(cert_sub_type) = ". trim((int) $cert_sub_type))
															->get()[0];
															//dd($certtype);
	
								}else{
									$certtype=Certtypedtl::whereRaw("trim(type) = '".trim( $certificates[$i]->cert_type) ."' and trim(cert_sub_type) = ". trim($certificates[$i]->cert_sub_type))
											->get()[0];
								}
									// dd($certtype);
							}else{ */
								$certtype=Certtype::whereRaw("trim(type) = '".trim( $certificates[$i]->cert_type) ."'")->get()[0];
							//}
	
							$certificate_type=Certtype::whereRaw("trim(type) = '".trim( $certificates[$i]->cert_type) ."'")->get()[0];
							// if no data in certmast
							if ($certificate_type == null) {
								$certificate_type=Certtype::whereRaw("trim(type) = '".trim($cert_type) ."'")->get()[0];
							}
	
							//	DB::beginTransaction();
							$BusType = 1;
							($modtl->trailer=='Y') ? $BusType=2 : $BusType =1;
							// return $BusType;
	
							$IntermediaryIRANumber= trim($agmnf->LICENCE_NUMBER);
	
							$Typeofcover= (int)$cover_type->cover_code;
							// $Typeofcover= (int)$cover_type->cover;
		
	
							$Policyholder= trim($client->name);
	
							$policynumber= trim($dcontrol->policy_no);
	
							$Commencingdatex = trim($request->input('date_from'));
							$Commencingdate = date("d/m/Y",strtotime($Commencingdatex));
	
	
							$Expiringdatex= trim($request->input('cert_expiry'));
							$Expiringdate = date("d/m/Y",strtotime($Expiringdatex));
	
							$Registrationnumber= trim($request->reg_no[$i]);
	
							$Chassisnumber= trim($modtl->chassis_no);
	
							$Phonenumber= trim(substr($client->telephone, 1));
	
							$Bodytype= trim($modtl->bodytype);
	
							$Vehiclemake= trim($modtl->make);
	
							$Vehiclemodel= trim($modtl->model);
	
							$Enginenumber= trim($modtl->engine_no);
	
							$Email= trim($client->e_mail);
	
							$SumInsured= trim($modtl->sum_insured);
	
							$InsuredPIN= trim($client->pin_number);
	
							$Yearofmanufacture= trim($modtl->manufacture_year);
	
							$HudumaNumber= "0";
							// $BusType = $BusType;
	
	
							$TypeOfCertificate = $certtype->cert_code;
							$Licensedtocarry = $modtl->seat_cap;
							$TonnageCarryingCapacity = $modtl->tonnage;
							$Tonnage = $modtl->tonnage;
							$aki_data = $certtype->aki_data;
							$aki_url = $certificate_type->aki_url;
	
							$array = array(
								'IntermediaryIRANumber'=>$IntermediaryIRANumber,
								'Licensedtocarry'=>$Licensedtocarry,
								'TonnageCarryingCapacity'=> (int) $TonnageCarryingCapacity,
								'TypeOfCertificate' => (int) $TypeOfCertificate,
								'Typeofcover' => $Typeofcover,
								'Policyholder'=>$Policyholder,
								'policynumber'=>$policynumber,
								'Commencingdate'=> $Commencingdate,
								'Expiringdate'=>$Expiringdate,
								'Registrationnumber'=>$Registrationnumber,
								'Chassisnumber'=>$Chassisnumber,
								'Phonenumber'=>$Phonenumber,
								'Bodytype'=>$Bodytype,
								'Tonnage'=>$Tonnage,
								'Vehiclemake'=>$Vehiclemake,
								'Vehiclemodel'=>$Vehiclemodel,
								'Enginenumber'=>$Enginenumber,
								'Email'=>$Email,
								'SumInsured'=>$SumInsured,
								'InsuredPIN'=>$InsuredPIN,
								'Yearofmanufacture'=>$Yearofmanufacture,
								'HudumaNumber'=>$HudumaNumber,
								'VehicleType' => $BusType
							);
								//dd($array);
	
							$client = new \GuzzleHttp\Client();
							
							$tokenpipcnam = Pipcnam::where('record_type',0)->get()[0];
							$url = $tokenpipcnam->aki_base_url. $aki_url;
							$token = $tokenpipcnam->aki_token;
							$aki_user = $tokenpipcnam->aki_user;
							$aki_passwd = $tokenpipcnam->aki_passwd;
						
							$pipcnam = Pipcnam::all()[0];
							$response = '';
							$status="";
							// $url = "http://***********:9005/SBProject/Services";
							//$url = "http://**********:9005/SBProject/Services/".$aki_url;

							if($pipcnam->digital_cert == 'Y'){
								$response = $client->post($url, [
									'auth' => [
										$aki_user, 
										$aki_passwd
									],
									'headers' => [
											'Content-Type'  => 'application/json',
											'clientID' =>'D9138895-95E8-4492-B41F-7B86D9AA892F'
										],
									'json'=>$array
		
								]);
								
								// response from aki
								$contents = json_decode($response->getBody()->getContents());
	
								$status=$contents->success;
	
	
								if($status == false){
									foreach ($contents->Error as $errors) {
										array_push($error, $errors->errorText.' for vehicle '.$Registrationnumber);
									}
								}
	
								if($status == true){
									foreach ($contents->Error as $errors) {
										array_push($error, $errors->errorText.' for vehicle '.$Registrationnumber);
									}
								}
								
							}
							// dd ($contents);
	
	
	
							if($status || $pipcnam->digital_cert == 'N'){
								$aki_cert="";
								$aki_tranx_no="";
								$AKI_APIRequestNumber="";
								$cert_number = $certificates[$i]->cert_no;
								if($pipcnam->digital_cert == 'Y'){
									$aki_cert = $contents->callbackObj->issueCertificate->actualCNo;
									$aki_tranx_no = $contents->callbackObj->issueCertificate->TransactionNo;
									$AKI_APIRequestNumber = $contents->APIRequestNumber;
								}
	
	
								//	DB::beginTransaction();

								//	try {
								
							    
								//add record to certalloc
								$certalloc=new Certalloc;
								$certalloc->cert_type=$cert_type;
								$certalloc->cert_no= $certificates[$i]->cert_no;
								$certalloc->branch=$dcontrol->branch;
								$certalloc->agent=$dcontrol->agent;

								$certalloc->aki_cert_no = $aki_cert;
								$certalloc->aki_tranx_no = $aki_tranx_no;
								$certalloc->AKI_APIRequestNumber = $AKI_APIRequestNumber;

								$certalloc->date_issued=Carbon::today();
								//$certalloc->insured=$dcontrol->insured;
								$certalloc->insured=$Policyholder;
								$certalloc->reg_no=$modtl->reg_no;
								$certalloc->cover_type=$modtl->covertype;
								$certalloc->eng_no=$modtl->engine_no;
								$certalloc->chassis_no=$modtl->chassis_no;
								$certalloc->period_from=$request->date_from;
								$certalloc->period_to=$request->cert_expiry;
								$certalloc->policy_no=$dcontrol->policy_no;
								$certalloc->cert_status= 1;
								$certalloc->branch_btch=$certificates[$i]->branch_btch;
								$certalloc->agent_btch=$certificates[$i]->agent_btch;
								$certalloc->dola=Carbon::today();
								//$certalloc->force_change=
								$certalloc->trans_type=$dcontrol->trans_type;
								$certalloc->endt_renewal_no=$dcontrol->endt_renewal_no;
								$certalloc->class=$modtl->class;
								$certalloc->add_premium=($most_update_acdet_record->nett * -1);
								$certalloc->sum_insured=$modtlsumm->sum_insured;
								$certalloc->total_premium=$modtlsumm->endorse_amount;
								//$certalloc->time_from=
								//$certalloc->time_to=
								$certalloc->user_name=$dcontrol->user_1;
								$certalloc->issue_time=Carbon::now();
								$certalloc->geo_scope=$geo_limit[$i];
								$certalloc->make=$modtl->make;
								$certalloc->capacity=$modtl->tare_weight;
								$certalloc->carry_cap=$modtl->tonnage;
								//$certalloc->space1=
								$certalloc->colour=$modtl->color;
								$certalloc->man_date=$modtl->manufacture_date;
								$certalloc->itp_injury_person=0;
								$certalloc->itp_injury_event=0;
								$certalloc->itp_property=0;
								//$certalloc->vehicle_use
								$certalloc->body_type=$modtl->bodytype;
								$certalloc->model=$modtl->model;
								//$certalloc->amount_in_words
								$certalloc->receipt_ref=$most_update_acdet_record->reference; //to be confirmed
								$certalloc->user_name = Auth::user()->user_name;
								$certalloc->location=$modtl->item_no;
								//$certalloc->cover_months
								//$certalloc->auth_ref
								//$certalloc->reason
								//$certalloc->authority
								//$certalloc->reference=
								//$certalloc->reference_description
								$certalloc->receipt_no=$most_update_acdet_record->reference;
								//$certalloc->cert_sub_type=
								//$certalloc->authority_date=
								//$certalloc->authority_time=
								//$certalloc->authorised_yn
								$certalloc->cert_print_yn='N';
								//$certalloc->type_of_auth
								//$certalloc->auth_no=
								//$certalloc->auth_due_date
								$certalloc->yellow_card_premium=0;
								$certalloc->ln_no=$modtl->item_no;
								//$certalloc->manager=
								$certalloc->credit_amount=0;
								$certalloc->rec_amount=$most_update_acdet_record->allocated;
								$certalloc->crm_flag='N';


								//UPDATE CERT MAST
								//$certificates[$i]->cert_status=1;
								$cert_type=trim($certificates[$i]->cert_type);
								$cert_type=trim($certificates[$i]->cert_type);

								$updatecertmast=Certmast::where('cert_no',$certificates[$i]->cert_no)
									->whereRaw("trim(cert_type)='".$cert_type."'")
									->update([
										'cert_status'=>1,
										'reg_no'=>$modtl->reg_no,
										'endt_renewal_no'=>$dcontrol->endt_renewal_no,
										'insured'=>$Policyholder,
										'cover_type'=>$modtl->covertype,
										'eng_no'=>$modtl->engine_no,
										'period_from'=>$request->date_from,
										'period_to'=>$request->cert_expiry,
										'policy_no'=>$dcontrol->policy_no,
										'class'=>$dcontrol->class,  
										'carry_cap'=>$modtl->tonnage,
										'capacity'=>$modtl->tonnage,
										'aki_cert_no' => $aki_cert,
										'aki_tranx_no' => $aki_tranx_no,
										'AKI_APIRequestNumber' => $AKI_APIRequestNumber,
										'add_premium'=>($most_update_acdet_record->nett * -1)
										//,
									]);
							
							
								$certalloc->save();
	
								//search for added certno in cert alloc
								$cert_alloc_rec=Certalloc::where('cert_no',$certificates[$i]->cert_no)->count();

								if($cert_alloc_rec>0){

									$m1=Modtlmast::where('policy_no',$modtl->policy_no)
													->where('endt_renewal_no',$modtl->endt_renewal_no)
													->where('reg_no',$modtl->reg_no)
													->update([
														'stickernumber'=>$certificates[$i]->cert_no
													]);

									$m2=Modtlhist::where('policy_no',$modtl->policy_no)
													->where('endt_renewal_no',$modtl->endt_renewal_no)
													->where('reg_no',$modtl->reg_no)
													->update([
														'stickernumber'=>$certificates[$i]->cert_no
													]);

								}

								$message='Certificate No '.$certificates[$i]->cert_no.' issued successfully for vehicle '.$modtl->reg_no;

								//$result=array('status' =>$message);

								array_push($error, $message);

							}
						}	
					}
		
				}
				//end of loop for each vehicle
			}else{
				$error = [
					"code" => -1,
					"msg" => "Certificate Type not set for class ".$class_checkType->class
				];
			}
		}else{
			$error = [
				"code" => -1,
				"msg" => "You seem to be offline. Please check your internet connection."
			];
		}

	       
		return $error;

    }//end of function

	/**********************/
	public function issue(Request $request){
	
		$endt_renewal_no=$request->input('cert_endt');
		$cert_no_from=$request->input('serial_from');
		$issue_cert_type = $request->input('issue_cert_type');
		$cert_no_to=$request->input('serial_to');
		//$cert_type=$request->input('cert_type');
		$reg_no=$request->input('reg_no');
		$geo_limit=$request->input('geo_limit');
		$receipt_amt=$request->input('cert_receipt');
		$credit_amt=$request->input('credit_amt');
		$no_of_veh=$request->input('no_of_veh');
		$no_of_certs=$request->input('no_of_certs');
		//$prof_flag = $request->input('prof_flag');
		$uia_reference = $request->input('uia_reference');
		
		$apply_certs=$request->apply;

		//dd($issue_cert_type);

		$certificates=collect();
				
		$username=trim(Auth::user()->user_name);  

		$dcontrol = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();

		$pipcnam = Pipcnam::where('record_type', 0)->first();
		$uw_parameters = UwParameter::first();

		$cert_no_current=$cert_no_from;

		if (count($apply_certs) < 1) {

			return [
				"code" => -1,
				"msg" => 'Please check atleast one vehicle to issue certificate' 
			];

		}

        //error/success message array
		$error=array();

		$certs_assigned = array();
		
		//$cert_status=Auth::user()->user_certificate;
		

		 //$use= User::pluck('user_name');
		$no_vehicle = count($reg_no) ;

		$cert_no_c = (int)$cert_no_current ; //- 1 ;
	
		$cert_count = 0;
				
		
		 
		
		//get acdet record
		$most_update_acdet_record = Acdet::where('doc_type','DRN')
									     ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
									     ->orderBy('dola','DESC')
									     ->orderBy('time','DESC')
									     ->take(1)
									     ->first();
		
		//agent
		// $agmnf = Agmnf::where('branch', $dcontrol->branch)->where('agent', $dcontrol->agent)->first();
		$intermediaryParams = new IntermediaryQueryParams([
			'agentNo' => $dcontrol->agent,
		]);
		$agmnf  =IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
		
		//client
		$client = Client::where('client_number', $dcontrol->client_number)->first();

		$count_applied = collect($apply_certs)->filter(function ($item) {
							// Condition to filter the elements
							return $item == 1;
						})->count();
		
		//CHECK FOR NO VEHICLES CHECKED
		if($count_applied < 1){

			$message='Cannot proceed. No vehicles selected ';
			
			array_push($error,$message);

			return $error;

		}
        
		for($i=0; $i < count($reg_no) ; $i++){//fleet

			if ((int)$apply_certs[$i] != 1) {
				continue;
			}

			$modtl = Modtlpivot::where('policy_no',$dcontrol->policy_no)
							   ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
							   ->where('reg_no',$reg_no[$i])
							   ->first();

			$modtlsumm = Modtlsumm::where('policy_no',$dcontrol->policy_no)
								  ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
								  ->where('reg_no',$reg_no[$i])
								  ->first();

			if($issue_cert_type == '' || $issue_cert_type == null){

				$cert_type = ClassModel::where('class', $modtl->class)->first()->cert_type;

			}else{

				$cert_type = $issue_cert_type;
				
			}
			

			//covertype
			$cover_type = Covertype::where('cover', $modtl->covertype)->first();
			
			// if ($no_vehicle > 1) {
			// 	$cert_no_c=(int)$request->sticker_n[$i];
				
			// }
			
			//dd($cert_no_c);

		   // GET CERTIFICATES

		   // check if the a cert exists for the same period
		   $check_cert = Certalloc::where(DB::raw('TRIM(reg_no)'),$reg_no[$i])
									->where('cert_status', 1)
									//->whereBetween('period_from', [$request->date_from, $request->cert_expiry])
									//->orWhereBetween('period_to', [$request->date_from, $request->cert_expiry])
									->where(function($query){
										$query->whereBetween('period_from', [$request->date_from, $request->cert_expiry])
											  ->orWhereBetween('period_to', [$request->date_from, $request->cert_expiry]);
									})
									->count();

			$check_cert_c = Certalloc::where(DB::raw('TRIM(reg_no)'),$reg_no[$i])
									->where('cert_status', 1)
									->where(function($query){
										$query->whereBetween('period_from', [$request->date_from, $request->cert_expiry])
											  ->orWhereBetween('period_to', [$request->date_from, $request->cert_expiry]);
									})
									->get();
			

			//if (count($check_cert) > 0) {
			if ($check_cert > 0) {
				array_push($error,'Certificate for vehicle '.$reg_no[$i].' has already been issued for this period');
			} else {
	
				//CHECK ALLOCATION LEVELS
				if($pipcnam->cert_level_allocate == 'U'){ //Allocation to user level

					if($i == 0){

						$certificates = Certmast::whereRaw("trim(cert_user)='".$username."' AND cert_status = 0")
										  		->where('cert_no','=',$cert_no_c)
										  		->whereRaw("trim(cert_type)='".$cert_type."'")
												->orderBy('cert_no','asc')
												->get();

					}else{

						$certificates = Certmast::whereRaw("trim(cert_user)='".$username."' AND cert_status = 0")
										  		->where('cert_no','>',$cert_no_c)
												->whereNotIn('cert_no',$certs_assigned)
										  		->whereRaw("trim(cert_type)='".$cert_type."'")
												->orderBy('cert_no','asc')
												->take(1)
												->get();

					}

											 
	
				}
				else if($pipcnam->cert_level_allocate == 'A'){ // Allocation to Agent Level

					if($i == 0){

						$certificates = Certmast::where('agent',$dcontrol->agent)
												->whereRaw("trim(cert_type)='".trim($cert_type)."' AND (cert_status = 0)")
												->where('cert_no','=', $cert_no_c)
												->where('branch', $dcontrol->branch)
												->orderBy('cert_no', 'asc')
												->get();

					}else{

						$certificates = Certmast::where('agent',$dcontrol->agent)
													->whereRaw("trim(cert_type)='".trim($cert_type)."' AND (cert_status = 0)")
													->where('cert_no','>', ($cert_no_c+$i))
													->where('branch', $dcontrol->branch)
													->orderBy('cert_no', 'asc')
													->take(1)
													->get();

					}

				}
				else if($pipcnam->cert_level_allocate == 'B'){ // Allocation to Branch Level

					if($i == 0){

						$certificates = Certmast::where('branch',$dcontrol->branch)
												->whereRaw("trim(cert_type)='".trim($cert_type)."' AND (cert_status = 0)")
												->where('cert_no','=',$cert_no_c)
												->orderBy('cert_no', 'asc')
												->get();

					}else{

						$certificates = Certmast::where('branch',$dcontrol->branch)
												->whereRaw("trim(cert_type)='".trim($cert_type)."' AND (cert_status = 0)")
												->where('cert_no','>',$cert_no_c)
												->orderBy('cert_no', 'asc')
												->take(1)
												->get();

					}

				}

			   // END OF CERTIFICATES
				if (count($certificates) < 1) {
					$message='Certificate No '.$cert_no_c.' of Type '.trim($cert_type).' has not been allocated';
					array_push($error,$message);
				}else{
					
					//dd($certificates[0]->cert_no,$cert_no_c);
					//access certmast
					// $certmast=Certmast::where('cert_type',$cert_type)
					// ->where('cert_no',$cert_no_current)
					// ->get();
		
		
					// $certmast=$certmast[0];$cert_type
		
					$count_cert_alloc = Certalloc::where('cert_no',$certificates[0]->cert_no)
											     ->whereNotNull('cert_no')
												 ->where('cert_type',$cert_type)
												 ->count();
				
					if($count_cert_alloc>0){
		
						$message = 'Certificate No '.$certificates[0]->cert_no.' has already been issued';
						
						array_push($certs_assigned, $certificates[0]->cert_no);
		
						array_push($error,$message);
		
					}else{
						
						$certtype = Certtype::whereRaw("trim(type) = '".trim( $certificates[0]->cert_type) ."'")->first();
		
						//	DB::beginTransaction();
						
						($modtl->trailer=='Y') ? $BusType=2 : $BusType =1;
						
		
						$IntermediaryIRANumber= trim($agmnf->LICENCE_NUMBER);
	
						$Typeofcover= (int)$cover_type->cover_code;
							// $Typeofcover= (int)$cover_type->cover;
						
						//check if covertype is comprehensive with logbook transferred and showroom status
						$dateFrom = Carbon::parse($request->input('date_from'));
						$certExpiry = Carbon::parse($request->input('cert_expiry'));

						$daysDifference = $dateFrom->diffInDays($certExpiry);

						if($uw_parameters->logbook_transferred_showroom =='Y'){
							if ($modtl->covertype == '1'){

								if($uw_parameters->comprehensive_valuation_mandatory =='Y'){
									if($modtl->valuation == 'N' && $modtl->vehicle_from_showroom == 'N'){

										if ($daysDifference > $uw_parameters->certificate_limit_days) {
											
											$message='Certificate No for vehicle '.$reg_no[$i].' cannot be issued beyond the '.$uw_parameters->certificate_limit_days.' day limit as it is neither valued nor from the showroom.<br>';
			
											array_push($error, $message);
											return $error;
										}
	
									}
								}
	
								if($modtl->logbook_transferred == 'N' && $modtl->vehicle_from_showroom == 'N'){
									if ($daysDifference > $uw_parameters->certificate_limit_days) {
										$message='Certificate cannot be issued for vehicle ' . $reg_no[$i] . 'cannot be issued beyond the ' . $uw_parameters->certificate_limit_days . ' day limit as the logbook is not transferred and the vehicle is not from the showroom.<br>';
			
										array_push($error, $message);
										return $error;
									}
								}
							}
						}
	
						$Policyholder= trim($client->name);
	
						$policynumber= trim($dcontrol->policy_no);
	
						$Commencingdatex = trim($request->input('date_from'));
						$Commencingdate = date("d/m/Y",strtotime($Commencingdatex));
	
	
						$Expiringdatex= trim($request->input('cert_expiry'));
						$Expiringdate = date("d/m/Y",strtotime($Expiringdatex));
	
						$Registrationnumber= trim($request->reg_no[$i]);
	
						$Chassisnumber= trim($modtl->chassis_no);
	
						$Phonenumber= trim(substr($client->telephone, 1));
	
						$Bodytype= trim($modtl->bodytype);
	
						$Vehiclemake= trim($modtl->make);
	
						$Vehiclemodel= trim($modtl->model);
	
						$Enginenumber= trim($modtl->engine_no);
	
						$Email= trim($client->e_mail);
	
						$SumInsured= trim($modtl->sum_insured);
	
						$InsuredPIN= trim($client->pin_number);
	
						$Yearofmanufacture= trim($modtl->manufacture_year);
	
						$HudumaNumber= "0";
							// $BusType = $BusType;
	
	
						$TypeOfCertificate = $certtype->cert_code;
						$Licensedtocarry = $modtl->seat_cap;
						$TonnageCarryingCapacity = $modtl->tonnage;
						$Tonnage = $modtl->tonnage;

						$array = array(
							'IntermediaryIRANumber'=>$IntermediaryIRANumber,
							'Licensedtocarry'=>$Licensedtocarry,
							'TonnageCarryingCapacity'=> (int) $TonnageCarryingCapacity,
							'TypeOfCertificate' => (int) $TypeOfCertificate,
							'Typeofcover' => $Typeofcover,
							'Policyholder'=>$Policyholder,
							'policynumber'=>$policynumber,
							'Commencingdate'=> $Commencingdate,
							'Expiringdate'=>$Expiringdate,
							'Registrationnumber'=>$Registrationnumber,
							'Chassisnumber'=>$Chassisnumber,
							'Phonenumber'=>$Phonenumber,
							'Bodytype'=>$Bodytype,
							'Licensedtocarry'=>$Licensedtocarry,
							'Tonnage'=>$Tonnage,
							'Vehiclemake'=>$Vehiclemake,
							'Vehiclemodel'=>$Vehiclemodel,
							'Enginenumber'=>$Enginenumber,
							'Email'=>$Email,
							'SumInsured'=>$SumInsured,
							'InsuredPIN'=>$InsuredPIN,
							'Yearofmanufacture'=>$Yearofmanufacture,
							'HudumaNumber'=>$HudumaNumber,
							'VehicleType' => $BusType
						);
		
						//if($status || $pipcnam->digital_cert == 'N') {

						$status = $this->getCertStatus($endt_renewal_no);
							
							//add record to certalloc
							//add record to certalloc
						$certalloc=new Certalloc;
						$certalloc->cert_type=$cert_type;
						$certalloc->cert_no= $certificates[0]->cert_no;
						$certalloc->branch=$dcontrol->branch;
						$certalloc->agent=$dcontrol->agent;

						$certalloc->date_issued=Carbon::today();
							//$certalloc->insured=$dcontrol->insured;
						$certalloc->insured=$Policyholder;
						$certalloc->reg_no=$modtl->reg_no;
						$certalloc->cover_type=$modtl->covertype;
						$certalloc->eng_no=$modtl->engine_no;
						$certalloc->chassis_no=$modtl->chassis_no;
						$certalloc->period_from=$request->date_from;
						$certalloc->period_to=$request->cert_expiry;
						$certalloc->policy_no=$dcontrol->policy_no;
						$certalloc->cert_status= 1;
						$certalloc->branch_btch=$certificates[$i]->branch_btch;
						$certalloc->agent_btch=$certificates[$i]->agent_btch;
						$certalloc->dola=Carbon::today();
							//$certalloc->force_change=
						$certalloc->trans_type=$dcontrol->trans_type;
						$certalloc->endt_renewal_no=$dcontrol->endt_renewal_no;
						$certalloc->class=$modtl->class;
						$certalloc->add_premium=($most_update_acdet_record->nett * -1);
						$certalloc->sum_insured=$modtlsumm->sum_insured;
						$certalloc->total_premium=$modtlsumm->endorse_amount;
							//$certalloc->time_from=
							//$certalloc->time_to=
						$certalloc->user_name=$dcontrol->user_1;
						$certalloc->issue_time=Carbon::now();
						$certalloc->geo_scope=$geo_limit[$i];
						$certalloc->make=$modtl->make;
						$certalloc->capacity=$modtl->tare_weight;
						$certalloc->carry_cap=$modtl->tonnage;
							//$certalloc->space1=
						$certalloc->colour=$modtl->color;
						$certalloc->man_date=$modtl->manufacture_date;
						$certalloc->itp_injury_person=0;
						$certalloc->itp_injury_event=0;
						$certalloc->itp_property=0;
							//$certalloc->vehicle_use
						$certalloc->body_type=$modtl->bodytype;
						$certalloc->model=$modtl->model;
							//$certalloc->amount_in_words
						$certalloc->receipt_ref=$most_update_acdet_record->reference; //to be confirmed
						$certalloc->user_name = Auth::user()->user_name;
						$certalloc->location=$modtl->item_no;
							//$certalloc->cover_months
							//$certalloc->auth_ref
							//$certalloc->reason
							//$certalloc->authority
							//$certalloc->reference=
							//$certalloc->reference_description
						$certalloc->receipt_no=$most_update_acdet_record->reference;
							//$certalloc->cert_sub_type=
							//$certalloc->authority_date=
							//$certalloc->authority_time=
							//$certalloc->authorised_yn
						$certalloc->cert_print_yn='N';
							//$certalloc->type_of_auth
							//$certalloc->auth_no=
							//$certalloc->auth_due_date
						$certalloc->yellow_card_premium=0;
						$certalloc->ln_no=$modtl->item_no;
							//$certalloc->manager=
						$certalloc->credit_amount=0;
						$certalloc->rec_amount=$most_update_acdet_record->allocated;
						$certalloc->crm_flag='N';


							//UPDATE CERT MAST
							//$certificates[$i]->cert_status=1;
						$cert_type=trim($certificates[$i]->cert_type);

						$certalloc->save();

						if($certalloc){

								// insert data to certificate_log

									$certificateData = [
										'endt_renewal_no' => $endt_renewal_no,
										'issue_date' => Carbon::now(),
										'uia_reference' => $uia_reference,
										'reg_no' => $modtl->reg_no,
										'cert_no' => $certificates[0]->cert_no,
										'cert_status' => $status,
										'processed_by' => Auth::user()->user_name
									];
									 //add record to certificate_log
									$this->insertCertificateLog($certificateData);
		
											//UPDATE CERT MAST
									//$certificates[0]->cert_status=1;
									$cert_type=trim($certificates[0]->cert_type);

									$updatecertmast=Certmast::where('cert_no',$certificates[0]->cert_no)
															->whereRaw("trim(cert_type)='".$cert_type."'")
															->update([
																'cert_status'=>1,
																'reg_no'=>$modtl->reg_no,
																'endt_renewal_no'=>$dcontrol->endt_renewal_no,
																'insured'=>$Policyholder,
																'cover_type'=>$modtl->covertype,
																'eng_no'=>$modtl->engine_no,
																'period_from'=>$request->date_from,
																'period_to'=>$request->cert_expiry,
																'policy_no'=>$dcontrol->policy_no,
																'class'=>$dcontrol->class,  
																'carry_cap'=>$modtl->tonnage,
																'capacity'=>$modtl->tonnage,
																'passengers_no'=>$modtl->seat_cap,
																'max_passengers'=>$modtl->seat_cap,
																'add_premium'=>($most_update_acdet_record->nett * -1)
																//,
															]);

							}
		
		
							//search for added certno in cert alloc
							$cert_alloc_rec=Certalloc::where('cert_no',$certificates[$i]->cert_no)->count();
		
							if($cert_alloc_rec>0){
		
								$modtl=Modtlmast::where('policy_no',$modtl->policy_no)
									->where('reg_no',$modtl->reg_no)
									->where('endt_renewal_no',$modtl->endt_renewal_no)
									->update([
										'stickernumber'=>$certificates[$i]->cert_no
									]);
												
								$modtl=Modtlhist::where('policy_no',$modtl->policy_no)
									->where('reg_no',$modtl->reg_no)
									->where('endt_renewal_no',$modtl->endt_renewal_no)
									->update([
										'stickernumber'=>$certificates[$i]->cert_no
									]);
		
							}
		
							$message='Certificate No '.$certificates[$i]->cert_no.' issued successfully <br>';
		
							array_push($error, $message);

							array_push($certs_assigned, $certificates[$i]->cert_no);
	
					}	
				}     
				
			}


        }//end of loop for each vehicle
	       
		return $error;

    }//end of function
	/********************/



	// /   digitalAuth
	public function digitalAuth(Request $request)
	{
		$pipcnam = Pipcnam::where('record_type', 0)->first();

		// update
		$pipcnam->aki_user = $request->aki_set_user;
		$pipcnam->aki_passwd = $request->aki_set_passwd;

		$pipcnam->save();
  

		return [
			"code"=> 1,
			"msg"=> "Data saved successfully"
		];
	}


    public function check_issued_cert(Request $request){
		$certalloc = Certalloc::where('endt_renewal_no',$request->endt_renewal_no)
				->where('cert_status','<>',99)
				->get();
		$status = 'N';
	
		if(count($certalloc) > 0){
			$status = 'Y';
		}

		return [ 'issued' => $status];

	}


    public function increment_cert_no($cert_no, $reg_no,$endt_renewal_no){

			$dcontrol=Dcontrol::where('endt_renewal_no',$endt_renewal_no)->get();
			$dcontrol=$dcontrol[0];

			$cert_no=$cert_no+1;
            $reg_no=$reg_no;
			//get certificate type from class

			

			$modtl=Modtl::where('policy_no',$dcontrol->policy_no)
			              ->where('reg_no',$reg_no)
			              ->get();

	        $class=ClassModel::where('class',$modtl[0]->class)
	                          ->get(['cert_type']);

		    $cert_type=$class[0]->cert_type;


		    //access certmast
			$certmast_count=Certmast::where('cert_type',$cert_type)
			->where('cert_no',$cert_no)
			->count();

			if($certmast_count>0){

				$certmast=Certmast::where('cert_type',$cert_type)
										->where('cert_no',$cert_no)
										->get();

				$certmast=$certmast[0];

			    $count_cert_alloc=Certalloc::where('cert_no',$cert_no)
					->where('cert_type',$cert_type)
					->count();


				if($count_cert_alloc>0){
	        
					
				 $new_cert_no=$this->increment_cert_no($cert_no, $reg_no,$endt_renewal_no);


				}else{

				 $new_cert_no=$cert_no;


				}


				return $new_cert_no;


			}else{

				return 0;
			}
	}
		

	// Function to check if a user is connected to the internet
	function check_internet($domain)
	{
		$connected = @fsockopen($domain, 80);//@fsockopen is used to connect to a socket
		
		if ($connected){
			$is_conn = true; //action when connected
			fclose($connected);
		}else{
			$is_conn = false; //action in connection failure
		}
		return $is_conn;
	}


	/********** CERTIFICATE REQUEST **********/

	public function get_cert_requests(Request $request){

	}

	public function get_cert_datatable(Request $request)
    {
		$user_name = Auth::user()->user_name;

		$request_type = $request->get('request_type');

		if($request_type == 'MINE'){

			$certreq = Certreq::where('raised_by', $user_name);

		}
		else if($request_type == 'APPROVER'){

			$certreq = Certreq::where('escalate_to', $user_name)
							   ->where(function($query){
									$query->whereNotIn('approval_flag',['Y','y'])
										  ->orWhereNull('approval_flag');
							   });

		}
		else if($request_type == 'ALL'){

			$certreq = Certreq::where('raised_by', $user_name)->orWhere('raised_by','<>',$user_name);

		}
		
        
        return datatables::of($certreq)

			->addColumn('cert_description', function($row){
				$c = Certtype::whereRaw("trim(type) = '".trim($row->cert_type)."'")->first();

				return $c->description;
				
			})
            ->addColumn('view', function ($row) {

                $links = '';

				if ($row->cancel_flag == 'Y' || $row->approval_flag == 'Y' || $row->declined_flag == 'Y') {
                    $links = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-';
                }
				else{

					if($row->raised_by == Auth::user()->user_name){
						$links = '<a class="btn btn-sm-default editrequest" title="Edit" style="padding: 0; margin-right: 10px;"><i title="Edit" class="glyphicon glyphicon-edit"> </i></a>
						<a class="btn btn-sm-default re-escalaterequest " title="Re-escalate" style="padding: 0; margin-right: 10px;""> <i title="Re-Escalate" class="fa fa-share"></i></a>
						<a class="btn btn-sm-default cancelrequest " title="Cancel" style="padding: 0; margin-right: 10px;""> <i title="Cancel" class="fa fa-trash"></i></a>';
					}
	
					if(Gate::check('approve-certificate-requisition') == true && $row->raised_by != Auth::user()->user_name){
						$links = $links.'<a class="btn btn-sm-default approverequest " title="Approve/Decline" style="padding: 0; margin-right: 10px;""> <i title="Approve/Decline" class="fa fa-check-square"></i></a>';
					}

				}

				return $links;

            })
            ->addColumn('status', function($row){
                if ($row->cancel_flag == 'Y') {
                    return 'Cancelled';
                }
				else if($row->approval_flag == 'Y'){
					return 'Approved';
				}
				else if($row->declined_flag == 'Y'){
					return 'Declined';
				}
				else{
					return 'Awaiting Approval';
				}
                
            })
			->addColumn('raised_by_name', function($row){
                $usr = User::where('user_name',$row->raised_by)->first();
				//return Auth::user()->name;
				return $usr->name;
                
            })
            ->escapeColumns([])

            ->make(true);

    }

	public function cert_request_view(){

		Gate::authorize('access-certificate-requisition');

		//return view('gb.underwriting.cert_request',compact('staff', 'endt_renewal_no'));
		$certtypes = Certtype::all();

		if(Gate::allows('raise-certificate-requisition')){
			$certreq = Certreq::where('raised_by',Auth::user()->user_name)
							   ->where(function($query){
									$query->whereNotIn('approval_flag',['Y','y'])
										->orWhereNull('approval_flag');
							   })
							   ->where(function($query){
									$query->whereNotIn('declined_flag',['Y','y'])
										->orWhereNull('declined_flag');
							   })
							   ->where(function($query){
									$query->whereNotIn('cancel_flag',['Y','y'])
										->orWhereNull('cancel_flag');
							   })->get(['cert_type'])->toArray();


			$certtypes = Certtype::whereNotIn(DB::raw('trim(type)'), $certreq)->get();

		}

		$permission = Permission::where('slug','approve-certificate-requisition')->first();
		$name_of_user = Auth::user()->name;
		//$username = Auth::user()->user_name;
        $users = $permission->users;
		$date_raised = Carbon::now();

		return view('gb.underwriting.cert_request', compact('certtypes','users','name_of_user','date_raised'));

	}

	public function generatecertreqserial($branch){
		//$this->current_year = Carbon::now()->format('Y');
		//$this->current_month = Carbon::now()->format('m');
        $serial = Doctype::where('doc_type','CRT')->first();
        $new_serial = str_pad((string) $branch, 3, '0', STR_PAD_LEFT).str_pad((string) $serial->serial_no, 6, '0', STR_PAD_LEFT).Carbon::now()->format('Y').Carbon::now()->format('m');
        return  $new_serial ;
    }

	public function updatecertreqserial(){

        $new_serial = Doctype::where('doc_type','CRT')->increment('serial_no',1);
        //  return $new_serial;
    }

	public function post_cert_request(Request $request){

		Gate::authorize('raise-certificate-requisition');
		
		/*** ASSIGN VALUES ***/

		$raised_by = Auth::user()->user_name;
		$raised_date = Carbon::now();
		$escalate_to = $request->input('escalate_to');
		$branch = Auth::user()->branch;
		$cert_type = $request->input('cert_type');
		$cert_total = $request->input('cert_total');
		$request_details = $request->input('request_details');
		$period_year = Carbon::now()->format('Y');
		$period_month = Carbon::now()->format('m');
		$type = 'escalate';

		DB::beginTransaction();

		try{

			for($i=0;$i<count($cert_type);$i++){

				$req_no = $this->generatecertreqserial($branch);

				$certreq = new Certreq;

				 $certreq->requisition_no = $req_no;
				 $certreq->requisition_date = $raised_date;
				 $certreq->raised_by = $raised_by;
				 $certreq->raised_date = $raised_date;
				 $certreq->period_year = $period_year;
				 $certreq->period_month = $period_month;
				 $certreq->cert_total = $cert_total[$i];
				 $certreq->cert_type = $cert_type[$i];
				 $certreq->branch = $branch;
				 $certreq->request_details = $request_details[$i];
				 $certreq->escalate_to = $escalate_to;

				$certreq->save();

				$this->updatecertreqserial();

				$this->email_request($requisition_no, $certreq->escalate_to, $type);

			}

			DB::commit();

			return redirect()->route('cert_request_view')->with('success', 'Certificate request was successful');
			
		}
		catch(Exception $e){

			DB::rollback();

			return redirect()->back()->with('error', 'Failed to create certificate requisition(s)');
		}

	}

	public function amend_cert_request(Request $request){

		Gate::authorize('raise-certificate-requisition');

		/*** ASSIGN VALUES ***/
		$amend_by = Auth::user()->user_name;
		$amend_date = Carbon::now();
		$message = '';
		$amend_type = $request->input('amend_type');
		$requisition_no = $request->input('requisition_no');
		$cert_total_e = $request->input('cert_total_e');
		$request_details_e = $request->input('request_details_e');
		$escalate_to = $request->input('re_escal');
		$type = 'escalate';

		DB::beginTransaction();

		try{

			$certreq = Certreq::where('requisition_no',$requisition_no)->first();

			if($certreq->raised_by != Auth::user()->user_name){

				return redirect()->route('cert_request_view')->with('error', 'This Requisition was not raised by you.');

			}

			if($amend_type == 'CANCEL' && ($certreq->approval_flag == 'Y' || $certreq->declined_flag == 'Y')){

				return redirect()->route('cert_request_view')->with('error', 'Request already actioned');

			}

			if($amend_type == 'AMEND'){
				$certreq->cert_total = $cert_total_e;
				$certreq->request_details = $request_details_e;
				$certreq->last_updated = $amend_date;

				$message = "Certificate request amended successfully";
			}
			else if($amend_type == 'RE-ESCALATE'){

				$certreq->escalate_to = $escalate_to;
				$certreq->last_updated = $amend_date;

				$message = "Certificate request re-escalated successfully";
				$type="escalate";

			}
			else if($amend_type == 'CANCEL'){

				$certreq->cancel_flag = 'Y';
				$certreq->cancel_date = $amend_date;
				$certreq->cancel_by = $amend_by;

				$message = "Certificate request cancelled successfully";

			}

			$certreq->save();

			DB::commit();

			if($amend_type == 'RE-ESCALATE'){
				
				$this->email_request($requisition_no, $certreq->escalate_to, $type);

			}

			return redirect()->route('cert_request_view')->with('success', $message);
			
		}
		catch(Exception $e){

			DB::rollback();

			return redirect()->back()->with('error', 'Action failed. Kindly try again.');

		}

	}

	public function approve_cert_request(Request $request){

		Gate::authorize('approve-certificate-requisition');

		/*** ASSIGN VALUES ***/
		$approve_by = Auth::user()->user_name;
		$approve_date = Carbon::now();
		$message = '';
		$requisition_no = $request->input('requisition_no');
		$approve_decline = $request->input('approve_decline');
		$approve_decline_comments = $request->input('approve_decline_comments');
		$type = '';

		DB::beginTransaction();

		try{

			$certreq = Certreq::where('requisition_no',$requisition_no)->first();

			if($certreq->cancel_flag == 'Y'){

				return redirect()->route('cert_request_view')->with('error', 'Cannot action. The Requisition is cancelled');

			}

			if($certreq->raised_by == Auth::user()->user_name){

				return redirect()->route('cert_request_view')->with('error', 'You cannot approve/decline your own requisitions.');

			}

			if($approve_decline == 'APPROVE'){

				$certreq->approval_flag = 'Y';
				$certreq->declined_flag = 'N';
				$certreq->approval_date = $approve_date;
				$certreq->approved_by = $approve_by;
				$certreq->approve_comment = $approve_decline_comments;

				$message = "Certificate request approved successfully";
				$type="approved";

			}
			else if($approve_decline == 'DECLINE'){

				$certreq->declined_flag = 'Y';
				$certreq->approval_flag = 'N';
				$certreq->declined_date = $approve_date;
				$certreq->declined_by = $approve_date;
				$certreq->declined_comment = $approve_decline_comments;

				$message = "Certificate request declined";
				$type="declined";

			}

			$certreq->save();

			$this->email_request($requisition_no, $certreq->raised_by, $type);

			DB::commit();

			return redirect()->route('cert_request_view')->with('success', $message);
			
		}
		catch(Exception $e){

			DB::rollback();

			return redirect()->back()->with('error', 'Action failed. Kindly try again.');

		}

	}

	public function email_request($requisition, $user_name, $type){

		$approver = User::where('user_name',$user_name)->first();

		if($type == 'escalate'){
			$category = 'CERTIFICATE REQUISITION APPROVAL';
			$message = "Kindly approve this certificate requisition: ".$requisition;

		}
		else if($type == 'declined'){
			$category = 'CERTIFICATE REQUISITION DECLINED';
			$message = "Your certificate requisition has been declined";
		}
		else if($type == 'approved'){
			$category = 'CERTIFICATE REQUISITION APPROVED';
			$message = "Your certificate requisition has been approved";
		}
		
        $email = $approver->email;
        $name = $approver->name;
    
        //$this->send_email($category,$email,$message,$name);
	
		$sendemail = new SendEmail;

        $sendemail->category = $category ;
        $sendemail->receiver = $email;
        $sendemail->message =$message;
        $sendemail->creator = $name;
		$sendemail->status = 'SEND';

        $sendemail->save();

	}

	/********** END CERTIFICATE REQUEST ******/


	/******** CERTIFICATE ISSUE LOG ********/
	private function insertCertificateLog($certificateData) {
		DB::table('Certificate_log')->insert($certificateData);
	}
	/************ END CERTIFICATE ISSUE LOG ***********/


	/********* CHECK CERT STATUS *********/

	private function getCertStatus($endt_renewal_no) {

		$certStatus = CertStatus::issued;

		//check cert credit status
		$creditStatus = $this->getCertCreditStatus();

		$debited = Debitmast::where('endt_renewal_no', $endt_renewal_no)->count();

		$certIssueApproved = Escalate_pol::where('endorse_no', $endt_renewal_no)
										->where('escalate_type',Escalate_pol::certificateEscalation)
										->where('re_escalate_date', null)
										->orderBy('created_at','desc')
										->first();

		$certIssueEscalated = Escalate_pol::where('endorse_no', $endt_renewal_no)->count();
		
		$creditRequestCheck = Cbcredit::where('claim_no',$endt_renewal_no)
									->where('cancelled',null)
									->where('cancelled_reference',null)
									->count();
		
		$certPartialPayment = Pipcnam::select('cert_partial_payment')
									->first();

		$outstandingPrem = Acdet::select('allocated','unallocated','nett')
								->where('doc_type','DRN')
								->where('endt_renewal_no',$endt_renewal_no)
								->where('source','U/W')
								->first();
		
		//on proforma with approval
		if($debited == 0 
			&& $certIssueApproved->approved == 'Y'
			) {
			$certStatus = CertStatus::OnProformaWithApproval;
		}

		//Issued
		if(($debited == 1 
		   && $creditRequestCheck == 0
		   ) && (
			($certPartialPayment->cert_partial_payment == 'N' 
			 && $outstandingPrem->unallocated == 0
			)||
			($certPartialPayment->cert_partial_payment == 'Y'
			 && (($outstandingPrem->allocated/$outstandingPrem->nett)*100) >= $creditStatus->min_cert_percentage
			 && $outstandingPrem->allocated <> 0
			)
		   )
		   )
		{
			$certStatus = CertStatus::issued;
			
		}

		//On credit
		if($debited == 1 
		   && $creditRequestCheck > 0
		   && $outstandingPrem->unallocated <> 0 
		   && $outstandingPrem->allocated == 0
		   &&  (
			$certPartialPayment->cert_partial_payment == 'N' ||
			$certPartialPayment->cert_partial_payment == 'Y'
		   ) 
		   )
		{
			$certStatus = CertStatus::issuedOnCredit;
		}

		//issue after debit
		if($debited == 1 
			&& $creditRequestCheck == 0  
			&& $certPartialPayment->cert_partial_payment == 'Y' 
			&& $creditStatus->min_cert_percentage == 0 
			&& $outstandingPrem->unallocated <> 0
			&& $outstandingPrem->allocated == 0
		) {
			
			$certStatus = CertStatus::issuedOnDebit;
		}
		

		return $certStatus;
	}

	public function endt_certlisting(Request $request){

		$endt_renewal_no = $request->get('endt_renewal_no');

		$certalloc = Certalloc::where('endt_renewal_no', $endt_renewal_no);

        return datatables::of($certalloc)

			->addColumn('cert_description', function($row){
				$c = Certtype::whereRaw("trim(type) = '".trim($row->cert_type)."'")->first();

				return $c->description;
				
			})
            ->addColumn('issued_by', function ($row) {

				$issued_by = Aimsuser_web::whereRaw("TRIM(user_name) ='".trim($row->user_name)."'")->first()->name;

				return $issued_by;
                
            })
            ->addColumn('status', function($row){

				$status = CertStatus::where('status_code', $row->cert_status)->first()->status_descr;
                
				return $status ;
                
            })
            ->escapeColumns([])

            ->make(true);


	}

	/********* END CHECK CERT STATUS ******/

}//end of class