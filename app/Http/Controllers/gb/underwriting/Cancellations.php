<?php

namespace App\Http\Controllers\gb\underwriting;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

use Carbon\Carbon;
use Session;
use Auth;


use App\Modtl;
use App\Dcontrol;
use App\Polmaster;
use App\Debitdtl;
use App\Debitmast;
use App\Polsect;
use App\Accessory;
use App\Extensions;
use App\Classbr;
use App\Pipcnam;
use App\Dtran0;
use App\ClassModel;
use App\Dept;
use App\Models\Modtlmast;
use App\Models\Modtlpivot;
use App\Models\Modtlsumm;
use App\Models\Motcvrdet;
use App\Models\Motorpolsec;
use App\RefundRates;


class Cancellations extends Controller
{
    //endorsement cancellations
    Public function getEndorsementAmount(Request $request){
      $endt_no = $request->get('endt_no');
      $prorate_date = $request->get('period_from');

      $endt_no = removePolicyOrClaimFormat($endt_no);
      $debitmast = Debitmast:://selectRaw('gross_amount as amount')
                          where('endt_renewal_no',$endt_no)
                          ->get();
      $debitmast = $debitmast[0];

      $check_motor = ClassModel::where('class',$debitmast->class)->get()[0];

      if($check_motor->motor_policy == 'Y'){
          $sum_ext = Modtl::where('endt_renewal_no',$debitmast->endt_renewal_no)->sum('extensions_prem');
          $basic_prorate_prem = $debitmast->gross_amount - $sum_ext;
      }else{
          $sum_ext = Polsect::where('policy_no',$debitmast->policy_no)->sum('extension_prem');
          $basic_prorate_prem = $debitmast->gross_amount - $sum_ext;
      }

      $period_from = date_create($debitmast->period_from);
      $period_to = date_create($debitmast->period_to);
      $prorate_date = date_create($prorate_date);

      $diff = date_diff($period_from, $period_to);
      $cover_days = (int)$diff->format('%a')+1;

      $diff1 = date_diff($prorate_date, $period_to);
      $prorate_days = (int)$diff1->format('%a')+1;

      $amount = $prorate_days/$cover_days*$basic_prorate_prem;
      //$amount = round($amount);
      $amount = $amount + $sum_ext;

      $result = array('amount' => $amount);

      return $result;

    }

    //post cancellations
    public function postPolCnc(Request $request){
      //DB::Transaction(function() use ($request){
        // dd($request); 

        // *** date format syntax ****
        // $d = new DateTime('10-16-2003');
        // $formatted_date = $d->format('Y-m-d'); 
        // $p_2 = new DateTime($request->cnc_period_from);
        // return 
        $today = Carbon::today();
        $user = Auth::user()->user_name;
        $cnc_pol_no = removePolicyOrClaimFormat($request->cnc_pol_no);
        
        $polmaster = Polmaster::where('policy_no',$cnc_pol_no)->get();
        $polmaster = $polmaster[0];

        //dd($polmaster);

        $classbr = Classbr::where('class',$polmaster->class)->get();
        $classbr = $classbr[0];

        $pipcnam = Pipcnam::get();
        $pipcnam = $pipcnam[0];

        
        $cnc_rfn_endt = removePolicyOrClaimFormat($request->cnc_endt_no);
        $dcontrol = Dcontrol::where('endt_renewal_no',$cnc_rfn_endt)->get();

        $dcontrol = $dcontrol[0];
        // return $dcontrol->period_from;

        $period_from = date_create($request->cnc_period_from); 
        $period_to = date_create($dcontrol->period_to);
        $cancellation_date = date_create($request->cnc_date); 

        // return [$period_from, $period_to];

        $diff = date_diff($cancellation_date, $period_to);
        $cover_days = (int) $diff->format('%a')+1;
        // return [$period_from, 'jjjj',  $period_to]; 
        

        $renewal_date = $polmaster->renewal_date;
        $endt_code = $pipcnam->refund_code;
        $endt_serial = $classbr->cancel_serial;
        $branch = $dcontrol->branch;
        $class = $dcontrol->class;
        $month = $today->format('m');
        $year = $today->format('Y');

        //dd($endt_serial);

        $branch=str_pad($branch, 3,"0",STR_PAD_LEFT);
        $class=str_pad($class, 3,"0",STR_PAD_LEFT);
        $month=str_pad($month, 2,"0",STR_PAD_LEFT);
        $serial=str_pad($endt_serial, 6,"0",STR_PAD_LEFT);

        $endorsement_no = $branch.$class.$endt_code.$serial.$year.$month;
        $doc_trans=Dtran0::where('rec_no',0)->get(['dtran_no','account_month','account_year']);
        $dtran_no=$doc_trans[0]->dtran_no;
        $account_month=$doc_trans[0]->account_month;
        $account_year=$doc_trans[0]->account_year;
        $dtran0=Dtran0::where('rec_no',0)->increment('dtran_no',(int)'1');

        // return $dcontrol->policy_no;  
        $create_dcontrol = Dcontrol::create([
          'policy_no'=>$dcontrol->policy_no,
          'endt_renewal_no'=>$endorsement_no,
          'class'=>$class,
          'branch'=>$branch,
          'agent'=>$dcontrol->agent,
          //'endt_type'=>$endt_code,
          'endorse_date'=>$today,
          'renewal_date'=>$dcontrol->renewal_date,
          'dprop_no'=>$dcontrol->dprop_no,
          'prop_date'=>$dcotrol->prop_date, 
          'user_str'=>trim($user),
          'period_from'=>$request->cnc_period_from,
          'period_to'=>$dcontrol->period_to,
          'effective_date'=>$request->cnc_date,
          'branch_code'=>$dcontrol->branch_code,
          'co_insure'=>$dcontrol->co_insure,
          'type_of_bus'=>$dcontrol->type_of_bus,
          'dept'=>$dcontrol->dept,
          'actual_period_from'=>$request->cnc_period_from,
          'actual_period_to'=>$dcontrol->actual_period_to,
          'financed'=>$dcontrol->financed,
          'ast_marker'=>$dcontrol->ast_marker,
          'items_total'=>$polmaster->items_total,
          'branch_cod'=>$dcontrol->branch_cod,
          'ext_from'=>$dcontrol->ext_from,
          'ext_to'=>$dcontrol->ext_to,
          'financed_code'=>$dcontrol->financed_code,
          'currency'=>$dcontrol->currency,
          'dtrans_no'=>$dtran_no,
          'insured'=>$dcontrol->insured,
          'trans_type'=>$request->cnc_trans_type,
          'dola'=>$today,
          'sum_insured'=>$dcontrol->sum_insured,
          'prov_premium'=>$request->cnc_amount,
          'location'=>$dcontrol->location,
          'time'=>Carbon::now(),
          'expiry_date'=>$dcontrol->expiry_date,
          'pin_no'=>$dcontrol->pin,
          'client_number'=>$dcontrol->client_number,
          'surname'=>$dcontrol->surname,
          'others'=>$dcontrol->others,
          'first_name'=>$dcontrol->first_name,
          'client_type'=>$dcontrol->client_type,
          'incept_date'=>$dcontrol->incept_date,
          'company_class_code'=>$dcontrol->company_class_code,
          'account_year'=>$account_year,
          'account_month'=>$account_month,
          'name'=>$dcontrol->name,
          //'cancelled'=>'Y',
          'source'=>$dcontrol->source,
          'doc_type'=>'CRN',
          'currency_rate'=>$dcontrol->currency_rate,
          'sticker_amount'=>$dcontrol->sticker_amount,
          'binder_flag'=>$dcontrol->binder_flag,
          'line_no'=>$dcontrol->line_no,
          'binder_pol_no'=>$dcontrol->binder_pol_no,
          'binder'=>$dcontrol->binder,
          'fleet'=>$dcontrol->fleet,
          'days_covered'=>$cover_days,
          'pvt_cover'=>$dcontrol->pvt_cover,
          'user_1'=>trim($user),
          'cnc_rfn_endt'=>$dcontrol->endt_renewal_no
        ]);

        if($create_dcontrol){
              //update dcontrol
              $dcontrol_upd = Dcontrol::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                                      ->update([
                                        'cancelled'=>'Y',
                                        'cnc_on'=>$today,
                                        'cnc_no'=>$endorsement_no
                                      ]);

              //increment cancellation serial
              $next_serial = Classbr::where('class',$polmaster->class)
                                    ->increment('cancel_serial',(int)'1');
            //update polmaster
              $this->modify_polmaster($endorsement_no,$year);

            //update motor details 
              $this->modify_modtl($endorsement_no,$dcontrol->endt_renewal_no);

            //insert narration details
              $this->debitdtl_create($endorsement_no, $request->cnc_reason, $request->other_cnc_reason);

              $dept=Dept::where('dept',$class)->get();
              Session::flash('success','Cancellation was Successful');
              
              return redirect()->action(
                'gb\underwriting\Policy_functions@index', ['endt_renewal_no' => $endorsement_no]);
          }
          Session::flash('error','Error, an error occured');
          return back();

        
        
      //});
    }

    public function checkUndebited(Request $request){
      $policy_no = $request->get('policy_no');
      $policy_no = removePolicyOrClaimFormat($policy_no);
      $polmaster = Polmaster::where('policy_no',$policy_no)
                            ->get();
      $polmaster = $polmaster[0];

      $count = 0;
      if($polmaster->trans_type == 'NIL'){
        $count = 1;
      }
      else{
        $count = Debitmast::where('endt_renewal_no',$polmaster->endorse_no)->count();
      }

      $result = array("count" => $count);
      return $result;

    }

    public function modify_polmaster($endt_renewal_no,$uw_year){
        //find dcontrol record
        $dcontrol=Dcontrol::where('endt_renewal_no',$endt_renewal_no)->get();
        $dcontrol=$dcontrol[0];
        //find polmaster record for update 
         $polmaster=Polmaster::where('policy_no',$dcontrol->policy_no)->update([
          'endorse_no'=>$dcontrol->endt_renewal_no,
          'name'=>(string)$dcontrol->name,
          'type_of_bus'=>$dcontrol->type_of_bus,
          'period_from'=>(string)$dcontrol->period_from,
          'period_to'=>(string)$dcontrol->period_to,
          'expiry_date'=>(string)$dcontrol->expiry_date,
          'renewal_date'=>(string)$dcontrol->renewal_date,
          'endorse_date'=>(string)$dcontrol->endorse_date,
          'dola'=>Carbon::today(),
          'period'=>$dcontrol->account_month.$dcontrol->account_year,
          'ast_marker'=>(string)$dcontrol->ast_marker,
          'tran_no'=>$dcontrol->tran_no,
          'client_number'=>(string)$dcontrol->client_number,
          'uw_year'=>$uw_year,
          'endorse_amount'=>$dcontrol->prov_premium,
          'prev_premium'=>0,
          'branch'=>$dcontrol->branch,
          'agent_no'=>$dcontrol->agent,
          'class'=>(string)$dcontrol->class,
          'dtrans_no'=>$dcontrol->dtrans_no,
          'created_by'=>(string)trim($dcontrol->user_str),
         //$polmaster->time_created=Carbon::now();
          'company_class_code'=>(string)$dcontrol->class,
          'finance_code'=>$dcontrol->financier,
          'financed'=>$dcontrol->financed,
          'currency_code'=>$dcontrol->currency_code,
          'premium_method'=>$dcontrol->ast_marker,
          'trans_type'=>(string)$dcontrol->trans_type,
          'sticker_amount'=>$dcontrol->sticker_amount,
         ]);
    }

    public function modify_modtl($new_endt_no,$old_endt_no){
      $dcontrol = Dcontrol::where('endt_renewal_no',$new_endt_no)
                            ->get();
        $dcontrol = $dcontrol[0];

      $modtl = Modtl::where('endt_renewal_no',$old_endt_no)
                          ->update([
                            'cancelled'=>'Y',
                            'endt_renewal_no'=>$dcontrol->endt_renewal_no,
                            'effective_date'=>$dcontrol->period_from,
                            'dtrans_no'=>$dcontrol->dtrans_no
                          ]);
          //modify accessories
        $accessories=Accessory::where('policy_no',$dcontrol->policy_no)
                              ->where('endt_renewal_no',$old_endt_no)
                              ->update([
                            'endt_renewal_no'=>$dcontrol->endt_renewal_no
                              ]);

        //modify extensionsssssss
         $extensions=Extensions::where('policy_no',$dcontrol->policy_no)
                              ->where('endt_renewal_no',$old_endt_no)
                              ->update([
                            'endt_renewal_no'=>$dcontrol->endt_renewal_no
                         ]);
    }

    public function debitdtl_create($endorsement_no, $detail_code, $other_reason){
      $dcontrol = Dcontrol::where('endt_renewal_no',$endorsement_no)
                          ->get();

      $dcontrol = $dcontrol[0];
      if($dcontrol->doc_type == 'DRN'){
        $dr_cr = 'D';
      }
      else if($dcontrol->doc_type == 'CRN'){
        $dr_cr = 'C';
      }
      else{
        $dr_cr = "";
      }

      $debitdtl = Debitdtl::create([
        'policy_no'=>$dcontrol->policy_no,
        'endt_renewal_no'=>$dcontrol->endt_renewal_no,
        'dtrans_no'=>$dcontrol->dtrans_no,
        'dr_cr'=>$dr_cr,
        'dola'=>Carbon::today(),
        'effective_date'=>$dcontrol->effective_date,
        'detail_code'=>$detail_code,
        'detail2'=>$other_reason,
        'doc_type'=>$dcontrol->doc_type
      ]);
    }

    //post reinstatement
    public function postPolRns(Request $request){
      //DB::Transaction(function() use ($request){
        $today = Carbon::today();
        $user = Auth::user()->user_name;
        $rns_pol_no = removePolicyOrClaimFormat($request->rns_pol_no);
        
        $polmaster = Polmaster::where('policy_no',$rns_pol_no)->get();
        $polmaster = $polmaster[0];

        //dd($polmaster);

        $classbr = Classbr::where('class',$polmaster->class)->get();
        $classbr = $classbr[0];

        $pipcnam = Pipcnam::get();
        $pipcnam = $pipcnam[0];

        
        $rns_endt = removePolicyOrClaimFormat($request->rns_endt_no);
        $dcontrol = Dcontrol::where('endt_renewal_no',$rns_endt)->get();

        $dcontrol = $dcontrol[0];
        //dd($dcontrol);

        $period_from = date_create($request->rns_period_from);
        $period_to = date_create($dcontrol->period_to);

        $diff = date_diff($period_from, $period_to);
        $cover_days = (int)$diff->format('%a')+1;
        

        $renewal_date = $polmaster->renewal_date;
        $endt_code = $pipcnam->extra_code;
        $endt_serial = $classbr->extra_serial;
        $branch = $dcontrol->branch;
        $class = $dcontrol->class;
        $month = $today->format('m');
        $year = $today->format('Y');

        //dd($endt_serial);

        $branch=str_pad($branch, 3,"0",STR_PAD_LEFT);
        $class=str_pad($class, 3,"0",STR_PAD_LEFT);
        $month=str_pad($month, 2,"0",STR_PAD_LEFT);
        $serial=str_pad($endt_serial, 6,"0",STR_PAD_LEFT);

        $endorsement_no = $branch.$class.$endt_code.$serial.$year.$month;
        $doc_trans=Dtran0::where('rec_no',0)->get(['dtran_no','account_month','account_year']);
        $dtran_no=$doc_trans[0]->dtran_no;
        $account_month=$doc_trans[0]->account_month;
        $account_year=$doc_trans[0]->account_year;
        $dtran0=Dtran0::where('rec_no',0)->increment('dtran_no',(int)'1');


        $create_dcontrol = Dcontrol::create([
          'policy_no'=>$dcontrol->policy_no,
          'endt_renewal_no'=>$endorsement_no,
          'class'=>$class,
          'branch'=>$branch,
          'agent'=>$dcontrol->agent,
          //'endt_type'=>$endt_code,
          'endorse_date'=>$today,
          'renewal_date'=>$dcontrol->renewal_date,
          'dprop_no'=>$dcontrol->dprop_no,
          'prop_date'=>$dcotrol->prop_date, 
          'user_str'=>trim($user),
          'period_from'=>$request->rns_period_from,
          'period_to'=>$dcontrol->period_to,
          'effective_date'=>$request->rns_period_from,
          'branch_code'=>$dcontrol->branch_code,
          'co_insure'=>$dcontrol->co_insure,
          'type_of_bus'=>$dcontrol->type_of_bus,
          'dept'=>$dcontrol->dept,
          'actual_period_from'=>$request->rns_period_from,
          'actual_period_to'=>$dcontrol->actual_period_to,
          'financed'=>$dcontrol->financed,
          'ast_marker'=>$dcontrol->ast_marker,
          'items_total'=>$polmaster->items_total,
          'branch_cod'=>$dcontrol->branch_cod,
          'ext_from'=>$dcontrol->ext_from,
          'ext_to'=>$dcontrol->ext_to,
          'financed_code'=>$dcontrol->financed_code,
          'currency'=>$dcontrol->currency,
          'dtrans_no'=>$dtran_no,
          'insured'=>$dcontrol->insured,
          'trans_type'=>$request->rns_trans_type,
          'dola'=>$today,
          'sum_insured'=>$dcontrol->sum_insured,
          'prov_premium'=>$request->rns_amount,
          'location'=>$dcontrol->location,
          'time'=>Carbon::now(),
          'expiry_date'=>$dcontrol->expiry_date,
          'pin_no'=>$dcontrol->pin,
          'client_number'=>$dcontrol->client_number,
          'surname'=>$dcontrol->surname,
          'others'=>$dcontrol->others,
          'first_name'=>$dcontrol->first_name,
          'client_type'=>$dcontrol->client_type,
          'incept_date'=>$dcontrol->incept_date,
          'company_class_code'=>$dcontrol->company_class_code,
          'account_year'=>$account_year,
          'account_month'=>$account_month,
          'name'=>$dcontrol->name,
          //'cancelled'=>'Y',
          'source'=>$dcontrol->source,
          'doc_type'=>'DRN',
          'currency_rate'=>$dcontrol->currency_rate,
          'sticker_amount'=>$dcontrol->sticker_amount,
          'binder_flag'=>$dcontrol->binder_flag,
          'line_no'=>$dcontrol->line_no,
          'binder_pol_no'=>$dcontrol->binder_pol_no,
          'binder'=>$dcontrol->binder,
          'fleet'=>$dcontrol->fleet,
          'days_covered'=>$cover_days,
          'pvt_cover'=>$dcontrol->pvt_cover,
          'user_1'=>trim($user)
        ]);

        if($create_dcontrol){
          //dd($create_dcontrol);
          //update dcontrol
          $dcontrol_upd = Dcontrol::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                                  ->update([
                                    'cancelled'=>'',
                                    'cnc_on'=>'',
                                    'cnc_no'=>''
                                  ]);

          //increment cancellation serial
          $next_serial = Classbr::where('class',$polmaster->class)
                                ->increment('extra_serial',(int)'1');
        //update polmaster
          $this->modify_polmaster($endorsement_no,$year);

        //update motor details
          $this->modify_modtl($endorsement_no,$dcontrol->endt_renewal_no);

          $dept=Dept::where('dept',$class)->get();
          $department = $dept[0];
        }
        return redirect()->route('risk',['policy_no' =>$rns_pol_no,'endt_renewal_no' =>$endorsement_no,'motor'=>$department->motor]);
    }

    public function getCncEndorsement(Request $request){
      $policy_no = $request->get('policy_no');
      $latest_rec = Debitmast::whereRaw("policy_no='".$policy_no."' and (entry_type_descr='POL' or entry_type_descr='REN' or entry_type_descr='RNS')")->orderBy('dtrans_no','DESC')->limit(1)->get()[0];

      $dcontrol_dtrans = Dcontrol::where("endt_renewal_no",$latest_rec->endt_renewal_no)->get(['dtrans_no'])[0];

      $rfn_dcon_rec = Dcontrol::whereRaw("policy_no='".$policy_no."' and to_number(dtrans_no)>=".(int)$dcontrol_dtrans->dtrans_no)->get();

      $pol_rec = Polmaster::where('policy_no',$policy_no)->first();
      $class = ClassModel::where('class',$pol_rec->class)->first();

      $debitmast = 0;

      if($class->motor_policy == 'Y'){
        $active_vehicles = Modtlmast::where('endt_renewal_no',$pol_rec->endorse_no)
          ->where('status','ACT')
          ->pluck('reg_no')
          ->toArray();
        $active_endorsments = Dcontrol::where('policy_no',$policy_no)
          ->whereNull('delete_str')
          ->pluck('endt_renewal_no');
        $debitmast = Modtlsumm::where('policy_no',$policy_no)
          ->whereIn('reg_no',$active_vehicles)
          ->whereIn('endt_renewal_no',$active_endorsments)
          // ->get();
          ->sum('endorse_amount') ?? 0;
      }
      else{
        foreach ($rfn_dcon_rec as $rfn_dcon) {
            $rfn_amount = Debitmast::where("endt_renewal_no",$rfn_dcon->endt_renewal_no)->sum('foreign_premium');
  
            $debitmast = $debitmast + $rfn_amount;
        }
      }
  
      $dat = array([
        'endt_renewal_no'=>$latest_rec->endt_renewal_no,
        'gross_amount'=>$debitmast,
        //'period_from'=>$latest_rec->period_from,
        //'period_to'=>$latest_rec->period_to,
        'period_from'=>$pol_rec->cov_period_from,
        'period_to'=>$pol_rec->period_to,
        'entry_type_descr'=>$latest_rec->entry_type_descr
      ]);

      return json_encode($dat);
    }

  public function getCncRefundAmount(Request $request){
      $debitmast = Debitmast::where('endt_renewal_no',$request->get('endt_no'))->get()[0];
      $latest_debit = Debitmast::whereRaw("policy_no='".$debitmast->policy_no."' and doc_type='DRN' and to_number(dtrans_no)>=".(int)$debitmast->dtrans_no)->orderBy('dtrans_no','DESC')->limit(1)->get()[0];
      $pol_rec = Polmaster::where('policy_no',$debitmast->policy_no)->first();
      //$period_from = Carbon::parse($debitmast->period_from);
      //$period_to = Carbon::parse($debitmast->period_to);
      //$cnc_date = Carbon::parse($request->get('cnc_date'));

      $period_from = new \DateTime($pol_rec->cov_period_from);
      $period_to = new \DateTime($latest_debit->period_to);
      $cnc_date = new \DateTime($request->get('cnc_date'));

      $cover_days = $period_to->setTime(0,0)->diff($period_from->setTime(0,0))->days;
      $cover_days = $cover_days + 1;
      
      $days_covered = $period_from->setTime(0,0)->diff($cnc_date->setTime(0,0))->days;
      //$days_covered = $days_covered + 1;
      

      $debit_amt = (float) str_replace(',','', $request->get('cnc_debit_amount'));
      $calc_method = $request->get('calc_method');

      //dd($debit_amt);
      //$days_covered = $period_from->diffInDays($cnc_date);
      //dd($days_covered);
      $trans_type = $debitmast->entry_type_descr;
      
      $refund_rates = RefundRates::whereRaw("minimum_days <= '".$days_covered."' and maximum_days >= '".$days_covered."'")->get()[0];
      $cnc_reason = $request->cnc_reason;

      $check_motor = ClassModel::where('class',$debitmast->class)->get()[0];

      if($check_motor->motor_policy == 'Y'){
          $pol_rec = Polmaster::where('policy_no',$debitmast->policy_no)->get()[0];
          $sum_ext = Modtl::where('policy_no',$debitmast->policy_no)->sum('extensions_prem');
          //$basic_prorate_prem = $pol_rec->annual_premium - $sum_ext;
          $active_vehicles = Modtlmast::where('endt_renewal_no',$pol_rec->endorse_no)
              ->where('status','ACT')
              ->pluck('reg_no');

          $sum_ext = Motorpolsec::where('policy_no',$debitmast->policy_no)
            ->where('policy_no',$debitmast->policy_no)
            ->whereIn('reg_no',$active_vehicles)
            ->whereExists(function($query) {
                $query->select('*')
                    ->from('motorprem_grp')
                    ->where('motorprem_grp.refundable','<>','Y')
                    ->whereColumn('motorprem_grp.grp_code','motorpolsec.grp_code');
            })
            ->sum('endorse_amount');
            
          $basic_prorate_prem = $debit_amt - $sum_ext;
      }else{
          $pol_rec = Polmaster::where('policy_no',$debitmast->policy_no)->get()[0];
          $sum_ext = Polsect::where('policy_no',$debitmast->policy_no)->sum('extension_prem');
          //$basic_prorate_prem = $pol_rec->annual_premium - $sum_ext;
          $basic_prorate_prem = $debit_amt - $sum_ext;
      }
     // dd($cnc_reason);
      if ($cnc_reason == "N") {
          
          if($calc_method == 'F'){
              $cnc_amount = $basic_prorate_prem;
          }
          else if($calc_method == "S"){
              $cnc_rate = $refund_rates->percentage;
                  
              if (!empty($cnc_rate) || $cnc_rate == 0) {
                  $retained_amount = $cnc_rate * $basic_prorate_prem / 100;
                  $cnc_amount = $basic_prorate_prem - $retained_amount;  // + $sum_ext;
              } else {
                  $cnc_amount = 0;
              }
          }
          else if($calc_method == "P"){
              $prorate_days = $cover_days - $days_covered;
                      
              $cnc_amount = ($prorate_days / $cover_days) * $basic_prorate_prem;

              //dd($prorate_days." - ".$cover_days." - ".$days_covered);
          }
                  
          
          $result = array("cnc_amount" => $cnc_amount);
           return $result;

      }else{
          $result = array("cnc_amount" => $debit_amt);
           return $result;
      }
  }


    /**refund functions */

    public function get_ref_dates(Request $request){
      $policy_no = $request->get('policy_no');
      $polmaster = Polmaster::where('policy_no',$policy_no)->get();
      $polmaster = $polmaster[0];
      // dd($polmaster);
    
      $dcontrol = Dcontrol::whereRaw("policy_no='".$policy_no."' and expiry_date='".$polmaster->expiry_date."' and (trans_type='POL' or trans_type='REN')")->get();
      $dcontrol = $dcontrol[0];
                                
      $debitmast = Debitmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)->get();
      $debitmast = $debitmast[0];
      //dd($polmaster->policy_no);
      return $polmaster;
    }

    public function get_ref_amount(Request $request){
      //$debitmast = Debitmast::where('endt_renewal_no',$request->get('endt_no'))->get()[0];

        $latest_rec = $this->latest_baseTransaction($request->get('pol_no'));


        $dcontrol_dtrans = Dcontrol::where("endt_renewal_no",$latest_rec->endt_renewal_no)->get(['dtrans_no'])[0];

        //$rfn_dcon_rec = Dcontrol::whereRaw("policy_no='".$request->get('pol_no')."' and to_number(dtrans_no)>=".(int)$dcontrol_dtrans->dtrans_no)->get();
        $rfn_dcon_rec = Dcontrol::where("endt_renewal_no", $request->get('ext_endt'))->get();

        if($rfn_dcon_rec->isEmpty()){

            $rfn_dcon_rec = Dcontrol::where("endt_renewal_no", $latest_rec->endt_renewal_no)->get();
            
        }

        $debitmast = 0;

        foreach ($rfn_dcon_rec as $rfn_dcon) {
            $rfn_amount = Debitmast::where("endt_renewal_no",$rfn_dcon->endt_renewal_no)->sum('foreign_premium');

            $debitmast = $debitmast + $rfn_amount;

        }
        
      // get refund amount for EXT being refunded against
      if($request->refund_ext == 'Y'){
        $ext_resp = $this->get_ext_amt($request);
        if($ext_resp['status'] == 0){
          return response()->json(['status'=>0,'message' => 'Endorsement number missing'], 400,);
        }
        else{
          $rfn_dcon = $ext_resp['rfn_dcon'];
          $debitmast = $ext_resp['debitmast'];
        }
      }
      else{
        $rfn_dcon = Dcontrol::where("endt_renewal_no",$latest_rec->endt_renewal_no)->first();
      }
      
      if($rfn_dcon->trans_type == 'PTA'){
        $period_from = Carbon::parse($rfn_dcon->period_from);
      }
      else{
        //$period_from = Carbon::parse($rfn_dcon->cov_period_from);
        $period_from = Carbon::parse($rfn_dcon->effective_date);
      }
        $period_to = Carbon::parse($rfn_dcon->period_to);
        $ref_date = Carbon::parse($request->get('ref_date'));


        $days_covered = $period_from->diffInDays($ref_date,false);
        // $days_covered = $days_covered + 1;
        // $trans_type = $debitmast->entry_type_descr;

        $refund_rates = RefundRates::whereRaw("minimum_days <= '".$days_covered."' and maximum_days >= '".$days_covered."'")->get()[0];

            $ref_date = $refund_rates->percentage;

            if(!empty($ref_date) || $ref_date == 0){

              $retained_amount = $ref_date * $debitmast / 100; 
              $refund_amount = $debitmast - $retained_amount;
              // dd($refund_amount);

            }
            else{
              $refund_amount = 0;
            }
     
        $result = array("refund_amount" => $refund_amount);
        return $result;
    }

    public function bkget_ref_amountbk(Request $request){
      //$debitmast = Debitmast::where('endt_renewal_no',$request->get('endt_no'))->get()[0];

        $latest_rec = Debitmast::whereRaw("policy_no='".$request->get('pol_no')."' and (entry_type_descr='POL' or entry_type_descr='REN' or entry_type_descr='RNS')")->orderBy('dtrans_no','DESC')->limit(1)->get()[0];


        $dcontrol_dtrans = Dcontrol::where("endt_renewal_no",$latest_rec->endt_renewal_no)->get(['dtrans_no'])[0];

        $rfn_dcon_rec = Dcontrol::whereRaw("policy_no='".$request->get('pol_no')."' and to_number(dtrans_no)>=".(int)$dcontrol_dtrans->dtrans_no)->get();

        $debitmast = 0;

        foreach ($rfn_dcon_rec as $rfn_dcon) {
            $rfn_amount = Debitmast::where("endt_renewal_no",$rfn_dcon->endt_renewal_no)->sum('foreign_premium');

            $debitmast = $debitmast + $rfn_amount;

        }
      
        $period_from = Carbon::parse($latest_rec->period_from);
        $period_to = Carbon::parse($latest_rec->period_to);
        $ref_date = Carbon::parse($request->get('ref_date'));


        $days_covered = $period_from->diffInDays($ref_date,false);
        // $trans_type = $debitmast->entry_type_descr;

        $refund_rates = RefundRates::whereRaw("minimum_days <= '".$days_covered."' and maximum_days >= '".$days_covered."'")->get()[0];

            $ref_date = $refund_rates->percentage;

            if(!empty($ref_date) || $ref_date == 0){

              $retained_amount = $ref_date * $debitmast / 100; 
              $refund_amount = $debitmast - $retained_amount;
              // dd($refund_amount);

            }
            else{
              $refund_amount = 0;
            }
     
        $result = array("refund_amount" => $refund_amount);
        return $result;
    }


    //end refund


    /**refund functions */

    public function get_partial_dates(Request $request){
      $policy_no = $request->get('policy_no');
      $polmaster = Polmaster::where('policy_no',$policy_no)->get();
      $polmaster = $polmaster[0];
      // dd($polmaster);
    
      $dcontrol = Dcontrol::whereRaw("policy_no='".$policy_no."' and expiry_date='".$polmaster->expiry_date."' and (trans_type='POL' or trans_type='REN')")->get();
      $dcontrol = $dcontrol[0];
                                
      $debitmast = Debitmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)->get();
      $debitmast = $debitmast[0];
    
          return $debitmast;
    }

    public function get_partial_amount(Request $request){

        $latest_rec = $this->latest_baseTransaction($request->get('pol_no'));

        $dcontrol_dtrans = Dcontrol::where("endt_renewal_no",$latest_rec->endt_renewal_no)->get(['dtrans_no'])[0];

        //$rfn_dcon_rec = Dcontrol::whereRaw("policy_no='".$request->get('pol_no')."' and to_number(dtrans_no)>=".(int)$dcontrol_dtrans->dtrans_no)->get();
        
        $rfn_dcon_rec = Dcontrol::where("endt_renewal_no", $request->get('ext_endt'))->get();

        if($rfn_dcon_rec->isEmpty()){

            $rfn_dcon_rec = Dcontrol::where("endt_renewal_no", $latest_rec->endt_renewal_no)->get();

        }
        
        $debitmast = 0;

        foreach ($rfn_dcon_rec as $rfn_dcon) {
            $rfn_amount = Debitmast::where("endt_renewal_no",$rfn_dcon->endt_renewal_no)->sum('foreign_premium');

            $debitmast = $debitmast + $rfn_amount;

        }
          
        // get refund amount for EXT being refunded against
        if($request->refund_ext == 'Y'){
            $ext_resp = $this->get_ext_amt($request);
            if($ext_resp['status'] == 0){
              return response()->json(['status'=>0,'message' => 'Endorsement number missing'], 400,);
            }
            else{
              $rfn_dcon = $ext_resp['rfn_dcon'];
              $debitmast = $ext_resp['debitmast'];
            }
        }
        //get endorsement details
        else{
            $rfn_dcon = Dcontrol::where("endt_renewal_no",$latest_rec->endt_renewal_no)->first();
        }

        if($rfn_dcon->trans_type == 'PTA'){
            $period_from = new \DateTime($rfn_dcon->period_from);
        }
        else{
            //$period_from = new \DateTime($rfn_dcon->cov_period_from);
            $period_from = new \DateTime($rfn_dcon->effective_date);
        }

        $period_to = new \DateTime($rfn_dcon->period_to);
        $ref_date = new \DateTime($request->get('ref_date'));

        //$cover_days = $period_to->setTime(0,0)->diff($period_from->setTime(0,0))->days;
        $days = $rfn_dcon->days_covered;

      
        $days_covered = $period_from->setTime(0,0)->diff($ref_date->setTime(0,0))->days;
        $days_covered = $days_covered;

        $days_remaining = $days - $days_covered;
        //dd($rfn_dcon);
        //dd($days_remaining." - ".$days." - ".$debitmast);
        $fin_amnt = round ((( $days_remaining / $days)*$debitmast),2);

        // $check_motor = ClassModel::select('motor_policy')
        //                           ->where('class',$rfn_dcon->class)
        //                           ->first();

        // if ($check_motor->motor_policy == 'Y') {

        //   $proratedAmount = $this->prorateMotorRisk($rfn_dcon->policy_no, 'P',$request->ref_date,$request->ext_endt);

        //   $fin_amnt = $proratedAmount;

        // }

        $result = array("refund_amount" => $fin_amnt);  
        return $result;
    }

    public function bkget_partial_amountbk(Request $request){
      //$debitmast = Debitmast::where('endt_renewal_no',$request->get('endt_no'))->get()[0];

        $latest_rec = Debitmast::whereRaw("policy_no='".$request->get('pol_no')."' and (entry_type_descr='POL' or entry_type_descr='REN' or entry_type_descr='RNS')")->orderBy('dtrans_no','DESC')->limit(1)->get()[0];


        $dcontrol_dtrans = Dcontrol::where("endt_renewal_no",$latest_rec->endt_renewal_no)->get(['dtrans_no'])[0];

        $rfn_dcon_rec = Dcontrol::whereRaw("policy_no='".$request->get('pol_no')."' and to_number(dtrans_no)>=".(int)$dcontrol_dtrans->dtrans_no)->get();

        $debitmast = 0;

        foreach ($rfn_dcon_rec as $rfn_dcon) {
            $rfn_amount = Debitmast::where("endt_renewal_no",$rfn_dcon->endt_renewal_no)->sum('foreign_premium');

            $debitmast = $debitmast + $rfn_amount;

        }
      
      
        //get endorsement details
        $dcontrol = Dcontrol::where('endt_renewal_no', $request->get('endt_no'))->get();
        $dcontrol = $dcontrol[0];
        $class = ClassModel::where('class', $dcontrol->class)->first();

        $period_from = Carbon::parse($latest_rec->period_from);
        $period_to = Carbon::parse($latest_rec->period_to);
        $ref_date = Carbon::parse($request->get('ref_date'));

        $days_remaining = $ref_date->diffInDays($period_to,false);
        $days = $period_from->diffInDays($period_to,false);
        
        if($class->exem == "Y"){
          $days_covered = $days +1;
          $days_remaining = $days_remaining +1;
        }
        elseif($class->exem == "N"){
          $days_covered = $days + 2;
          $days_remaining = $days_remaining + 2;
        }
        else{
          $days_covered = $days ;
          $days_remaining = $days_remaining;
        }
        
        $fin_amnt = round ((( $days_remaining/ $days_covered)*$debitmast),2);

        $result = array("refund_amount" => $fin_amnt);    
        return $result;
    }


    //end of partial refund

    //validaate risk note number
    public function verify_risk_note(Request $request)
    {
      $risk_note = $request->risk_note;
      $branch = $request->branch;
      $agent = $request->agent;
      $note_exists = Dcontrol::where('risk_note_no', $risk_note)
        ->where('branch',$branch)
        ->where('agent',$agent)
        ->whereNull('delete_str')
        ->exists();

      if($note_exists){
        return response()->json(['status' => 0], 200);
      }
      else{
        return response()->json(['status' => 1], 200);
      }
    }

    public function latest_baseTransaction($policy_no)
    {

      $lastTrans = Debitmast::select('endt_renewal_no','policy_no','period_from','period_to')
                            ->where('policy_no',$policy_no)
                            ->whereIn('entry_type_descr',['POL','REN','INS','RNS']) 
                            ->orderBy('dtrans_no','DESC')
                            ->limit(1)
                            ->first();

      if(empty($lastTrans)){
        $countMac = Dcontrol::where('policy_no',$policy_no)->where('trans_type','MAC')->count();
        if($countMac > 0){
            $lastTrans = Dcontrol::select('endt_renewal_no','policy_no','period_from','period_to')
                                ->where('policy_no',$policy_no)
                                ->whereIn('trans_type',['POL','REN','INS','RNS']) 
                                ->orderBy('dtrans_no','DESC')
                                ->limit(1)
                                ->first();
        }
        

      }

      return $lastTrans;
    }

    // check if there are any EXT that happened after POL,RNS or REN
    public function check_for_ext(Request $request)
    {
      $policy_no = $request->policy_no;
      $latest_rec = $this->latest_baseTransaction($policy_no);

                    
      $dcontrol_dtrans = Dcontrol::where("endt_renewal_no",$latest_rec->endt_renewal_no)->get(['dtrans_no'])[0];

      $drn_trans = Dcontrol::whereRaw("policy_no='".$policy_no."' and to_number(dtrans_no)>".(int)$dcontrol_dtrans->dtrans_no)
                        ->whereIn('trans_type',['EXT','PTA','CXT','INS','MAC','STK'])
                        ->get();
      // check whether the EXT were refunded
      $count_trans = 0;
      foreach($drn_trans as $ext){
        $rfns = Dcontrol::where("cnc_rfn_endt",$ext->endt_renewal_no)
                    ->where('trans_type','RFN')
                    ->where(function($query){
                      $query->where('cancelled','<>','Y')->orWhereNull('cancelled');
                    })
                    ->get();

        $rfn_sum = Debitmast::whereIn("endt_renewal_no",$rfns->pluck('endt_renewal_no'))->sum('foreign_premium');
        $ext_rec = Debitmast::where("endt_renewal_no",$ext->endt_renewal_no)->first();

        // if premium was refunded through premium refund option skip
        $has_prem_refund = $rfns->contains(function($item,$key){
          return $item->ext_to_rfn != 'Y';
        });
        $skip_ext = (isset($rfns) and $has_prem_refund) ? 'Y' : 'N';

        if($skip_ext == 'N' and (abs($rfn_sum) <  abs($ext_rec->foreign_premium))){
          // Check for EXT with more than one RFN and sum up amounts
          $rfn_total = 0;
          foreach($rfns as $rfn){
              $rfn_amount = Debitmast::where("endt_renewal_no",$rfn->endt_renewal_no)->pluck('foreign_premium');
              $rfn_total += $rfn_amount;
          }

          if(isset($ext_rec)){
              $count_trans++;
          }
        }
 
      }

      return response()->json(['count' =>$count_trans], 200);
    }

    // Fetch EXTs that happened after POL,RNS or REN
    public function fetch_ext(Request $request)
    {
      $policy_no = $request->policy_no;
      $drns = array();

      $latest_rec = $this->latest_baseTransaction($policy_no);
                    
      $dcontrol_dtrans = Dcontrol::where("endt_renewal_no",$latest_rec->endt_renewal_no)->get(['dtrans_no','class'])[0];

      $cls = ClassModel::where('class', $dcontrol_dtrans->class)->first();

      $drn_trans = Dcontrol::whereRaw("policy_no='".$policy_no."' and to_number(dtrans_no)>".(int)$dcontrol_dtrans->dtrans_no)
                        ->whereIn('trans_type',['EXT','PTA','CXT','INS','MAC','STK'])
                        ->where(function($query){
                            $query->where('cancelled','<>','Y')->orWhereNull('cancelled');
                          })
                        ->orderBy('dtrans_no','DESC')
                        ->get();
      $zeroGrossDrns = ['STK'];
      $zeroGrossDrnsColumn = [
        'STK' => 'foreign_sticker_amount'
      ];
      $grossColumn = 'foreign_premium';
      
      foreach ($drn_trans as $ext) {
        if(in_array($ext->trans_type,$zeroGrossDrns))
        {
          $grossColumn = $zeroGrossDrnsColumn[$ext->trans_type];
        }

        $rfns = Dcontrol::where("cnc_rfn_endt",$ext->endt_renewal_no)
                    ->where('trans_type','RFN')
                    ->where(function($query){
                      $query->where('cancelled','<>','Y')->orWhereNull('cancelled');
                    })
                    ->get();
        
        $rfn_sum = Debitmast::whereIn("endt_renewal_no",$rfns->pluck('endt_renewal_no'))->sum($grossColumn);
        $drn_rec = Debitmast::where("endt_renewal_no",$ext->endt_renewal_no)->first();

        // if premium was refunded through premium refund option skip
        $has_prem_refund = $rfns->contains(function($item,$key){
          return $item->ext_to_rfn != 'Y';
        });
        $skip_ext = (isset($rfns) and $has_prem_refund) ? 'Y' : 'N';

        if($skip_ext == 'N' and (abs($rfn_sum) <  abs($drn_rec->{$grossColumn}))){
          // Check for EXT with more than one RFN and sum up amounts
          $rfn_total = 0;
          foreach($rfns as $rfn){
              $rfn_rec = Debitmast::where("endt_renewal_no",$rfn->endt_renewal_no)
                              ->first();
              $rfn_amount = $rfn_rec->{$grossColumn};
              $rfn_total += $rfn_amount;
          }

          $drn_rec = Debitmast::where("endt_renewal_no",$ext->endt_renewal_no)->first();

          if(isset($drn_rec)){
            $ext_amount = $drn_rec->{$grossColumn} + $rfn_total;

            array_push($drns,[
                'endt_renewal_no' => $drn_rec->endt_renewal_no,
                'amount' => $ext_amount,
                'trans_type' => $drn_rec->entry_type_descr,
                'period_from'=>$drn_rec->period_from,
                'period_to'=>$drn_rec->period_to,
                'effective_date'=>$drn_rec->effective_date,
                'open_cover'=>$cls->open_cover
              ]);
          }
        }
      }

      return response()->json(['exts' =>$drns,'count' => count($drns)], 200);
    }

    public function get_ext_amt($request){

        if(!$request->filled('ext_endt')){
          $resp = [
            'status' => 0,
          ];

          return $resp;
        }

        $dcon = Dcontrol::where('endt_renewal_no',$request->ext_endt)->first();
        $req = new Request([
            'policy_no' => $dcon->policy_no
        ]);
        
        $resp = $this->fetch_ext($req);
        $resp = $resp->getOriginalContent();
        $unrefunded_exts = $resp['exts'];
        
        foreach($unrefunded_exts as $ext){

          if($request->ext_endt == $ext['endt_renewal_no']){
            $rfn_dcon = Dcontrol::where('endt_renewal_no',$request->ext_endt)->first();
            $debitmast = $ext['amount'];
            break;
          }
        }
        $resp = [
          'status' => 1,
          'rfn_dcon' => $rfn_dcon,
          'debitmast' => $debitmast
        ];

      return $resp;
    }
}



