<?php
namespace App\Http\Controllers\gb\underwriting;

use Auth;
use App\Agmnf;
use App\Client;

// models
use App\Pipcnam;
use App\Polsect;
use App\Currency;
use App\Dcontrol;
use App\Debitmast;
use App\SendEmail;
use App\Vat_setup;
use App\Models\Efris_data;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Controllers\pdfController;
use App\ValueObjects\IntermediaryQueryParams;
use App\Services\IntermediaryQueryService;

class EnfrisController extends Controller
{
    private $deviceNo = '';
    private $invoice_id = '';
    private $api_endpoint = "";
    private $sellertin="";
    //$this->initialize();
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
     public function __construct() {
        $pipcnam = Pipcnam::whereRaw("trim(record_type) = '0'")->first();
        $this->api_endpoint=$pipcnam->efris_endpoint;  
        $this->deviceNo=$pipcnam->device_no; 
        $this->sellertin=$pipcnam->sellertin;      
        }
        
    public function index()
    {
        return view('gb.underwriting.efrisdashboard',compact('policies','agents','branches','clients'));

    }
 
    public function pendingIntergration(Request $request){
          $endt_renewal_no =trim( $request->endt_renewal_no);
         
        if (isset($endt_renewal_no) && $endt_renewal_no !== null && !empty($endt_renewal_no)) {
            //  dd($endt_renewal_no);
            $policies = Debitmast::where('endt_renewal_no',$endt_renewal_no)
             ->select('insured','policy_no','endt_renewal_no','agent_no','period_from','period_to','efris_invoiceno','efris_invoiceid','gross_amount','vat_amount','entry_type_descr','doc_type','efris_intergrated','efris_cancelled')
              ->get();

        }else{

            $policies = Debitmast::where('efris_intergrated','<>','Y')
            ->where('efris_cancelled','<>','Y')
             ->select('insured','policy_no','endt_renewal_no','agent_no','period_from','period_to','efris_invoiceno','efris_invoiceid','gross_amount','vat_amount','entry_type_descr','doc_type','efris_intergrated','efris_cancelled')
                    //->where('efris_cancelled','<>','Y')
              ->get();
            //   dd($policies,1);

        }
 
        return Datatables::of($policies)
        ->addColumn('agent_name', function($row){
           // $agent=  Agmnf::where('agent',$row->agent_no)->first()->name;

            $intermediaryParams = new IntermediaryQueryParams([
                'agentNo' => $row->agent_no,
            ]);
        
            
            $agent = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first()->name;

            return $agent;

        })
        
        ->addColumn('action', function ($row) {
            if($row->efris_intergrated == "Y"){
                return '<a class="btn btn-xs bg-info " id="" disabled><i class="fa-solid fa-arrow-up-from-bracket" aria-hidden="true"></i>Integrated</a>';


            }elseif($row->efris_cancelled == "Y"){
                return '<a class="btn btn-xs bg-info" id="" disabled><i class="fa-solid fa-arrow-up-from-bracket" aria-hidden="true"></i>Cancelled</a>';
            }else{
                return '<a class="btn btn-xs bg-info intergrate_efris" id="intergrate_efris"><i class="fa-solid fa-arrow-up-from-bracket" aria-hidden="true"></i>Intergrate</a>&nbsp;<a class="btn btn-xs bg-info edit_premium" id="edit_premium"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Cancel</a>';


            }
        
            
            })
        ->rawColumns(['action'])
            ->make(true);  

    }
    public function intergrated(Request $request){
       
      
        $policies = Debitmast::where('efris_intergrated','Y')
                             ->select('insured','policy_no','endt_renewal_no','agent_no','period_from','period_to','gross_amount','vat_amount','entry_type_descr','doc_type','efris_invoiceno','efris_invoiceid')
                                    //->where('efris_cancelled','<>','Y')
                              ->get();
                                    //  dd($policies);
 
        return Datatables::of($policies)
        ->addColumn('agent_name', function($row){
          //  $agent=  Agmnf::where('agent',$row->agent_no)->first()->name;
            $intermediaryParams = new IntermediaryQueryParams([
                'agentNo' => $row->agent_no,
            ]);
        
            
            $agent = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first()->name;
            
            return $agent;

        })
        
        ->addColumn('action', function ($row) {
                return '<a class="btn btn-xs bg-info" id=""><i class="fa fa-pencil-square-o" aria-hidden="true"></i> intergrated</a>';
            
            })
        ->rawColumns(['action'])
            ->make(true);  

    }
    public function getefriscancelled(Request $request){
       
      
        $policies = Debitmast::where('efris_cancelled','Y')
                             ->select('insured','policy_no','endt_renewal_no','agent_no','period_from','period_to','gross_amount','vat_amount','entry_type_descr','doc_type','efris_cancelled_by')
                                    //->where('efris_cancelled','<>','Y')
                              ->get();
                                    //  dd($policies);
 
        return Datatables::of($policies)
        ->addColumn('agent_name', function($row){
           // $agent=  Agmnf::where('agent',$row->agent_no)->first()->name;

            $intermediaryParams = new IntermediaryQueryParams([
                'agentNo' => $row->agent_no,
            ]);
        
            
            $agent = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first()->name;
            return $agent;

        })
        
        ->addColumn('action', function ($row) {
                return '<a class="btn btn-xs bg-info" id="intergrate_efris"><i class="fa-solid fa-arrow-up-from-bracket" aria-hidden="true"></i>Intergrate</a>&nbsp;<a class="btn btn-xs bg-info edit_premium" id="edit_premium"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Cancel</a>';
            
            })
        ->rawColumns(['action'])
            ->make(true);  

    }
    public function CancelIntergration(Request $request){
        // dd($request->all());
        $cnc = Debitmast::where('endt_renewal_no',trim($request->endt_renewal_no))
                            ->update([
                                'efris_cancelled'=>'Y',
                                'efris_cancelled_by'=> Auth::user()->user_name,
                                'efris_cnc_reason'=>trim($request->cncreasons)
                            ]);
        return [
            'status'=>1,
            'msg'=>"success"
        ];
    }
    public function checkExemptDeemed($tin_no,$class){
       
        try{
              $iterfacecode="T137";
              $tin_No=trim($tin_no);
              $class=trim($class);
              //confirm tin validity
              $tin_data=$this->validateTin($tin_No);
         
              if((int)$tin_data['status']== 0)
              {
                return $tin_data;
              }
              
              //get class from efris_data
              $efris_param = $this->getEfriscode($class);
              $commodityCategoryCode=trim($efris_param->category_code);
              $efris_data=[
                            "tin" =>$tin_No,
                            "commodityCategoryCode"=>$commodityCategoryCode
                        ];
             $d_data = json_encode($efris_data);
            
             $validate_data = base64_encode($d_data); 
             $returned_data = $this->efrisRequest($validate_data,$iterfacecode);
             $returnCode= $returned_data->returnStateInfo->returnCode;
             $returnMessage= $returned_data->returnStateInfo->returnMessage;
                if($returnCode == 00){
                    $dataDescription =$returned_data ->data->dataDescription;
                    $decodedcontent = base64_decode($returned_data->data->content);
                    $decodedcontent = json_decode($decodedcontent);
                    return [
                        'status'=>1,
                        'msg'=>$decodedcontent

                    ];

                    

                }else{

                    return [
                        'status'=>0,
                        'msg'=>$returnMessage
                    ];

                }
                     
         
        }catch(Throwable $th){
             DB::rollback();
            throw $th;
         
        }
    }
    
    public function validateTin(Request $request){//returning tin details
           
        try{
                
         
                $iterfacecode="T119";
                $efris_data  = [
                "tin" => trim($request->pin),
                "ninBrn"=>trim($request->id)
                ];
                $tin_data = base64_encode(json_encode($efris_data)); 
                $returned_data = $this->efrisRequest($tin_data,$iterfacecode);
                $returnCode= $returned_data->returnStateInfo->returnCode;
                $returnMessage= $returned_data->returnStateInfo->returnMessage;
                if($returnCode == 00){
                    $dataDescription =$returned_data ->data->dataDescription;
                    $decodedcontent = base64_decode($returned_data->data->content);
                    $decodedcontent = json_decode($decodedcontent)->taxpayer;
                    return [
                        'status'=>1,
                        'msg'=>$decodedcontent

                    ];

                    

                }else{

                    return [
                        'status'=>0,
                        'msg'=>$returnMessage
                    ];

                }
         
        }catch(Throwable $th){
            //dd($th);
             DB::rollback();
            throw $th;
         
        }
    }
    public function getDebitnotes(Request $request){
        $doc_type="DRN";
        $dcontrol_cnc = Dcontrol::whereRaw("trim(endt_renewal_no) = '" . trim($request->endorsement_no) . "'")->first();
        $dcontrol = Dcontrol::whereRaw('trim(policy_no) = ?', [$request->policy_no])
                            // ->whereRaw('trim(policy_no) = ? and cov_period_from=?', [$request->policy_no, $dcontrol_cnc->cov_period_from])
                             ->pluck('endt_renewal_no');
        // dd($dcontrol);
        $fetch_debitmast = Debitmast::whereRaw("trim(policy_no) = '" . trim($request->policy_no) . "'")
        ->whereRaw("trim(doc_type) = '" . trim($doc_type) . "'")
        ->whereIn('endt_renewal_no', $dcontrol)
        ->where('efris_intergrated','Y')
        ->select('insured','policy_no','endt_renewal_no','agent_no','period_from','period_to','gross_amount','period_from','period_to','doc_type')
        ->get();

        return  $fetch_debitmast;

        
    }
    public function intergratecrn(Request $request){
        $debit_endorsement = trim($request->debits);
        $cnc_endorsement = trim($request->crn_endorsement);
        $get_dcontrol = Dcontrol::whereRaw("trim(endt_renewal_no) = '" . $cnc_endorsement . "'")->update([
            "cnc_rfn_endt"=>$debit_endorsement
        ]);
        
        $request = new Request();
        $request->merge(['endorsement_no' => $cnc_endorsement]);

       
        return $this->intergrate($request);
    


    }



    public function intergrate(Request $request){
      try {
        DB::beginTransaction();
        $endt_renewal_no = trim($request->endorsement_no);
        $get_dcontrol = Dcontrol::whereRaw("trim(endt_renewal_no) = '" . $endt_renewal_no . "'")->first();
        if($get_dcontrol->doc_type =="DRN"){
            $rec_count = Debitmast::whereRaw("trim(endt_renewal_no) = '" . trim($endt_renewal_no) . "'")->count();
            if($rec_count==0){
                        return [
                            'code' => -1,
                            'msg' => 'The Endorsement no does not exist in debitmast :'. trim($endt_renewal_no),
                            ];
            }
            $fetch_debitmast = Debitmast::whereRaw("trim(endt_renewal_no) = '" . trim($endt_renewal_no) . "'")->first();

        }else{
            $rec_count=DB::table("efris_approval_records")->where("endt_renewal_no",$get_dcontrol->endt_renewal_no)->count();
            
            if($rec_count==0){
                return [
                    'code' => -1,
                    'msg' => 'The Endorsement no does not exist in efris_approval_records :'. trim($endt_renewal_no),
                ];
            }
           $cnc_rec=DB::table("efris_approval_records")->where("endt_renewal_no",$get_dcontrol->endt_renewal_no)->first();

        }
        

        
        $currency = Currency::whereRaw("trim(currency_code) = '".trim($get_dcontrol->currency) ."'")->first();
       

        $client_data_count = Client::whereRaw("trim(client_number) = '" . $get_dcontrol->client_number . "'")->count();

        if($client_data_count <1){
                $message='No record of Client with no:'.trim($get_dcontrol->client_number);
                return [
                'code'=>-1,
                'msg'=>$message
                ];
        }
         $client_data = Client::whereRaw("trim(client_number) = '" . $get_dcontrol->client_number . "'")->first();

        $pipcnam = Pipcnam::whereRaw("trim(record_type) = '0'")->first();

           // check fulcultative branch
        $efris_param = Efris_data::where('class', $get_dcontrol->class)->first();
               //facultative
        if (trim($get_dcontrol->branch) == trim($pipcnam->fucin_branch)) {
    
            $efris_param = Efris_data::whereRaw("trim(class) = '" . $pipcnam->efris_fucin_class . "'")->first();
            
        }
        

           
           $origin_debit = Debitmast::whereRaw("trim(endt_renewal_no) = '" . $get_dcontrol->cnc_rfn_endt . "'")->first();
           $origin_debit1 = Debitmast::whereRaw("trim(endt_renewal_no) = '" . $get_dcontrol->cnc_rfn_endt . "'")->count();
           $item = trim($efris_param->item_name);
           if($origin_debit1 > 0){
               if (strlen($origin_debit->efris_invoiceid) < 1 && strlen($origin_debit->efris_invoiceno) > 0) {
                   $this->getInvoiceDetails($origin_debit->efris_invoiceno,$get_dcontrol->endt_renewal_no);
               }
               
           }
         
          $vatsetup =  Vat_setup::where('vat_code',$get_dcontrol->vat_code)->first();
          $taxCategory =$vatsetup->vat_type; 
          $taxRateName =$vatsetup->vat_type;
          $taxCategoryCode=$vatsetup->tax_category_code;
          $deemed = '2';
        if( trim($get_dcontrol->vat_code) == 4){
            $deemed = '1';
            $item = $item." (Deemed)";
        }
        if(strlen($client_data->buyer_type) > 0){
            $buyerType = trim($client_data->buyer_type);
        }
        if ($buyerType=='B2B'  || $buyerType=='B2G'){
            $buyerTypeCode=0;
        }else if ($buyerType=='B2C'){
            $buyerTypeCode=1;
        }else if($buyerType=='B2Foreigner'){
            $buyerTypeCode=2;

        }else{
            $buyerTypeCode=1;
        }
        if($get_dcontrol->doc_type =="DRN"){
            $gross_amount = $fetch_debitmast->foreign_premium + $fetch_debitmast->foreign_levy_amount + $fetch_debitmast->foreign_sticker_amount +$fetch_debitmast->foreign_vat_amount;
             $nett_amount =  $fetch_debitmast->foreign_premium + $fetch_debitmast->foreign_levy_amount + $fetch_debitmast->foreign_sticker_amount;
            $gross_amount=(string)$gross_amount;
            $nett_amount=(string)$nett_amount;
            $efris_intergrated_flag = $fetch_debitmast->efris_intergrated;

        }elseif($get_dcontrol->doc_type =="CRN"){
           
             $gross_amount = $cnc_rec->foreign_premium + $cnc_rec->foreign_levy_amount + $cnc_rec->foreign_sticker_amount +$cnc_rec->foreign_vat_amount;
             $nett_amount =  $cnc_rec->foreign_premium + $cnc_rec->foreign_levy_amount + $cnc_rec->foreign_sticker_amount;
             $gross_amount=(string)$gross_amount;
             $nett_amount=(string)$nett_amount;
             $efris_intergrated_flag = $cnc_rec->efris_intergrated;


        }
        
       
        if ($rec_count > 0) {

            if ($efris_intergrated_flag != 'Y' && $client_data != null) {
                if ($get_dcontrol->doc_type =="DRN") {
                    $iterfacecode="T109";
                   $efris_data = [
                       "sellerDetails" => [
                           "tin" => $this->sellertin,
                           "ninBrn" => "/120957",
                           "legalName" => trim($pipcnam->company_name),
                           "businessName" =>  trim($pipcnam->company_name),
                           "address" => trim($pipcnam->addr2) .''. trim($pipcnam->addr3) .''. trim($pipcnam->addr4),
                           "mobilePhone" =>trim($pipcnam->telephone),
                           "linePhone" => trim($pipcnam->telephone),
                           "emailAddress" => trim($pipcnam->email),
                           "placeOfBusiness" => "",
                           "referenceNo" => trim($fetch_debitmast->endt_renewal_no), 
                       ],
                       "basicInformation" => [
                           "invoiceNo" => "", 
                           "antifakeCode" => "",
                           "deviceNo" => $this->deviceNo,
                           "issuedDate" => $fetch_debitmast->dola,
                           "operator" => trim($fetch_debitmast->user_str),
                           "currency" => $currency->currency, 
                           "oriInvoiceId" => "",
                           "invoiceType" => "1",
                           "invoiceKind" => "1",
                           "dataSource" => "106",
                           "invoiceIndustryCode" => "106",
                           "isBatch" => "0",
                       ],
                       "buyerDetails" => [
                           "buyerTin" =>  trim($client_data->pin_number), 
                           "buyerNinBrn" => trim($client_data->id_number),
                           "buyerPassportNum" => "",
                           "buyerLegalName" => trim($fetch_debitmast->insured),
                           "buyerBusinessName" => trim($fetch_debitmast->insured),
                           "buyerAddress" => trim($client_data->mobile_no),
                           "buyerEmail" => trim($client_data->e_mail),
                           "buyerMobilePhone" => trim($client_data->telephone),
                           "buyerLinePhone" => trim($client_data->telephone),
                           "buyerPlaceOfBusi" => trim($client_data->adress1),
                           "buyerType" =>0,
                           "buyerCitizenship" => "",
                           "buyerSector" => "",
                           "buyerReferenceNo" => "",
                       ],
                       "goodsDetails" => [
                           [
                               "item" => $item,
                               "itemCode" => trim($efris_param->item_code),
                               "qty" => "1",
                               "unitOfMeasure" => "116",
                               "unitPrice" => $gross_amount,
                               "total" =>$gross_amount,
                               "taxRate" => $fetch_debitmast->vat_rate/100,
                               "tax" =>$fetch_debitmast->foreign_vat_amount,
                               "discountTotal" => "",
                               "discountTaxRate" => "",
                               "orderNumber" => "0",
                               "discountFlag" => "2",
                               "deemedFlag" => $deemed,
                               "exciseFlag" => "2",
                               "categoryId" => trim($efris_param->category_code),
                               "categoryName" => trim($efris_param->category_name), 
                               "goodsCategoryId" => trim($efris_param->category_code),
                               "goodsCategoryName" => trim($efris_param->category_name),
                               "exciseRate" => "",
                               "exciseRule" => "",
                               "exciseTax" => "",
                               "pack" => "",
                               "stick" => "",
                               "exciseUnit" => "",
                               "exciseCurrency" => "",
                               "exciseRateName" => "",
                           ],
                       ],
                       "taxDetails" => [
                           [
                               "taxCategory" => $taxCategory,
                               "taxCategoryCode"=> $taxCategoryCode,
                               "netAmount" =>  $nett_amount, //"9840",
                               "taxRate" => $fetch_debitmast->vat_rate/100, //"0.18",
                               "taxAmount" => $fetch_debitmast->foreign_vat_amount,
                               "grossAmount" => $get_dcontrol->vat_code == 4 ? "".$nett_amount: $gross_amount, //"12000.00",
                               "exciseUnit" => "",
                               "exciseCurrency" => $currency->currency,
                               "taxRateName" =>trim($taxRateName),
                           ],
                           [
                               "taxCategory"=> "Stamp Duty",
                               "taxCategoryCode"=>"07",
                               "netAmount"=> "0",
                               "taxRate"=> "1",
                               "taxAmount"=> $fetch_debitmast->foreign_stamp_duty,
                               "grossAmount"=> $fetch_debitmast->foreign_stamp_duty,
                               "taxRateName"=> "Stamp Duty"
                           ],
                      
                           
                       ],
                       "summary" => [
                           "netAmount" =>  $nett_amount, //"9840",
                           "taxAmount" =>  $get_dcontrol->vat_code == 4?$fetch_debitmast->foreign_stamp_duty:$fetch_debitmast->foreign_vat_amount + $fetch_debitmast->foreign_stamp_duty, //"2160",
                           "grossAmount" =>$get_dcontrol->vat_code == 4?$nett_amount + $fetch_debitmast->foreign_stamp_duty:$gross_amount+ $fetch_debitmast->foreign_stamp_duty, // "12000",
                           "itemCount" => "1",
                           "modeCode" => "0",
                           "remarks" => trim($get_dcontrol->remark),
                           "qrCode" => "",
                       ],
                       "payWay" => [
                           [
                               "paymentMode" => "102",
                               "paymentAmount" =>  $gross_amount + $fetch_debitmast->foreign_stamp_duty, //"12000.00",
                               "orderNumber" => "0",
                           ],
                       ],
                       "extend" => [
                           "reason" => "",
                           "reasonCode" => "",
                       ],
                   ];
                  //   dd($efris_data);

               } else if ($get_dcontrol->doc_type =="CRN") {
                   $iterfacecode="T110";
                   $reason = 'Cancellation';
                  

                   if($origin_debit1 > 0){

                       if(strlen( trim($origin_debit->efris_invoiceid)) == 0){
                           $w_invoice_id = $this->invoice_id;
                       }else{
                           $w_invoice_id = $origin_debit->efris_invoiceid;
                       }

                        $efris_data = [
                            "oriInvoiceId" => trim($w_invoice_id),
                            "oriInvoiceNo" => trim($origin_debit->efris_invoiceno),
                            "reasonCode" => "102",
                            "reason" => $reason,
                            "applicationTime" => Carbon::now()->format('yy-m-d H:m:s'),
                            "invoiceApplyCategoryCode" => "101",
                            "currency" => trim($currency->currency),
                            "contactName" => trim($get_dcontrol->insured),
                            "contactMobileNum" => trim($client_data->mobile_no),
                            "contactEmail" => trim($client_data->e_mail),
                            "source" => "106",//offline enabler
                            "remarks" => $reason,
                            "sellersReferenceNo" => trim($get_dcontrol->endt_renewal_no),
                            "basicInformation"=> [
                                "deviceNo"=>  $this->deviceNo,
                                "issuedDate"=> $get_dcontrol->dola,
                                "operator"=> trim(Auth::user()->user_name),
                                "currency"=> $currency->currency,
                                "invoiceType"=> "1",
                                "invoiceKind"=> "1",
                                "dataSource"=> "106",
                                "invoiceIndustryCode"=> "106",
                                "isBatch"=> "0"
                                ],
                                "buyerDetails"=> [
                                "buyerTin"=>trim($client_data->pin_number),
                                "buyerType"=> trim($buyerTypeCode)
                                ],
                            "goodsDetails" => [
                                [
                                    "item" =>$item,
                                    "itemCode" => trim($efris_param->item_code),
                                    "qty" => "-1",
                                    "unitOfMeasure" => "116",
                                    "unitPrice" => (string)abs($gross_amount),
                                    "total" => (string)$gross_amount,
                                    "taxRate" =>$cnc_rec->vat_rate/100,
                                    "tax" => $cnc_rec->foreign_vat_amount,
                                    "discountTotal" => "",
                                    "discountTaxRate" => "",
                                    "orderNumber" => '0',
                                    "discountFlag" => "2",
                                    "deemedFlag" => $deemed,
                                    "exciseFlag" => "2",
                                    "categoryId" => trim($efris_param->category_code),
                                    "categoryName" => trim($efris_param->category_name), 
                                    "goodsCategoryId" => trim($efris_param->category_code),
                                    "goodsCategoryName" => trim($efris_param->category_name), 
                                    "exciseRate" => "",
                                    "exciseRule" => "",
                                    "exciseTax" => "",
                                    "pack" => "",
                                    "stick" => "",
                                    "exciseUnit" => "",
                                    "exciseCurrency" => "",
                                    "exciseRateName" => "",
                                ],
                            ],
                           
                            
                            "taxDetails" => [
                                [
                                    "taxCategory" => trim($taxCategory),
                                    "taxCategoryCode"=> $taxCategoryCode,
                                    "netAmount" => (string)$nett_amount,
                                    "taxRate" => (string)$cnc_rec->vat_rate/100,
                                    "taxAmount" =>(string) $cnc_rec->foreign_vat_amount,
                                    "grossAmount" =>(string) $get_dcontrol->vat_code == 4 ? "".$nett_amount: $gross_amount,
                                    "exciseUnit" => "101",
                                    "exciseCurrency" => $currency->currency,
                                    "taxRateName" => trim($taxRateName),
                                ],
                                [
                                    "taxCategory"=> "Stamp Duty",
                                    "taxCategoryCode"=> '07',
                                    "netAmount"=> "0",
                                    "taxRate"=> "1",
                                    "taxAmount"=> $cnc_rec->foreign_stamp_duty,
                                    "grossAmount"=> $cnc_rec->foreign_stamp_duty,
                                    "taxRateName"=> "Stamp Duty"
                                ],
                            ],
                            "summary" => [
                                "netAmount" => (string) $nett_amount, //"9840",
                                "taxAmount" =>(string)  $get_dcontrol->vat_code == 4?$cnc_rec->foreign_stamp_duty:$cnc_rec->foreign_vat_amount + $cnc_rec->foreign_stamp_duty, //"2160",
                                "grossAmount" =>(string)$get_dcontrol->vat_code == 4?$nett_amount + $cnc_rec->foreign_stamp_duty:$gross_amount+ $cnc_rec->foreign_stamp_duty, // "12000",
                                "itemCount" => "1",
                                "modeCode" => "0",
                                "remarks" => trim($get_dcontrol->remark),
                                "qrCode" => "",
                           ],
                
                            "payWay" => [
                                [
                                    "paymentMode" => "103",
                                    "paymentAmount" => (string) $gross_amount + $cnc_rec->foreign_stamp_duty,
                                    "orderNumber" => "a",
                                ],
                            ],
                        ];
                        // dd($efris_data);
                   }else{
                    
                     

                    return [
                        'code' => -1,
                        'msg' => 'Original Debit Note Not found '
                    ];
                     
                   }
                 
            
    
                   
               } else {
                   return [
                       'code' => -1,
                       'msg' => 'The debit found is neither credit or debit ...',
                   ];
               }
            
             
               $d_data = base64_encode(json_encode($efris_data)); 
              
               $returned_data = $this->efrisRequest($d_data,$iterfacecode);
               $returnCode= $returned_data->returnStateInfo->returnCode;
                $returnMessage= $returned_data->returnStateInfo->returnMessage;
               if($returnCode == 00){
                    $dataDescription =$returned_data ->data->dataDescription;
                    $decodedcontent = base64_decode($returned_data->data->content);
                    $decodedcontent = json_decode($decodedcontent);
                    if ($get_dcontrol->doc_type =="DRN") {
                        Debitmast::whereRaw("trim(endt_renewal_no) = '" . $fetch_debitmast->endt_renewal_no . "'")
                                ->update([
                                'efris_intergrated' => 'Y',
                                'efris_antifakeCode' => $decodedcontent->basicInformation->antifakeCode,
                                'efris_qrCode' => $decodedcontent->summary->qrCode,
                                'efris_invoiceno' => $decodedcontent->basicInformation->invoiceNo,
                                'efris_invoiceId' => $decodedcontent->basicInformation->invoiceId,
                                'efris_time' => date("Y-m-d H:i:s"),

                            ]);
                            #MAIL PARAMS
                            $category="EFRIS INVOICE";
                            $message="Policy  '$fetch_debitmast->policy_no' and Endorsement number '$fetch_debitmast->endt_renewal_no' Has been Debited and Intergrated to URA.
                            <br> Thank You. ";
                            $name = trim(Auth::user()->user_name);
                            $base64 =  (new pdfController())->debitNoteDocument($request, encrypt($fetch_debitmast->endt_renewal_no),"Y");
                            #send Email to Client
                            $email=trim($client_data->e_mail);
                            $this->send_email($category,$email,$message,$name,$base64);
                    
                            #send Email to Agent
                            // $email=  Agmnf::where('agent',$fetch_debitmast->agent_no)
                            // ->where('branch',$fetch_debitmast->branch)
                            // ->first()->email;
                    
                            $intermediaryParams = new IntermediaryQueryParams([
                                'agentNo' => $fetch_debitmast->agent_no,
                            ]);
                        
                            $email = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first()->email;
                            
                            $this->send_email($category,$email,$message,$name,$base64);


                    } else {
                        DB::table("efris_approval_records")->where("endt_renewal_no",$get_dcontrol->endt_renewal_no)
                                ->update([
                                    'efris_reference_no' =>$decodedcontent->referenceNo,
                                    'efris_intergrated' => 'Y',
                                    'updated_at' => Carbon::now(),
                                ]);
                                // dd($decodedcontent);
                              
                    }
                    $errorlog = DB::table('efris_error_log')->insert([
                        'request'=>json_encode($efris_data),
                        'endt_renewal_no'=>$get_dcontrol->endt_renewal_no,
                        'error_code'=>$returnCode,
                        'error_message'=>$returnMessage,
                        'log_time'=>date("Y-m-d H:i:s"),
                        'user_name'=>Auth::user()->user_name

                    ]);
                    DB::commit();
                    
                    return [
                        'status'=>1,
                        'msg'=>'Efris Integration Successful'

                    ];    

               }else{
             
                

                    $errorlog = DB::table('efris_error_log')->insert([
                        'request'=>json_encode($efris_data),
                        'endt_renewal_no'=>$fetch_debitmast->endt_renewal_no,
                        'error_code'=>$returnCode,
                        'error_message'=>$returnMessage,
                        'log_time'=>date("Y-m-d H:i:s"),
                        'user_name'=>Auth::user()->user_name

                    ]);
                    DB::commit();

                    return [
                        'status'=>0,
                        'msg'=>$returnMessage
                    ];

               }
           
               

            }else{
                return [
                    'code' => -1,
                    'msg' => 'Record already intergrated',
                ];

            }

        }else{
            return [
                'code' => -1,
                'msg' => 'No record found that is debit with the arguments given',
            ];
        }




      } catch (\Throwable $th) {
        //throw $th;
        DB::rollback();
        throw $th;

      }
    }
    public function efrisRequest($data,$iterfacecode){
        
        
        try{
                $efris_req =  
            [
                "data"=>[
                    "content"=> $data,
                    "dataDescription"=> [
                        "codeType"=> "0",
                        "encryptCode"=> "2",
                        "zipCode"=> "0"
                    ],
                    "signature"=> ""
                ],
                "globalInfo"=> [
                    "appId"=> "AP02",
                    "brn"=> "",
                    "dataExchangeId"=> "9230489223014123",
                    "deviceMAC"=> "FFFFFFFFFFFF",
                    "deviceNo"=> $this->deviceNo,
                    "extendField"=> [
                        "responseDateFormat"=> "dd/MM/yyyy",
                        "responseTimeFormat"=> "dd/MM/yyyy HH:mm:ss"
                    ],
                    "interfaceCode"=>$iterfacecode,
                    "latitude"=> "39.916527",
                    "longitude"=> "116.397128",
                    "requestCode"=> "TP",
                    "requestTime"=> date("Y-m-d H:i:s"),
                    "responseCode"=> "TA",
                    "taxpayerID"=> "1",
                    "tin"=> $this->sellertin,
                    "userName"=> "admin",
                    "version"=> "1.1.20191201"
                ],
                "returnStateInfo"=> [
                    "returnCode"=> "1176",
                    "returnMessage"=> "goodsDetails-->item:Byte length cannot be greater than 100!Collection index=>0"
                ]
            ];
            $client = new \GuzzleHttp\Client();
                $response = $client->request('POST', $this->api_endpoint, [
                    'headers' => ['Content-Type' => 'application/json'],
                    'body' => json_encode($efris_req),
                ]);
            $returned_data = json_decode($response->getBody()->getContents());
            return $returned_data;

            

        }catch(Throwable $th){
             DB::rollback();
            throw $th;
        }
    }
    public function getEfriscode($class){
        $efris_param = Efris_data::whereRaw("trim(class) = '" . $class. "'")->first();
        return $efris_param;    

    }
    public function getInvoiceDetails($invoiceNo,$endt_renewal_no){
        try{
             //1.Get the creditnote 
         $iterfacecode="T108";
            $efris_data  = [
            "invoiceNo" => trim($invoiceNo)
            ];
            $tdata = base64_encode(json_encode($efris_data)); 
            $returned_data = $this->efrisRequest($tdata,$iterfacecode);   
            $returnCode= $returned_data->returnStateInfo->returnCode;
            $returnMessage= $returned_data->returnStateInfo->returnMessage;
           
                if($returnCode == 00){
                    $dataDescription =$returned_data ->data->dataDescription;
                    $decodedcontent = base64_decode($returned_data->data->content);
                    $decodedcontent = json_decode($decodedcontent);
                    if (!isset($decodedcontent->invoiceId)) {
                        $new_invoice = "";
                        $this->invoice_id = "";
                   }else{
                       $new_invoice =  $decodedcontent->invoiceId;
                       $this->invoice_id = $decodedcontent->invoiceId;
                   }
   
                       Debitmast::whereRaw("trim(endt_renewal_no) = '" .trim($endt_renewal_no). "'")
                       ->update([
                         'efris_intergrated' => 'Y',
                         'efris_antifakeCode' => $decodedcontent->basicInformation->antifakeCode,
                         'efris_qrCode' => $decodedcontent->summary->qrCode,
                         'efris_invoiceno' => $decodedcontent->basicInformation->invoiceNo,
                         'efris_invoiceId' => $decodedcontent->basicInformation->invoiceId,
                         'efris_time' => date("Y-m-d H:i:s"),
   
                       ]);

                    

                }else{

                    return [
                        'status'=>0,
                        'msg'=>$returnMessage
                    ];

                }
                     
         
        }catch(Throwable $th){
             DB::rollback();
            throw $th;
         
        }
    }
    public function checkApproval($referenceNo){
           try{ 
            $iterfacecode="T111";
               $fetch_approval =  DB::select("select * from efris_approval_records where trim(efris_reference_no)= '" . trim($referenceNo) . "'");
               $count_approval = count($fetch_approval);
                    if($count_approval<1){
                       $message='No record in approval table with efris_reference_no:'.trim($referenceNo);
                        //    return [
                        //     'status'=>-1,
                        //     'msg'=>$message
                        //    ];
                    }
               $fetch_approval = $fetch_approval[0];
               
                $efris_data  =
                 [
                           
                    "referenceNo"=>trim($referenceNo), 
                    "oriInvoiceNo" =>"",
                    "invoiceNo" => "",
                    "combineKeywords"=>"",
                    "approveStatus"=> "", 
                    "queryType"=> "1", 
                    "invoiceApplyCategoryCode"=> "101", 
                    "startDate"=>"",
                    "endDate"=>"",
                    "pageNo"=> "1", 
                    "pageSize"=>"10" 
                 ];

                $credit_data = base64_encode(json_encode($efris_data));
                $returned_data = $this->efrisRequest($credit_data,$iterfacecode);
                $returnCode= $returned_data->returnStateInfo->returnCode;
                $returnMessage= $returned_data->returnStateInfo->returnMessage;
                if($returnCode == 00){
                    $dataDescription =$returned_data ->data->dataDescription;
                    $decodedcontent = base64_decode($returned_data->data->content);
                    $decodedcontent = json_decode($decodedcontent);
                    
                    $approved=$decodedcontent->records[0]->approveStatus;
                    $approvestr="Submitted";
                    if($approved==101){
                        $approvestr="Approved";
                    }else if($approved==102){
                        $approvestr="Submitted";
                    }else if($approved==103){
                        $approvestr="Rejected";
                    }else if($approved==104){
                        $approvestr="Voided";
                    }
                    DB::table("efris_approval_records")->whereRaw("trim(efris_reference_no) = '" .trim($referenceNo). "'")
                                        ->update([
                                                'approval_status' =>$approved,     
                                                ]);
                  
                  
                    

                }else{

                    // return [
                    //     'status'=>0,
                    //     'msg'=>$returnMessage
                    // ];
                    $errorlog = DB::table('efris_error_log')->insert([
                        'request'=>json_encode($efris_data),
                        'endt_renewal_no'=>$fetch_approval->endt_renewal_no,
                        'error_code'=>$returnCode,
                        'error_message'=>$returnMessage,
                        'log_time'=>date("Y-m-d H:i:s"),
                        'user_name'=>Auth::user()->user_name

                    ]);

                }
                
                      
               
                   
        }catch(\Throwable $th){
                DB::rollback();
                throw $th;
        }
    }
    public function send_email($category,$receiver,$message,$name,$base64){
        
        $maxId = SendEmail::max('id') +1;

        $sendemail = new SendEmail;

        $sendemail->category = $category ;
        $sendemail->receiver =$receiver;
        $sendemail->message =$message;
        $sendemail->creator = $name;
        $sendemail->id = $maxId;
        $sendemail->save();

        DB::table('mail_attachments')->insert([
        'code'=>$maxId,
        'document'=>$base64
        ]);
    }
    public function checkcrnstatus(Request $request){
        $rec = DB::table("efris_approval_records")->where('endt_renewal_no',$request->endt_renewal_no)->get();
        $countrec = count($rec);
        if($countrec < 1){
            return[
                'status'=>-1,
                'msg'=>"No Record Found Please Submit the Credit note To URA before proceeding"
            ];

        } 
        $rec=$rec[0];
        return[
            'status'=>1,
            'msg'=>$rec->approval_status
        ];
    }
    public function approvecnc($dcontrol){
        $cncapproval= DB::table("efris_approval_records")->where('endt_renewal_no',$dcontrol->endt_renewal_no)->first();
        $cnc = Debitmast::where('endt_renewal_no',trim($dcontrol->endt_renewal_no))
        ->update([
            'efris_intergrated' => 'Y',
            'efris_approvestatus'=>"APPROVED",
            'efris_refernce_no'=>trim($cncapproval->efris_reference_no)
        ]);
         #MAIL PARAMS
         $category="EFRIS INVOICE";
         $message="Policy  '$dcontrol->policy_no' and Endorsement number '$dcontrol->endt_renewal_no' Has been Debited and Intergrated to URA.
         <br> Thank You. ";
         $request = New Request();
         $name = trim(Auth::user()->user_name);
         $base64 =  (new pdfController())->debitNoteDocument($request, encrypt($dcontrol->endt_renewal_no),"Y");
         #send Email to Client
         $client_data = Client::whereRaw("trim(client_number) = '" . $dcontrol->client_number . "'")->first();
         $email=trim($client_data->e_mail);
         $this->send_email($category,$email,$message,$name,$base64);
 
         #send Email to Agent
        //  $email=  Agmnf::where('agent',$dcontrol->agent)
        //  ->where('branch',$dcontrol->branch)
        //  ->first()->email;

         $intermediaryParams = new IntermediaryQueryParams([
            'agentNo' =>$dcontrol->agent,
        ]);
    
        $email = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first()->email;
 

         $this->send_email($category,$email,$message,$name,$base64);


    }

}


?>