<?php

namespace App\Http\Controllers\gb\underwriting;

use App\Make;
use App\Clhmn;
use App\Modtl;

use Exception;
use Throwable;
use App\Dtran0;
use App\Polcmb;
use App\Vehcol;
use App\Bustype;

use App\Clparam;
use App\Country;

use App\Doctype;
use App\Folders;
use App\Pipcnam;
use App\Polsect;
use App\Ptamdtl;
use App\Bodytype;
use App\Dcontrol;
use App\Debitdtl;
use App\Salvages;
use App\Binderpol;
use App\Classtype;
use App\Covertype;
use App\Debitmast;
use App\Financier;
use App\Polmaster;
use App\Veh_Model;
use Carbon\Carbon;
use App\ClassModel;
use App\ComesaRates;
use App\Instalparam;
use App\RefundRates;
use App\Endorse_descr;
use App\Models\Usages;
use App\Models\Ageloads;
use App\Models\Modtlhist;
use App\Models\Modtlmast;
use App\Models\Modtlsumm;
use App\Models\Motcvrdet;
use App\Models\Motorsect;
use App\Regex_parameters;
use App\Sec_ext_reinsure;
use App\Vehiclemodelyear;
use App\Models\Modtlpivot;
use App\Discounts_loadings;
use App\Models\Motivepower;
use App\Models\Motorpolsec;
use App\Models\Motorstatus;
use App\Models\SiRateBands;
use App\Models\UwParameter;
use App\workflow\Documents;
use App\Models\FleetStaging;
use App\Rules\RegNumberRule;
use Illuminate\Http\Request;
use App\Models\Motorprem_grp;
use App\Models\Occupantparam;
use App\DiscountLoadingparams;
use App\Models\Motorpolsecsnap;
use App\Models\Motorpremmethod;
use App\Models\SmartCoverNotes;
use App\Models\Binder;
use App\Models\MotorMasterParam;
use App\Models\SmartTaxInvoices;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\MessageBag;
use App\Models\Motorprem_grp_categ;
use App\Http\Controllers\Controller;
use App\Models\Blacklisted_vehicles;
use Illuminate\Support\Facades\Auth;
use App\Models\Vehicleclassification;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use App\Http\Requests\NewMotorRequest;
use Illuminate\Support\Facades\Session;
use Yajra\DataTables\Facades\DataTables;
use App\Classes\Common\FileUploadManager;
use App\Models\IceCash;

class MotorProcessing extends Controller
{

    private $_total_sum_insured;
    private $_cls;
    private $_reg_no;
    private $_endt_renewal_no;
    private $_seat_cap;
    private $_insured_seats;
    private $_manf_year;
    private $_preview;
    private $_initial_rns;
    private $_extend_tor;
    public $current_year;
    // private $;

    public function setProps(
        string $endt_renewal_no,
        string $reg_no,
        string $cls,
        int $total_sum_insured = 0,
    )
    {
        $this->_cls = $cls;
        $this->_reg_no = $reg_no;
        $this->_endt_renewal_no = $endt_renewal_no;
        $this->_total_sum_insured = $total_sum_insured;
    }

    public function __construct(){
        $this->_initial_rns = false;
        $this->_extend_tor  = false;
        $dtran = Dtran0::all();
		$this->current_year = $dtran[0]->account_year;
    }

    public function set_initial_rns($rns_status){
        $this->_initial_rns = $rns_status;
    }

    public function set_extend_tor($extend_tor){
        $this->_extend_tor = $extend_tor;
    }

    public function index(Request $request)
    {
        $cls = $request->cls;
        $dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)
                ->first(['endt_renewal_no','class','insured','external_pol_no', 'binder_pol_no','currency_rate','onboard_old_policy']);

        $class = ClassModel::with(['motor_sections' => function($query){
                $query->where('status','ACTIVE');
            }
            ])
            ->withCount('motor_sections')
            ->where('class',$cls)
            ->first();

        $external_policy = 'N';
        if(!is_null($dcontrol->external_pol_no)){
            $externalTaxInvoice = SmartTaxInvoices::where('customer_tax_invoice',$dcontrol->external_pol_no)->exists();
            if($externalTaxInvoice){
                $external_policy = 'Y';
            }
        }
        $motorGroups =Motorprem_grp::all();
        $mandGroups = DB::table('motorprem_grp')
            ->leftJoin('MOTORPREM_GRP_CATEG', 'MOTORPREM_GRP_CATEG.GRP_CODE', '=', 'motorprem_grp.GRP_CODE')
            ->rightJoin('Motorsect',function($join){
                $join->on('Motorsect.GRP_CODE', '=', 'MOTORPREM_GRP_CATEG.GRP_CODE')
                    ->on('Motorsect.grp_categ_id', '=', 'MOTORPREM_GRP_CATEG.id');
            })
            ->whereExists(function($query) use($cls){
                $query->select('*')
                    ->from('Motorsect')
                    ->where('Motorsect.class',$cls)
                    ->whereColumn('motorprem_grp.grp_code','Motorsect.grp_code');
            })
            ->where('Motorsect.class',$cls)
            ->where('motorprem_grp.mandatory','Y')
            ->where('motorprem_grp.status','ACTIVE')
            ->where('MOTORPREM_GRP_CATEG.slug','basic-premium')
            ->get(['motorprem_grp.*', 'motorsect.classtype','motorsect.tarrif_code', 
            'motorsect.covertype' ,'MOTORPREM_GRP_CATEG.id as categ', 'MOTORPREM_GRP_CATEG.slug']);

            // dd($mandGroups);
        $otherGroups = DB::table('motorprem_grp')
            ->whereExists(function($query) use($cls){
                $query->select('*')
                    ->from('Motorsect')
                    ->where('Motorsect.class',$cls)
                    ->whereColumn('motorprem_grp.grp_code','Motorsect.grp_code');
            })
            // ->where('motorprem_grp.basic_premium','<>','Y')
            ->where('motorprem_grp.status','ACTIVE')
            ->get(['motorprem_grp.*']);

        $cover_type = DB::table('covertype')
            ->whereExists(function($query) use($cls){
                $query->select('*')
                    ->from('Motorsect')
                    ->where('Motorsect.class',$cls)
                    ->whereColumn('covertype.cover','Motorsect.covertype');
            })
            ->where('covertype.active','Y')
            ->get();

        $financier = Financier::all();
        $classtype = Classtype::where('class',$cls)->get();
        $motive = Motivepower::all();
        $make = Make::all();
        $model = Veh_Model::all();
        $valuers = Clparam::selectRaw('claimant_code as valuer_code, name as valuer_name')->where('valuer','Y')->where('status','ACTIVE')->get();
        $color = Vehcol::all();
        $bodytype = Bodytype::all()->sortby('body_type');
        $pipcnam = Pipcnam::where('record_type', 0)->first();
        $vehiclemodelyear = Vehiclemodelyear::distinct()->get(['make']);
        $comesaCountries = (new ComesaExtension)->getCountries();

        $motor_trade = ClassModel::where('class', $request->cls)->first('motor_trade');
        $motor_trade = $motor_trade->motor_trade;

        $uw_parameters = UwParameter::first();
        $binder_pol = Binderpol::where('binder_pol_no', $dcontrol->binder_pol_no)->first();

        $premium_method = Motorpremmethod::where('active', 'Y')->first()->slug;
        $veh_classes = Vehicleclassification::all();
        $masterParam = MotorMasterParam::first();
            
        return view('gb.underwriting.motor_uw',[
            'dcontrol' => $dcontrol,
            'class' => $class,
            'motorGroups' => $motorGroups,
            'binder_pol' => $binder_pol,
            'mandGroups' => $mandGroups,
            'otherGroups' => $otherGroups,
            'cover_type' => $cover_type,
            'classtype' => $classtype,
            'make' => $make,
            'model' => $model,
            'bodytype' => $bodytype,
            'color' => $color,
            'pipcnam'=>$pipcnam,
            'vehiclemodelyear'=>$vehiclemodelyear,
            'motive'=>$motive,
            'financier' => $financier,
            'valuers' => $valuers,
            'motor_trade'=>$motor_trade,
            'external_policy'=>$external_policy,
            'comesaCountries'=>$comesaCountries,
            'premium_method'=>$premium_method,
            'veh_classes' => $veh_classes,
            'masterParam'=>$masterParam,
            'uw_parameters' => $uw_parameters,
        ]);
    }

    public function single_motor(Request $request)
    {
        $cls = $request->cls;
        $dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)->firstOrFail();
        $debited = static::endorsement_debited($dcontrol->endt_renewal_no);

        // $modtl = Modtlmast::motor_details($request->endt_renewal_no,$request->reg_no);
        $modtl = Modtlpivot::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$request->reg_no)
                ->first();
                
        $modtlsum = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$request->reg_no)
                ->first();
     
        $existingSections = Motcvrdet::where('policy_no',$dcontrol->policy_no)
            ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
            ->where('reg_no',$request->reg_no)
            ->get(['grp_code','item_code']);

        $class = ClassModel::with(['motor_sections' => function($query){
                $query->where('status','ACTIVE');
            }])
            ->withCount('motor_sections')
            ->where('class',$cls)
            ->first();

        $basicPremium = Motorpolsec::basic_premium($dcontrol->endt_renewal_no,$request->reg_no);
        $motorGroups =Motorprem_grp::all();
        $mandGroups =Motorprem_grp::where('mandatory','Y')->get();
        $mandatorySection = DB::table('motorprem_grp')
            ->whereExists(function($query) use($cls){
                $query->select('*')
                    ->from('Motorsect')
                    ->where('Motorsect.class',$cls)
                    ->whereColumn('motorprem_grp.grp_code','Motorsect.grp_code');
            })
            ->where('motorprem_grp.mandatory','Y')
            ->where('motorprem_grp.status','ACTIVE')
            ->get(['motorprem_grp.*']);

        $otherGroups = DB::table('motorprem_grp')
            ->whereExists(function($query) use($cls){
                $query->select('*')
                    ->from('Motorsect')
                    ->where('Motorsect.class',$cls)
                    ->whereColumn('motorprem_grp.grp_code','motorsect.grp_code');
            })
            ->where('motorprem_grp.basic_premium','<>','Y')
            ->where('motorprem_grp.status','ACTIVE')
            ->get(['motorprem_grp.*']);

        $cover_type = Covertype::all();
        $classtype = Classtype::where('class',$cls)->get();
        $activeCoverType = Covertype::where('cover',$modtl->covertype)->first();
        $activeClassType = Classtype::where('classtype',$modtl->usage)->first();
        $classtype = Classtype::where('class',$cls)->get();
        $financier = Financier::all();
        $motive = Motivepower::all();
        $color = Vehcol::all();
        $valuers = Clparam::selectRaw('claimant_code as valuer_code, name as valuer_name')->where('valuer','Y')->where('status','ACTIVE')->get();
        $vehiclemodelyear = Vehiclemodelyear::distinct()->get(['make']);

        // actionable
        $actionable = 'Y';
        $details_amendable = 'Y';
        $reinstate_veh = 'N';

        if($debited){
            $actionable = 'N';
        }
        elseif ($dcontrol->trans_type == 'CNC' || ($dcontrol->trans_type == 'RFN' && $dcontrol->ext_to_rfn == 'N' ) || $dcontrol->trans_type == 'PTA') {
            $actionable = 'N';
        }
        elseif($modtl->status != 'ACT'){
            $actionable = 'N';
        }
        
        if($dcontrol->trans_type == 'NIL' and $dcontrol->committed == 'Y'){
            $details_amendable = 'N';
        }
        
        if($request->action_type  == 'RNS'){
            $reinstate_veh = 'Y';
        }
        

        $motor_trade = ClassModel::where('class', $request->cls)->first('motor_trade');
        $motor_trade = $motor_trade->motor_trade;
        #discounts parameters
        $discountparams =DiscountLoadingparams::where('motor_discount','Y')
                                               // ->where('fleet_discount','<>','Y')
                                                ->get();
        $uw_parameters = UwParameter::first();
        $masterParam = MotorMasterParam::first();

        
        $premium_method = Motorpremmethod::where('active', 'Y')->first()->slug;
        $classifications = Vehicleclassification::where('subclass', $modtl->usage)->get();

        if (!is_null($modtl) && !is_null($modtl->valuer)) {
            $valuerDetails = Clparam::selectRaw('claimant_code as valuer_code, name as valuer_name')
                    ->where('claimant_code', $modtl->valuer) 
                    ->first();
        }

        return view('gb.underwriting.single_motor',[
            'modtl' => $modtl,
            'modtlsum' => $modtlsum,
            'dcontrol' => $dcontrol,
            'actionable' => $actionable,
            'details_amendable' => $details_amendable,
            'class' => $class,
            'motive' => $motive,
            'vehiclemodelyear' => $vehiclemodelyear,
            'color' => $color,
            'cover_type' => $cover_type,
            'classtype' => $classtype,
            'motorGroups' => $motorGroups,
            'mandGroups' => $mandGroups,
            'otherGroups' => $otherGroups,
            'mandatorySection' => $mandatorySection,
            'basicPremium' => $basicPremium,
            'existingSections' => $existingSections,
            'activeCoverType' => $activeCoverType,
            'activeClassType' => $activeClassType,
            'financier' => $financier,
            'valuers' => $valuers,
            'motor_trade' => $motor_trade,
            'reinstate_veh' => $reinstate_veh,
            'discountparams'=>$discountparams,
            'uw_parameters' => $uw_parameters,
            'masterParam' => $masterParam,
            'premium_method' => $premium_method,
            'classifications' => $classifications,
            'valuerDetails' => $valuerDetails,
        ]);
    }
        
    public function fetch_premsect(Request $request)
    {
        // if($request->ajax()){
            try{
                $section = Motcvrdet::with('motor_group')
                    ->leftJoin('motorsect',function($join){
                        $join->on('motorsect.grp_code','motcvrdet.grp_code');
                        $join->on('motorsect.item_code','motcvrdet.item_code');
                    })
                    ->leftJoin('motorpolsec',function($join){
                        $join->on('motorpolsec.endt_renewal_no','motcvrdet.endt_renewal_no');
                        $join->on('motorpolsec.reg_no','motcvrdet.reg_no');
                        $join->on('motorpolsec.grp_code','motcvrdet.grp_code');
                        $join->on('motorpolsec.item_code','motcvrdet.item_code');
                    })
                    ->where('motcvrdet.endt_renewal_no',$request->endt_renewal_no)
                    ->where('motcvrdet.reg_no',$request->reg_no)
                    ->where('motcvrdet.grp_code',$request->group)
                    ->where('motcvrdet.item_code',$request->item)
                    ->first([
                        'motcvrdet.*',
                        'motorpolsec.endorse_amount',
                        'motorsect.basis',
                        'motorsect.min_rate_amt',
                        'motorsect.rate_basis',
                        'motorsect.free_limit',
                        'motorsect.description',
                        'motorsect.classtype'
                    ]);

                $section->rate_amount = $section->basis == 'R' ? $section->rate : $section->annual_premium;

                return [
                    'status' => 1,
                    'msg' => 'Success',
                    'data' => $section
                ];
            }
            catch(\Throwable $e){
                // dd($e);
                return [
                    'status' => 0,
                    'msg' => 'Failed to fetch section'
                ];
            }
        // }
    }


    public function verify_reg_no(Request $request){

        $endt_renewal_no = $request->get('endt_renewal_no');
        $effective_date = $request->get('effective_date');
        $reg_no = $request->get('reg_no');
        $registration = str_replace(' ', '', $reg_no);
        if(is_null($effective_date)){
            $effective_date = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first()->effective_date;
            $period_to = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first()->period_to;

        }
    
        $modtl_reg_exist = Modtlmast::where('reg_no', $registration)
                              ->where('status', 'ACT') 
                              ->count();
        $modtl_reg_wro = Modtlmast::where('reg_no', $registration)
                              ->where('status', 'WRO') 
                              ->count();
        $modtl_reg_stl = Modtlmast::where('reg_no', $registration)
                              ->where('status', 'STL') 
                              ->count();
        $blacklisted =Blacklisted_vehicles::where('reg_no', $registration)->count();

        $valid = 1;
        $result = [];

        if ($modtl_reg_wro > 0 && $request->allow_salvage == 'N') {
            $valid = 0;
            $message = "Cannot register a write off.";
        }else if($modtl_reg_stl > 0) {
            $valid = 0;
            $message = "Cannot register a stolen vehicle.";
        }else if($blacklisted > 0) {
            $valid = 0;
            $message = "Cannot register a Blacklisted vehicle! Kindly remove from Blacklist";
        }
        else {
            if($modtl_reg_exist > 0) {
                $modtl_regs = Modtlmast::where('reg_no', $registration)
                                      ->where('status', 'ACT')
                                      ->get();
                                      
                foreach ($modtl_regs as $key => $modtl_rec) {
                    $dcontrol = Dcontrol::where('endt_renewal_no', $modtl_rec->endt_renewal_no)->first();
                    $prev_period_to = $dcontrol->cov_period_to;
                    $prev_incept_date = $dcontrol->incept_date;
        
        if ($dcontrol->trans_type != 'PTA' && 
                (
                    // Check if effective_date is between prev_incept_date and prev_period_to
                    (Carbon::parse($effective_date)->gt(Carbon::parse($prev_incept_date)) && 
                    Carbon::parse($effective_date)->lt(Carbon::parse($prev_period_to))) ||
            
                    // Check if period_to is between prev_incept_date and prev_period_to
                    (Carbon::parse($period_to)->gt(Carbon::parse($prev_incept_date)) && 
                    Carbon::parse($period_to)->lt(Carbon::parse($prev_period_to)))
                )
            ){
                            $valid = 0;
                            $message = "Registration number already in use. Policy no. : {$dcontrol->policy_no}";
                    }
                }
            }
        }
        
        $result['valid'] =$valid;
        $result['message'] =$message;

    
        return json_encode($result);
    }
  
  
    public function verify_chassis_no(Request $request){
  
        $chassis = $request->get('chassis_no');
        $endt_renewal_no = $request->get('endt_renewal_no');
        $effective_date = $request->get('effective_date');
        $chassis_no = str_replace(' ', '', $chassis);
        if(is_null($effective_date)){
            $effective_date = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first()->effective_date;

        }

        $modtl_chassis_exist = Modtlmast::where('chassis_no', $chassis_no)
                                    ->where('status', 'ACT')
                                    ->count();
        $modtl_chassis_wro = Modtlmast::where('chassis_no', $chassis_no)
                                ->where('status', 'WRO') 
                                ->count();

        $blacklisted =Blacklisted_vehicles::where('chassis_no', $chassis_no)->count();
        $valid = 1;
        $message="";

        if ($modtl_chassis_wro > 0 && $request->allow_salvage == 'N') {
            $valid = 0;
            $message = "Cannot register a write off.";
        }else if($blacklisted > 0) {
            $valid = 0;
            $message = "Cannot register a Blacklisted vehicle! Kindly remove from Blacklist";
        }else if($modtl_chassis_exist > 0) {
            $modtl_chassis = Modtlmast::where('chassis_no', $chassis_no)
                                ->where('status', 'ACT')
                                ->get();

            foreach ($modtl_chassis as $key => $modtl_rec) {
                $dcontrol = Dcontrol::where('endt_renewal_no', $modtl_rec->endt_renewal_no)->first();
                $prev_period_to = $dcontrol->cov_period_to;        
                $year = substr($modtl_rec->endt_renewal_no, 13, 4);
                $curr_yr = date("Y");

                if ($year == $curr_yr && $prev_period_to->trans_type != 'PTA') {
                    if (Carbon::parse($effective_date)->lte(Carbon::parse($prev_period_to))) {
                        $valid = 0;
                        $message="Chasis Number already In use! Please use a different chassis number";
                    }
                }
            }
        }

        $result = [
            'valid'=>$valid,
            'msg'=>$message
        ];
    
        return json_encode($result);
    }

    public function verify_engine_no(Request $request){
      $engine = $request->engine_no;
      $endt_renewal_no = $request->endt_renewal_no;
      $engine_no = str_replace(' ', '', $engine);
      
      $effective_date = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first()->effective_date;
  
      $modtl_engine_exist = Modtlmast::where('engine_no', $engine_no)
                                  ->where('status', 'ACT')
                                  ->count();
      $blacklisted =Blacklisted_vehicles::where('engine_no', $engine_no)->count();
  
        $valid = 1;
        $message="";
   
      
        if($blacklisted > 0) {
            $valid = 0;
            $message = "Cannot register a Blacklisted vehicle! Kindly remove from Blacklist";
        }

        if($modtl_engine_exist > 0) {
            $modtl_engines = Modtlmast::where('engine_no', $engine_no)
                                  ->where('status', 'ACT')
                                  ->get();
            foreach ($modtl_engines as $key => $modtl_rec) {
                $dcontrol = Dcontrol::where('endt_renewal_no', $modtl_rec->endt_renewal_no)->first();
                $prev_period_to = $dcontrol->cov_period_to;
                
                $year = substr($modtl_rec->endt_renewal_no, 13, 4);
                $curr_yr = date("Y");
    
                if ($year == $curr_yr && $prev_period_to->trans_type != 'PTA') {
                    if (Carbon::parse($effective_date)->lte(Carbon::parse($prev_period_to))) {
                        $valid = 0;
                        $message="Engine Number Already in use! Please use a different engine number ";
                    }
                }
            }
        }

        $result = ['valid'=>$valid,'msg'=>$message];
    
        return json_encode($result);
    }

    public function checkIfSalvaged(Request $request) {
      $reg_no = $request->reg_no;
  
      $checkIfSalvaged = Salvages::where('reg_no', $reg_no)->first();
  
      return $checkIfSalvaged;
    }


    public function save_vehicle(NewMotorRequest $request)
    {
        // return $request;
        $groups = $request->group;
        $sections = $request->section;
        $rate_amounts = $request->rate_amount;
        $risk_values = $request->risk_value;
        $cls = $request->cls;

        // dd($request->all());
        DB::beginTransaction();
        try {

            $this->pol_to_pta($request->endt_renewal_no);
            $dcontrol = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->firstOrFail();
            $bustype = Bustype::where('type_of_bus', $dcontrol->type_of_bus)->first();

            $sum_insured = $total_sum_insured = (float) str_replace(',', '', $request->get('sum_insured'));
            $this->_total_sum_insured = $total_sum_insured;
            
            if (($dcontrol->co_insure == 'Y' && $bustype->leader == 'Y') || $bustype->facult_in == 'Y') {
                $sum_insured = ($sum_insured * $dcontrol->company_share) / 100;
            }
            $transtype = $dcontrol->trans_type;
            $this->_cls = $request->cls;
            $this->_reg_no = $request->reg_no;
            $this->_endt_renewal_no = $request->endt_renewal_no;
            $this->_seat_cap = $request->insured_seats;

            if($transtype != 'CNC' || ($transtype == 'RFN' && $dcontrol->ext_to_rfn =='Y')){
                $this->Save_motor_details($request);
                $this->update_motor_summary($dcontrol->endt_renewal_no,$cls,$request->reg_no);
            }

            foreach ($groups as $key => $grp) {
                $risk_value = (float)str_replace(',','',$risk_values[$key]);
                $rate_amount = (float)str_replace(',','',$rate_amounts[$key]);

                $motorate = Motorsect::with('motprem_group')
                    ->where('class',$cls)
                    ->where('grp_code',$grp)
                    ->where('item_code',$sections[$key])
                    ->firstOrFail();

                if($motorate->basis == 'R' && $motorate->rate_basis == 'S'){
                    $risk_value = $sum_insured;
                }

                $section = [
                    'group' => $grp,
                    'item' => $sections[$key],
                    'rate_amount' => $rate_amount,
                    'risk_value' => $risk_value,
                    'cancel' => 'N',
                ];
                
                $section['rate_amount'] = $this->get_minRateAmt($section);
                $premium_amounts = $this->compute_motor_premium($section,$dcontrol->ast_marker);
                $resp = $this->save_section_dtl($section,$premium_amounts);
                if($dcontrol->trans_type == 'PTA'){
                    $this->saveComesaDetails($request,$request->endt_renewal_no,$request->reg_no);
                }

                if($resp['status'] == 1){
                    continue;
                }
                else{
                    break;
                    throw new Exception($resp['message'],500);
                    
                }
            }
  
            $this->update_motor_summary($dcontrol->endt_renewal_no,$cls,$request->reg_no);
            $this->update_polmaster($dcontrol->endt_renewal_no);

            DB::commit();
            return [
                'status' => 1,
                'message' => 'Motor details saved successfully'
            ];
        } catch (\Throwable $e) {
            DB::rollback();
            // dd($e->getMessage());
            $error_msg = json_encode($e->getMessage());
			$reference = "Endorsement_no: {$request->endt_renewal_no}";
            $module = __METHOD__;
			$route_name = Route::getCurrentRoute()->getActionName();

			log_error_details($route_name,$error_msg,$reference,$module);

            return response()-> json([
                'status' => 0,
                'message' => 'Failed to process motor',
                
            ],500);
        }
        
    }

    public function save_motor_sections(Request $request)
    {
        // return $request;
        $request->validate([
            'cls' => 'required',
            'reg_no' => 'required',
            'group' => 'required',
            'group.*' => 'required',
            'section' => 'required',
            'section.*' => 'required',
            'rate_amount.*' => 'required',
            // 'risk_value.*' => 'required',
        ]);
       
        $groups = $request->group;
        $sections = $request->section;
        $rate_amount = $request->rate_amount;
        $cls = $request->cls;

        DB::beginTransaction();
        try {
            $dcontrol = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->firstOrFail();
            static::endorsement_debited($request->endt_renewal_no,'PROCESS');
            $bustype = Bustype::where('type_of_bus', $dcontrol->type_of_bus)->first();
            $modtl = Modtlmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$request->reg_no)
                ->first();

            $total_sum_insured = $modtl->premium_dtl->total_sum_insured;
            $this->_total_sum_insured = $request->total_sum_insured;
            $this->_cls = $request->cls;
            $this->_reg_no = $request->reg_no;
            $this->_endt_renewal_no = $request->endt_renewal_no;
            $this->_seat_cap = $modtl->seat_cap;
            $this->_insured_seats = $modtl->insured_seats;
            
            if (($dcontrol->co_insure == 'Y' && $bustype->leader == 'Y') || $bustype->facult_in == 'Y') {
                $sum_insured = ($total_sum_insured * $dcontrol->company_share) / 100;
            }

            foreach ($groups as $key => $grp) {

                $sect_dtl = Motcvrdet::where('policy_no',$dcontrol->policy_no)
                    ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$request->reg_no)
                    ->where('grp_code',$grp)
                    ->where('item_code',$sections[$key])
                    ->first();

                if(isset($sect_dtl)){
                    throw new Exception("Section already exists",403);
                }
                $risk_value = str_replace(',','',$request->risk_value);
                
                $section = [
                    'group' => $grp,
                    'item' => $sections[$key],
                    'rate_amount' => $rate_amount[$key],
                    'risk_value' => $risk_value[$key],
                    'cancel' => 'N',
                ];

                $section['rate_amount'] = $this->get_minRateAmt($section);
                $premium_amounts = $this->compute_motor_premium($section,$dcontrol->ast_marker);
                $resp = $this->save_section_dtl($section,$premium_amounts);
                
                if($resp['status'] == 1){
                    continue;
                }
                else{
                    break;
                    throw new Exception($resp['message'],500);
                }
            }
            $this->update_motor_summary($dcontrol->endt_renewal_no,$cls,$request->reg_no);
            $this->update_polmaster($dcontrol->endt_renewal_no);

            DB::commit();
            return [
                'status' => 1,
                'message' => 'Cover details saved successfully'
            ];

        } catch (\Throwable $e) {
            DB::rollback();
            // dd($e);
            $error_msg = json_encode($e->getMessage());
			$reference = "endt_renewal_no: {$request->endt_renewal_no} reg_no {$request->reg_no}";
            $module = __METHOD__;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$reference,$module);

            $message = 'Failed to process motor';
            if($e->getCode() == 403){
                $message = $e->getMessage();
            }
            return response()-> json([
                'status' => 0,
                'message' => $message,
                
            ],500);
        }
    }
        
    public function Save_motor_details($request){
        // dd($request->all());
        $user = Auth::user()->user_name;
        $doc = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->firstOrFail();
        $cls = $request->cls;

        $bustype = Bustype::where('type_of_bus', $doc->type_of_bus)->first();
        $policy_no = $doc->policy_no;

        $item_no = Modtlmast::next_serial($doc->policy_no);
        

        $total_sum_insured = $sum_insured = (float) str_replace(',', '', $request->get('sum_insured'));

        if (($doc->co_insure == 'Y' && $bustype->leader == 'Y') || $bustype->facult_in == 'Y') {
            $sum_insured = ($sum_insured * $doc->company_share) / 100;
        }
        
        Modtlmast::create([
            'item_no' => $item_no,
            'policy_no' => $doc->policy_no,
            'endt_renewal_no' => $doc->endt_renewal_no,
            'transeq_no' => $doc->transeq_no,
            'client_number' => $doc->client_number,
            'class' => $cls,
            'reg_no' => $request->reg_no,
            'usage' => $request->subclass,
            'tarrif_code' => $request->veh_class,
            'financed' => $request->financed,
            'financier' => $request->financier,
            'owner' => $request->owner,
            'driver' => $request->driver,
            'covertype' => $request->cover_type ,
            'engine_no' => $request->engine,
            'bodytype' => $request->body_type,
            'veh_condition' => $request->condition,
            'chassis_no' => $request->chasis,
            'cubic_capacity' => $request->cc ,
            'model' => $request->v_model,
            'make' => $request->v_make, 
            'trailer' => $request->trailer,
            'manufacture_year' => $request->manufacture_yr,
            'color' => $request->color,
            'seat_cap' => $request->seat_cap,
            'insured_seats' => $request->insured_seats,
            'logbook_no' => $request->log_book_no,
            'motive_power' => $request->motive_p,
            'tare_weight' => $request->tare_weight,
            'tonnage' => $request->carry_cap,
            'axles_no' => $request->axles_no,
            'axle_distance' => $request->axle_distance,
            'metallic_color' => $request->met_color,
            'valuation' => $request->valuation,
            'valuer' => $request->valuer,
            'created_by' => $user,
            'logbook_transferred' => $request->logbook_transferred ?? 'N',
            'vehicle_from_showroom' => $request->vehicle_from_showroom ?? 'N',
        ]);
    
    }

    public function edit_motor(Request $request){
        
        DB::beginTransaction();
        try {
            $user = Auth::user()->user_name;
            $doc = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->firstOrFail();
            $bustype = Bustype::where('type_of_bus', $doc->type_of_bus)->first();
            $policy_no = $doc->policy_no;
            $total_sum_insured = (float) str_replace(',', '', $request->get('sum_insured'));

            $transtype = $doc->trans_type;

            $vehicle_exists = Modtlmast::where('policy_no', $policy_no)
                                        ->where('reg_no', $request->reg_no)
                                        ->exists();

            

            if ($vehicle_exists) {
                $vehicle_modtlmast = Modtlmast::where('policy_no', $policy_no)
                                                ->where('reg_no', $request->reg_no)
                                                ->firstOrFail();
                
                if($vehicle_modtlmast->status != 'ACT'){
                    throw new Exception('Vehicle not active, you cannot update details.  ',403);
                }

                $total_sum_insured = $sum_insured = (float) str_replace(',', '', $request->get('sum_insured'));

                if (($doc->co_insure == 'Y' && $bustype->leader == 'Y') || $bustype->facult_in == 'Y') {
                    $sum_insured = ($sum_insured * $doc->company_share) / 100;
                }

                if($vehicle_modtlmast->total_sum_insured != $total_sum_insured){
                    $this->_total_sum_insured = $total_sum_insured;
                    $this->_cls = $request->cls;
                    $this->_reg_no = $request->reg_no;
                    $this->_endt_renewal_no = $request->endt_renewal_no;
                        
                    $resp = $this->recompute_premiums();
                    if($resp['status'] != 1){
                        throw new Exception("Error Processing Request", $resp['status_code']);
                    }
                }

                Modtlmast::where('policy_no', $policy_no)
                        ->where('reg_no', $request->reg_no)
                        ->where('endt_renewal_no', $request->endt_renewal_no)
                        ->update([
                            'usage' => $request->subclass,
                            'tarrif_code' => $request->veh_class,
                            'financed' => $request->financed,
                            'financier' => $request->financier,
                            'owner' => $request->owner,
                            'driver' => $request->driver,
                            'covertype' => $request->cover_type ,
                            'cubic_capacity' => $request->cc ,
                            'engine_no' => $request->engine,
                            'veh_condition' => $request->condition,
                            'chassis_no' => $request->chasis,
                            'model' => $request->v_model,
                            'make' => $request->v_make, 
                            'bodytype' => $request->body_type,
                            'manufacture_year' => $request->manufacture_yr,
                            'color' => $request->color,
                            'trailer' => $request->trailer,
                            'seat_cap' => $request->seat_cap,
                            'insured_seats' => $request->insured_seats,
                            'logbook_no' => $request->log_book_no,
                            'motive_power' => $request->motive_p,
                            'tonnage' => $request->carry_cap,
                            'metallic_color' => $request->met_color,
                            'logbook_no' => $request->log_book_no,
                            'valuation' => $request->valuation,
                            'logbook_transferred' => $request->logbook_transferred ?? 'N',
                            'vehicle_from_showroom' => $request->vehicle_from_showroom ?? 'N',
                            'valuer' => $request->valuer,
                            'updated_by' => $user,
                        ]);
                

                DB::commit();
                     
            }else {  
                throw new Exception('Vehicle doesn\'t exist',403);
            }
            return [
                'status' => 1,
                'message' => 'Motor details saved successfully'
            ];
        
        } catch (\Throwable $e) {
            DB::rollback();
            // dd($e);
            $error_msg = json_encode($e->getMessage());
			$reference = "Endorsement_no: {$request->endt_renewal_no}";
            $module = __METHOD__;
			$route_name = Route::getCurrentRoute()->getActionName();

			log_error_details($route_name,$error_msg,$reference,$module);

            return response()-> json([
                'status' => 0,
                'message' => 'Failed to process motor',
                
            ],500);
        }
    }

    public function get_minRateAmt($section) : float
    {
        $cls = $this->_cls;
        $risk_value = (float) str_replace(',', '', $section['risk_value']);

        $dcontrol = Dcontrol::where('endt_renewal_no', $this->_endt_renewal_no)->firstOrFail();
        $motorate = Motorsect::where('class',$cls)
            ->where('grp_code',$section['group'])
            ->where('item_code',$section['item'])
            ->firstOrFail();

        $paramRateAmount = $motorate->rate_amount;
        $minRateAmount = $motorate->min_rate_amount;
        $basis = $motorate->basis;

        $isBasic = Motorprem_grp::where('grp_code',$section['group'])
            ->where('basic_premium','Y')
            ->exists();
        $rate_amount = $section['rate_amount'];
        $masterParam = MotorMasterParam::first();

        // Basic rate based sum insured based 
        if($masterParam->sum_insured_based_rate == 'Y' && $isBasic == 'Y')
        {
            $siband = SiRateBands::where('class',$cls)
                ->where('min_si','<=',$risk_value)
                ->where('max_si','>=',$risk_value)
                ->when($masterParam->si_band_app_level == 'USAGE',function($query) use ($motorate){
                    return $query->where('classtype',$motorate->classtype);
                });
            if($siband->count() ==0)
            {
                throw new Exception("No Sum Insured Band found for this sum insured ({$section['risk_value']})",403);
            }

            $paramRateAmount = $siband->first()->rate_amount;
            $minRateAmount = $siband->first()->min_rate_amount;
            $basis = $siband->first()->basis;
        }

        switch($basis){
            case 'R':
                // check basic

                $binder = (new Risk)->getBinderRate($dcontrol);
                
                $rate_amount = $section['rate_amount'];
                if($isBasic && $binder->rate > 0){
                    $rate_amount = $binder->rate;
                }
                else if ((float)$section['rate_amount'] < $minRateAmount && $minRateAmount != 0 ) {
                    $rate_amount = $minRateAmount;
                }

                if($rate_amount > 100){
                    throw new Exception("Rate cannot exceed 100",403);
                    
                }
                break;
            default:
                if ((float)$section['rate_amount'] < ($minRateAmount * $dcontrol->currency_rate) && $minRateAmount !=0) {
                    $rate_amount = $minRateAmount;
                }
                break;
        }

        return $rate_amount;

    }

    public function compute_motor_premium($section,$method)
    {
        // normalize data
        $risk_value = (float) str_replace(',', '', $section['risk_value']);
        $rate_amount = (float) str_replace(',', '', $section['rate_amount']);

        // globals
        $cls = $this->_cls;
        $rate = 0;
        $annual_premium = 0;
        $premium_movt = 0;
        $risk_value_movt = 0;
        $endorse_amount = 0;
        $cancelNewSection = false;

        $dcontrol = Dcontrol::where('endt_renewal_no', $this->_endt_renewal_no)->firstOrFail();
        $polmaster = Polmaster::where('endorse_no', $this->_endt_renewal_no)->firstOrFail();
        $modtl = Modtlmast::where('policy_no',$dcontrol->policy_no)
            ->where('reg_no',$this->_reg_no)
            ->first();
        $dcontrolPrev = Dcontrol::previous_endorsement($dcontrol->endt_renewal_no);
        $motorate = Motorsect::with('motprem_group')
            ->where('class',$cls)
            ->where('grp_code',$section['group'])
            ->where('item_code',$section['item'])
            ->firstOrFail();

        // check if vehicle has been reinstated in this endorsement
        $reinstated = Modtlpivot::reinstatement_status($dcontrol->endt_renewal_no,$this->_reg_no);
        
        // if reinstated, don't pick previous section details. Treat like POL/REN/RNS
        switch ($reinstated) {
            case false:
                $prevSect = Motcvrdet::where('policy_no',$dcontrol->policy_no)
                    ->where('endt_renewal_no',$dcontrolPrev->endt_renewal_no)
                    ->where('reg_no',$this->_reg_no)
                    ->where('grp_code',$section['group'])
                    ->where('item_code',$section['item'])
                    ->first();
            break;
            default:
                $prevSect = null;
        }

        $prev_annual = $prevSect->annual_premium ?? 0;
        $prev_risk_value = $prevSect->risk_value ?? 0;
        $free_limit = (float)$motorate->free_limit ?? 0;
        $base = $motorate->base;

        $basicPremium = Motorpolsec::basic_premium($dcontrol->endt_renewal_no,$this->_reg_no);
        if($this->_preview == 'Y' && $basicPremium == null){
            $basicPremium = Cache::get($this->_endt_renewal_no);
        }

        switch($motorate->basis){
            case 'R':
                // check if risk value is negative
                if($risk_value< 0){
                    throw new Exception("Risk Value cannot be less than 0",403);
                }
                elseif ((float)$basicPremium == 0 && $motorate->rate_basis == 'P') {
                    throw new Exception("Basic Premium is zero. Principal is the Basic Premium",403);
                }

                if($free_limit >= $risk_value){
                    $actual_risk_value = 0;
                }
                elseif($section['cancel'] == 'Y'){
                    $actual_risk_value = $risk_value;
                }
                else{
                    switch ($motorate->rate_basis) {
                        case 'S':
                        case 'F':
                            $actual_risk_value = $risk_value - $free_limit;
                        break;
                        case 'P':
                            $actual_risk_value = $basicPremium - $free_limit;
                        break;
                        default:
                            # code...
                            break;
                    }
                }

                $rate = $rate_amount;
                // sum insured
                if($motorate->rate_basis == 'S'){
                    $annual_premium = ($rate * ($actual_risk_value))/$base;
                }
                // basic premium
                else if($motorate->rate_basis == 'P'){
                    $annual_premium = ($rate * ($actual_risk_value))/$base;
                }
                else if($motorate->rate_basis == 'F'){
                    $annual_premium = ($rate * ($actual_risk_value))/$base; 
                }
                break;
            case 'A':
                $annual_premium = (float)$rate_amount;
            default: 
                break;
        }

        // check if per seat premium applies
        if($motorate->per_carry == 'Y' || $motorate->section_type == 'seat-loading'){
            if(isset($this->_seat_cap)){
                $seat_cap = (int)$this->_seat_cap;
            }else{
                $seat_cap = (int)$modtl->seat_cap;
            }

            $annual_premium = $annual_premium * ($seat_cap);
        }

        // check if per passenger premium applies
        if($motorate->per_passenger == 'Y' || $motorate->section_type == 'passenger-loading'){
            if(isset($this->_seat_cap)){
                $seat_cap = (int)$this->_insured_seats;
            }else{
                $seat_cap = (int)$modtl->insured_seats;
            }

            $annual_premium = $annual_premium * ($seat_cap);
        }

        
        // check if age loading applies
        if($motorate->age_load == 'Y'){
            $age = Carbon::now()->diffInYears(Carbon::createFromDate($this->_manf_year));
            $ageloading = Ageloads::whereRaw("? between min_age and max_age", $age)->first();

            if (!is_null($ageloading) && $this->_manf_year > 0) {
                if ($ageloading->rate_basis == 'R') {
                    $age_rate = $ageloading->rate_amount/100;
                    $age_load_prem = $age_rate*$annual_premium;
                } else {
                    $age_load_prem = $ageloading->rate_amount;
                }

                $annual_premium += $age_load_prem;
                

            }
        }

        // check if occupant
        if($motorate->section_type == 'occupants'){
            $param = Occupantparam::where('occupant_option', $motorate->occupant_option)->first();
            $class_m_cycle = ClassModel::where('class', $motorate->class)->first();
            $usage = Usages::where('id', $motorate->classtype)->first();

            if (!is_null($param) && !is_null($class_m_cycle) && !is_null($usage)) {
                if (!is_null($usage)) {
                    $usage = $usage->slug;
                }
    
                if (!is_null($class_m_cycle)) {
                    $class_m_cycle = $class_m_cycle->motor_cycle;
                }
                switch ($usage) {
                    case 'private':
                            if ($class_m_cycle == 'Y') {
                                $occ_rate = $param->private_motor_cycle_rate/100;
                            } else {
                                $occ_rate = $param->private_rate/100;
                            }
                        break;
                    
                    default:
                            $occ_rate = $param->commercial_rate/100;
                        break;
                }
                
                $occ_prem = $occ_rate*$param->death_limit;

                if(isset($this->_seat_cap)){
                    $occ_prem = (int)$this->_seat_cap * $occ_prem;
                }
                
                $annual_premium = $occ_prem;
            }
        }

        $binder_upload_check = Binder::where('policy_no',$dcontrol->policy_no)->first();
        $icecash = IceCash::where('policy_no',$dcontrol->policy_no)->first();

        // CHECK IF annual premium IS LESS THAN MINIMUM PREMIUM
        $minimum_prem = (float)($motorate->minimum_premium/$dcontrol->currency_rate);
        if($minimum_prem > (float)$annual_premium && $dcontrol->onboard_old_policy != 'Y' && empty($binder_upload_check)){
            $annual_premium = $minimum_prem;
        }     
        
        switch ($dcontrol->trans_type) {
            case 'EXT':
            case 'RFN':
            case 'CXT':
                // section existed in previous transaction and not extending tor/short term
                if(isset($prevSect) && $section['cancel'] != 'Y' && !$this->_extend_tor){
                    $premium_movt = (float)$annual_premium - $prev_annual;
                    $risk_value_movt = (float)$risk_value - $prev_risk_value;
                }
                else{
                    $premium_movt = $annual_premium;
                    $risk_value_movt = $risk_value;
                }
            break;
            default:
                $premium_movt = $annual_premium;
                $risk_value_movt = $risk_value;
            break;
        }

        // cancellation but section didn't exist in previous endorsement or POL, REN,RNS
        if($section['cancel'] == 'Y'){
            switch ($dcontrol->trans_type) {
                case 'EXT':
                case 'RFN':
                case 'CXT':
                    if(empty($prevSect)){
                        $cancelNewSection = true;
                    }
                    break;
                default:
                    $cancelNewSection = true;
                    break;
            }
        }
        // No refund for new sections/vehicle
        if($cancelNewSection){
            $endorse_amount = 0;
        }
        else if($this->_initial_rns){
            $dcontrolPrev = Dcontrol::previous_endorsement($dcontrol->endt_renewal_no);

            $prevSect = Motorpolsec::where('policy_no',$dcontrol->policy_no)
                                    ->where('endt_renewal_no',$dcontrolPrev->endt_renewal_no)
                                    ->where('reg_no',$this->_reg_no)
                                    ->where('grp_code',$section['group'])
                                    ->where('item_code',$section['item'])
                                    ->first();

            $endorse_amount = $prevSect->endorse_amount * -1 ;

        }
        elseif($motorate->motprem_group->proratable == 'Y'){
            $yearLength = (new Risk)->yearLength($polmaster->uw_year, $dcontrol->endt_renewal_no);
            switch ($method) {
                case 'A':
                    $endorse_amount = (new Risk)->prorate($dcontrol->endt_renewal_no, $premium_movt);
                    break;
                case 'S':
                    // percentage
                    if($icecash){
                        $endorse_amount = $section['rate_amount'];
                    }else{
                        if($dcontrol->short_term_method == 'S'){
                            $endorse_amount = $premium_movt * $dcontrol->short_term_percent / 100;
                        }else{
                            $endorse_amount = $premium_movt * ($dcontrol->days_covered / $yearLength);
                        }

                    }
                   
                    break;
                case 'T':
                    $endorse_amount = $premium_movt * ($dcontrol->endt_days / $dcontrol->days_covered);
                    break;
                case 'I':
                    $instalparam = Instalparam::where('plan', $dcontrol->plan)
                        ->where('plan_categ', $dcontrol->plan_categ)
                        ->where('instal_categ', $dcontrol->instal_categ)
                        ->first();
                    $rate = $instalparam->instal_rate;
                    
                    $endorse_amount = round($premium_movt  * ($rate / 100), 0);

                    if ($dcontrol->trans_type == 'EXT' || $dcontrol->trans_type == 'RFN') {
                        $dcontrol_instal = Dcontrol::whereIn('trans_type', ['INS', 'POL', 'REN', 'RNS'])
                        ->where('policy_no', $dcontrol->policy_no)
                        ->orderBy('dcon_no', 'DESC')
                        ->first();
            
                        $endorse_amount = round(($premium_movt * ($rate / 100)) * ($dcontrol->endt_days / $dcontrol_instal->endt_days), 0);
                
                    }
                
                default:
                    
                    break;
            }
        }
        else{
            $endorse_amount = $premium_movt;
        }

        if($section['cancel'] == 'Y'){
            $endorse_amount *=-1; 
        }

        return [
            'annual_premium' => $annual_premium,
            'premium_movt' => $premium_movt,
            'risk_value_movt' => $risk_value_movt,
            'endorse_amount' => $endorse_amount
        ];
    }

    public function save_section_dtl($section,$premiumAmts)
    {
        $cls = $this->_cls;
        $dcontrol = Dcontrol::where('endt_renewal_no', $this->_endt_renewal_no)->firstOrFail();
        $motorate = Motorsect::where('class',$cls)
            ->where('grp_code',$section['group'])
            ->where('item_code',$section['item'])
            ->firstOrFail();

        $sectionAnnual = Motcvrdet::where('policy_no',$dcontrol->policy_no)
            ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
            ->where('reg_no',$this->_reg_no)
            ->where('grp_code',$section['group'])
            ->where('item_code',$section['item'])
            ->first();

        $trans_section = Motorpolsec::where('policy_no',$dcontrol->policy_no)
            ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
            ->where('reg_no',$this->_reg_no)
            ->where('grp_code',$section['group'])
            ->where('item_code',$section['item'])
            ->first();

        $modtl = Modtlpivot::where('policy_no',$dcontrol->policy_no)
            ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
            ->where('reg_no',$this->_reg_no)
            ->first();
        // global
        $deleteSection = false;
        $rate = $motorate->basis == 'R' ? $section['rate_amount'] : 0;
        $risk_value = (float) $section['risk_value'];
        
        if(isset($sectionAnnual)){
            Motcvrdet::where('policy_no',$dcontrol->policy_no)
                ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$this->_reg_no)
                ->where('grp_code',$section['group'])
                ->where('item_code',$section['item'])
                ->update([
                    'risk_value' => $risk_value,
                    'rate' => $rate,
                    'annual_premium' => $premiumAmts['annual_premium'],
                    'updated_by' => Auth::user()->user_name,
                    'cancelled' => $section['cancel'],
                    'deleted' => ($section['cancel'] == 'N') ? 'N' : $sectionAnnual->deleted,
                ]);

            # Update Sec_ext_reinsure 
            
            if($section['cancel'] == 'Y'){
                $secExtUpdate = Sec_ext_reinsure::where('policy_no', $dcontrol->policy_no)
                    ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                    ->where('reg_no', $modtl->reg_no)
                    ->where('location', $modtl->item_no)
                    ->where('section_grp', $section['group'])
                    ->where('section_no', $section['item'])
                    ->delete();
            }
            else{
                
                $secExtUpdate = Sec_ext_reinsure::where('policy_no', $dcontrol->policy_no)
                    ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
                    ->where('reg_no', $modtl->reg_no)
                    ->where('section_grp', $section['group'])
                    ->where('section_no', $section['item'])
                    ->update([
                        'location'     => $modtl->item_no,
                        'sum_insured'     => $risk_value,
                        'premium'         => $premiumAmts['annual_premium'],
                        'endorse_amount'  => $premiumAmts['endorse_amount'],
                    ]);
            }
        }
        else{
            $motcvrdet = new Motcvrdet();
            $motcvrdet->policy_no = $dcontrol->policy_no;
            $motcvrdet->endt_renewal_no = $dcontrol->endt_renewal_no;
            $motcvrdet->class = $cls;
            $motcvrdet->transeq_no = $dcontrol->transeq_no;
            $motcvrdet->section_description = $motorate->description;
            $motcvrdet->reg_no = $this->_reg_no;
            $motcvrdet->rate = $rate;
            $motcvrdet->grp_code = $section['group'];
            $motcvrdet->item_code = $section['item'];
            $motcvrdet->risk_value = $risk_value;
            $motcvrdet->annual_premium = $premiumAmts['annual_premium'];
            $motcvrdet->created_by = Auth::user()->user_name;
            $motcvrdet->cancelled = $section['cancel'];
            $motcvrdet->save();

            # Sec_ext_reinsure
            if ($motorate->reinsure_seperate == 'Y') {
                $polmaster = Polmaster::where('endorse_no', $dcontrol->endt_renewal_no)->first();

                $secExtReinsure = Sec_ext_reinsure::create([
                    'policy_no'       => $dcontrol->policy_no,
                    'endt_renewal_no' => $dcontrol->endt_renewal_no,
                    'location'        => $modtl->item_no,
                    'reg_no'        => $modtl->reg_no,
                    'uw_class'        => $motorate->uw_class,
                    'uw_year'         => $polmaster->uw_year,
                    'sum_insured'     => $risk_value,
                    'premium'         => $premiumAmts['annual_premium'],
                    'endorse_amount'  => $premiumAmts['endorse_amount'],
                    'section_grp'      => $section['group'],
                    'section_no'      => $section['item'],
                    'class'         => $cls,
                ]);
            }
        }
        
        switch ($dcontrol->trans_type) {
            case 'EXT':
            case 'RFN':
            case 'CXT':
                $dcontrolPrev = Dcontrol::previous_endorsement($dcontrol->endt_renewal_no);  
                $prevTrans_sect = Motorpolsec::where('policy_no',$dcontrol->policy_no)
                    ->where('endt_renewal_no',$dcontrolPrev->endt_renewal_no)
                    ->where('reg_no',$this->_reg_no)
                    ->where('grp_code',$section['group'])
                    ->where('item_code',$section['item'])
                    ->first();

                // remove item if no change has happened
                if(isset($prevTrans_sect) && $premiumAmts['endorse_amount'] ==0){

                    $deleteSection = true;
                }
                break;
            
            default:
                if($section['cancel'] == 'Y' && $premiumAmts['endorse_amount'] ==0){
                    $deleteSection = true;
                }
                break;
        }
        
        if($deleteSection){
            
            $deletedSection =Motorpolsec::where('policy_no',$dcontrol->policy_no)
                ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$this->_reg_no)
                ->where('grp_code',$section['group'])
                ->where('item_code',$section['item'])
                ->delete();
        }
        else if(isset($trans_section)){

            Motorpolsec::where('policy_no',$dcontrol->policy_no)
                ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$this->_reg_no)
                ->where('grp_code',$section['group'])
                ->where('item_code',$section['item'])
                ->update([
                    'risk_value' => $risk_value,
                    'risk_value_movt' => $premiumAmts['risk_value_movt'],
                    'rate' => $rate,
                    'annual_premium' => $premiumAmts['annual_premium'],
                    'premium_movt' => $premiumAmts['premium_movt'],
                    'endorse_amount' => $premiumAmts['endorse_amount'],
                    'updated_by' => Auth::user()->user_name
                ]);

        }
        else{
            $motorpolsec = new Motorpolsec();
            $motorpolsec->policy_no = $dcontrol->policy_no;
            $motorpolsec->endt_renewal_no = $dcontrol->endt_renewal_no;
            $motorpolsec->class = $cls;
            $motorpolsec->transeq_no = $dcontrol->transeq_no;
            $motorpolsec->reg_no = $this->_reg_no;
            $motorpolsec->grp_code = $section['group'];
            $motorpolsec->item_code = $section['item'];
            $motorpolsec->risk_value = $risk_value;
            $motorpolsec->risk_value_movt = $premiumAmts['risk_value_movt'];
            $motorpolsec->rate = $rate;
            $motorpolsec->annual_premium = $premiumAmts['annual_premium'];
            $motorpolsec->premium_movt = $premiumAmts['premium_movt'];
            $motorpolsec->endorse_amount = $premiumAmts['endorse_amount'];
            $motorpolsec->created_by = Auth::user()->user_name;
            $motorpolsec->save();
        }
        
        return [
            'status' => 1,
            'message' => 'Persisted to database'
        ];
    }

    public function update_motor_summary($endt_renewal_no,$cls,$reg_no)
    {
        $dcontrol = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->firstOrFail();
        $dcontrolPrev = Dcontrol::previous_endorsement($endt_renewal_no);
        $bustype = Bustype::where('type_of_bus', $dcontrol->type_of_bus)->first();

        $sectionEndorseSum = Motorpolsec::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                                        ->where('reg_no',$reg_no)
                                        ->sum('endorse_amount');
                                      

        $sections = Motcvrdet::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                             ->where('reg_no',$reg_no)
                             ->where('deleted','<>','Y')
                             ->get();

      

        $sectionAnnualSum = 0;
        $total_sum_insured = $this->_total_sum_insured;
      

        foreach ($sections as $currSect) {

            $prevSectStatus = Motcvrdet::where('endt_renewal_no',$dcontrolPrev->endt_renewal_no)
                                        ->where('reg_no',$reg_no)
                                        ->where('grp_code',$currSect->grp_code)
                                        ->where('item_code',$currSect->item_code)
                                        ->first();

            $reinstated = Modtlpivot::reinstatement_status($endt_renewal_no,$reg_no);
            // eleminate sections cancelled previously and still cancelled from summation of annual prem
            if($prevSectStatus->cancelled == 'Y' && $currSect->cancelled == 'Y' && !$reinstated ){
                $sectPremium = 0;
            }
            // not existing previously, new section but cancelled before debit
            elseif(empty($prevSectStatus) && $currSect->cancelled == 'Y' && !$reinstated){
                $sectPremium = 0;
            }
            else{
                $sectPremium = (float)$currSect->annual_premium;
            }

            $sectionAnnualSum += $sectPremium;
        }
      

                # Discounts and Loadings
        $total_discounts = Discounts_loadings::where('policy_no', $dcontrol->policy_no)
            ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
            ->where('reg_no', $reg_no)
            ->where('type', 'D')
            ->where('deleted', '<>','Y')
            ->sum('endorse_amount');

        $total_loadings = Discounts_loadings::where('policy_no', $dcontrol->policy_no)
            ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
            ->where('reg_no', $reg_no)
            ->where('deleted', '<>','Y')
            ->where('type', 'L')
            ->sum('endorse_amount');

        $modtl = Modtlpivot::where('policy_no',$dcontrol->policy_no)
            ->where('endt_renewal_no', $dcontrol->endt_renewal_no)
            ->where('reg_no',$reg_no)
            ->firstOrFail();

        $summaryExists = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
            ->where('reg_no',$reg_no)
            ->exists();

        $modtlsumm = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
            ->where('reg_no',$reg_no)
            ->first();

        if(isset($total_sum_insured)){
            $sum_insured = $total_sum_insured;
            if (($dcontrol->co_insure == 'Y' && $bustype->leader == 'Y') || $bustype->facult_in == 'Y') {
                $sum_insured  = ($total_sum_insured*$dcontrol->company_share)/ 100;
            }
        }
        else{
            $total_sum_insured = $modtlsumm->total_sum_insured;
            $sum_insured = $modtlsumm->sum_insured;
        }

        $net_discountLoad = $total_loadings - $total_discounts;
        $endorse_amount = $sectionEndorseSum +$net_discountLoad;
        $annual_premium = $sectionAnnualSum +$net_discountLoad;
   

        if($summaryExists){
            Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$reg_no)
                ->update([
                    'covertype' => $modtl->covertype,
                    'usage' => $modtl->usage,
                    'total_sum_insured' => $total_sum_insured,
                    'sum_insured' => $sum_insured,
                    'annual_premium' => $annual_premium,
                    'endorse_amount' => $endorse_amount,
                    'sys_endorse_amount' => $endorse_amount
                ]);
        }
        else{
            $modtlsumm = new Modtlsumm();
            $modtlsumm->policy_no = $dcontrol->policy_no;
            $modtlsumm->endt_renewal_no = $dcontrol->endt_renewal_no;
            $modtlsumm->reg_no = $reg_no;
            $modtlsumm->total_sum_insured = $total_sum_insured;
            $modtlsumm->sum_insured = $sum_insured;
            $modtlsumm->transeq_no = $dcontrol->transeq_no;
            $modtlsumm->class = $cls;
            $modtlsumm->covertype = $modtl->covertype;
            $modtlsumm->usage = $modtl->usage;
            $modtlsumm->annual_premium = $sectionAnnualSum;
            $modtlsumm->endorse_amount = $endorse_amount;
            // $modtlsumm->endorse_amount = $sys_endorse_amount;
            $modtlsumm->save();
        }
    }

    public function update_polmaster($endt_renewal_no)
    {
        $dcontrol = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->firstOrFail();
        static::endorsement_debited($endt_renewal_no,'PROCESS');

        //motor
        
        if($dcontrol->trans_type == 'CNC'){
            $active_reg_no = Modtlmast::where('endt_renewal_no',$endt_renewal_no)->where('deleted','<>','Y')->pluck('reg_no');
        }
        else if($dcontrol->trans_type == 'PTA'){
            $active_reg_no = Modtlhist::where('endt_renewal_no',$endt_renewal_no)->where('deleted','<>','Y')->pluck('reg_no');
        }
        else{
            $active_reg_no = Modtlmast::where('endt_renewal_no',$endt_renewal_no)->where('status','ACT')->pluck('reg_no');
        }
        
        $motor_tot_sum = Modtlsumm::where('endt_renewal_no',$endt_renewal_no)->whereIn('reg_no', $active_reg_no)->sum('total_sum_insured') ?? 0;
        $motor_sum = Modtlsumm::where('endt_renewal_no',$endt_renewal_no)->whereIn('reg_no', $active_reg_no)->sum('sum_insured') ?? 0;
        $motor_tot_prem = Modtlsumm::where('endt_renewal_no',$endt_renewal_no)->whereIn('reg_no', $active_reg_no)->sum('annual_premium') ?? 0;
        $motor_tot_endorse = Modtlsumm::where('endt_renewal_no',$endt_renewal_no)->whereIn('reg_no', $active_reg_no)->sum('endorse_amount') ?? 0;
        

        // non-motor
        $non_motor_tot_sum = Polsect::where('endt_renewal_no',$endt_renewal_no)->sum('total_sum_insured') ?? 0;
        $non_motor_sum = Polsect::where('endt_renewal_no',$endt_renewal_no)->sum('sum_insured') ?? 0;
        $non_motor_tot_prem = Polsect::where('endt_renewal_no',$endt_renewal_no)->sum('annual_premium') ?? 0;
        $non_motor_tot_endorse = Polsect::where('endt_renewal_no',$endt_renewal_no)->sum('endorse_amount') ?? 0;

        

        // totals
        $total_tot_sum = $motor_tot_sum + $non_motor_tot_sum;
        $total_sum = $motor_sum + $non_motor_sum;
        $total_prem = $motor_tot_prem + $non_motor_tot_prem;
        $total_endorse = $motor_tot_endorse + $non_motor_tot_endorse;

        $checkCombined = ClassModel::where('class',$dcontrol->class)->first()->combined;
        
        
        if($checkCombined == 'Y'){

            $vehicles = Modtlsumm::where('endt_renewal_no',$endt_renewal_no)->whereIn('reg_no', $active_reg_no)->get();

            foreach ($vehicles as $veh) {
                $cls = $veh->class;
                $class_sum = Modtlsumm::where('endt_renewal_no',$endt_renewal_no)
                    ->where('class',$cls)
                    ->sum('sum_insured');
                $class_tot_prem = Modtlsumm::where('endt_renewal_no',$endt_renewal_no)
                    ->where('class',$cls)
                    ->sum('annual_premium');
                $class_tot_endorse = Modtlsumm::where('endt_renewal_no',$endt_renewal_no)
                    ->where('class',$cls)
                    ->sum('endorse_amount');

                Polcmb::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                        ->where('class',$cls)
                        ->update([
                            'total_sum_insured' => $class_sum,
                            'sum_insured' => $class_sum,
                            'total_premium' => $class_tot_prem,
                            'premium' => $class_tot_prem,
                            'effective_premium' => $class_tot_endorse,
                            'endorse_amount' => $class_tot_endorse,
                        ]);
            }
        }
        // dd('checkCombined',$checkCombined,' motor_count',$motor_count,' non_motor_count',$non_motor_count);

        // else{
                Polmaster::where('endorse_no',$endt_renewal_no)
                    ->update([
                        'total_sum_insured' => $total_tot_sum,
                        'sum_insured' => $total_sum,
                        'annual_premium' => $total_prem,
                        'renewal_premium' => $total_prem,
                        'fire_premium' => $total_prem,
                        'endorse_amount' => $total_endorse,
                        'sys_endorse_amount' => $total_endorse,
                    ]);
                // }
        //ext to rfn or rfn to ext converter
        Policy_functions::extraToRefundEndorsement($endt_renewal_no);
    }

    public function modify_section(Request $request)
    {
        $request->validate([
            'cls' => 'required',
            'group' => 'required',
            'section' => 'required',
            'rate_amount' => 'required',
            'risk_value' => 'required',
        ]);

        DB::beginTransaction();
        try{
            static::endorsement_debited($request->endt_renewal_no,'PROCESS');

            $cls = $request->cls;
            $risk_value = str_replace(',','',$request->risk_value);

            $dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)->firstOrFail();
            $modtl = Modtlmast::where('policy_no',$dcontrol->policy_no)
                ->where('reg_no',$request->reg_no)
                ->first();
            
            $sect_dtl = Motcvrdet::where('policy_no',$dcontrol->policy_no)
                ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$request->reg_no)
                ->where('grp_code',$request->group)
                ->where('item_code',$request->section)
                ->first();
            if($sect_dtl->cancelled == 'Y'){
                throw new Exception("You can't modify section a cancelled section",403);
            }
            
            $this->_total_sum_insured = $modtl->premium_dtl->total_sum_insured;
            $this->_cls = $request->cls;
            $this->_reg_no = $request->reg_no;
            $this->_endt_renewal_no = $request->endt_renewal_no;
            $this->_seat_cap = $modtl->seat_cap;
            $this->_insured_seats = $modtl->insured_seats;

            $section = [
                'group' => $request->group,
                'item' => $request->section,
                'rate_amount' => $request->rate_amount,
                'risk_value' => $risk_value,
                'cancel' => 'N',
            ];

            $section['rate_amount'] = $this->get_minRateAmt($section);
            $premium_amounts = $this->compute_motor_premium($section,$dcontrol->ast_marker);
            $resp = $this->save_section_dtl($section,$premium_amounts);

            if($resp['status'] != 1){
                throw new Exception($resp['message'],403);
            }

            $this->update_motor_summary($dcontrol->endt_renewal_no,$cls,$request->reg_no);
            $this->update_polmaster($dcontrol->endt_renewal_no);

            DB::commit();

            return response()-> json([
                'status' => 1,
                'message' => 'Succesfully updated motor details',
                
            ]);
        }
        catch(\Throwable $e){
            DB::rollback();
             //dd($e);
            $error_msg = json_encode($e->getMessage());
			$reference = "endt_renewal_no: {$request->endt_renewal_no} reg_no {$request->reg_no}";
            $module = __METHOD__;
			$route_name = Route::getCurrentRoute()->getActionName();

			log_error_details($route_name,$error_msg,$reference,$module);

            $message = 'Failed to process motor';

            if($e->getCode() == 403){
                $message = $e->getMessage();
            }
            return response()-> json([
                'status' => 0,
                'message' => $message,
                
            ],$e->getCode);
        }
    }

    public function precompute_sec_premium(Request $request)
    {
        $request->validate([
            'cls' => 'required',
            'reg_no' => 'required',
            'endt_renewal_no' => 'required',
            'group' => 'required',
            'section' => 'required',
            'rate_amount' => 'required',
            // 'risk_value' => 'required',
        ]);
        
         
        try {
            $dcontrol = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->firstOrFail();
            $bustype = Bustype::where('type_of_bus', $dcontrol->type_of_bus)->first();
            $modtl = Modtlmast::motor_details($dcontrol->endt_renewal_no,$request->reg_no);
            $motorate = Motorsect::with('motprem_group')
                ->where('class',$request->cls)
                ->where('grp_code',$request->group)
                ->where('item_code',$request->section)
                ->first();
            
            $basicSection =  Motorprem_grp::where('basic_premium','Y')->first();

            // normalize data
            $risk_value = (float) str_replace(',', '', $request->get('risk_value'));
            $rate_amount = (float) str_replace(',', '', $request->get('rate_amount'));
            $request->merge(['preview' => 'Y']);
            
            $this->_cls = $request->cls;
            $this->_reg_no = $request->reg_no;
            $this->_endt_renewal_no = $request->endt_renewal_no;
            $this->_seat_cap = (int)$request->seat_cap;
            $this->_insured_seats = $request->insured_seats;
            $this->_manf_year = (int)$request->manf_year;
            $this->_preview = 'Y';

            $section = [
                'group' => $request->group,
                'item' => $request->section,
                'rate_amount' => $rate_amount,
                'risk_value' => $risk_value,
                'cancel' => 'N',
            ];

            $section['rate_amount'] = $this->get_minRateAmt($section);

            $premium_amounts = $this->compute_motor_premium($section,$dcontrol->ast_marker);
            
            if($basicSection->grp_code == $request->group){
                Cache::put($request->endt_renewal_no,$premium_amounts['endorse_amount'],now()->addMinutes(15));
            }

            // dd($premium_amounts);

            return [
                'status' => 1,
                'data' => $premium_amounts,
                'basic_premium' => Cache::get($request->endt_renewal_no),
                'message' => 'Successful'
            ];
        } catch (\Throwable $e) {
            // dd($e);
            return [
                'status' => 0,
                'message' => 'Failed to compute'
            ];
         }

    }

    public function premium_sections(Request $request)
    {
        $dcontrol = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->firstOrFail();
        $debited = static::endorsement_debited($dcontrol->endt_renewal_no);
        // $modtl = Modtlmast::motor_details($dcontrol->endt_renewal_no,$request->reg_no);
        $modtl = Modtlpivot::where('endt_renewal_no', $request->endt_renewal_no)
            ->where('reg_no',$request->reg_no)
            ->first();
            
        $actionable = true;
        if($debited){
            $actionable = false;
        }
        elseif($modtl->status != 'ACT'){
            $actionable = false;
        }

        $sections = Motcvrdet::with('motor_group')
            ->leftJoin('motorpolsec', function($join){
                $join->on('motcvrdet.endt_renewal_no','motorpolsec.endt_renewal_no');
                $join->on('motcvrdet.reg_no','motorpolsec.reg_no');
                $join->on('motcvrdet.grp_code','motorpolsec.grp_code');
                $join->on('motcvrdet.item_code','motorpolsec.item_code');
            })
            ->join('motorsect', function($join){
                $join->on('motcvrdet.class','motorsect.class');
                $join->on('motcvrdet.grp_code','motorsect.grp_code');
                $join->on('motcvrdet.item_code','motorsect.item_code');
            })
            ->where('motcvrdet.policy_no',$dcontrol->policy_no)
            ->where('motcvrdet.endt_renewal_no',$dcontrol->endt_renewal_no)
            ->where('motcvrdet.reg_no',$request->reg_no)
            ->get(['motcvrdet.*','motorpolsec.endorse_amount','motorsect.description AS item_description']);

        return DataTables::of($sections)
            ->editColumn('endorse_amount',function($data){
                return $data->endorse_amount ?? 0;
            })
            ->editColumn('endorse_amount',function($data){
                return $data->endorse_amount ?? 0;
            })
            ->editColumn('item_description',function($data) use ($actionable){
                return "{$data->item_code}-{$data->item_description}";
            })
            ->addColumn('action',function($data) use ($actionable){
                $btn = '';
                // vehicle not active
                if($actionable && $data->cancelled == 'N'){
                    $btn .= '<a href="#" id="premsect-update" data-item="' . $data->item_code . '" data-group="' . $data->grp_code . '" class="btn btn-xs btn-primary"><i class="fa fa-pencil"></i> Edit</a>';
                    $btn .= '&nbsp;&nbsp;';
                    if($data->motor_group->mandatory != 'Y'){
                        $btn .= '<a href="#" id="premsect-cancelrns" data-item="' . $data->item_code . '" 
                            data-group="' . $data->grp_code . '" data-type="CNC" class="btn btn-xs btn-danger">
                            <i class="fa fa-trash"></i> 
                            Cancel
                        </a>';
                    }
                }
                elseif($actionable && $data->cancelled == 'Y'){
                    $btn .= '<a href="#" id="premsect-cancelrns" data-item="' . $data->item_code . '" 
                        data-group="' . $data->grp_code . '" data-type="RNS" class="btn btn-xs btn-warning ">
                        <i class="fa fa-undo"></i> 
                        Reinstate
                    </a>';
                }
                else{
                    $btn .= '<a href="#" class="btn btn-xs btn-danger disabled"><i class="fa fa-ban"></i> Not Allowed</a>';
                }
                return $btn;
            })
            ->make(true);
    }

    public function discload_datatable(Request $request)
    {
        $debited =  static::endorsement_debited($request->endt_renewal_no);
        $modtl = Modtlmast::motor_details($request->endt_renewal_no,$request->reg_no);
            
        $actionable = true;
        if($debited){
            $actionable = false;
        }
        
        
        if($request->endt_flag <> "Y"){
            $dls = Discounts_loadings::query()
            ->where('endt_renewal_no',$request->endt_renewal_no)
            ->where('reg_no',$request->reg_no);

        }else{
            $dls = Discounts_loadings::query()
            ->where('endt_renewal_no',$request->endt_renewal_no)
            ->where('endt_discount',"Y");
            
        }
        

        return DataTables::of($dls)
            ->editColumn('type',function($data){
                $type = 'Discount';
                if ($data->type == 'L') {
                    $type = 'Loading';
                }
                return $type;
            })
            ->editColumn('endorse_amount',function($data){
                return $data->endorse_amount ?? 0;
            })
            ->addColumn('action',function($data) use ($actionable){
                $btn = '';
                // vehicle not active
                if($actionable && $data->deleted == 'N'){
                    $btn .= '<a href="#" id="dl-update" data-item="' . $data->item_no . '" data-type="' . $data->type . '" class="btn btn-xs btn-primary"><i class="fa fa-pencil"></i> Update</a>';
                    $btn .= '&nbsp;&nbsp;';
                }
                else{
                    $btn .= '<a href="#" class="btn btn-xs btn-danger disabled"><i class="fa fa-ban"></i> Not Allowed</a>';
                }
                return $btn;
            })
            ->make(true);
    }

    public function save_discountload(Request $request)
    {
     
        $endorsement_disc= $request->endorsement_disc;
        $validated = $request->validate([
            'name' => 'required',
            'description' => 'required',
            'type' => 'required',
            'principle' => 'required',

        ]);
        if($endorsement_disc == "Y"){
            return $this->save_endt_discountload($request);

        }else{
            # Motor discounts and loadings
            $endt_renewal_no = $request->get('endt_renewal_no');
            $reg_no = $request->get('reg_no');
            $cls = $request->get('cls');
            $addDL = $request->get('add_dl');
        
            $type = $request->get('type');
            $description = $request->get('description');
            $name = $request->get('name');
            $principle = $request->get('principle');
            $basis = $request->get('basis');
            $rate_amt = str_replace(',','',$request->get('rate_amt'));
            $sum_insured = str_replace(',','',$request->get('sum_insured'));
        
            $basic_premium = Motorpolsec::basic_premium($endt_renewal_no,$reg_no);
            $typeDL = $request->type;
            $deleteDL = $request->delete_dl;
            $item_no = $request->get('item_no');
            
                    
            DB::beginTransaction();
            try {
                static::endorsement_debited($request->endt_renewal_no,'PROCESS');

                if ($addDL == 'Y') {
                    # Add discounts and loadings
                    $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
                    $modtl = Modtlsumm::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                        ->where('reg_no', $reg_no)
                        ->first();

                    # Add the discounts and loadings
                    $nextCount = Discounts_loadings::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                        ->where('reg_no', $reg_no)
                        ->count();
                   

                    for ($i = 0; $i < count($typeDL); $i++) {
                    $exists = Discounts_loadings::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                                                ->where('reg_no', $reg_no)
                                                ->where('param_id', $name[$i])
                                                ->where('deleted','<>', 'Y')
                                                ->exists();
                        if ($exists) {
                            throw new Exception("$description[$i] Already exists on vehicle $reg_no ",403);
                        }

                        
                        if ($basis[$i] == 'R') {
                            if ($principle[$i] == 'A') {
                            $prem = ((float)$rate_amt[$i] * (float)$sum_insured) / 100;
                            }
                            if ($principle[$i] == 'C') {
                            $prem = ((float)$rate_amt[$i] * (float)$basic_premium) / 100;
                            }
                        } else {
                            $prem = ((float)$rate_amt[$i]);
                        }

                        $disc_load = new Discounts_loadings;
                        $nextCount = $nextCount + 1;
                
                        $disc_load->policy_no       = $dcontrol->policy_no;
                        $disc_load->endt_renewal_no = $dcontrol->endt_renewal_no;
                        $disc_load->reg_no          = $reg_no;
                        $disc_load->item_no         = $nextCount;
                        $disc_load->type            = $type[$i];
                        $disc_load->description     = $description[$i];
                        $disc_load->param_id     = (int)$name[$i];
                        $disc_load->principle       = $principle[$i];
                        $disc_load->basis           = $basis[$i];
                        $disc_load->compute_prem    = 'Y';
                        $disc_load->rate_amount     = $rate_amt[$i];
                        $disc_load->premium         = $prem;
                        $disc_load->endorse_amount  = $prem;
                        $disc_load->dtrans_no       = $dcontrol->dtrans_no;
                        $disc_load->save();
                    }
                    
                    $response = [
                        'status' => 1,
                        'type' => 'success',
                        'message' => 'Added successfully'
                    ];
                }
                else {
                    # Edit discounts and loadings
                    $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
                    $modtl = Modtlsumm::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                        ->where('reg_no', $reg_no)
                        ->first();

                    switch ($deleteDL) {
                        case 'Y':
                            
                            Discounts_loadings::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                                ->where('item_no', $item_no)
                                ->where('reg_no', $reg_no)
                                ->where('type', $typeDL)
                                ->update([
                                    'deleted' => 'Y',
                                    'endorse_amount' => 0,
                                ]);
                
                            $response = [
                                'status' => 1,
                                'type' => 'success',
                                'message' => 'Deleted successfully'
                            ];
                            break;
            
                        default:
                            # Edit the discount and loadings
                            if ($basis == 'R') {
                                if ($principle == 'A') {
                                    $dlAmount = ($rate_amt * $sum_insured) / 100;
                                }
                                
                                if ($principle == 'C') {
                                    $dlAmount = ($rate_amt * $basic_premium) / 100;
                                }
                            } else {
                                $dlAmount = ($rate_amt);
                            }
                
                            $rateAmount = $request->rate_amt;

                            $updateDL = Discounts_loadings::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                                ->where('item_no', $request->item_no)
                                ->where('type', $typeDL)
                                ->where('reg_no', $reg_no)
                                ->update([
                                    'description'     => $request->description,
                                    'principle'       => $request->principle,
                                    'basis'           => $request->basis,
                                    'premium'         => (float) $dlAmount,
                                    'endorse_amount'  => (float) $dlAmount,
                                    'rate_amount'     => str_replace(',', '', $rateAmount)
                                ]);
            
                            $response = [
                                'status' => 1,
                                'type' => 'success',
                                'message' => 'Successfully updated'
                            ];
                        break;
                    }
                }
                // validate
                $actualDiscounts = Discounts_loadings::where('endt_renewal_no', $endt_renewal_no)
                    ->where('type','D')
                    ->where('reg_no', $reg_no)
                    ->sum('endorse_amount');

                $actualLoadings = Discounts_loadings::where('endt_renewal_no',$endt_renewal_no)
                    ->where('type','L')
                    ->where('reg_no', $reg_no)
                    ->sum('endorse_amount');

                $sectionEndorseSum = Motorpolsec::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$reg_no)
                    ->sum('endorse_amount');
                
                $basicAndLoadings = (float) $sectionEndorseSum + (float) $actualLoadings;

                if($actualDiscounts > $basicAndLoadings){
                    # Endorse amount cannot be negative
                    $response = [
                        'status' => 0,
                        'type' => 'error',
                        'message' => 'Discounts cannot be higher than net premium'
                    ];
                }
                
                if($response['status'] != 1){
                    throw new Exception($response['message'],403);
                }

                $this->update_motor_summary($dcontrol->endt_renewal_no,$cls,$request->reg_no);
                $this->update_polmaster($dcontrol->endt_renewal_no);
                DB::commit();
                Session::flash($response['type'],$response['message']);
                return back();
            } catch (\Throwable $e) {
                DB::rollback();
                //dd($e);
                $msg = 'Failed to Save';
                if($e->getCode() == 403){
                    $msg = $e->getMessage();
                }
                Session::flash('error',$msg);
                return back();
            }

        }
        
       
        
    }
    public function save_endt_discountload($request)
    {
   
        $validated = $request->validate([
            'cars' => 'required|array|min:1'

        ]);

   
       
        # Motor discounts and loadings
        $endt_renewal_no = $request->get('endt_renewal_no');
        $reg_no = $request->get('cars');
        $cls = $request->get('cls');
        $addDL = $request->get('add_dl');
        $endt_flag = $request->get('endorsement_disc');
    
        $type = $request->get('type')[0];
        $description = $request->get('description')[0];
        $name = $request->get('name')[0];
        $principle = $request->get('principle')[0];
       
        $basis = $request->get('basis')[0];
        $rate_amt = str_replace(',','',$request->get('rate_amt'))[0];
        $sum_insured = str_replace(',','',$request->get('sum_insured'));
     
        
        $typeDL = $request->type;
        $deleteDL = $request->delete_dl;
        $item_no = $request->get('item_no');
        
                
        DB::beginTransaction();
            try {
                static::endorsement_debited($request->endt_renewal_no,'PROCESS');

                if ($addDL == 'Y') {
                    # Add discounts and loadings
                    $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
                   
                   

                    for ($i = 0; $i < count($reg_no); $i++) {
                        $basic_premium = Motorpolsec::basic_premium($endt_renewal_no,$reg_no[$i]);
                    
                        $exists = Discounts_loadings::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                                                    ->where('reg_no', $reg_no[$i])
                                                    ->where('param_id', $name)
                                                    ->where('deleted','<>', 'Y')
                                                    ->exists();
                                                    
                        if ($exists) {
                            throw new Exception("$description Already exists on vehicle $reg_no[$i] ",403);
                        }
                        $modtl = Modtlsumm::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                        ->where('reg_no', $reg_no[$i])
                        ->first();

                        # Add the discounts and loadings
                        $nextCount = Discounts_loadings::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                            ->where('reg_no', $reg_no[$i])
                            ->count();

                        
                        if ($basis == 'R') {
                            if ($principle == 'A') {
                            $prem = ((float)$rate_amt * (float)$sum_insured) / 100;
                            }
                            if ($principle == 'C') {
                            $prem = ((float)$rate_amt * (float)$basic_premium) / 100;
                            }
                        } else {
                            $prem = ((float)$rate_amt);
                        }
                       

                        $disc_load = new Discounts_loadings;
                        $nextCount = $nextCount + 1;
                
                        $disc_load->policy_no       = $dcontrol->policy_no;
                        $disc_load->endt_renewal_no = $dcontrol->endt_renewal_no;
                        $disc_load->reg_no          = $reg_no[$i];
                        $disc_load->item_no         = $nextCount;
                        $disc_load->type            = $type;
                        $disc_load->description     = $description;
                        $disc_load->param_id     = (int)$name;
                        $disc_load->principle       = $principle;
                        $disc_load->basis           = $basis;
                        $disc_load->compute_prem    = 'Y';
                        $disc_load->rate_amount     = $rate_amt;
                        $disc_load->premium         = $prem;
                        $disc_load->endorse_amount  = $prem;
                        $disc_load->dtrans_no       = $dcontrol->dtrans_no;
                        $disc_load->save();

                        $actualDiscounts = Discounts_loadings::where('endt_renewal_no', $endt_renewal_no)
                        ->where('type','D')
                        ->where('reg_no', $reg_no[$i])
                        ->where('param_id', (int)$name)
                        ->sum('endorse_amount');

                        $actualLoadings = Discounts_loadings::where('endt_renewal_no',$endt_renewal_no)
                            ->where('type','L')
                            ->where('reg_no', $reg_no[$i])
                            ->where('param_id', (int)$name)
                            ->sum('endorse_amount');

                        $sectionEndorseSum = Motorpolsec::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                            ->where('reg_no',$reg_no[$i])
                            ->sum('endorse_amount');
                        
                        $basicAndLoadings = (float) $sectionEndorseSum + (float) $actualLoadings;
                       
                        if((float)$actualDiscounts > $basicAndLoadings){
                            # Endorse amount cannot be negative
                            $response = [
                                'status' => 0,
                                'type' => 'error',
                                'message' => "Discounts cannot be higher than net premium for vehicle $reg_no[$i]"
                            ];
                            throw new Exception($response['message'],403);
                        }
                         
                     

                        $this->update_motor_summary($dcontrol->endt_renewal_no,$cls,$reg_no[$i]);
                        
                    }
                    
                    $response = [
                        'status' => 1,
                        'type' => 'success',
                        'message' => 'Added successfully'
                    ];
                }
                    
                
                $this->update_polmaster($dcontrol->endt_renewal_no);
                DB::commit();
                Session::flash($response['type'],$response['message']);
                return back();
            } catch (\Throwable $e) {
                DB::rollback();
                // dd($e);
                $msg = 'Failed to Save';
                if($e->getCode() == 403){
                    $msg = $e->getMessage();
                }
                Session::flash('error',$msg);
                return back();
            }
    }
    public function cancel_motor(Request $request)
    {
        DB::beginTransaction();
        
        try {
            
            $this->cancelMotorVehicle($request);

            DB::commit();

            Session::flash('success','Successfully canceled vehicle');

            return back();

        } catch (\Throwable $e) {

            DB::rollback();

            Session::flash('error','Failed to cancel vehicle');

            return back();

        }
    }

    public function cancelMotorVehicle(Request $request)
    {
        
        $request->validate([
            'cls' => 'required',
            'reg_no' => 'required',
            'endt_renewal_no' => 'required',
            'policy_no' => 'required',
            'zero_refund' => 'required',
            'cncreason' => 'required',
        ]);
        
       
        try {
            $cls = $request->cls;
            $dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)
                ->first(['policy_no','trans_type','endt_renewal_no','class','ast_marker']);

            $modtl = Modtlmast::where('policy_no',$request->policy_no)
                    ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$request->reg_no)
                    ->first();

            $this->_total_sum_insured = $modtl->premium_dtl->total_sum_insured;
            $this->_cls = $request->cls;
            $this->_reg_no = $request->reg_no;
            $this->_endt_renewal_no = $request->endt_renewal_no;
            $this->_seat_cap = $modtl->seat_cap;
            $this->_insured_seats = $modtl->insured_seats;

            $sections = Motcvrdet::with('motor_group')
                ->leftJoin('motorsect',function($join){
                    $join->on('motorsect.grp_code','motcvrdet.grp_code');
                    $join->on('motorsect.item_code','motcvrdet.item_code');
                })
                ->where('policy_no',$dcontrol->policy_no)
                ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('cancelled','<>','Y')
                ->where('reg_no',$request->reg_no)
                ->get(['motcvrdet.*','motorsect.basis']);

            // cancellation ,ode Prorata/short term
            if($request->cancel_mode == 'S'){
                $method = 'S';
            }
            elseif($request->cancel_mode == 'P'){
                $method = 'A';
            }

            foreach ($sections as $sect) {

                $section = [
                    'group' => $sect->grp_code,
                    'item' => $sect->item_code,
                    'rate_amount' => $sect->basis == 'R' ? $sect->rate : $sect->annual_premium,
                    'risk_value' => $sect->risk_value,
                    'cancel' => 'Y',
                ];

                $motor_grp = Motorprem_grp::where('grp_code',$sect->grp_code)->firstOrFail();
                if($motor_grp->refundable == 'Y' && $request->zero_refund == 'N'){
                    $refundAmts = $this->compute_motor_premium($section,$method);
                }
                else{
                    $refundAmts = [
                        'annual_premium' => $sect->annual_premium,
                        'premium_movt' => $sect->annual_premium,
                        'risk_value_movt' => $sect->annual_premium,
                        'endorse_amount' => 0
                    ];
                }

                $this->save_section_dtl($section,$refundAmts);
            }

            // cancel discounts/ loadings
            Discounts_loadings::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                                ->where('reg_no', $request->reg_no)
                                ->update([
                                    'endorse_amount' => 0,
                                    'deleted' => 'Y'
                                ]);

            $this->update_motor_summary($dcontrol->endt_renewal_no,$cls,$request->reg_no);

            $this->update_polmaster($dcontrol->endt_renewal_no);

            $del_veh = Modtlpivot::exist_in_previous($dcontrol->endt_renewal_no,$request->reg_no);

            // update modtlmast
            Modtlmast::where('policy_no',$request->policy_no)
                    ->where('reg_no',$request->reg_no)
                    ->update([
                        'status' => 'CNC',
                        'narration' => $request->cncreason,
                        'updated_by' => Auth::user()->user_name,
                        'deleted' => ($del_veh) ? 'Y' : 'N'
                    ]);


        } catch (\Throwable $e) {
            // log
            $error_msg = json_encode($e->getMessage());
			$reference = "endt_renewal_no: {$request->endt_renewal_no}";
            $module = __METHOD__;
			$route_name = Route::getCurrentRoute()->getActionName();

			log_error_details($route_name,$error_msg,$reference,$module);

            throw $e;
        }
    }

    public function remove_reinstate_motorsect(Request $request)
    {
        $request->validate([
            'cls' => 'required',
            'grp_code' => 'required',
            'item_code' => 'required',
            'reg_no' => 'required',
            'endt_renewal_no' => 'required',
            'actionType' => 'required',
        ]);
       
        DB::beginTransaction();
        try {
            $dcontrol = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->first();
            $customRequest = new Request([
                'cls' => $request->cls,
                'grp_code' => $request->grp_code,
                'item_code' => $request->item_code,
                'reg_no' => $request->reg_no,
                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                'actionType' => $request->actionType
            ]);

            $this->cancelReinstate_section($customRequest);

            DB::commit();
            return [
                'status' => 1,
                'message' => 'Successfully updated'
            ];
        }
        catch(\Throwable $e){
            DB::rollback();
            // dd($e);
            $message = 'Failed to update';
            if($e->getCode() == 403){
                $message = $e->getMessage();
            }
            
            return response()-> json([
                'status' => 0,
                'message' => $message,
            ],$e->getCode());
        }
    }

    public function cancelReinstate_section(Request $request)
    {
        $request->validate([
            'cls' => 'required',
            'grp_code' => 'required',
            'item_code' => 'required',
            'reg_no' => 'required',
            'endt_renewal_no' => 'required',
            'actionType' => 'required',
        ]);

        try {
            $cls = $request->cls;
            $dcontrol = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->first();
            static::endorsement_debited($dcontrol->endt_renewal_no,'PROCESS');

            $modtl = Modtlmast::where('policy_no',$dcontrol->policy_no)
                ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$request->reg_no)
                ->first();
            $modtlsum = Modtlsumm::where('policy_no',$dcontrol->policy_no)
                ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$request->reg_no)
                ->first();

            $sect_dtl = Motcvrdet::with('motor_group')
                ->leftJoin('motorsect',function($join){
                    $join->on('motorsect.grp_code','motcvrdet.grp_code');
                    $join->on('motorsect.item_code','motcvrdet.item_code');
                })
                ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('motcvrdet.grp_code',$request->grp_code)
                ->where('motcvrdet.item_code',$request->item_code)
                ->where('reg_no',$request->reg_no)
                ->first(['motcvrdet.*','motorsect.basis']);

            $this->_total_sum_insured = $modtlsum->total_sum_insured;
            $this->_cls = $request->cls;
            $this->_reg_no = $request->reg_no;
            $this->_endt_renewal_no = $request->endt_renewal_no;
            $this->_seat_cap = $modtl->seat_cap;
            $this->_insured_seats = $modtl->insured_seats;
            $section = [
                'group' => $sect_dtl->grp_code,
                'item' => $sect_dtl->item_code,
                'rate_amount' => $sect_dtl->basis == 'R' ? $sect_dtl->rate : $sect_dtl->annual_premium,
                'risk_value' => $sect_dtl->risk_value,
                'cancel' => 'N',//default
            ];

            $motor_grp = Motorprem_grp::where('grp_code',$sect_dtl->grp_code)->firstOrFail();

            $compute_prem = true;

            // cancel section
            if($request->actionType == 'CNC'){
                switch ($dcontrol->trans_type) {
                    case 'POL':
                    case 'REN':
                    case 'RNS':
                        $compute_prem = false;
                        break;
                    
                    default:
                        $compute_prem = true;
                        break;
                }

                $section['cancel'] = 'Y';
                if($motor_grp->refundable == 'Y' && $compute_prem){
                    $premiumAmts = $this->compute_motor_premium($section,$dcontrol->ast_marker);
                }
                else{
                    $premiumAmts = [
                        'annual_premium' => $sect_dtl->annual_premium,
                        'premium_movt' => $sect_dtl->annual_premium,
                        'risk_value_movt' => $sect_dtl->annual_premium,
                        'endorse_amount' => 0
                    ];
                }
            }
            // reinstate section
            else{
                $premiumAmts = $this->compute_motor_premium($section,$dcontrol->ast_marker);
            }

            $this->save_section_dtl($section,$premiumAmts);
            $this->update_motor_summary($dcontrol->endt_renewal_no,$cls,$request->reg_no);
            $this->update_polmaster($dcontrol->endt_renewal_no);

            return [
                'status' => 1,
                'message' => 'Successfully updated'
            ];
        }
        catch(\Throwable $e){
            // dd($e);
            throw $e;
        }
    }

    public function motor_datatable(Request $request)
    {
        $policy_no = $request->get('policy_no');
        $endt_renewal_no = $request->get('endt_renewal_no');
        $cls = $request->get('cls');
    
        $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->first();
    
        //access dcontrol using polmaster endorse_no
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $debited = static::endorsement_debited($dcontrol->endt_renewal_no);
    
        //Modtl::query()
        // $vehicles = Modtlmast::endorsement_vehicles($endt_renewal_no);
        $vehicles = Modtlpivot::vehicles($endt_renewal_no);

        // dd($vehicles);
        
        return datatables::of($vehicles)
            ->editColumn('status', function ($data) {
                $status = '';
                switch($data->status){
                case 'ACT':
                    $status .= '<div class="label label-success">';
                    break;
                default:
                    $status .= '<div class="label label-danger">';
                    break;
                }
                $status .= $data->motor_status->description;
                $status .= '</div>';

                return $status;
            })
            ->addColumn('endorse_amount', function ($modtl_rec) use ($dcontrol) {
                $modtlsumm = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$modtl_rec->reg_no)
                    ->first();
                return $modtlsumm->endorse_amount;
              })
            ->addColumn('sum_insured',function($data) use ($dcontrol){
                $modtlsumm = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$data->reg_no)
                    ->first();

                return $modtlsumm->sum_insured;
            })
            ->editColumn('usage',function($data) use ($dcontrol){

                return Motorsect::where('class',$dcontrol->class)
                            ->where('classtype', $data->usage)->get()[0]->description;

            })
            ->editColumn('covertype',function($data){

               return Covertype::where('cover', $data->covertype)->get()[0]->cover_description;

            })
            ->addColumn('annual_premium',function($data) use ($dcontrol){
                $modtlsumm = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$data->reg_no)
                    ->first();
                return $modtlsumm->annual_premium;
            })
            ->addColumn('action1', function ($data) use ($debited,$dcontrol) {
                // actionable
                $actionable = 'Y';
                $text = ' <i class="fa fa-eye"></i> View';

                if($debited){
                    $actionable = 'N';
                }
                elseif ($dcontrol->trans_type == 'CNC' || ($dcontrol->trans_type == 'RFN' && $dcontrol->ext_to_rfn == 'N' )  || $dcontrol->trans_type == 'PTA') {
                    $actionable = 'N';
                }
                elseif($data->status != 'ACT'){
                    $actionable = 'N';
                }

                if($actionable == 'Y'){
                    $text = ' <i class="fa fa-pencil-o"></i> View/Modify';
                }
                $route = route('motorprocessing.single_motor',['endt_renewal_no' => $data->endt_renewal_no,'cls' => $data->class,'reg_no' => $data->reg_no]);
                $route = e($route);
                return '<a class="btn btn-primary" style="color:white" href="'.$route.'">'.$text.'</a>';
                
            })
            ->addColumn('action2', function ($modtl_rec) use ($polmaster) {
                // $path = url('/edms/');
    
                if ($modtl_rec->log_book_img != null) {
                return '<button type="button" class="btn btn-default btn-xs btn-black btn-block logbook_preview"
                            data-document_type="'.$modtl_rec->log_book_mimetype.'"
                            data-document_path="'.$modtl_rec->log_book_img.'">
                            <i class="fa fa-eye"></i> Logbook 
                        </button>';
                } else {
                return '<button type="button" class="btn btn-default btn-xs btn-black btn-block" disabled="disabled">
                            <i class="fa fa-eye"></i> Logbook 
                        </button>';
                }
            })
            ->addColumn('detail_modal', function ($modtl_rec) {
                $btn = '<a class="btn btn-sm-default view-veh-details " title="View Risk Details" data-reg_no="'.$modtl_rec->reg_no.'"> 
                        <i title="View Risk Details" class="fa fa-eye"> </i>
                    </a>';
                return $btn;
            })
            ->addColumn('check', function ($row) {
                return '<p style="margin-right:10px"> <input type="checkbox" id="check_'.$row->endt_renewal_no.''.$row->reg_no.'" class="multicancel" data-endt-renewal-no="'.$row->endt_renewal_no.'" data-reg-no="'.$row->reg_no.'" onclick="multiCancelvehicle(`'.$row->endt_renewal_no.'`,`'.$row->reg_no.'`)"> </p>';
            })
            
            ->escapeColumns(['action'])
            ->make(true);

    }
    public function multicancel(Request $request){
        $vehicles =$request->data;
        $endt_no=$request->endt_renewal_no;
        $policy_no=$request->policy_no;
        $class=$request->class;
        $cnc_reason=$request->cnc_reason;
        $cancel_mode=$request->cancel_mode;
        $zero_refund=$request->zero_refund;
        

        
            DB::beginTransaction();
                try {
                    foreach ($vehicles as $key => $value) {
                     
                        $request = new Request();
                        $request->merge([
                            'reg_no' => $value['reg_no'],
                            'cls' => $class,
                            'policy_no' => $policy_no,
                            'endt_renewal_no' =>$endt_no,
                            'registration' => $value['reg_no'],
                            'zero_refund' => "Y",
                            'cncreason' => $cnc_reason,
                            'endorsement_no' => $endt_no,
                            'zero_refund'=>$zero_refund,
                            'cancel_mode'=>$cancel_mode

                        ]);
                      
                        $this->cancelMotorVehicle($request);
                       
                        
                    }
                  
                    DB::commit();
                    return[
                        'status'=>1,
                        'msg'=>'Vehicles Cancelled Successful'
                    ];
                } catch (\Throwable $th) {
                    DB::rollback();
                    throw $th;
                }
            
            
        
        
    }

    public function inactive_vehicles(Request $request)
    {
        $policy_no = $request->get('policy_no');
        $endt_renewal_no = $request->get('endt_renewal_no');
        $cls = $request->get('cls');
        $action_type = $request->action_type;
    
        //access dcontrol using polmaster endorse_no
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $debited = static::endorsement_debited($dcontrol->endt_renewal_no);
    
        //Modtl::query()
        // $vehicles = Modtlmast::endorsement_vehicles($endt_renewal_no);
        $vehicles = Modtlpivot::inactive_vehicles($endt_renewal_no);
        
        return datatables::of($vehicles)
            ->editColumn('status', function ($data) {
                $status = '';
                switch($data->status){
                case 'ACT':
                    $status .= '<div class="label label-success">';
                    break;
                default:
                    $status .= '<div class="label label-danger">';
                    break;
                }
                $status .= $data->motor_status->description;
                $status .= '</div>';

                return $status;
            })
            ->addColumn('endorse_amount', function ($modtl_rec) use ($dcontrol) {
                $modtlsumm = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$modtl_rec->reg_no)
                    ->first();
                return $modtlsumm->endorse_amount;
              })
            ->addColumn('sum_insured',function($data) use ($dcontrol){
                $modtlsumm = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$data->reg_no)
                    ->first();

                return $modtlsumm->sum_insured;
            })
            ->addColumn('annual_premium',function($data) use ($dcontrol){
                $modtlsumm = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$data->reg_no)
                    ->first();
                return $modtlsumm->annual_premium;
            })
            ->addColumn('action1', function ($data) use ($debited,$dcontrol,$action_type) {
                // actionable
                $actionable = 'Y';
                //$take_action = null;
                $text = ' <i class="fa fa-eye"></i> View';

                if($debited){
                    $actionable = 'N';
                }
                elseif($data->status != 'CNC'){
                    $actionable = 'N';
                }
                elseif ($dcontrol->trans_type == 'CNC' || ($dcontrol->trans_type == 'RFN' && $dcontrol->ext_to_rfn == 'N' )  || $dcontrol->trans_type == 'PTA') {
                    $actionable = 'N';
                }
                if($actionable == 'Y'){
                    $text = ' <i class="fa fa-pencil-o"></i> Reinstate';
                    $action_type = 'RNS';
                }
                $route = route('motorprocessing.single_motor',['endt_renewal_no' => $data->endt_renewal_no,'cls' => $data->class,'reg_no' => $data->reg_no,'action_type' => $action_type]);
                $route = e($route);
                return '<a class="btn btn-primary" style="color:white" href="'.$route.'">'.$text.'</a>';
                
            })
            ->addColumn('action2', function ($modtl_rec) {
                // $path = url('/edms/');
    
                if ($modtl_rec->log_book_img != null) {
                return '<button type="button" class="btn btn-default btn-xs btn-black btn-block logbook_preview"
                            data-document_type="'.$modtl_rec->log_book_mimetype.'"
                            data-document_path="'.$modtl_rec->log_book_img.'">
                            <i class="fa fa-eye"></i> Logbook 
                        </button>';
                } else {
                return '<button type="button" class="btn btn-default btn-xs btn-black btn-block" disabled="disabled">
                            <i class="fa fa-eye"></i> Logbook 
                        </button>';
                }
            })
            ->addColumn('detail_modal', function ($modtl_rec) {
                $btn = '<a class="btn btn-sm-default view-veh-details " title="View Risk Details" data-reg_no="'.$modtl_rec->reg_no.'"> 
                        <i title="View Risk Details" class="fa fa-eye"> </i>
                    </a>';
                return $btn;
            })
            ->escapeColumns(['action'])
            ->make(true);

    }

    public function motor_risk_datatable(Request $request)
    {
        $policy_no = $request->get('policy_no');
        $endt_renewal_no = $request->get('endt_renewal_no');
        $cls = $request->get('cls');
    
        $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->first();
    
        //access dcontrol using polmaster endorse_no
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $debited = static::endorsement_debited($dcontrol->endt_renewal_no);
    
        //Modtl::query()
        // $vehicles = Modtlmast::endorsement_vehicles($endt_renewal_no);
        $vehicles = Modtlpivot::query()->where('endt_renewal_no',$endt_renewal_no);
       
        return datatables::of($vehicles)
            ->editColumn('status', function ($data) {
                $status = Motorstatus::where('status_code',$data->status)->first();
                
                return $status->description; 
            })
            ->addColumn('endorse_amount', function ($modtl_rec) use ($dcontrol) {
                $modtlsumm = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$modtl_rec->reg_no)
                    ->first();
                return $modtlsumm->endorse_amount;
              })
            ->addColumn('annual_premium',function($data) use ($dcontrol){
                $modtlsumm = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$data->reg_no)
                    ->first();
                return $modtlsumm->annual_premium;
            })
            ->addColumn('action1', function ($data) use ($debited,$dcontrol) {
                // actionable
                $actionable = 'Y';
                if($debited){
                    $actionable = 'N';
                }
                elseif($data->status != 'ACT'){
                    $actionable = 'N';
                }
                $route = route('motorprocessing.single_motor',['endt_renewal_no' => $dcontrol->endt_renewal_no,'cls' => $data->class,'reg_no' => $data->reg_no]);
                $route = e($route);
                    return '<a class="btn  btn-info btn-sm" href="'.$route.'">
                            <i class="glyphicon glyphicon-edit"></i>
                            View/Modify
                        </a>';
                
            })
          ->addColumn('action2', function ($modtl_rec) use ($polmaster) {
            $path = url('/edms/');
  
  
            if ($modtl_rec->log_book_img != null) {
              return '<button type="button" class="btn btn-default btn-xs btn-black btn-block"
                        data-toggle="modal"
                        data-document_path="' . $path . '/' . $modtl_rec->log_book_file . '"
                        data-target="#view_logbook">
                        <i class="fa fa-eye"></i> Logbook 
                      </button>';
            } else {
              return '<button type="button" class="btn btn-default btn-xs btn-black btn-block" disabled="disabled">
                        <i class="fa fa-eye"></i> Logbook 
                      </button>';
            }
          })
          ->escapeColumns(['action'])
          ->make(true);

    }


    /** Check if transaction is already debited
     * 
     * @param string $endt_renewal_no Endorsement number
     * @param string $type determine if request is for rendering output 
     * or it's a request to process input
     * OPTIONS => ['RENDER','PROCESS']
     * 
     */
    public static function endorsement_debited($endt_renewal_no,$type='RENDER')
    {
        $dcontrol = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();
        
        $locked = false;
        if($dcontrol->trans_type == 'NIL' && $dcontrol->nil_code != 7){
            // if($dcontrol->committed == 'Y'){
                $locked = true;//true
            // }
        }
        else{
            $locked = Debitmast::where('endt_renewal_no',$endt_renewal_no)->exists();//true
        }

        throw_if($locked && $type != 'RENDER');

        return $locked;
    }

    public function cancel_motorSects($endt_renewal_no, $cnc_rfn_endt = null)
    {
        $dcontrol = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();
        $dcontrolPrev = Dcontrol::previous_endorsement($dcontrol->endt_renewal_no); 
        $debitdtl = Debitdtl::where('endt_renewal_no',$endt_renewal_no)->first();
        static::endorsement_debited($dcontrol->endt_renewal_no,'PROCESS');

        $refund_method = $dcontrol->refund_type;
        $cancel = 'N';

        if($refund_method == 'F'){
            $full_refund = 'Y';
        }
        elseif($dcontrol->trans_type == 'CNC'){
            $cnc_reason = Endorse_descr::where('descr_code',$debitdtl->detail_code)->first();
            $full_refund = $cnc_reason->full_refund;
            $cancel = 'Y';
        }
        
        $effective_date = Carbon::parse($dcontrol->effective_date);
        $period_from = Carbon::parse($dcontrol->cov_period_from);

        $days_covered = $effective_date->diffInDays($period_from);

        $vehicles = Modtlpivot::vehicles($dcontrol->endt_renewal_no);
        foreach ($vehicles as $veh) {
            $sections = Motcvrdet::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$veh->reg_no)
                ->get();
            foreach ($sections as $sect) {

                $active_endorsments = (!is_null($cnc_rfn_endt)) ? [$cnc_rfn_endt] : Dcontrol::activeCoverEndorsements($dcontrol->policy_no);

                $total_endorse_amt = Motorpolsec::where('policy_no',$dcontrol->policy_no)
                    ->where('reg_no',$veh->reg_no)
                    ->whereIn('endt_renewal_no',$active_endorsments)
                    ->where('grp_code',$sect->grp_code)
                    ->where('item_code',$sect->item_code)
                    ->sum('endorse_amount');
                
                $group = Motorprem_grp::where('grp_code',$sect->grp_code)->first();

                if($group->refundable == 'Y' || $full_refund == 'Y'){
                        
                    if($refund_method == 'F'){
                        $cnc_amount = $total_endorse_amt;
                    }
                    else if($refund_method == "S"){
                        $refund_rates = RefundRates::whereRaw("minimum_days <= '".$days_covered."' and maximum_days >= '".$days_covered."'")->first();
                        $cnc_rate = $refund_rates->percentage;
                            
                        if (!empty($cnc_rate) || $cnc_rate == 0) {
                            $retained_amount = ($cnc_rate * $total_endorse_amt) / 100;
                            $cnc_amount = $total_endorse_amt - $retained_amount; 
                        } else {
                            $cnc_amount = 0;
                        }
                    }
                    else if($refund_method == "P"){
                        $cnc_amount = (new Risk)->prorate($dcontrol->endt_renewal_no, $total_endorse_amt);
                    }

                    $cnc_amount *=-1;
                    $premium_amounts = [
                        'annual_premium' => $sect->annual_premium,
                        'premium_movt' => 0,
                        'risk_value_movt' => 0,
                        'endorse_amount' => $cnc_amount
                    ];
                }
                else{
                    $premium_amounts = [
                        'annual_premium' => $sect->annual_premium,
                        'premium_movt' => 0,
                        'risk_value_movt' => 0,
                        'endorse_amount' => 0
                    ];      
                }
                $this->_reg_no = $sect->reg_no;
                $this->_cls = $veh->class;
                $this->_endt_renewal_no = $dcontrol->endt_renewal_no;

                $section = [
                    'group' => $sect->grp_code,
                    'item' => $sect->item_code,
                    'rate_amount' => $sect->rate,
                    'risk_value' => $sect->risk_value,
                    'cancel' => $cancel,
                ];

                $resp = $this->save_section_dtl($section,$premium_amounts);
            }
            $this->update_motor_summary($dcontrol->endt_renewal_no,$veh->class,$veh->reg_no);
        }

        $this->update_polmaster($dcontrol->endt_renewal_no);

        return [
            'status' => 1,
            'message' => 'Successfully cancelled'
        ];
    }

    // fleet processing
    public function fleet(Request $request)
    {
  
      $schem = schemaName();
  
      $gb = $schem['gb'];
      $gl = $schem['gl'];
      $common = $schem['common'];
  
      $endt_renewal_no = $request->get('endorsement');
      $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
      $debited = static::endorsement_debited($dcontrol->endt_renewal_no);
      //check whether endt_renewal_no already exists in debitmast
      $class = $request->get('cls');
      $count = 0;
  
      $debitmast_recs = Debitmast::where('endt_renewal_no', $dcontrol->endt_renewal_no)->count();
  
  
      if ($dcontrol->trans_type == 'RFN' || $dcontrol->trans_type == 'CNC') {
  
        Session::flash('warning', 'Cannot add Fleet.Invalid transaction type ' . $dcontrol->trans_type . '.Kindly raise an extra endorsement to add fleet to policy');
  
        return redirect()->route('risk', ['endt_renewal_no' => $dcontrol->endt_renewal_no, 'policy_no' => $dcontrol->policy_no, 'cls' => $class]);
      } else {
      
  
        if ($debitmast_recs > 0) {
  
          Session::flash('warning', 'Cannot add Fleet.Endorsement number already debited.To add more vehicles to this policy, kindly raise an extra endorsement');
  
          return redirect()->route('risk', ['endt_renewal_no' => $dcontrol->endt_renewal_no, 'policy_no' => $dcontrol->policy_no, 'cls' => $class]);
        } else {
          $classtypes = Classtype::where('class', $request->cls)->get();
          $cover_type = DB::table('covertype')
            ->whereExists(function($query) use($class){
                $query->select('*')
                    ->from('motorsect')
                    ->where('motorsect.class',$class)
                    ->whereColumn('covertype.cover','motorsect.covertype');
            })
            ->where('covertype.active','Y')
            ->get();
          $make = Make::all();
  
          $motor_trade = ClassModel::where('class', $request->cls)->first('motor_trade');
          $motor_trade = $motor_trade->motor_trade;
          $vehicles = Modtlmast::where('endt_renewal_no', $endt_renewal_no)->count();
          $class_model = ClassModel::where("class", $class)->first();
          $vehiclemodelyear = Vehiclemodelyear::distinct()->get(['make']);
          $color=Vehcol::all();
          $motive = Motivepower::all();

          
        
        $premium_method = Motorpremmethod::where('active', 'Y')->first()->slug;

          
          return view('gb.underwriting.motor_fleet', [
            'vehicles' => $vehicles,
            'count' => $count,
            'class_model' => $class_model,
            'make' => $make,
            'motor_trade' => $motor_trade,
            'accessory' => '[]',
            'dcontrol' => $dcontrol,
            'cover_type' => $cover_type,
            'classtype' => $classtypes,
            'endt_renewal_no' => $endt_renewal_no,
            'class' => $class,
            'color' => $color,
            'motive' => $motive,
            'vehiclemodelyear'=>$vehiclemodelyear,
            'premium_method'=>$premium_method
  
          ]);
        }
      }
    }
    public function fleet_processing(Request $request){
        DB::beginTransaction();
        try {
            $endt_renewal_no = $request->input('endorsement_no');
            $class = $request->input('cls');
            $FileData = $request->file('csv_file');
            $fileExtension = $FileData->getClientOriginalExtension();
            $pipcnam = Pipcnam::where('record_type', 0)->first();
            //check and verify uploaded file type
            if($fileExtension == "csv" || $fileExtension == "xls" || $fileExtension == "xlsx"){
                // $excel_data = (readCSV($FileData));
                $excel_data = FileUploadManager::excelUpload($request->file('csv_file'));

            }
            else{
                Session::flash('error','Uploaded File Type Not Supported, Use a CSV, XLS or XLSX file! ');
                return redirect()->route('fleet_form', ['endorsement' => $endt_renewal_no, 'cls' => $class, 'count' => 0]);
            }
            $doctype = 'FLT';
            $fleet_serial = $this->GetSerial($doctype);
            $fleet_serial = str_pad((string) $fleet_serial->serial_no, 3, '0', STR_PAD_LEFT);
            $current_year = $this->current_year;
            $batchserial = $doctype.$fleet_serial.$current_year;
            //A list of all existing vehicle reg numbers
            $vehicle_reg_nos = [];
            $vehicle_chassis_no = [];
            $vehicle_engine_nos = [];
            $errorsCount = 0;
            $errorsList = array();
            $row = 0;
           #loop Excel data to do validations
            foreach ($excel_data as $value) {
            
                $row++;
                // $checker = $this->vehicle_checks($value,$endt_renewal_no,$row,$vehicle_reg_nos,$vehicle_chassis_no,$vehicle_engine_nos,$pipcnam,$request);
                $errorsList = $this->vehicle_checks($value, $endt_renewal_no, $row, $vehicle_reg_nos, $vehicle_chassis_no, $vehicle_engine_nos, $pipcnam, $request, $errorsCount, $errorsList);

                // if(!empty($checker)){
                //     $checker = json_encode($checker);
        
                // }else{
                //     $checker = '';
                // }
            }
 
            if (sizeof($errorsList) > 0) {
                $response = json_encode($errorsList);
                $response_array = json_decode($response);
                //IF ERRORS RETURN BACK
                Session::flash('errorsList', $errorsList);
                return redirect()->back()->with(['errorsList' => $errorsList]);
            }


            // Loop to add the vehicles where there is no error
            foreach($excel_data as $value){
                $covrtype=$request->cover_type;
                $usrge=$request->subclass;

                if (isset($value["sum_insured"]) && $value["sum_insured"] == 0 && in_array($value['cover_type'], [3, 4])) {
                    $sumInsured = CoverType::where('cover', $value['cover_type'])->value('minimum_sum_insured');
                } else {
                    $sumInsured = $value["sum_insured"] ?? null; // Handle cases where `sum_insured` might not be set
                }               

                if($pipcnam->cover_from_excel == "Y"){
                    $covrtype=$value['cover_type'];
                    $usrge=$value['usage'];
                }
                if ($request->rate_type == 'system') {
                    $rate_amount = $value['rate_amount'];
                    $rate_type = 'S';
                }else{
                    $rate_amount = $value['rate_amount'];
                    $rate_type = 'E';
                } 
                
                
                FleetStaging::Create([
                    'batch_no' => $batchserial,
                    'endt_renewal_no'  => $endt_renewal_no,
                    'OWNER' => $value["owner"],
                    'REG_NO' => $value["reg_no"],
                    'MAKE'=> $value["make"],
                    'MODEL'=> $value["model"],
                    'BODY_TYPE' => $value["body_type"],
                    'CUBIC_CAPACITY' => $value["cubic_capacity"],
                    'SUM_INSURED'=> $sumInsured,
                    'RATE_AMOUNT' => $rate_amount,
                    'RATE_TYPE'=>$rate_type,
                    'TRAILER' =>$value["trailer"],
                    'USAGE' =>$usrge,
                    'ENGINE_NO' =>$value["engine_number"],
                    'CHASSIS_NUMBER' =>$value["chassis_number"],
                    'YEAR' =>$value["year"],
                    'COVER_TYPE' => $covrtype,
                    'MOTIVE_POWER' =>$value["motive_power"],
                    'SEAT_CAP' => $value["seat_capacity"],
                    'COLOR' => $value["color"],
                    'METALLIC_COLOR' => $value["metallic_color"],
                    'PVT' => $value["political_violence"],
                    'TONNAGE' =>$value["tonnage"],
                    'TARRIF_CODE' =>$value["tarrif_code"],
                    'SEAT_LOADING' =>$value["seat_loading"],
                    'INSURED_SEATS' =>$value["insured_seats"],
                    'OCCUPANTS' =>$value["occupants"],
                    'OCCUPANTS_OPTION' =>$value["occupants_option"],
                    'PASSENGER_LOADING' =>$value["passenger_loading"],
                    'cancelled' => 'N',
                    'created_by' => Auth::User()->user_name,
                    'created_at' => Carbon::today(),
                    'error_log'=> $checker
                  ]);
            }
          
           
              
              $this->updateSerial($doctype);
              DB::commit();
              Session::flash('success', 'Upload done Successfully');
              return redirect()->back();
         

        } catch (\Throwable $th) {
            DB::rollback();
            dd($th);
        }

    }
    
    public function vehicle_checks($value,$endt_renewal_no,$row,&$vehicle_reg_nos,&$vehicle_chassis_no,&$vehicle_engine_nos,$pipcnam,$request, &$errorsCount, &$errorsList){
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();

        $premium_method = Motorpremmethod::where('active', 'Y')->first()->slug;

        if($pipcnam->cover_from_excel == "Y"){
            #check cover_type
             if(is_null($value['cover_type'])) {
               $errorsCount++;
               $errorsList[$errorsCount] = 'Enter Cover type in Row ' . $row;
             }else{
                $check_cover = Covertype::where('cover', trim($value['cover_type']))->exists();
               if(!$check_cover){
                   $errorsCount++;
                   $errorsList[$errorsCount] = 'Cover Type not found in in Row ' . $row.' check available ones or add this in parameters';
               }
               // dd($value['cover_type'], $check_cover);
             }

             #check usage
            
             if (is_null($value['usage'])) {
               $errorsCount++;
               $errorsList[$errorsCount] = 'Enter Usage in Row ' . $row;
             }else{
               $check_classtype = Classtype::where('class', trim($dcontrol->class))->where('classtype', (int)$value['usage'])->exists();
               if(!$check_classtype){
                   $errorsCount++;
                   $errorsList[$errorsCount] = 'Usage not found in in Row ' . $row.' check available ones or add this in parameters';
               }
               // dd($value['cover_type'], $check_cover);
             }


             if ($premium_method == 'rwanda-tarrif') {
                if (is_null($value['tarrif_code'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter tarrif code in Row ' . $row;
                }else{
                    $check_classtype = Vehicleclassification::where('subclass', (int)$value['usage'])->where('tarrif_code', (int)$value['tarrif_code'])->exists();
                    if(!$check_classtype){
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Tarrif not found in in Row ' . $row.' check available ones or add this in parameters';
                    }
                }

                
                if (is_null($value['seat_loading'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter seat loading in Row ' . $row;
                }else{
                    if($value['seat_loading'] != 'Y' && $value['seat_loading'] != 'N'){
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Invalid seat loading in Row ' . $row.' . use Y or N ';
                    }
                }

                
                if (is_null($value['passenger_loading'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter passenger loading in Row ' . $row;
                }else{
                    if($value['passenger_loading'] != 'Y' && $value['passenger_loading'] != 'N'){
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Invalid passenger loading in Row ' . $row.' . use Y or N ';
                    }
                }

                
                
                if (is_null($value['occupants'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter occupants in Row ' . $row;
                }else{
                    if($value['occupants'] != 'Y' && $value['occupants'] != 'N'){
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Invalid occupants in Row ' . $row.' . use Y or N ';
                    }
                }
                
                if (is_null($value['insured_seats'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter insured seats in Row ' . $row;
                }else{
                    if($value['seat_loading'] == 'Y' && $value['insured_seats'] < 1){
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Insured seats in Row ' . $row.' must be greater than 0';
                    }
                    if($value['seat_loading'] == 'Y' && $value['insured_seats'] > $value['seat_capacity']){
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Insured seats in Row ' . $row.' cannot be greater than seat capacity';
                    }
                }

                if (is_null($value['occupants_option']) && $value['occupants'] == 'Y') {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter occupants option in Row ' . $row;
                }else{
                    if($value['occupants'] == 'Y' && ($value['occupants_option'] != 'I' && $value['occupants_option'] != 'II' && $value['occupants_option'] != 'III' && $value['occupants_option'] != 'IV' && $value['occupants_option'] != 'V')){
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Invalid occupants option in Row ' . $row.' must be I, II, III, IV, V';
                    }
                }
             }

        }
        
        //check if there are duplicate reg numbers
        if (!in_array($value['reg_no'], $vehicle_reg_nos)) {
            array_push($vehicle_reg_nos, $value['reg_no']);
    
            if (!(new Risk)->validateRegex($value['reg_no'], 'VEH')) {
                $errorsCount++;
                $errorsList[$errorsCount] = 'Invalid Registration number format specified in excel document in Row ' . $row;
            }
        }
        else {
            $valid = 0;
    
            $errorsCount++;
            $errorsList[$errorsCount] = 'Duplicate Reg number excel document in Row ' . $row;
        }
        if ($request->rate_type == 'user') {
            $errorsCount++;
            if (is_null($value['rate_amount'])) {
                $errorsList[$errorsCount] = 'Enter Rate in Row ' . $row;
            }
        }
        $make=$value['make'];
        if (is_null($value['make']) ) {
            $errorsCount++;
            $errorsList[$errorsCount] = 'Enter Valid Make in Row ' . $row;
        }else{
            $mymake=Vehiclemodelyear::where('make', '=', strtoupper($make) )->get()[0]->make;
            $vehiclemake=$mymake;
            if(is_null($mymake)){
                $vehiclemake=$make;
                $errorsCount++;
                $errorsList[$errorsCount] = 'Make not found in in Row ' . $row;
            }
        }
        // if (is_null($value['seat_capacity'])) {
        //     $errorsCount++;
        //     $errorsList[$errorsCount] = 'Enter Seat capacity in Row ' . $row;
        // }
        if (is_null($value['sum_insured'])) {
            $errorsCount++;
            $errorsList[$errorsCount] = 'Enter sum insured ' . $row;
        }

        $model=$value['model'];
        if (is_null($value['model']) ) {
            $errorsCount++;
            
            $errorsList[$errorsCount] = 'Enter Model in Row ' . $row;
        }
        // else{
        
        //     //check with lowercase first
        //     $mymodel=Vehiclemodelyear::where('make','like',$vehiclemake)
        //         ->where('model', 'like', '%' . ucfirst(strtolower($model)). '%')
        //         ->get()[0]->model;
            
        //     //check with uppercase first
        //     if(is_null($mymodel)){
        //         $mymodel=Vehiclemodelyear::where('make','like',$vehiclemake)
        //             ->where('model', 'like', '%' . strtoupper($model). '%')
        //             ->get()[0]->model;
        //     }
        
        //     if(is_null($mymodel)){
        //         $errorsCount++;
        //         $errorsList[$errorsCount] = 'Model not found in in Row ' . $row;
        //         $mymodel= $vehiclemake;
        //     }
        // }

        if (is_null($value['body_type'])) {
            $errorsCount++;
            $errorsList[$errorsCount] = 'Enter Body Type in Row ' . $row;
        }

        $type_of_cover = Covertype::where('cover', trim($value['cover_type']))->first()->type_of_cover;
        // if (is_null($value['cubic_capacity'])) {
        //     if ($type_of_cover == 'T') {
        //         $errorsCount++;
        //         $errorsList[$errorsCount] = 'Enter Cubic Capacity in Row ' . $row;
        //     }
        // }

        // if (is_null($value['chassis_number'])) {
        //     if ($type_of_cover == 'T') {
        //         $errorsCount++;
        //         $errorsList[$errorsCount] = 'Enter Chassis in Row ' . $row;
        //     }
        // }

        // if (is_null($value['motive_power'])) {
        //     $errorsCount++;
        //     $errorsList[$errorsCount] = 'Enter Motive power in Row ' . $row;
        // }

        // if (is_null($value['tonnage'])) {
        //     $errorsCount++;
        //     $errorsList[$errorsCount] = 'Enter Tonnage in Row ' . $row;
        // }
        // if (is_null($value['color'])) {
        //     if ($type_of_cover == 'T') {
        //         $errorsCount++;
        //         $errorsList[$errorsCount] = 'Enter color in Row ' . $row;
        //     }
        // }
        if (is_null($value['year'])) {
            $errorsCount++;
            $errorsList[$errorsCount] = 'Enter Manufacture date in Row ' . $row;
        }
        if (is_null($value['owner'])) {
            $errorsCount++;
            $errorsList[$errorsCount] = 'Enter owner name in Row ' . $row;
        }
        

        if (is_null($value['trailer'])) {
            $errorsCount++;
            $errorsList[$errorsCount] = 'Enter Trailer Y for Yes and N for No in Row ' . $row;
        }
        
        // if (is_null($value['engine_number']) && $value['trailer'] == 'N') {
        //     $errorsCount++;
        //     $errorsList[$errorsCount] = 'Enter Engine Number in Row ' . $row;
        // }

        //Check if there is duplicate chasis number 
        // if (!in_array($value['chassis_number'], $vehicle_chassis_no)) {
        //     array_push($vehicle_chassis_no, $value['chassis_number']);
        // } else {
        //     $errorsCount++;
        //     $errorsList[$errorsCount] = 'Duplicate Chasis  number in excel document at Row ' . $row;
        // }

        // if (!in_array($value['engine_number'], $vehicle_engine_nos)) {
        //     array_push($vehicle_engine_nos, $value['engine_number']);
        // } else {

        //     $errorsCount++;
        //     $errorsList[$errorsCount] = 'Duplicate Engine  number in excel document at row ' . $row;
        // }
        if ($request->rate_type != 'system') {
            $rate_amount = $value['rate_amount'];
            if (is_null($rate_amount)) {   
                $errorsCount++;
                $errorsList[$errorsCount] = 'Rate/Amount not set in row ' . $row.' Confirm classtype and covertype';
            }
        }

        return $errorsList;




    }
    public function updateSerial($doctype) {
		$new_serial = Doctype::Where('doc_type',$doctype)->increment('serial_no', (int) '1');
		return $new_serial;
	}

    public function GetSerial($doctype) {
		$new_serial = Doctype::Where('doc_type',$doctype)->first();
		return $new_serial;
	}
    public function unprocessed_fleet_batch(Request $request){

        $endt_renewal_no = $request->get('endt_renewal_no');
        $cls = $request->get('cls');
    
        $motor_datatable = DB::select(" 
          SELECT endt_renewal_no,batch_no,created_at,created_by,cancelled,cancelled_at,cancelled_by FROM fleet_staging 
          where endt_renewal_no = '$endt_renewal_no' and processed <> 'Y'  GROUP BY endt_renewal_no,batch_no,created_at,created_by,
          cancelled,cancelled_at,cancelled_by
        ");
    
    
        return datatables::of($motor_datatable)
        ->addColumn('action', function ($motor_datatable) {
    
          $batch_status = FleetStaging::where('batch_no',$motor_datatable->batch_no)
                        ->where('error_log','<>',null)->count();
            return '<button class="btn  btn-default show_batch"
                  data-target="#modal_view_batch"
                  data-toggle="modal"
                  data-endorse-no = "'.$motor_datatable->endt_renewal_no.'"
                  data-batch-no = "'.$motor_datatable->batch_no.'"
                  data-cnc_status = "'.$motor_datatable->cancelled.'"
                  data-batchx = "'.$batch_status.'"
                  >
                    <i class="fa fa-eye"></i>View Vehicle Details </button>
                  ';
        })
        ->editColumn('date', function ($motor_datatable) {
            return (formatDate($motor_datatable->created_date));
        })
        
        ->editColumn('status', function ($motor_datatable) {
            if($motor_datatable->cancelled == 'Y'){
              return 'cancelled';
            }else{
              return 'unprocessed';
            }
        })
    
        ->escapeColumns(['action'])
        ->rawColumns(['action'])
        ->make(true);
    
    }
    public function batch_fleet_datatable(Request $request){
       
        $fleet = FleetStaging::where('endt_renewal_no',$request->endt_renewal_no)
                ->where('batch_no',$request->batch)
                ->get();
        return datatables::of($fleet)
          ->addColumn('action', function ($fleet) {
            if($fleet->cancelled == 'N' ){
            return '<a class="btn  btn-default update_single_veh_dets "
                  data-target="#modify_single_vehicle"
                  data-toggle="modal"
                  data-batch_no = "'.$fleet->batch_no.'"
                  data-endorse-no = "'.$fleet->endt_renewal_no.'"
                  data-ln_no = "'.$fleet->ln_no.'"
                  data-reg_no = "'.$fleet->reg_no.'"
                  > <i class="fa fa-edit"></i>Modify Details </a>
                  
                  <a class="btn btn-danger clear_motor_vehicle"
                    data-target="#remove_vehicle"
                    data-toggle="modal"
                    data-batch_no = "'.$fleet->batch_no.'"
                    data-endorse-no = "'.$fleet->endt_renewal_no.'"
                    data-ln_no = "'.$fleet->ln_no.'"
                    data-reg_no = "'.$fleet->reg_no.'"> <i class="fa fa-trash"> Remove </i> </a>
                  ';
            }else{
              return ' - ';
            }
        })
        ->addColumn('check', function ($fleet) {
          if($fleet->uploaded == 'N' && $fleet->cancelled == 'N' ){
  
            return '<input type="checkbox" name="selected[]" value="' . $fleet->ln_no . '"/>
                  <input type="hidden" name="batch_no[]" value="' . $fleet->batch_no . '"/>
                  <input type="hidden" name="endorse_no[]" value="' . $fleet->endt_renewal_no . '"/>';
          }else{
  
            return ' - ';
          }
            
        })
  
  
        ->addColumn('has_error', function ($fleet) {
  
          if($fleet->cancelled == 'Y'){
            return '<i class="fa fa-times" style="color:red"></i></button>
            ';
          }
          if(empty($fleet->error_log)){
            return '<i class="fa fa-check style="color:green""></i></button>
            ';
          }else{
            return '<i class="fa fa-times" style="color:red"></i></button>
            '; 
          }
         
        })
        ->escapeColumns(['action','has_error'])
        ->rawColumns(['action','has_error'])
        ->make(true);
  
    }
    public function cancel_fleet(Request $request){

        $endt_renewal_no = $request->fleet_endorse;
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $class = $dcontrol->class;
    
        try {
          //code...
          $fleet = FleetStaging::where('endt_renewal_no',$request->fleet_endorse)
          ->where('batch_no',$request->fleet_batch)
          ->update([
              'cancelled' =>'Y',
              'cancelled_by' =>Auth::user()->user_name,
              'cancelled_at' =>Carbon::now()
          ]);
    
          // Session::flash('success', 'Upload done Successfully');
          return 1;
        } catch (\Throwable $e) {
          return 0;
        }
    
    }
    public function fetchsinglerisk(Request $request){
        $endt_renewal_no = $request->endt_renewal_no;
        $reg_no=$request->reg_no;
        $batch_no=$request->batch_no;

        $risk = FleetStaging::where('endt_renewal_no',$endt_renewal_no)
                             ->where('batch_no',$batch_no)
                             ->where('reg_no',$reg_no)
                             ->first();
        return $risk;


    }
    public function delete_vehicles_from_fleet(Request $request){

        $endt_renewal_no = $request->endt_renewal_no;
        $batch_no = $request->batch_no;
        
    
        try {
    
            $delete= FleetStaging::where('endt_renewal_no',$endt_renewal_no )
                                    ->where('batch_no',$batch_no)
                                    ->delete();
          
    
          return 1;
        } catch (\Throwable $e) {
          DB::rollback();
          $codex = json_encode($e->getMessage());
                $error = explode('\n', $codex);
                $error_msg = $error[1];
                $referrence = $endt_renewal_no.$batch_no;
                $route_name = Route::getCurrentRoute()->getActionName();
                log_error_details($route_name,$error_msg,$referrence);
          Session::flash('error', 'Failed to Delete Vehicle From Batch');
          
          return 0;
        }
    
    }
    public function del_veh_fr_fleet(Request $request){

        try {
          //code...
          $fleet = FleetStaging::where('endt_renewal_no',$request->fleet_endorse)
                  ->where('batch_no',$request->fleet_batch)
                  ->where('reg_no',$request->reg_no)
                  ->delete();
          // Session::flash('success', 'Upload done Successfully');
          return 1;
        } catch (\Throwable $e) {
          return 0;
        }
    
      }
    public function modify_vehicle_fleet(Request $request){


        $value = array();
        $value = ($request->all());
    
        $endt_renewal_no = $request->fleet_endorse;
        $vehicle_reg_nos = [];
        $vehicle_chassis_no = [];
        $vehicle_engine_nos = [];
        $class = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();
        $rate_type = 'user';
        $pipcnam = Pipcnam::where('record_type', 0)->first();
        $row=1;
        $request->request->add(['rate_type'=>$rate_type,'class'=>$class->class]);
        
        
        // $data = $this->vehicle_checks($value,$endt_renewal_no,$row,$vehicle_reg_nos,$vehicle_chassis_no,$vehicle_engine_nos,$pipcnam,$request);
        $data = $this->vehicle_checks($value, $endt_renewal_no, $row, $vehicle_reg_nos, $vehicle_chassis_no, $vehicle_engine_nos, $pipcnam, $request, $errorsCount, $errorsList);

        
                        
       
        if(!empty($data)){
              ##update table with new error 
             
              $error = json_encode($data);
              
                $status = 1;
                $message=$error;
               

            
    
        }else{
              ##update table 
              $error = '';
              $status = 2;
              $message="";
    
        }

    
        try {

       
          FleetStaging::where('endt_renewal_no',$endt_renewal_no )
                ->where('reg_no',$request->original_reg_no)
                ->where('batch_no',$request->fleet_batch)
                ->update([
                    //   "owner" =>$request->owner,
                      "reg_no" =>$request->reg_no,
                      "make" =>$request->make,
                      "tonnage" =>$request->tonnage,
                      "model" =>$request->model,
                      "body_type" => $request->body_type,
                      "cubic_capacity" =>$request->cubic_capacity,
                      "sum_insured" => $request->sum_insured,
                      "rate_amount" => $request->rate_amount,
                      "trailer" =>  $request->trailer,
                      "usage" =>$request->usage,
                      "tarrif_code" =>$request->tarrif_code,
                      "engine_no" => $request->engine_number,
                      "chassis_number" => $request->chassis_number,
                      "year" => $request->year,
                      "cover_type" => $request->cover_type,
                      "motive_power" => $request->motive_power,
                      "seat_cap" => $request->seat_capacity,
                      "color" => $request->color,
                      "metallic_color" => $request->met_color,
                      "error_log" => $error,
                      "pvt"=>$request->pvt,
                      "seat_loading"=>$request->seat_loading,
                      "passenger_loading"=>$request->passenger_loading,
                      "occupants"=>$request->occupants,
                      "insured_seats"=>$request->insured_seats,
                      "occupants_option"=>$request->occupants_option
                ]);


    
            $batch_status = FleetStaging::where('batch_no',$request->fleet_batch)
                                         ->where('error_log','<>',null)
                                         ->count();

            return [
                'status'=>$status,
                'message'=>$message,
                'batch_status'=>$batch_status

            ];
    
        } catch (\Throwable $e) {
    
          DB::rollback();
        //   dd($e);
          $codex = json_encode($e->getMessage());
                $error = explode('\n', $codex);
                $error_msg = $error[1];
                $referrence = $endt_renewal_no.$request->fleet_batch.$request->reg_no ;
                $route_name = Route::getCurrentRoute()->getActionName();
                log_error_details($route_name,$error_msg,$referrence);
    
          $response = ['status' => 3,'message'=>'Failed to Update'];
          return $response;
    
    
        }
    }
    public function process_fleet_batches (Request $request){
        DB::beginTransaction();
        try {
            $errorsCount = 0;
            $errorsList = array();
            $row = 1;
            
            $dcontrol = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->first();
            $btyp = Bustype::where('type_of_bus', $dcontrol->type_of_bus)->first();
            $uw_parameters = UwParameter::first();
    
            $polmaster = Polmaster::where('policy_no', $dcontrol->policy_no)->first();
    
            $fleetrecs =FleetStaging::where('endt_renewal_no',$request->endt_renewal_no)
                                    ->where('batch_no',$request->batch_no)
                                    ->get();
            #loop through the vehicles
            foreach ($fleetrecs as $key => $value) {
    
                $reg_no = str_replace(' ', '', $value->reg_no);
                    
                    $vehicle_exists = $this->verify_reg_no(new Request([
                        'reg_no' => $reg_no,
                        'endt_renewal_no' => $dcontrol->endt_renewal_no
                    ]));

                    if($uw_parameters->verify_engine_no_on_fleet == 'Y'){

                        $engine_exist = $this->verify_engine_no(new Request([
                            'engine_no' => $value->engine_no,
                            'endt_renewal_no' => $dcontrol->endt_renewal_no
                        ]));
                    
                    }

                    if($uw_parameters->verify_chasis_no_on_fleet == 'Y'){

                        $chassis_exist = $this->verify_chassis_no(new Request([
                            'chassis_no' => $value->chassis_number,
                            'endt_renewal_no' => $dcontrol->endt_renewal_no
                        ]));

                    }
    
                    $reg_valid = (int) json_decode($vehicle_exists)->valid;

                    if($uw_parameters->verify_engine_no_on_fleet == 'Y'){
                        $eng_valid = (int) json_decode($engine_exist)->valid;
                        if ($eng_valid == 0) {
                            $errorsCount++;
                            $errorsList[$errorsCount] = 'Vehicle with Engine. No. '.$value->engine_no.' at row ' . $row. ' seems to be active in the system, Cancel or use another Engine No';
                        }
                    }
                    if($uw_parameters->verify_chasis_no_on_fleet == 'Y'){ 
                        $chassis_valid = (int) json_decode($chassis_exist)->valid;
                        if ($chassis_valid == 0) {

                            $errorsCount++;
                            $errorsList[$errorsCount] = 'Vehicle with Chassis. No. '.$value->chassis_number.' at row ' . $row. ' seems to be active in the system, Cancel or use another Chassis No';
                        }
                    }

    
                    if ($reg_valid == 0 ) {
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Vehicle with Reg. No. '.$reg_no.' at row ' . $row. ' seems to be active in the system, Cancel or use another Reg No';
    
                    }
    
                    #increment row
                    $row++;
             
            
            }
            if (sizeof($errorsList) > 0) {
                
                $response = json_encode($errorsList);
    
                $response_array = json_decode($response);
                 
                return [
                   'status'=>-1,
                   'msg'=> $response_array
                ];
                
                
            }
       
            foreach ($fleetrecs as $keyindex => $row) {
                $row = (object) $row->toArray();
                $reg_no = (string) str_replace(' ', '', $row->reg_no);
                $reg_no = strtoupper($reg_no);
                
                $total_sum_insured = $sum_insured = 0;

                $covrtype=$row->cover_type;
                $usrge=$row->usage;
                $tarrif=$row->tarrif_code;
                
                if($row->sum_insured != 0 ){
                    $total_sum_insured = $sum_insured = (float) str_replace(',','',$row->sum_insured);
                    
                    if (($dcontrol->co_insure == 'Y' && $btyp->leader == 'Y') || $btyp->facult_in == 'Y') {
                        $sum_insured = ( (float) $total_sum_insured * $dcontrol->company_share) / 100;
                    }
                }


    
                $motorates = Motorsect::basicSection($dcontrol->class,$covrtype,$usrge, $tarrif);
                
                if ($row->seat_loading == 'Y') {
                    $seat_sect = Motorsect::where('class',$dcontrol->class)
                            ->where('classtype', $usrge)
                            ->where('tarrif_code', $tarrif)
                            ->where('section_type', 'seat-loading')
                            ->get();

                    $motorates = $motorates->merge($seat_sect);
                    
                }

                if ($row->occupants == 'Y') {
                    $occ_sect = Motorsect::where('class',$dcontrol->class)
                            ->where('classtype', $usrge)
                            ->where('tarrif_code', $tarrif)
                            ->where('section_type', 'occupants')
                            ->where('occupant_option', $row->occupants_option)
                            ->get();
                    $motorates = $motorates->merge($occ_sect);
                    
                }

                

                if ($row->passenger_loading == 'Y') {
                    $pass_sect = Motorsect::where('class',$dcontrol->class)
                            ->where('classtype', $usrge)
                            ->where('tarrif_code', $tarrif)
                            ->where('section_type', 'passenger-loading')
                            ->get();

                    $motorates = $motorates->merge($pass_sect);

                    
                    
                }
                
                
                $item_no = Modtlmast::next_serial($dcontrol->policy_no);
               
                // begin persisting to database
                Modtlmast::create([
                    'item_no' => $item_no,
                    'policy_no' => $dcontrol->policy_no,
                    'transeq_no' => $dcontrol->transeq_no,
                    'endt_renewal_no' => $dcontrol->endt_renewal_no,
                    'client_number' => $dcontrol->client_number,
                    'class' => $dcontrol->class,
                    'reg_no' => $row->reg_no,
                    'usage' => $usrge,
                    'tarrif_code' => $tarrif,
                    // 'financed' => $row->financed,
                    // 'financier' => $row->financier,
                    'owner' => $row->owner,
                    'trailer' => $row->trailer,
                    'driver' => $row->driver,
                    'covertype' => $covrtype ,
                    'engine_no' => $row->engine_no,
                    'chassis_no' => $row->chassis_number,
                    'cubic_capacity' => $row->cubic_capacity,
                    'model' => $row->model,
                    'make' => $row->make, 
                    'bodytype' => $row->body_type, 
                    'manufacture_year' => $row->year,
                    'color' => $row->color,
                    'seat_cap' => $row->seat_cap,
                    'motive_power' => $row->motive_power,
                    'tonnage' => $row->tonnage,
                    'metallic_color' => $row->metallic_color,
                    'created_by' => Auth::user()->user_name,
                ]);
    
                $this->_total_sum_insured = $total_sum_insured;
                $this->_cls = $dcontrol->class;
                $this->_reg_no = $row->reg_no;
                $this->_endt_renewal_no = $dcontrol->endt_renewal_no;
                $this->_seat_cap = $row->seat_cap;
                $this->_insured_seats = $row->insured_seats;
                $this->_manf_year = $row->year;
                
                foreach ($motorates as $motorate) {

                    $rateAmount = $row->rate_type == 'E' ? $row->rate_amount : $motorate->rate_amount;
                    $section = [
                        'group' => $motorate->grp_code,
                        'item' => $motorate->item_code,
                        'rate_amount' => $rateAmount,
                        'risk_value' => $sum_insured,
                        'cancel' => 'N',
                    ];

                    if($uw_parameters->apply_min_rate_on_fleet == 'Y'){
                        $section['rate_amount'] = $this->get_minRateAmt($section);

                    }

                    $premium_amounts = $this->compute_motor_premium($section,$dcontrol->ast_marker);

                    if ($row->rate_type == 'E' && in_array($dcontrol->ast_marker,['S','T']) && $motorate->basis == 'A') {
                        $premium_amounts['endorse_amount'] = $row->rate_amount;
                    }
                    
                    $resp = $this->save_section_dtl($section,$premium_amounts);
                }
                
                if($row->pvt=="Y"){
                    $this->auto_add_pvt($dcontrol,$row->reg_no,$sum_insured,$covrtype,$usrge);
                    
                }
                
                $this->update_motor_summary($dcontrol->endt_renewal_no,$dcontrol->class,$row->reg_no);

                FleetStaging::where('endt_renewal_no',$request->endt_renewal_no)
                                    ->where('batch_no',$request->batch_no)
                                    ->update([
                                       'processed'=>'Y' 
                                    ]);
            }               
            $this->update_polmaster($dcontrol->endt_renewal_no);
            
            DB::commit();
        
            return[
                'status'=>1,
                'msg'=>"Fleet Saved Successful"
            ];
    
           
        } catch (\Throwable $th) {
            DB::rollback();
            dd($th->getMessage());
            Session::flash('error','Failed to Upload fleet');
            return[
                'status'=>0,
                'msg'=>"Failed"
            ];
        }

       


    }

    public function fleet_processing1(Request $request)
    {
     
        
        DB::beginTransaction();
        try{
            $endt_renewal_no = $request->input('endorsement_no');
            $class = $request->input('cls');
            $FileData = $request->file('csv_file');
            $fileExtension = $FileData->getClientOriginalExtension();
            $pipcnam = Pipcnam::where('record_type', 0)->first();
            
            //check and verify uploaded file type
            if($fileExtension == "csv" || $fileExtension == "xls" || $fileExtension == "xlsx"){
                // $excel_data = (readCSV($FileData));
                $excel_data = FileUploadManager::excelUpload($request->file('csv_file'));

            }
            else{
                Session::flash('error','Uploaded File Type Not Supported, Use a CSV, XLS or XLSX file! ');
                return redirect()->route('fleet_form', ['endorsement' => $endt_renewal_no, 'cls' => $class, 'count' => 0]);
            }
        
            //A list of all existing vehicle reg numbers
            $vehicle_reg_nos = [];
            $vehicle_chassis_no = [];
            $vehicle_engine_nos = [];
            $errorsCount = 0;
            $errorsList = array();
            $row = 0;

                        //fetch other details from dcontrol
            $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
    
            $btyp = Bustype::where('type_of_bus', $dcontrol->type_of_bus)->first();
    
            $polmaster = Polmaster::where('policy_no', $dcontrol->policy_no)->first();

            // return $excel_data;
            foreach ($excel_data as $value) {
                
                $row++;
               
                 
                 if($pipcnam->cover_from_excel == "Y"){
                     #check cover_type
                      if(is_null($value['cover_type'])) {
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Enter Cover type in Row ' . $row;
                      }else{
                         $check_cover = Covertype::where('cover', trim($value['cover_type']))->exists();
                        if(!$check_cover){
                            $errorsCount++;
                            $errorsList[$errorsCount] = 'Cover Type not found in in Row ' . $row.' check available ones or add this in parameters';
                        }
                        // dd($value['cover_type'], $check_cover);
                      }
    
                      #check usage
                     
                      if (is_null($value['usage'])) {
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Enter Usage in Row ' . $row;
                      }else{
                        $check_classtype = Classtype::where('class', trim($class))->where('classtype', (int)$value['usage'])->exists();
                        if(!$check_classtype){
                            $errorsCount++;
                            $errorsList[$errorsCount] = 'Usage not found in in Row ' . $row.' check available ones or add this in parameters';
                        }
                        // dd($value['cover_type'], $check_cover);
                      }

                 }
                 
                //check if there are duplicate reg numbers
                if (!in_array($value['reg_no'], $vehicle_reg_nos)) {
                    array_push($vehicle_reg_nos, $value['reg_no']);
            
                    if (!(new Risk)->validateRegex($value['reg_no'], 'VEH')) {
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Invalid Registration number format specified in excel document in Row ' . $row;
                    }
                }
                else {
                    $valid = 0;
            
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Duplicate Reg number excel document in Row ' . $row;
                }
        
                if ($request->rate_type == 'user') {
                    $errorsCount++;
                    if (is_null($value['rate_amount'])) {
                        $errorsList[$errorsCount] = 'Enter Rate in Row ' . $row;
                    }
                }
                $make=$value['make'];
                if (is_null($value['make']) ) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Valid Make in Row ' . $row;
                }else{
                    $mymake=Vehiclemodelyear::where('make', '=', strtoupper($make) )->get()[0]->make;
                    $vehiclemake=$mymake;
                    if(is_null($mymake)){
                        $vehiclemake=$make;
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Make not found in in Row ' . $row;
                    }
                }
                if (is_null($value['seat_capacity'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Seat capacity in Row ' . $row;
                }
                if (is_null($value['sum_insured'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter sum insured ' . $row;
                }
        
                $model=$value['model'];
                if (is_null($value['model']) ) {
                    $errorsCount++;
                    
                    $errorsList[$errorsCount] = 'Enter Model in Row ' . $row;
                }
                else{
                
                    //check with lowercase first
                    $mymodel=Vehiclemodelyear::where('make','like',$vehiclemake)
                        ->where('model', 'like', '%' . ucfirst(strtolower($model)). '%')
                        ->get()[0]->model;
                    
                    //check with uppercase first
                    if(is_null($mymodel)){
                        $mymodel=Vehiclemodelyear::where('make','like',$vehiclemake)
                            ->where('model', 'like', '%' . strtoupper($model). '%')
                            ->get()[0]->model;
                    }
                
                    if(is_null($mymodel)){
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Model not found in in Row ' . $row;
                        $mymodel= $vehiclemake;
                    }
                }
        
                if (is_null($value['body_type'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Body Type in Row ' . $row;
                }
        
                if (is_null($value['cubic_capacity'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Cubic Capacity in Row ' . $row;
                }
        
                if (is_null($value['chassis_number'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Chassis in Row ' . $row;
                }
        
                if (is_null($value['motive_power'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Motive power in Row ' . $row;
                }
        
                if (is_null($value['tonnage'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Tonnage in Row ' . $row;
                }
                if (is_null($value['color'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter color in Row ' . $row;
                }
        
                // if (is_null($value['metallic_color'])) {
                //     $errorsCount++;
                //     $errorsList[$errorsCount] = 'Enter Y for yes or N for No  in Row ' . $row;
                // }

        
                // if (is_null($value['trailer'])) {
                //     $errorsCount++;
                //     $errorsList[$errorsCount] = 'Enter Trailer Y for Yes and N for No in Row ' . $row;
                // }
        
                // if (is_null($value['classtype'])) {
                //     $errorsCount++;
                //     $errorsList[$errorsCount] = 'Enter Class type in Row ' . $row;
                // }else{
                //     ($request->rate_type == 'system') ? $classtypeCheck = $request->subclass : $classtypeCheck = $value['classtype'];
                //     $check_classtype = Classtype::where('class', trim($request->class))->where('classtype', $classtypeCheck)->get();
                //     if(count($check_classtype) < 1){
                //         $errorsCount++;
                //         $errorsList[$errorsCount] = 'Classtype not found in in Row ' . $row .' check available ones or add this in parameters';
                //     }
                // }
                
                // if (is_null($value['cover_type'])) {
                //     $errorsCount++;
                //     $errorsList[$errorsCount] = 'Enter Cover type in Row ' . $row;
                // }else{
                //     $check_cover = Covertype::where('cover', trim($value['cover_type']))->get();
                //     if(count($check_cover) < 1){
                //         $errorsCount++;
                //         $errorsList[$errorsCount] = 'Cover not found in in Row ' . $row.' check available ones or add this in parameters';
                //     }
                //     // dd($value['cover_type'], $check_cover);
                // }
        
                if (is_null($value['year'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Manufacture date in Row ' . $row;
                }

                if (is_null($value['trailer'])) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Trailer Y for Yes and N for No in Row ' . $row;
                }
                
                if (is_null($value['engine_number']) && $value['trailer'] == 'N') {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Enter Engine Number in Row ' . $row;
                }
        
                //Check if there is duplicate chasis number 
                if (!in_array($value['chassis_number'], $vehicle_chassis_no)) {
                    array_push($vehicle_chassis_no, $value['chassis_number']);
                } else {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Duplicate Chasis  number in excel document at Row ' . $row;
                }
        
                if (!in_array($value['engine_number'], $vehicle_engine_nos)) {
                    array_push($vehicle_engine_nos, $value['engine_number']);
                } else {
        
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Duplicate Engine  number in excel document at row ' . $row;
                }
            
                $reg_no = str_replace(' ', '', $value['reg_no']);
                
                $vehicle_exists = $this->verify_reg_no(new Request([
                    'reg_no' => $reg_no,
                    'endt_renewal_no' => $dcontrol->endt_renewal_no
                ]));
                
                $engine_exist = $this->verify_engine_no(new Request([
                    'engine_no' => $value['engine_number'],
                    'endt_renewal_no' => $dcontrol->endt_renewal_no
                ]));
                
                $chassis_exist = $this->verify_chassis_no(new Request([
                    'chassis_no' => $value['chassis_number'],
                    'endt_renewal_no' => $dcontrol->endt_renewal_no
                ]));

                $reg_valid = (int) json_decode($vehicle_exists)->valid;
                $eng_valid = (int) json_decode($engine_exist)->valid;
                $chassis_valid = (int) json_decode($chassis_exist)->valid;

                if ($reg_valid == 0 || $eng_valid == 0) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Vehicle with Reg. No. '.$reg_no.' at row' . $row. ' seems to be active in the system, Cancel or use another Reg No';

                }

                if ($eng_valid == 0) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Vehicle with Engine. No. '.$value['engine_number'].' at row' . $row. ' seems to be active in the system, Cancel or use another Engine No';

                }

                if ($chassis_valid == 0) {
                    $errorsCount++;
                    $errorsList[$errorsCount] = 'Vehicle with Chassis. No. '.$value['chassis_number'].' at row' . $row. ' seems to be active in the system, Cancel or use another Chassis No';

                }
                $motorate = Motorsect::basicSection($class,$request->cover_type,$request->subclass);
                if($pipcnam->cover_from_excel == "Y"){
                    $motorate = Motorsect::basicSection($class,$value['cover_type'],$value['usage']);
                }    
                
                if ($request->rate_type != 'system') {
                    $rate_amount = $value['rate_amount'];
                    if (is_null($rate_amount)) {   
                        $errorsCount++;
                        $errorsList[$errorsCount] = 'Rate/Amount not set in row ' . $row.' Confirm classtype and covertype';
                    }
                }
                //end of rate confirmation
            }
             
            if (sizeof($errorsList) > 0) {
            
                $response = json_encode($errorsList);

                $response_array = json_decode($response);
                //IF ERRORS RETURN BACK
                Session::flash('errorsList', $errorsList);
                return redirect()->route('motorprocessing.fleet', ['endorsement' => $endt_renewal_no, 'cls' => $class, 'count' => 0, 'errorsList' => serialize($errorsList)]); //->with('arr1'=>$errorsList);

            }
            
            // start processing
            foreach ($excel_data as $keyindex => $row) {
                $row = (object) $row->toArray();
                $reg_no = (string) str_replace(' ', '', $row->reg_no);
                $reg_no = strtoupper($reg_no);
                
                $total_sum_insured = $sum_insured = 0;
                $covrtype=$request->cover_type;
                $usrge=$request->subclass;

                if($pipcnam->cover_from_excel == "Y"){
                    $covrtype=$row->cover_type;
                    $usrge=$row->usage;
                }
                if((int)$covrtype != 3){
                    $total_sum_insured = $sum_insured = (float) str_replace(',','',$row->sum_insured);
                    
                    if (($dcontrol->co_insure == 'Y' && $btyp->leader == 'Y') || $btyp->facult_in == 'Y') {
                        $sum_insured = ( (float) $total_sum_insured * $dcontrol->company_share) / 100;
                    }
                }

                $motorate = Motorsect::basicSection($class,$covrtype,$usrge);
                if ($request->rate_type == 'system') {
                    $rate_amount = $request->rate;
                }else{
                    $rate_amount = $row->rate_amount;
                } 

                
                $item_no = Modtlmast::next_serial($dcontrol->policy_no);

                // begin persisting to database
                Modtlmast::create([
                    'item_no' => $item_no,
                    'policy_no' => $dcontrol->policy_no,
                    'transeq_no' => $dcontrol->transeq_no,
                    'endt_renewal_no' => $dcontrol->endt_renewal_no,
                    'client_number' => $dcontrol->client_number,
                    'class' => $request->cls,
                    'reg_no' => $row->reg_no,
                    'usage' => $usrge,
                    'tarrif_code' => $tarrif,
                    // 'financed' => $row->financed,
                    // 'financier' => $row->financier,
                    'owner' => $dcontrol->insured,
                    'trailer' => $row->trailer,
                    'driver' => $row->driver,
                    'covertype' => $covrtype ,
                    'engine_no' => $row->engine_number,
                    'chassis_no' => $row->chassis_number,
                    'cubic_capacity' => $row->cubic_capacity,
                    'model' => $row->model,
                    'make' => $row->make, 
                    'bodytype' => $row->body_type, 
                    'manufacture_year' => $row->year,
                    'color' => $row->color,
                    'seat_cap' => $row->seat_capacity,
                    'motive_power' => $row->motive_power,
                    'tonnage' => $row->tonnage,
                    'metallic_color' => $row->metallic_color,
                    'created_by' => Auth::user()->user_name,
                ]);

                $this->_total_sum_insured = $total_sum_insured;
                $this->_cls = $request->cls;
                $this->_reg_no = $row->reg_no;
                $this->_endt_renewal_no = $dcontrol->endt_renewal_no;
                $this->_seat_cap = $row->seat_capacity;
                $this->_insured_seats = $row->insured_seats;
                $section = [
                    'group' => $motorate->grp_code,
                    'item' => $motorate->item_code,
                    'rate_amount' => $rate_amount,
                    'risk_value' => $sum_insured,
                    'cancel' => 'N',
                ];
                $section['rate_amount'] = $this->get_minRateAmt($section);
                $premium_amounts = $this->compute_motor_premium($section,$dcontrol->ast_marker);
                $resp = $this->save_section_dtl($section,$premium_amounts);
                 if($row->political_violence=="Y"){
                    $this->auto_add_pvt($dcontrol,$row->reg_no,$sum_insured,$covrtype,$usrge);

                 }
                $this->update_motor_summary($dcontrol->endt_renewal_no,$class,$row->reg_no);
            }
            $this->update_polmaster($dcontrol->endt_renewal_no);
            
            DB::commit();

            return redirect()->route('risk', [
                'endt_renewal_no' => $dcontrol->endt_renewal_no,
                'policy_no' => $dcontrol->policy_no,
                'cls' => $class,
                'cmb' => 'N',
            ]);
        }
        catch(\Throwable $e){
            DB::rollback();
            Session::flash('error','Failed to Upload fleet');
            return back();
        }
    }
    public function auto_add_pvt($dcontrol,$reg_no,$sum_insured,$covrtype,$usrge){
        $modtl = Modtlmast::where('policy_no',$dcontrol->policy_no)
                                ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                                ->where('reg_no',$reg_no)
                                ->first();
        $modtlsum = Modtlsumm::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                                ->where('reg_no',$reg_no)
                                ->first();
        
      
        $motorprem_grp_categ = Motorprem_grp_categ::where('slug','political-violence-and-terrorism')->first();

        $motorate= Motorsect::where('grp_categ_id',$motorprem_grp_categ->id)
                                ->where('class',$dcontrol->class)
                                ->where('covertype',$covrtype)
                                ->where('classtype',$usrge)
                                ->firstOrFail();
        $this->_total_sum_insured = $sum_insured;
        $this->_cls =$dcontrol->class;
        $this->_reg_no = $reg_no;
        $this->_endt_renewal_no = $dcontrol->endt_renewal_no;
        $this->_seat_cap = $modtl->insured_seats;
        $this->_insured_seats = $modtl->insured_seats;

        $grp=$motorate->grp_code;
        $section=$motorate->item_code;
        $rate_amount=$motorate->rate_amount;

        
        $section = [
            'group' => $grp,
            'item' => $section,
            'rate_amount' => $rate_amount,
            'risk_value' => $sum_insured,
            'cancel' => 'N',
        ];
    
      
        $section['rate_amount'] = $this->get_minRateAmt($section);
        $premium_amounts = $this->compute_motor_premium($section,$dcontrol->ast_marker);
        $resp = $this->save_section_dtl($section,$premium_amounts);
         return $resp;

    }

    public function nil_motor_process(Request $request){
        $policy_no = $request->get('policy_no');
        $endt_renewal_no = $request->get('endt_renewal_no');
        $reg_no = $request->get('reg_no');

    
        $modtlmast = Modtlmast::where('policy_no', $policy_no)
                                ->where('reg_no', $reg_no)
                                ->where('endt_renewal_no', $endt_renewal_no)
                                ->first();


        $data = array();

        $audit1 = [
            'policy_no'=>$policy_no,
            'endt_renewal_no'=>$endt_renewal_no,
            'risk_item'=>$reg_no,
            'table_name'=>'modtlmast',
            'date_changed'=>Carbon::now('EAT'),
            'ip_address'=>$request->ip(),
            'system_user'=>Auth::user()->user_name
        ];
    
        if(trim($modtlmast->chassis_no) != trim($request->get('chasis'))){
            
            $audit1['field_changed'] = 'chassis no';
            $audit1['old_value'] = $modtlmast->chassis_no;
            $audit1['new_value'] = $request->get('chasis');

            $modtl['chassis_no'] = $request->get('chasis');
            array_push($data, $audit1);
    
        }
    
        if(trim($modtlmast->engine_no) != trim($request->get('engine'))){
            
            $audit1['field_changed'] = 'engine no';
            $audit1['old_value'] = $modtlmast->engine_no;
            $audit1['new_value'] = $request->get('engine');

            $modtl['engine_no'] = $request->get('engine');
            array_push($data, $audit1);
        }
    
        if($modtlmast->manufacture_year != (int)$request->get('manufacture_yr')){
            
            $audit1['field_changed'] = 'manufacture year';
            $audit1['old_value'] = $modtlmast->manufacture_year;
            $audit1['new_value'] = $request->get('manufacture_yr');
    
            
            $modtl['manufacture_year'] = $request->get('manufacture_yr');
            array_push($data, $audit1);
        }
    
        if(trim($modtlmast->make) != trim($request->get('v_make'))){
            
            $audit1['field_changed'] = 'vehicle make';
            $audit1['old_value'] = $modtlmast->make;
            $audit1['new_value'] = $request->get('v_make');
            
            $modtl['make'] = $request->get('v_make');
            array_push($data, $audit1);
        }
    
        if(trim($modtlmast->model) != trim($request->get('v_model'))){
            
            $audit1['field_changed'] = 'vehicle model';
            $audit1['old_value'] = $modtlmast->model;
            $audit1['new_value'] = $request->get('v_model');
            
            $modtl['model'] = $request->get('v_model');
            array_push($data, $audit1);
        }
    
        if(trim($modtlmast->bodytype) != trim($request->get('body_type'))){
            
            $audit1['field_changed'] = 'body type';
            $audit1['old_value'] = $modtlmast->bodytype;
            $audit1['new_value'] = $request->get('body_type');
    
            
            $modtl['bodytype'] = $request->get('body_type');
            array_push($data, $audit1);
        }
    
        if($modtlmast->cubic_capacity != (int)$request->get('cc')){

            $audit1['field_changed'] = 'Cubic capacity';
            $audit1['old_value'] = $modtlmast->cubic_capacity;
            $audit1['new_value'] = $request->get('cc');
    
            
            $modtl['cubic_capacity'] = $request->get('cc');
            array_push($data, $audit1);
        }
    
        if(trim($modtlmast->motive_power) != trim($request->get('motive_p'))){

            $audit1['field_changed'] = 'motive power';
            $audit1['old_value'] = $modtlmast->motive_power;
            $audit1['new_value'] = $request->get('motive_p');
    
            
            $modtl['motive_power'] = $request->get('motive_p');
            array_push($data, $audit1);
        }
    
        if($modtlmast->tonnage != (int)$request->get('tare_weight')){

            $audit1['field_changed'] = 'Carrying capacity';
            $audit1['old_value'] = $modtlmast->tonnage;
            $audit1['new_value'] = $request->get('tare_weight');
    
            
            $modtl['tare_weight'] = $request->get('tare_weight');
            array_push($data, $audit1);
        }
    
        if($modtlmast->tonnage != (int)$request->get('carry_cap')){

            $audit1['field_changed'] = 'Carrying capacity';
            $audit1['old_value'] = $modtlmast->tonnage;
            $audit1['new_value'] = $request->get('carry_cap');
    
            
            $modtl['tonnage'] = $request->get('carry_cap');
            array_push($data, $audit1);
        }
    
        if($modtlmast->seat_cap != (int)$request->get('seat_cap')){

            $audit1['field_changed'] = 'seating capacity';
            $audit1['old_value'] = $modtlmast->seat_cap;
            $audit1['new_value'] = $request->get('seat_cap');
    
            
            $modtl['seat_cap'] = $request->get('seat_cap');
            array_push($data, $audit1);
        }
    
        if(trim($modtlmast->color) != trim($request->get('color'))){

            $audit1['field_changed'] = 'color';
            $audit1['old_value'] = $modtlmast->color;
            $audit1['new_value'] = $request->get('color');

            $modtl['color'] = $request->get('color');
            array_push($data, $audit1);
        }
    
        if($modtlmast->metallic_color != trim($request->get('met_color'))){

            $audit1['field_changed'] = 'metallic color';
            $audit1['old_value'] = $modtlmast->metallic_color;
            $audit1['new_value'] = $request->get('met_color');
    
            
            $modtl['metallic_color'] = $request->get('met_color');
            array_push($data, $audit1);
        }
    
        if(trim($modtlmast->owner) != trim($request->get('owner'))){
            
            $audit1['field_changed'] = 'owner';
            $audit1['old_value'] = $modtlmast->owner;
            $audit1['new_value'] = $request->get('owner');
    
            
            $modtl['owner'] = $request->get('owner');
            array_push($data, $audit1);
        }
    
        if(trim($modtlmast->veh_condition) != trim($request->get('condition'))){
            
            $audit1['field_changed'] = 'vehicle condition';
            $audit1['old_value'] = $modtlmast->veh_condition;
            $audit1['new_value'] = $request->get('condition');
    
            
            $modtl['veh_condition'] = $request->get('condition');
            array_push($data, $audit1);
        }
    
        if(trim($modtlmast->driver) != trim($request->get('driver'))){
            
            $audit1['field_changed'] = 'driver';
            $audit1['old_value'] = $modtlmast->driver;
            $audit1['new_value'] = $request->get('driver');
    
            
            $modtl['driver'] = $request->get('driver');
            array_push($data, $audit1);
        }
    
        if(trim($modtlmast->valuation) != trim($request->get('valuation'))){
            
            $audit1['field_changed'] = 'valuation';
            $audit1['old_value'] = $modtlmast->valuation;
            $audit1['new_value'] = $request->get('valuation');
    
            
            $modtl['valuation'] = $request->get('valuation');
            array_push($data, $audit1);
        }
    
        if(trim($modtlmast->valuer) != trim($request->get('valuer'))){
            
            $audit1['field_changed'] = 'valuer';
            $audit1['old_value'] = $modtlmast->valuer;
            $audit1['new_value'] = $request->get('valuer');
    
            
            $modtl['valuer'] = $request->get('valuer');
            array_push($data, $audit1);
        }

        if(trim($modtlmast->logbook_no) != trim($request->get('log_book_no'))){
            
            $audit1['field_changed'] = 'Log book no';
            $audit1['old_value'] = $modtlmast->logbook_no;
            $audit1['new_value'] = $request->get('log_book_no');
    
            
            $modtl['logbook_no'] = $request->get('log_book_no');
            array_push($data, $audit1);
        }

        // dd($data);

        Modtlmast::where('policy_no', $policy_no)
                    ->where('reg_no', $reg_no)
                    ->where('endt_renewal_no', $endt_renewal_no)
                    ->update($modtl);
    
        DB::table('risk_audit_trail')->insert($data);
    
    
        return redirect()->route('policy_functions', ['endt_renewal_no' => $endt_renewal_no]);
    }
    
    public function update_sum_insured(Request $request)
    {
        $request->validate([
            'reg_no' => 'required',
            'endt_renewal_no' => 'required',
            'cls' => 'required',
            'sum_insured' => 'required',
        ]);

        DB::beginTransaction();
        try{
            $dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)->firstOrFail();
            $modtl = Modtlmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$request->reg_no)
                ->first();

            $total_sum_insured = (float) str_replace(',', '', $request->get('sum_insured'));
            $this->_total_sum_insured = $total_sum_insured;
            $this->_cls = $request->cls;
            $this->_reg_no = $request->reg_no;
            $this->_endt_renewal_no = $request->endt_renewal_no;
            $this->_seat_cap = $modtl->_seat_cap;
            $this->_insured_seats = $modtl->insured_seats;
                
            $resp = $this->recompute_premiums();
            if($resp['status'] != 1){
                throw new Exception("Error Processing Request", $resp['status_code']);
            }
            
            DB::commit();
            Session::flash('success','Successfully updated sum insured');
            return back();
        

        }
        catch(\Throwable $e){
            DB::rollBack();
            // dd($e);
            Session::flash('error','Failed to update sum insured');
            return back();
        }
        
    }

    public function recompute_premiums()
    {
    
        $dcontrol = Dcontrol::where('endt_renewal_no',$this->_endt_renewal_no)->firstOrFail();
        $dcontrolPrev = Dcontrol::previous_endorsement($dcontrol->endt_renewal_no);
        static::endorsement_debited($dcontrol->endt_renewal_no,'PROCESS');
        $bustype = Bustype::where('type_of_bus', $dcontrol->type_of_bus)->first();    
        $modtl = Modtlmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)
            ->where('reg_no',$this->_reg_no)
            ->first();     
        
        $sum_insured = $this->_total_sum_insured ;
        if (($dcontrol->co_insure == 'Y' && $bustype->leader == 'Y') || $bustype->facult_in == 'Y') {
            $sum_insured = ($this->_total_sum_insured * $dcontrol->company_share) / 100;
        }
        // cover type not TPO
        if($modtl->cover_type != 3){
            $mand_sects = Motcvrdet::join('motorprem_grp','motorprem_grp.grp_code','=','motcvrdet.grp_code')
                ->leftJoin('motorsect',function($join){
                    $join->on('motorsect.grp_code','motcvrdet.grp_code');
                    $join->on('motorsect.item_code','motcvrdet.item_code');
                })
                ->where('motcvrdet.endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('motcvrdet.reg_no',$this->_reg_no)
                ->where('motorprem_grp.basic_premium','Y')
                ->where('motorprem_grp.status','ACTIVE')
                ->get(['motcvrdet.*','motorsect.basis','motorsect.rate_basis']);

            $other_sects = Motcvrdet::join('motorprem_grp','motorprem_grp.grp_code','=','motcvrdet.grp_code')
                ->leftJoin('motorsect',function($join){
                    $join->on('motorsect.grp_code','motcvrdet.grp_code');
                    $join->on('motorsect.item_code','motcvrdet.item_code');
                })
                ->where('motcvrdet.endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('motcvrdet.reg_no',$this->_reg_no)
                ->where('motorprem_grp.basic_premium','<>','Y')
                ->where('motorprem_grp.status','ACTIVE')
                ->get(['motcvrdet.*','motorsect.basis','motorsect.rate_basis']);

            foreach ($mand_sects as $sect) {

                $risk_value = $sect->risk_value;
                if($sect->rate_basis == 'S'){
                    $risk_value = $sum_insured;
                }

                $section = [
                    'group' => $sect->grp_code,
                    'item' => $sect->item_code,
                    'rate_amount' => $sect->basis == 'R' ? $sect->rate : $sect->annual_premium,
                    'risk_value' => $risk_value,
                    'cancel' => $sect->cancelled,
                ];
                $section['rate_amount'] = $this->get_minRateAmt($section);
                $premium_amounts = $this->compute_motor_premium($section,$dcontrol->ast_marker);
                $resp = $this->save_section_dtl($section,$premium_amounts);
            }

            foreach ($other_sects as $sect) {

                $risk_value = $sect->risk_value;
                if($sect->rate_basis == 'S'){
                    $risk_value = $sum_insured;
                }
                elseif($sect->rate_basis == 'P'){
                    $risk_value = Motorpolsec::basic_premium($dcontrol->endt_renewal_no,$this->_reg_no);
                }
                $section = [
                    'group' => $sect->grp_code,
                    'item' => $sect->item_code,
                    'rate_amount' => $sect->basis == 'R' ? $sect->rate : $sect->annual_premium,
                    'risk_value' => $risk_value,
                    'cancel' => $sect->cancelled,
                ];
    
                $prevSect = Motcvrdet::where('policy_no',$dcontrol->policy_no)
                    ->where('endt_renewal_no',$dcontrolPrev->endt_renewal_no)
                    ->where('reg_no',$this->_reg_no)
                    ->where('grp_code',$section['group'])
                    ->where('item_code',$section['item'])
                    ->first();
                    
                // if no change in annual premium from prev endorsement don't recompute premium
                $recompute = false;
                switch ($dcontrol->trans_type) {
                    case 'POL':
                    case 'REN':
                    case 'RNS':
                        $recompute = true;
                        break;
                    
                    default:
                        if($prevSect->annual_premium != $sect->annual_premium){
                            $recompute = true;
                        }
                        break;
                }

                if($recompute){
                    $section['rate_amount'] = $this->get_minRateAmt($section);
                    $premium_amounts = $this->compute_motor_premium($section,$dcontrol->ast_marker);
                    $resp = $this->save_section_dtl($section,$premium_amounts);
                }
            }

            // discounts & loadings
            $basicPremium = Motorpolsec::basic_premium($dcontrol->endt_renewal_no,$this->_reg_no);
            $discLoadings = Discounts_loadings::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('deleted' ,'<>','Y')
                ->whereIn('principle' ,['A','C'])
                ->get();

            foreach ($discLoadings as $item) {
                if ($item->basis == 'R') {
                    if ($item->principle == 'A') {
                        $dscAmount = ($item->rate_amount * $sum_insured) / 100;
                    }
                    if ($item->principle == 'C') {
                        $dscAmount = ($item->rate_amount * $basicPremium) / 100;
                    }
                } else {
                    $dscAmount = ($item->rate_amount);
                }
        
                Discounts_loadings::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$this->_reg_no)
                    ->where('item_no',$item->item_no)
                    ->update([
                        'premium' => $dscAmount,
                        'endorse_amount' => $dscAmount
                    ]);
            }

            $this->update_motor_summary($dcontrol->endt_renewal_no,$this->_cls,$this->_reg_no);
            $this->update_polmaster($dcontrol->endt_renewal_no);

            return [
                'status' => 1,
                'message' => 'Succesfully updated Sum Insured',
            ];
        }
    }

    public function get_basic_rate(Request $request)
    {
        try{
            $motorate = Motorsect::basicSection($request->class,$request->cover_type,$request->subclass);

            return [
                'status' => 1,
                'data' => $motorate,
                'message' => 'Successful'
            ];
        }
        catch(\Throwable $e){
            // dd($e);
            return [
                'status' => 0,
                'message' => 'failed to load rate'
            ];
        }
    }

    public function externalPol_vehDtl(Request $request)
    {
        $reg_no = $request->reg_no;
        $endt_no = $request->endt_renewal_no;

        try{
            $dcontrol = Dcontrol::where('endt_renewal_no',$endt_no)->first();
            $taxInv = SmartTaxInvoices::where('customer_tax_invoice',$dcontrol->external_pol_no)->first();
            
            $veh_dtl = SmartCoverNotes::where('debit_note',$taxInv->debit_note)
                ->where('reg_no',$reg_no)
                ->first();

            return json_encode([
                'vehicle' => $veh_dtl,
                'status' => 1,
            ]);
        }
        catch(\Throwable $e){
            // dd($e);
            return json_encode([
                'status' => 0,
            ]);
        }
    }

    public function reinstate_vehicle(Request $request)
    {
        DB::beginTransaction();
        $request->validate([
            'cls' => 'required',
            'reg_no' => ['required',new RegNumberRule()],
            'endt_renewal_no' => 'required',
        ]);
        try{

            $dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)->firstOrFail();
            $debited = static::endorsement_debited($dcontrol->endt_renewal_no);

            // validate chassis
                
            $modtlmast = Modtlmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$request->reg_no)
                ->first();

            $chassis_exist = $this->verify_chassis_no(new Request([
                'chassis_no' => $modtlmast->reg_no,
                'endt_renewal_no' => $dcontrol->endt_renewal_no
            ]));

            $engine_exist = $this->verify_engine_no(new Request([
                'chassis_no' => $modtlmast->engine_no,
                'endt_renewal_no' => $dcontrol->endt_renewal_no
            ]));

            $eng_valid = (int) json_decode($engine_exist)->valid;
            $chassis_valid = (int) json_decode($chassis_exist)->valid;

            $reg_no = $request->reg_no;

            $errorsCount = 0;
            $errorBag = new MessageBag();

            if ($chassis_valid == 0) {
                $errorsCount++;
                $errorBag->add('chassis number','Chassis number for this vehicle seems to be active in the system');
            }
            if ($eng_valid == 0) {
                $errorsCount++;
                $errorBag->add('Engine number','Engine number for this vehicle seems to be active in the system');
            }

            if($errorsCount > 0){
                Session::flash('error','Data validation failed');
                return back()->withErrors($errorBag);
            }
            
            Modtlmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$request->reg_no)
                ->update([
                    'status' => 'ACT',
                    'updated_by' => Auth::user()->user_name,
                    'deleted' => 'N'
                ]);

            $motorSect = Motcvrdet::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('reg_no',$request->reg_no)
                ->get();
                
            foreach($motorSect as $sect){
                $customRequest = new Request([
                    'cls' => $sect->class,
                    'grp_code' => $sect->grp_code,
                    'item_code' => $sect->item_code,
                    'reg_no' => $sect->reg_no,
                    'endt_renewal_no' => $dcontrol->endt_renewal_no,
                    'actionType' => 'RNS'
                ]);

                $this->cancelReinstate_section($customRequest);
            }

            DB::commit();

            return [
                'status' => 1,
                'message' => 'Successfully Reinstated vehicle'
            ];
        }
        catch(Throwable $e){
            DB::rollback();
            // dd($e);
            return [
                'status' => 1,
                'message' => 'Failed to reinstate vehicle'
            ];
        }
    }

    public function pol_to_pta($endt_renewal_no)
    {
        $dcontrol = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->firstOrFail();
        $class = ClassModel::where('class',$dcontrol->class)->firstOrFail();
        $debited = static::endorsement_debited($dcontrol->endt_renewal_no,'PROCESS');

        /** change POL to PTA for COMESA businesses being underwritten as new policies
        * This is a temporary feature to enable booking COMESA for Vehicles in old aims
        **/
        switch((int)$class->classaims){
            case 76:
                if($dcontrol->trans_type == 'POL'){
                    $pol = Polmaster::where('policy_no',$dcontrol->policy_no)->update([
                        'trans_type'=>'PTA'
                    ]);

                    $dc_upd = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->update([
                        'trans_type'=>'PTA',
                    ]);
                }
            default: 
                break;
        }
    }

    public function saveComesaDetails($request,$endt_renewal_no,$reg_no)
    {
        $dcontrol = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->firstOrFail();
        $class = ClassModel::where('class',$dcontrol->class)->firstOrFail();
        $effective_date = Carbon::parse($dcontrol->effective_date);
        $month = $effective_date->month;
        $year = $effective_date->year;
        $days = cal_days_in_month(CAL_GREGORIAN, $month, $year);
        if($month == 2){
            $Months = ($dcontrol->days_covered / 30);
        }else{
            $Months = ($dcontrol->days_covered / $days);
        }
        //round to next month count
        $Months = ceil($Months);
        $coveredMonths = (int)$Months;

        $comesaCountries = (new ComesaExtension)->getCountries();
        $detail = '';
        foreach($comesaCountries as $key => $country){
            $detail .= $country->country.'_';
        }

        $detail = rtrim($detail, "_");
        $line_no = Ptamdtl::where('endt_renewal_no', $dcontrol->endt_renewal_no)->max('ln_no');

        $endorse_amt = Motorpolsec::where('endt_renewal_no', $dcontrol->endt_renewal_no)
            ->where('reg_no',$reg_no)
            ->sum('endorse_amount');
        $modtl = Modtlmast::where('endt_renewal_no', $dcontrol->endt_renewal_no)
            ->where('reg_no',$reg_no)
            ->first();
        $endorse_amt = Motorpolsec::where('endt_renewal_no', $dcontrol->endt_renewal_no)->sum('endorse_amount');
        $comesaRates = DB::table('comesarates')
            ->where('dept', $class->aimsdept)
            ->whereRaw("(min_days <= $dcontrol->days_covered AND max_days >= $dcontrol->days_covered) OR period=$coveredMonths")
            ->first();

        $med_prem = $comesaRates->medical * $modtl->insured_seats;

        $new_line_no = $line_no + 1;
        $pta_dtl = new Ptamdtl();
        $pta_dtl->endt_renewal_no = $dcontrol->endt_renewal_no;
        $pta_dtl->policy_no = $dcontrol->policy_no;
        $pta_dtl->period_from = $dcontrol->period_from;
        $pta_dtl->period_to = $dcontrol->period_to;
        $pta_dtl->no_of_days = $dcontrol->days_covered;
        $pta_dtl->class = $dcontrol->class;
        $pta_dtl->ln_no = $new_line_no;
        $pta_dtl->card_no = $request->comesacard_no;
        $pta_dtl->medical = $med_prem;
        $pta_dtl->med_per_pass = $comesaRates->medical;
        $pta_dtl->card_fee = $comesaRates->cardfee;
        $pta_dtl->reg_no = $reg_no;
        $pta_dtl->created_at = Carbon::now();
        $pta_dtl->countries = $detail;
        $pta_dtl->passengers = $modtl->insured_seats;
        $pta_dtl->travellers = $dcontrol->insured;
        $pta_dtl->pta_prem = $endorse_amt;
        $pta_dtl->endorse_amt = $endorse_amt;
        $pta_dtl->pta_rate = $request->pta_rate;
        $pta_dtl->dept = $request->pta_dept;
        $pta_dtl->client_number = $dcontrol->client_number;
        $pta_dtl->motor_categ = $request->motor_categ;

        $pta_dtl->save();
    }


    public function nilVehicleValue(Request $request)
    {
      $endt_renewal_no = $request->endt_renewal_no;
      try{
        $dcontrol = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();
        $vehicles = Modtlmast::where('policy_no',$dcontrol->policy_no)
          // ->where('status','ACT')
          // ->where('covertype',1)
          ->get();
  
        return view('gb.underwriting.nil_change_vehicle_value',[
          'vehicles' => $vehicles,
          'dcontrol' => $dcontrol,
        ]);
      }
      catch(Throwable $e)
      {
        return redirect()->action(
            'gb\underwriting\Policy_functions@index',
            ['endt_renewal_no' => $request->endt_renewal_no]
        );
      }
    }

    public function NilChangeSumInsured(Request $request)  
    {
        $request->validate([
            'reg_no.*' => 'required',
            'sum_insured.*' => 'required',
            'endt_renewal_no' => 'required',
        ]);

        $reg_no = $request->reg_no;
        $sum_insured = $request->sum_insured;

        DB::beginTransaction();
        try {
            for($i=0;$i<= count($reg_no); $i++)
            {
                $resp = $this->validateNilSumInsuredChange(new Request([
                    'reg_no' => $reg_no[$i],
                    'endt_renewal_no' => $request->endt_renewal_no,
                    'sum_insured' => $sum_insured[$i],
                ]));

                $resp = json_decode($resp->getContent());

                if($resp->status == 0)
                {
                    return $resp;
                }

                $dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)->firstOrFail();
                $modtl = Modtlmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$reg_no[$i])
                    ->first();
    
                $total_sum_insured = (float) str_replace(',', '', $sum_insured[$i]);
                $this->_total_sum_insured = $total_sum_insured;
                $this->_cls = $modtl->class;
                $this->_reg_no = $reg_no[$i];
                $this->_endt_renewal_no = $request->endt_renewal_no;
                $this->_seat_cap = $modtl->insured_seats;

                $sections = Motcvrdet::with('motor_group')
                    ->leftJoin('motorsect',function($join){
                        $join->on('motorsect.grp_code','motcvrdet.grp_code');
                        $join->on('motorsect.item_code','motcvrdet.item_code');
                    })
                    ->leftJoin('motorprem_grp',function($join){
                        $join->on('motorprem_grp.grp_code','motcvrdet.grp_code');
                    })
                    ->where('policy_no',$modtl->policy_no)
                    ->where('endt_renewal_no',$modtl->endt_renewal_no)
                    ->where('cancelled','<>','Y')
                    ->where('reg_no',$reg_no[$i])
                    ->where('rate_basis','S')
                    ->get(['motcvrdet.*','motorsect.description','motorsect.rate_basis','motorsect.basis','motorprem_grp.description as grp_desc','motorprem_grp.basic_premium']);
                   
                foreach ($sections as $sect) {
                    if($sect->basic_premium != 'Y' && $sect->rate_basis != 'S')
                    {
                        continue;
                    }

                    $section = [
                        'group' => $sect->grp_code,
                        'item' => $sect->item_code,
                        'rate_amount' => $sect->basis == 'R' ? $sect->rate : $sect->annual_premium,
                        'risk_value' => $sum_insured[$i],
                        'cancel' => $sect->cancelled,
                    ];
                    
                    $section['rate_amount'] = $this->get_minRateAmt($section);
                    $premium_amounts = $this->compute_motor_premium($section,$dcontrol->ast_marker);

                    $resp = $this->save_section_dtl($section,$premium_amounts);
                }
                
                if($resp['status'] != 1)
                {
                    throw new Exception("Error Processing Request", $resp['status_code']);
                }
                
                $updatedModtl = Modtlmast::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                    ->where('reg_no',$reg_no[$i])
                    ->first();
                $data =[
                    'total_sum_insured'=>$updatedModtl->premium_dtl->total_sum_insured,
                    'sum_insured'=>$updatedModtl->premium_dtl->sum_insured,
                ];

                $tableKeys = [
                    'policy_no' => $dcontrol->policy_no,
                    'endt_renewal_no' => $dcontrol->endt_renewal_no,
                    'risk_item' => $modtl->reg_no,
                    'table_name' => 'modtlsumm'
                  ];

                (new Risk)->nil_audit_update($data, $modtl->premium_dtl, $tableKeys);

                DB::commit();

                return response()->json([
                    'status' => 1,
                    'message' => 'Successfully Updated sum insured',
                ]);
            }
        } catch (\Throwable $e) {
            report($e);
            // dd($e);
            $error_msg = json_encode($e->getMessage());
			$reference = "Endorsement_no: {$request->endt_renewal_no}";
            $module = __METHOD__;
			$route_name = Route::getCurrentRoute()->getActionName();
            log_error_details($route_name,$error_msg,$reference,$module);
            return response()->json([
                'status' => 0,
                'message' => 'Failed to Update sum insured',
            ]);
        }
    }

    public function validateNilSumInsuredChange(Request $request)
    {
        $request->validate([
            'reg_no' => 'required',
            'endt_renewal_no' => 'required',
            'sum_insured' => 'required',
        ]);

        try {
            $sum_insured = str_replace(',','',$request->sum_insured);
            $modtl = Modtlmast::where('endt_renewal_no',$request->endt_renewal_no)
                ->where('reg_no',$request->reg_no)
                ->first();

            $active_endorsments = Dcontrol::activeCoverEndorsements($modtl->policy_no);
            $hasDiscounts = Discounts_loadings::whereIn('endt_renewal_no',$active_endorsments)
                ->where('reg_no',$request->reg_no)
                ->sum('endorse_amount');

            if($hasDiscounts > 0)
            {
                Session::flash('error',"You cannot use NIL since vehicle {$request->reg_no} had discounts/loadings applied");
            }

            $sections = Motcvrdet::with('motor_group')
                ->leftJoin('motorsect',function($join){
                    $join->on('motorsect.grp_code','motcvrdet.grp_code');
                    $join->on('motorsect.item_code','motcvrdet.item_code');
                })
                ->leftJoin('motorprem_grp',function($join){
                    $join->on('motorprem_grp.grp_code','motcvrdet.grp_code');
                })
                ->where('policy_no',$modtl->policy_no)
                ->where('endt_renewal_no',$modtl->endt_renewal_no)
                ->where('cancelled','<>','Y')
                ->where('reg_no',$request->reg_no)
                ->get(['motcvrdet.*','motorsect.description','motorsect.rate_basis','motorsect.basis','motorprem_grp.description as grp_desc','motorprem_grp.basic_premium']);
                
            $PremiumChange = [];
            $totalPremium = 0;

            foreach ($sections as $sect) {
                if($sect->basic_premium != 'Y' && $sect->rate_basis != 'S')
                {
                    continue;
                }

                $requestData = [
                    'cls'               => $sect->class,
                    'reg_no'            => $modtl->reg_no,
                    'endt_renewal_no'   => $modtl->endt_renewal_no,
                    'group'             => $sect->grp_code,
                    'section'           => $sect->item_code,
                    'rate_amount'       => $sect->basis == 'R' ? $sect->rate : $sect->annual_premium,
                    'risk_value'        => $sum_insured,
                ];

                $premiumAmts = $this->precompute_sec_premium(new Request($requestData));
                if($premiumAmts['status'] == 0)
                {
                    throw new Exception('An internal error occured while processing the request');
                }

                $newAnnualPremium = $premiumAmts['data']['annual_premium'];

                if($sect->annual_premium != $newAnnualPremium)
                {
                    array_push($PremiumChange,[
                        'item' => "{$sect->grp_desc}: {$sect->description}",
                        'current_premium' => $sect->annual_premium,
                        'new_premium' => $newAnnualPremium,
                    ]);
                }

                $totalPremium += $newAnnualPremium;

            }

            $updatable = true;
            $message = 'Okay to proceed';
            if($sum_insured == (float)$modtl->premium_dtl->sum_insured)
            {
                $updatable = false;
                $message = 'The Sum Insured has not changed';
            }
            elseif(count($PremiumChange) > 0)
            {
                $updatable = false;
                $message = 'Vehicle value can not be changed since premiums are changing';
            }
            return response()->json([
                'status' => 1,
                'message' => $message,
                'updatable' => $updatable,
                'errorMsg' => $PremiumChange,
                'totalPremium' => $totalPremium,
            ]);
        } catch (\Throwable $th) {
            // dd($th);
            return [
                'status' => 0,
                'message' =>'An internal error Occured',
            ];
        }
    }

    public function getSumInsuredBasedRate(Request $request)
    {
        try 
        {
            $masterParam = MotorMasterParam::first();
            
            $sum_insured = str_replace(",","",$request->sum_insured);
            $siband = null;
            // sum insured based 
            if($masterParam->sum_insured_based_rate == 'N')
            {
                throw new Exception("Basic Rate is not based on Sum insured bands",403);
            }
                
            $siband = SiRateBands::where('class',$request->cls)
                ->where('min_si','<=',$sum_insured)
                ->where('max_si','>=',$sum_insured)
                ->when($masterParam->si_band_app_level == 'USAGE',function($query) use ($request){
                    return $query->where('classtype',$request->classtype);
                });

            if($siband->count() ==0)
            {
                throw new Exception("Unable to obtain basic rate. Sum insured ({$sum_insured}) not within set bands",403);
            }

            return [
                'status' => 1,
                'message' => 'Successful',
                'data' =>$siband->first()
            ];
        }
        catch(Throwable $e)
        {
            $message = 'Failed to obtain Basic rate';
            $status = 0;

            if($e->getCode() == 403){
                $message = $e->getMessage();
                $status = $e->getCode();
            }
            return response()->json([
                'status' => $status,
                'message' => $message
            ],$e->getCode());
        }
    }
}
