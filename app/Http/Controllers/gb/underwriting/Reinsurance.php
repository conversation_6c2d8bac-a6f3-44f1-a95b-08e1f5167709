<?php

namespace App\Http\Controllers\gb\underwriting;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\MessageBag;
use Ya<PERSON>ra\Datatables\Datatables;
use App\Polremast;
use App\PPWParam;
use App\Polremastend;
use App\RiskCategoryOccup;
use App\Dcontrol;
use App\Polmaster;
use App\Polremast_tmp;
use App\Classyear;
use App\Reinsetup;
use App\Polsect;
use App\Polsec;
use App\Endtrepart;
use App\Debitmast;
use App\Acccater;
use App\Dtran0;
use App\Crmast;
use Session;
use App\Polrepart;
use App\ClassModel;
use App\Creditmast;
use Carbon\Carbon;
use App\Acdet;
use App\Pipcnam;
use App\Polcmb;
use App\Bustype;
use App\Gradedown;
use App\reinacdetallo;
use App\Rein_acdetallonew;
use App\Period;
use App\Polredtl;
use App\Reinscons;
use Auth;

// use policyfunctionsbk
use App\Http\Controllers\gb\underwriting\Policy_functions;
use App\Http\Controllers\gb\reinsurance\ReinController;
use App\Models\CreditRating;
use App\Models\Facult_slips;
use App\Models\Modtlpivot;
use App\Models\Modtlsumm;
use App\Polmasterend;
use App\Polsectend;
use App\Tax_code;
use Exception;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;
use Throwable;

class Reinsurance extends Controller
{
  // private variables
  private float $_fac_gross_prem;
  private float $_fac_net_prem;
  private float $_fac_comm_amt;
  private float $_fac_wht_amt;
  private float $_fac_vat_prem_amt;
  private float $_fac_vat_comm_amt;

  public function setFacGrossPrem($amount)
  {
    $this->_fac_gross_prem = $amount;
  }

  public function setFacNetPrem($amount)
  {
    $this->_fac_net_prem = $amount;
  }

  public function setFacCommAmt($amount)
  {
    $this->_fac_comm_amt = $amount;
  }

  public function setWhtAmt($amount)
  {
    $this->_fac_wht_amt = $amount;
  }

  public function setVatFacPremAmt($amount)
  {
    $this->_fac_vat_prem_amt = $amount;
  }

  public function setVatFacCommAmt($amount)
  {
    $this->_fac_vat_comm_amt = $amount;
  }

  public function reinsure(Request $request)
  {
    $schem = schemaName();

    $gb = $schem['gb'];
    $gl = $schem['gl'];
    $common = $schem['common'];


    $endt_renewal_no = $request->get('endt_renewal_no');

    $result = array('status' => 0);

    $reinsured = Polremastend::where('endt_renewal_no', $endt_renewal_no)->count();

    if ($reinsured < 1) {

      /*execute oracle procedure reinsure*/
      $procedureName = '' . $gb . '.reinsure';
      $bindings = [
        'endt_renewal_no'  =>  $endt_renewal_no,
      ];
      $resp = DB::executeProcedure($procedureName, $bindings);

      if ($resp) {

        $result = array('status' => 1);
      }
    }

    echo json_encode($result);
  }


  public function interactive_rein(Request $request)
  {
    //dd($request);
    $this->reinsurance_pre_computation($request);
  }


  public function reinsurance_pre_computation(Request $request)
  {
    // dd('request',$request);
    DB::beginTransaction();

    try {
      $schem = schemaName();

      $gb = $schem['gb'];
      $gl = $schem['gl'];
      $common = $schem['common'];
      $endt_renewal_no = $request->get('endt_renewal_no');

      //debitmast record
      $debit_count = Debitmast::where('endt_renewal_no', $endt_renewal_no)
        ->count();
      if ($debit_count > 0) {

        Session::flash('error', 'Endorsement already debitted');
      } else {

        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();

        $dcontrol = $dcontrol[0];



        $polmaster = Polmaster::where('policy_no', $dcontrol->policy_no)->get();
        $polmaster = $polmaster[0];
        $final_reinsure = $request->get('final_reinsure');
        $grading = $request->get('grading');
        $per_location = $request->get('per_location');
        $per_section = $request->get('per_section');
        $per_combined = $request->get('per_combined');
        $combined_class = $request->get('combined');
        $recon = $request->get('recon');
        
        // $final_reinsure=$request->get('final_reinsure');


        //$effective_sum=$request->get('effective_sum');
        $effective_sum = str_replace(",", '', $request->get('effective_sum'));
        //dd($effective_sum);
        $user_name = trim(Auth::user()->user_name);


        if ($final_reinsure == 'N') {
          $glocation = $request->get('glocation');
          $gsection = $request->get('gsection');
          $eml = $request->get('eml');
          $facultative_reice = $request->get('facultative_reice');
          $gfacultative_comm_reice = $request->get('gfacultative_comm_reice');
          $consolidatex = $request->get('consolidate');
        } else {
          $glocation = $request->get('location');
          $gsection = $request->get('section');
          $eml = $request->get('eml_mpl');
          // $facultative_reice = $request->get('facult_reice');
          // $facultative_reice = $request->get('facultative_reice');
          $facultative_reice = $request->get('facult_reice_offload');
          $gfacultative_comm_reice = (float) $request->get('facult_commission');
          $consolidatex = $request->get('confirm_consolidatex');
        }
        //dd($consolidatex);
        if ($consolidatex == 'Y') {
          $consolidate = 'Y';
        } else {
          $consolidate = 'N';
        }

        if($recon=='Y'){

          $recon='Y';
        }
        else{
          $recon='N';
        }

        //dd($final_reinsure);
        if ($per_combined == 'Y') {
          $gclass = $combined_class;
          $combined = 'Y';
        } else {
          $gclass = $dcontrol->class;
          $combined = 'N';
        }


        //dd($final_reinsure);

        switch (trim($per_location)) {
          case 'Y':

            $location = $glocation; //$request->get('glocation');            

            switch ($per_section) {
              case 'Y':
                $section_no = $gsection; //$request->get('gsection');
                break;

              default:
                $section_no = 0;
                break;
            }

            break;

          case 'T':
            $location = $glocation;
            $per_location = 'T';

            switch ($per_section) {
              case 'Y':
                $section_no = $gsection; //$request->get('gsection');
                break;

              default:
                $section_no = 0;
                break;
            }

            break;

          default:
            $per_location = 'N';
            $location = 0;
            $per_section = 'N';
            $section_no = 0;

            break;
        }

        if($grading==null){
          $grading='N';
        }
        //switch to check if grading has been done 
        switch ($grading) {
          case 'Y':

            $grade = $request->get('grade');

            break;

          default:

            $grade = 'Z'; //z represents no grading

            break;
        }

        $riskCtrl = new Risk;
        $prev_endt_no = $riskCtrl->previousEndorsement($endt_renewal_no);
        
        $prev_ri = Polremastend::where('endt_renewal_no', $prev_endt_no)
          ->where('location', $location)
          ->where('section', $section_no)
          ->where('policy_no', $polmaster->policy_no)
          ->where('comb_class', $gclass)
          ->first(['surplus_referral']);

        $surplus_referral = $request->surplus_referral;
        $prev_surplus_referral = $prev_ri->surplus_referral;
        if($request->init_surplus_referral == 'Y' && !in_array($dcontrol->trans_type,['POL','REN','RNS']))
        {
          $surplus_referral = $prev_ri->surplus_referral;
        }

          //dd($gfacultative_comm_reice);
          
          /*execute oracle procedure interactive_reinsure*/
          $procedureName = '' . $gb . '.interactive_reinsure_precomput';
          $bindings = [
            'endt_renewal_no'  =>  $endt_renewal_no,
            'grading'  => $grading,
            'grade_down' => $grade,
            'grading_descr_code' => $request->risk_occup_code,
            'eml'  =>  (float) $eml,
            'facultative_reice'  =>  (float) $facultative_reice,
            'gfacultative_comm_reice'  => (float) $gfacultative_comm_reice,
            'per_location'  => $per_location,
            'glocation'  => (int) $location,
            'per_section'  => $per_section,
            'gsection'  =>  (int) $section_no,
            'do_debit' => 'N',
            'per_combined'  => $combined,
            'gcombined'  => (int) $gclass,
            'final_reinsure'  => $final_reinsure,
            'consolidate' => $consolidate,
            'w_effective_sum' => (float) $effective_sum,
            'recon' => $recon,
            'g_user' => $user_name,
            'surplus_referral' => $surplus_referral,
            //'retention_amt'=>$company_retention

          ];
          // dd($bindings);
          $resp = DB::executeProcedure($procedureName, $bindings);

          if ($resp) {
            if ($final_reinsure == "N") {
              
      
                $polremast_tmp = Polremast_tmp::where('endt_renewal_no', $endt_renewal_no)
                  ->where('location', $location)
                  ->where('section', $section_no)
                  ->where('policy_no', $polmaster->policy_no)
                  ->where('uw_year', $polmaster->uw_year)
                  ->where('comb_class', $gclass)
                  ->first();
      
                  //dd($polremast_tmp[0]->comb_class." - ".$polremast_tmp[0]->premium_amount_1);
                $classyear = Classyear::where('class', $gclass)
                  ->where('uw_year', $polmaster->uw_year)
                  ->first();
      
                $reinsetup = Reinsetup::where('class', $classyear->reinclass)
                  ->where('uw_year', $polmaster->uw_year)
                  ->first();

                $surplus_referral = $this->surplus_referral_limit($gclass,$endt_renewal_no);
                
                $result = [
                  'status' => 1, 
                  'tmp_remast' => $polremast_tmp, 
                  'reinsetup' => $reinsetup,
                  'surplus_referral' => $surplus_referral,
                  'prev_surplus_referral' => $prev_surplus_referral,
                ];

            } else {
                $this->facultative_ceding($endt_renewal_no,$gclass,$location,$section_no);

                $wclass = (int)substr($endt_renewal_no, 3, 3);
                $workflow_id = ClassModel::where('class',$wclass)->first();
                if($workflow_id->motor_policy!='Y'){
                  $workflow = new Policy;
                  $wkflow = $workflow->add_to_workflow($endt_renewal_no,$workflow_id->workflow_id,$request->get('pid')); 
          
                }
                
                $result = array('status' => 1);
            }
          } else {
            throw new Exception("Error Processing Request", 500);
            
          }
          DB::commit();

        echo json_encode($result);
      }
    } catch (\Throwable $e) {
      DB::rollback();
      // dd($e);
    
      $error_msg = json_encode($e->getMessage());
      $reference = $endt_renewal_no;
      $module = __METHOD__;
      $route_name = Route::getCurrentRoute()->getActionName();
      log_error_details($route_name,$error_msg,$reference,$module);

      $resp = false;
      $warning = 'Error. Section or Location already reinsured.';

      $result = array('status' => 0, 'warning' => $warning);
      echo json_encode($result);
    }
  }

  public function auto_reinsure(Request $request)
  {
    DB::beginTransaction();
    try {
      $schem = schemaName();
      $gb = $schem['gb'];
      $gl = $schem['gl'];
      $common = $schem['common'];

      $endt_renewal_no = $request->endt_renewal_no;
      $debit_count = Debitmast::where('endt_renewal_no', $endt_renewal_no)->count();

      if ($debit_count > 0) {
        $warning = 'Endorsement already debitted.';
        Session::flash('error', $warning);

        $result = array('status' => 0, 'warning' => $warning);
        return response()->json($result, 200);
      } 

      $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
      $polmaster = Polmaster::where('policy_no', $dcontrol->policy_no)->first();

      $policy_fn = new Policy_functions;
      $prev_endt_no = $policy_fn->previousEndorsement($endt_renewal_no);

      $prevRemast =  DB::table('polremastend')
          ->select('polremastend.*', 'class.description as class_description')
          ->join('class', 'class.class', '=', 'polremastend.comb_class')
          ->where(['endt_renewal_no' => $prev_endt_no])
          ->whereRaw("(sec_ext_flag ='N' OR sec_ext_flag IS NULL)")
          ->get();

        foreach($prevRemast as $remast){
          $consolidate = 'N';
          $grading = 'N';

          if($remast->gradedown != 'Z'){
            $grading = 'Y';
          }

          $reinscons=Reinscons::where('policy_no',$polmaster->policy_no)->first();
          if(isset($reinscons)){
            $new_request = new Request([
                'main_pol_no' => $polmaster->policy_no,
                'main_sum_insured' => $polmaster->sum_insured,
                'main_premium' => $reinscons->main_premium,
                'other_policy_no' => $reinscons->other_policy_no,
                'other_sum_insured' => $reinscons->other_sum_insured,
                'other_premium' => $reinscons->other_premium,
            ]);

            // call reincontroller controller
            $reinc = new ReinController;
            $reinc->update_reincons($new_request);

            $consolidate = 'Y';
          }

          $grade = $remast->gradedown;
          $eml = $remast->mpl;
          $facultative_reice = $remast->facult_p_reice;
          $gfacultative_comm_reice = $remast->facult_p_comm_per;
          $per_location = $remast->reinsure_location;
          $location = $remast->location;
          $per_section = $remast->reinsure_section;
          $section = $remast->section;
          $combined = $remast->combine;
          $gclass = $remast->comb_class;
          $final_reinsure = $request->final_reinsure;
          $consolidate = $consolidate;
          $recon = 'Y';
          $surplus_referral = $remast->surplus_referral;
          $user_name = trim(Auth::user()->user_name);

          /*execute oracle procedure interactive_reinsure*/
          $procedureName = '' . $gb . '.interactive_reinsure_precomput';
          $bindings = [
              'endt_renewal_no'  =>  $endt_renewal_no,
              'grading'  => $grading,
              'grade_down' => $grade,
              'grading_descr_code' => $remast->grade_descr_code,
              'eml'  =>  (float) $eml,
              'facultative_reice'  =>  (float) $facultative_reice,
              'gfacultative_comm_reice'  => (float) $gfacultative_comm_reice,
              'per_location'  => $per_location,
              'glocation'  => (int) $location,
              'per_section'  => $per_section,
              'gsection'  =>  (int) $section,
              'do_debit' => 'N',
              'per_combined'  => $combined,
              'gcombined'  => (int) $gclass,
              'final_reinsure'  => $final_reinsure,
              'consolidate' => $consolidate,
              'w_effective_sum' => (float) $remast->effective_sum_1,
              'recon' => $recon,
              'g_user' => $user_name,
              'surplus_referral' => $surplus_referral,
            ];
            // dd($bindings);
            $resp = DB::executeProcedure($procedureName, $bindings);

        }

      
      $result = [];
      
      if ($resp) {
        if ($final_reinsure == "N") {
            $polremast_tmp = Polremast_tmp::where('endt_renewal_no', $endt_renewal_no)->get();

            $classyear = Classyear::where('class', $gclass)
              ->where('uw_year', $polmaster->uw_year)
              ->first();

            $reinsetup = Reinsetup::where('class', $classyear->reinclass)
              ->where('uw_year', $polmaster->uw_year)
              ->first();


            $result = array('status' => 1, 'tmp_remast' => $polremast_tmp, 'reinsetup' => $reinsetup);
        } else {
            $this->facultative_ceding($endt_renewal_no,$gclass,$location,$section);

            $wclass = (int)substr($endt_renewal_no, 3, 3);
            $workflow_id = ClassModel::where('class',$wclass)->first();
            if($workflow_id[0]->motor_policy!='Y'){
              $workflow = new Policy;
              $wkflow = $workflow->add_to_workflow($endt_renewal_no,$workflow_id->workflow_id,$request->get('pid')); 
            }
            
            $result = array('status' => 1);
        }
      } else {
        throw new Exception("Error Processing Request", 500);
      }

      DB::commit();

    return response()->json($result, 200);

    } catch (\Throwable $e) {
      // dd($e);
      DB::rollback();

      
      $error_msg = json_encode($e->getMessage());
      $reference = "endt_renewal_no: {$endt_renewal_no}";
      $module = __METHOD__;
      $route_name = Route::getCurrentRoute()->getActionName();

      log_error_details($route_name,$error_msg,$reference,$module);

      $resp = false;
      $result['warning'] = 'Error. Section or Location already reinsured.';

      return response()->json($result, $e->getCode());
    }
  }

  public function auto_reinsure_fleet(Request $request)
  {
      DB::beginTransaction();
  
      try {
          $schem = schemaName();
          $gb = $schem['gb'];
          $endt_renewal_no = $request->endt_renewal_no;
  
          $debitCount = Debitmast::where('endt_renewal_no', $endt_renewal_no)->count();
          if ($debitCount > 0) {
              throw new Exception('Endorsement already debitted.', 400);
          }
  
          $data1 = DB::table('polmaster as p')
              ->join('dcontrol as d', 'p.endorse_no', '=', 'd.endt_renewal_no')
              ->where('endt_renewal_no', $endt_renewal_no)
              ->select('p.class as w_class', 'p.uw_year as w_uw_year', 'd.currency_rate as w_curr_rate')
              ->first();
  
          if (!$data1) {
              throw new Exception('Policy details not found.', 404);
          }
  
          $w_class = $data1->w_class;
          $w_uw_year = $data1->w_uw_year;
          $w_curr_rate = $data1->w_curr_rate;
  
          $data2 = DB::table('classyear as cy')
              ->join('reinsetup as r', function ($join) {
                  $join->on('cy.reinclass', '=', 'r.class')
                      ->on('cy.uw_year', '=', 'r.uw_year');
              })
              ->where('cy.uw_year', $w_uw_year)
              ->where('cy.class', $w_class)
              ->select(DB::raw('r.company_retention_amt + r.global_treaty_limit as w_retention'))
              ->first();
  
          $w_retention = $data2->w_retention ?? throw new Exception('Retention details not found.', 404);
  
          $count = DB::table('modtlpivot as mp')
              ->join('modtlsumm as m', function ($join) {
                  $join->on('mp.endt_renewal_no', '=', 'm.endt_renewal_no')
                      ->on('mp.reg_no', '=', 'm.reg_no');
              })
              ->where('mp.endt_renewal_no', '=', $endt_renewal_no)
              ->where('mp.status', '=', 'ACT')
              ->whereRaw("m.sum_insured * ? <= ?", [$w_curr_rate, $w_retention])
              ->where('m.sum_insured', '<>', 0)
              ->whereNotIn('mp.item_no', function ($query) use ($endt_renewal_no) {
                  $query->select('location')
                      ->from('polremastend')
                      ->where('endt_renewal_no', '=', $endt_renewal_no)
                      ->whereNotNull('location');
              })
              ->count();
  
          if ($count > 0) {
              $procedureName = "$gb.auto_reinsure_motor_fleet";
              $bindings = [
                  'endt_renewal_no' => $endt_renewal_no,
                  'g_user' => trim(Auth::user()->user_name),
              ];
  
              $resp = DB::executeProcedure($procedureName, $bindings);
              if (!$resp) {
                  throw new Exception('Procedure execution failed.', 500);
              }
          } else {
              throw new Exception('No vehicles to auto reinsure.', 400);
          }
  
          DB::commit();
          return response()->json(['status' => 1], 200);
  
      } catch (\Throwable $e) {
          DB::rollback();
          log_error_details(Route::getCurrentRoute()->getActionName(), $e->getMessage(), "endt_renewal_no: $endt_renewal_no", __METHOD__);
          return response()->json([
              'status' => 0,
              'warning' => $e->getMessage(),
          ], $e->getCode() ?: 500);
      }
  }
  

  public function compute_effective_sum(Request $request)
  {

    $endt_renewal_no = $request->get('endt_renewal_no');

    $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();

    $dcontrol = $dcontrol[0];

    $polmaster = Polmaster::where('policy_no', $dcontrol->policy_no)->get();
    $polmaster = $polmaster[0];

    $grading = $request->get('grading');
    $eml = $request->get('eml');
    $facultative_reice = $request->get('facultative_reice');
    $gfacultative_comm_reice = $request->get('gfacultative_comm_reice');
    $per_location = $request->get('per_location');
    $per_section = $request->get('per_section');
    $per_combined = $request->get('per_combined');
    $combined_class = $request->get('gcombined');
    $w_effective_sum = 0;

    $classModel = ClassModel::select('motor_policy')->where('class',$combined_class)->first();

    if ($per_combined == 'Y') {
      $gclass = $combined_class;
      $combined = 'Y';
    } else {
      $gclass = $dcontrol->class;
      $combined = 'N';
    }

    switch ($per_location) {
      case 'Y':

        $location = $request->get('glocation');

        switch ($per_section) {
          case 'Y':
            $section_no = $request->get('gsection');


            $polsec = Polsec::where('policy_no', $dcontrol->policy_no)
              ->where('location', $location)
              ->where('class', $gclass)
              ->where('section_no', $section_no)
              ->get(['sum_insured']);

            $w_effective_sum = $w_effective_sum + $polsec[0]->sum_insured;


            break;

          default:
            $section_no = 0;

            if($classModel->motor_policy == 'Y')
            {
              $modtl = Modtlpivot::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('item_no',$location)
                ->first();


              $w_effective_sum = $w_effective_sum + $modtl->premium_dtl->sum_insured;

            }
            else {

              $polsect = Polsect::where('policy_no', $dcontrol->policy_no)
                ->where('location', $location)
                ->where('class', $gclass)
                ->get(['total_sum_insured']);
              $w_effective_sum = $w_effective_sum + $polsect[0]->total_sum_insured;
            }


            break;
        }


        break;

      case 'T':
        $polsect = Polsect::where('policy_no', $dcontrol->policy_no)
          ->where('location',$request->get('glocation'))
          ->first();

        $location = $polsect->location;
        $per_location = 'T';

        switch ($per_section) {
          case 'Y':
            $section_no = $request->get('gsection');


            $polsec = Polsec::where('policy_no', $dcontrol->policy_no)
              ->where('location', $location)
              ->where('class', $gclass)
              ->where('section_no', $section_no)
              ->get(['sum_insured']);

            $w_effective_sum = $w_effective_sum + $polsec[0]->sum_insured;


            break;

          default:
            $section_no = 0;
            if($classModel->motor_policy == 'Y')
            {
              $modtl = Modtlpivot::where('endt_renewal_no',$dcontrol->endt_renewal_no)
                ->where('item_no',$location)
                ->first();


              $w_effective_sum = $w_effective_sum + $modtl->premium_dtl->sum_insured;

            }
            else {
              $polsect = Polsect::where('policy_no', $dcontrol->policy_no)
                ->where('location', $location)
                ->where('class', $gclass)
                ->get(['total_sum_insured']);

              $w_effective_sum = $w_effective_sum + $polsect[0]->total_sum_insured;
            }

            break;
        }

        break;

      default:
        $per_location = 'N';
        $location = 0;
        $per_section = 'N';
        $section_no = 0;

        $w_effective_sum = $w_effective_sum + $polmaster->sum_insured;

        break;
    }


    //apply eml/mpl
    //multicurrency sum insured

      $w_effective_sum = $w_effective_sum * $dcontrol->currency_rate;
      $w_effective_sum = ($w_effective_sum * $eml) / 100;
   
    echo $w_effective_sum;
  }


  public function facultative(Request $request)
  {
    
    $schem = schemaName();

    $gb = $schem['gb'];
    $gl = $schem['gl'];
    $common = $schem['common'];


    $endt_renewal_no = $request->input('facult_endorsement');
    $debited = $request->input('debited');
    $debit_rec = Debitmast::where('endt_renewal_no', $endt_renewal_no)->first();
    
    //check if the endorsement is reinsured
    $count = Polremastend::where('endt_renewal_no', $endt_renewal_no)
      //->where('comb_class',$gclass)
      ->count();

    // dd($request->all(),$endt_renewal_no,$count);
    $tot_facult_reice = Endtrepart::where('endt_renewal_no', $endt_renewal_no)->sum('reice_1');
 

    $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
    $dcontrol = $dcontrol[0];

    $classsect = ClassModel::where('class', $dcontrol->class)->get();
    $classsect = $classsect[0];

    if ($count == 0) {

      Session::flash('warning', 'Endorsement does not have facultative amount to ceed');

      switch ($classsect->motor_policy) {
        case 'Y':
          return redirect()->route('policy_functions', ['endt_renewal_no' => $endt_renewal_no]);
          break;

        default:
          return redirect()->route('non_motor_index', ['endt_renewal_no' => $endt_renewal_no]);
          break;
      }
    } else {


      $facult_sum = Polremastend::where('endt_renewal_no', $endt_renewal_no)
        //->where('comb_class',$gclass)
        ->sum('facult_p_sum_3');

      $facult_res = Polremastend::where('endt_renewal_no', $endt_renewal_no)
                                ->where('facult_p_sum_3', '>', 0)
                                ->get();
        $facult_p_reice=$facult_res[0]->facult_p_reice;
        $total_sum=(float)$facult_res[0]->effective_sum_3;
        $total_sum_value=(float)$facult_res[0]->sum_insured_1;
        $total_prem=(float)$facult_res[0]->premium_amount_3;

        $tot_facult_reic = $facult_p_reice - $tot_facult_reice;

      $count_fac_class = Polremastend::select('comb_class')
                                     ->where('endt_renewal_no', $endt_renewal_no)
                                     ->groupBy('comb_class')
                                     ->get()->count();

      $facult_prem = Polremastend::where('endt_renewal_no', $endt_renewal_no)
        //->where('comb_class',$gclass)
        ->sum('facult_p_prem_3');

      $facult_comm = Polremastend::where('endt_renewal_no', $endt_renewal_no)
        //->where('comb_class',$gclass)
        ->sum('facult_p_comm_amt_3');

      $facult_comm_rate = Polremastend::where('endt_renewal_no', $endt_renewal_no)
        //->where('comb_class',$gclass)
        ->max('FACULT_P_COMM_PER');

      if ($facult_prem != 0 || $dcontrol->onboard_old_policy == 'Y') {

        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol = $dcontrol[0];

        $polmaster = Polmaster::where('policy_no', $dcontrol->policy_no)->get();
        $polmaster = $polmaster[0];

        $ceeded_sum = Endtrepart::where('endt_renewal_no', $endt_renewal_no)
          //->where('class',$gclass)
          ->where('dtran_no', '>', 0)
          ->sum('re_sum_1');

        $ceeded_comm = Endtrepart::where('endt_renewal_no', $endt_renewal_no)
          //->where('class',$gclass)
          ->where('dtran_no', '>', 0)
          ->sum('comm_amt_1');

        $outstanding = $facult_sum - $ceeded_sum;


        $polremast = Polremastend::where('endt_renewal_no', $dcontrol->endt_renewal_no)
          ->where('policy_no', $dcontrol->policy_no)
          ->get();


        $tot_fac_slip_amount = Facult_slips::where('endt_renewal_no', $endt_renewal_no)
            ->where('reversed','N')
            ->sum('gross_amount');
        $facult_slips = Facult_slips::where('endt_renewal_no', $endt_renewal_no)
            ->where('reversed','N')
            ->get();
        $pipcnam = Pipcnam::first();
        $endtrepart = Endtrepart::where('endt_renewal_no', $endt_renewal_no)
            ->where('dtran_no', '>', 0)
            ->get();

        $ceded_parts = $endtrepart->map(function ($item, $key) {
            return [
              'endt_renewal_no' => $item->endt_renewal_no,
              'class' => $item->class,
              'comb_class' => $item->comb_class,
              'location' => $item->location,
              'section' => $item->section,
              'sum_insured' => $item->re_sum_1,
              'premium' => $item->re_prem_1,
              'comm_amount' => $item->comm_amt_1,
              'reice' => $item->reice_1,
            ];
          });

        $facult_slip_prem = 0;

        if ($pipcnam->generate_fac_sign_slip == 'Y')
        {
          $ceded_parts = $facult_slips->map(function ($item, $key) {
            return [
              'endt_renewal_no' => $item->endt_renewal_no,
              'class' => $item->class,
              'comb_class' => $item->comb_class,
              'location' => $item->location,
              'section' => $item->section,
              'sum_insured' => $item->sum_insured,
              'premium' => $item->gross_amount,
              'comm_amount' => $item->comm_amount,
              'reice' => $item->reice,
            ];
          });
          $facult_slip_prem = $ceded_parts->sum('premium');
          $facult_slip_sum = $ceded_parts->sum('sum_insured');
        }
        
        // ceded prem
        $ceeded_prem = $ceded_parts->sum('premium');

        if($dcontrol->onboard_old_policy != 'Y') {
        $rem_facult_premium = $facult_prem - $ceeded_prem;
        } else {
        $rem_facult_premium = $facult_sum - $ceeded_sum;
        }

        // credit note sum
        $ceeded_cr_prem = Endtrepart::where('endt_renewal_no', $endt_renewal_no)
          //->where('class',$gclass)
          ->where('dtran_no', '>', 0)
          ->sum('re_prem_1');

        $ceeded_cr_sum = Endtrepart::where('endt_renewal_no', $endt_renewal_no)
          //->where('class',$gclass)
          ->where('dtran_no', '>', 0)
          ->sum('re_sum_1');

        $acccater = Acccater::all();

        $dtran0 = Dtran0::where('rec_no', 0)->first();

        $crmast = DB::select("SELECT c.*,d.id credit_rating,d.description credit_rating_desc FROM crmast c 
          INNER JOIN acctype a ON c.account_type = a.acc_type 
          left outer JOIN CREDIT_RATING d ON d.id = c.credit_rating 
          WHERE a.facultative = 'Y' and c.ri_broker ='N'");
          
        $ri_brokers = DB::select("SELECT (lpad(c.branch,3,0)||c.agent) branch_agent,c.branch,c.agent,c.name FROM crmast c WHERE c.ri_broker ='Y' AND direct_broker_acc <>'Y'");
        $ceded_ri_brokers = DB::select("SELECT (lpad(e.broker_branch,3,0)||e.broker_agent) broker,broker_branch,broker_agent,
          e.branch,e.agent,e.comb_class,e.class,e.location,e.dtran_no 
          FROM endtrepart e WHERE endt_renewal_no='$endt_renewal_no'");
        //load credit/debit notes from creditmast
        $creditmast = Creditmast::where('endt_renewal_no', $dcontrol->endt_renewal_no)->get();
        $bustype = Bustype::where('type_of_bus', trim($dcontrol->type_of_bus))->get();
        $bustype = $bustype[0];

        if($classsect->motor_policy == 'Y'){
          $polsect = DB::table('modtlpivot as t')
          ->select(
              'm.policy_no',
              'm.endt_renewal_no',
              'm.class',
              'm.reg_no as plot_no',
              't.item_no as location',
              'm.endorse_amount',
              'm.sum_insured',
              'm.reg_no as name',
              DB::raw("CASE WHEN p.location IS NULL THEN 'NOT REINSURED' ELSE 'REINSURED' END as reinsured")
          )
          ->where('t.endt_renewal_no', $endt_renewal_no)
          ->join('modtlsumm as m', function($join) {
              $join->on('t.endt_renewal_no', '=', 'm.endt_renewal_no')
                  ->on('t.reg_no', '=', 'm.reg_no');
          })
          ->join('polremastend as p', function($join) {
              $join->on('m.endt_renewal_no', '=', 'p.endt_renewal_no')
                  ->on('t.item_no', '=', 'p.location')
                  ->on('p.facult_p_prem_3', '<>', DB::raw(0));
          })
          ->get();

        }
        else{
          $polsect = DB::select("SELECT m.policy_no,m.endt_renewal_no,m.class,
            m.plot_no,m.location,m.plot_no,m.endorse_amount,m.total_sum_insured,m.name,
            CASE WHEN p.location is null THEN 'NOT REINSURED' ELSE 'REINSURED' END as reinsured
            FROM polsectend m
            JOIN polremastend p ON m.endt_renewal_no = p.endt_renewal_no and m.location = p.location
            WHERE m.endt_renewal_no ='$endt_renewal_no'  AND p.facult_p_prem_3 <>0
            ");
        }

        $sections = DB::select("SELECT m.policy_no,m.endt_renewal_no,m.class,m.classgrp,
            m.location,m.endorse_amount,m.sum_insured,m.group_description,
            CASE WHEN p.location is null THEN 'NOT REINSURED' ELSE 'REINSURED' END as reinsured
            FROM polsec_classgrp_description m 
            JOIN polremastend p ON m.endt_renewal_no = p.endt_renewal_no and m.location = p.location AND m.classgrp = p.section
            WHERE m.endt_renewal_no ='$endt_renewal_no' --AND p.facult_p_prem_3 <>0
          ");

        $reinsured_classes = DB::select("SELECT 
              polremastend.comb_class, 
              SUM(polremastend.sum_insured_1) AS sum_insured, 
              SUM(polremastend.premium_amount_3) AS premium_amount, 
              class.description, 
              COUNT(polremastend.endt_renewal_no) AS rein_status
          FROM 
              polremastend
          JOIN 
              class ON class.class = polremastend.comb_class
          WHERE 
              polremastend.policy_no = '$dcontrol->policy_no'
              AND polremastend.endt_renewal_no = '$endt_renewal_no'
              --AND polremastend.facult_p_prem_3 <>0
          GROUP BY 
              polremastend.comb_class, class.description
          ");
        $reinsured_classes = collect($reinsured_classes);
        // dd('reinsured_classes',$reinsured_classes);
        // check if mandatory documents have been uploaded
        $docs_uploaded = false;
        $docsNotUploaded = [];

        foreach($facult_slips as $slip)
        {

          $part_name = Crmast::where('branch',$slip->branch)->where('agent',$slip->agent)->value('name');
          $contextId = $slip->slip_no;
          $process_code = 130;
          $dept = 0;
          $agent = str_pad($slip->agent , 5, 0, STR_PAD_LEFT);
          $branch = str_pad($slip->branch , 3, 0, STR_PAD_LEFT);
          $entityId = $branch.$agent;
          $getDocUploadStatus = $this->checkuploaded_docs($entityId, $contextId, $process_code, $dept);
          $docUploadStatus = $getDocUploadStatus['status'];
          
          $docs_uploaded = $docUploadStatus != 1 ? 0 : 1;   

          // Add key-value pairs to $docsNotUploaded array
          if(!$docs_uploaded){
            array_push($docsNotUploaded, [
                'part_name' => $part_name,
                'slip_id' => $contextId,
            ]);
          }

        }
        $facultinppws = PPWParam::select('ppw_code', 'ppw_name')->get();
        $docsNotUploadedJson = json_encode($docsNotUploaded);
        
        return view('gb.underwriting.facultative', [

          'dcontrol' => $dcontrol,
          'bustype' =>$bustype,
          'facult_sum' => $facult_sum - $ceeded_sum,
          'rem_facult_premium' => $rem_facult_premium,
          'facult_comm' => $facult_comm - $ceeded_comm,
          'facult_comm_rate' => $facult_comm_rate,
          'polmaster' => $polmaster,
          'ceeded_sum' => $ceeded_sum,
          'ceeded_prem' => $ceeded_prem,
          'Outstanding' => $outstanding,
          'polsect' => $polsect,
          'sections' => $sections,
          'polremast' => $polremast,
          'acccater' => $acccater,
          'dtran' => $dtran0,
          'crmast' => $crmast,
          'ri_brokers' => $ri_brokers,
          'ceded_ri_brokers' => $ceded_ri_brokers,
          'creditmast' => $creditmast,
          'total_facult_sum' => $facult_sum,
          'total_facult_prem' => $facult_prem,
          'total_facult_comm' => $facult_comm,
          'facult_p_reice' => $facult_p_reice,
          'total_sum' => $total_sum,
          'total_sum_value' => $total_sum_value,
          'total_prem' => $total_prem,
          'tot_facult_reic' => $tot_facult_reic,
          'debited' =>$debited,
          'endtrepart' => $endtrepart,
          'class' => $classsect,
          'count_fac_class' => $count_fac_class,
          'reinsured_classes' => $reinsured_classes,
          'ceded_parts' => $ceded_parts,
          'docs_uploaded'=>$docs_uploaded,
          'docsNotUploaded'=>$docsNotUploadedJson,
          'facult_slip_prem'=>$facult_slip_prem,
          'facult_slip_sum'=>$facult_slip_sum,
          'ceeded_cr_prem'=>$ceeded_cr_prem,
          'ceeded_cr_sum'=>$ceeded_cr_sum,
          'facultinppws'=>$facultinppws,
        ]);
      } else {

        Session::flash('warning', 'Cannot Access facultative section.No facultative amount was found in the debit');

        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol = $dcontrol[0];

        $classsect = ClassModel::where('class', $dcontrol->class)->get();
        $classsect = $classsect[0];

        switch ($classsect->motor_policy) {
          case 'Y':
            return redirect()->route('policy_functions', ['endt_renewal_no' => $endt_renewal_no]);
            break;

          default:
            return redirect()->route('non_motor_index', ['endt_renewal_no' => $endt_renewal_no]);
            break;
        }
      }
    }
  }


  public function get_reinsurance_section(Request $request)
  {
    $schem = schemaName();

    $gb = $schem['gb'];
    $gl = $schem['gl'];
    $common = $schem['common'];


    $location = $request->get('location');
    $endt_renewal_no = $request->get('endt_renewal_no');

    $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
    $dcontrol = $dcontrol[0];

    $per_combined = $request->get('per_combined');
    $combined_class = $request->get('gcombined');
    if ($per_combined == 'Y') {
      $gclass = $combined_class;
      $combined = 'Y';
    } else {
      $gclass = $dcontrol->class;
      $combined = 'N';
    }

    $polsect = DB::table('' . $gb . '.polsect')->where([
      ['polsect.endt_renewal_no', $dcontrol->endt_renewal_no],
      ['polsect.policy_no', $dcontrol->policy_no],
      ['polsect.location', $location],
    ])->get();

    //  $no_of_recs=count($polsect);

    //  if($no_of_recs==0){
    //   $polremast=Polremast::where('endt_renewal_no',$endt_renewal_no)
    //                   ->where('location',$location)
    //                   ->where('comb_class',$gclass)
    //                   ->where('policy_no',$dcontrol->policy_no)
    //                   ->get();
    //  }

    echo json_encode($polsect[0]);
  }



  public function get_reinsurance_section_sums(Request $request)
  {

    $location = $request->get('location');
    $section = $request->get('section');
    $endt_renewal_no = $request->get('endt_renewal_no');
    $per_combined = $request->get('per_combined');
    $combined_class = $request->get('gcombined');

    if ($per_combined == 'Y') {
      $gclass = $combined_class;
      $combined = 'Y';
    } else {
      $gclass = $dcontrol->class;
      $combined = 'N';
    }
    $polremast = Polremast::where('endt_renewal_no', $endt_renewal_no)
      ->where('comb_class', $gclass)
      ->where('location', $location)
      ->where('section', $section)
      ->get();



    echo json_encode($polremast);
  }

  public function participants(Request $request)
  {

    $endt_renewal_no = $request->get('endt_renewal_no');

    $polrepart = Polrepart::query()->where('endt_renewal_no', $endt_renewal_no);

    return datatables::of($polrepart)

      ->editColumn('location', function ($fn) {

        if (($fn->location == 0) || ($fn->location == null)) {
          return "All Locations";
        } else {
          $polsect = Polsect::where('location', $fn->location)
            ->where('endt_renewal_no', $fn->endt_renewal_no)
            ->get();

          return $polsect[0]->name;
        }
      })
      ->addColumn('broker_name', function ($fn) {

        if($fn->broker_branch == 0){
          $broker_name = 'Direct';
        }
        else{
          $crmast = Crmast::where('branch', $fn->broker_branch)
            ->where('agent', $fn->broker_agent)
            ->first();
          $broker_name = $crmast->name;
        }

        return $broker_name;
      })
      ->addColumn('participant_name', function ($fn) {

        $crmast = Crmast::where('branch', $fn->branch)
          ->where('agent', $fn->agent)
          ->first();

        return $crmast->name;
      })
      ->editColumn('endt_renewal_no', function ($endt) {
        return formatPolicyOrClaim($endt->endt_renewal_no);
      })
      ->editColumn('re_sum_1', function ($sum) {
        return number_format($sum->re_sum_1, 2);
      })
      ->editColumn('re_prem_1', function ($prem) {
        return number_format($prem->re_prem_1, 2);
      })
      ->editColumn('reice_1', function ($prem) {
        return number_format($prem->reice_1, 2);
      })
      ->editColumn('comm_amt_1', function ($com) {
        return number_format($com->comm_amt_1,2);
      })
      ->make(true);
  }

  public function reins_notes(Request $request)
  {

    $endt_renewal_no = $request->get('endt_renewal_no');

    //credit/debit notes which have not been reversed

    $creditmast = Creditmast::query()->where('endt_renewal_no', $endt_renewal_no);

    return datatables::of($creditmast)

      ->editColumn('effective_date', function ($fn) {


        return  formatDate($fn->effective_date);
      })

      ->editColumn('endt_renewal_no', function ($fn) {


        return  formatPolicyOrClaim($fn->endt_renewal_no);
      })


      ->editColumn('sum_insured', function ($fn) {


        return  number_format($fn->sum_insured,2);
      })

      ->editColumn('gross_amount', function ($fn) {


        return  number_format($fn->gross_amount,2);
      })
      ->editColumn('nett_amount', function ($fn) {
        return  number_format($fn->nett_amount,2);
      })
      ->addColumn('net_vat', function ($fn) {
        $net_vat = ($fn->vat_on_prem - $fn->vat_on_comm);
        return  number_format($net_vat,2);
      })
      ->addColumn('member', function ($fn) {
        $crmast = Crmast::where('branch', $fn->branch)
          ->where('agent', $fn->agent_no)
          ->get(['name']);

        return $crmast[0]->name;
      })->addColumn('check', function ($fn) {

        return '<input type="checkbox" name="selected[]" value="' . $fn->dtrans_no . '"/>';
      })
      ->addColumn('reference', function ($fn) {

        $reference =  str_pad($fn->dtrans_no, 6,'0', STR_PAD_LEFT).$fn->account_year;

        return formatReference($reference);
      })

      ->escapeColumns(['member'])


      ->make(true);
  }

  public function reins_notes_display(Request $request)
  {

    $endt_renewal_no = $request->get('endt_renewal_no');

    //credit/debit notes which have not been reversed

    $creditmast = Endtrepart::query()->where('endt_renewal_no', $endt_renewal_no)->where('reverse', '!=', 'Y')->get();

    // return $endt_renewal_no;
    return datatables::of($creditmast)

      ->editColumn('effective_date', function ($fn) {


        return  formatDate($fn->effective_date);
      })
      ->editColumn('endt_renewal_no', function ($fn) {
        return  formatPolicyOrClaim($fn->endt_renewal_no);
      })
      ->editColumn('sum_insured', function ($fn) {
        return  number_format($fn->sum_insured);
      })
      ->editColumn('re_sum_1', function ($fn) {
        return  number_format($fn->re_sum_1,2);
      })
      ->editColumn('comm_amt_1', function ($fn) {
        return  number_format($fn->comm_amt_1,2);
      })
      ->editColumn('re_prem_1', function ($fn) {
        return  number_format($fn->re_prem_1,2);
      })
      ->editColumn('gross_amount', function ($fn) {

        return  number_format($fn->gross_amount);
      })
      ->addColumn('broker', function ($fn) {

        if($fn->broker_branch == 0){
          $broker_name = 'Direct';
        }
        else{
          $crmast = Crmast::where('branch', $fn->broker_branch)
            ->where('agent', $fn->broker_agent)
            ->first();
          $broker_name = $crmast->name;
        }

        return $broker_name;
      })
      ->addColumn('member', function ($fn) {

        $crmast = Crmast::where('branch', $fn->branch)
          ->where('agent', $fn->agent)
          ->get(['name']);

        return $crmast[0]->name;
      })->addColumn('check', function ($fn) {
        $creditmast = Creditmast::where('dtrans_no', $fn->dtran_no)
          ->where('endt_renewal_no',$fn->endt_renewal_no)
          ->where('branch',$fn->branch)
          ->where('agent_no',$fn->agent)
          ->first();
        return '<input type="checkbox" name="selected[]" value="' . $fn->dtran_no . '"/>
          <input type="hidden" name="type[]" value="CR_NOTE"/>
          <input type="hidden" name="doc_type[]" value="' . $creditmast->doc_type . '"/>';
      })
      ->escapeColumns(['member'])
      ->make(true);
  }

  public function facultative_reversal(Request $request)
  {
    $request->validate([
        'selected' => 'required',
        'endt_renewal_no' => 'required',
        'type' => 'required',
    ]);

    DB::beginTransaction();
    try{
      $selected = $request->input('selected');
      $slip_cr = $request->input('type');
      $endt_renewal_no = $request->endt_renewal_no;

      for ($i = 0; $i < count($selected); $i++) {
        $endtrepart_prv = Endtrepart::where('endt_renewal_no',$endt_renewal_no)
          ->where('dtran_no', $selected[$i])
          ->first();
        $result = $this->single_fac_reversal($endt_renewal_no,$endtrepart_prv->branch,$endtrepart_prv->agent,$selected[$i]);

      }
      DB::commit();
      
      return json_encode($result);
    }
    catch(\Throwable $e){
      DB::rollback();
      // dd($e);
      $error_msg = $e->getMessage();
      $reference = $endt_renewal_no;
      $module = __METHOD__;
      $route_name = Route::getCurrentRoute()->getActionName();
      log_error_details($route_name,$error_msg,$reference,$module);

      $status = array('status' => 0);
      return json_encode($status);
    }
  }


  /*new  allocation process */
  public function allocate_credit_reversal($acdet,$prev_reference,$allocation_no){
    /*Moris */
    //INITIAL CREDIT NOTE

    $prev_doc_type = $acdet->doc_type == 'DRN' ? 'CRN' : 'DRN';
    $crd = Acdet::where('endt_renewal_no',$acdet->endt_renewal_no)
                ->where('reference',$prev_reference)
                ->where('branch',$acdet->branch)
                ->where('agent',$acdet->agent)
                ->where('source','CRD')
                ->where('doc_type',$prev_doc_type)->first();

    $allocated_amount = $acdet->unallocated;
    $foreign_amount = $acdet->foreign_unallocated;
    $acdetallo_recs = array();
    $crn_allo = array();
    $drn_allo = array();

    // credit note
    $crn_allo = [
      'DOC_TYPE' => $crd->doc_type,
      'REFERENCE' => $crd->reference,
      'ENTRY_TYPE_DESCR' => $crd->entry_type_descr,
      'AMOUNT' => $allocated_amount *-1,
      'FOREIGN_AMOUNT' => $foreign_amount *-1,
      'FOREIGN_ALLOCATED' => $foreign_amount *-1,
    ];

    //  reversal note 
    $drn_allo = [
        'DOC_TYPE' => $acdet->doc_type,
        'REFERENCE' => $acdet->reference,
        'ENTRY_TYPE_DESCR' => $acdet->entry_type_descr,
        'AMOUNT' => $allocated_amount,
        'FOREIGN_AMOUNT' => $foreign_amount,
        'FOREIGN_ALLOCATED' => $foreign_amount,
    ];
    $common_allo_data = [
        'ALLOCATION_NO' => $allocation_no,
        'POLICY_NO' => $acdet->policy_no,
        'ENDT_RENEWAL_NO' => $acdet->endt_renewal_no,
        'BRANCH' => $acdet->branch,
        'AGENT' => $acdet->agent,
        'CURRENCY_CODE' => $acdet->currency_code,
        'CURRENCY_RATE' => $acdet->currency_rate,
        'ACCOUNT_MONTH' => $acdet->account_month,
        'ACCOUNT_YEAR' => $acdet->account_year,
        'CLIENT_NUMBER' => $acdet->client_number,
        'USER_STR' => Auth::user()->user_name,
        'ALLOCATION_DATE' => Carbon::now(),
    ];

    $crn_allo = array_merge($common_allo_data,$crn_allo);
    $drn_allo = array_merge($common_allo_data,$drn_allo);
    array_push($acdetallo_recs, $crn_allo,$drn_allo);

    foreach($acdetallo_recs as $allo){
        $item_no = (int)Rein_acdetallonew::where('allocation_no', $allocation_no)->count() + 1;
        $allo['ITEM_NO'] = $item_no;
        $allo['ALLOCATION_DATE'] = Carbon::now();
        $allo['DR_CR'] = $allo['AMOUNT'] < 0 ? 'C' : 'D';

        $acdetallo = Rein_acdetallonew::create($allo);
    }
    
  }

  public function updateAcdet($acdet,$prev_reference){
    $prev_doc_type = $acdet->doc_type == 'DRN' ? 'CRN' : 'DRN';
    //credit note
    $crn = Acdet::where('endt_renewal_no',$acdet->endt_renewal_no)
            ->where('reference',$prev_reference)
            ->where('branch',$acdet->branch)
            ->where('agent',$acdet->agent)
            ->where('source','CRD')
            ->where('doc_type',$prev_doc_type)
            ->first();
            
    //reversal
    $drn = Acdet::where('endt_renewal_no',$acdet->endt_renewal_no)
          ->where('reference',$acdet->reference)
          ->where('branch',$acdet->branch)
          ->where('agent',$acdet->agent)
            ->where('source','CRD')
            ->where('doc_type',$acdet->doc_type)
            ->first();

    $crn_acdetallo = DB::select( "SELECT sum(amount) as amount,sum(foreign_amount) as foreign_amount FROM rein_acdetallonew
            where doc_type='$crn->doc_type'  AND reference ='$crn->reference' AND endt_renewal_no ='$crn->endt_renewal_no'")[0];

    $drn_acdetallo = DB::select( "SELECT sum(amount) as amount,sum(foreign_amount) as foreign_amount FROM rein_acdetallonew
            WHERE endt_renewal_no='$drn->endt_renewal_no' AND doc_type='$drn->doc_type' AND reference ='$drn->reference'")[0];

    // original credit note
    $allocated_sum = $crn_acdetallo->amount;
    $f_allocated_sum = $crn_acdetallo->foreign_amount;
    $unallocated_sum = $crn->nett - $allocated_sum;
    $f_unallocated_sum = $crn->foreign_net - $f_allocated_sum;

    $crn->allocated = $allocated_sum;
    $crn->foreign_allocated = $f_allocated_sum;
    $crn->unallocated = $unallocated_sum;
    $crn->foreign_unallocated = $f_unallocated_sum;
    $crn->save();

    // reversal
    $drn_allocated_sum = $drn_acdetallo->amount;
    $drn_f_allocated_sum = $drn_acdetallo->foreign_amount;
    $drn_unallocated_sum = $drn->nett - $drn_allocated_sum;
    $drn_f_unallocated_sum = $drn->foreign_net - $drn_f_allocated_sum;

    $drn->allocated = $drn_allocated_sum;
    $drn->foreign_allocated = $drn_f_allocated_sum;
    $drn->unallocated = $drn_unallocated_sum;
    $drn->foreign_unallocated = $drn_f_unallocated_sum;
    $drn->save();
  }

  public function confirm_facult_placement(Request $request)
  {

    $endt_renewal_no = $request->get('endt_renewal_no');

    $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
    $dcontrol = $dcontrol[0];

    $total_facult_sum = Polremastend::where('endt_renewal_no', $endt_renewal_no)->sum('facult_p_prem_3');

    $total_placed_sum = Endtrepart::where('endt_renewal_no', $endt_renewal_no)
      // ->where('class', $dcontrol->class)
      ->where('dtran_no', '>', 0)
      ->sum('re_prem_1');


    if (round($total_placed_sum,2) == round($total_facult_sum,2)) {

      $status = array('status' => 1);
    } else {

      //check pipcan determine if debit is allowed
      $pipcnam = Pipcnam::where('record_type', 0)->get();
      $pipcnam = $pipcnam[0];

      if ($pipcnam->cession_mode == 'A') {

        $status = array('status' => 1);
      } else {

        $status = array('status' => 0);
      }
    }

    echo json_encode($status);
  }


  public function confirm_express_reinsure(Request $request)
  {

    $endt_renewal_no = $request->get('endt_renewal_no');

    $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
    $dcontrol = $dcontrol[0];

    $polmaster = Polmaster::where('policy_no', $dcontrol->policy_no)->get();
    $polmaster = $polmaster[0];

    $classyear = Classyear::where('class', $dcontrol->class)
            ->where('uw_year', $polmaster->uw_year)
            ->get();
          $classyear = $classyear[0];

    if($classyear->reinclass==0){
      $status = array('status' => 0);
    }else{
      $reinsetup = Reinsetup::where('class', $classyear->reinclass)
      ->where('uw_year', $polmaster->uw_year)
      ->get();
      $reinsetup=$reinsetup[0];
      if($reinsetup->treaty_type==1 or $reinsetup->treaty_type=='1'){

      $total_limits=$reinsetup->company_retention_amt+$reinsetup->quota_share_limit+$reinsetup->surplus_limit_1+$reinsetup->surplus_limit_2+$reinsetup->surplus_limit_3;
      if($total_limits < $polmaster->sum_insured){
        $status = array('status' => 0);
      }else{
        $status = array('status' => 1);
      }
      }else{
        $status = array('status' => 1);
      }
    }
    echo json_encode($status);
  }

  
  function surplus_referral_limit($class,$endt_renewal_no):object {
    $polmaster = Polmaster::where('endorse_no',$endt_renewal_no)->first(['uw_year']);

    $cls_year = Classyear::where('class', $class)
        ->where('uw_year', $polmaster->uw_year)
        ->first(['reinclass', 'treaty_type']);

    $reinsetup = Reinsetup::where('class',$cls_year->reinclass)
        ->where('uw_year',$polmaster->uw_year)
        ->first([
            'surplus_1','surplus_2','surplus_3',
            'surplus_referral_lines_1','surplus_referral_lines_2','surplus_referral_lines_3',
            'surplus_referral_limit_1','surplus_referral_limit_2','surplus_referral_limit_3',
        ]);

    $surplus_referral_lines = 0;
    $surplus_referral_limit = 0;
    $applicable = 'N';
    
    if($reinsetup->surplus_referral_lines_3 != 0){
        $applicable = 'Y';
        $surplus_referral_lines = $reinsetup->surplus_referral_lines_3 ;
        $surplus_referral_limit = $reinsetup->surplus_referral_limit_3 ;
    } 
    elseif($reinsetup->surplus_referral_lines_2 != 0){
        $applicable = 'Y';
        $surplus_referral_lines = $reinsetup->surplus_referral_lines_2 ;
        $surplus_referral_limit = $reinsetup->surplus_referral_limit_2;
    } 
    elseif($reinsetup->surplus_referral_lines_1 != 0){
        $applicable = 'Y';
        $surplus_referral_lines = $reinsetup->surplus_referral_lines_1 ;
        $surplus_referral_limit = $reinsetup->surplus_referral_limit_1;
    }

    return (object) [
      'applicable' =>$applicable,
      'surplus_referral_lines' =>$surplus_referral_lines,
      'surplus_referral_limit' =>$surplus_referral_limit,
    ];
  }


  public function facult_slips_table(Request $request)
  {
    
    $endt_renewal_no = $request->get('endt_renewal_no');

    $slips = Facult_slips::query()->where('endt_renewal_no', $endt_renewal_no)
      ->where('reversed','N');

    return datatables::of($slips)
      ->addColumn('location', function ($fn) {

        if (($fn->location == 0) || ($fn->location == null)) {
          return "All Locations";
        } else {
          $polsect = Polsectend::where('endt_renewal_no', $fn->endt_renewal_no)
            ->where('location', $fn->location)
            ->first();
          $risk = trim($polsect->name);

          $classModel = ClassModel::where('class', $polsect->class)->first();

          if ($classModel->motor_policy == 'Y') {
            $modtl = Modtlpivot::where('endt_renewal_no', $fn->endt_renewal_no)
              ->where('item_no', $fn->location)
              ->first();

            $risk = $modtl->reg_no;
          } else if ($classModel->bypass_location != 'Y') {
            $risk = $polsect->name;
          } 
          
          return $risk;
        }
      })
      ->addColumn('participant_name', function ($fn) {

        $crmast = Crmast::where('branch', $fn->branch)
          ->where('agent', $fn->agent)
          ->first();

        return $crmast->name;
      })
      ->addColumn('broker', function ($fn) {
        if($fn->broker_branch == 0){
          $broker_name = 'Direct';
        }
        else{
          $crmast = Crmast::where('branch', $fn->broker_branch)
            ->where('agent', $fn->broker_agent)
            ->first();
          $broker_name = $crmast->name;
        }
        return $broker_name;
      })
      ->editColumn('slip_no', function ($slip) {
        return formatReference($slip->slip_no);
      })
      ->editColumn('sum_insured', function ($slip) {
        return number_format($slip->sum_insured,2);
      })
      ->editColumn('gross_amount', function ($slip) {
        return number_format($slip->gross_amount,2);
      })
      ->editColumn('comm_amount', function ($slip) {
        return number_format($slip->comm_amount,2);
      })
      ->addColumn('action',function($data){
        $crmast = Crmast::where('branch', $data->branch)
          ->where('agent', $data->agent)
          ->first();

        $credit_notes = Creditmast::where('endt_renewal_no',$data->endt_renewal_no)
          ->where('broker_branch', $data->broker_branch)
          ->where('broker_agent', $data->broker_agent)
          ->where('branch', $data->branch)
          ->where('agent_no', $data->agent)
          ->where('class', $data->class)
          ->where('comb_class', $data->comb_class)
          ->where('location', $data->location)
          ->where('section', $data->section)
          ->sum('gross_amount') ??0;

        $action = '';
        
          $action .= '<button type="button" class="btn btn-outline-primary btn-xs " id="upload-documents"
                data-context_id="'.$data->slip_no.'" data-agent_no="'.$crmast->agent.'" data-branch="'.$crmast->branch.'"
                data-process_code="130" data-dept_id="0">
                <i class="fa fa-file-text-o"></i>  Upload / view Documents
            </button>';
        

        return $action;
      })
      ->addColumn('check', function ($data) {
        return '
          <input type="checkbox" name="selected[]" value="' . $data->slip_no . '"/>
          <input type="hidden" name="type[]" value="SLIP"/>
        ';
      })
      ->escapeColumns(['participant_name'])
      ->make(true);
  }

  public function facult_slips_rev_table(Request $request)
  {
    
    $endt_renewal_no = $request->get('endt_renewal_no');

    $converted_slip_nos = Endtrepart::where('endt_renewal_no', $endt_renewal_no)
      ->whereNotNull('slip_no')
      ->pluck('slip_no')->toArray();

    $slips = Facult_slips::query()->where('endt_renewal_no', $endt_renewal_no)
      ->whereNotIn('slip_no',$converted_slip_nos)
      ->where('reversed','N');

    return datatables::of($slips)
      ->addColumn('location', function ($fn) {

        if (($fn->location == 0) || ($fn->location == null)) {
          return "All Locations";
        } else {
          $polsect = Polsectend::where('endt_renewal_no', $fn->endt_renewal_no)
            ->where('location', $fn->location)
            ->first();
          $risk = trim($polsect->name);

          $classModel = ClassModel::where('class', $polsect->class)->first();

          if ($classModel->motor_policy == 'Y') {
            $modtl = Modtlpivot::where('endt_renewal_no', $fn->endt_renewal_no)
              ->where('item_no', $fn->location)
              ->first();

            $risk = $modtl->reg_no;
          } else if ($classModel->bypass_location != 'Y') {
            $risk = $polsect->name;
          } 
          
          return $risk;
        }
      })
      ->addColumn('participant_name', function ($fn) {

        $crmast = Crmast::where('branch', $fn->branch)
          ->where('agent', $fn->agent)
          ->first();

        return $crmast->name;
      })
      ->addColumn('broker', function ($fn) {
        if($fn->broker_branch == 0){
          $broker_name = 'Direct';
        }
        else{
          $crmast = Crmast::where('branch', $fn->broker_branch)
            ->where('agent', $fn->broker_agent)
            ->first();
          $broker_name = $crmast->name;
        }
        return $broker_name;
      })
      ->editColumn('slip_no', function ($slip) {
        return formatReference($slip->slip_no);
      })
      ->editColumn('sum_insured', function ($slip) {
        return number_format($slip->sum_insured,2);
      })
      ->editColumn('gross_amount', function ($slip) {
        return number_format($slip->gross_amount,2);
      })
      ->editColumn('comm_amount', function ($slip) {
        return number_format($slip->comm_amount,2);
      })
      ->addColumn('action',function($data){
        $crmast = Crmast::where('branch', $data->branch)
          ->where('agent', $data->agent)
          ->first();

        $credit_notes = Creditmast::where('endt_renewal_no',$data->endt_renewal_no)
          ->where('broker_branch', $data->broker_branch)
          ->where('broker_agent', $data->broker_agent)
          ->where('branch', $data->branch)
          ->where('agent_no', $data->agent)
          ->where('class', $data->class)
          ->where('comb_class', $data->comb_class)
          ->where('location', $data->location)
          ->where('section', $data->section)
          ->sum('gross_amount') ??0;

        $action = '';
        if($credit_notes == 0)
        {
          $action .= '<button type="button" class="btn btn-outline-primary btn-xs convert-single-slip"
                data-slip_no="'.$data->slip_no.'" data-partner="'.$crmast->name.'">
                <i class="fa fa-file-text-o"></i>  Generate credit note
            </button>';
        }

        return $action;
      })
      ->addColumn('check', function ($data) {
        return '
          <input type="checkbox" name="selected[]" value="' . $data->slip_no . '"/>
          <input type="hidden" name="type[]" value="SLIP"/>
        ';
      })
      ->escapeColumns(['participant_name'])
      ->make(true);
  }

  
  public function process_facult_slips($reinmast,$data) {
    // dd($data,$reinmast);
    try{
      $broker_branch = $data['broker_branch'];
      $broker_agent = $data['broker_agent'];
      $branch = $data['branch'];
      $agent = $data['agent'];
      $share = $data['share'];
      $comm_rate = $data['comm_rate'];
      $comm_amt = $data['comm_amt'];
      $premium = $data['premium'];
      $sum_insured = $data['sum_insured'];
      $comb_class = $data['comb_class'];
      $class = $data['class'];
      $cede_method = $data['cede_method'];
      $cr_rating = $data['cr_rating'];
      $ppw_code = $data['ppw_code'];
      $reinsurer_fronting_amt = $data['reinsurer_fronting_amt'];
      $endt_renewal_no = $reinmast->endt_renewal_no;

      $vat_amount_on_prem = $data['vat_amount_on_prem'];
      $vat_amount_on_comm = $data['vat_amount_on_comm'];
      $nett_comm_amt = $comm_amt*-1;
      $nett_gross_amount = $premium *-1;
      $this->_fac_gross_prem = $nett_gross_amount;
      $this->_fac_comm_amt = $nett_comm_amt;
      
      //dcontrol record
      $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
      $pipcnam = Pipcnam::first();

      if($pipcnam->charge_vat_on_fac_reins_prem=='Y' && $vat_amount_on_prem == 0 && $dcontrol->vat_rate > 0){
        $vat_amount_on_prem = ($dcontrol->vat_rate * $nett_gross_amount) / 100 ;
        $nett_gross_amount +=  $vat_amount_on_prem;
      }
      
      if($pipcnam->charge_vat_on_reins_comm=='Y' && $vat_amount_on_comm == 0 && $dcontrol->vat_rate > 0){
        
        $vat_amount_on_comm = ($dcontrol->vat_rate * $nett_comm_amt ) / 100 ;
        $nett_comm_amt += $vat_amount_on_comm;

      }

      $vat_amount_on_comm = $vat_amount_on_comm ?? 0;
      $vat_amount_on_prem = $vat_amount_on_prem ?? 0;
      $reinsurer_fronting_amt = $reinsurer_fronting_amt ?? 0;

      $this->_fac_vat_prem_amt = $vat_amount_on_prem ;
      $this->_fac_vat_comm_amt = $vat_amount_on_comm;
      $this->_reinsurer_fronting_amt = $reinsurer_fronting_amt*-1;

      $nett_amount = $this->facultative_nett_amount();


      //polmaster record
      $polmaster = Polmasterend::where('endorse_no', $dcontrol->endt_renewal_no)->first();

      $dtran0_rec = Dtran0::where('rec_no', 0)->first();

      $ppw = PPWParam::where('ppw_code',$ppw_code)->first();
      $ppw_days = $ppw->ppw_days;
      //generate slip no
      $slip_serial = $dtran0_rec->fac_slip_no;
      Dtran0::where('rec_no', 0)->increment('fac_slip_no', (int) '1');

      $slip_no = str_pad($slip_serial, 6,'0', STR_PAD_LEFT).$dtran0_rec->account_year;

      # Validation control to prevent adding mutliple participants on the same risk item
      $participantsCheck = Facult_slips::where('endt_renewal_no', $endt_renewal_no)
        ->where('comb_class', $comb_class)
        ->where('branch', $branch)
        ->where('broker_branch', $branch)
        ->where('broker_agent', $agent)
        ->where('location', $reinmast->location)
        ->where('section', $reinmast->section)
        ->where('reversed', 'N')
        ->exists();

      if (!$participantsCheck) {
        # Add if participant does not exist
        $slip = new Facult_slips;

        $slip->slip_no              = $slip_no;
        $slip->policy_no            = $polmaster->policy_no;
        $slip->endt_renewal_no      = $endt_renewal_no;
        $slip->broker_branch        = $broker_branch;
        $slip->broker_agent         = $broker_agent;
        $slip->branch               = $branch;
        $slip->agent                = $agent;
        $slip->class                = $class;
        $slip->comb_class           = $comb_class;
        $slip->location             = $reinmast->location;
        $slip->section              = $reinmast->section;
        $slip->cede_method          = $cede_method;
        $slip->uw_year              = $polmaster->uw_year;
        $slip->account_year         = $dtran0_rec->account_year;
        $slip->account_month        = $dtran0_rec->account_month;
        $slip->sum_insured          = $sum_insured;
        $slip->gross_amount         = $premium;
        $slip->comm_amount          = $comm_amt;
        $slip->comm_rate            = $comm_rate; 
        $slip->nett_amount          = $nett_amount; 
        $slip->reice                = $share;
        $slip->cr_rating            = $cr_rating;
        $slip->ppw_code             = $ppw_code;
        $slip->ppw_days             = $ppw_days;
        $slip->vat_on_prem          = $vat_amount_on_prem * $dcontrol->currency_rate;
        $slip->vat_on_comm          = $vat_amount_on_comm  * $dcontrol->currency_rate;
        $slip->foreign_vat_on_prem  = $vat_amount_on_prem;
        $slip->foreign_vat_on_comm  = $vat_amount_on_comm;
        $slip->reinsurer_fronting_amt  = $reinsurer_fronting_amt * $dcontrol->currency_rate;
        $slip->foreign_reinsurer_fronting_amt  = $reinsurer_fronting_amt;

        $slip->save();
      }
      return json_encode(['status' => 1,'description' => 'Signing Slip has been generated', 'slip_no'=>$slip_no]);
    } catch (\Throwable $e) {
      throw $e;
    }
  }

  public function cancel_facultative_slips(Request $request)
  {
    try
    {
      $selected = $request->input('selected');
      $endt_renewal_no = $request->endt_renewal_no;

      for ($i = 0; $i < count($selected); $i++) {
        $result = $this->single_facultative_slip_cancellation($endt_renewal_no,$selected[$i]);
      }
      return [
        'status' => 1,
        'message' => 'Facultative slip reversed'
      ];

    }
    catch(\Throwable $e)
    {
      return [
        'status' => 0,
        'message' => 'Failed to reverse debit note'
      ];
    }
  }

  public function single_facultative_slip_cancellation($endt_renewal_no,$slip_no)
  {
    try
    {

      $slip = Facult_slips::where('endt_renewal_no',$endt_renewal_no)
        ->where('slip_no',$slip_no)
        ->first();
      $slip->reversed = 'Y';
      $slip->save();

      return [
        'status' => 1,
        'message' => 'Facultative slip reversed'
      ];

    }
    catch(\Throwable $e)
    {
      throw $e;
    }
  }

  public function convert_signing_slips_to_crDr(Request $request)
  {
    $request->validate([
        'endt_renewal_no' => 'required',
    ]);

    try
    {
      $converted_slip_nos = Endtrepart::where('endt_renewal_no', $request->endt_renewal_no)
        ->whereNotNull('slip_no')
        ->pluck('slip_no')
        ->toArray();

      $slips = Facult_slips::where('endt_renewal_no', $request->endt_renewal_no)
        ->where('reversed','N')
        ->whereNotIn('slip_no',$converted_slip_nos)
        ->get();


      foreach($slips as $slip)
      {
         $this->single_signing_slip_to_crDr($request->endt_renewal_no,$slip->slip_no);
        

      }
      
      $resp = [
        'status' => 1,
        'message' => 'Successfully converted Signing Slips to Credit/Debit notes'
      ];

      return $resp;
    }
    catch(Throwable $e)
    {
      $error_msg = json_encode($e->getMessage());
      $reference = $endt_renewal_no;
      $module = __METHOD__;
      $route_name = Route::getCurrentRoute()->getActionName();
      log_error_details($route_name,$error_msg,$reference,$module);

      return [
        'status' => 0,
        'message' => 'Failed to convert Signing Slips to Credit/Debit notes'
      ];
    }
  }

  public function convert_single_signing_slips_to_crDr(Request $request)
  {
    $request->validate([
        'endt_renewal_no' => 'required',
        'slip_no' => 'required',
    ]);

    try
    {
      $slip = Facult_slips::where('endt_renewal_no', $request->endt_renewal_no)
        ->where('slip_no',$request->slip_no)
        ->where('reversed','N')
        ->first();

      if($slip)
      {
        $this->single_signing_slip_to_crDr($request->endt_renewal_no,$slip->slip_no);
      }

      return [
        'status' => 1,
        'message' => 'SUccessfully converted Signing Slip to Credit/Debit notes'
      ];
    }
    catch(Throwable $e)
    {
      return [
        'status' => 0,
        'message' => 'Failed to convert Signing Slip to Credit/Debit notes'
      ];
    }
  }

  public function single_signing_slip_to_crDr($endt_renewal_no,$slip_no)
  {
    try{
      $slip = Facult_slips::where('endt_renewal_no', $endt_renewal_no)
        ->where('slip_no',$slip_no)
        ->where('reversed','N')
        ->first();


      $ri = Polremastend::where('endt_renewal_no', $endt_renewal_no)
          ->where('comb_class',$slip->comb_class)
          ->where('location',$slip->location)
          ->where('section',$slip->section)
          ->first();

      $data = [
        'cede_method'   => $slip->cede_method,
        'broker_branch' => $slip->broker_branch,
        'broker_agent'  => $slip->broker_agent,
        'branch'        => $slip->branch,
        'agent'         => $slip->agent,
        'share'         => $slip->reice,
        'comm_rate'     => $slip->comm_rate,
        'comm_amt'      => $slip->comm_amount,
        'premium'       => $slip->gross_amount,
        'sum_insured'   => $slip->sum_insured,
        'comb_class'    => $slip->comb_class,
        'class'         => $slip->class,
        'slip_no'       => $slip->slip_no,
        'cr_rating'     => $slip->cr_rating,
        'ppw_code'     => $slip->ppw_code,
        'ppw_days'     => $slip->ppw_days,
        'reinsurer_fronting_amt' => $slip->reinsurer_fronting_amt,       
      ];

      $result = $this->single_fac_placement($ri,$data);

      return $result;
    }
    catch(Throwable $e)
    {
      throw $e;
    }
  }

  public function checkuploaded_docs($entityid, $contextid, $process_code, $dept){
    
		$upload_status = checkDocUploadStatus($entityid, $contextid, $process_code, $dept);

        return $upload_status;
  }

  function facultative_ceding($endt_renewal_no,$class,$location=0,$section=0) {
    
    try{
      $prev_endorsement = (new Risk)->previousEndorsement($endt_renewal_no);
      
      $ri = Polremastend::where('endt_renewal_no', $endt_renewal_no)
        ->where('comb_class',$class)
        ->where('location',$location)
        ->where('section',$section)
        ->first();

      $prev_ri = Polremastend::where('endt_renewal_no', $prev_endorsement)
        ->where('comb_class',$class)
        ->where('location',$location)
        ->where('section',$section)
        ->first();
        
      $count_participants = Endtrepart::where('endt_renewal_no', $endt_renewal_no)
        ->where('class',$class)
        ->whereRaw("nvl(location,0)=$location")
        ->whereRaw("nvl(section,0)=$section")
        ->count();
        
      $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();

      $participants = Endtrepart::where('endt_renewal_no', $endt_renewal_no)
        ->where('class',$class)
        ->whereRaw("nvl(location,0)=$location")
        ->whereRaw("nvl(section,0)=$section")
        ->get();

      $prev_participants = Endtrepart::where('endt_renewal_no', $prev_endorsement)
        ->where('class',$class)
        ->whereRaw("nvl(location,0)=$location")
        ->whereRaw("nvl(section,0)=$section")
        ->get();

      $total_fac_share = $ri->facult_p_reice;
      $fac_prem = $ri->facult_p_prem_3;
      $fac_sum = $ri->facult_p_sum_3;

      /**
       *  Facult was placed but currently no facultative ceded
       * Could be after redoing RI or new endorsement has come in
       * Any trans type [POL,REN,RNS,EXT,RFN,etc]
      **/
      if(
        ((double)$ri->facult_p_reice == 0 && $count_participants > 0) && (double)$ri->facult_p_prem_3 == 0 
      )
      {
        // reverse current
        foreach ($participants as $part){
          $dtrans_no = $part->dtran_no;
          $this->single_fac_reversal($endt_renewal_no,$part->branch,$part->agent,$dtrans_no);
        }
      }
      elseif(
        (double)$ri->facult_p_prem_3 != 0 && $count_participants > 0
      )
      {//ceding done, rework participant cessions
        if((double)$ri->facult_p_reice == 0){
          $total_fac_share = $prev_ri->facult_p_reice;
        }
        
        $rem_fac_share = $total_fac_share;
        
        foreach ($participants as $part){
          $dtrans_no = $part->dtran_no;
          
          if($part->reice_1 <= $rem_fac_share){
            $this->single_fac_placement_update($ri,$part->branch,$part->agent,$dtrans_no,$total_fac_share);

            $rem_fac_share -=(float) $part->reice_1;
          }
        }
      }
      elseif((double)$ri->facult_p_prem_3 != 0 && $count_participants == 0 && !in_array($dcontrol->trans_type,['POL','REN','RNS']))
      {//ceding not done, rework participant cessions from previous
        if((double)$ri->facult_p_reice == 0){
          $total_fac_share = $prev_ri->facult_p_reice;
        }
        
        $rem_fac_share = $total_fac_share;
        
        foreach ($prev_participants as $part){
          $dtrans_no = $part->dtran_no;
          if($part->reice_1 <= $rem_fac_share){
            
            $new_sum = ($part->reice_1 / $total_fac_share) * $fac_sum;
            $new_prem = ($part->reice_1 / $total_fac_share) * $fac_prem;
            $new_comm = ($part->reico_1 * $new_prem)/100;

            $data = [
              'broker_branch'        => $part->broker_branch,
              'broker_agent'         => $part->broker_agent,
              'branch'        => $part->branch,
              'agent'         => $part->agent,
              'share'         => $part->reice_1,
              'comm_rate'     => $part->reico_1,
              'comm_amt'      => $new_comm,
              'premium'       => $new_prem,
              'sum_insured'   => $new_sum,
              'comb_class'    => $part->comb_class,
              'class'         => $part->class,
              'slip_no'       => null,
              'cr_rating'     => $part->cr_rating,
              'ppw_code'     => $part->ppw_code,
              'ppw_days'     => $part->ppw_days,
            ];
            $this->single_fac_placement($ri,$data);

            $rem_fac_share -=(float) $part->reice_1;
          }
        }
      }

      return (object)[
        'status' => 1,
        'msg' => 'Successfully processed facultative'
      ];
    }
    catch(\Throwable $e){
      // dd($e);
      throw $e;
    }
  }

  public function single_fac_reversal($endt_renewal_no,$branch,$agent,$dtrans_no) {
    try{
      $current_user = trim(Auth::user()->user_name);

            //get endtrepart record
      $endtrepart_prv = Endtrepart::where('endt_renewal_no',$endt_renewal_no)
        ->where('branch', $branch)
        ->where('agent', $agent)
        ->where('dtran_no', $dtrans_no)
        ->first();

      $orig_doc_type ='CRN';//original creditmast doc_type
      if($endtrepart_prv->re_prem_1 < 0){
        $orig_doc_type = 'DRN';
      }

      //get credit/debit note to be cancelled
      $creditmast_prv = Creditmast::where('endt_renewal_no',$endt_renewal_no)
        ->where('branch', $branch)
        ->where('agent_no', $agent)
        ->where('dtrans_no', $dtrans_no)
        ->where('doc_type', $orig_doc_type)
        ->first();
        
      $line_no =(int) Creditmast::where('endt_renewal_no',$endt_renewal_no)->max('ln_no') +1 ;
      $acdet_line_no =(int) Acdet::where('endt_renewal_no',$endt_renewal_no)->where('source','CRD')->max('ln_no') +1 ;
      //Acdet record to be cancelled
      $prev_reference =str_pad($creditmast_prv->dtrans_no, 6,'0', STR_PAD_LEFT).$creditmast_prv->account_year;

      //dcontrol record
      $dcontrol = Dcontrol::where('endt_renewal_no', $endtrepart_prv->endt_renewal_no)->first();

      //debitmast record
      $debitmast = Debitmast::where('endt_renewal_no', $dcontrol->endt_renewal_no)->first();

      $reinmast = Polremastend::where('endt_renewal_no', $endtrepart_prv->endt_renewal_no)
        ->where('comb_class',$endtrepart_prv->class)
        ->where('location',$endtrepart_prv->location)
        ->where('section',$endtrepart_prv->section)
        ->first();

      //reverse facult_slip 
      $facult_slip_count = Facult_slips::where('endt_renewal_no',  $dcontrol->endt_renewal_no)
        ->where('broker_branch', $endtrepart_prv->broker_branch)
        ->where('broker_agent', $endtrepart_prv->broker_agent)
        ->where('branch', $endtrepart_prv->branch)
        ->where('agent', $endtrepart_prv->agent)
        ->where('comb_class', $endtrepart_prv->comb_class)
        ->where('class', $endtrepart_prv->class)
        ->where('location',  $endtrepart_prv->location)
        ->where('section',  $endtrepart_prv->section)
        ->where('reversed',  'N')
        ->count();

      if($facult_slip_count > 0)
      {
        $facult_slip = Facult_slips::where('endt_renewal_no',  $dcontrol->endt_renewal_no)
          ->where('broker_branch', $endtrepart_prv->broker_branch)
          ->where('broker_agent', $endtrepart_prv->broker_agent)
          ->where('branch', $endtrepart_prv->branch)
          ->where('agent', $endtrepart_prv->agent)
          ->where('comb_class', $endtrepart_prv->comb_class)
          ->where('class', $endtrepart_prv->class)
          ->where('location',  $endtrepart_prv->location)
          ->where('section',  $endtrepart_prv->section)
          ->where('reversed',  'N')
          ->first();
        $facult_slip->reversed = 'Y';
        $facult_slip->save();
      }


      //delete endtrepart 
      $endtrepart = Endtrepart::where('dtran_no', $dtrans_no)
        ->where('endt_renewal_no',  $dcontrol->endt_renewal_no)
        ->where('class', $endtrepart_prv->class)
        ->where('location',  $endtrepart_prv->location)
        ->where('section',  $endtrepart_prv->section)
        ->delete();

      //delete polrepart record
      $polrepart = Polrepart::where('endt_renewal_no', $dcontrol->endt_renewal_no)
        ->where('dtran_no', $endtrepart_prv->dtran_no)
        ->where('class', $endtrepart_prv->class)
        ->where('location', $endtrepart_prv->location)
        ->where('class', $endtrepart_prv->class)
        ->where('branch', $endtrepart_prv->branch)
        ->where('agent', $endtrepart_prv->agent)
        ->delete();

      //indicate that that credit/debit note has been reversed
      $update_creditmast = Creditmast::where('endt_renewal_no',  $dcontrol->endt_renewal_no)
        ->where('dtrans_no', $dtrans_no)
        ->where('class', $creditmast_prv->class)
        ->where('doc_type', $creditmast_prv->doc_type)
        ->update([
          'reversed' => 'Y'
        ]);

      if (($endtrepart_prv->re_prem_1 ) < 0) {

        $doc_trans = Dtran0::where('rec_no', 0)->get(['credit_no']);
        $dtran_no = $doc_trans[0]->credit_no;
        $dr_cr = 'C';
        $new_doc_type = 'CRN';

        $dtran0 = Dtran0::where('rec_no', 0)->increment('credit_no', (int) '1');
      } else {

        $doc_trans = Dtran0::where('rec_no', 0)->get(['debit_no']);
        $dtran_no = $doc_trans[0]->debit_no;
        $dr_cr = 'D';
        $new_doc_type = 'DRN';

        $dtran0 = Dtran0::where('rec_no', 0)->increment('debit_no', (int) '1');
      }

      //add new creditmast record
      // $creditmast = new Creditmast;
      // $creditmast = $creditmast_prv->replicate();
      $creditmast = new Creditmast($creditmast_prv->getAttributes());

      $creditmast->gross_amount = $creditmast_prv->gross_amount*-1;
      $creditmast->reice = $creditmast_prv->reice;               
      $creditmast->comm_rate = $creditmast_prv->comm_rate;
      $creditmast->comm_amount = $creditmast_prv->comm_amount*-1;
      $creditmast->nett_amount = $creditmast_prv->nett_amount*-1;

      $creditmast->effective_date = $creditmast_prv->effective_date;
      $creditmast->quake_premium = $debitmast->quake_premium;
      $creditmast->quake_comm = $debitmast->quake_comm;
      $creditmast->quake_comm_rate =  $debitmast->quake_comm_rate;
      $creditmast->doc_type = $new_doc_type;
      $creditmast->dr_cr = $dr_cr;
      $creditmast->dtrans_no = $dtran_no;
      $creditmast->account_year = $dcontrol->account_year;
      $creditmast->account_month = $dcontrol->account_month;
      $creditmast->dola = Carbon::now();                   
      $creditmast->tran_no = 0;
      $creditmast->trans_type = $debitmast->trans_type;    
      $creditmast->debit_credit_no = $creditmast_prv->dtrans_no;

      $creditmast->user_str = $dcontrol->user_str;
      $creditmast->type_of_bus = 7;  //facultative outward                                               
      $creditmast->reinsured = 'Y';
      $creditmast->entry_type = 11;
      $creditmast->entry_type_descr = 'REV';
      $creditmast->approved     = 'N';
      $creditmast->tax_rate     = 0;
      $creditmast->tax_amount   = $creditmast_prv->tax_amount *-1;
      $creditmast->levy_rate    = 0;
      $creditmast->levy_amount  = 0;
      $creditmast->mpl_minimum_rate = 0;            
      $creditmast->ln_no   = $line_no;

      $creditmast->ref_doc_type   = "U/W";
      $creditmast->ref_doc  = $prev_reference;
      $creditmast->ref_ln_no = $creditmast_prv->ln_no;
      $creditmast->type = 0;
      $creditmast->local_gross_amt = 0;
      $creditmast->local_comm_amt   = 0;
      $creditmast->local_stamp_duty  = 0;
      $creditmast->local_levy_amount = 0;
      $creditmast->local_tax_amount  = 0;
      $creditmast->local_nett_amount  = 0;

      $creditmast->user_1 = $current_user;

      $creditmast->foreign_comm_amt = $creditmast_prv->foreign_comm_amt *-1;
      $creditmast->foreign_tax_amount = $creditmast_prv->foreign_tax_amount *-1;
      $creditmast->foreign_net = $creditmast_prv->foreign_net *-1;
      $creditmast->foreign_premium = $creditmast_prv->foreign_premium *-1;
      $creditmast->ppw_code = $creditmast_prv->ppw_code;
      $creditmast->save();

      //add to acdet
      $acdet_prv = Acdet::where('endt_renewal_no',$endt_renewal_no)
        ->where('source','CRD')
        ->where('reference',$prev_reference)
        ->where('doc_type',$creditmast_prv->doc_type)
        ->first();
      $acdet = new Acdet($acdet_prv->getAttributes());

      $acdet->reference = str_pad($creditmast->dtrans_no, 6,'0', STR_PAD_LEFT).$creditmast->account_year;
      $acdet->doc_type = $creditmast->doc_type;
      $acdet->ln_no = $acdet_line_no;
      $acdet->account_year = $creditmast->account_year;
      $acdet->account_month = $creditmast->account_month;                    
      $acdet->ref_doc_type = $creditmast->ref_doc_type;
      $acdet->ref_ln_no = $creditmast->ref_ln_no;
      $acdet->ref_doc = $creditmast->ref_doc;

      $acdet->currency_code = $creditmast->currency_code;
      $acdet->currency_rate = $creditmast->currency_rate;
      $acdet->entry_type = $creditmast->entry_type;
      $acdet->entry_type_descr = $creditmast->entry_type_descr;
      $acdet->time=Carbon::now();                           
      $acdet->dola = Carbon::today();
      $acdet->date_effective = $creditmast->effective_date;
      $acdet->date_processed = Carbon::now();
      $acdet->sum_insured = $creditmast->sum_insured;
      $acdet->dr_cr = $creditmast->dr_cr;

      $acdet->gross = $creditmast->gross_amount;
      $acdet->comm_rate = $creditmast->comm_rate;
      $acdet->comm_amt = $creditmast->comm_amount;
      $acdet->nett = $creditmast->nett_amount;
      $acdet->unallocated = $creditmast->nett_amount;
      $acdet->allocated = 0;

      $acdet->stamp_duty = 0;
      $acdet->tax_rate = 0;
      $acdet->tax_amount = 0;
      $acdet->levy_rate = 0;
      $acdet->levy_amount = 0;              
      $acdet->vat_rate = 0;
      $acdet->vat_amount = 0;                  
      $acdet->interest_amt = 0;
      $acdet->foreign_tax_amount = 0;
      $acdet->foreign_stamp_duty = 0;
      
      $acdet->foreign_levy_amount = 0;
      $acdet->foreign_vat_amount = 0;
      $acdet->foreign_sticker_amount = 0;

      $acdet->trans_number = $creditmast->dtrans_no;
      $acdet->user_1 = $dcontrol->user_str;
                   
      $acdet->gl_update = 'N';
      $acdet->sticker_amount = $debitmast->sticker_amount;
      $acdet->extension_amount = $debitmast->extension_amount;
      $acdet->foreign_premium = $creditmast->foreign_premium;

      $acdet->foreign_comm_amount = $creditmast->foreign_comm_amt;
      $acdet->foreign_net = $creditmast->foreign_net;
      $acdet->foreign_unallocated = $creditmast->foreign_net;
      $acdet->foreign_allocated = 0;

      $acdet->foreign_extension_amount = 0;
      $acdet->policy_fund_rate = 0;
      $acdet->policy_fund = 0;
      $acdet->foreign_policy_fund = 0;
      $acdet->narration = 'FACULTATIVE OUTWARD REVERSAL';
      $acdet->phc_fund = 0;
      $acdet->debit_nos = $debitmast->debit_nos;
      $acdet->entry_type_descr1 = $dcontrol->entry_type_descr;
      $acdet->line_no = 1;
      $acdet->agency_no = $creditmast->agent_no;
      $acdet->crm_flag = 'N';
      $acdet->debit_year = $debitmast->account_year;
      $acdet->debit_month = $debitmast->account_month;
      $acdet->ppw_code = $creditmast->ppw_code;

      $acdet->save();

      $this->update_facultative_commission($reinmast);  
      
      $allocation_no = Rein_acdetallonew::next_serial();
      $this->allocate_credit_reversal($acdet,$prev_reference,$allocation_no);
      $this->updateAcdet($acdet,$prev_reference);

      return (object)[
        'status' => 1,
        'msg' => 'Successfully reversed facultative'
      ];
    }
    catch(\Throwable $e){
      throw $e;
    }
  }

  public function single_fac_placement($reinmast,$data):object {
    try {

      $broker_branch = $data['broker_branch'];
      $broker_agent = $data['broker_agent'];
      $branch = $data['branch'];
      $agent = $data['agent'];
      $share = $data['share'];
      $comm_rate = $data['comm_rate'];
      $comm_amt = $data['comm_amt'];
      $premium = $data['premium'];
      $sum_insured = $data['sum_insured'];
      $comb_class = $data['comb_class'];
      $class = $data['class'];
      $cede_method = $data['cede_method'];
      $slip_no = $data['slip_no'];
      $cr_rating = $data['cr_rating'];
      $ppw_code = $data['ppw_code'];
      $ppw_days = $data['ppw_days'];
      $vat_amount_on_prem = $data['vat_amount_on_prem'];
      $vat_amount_on_comm = $data['vat_amount_on_comm'];
      $reinsurer_fronting_amt = $data['reinsurer_fronting_amt'];
      $endt_renewal_no = $reinmast->endt_renewal_no;
      
      $current_user = trim(Auth::user()->user_name);
      //dcontrol record
      $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();

      //polmaster record
      $polmaster = Polmaster::where('endorse_no', $dcontrol->endt_renewal_no)->first();

      //debitmast record
      $debitmast = Debitmast::where('endt_renewal_no', $dcontrol->endt_renewal_no)->first();
      $pipcnam = Pipcnam::where('record_type', 0)->first();
      $dtran0_rec = Dtran0::where('rec_no', 0)->first();

      $categ_branch = Acccater::where('branch',40)->first();
      $line_no =(int) Creditmast::where('endt_renewal_no',$endt_renewal_no)->max('ln_no') +1 ;
      $acdet_line_no =(int) (Acdet::where('endt_renewal_no',$endt_renewal_no)->where('source','CRD')->max('line_no') ??0) +1 ;
 
      /**** NETT AMOUNT COMPUTATIONS *****/
      //if vat is yes then compute vat on facult comm and premium

      $nett_comm_amt = $comm_amt*-1;
      $nett_gross_amount = $premium *-1;
      $this->_fac_gross_prem = $nett_gross_amount;
      $this->_fac_comm_amt = $nett_comm_amt;

      if($pipcnam->charge_vat_on_fac_reins_prem=='Y' && $vat_amount_on_prem == 0 && $dcontrol->vat_rate > 0){
        $vat_amount_on_prem = ($dcontrol->vat_rate * $nett_gross_amount) / 100 ;
        $nett_gross_amount +=  $vat_amount_on_prem;
      }
      
      if($pipcnam->charge_vat_on_reins_comm=='Y' && $vat_amount_on_comm == 0 && $dcontrol->vat_rate > 0){
        
        $vat_amount_on_comm = ($dcontrol->vat_rate * $nett_comm_amt ) / 100 ;
        $nett_comm_amt += $vat_amount_on_comm;

      }

      $vat_amount_on_comm = $vat_amount_on_comm ?? 0;
      $vat_amount_on_prem = $vat_amount_on_prem ?? 0;

      $this->_fac_vat_prem_amt = $vat_amount_on_prem ;
      $this->_fac_vat_comm_amt = $vat_amount_on_comm;
      $this->_reinsurer_fronting_amt = $reinsurer_fronting_amt*-1;
      
      $net_prem = $this->facultative_nett_amount();
      //generate credit/debit dtrans_no
      if ($net_prem > 0) {
        $dtran_no = $dtran0_rec->debit_no;
        $dr_cr = 'D';
        $doc_type = 'DRN';

        Dtran0::where('rec_no', 0)->increment('debit_no', (int) '1');
      } else {
        $dtran_no = $dtran0_rec->credit_no;
        $dr_cr = 'C';
        $doc_type = 'CRN';

        Dtran0::where('rec_no', 0)->increment('credit_no', (int) '1');
      }


                
      $agent_dtl = Crmast::where('branch',$branch)
        ->where('agent',$agent)
        ->first();
      $tax_code = Tax_code::where('tax_code',$agent_dtl->tax_code)->first();

      $wht_tax_rate = $tax_code->tax_rate;
      $wht_tax_amt = ($wht_tax_rate * ($comm_amt *-1))/100;

      if(empty($tax_code)){
        throw new Exception("Tax code not set for the {$agent_dtl->name}", 403);
      }

      $direct_ri_broker_account = Crmast::where('direct_broker_acc','Y')->first();
      if($pipcnam->set_direct_ri_broker_acc == 'Y' && empty($direct_ri_broker_account))
      {
        throw new Exception("Direct Broker account has not been configured", 403);
        
      }

      if($cede_method == 'D' && $pipcnam->set_direct_ri_broker_acc == 'Y'){
        $broker_branch = $direct_ri_broker_account->branch;;
        $broker_agent = $direct_ri_broker_account->agent;
      }
      elseif($cede_method == 'D'){
        $broker_branch = 0;
        $broker_agent = 0;
      }
      
      $endtrepart = new Endtrepart;

      $endtrepart->dtran_no                 = $dtran_no;
      $endtrepart->slip_no                 = $slip_no;
      $endtrepart->policy_no                = $dcontrol->policy_no;
      $endtrepart->uw_year                  = $polmaster->uw_year;
      $endtrepart->endt_renewal_no          = $dcontrol->endt_renewal_no;
      $endtrepart->location                 = $reinmast->location;
      $endtrepart->section                  = $reinmast->section;
      $endtrepart->part_type                = substr($pipcnam->facult_p_code, 0, 1);
      $endtrepart->reino                    = 0;
      $endtrepart->treaty_type              = $reinmast->treaty_type;
      $endtrepart->class                    =  $class;
      $endtrepart->comb_class               = $comb_class;
      $endtrepart->risk_ind                 = 0;     
      $endtrepart->reice_1                  = $share;// $endtrepart->reice_1  = ($reice[$i] * $polremast->facult_p_reice) / 100;
      $endtrepart->reice_2                  = 0;
      $endtrepart->reice_3                  = $share;
      $endtrepart->reico_1                  =  $comm_rate; //(($commission[$i]/$polremast->facult_p_comm_amt_1)*100)*($polremast->facult_p_comm_per/100) ;           
      $endtrepart->reico_2                  =  0;
      $endtrepart->reico_3                  =  $comm_rate;
      $endtrepart->re_sum_1                 = $sum_insured;
      $endtrepart->re_sum_2                 = 0;
      $endtrepart->re_sum_3                 = $sum_insured;
      $endtrepart->re_prem_1                = $premium;
      $endtrepart->re_prem_2                =  0;
      $endtrepart->re_prem_3                =  $premium;
      $endtrepart->comm_amt_1               = $comm_amt;
      $endtrepart->comm_amt_2               = 0;
      $endtrepart->comm_amt_3               = $comm_amt;
      $endtrepart->type_of_bus              = $dcontrol->type_of_bus;
      $endtrepart->treaty_code              = $pipcnam->facult_p_code;
      $endtrepart->branch                   = $branch;
      $endtrepart->agent                    = $agent;
      $endtrepart->broker_branch            = $broker_branch;
      $endtrepart->broker_agent             = $broker_agent;
      $endtrepart->cede_method              = $cede_method;
      $endtrepart->user_str                 = $current_user;

      $endtrepart->account_year             = $dcontrol->account_year;
      $endtrepart->account_month            = $dcontrol->account_month;
      $endtrepart->categ_branch             = $categ_branch->branch;
      $endtrepart->bad                      = 'N';
      $endtrepart->reverse                  = 'N';
      $endtrepart->dr_cr                    = $premium > 0 ? 'D':'C';
      $endtrepart->orig_drcr                = $endtrepart->dr_cr ;
      $endtrepart->rev_dtrano               = 0;
      $endtrepart->orig_dtrano              = $dtran_no;
      $endtrepart->total_sum_insured        = $polmaster->total_sum_insured;
      $endtrepart->total_gross_amount       = $polmaster->endorse_amount;
      $endtrepart->cr_rating                = $cr_rating;
      $endtrepart->ppw_code                = $ppw_code;
      $endtrepart->ppw_days                = $ppw_days;
      
      $endtrepart->save();

      ///add to polrepart records of endtrepart

      $polrepart = new Polrepart;
      $polrepart->policy_no                 = $dcontrol->policy_no;
      $polrepart->uw_year                   = $polmaster->uw_year;
      $polrepart->endt_renewal_no           = $dcontrol->endt_renewal_no;
      $polrepart->location                  = $reinmast->location;
      $polrepart->section                   = $reinmast->section;
      $polrepart->part_type                 = $endtrepart->part_type;
      $polrepart->reino                     = $endtrepart->reino;
      $polrepart->treaty_type               = $endtrepart->treaty_type;
      $polrepart->class                     = $endtrepart->class;
      $polrepart->comb_class                = $endtrepart->comb_class;
      $polrepart->risk_ind                  = $endtrepart->risk_ind;
      $polrepart->reice_1                   = $endtrepart->reice_1;
      $polrepart->reice_2                   = $endtrepart->reice_2;
      $polrepart->reice_3                   = $endtrepart->reice_3;
      $polrepart->reico_1                   = $endtrepart->reico_1;
      $polrepart->reico_2                   = $endtrepart->reico_2;
      $polrepart->reico_3                   = $endtrepart->reico_3;
      $polrepart->re_sum_1                  = $endtrepart->re_sum_1;
      $polrepart->re_sum_2                  = $endtrepart->re_sum_2;
      $polrepart->re_sum_3                  = $endtrepart->re_sum_3;
      $polrepart->re_prem_1                 = $endtrepart->re_prem_1;
      $polrepart->re_prem_2                 = $endtrepart->re_prem_2;
      $polrepart->re_prem_3                 = $endtrepart->re_prem_3;
      $polrepart->comm_amt_1                = $endtrepart->comm_amt_1;
      $polrepart->comm_amt_2                = $endtrepart->comm_amt_2;
      $polrepart->comm_amt_3                = $endtrepart->comm_amt_3;
      $polrepart->type_of_bus               = $endtrepart->type_of_bus;
      $polrepart->treaty_code               = $endtrepart->treaty_code;
      $polrepart->branch                    = $endtrepart->branch;
      $polrepart->agent                     = $endtrepart->agent;
      $polrepart->broker_branch             = $endtrepart->broker_branch;
      $polrepart->broker_agent              = $endtrepart->broker_agent;
      $polrepart->cede_method              = $endtrepart->cede_method;
      $polrepart->dtran_no                  = $endtrepart->dtran_no;
      $polrepart->dr_cr                     = $endtrepart->dr_cr;
      $polrepart->user_str                  = $current_user;
      $polrepart->save();

      //add to credit mast
      $creditmast = new Creditmast;

      $creditmast->doc_type                 = $doc_type;
      $creditmast->dr_cr                    = $dr_cr;
      $creditmast->dtrans_no                = $polrepart->dtran_no;
      $creditmast->account_year             = $dtran0_rec->account_year;
      $creditmast->account_month            = $dtran0_rec->account_month;                
      $creditmast->endt_renewal_no          = $polrepart->endt_renewal_no;
      $creditmast->treaty_code              = $polrepart->treaty_code;
      $creditmast->uw_year                  = $polrepart->uw_year;
      $creditmast->class                    = $polrepart->class;
      $creditmast->comb_class               = $polrepart->comb_class;
      $creditmast->branch                   = $polrepart->branch;
      $creditmast->agent_no                 = $polrepart->agent;
      $creditmast->broker_branch            = $polrepart->broker_branch;
      $creditmast->broker_agent             = $polrepart->broker_agent;
      $creditmast->cede_method              = $polrepart->cede_method;
      $creditmast->policy_no                = $polrepart->policy_no;
      $creditmast->dola                     = carbon::now();                     
      $creditmast->tran_no                  = 0;
      $creditmast->trans_type               = 10;

      if ($pipcnam->cession_mode == 'A') {

        $creditmast->debit_credit_no        = $debitmast->dtrans_no;
        $creditmast->effective_date         = $debitmast->effective_date;
        $creditmast->quake_premium          = $debitmast->quake_premium*-1;
        $creditmast->quake_comm             = $debitmast->quake_comm*-1;
        $creditmast->quake_comm_rate        =  $debitmast->quake_comm_rate;
        $creditmast->fire_comm_rate         = $debitmast->fire_comm_rate;
        $creditmast->fire_premium           = $debitmast->fire_premium*-1;
        $creditmast->fire_comm              = $debitmast->fire_comm*-1;
        $creditmast->future_premium         = $debitmast->future_premium*-1;
        $creditmast->currency_code          = $debitmast->currency_code;
        $creditmast->currency_rate          = $debitmast->currency_rate;
        $creditmast->stamp_duty             = $debitmast->stamp_duty;
        $creditmast->ref_doc                = $debitmast->dtrans_no . '' . $debitmast->account_year;
      } else {

        $creditmast->debit_credit_no        = 123;
        $creditmast->effective_date         = $dcontrol->effective_date;
        $creditmast->quake_premium          = 0;
        $creditmast->quake_comm             = 0;
        $creditmast->quake_comm_rate        =  0;
        $creditmast->fire_comm_rate         = 0;
        $creditmast->fire_premium           = 0;
        $creditmast->fire_comm              = 0;
        $creditmast->future_premium         = 0;
        $creditmast->currency_code          = $dcontrol->currency;
        $creditmast->currency_rate          = $dcontrol->currency_rate;
        $creditmast->stamp_duty             = 0;
        $creditmast->ref_doc                = '123' . $dcontrol->account_year;

      }

      $creditmast->vat_on_prem              = $vat_amount_on_prem;
      $creditmast->vat_on_comm              = $vat_amount_on_comm;
      $creditmast->foreign_vat_on_prem      = $vat_amount_on_prem/$dcontrol->currency_rate;
      $creditmast->foreign_vat_on_comm      = $vat_amount_on_comm/$dcontrol->currency_rate;
      
      $creditmast->sum_insured              = $polrepart->re_sum_1;
      $creditmast->total_gross_amount       = $polmaster->endorse_amount;
      $creditmast->total_sum_insured        = $polrepart->sum_insured;
      $creditmast->gross_amount             = $polrepart->re_prem_1*-1;
      $creditmast->reice                    = $polrepart->reice_1;           
      $creditmast->comm_rate                = $polrepart->reico_1;
      $creditmast->comm_amount              = $polrepart->comm_amt_1*-1;
      $creditmast->tax_rate                 = $wht_tax_rate;
      $creditmast->tax_amount               = $wht_tax_amt;
      $creditmast->nett_amount              = $net_prem;
      $creditmast->reinsurer_fronting_amt   = $reinsurer_fronting_amt*-1;

      $creditmast->user_str                 = $current_user;
      $creditmast->section_sum_insured_1    = $polmaster->section_sum_insured_1;
      $creditmast->section_sum_insured_2    = $polmaster->section_sum_insured_2;
      $creditmast->section_sum_insured_3    = $polmaster->section_sum_insured_3;
      $creditmast->section_sum_insured_4    = $polmaster->section_sum_insured_4;
      $creditmast->section_sum_insured_5    = $polmaster->section_sum_insured_5;
      $creditmast->section_sum_insured_6    = $polmaster->section_sum_insured_6;
      $creditmast->section_premium_1        = $polmaster->section_premium_1*-1;
      $creditmast->section_premium_2        = $polmaster->section_premium_2*-1;
      $creditmast->section_premium_3        = $polmaster->section_premium_3*-1;
      $creditmast->section_premium_4        = $polmaster->section_premium_4*-1;
      $creditmast->section_premium_5        = $polmaster->section_premium_5*-1;
      $creditmast->section_premium_6        = $polmaster->section_premium_6*-1;

      $creditmast->period_to                = $dcontrol->period_to;
      $creditmast->period_from              = $dcontrol->period_from;
      $creditmast->type_of_bus              = 7;  //facultative outward                                               
      $creditmast->reinsured                = 'Y';
      $creditmast->proposal_date            = $dcontrol->prop_date;
      $creditmast->entry_type               = 11;
      $creditmast->entry_type_descr         = $dcontrol->trans_type;
      $creditmast->approved                 = 'N';
      $creditmast->tax_rate                 = 0;
      $creditmast->tax_amount               = 0;
      $creditmast->levy_rate                = 0;
      $creditmast->levy_amount              = 0;
      $creditmast->mpl_minimum_rate         = 0;
      $creditmast->insured                  = $dcontrol->insured;
      $creditmast->client_number            = $dcontrol->client_number;               
      $creditmast->location                 = $polrepart->location;
      $creditmast->section                  = $polrepart->section;
      $creditmast->ln_no                    = $line_no;
      $creditmast->ref_doc_type             = "U/W";
      $creditmast->ref_ln_no                = 0;
      $creditmast->type                     = 0;
      $creditmast->local_gross_amt          = 0;
      $creditmast->local_comm_amt           = 0;
      $creditmast->local_stamp_duty         = 0;
      $creditmast->local_levy_amount        = 0;
      $creditmast->local_tax_amount         = 0;
      $creditmast->local_nett_amount        = $net_prem;
      $creditmast->source_code              = "U/W";
      $creditmast->user_1                   = $current_user;
      $creditmast->cr_rating                = $cr_rating;
      $creditmast->ppw_code                = $ppw_code;
      $creditmast->ppw_days                = $ppw_days;
      $creditmast->reinsurer_fronting_rate = $dcontrol->reinsurer_fronting_rate;

      $creditmast->foreign_comm_amt         = $creditmast->comm_amount/$dcontrol->currency_rate;
      $creditmast->foreign_tax_amount       = $creditmast->tax_amount/$dcontrol->currency_rate;
      $creditmast->foreign_net              = $creditmast->nett_amount/$dcontrol->currency_rate;
      $creditmast->foreign_premium          = $creditmast->gross_amount/$dcontrol->currency_rate;
      $creditmast->foreign_sum              = $creditmast->sum_insured/$dcontrol->currency_rate;
      $creditmast->foreign_reinsurer_fronting_amt = $creditmast->reinsurer_fronting_amt/$dcontrol->currency_rate;
      
      $creditmast->save();
      

      //balance code computation
      $period_to = Carbon::parse($creditmast->period_to);

      $period_from = Carbon::parse($creditmast->effective_date);

      $w_no_of_months = $period_to->diffInMonths($period_from);

      $w_balance_code = 'A';

      if ($creditmast->nett_amount > 0) {

        switch ($w_no_of_months) {
          case 0:
            $w_balance_code = 'F';
            break;

          case 1:
            $w_balance_code = 'E';
            break;
          case 2:
            $w_balance_code = 'D';
            break;
          case 3:
            $w_balance_code = 'C';
            break;
          case 4:
            $w_balance_code = 'B';
            break;

          default:
            $w_balance_code = 'A';
            break;
        }
      } else {

        $w_balance_code = 'G';
      }
    
      //add to acdet
      $acdet=new Acdet;
      $acdet->branch                          = $creditmast->branch;
      $acdet->agent                           = $creditmast->agent_no;
      $acdet->broker_branch                   = $creditmast->broker_branch;
      $acdet->broker_agent                    = $creditmast->broker_agent;
      $acdet->source                          = "CRD";
      $acdet->type                            = $creditmast->type;
      $acdet->acc_type                        = 0;
      $acdet->doc_type                        = $creditmast->doc_type;
      $acdet->ln_no                           = 1;
      $acdet->reference                       = str_pad($creditmast->dtrans_no, 6,'0', STR_PAD_LEFT).$creditmast->account_year;
      $acdet->account_year                    = $dtran0_rec->account_year;
      $acdet->account_month                   = $dtran0_rec->account_month;                    
      $acdet->ref_doc_type                    = $creditmast->ref_doc_type;
      $acdet->ref_ln_no                       = $creditmast->ln_no;
      $acdet->ref_doc                         = $creditmast->ref_doc;
      $acdet->policy_no                       = $creditmast->policy_no;
      $acdet->endt_renewal_no                 = $creditmast->endt_renewal_no;                 
      $acdet->policyholder                    = $polmaster->name;
      $acdet->class                           = $creditmast->class;
      $acdet->comb_class                      = $creditmast->comb_class;
      $acdet->currency_code                   = $creditmast->currency_code;
      $acdet->currency_rate                   = $creditmast->currency_rate;
      $acdet->entry_type                      = $creditmast->entry_type;
      $acdet->entry_type_descr                = $dcontrol->trans_type;
      $acdet->dola                            = Carbon::today();
      $acdet->time                            = Carbon::now();
      $acdet->date_effective                  = $creditmast->effective_date;
      $acdet->date_processed                  = Carbon::now();                   
      $acdet->sum_insured                     = $creditmast->sum_insured;
      $acdet->dr_cr                           = $creditmast->dr_cr;
      $acdet->comm_rate                       = $creditmast->comm_rate;
      $acdet->foreign_stamp_duty              = 0;
      $acdet->foreign_levy_amount             = 0;

      $acdet->vat_amount                      = ($creditmast->foreign_vat_on_prem + $creditmast->foreign_vat_on_comm) * $dcontrol->currency_rate;
      $acdet->fac_vat_on_prem                 = $creditmast->foreign_vat_on_prem  * $dcontrol->currency_rate;
      $acdet->fac_vat_on_comm                 = $creditmast->foreign_vat_on_prem  * $dcontrol->currency_rate;

      $acdet->foreign_fac_vat_on_prem         = $creditmast->foreign_vat_on_prem;
      $acdet->foreign_vat_amount              = $creditmast->foreign_vat_on_prem + $creditmast->foreign_vat_on_comm;
      $acdet->foreign_fac_vat_on_comm         = $creditmast->foreign_vat_on_comm ;
      
      $acdet->foreign_sticker_amount          = 0;

      if ($pipcnam->cession_mode == 'A') {

        $acdet->stamp_duty                    = $debitmast->stamp_duty;
        $acdet->levy_rate                     = $debitmast->levy_rate;
        $acdet->levy_amount                   = $debitmast->levy_amount;
        $acdet->interest_amt                  = $debitmast->interest_amt;
        $acdet->sticker_amount                = $debitmast->sticker_amount;
        $acdet->extension_amount              = $debitmast->extension_amount;
        $acdet->foreign_tax_amount            = $debitmast->foreign_tax_amount;
        $acdet->foreign_stamp_duty            = $debitmast->foreign_stamp_duty;
        $acdet->foreign_levy_amount           = $debitmast->foreign_levy_amount;
        $acdet->foreign_sticker_amount        = $debitmast->foreign_sticker_amount;
        $acdet->phc_fund                      = $debitmast->phc_fund;
        $acdet->debit_nos                     = $debitmast->debit_nos;
        $acdet->policy_fund_rate              = $debitmast->policy_fund_rate;
        $acdet->policy_fund                   = $debitmast->policy_fund;
        $acdet->foreign_policy_fund           = $debitmast->foreign_policy_fund;
        $acdet->foreign_extension_amount      = $debitmast->foreign_extension_amount;
        $acdet->debit_year                    = $debitmast->account_year;
        $acdet->debit_month                   = $debitmast->account_month;
      }

      $acdet->balance_code                    = $w_balance_code;
      $acdet->trans_number                    = $creditmast->dtrans_no;               
      $acdet->treaty_code                     = $creditmast->treaty_code;
      $acdet->uw_year                         = $creditmast->uw_year;                    
      $acdet->client_number                   = $creditmast->client_number;                 
      $acdet->user_1                          = $dcontrol->user_str;                   
      $acdet->gl_update                       = 'N';

      $acdet->gross                           = $creditmast->foreign_premium  * $dcontrol->currency_rate;
      $acdet->comm_amt                        = $creditmast->foreign_comm_amt * $dcontrol->currency_rate;
      $acdet->tax_amount                      = $creditmast->foreign_tax_amount * $dcontrol->currency_rate;
      $acdet->nett                            = $creditmast->foreign_net  * $dcontrol->currency_rate;
      $acdet->unallocated                     = $creditmast->foreign_net  * $dcontrol->currency_rate;
      $acdet->allocated                       = 0;
      $acdet->reinsurer_fronting_amt          = $creditmast->reinsurer_fronting_amt  * $dcontrol->currency_rate;
      

      $acdet->foreign_comm_amount             = $creditmast->foreign_comm_amt ;
      $acdet->foreign_tax_amount              = $creditmast->foreign_tax_amount;
      $acdet->foreign_premium                 = $creditmast->foreign_premium;
      $acdet->foreign_net                     = $creditmast->foreign_net;
      $acdet->foreign_unallocated             = $creditmast->foreign_net;
      $acdet->foreign_allocated               = 0;
      $acdet->foreign_reinsurer_fronting_amt  = $creditmast->reinsurer_fronting_amt;

      $acdet->reinsurer_fronting_rate         = $creditmast->reinsurer_fronting_rate;

      $acdet->narration                       = 'Facultative Outward';
      $acdet->entry_type_descr1               = $dcontrol->trans_type;
      $acdet->line_no                         = $acdet_line_no;
      $acdet->agency_no                       = $creditmast->agent_no;             
      $acdet->crm_flag                        = 'N';
      $acdet->cr_rating                       = $cr_rating;
      $acdet->ppw_code                       = $ppw_code;
      $acdet->ppw_days                       = $ppw_days;

      $acdet->save();

      $this->update_facultative_commission($reinmast);

      return (object)[
        'status' => 1,
        'msg' => 'Successfully placed facultative'
      ];  
    } catch (\Throwable $e) {
      // dd($e);
      throw $e;
    }
  }

  function single_fac_placement_update($reinmast,$branch,$agent,$dtrans_no,$total_fac_share):object {
    try {
      $endt_renewal_no = $reinmast->endt_renewal_no;
      $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
      $pipcnam = Pipcnam::first();
      //get credit/debit note to be cancelled
      $creditmast_prv = Creditmast::where('endt_renewal_no',$endt_renewal_no)
        ->where('branch', $branch)
        ->where('agent_no', $agent)
        ->where('dtrans_no', $dtrans_no)
        ->first();
  
      //Acdet record to be cancelled
      $reference =str_pad($creditmast_prv->dtrans_no, 6,'0', STR_PAD_LEFT).$creditmast_prv->account_year;

      $endtrepart_prv = Endtrepart::where('endt_renewal_no',$endt_renewal_no)
        ->where('branch', $creditmast_prv->branch)
        ->where('class', $creditmast_prv->class)
        ->where('agent', $creditmast_prv->agent_no)
        ->where('dtran_no', $dtrans_no)
        ->first();
        
      $fac_share = $total_fac_share ?? $reinmast->facult_p_reice;
      $fac_prem = $reinmast->facult_p_prem_3;
      $fac_sum = $reinmast->facult_p_sum_3;

      $new_sum = ($endtrepart_prv->reice_1 / $fac_share) * $fac_sum;
      $new_prem = ($endtrepart_prv->reice_1 / $fac_share) * $fac_prem;
      $new_comm = ($endtrepart_prv->reico_1 * $new_prem)/100;
      $vat_amount_on_prem = 0;
      $vat_amount_on_comm = 0;
      $nett_comm_amt = $new_comm*-1;
      $nett_gross_amount = $new_prem *-1;
      $this->_fac_gross_prem = $new_prem;
      $this->_fac_comm_amt = $new_comm;

      if($pipcnam->charge_vat_on_fac_reins_prem=='Y'){
        $vat_amount_on_prem = ($dcontrol->vat_rate * $nett_gross_amount) / 100 ;
        $nett_gross_amount +=  $vat_amount_on_prem;
      } else {
        $vat_amount_on_prem = 0;
      }
      
      if($pipcnam->charge_vat_on_reins_comm=='Y'){
        
        $vat_amount_on_comm = ($dcontrol->vat_rate * $nett_comm_amt ) / 100 ;
        $nett_comm_amt += $vat_amount_on_comm;

      } else {
        $vat_amount_on_comm = 0;
      }

      $this->_fac_vat_prem_amt = $vat_amount_on_prem ;
      $this->_fac_vat_comm_amt = $vat_amount_on_comm;
      $this->_reinsurer_fronting_amt = $reinsurer_fronting_amt;

      $new_net = $this->facultative_nett_amount();

      $foreign_new_prem = $new_prem / $dcontrol->currency_rate;
      $foreign_new_comm = $new_comm / $dcontrol->currency_rate;
      $foreign_new_net = $new_net / $dcontrol->currency_rate;

      Endtrepart::where('endt_renewal_no',$endt_renewal_no)
        ->where('branch', $creditmast_prv->branch)
        ->where('class', $creditmast_prv->class)
        ->where('agent', $creditmast_prv->agent_no)
        ->where('dtran_no', $dtrans_no)
        ->update([
          're_sum_1' => $new_sum,
          're_prem_1' => $new_prem,
          'comm_amt_1' => $new_comm,
        ]);

      Polrepart::where('endt_renewal_no',$endt_renewal_no)
        ->where('branch', $creditmast_prv->branch)
        ->where('class', $creditmast_prv->class)
        ->where('agent', $creditmast_prv->agent_no)
        ->where('dtran_no', $dtrans_no)
        ->update([
          're_sum_1' => $new_sum,
          're_prem_1' => $new_prem,
          'comm_amt_1' => $new_comm,
        ]);

      Creditmast::where('endt_renewal_no',$endt_renewal_no)
        ->where('branch', $creditmast_prv->branch)
        ->where('class', $creditmast_prv->class)
        ->where('agent_no', $creditmast_prv->agent_no)
        ->where('dtrans_no', $dtrans_no)
        ->update([
          'sum_insured' => $new_sum,
          'gross_amount' => $new_prem *-1,
          'nett_amount' => $new_net*-1,
          'foreign_premium' => $foreign_new_prem*-1,
          'comm_amount' => $new_comm*-1,
          'foreign_comm_amt' => $foreign_new_comm*-1,
          'foreign_net' => $foreign_new_net*-1,
        ]);

      Acdet::where('endt_renewal_no',$endt_renewal_no)
        ->where('branch', $creditmast_prv->branch)
        ->where('class', $creditmast_prv->class)
        ->where('agent', $creditmast_prv->agent_no)
        ->where('reference', $reference)
        ->where('source', 'CRD')
        ->update([
          'sum_insured' => $new_sum,
          'gross' => $new_prem*-1,
          'comm_amt' => $new_comm*-1,
          'nett' => $new_net*-1,
          'foreign_premium' => $foreign_new_prem*-1,
          'foreign_comm_amount' => $foreign_new_comm*-1,
          'foreign_net' => $foreign_new_net*-1,
        ]);

      $this->update_facultative_commission($reinmast);

      return (object)[
        'status' => 0,
        'msg' => 'Successfully placed facultative'
      ];  
    } catch (\Throwable $e) {
      throw $e;
    }
  }

  public function update_facultative_commission($reinmast): object {
    $total_fac_comm = Endtrepart::where('endt_renewal_no', $reinmast->endt_renewal_no)
        ->where('class',$reinmast->comb_class)
        ->whereRaw("nvl(location,0)=$reinmast->location")
        ->whereRaw("nvl(section,0)=$reinmast->section")
        ->sum('comm_amt_1');

      $avg_fac_comm_rate = 0;

      if($total_fac_comm > 0)
      {
        $avg_fac_comm_rate = ($total_fac_comm * 100)/$reinmast->facult_p_prem_3; 
      }
       
    Polremast::where('endt_renewal_no', $reinmast->endt_renewal_no)
      ->where('comb_class',$reinmast->comb_class)
      ->where('location',$reinmast->location)
      ->where('section',$reinmast->section)
      ->update([
        'facult_p_comm_amt_1' => $total_fac_comm + (double)$reinmast->facult_p_comm_amt_2,
        'facult_p_comm_amt_3' => $total_fac_comm ,
        'facult_p_comm_per' => $avg_fac_comm_rate,
      ]);
      
    Polremastend::where('endt_renewal_no', $reinmast->endt_renewal_no)
      ->where('comb_class',$reinmast->comb_class)
      ->where('location',$reinmast->location)
      ->where('section',$reinmast->section)
      ->update([
        'facult_p_comm_amt_1' => $total_fac_comm + (double)$reinmast->facult_p_comm_amt_2,
        'facult_p_comm_amt_3' => $total_fac_comm ,
        'facult_p_comm_per' => $avg_fac_comm_rate,
      ]);

    return (object)[
      'status' => 1,
      'msg' => 'Facultative commission updated'
    ];
  }

  public function facultative_nett_amount()
  {
    throw_if(is_null($this->_fac_gross_prem),'RuntimeException','Gross amount has not been set');
    throw_if(is_null($this->_fac_comm_amt),'RuntimeException','Commission has not been set');
    throw_if(is_null($this->_fac_vat_prem_amt),'RuntimeException','Vat on Gross amount has not been set');
    throw_if(is_null($this->_fac_vat_comm_amt),'RuntimeException','Vat on Commission has not been set');
    throw_if(is_null($this->_reinsurer_fronting_amt),'RuntimeException','Reinsurer Fronting Amount has not been set');

    $net_amt = ($this->_fac_gross_prem + $this->_fac_vat_prem_amt + $this->_reinsurer_fronting_amt) - ($this->_fac_comm_amt  + $this->_fac_vat_comm_amt) ;

    return $net_amt;
  }

  function get_risk_accup(Request $request) {
    $grade = Gradedown::where('class',$request->uw_class)
                      ->where('uw_year',$request->uw_year)
                      ->where('gradedown',$request->grade)
                      ->first();

    $GradeRiskOccup = null;
    $GradeRiskOccup = RiskCategoryOccup::where('categ_code',$grade->categ_id)->get();

    echo json_encode($GradeRiskOccup);

  }
  function get_reins_limits(Request $request){
    // dd($request->endt_renewal_no);
      $classyear = explode('-', $request->id);
      $gclass = $classyear[0];
      $uw_year = $classyear[1];
      // $endt_no = $request->$endt_renewal_no;
      // dd('dd',$gclass);
      // dd($endt_no);
      $classyear = Classyear::where('class', $gclass)
                        ->where('uw_year', $uw_year)
                        ->first();

      $reinsetup = Reinsetup::where('class', $classyear->reinclass)
                            ->where('uw_year', $uw_year)
                            ->first();

      // $polremastend_one_cls =  Polremastend::where('endt_renewal_no', $request->endt_renewal_no)
      //                                       ->where('comb_class', $gclass)
      //                                       ->get();
      $dcontrol = Dcontrol::select('endt_renewal_no','trans_type')
        ->where('endt_renewal_no',$request->endt_renewal_no)
        ->first();

      $classModel = ClassModel::where('class',$gclass)->first();

      $autoGrade = false;
      $locationGrade = null;
      $GradeRiskOccup = null;
      if($classModel->motor_policy !== 'Y')
      {
        $locations = DB::select("SELECT endt_renewal_no,risk_category,occup_code,COUNT(*) 
          FROM polsectend WHERE endt_renewal_no='$request->endt_renewal_no' AND risk_category IS NOT NULL
          GROUP BY endt_renewal_no,risk_category,occup_code");
        
        (count($locations) > 1 || count($locations) == 0) ? $autoGrade = false : $autoGrade = true;
        if($autoGrade)
        {
          $grades = Gradedown::where('class',$gclass)
            ->where('uw_year',$uw_year)
            ->where('categ_id',$locations[0]->risk_category);

          $riskoccups = RiskCategoryOccup::where('occup_code',$locations[0]->occup_code)
                                          ->where('categ_code',$locations[0]->risk_category);

          if($grades->count() == 1)
          {
            $locationGrade = $grades->first()->gradedown;
          }

          if($riskoccups->count() == 1)
          {
            $GradeRiskOccup = $riskoccups->first()->occup_code;
          }


        }

      }

      $polremastend_one_cls = Polremastend::where('polremastend.endt_renewal_no', $request->endt_renewal_no)
                                          ->where('polremastend.comb_class', $gclass)
                                          ->when($classModel->motor_policy == 'Y', function($query){
                                              $query->leftJoin('modtlpivot', function($join) {
                                                $join->on('polremastend.endt_renewal_no', '=', 'modtlpivot.endt_renewal_no')
                                                  ->on('polremastend.location', '=', 'modtlpivot.item_no');
                                              });
                                            })
                                            ->when($classModel->motor_policy =='N', function($query){
                                              $query->leftJoin('polsectend', function($join) {
                                                  $join->on('polremastend.endt_renewal_no', '=', 'polsectend.endt_renewal_no')
                                                      ->on('polremastend.location', '=', 'polsectend.location');
                                                });
                                              })
                                          ->when($classModel->motor_policy =='Y', function($query){
                                              $query->select('polremastend.*', DB::raw('TRIM(modtlpivot.reg_no) as risk_description'));
                                          })
                                          ->when($classModel->motor_policy =='N', function($query){
                                            $query->select('polremastend.*', DB::raw('TRIM(polsectend.name) as risk_description'));
                                          })
                                          ->get();
      $reinsetup_count = Reinsetup::where('class', $classyear->reinclass)
                                  ->where('uw_year', $uw_year)
                                  ->count();
          // dd('dd',$gclass);
      $fully_reinsured = $this->fully_reinsured($request->endt_renewal_no,$gclass);

      $polremastend_one_cls_count =  Polremastend::where('endt_renewal_no', $request->endt_renewal_no)
                                      ->where('comb_class', $gclass)
                                      ->count();

      $prev_polremastend_one_cls = null;
      $prev_polremastend_one_cls_count = 0;
      if($polremastend_one_cls_count == 0 && in_array($dcontrol->trans_type,['EXT','RFN']))
      {
        $prev_endt = Dcontrol::previous_debittedEndorsement($request->endt_renewal_no);

        $prev_polremastend_one_cls_count =  Polremastend::where('endt_renewal_no', $prev_endt->endt_renewal_no)
            ->where('comb_class', $gclass)
            ->count();
            
        $prev_polremastend_one_cls = Polremastend::where('polremastend.endt_renewal_no', $prev_endt->endt_renewal_no)
          ->where('polremastend.comb_class', $gclass)
          ->leftJoin('polsectend', function($join) {
              $join->on('polremastend.endt_renewal_no', '=', 'polsectend.endt_renewal_no')
                  ->on('polremastend.location', '=', 'polsectend.location');
              })
              ->leftJoin('polsectend', function($join) {
                $join->on('polremastend.endt_renewal_no', '=', 'polsectend.endt_renewal_no')
                    ->on('polremastend.location', '=', 'polsectend.location');
                })
          ->select('polremastend.*', DB::raw('TRIM(polsectend.name) as risk_description'))
          ->get();
      }
        // dd('wewe',$polremastend_one_cls_count,$request->endt_renewal_no,$gclass)  ;                      
      $result = [
        'reinsetup' => $reinsetup,
        'polremastend_one_cls' => $polremastend_one_cls,
        'reinsetup_count' => $reinsetup_count,
        'polremastend_one_cls_count' => $polremastend_one_cls_count,
        'prev_polremastend_one_cls' => $prev_polremastend_one_cls,
        'prev_polremastend_one_cls_count' => $prev_polremastend_one_cls_count,
        'fully_reinsured' =>$fully_reinsured,
        'autoGrade' =>$autoGrade,
        'locationGrade' =>$locationGrade,
        'GradeRiskOccup' =>$GradeRiskOccup,
      ];

      return Response::json($result);

  }
  function fully_reinsured($endt_renewal_no,$class){
    $fully_reinsured = 'N';
    $dcontrol = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();
    $polmaster = Polmaster::where('policy_no',$dcontrol->policy_no)->first();
    $classmodel = ClassModel::where('class',$polmaster->class)->first();
    // dd('tt',$classmodel->class,$class,$polmaster->class,$classmodel->combined);
    if($polmaster->class == $class && $classmodel->combined != 'Y'){
      $polremast_count = Polremastend::where('endt_renewal_no', $endt_renewal_no)->count();
      if ($polremast_count > 0) {
        //confirm if all the amount has been reinsured
        $reinsured_prem = DB::select('select sum(premium_amount_3) as p 
            from polremastend where endt_renewal_no = :endorsement',
                ['endorsement' => $endt_renewal_no]);
  
        $cessioned_prem = DB::select('SELECT sum(nvl(company_prem_3,0)) as company_share_prem,
            sum(nvl(quota_share_premium_3,0)) as quota_share_prem,sum(nvl(mandatory_prem_3,0)) as mand_share_prem,
            sum(nvl(surplus_prem1_3,0)) as surplus_1st_prem,sum(nvl(surplus_prem2_3,0)) as surplus_2nd_prem,
            sum(nvl(surplus_prem_3,0)) as surplus_3rd_prem,sum(nvl(facob_prem_3,0)) as facob_prem, 
            sum(nvl(facult_p_prem_3,0)) as facult_p_prem
            FROM polremastend where endt_renewal_no = :endorsement',
            ['endorsement' => $endt_renewal_no])[0];
  
        $cessioned_prem_tot = 
            $cessioned_prem->company_share_prem + $cessioned_prem->mand_share_prem +
            $cessioned_prem->quota_share_prem + $cessioned_prem->surplus_1st_prem + 
            $cessioned_prem->surplus_2nd_prem + $cessioned_prem->surplus_3rd_prem + 
            $cessioned_prem->facob_prem + $cessioned_prem->facult_p_prem;
  
        if (
        ((round($reinsured_prem[0]->p)) == (round($polmaster->endorse_amount * $dcontrol->currency_rate)) && 
        round($reinsured_prem[0]->p) == round($cessioned_prem_tot)) 
        ) {
            $fully_reinsured = 'Y';
        }
  
    }
    }elseif($polmaster->class != $class && $classmodel->combined == 'Y'){
// dd('wewe');
      $polcmb = Polcmb::where('endt_renewal_no', $endt_renewal_no)->where('class',$class)->first();
      $polremast_count = Polremastend::where('endt_renewal_no', $endt_renewal_no)->where('comb_class',$class)->count();
      // dd($polremast_count);
      if ($polremast_count > 0) {
        //confirm if all the amount has been reinsured
        $reinsured_prem = DB::select('select sum(premium_amount_3) as p 
            from polremastend where endt_renewal_no = :endorsement and comb_class = :comb_class',
                ['endorsement' => $endt_renewal_no,'comb_class'=>$class]);
  
        $cessioned_prem = DB::select('SELECT sum(nvl(company_prem_3,0)) as company_share_prem,
            sum(nvl(quota_share_premium_3,0)) as quota_share_prem,sum(nvl(mandatory_prem_3,0)) as mand_share_prem,
            sum(nvl(surplus_prem1_3,0)) as surplus_1st_prem,sum(nvl(surplus_prem2_3,0)) as surplus_2nd_prem,
            sum(nvl(surplus_prem_3,0)) as surplus_3rd_prem,sum(nvl(facult_p_prem_3,0)) as facult_p_prem, 
            sum(nvl(facob_prem_3,0)) as facob_prem
            FROM polremastend where endt_renewal_no = :endorsement and comb_class = :comb_class',
            ['endorsement' => $endt_renewal_no,'comb_class'=>$class])[0];
  
        $cessioned_prem_tot = 
            $cessioned_prem->company_share_prem + $cessioned_prem->mand_share_prem +
            $cessioned_prem->quota_share_prem + $cessioned_prem->surplus_1st_prem + 
            $cessioned_prem->surplus_2nd_prem + $cessioned_prem->surplus_3rd_prem + 
            $cessioned_prem->facult_p_prem + $cessioned_prem->facob_prem;
        // dd($reinsured_prem,$cessioned_prem_tot);
        if (
        ((round($reinsured_prem[0]->p)) == (round($polcmb->endorse_amount * $dcontrol->currency_rate)) && 
        round($reinsured_prem[0]->p) == round($cessioned_prem_tot)) 
        ) {
            $fully_reinsured = 'Y';
        }
  
    }
    }else{
      $polremast_count = Polremastend::where('endt_renewal_no', $endt_renewal_no)->count();
      if ($polremast_count > 0) {
        //confirm if all the amount has been reinsured
        $reinsured_prem = DB::select('select sum(premium_amount_3) as p 
            from polremastend where endt_renewal_no = :endorsement',
                ['endorsement' => $endt_renewal_no]);
  
        $cessioned_prem = DB::select('SELECT sum(nvl(company_prem_3,0)) as company_share_prem,
            sum(nvl(quota_share_premium_3,0)) as quota_share_prem,sum(nvl(mandatory_prem_3,0)) as mand_share_prem,
            sum(nvl(surplus_prem1_3,0)) as surplus_1st_prem,sum(nvl(surplus_prem2_3,0)) as surplus_2nd_prem,
            sum(nvl(surplus_prem_3,0)) as surplus_3rd_prem,sum(nvl(facult_p_prem_3,0)) as facult_p_prem, 
            sum(nvl(facob_prem_3,0)) as facob_prem
            FROM polremastend where endt_renewal_no = :endorsement',
            ['endorsement' => $endt_renewal_no])[0];
  
        $cessioned_prem_tot = 
            $cessioned_prem->company_share_prem + $cessioned_prem->mand_share_prem +
            $cessioned_prem->quota_share_prem + $cessioned_prem->surplus_1st_prem + 
            $cessioned_prem->surplus_2nd_prem + $cessioned_prem->surplus_3rd_prem + 
            $cessioned_prem->facult_p_prem + $cessioned_prem->facob_prem;
  
        if (
        ((round($reinsured_prem[0]->p)) == (round($polmaster->endorse_amount * $dcontrol->currency_rate)) && 
        round($reinsured_prem[0]->p) == round($cessioned_prem_tot)) 
        ) {
            $fully_reinsured = 'Y';
        }
  
    }
    }

    return $fully_reinsured;
    
  }

  public function previousRiCessionParams(Request $request)
  {
    $endt_renewal_no = $request->endt_renewal_no;
    $class = $request->gclass;
    $location = $request->location;
    $section = $request->section;

    $dcontrol =  Dcontrol::select('endt_renewal_no','trans_type')
        ->where('endt_renewal_no', $endt_renewal_no)
        ->first();

    $currRi =  Polremastend::where('endt_renewal_no', $request->endt_renewal_no)
        ->where('class',$class)
        ->count();

    $prevRi = 0;
    $prevRiCount = 0;
    $currRiCount = 0;

    $prev_endt = Dcontrol::previous_debittedEndorsement($request->endt_renewal_no);

    if(!is_null($section))
    {
      $currRiCount =  Polremastend::where('endt_renewal_no', $request->endt_renewal_no)
        ->where('class',$class)
        ->where('location',$location)
        ->where('section',$section)
        ->count();

      $prevRiCount =  Polremastend::where('endt_renewal_no', $prev_endt->endt_renewal_no)
        ->where('class',$class)
        ->where('location',$location)
        ->where('section',$section)
        ->count();

      $prevRi =  Polremastend::where('endt_renewal_no', $prev_endt->endt_renewal_no)
        ->where('class',$class)
        ->where('location',$location)
        ->where('section',$section)
        ->get();
    }
    else if(!is_null($location))
    {
      $currRiCount =  Polremastend::where('endt_renewal_no', $request->endt_renewal_no)
        ->where('class',$class)
        ->where('location',$location)
        ->count();

      $prevRiCount =  Polremastend::where('endt_renewal_no', $prev_endt->endt_renewal_no)
        ->where('class',$class)
        ->where('location',$location)
        ->count();

      $prevRi =  Polremastend::where('endt_renewal_no', $prev_endt->endt_renewal_no)
        ->where('class',$class)
        ->where('location',$location)
        ->get();
    }
    else if(!is_null($class))
    {
      $currRiCount =  Polremastend::where('endt_renewal_no', $request->endt_renewal_no)
        ->where('class',$class)
        ->count();

      $prevRiCount =  Polremastend::where('endt_renewal_no', $prev_endt->endt_renewal_no)
        ->where('class',$class)
        ->count();

      $prevRi =  Polremastend::where('endt_renewal_no', $prev_endt->endt_renewal_no)
        ->where('class',$class)
        ->get();
    }

    return Response::json([
      'prevRi' => $prevRi,
      'currRiCount' => $currRiCount,
      'prevRiCount' => $prevRiCount,
    ]);
  }

}
