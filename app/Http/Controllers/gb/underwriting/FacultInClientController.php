<?php

namespace App\Http\Controllers\gb\underwriting;

use App\Dcontrol;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\FacultInClient;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Gate;
use App\Http\Requests\SaveFacultInClientRequest;

class FacultInClientController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        Gate::authorize('access-facultin-clients');

        if ($request->ajax()) {
            $data = DB::table('facult_in_clients')
                    ->select('facultin_client_no','name','created_by','updated_by');

            return DataTables::of($data)
                    ->addColumn('action',function($data){
                        $btn = '';

                        if (Gate::check('edit-facultin-client')) {
                            $btn .= '<a href="#" id="edit-facultinclnt" data-id="' . $data->facultin_client_no . '" class="btn btn-xs btn-primary"><i class="fa fa-pencil-square-o"></i> Edit</a>';

                            $btn .= '&nbsp;&nbsp';
                        }

                        if (Gate::check('delete-facultin-client')) {
                            $btn .= '<a href="#" id="delete-facultinclnt" data-id="' . $data->facultin_client_no . '" class="btn btn-xs btn-danger"><i class="fa fa-minus-square-o"></i> Delete</a>';
                        }

                        return $btn;
                    })
                    ->make(true);
        }

        return view('gb.underwriting.facult_in_clients');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(SaveFacultInClientRequest $request)
    {
        Gate::authorize('create-facultin-client');

        $status = 0;

        $loggedInUser = Auth()->user()->user_name;

        $data = $request->validated();

        DB::beginTransaction();
        try {

            $user = new FacultInClient();
            $user->name = $data['name'];
            $user->created_by = $loggedInUser;
            $user->created_at = Carbon::now();
            $user->updated_by = $loggedInUser;
            $user->updated_at = Carbon::now();
            $user->save();

            DB::commit();
            $status = 1;
        } catch (\Throwable $th) {
            throw $th;
            DB::rollBack();
        }

        return array('status' => $status);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\FacultInClient  $facultInClient
     * @return \Illuminate\Http\Response
     */
    public function edit(FacultInClient $facultInClient)
    {
        Gate::authorize('edit-facultin-client');

        return $facultInClient;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\FacultInClient  $facultInClient
     * @return \Illuminate\Http\Response
     */
    public function update(SaveFacultInClientRequest $request, FacultInClient $facultInClient)
    {
        Gate::authorize('edit-facultin-client');

        $status = 0;

        $data = $request->validated();

        $loggedInUser = Auth()->user()->user_name;

        try {
            $facultInClient->name = $data['name'];
            $facultInClient->updated_by = $loggedInUser;
            $facultInClient->updated_at = Carbon::now();
            $facultInClient->save();

            $status = 1;
        } catch (\Throwable $th) {
            throw $th;
        }

        return array('status' => $status);
    }

    public function destroy(FacultInClient $facultInClient)
    {
        Gate::authorize('delete-facultin-client');

        $status = 0;

        DB::beginTransaction();

        try {
            $facultInClient->delete();

            DB::commit();

            $status = 1;

        } catch (\Throwable $th) {
            DB::rollBack();
        }

        return array('status' => $status);
    }

    public function facinclntUsageCheck($facultInClient)
    {
        $facinclntCount = Dcontrol::where('facultin_client_no',$facultInClient)->count();

        $status = $facinclntCount == 0 ? 1 : 0;

        return array('status' => $status);
    }

}
