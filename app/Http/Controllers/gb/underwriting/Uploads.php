<?php
/*maxwell munene*/
namespace App\Http\Controllers\gb\underwriting;

use DB;
use Auth;
use File;
use View;
use Excel;

use Session;
use Response;
use App\Polsec;
use App\Pipstmp;
use App\Polsect;
use App\Dcontrol;
use App\Polsched;
use App\Prosched;

use App\Classsect;
use App\Polmaster;
use App\TravelPlan;

use Carbon\Carbon;
use App\ClassModel;
use App\Polsectend;
use App\TravelPrem;
use App\Imports\DpSchedule;
use Illuminate\Http\Request;
use App\Models\UploadScreens;
use Yajra\Datatables\Datatables;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Input;

use App\Classes\Common\FileUploadManager;
use App\Http\Controllers\gb\underwriting\Risk;

class Uploads extends Controller
{
  public function wiba_employer(Request $request){
    //dd($request);
    $polsect = Polsect::where('policy_no',$request->policy_no)
      ->where('class',$request->class)
      ->update([
        'name'=>$request->employer_name,
        'no_units'=> str_replace(',' ,'', $request->no_persons ),
        'owner_lib'=> str_replace(',' ,'', $request->no_persons ),
        'owner_lib'=> str_replace(',' ,'', $request->no_persons ),
        'total_benefits'=> str_replace(',' ,'', $request->tot_benefits ),
        'sum_insured' => str_replace(',' ,'', $request->tot_benefits ),
        'annual_monthly_salary'=>$request->salary_type,
        'multiple_of_salary'=>$request->multiple_annual,
      ]);

      if($polsect){
        Session::Flash('success','Employer details updated successfully');
      }
      else{
        Session::Flash('error','Employer details failed to update');
      }
  }

	public function wiba_schedule(Request $request){

        $dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)->get();
        $dcontrol = $dcontrol[0];

       $polsect=Polsect::where('policy_no',$request->policy_no)
            ->where('class',$request->class)
            ->where('location',$request->location)
            ->first();

            $item_no = Prosched::where('policy_no',$request->policy_no)
                                ->where('class',$request->class)
                                ->count();
                                
              $item_no = $item_no+1;

        $create_prosched = Prosched::create([
            'policy_no'=>$request->policy_no,
            'endorse_no'=>$request->endt_renewal_no,
            'class'=>$request->class,
            'location'=>$request->location,
            'quantity'=>$item_no,
            's_code'=>2,
            'proposal_no'=>$dcontrol->dprop_no,
            'delete_str'=>'N',
            'location_name'=>$polsect->name,
            'dola'=>Carbon::today(),
            'endorse_date'=>Carbon::today(),
            'head1'=>$request->get('staff_pay_no'),
            'amounta'=>$request->get('employee_amt'),
            'amount2'=>$request->get('employee_benefit'),
            'manu_year'=>0,
            'age'=>$request->get('employee_age'),
            'detail_line'=>$request->get('employee_name'),
            'detail_line2'=>$request->get('employee_role'),
            'categ_class'=>$polsect->class,
            'multiple_of_salary'=>$request->get('multiple_annual'),
            'old_s_code'=>0,
            'user_filler'=>'  '
        ]);

        if($create_prosched){

            return redirect()->back()->with('success','Employee creation was Successful');

        }
        else{
            return redirect()->back()->with('error','Employee creation was not Successful');
        }


    }

    public function wiba_schedule_update(Request $request){
      if($request->delete_item){
        $prosched = Prosched::where('policy_no',$request->ed_policy_no)
                          ->where('location',$request->ed_location)
                          ->where('class',$request->ed_class)
                          ->where('quantity',$request->ed_item_no)
                          ->delete();

        return redirect()->back()->with('success','Employee deleted successfully');
      }

      else{
        $polsect =  Polsect::where('policy_no',$request->ed_policy_no)
                          ->where('location',$request->ed_location)
                          ->where('class',$request->ed_class)
                          ->first();

        $prosched = Prosched::where('policy_no',$request->ed_policy_no)
                          ->where('location',$request->ed_location)
                          ->where('class',$request->ed_class)
                          ->where('quantity',$request->ed_item_no)
                          ->update([
                            'head1' => $request->ed_staff_pay_no,
                            'detail_line' => $request->ed_employee_name,
                            'detail_line2' => $request->ed_employee_role,
                            'age' => $request->ed_employee_age,
                            'multiple_of_salary' => $polsect->multiple_of_salary,
                            'amounta' => $request->ed_employee_amt,
                            //'amount2' => $request->ed_employee_amt*$polsect->multiple_of_salary,
                            'dola' => Carbon::today()
                          ]);

          return redirect()->back()->with('success','Employee updated successfully');
      }
    }


	public function wiba_upload(Request $request){
		//dd($request);

		$dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)->get();
		$dcontrol = $dcontrol[0];

		$polsect=Polsect::where('policy_no',$request->policy_no)
                    ->where('location',$request->location)
                    ->first();

        $item = 0;
        $sum_total = 0;
        $prem_total =0;

		if($request->hasfile('emp_data')){

            //Get uploaded File Details
		    $path = $request->file('emp_data')->getRealPath(); 
		    $extension = File::extension($request->file('emp_data')->getClientOriginalName());

		if($extension == "csv" || $extension == "xls" || $extension == "xlsx"){
        $excel_data = Excel::load($path, function($reader){})->get();
        //return redirect()->with('success','Correct file uploaded');
        if(!empty($excel_data) && $excel_data->count()){
        	$count = $excel_data->count();

          	foreach ($excel_data as $value) {
          		$item_no = Prosched::where('policy_no',$request->policy_no)
                                ->where('class',$request->class)
                                ->count();
              $item = $item_no+1;

          		//dd($value);
          		$multiple_of_salary = $request->multiple_annual;
          		$benefit = $multiple_of_salary*$value->earnings;
          		
            
            $create_prosched = Prosched::create([
              'policy_no'=>$request->policy_no,
              'endorse_no'=>$request->endt_renewal_no,
              'class'=>$request->class,
              'location'=>$request->location,
              'quantity'=>$item,
              's_code'=>$item,
              'proposal_no'=>$dcontrol->dprop_no,
              'delete_str'=>'N',
              'location_name'=>$polsect->name,
              'dola'=>Carbon::today(),
              'endorse_date'=>Carbon::today(),
              'head1'=>(int)$value->staffpay_no,
              'amounta'=>$value->earnings,
              //'amount2'=>$benefit,
              'manu_year'=>0,
              'age'=>$value->employee_age,
              'detail_line'=>$value->employee_name,
              'detail_line2'=>$value->employee_position,
              'categ_class'=>$polsect->class,
              'multiple_of_salary'=>$multiple_of_salary,
              'old_s_code'=>0,
              'user_filler'=>'  '
            ]); 

            //dd($create_prosched);
            //get class sections
            $classsect=Classsect::where('class',$request->class)->get();



            /*foreach($classsect as $sect){

            	if($sect->si_type=='S'){
            		$head1 = 'SUM INSURED';
            	}
            	else{
            		$head1 = 'LIABILITY';
            	}
            	 
            	$create_polsched = Polsched::create([
            		'policy_no'=>$request->policy_no,
            		'company_code'=>$dcontrol->company_class_code,
            		'categ_class'=>$polsect->categ_class,
            		'delete_str'=>'N',
            		'class'=>$dcontrol->class,
            		'location'=>$request->location,
            		'proposal_no'=>$dcontrol->dprop_no,
            		'location_name'=>$polsect->location,
            		'section_rate'=>$sect->rate,
            		'section_no'=>$sect->section_no,
            		'endorse_date'=>$dcontrol->effective_date,
            		'endorse_no'=>$dcontrol->endt_renewal_no,
            		'head1'=>$head1,
            		'escalate'=>'N',
            		'escalate_rate'=>0,
            		'declare'=>'N',
            		'declare_rate'=>0,
            		'discount'=>'N',
            		'discount_rate'=>0,
            		'item_no'=>$item,
            		'amounta'=>$benefit,
            		'amount2'=>$benefit,
            		's_code'=>99,
            		'detail_line'=>$sect->section_description,
            		'detail_line2'=>$sect->section_description,
            		'classgrp'=>$sect->classgrp

            	]);

            	//create polsec
            	$polsec_create = Polsec::create([
            		'policy_no'=>$request->policy_no,
            		'class'=>$dcontrol->class,
            		'location'=>$request->location,
            		'section_no'=>$sect->section_no,
            		'endt_renewal_no'=>$dcontrol->endt_renewal_no,
            		'gross_sum_insured'=>$benefit,
            		'sum_insured'=>$benefit,
            		'premium'=>$benefit*$sect->rate/$sect->base,
            		'quake_premium'=>0,
            		'endorse_amount'=>$benefit*$sect->rate/$sect->base,
            		'endorse_date'=>$dcontrol->effective_date,
            		'declare'=>'N',
            		'escalate'=>'N',
            		'rate'=>$sect->rate,
            		'quake_rate'=>0,
            		'item_no'=>$item,
            		'rate1'=>$sect->rate,
            		'base'=>$sect->base,
            		'first_rate'=>0,
            		'first_sum'=>0,
            		'prev_premium'=>0,
            		'no_units'=>$count,
            		'sticker_amount'=>0,
            		'discount_r'=>0,
            		'discount_p'=>0,
            		'line_no'=>$item-1,
            		'total_loadings'=>0,
            		'total_discounts'=>0,
            		'gross_premium'=>$benefit*$sect->rate/$sect->base,
            		'net_premium'=>$benefit*$sect->rate/$sect->base,
            		'pvt_prem'=>0,
            		'pvt_sum_insured'=>0
            	]);

            	$sum_total = $sum_total+$benefit;
            	$prem_total = $prem_total+($benefit*$sect->rate/$sect->base);
            }*/

            //end foreach


        	}

            if($create_prosched){

            	//update polsect
            	/*$polsect=Polsect::where('policy_no',$request->policy_no)
                    			->where('location',$request->location)
                    			->update([
                    				'total_sum_insured'=>$sum_total,
                    				'total_premium'=> $prem_total,
                    				'endorse_amount'=>$prem_total,
                    			]);  

                $polmaster = Polmaster::where('policy_no',$request->policy_no)
                						->update([
                							'annual_premium'=>$prem_total,
                							'endorse_amount'=>$prem_total,
                							'renewal_premium'=>$prem_total,
                							'sum_insured'=>$sum_total,
                							'total_sum_insured'=>$sum_total,
                							'first_premium'=>$prem_total,
                							'items_total'=>$item,
                						]);*/

           
            if($extension == "csv"){
                return redirect()->back()->with('success','CSV uploaded successfully');
            }
            elseif ($extension == "xls" || $extension == "xlsx") {
                return redirect()->back()->with('success','Excel sheet uploaded successfully');
            }
            } 
            else{
              return redirect()->back()->with('error','An error was encountered during upload');
            } 
        }   
      }
      else if($extension != "csv" || $extension != "xls" || $extension != "xlsx"){
        return redirect()->back()->with('error','You can only upload .csv, .xls or .xlsl files');
      }
  		}
  		else{
  			return redirect()->back()->with('error','No file chosen. Please Choose a file to upload');
  		}

	}

	public function schedule_datatable(Request $request){
		$model=Prosched::where('policy_no',$request->get('policy_no'))
                      ->where('location',$request->get('location'))
                      ->get();
                      //dd($policy_no +'....'+);

        return datatables::of($model)
        ->addColumn('action', function ($mod) {
            return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
        })
        ->make(true);
	}

	public function download_temp(Request $request)
	{
	    $file = public_path()."/downloads/schedule_template.csv";
	    $headers = array('Content-Type: application/csv',);
	    return Response::download($file, 'schedule_template.csv',$headers);
	}

  public function download_zw_temp(Request $request)
	{
	    $file = public_path()."/downloads/zw_schedule_template.csv";
	    $headers = array('Content-Type: application/csv',);
	    return Response::download($file, 'zw_schedule_template.csv',$headers);
	}
	
	 //personal accident uploads
  public function pa_sched_upload(Request $request){
    if ($request->hasfile('data')) {
      $dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)->get();
      $dcontrol = $dcontrol[0];
      $class = ClassModel::where(['class'=>trim($dcontrol->class)])->first();
      $sections = array();

      $polsect=Polsect::where('policy_no',$request->policy_no)
        ->where('location',$request->location)
        ->first();

      $path = $request->file('data')->getRealPath(); 
      $extension = File::extension($request->file('data')->getClientOriginalName());
      $today = Carbon::today();
      $user = Auth::user()->user_name;

      if($extension == "csv" || $extension == "xls" || $extension == "xlsx"){
        // $excel_data = Excel::load($path, function($reader){})->get();
        // $excel_data = Excel::toCollection(new DpSchedule,$request->file('data'))->first();
        $excel_data = FileUploadManager::excelUpload($request->file('data'));

        //return redirect()->with('success','Correct file uploaded');

        if($class->zw_accident != 'Y'){
          
          if ($polsect->mult_earnings == '' || empty($polsect->mult_earnings)) {
            return redirect()->back()->with('error', 'Kindly add the accident details before you upload employees schedule...');
          }

        }


        if (!empty($excel_data) && $excel_data->count()) {

          # Delete current employee schedule before uploading anew
          $fresh_upload = $request->fresh_upload;
          switch($fresh_upload) {
            case '1':
              $proschDel = Prosched::where('policy_no', $request->policy_no)
                                    ->where('endorse_no', $request->endt_renewal_no)
                                    ->where('location', $request->location)
                                    ->delete();
            break;
            
          }

          $employee_numbers = array();
          $errors = array();
          $count_errors = 0;
          $z = 0;
          $emp_count = 0;
          $section_code = array();
          $prosched_employee_numbers = array();

          $sections = Polsec::where('policy_no', $request->policy_no)
                            ->where('endt_renewal_no', $request->endt_renewal_no)
                            ->pluck('section_no')
                            ->toArray();

          $prosched_employee_numbers = Prosched::where('policy_no',$request->policy_no)
                                              ->where('endorse_no',$request->endt_renewal_no)
                                              ->where('location',$request->location)
                                              ->pluck('head1')
                                              ->toArray();
          
          $prosched = Prosched::where('policy_no',$request->policy_no)
                              ->where('endorse_no',$request->endt_renewal_no)
                              ->where('location',$request->location)
                              ->first();

          
          
          foreach ($excel_data as $value) {
            $count = $count+1;

            //check for duplicates in excel
            for($i=0; $i < count($prosched_employee_numbers); $i++){
              if($prosched_employee_numbers[$i] == trim($value['staffpay_no'])){
                $count_errors++;
                $errors[$z++] = "Duplicate employee number ".trim($value['staffpay_no'])." in the uploaded file";
              }

              // else{
              //   $employee_numbers[$emp_count++] = trim($value->staffpay_no);
              // }
            }

            if(!empty($prosched) && in_array(trim($value['staffpay_no']),$prosched_employee_numbers)){
              
              $count_errors++;
              $errors[$z++] = "Employee number ".trim($value['staffpay_no'])." exists for the endorsement ".$request->endt_renewal_no." in row ".$count;
              
            }

            if($class->zw_accident != 'Y'){

              if(!in_array(trim($value['section_code']), $sections)){
              
                $count_errors++;
                $errors[$z++] = "Section code ".trim($value['section_code'])." in row ". $count ." in you excel template must correspond to the section number under the sections tab on this transaction. Kindly click on back and check to ensure the codes match.";
                
              }

            }

            //add employee number to employees array
            $employee_numbers[$emp_count++] = trim($value['staffpay_no']);
          }

          if($count_errors > 0){

            $endt_renewal_no = $request->endt_renewal_no;
            return view::make('gb.underwriting.upload_gpa_errors',compact('errors','endt_renewal_no'));
            
          } 
          else{

            foreach ($excel_data as $value) {
              $item_no = Prosched::where('policy_no',$request->policy_no)
                                  ->where('class',$request->class)
                                  ->max('quantity');
              
              $earnings = (float) str_replace(',', '', $value['earnings']);

              $item_no = $item_no+1;
              $count = $count+1;

              # Calculate Employee Benefits
              if ($polsect->mult_earnings == 'F') {
                $employeeBenefit = $polsect->total_benefits;
              } else if ($polsect->mult_earnings == 'M') {
                $employeeBenefit = $earnings * $polsect->multiple_of_salary;
              }

              if($class->zw_accident == 'Y'){
                $employeeBenefit = $value['employee_benefit'];
              }

              $create_prosched = Prosched::create([
                'policy_no' => $request->policy_no,
                'endorse_no' => $request->endt_renewal_no,
                'class' => $request->class,
                'location' => $polsect->location,
                'quantity' => $item_no,
                's_code' => $item_no,
                'proposal_no' => $dcontrol->dprop_no,
                'delete_str' => 'N',
                'location_name' => $polsect->name,
                'dola' => Carbon::now(),
                'endorse_date' => Carbon::today(),
                'head1' => (int)$value['staffpay_no'],
                'amounta' => $earnings,
                'manu_year' => 0,
                'age' => $value['employee_age'],
                'detail_line' => $value['employee_name'],
                'detail_line2' => $value['employee_position'],
                'categ_class' => $polsect->class,
                'multiple_of_salary' => $polsect->multiple_of_salary,
                'old_s_code' => 0,
                'user_filler' => '  ',
                'amount2' => $employeeBenefit,
                'section_no' => $value['section_code']
              ]); 
            }
          }

          if($create_prosched){
            if($extension == "csv"){
              return redirect()->back()->with('success','Employees CSV File Uploaded Successfully');
            }
            elseif ($extension == "xls" || $extension == "xlsx") {
              return redirect()->back()->with('success','Employees Excel Sheet File Uploaded Successfully');
            }
          }
          else{
            return redirect()->back()->with('error','An error was encountered during file upload');
          } 
        }   
      }
      else if($extension != "csv" || $extension != "xls" || $extension != "xlsx"){
        return redirect()->back()->with('error','You can only upload .csv, .xls or .xlsl files...');
      }
    }
    else{
      return redirect()->back()->with('error','No file chosen. Please Choose a file to upload');
    }
  }

   //Agriculture schedule upload
   public function agric_sched_upload(Request $request){
   
     $classcateg=$request->classcateg;
     if ($request->hasfile('data')) {
        if($classcateg == "EMP"){
          $risk = New Risk;

          $saveempdtls =$risk->new_fguarantee_staff_upload($request);
          return $saveempdtls;
     

        }else{

            $dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)->first();
            $date_control = ClassModel::select('use_template_dates')->where('class',trim($dcontrol->class))->first();

            $dcon_period_from = Carbon::createFromFormat("Y-m-d", trim(Carbon::parse($dcontrol->period_from)->format('Y-m-d')))->startOfDay();
            $dcon_period_to = Carbon::createFromFormat("Y-m-d", trim(Carbon::parse($dcontrol->period_to)->format('Y-m-d')))->startOfDay();
      
            $dcon_cov_period_from = Carbon::createFromFormat("Y-m-d", trim(Carbon::parse($dcontrol->cov_period_from)->format('Y-m-d')))->startOfDay();
            $dcon_cov_period_to = Carbon::createFromFormat("Y-m-d", trim(Carbon::parse($dcontrol->cov_period_to)->format('Y-m-d')))->startOfDay();
      
            $cover_days = $dcon_cov_period_from->diffInDays($dcon_cov_period_to) + 1;
      
            $polsect=Polsect::where('policy_no',$request->policy_no)
                            ->where('location',$request->location)
                            ->first();
      
            $path = $request->file('data')->getRealPath(); 
            $extension = File::extension($request->file('data')->getClientOriginalName());
            $today = Carbon::today();
            $user = Auth::user()->user_name;
            $class_category =$request->classcateg;
      
            $class = ClassModel::where('class',trim($polsect->class))->first();
      
            $risk = new Risk;
      
            $FileData = $request->file('data');
            $fresh_upload = ($request->fresh_upload == 1 || $request->fresh_upload == '1') ? true : false ;
      
                
            if($extension == "csv" || $extension == "xls" || $extension == "xlsx"){
                
                $excel_data = FileUploadManager::excelUpload($request->file('data'));
                
                  
      
                if ($polsect->total_sum_insured == '' || empty($polsect->total_sum_insured)) {
      
                    return redirect()->back()->with('error', 'Kindly add the sections before you upload employees schedule...');
      
                }
      
                if (!empty($excel_data) && $excel_data->count()) {
                    $member_numbers = array();
                    $errors = array();
                    $count_errors = 0;
                    $z = 0;
                    $member_count = 0;
                    $count = 0;
                    $all_prosched = array();
                    $total_sum_insured = 0;
      
                    $item_no = Prosched::where('policy_no',$request->policy_no)
                                           ->Where(function($query){
                                                $query->Where('delete_str','<>','Y')
                                                    ->OrWhereNull('delete_str');
                                            })
                                            ->max('s_code');
                    
                    $errors[$z++] = "<ul>";
      
                    foreach ($excel_data as $value) {
                    
                        $prosched_count = 0;

                        $prosched = Prosched::where('policy_no', $request->policy_no)
                                            ->where('endorse_no', $request->endt_renewal_no)
                                            ->where('location', $request->location)
                                            //->whereRaw("trim(tin_number) ='".trim($value['tin_number'])."'")
                                            ->whereRaw("trim(head1) ='".trim($value['account_number'])."'")
                                            ->first();
                          
                        if ($prosched != null) {
                            //dd($prosched);
                            $prosched_count = $prosched->count() ;
      
                        }
      
                        //$member_numbers[$member_count++] = trim($value['tin_number']);
                        $member_numbers[$member_count++] = trim($value['account_number']);
                        
                        $item_no = $item_no + 1;
                        $count = $count + 1;
                        $sum_insured = (float) str_replace(',', '', $value['sum_insured']);
      
                        if($sum_insured <= 0){
                          $count_errors++;
                          $errors[$z++] = "<li>Sum Insured of member of account number ".trim($value['account_number'])." cannot be zero or less than zero in row ".$count."</li>";
                        }

                        if($date_control->use_template_dates == 'Y'){

                          if (!Carbon::hasFormat($value['period_from'], 'Y-m-d') || !Carbon::hasFormat($value['period_to'], 'Y-m-d')) {
      
                            $count_errors++;
                            $errors[$z++] = "<li>Please enter the cover dates for member of account number ".trim($value['account_number'])." in this format YYYY-MM-DD at row ".$count."</li>";
                         }
                        }
      
                        // if (empty($value['tin_number'])) {
                          
                        //    $count_errors++;
                        //    $errors[$z++] = "<li>TIN Number is not captured at row ".$count."</li>";
                        // }
      
                        if (empty($value['account_number'])) {
                          
                            $count_errors++;
                            $errors[$z++] = "<li>Account Number is not captured at row ".$count."</li>";
                        }
      
                        if (empty($value['member_name'])) {
      
                            $count_errors++;
                            $errors[$z++] = "<li>Member name is not captured at row ".$count."</li>";
                        }
      
                        try {
                            
                            //$period_from = Carbon::createFromFormat("Y-m-d", trim($value['period_from']));
                            //$period_to = Carbon::createFromFormat("Y-m-d", trim($value['period_to']));

                            if($date_control->use_template_dates == 'Y'){

                              $period_from = Carbon::createFromFormat("Y-m-d", trim(Carbon::parse($value['period_from'])->format('Y-m-d')))->startOfDay();
                              $period_to = Carbon::createFromFormat("Y-m-d", trim(Carbon::parse($value['period_to'])->format('Y-m-d')))->startOfDay();
        
                              if($dcon_period_from->gt($period_from) || $period_to->gt($dcon_period_to) || $period_from->gte($period_to)){
                          
                                $count_errors++;
                                $errors[$z++] = "<li>Member of account number ".trim($value['account_number'])." has policy periods greater than or less than policy cover periods of endorsement ".$request->endt_renewal_no." in row ".$count." Cover Periods: ".$dcon_period_from." - ".$dcon_period_to." Schedule Periods: ".$period_from." - ".$period_to."</li>";
                              }

                            }else{

                              $period_from = $dcon_cov_period_from;
                              $period_to = $dcon_cov_period_to;

                            }
      
                        } catch (\Throwable $e) {
                            //$count_errors++;
                            //$errors[$z++] = "<li>Please enter the dates in the format YYYY-MM-DD at row ".$count."</li>";
                        }
      
                        // if ($risk->validateRegex($value['tin_number'],'PIN') == false) {
      
                        //     $count_errors++;
                        //     $errors[$z++] = "<li>TIN Number format captured at row ".$count." is invalid</li>";
                        // }
                        
                        $total_sum_insured += $sum_insured;

                        if($count_errors < 1)
                        {
      
                            $annual_premium = ($sum_insured / $polsect->total_sum_insured) * $polsect->total_premium;
      
                            $prosched_array = [
                                                'policy_no' => $dcontrol->policy_no,
                                                'endorse_no' => $dcontrol->endt_renewal_no,
                                                'class' => $polsect->class,
                                                'location' => $polsect->location,
                                                'quantity' => ($prosched_count > 0 && !$fresh_upload) ? $prosched->quantity : $item_no,
                                                's_code' => 0,
                                                'proposal_no' => $dcontrol->dprop_no,
                                                'delete_str' => 'N',
                                                'location_name' => $polsect->name,
                                                'dola' => Carbon::now(),
                                                'endorse_date' => Carbon::today(),
                                                'head1' => ($prosched_count > 0 && !$fresh_upload) ? trim($prosched->head1) : trim($value['account_number']),
                                                'amounta' => $sum_insured,
                                                'manu_year' => 0,
                                                'age' => 0,
                                                'detail_line' =>  ($prosched_count > 0 && !$fresh_upload) ? $prosched->detail_line : $value['member_name'],
                                                'detail_line2' => '',
                                                'categ_class' => $polsect->class,
                                                'multiple_of_salary' => 0,
                                                'old_s_code' => 0,
                                                'user_filler' => '  ',
                                                'amount2' => $sum_insured,
                                                'period_from' => $period_from->startOfDay(),
                                                'period_to' => $period_to->startOfDay(),
                                                'annual_premium'=> $annual_premium,
                                                'tin_number' => ($prosched_count > 0 && !$fresh_upload) ? $prosched->tin_number : $value['tin_number'],
                                                'id_number' => ($prosched_count > 0 && !$fresh_upload) ? $prosched->id_number : $value['nin_number']
                                        ];
      
                            array_push($all_prosched, $prosched_array);
                        }
                        //$count = $count + 1;
      
                    } //end loop
                    /***** END LOOP OF SCHEDULE ******/
      
                    $duplicates = collect($member_numbers)->duplicates();
      
                    if ($duplicates->isNotEmpty()) {
      
                        $count_errors++;
                        $errors[$z++] = "<li>Duplicate Account numbers identified. Validate the Account Numbers</li>";
                        
                    }
                    
                    // if((float)$polsect->total_sum_insured != $total_sum_insured){
                    //     $count_errors++;
                    //     $errors[$z++] = "<li>Schedule sum insured should match location sum insured</li>";
                    // }
                            
                    //if (($dcontrol->trans_type == 'CNC' || $dcontrol->trans_type == 'RFN')) {
                    //if (($dcontrol->trans_type == 'CNC')) {
      
                    //  $count_errors++;
                    //  $errors[$z++] = "<li>You cannot upload a schedule for a refund cancellation endorsement</li>";
      
                    //}
      
                    $errors[$z++] = "</ul>";
                    
                    if($count_errors > 0){

                      $endt_renewal_no = $request->endt_renewal_no;
                      return view::make('gb.underwriting.upload_gpa_errors',compact('errors','endt_renewal_no'));
                    } 
                    else{
      
                      DB::beginTransaction();
      
                      try{
      
                        if($fresh_upload) {
                          
                            $proschDel = Prosched::where('policy_no', $dcontrol->policy_no)
                                                  ->where('endorse_no', $dcontrol->endt_renewal_no)
                                                  ->where('location', $polsect->location)
                                                  ->delete();
                         
                        }
                        else{
                                  
                            $del = Prosched::where('policy_no',$dcontrol->policy_no)
                                          ->where('endorse_no',$dcontrol->endt_renewal_no)
                                          ->where('location',$polsect->location)
                                          ->whereIn(DB::raw('TRIM(HEAD1)'), $member_numbers)
                                          ->delete();
                        }
      
                          $create_prosched = Prosched::insert($all_prosched);
      
                          $count_mem = Prosched::where('policy_no',$dcontrol->policy_no)
                                          ->where('endorse_no',$dcontrol->endt_renewal_no)
                                          ->Where(function($query){
                                              $query->Where('delete_str','<>','Y')
                                                  ->OrWhereNull('delete_str');
                                          })
                                          ->count();
      
                          $polsect_upd = Polsect::where('policy_no',$dcontrol->policy_no)
                                                ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                                                ->where('location',$polsect->location)
                                                ->update(['owner_lib' => $count_mem]);
      
                          $polsectend_upd = Polsectend::where('policy_no',$dcontrol->policy_no)
                                                      ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                                                      ->where('location',$polsect->location)
                                                      ->update(['owner_lib' => $count_mem]);
      
                          DB::commit();
      
                          Session::flash("success","Member Schedule Successfully uploaded");
      
                      }catch(\Exception $e){
      
                          DB::rollback();
                               dd($e);     
                          return redirect()->back()->with('error','Please check file and try again');
      
                      }//end catch
                      
                    }
                    
                    if($create_prosched){
                     
                        return redirect()->back()->with('success','Member Schedule Uploaded Successfully');
                      
                    }
                    else{
                        return redirect()->back()->with('error','An error was encountered during file upload');
                    } 
                  }   
      
                }
                else{
                    
                    return redirect()->back()->with('error','You can only upload .csv. Please use the template file format...');
                    
                }
      
            
          

        }
   

    }else{
      return redirect()->back()->with('error','No file chosen. Please Choose a file to upload');
    }
  }

  //domestic package items datatable
  public function items_datatable(Request $request) {
    $prosched = Prosched::where('endorse_no', $request->get('endt_renewal_no'))
    ->where('location',$request->get('location'))
    ->where('s_code', $request->section_no)
    ->where('delete_str',null)
    ->get();

    return datatables::of($prosched)
    ->editColumn('amount2',function($amt){
      return number_format($amt->amount2);
    })
    ->addColumn('action', function ($pro) {
        return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
    })
    ->make(true);
  }

  public function download_temp_dp(Request $request)
      {
        $file = public_path()."/downloads/dp_template.csv";
        $headers = array('Content-Type: application/csv',);
        return Response::download($file, 'dp_template.csv',$headers);
      }

  public function dp_sched_upload(Request $request) {
    if($request->hasfile('data')) {
      $polsect = Polsect::where('policy_no',$request->policy_no)
      ->where('location',$request->location)
      ->first();

      $path = $request->file('data')->getRealPath(); 
      $extension = File::extension($request->file('data')->getClientOriginalName());
      $today = Carbon::today();
      $user = Auth::user()->user_name;

      $classsect = Classsect::where('section_no', $request->section)
      ->where('class', $request->class)
      ->first();

      if($extension == "csv" || $extension == "xls" || $extension == "xlsx"){
        // $excel_data = Excel::load($path, function($reader){})->get();
        // $excel_data = Excel::toCollection(new DpSchedule,$request->file('data'))->first();
        $excel_data = FileUploadManager::excelUpload($request->file('data'));

        if(!empty($excel_data) && $excel_data->count()){
            $i = 0;

            # Section SI
            $polsec = Polsec::where('policy_no', $request->policy_no)
            ->where('endt_renewal_no', $request->endt_renewal_no)
            ->where('location', $request->location)
            ->where('section_no', $request->section)
            ->first();
            $sectionSI = $polsec->sum_insured;

            $uploadedSI = Prosched::where('policy_no',$request->policy_no)
            ->where('endorse_no', $request->endt_renewal_no)
            ->where('location',$request->location)
            ->where('section_no',$request->section)
            ->where('delete_str', null)
            ->sum('amounta');

            $scheduleCount = Prosched::where('policy_no',$request->policy_no)
            ->where('endorse_no', $request->endt_renewal_no)
            ->where('location',$request->location)
            ->where('section_no',$request->section)
            ->where('delete_str', null)
            ->count();

            if ($sectionSI == $uploadedSI) {
              # Exit With Error
              return redirect()->route('loaditemsschedule',['endt_no' =>$request->endt_renewal_no,'pol_no'=>$request->policy_no,'cls_no'=>$request->class,'loc_no'=>$request->location,'sec_no'=>$request->section])->with('error', 'Schedule of items sum insured cannot exceed the section sum insured');
            } else if ($scheduleCount == 0) {
              # No Existing Schedule of Items
              $scheduleSI = 0;

              foreach ($excel_data as $value) {
                $rowValue = str_replace(',', '', $value['price']);
                $scheduleSI = $scheduleSI + $rowValue;
              }

              /* if ((int)$scheduleSI == (int)$sectionSI) {
                echo 'Y';
              } else {
                echo 'N';
              }

              dd($sectionSI, $scheduleSI, $scheduleCount); */

              if ((int)$scheduleSI == (int)$sectionSI) {
                # Upload the Schedule
                $count = Prosched::where('policy_no',$request->policy_no)
                ->where('endorse_no', $request->endt_renewal_no)
                ->where('location', $request->location)
                ->where('section_no', $request->section)
                ->max('quantity');

                foreach ($excel_data as $value) {    
                  $count = $count+1;
    
                  $prosched = new Prosched;
                  $prosched->policy_no    = $request->policy_no;
                  $prosched->endorse_no   = $request->endt_renewal_no;
                  $prosched->location     = $request->location;
                  $prosched->quantity     = $count;
                  $prosched->class        = $request->class;
                  $prosched->s_code       = $request->section;
                  $prosched->amounta      = str_replace(',', '', $value['price']);
                  $prosched->amount2      = str_replace(',', '', $value['price']);
                  $prosched->dola         = Carbon::today();
                  $prosched->endorse_date = Carbon::today();
                  $prosched->detail_line  = $value['name'];
                  $prosched->detail_line2 = $value['make'];
                  $prosched->head1        = $value['serial_number'];
                  $prosched->categ_class  = $polsect->class;
                  $prosched->section_no   = $request->section;
                  $prosched->classgrp     = $classsect->classgrp;
                  $prosched->location_name = $polsect->name;
    
                  $prosched->save();   
                }

                # Items Uploaded Successfully
                return redirect()->route('loaditemsschedule',['endt_no' =>$request->endt_renewal_no,'pol_no'=>$request->policy_no,'cls_no'=>$request->class,'loc_no'=>$request->location,'sec_no'=>$request->section])->with('success','CSV uploaded successfully');
              } else {
                # Exit With Error
                return redirect()->route('loaditemsschedule',['endt_no' =>$request->endt_renewal_no,'pol_no'=>$request->policy_no,'cls_no'=>$request->class,'loc_no'=>$request->location,'sec_no'=>$request->section])->with('error', 'Schedule of items sum insured cannot exceed the section sum insured');
              }
            } else if ($scheduleCount > 0) {
              # Existing Schedule of Items
              $remainderSI = $sectionSI - $uploadedSI;
              $jaziliaSI = 0;

              foreach ($excel_data as $value) {

                $jaziliaSI = $jaziliaSI + str_replace(',' ,'', $value['price']);
              }

              if ($jaziliaSI > $remainderSI) {
                # Jazilia SI Is Greater than Remaining SI
                #Exit With Error
                return redirect()->route('loaditemsschedule',['endt_no' =>$request->endt_renewal_no,'pol_no'=>$request->policy_no,'cls_no'=>$request->class,'loc_no'=>$request->location,'sec_no'=>$request->section])->with('error', 'Schedule of items sum insured cannot exceed the section sum insured');
              } else {
                # Upload the Schedule
                $count = Prosched::where('policy_no', $request->policy_no)
                ->where('endorse_no', $request->endt_renewal_no)
                ->where('location', $request->location)
                ->where('section_no', $request->section)
                ->max('quantity');

                foreach ($excel_data as $value) {    
                  $count = $count + 1;
    
                  $prosched = new Prosched;
                  $prosched->policy_no    = $request->policy_no;
                  $prosched->endorse_no   = $request->endt_renewal_no;
                  $prosched->location     = $request->location;
                  $prosched->quantity     = $count;
                  $prosched->class        = $request->class;
                  $prosched->s_code       = $request->section;
                  $prosched->amounta      = str_replace(',', '', $value['price']);
                  $prosched->amount2      = str_replace(',', '', $value['price']);
                  $prosched->dola         = Carbon::today();
                  $prosched->endorse_date = Carbon::today();
                  $prosched->detail_line  = $value['name'];
                  $prosched->detail_line2 = $value['make'];
                  $prosched->head1        = $value['serial_number'];
                  $prosched->categ_class  = $polsect->class;
                  $prosched->section_no   = $request->section;
                  $prosched->classgrp     = $classsect->classgrp;
                  $prosched->location_name = $polsect->name;
    
                  $prosched->save();   
                }

                # Items Uploaded Successfully
                return redirect()->route('loaditemsschedule',['endt_no' =>$request->endt_renewal_no,'pol_no'=>$request->policy_no,'cls_no'=>$request->class,'loc_no'=>$request->location,'sec_no'=>$request->section])->with('success','CSV uploaded successfully');
              }
            }

            if($prosched){
              if($extension == "csv"){
                return redirect()->route('loaditemsschedule',['endt_no' =>$request->endt_renewal_no,'pol_no'=>$request->policy_no,'cls_no'=>$request->class,'loc_no'=>$request->location,'sec_no'=>$request->section])->with('success','CSV uploaded successfully');
              }
              elseif ($extension == "xls" || $extension == "xlsx") {
                return redirect()->route('loaditemsschedule',['endt_no' =>$request->endt_renewal_no,'pol_no'=>$request->policy_no,'cls_no'=>$request->class,'loc_no'=>$request->location,'sec_no'=>$request->section])->with('success','Excel sheet uploaded successfully');
              }
            }
            else{
              return redirect()->route('loaditemsschedule',['endt_no' =>$request->endt_renewal_no,'pol_no'=>$request->policy_no,'cls_no'=>$request->class,'loc_no'=>$request->location,'sec_no'=>$request->section])->with('error','An error was encountered during upload');
            } 
        }   
      }
      else if($extension != "csv" || $extension != "xls" || $extension != "xlsx"){
        return redirect()->route('loaditemsschedule',['endt_no' =>$request->endt_renewal_no,'pol_no'=>$request->policy_no,'cls_no'=>$request->class,'loc_no'=>$request->location,'sec_no'=>$request->section])->with('error','You can only upload .csv, .xls or .xlsl files');
      }
    }
    else{
      return redirect()->route('loaditemsschedule',['endt_no' =>$request->endt_renewal_no,'pol_no'=>$request->policy_no,'cls_no'=>$request->class,'loc_no'=>$request->location,'sec_no'=>$request->section])->with('error','No file chosen. Please Choose a file to upload');
    }

  }

  # Add Schedule Items
  public function post_dp_items(Request $request) {
    $policy_no = $request->policy_no;
    $endt_renewal_no = $request->endt_renewal_no;
    $location = $request->location;
    $class = $request->class;
    $section = $request->section;

    $item_name = $request->item_name;
    $item_make = $request->item_make;
    $item_serial = $request->item_serial;
    $item_value = $request->item_value;

    $polsect = Polsect::where('policy_no', $policy_no)
    ->where('location', $request->location)
    ->first();

    # Section SI
    $polsec = Polsec::where('policy_no', $policy_no)
    ->where('endt_renewal_no', $endt_renewal_no)
    ->where('location', $location)
    ->where('section_no', $section)
    ->first();
    $sectionSI = $polsec->sum_insured;

    $scheduleCount = Prosched::where('policy_no', $request->policy_no)
    ->where('endorse_no', $request->endt_renewal_no)
    ->where('location', $request->location)
    ->where('delete_str', null)
    ->where('s_code', $request->section)
    ->count();

    # Schedule SI
    $scheduleSI = Prosched::where('policy_no', $request->policy_no)
    ->where('endorse_no', $request->endt_renewal_no)
    ->where('location', $request->location)
    ->where('s_code', $request->section)
    ->where('delete_str', null)
    ->sum('amounta');

    if ($scheduleSI == $sectionSI) {
      # Section SI is equal to Schedule SI
      Session::Flash('error', 'Item(s) not added. The schedule sum insured will exceed section sum insured. Kindly change section SI to add new item');
      return redirect()->back();
    } elseif ($scheduleCount == 0) {
      # Empty Schedule of Items
      $scheduleSI = 0;

      # Check for the Schedule SI
      for ($i = 0; $i < count($item_name); $i++) {
        $scheduleSI = $scheduleSI + str_replace(',' ,'', $item_value[$i]);
      }

      if ($scheduleSI <= $sectionSI) {
        # Upload the schedule of Items
        $count = Prosched::where('policy_no',$request->policy_no)
                ->where('endorse_no', $request->endt_renewal_no)
                ->where('location', $request->location)
                ->where('section_no', $request->section)
                ->max('quantity');
        for ($i = 0; $i < count($item_name); $i++) {
          $count = $count + 1;
    
          $prosched = new Prosched;
          $prosched->policy_no      = $policy_no;
          $prosched->endorse_no     = $endt_renewal_no;
          $prosched->location       = $location;
          $prosched->class          = $class;
          $prosched->s_code         = $section;
          $prosched->location_name  = $polsect->name;
          $prosched->categ_class    = $class;
    
          $prosched->quantity       = $count;
          $prosched->amounta        = str_replace(',' ,'', $item_value[$i]);
          $prosched->amount2        = str_replace(',' ,'', $item_value[$i]);
          $prosched->detail_line    = $item_name[$i];
          $prosched->detail_line2   = $item_make[$i];
          $prosched->head1          = $item_serial[$i];
    
          $prosched->dola           =  Carbon::today();
          $prosched->endorse_date   = Carbon::today();
    
          $prosched->section_no     = $section;
          $prosched->classgrp       = $classsect->classgrp;
          $prosched->save();
        }

        Session::Flash('success', 'Schedule of Item(s) Uploaded Successfully');
        return redirect()->back();
      } else {
        Session::Flash('error', 'Item(s) not added. The schedule sum insured will exceed section sum insured. Kindly change section SI to add new item');
        return redirect()->back();
      }
    } elseif ($scheduleCount > 0) {
      # Where the Policy Contains an Existing Schedule of Items
      # Add the remaining Sum Insured
      $remainderSI = $sectionSI - $scheduleSI;
      $jaziliaSI = 0;

      for ($i = 0; $i < count($item_name); $i++) {
        $jaziliaSI = $jaziliaSI + str_replace(',' ,'', $item_value[$i]);
      }

      if ($jaziliaSI > $remainderSI) {
        # Section SI Will Not Be Equal To Schedule SI
        Session::Flash('error', 'Item(s) not added. The schedule sum insured will exceed section sum insured. Kindly change section SI to add new item');
        return redirect()->back();
      } else {
        # Jazilia SI is Less than Remainder SI
        # Upload the schedule of Items
        $count = Prosched::where('policy_no',$request->policy_no)
                ->where('endorse_no', $request->endt_renewal_no)
                ->where('location', $request->location)
                ->where('section_no', $request->section)
                ->max('quantity');
        for ($i = 0; $i < count($item_name); $i++) {
          $count = $count + 1;
    
          $prosched = new Prosched;
          $prosched->policy_no      = $policy_no;
          $prosched->endorse_no     = $endt_renewal_no;
          $prosched->location       = $location;
          $prosched->class          = $class;
          $prosched->s_code         = $section;
          $prosched->location_name  = $polsect->name;
          $prosched->categ_class    = $class;
    
          $prosched->quantity       = $count;
          $prosched->amounta        = str_replace(',' ,'', $item_value[$i]);
          $prosched->amount2        = str_replace(',' ,'', $item_value[$i]);
          $prosched->detail_line    = $item_name[$i];
          $prosched->detail_line2   = $item_make[$i];
          $prosched->head1          = $item_serial[$i];
    
          $prosched->dola           =  Carbon::today();
          $prosched->endorse_date   = Carbon::today();
    
          $prosched->section_no     = $section;
          $prosched->classgrp       = $classsect->classgrp;
          $prosched->save();
        }

        Session::Flash('success', 'Schedule of Item(s) Uploaded Successfully');
        return redirect()->back();
      }
    }
  }

  public function modify_dp_items(Request $request){
    if($request->delete_item){
      # Delete Schedule Item
      $prosched = Prosched::where('endorse_no', $request->endt_renewal_no)
        ->where('location',$request->location)
        ->where('s_code', $request->section)
        ->where('class',$request->class)
        ->where('quantity',$request->ed_item_no)
        ->delete();

      Session::Flash('success','Item ' . $request->ed_item_name .' deleted successfully');
      return redirect()->back();
    } else {
      # Section SI
      $polsec = Polsec::where('endt_renewal_no', $request->endt_renewal_no)
      ->where('location', $request->location)
      ->where('section_no', $request->section)
      ->first();
      $sectionSI = $polsec->sum_insured;

      # Schedule SI
      $scheduleSI = Prosched::where('endorse_no', $request->endt_renewal_no)
      ->where('location', $request->location)
      ->where('s_code', $request->section)
      ->where('quantity', '<>', $request->ed_item_no)
      ->where('delete_str', null)
      ->sum('amounta');

      $jaziliaSI = $sectionSI - $scheduleSI;
      $itemSI = str_replace(',' ,'', $request->ed_item_value );

      if ($itemSI > $jaziliaSI) {
        # Item SI is beyond the limit
        Session::Flash('error', 'Item not edited. The schedule sum insured will exceed section sum insured. Kindly change section SI to edit item(s)');
        return redirect()->back();
      } else {
        # Item SI Is within the Limit
        $prosched = Prosched::where('endorse_no', $request->endt_renewal_no)
        ->where('location',$request->location)
        ->where('s_code', $request->section)
        ->where('quantity',$request->ed_item_no)
        ->update([
          'amounta' => str_replace(',' ,'', $request->ed_item_value ),
          'amount2' => str_replace(',' ,'', $request->ed_item_value ),
          'detail_line' => $request->ed_item_name,
          'detail_line2' => $request->ed_item_make,
          'head1' => $request->ed_item_serial,
          'dola' => Carbon::today()
        ]);

        Session::Flash('success','Item updated successfully');
        return redirect()->back();
      }
    }
  }

   //upload all risk items
  public function all_risk_upload(Request $request)
  {
    

    if($request->hasfile('data')){
            //Get uploaded File Details
      //dd($request);

      $polsect=Polsect::where('policy_no',$request->policy_no)
                    ->where('location',$request->location)
                    ->first();


      $path = $request->file('data')->getRealPath(); 
      $extension = File::extension($request->file('data')->getClientOriginalName());
      $today = Carbon::today();
      $user = Auth::user()->user_name;


      if($extension == "csv" || $extension == "xls" || $extension == "xlsx"){
        $excel_data = Excel::load($path, function($reader){})->get();
        //return redirect()->with('success','Correct file uploaded');
        if(!empty($excel_data) && $excel_data->count()){
          //dd($excel_data);
            $i = 0;
            foreach ($excel_data as $value) {
              $count = Prosched::where('policy_no',$request->policy_no)
                          ->where('location',$request->location)
                          ->where('s_code',$request->section)
                          ->where('class',$request->class)
                          ->count();

              $count = $count+1;
              //dd($value-)

            $prosched=new Prosched;
              $prosched->policy_no = $request->policy_no;
              $prosched->endorse_no = $request->endt_renewal_no;
              $prosched->location = $request->location;
              $prosched->quantity =$count;
              $prosched->class    = $request->class;
              $prosched->s_code = $request->section;
              $prosched->location_name = $polsect->name;
              $prosched->amounta = $value->price;
              $prosched->amount2 = $value->price;
              $prosched->dola    =  Carbon::today();
              $prosched->endorse_date   = Carbon::today();
              $prosched->detail_line =$value->name;
              $prosched->detail_line2 =$value->make;
              $prosched->head1   = $value->serial_number;
              $prosched->categ_class = $polsect->class;
              //dd($polsched);

            $prosched->save(); 

          }


            if($prosched){
              if($extension == "csv"){
                return redirect()->back()->with('success','CSV uploaded successfully');
              }
              elseif ($extension == "xls" || $extension == "xlsx") {
                return redirect()->back()->with('success','Excel sheet uploaded successfully');
              }
            }
            else{
              return redirect()->back()->with('error','An error was encountered during upload');
            } 
        }   
      }
      else if($extension != "csv" || $extension != "xls" || $extension != "xlsx"){
        return redirect()->back()->with('error','You can only upload .csv, .xls or .xlsl files');
      }
    }
    else{
      return redirect()->back()->with('error','No file chosen. Please Choose a file to upload');
    }

  }
  
  public function download_temp_travel(Request $request)
    {
        $file = public_path()."/downloads/travel_template.csv";
        $headers = array('Content-Type: application/csv',);
        return Response::download($file, 'travel_template.csv',$headers);
    }
  
  public function download_temp_med_members(Request $request)
  {
      $file = public_path()."/downloads/med_members_template.xlsx";
      $headers = array('Content-Type: application/xlsx',);
      return Response::download($file, 'members_template.xlsx',$headers);
  }

  public function download_temp_med_deps(Request $request)
  {
      $file = public_path()."/downloads/med_deps_template.xlsx";
      $headers = array('Content-Type: application/xlsx',);
      return Response::download($file, 'dependants_template.xlsx',$headers);
  }


  public function download_combined_med_sched(Request $request)
  {
      $file = public_path()."/downloads/combined_med_schedule.xlsx";
      $headers = array('Content-Type: application/xlsx',);
      return Response::download($file, 'combined_schedule_template.xlsx',$headers);
  }

  public function agric_download_temp(Request $request)
  {
      $class = $request->class;
      $classdata = ClassModel::where("class",$class)->first();
      $upload_id =$classdata->upload_id;
      $uploadcateg = UploadScreens::where("screen_id",$upload_id)->first()->category;
  

      switch (trim($uploadcateg)) {
        case 'AGR':
          if($classdata->use_template_dates == 'Y'){

            $file = public_path()."/downloads/agric_schedule_template.csv";

          }else{

            $file = public_path()."/downloads/agric_schedule_template1.csv";

          }
          break;
        case 'EMP':
          $file = public_path()."/downloads/employee_schedule_template.csv";
          break;
         
        default:
            $file = public_path()."/downloads/agric_schedule_template.csv";
         
            break;
            
    }

     
      $headers = array('Content-Type: application/csv');
      return Response::download($file, 'schedule_template.csv',$headers);
  }


  public function taPremComputation($dcontrol, $plan, $age, $endt_renewal_no){
      $duration = $dcontrol->days_covered;
      $plan = $plan;
      $age = (int)$age;

      $nett_prem = 0;
      $endt_renewal_no = $endt_renewal_no;
      $plan_rec = TravelPlan::where('plan_code',$plan)->get()[0];
      $age_limit = TravelPrem::selectRaw("min(min_age) as min_age,max(max_age) as max_age")
                    ->whereRaw("plan = '" . $plan . "'")
                    ->get()[0];
      $minimum_age = (int)$age_limit->min_age;
      $maximum_age = (int)$age_limit->max_age; 

      if($age < $minimum_age && $plan_rec->plan_type != 'G'){
          
          $premium = TravelPrem::whereRaw("min <= '" . $duration . "'")
                      ->whereRaw("max >= '" . $duration . "'")
                      ->whereRaw("min_age = ".$minimum_age)
                      ->whereRaw("plan = '" . $plan . "'")
                      ->first();

      }
      else if($age > $maximum_age && $plan_rec->plan_type != 'G'){

          $premium = TravelPrem::whereRaw("min <= '" . $duration . "'")
                      ->whereRaw("max >= '" . $duration . "'")
                      ->whereRaw("max_age = ".$maximum_age)
                      ->whereRaw("plan = '" . $plan . "'")
                      ->first();

      }else{

          $premium = TravelPrem::whereRaw("min <= '" . $duration . "'")
                      ->whereRaw("max >= '" . $duration . "'")
                      ->whereRaw("min_age <= '" . $age . "'")
                      ->whereRaw("max_age >= '" . $age . "'")
                      ->whereRaw("plan = '" . $plan . "'")
                      ->first();  

      }

      $pipstmp = Pipstmp::whereRaw("trim(key) = '02'")->get();
      $t_levy_rate = (float)$pipstmp[0]->levy_rate / 100;
      $phcf_rate = (float)$pipstmp[0]->policy_fund_rate / 100;

      $dcontrol = $dcontrol;

      $cls = ClassModel::where('class',$dcontrol->class)->get();
      $cls = $cls[0];

      $stamp_duty = (float)$cls->stamp_duty / (float)$dcontrol->currency_rate;
      $vat_rate = (float)$dcontrol->vat_rate / 100;

      if(empty($vat_rate)){
        $vat_rate = 0;
    }
      
      $basic = ($premium->premium - $stamp_duty) / (1 + $t_levy_rate + $phcf_rate + ($vat_rate * (1 + $t_levy_rate)));


      return  $dcontrol->conv_rate*$basic;

  }

  
  public function ta_sched_upload(Request $request){

       if($request->hasfile('data')){

            //Get uploaded File Details
            $dcontrol = Dcontrol::where('endt_renewal_no',$request->endt_renewal_no)->get();
            $dcontrol = $dcontrol[0];
            $period_from = Carbon::parse($dcontrol->period_from);
            $period_to = Carbon::parse($dcontrol->period_to);

            $polsect=Polsect::where('policy_no',$request->policy_no)
                ->where('location',$request->location)
                ->first();

            $path = $request->file('data')->getRealPath();
            $extension = File::extension($request->file('data')->getClientOriginalName());
            $today = Carbon::today();
            $user = Auth::user()->user_name;

            $risk = new Risk;
            $payload_req = new Request;

            $prem_total = $polsect->total_premium;


            $duration = $dcontrol->days_covered;
            $prem = TravelPrem::whereRaw("min <= '" . $duration . "'")
                        ->whereRaw("max >= '" . $duration . "'")
                        ->whereRaw("plan = '" . $request->plan . "'")
                        ->first();


            if($extension == "csv" || $extension == "xls" || $extension == "xlsx"){
              
                // $excel_data = Excel::load($path, function($reader){})->get();

                $excel_data = FileUploadManager::excelUpload($request->file('data'));

                //return redirect()->with('success','Correct file uploaded');
                if(!empty($excel_data) && $excel_data->count()){
                    $employee_numbers = array();
                    $errors = array();
                    $count_errors = 0;
                    $z = 0;
                    $emp_count = 0;


                    $employee_passport_numbers = Prosched::where('policy_no',$request->policy_no)
                          ->where('endorse_no',$request->endt_renewal_no)
                          ->where('location',$request->location)
                          ->pluck('passport')
                          ->toArray();
                 

                    foreach ($excel_data as $value) {

                        $count = $count+1;

                        $prosched = Prosched::where('policy_no',$request->policy_no)
                            ->where('endorse_no',$request->endt_renewal_no)
                            ->where('location',$request->location)
                            ->get();

                        $prosched = $prosched[0];

                        // Validate if the departure date is between period_from and period_to

                        $departure_date = Carbon::parse($value['departure']);
                        if (!$departure_date->between($period_from, $period_to)) {
                          $count_errors++;
                          $errors[$z++] = "Departure date ".$departure_date->toDateString()." must be between ".$period_from->toDateString()." and ".$period_to->toDateString()." in row ".$count;
                        }

                        if(empty(trim($value['passport']))) {

                          $count_errors++;
                          $errors[$z++] = "Passport number is missing for the member in row ".$count;
                      
                        }

                        // check for duplicates in excel

                        $current_passport = trim($value['passport']);
                        $seen_passports = [];

                    
                        if (isset($seen_passports[$current_passport])) {
                            $count_errors++;
                            $errors[$z++] = "Duplicate passport number {$current_passport} in the uploaded file";
                        } else {
                            $seen_passports[$current_passport] = true;
                        }

                        // check for duplicates in this endorsement

                        if(!empty($prosched) && in_array(trim($value['passport']),$employee_passport_numbers)){
              
                          $count_errors++;
                          $errors[$z++] = "Member of passport no ".trim($value['passport'])." exists for the endorsement ".$request->endt_renewal_no." in row ".$count;

                        }
                        
                        // add employee passport to employees array
                        $employee_passport_numbers[$emp_count++] = trim($value['passport']);
                    }

                    if($count_errors > 0){
                        $endt_renewal_no = $request->endt_renewal_no;
                        return view::make('gb.underwriting.upload_gpa_errors',compact('errors','endt_renewal_no'));
                    }
                    else{
                        foreach ($excel_data as $value) {

                            $item_no = Prosched::where('policy_no',$request->policy_no)
                                ->where('class',$request->class)
                                ->count();

                            $item_no = $item_no+1;
                            $count = $count+1;

                            $dob = Carbon::parse($value['dob']);
                            $departure_date = Carbon::parse($value['departure']);
                            
                            $age = $today->diffInDays($dob);

                            $age = floor($age / 365);

                            $plan = $request->plan;
                            $check_group = TravelPlan::where('plan_code',$plan)->get()[0];

                            $endt_renewal_no = $request->endt_renewal_no;

                            $payload = [
                                          'duration' => $duration,
                                          'plan' => $plan,
                                          'age' => $age,
                                          'field_no' => $item_no,
                                          'travel_with_isured' => ($age < 18) ? 'Y' : 'N',
                                          'endt_renewal_no' => $endt_renewal_no
                                      ];

                            $payload_req->merge($payload);

                            //dd($payload_req->endt_renewal_no);

                            $ta_prem = $risk->get_premium($payload_req);

                            $prosched = new Prosched;

                            if($check_group->plan_type=='G'){

                              $prosched->amounta = 0;

                            }else{

                              $prosched->amounta = (float)$ta_prem;

                            }
                            
                            $prosched->policy_no = $request->policy_no;
                            $prosched->endorse_no= $request->endt_renewal_no;
                            $prosched->class=$request->class;
                            $prosched->location=$request->location;
                            $prosched->quantity=$item_no;
                            $prosched->s_code=$item_no;
                            $prosched->proposal_no=$dcontrol->dprop_no;
                            $prosched->delete_str='N';
                            $prosched->location_name=$value['name'];
                            $prosched->dola=Carbon::today();
                            $prosched->endorse_date=Carbon::today();
                            $prosched->head1=$item_no;
                            $prosched->detail_line=$value['name'];
                            $prosched->categ_class=$polsect->class;
                            $prosched->old_s_code=0;
                            $prosched->user_filler='  ';
                            $prosched->age =$age;
                            $prosched->dob = $dob ;
                            $prosched->departure_date=$departure_date;
                            $prosched->plan= $request->plan;
                            $prosched->passport= $value['passport'];
                            $prosched->duration= $duration;

                            $prosched->save();

                            if($check_group->plan_type != 'G'){

                              $prem_total += $ta_prem;

                            }else{

                              $prem_total += (float)$ta_prem;

                            }                              

                        }

                    }

                    if($prosched){
                        $no_persons = Prosched::where('policy_no',$request->policy_no)
                                              ->where('endorse_no',$request->endt_renewal_no)
                                              ->where('location',$request->location)
                                              ->count();

                        $prosched_sum = Prosched::where('endorse_no', $endt_renewal_no)
                                                ->where('location', $request->location)->sum('amounta');
                        $request->merge(['prem_total' => $prosched_sum]);

                        $upd = $risk->updateTravelPremiums($request);

                        if($extension == "csv"){
                            return redirect()->back()->with('success','CSV uploaded successfully');
                        }
                        elseif ($extension == "xls" || $extension == "xlsx") {
                            return redirect()->back()->with('success','Excel sheet uploaded successfully');
                        }
                    }
                    else{
                        return redirect()->back()->with('error','An error was encountered during upload');
                    }
                }
            }
            else if($extension != "csv" || $extension != "xls" || $extension != "xlsx"){
                return redirect()->back()->with('error','You can only upload .csv, .xls or .xlsl files');
            }
        }
        else{
            return redirect()->back()->with('error','No file chosen. Please Choose a file to upload');
        }


    }



 
  
  
  
}
