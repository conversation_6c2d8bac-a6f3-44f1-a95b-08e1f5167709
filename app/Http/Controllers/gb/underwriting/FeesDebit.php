<?php

namespace App\Http\Controllers\gb\underwriting;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\Auth;

use App\Debitmast;
use App\Dcontrol;
use App\Polmaster;
use App\ClassModel;
use App\Vat_setup;
use App\Pipstmp;
use App\Dtran0;
use App\Currency;
use App\Reidoc;
use Response;
use Session;
use DateTime;
use App\Modtlend;
use App\Certalloc;
use App\Certmast;
use App\Misc_fees_param;
use App\Models\Fees_debit;
use App\Models\Modtlmast;
use App\Models\Modtlpivot;
use App\Models\attestationEntry;
use App\Pipcnam;
use App\Ptamdtl;
use Carbon\Carbon;
use Exception;

class FeesDebit extends Controller
{
    
    public function getFeesDebitDetails(Request $request){
        $dcon = Dcontrol::where('endt_renewal_no',$request->get('policy_no'))->first();

        $reidoc = Reidoc::where('entry_type_descr',$request->get('entry_type'))->first();
        $pipcnam = Pipcnam::first();

        $currency_rate = (float)$request->get('curr_rate');
        $vehicles_count = (float)$request->get('vehicles_count');

        switch($reidoc->entry_type_descr){
            case 'ATT':
                $pip = Pipstmp::where('key','02')->first();
                $fee_amt = $pip->attestation_fee;
                // dd($fee_amt);

            break;
            case 'SRR':
                $cls_rec = ClassModel::where('class',$dcon->class)->first();
                $fee_amt = (float)$cls_rec->admin_fees / $currency_rate;
                
            break;
            // comesa yellow card fees
            case 'CYC':
                $comesa_fees = Misc_fees_param::where('fee_entry_type','CYC')->first();
                $fee_amt = $comesa_fees->fee_amt /$currency_rate;
            break;
            case 'STK':
                $standardStickerFees = ClassModel::where('class',$dcon->class)->first()->sticker_fees;
                $fee_amt = $standardStickerFees;

                if($pipcnam->calculate_per_fleet == 'Y')
                {
                    $fee_amt = $standardStickerFees * (int)$vehicles_count;
                }
                $fee_amt = $fee_amt /$currency_rate;
            break;
        }

        if($reidoc->vat_flag == 'Y'){
            $dbt_vat = Vat_setup::where('vat_code',$request->vat_type)->first();
            $vat_rate = $dbt_vat->vat_rate;
            $vat_charged = $dbt_vat->vat_code;
            $vat_amount = $fee_amt * ($vat_rate / 100);
            $vat_chargeable = 'Y';
        }else{
            $vat_rate = 0;
            $vat_charged = 0;
            $vat_amount = 0;
            $vat_chargeable = 'N';
        }

        $amount_payable = $fee_amt + $vat_amount;

        $dat = array([
                'fee_amt'=>(float)$fee_amt,
                'vat_charged'=>$vat_charged,
                'vat_rate'=>$vat_rate,
                'vat_amount'=> $vat_amount,
                'vat_chargeable'=>$vat_chargeable,
                'amount_payable'=>$amount_payable
              ]);

        return json_encode($dat);
    }

    public function createFeesDebit(Request $request){

        DB::beginTransaction();
        try{
            $trans_type = $request->get('type');
            $entry_type_descr = $request->get('svc_type_descr');

            $pipcnam = Pipcnam::first();

            switch ($entry_type_descr) {
                case 'STK':
                    $policyController = new Policy;

                    if($pipcnam->debit_stickerfees_endt == 'Y')
                    {

                        $customRequest = new Request([
                            'type' => $entry_type_descr,
                            'previous_endt_renewal_no' => $request->endt_renewal_no,
                            'class' => $request->class,
                            'period_from' => $request->period_from,
                            'period_to' => $request->period_to,
                            'effective_date' => $request->svc_date,
                            'endorse_date' => $request->svc_date,
                            'currency' => $request->svc_currency,
                            'currency_rate' => $request->svc_curr_rate,
                            'reg_no' => $request->reg_no,
                            'vat_charged' => $request->svc_vat_rate,
                            'client_number' => $request->client_number,
                            'insured' => $request->client_number,
                            'replacement_reason' => $request->svc_reason,
                        ]);


                        $resp = $policyController->create($customRequest);

                        if($resp['status'] == 1)
                        {
                            DB::commit();
                            return redirect()->action(
                                'gb\underwriting\Policy_functions@index',
                                [
                                    'endt_renewal_no' => $resp['endt_renewal_no']
                                ]
                            );
                        }
                        throw new Exception('Failed to create Sticker replacement transaction');
                    }
                    break;
                
                default:
                    break;
            }
            $policy_tied = Reidoc::where('entry_type_descr',$entry_type_descr)->where('trans_type',$trans_type)->first();

            $effective_date = $request->get('svc_date');
            
            $dr_cr = 'D';
            $doc_type = 'DRN';
            $source = 'U/W';
            $currency_code = (int)$request->get('svc_currency');
            $currency_rate = (float)$request->get('svc_curr_rate');
            $debit_amount = (float)$request->get('svc_amt');
            $vat_rate = (float)$request->get('svc_vat_rate');
            $vat_amount = (float)$request->get('svc_vat');
            $amount_payable = (float)$request->get('svc_payable');
            $debit_reason = $request->get('svc_reason');
            $reference = $request->get('reference');

            $vehicle_owner = $request->get('vehicle_owner');
            $chassis_no = $request->get('chassis_no');
            $vehicle_make = $request->get('vehicle_make');
            $body_type = $request->get('body_type');
            $man_date = $request->get('man_date');
            $model = $request->get('model');
            //dd($stickerno);
            if($policy_tied->tied_to_policy == 'Y'){
                $policy_no = $request->get('policy_no');
                $period_from = $request->get('period_from');
                $period_to = $request->get('period_to');
                $client_number = $request->get('client_number');

                $debit_rec = Debitmast::whereRaw("policy_no='".$policy_no."' and (entry_type_descr='POL' or entry_type_descr='REN' or entry_type_descr='RNS' or entry_type_descr='CXT' or entry_type_descr='PTA')")->orderBy('dtrans_no','DESC')->limit(1)->get()[0];

                $endt_renewal_no = $debit_rec->endt_renewal_no;
                
                //attestation 
                $cover_from = $effective_date;
                $cover_to = $effective_date;
                $usage = '';
                
            }else{
                $endt_renewal_no = '';
                $policy_no = '';
                $period_from = $effective_date;
                $period_to = $effective_date;
                $cover_from = $request->get('period_from1');
                $cover_to = $request->get('period_to1');
                $usage = $request->get('usage');
                $cert_type = $request->get('svc_cert_type');
                $client_number = $request->get('client_no');
                $signature = Auth::user()->signature;

            }

            if($entry_type_descr == 'CYC'){
                $stickerno = $request->card_number;
                $reg_no = $request->reg_no;
            }
            else{
                $stickerno=  $request->stickerno;
                $reg_no = $request->crr_reg_no;
            }

            $username = trim(Auth::user()->user_name);
            $today = Carbon::now();

            $dtran_rec = Dtran0::where('rec_no',0)->get()[0];
            $dtrans_no = (int)$dtran_rec->debit_no;
            $new_serial = Dtran0::where('rec_no', 0)->increment('debit_no', (int) '1');

            $account_year = $dtran_rec->account_year;
            $account_month = $dtran_rec->account_month;
            //



            $fees_debit = new Fees_debit();

            $fees_debit->policy_no = $policy_no;
            $fees_debit->endt_renewal_no = $endt_renewal_no;
            $fees_debit->client_number = $client_number;
            $fees_debit->sticker_no = $stickerno;
            $fees_debit->dtrans_no = $dtrans_no;
            $fees_debit->period_from = $period_from;
            $fees_debit->period_to = $period_to;
            $fees_debit->effective_date = $effective_date;
            $fees_debit->account_year = $account_year;
            $fees_debit->account_month = $account_month;
            $fees_debit->dr_cr = $dr_cr;
            $fees_debit->doc_type = $doc_type;
            $fees_debit->trans_type = $trans_type;
            $fees_debit->entry_type_descr = $entry_type_descr;
            $fees_debit->source = $source;
            $fees_debit->currency_code = $currency_code;
            $fees_debit->currency_rate = $currency_rate;
            $fees_debit->fees_amt = $debit_amount * $currency_rate;
            $fees_debit->foreign_fees_amt = $debit_amount;
            $fees_debit->amt_payable = $amount_payable * $currency_rate;
            $fees_debit->foreign_amt_payable = $amount_payable;
            $fees_debit->vat_rate = $vat_rate;
            $fees_debit->vat_amount = $vat_amount * $currency_rate;
            $fees_debit->foreign_vat_amount = $vat_amount;
            $fees_debit->allocated = 0;
            $fees_debit->unallocated = $amount_payable * $currency_rate;
            $fees_debit->foreign_allocated = 0;
            $fees_debit->foreign_unallocated = $amount_payable;
            $fees_debit->debit_reason = $debit_reason;
            $fees_debit->vehicle_owner = $vehicle_owner;
            $fees_debit->make = $vehicle_make;
            $fees_debit->body_type = $body_type;
            $fees_debit->chassis_no = $chassis_no;
            $fees_debit->reg_no = $reg_no;
            $fees_debit->man_date = $man_date;
            $fees_debit->issued_by = $username;
            $fees_debit->issued_by_signature = $signature;
            $fees_debit->issue_date = $today;
            $fees_debit->cover_from = $cover_from;
            $fees_debit->cover_to = $cover_to;
            $fees_debit->usage = $usage;
            $fees_debit->model = $model;
            $fees_debit->cert_type = $cert_type;
            $fees_debit->ref_dtrans_no = $reference;
            $fees_debit->save();

            $attestation_entry = AttestationEntry::where('reference', $reference)->first();

            if ($attestation_entry) {
                $attestation_entry->fees_debit_dtrans = $dtrans_no; 
                $attestation_entry->save();
            }

            DB::commit();

            Session::flash('success','Debit Note Generated Successfully');

            if($policy_tied->tied_to_policy == 'Y'){
                return redirect()->route('endorse_functions', ['policy_no' => $policy_no]);
            }else{
                return redirect()->route('attestationentry.show', ['reference' => $reference]);  
 
            }
            
        }
        catch(\Throwable $e){
            DB::rollback();
            // dd($e);

            Session::flash('error','Debit Note Failed to Generate');

            if($policy_tied->tied_to_policy == 'Y'){
                return redirect()->route('endorse_functions', ['policy_no' => $policy_no]);
            }else{
                return redirect()->route('attestationentry.show', ['reference' => $reference]);    
            }      
        }

    }
    public function validateSticker(Request $request){
        $polno = trim($request->polno);
        $cert_no = trim($request->stcker);
        $certificates=Certmast::where('cert_no',$cert_no)->count();
											
											
       // dd($polno,$cert_no);
        $count_cert_alloc=Certalloc::where('cert_no',$cert_no)
        ->where('policy_no',$polno)
        ->where('cert_status', 99)
        ->count();
        return[
            'exists'=>$certificates,
            'cancelled'=>$count_cert_alloc
        ];
    }

    public function reverseFeesDebit(Request $request){
        $trans_type = $request->get('type');
        $policy_no = $request->get('policy_no');
        $client_number = $request->get('client_no');
        $reference = $request->get('reference');
        $ref = explode('/',$request->get('dtrans_no'));
        $dtrans_no = (int)$ref[0];
        $account_year = (int)$ref[1]; 
        $doc_type = $request->get('doc_type');
        $entry_type_descr = $request->get('rev_type_descr');
        $rev_reason = $request->get('rev_reason');
        $effective_date = date("Y-m-d");

        $username = trim(Auth::user()->user_name);
        $signature = Auth::user()->signature;

        $policy_tied = Reidoc::where('entry_type_descr',$entry_type_descr)->where('trans_type',$trans_type)->first();
        //dd($entry_type_descr, $trans_type, $policy_tied, $request);

        $rev_rec = Fees_debit::where('dtrans_no',$dtrans_no)->where('account_year',$account_year)->where('entry_type_descr',$entry_type_descr)->where('doc_type',$doc_type)->first();
        //dd($rev_rec);

        $dtran_rec = Dtran0::where('rec_no',0)->get()[0];

        $account_year = $dtran_rec->account_year;
        $account_month = $dtran_rec->account_month;

        $source = 'U/W';
        $doc_type = 'CRN';
        $dr_cr = 'C';

        DB::beginTransaction();
        try{

            $fees_debit = new Fees_debit;

            $fees_debit->policy_no = $rev_rec->policy_no;
            $fees_debit->endt_renewal_no = $rev_rec->endt_renewal_no;
            $fees_debit->client_number = $rev_rec->client_number;
            $fees_debit->dtrans_no = $rev_rec->dtrans_no;
            $fees_debit->period_from = $rev_rec->period_from;
            $fees_debit->period_to = $rev_rec->period_to;
            $fees_debit->effective_date = $effective_date;
            $fees_debit->account_year = $account_year;
            $fees_debit->account_month = $account_month;
            $fees_debit->dr_cr = $dr_cr;
            $fees_debit->doc_type = $doc_type;
            $fees_debit->trans_type = $rev_rec->trans_type;
            $fees_debit->entry_type_descr = $entry_type_descr;
            $fees_debit->source = $source;
            $fees_debit->currency_code = $rev_rec->currency_code;
            $fees_debit->currency_rate = $rev_rec->currency_rate;
            $fees_debit->fees_amt = $rev_rec->fees_amt * -1;
            $fees_debit->foreign_fees_amt = $rev_rec->foreign_fees_amt * -1;
            $fees_debit->amt_payable = $rev_rec->amt_payable * -1;
            $fees_debit->foreign_amt_payable = $rev_rec->foreign_amt_payable * -1;
            $fees_debit->vat_rate = $rev_rec->vat_rate;
            $fees_debit->vat_amount = $rev_rec->vat_amount * -1;
            $fees_debit->foreign_vat_amount = $rev_rec->foreign_vat_amount * -1;
            $fees_debit->allocated = $rev_rec->amt_payable * -1;;
            $fees_debit->unallocated = 0;
            $fees_debit->foreign_allocated = $rev_rec->foreign_amt_payable * -1;
            $fees_debit->foreign_unallocated = 0;
            $fees_debit->debit_reason = $rev_reason;
            $fees_debit->vehicle_owner = $rev_rec->vehicle_owner;
            $fees_debit->make = $rev_rec->make;
            $fees_debit->body_type = $rev_rec->body_type;
            $fees_debit->chassis_no = $rev_rec->chassis_no;
            $fees_debit->reg_no = $rev_rec->reg_no;
            $fees_debit->man_date = $rev_rec->man_date;
            $fees_debit->issued_by = $username;
            $fees_debit->issue_date = $effective_date;
            $fees_debit->issued_by_signature = $signature;

            $fees_debit->save();

            $rev_upd = Fees_debit::where('policy_no',$rev_rec->policy_no)->where('dtrans_no',$rev_rec->dtrans_no)->where('account_year',$rev_rec->account_year)->where('doc_type',$rev_rec->doc_type)->where('entry_type_descr',$rev_rec->entry_type_descr)->update([
                            'allocated'=> $rev_rec->amt_payable,
                            'unallocated' => 0,
                            'foreign_allocated'=>$rev_rec->foreign_amt_payable,
                            'foreign_unallocated'=>0,
                            'cancelled'=>'Y'
                        ]);

            DB::commit();

            Session::flash('success','Credit Note Generated Successfully');

            if($policy_tied->tied_to_policy == 'Y'){
                return redirect()->route('endorse_functions', ['policy_no' => $policy_no]);
            }else{
                return redirect()->route('attestationentry.show', ['reference' => $reference]);    
            }
        }
        catch(\Throwable $e){
            // dd($e);
            DB::rollback();

            Session::flash('error','Credit Note Failed to Generate');
            
            if($policy_tied->tied_to_policy == 'Y'){
                return redirect()->route('endorse_functions', ['policy_no' => $policy_no]);
            }else{
                return redirect()->route('attestationentry.show', ['reference' => $reference]); 
            }        
        }

    }


    public function svc_fees_datatable(Request $request)
    {
        $policy_no = $request->get('policy_no');
            
        $query = Fees_debit::where('policy_no', $policy_no)->get();
        
        //dd($query);
        return datatables::of($query)

            ->editColumn('dtrans_no', function ($fn) {
                
                return str_pad($fn->dtrans_no, 6, '0', STR_PAD_LEFT)."/".$fn->account_year;
            })
            ->editColumn('effective_date', function ($fn) {
                
                return date("Y-m-d",strtotime($fn->effective_date));
            })
            ->editColumn('issue_date', function ($fn) {
                
                return date("Y-m-d",strtotime($fn->issue_date));
            })
            ->editColumn('currency_code', function ($fn) {
                $curr = Currency::where('currency_code',$fn->currency_code)->first();
                return $curr->currency;
            })
            ->addColumn('premium_status', function ($fn) {
                $check_rev = Fees_debit::where('dtrans_no',$fn->dtrans_no)
                    ->where('entry_type_descr',$fn->entry_type_descr)
                    ->where('doc_type','CRN')
                    ->where('source','U/W')
                    ->count();
                //dd($check_rev);

                if($fn->allocated == '' || $fn->allocated == 0){
                    $status = 'Not paid';
                }
                else if(($fn->unallocated == '' || $fn->unallocated == 0) && $check_rev < 1){
                    $status = 'Fully Paid';
                }
                else if(($fn->unallocated == '' || $fn->unallocated == 0) && $check_rev > 0){
                    if($fn->doc_type == 'CRN'){
                        $status = null;
                    }else{
                        $status = 'Reversed';    
                    }
                    
                }
                else{
                    $status = 'Partially Paid';
                }

                if($fn->doc_type == 'REC'){
                    $status = null;
                }
                return $status;
            })
            ->addColumn('action', function ($fn) {

                if($fn->allocated == 0 || $fn->allocated == ""){
                    return '<a class="rev_svc" style="cursor: pointer; color: blue"><i class="glyphicon glyphicon-trash"></i>Reverse</a>';
                }
                
            })
            ->escapeColumns([])
            //->withTrashed()
            ->make(true);
    }

    public function svc_att_list_datatable(Request $request)
    {
        
        $client_no = $request->get('client_no');

        $query = Fees_debit::where('client_number', $client_no)->where('entry_type_descr', 'ATT')->where('source', 'U/W')->where('doc_type', 'DRN')->get();
        
        //dd($query);
        return datatables::of($query)

            ->addColumn('dtrans', function ($fn) {
                
                return str_pad($fn->dtrans_no, 6, '0', STR_PAD_LEFT)."/".$fn->account_year;
            })
            ->editColumn('issue_date', function ($fn) {
                
                return date("Y-m-d",strtotime($fn->issue_date));
            })
            
            ->addColumn('status', function ($fn) {

                if($fn->cancelled == 'Y'){
                    return 'Reversed';
                }
                else{
                    return '<a class="rev_svc" id="fees_debit" style="cursor: pointer; color: blue"><i class="glyphicon glyphicon-file"></i>Print Out</a><br><br><a class="rev_svc" id="debit_note" style="cursor: pointer; color: blue"><i class="glyphicon glyphicon-file"></i>Debit Note</a>';
                }
            })
            ->escapeColumns([])
            //->withTrashed()
            ->make(true);
    }

    public function svc_att_stmt_datatable(Request $request)
    {
        
        $reference = $request->get('reference');

        $query = Fees_debit::where('ref_dtrans_no', $reference)->get();
        
        //dd($query);
        return datatables::of($query)

            ->editColumn('dtrans_no', function ($fn) {
                
                return str_pad($fn->dtrans_no, 6, '0', STR_PAD_LEFT)."/".$fn->account_year;
            })
            ->editColumn('effective_date', function ($fn) {
                
                return date("Y-m-d",strtotime($fn->effective_date));
            })
            ->editColumn('issue_date', function ($fn) {
                
                return date("Y-m-d",strtotime($fn->issue_date));
            })
            ->editColumn('currency_code', function ($fn) {
                $curr = Currency::where('currency_code',$fn->currency_code)->first();
                return $curr->currency;
            })
            ->addColumn('premium_status', function ($fn) {
                $check_rev = Fees_debit::where('dtrans_no',$fn->dtrans_no)->where('entry_type_descr',$fn->entry_type_descr)->where('source','U/W')->count();

                if($fn->allocated == '' || $fn->allocated == 0){
                    return 'Not paid';
                }
                else if(($fn->unallocated == '' || $fn->unallocated == 0) && $check_rev < 2){
                    return 'Fully Paid';
                }
                else if(($fn->unallocated == '' || $fn->unallocated == 0) && $check_rev > 1){
                    if($fn->doc_type == 'CRN'){
                        return 'Reversal';
                    }else{
                        return 'Reversed';    
                    }
                    
                }
                else{
                    return 'Partially Paid';
                }
            })
            ->addColumn('action', function ($fn) {

                if($fn->allocated == 0 || $fn->allocated == ""){
                    return '<a class="rev_svc" style="cursor: pointer; color: blue"><i class="glyphicon glyphicon-trash"></i>Reverse</a>';
                }
                
            })
            ->escapeColumns([])
            //->withTrashed()
            ->make(true);
    }

    public function checkVehInsured(Request $request){

       $reg_no= $request->plate_no;
       $policy_no= $request->policy_no;
       $first_modtl = Modtlpivot::where('reg_no', $reg_no)
            ->where('policy_no', $policy_no)
            ->where('status', 'ACT')
            ->orderBy('transeq_no', 'asc')
            ->first();
       $last_modtl = Modtlpivot::where('reg_no', $reg_no)
            ->where('policy_no', $policy_no)
            ->where('status', 'ACT')
            ->orderBy('transeq_no', 'desc')
            ->first();
       $pta_modtl = Ptamdtl::where('reg_no', $reg_no)
            ->where('endt_renewal_no', $last_modtl->endt_renewal_no)
            ->where('status', 1)
            ->first();
            
       $first_debit = Debitmast::where('policy_no', $policy_no )
            ->where('endt_renewal_no',$first_modtl->endt_renewal_no)
            ->first();
       $last_debit = Debitmast::where('policy_no', $policy_no )
            ->where('endt_renewal_no',$last_modtl->endt_renewal_no)
            ->first();

       $class = ClassModel::where('class', $last_debit->class)->first();
     
       if((int)$class->aimsdept == 7){
            $usage = "Private";
            
       }elseif((int)$class->aimsdept == 8){
            $usage = "Commercial";

       }else{
            $usage = "";
       }

       return response()->json([
            'status' => 1,
            'last_debit' => $last_debit,
            'first_debit' =>  $first_debit,
            'usage' => $usage,
            'last_modtl' => $last_modtl,
            'pta_modtl' => $pta_modtl
        ]);
 
    }

    public function comesa_vehicles(Request $request)
    {
        $policy_no = $request->policy_no;
        $vehicles = Modtlpivot::with('cover_type')
            ->where('policy_no',$policy_no)
            ->orderBy('transeq_no','desc')
            ->get();

        return json_encode($vehicles);
    }

    public function fetchVehDetails(Request $request) {
        $reg_no = $request->plate_no;
    
        // Step 1: Retrieve the `transeq_no` from `modtlmast`
        $modtlmastRecord = Modtlmast::where('reg_no', $reg_no)->first();
        
        if ($modtlmastRecord) {
            $transeq_no = $modtlmastRecord->transeq_no;
            $vehicle = Modtlpivot::where('transeq_no', $transeq_no)
                ->where('reg_no', $reg_no)
                ->first();
                // dd($vehicle);
    
            if ($vehicle) {
                return response()->json([
                    'success' => true,
                    'owner' => $vehicle->owner,
                    'chassis_no' => $vehicle->chassis_no,
                    'make' => $vehicle->make,
                    'bodytype' => $vehicle->bodytype,
                    'manufacture_year' => $vehicle->manufacture_year,
                    'model' => $vehicle->model,
                    'client_number' => $vehicle->client_number,
                ]);
            }
        }
    
        // If no vehicle details found
        return response()->json([
            'success' => false
        ]);
    }
    
    
}
