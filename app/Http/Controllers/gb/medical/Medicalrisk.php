<?php

namespace App\Http\Controllers\gb\medical;

use File;
use View;
use Excel;
use App\Classbr;
use App\Polsect;
use App\Dcontrol;
use App\Debitdtl;
use App\Debitmast;
use App\Polmaster;
use Carbon\Carbon;
use App\Polsectend;

use App\RefundRates;
use App\ClaimSchedule;
use App\Endorse_descr;
use App\Models\MediPlan;
use App\Discounts_loadings;
use Illuminate\Http\Request;
use App\Medical\MedScheduleModel;
use App\Models\Medical\Medmember;
use App\Models\Medical\Polmedsec;
use App\Models\Medical\Polmedprem;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Medical\MedmemberExcelModel;
use App\Models\Medical\Meddependant;
use Illuminate\Support\Facades\Auth;
use App\Models\Medical\Polmedoptplan;
use App\Medical\MeddependantExcelModel;
use App\Http\Controllers\gb\underwriting\Risk;
use Illuminate\Validation\ValidationException;

class Medicalrisk extends Controller
{

	public function confirmMembers(Request $request)
    {
        $policy_no = $request->get('policy_no');
        $location = $request->get('location');
        $polsect = Polsect::where('policy_no', $policy_no)
            ->where('location', $location)
            ->count();

        if ($polsect > 0) {
            $polsect = Polsect::where('policy_no', $policy_no)
                ->where('location', $location)
                ->first();

            $number = $polsect->owner_lib;
            $response['persons'] = $number;
        } else {
            $number = 0;
            $response['persons'] = $number;
        }

        echo json_encode($response);
    }

    public function confirmRisk(Request $request)
    {
        $policy_no = $request->get('policy_no');
        $endt_renewal_no = $request->get('endt_renewal_no');

        $response = ['status'=>$this->checkFullMedicalSchedule($endt_renewal_no)];

        echo json_encode($response);
    }

    
    public function getSelectedSections(Request $request){
        $claim_no = $request->claim_no;
        $peril = $request->peril;
        $perilitem = $request->perilitem;
        $corrtype = $request->corrtype;

        $polscheds = ClaimSchedule::where('claim_no', $claim_no)
                    ->where('peril', $peril)
                    ->where('perilsitem', $perilitem)
                    ->where('corr_type', $corrtype)
                    ->get();
      
        return $polscheds;
    }

    public function addMedicalMembers(Request $request)
    {
        DB::beginTransaction();
        try {
            $policy_no = $request->policy_no;
            $endt_renewal_no = $request->endt_renewal_no;
            $location = $request->location;

            $firstname = $request->firstname;
            $surname = $request->surname;
            $payroll_no = $request->payroll_no;
            $title = $request->title;
            $date_of_birth = $request->date_of_birth;
            $gender = $request->gender;
            $incept_date = $request->incept_date;
            $number_of_dependants = $request->depedants_no;
            $rate_type = $request->rate_type;
            $plan = $request->med_plan;
            $limit_per = $request->prem_method;



            $numberOfMembers = Polsect::where('policy_no', $policy_no)
                ->where('endt_renewal_no', $endt_renewal_no)
                ->where('location', $location)
                ->first()->owner_lib;

            $numberOfMembers = $numberOfMembers + count($request->surname);
            
    
            $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
            
            $polsect = Polsect::where('policy_no', $policy_no)->where('location', $location)->first();
    
            $total_prem = $polsect->renewal_premium;
            for ($i = 0; $i < count($surname); $i++) {
                $member_exists = Medmember::where('policy_no', $policy_no)
                        ->where('endt_renewal_no', $endt_renewal_no)
                        ->where('payroll_no', $payroll_no[$i])
                        ->count();

                if ($member_exists > 0) {
                    throw ValidationException::withMessages([
                        'member' => ["Member at row ".($i+1)." already exist"],
                        ]);
                }
                        
                $countmedmember = Medmember::where('policy_no', $policy_no)
                                ->where('endt_renewal_no', $endt_renewal_no)
                                ->max('member_no');
                                
                $reference = $this->getMedicalReference($dcontrol->class);
                $member_no = 'APAUG-'.$reference;
                Medmember::create([
                    'payroll_no' => $payroll_no[$i],
                    'policy_no' => $policy_no,
                    'endt_renewal_no' => $endt_renewal_no,
                    'reference_no' => $reference,
                    'member_no' => $member_no,
                    'title' => strtoupper($title[$i]),
                    'surname' => strtoupper($surname[$i]),
                    'firstname' => strtoupper($firstname[$i]),
                    'gender' => strtoupper($gender[$i]),
                    'date_of_birth' => $date_of_birth[$i],
                    'number_of_dependants' => $number_of_dependants[$i],
                    'incept_date' => $incept_date[$i],
                    'rate_type' => $rate_type[$i],
                    'plan_code' => $plan[$i],
                    'cancelled' => Null,
                    'limit_per' => $limit_per[$i],
                    'created_by' => Auth::user()->user_name,
                    'updated_by' => Auth::user()->user_name,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);

                if ($request->dept != 'Y') {

                    Meddependant::create([
                        'dependant_id' => 1,
                        'member_no' => $member_no,
                        'policy_no' => $policy_no,
                        'endt_renewal_no' => $endt_renewal_no,
                        'surname' => $surname[$i],
                        'firstname' => $firstname[$i],
                        'relationship' => 0,
                        'adult' => 'Y',
                        'date_of_birth' => $date_of_birth[$i],
                        'incept_date' => $incept_date[$i],
                        'created_by' => Auth::user()->user_name,
                        'updated_by' => Auth::user()->user_name,
                    ]);
                }

                $member =  Medmember::where('policy_no', $policy_no)
                        ->where('endt_renewal_no', $endt_renewal_no)
                        ->where('payroll_no', $payroll_no[$i])
                        ->where('member_no', $member_no)
                        ->first();

                
                $this->addSections($member_no, $plan[$i]);
                $this->addLimits($member_no, $plan[$i]);


            }

            if ($request->dept != 'Y') {

                Polsect::where('policy_no', $policy_no)
                    ->where('endt_renewal_no', $endt_renewal_no)
                    ->where('location', $location)
                    ->update([
                        'owner_lib' => $numberOfMembers
                    ]);
        
        
                Polsectend::where('policy_no', $policy_no)
                    ->where('endt_renewal_no', $endt_renewal_no)
                    ->where('location', $location)
                    ->update([
                        'owner_lib' => $numberOfMembers
                    ]);
                
                    
                $planmembers = Medmember::where('policy_no', $policy_no)
                                ->where('endt_renewal_no', $endt_renewal_no)
                                ->whereNull('cancelled')
                                ->get();
                foreach ($planmembers as $member) {
                    $this->validatePlanPremiumSetup($member);
                }

                
                $full_details = $this->checkFullMedicalSchedule($endt_renewal_no);

                if ($full_details) {
                    $this->computeMedicalPremium($endt_renewal_no, $policy_no);
                    $this->updatePremiumPols($endt_renewal_no, $policy_no);
                }
            }
            
            $response['status'] = 1;
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollback();
            $response['status'] = 0;
            if (get_class($th) == "Illuminate\Validation\ValidationException") {
                $response['message'] = $th->validator->errors()->first();
            }else{
                $response['message'] = "Failed, please check data";
            }
        }

        return json_encode($response);
    }

    public function modifyMember(Request $request)
    {
        DB::beginTransaction();
        try {
            $pol = Polmaster::where('endorse_no', $request->e_endt_renewal_no)->first();
            $member = Medmember::where('reference_no', $request->reference)
                        ->where('payroll_no', $request->payroll_no)
                        ->where('policy_no', $request->e_policy_no)
                        ->where('endt_renewal_no', $request->e_endt_renewal_no)
                        ->first();
            if ($request->delete_member == 'on' || $request->delete_member == 'ON') {
                $curr_endt = $pol->endorse_no;
            
                # Get Previous Endt
                $dcon_no = Dcontrol::where('policy_no', $pol->policy_no)
                            ->where('class', $pol->class)
                            ->where('endt_renewal_no', '<>', $curr_endt)
                            ->whereNull('delete_str')
                            ->max('dcon_no');
                $dcontrolOld = Dcontrol::where('policy_no', $pol->policy_no)
                                ->where('class', $pol->class)
                                ->where('dcon_no', $dcon_no)
                                ->first();

                # Check Debit Mast For Record
                $prev_endt = $dcontrolOld->endt_renewal_no;
                $debitMastCheck = Debitmast::where('policy_no', $pol->policy_no)
                                    ->where('endt_renewal_no', $prev_endt)
                                    ->where('class', $pol->class)
                                    ->exists();

                if ($dcontrolOld->trans_type == 'NIL') {
                    $debitMastCheck = true;
                }

                $member_old = Medmember::where('policy_no', $request->e_policy_no)
                            ->where('endt_renewal_no', $prev_endt)
                            ->where('member_no', $member->member_no)
                            ->first();
                if (is_null($member_old)) {
                    Medmember::where('reference_no', $request->reference)
                            ->where('payroll_no', $request->payroll_no)
                            ->where('policy_no', $request->e_policy_no)
                            ->where('endt_renewal_no', $request->e_endt_renewal_no)
                            ->delete();
                    
                    Meddependant::where('member_no', $member->member_no)
                            ->where('policy_no', $request->e_policy_no)
                            ->where('endt_renewal_no', $request->e_endt_renewal_no)
                            ->delete();

                    Polmedprem::where('member_no', $member->member_no)
                            ->where('policy_no', $request->e_policy_no)
                            ->where('endt_renewal_no', $request->e_endt_renewal_no)
                            ->delete();

                    Polmedsec::where('member_no', $member->member_no)
                            ->where('policy_no', $request->e_policy_no)
                            ->where('endt_renewal_no', $request->e_endt_renewal_no)
                            ->delete();
                }else {
            
                    Medmember::where('reference_no', $request->reference)
                            ->where('payroll_no', $request->payroll_no)
                            ->where('policy_no', $request->e_policy_no)
                            ->where('endt_renewal_no', $request->e_endt_renewal_no)
                            ->update([
                                'cancelled' => 'Y',
                                'updated_by' => Auth::user()->user_name,
                            ]);
                    
                    Meddependant::where('member_no', $member->member_no)
                            ->where('policy_no', $request->e_policy_no)
                            ->where('endt_renewal_no', $request->e_endt_renewal_no)
                            ->update([
                                'cancelled'=> 'Y',
                                'updated_by' => Auth::user()->user_name,
                            ]);

                    Polmedprem::where('member_no', $member->member_no)
                            ->where('policy_no', $request->e_policy_no)
                            ->where('endt_renewal_no', $request->e_endt_renewal_no)
                            ->update([
                                'cancelled'=> 'Y',
                                'updated_by' => Auth::user()->user_name,
                            ]);
                            
                    
                }

                $optionals = Polmedoptplan::where('member_no', $member->member_no)
                            ->where('policy_no', $request->e_policy_no)
                            ->where('endt_renewal_no', $request->e_endt_renewal_no)
                            ->whereNull('cancelled')
                            ->get();
                
                foreach ($optionals as $option) {
                    $this->removeCover(new Request(
                        [
                            'member_no' => $option->member_no,
                            'policy_no' => $option->policy_no,
                            'endt_renewal_no' => $option->endt_renewal_no,
                            'plan' => $option->plan_code
                        ]
                    ));
                }

                
            } else {
            
                Medmember::where('reference_no', $request->reference)
                        ->where('payroll_no', $request->payroll_no)
                        ->where('policy_no', $request->e_policy_no)
                        ->where('endt_renewal_no', $request->e_endt_renewal_no)
                        ->update([
                            'title' => $request->title,
                            'surname' => $request->surname,
                            'firstname' => $request->firstname,
                            'gender' => $request->gender,
                            'date_of_birth' => $request->date_of_birth,
                            'number_of_dependants' => $request->depedants_no,
                            'incept_date' => $request->incept_date,
                            'rate_type' => $request->rate_type,
                            'plan_code' => $request->med_plan,
                            'limit_per' => $request->prem_method,
                            'updated_by' => Auth::user()->user_name,
                        ]);
            }

            // $full_details = $this->checkFullMedicalSchedule($request->e_endt_renewal_no);

            // if ($full_details) {
                $this->computeMedicalPremium($request->e_endt_renewal_no, $request->e_policy_no);
                $this->updatePremiumPols($request->e_endt_renewal_no, $request->e_policy_no);
            // }

            DB::commit();
                        
            return redirect()->back()->with('success', 'Member details amended Successfully');
        } catch (\Throwable $th) {
            
            DB::rollback();
            return redirect()->back()->with('error', 'Failed to update member details');
        }
    }

    public function addMedicalDependants(Request $request)
    {
        DB::beginTransaction();
        try {
            $policy_no = $request->policy_no;
            $endt_renewal_no = $request->endt_renewal_no;
            $location = $request->location;

            $firstname = $request->firstname;
            $surname = $request->surname;
            $member_no = $request->member_no;
            $relation = $request->relation;
            $date_of_birth = $request->date_of_birth;
            $incept_date = $request->incept_date;
    
            $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
            $polsect = Polsect::where('policy_no', $policy_no)->where('location', $location)->first();
    
    
            for ($i = 0; $i < count($surname); $i++) {
                $count_dep = Meddependant::where('policy_no', $policy_no)
                                ->where('endt_renewal_no', $endt_renewal_no)
                                ->where('member_no', $member_no[$i])
                                ->max('dependant_id');

                $dep_exists = Meddependant::where('policy_no', $policy_no)
                            ->where('endt_renewal_no', $endt_renewal_no)
                            ->where('member_no', $member_no[$i])
                            ->where('firstname', $firstname[$i])
                            ->where('relationship', $relation[$i])
                            ->whereNull('cancelled')
                            ->count();
                            
                if ($dep_exists > 0) {
                    throw ValidationException::withMessages([
                        'member' => ["Dependant at row ".($i+1)." already exists"],
                    ]);
                }

                
            
                $dep_count = Meddependant::where('policy_no', $policy_no)
                            ->where('endt_renewal_no', $endt_renewal_no)
                            ->where('member_no', $member_no[$i])
                            ->whereNull('cancelled')
                            ->count();
        
                $member_count = $dep_count+1;

                $member = Medmember::where('policy_no', $policy_no)
                    ->where('endt_renewal_no', $endt_renewal_no)
                    ->where('member_no', $member_no[$i])
                    ->first();
                $number_of_dep = $member->number_of_dependants+1;
        
                if ($member_count > $number_of_dep) {
                    throw ValidationException::withMessages([
                        'dependants' => ["Number of dependants for member ".$member->firstname." ".$member->surname." exceeded"],
                    ]);
                }

                $birthdate = Carbon::parse($date_of_birth[$i]);
                $currentDate = Carbon::now();
                
                // Calculate the age
                $age = $birthdate->diffInYears($currentDate);

                $adult = 'Y';
                if ($age < 20) {
                    $adult = 'N';
                }
            
    
            
                Meddependant::create([
                    'dependant_id' => $count_dep+1,
                    'member_no' => $member_no[$i],
                    'policy_no' => $policy_no,
                    'endt_renewal_no' => $endt_renewal_no,
                    'surname' => $surname[$i],
                    'firstname' => $firstname[$i],
                    'relationship' => $relation[$i],
                    'adult' => $adult,
                    'date_of_birth' => $date_of_birth[$i],
                    'incept_date' => $incept_date[$i],
                    'created_by' => Auth::user()->user_name,
                    'updated_by' => Auth::user()->user_name,
                ]);
            }

            $full_details = $this->checkFullMedicalSchedule($endt_renewal_no);
            if ($full_details) {
                $this->computeMedicalPremium($endt_renewal_no, $policy_no);
                $this->updatePremiumPols($endt_renewal_no, $policy_no);
            }

            DB::commit();
            $response['status'] = 1;

        } catch (\Throwable $e) {
            DB::rollback();
            if (get_class($e) == "Illuminate\Validation\ValidationException") {
                $response['status'] = 2;
                $response['message'] = $e->validator->errors()->first();
            }else{
                $response['status'] = 0;
            }
        }
        echo json_encode($response);

    }

    public function dependantDetails(Request $request)
    {
        $member_no = $request->member_no;
        $endt_renewal_no = $request->endt_renewal_no;
        $item_no = $request->item_no;
        $dependant = Meddependant::where('endt_renewal_no', $endt_renewal_no)
                            ->where('member_no', $member_no)->where('dependant_id', $item_no)->first();
        
        return $dependant;
    }

    public function memberDependants(Request $request)
    {
        $member_no = $request->member_no;
        $endt_renewal_no = $request->endt_renewal_no;
        $member = Medmember::where('endt_renewal_no', $endt_renewal_no)
                            ->where('member_no', $member_no)
                            ->first()->gender;
        $dependant = Meddependant::where('endt_renewal_no', $endt_renewal_no)
                            ->where('member_no', $member_no)
                            ->where('adult', 'Y')
                            ->whereIn('relationship', [0,1])
                            ->get();

        if ($member == 'M') {
            $dependant = $dependant->reject(function ($item) {
                return $item['dependant_id'] == 1;
            });
        }
        
        return json_encode($dependant);
    }

    public function modifyDependant(Request $request)
    {
        try {
            if ($request->delete_dependant == 'on' || $request->delete_dependant == 'ON') {
                Meddependant::where('member_no', $request->member_no)
                            ->where('dependant_id', $request->dependant_id)
                            ->where('policy_no', $request->m_policy_no)
                            ->where('endt_renewal_no', $request->m_endt_renewal_no)
                            ->update([
                                'cancelled'=> 'Y',
                                'updated_by' => Auth::user()->user_name,
                            ]);
            }else{
                Meddependant::where('member_no', $request->member_no)
                            ->where('dependant_id', $request->dependant_id)
                            ->where('policy_no', $request->m_policy_no)
                            ->where('endt_renewal_no', $request->m_endt_renewal_no)
                            ->update([
                                'surname' => $request->surname,
                                'firstname' => $request->firstname,
                                'relationship' => $request->relation,
                                'date_of_birth' => $request->date_of_birth,
                                'incept_date' => $request->incept_date,
                                'updated_by' => Auth::user()->user_name,
                            ]);
            }

            $full_details = $this->checkFullMedicalSchedule($request->m_endt_renewal_no);

            if ($full_details) {
                $this->computeMedicalPremium($request->m_endt_renewal_no, $request->m_policy_no);
                $this->updatePremiumPols($request->m_endt_renewal_no, $request->m_policy_no);
            }
                        
            return redirect()->back()->with('success', 'Dependant details amended Successfully');
        } catch (\Throwable $th) {
            
            return redirect()->back()->with('error', 'Failed to amend details');
        }
    }

    public function uploadMembers(Request $request)
    {
        DB::beginTransaction();
        try {
            $request->validate([
                'data' => 'required|mimes:xlsx,csv,xls',
            ]);
    
            $policy_no = $request->policy_no;
            $endt_renewal_no = $request->endt_renewal_no;
            $today = Carbon::now();
            $user = Auth::user()->user_name;
            $file = $request->file('data');
            
            Excel::import(new MedmemberExcelModel($policy_no, $endt_renewal_no, $user, $today), $file);

            $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();

            if ($dcontrol->transtype != 'POL') {# Get Previous Endt
                $dcon_no = Dcontrol::where('policy_no', $dcontrol->policy_no)
                            ->where('class', $dcontrol->class)
                            ->where('endt_renewal_no', '<>', $endt_renewal_no)
                            ->whereNull('delete_str')
                            ->max('dcon_no');

                $dcontrolOld = Dcontrol::where('policy_no', $dcontrol->policy_no)
                                ->where('class', $dcontrol->class)
                                ->where('dcon_no', $dcon_no)
                                ->first();
    
                # Check Debit Mast For Record
                $prev_endt = $dcontrolOld->endt_renewal_no;
                
                $prevmembers = Medmember::where('policy_no', $policy_no)
                        ->where('endt_renewal_no', $prev_endt)
                        ->whereNull('cancelled')
                        ->pluck('member_no');
                
                $members = Medmember::where('policy_no', $policy_no)
                        ->where('endt_renewal_no', $endt_renewal_no)
                        ->whereNotIn('member_no', $prevmembers)
                        ->whereNull('cancelled')
                        ->get();
            }else{
                $members = Medmember::where('policy_no', $policy_no)
                        ->where('endt_renewal_no', $endt_renewal_no)
                        ->whereNull('cancelled')
                        ->get();
            }


            foreach ($members as $member) {
                $this->addSections($member->member_no, $member->plan_code);
                $this->addLimits($member->member_no, $member->plan_code);
            }

            

            $numberOfMembers = count($members);
            
            Polsect::where('policy_no', $policy_no)
                ->where('endt_renewal_no', $endt_renewal_no)
                ->update([
                    'owner_lib' => $numberOfMembers
                ]);
    
    
            Polsectend::where('policy_no', $policy_no)
                ->where('endt_renewal_no', $endt_renewal_no)
                ->update([
                    'owner_lib' => $numberOfMembers
                ]);

            $full_details = $this->checkFullMedicalSchedule($endt_renewal_no);

            if ($full_details) {
                $this->computeMedicalPremium($endt_renewal_no, $policy_no);
                $this->updatePremiumPols($endt_renewal_no, $policy_no);
            }

            
    
            DB::commit();
            return redirect()->back()->with('success', 'Members uploaded successfully.');
        }catch (\Throwable $e) {
            DB::rollback();
            if (get_class($e) == "Illuminate\Validation\ValidationException") {
                return redirect()->back()->with('warning', "Failed ".$e->validator->errors()->first());
            }

            return redirect()->back()->with('error', 'Failed to upload.');
        }
    }

    public function uploadDependants(Request $request)
    {
        DB::beginTransaction();
        try {
            $request->validate([
                'data' => 'required|file|mimes:xlsx,csv,xls',
            ]);
    
            $policy_no = $request->policy_no;
            $endt_renewal_no = $request->endt_renewal_no;
            $today = Carbon::now();
            $user = Auth::user()->user_name;
            $file = $request->file('data');
            
            Excel::import(new MeddependantExcelModel($policy_no, $endt_renewal_no, $user, $today), $file);

            $full_details = $this->checkFullMedicalSchedule($endt_renewal_no);

            if ($full_details) {
                $this->computeMedicalPremium($endt_renewal_no, $policy_no);
                $this->updatePremiumPols($endt_renewal_no, $policy_no);
            }

            DB::commit();
            return redirect()->back()->with('success', 'Dependants uploaded successfully.');
        }catch (\Throwable $e) {
            DB::rollback();
            if (get_class($e) == "Illuminate\Validation\ValidationException") {
                return redirect()->back()->with('warning', "Failed, ".$e->validator->errors()->first());
            }
            return redirect()->back()->with('error', 'Failed to upload.');
        }
    }

    public function uploadCombinedSchedule(Request $request)
    {
        DB::beginTransaction();
        try {
            $request->validate([
                'data' => 'required|file|mimes:xlsx,csv,xls',
            ]);
    
            $policy_no = $request->policy_no;
            $endt_renewal_no = $request->endt_renewal_no;
            $today = Carbon::now();
            $user = Auth::user()->user_name;
            $file = $request->file('data');
            
            
            Excel::import(new MedScheduleModel($policy_no, $endt_renewal_no, $user, $today), $file);

            $members = Medmember::where('endt_renewal_no',$endt_renewal_no)->whereNull('cancelled')->get();

            $count = 0;
            foreach ($members as $member) {
                $dependants = Meddependant::where('member_no', $member->member_no)
                                ->where('endt_renewal_no', $endt_renewal_no)
                                ->where('dependant_id', '<>', 1)
                                ->whereNull('cancelled')
                                ->count();

                Medmember::where('member_no', $member->member_no)
                ->where('endt_renewal_no', $endt_renewal_no)
                ->update([
                    'number_of_dependants' => $dependants
                ]);
                $count = $count + 1;
            }

            Polsect::where('policy_no', $policy_no)
                ->where('endt_renewal_no', $endt_renewal_no)
                ->where('location', $location)
                ->update([
                    'owner_lib' => $count
                ]);
    
    
            Polsectend::where('policy_no', $policy_no)
                ->where('endt_renewal_no', $endt_renewal_no)
                ->where('location', $location)
                ->update([
                    'owner_lib' => $count
                ]);

            $full_details = $this->checkFullMedicalSchedule($endt_renewal_no);

            if ($full_details) {
                $this->computeMedicalPremium($endt_renewal_no, $policy_no);
                $this->updatePremiumPols($endt_renewal_no, $policy_no);
            }

            DB::commit();
            return redirect()->back()->with('success', 'schedule uploaded successfully.');
        }catch (\Throwable $e) {
            DB::rollback();
            if (get_class($e) == "Illuminate\Validation\ValidationException") {
                return redirect()->back()->with('warning', "Failed, ".$e->validator->errors()->first());
            }
            return redirect()->back()->with('error', 'Failed to upload.');
        }
    }

    public function addCover(Request $request){
        DB::beginTransaction();
        try {
            $policy_no = $request->policy_no;
            $endt_renewal_no = $request->endt_renewal_no;
    
            for ($i=0; $i < count($request->opt_cover) ; $i++) { 
                $member  = Medmember::where('endt_renewal_no', $endt_renewal_no)
                            ->where('policy_no', $policy_no)
                            ->where('member_no', $request->opt_member[$i])
                            ->first();
                            
                $premium = $this->memberPlanPremium($member, $request->opt_cover[$i]);
                $this->addSections($member->member_no, $request->opt_cover[$i]);
                $this->addLimits($member->member_no, $request->opt_cover[$i]);
                $plan_limit = DB::table('mediplans')->where('plan_code', $request->opt_cover[$i])->first()->plan_limit;
                $amounts = $this->getEndorsePremium($endt_renewal_no,$member->member_no,$request->opt_cover[$i], $premium);
                
                $exists = Polmedoptplan::where('member_no' , $request->opt_member[$i])
                                ->where('policy_no' , $policy_no)
                                ->where('endt_renewal_no' , $endt_renewal_no)
                                ->where('plan_code' , $request->opt_cover[$i])->exists();
            

                if ($exists) {
                    
                    Polmedoptplan::where('member_no' , $request->opt_member[$i])
                        ->where('policy_no' , $policy_no)
                        ->where('endt_renewal_no' , $endt_renewal_no)
                        ->where('plan_code' , $request->opt_cover[$i])
                        ->update([
                            'premium' => $amounts[1],
                            'endorse_amount' => 0,
                            'sum_insured' => $plan_limit,
                            'maternity_beneficiary' => $request->opt_dependant[$i],
                            'cancelled' => Null,
                            'created_by' => Auth::user()->user_name,
                            'updated_by' => Auth::user()->user_name,
                        ]);
                }else{
                    Polmedoptplan::create([
                        'member_no' => $request->opt_member[$i],
                        'policy_no' => $policy_no,
                        'endt_renewal_no' => $endt_renewal_no,
                        'plan_code' => $request->opt_cover[$i],
                        'maternity_beneficiary' => $request->opt_dependant[$i],
                        'premium' => $amounts[1],
                        'endorse_amount' => $amounts[0],
                        'sum_insured' => $plan_limit,
                        'created_by' => Auth::user()->user_name,
                        'updated_by' => Auth::user()->user_name,
                    ]);
                }
            }
            
            $this->updatePremiumPols($endt_renewal_no, $policy_no);
            DB::commit();
            return redirect()->back()->with('success', 'Cover added Successfully');
        } catch (\Throwable $e) {
            DB::rollback();
            if (get_class($e) == "Illuminate\Validation\ValidationException") {
                return redirect()->back()->with('error', $e->validator->errors()->first());
            }else{
                return redirect()->back()->with('error', 'Failed to add cover');
            }
            // throw $th;
        }
    }

    public function removeCover(Request $request){
        DB::beginTransaction();
        try {
            $pol = Polmaster::where('endorse_no', $request->endt_renewal_no)->first();
            $premium = Polmedoptplan::where('member_no' , $request->member_no)
                            ->where('endt_renewal_no' , $request->endt_renewal_no)
                            ->where('plan_code' , $request->plan)
                            ->whereNull('cancelled')
                            ->first()->premium;
            $amounts = $this->getEndorsePremium($request->endt_renewal_no,$request->member_no, $request->plan, $premium);
           
            $curr_endt = $pol->endorse_no;
        
            # Get Previous Endt
            $dcon_no = Dcontrol::where('policy_no', $pol->policy_no)
                        ->where('class', $pol->class)
                        ->where('endt_renewal_no', '<>', $curr_endt)
                        ->whereNull('delete_str')
                        ->max('dcon_no');
            $dcontrolOld = Dcontrol::where('policy_no', $pol->policy_no)
                            ->where('class', $pol->class)
                            ->where('dcon_no', $dcon_no)
                            ->first();

            # Check Debit Mast For Record
            $prev_endt = $dcontrolOld->endt_renewal_no;
            $debitMastCheck = Debitmast::where('policy_no', $pol->policy_no)
                                ->where('endt_renewal_no', $prev_endt)
                                ->where('class', $pol->class)
                                ->exists();


            if ($dcontrolOld->trans_type == 'NIL') {
                $debitMastCheck = true;
            }

            $member_old = Polmedoptplan::where('policy_no', $pol->policy_no)
                        ->where('endt_renewal_no', $prev_endt)
                        ->where('member_no', $request->member_no)
                        ->first();
                        
            if (is_null($member_old)) {
            
                Polmedoptplan::where('member_no' , $request->member_no)
                                        ->where('endt_renewal_no' , $request->endt_renewal_no)
                                        ->where('plan_code' , $request->plan)
                                        ->delete();
            }else{
            
                Polmedoptplan::where('member_no' , $request->member_no)
                                        ->where('endt_renewal_no' , $request->endt_renewal_no)
                                        ->where('plan_code' , $request->plan)
                                        ->update([
                                            'cancelled'=>'Y',
                                            'endorse_amount'=> $amounts[0]
                                        ]);
            }

            
            $this->updatePremiumPols($request->endt_renewal_no, $pol->policy_no);
            DB::commit();
            return ['status'=>1];
        } catch (\Throwable $th) {
            DB::rollback();
            return ['status'=>0];
        }
    }

    public function checkFullMedicalSchedule($endt_renewal_no){
        try {
            $pol = Polmaster::where('endorse_no',$endt_renewal_no)->first();
            $members_count  = Medmember::where('endt_renewal_no', $endt_renewal_no)->whereNull('cancelled')->count();
            $all_details = true;
            if ($members_count > 0) {
                $members  = Medmember::where('endt_renewal_no', $endt_renewal_no)->whereNull('cancelled')->get();
    
                foreach ($members as $member) {
                    $dependant_count = $member->number_of_dependants+1;
    
                    $uploaded_dependants = Meddependant::where('member_no', $member->member_no)
                                ->where('endt_renewal_no', $endt_renewal_no)
                                ->whereNull('cancelled')
                                ->count();
                    if ($dependant_count != $uploaded_dependants) {
                        $all_details = false;
                        break;
                    }
                }
            }else {
                $this->updatePremiumPols($endt_renewal_no, $policy_no);
                $all_details = false;
            }
            $prem = Polmedprem::where('endt_renewal_no', $endt_renewal_no)->sum('endorse_amount');

            if ($prem <= 0) {
                $all_details = true;
            }

            if ($pol->trans_type =='RFN') {
                $all_details = true;
            }
    
            return $all_details;
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function computeMedicalPremium($endt_renewal_no, $policy_no){
        try {
            $members  = Medmember::where('endt_renewal_no', $endt_renewal_no)
                        ->where('policy_no', $policy_no)
                        ->get();
            foreach ($members as $member) {
                $premium = $this->memberPlanPremium($member, $member->plan_code);
                $plan = DB::table('mediplans')->where('plan_code', $member->plan_code)->first();
                $plan_limit = $plan->plan_limit;
                $amounts = $this->getEndorsePremium($endt_renewal_no,$member->member_no, $member->plan_code, $premium);
                
                Polmedprem::updateOrInsert(
                    [
                        'member_no' => $member->member_no,
                        'policy_no' => $policy_no,
                        'endt_renewal_no' => $endt_renewal_no,
                    ],
                    [
                        'premium' => $amounts[1],
                        'endorse_amount' => $amounts[0],
                        'sum_insured' => $plan_limit,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                        'created_by' => Auth::user()->user_name,
                        'updated_by' => Auth::user()->user_name,
                ]);
    
            }
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function memberPlanPremium(Medmember $member, $plan_code){
        try {
            $dependant_count = $member->number_of_dependants;
    
            
            $plan = DB::table('mediplans')->where('plan_code', $plan_code)->first();
            $rate_type = $member->rate_type;

            #premium for optional to use table based
            if ($plan->plantype_code != 1) {
                $rate_type = 'G';
                #maternity to apply no dependants
                if ($plan->plantype_code == 3) {
                    $dependant_count = 0;
                }
            }
            $policy_no = $member->policy_no;
            $endt_renewal_no = $member->endt_renewal_no;
            $premium = 0;
            switch ($rate_type) {
                case 'G':
                case 'I':
                    if ($member->limit_per == 'F') {
                        $prem_det = DB::table('medprems')->where('plan_code', $plan_code)
                                    ->where('rate_type', $rate_type)
                                    ->where('no_of_dependants', $dependant_count)
                                    ->first();
                                    
                        if (is_null($prem_det)) {
                            $max_dependants = DB::table('medmembernos')
                                                ->where('id', '<>', 99)
                                                ->max('id');
                            if ($max_dependants < $dependant_count) {
                                $dep_premium = DB::table('medprems')->where('plan_code', $plan_code)
                                            ->where('rate_type', $rate_type)
                                            ->where('number_of_dependants', $max_dependants)
                                            ->first()->premium;

                                if (is_null($dep_premium)) {
                                    throw ValidationException::withMessages([
                                        'member' => ['Premium configurations for this plan is not set.'],
                                        ]);
                                }

                                $extra_premium = DB::table('medprems')->where('plan_code', $plan_code)
                                                ->where('rate_type', $rate_type)
                                                ->where('number_of_dependants', 99)
                                                ->first()->premium;

                                if (is_null($extra_premium)) {
                                    throw ValidationException::withMessages([
                                        'member' => ['Premium configurations for this plan is not set.'],
                                        ]);
                                }

                                $total_extra_premium = $extra_premium * ($dependant_count - $max_dependants);
    
                                $premium = $total_extra_premium + $dep_premium;
                            }
                        }else {
                            $premium = $prem_det->premium;
                        }
                    } else {
                        
                        $prem_det = DB::table('medprems')->where('plan_code', $plan_code)
                                    ->where('rate_type', $rate_type)
                                    ->where('no_of_dependants', 0)
                                    ->first()->premium;
                        
                        $premium = $prem_det * ($dependant_count+1);

                        if (is_null($prem_det)) {
                            throw ValidationException::withMessages([
                                'member' => ['Premium configurations for this plan is not set.'],
                                ]);
                        }
    
                    }
                    
                    break;
    
                case 'A':
                    $all_dependants = Meddependant::where('member_no', $member->member_no)
                                ->where('policy_no', $policy_no)
                                ->where('endt_renewal_no', $endt_renewal_no)
                                ->get();
    
                    foreach ($all_dependants as $dept) {
                        
                        $birthdate = Carbon::parse($dept->date_of_birth);
                        $currentDate = Carbon::now();
                        
                        // Calculate the age
                        $age = $birthdate->diffInYears($currentDate);
    
                        $dependant_type = DB::table('medirelationships')->where('relation_code',$dept->relationship)->first()->member_type;
    
    
                        $prem_det = DB::table('medpremage')->where('plan_code', $plan_code)
                                        ->where('age_from', '<', $age)
                                        ->where('age_to', '>', $age)
                                        ->where('dependant_type', $dependant_type)
                                        ->first();

                        if (!is_null($prem_det)) {
                            $dept_prem = $prem_det->premium;
    
                            $premium = $premium + $dept_prem;
                        }else{
                            throw ValidationException::withMessages([
                                'member' => ['Premium configurations for this plan is not set.'],
                            ]);
                        }
                    }
                    break;
                
                default:
                throw ValidationException::withMessages([
                    'member' => ['Failed, try again.'],
                    ]);
                    break;
            }
            if ($premium == 0 || $premium < 1) {
                throw ValidationException::withMessages([
                    'member' => ['Premium configurations for '.$plan->description.' this plan is not set.'],
                    ]);
            }
            return $premium;
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function updatePremiumPols($endt_renewal_no, $policy_no){
        try {
            $risk = new Risk;
            $location = 1;

            $risk->update_polsect($policy_no, $location, $endt_renewal_no);  
            # Update Polmaster
            $risk->update_polmaster($endt_renewal_no);
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function addSections($member_no, $plan_code){
        try {
            

            $member =  Medmember::where('member_no', $member_no)
                        ->first();

            $sections = DB::table('mediplansections')
                        ->join('medisections', 'mediplansections.section_code', '=', 'medisections.section_code')
                        ->where('plan_code', $plan_code)
                        ->select('medisections.section_code', 'medisections.section_description', 'mediplansections.coverage')
                        ->get();
            
            foreach ($sections as $section) {
                DB::table('polmedsecs')->updateOrInsert(
                    [
                        'member_no' => $member->member_no,
                        'policy_no' => $member->policy_no,
                        'endt_renewal_no' => $member->endt_renewal_no,
                        'plan_code' => $plan_code,
                        'section_code' => $section->section_code,
                    ],
                [
                    'description' => $section->section_description,
                    'coverage' => $section->coverage,
                    'created_by' => Auth::user()->user_name,
                    'updated_by' => Auth::user()->user_name,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
            }
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function addLimits($member_no, $plan_code){
        try {
            
            $member =  Medmember::where('member_no', $member_no)
                        ->first();

            $limits = DB::table('mediplanlimits')
                        ->join('medilimits', 'mediplanlimits.limit_id', '=', 'medilimits.limit_id')
                        ->where('plan_code', $plan_code)
                        ->select('medilimits.limit_id', 'medilimits.limit_description', 'mediplanlimits.rate_amount', 'mediplanlimits.limit_amount', 'mediplanlimits.limit_rate')
                        ->get();
            $plan = MediPlan::where('plan_code', $plan_code)->first();
            
            foreach ($limits as $limit) {
                DB::table('polmedlimits')->updateOrInsert(
                    [
                        'member_no' => $member->member_no,
                        'policy_no' => $member->policy_no,
                        'endt_renewal_no' => $member->endt_renewal_no,
                        'plan_code' => $plan_code,
                        'limit_id' => $limit->limit_id,
                    ],
                [
                    'description' => $limit->limit_description,
                    'amount' => $limit->rate_amount == 'A' ? (float)$limit->limit_amount: (float)$limit->limit_rate*$plan->plan_limit/100,
                    'created_by' => Auth::user()->user_name,
                    'updated_by' => Auth::user()->user_name,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
            }
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function getEndorsePremium($endt_renewal_no,$member_no,$plan_code, $gross_amount){
        $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->first();
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->first();
        $risk = new Risk;
        $gross_premium = $gross_amount;
        $endorse_amount = $gross_amount;
        $yearLength = $risk->yearLength($polmaster->uw_year, $dcontrol->endt_renewal_no);

        $curr_endt = $dcontrol->endt_renewal_no;
    
        # Get Previous Endt
        $dcon_no = Dcontrol::where('policy_no', $dcontrol->policy_no)
                    ->where('class', $dcontrol->class)
                    ->where('endt_renewal_no', '<>', $curr_endt)
                    ->whereNull('delete_str')
                    ->max('dcon_no');
        $dcontrolOld = Dcontrol::where('policy_no', $dcontrol->policy_no)
                        ->where('class', $dcontrol->class)
                        ->where('dcon_no', $dcon_no)
                        ->first();

        # Check Debit Mast For Record
        $prev_endt = $dcontrolOld->endt_renewal_no;
        $debitMastCheck = Debitmast::where('policy_no', $dcontrol->policy_no)
                            ->where('endt_renewal_no', $prev_endt)
                            ->where('class', $dcontrol->class)
                            ->exists();

        if ($dcontrolOld->trans_type == 'NIL') {
            $debitMastCheck = true;
        }

        $plan = DB::table('mediplans')->where('plan_code', $plan_code)->first();
        $plan_optional = $plan->plantype_code;
        if ($plan_optional != 1) {
            $member_old = Polmedoptplan::where('policy_no', $dcontrol->policy_no)
                        ->where('endt_renewal_no', $prev_endt)
                        ->where('member_no', $member_no)
                        ->first();
        }else {
            $member_old = Polmedprem::where('policy_no', $dcontrol->policy_no)
                        ->where('endt_renewal_no', $prev_endt)
                        ->where('member_no', $member_no)
                        ->first();
        }
        

        if ($plan_optional == 1) {


            $old_member_det = Medmember::where('policy_no', $dcontrol->policy_no)
                            ->where('endt_renewal_no', $prev_endt)
                            ->where('member_no', $member_no)
                            ->first();

            $new_member_det = Medmember::where('policy_no', $dcontrol->policy_no)
                            ->where('endt_renewal_no', $curr_endt)
                            ->where('member_no', $member_no)
                            ->first();
            

            if ($new_member_det->cancelled != 'Y' && $dcontrol->trans_type != 'REN' && $dcontrol->trans_type != 'RNS' ) {
                if ($old_member_det->plan_code == $new_member_det->plan_code) {
                    if ($old_member_det->rate_type == $new_member_det->rate_type) {
                        if ($old_member_det->number_of_dependants == $new_member_det->number_of_dependants) {
                            if ($new_member_det->rate_type == 'A') {
                                if ($old_member_det->limit_per == $new_member_det->limit_per) {
                                    $depet_old = Meddependant::where('member_no', $old_member_det->member_no)
                                                ->where('endt_renewal_no', $prev_endt)
                                                ->whereNull('cancelled')
                                                ->get();
                                    $dob_change = 0;
                                    foreach ($depet_old as $d_old) {
                                        $depet_new = Meddependant::where('member_no', $new_member_det->member_no)
                                                ->where('endt_renewal_no', $curr_endt)
                                                ->whereNull('cancelled')
                                                ->get();
                                        foreach ($depet_new as $d_new) {
                                            if ($d_old->dependant_id == $d_new->dependant_id) {
                                                if ($d_old->date_of_birth != $d_new->date_of_birth) {
                                                    $dob_change = 1;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    if ($dob_change == 0) {
                                        return [0, $gross_premium];
                                    }
                                }
                            }else {
                                return [0, $gross_premium];
                            }
                        }
                    }elseif (($old_member_det->rate_type == 'G' && $new_member_det->rate_type == 'I') ||
                    ($old_member_det->rate_type == 'I' && $new_member_det->rate_type == 'G')) {
                        return [0, $gross_premium];
                    }
                    
                }
            }
        }

        if (($polmaster->trans_type != 'POL' && $polmaster->trans_type != 'REN' && $polmaster->trans_type != 'RNS') && (!is_null($member_old))) {
            
            if ($debitMastCheck) {
    
                $new_endorse_amount = 0;
                switch ($dcontrol->ast_marker) {
                    case 'A':
                        # New Endorsement Process For Annual Policies Only
                        switch ($dcontrol->trans_type) {
                            case 'RFN':
                            case 'CXT':
                            case 'EXT':
                                    $new_gross_amount = $gross_premium;
        
                                    # Calculate Endorsement Amount
                                    $curr_amount = $new_endorse_amount;
                                    $prev_amount = $member_old->premium;
                                    $true_amount = $curr_amount - $prev_amount;
        
                                    # Premium Proration
                                    $new_endorse_amount = $true_amount;
                                    $new_endorse_amount = $risk->prorate($dcontrol->endt_renewal_no, $true_amount);
        
                            break;
        
                            default:
        
                            break;
                        }
                    break;
                    case 'S':
                        $method = $dcontrol->short_term_method;
                        $curr_amount = $new_endorse_amount;
                        $prev_amount = $member_old->premium;
                        $true_amount = $curr_amount - $prev_amount;
    
                
                        if ($method == 'P') {
                            # Percentage Method
                            $new_gross_amount = $gross_premium;
                
                            $new_endorse_amount = $true_amount * ($dcontrol->days_covered / $yearLength);
                            
                        } else if($method == 'S' ) {
                            # Percentage Method
                            $new_gross_amount = $gross_premium;
                
                            $new_endorse_amount = $true * ($dcontrol->short_term_percent / 100);
                            
                        }
                    break;
                    case 'T':
                        $new_gross_amount = $gross_premium;
                        $new_endorse_amount = $true_amount * ($dcontrol->endt_days / $dcontrol->days_covered);
            
                    break;
        
                    case 'I':
                        $new_gross_amount = $gross_premium * ($dcontrol->days_covered / $yearLength);
                        $new_endorse_amount = $true_amount * ($dcontrol->days_covered / $yearLength);
                        
                    break;
                    default:
        
                    break;
                }
    
    
    
                
                if($dcontrol->prorate_cxt == "P" && $dcontrol->trans_type=='CXT'){
                    $ext_from = $polmaster->period_from;
                    $ext_to = $polmaster->period_to;
                    $cov_from = $polmaster->cov_period_from;
                    $cov_to = $polmaster->cov_period_to;
                    $ext_period = (strtotime($ext_to) - strtotime($ext_from)) / (60 * 60 * 24);
                    $cov_period = (strtotime($cov_to) - strtotime($cov_from)) / (60 * 60 * 24);
            
                    $new_gross_amount = $new_endorse_amount * ($ext_period / $cov_period);
            
                }else if($dcontrol->prorate_cxt == "S" && $dcontrol->trans_type=='CXT'){
    
                    $new_gross_amount = $new_endorse_amount * ($dcontrol->short_term_percent / 100);
    
                }else if($dcontrol->prorate_cxt == "F" && $dcontrol->trans_type=='CXT'){
                    $new_gross_amount = $new_endorse_amount;
                }
    
    
            }
        }else {
            # Premium Proration
            $new_gross_amount = $gross_premium;
            switch ($dcontrol->ast_marker) {
              case 'A':
                $new_endorse_amount = $endorse_amount;
              break;
        
              case 'T':
                $new_endorse_amount = $endorse_amount * ($dcontrol->endt_days / $dcontrol->days_covered);
              break;
        
              case 'S':
                $method = $dcontrol->short_term_method;
        
                if ($method == 'P') {
                  $new_endorse_amount = $endorse_amount * ($dcontrol->days_covered / $yearLength);
                } else if($method == 'S' ) {
                  # Percentage Method
                  $new_endorse_amount = $endorse_amount * ($dcontrol->short_term_percent / 100);
                }
              break;
        
              case 'I':
                $new_endorse_amount = $endorse_amount * ($dcontrol->days_covered / 365);
              break;
            }
        }
        return [$new_endorse_amount, $new_gross_amount];
    }

    public function validatePlanPremiumSetup(Medmember $member){
        try {
            $dependant_count = $member->number_of_dependants;
    
            $plan_code = $member->plan_code;
            $plan = DB::table('mediplans')->where('plan_code', $plan_code)->first();
            $rate_type = $member->rate_type;
            $premium = 0;
            switch ($member->rate_type) {
                case 'G':
                case 'I':
                    if ($member->limit_per == 'F') {
                        $prem_det = DB::table('medprems')->where('plan_code', $plan_code)
                                    ->where('rate_type', $rate_type)
                                    ->where('no_of_dependants', $dependant_count)
                                    ->first();
                                    
                        if (is_null($prem_det)) {
                            $max_dependants = DB::table('medmembernos')
                                                ->where('id', '<>', 99)
                                                ->max('id');
                                                
                    
                            if ($max_dependants < $dependant_count) {
                                $dep_premium = DB::table('medprems')->where('plan_code', $plan_code)
                                            ->where('rate_type', $rate_type)
                                            ->where('number_of_dependants', $max_dependants)
                                            ->first()->premium;

                                if (is_null($dep_premium)) {
                                    throw ValidationException::withMessages([
                                        'member' => ['Premium configurations for '.$plan->description.' is not set.'],
                                        ]);
                                }

                                $extra_premium = DB::table('medprems')->where('plan_code', $plan_code)
                                                ->where('rate_type', $rate_type)
                                                ->where('number_of_dependants', 99)
                                                ->first()->premium;

                                if (is_null($extra_premium)) {
                                   
                                    throw ValidationException::withMessages([
                                        'member' => ['Premium configurations for '.$plan->description.' is not set.'],
                                    ]);
                                }
                            }
                            else{
                                throw ValidationException::withMessages([
                                    'member' => ['Premium configurations for '.$plan->description.' is not set.'],
                                ]);
                            }
                        }
                        
                    } else {
                        $prem_det = DB::table('medprems')->where('plan_code', $plan_code)
                                    ->where('rate_type', $rate_type)
                                    ->where('no_of_dependants', 0)
                                    ->first()->premium;
                        if (is_null($prem_det)) {
                            throw ValidationException::withMessages([
                                'member' => ['Premium configurations for '.$plan->description.' is not set.'],
                            ]);
                        }
                    }
                    
                    break;
    
                case 'A':
                    $all_dependants = Meddependant::where('member_no', $member->member_no)
                                ->where('policy_no', $policy_no)
                                ->where('endt_renewal_no', $endt_renewal_no)
                                ->whereNull('cancelled')
                                ->get();
    
                    foreach ($all_dependants as $dept) {
                        
                        $birthdate = Carbon::parse($dept->date_of_birth);
                        $currentDate = Carbon::now();
                        
                        // Calculate the age
                        $age = $birthdate->diffInYears($currentDate);
    
                        $dependant_type = DB::table('medirelationships')->where('relation_code',$dept->relationship)->first()->member_type;
    
    
                        $prem_det = DB::table('medpremage')->where('plan_code', $plan_code)
                                        ->where('age_from', '<', $age)
                                        ->where('age_to', '>', $age)
                                        ->where('dependant_type', $dependant_type)
                                        ->first();

                        if (is_null($prem_det)){
                            throw ValidationException::withMessages([
                                'member' => ['Premium configurations for '.$plan->description.' is not set.'],
                            ]);
                        }
                    }
                    break;
                
                default:
                throw ValidationException::withMessages([
                    'member' => ['Failed, try again'],
                ]);
                    break;
            }
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function getMedicalReference($class){
        $ref = Classbr::where('class', $class)->first()->medical_reference;
        if (is_null($ref)) {
            $ref = 1;
        }

        Classbr::where('class', $class)->update([
            'medical_reference'=>$ref+1
        ]);
        return $ref;
    }

    public function cancelMedicals($endt_renewal_no, $cnc_rfn_endt = null)
    {
        $dcontrol = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();
        $dcontrolPrev = Dcontrol::previous_endorsement($dcontrol->endt_renewal_no); 
        $debitdtl = Debitdtl::where('endt_renewal_no',$endt_renewal_no)->first();

        $refund_method = $dcontrol->refund_type;
        $cancel = 'N';

        if($refund_method == 'F'){
            $full_refund = 'Y';
        }
        elseif($dcontrol->trans_type == 'CNC'){
            $cnc_reason = Endorse_descr::where('descr_code',$debitdtl->detail_code)->first();
            $full_refund = $cnc_reason->full_refund;
            $cancel = 'Y';
        }
        
        $effective_date = Carbon::parse($dcontrol->effective_date);
        $period_from = Carbon::parse($dcontrol->cov_period_from);

        $days_covered = $effective_date->diffInDays($period_from);

        $members = Medmember::where('endt_renewal_no', $dcontrol->endt_renewal_no)
                    ->get();

        $disc_prem = Discounts_loadings::where('endt_renewal_no', $cnc_rfn_endt)
                    ->where('location', 1)
                    ->where('compute_prem', 'Y')
                    ->where('deleted','<>', 'Y')
                    ->where('type', 'D')
                    ->sum('endorse_amount');

        $load_prem = Discounts_loadings::where('endt_renewal_no', $cnc_rfn_endt)
                    ->where('location', 1)
                    ->where('compute_prem', 'Y')
                    ->where('deleted','<>', 'Y')
                    ->where('type', 'L')
                    ->sum('endorse_amount');

        $loc_endorse_amt = 0;
        foreach ($members as $mem) {
            $active_endorsments = (!is_null($cnc_rfn_endt)) ? [$cnc_rfn_endt] :
                                    Dcontrol::where('policy_no',$dcontrol->policy_no)
                                        ->whereNull('delete_str')
                                        ->pluck('endt_renewal_no');

            $total_endorse_amt = Polmedprem::where('policy_no',$dcontrol->policy_no)
                ->where('member_no', $mem->member_no)
                ->whereIn('endt_renewal_no', $active_endorsments)
                ->sum('endorse_amount');

            $endorse_amount = $this->refundAmount($total_endorse_amt, $full_refund, $refund_method, $days_covered);
            $loc_endorse_amt += $endorse_amount;
            Polmedprem::where('policy_no',$dcontrol->policy_no)
                ->where('member_no', $mem->member_no)
                ->where('endt_renewal_no', $endt_renewal_no)
                ->update([
                    'updated_at' => Carbon::now(),
                    'endorse_amount' => $endorse_amount,
                    'updated_by' => Auth::user()->user_name
                ]);

            $plans = DB::table('mediplans')->where('optional', 'Y')->get();

            foreach ($plans as $plan) {
                $total_opt_endorse_amt = Polmedoptplan::where('policy_no',$dcontrol->policy_no)
                    ->where('member_no', $mem->member_no)
                    ->where('plan_code', $plan->plan_code)
                    ->whereIn('endt_renewal_no', $active_endorsments)
                    ->sum('endorse_amount');

                if (!is_null($total_opt_endorse_amt)) {

                
                    $opt_endorse = $this->refundAmount($total_opt_endorse_amt, $full_refund, $refund_method, $days_covered);
    
                    Polmedoptplan::where('policy_no',$dcontrol->policy_no)
                        ->where('member_no', $mem->member_no)
                        ->where('plan_code', $plan->plan_code)
                        ->where('endt_renewal_no', $endt_renewal_no)
                        ->update([
                            'updated_at' => Carbon::now(),
                            'endorse_amount' => $opt_endorse,
                            'updated_by' => Auth::user()->user_name
                        ]);
                    $loc_endorse_amt += $opt_endorse;
                }
            }
            
            

        }

        if($refund_method == "S"){
            $refund_rates = RefundRates::whereRaw("minimum_days <= '".$days_covered."' and maximum_days >= '".$days_covered."'")->first();
            $cnc_rate = $refund_rates->percentage;             
            if (!empty($cnc_rate) || $cnc_rate > 0) {
                    $disc_prem = $disc_prem - (($cnc_rate * $disc_prem) / 100);
                    $load_prem = $load_prem - (($cnc_rate * $load_prem) / 100);
            }

        }
        else if($refund_method == "P"){

                $disc_prem = $this->prorate($dcontrol->endt_renewal_no, $disc_prem);
                $load_prem = $this->prorate($dcontrol->endt_renewal_no, $load_prem);

        }

        $loc_endorse_amt = $loc_endorse_amt - $load_prem + $disc_prem;   

        //UPDATE LOC
        $upd_polsect = Polsect::where('policy_no',$dcontrol->policy_no)
                              ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                              ->where('location',1)
                              ->update([
                                  'endorse_amount' => $loc_endorse_amt,
                                  'sys_endorse_amount' => $loc_endorse_amt
                              ]);

        $upd_polsect = Polsectend::where('policy_no',$dcontrol->policy_no)
                                  ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                                  ->where('location',1)
                                  ->update([
                                      'endorse_amount' => $loc_endorse_amt,
                                      'sys_endorse_amount' => $loc_endorse_amt
                                  ]);

        (new Risk)->update_polmaster($endt_renewal_no);

        return [
            'status' => 1,
            'message' => 'Successfully cancelled'
        ];
    }

    public function refundAmount($total_endorse_amt, $full_refund, $refund_method, $days_covered){
        
        $endorse_amount = 0;
        if($full_refund == 'Y'){
                
            if($refund_method == 'F'){
                $cnc_amount = $total_endorse_amt;
            }
            else if($refund_method == "S"){
                $refund_rates = RefundRates::whereRaw("minimum_days <= '".$days_covered."' and maximum_days >= '".$days_covered."'")->first();
                $cnc_rate = $refund_rates->percentage;
                    
                if (!empty($cnc_rate) || $cnc_rate == 0) {
                    $retained_amount = ($cnc_rate * $total_endorse_amt) / 100;
                    $cnc_amount = $total_endorse_amt - $retained_amount; 
                } else {
                    $cnc_amount = 0;
                }
            }
            else if($refund_method == "P"){
                $cnc_amount = (new Risk)->prorate($dcontrol->endt_renewal_no, $total_endorse_amt);
            }

            $cnc_amount *=-1;

            $endorse_amount = $cnc_amount;

        }
        else{
            $endorse_amount = 0;      
        }

        return $endorse_amount;
    }

}