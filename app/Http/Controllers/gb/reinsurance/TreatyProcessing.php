<?php

namespace App\Http\Controllers\gb\reinsurance;

use App\Clmest;
use App\Clpmn;
use App\Crmast;
use App\Currency;
use App\Debitmast;
use App\Dtran0;
use App\Http\Controllers\Controller;
use App\Models\Debitmastdtl;
use App\Models\Treaty_mdp;
use App\Models\Treaty_prem_adj;
use App\Models\Treaty_prem_adj_dtl;
use App\Models\Treaty_prem_adj_part;
use App\Models\Treaty_sliding_scale_comm;
use App\Models\TreatyCr_stmt;
use App\Models\TreatyCreditor;
use App\Models\TreatyDtl_stmt;
use App\Models\Treatymastclassuwyr;
use App\Models\Treatymastpart;
use App\Models\Treatymastpartsum;
use App\Models\Treatymastuwyr;
use App\Period;
use App\Pipcnam;
use App\Reiacdet;
use App\Reinsetup;
use App\Tax_code;
use App\Treaty;
use App\Treatypart;
use App\Treatysetup;
use App\Treatytype;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Yajra\DataTables\DataTables;

class TreatyProcessing extends Controller
{
    private int $_account_year;
    private int $_qtr;
    private array $_qtr_months;
    private string $_qtr_months_str;

    private bool $_quarterly_process;

    private bool $_deleteExistingData;
    private array $_deletableMonthlyTables;
    private array $_deletableQtrTables;
    private array $_deletableAnnualTables;

    private $_debit;
    private $_clm;
    private $_cashloss;
    private $_trtpayment;
    private $_treatySummary;
    private $_treatySummaryPart;
    private $_treatymastpart;
    private $_treatysetup;
    private $_mdp;
    private $_retPremiumData;

    private $_xl_premium_dtl;
    private $_xl_treaty_premiums;
    private $_treatyAdjustment;

    private float $_writtenPremium;
    private string $_treatyCode;
    private int $_uwYear;

    public string $endt_renewal_no;
    public string $claim_no;
    public string $treaty_code;
    public string $doc_type;
    public string $entry_type_descr;
    public string $dtrans_no;
    public string $account_year;
    public string $ln_no;

    private array $_treatyData;

    public function __construct()
    {
        $this->_deleteExistingData = true;
        $this->_deletableMonthlyTables = [
            'treatymastpart',
            'treatymastclassuwyr',
            'treatymastuwyr',
        ];
        $this->_deletableQtrTables = [
            'treatymastpartsum',
        ];
        $this->_deletableAnnualTables = [
            'treaty_prem_adj_dtl',
            'treaty_prem_adj',
            'treaty_prem_adj_part',
        ];

        $this->_xl_premium_dtl = collect();
        $this->_xl_treaty_premiums = collect();

        $this->_quarterly_process = false;
    }

    public function process_qtr_treaty(Request $request)
    {
        $request->validate([
            'account_year' => 'required',
            'frequency_value' => 'required'
        ]);

        DB::beginTransaction();
        try
        {
            $frequency_value = $request->frequency_value;
            $this->_quarterly_process = true;
            $this->_account_year = $request->account_year;

            $pipcnam = Pipcnam::first();
            // get months to process

            switch ($pipcnam->treaty_process_frequency ) {
                case 'MONTHLY':
                    $months = [$frequency_value];
                    $account_month = $frequency_value;
                    break;
                case 'QUARTERLY':
                    $months = Period::where('account_year',$this->_account_year)
                        ->where('quarter',$frequency_value)
                        ->pluck('account_month')
                        ->toArray();
                    $account_month = Period::where('account_year',$this->_account_year)
                        ->where('quarter',$frequency_value)
                        ->max('account_month');
                    break;
                case 'HALFWAY':
                    if((int)$frequency_value == 1){
                        $months = Period::where('account_year',$this->_account_year)
                            ->where('account_month','<=',6)
                            ->pluck('account_month')
                            ->toArray();
                        $account_month = 6;
                    }
                    else{
                        $months = Period::where('account_year',$this->_account_year)
                            ->where('account_month','>',6)
                            ->pluck('account_month')
                            ->toArray();
                        $account_month = 12;
                    }

                    break;
                case 'ANNUALLY':
                    $months = Period::where('account_year',$this->_account_year)
                        ->pluck('account_month')
                        ->toArray();
                    $account_month = 12;
                    break;
            }
            $this->_qtr = Period::where('account_year',$this->_account_year)
                ->where('account_month',$account_month)
                ->value('quarter');
            
            $processedMonths = Treatymastclassuwyr::where('account_year',$this->_account_year)
                ->whereIn('account_month',$months)
                ->distinct()
                ->pluck('account_month')
                ->toArray();
            $unprocessedMonths = array_diff($months,$processedMonths);

            if(count($unprocessedMonths) == 0)
            {                
                Session::flash('error',"Statements for all months in Year {$this->_account_year}, Quarter {$this->_qtr} were already generated");
                return redirect()->route('deftrtprocess');
            }

            $this->_qtr_months = $unprocessedMonths;
            $this->_qtr_months_str = implode(',',$unprocessedMonths);
            
            $UWData         = $this->fetchDebitmastdtlData();
            $claimsPaidData = $this->fetchClaimsPaidData();
            $claimsOsData   = $this->fetchClaimsOsData();
            $cashCall       = $this->fetchCashCall();
            $treatyPayments = $this->fetchTreatyPayments();

            // begin
            $this->clearExistingData();

 
            // Process UW transactions
            foreach($UWData as $debit)
            {
                $this->_debit = $debit;
                $this->processUWData();
                $this->persistUWToTreatymastclassuwyr();
            }

            // process claims data
            foreach($claimsPaidData as $claim)
            {
                $this->_clm = $claim;
                $this->processClaimsPaidData();
                $this->persistClaimsPaidToTreatymastclassuwyr();
            }

            // process claims data
            foreach($claimsOsData as $claim)
            {
                $this->_clm = $claim;
                $this->processClaimsOsData();
            }

            // process Cash loss
            foreach($cashCall as $rec)
            {
                $this->_cashloss = $rec;
                $this->processCashCallData();
            }
            // process Cash loss
            foreach($treatyPayments as $trt)
            {
                $this->_trtpayment = $trt;
                $this->processTreatyPaymentsData();
            }

            $treatymastclassuwyrSummaries = $this->fetchTreatymastclassuwyrSummary();
            foreach($treatymastclassuwyrSummaries as $trt)
            {
                $this->_treatySummary = $trt;
                $this->persistSummaryToTreatymastuwyr();
            }

            // treatymastuwyr summary into per participant - non-portfolio
            $treatySummaryParts = $this->fetchTreatymastuwyrSummaryNonPortfolio();
            foreach($treatySummaryParts  as $perPart)
            {
                $this->_treatySummaryPart = $perPart;
                $this->persistTreatymastpartPerParticipant();
            }
            
            // treatymastuwyr summary into per participant - portfolio
            $treatySummaryParts = $this->fetchTreatymastuwyrSummaryPortfolio();
            foreach($treatySummaryParts  as $perPart)
            {
                $this->_treatySummaryPart = $perPart;
                $this->persistTreatymastpartPerParticipant();
            }
            
            $treatysetups = $this->fetchNonProportionalTreatysetups();
            foreach($treatysetups as $treatysetup)
            {
                $this->_treatysetup = $treatysetup;
                $this->processMDP();
                $this->persistMDP();
            }

            // end year processes
            if((int)$this->_qtr == 4)
            {
                // update portfolio withdrawal and entry
                $treatymastclassuwyrSummaries = $this->fetchTreatymastuwyrPortfolio();

                foreach($treatymastclassuwyrSummaries as $trt)
                {
                    $this->_treatySummary = $trt;
                    $this->updatePortfolioToTreatymastuwyr();;
                }
                
                $treatymastclassuwyrSummaries = $this->fetchTreatymastuwyrPortfolio();

                foreach($treatymastclassuwyrSummaries as $trt)
                {
                    $this->_treatySummary = $trt;
                    $this->updatePortfolioToTreatymastpart();;
                }

                // compute profit commission
                $treatymastuwyr = $this->fetchTreatymastuwyrProfitCommission();
                foreach($treatymastuwyr as $trt)
                {
                    if((float)$trt->profit_comm_rate > 0)
                    {
                        $this->_treatySummary = $trt;
                        $this->updateTreatymastuwyrProfitCommission();
                    }
                }

                // compute adjustment commission
                $treatymastuwyr = $this->fetchTreatymastuwyrAdjustmentCommission();

                foreach($treatymastuwyr as $trt)
                {
                    $this->_treatySummary = $trt;
                    $this->updateTreatymastuwyrAdjustmentCommission();
                }

                // compute adjustment commission
                $treatymastuwyr = $this->fetchTreatymastuwyrAdjustmentCommission();

                foreach($treatymastuwyr as $trt)
                {
                    $this->_treatySummary = $trt;
                    $this->updateAdjustmentCommToTreatymastpart();
                }

                // process premium adjustments
                $retPremiumData = $this->fetchCompanyRetentionDebitmastdtl();
                foreach ($retPremiumData as $data) {
                    $this->_retPremiumData = $data;
                    $this->processPremiumAdjustment();
                }
                $this->persistPremiumAdjustmentDtl();
                $this->persistPremiumAdjustment();

                $treatyPremAdjs = $this->fetchTreatyPremAdj();
                foreach($treatyPremAdjs as $adj)
                {
                    $this->_treatyAdjustment = $adj;
                    $this->persistPremiumAdjustmentPart();
                }
            }

            
            // insert into treatymastpartsummary (per broker)
            $treatymastparts = $this->fetchTreatymastpartSummary();
            foreach($treatymastparts as $treatyPart)
            {
                $this->_treatymastpart = $treatyPart;
                $this->persistTreatymastpartSummary();
            }
            DB::commit();

            Session::flash('success','Quarterly Treaty Process Completed Successfully');
            return redirect()->route('deftrtprocess');
        }
        catch(\Throwable $e)
        {
            DB::rollback();

            $error_msg = $e->getMessage();
            $reference = "Year $request->account_year Quarter: $request->quarter";
            $module = __METHOD__;
            $route_name = Route::getCurrentRoute()->getActionName();
            log_error_details($route_name,$error_msg,$reference,$module);
            // dd($e);
            Session::flash('error','Quarterly Treaty Process failed');
            return redirect()->route('deftrtprocess');
        }
        
    }

    public function fetchDebitmastdtlData(): Collection {

        $data = Debitmastdtl::select(
                'd.account_year',
                'd.account_month',
                'd.uw_year',
                'd.class',
                'd.period_from',
                'd.period_to',
                'clsyr.reinclass as reinclass',
                'reincls.treaty_expiry_midyear',
               DB::raw( 'SUM(nvl(d.ret_premium, 0))                as ret_premium'),
               DB::raw( 'SUM(nvl(d.kr_premium, 0))                 as kr_premium'),
               DB::raw( 'SUM(nvl(d.quota_premium, 0))              as quota_premium'),
               DB::raw( 'SUM(nvl(d.surp_1st_premium, 0))           as surp_1st_premium'),
               DB::raw( 'SUM(nvl(d.surp_2nd_premium, 0))           as surp_2nd_premium'),
               DB::raw( 'SUM(nvl(d.surp_3rd_premium, 0))           as surp_3rd_premium'),
               DB::raw( 'SUM(nvl(d.kr_commission, 0))              as kr_commission'),
               DB::raw( 'SUM(nvl(d.quota_commission, 0))           as quota_commission'),
               DB::raw( 'SUM(nvl(d.surp_1st_commission, 0))        as surp_1st_commission'),
               DB::raw( 'SUM(nvl(d.surp_2nd_commission, 0))        as surp_2nd_commission'),
               DB::raw( 'SUM(nvl(d.surp_3rd_commission, 0))        as surp_3rd_commission'),
               DB::raw( 'SUM(nvl(d.vat_on_kr_premium, 0))          as vat_on_kr_premium'),
               DB::raw( 'SUM(nvl(d.vat_on_quota_premium,0))        as vat_on_quota_premium'),
               DB::raw( 'SUM(nvl(d.vat_on_surp_1st_premium, 0))    as vat_on_surp_1st_premium'),
               DB::raw( 'SUM(nvl(d.vat_on_surp_2nd_premium, 0))    as vat_on_surp_2nd_premium'),
               DB::raw( 'SUM(nvl(d.vat_on_surp_3rd_premium, 0))    as vat_on_surp_3rd_premium'),
               DB::raw( 'SUM(nvl(d.vat_on_kr_commission, 0))       as vat_on_kr_commission'),
               DB::raw( 'SUM(nvl(d.vat_on_quota_commission, 0))    as vat_on_quota_commission'),
               DB::raw( 'SUM(nvl(d.vat_on_surp_1st_commission, 0)) as vat_on_surp_1st_commission'),
               DB::raw( 'SUM(nvl(d.vat_on_surp_2nd_commission, 0)) as vat_on_surp_2nd_commission'),
               DB::raw( 'SUM(nvl(d.vat_on_surp_3rd_commission, 0)) as vat_on_surp_3rd_commission')
            )
            ->from('debitmastdtl as d')
            ->join('classyear as clsyr', function ($join) {
                $join->on('clsyr.uw_year', '=', 'd.uw_year')
                    ->on('clsyr.class', '=', 'd.class');
            })
            ->join('reinclass as reincls', 'reincls.reinclass', '=', 'clsyr.reinclass')
            ->where('d.account_year', '=', $this->_account_year)
            ->whereIn('d.account_month', $this->_qtr_months)
            ->where(function ($query) {
                $query->where('d.kr_premium', '<>', 0)
                    ->orWhere('d.quota_premium', '<>', 0)
                    ->orWhere('d.ret_premium', '<>', 0)
                    ->orWhere('d.surp_1st_premium', '<>', 0)
                    ->orWhere('d.surp_2nd_premium', '<>', 0)
                    ->orWhere('d.surp_3rd_premium', '<>', 0);
            })
            ->groupBy('d.account_year', 'd.account_month', 'd.uw_year', 'd.class', 'd.period_from', 'd.period_to', 'clsyr.reinclass', 'reincls.treaty_expiry_midyear')
            ->get();

        return $data;
    }


    public function fetchClaimsPaidData(): array
    {
        $data = DB::select("
                SELECT
                d.account_year,
                d.account_month,
                clh.uw_year,
                clh.class,
                db.period_from,
                db.period_to,
                clsyr.reinclass AS reinclass,
                reincls.treaty_expiry_midyear,
                d.pay_type,
            SUM(NVL(d.movt_mandatory_recovery, 0)) AS mandatory_recovery,
            SUM(NVL(d.movt_quota_recovery, 0)) AS quota_recovery,
            SUM(NVL(d.movt_surplus_1_recovery, 0)) AS surplus_1_recovery,
            SUM(NVL(d.movt_surplus_2_recovery, 0)) AS surplus_2_recovery,
            SUM(NVL(d.movt_surplus_3_recovery, 0)) AS surplus_3_recovery
            FROM
                clpmn d
            JOIN
                clhmn clh ON clh.claim_no = d.claim_no
            JOIN
                debitmast db ON db.endt_renewal_no = clh.endt_renewal_no
            JOIN
                classyear clsyr ON clsyr.uw_year = clh.uw_year AND clsyr.class = clh.class
            JOIN
                reinclass reincls ON reincls.reinclass = clsyr.reinclass
            WHERE
                d.pay_type IN (10, 20)
                AND d.account_year = $this->_account_year
                AND d.account_month IN ($this->_qtr_months_str)
                AND (
                    d.movt_mandatory_recovery <> 0
                    OR d.movt_quota_recovery <> 0
                    OR d.movt_surplus_1_recovery <> 0
                    OR d.movt_surplus_2_recovery <> 0
                    OR d.movt_surplus_3_recovery <> 0
                )
            GROUP BY
                d.account_year,
                d.account_month,
                clh.uw_year,
                clh.class,
                db.period_from,
                db.period_to,
                clsyr.reinclass,
                reincls.treaty_expiry_midyear,
                d.pay_type
        ");

        return $data;
    }    

    public function fetchClaimsOsData(): array
    {
        $data = DB::select("
            SELECT
                d.account_year,
                d.account_month,
                clh.uw_year,
                clh.class,
                db.period_from,
                db.period_to,
                clsyr.reinclass AS reinclass,
                reincls.treaty_expiry_midyear,
                SUM(NVL(d.mandatory_claim, 0)) AS mandatory_claim,
                SUM(NVL(d.quota_share_claim, 0)) AS quota_claim,
                SUM(NVL(d.surplus_1st_claim, 0)) AS surplus_1_claim,
                SUM(NVL(d.surplus_2nd_claim, 0)) AS surplus_2_claim,
                SUM(NVL(d.surplus_3rd_claim, 0)) AS surplus_3_claim
            FROM
                clmest d
            JOIN
                clhmn clh ON clh.claim_no = d.claim_no
            JOIN
                debitmast db ON db.endt_renewal_no = clh.endt_renewal_no
            JOIN
                classyear clsyr ON clsyr.uw_year = clh.uw_year AND clsyr.class = clh.class
            JOIN
                reinclass reincls ON reincls.reinclass = clsyr.reinclass
            WHERE
                d.account_year = $this->_account_year
                AND d.account_month IN (3, 6, 9, 12)
                AND d.account_month IN ($this->_qtr_months_str) 
                AND (
                    NVL(d.mandatory_claim, 0) + NVL(d.quota_share_claim, 0) + NVL(d.surplus_1st_claim, 0)
                    + NVL(d.surplus_2nd_claim, 0) + NVL(d.surplus_3rd_claim, 0)
                ) <> 0
            GROUP BY
                d.account_year,
                d.account_month,
                clh.uw_year,
                clh.class,
                db.period_from,
                db.period_to,
                clsyr.reinclass,
                reincls.treaty_expiry_midyear
        ");

        return $data;
    }  

    public function fetchCashCall(): array
    {
        
        $data = DB::table('Reiacdet as d')
        ->join('cbmast as cb', function ($join) {
            $join->on('cb.dtrans_no', '=', 'd.dtrans_no')
                ->where('cb.doc_type', '=', 'd.doc_type')
                ->where('cb.account_year', '=', 'd.account_year')
                ->where('cb.entry_type_descr', '=', 'd.entry_type_descr');
        })
        ->join('clhmn as clh', 'clh.claim_no', '=', 'cb.claim_no')
        ->join('debitmast as db', 'db.endt_renewal_no', '=', 'clh.endt_renewal_no')
        ->join('classyear as clsyr', function ($join) {
            $join->on('clsyr.uw_year', '=', 'clh.uw_year')
                ->where('clsyr.class', '=', 'clh.class');
        })
        ->join('reinclass as reincls', 'reincls.reinclass', '=', 'clsyr.reinclass')
        ->select(
            'd.treaty_code',
            'd.account_year',
            'd.account_month',
            'd.uw_year',
            'clh.class',
            'db.period_from',
            'db.period_to',
            DB::raw('clsyr.reinclass AS reinclass'),
            DB::raw('reincls.treaty_expiry_midyear'),
            DB::raw('SUM(NVL(d.nett, 0)) AS nett')
        )
        ->where('d.account_year', '=', $this->_account_year)
        ->whereIn('d.account_month', explode(',', $this->_qtr_months_str)) // Assuming _qtr_months_str is a comma-separated string
        ->where('d.doc_type', '=', 'REC')
        ->where('d.entry_type_descr', '=', 'CAL')
        ->groupBy(
            'd.treaty_code',
            'd.account_year',
            'd.account_month',
            'd.uw_year',
            'clh.class',
            'db.period_from',
            'db.period_to',
            'clsyr.reinclass',
            'reincls.treaty_expiry_midyear'
        )
        ->get();
    
        return $data->toArray();
    }  

    public function fetchReiacdetPerPart($treatypart,$doc_type,$entry_type_descr): object
    {

        $data = DB::table('Reiacdet as d')
        ->select(
            'd.treaty_code',
            'd.account_year',
            'd.account_month',
            'd.uw_year',
            DB::raw('SUM(COALESCE(d.nett, 0)) AS nett')
        )
        ->where('d.account_year', '=', $this->_account_year)
        ->where('d.treaty_code', '=', $treatypart->treaty_code)
        ->where('d.uw_year', '=', $this->_treatySummaryPart->uw_year)
        ->whereIn('d.broker_branch', explode(',', $treatypart->broker_branch))
        ->whereIn('d.broker_agent', explode(',', $treatypart->broker_agent))
        ->whereIn('d.branch', explode(',', $treatypart->branch))
        ->whereIn('d.agent', explode(',', $treatypart->agent))
        ->whereIn('d.account_month', explode(',', $this->_treatySummaryPart->account_month))
        ->where('d.doc_type', '=', $doc_type)
        ->where('d.entry_type_descr', '=', $entry_type_descr)
        ->groupBy('d.treaty_code', 'd.account_year', 'd.account_month', 'd.uw_year')
        ->first(); // Use first() to get a single result (equivalent to [0])


        return (object)$data;
    }  

    public function fetchTreatyPayments(): Collection
    {
        $data = DB::table('Reiacdet d')
            ->select(
                'd.treaty_code',
                'd.account_year',
                'd.account_month',
                'd.uw_year',
                DB::raw('999 as class'),
                DB::raw('SUM( NVL(d.nett, 0)) as nett')
            )
            ->where('d.account_year', '=', $this->_account_year)
            ->whereIn('d.account_month', $this->_qtr_months)
            ->where('d.doc_type', '=', 'PAY')
            ->where('d.entry_type_descr', '=', 'TRT')
            ->groupBy('d.treaty_code', 'd.account_year', 'd.account_month', 'd.uw_year')
            ->get();

        return $data;
    }  

    public function fetchTreatymastclassuwyrSummary(): array
    {
        $data = DB::table('Treatymastclassuwyr as d')
            ->leftJoin('treatysetup as ts', function ($join) {
                $join->on('ts.uw_year', '=', 'd.account_year')
                    ->where('ts.treaty_code', '=', 'd.treaty_code');
            })
            ->select(
                'd.treaty_code',
                'd.account_year',
                'd.account_month',
                'd.uw_year',
                DB::raw('SUM(NVL(d.premium, 0)) AS premium'),
                DB::raw('SUM(NVL(d.earned_premium, 0)) AS earned_premium'),
                DB::raw('SUM(NVL(d.commission, 0)) AS commission'),
                DB::raw('SUM(NVL(d.claims_os, 0)) AS claims_os'),
                DB::raw('SUM(NVL(d.claims_paid, 0)) AS claims_paid'),
                DB::raw('SUM(NVL(d.claims_recovery, 0)) AS claims_recovery'),
                DB::raw('SUM(NVL(d.vat_on_premium, 0)) AS vat_on_premium'),
                DB::raw('SUM(NVL(d.vat_on_commission, 0)) AS vat_on_commission'),
                DB::raw('MAX((NVL(ts.expense_rate, 0) / 100) * d.premium) AS management_expense'),
                DB::raw('SUM(NVL(d.payments, 0)) AS payments'),
                DB::raw('SUM(NVL(d.cashloss, 0)) AS cashloss')
            )
            ->where('d.account_year', '=', $this->_account_year)
            ->whereIn('d.account_month', explode(',', $this->_qtr_months_str))
            ->groupBy(
                'd.treaty_code',
                'd.account_year',
                'd.account_month',
                'd.uw_year'
            )
            ->get();
        
        return $data->toArray();
    }

    public function fetchTreatymastuwyrProfitCommission(): Collection
    {
        $data = DB::Select("
            SELECT D.TREATY_CODE, D.ACCOUNT_YEAR,d.uw_year,
                MAX( NVL(ts.profit_comm_rate, 0)) as profit_comm_rate, 
                SUM( NVL(d.premium, 0)) as premium, 
                SUM( NVL(d.commission, 0)) as commission, 
                SUM( NVL(d.claims_os, 0)) as claims_os, 
                SUM( NVL(d.claims_paid, 0)) as claims_paid, 
                SUM( NVL(d.vat_on_premium, 0)) as vat_on_premium, 
                SUM( NVL(d.vat_on_commission, 0)) as vat_on_commission,
                SUM( NVL(d.portfolio_prem_withdrawal, 0)) as portfolio_prem_withdrawal,
                SUM( NVL(d.portfolio_loss_withdrawal, 0)) as portfolio_loss_withdrawal,
                SUM( NVL(d.portfolio_prem_entry, 0)) as portfolio_prem_entry,
                SUM( NVL(d.portfolio_loss_entry, 0)) as portfolio_loss_entry,
                SUM( NVL(d.payments, 0)) as payments 
            FROM TREATYMASTUWYR d 
            left OUTER join TREATYSETUP ts on TS.UW_YEAR = D.UW_YEAR and TS.TREATY_CODE = d.treaty_code 
            where D.ACCOUNT_YEAR =$this->_account_year
            and (ts.comm_type ='F' or  ts.comm_type is null)
            and nvl(ts.profit_comm_rate,0) <>0 
            group by D.TREATY_CODE, D.ACCOUNT_YEAR,d.uw_year
        ");

        return collect($data);
    }

    public function fetchTreatymastuwyrAdjustmentCommission(): Collection
    {
        $data = DB::Select("
            SELECT D.TREATY_CODE, D.ACCOUNT_YEAR,d.uw_year,
                MAX( NVL(ts.profit_comm_rate, 0)) as profit_comm_rate, 
                SUM( NVL(d.premium, 0)) as premium, 
                SUM( NVL(d.earned_premium, 0)) as earned_premium, 
                SUM( NVL(d.commission, 0)) as commission, 
                SUM( NVL(d.claims_os, 0)) as claims_os, 
                SUM( NVL(d.claims_paid, 0)) as claims_paid, 
                SUM( NVL(d.vat_on_premium, 0)) as vat_on_premium, 
                SUM( NVL(d.vat_on_commission, 0)) as vat_on_commission,
                SUM( NVL(d.portfolio_prem_withdrawal, 0)) as portfolio_prem_withdrawal,
                SUM( NVL(d.portfolio_loss_withdrawal, 0)) as portfolio_loss_withdrawal,
                SUM( NVL(d.portfolio_prem_entry, 0)) as portfolio_prem_entry,
                SUM( NVL(d.portfolio_loss_entry, 0)) as portfolio_loss_entry,
                SUM( NVL(d.adjusted_net_comm, 0)) as adjusted_net_comm,
                SUM( NVL(d.adjusted_comm_amt, 0)) as adjusted_comm_amt,
                SUM( NVL(d.payments, 0)) as payments 
            FROM TREATYMASTUWYR d 
            left OUTER join TREATYSETUP ts on TS.UW_YEAR = D.UW_YEAR and TS.TREATY_CODE = d.treaty_code 
            where D.ACCOUNT_YEAR =$this->_account_year
            and ts.comm_type ='S'
            and nvl(ts.profit_comm_rate,0) <>0 
            group by D.TREATY_CODE, D.ACCOUNT_YEAR,d.uw_year
        ");

        return collect($data);
    }

    public function fetchTreatymastuwyrPortfolio(): array
    {
        $data = DB::select("
            SELECT 
                d.treaty_code,
                d.account_year,
                d.uw_year,
                ts.clean_cut,
                MAX( NVL(ts.premium_port_rate, 0)) as premium_port_rate,
                MAX( NVL(ts.loss_port_rate, 0)) as loss_port_rate,
                SUM( NVL(d.premium, 0)) as premium,
                SUM( NVL(d.commission, 0)) as commission,
                SUM( NVL(d.claims_os, 0)) as claims_os,
                SUM( NVL(d.claims_paid, 0)) as claims_paid,
                SUM( NVL(d.vat_on_premium, 0)) as vat_on_premium,
                SUM( NVL(d.vat_on_commission, 0)) as vat_on_commission,
                SUM( NVL(portfolio_prem_withdrawal, 0)) as portfolio_prem_withdrawal,
                SUM( NVL(portfolio_loss_withdrawal, 0)) as portfolio_loss_withdrawal,
                SUM( NVL(portfolio_prem_entry, 0)) as portfolio_prem_entry,
                SUM( NVL(portfolio_loss_entry, 0)) as portfolio_loss_entry,
                SUM( NVL(d.payments, 0)) as payments
            FROM Treatymastuwyr d
            LEFT OUTER JOIN treatysetup ts on ts.uw_year =d.account_year AND ts.treaty_code = d.treaty_code
            WHERE d.account_year =$this->_account_year
            AND d.uw_year =$this->_account_year
            AND ts.clean_cut = 'Y'
            AND nvl(ts.premium_port_rate,0) <> 0 AND nvl(ts.loss_port_rate,0) <> 0
            GROUP BY d.treaty_code, d.account_year,d.uw_year,ts.clean_cut
        ");

        return $data;
    }

    public function fetchTreatymastuwyrSummaryNonPortfolio(): Collection
    {
        $data = DB::table('Treatymastuwyr d')
            ->where('d.portfolio', '=', 'N')
            ->where('d.account_year', '=', $this->_account_year)
            ->whereIn('d.account_month', $this->_qtr_months)
            ->groupBy('treaty_code', 'account_year', 'account_month', 'uw_year','portfolio')
            ->selectRaw('
                treaty_code,
                account_year,
                account_month,
                uw_year,
                portfolio,
                SUM( NVL(premium, 0)) as premium,
                SUM( NVL(earned_premium, 0)) as earned_premium,
                SUM( NVL(commission, 0)) as commission,
                SUM( NVL(claims_os, 0)) as claims_os,
                SUM( NVL(claims_paid, 0)) as claims_paid,
                SUM( NVL(vat_on_premium, 0)) as vat_on_premium,
                SUM( NVL(vat_on_commission, 0)) as vat_on_commission,
                SUM( NVL(portfolio_prem_withdrawal, 0)) as portfolio_prem_withdrawal,
                SUM( NVL(portfolio_loss_withdrawal, 0)) as portfolio_loss_withdrawal,
                SUM( NVL(portfolio_prem_entry, 0)) as portfolio_prem_entry,
                SUM( NVL(portfolio_loss_entry, 0)) as portfolio_loss_entry,
                SUM( NVL(management_expense, 0)) as management_expense,
                SUM( NVL(prem_tax, 0)) as prem_tax,
                SUM( NVL(payments, 0)) as payments,
                SUM( NVL(profit_commission, 0)) as profit_commission,
                SUM( NVL(adjusted_comm_amt, 0)) as adjusted_comm_amt,
                SUM( NVL(adjusted_net_comm, 0)) as adjusted_net_comm,
                SUM( NVL(claims_recovery, 0)) as claims_recovery
            ')
            ->get();

        return $data;
    }

    public function fetchTreatymastuwyrSummaryPortfolio(): array
    {

        $data = DB::select("
            SELECT
                treaty_code,
                account_year,
                account_year AS uw_year,
                account_month,
                portfolio,
                SUM(NVL(premium, 0)) AS premium,
                SUM(NVL(earned_premium, 0)) AS earned_premium,
                SUM(NVL(commission, 0)) AS commission,
                SUM(NVL(claims_os, 0)) AS claims_os,
                SUM(NVL(claims_paid, 0)) AS claims_paid,
                SUM(NVL(vat_on_premium, 0)) AS vat_on_premium,
                SUM(NVL(vat_on_commission, 0)) AS vat_on_commission,
                SUM(NVL(portfolio_prem_withdrawal, 0)) AS portfolio_prem_withdrawal,
                SUM(NVL(portfolio_prem_entry, 0)) AS portfolio_prem_entry,
                SUM(NVL(portfolio_loss_withdrawal, 0)) AS portfolio_loss_withdrawal,
                SUM(NVL(portfolio_loss_entry, 0)) AS portfolio_loss_entry,
                SUM(NVL(management_expense, 0)) AS management_expense,
                SUM( NVL(prem_tax, 0)) as prem_tax,
                SUM(NVL(payments, 0)) AS payments,
                SUM(NVL(profit_commission, 0)) AS profit_commission,
                SUM( NVL(adjusted_comm_amt, 0)) as adjusted_comm_amt,
                SUM( NVL(adjusted_net_comm, 0)) as adjusted_net_comm,
                SUM( NVL(claims_recovery, 0)) as claims_recovery
            FROM
                Treatymastuwyr d
            WHERE
                d.portfolio = 'Y'
                AND d.account_year = $this->_account_year
                AND d.account_month IN ($this->_qtr_months_str)
            GROUP BY
                treaty_code, account_year, account_month, portfolio
        ");
        return $data;
    }

    public function fetchTreatymastpart(): array
    {
        $data = DB::select("
            SELECT
                b.part_per,
                a.treaty_code,
                a.account_year,
                a.account_month,
                a.uw_year,
                a.quarter,
                a.broker_branch,
                a.broker_agent_no,
                a.branch,
                a.agent_no
            FROM
                Treatymastpart a
            JOIN
                treatypart b ON
                    b.treaty_code = a.treaty_code
                    AND b.uw_year = a.uw_year
                    AND b.broker_branch = a.broker_branch
                    AND b.broker_agent = a.broker_agent_no
                    AND b.branch = a.branch
                    AND b.agent = a.agent_no
            WHERE
                a.account_year = $this->_account_year
                AND a.account_month IN ($this->_qtr_months_str)
            GROUP BY
                b.part_per,
                a.treaty_code,
                a.account_year,
                a.account_month,
                a.uw_year,
                a.quarter,
                a.broker_branch,
                a.broker_agent_no,
                a.branch,
                a.agent_no
        ");
        return $data;
    }

    // 
    public function fetchTreatymastpartSummary(): Collection
    {
        $data = Treatymastpart::where('account_year', '=', $this->_account_year)
            ->where('quarter', '=', $this->_qtr)
            ->groupBy('treaty_code', 'account_year', 'quarter', 'portfolio', 'uw_year', 'broker_branch', 'broker_agent_no')
            ->selectRaw('
                treaty_code,
                account_year,
                quarter,
                portfolio,
                uw_year,
                broker_branch,
                broker_agent_no,
                sum(nvl(premium, 0)) as premium,
                sum(nvl(earned_premium, 0)) as earned_premium,
                sum(nvl(commission, 0)) as commission,
                sum(nvl(claims_os, 0)) as claims_os,
                sum(nvl(claims_paid, 0)) as claims_paid,
                sum(nvl(claims_recovery, 0)) as claims_recovery,
                sum(nvl(vat_on_premium, 0)) as vat_on_premium,
                sum(nvl(vat_on_commission, 0)) as vat_on_commission,
                sum(nvl(portfolio_prem_withdrawal, 0)) as portfolio_prem_withdrawal,
                sum(nvl(portfolio_prem_entry, 0)) as portfolio_prem_entry,
                sum(nvl(management_expense, 0)) as management_expense,
                sum(nvl(prem_tax, 0)) as prem_tax,
                sum(nvl(profit_commission, 0)) as profit_commission,
                sum(nvl(adjusted_comm_amt, 0)) as adjusted_comm_amt,
                sum(nvl(adjusted_net_comm, 0)) as adjusted_net_comm,
                sum(nvl(cashloss, 0)) as cashloss,
                sum(nvl(payments, 0)) as payments
            ')
            ->get();

        return $data;
    }

    public function fetchNonProportionalTreatysetups()
    {
        $data = DB::table('treatysetup')
            ->where('uw_year',$this->_account_year)
            ->whereRaw("substr(treaty_code,1,1)='F'")
            ->where('min_deposit','<>',0)
            ->get([
                'uw_year',
                'treaty_code',
                'min_deposit',
                'deposit_frequency'
            ]);

        return $data;
    }


    public function fetchCompanyRetentionDebitmastdtl(): Collection {
        $data = DB::select("
            SELECT d.ACCOUNT_YEAR ,d.UW_YEAR  ,d.CLASS,r.CLASS reinclass, RET_PREMIUM ,
                d.endt_renewal_no,d.policy_no,
                r.XL_CODE_1 ,r.XL_CODE_2,r.XL_CODE_3 ,r.XL_CODE_4 ,r.XL_CODE_4 ,
                r.CXL_CODE_1 ,r.CXL_CODE_2,r.CXL_CODE_3,r.CXL_CODE_4
                FROM DEBITMASTDTL d
                JOIN CLASSYEAR c ON c.UW_YEAR =d.UW_YEAR  AND c.CLASS =d.CLASS 
                JOIN REINSETUP r ON r.CLASS =c.REINCLASS AND c.UW_YEAR =r.UW_YEAR 
                WHERE RET_PREMIUM <>0 
                AND ACCOUNT_YEAR ='$this->_account_year'
                AND r.UW_YEAR ='$this->_account_year'
                AND (
                    r.XL_CODE_1  IS NOT NULL
                    OR r.XL_CODE_2 IS NOT NULL
                    OR r.XL_CODE_3  IS NOT NULL
                    OR r.XL_CODE_4  IS NOT NULL
                    OR r.XL_CODE_4  IS NOT NULL
                    OR r.CXL_CODE_1  IS NOT NULL
                    OR r.CXL_CODE_2 IS NOT NULL
                    OR r.CXL_CODE_3 IS NOT NULL
                    OR r.CXL_CODE_4 IS NOT NULL
                )
        ");

        return collect($data);
    }

    public function fetchTreatyPremAdj(): Collection {
        $data = DB::select("
            SELECT * FROM treaty_prem_adj d
                WHERE d.adj_premium <>0 
                AND account_year ='$this->_account_year'
                AND uw_year ='$this->_account_year'
        ");

        return collect($data);
    }

    public function fetchTreatysetup(): Treatysetup {

        $treatysetup = Treatysetup::where('treaty_code',$this->_treatyCode)
            ->where('uw_year',$this->_uwYear)
            ->first([
                'treaty_code','uw_year','clean_cut',
                'tax_code','clean_cut',
                'upr_method','upr_flat_rate',
                'period_from','period_to'
            ]);

        return $treatysetup;
    }

    public function clearExistingData()
    {
        if($this->_deleteExistingData)
        {
            foreach ($this->_deletableMonthlyTables as $table) {
                DB::table($table)
                    ->where('account_year',$this->_account_year)
                    ->whereIn('account_month',$this->_qtr_months)
                    ->delete();
            }

            foreach ($this->_deletableQtrTables as $table) {
                DB::table($table)
                    ->where('account_year',$this->_account_year)
                    ->where('quarter',$this->_qtr)
                    ->delete();
            }

            foreach ($this->_deletableAnnualTables as $table) {
                DB::table($table)
                    ->where('account_year',$this->_account_year)
                    ->delete();
            }
        }
    }

    public function processUWData()
    {
        try
        {
            $debit = $this->_debit;
            $reinsetup = $this->reinsetupData();
            $treaty_data = [];

            if ($debit->kr_premium != 0)
            {
                
                $this->_writtenPremium = $debit->kr_premium; 
                $this->_treatyCode = $reinsetup->mandatory_code; 
                $this->_uwYear = $debit->uw_year; 
                $earned_premium = $this->calculateEarnedPremium();
                
                $data = [
                    'treaty_code' => $reinsetup->mandatory_code,
                    'premium' => $debit->kr_premium,
                    'earned_premium' => $earned_premium,
                    'commission' => $debit->kr_commission,
                    'vat_on_premium' => $debit->vat_on_kr_premium,
                    'vat_on_commission' => $debit->vat_on_kr_commission,
                ];

                array_push($treaty_data,$data);
            }

            // quota premium
            if ($debit->quota_premium != 0) 
            {
                                
                $this->_writtenPremium = $debit->quota_premium; 
                $this->_treatyCode = $reinsetup->quota_code; 
                $this->_uwYear = $debit->uw_year; 
                $earned_premium = $this->calculateEarnedPremium();

                $data = [
                    'treaty_code' => $reinsetup->quota_code,
                    'premium' => $debit->quota_premium,
                    'earned_premium' => $earned_premium,
                    'commission' => $debit->quota_commission,
                    'vat_on_premium' => $debit->vat_on_kr_premium,
                    'vat_on_commission' => $debit->vat_on_kr_commission,
                ];

                array_push($treaty_data,$data);
            }

            // surplus 1st premium
            if ($debit->surp_1st_premium != 0) 
            {
                                
                $this->_writtenPremium = $debit->surp_1st_premium; 
                $this->_treatyCode = $reinsetup->surplus_1_code; 
                $this->_uwYear = $debit->uw_year; 
                $earned_premium = $this->calculateEarnedPremium();

                $data = [
                    'treaty_code' => $reinsetup->surplus_1_code,
                    'premium' => $debit->surp_1st_premium,
                    'earned_premium' => $earned_premium,
                    'commission' => $debit->surp_1st_commission,
                    'vat_on_premium' => $debit->vat_on_surp_1st_premium,
                    'vat_on_commission' => $debit->vat_on_surp_1st_commission,
                ];

                array_push($treaty_data,$data);
            }

            // surplus 2nd premium
            if ($debit->surp_2nd_premium != 0) 
            {
                                
                $this->_writtenPremium = $debit->surp_2nd_premium; 
                $this->_treatyCode = $reinsetup->surplus_2_code; 
                $this->_uwYear = $debit->uw_year; 
                $earned_premium = $this->calculateEarnedPremium();

                $data = [
                    'treaty_code' => $reinsetup->surplus_2_code,
                    'premium' => $debit->surp_2nd_premium,
                    'earned_premium' => $earned_premium,
                    'commission' => $debit->surp_2nd_commission,
                    'vat_on_premium' => $debit->vat_on_surp_2nd_premium,
                    'vat_on_commission' => $debit->vat_on_surp_2nd_commission,
                ];

                array_push($treaty_data,$data);
            }

            // surplus 3rd premium
            if ($debit->surp_3rd_premium != 0) 
            {
                                
                $this->_writtenPremium = $debit->surp_3rd_premium; 
                $this->_treatyCode = $reinsetup->surplus_3_code; 
                $this->_uwYear = $debit->uw_year; 
                $earned_premium = $this->calculateEarnedPremium();

                $data = [
                    'treaty_code' => $reinsetup->surplus_3_code,
                    'premium' => $debit->surp_3rd_premium,
                    'earned_premium' => $earned_premium,
                    'commission' => $debit->surp_3rd_commission,
                    'vat_on_premium' => $debit->vat_on_surp_3rd_premium,
                    'vat_on_commission' => $debit->vat_on_surp_3rd_commission,
                ];

                array_push($treaty_data,$data);
            }
            
            $this->_treatyData = $treaty_data;
        }
        catch(\Throwable $e)
        {
            throw $e;
        }
    }

    public function persistUWToTreatymastclassuwyr()
    {
        foreach($this->_treatyData as $data)
        {
            $treaty_code = $data['treaty_code'];

            $w_count = Treatymastclassuwyr::where('account_year', $this->_debit->account_year)
                ->where('account_month', $this->_debit->account_month)
                ->where('uw_year', $this->_debit->uw_year)
                ->where('class', $this->_debit->class)
                ->where('period_from', $this->_debit->period_from)
                ->where('period_to', $this->_debit->period_to)
                ->where('reinclass', $this->_debit->reinclass)
                ->where('treaty_code', $treaty_code)
                ->count();

            if ($w_count == 0) {
                Treatymastclassuwyr::create([
                    'treaty_code'           => $treaty_code,
                    'account_year'          => $this->_account_year,
                    'account_month'         => $this->_debit->account_month,
                    'uw_year'               => $this->_debit->uw_year,
                    'class'                 => $this->_debit->class,
                    'reinclass'             => $this->_debit->reinclass,
                    'period_from'           => $this->_debit->period_from,
                    'period_to'             => $this->_debit->period_to,
                    'premium'               => $data['premium'],
                    'earned_premium'        => $data['earned_premium'],
                    'commission'            => $data['commission'],
                    'vat_on_premium'        => $data['vat_on_premium'],
                    'vat_on_commission'     => $data['vat_on_commission'],
                ]);
            } else {
                Treatymastclassuwyr::where('treaty_code', $treaty_code)
                    ->where('account_year', $this->_account_year)
                    ->where('account_month', $this->_debit->account_month)
                    ->where('uw_year', $this->_debit->uw_year)
                    ->where('class', $this->_debit->class)
                    ->where('period_from', $this->_debit->period_from)
                    ->where('period_to', $this->_debit->period_to)
                    ->where('reinclass', $this->_debit->reinclass)
                    ->update([
                        'premium'           => $data['premium'],
                        'earned_premium'    => $data['earned_premium'],
                        'commission'        => $data['commission'],
                        'vat_on_premium'    => $data['vat_on_premium'],
                        'vat_on_commission' => $data['vat_on_commission'],
                    ]);
            }
        }
    }

    public function processClaimsPaidData()
    {
        try
        {
            $clm = $this->_clm;
            $reinsetup = $this->reinsetupData('CLM');

            $treaty_data = [];

            if ($clm->mandatory_recovery != 0)
            {
                $data = [
                    'treaty_code'       => $reinsetup->mandatory_code,
                    'claims_paid'       => $clm->mandatory_recovery,
                    'claims_recovery'   => $clm->mandatory_recovery,
                    'allocated'         => 0,
                    'unallocated'       => $clm->mandatory_recovery,
                ];

                array_push($treaty_data,$data);
            }

            // quota premium
            if ($clm->quota_recovery != 0) 
            {
                $data = [
                    'treaty_code'       => $reinsetup->quota_code,
                    'claims_paid'       => $clm->quota_recovery,
                    'claims_recovery'   => $clm->quota_recovery,
                    'allocated'         => 0,
                    'unallocated'       => $clm->quota_recovery,
                ];

                array_push($treaty_data,$data);
            }

            // surplus 1st premium
            if ($clm->surplus_1_recovery != 0) 
            {
                $data = [
                    'treaty_code'       => $reinsetup->surplus_1_code,
                    'claims_paid'       => $clm->surplus_1_recovery,
                    'claims_recovery'   => $clm->surplus_1_recovery,
                    'allocated'         => 0,
                    'unallocated'       => $clm->surplus_1_recovery,
                ];

                array_push($treaty_data,$data);
            }

            // surplus 2nd premium
            if ($clm->surplus_2_recovery != 0) 
            {
                $data = [
                    'treaty_code'       => $reinsetup->surplus_2_code,
                    'claims_paid'       => $clm->surplus_2_recovery,
                    'claims_recovery'   => $clm->surplus_2_recovery,
                    'allocated'         => 0,
                    'unallocated'       => $clm->surplus_2_recovery,
                ];

                array_push($treaty_data,$data);
            }

            // surplus 3rd premium
            if ($clm->surplus_3_recovery != 0) 
            {
                $data = [
                    'treaty_code'       => $reinsetup->surplus_3_code,
                    'claims_paid'       => $clm->surplus_3_recovery,
                    'claims_recovery'   => $clm->surplus_3_recovery,
                    'allocated'         => 0,
                    'unallocated'       => $clm->surplus_3_recovery,
                ];

                array_push($treaty_data,$data);
            }

            $this->_treatyData = $treaty_data;
        }
        catch(\Throwable $e)
        {
            throw $e;
        }
    }

    public function persistClaimsPaidToTreatymastclassuwyr()
    {
        foreach($this->_treatyData as $data)
        {
            $treaty_code = $data['treaty_code'];

            $w_count = Treatymastclassuwyr::where('account_year', $this->_account_year)
                ->where('account_month', $this->_clm->account_month)
                ->where('uw_year', $this->_clm->uw_year)
                ->where('class', $this->_clm->class)
                ->where('period_from', $this->_clm->period_from)
                ->where('period_to', $this->_clm->period_to)
                ->where('reinclass', $this->_clm->reinclass)
                ->where('treaty_code', $treaty_code)
                ->count();

            if ($w_count == 0) {
                Treatymastclassuwyr::create([
                    'treaty_code'       => $treaty_code,
                    'account_year'      => $this->_account_year,
                    'account_month'     => $this->_clm->account_month,
                    'uw_year'           => $this->_clm->uw_year,
                    'class'             => $this->_clm->class,
                    'reinclass'         => $this->_clm->reinclass,
                    'period_from'       => $this->_clm->period_from,
                    'period_to'         => $this->_clm->period_to,
                    'claims_paid'       => $data['claims_paid'],
                    'claims_recovery'   => $data['claims_recovery'],
                ]);
            }
            else {
                Treatymastclassuwyr::where('treaty_code', $treaty_code)
                    ->where('account_year', $this->_clm->account_year)
                    ->where('account_month', $this->_clm->account_month)
                    ->where('uw_year', $this->_clm->uw_year)
                    ->where('class', $this->_clm->class)
                    ->where('period_from', $this->_clm->period_from)
                    ->where('period_to', $this->_clm->period_to)
                    ->where('reinclass', $this->_clm->reinclass)
                    ->update([
                        'claims_paid'       => $data['claims_paid'],
                        'claims_recovery'   => $data['claims_recovery'],
                    ]);
            }
        }
    }

    public function processClaimsOsData()
    {
        try
        {
            $clm = $this->_clm;
            $reinsetup = $this->reinsetupData('CLM');

            if ($clm->mandatory_claim != 0)
            {
                $data = [
                    'claims_os'       => $clm->mandatory_claim,
                ];
                if(empty(trim($reinsetup->mandatory_code)))
                {
                    throw new Exception("Missing treaty code for mandatory_code) => REINCLASS =>{$clm->reinclass},UW CLASS =>{$clm->class},UW YEAR =>{$clm->uw_year}");
                }
                $this->persistClaimsOsToTreatymastclassuwyr(trim($reinsetup->mandatory_code),$data);
            }

            // quota premium
            if ($clm->quota_claim != 0) 
            {
                $data = [
                    'claims_os'       => $clm->quota_claim,
                ];
                if(empty(trim($reinsetup->quota_code)))
                {
                    throw new Exception("Missing treaty code for quota_code => REINCLASS =>{$clm->reinclass},UW CLASS =>{$clm->class},UW YEAR =>{$clm->uw_year}");
                }
                $this->persistClaimsOsToTreatymastclassuwyr(trim($reinsetup->quota_code),$data);
            }

            // surplus 1st premium
            if ($clm->surplus_1_claim != 0) 
            {
                $data = [
                    'claims_os'       => $clm->surplus_1_claim,
                ];
                
                if(empty(trim($reinsetup->surplus_1_code)))
                {
                    throw new Exception("Missing treaty code for surplus_1_code => REINCLASS =>{$clm->reinclass},UW CLASS =>{$clm->class},UW YEAR =>{$clm->uw_year}");
                }
                $this->persistClaimsOsToTreatymastclassuwyr(trim($reinsetup->surplus_1_code),$data);
            }

            // surplus 2nd premium
            if ($clm->surplus_2_claim != 0) 
            {
                $data = [
                    'claims_os'       => $clm->surplus_2_claim,
                ];
                
                if(empty(trim($reinsetup->surplus_2_code)))
                {
                    throw new Exception("Missing treaty code for surplus_2_code => REINCLASS =>{$clm->reinclass},UW CLASS =>{$clm->class},UW YEAR =>{$clm->uw_year}");
                }
                $this->persistClaimsOsToTreatymastclassuwyr(trim($reinsetup->surplus_2_code),$data);
            }

            // surplus 3rd premium
            if ($clm->surplus_3_claim != 0) 
            {
                $data = [
                    'claims_os'       => $clm->surplus_3_claim,
                ];
                if(empty(trim($reinsetup->surplus_3_code)))
                {
                    throw new Exception("Missing treaty code for surplus_3_code => REINCLASS =>{$clm->reinclass},UW CLASS =>{$clm->class},UW YEAR =>{$clm->uw_year}");
                }
                $this->persistClaimsOsToTreatymastclassuwyr(trim($reinsetup->surplus_3_code),$data);
            }
        }
        catch(\Throwable $e)
        {
            throw $e;
        }
    }

    public function persistClaimsOsToTreatymastclassuwyr($treaty_code, $data)
    {
        $w_count = Treatymastclassuwyr::where('account_year', $this->_account_year)
            ->where('account_month', $this->_clm->account_month)
            ->where('uw_year', $this->_clm->uw_year)
            ->where('class', $this->_clm->class)
            ->where('period_from', $this->_clm->period_from)
            ->where('period_to', $this->_clm->period_to)
            ->where('reinclass', $this->_clm->reinclass)
            ->where('treaty_code', $treaty_code)
            ->count();

        if ($w_count == 0) {
            Treatymastclassuwyr::create([
                'treaty_code'       => $treaty_code,
                'account_year'      => $this->_account_year,
                'account_month'     => $this->_clm->account_month,
                'uw_year'           => $this->_clm->uw_year,
                'class'             => $this->_clm->class,
                'reinclass'         => $this->_clm->reinclass,
                'period_from'       => $this->_clm->period_from,
                'period_to'         => $this->_clm->period_to,
                'claims_os'         => $data['claims_os'],
            ]);
        } else {
            Treatymastclassuwyr::where('treaty_code', $treaty_code)
                ->where('account_year', $this->_clm->account_year)
                ->where('account_month', $this->_clm->account_month)
                ->where('uw_year', $this->_clm->uw_year)
                ->where('class', $this->_clm->class)
                ->where('period_from', $this->_clm->period_from)
                ->where('period_to', $this->_clm->period_to)
                ->where('reinclass', $this->_clm->reinclass)
                ->update([
                    'claims_os'       => $data['claims_os'],
                ]);
        }
    }

    public function processCashCallData()
    {
        try
        {
            $cashloss = $this->_cashloss;

            if ($cashloss->nett != 0)
            {
                $data = [
                    'cashloss'       => $cashloss->nett,
                ];

                $this->persistCashCallToTreatymastclassuwyr($this->_cashloss->treaty_code,$data);
            }
        }
        catch(\Throwable $e)
        {
            throw $e;
        }
    }

    public function persistCashCallToTreatymastclassuwyr($treaty_code, $data)
    {
        $w_count = Treatymastclassuwyr::where('account_year', $this->_account_year)
            ->where('account_month', $this->_cashloss->account_month)
            ->where('uw_year', $this->_cashloss->uw_year)
            ->where('class', $this->_cashloss->class)
            ->where('period_from', $this->_cashloss->period_from)
            ->where('period_to', $this->_cashloss->period_to)
            ->where('reinclass', $this->_cashloss->reinclass)
            ->where('treaty_code', $treaty_code)
            ->count();

        if ($w_count == 0) {
            Treatymastclassuwyr::create([
                'treaty_code'       => $treaty_code,
                'account_year'      => $this->_account_year,
                'account_month'     => $this->_cashloss->account_month,
                'uw_year'           => $this->_cashloss->uw_year,
                'class'             => $this->_cashloss->class,
                'reinclass'         => $this->_cashloss->reinclass,
                'period_from'       => $this->_cashloss->period_from,
                'period_to'         => $this->_cashloss->period_to,
                'cashloss'         => $data['cashloss'],
            ]);
        } else {
            Treatymastclassuwyr::where('treaty_code', $treaty_code)
                ->where('account_year', $this->_cashloss->account_year)
                ->where('account_month', $this->_cashloss->account_month)
                ->where('uw_year', $this->_cashloss->uw_year)
                ->where('class', $this->_cashloss->class)
                ->where('period_from', $this->_cashloss->period_from)
                ->where('period_to', $this->_cashloss->period_to)
                ->where('reinclass', $this->_cashloss->reinclass)
                ->update([
                    'cashloss'       => $data['cashloss'],
                ]);
        }
    }

    public function processTreatyPaymentsData()
    {
        try
        {
            $trtpayment = $this->_trtpayment;

            if ($trtpayment->nett != 0)
            {
                $data = [
                    'payments'       => $trtpayment->nett,
                ];

                $this->persistTreatyPaymentsToTreatymastclassuwyr($trtpayment->treaty_code,$data);
            }
        }
        catch(\Throwable $e)
        {
            throw $e;
        }
    }

    // save treaty payments
    public function persistTreatyPaymentsToTreatymastclassuwyr($treaty_code, $data)
    {
        $w_count = Treatymastclassuwyr::where('account_year', $this->_account_year)
            ->where('account_month', $this->_trtpayment->account_month)
            ->where('uw_year', $this->_trtpayment->uw_year)
            ->where('class', $this->_trtpayment->class)
            ->where('treaty_code', $treaty_code)
            ->count();

        if ($w_count == 0) {
            $treatymastclassuwyr = new Treatymastclassuwyr;

            $treatymastclassuwyr->treaty_code       = $treaty_code;
            $treatymastclassuwyr->account_year      = $this->_account_year;
            $treatymastclassuwyr->account_month     = $this->_trtpayment->account_month;
            $treatymastclassuwyr->uw_year           = $this->_trtpayment->uw_year;
            $treatymastclassuwyr->class             = $this->_trtpayment->class;
            $treatymastclassuwyr->payments          = $data['payments'];

            $treatymastclassuwyr->save();

        } else {
            Treatymastclassuwyr::where('account_year', $this->_account_year)
                ->where('account_month', $this->_trtpayment->account_month)
                ->where('uw_year', $this->_trtpayment->uw_year)
                ->where('class', $this->_trtpayment->class)
                ->where('treaty_code', $treaty_code)
                ->update([
                    'payments'       => $data['payments'],
                ]);
        }
    }

    // save treatymastclassuwyr summary
    public function persistSummaryToTreatymastuwyr()
    {
        $treatysetup = Treatysetup::where('treaty_code',$this->_treatySummary->treaty_code)
            ->where('uw_year',$this->_treatySummary->uw_year)
            ->first(['tax_code','clean_cut']);

        $tax_rate = Tax_code::where('tax_code',$treatysetup->tax_code)->first()->tax_rate;
        $prem_tax = ($tax_rate * $this->_treatySummary->premium)/100;

        $treatymastuwyr = new Treatymastuwyr;
        $treatymastuwyr->treaty_code           = $this->_treatySummary->treaty_code;
        $treatymastuwyr->account_year          = $this->_treatySummary->account_year;
        $treatymastuwyr->account_month         = $this->_treatySummary->account_month;
        $treatymastuwyr->uw_year               = $this->_treatySummary->uw_year;
        $treatymastuwyr->quarter               = $this->_qtr;
        $treatymastuwyr->portfolio             = $treatysetup->clean_cut?? 'N';
        $treatymastuwyr->premium               = $this->_treatySummary->premium;
        $treatymastuwyr->earned_premium        = $this->_treatySummary->earned_premium;
        $treatymastuwyr->commission            = $this->_treatySummary->commission;
        $treatymastuwyr->vat_on_premium        = $this->_treatySummary->vat_on_premium;
        $treatymastuwyr->vat_on_commission     = $this->_treatySummary->vat_on_commission;
        $treatymastuwyr->claims_os             = $this->_treatySummary->claims_os;
        $treatymastuwyr->claims_paid           = $this->_treatySummary->claims_paid;
        $treatymastuwyr->claims_recovery       = $this->_treatySummary->claims_recovery;
        $treatymastuwyr->management_expense    = $this->_treatySummary->management_expense;
        $treatymastuwyr->payments              = $this->_treatySummary->payments;
        $treatymastuwyr->cashloss              = $this->_treatySummary->cashloss;
        $treatymastuwyr->prem_tax              = $prem_tax;
        $treatymastuwyr->prem_tax_rate         = $tax_rate;

        $treatymastuwyr->save();
    }


    // save treatymastclassuwyr summary profit commission
    public function updateTreatymastuwyrProfitCommission()
    {
        $curr_profit = 
                (
                      $this->_treatySummary->premium
                    + $this->_treatySummary->portfolio_prem_entry
                    + $this->_treatySummary->portfolio_loss_entry
                )
                - 
                (
                      $this->_treatySummary->commission
                    + $this->_treatySummary->claims_paid
                    + $this->_treatySummary->claims_os
                    + $this->_treatySummary->management_expense
                    + $this->_treatySummary->portfolio_prem_withdrawal
                    + $this->_treatySummary->portfolio_loss_withdrawal
                );

        $profit_deficit = Treatysetup::where('treaty_code',$this->_treatySummary->treaty_code)
                ->where('uw_year',$this->_treatySummary->uw_year)
                ->value('deficit');
        $deficit_1st_yr = $this->_treatySummary->uw_year - (int)$profit_deficit;

        $deficit_yrs = range($deficit_1st_yr,$this->_treatySummary->uw_year);

        $prev_profit = Treatymastuwyr::where('treaty_code',$this->_treatySummary->treaty_code)
                ->whereIn('uw_year',$deficit_yrs)
                ->value('profit') ??0;

        $profit = $curr_profit - $prev_profit;
        $profit_comm = 0;
        if($profit > 0)
        {
            $profit_comm = ($this->_treatySummary->profit_comm_rate * $profit)/100;
        }
        
        Treatymastuwyr::where([
                'treaty_code' => $this->_treatySummary->treaty_code,
                'account_year' => $this->_treatySummary->account_year,
                'account_month' =>12,
            ])
            ->update([
                'profit' => $curr_profit ??0,
                'profit_commission' => $profit_comm,
                'profit_comm_rate' => $this->_treatySummary->profit_comm_rate,
            ]);
        
        Treatymastuwyr::where([
                'treaty_code' => $this->_treatySummary->treaty_code,
                'account_year' => $this->_treatySummary->account_year,
                'account_month' =>12,
            ])
            ->update([
                'profit' => $curr_profit ??0,
                'profit_commission' => $profit_comm,
                'profit_comm_rate' => $this->_treatySummary->profit_comm_rate,
            ]);
        
        $participants = Treatypart::where([
                'treaty_code' => $this->_treatySummary->treaty_code,
                'uw_year' => $this->_treatySummary->uw_year
            ])
            ->get();
            
        foreach($participants as $part)
        {
            $part_profit_comm = ($profit_comm * $part->part_per)/100;

            Treatymastpart::where([
                    'treaty_code' => $this->_treatySummary->treaty_code,
                    'account_year' => $this->_treatySummary->account_year,
                    'broker_branch' => $part->broker_branch,
                    'broker_agent_no' => $part->broker_agent,
                    'branch' => $part->branch,
                    'agent_no' => $part->agent,
                    'uw_year' => $this->_treatySummary->uw_year,
                    'account_month' =>12,
                ])
                ->update([
                    'profit_commission' => $part_profit_comm,
                    'profit_comm_rate' => $this->_treatySummary->profit_comm_rate,
                ]);
        }
    }

    // save treatymastuwyr adjustment commission
    public function updateTreatymastuwyrAdjustmentCommission()
    {
        $claims_incurred = 
            (
                  $this->_treatySummary->claims_paid
                + $this->_treatySummary->portfolio_loss_entry
            )
            -
            (
                + $this->_treatySummary->portfolio_loss_withdrawal
            );

        $earned_premium = $this->_treatySummary->earned_premium;
        $loss_ratio = $claims_incurred/$earned_premium ??0;
        
        $comm_rate = Treaty_sliding_scale_comm::where('treaty_code',$this->_treatySummary->treaty_code)
            ->where('uw_year',$this->_treatySummary->uw_year)
            ->whereRaw("min_loss_ratio <= $loss_ratio AND max_loss_ratio >= $loss_ratio")
            ->value('comm_rate');
            
        $adjusted_comm_amt = ($comm_rate/100) * $this->_treatySummary->premium;

        $adjusted_net_comm = $adjusted_comm_amt - $this->_treatySummary->commission;

        $count = Treatymastuwyr::where([
                'treaty_code' => $this->_treatySummary->treaty_code,
                'uw_year' => $this->_treatySummary->account_year,
                'account_year' => $this->_treatySummary->account_year,
                'account_month' => 12,
            ])
            ->count();

        if($count  == 0)
        {
            $treatymastuwyr = new Treatymastuwyr;
            $treatymastuwyr->treaty_code           = $this->_treatySummary->treaty_code;
            $treatymastuwyr->account_year          = $this->_treatySummary->account_year;
            $treatymastuwyr->account_month         = 12;
            $treatymastuwyr->uw_year               = $this->_treatySummary->uw_year;
            $treatymastuwyr->quarter               = $this->_qtr;
            $treatymastuwyr->portfolio             = $this->_treatySummary->clean_cut?? 'N';
            $treatymastuwyr->adjusted_comm_amt     = $adjusted_comm_amt ??0;
            $treatymastuwyr->adjusted_net_comm     = $adjusted_net_comm ??0;

            $treatymastuwyr->save();
        }
        else 
        {
            Treatymastuwyr::where([
                'treaty_code' => $this->_treatySummary->treaty_code,
                'uw_year' => $this->_treatySummary->account_year,
                'account_year' => $this->_treatySummary->account_year,
                'account_month' => 12,
            ])
            ->update([
                'adjusted_net_comm' => $adjusted_net_comm ??0,
                'adjusted_comm_amt' => $adjusted_comm_amt ??0,
            ]);
        }

    }

    // save treatymastclassuwyr summary portfolio
    public function updateAdjustmentCommToTreatymastpart()
    {
        $participants = Treatypart::where([
                'treaty_code' => $this->_treatySummary->treaty_code,
                'uw_year' => $this->_treatySummary->uw_year
            ])
            ->get();

        foreach($participants as $part)
        {

            $count = Treatymastpart::where([
                    'treaty_code' => $this->_treatySummary->treaty_code,
                    'uw_year' => $this->_treatySummary->account_year,
                    'account_year' => $this->_treatySummary->account_year,
                    'account_month' => 12,
                    'broker_branch' => $part->broker_branch,
                    'broker_agent_no' => $part->broker_agent,
                    'branch' => $part->branch,
                    'agent_no' => $part->agent,
                ])
                ->count();

            if($count  == 0)
            {
                $treatymastpart = new Treatymastpart();
                $treatymastpart->treaty_code           = $this->_treatySummary->treaty_code;
                $treatymastpart->account_year          = $this->_treatySummary->account_year;
                $treatymastpart->account_month         = 12;
                $treatymastpart->uw_year               = $this->_treatySummary->uw_year;
                $treatymastpart->broker_branch         = $part->broker_branch;
                $treatymastpart->broker_agent_no       = $part->broker_agent;
                $treatymastpart->branch                = $part->branch;
                $treatymastpart->agent_no              = $part->agent;
                $treatymastpart->quarter               = $this->_qtr;
                $treatymastpart->portfolio             = $this->_treatySummary->clean_cut?? 'N';
                $treatymastpart->adjusted_net_comm     = ($part->part_per/100) * $this->_treatySummary->adjusted_net_comm;
                $treatymastpart->adjusted_comm_amt     = ($part->part_per/100) * $this->_treatySummary->adjusted_comm_amt;
                $treatymastpart->save();
            }
            else 
            {
                Treatymastpart::where([
                    'treaty_code' => $this->_treatySummary->treaty_code,
                    'uw_year' => $this->_treatySummary->uw_year,
                    'account_year' => $this->_treatySummary->account_year,
                    'account_month' => 12,
                    'broker_branch' => $part->broker_branch,
                    'broker_agent_no' => $part->broker_agent,
                    'branch' => $part->branch,
                    'agent_no' => $part->agent,
                ])
                ->update([
                    'adjusted_net_comm'         => ($part->part_per/100) * $this->_treatySummary->adjusted_net_comm,
                    'adjusted_comm_amt'         => ($part->part_per/100) * $this->_treatySummary->adjusted_comm_amt,
                ]);
            }
        }
    }

    // save treatymastclassuwyr summary portfolio
    public function updatePortfolioToTreatymastuwyr()
    {
        
        $net_premium = $this->_treatySummary->premium -  $this->_treatySummary->commission;
        $portfolio_prem_withdrawal = ($this->_treatySummary->premium_port_rate/100) * $net_premium;

        $claims_os = $this->_treatySummary->claims_os;
        $portfolio_loss_withdrawal = ($this->_treatySummary->loss_port_rate/100) * $claims_os;

        $previous_portfolio = Treatymastuwyr::where('treaty_code',$this->_treatySummary->treaty_code)
            ->where('account_year',$this->_treatySummary->account_year-1)
            ->first(['portfolio_prem_withdrawal','portfolio_loss_withdrawal']);

        $count = Treatymastuwyr::where([
                'treaty_code' => $this->_treatySummary->treaty_code,
                'uw_year' => $this->_treatySummary->account_year,
                'account_year' => $this->_treatySummary->account_year,
                'account_month' => 12,
            ])
            ->count();

        if($count  == 0)
        {
            $treatymastuwyr = new Treatymastuwyr;
            $treatymastuwyr->treaty_code           = $this->_treatySummary->treaty_code;
            $treatymastuwyr->account_year          = $this->_treatySummary->account_year;
            $treatymastuwyr->account_month         = 12;
            $treatymastuwyr->uw_year               = $this->_treatySummary->uw_year;
            $treatymastuwyr->quarter               = $this->_qtr;
            $treatymastuwyr->portfolio             = $this->_treatySummary->clean_cut?? 'N';
            $treatymastuwyr->portfolio_prem_withdrawal  = $portfolio_prem_withdrawal;
            $treatymastuwyr->portfolio_loss_withdrawal  = $portfolio_loss_withdrawal;
            $treatymastuwyr->portfolio_prem_entry       = $previous_portfolio->portfolio_prem_withdrawal ??0;
            $treatymastuwyr->portfolio_loss_entry       = $previous_portfolio->portfolio_claim_withdrawal??0;
    
            $treatymastuwyr->save();
        }
        else 
        {
            Treatymastuwyr::where([
                'treaty_code' => $this->_treatySummary->treaty_code,
                'uw_year' => $this->_treatySummary->uw_year,
                'account_year' => $this->_treatySummary->account_year,
                'account_month' => 12,
            ])
            ->update([
                'portfolio_prem_withdrawal' => $portfolio_prem_withdrawal,
                'portfolio_loss_withdrawal' => $portfolio_loss_withdrawal,
                'portfolio_prem_entry'      => $previous_portfolio->portfolio_prem_entry ??0,
                'portfolio_loss_entry'      => $previous_portfolio->portfolio_claim_entry??0,
            ]);
        }
    }

    // save treatymastclassuwyr summary portfolio
    public function updatePortfolioToTreatymastpart()
    {
        $participants = Treatypart::where([
                'treaty_code' => $this->_treatySummary->treaty_code,
                'uw_year' => $this->_treatySummary->uw_year
            ])
            ->get();

        foreach($participants as $part)
        {

            $count = Treatymastpart::where([
                    'treaty_code' => $this->_treatySummary->treaty_code,
                    'uw_year' => $this->_treatySummary->account_year,
                    'account_year' => $this->_treatySummary->account_year,
                    'account_month' => 12,
                    'broker_branch' => $part->broker_branch,
                    'broker_agent_no' => $part->broker_agent,
                    'branch' => $part->branch,
                    'agent_no' => $part->agent,
                ])
                ->count();

            if($count  == 0)
            {
                $treatymastpart = new Treatymastpart();
                $treatymastpart->treaty_code           = $this->_treatySummary->treaty_code;
                $treatymastpart->account_year          = $this->_treatySummary->account_year;
                $treatymastpart->account_month         = 12;
                $treatymastpart->uw_year               = $this->_treatySummary->uw_year;
                $treatymastpart->broker_branch         = $part->broker_branch;
                $treatymastpart->broker_agent_no       = $part->broker_agent;
                $treatymastpart->branch                = $part->branch;
                $treatymastpart->agent_no              = $part->agent;
                $treatymastpart->quarter               = $this->_qtr;
                $treatymastpart->portfolio             = $this->_treatySummary->clean_cut?? 'N';
                $treatymastpart->premium               = ($part->part_per/100) * $this->_treatySummary->premium;
                $treatymastpart->commission            = ($part->part_per/100) * $this->_treatySummary->commission;
                $treatymastpart->vat_on_premium        = ($part->part_per/100) * $this->_treatySummary->vat_on_premium;
                $treatymastpart->vat_on_commission     = ($part->part_per/100) * $this->_treatySummary->vat_on_commission;
                $treatymastpart->claims_os             = ($part->part_per/100) * $this->_treatySummary->claims_os;
                $treatymastpart->claims_paid           = ($part->part_per/100) * $this->_treatySummary->claims_paid;
                $treatymastpart->claims_recovery       = ($part->part_per/100) * $this->_treatySummary->claims_paid;
                $treatymastpart->portfolio_prem_withdrawal  = ($part->part_per/100) * $this->_treatySummary->portfolio_prem_withdrawal;
                $treatymastpart->portfolio_loss_withdrawal  = ($part->part_per/100) * $this->_treatySummary->portfolio_loss_withdrawal;
                $treatymastpart->portfolio_prem_entry       = ($part->part_per/100) * $this->_treatySummary->portfolio_prem_entry ??0;
                $treatymastpart->portfolio_loss_entry       = ($part->part_per/100) * $this->_treatySummary->portfolio_claim_entry??0;
                $treatymastpart->save();
            }
            else 
            {
                Treatymastpart::where([
                    'treaty_code' => $this->_treatySummary->treaty_code,
                    'uw_year' => $this->_treatySummary->uw_year,
                    'account_year' => $this->_treatySummary->account_year,
                    'account_month' => 12,
                    'broker_branch' => $part->broker_branch,
                    'broker_agent_no' => $part->broker_agent,
                    'branch' => $part->branch,
                    'agent_no' => $part->agent,
                ])
                ->update([
                    'portfolio_prem_withdrawal' => ($part->part_per/100) * $this->_treatySummary->portfolio_prem_withdrawal,
                    'portfolio_loss_withdrawal' => ($part->part_per/100) * $this->_treatySummary->portfolio_loss_withdrawal,
                    'portfolio_prem_entry'      => ($part->part_per/100) * $this->_treatySummary->portfolio_prem_entry ??0,
                    'portfolio_loss_entry'      => ($part->part_per/100) * $this->_treatySummary->portfolio_claim_entry??0,
                ]);
            }
        }
    }

    // save treatymastclassuwyr summary portfolio
    public function persistTreatymastpartPerParticipant()
    {
        
        $year = $this->_account_year;
        if ($this->_treatySummaryPart->portfolio == 'N') {
            $year = $this->_treatySummaryPart->uw_year;
        }

        $treatyParts = Treatypart::where('treaty_code',$this->_treatySummaryPart->treaty_code)
            ->where('uw_year',$year)
            ->get();

        $treatysetup = Treatysetup::where('treaty_code',$this->_treatySummary->treaty_code)
            ->where('uw_year',$this->_treatySummary->uw_year)
            ->first(['tax_code','clean_cut']);

        $tax_rate = Tax_code::where('tax_code',$treatysetup->tax_code)->first()->tax_rate;

        foreach ($treatyParts as $part) {
            $cashloss = $this->fetchReiacdetPerPart($part,$doc_type = 'REC',$entry_type_descr='CAL');
            $payment = $this->fetchReiacdetPerPart($part,$doc_type='PAY',$entry_type_descr='TRT');

            $wht_tax = ($this->_treatySummaryPart->premium * ($part->part_per/100)) * ($part->remittance_tax_rate/100) ?? 0;

            $treatymastpart = new Treatymastpart;
            $treatymastpart->treaty_code               = $this->_treatySummaryPart->treaty_code;
            $treatymastpart->account_year              = $this->_treatySummaryPart->account_year;
            $treatymastpart->account_month             = $this->_treatySummaryPart->account_month;
            $treatymastpart->uw_year                   = $this->_treatySummaryPart->uw_year;
            $treatymastpart->quarter                   = $this->_qtr;
            $treatymastpart->broker_branch             = $part->broker_branch;
            $treatymastpart->broker_agent_no           = $part->broker_agent;
            $treatymastpart->branch                    = $part->branch;
            $treatymastpart->agent_no                  = $part->agent;
            $treatymastpart->portfolio                 = 'Y';
            $treatymastpart->premium                   = ($part->part_per / 100) * $this->_treatySummaryPart->premium;
            $treatymastpart->earned_premium            = ($part->part_per / 100) * $this->_treatySummaryPart->earned_premium;
            $treatymastpart->commission                = ($part->part_per / 100) * $this->_treatySummaryPart->commission;
            $treatymastpart->vat_on_premium            = ($part->part_per / 100) * $this->_treatySummaryPart->vat_on_premium;
            $treatymastpart->vat_on_commission         = ($part->part_per / 100) * $this->_treatySummaryPart->vat_on_commission;
            $treatymastpart->claims_os                 = ($part->part_per / 100) * $this->_treatySummaryPart->claims_os;
            $treatymastpart->claims_paid               = ($part->part_per / 100) * $this->_treatySummaryPart->claims_paid;
            $treatymastpart->portfolio_prem_withdrawal = ($part->part_per / 100) * $this->_treatySummaryPart->portfolio_prem_withdrawal;
            $treatymastpart->portfolio_prem_entry      = ($part->part_per / 100) * $this->_treatySummaryPart->portfolio_prem_entry;
            $treatymastpart->portfolio_loss_withdrawal = ($part->part_per / 100) * $this->_treatySummaryPart->portfolio_loss_withdrawal;
            $treatymastpart->portfolio_loss_entry      = ($part->part_per / 100) * $this->_treatySummaryPart->portfolio_loss_entry;
            $treatymastpart->management_expense        = ($part->part_per / 100) * $this->_treatySummaryPart->management_expense;
            $treatymastpart->prem_tax_rate             = $tax_rate;
            $treatymastpart->prem_tax                  = ($part->part_per / 100)  * $this->_treatySummaryPart->prem_tax;
            $treatymastpart->wht_tax                   = $wht_tax;
            $treatymastpart->payments                  = ($part->part_per / 100) * $this->_treatySummaryPart->payments;
            $treatymastpart->cashloss                  = $cashloss->nett;
            $treatymastpart->profit_commission         = ($part->part_per / 100) * $this->_treatySummaryPart->profit_commission;
            $treatymastpart->adjusted_comm_amt         = ($part->part_per / 100) * $this->_treatySummaryPart->adjusted_comm_amt;
            $treatymastpart->adjusted_net_comm         = ($part->part_per / 100) * $this->_treatySummaryPart->adjusted_net_comm;
            $treatymastpart->claims_recovery            = ($part->part_per / 100) * $this->_treatySummaryPart->claims_recovery;
            $treatymastpart->save();
        }
    }

    // MDP computation
    public function processMDP() : void
    {

        $treatysetup = $this->_treatysetup;
        $mdp = $treatysetup->min_deposit;

        switch($treatysetup->deposit_frequency)
        {
            case 'H':
                $mdp = $mdp/2;
                if($this->_qtr > 2){
                    $account_mnths = [7];
                }
                else{
                    $account_mnths = [1];
                }
                break;
            case 'Q':
                $mdp = $mdp/4;
                $all_first_mnths = [1,4,7,10];
                $account_mnths=array_intersect($this->_qtr_months,$all_first_mnths);
                break;
            case 'M':
                $mdp = $mdp/12;
                $all_first_mnths = [1,2,3,4,5,6,7,8,9,10,11,12];
                $account_mnths=array_intersect($this->_qtr_months,$all_first_mnths);
                break;
            case 'A':
            default:
                $mdp = $mdp;
                $all_first_mnths = [1];
                $account_mnths=array_intersect($this->_qtr_months,$all_first_mnths);
                break;
        }

        $treatysetup = Treatysetup::where('treaty_code',$treatysetup->treaty_code)
            ->where('uw_year',$treatysetup->uw_year)
            ->first('tax_code');
            
        $tax_rate = Tax_code::where('tax_code',$treatysetup->tax_code)->first()->tax_rate;
        $prem_tax = ($tax_rate * $mdp)/100;

        $this->_mdp = [
            'mdp' => $mdp,
            'prem_tax' => $prem_tax,
            'account_month' => $account_mnths,
        ];
    }

    // SAve MDP computation
    public function persistMDP()
    {
        $treatyParts = Treatypart::where('treaty_code',$this->_treatysetup->treaty_code)
            ->where('uw_year',$this->_treatysetup->uw_year)
            ->get();

        foreach ($treatyParts as $part) {
            $mdp_amt = $this->_mdp['mdp'] * ($part->part_per/100) ?? 0;
            $prem_tax = $this->_mdp['prem_tax'] * ($part->part_per/100) ?? 0;
            $wht_tax = $mdp_amt * ($part->remittance_tax_rate/100) ?? 0;
            $net_mdp = $mdp_amt - ($wht_tax + $prem_tax);

            foreach ($this->_mdp['account_month'] as $month) {
    
                $treaty_mdp = new Treaty_mdp;
                $treaty_mdp->treaty_code               = $this->_treatysetup->treaty_code;
                $treaty_mdp->account_year              = $this->_account_year;
                $treaty_mdp->account_month             = $month;
                $treaty_mdp->uw_year                   = $this->_treatysetup->uw_year;
                $treaty_mdp->quarter                   = $this->_qtr;
                $treaty_mdp->broker_branch             = $part->broker_branch;
                $treaty_mdp->broker_agent              = $part->broker_agent;
                $treaty_mdp->branch                    = $part->branch;
                $treaty_mdp->agent                     = $part->agent;
                $treaty_mdp->mdp                       = $mdp_amt;
                $treaty_mdp->prem_tax                  = $prem_tax;
                $treaty_mdp->wht_tax                   = $wht_tax;
                $treaty_mdp->net_mdp                   = $net_mdp;
                $treaty_mdp->save();
            }
        }
    }

    public function processPremiumAdjustment()
    {
        $xol_columns = [
            'xl_code_1',
            'xl_code_2',
            'xl_code_3',
            'xl_code_4',
            'xl_code_5',
            'cxl_code_1',
            'cxl_code_2',
            'cxl_code_3',
        ];

        $treaty = $this->_retPremiumData;

        foreach($xol_columns as $column)
        {
            $treaty_code = $treaty->{$column};
            
            if(!is_null(trim($treaty_code)) && !empty(trim($treaty_code))){
                $treatysetup = Treatysetup::where('uw_year',$treaty->uw_year)
                    ->where('treaty_code',$treaty_code)
                    ->first(['applied_rate','min_deposit']);

                $layer_premium =  ([
                    'treaty_code' => trim($treaty_code),
                    'uw_year' => $treaty->uw_year,
                    'account_year' => $treaty->account_year,
                    'policy_no' => $treaty->policy_no,
                    'endt_renewal_no' => $treaty->endt_renewal_no,
                    'class' => $treaty->class,
                    'reinclass' => $treaty->reinclass,
                    'ret_premium' => (float)$treaty->ret_premium,
                ]);

                $treaty_premium =  ([
                    'treaty_code' => trim($treaty_code),
                    'uw_year' => $treaty->uw_year,
                    'account_year' => $treaty->account_year,
                    'ret_premium' => (float)$treaty->ret_premium,
                    'applied_rate' => $treatysetup->applied_rate,
                    'mdp' => $treatysetup->min_deposit,
                ]);

                $this->_xl_premium_dtl->push($layer_premium);
                
                if(!$this->_xl_treaty_premiums->contains(function($item) use ($treaty_code,$treaty){
                    return $item['treaty_code'] == $treaty_code && $item['uw_year'] == $treaty->uw_year;
                }))
                {
                    $this->_xl_treaty_premiums->push($treaty_premium);
                    continue;
                }

                $this->_xl_treaty_premiums->transform(function($item) use ($treaty_premium,$layer_premium,$treaty_code){

                    if($item['treaty_code'] == $treaty_code && $item['uw_year'] == $treaty_premium['uw_year'])
                    {
                        $item['ret_premium'] = (float)$item['ret_premium'] + (float)$treaty_premium['ret_premium'];
                    }
                    return $item;
                });
            }
        }

    }

    public function persistPremiumAdjustmentDtl()
    {
        foreach($this->_xl_premium_dtl as $xl_layer)
        {
            $treaty_prem_adj = new Treaty_prem_adj_dtl;

            $treaty_prem_adj->endt_renewal_no = $xl_layer['endt_renewal_no'];
            $treaty_prem_adj->policy_no = $xl_layer['policy_no'];
            $treaty_prem_adj->class = $xl_layer['class'];
            $treaty_prem_adj->reinclass = $xl_layer['reinclass'];
            $treaty_prem_adj->account_year = $xl_layer['account_year'];
            $treaty_prem_adj->uw_year = $xl_layer['uw_year'];
            $treaty_prem_adj->treaty_code = $xl_layer['treaty_code'];
            $treaty_prem_adj->ri_premium = $xl_layer['ret_premium'];
            
            $treaty_prem_adj->save();
        }
    }

    public function persistPremiumAdjustment()
    {
        foreach($this->_xl_treaty_premiums as $xl_treaty)
        {
            $applied_rate = (float)$xl_treaty['applied_rate'] ;
            $xl_annual_prem = (float) $xl_treaty['ret_premium'] * ($applied_rate/100);
            $mdp = (float) $xl_treaty['mdp'] ;

            $adj_premium = 0;
            if(($xl_annual_prem - $xl_treaty['mdp']) > 0)
            {
                $adj_premium = $xl_annual_prem - $xl_treaty['mdp'] ;
            }

            if($adj_premium == 0)
            {
                continue;
            }

            $treaty_prem_adj = new Treaty_prem_adj;
            $treaty_prem_adj->account_year = $xl_treaty['account_year'];
            $treaty_prem_adj->uw_year = $xl_treaty['uw_year'];
            $treaty_prem_adj->treaty_code = $xl_treaty['treaty_code'];
            $treaty_prem_adj->ri_premium = $xl_treaty['ret_premium'];
            $treaty_prem_adj->xl_annual_prem = $xl_annual_prem;
            $treaty_prem_adj->adj_premium = $adj_premium;
            $treaty_prem_adj->applied_rate = $applied_rate;
            $treaty_prem_adj->mdp = $mdp;

            $treaty_prem_adj->save();
        }
    }

    public function persistPremiumAdjustmentPart()
    {
        $xl_treaty = $this->_treatyAdjustment;
        $treatyParts = Treatypart::where('treaty_code',$xl_treaty->treaty_code)
            ->where('uw_year',$xl_treaty->uw_year)
            ->get();
        $treatysetup = Treatysetup::where('treaty_code',$xl_treaty->treaty_code)
            ->where('uw_year',$xl_treaty->uw_year)
            ->first();
        $tax_rate = Tax_code::where('tax_code',$treatysetup->tax_code)->first()->tax_rate;
        $total_prem_tax = ($tax_rate * $xl_treaty->adj_premium)/100;

        foreach ($treatyParts as $part) {
            $xl_annual_prem = (float) $xl_treaty->xl_annual_prem * ($part->part_per/100);
            $ri_premium = (float) $xl_treaty->ri_premium * ($part->part_per/100);
            $adj_premium = (float) $xl_treaty->adj_premium * ($part->part_per/100);
            $mdp = (float) $xl_treaty->mdp * ($part->part_per/100);

            $prem_tax = $total_prem_tax * ($part->part_per/100) ?? 0;
            $wht_tax = $adj_premium  * ($part->remittance_tax_rate/100) ?? 0;
            $net_adj_premium = $adj_premium - ($wht_tax + $prem_tax);

            
            $treaty_prem_adj = new Treaty_prem_adj_part;

            $treaty_prem_adj->account_year          = $xl_treaty->account_year;
            $treaty_prem_adj->uw_year               = $xl_treaty->uw_year;
            $treaty_prem_adj->treaty_code           = $xl_treaty->treaty_code;
            $treaty_prem_adj->broker_branch         = $part->broker_branch;
            $treaty_prem_adj->broker_agent          = $part->broker_agent;
            $treaty_prem_adj->branch                = $part->branch;
            $treaty_prem_adj->agent                 = $part->agent;
            $treaty_prem_adj->total_ri_premium      = $xl_treaty->ri_premium;
            $treaty_prem_adj->ri_premium            = $ri_premium;
            $treaty_prem_adj->total_xl_annual_prem  = $xl_treaty->xl_annual_prem;
            $treaty_prem_adj->xl_annual_prem        = $xl_annual_prem;
            $treaty_prem_adj->total_adj_premium     = $xl_treaty->adj_premium;
            $treaty_prem_adj->adj_premium           = $adj_premium;
            $treaty_prem_adj->total_mdp             = $xl_treaty->mdp;
            $treaty_prem_adj->mdp                   = $mdp;
            $treaty_prem_adj->wht_tax               = $wht_tax;
            $treaty_prem_adj->wht_tax_rate          = $part->remittance_tax_rate;
            $treaty_prem_adj->prem_tax              = $prem_tax;
            $treaty_prem_adj->prem_tax_rate         = $tax_rate;
            $treaty_prem_adj->net_adj_premium       = $net_adj_premium;

            $treaty_prem_adj->save();
        }
    }

    // save treatymastpart summary 
    public function persistTreatymastpartSummary()
    {
        $treatyMastPartSum = new Treatymastpartsum();

        $treatyMastPartSum->treaty_code             = $this->_treatymastpart->treaty_code;
        $treatyMastPartSum->account_year            = $this->_treatymastpart->account_year;
        $treatyMastPartSum->uw_year                 = $this->_treatymastpart->uw_year;
        $treatyMastPartSum->quarter                 = $this->_treatymastpart->quarter;
        $treatyMastPartSum->broker_branch           = $this->_treatymastpart->broker_branch;
        $treatyMastPartSum->broker_agent_no         = $this->_treatymastpart->broker_agent_no;
        $treatyMastPartSum->portfolio               = $this->_treatymastpart->portfolio;
        $treatyMastPartSum->premium                 = $this->_treatymastpart->premium;
        $treatyMastPartSum->earned_premium          = $this->_treatymastpart->earned_premium;
        $treatyMastPartSum->commission              = $this->_treatymastpart->commission;
        $treatyMastPartSum->vat_on_premium          = $this->_treatymastpart->vat_on_premium;
        $treatyMastPartSum->vat_on_commission       = $this->_treatymastpart->vat_on_commission;
        $treatyMastPartSum->claims_os               = $this->_treatymastpart->claims_os;
        $treatyMastPartSum->claims_paid             = $this->_treatymastpart->claims_paid;
        $treatyMastPartSum->claims_recovery         = $this->_treatymastpart->claims_recovery;
        $treatyMastPartSum->portfolio_prem_withdrawal = $this->_treatymastpart->portfolio_prem_withdrawal;
        $treatyMastPartSum->portfolio_prem_entry    = $this->_treatymastpart->portfolio_prem_entry;
        $treatyMastPartSum->management_expense      = $this->_treatymastpart->management_expense;
        $treatyMastPartSum->prem_tax                = $this->_treatymastpart->prem_tax;
        $treatyMastPartSum->profit_commission       = $this->_treatymastpart->profit_commission;
        $treatyMastPartSum->adjusted_comm_amt       = $this->_treatymastpart->adjusted_comm_amt;
        $treatyMastPartSum->adjusted_net_comm       = $this->_treatymastpart->adjusted_net_comm;
        $treatyMastPartSum->cashloss                = $this->_treatymastpart->cashloss;
        $treatyMastPartSum->payments                = $this->_treatymastpart->payments;
        
        $treatyMastPartSum->save();
    }

    public function persistUWTreatyCrStatement()
    {
        $debit = $this->_debit;

        foreach($this->_treatyData as $data)
        {
        
            $participants = Treatypart::where('treaty_code',$data['treaty_code'])
                ->where('uw_year',$debit->uw_year)
                ->get();

            foreach($participants as $part)
            {
                $total_nett = $data['premium'] - $data['commission'];
                $premium = ($data['premium'] * $part->part_per)/100;
                $nett = ($total_nett * $part->part_per)/100;
                $allocated = 0;
                $unallocated = $nett;
                $commission = ($data['commission'] * $part->part_per)/100;

                $reference = str_pad($debit->dtrans_no,6,'0',STR_PAD_LEFT).$debit->account_year;

                $stmt = new TreatyCr_stmt;
                $stmt->source  = 'U/W';
                $stmt->treaty_code              = $data['treaty_code'];
                $stmt->account_year             = $debit->account_year;
                $stmt->account_month            = $debit->account_month;
                $stmt->uw_year                  = $debit->uw_year;
                $stmt->doc_type                 = $debit->doc_type;
                $stmt->entry_type_descr         = $debit->entry_type_descr;
                $stmt->policy_no                = $debit->policy_no;
                $stmt->endt_renewal_no          = $debit->endt_renewal_no;
                $stmt->period_from              = $debit->period_from;
                $stmt->period_to                = $debit->period_to;
                $stmt->class                    = $debit->class;
                $stmt->reinclass                = $debit->reinclass;
                $stmt->entry_type_descr         = $debit->entry_type_descr;
                $stmt->reference                = $reference;
                $stmt->broker_branch            = $part->broker_branch;
                $stmt->broker_agent             = $part->broker_agent;
                $stmt->branch                   = $part->branch;
                $stmt->agent                    = $part->agent;
                $stmt->total_premium            = $data['premium'];
                $stmt->premium                  = $premium;
                $stmt->amount                   = $nett;
                $stmt->gross                    = $premium;
                $stmt->total_amount             = $total_nett;
                $stmt->total_commission         = $data['commission'];
                $stmt->commission               = $commission;
                $stmt->allocated                = $allocated;
                $stmt->unallocated              = $unallocated;
                $stmt->date_effective           = $debit->dola;

                $stmt->save();
            }
        }
    }

    public function persistUWTreatyDtlStatement()
    {
        $debit = $this->_debit;

        foreach($this->_treatyData as $data)
        {
            $nett = $data['premium'] - $data['commission'];
            $reference = str_pad($debit->dtrans_no,6,'0',STR_PAD_LEFT).$debit->account_year;

            $stmt = new TreatyDtl_stmt;
            $stmt->source  = 'U/W';
            $stmt->treaty_code              = $data['treaty_code'];
            $stmt->account_year             = $debit->account_year;
            $stmt->account_month            = $debit->account_month;
            $stmt->uw_year                  = $debit->uw_year;
            $stmt->doc_type                 = $debit->doc_type;
            $stmt->entry_type_descr         = $debit->entry_type_descr;
            $stmt->policy_no                = $debit->policy_no;
            $stmt->endt_renewal_no          = $debit->endt_renewal_no;
            $stmt->period_from              = $debit->period_from;
            $stmt->period_to                = $debit->period_to;
            $stmt->class                    = $debit->class;
            $stmt->reinclass                = $debit->reinclass;
            $stmt->entry_type_descr         = $debit->entry_type_descr;
            $stmt->reference                = $reference;
            $stmt->premium                  = $data['premium'];
            $stmt->amount                   = $data['premium'];
            $stmt->allocated                = 0;
            $stmt->unallocated              = $nett;
            $stmt->commission               = $data['commission'];
            $stmt->date_effective           = $debit->dola;

            $stmt->save();
        }
    }

    public function persistCLMTreatyCrStatement()
    {
        $clm = $this->_clm;

        foreach($this->_treatyData as $data)
        {
            
            $participants = Treatypart::where('treaty_code',$data['treaty_code'])
                ->where('uw_year',$clm->uw_year)
                ->get();

            foreach($participants as $part)
            {
                $claims_recovery = ($data['claims_recovery'] * $part->part_per)/100;

                $reference = str_pad($clm->dtrans_no,6,'0',STR_PAD_LEFT).$clm->account_year;

                $stmt = new TreatyCr_stmt;
                $stmt->source  = 'CLM';
                $stmt->treaty_code              = $data['treaty_code'];
                $stmt->account_year             = $clm->account_year;
                $stmt->account_month            = $clm->account_month;
                $stmt->uw_year                  = $clm->uw_year;
                $stmt->claim_year               = $clm->claim_year;
                $stmt->doc_type                 = $clm->doc_type;
                $stmt->entry_type_descr         = $clm->entry_type_descr;
                $stmt->policy_no                = $clm->policy_no;
                $stmt->endt_renewal_no          = $clm->endt_renewal_no;
                $stmt->class                    = $clm->class;
                $stmt->reinclass                = $clm->reinclass;
                $stmt->reference                = $reference;
                $stmt->broker_branch            = $part->broker_branch;
                $stmt->broker_agent             = $part->broker_agent;
                $stmt->branch                   = $part->branch;
                $stmt->agent                    = $part->agent;
                $stmt->total_amount             = $data['claims_recovery'];
                $stmt->gross                    = $claims_recovery;
                $stmt->amount                   = $claims_recovery;
                $stmt->allocated                = $data['allocated'];
                $stmt->unallocated              = $claims_recovery;
                $stmt->date_effective           = $clm->pay_date;

                $stmt->save();
            }
        }
    }

    public function persistCLMTreatyDtlStatement()
    {
        $clm = $this->_clm;

        foreach($this->_treatyData as $data)
        {
            
            $claims_recovery = $data['claims_recovery'];

            $reference = str_pad($clm->dtrans_no,6,'0',STR_PAD_LEFT).$clm->account_year;

            $stmt = new TreatyDtl_stmt();
            $stmt->source  = 'CLM';
            $stmt->treaty_code              = $data['treaty_code'];
            $stmt->account_year             = $clm->account_year;
            $stmt->account_month            = $clm->account_month;
            $stmt->uw_year                  = $clm->uw_year;
            $stmt->claim_year               = $clm->claim_year;
            $stmt->doc_type                 = $clm->doc_type;
            $stmt->entry_type_descr         = $clm->entry_type_descr;
            $stmt->policy_no                = $clm->policy_no;
            $stmt->endt_renewal_no          = $clm->endt_renewal_no;
            $stmt->class                    = $clm->class;
            $stmt->reinclass                = $clm->reinclass;
            $stmt->reference                = $reference;
            $stmt->gross                    = $claims_recovery;
            $stmt->amount                   = $claims_recovery;
            $stmt->allocated                = $data['allocated'];
            $stmt->unallocated              = $data['unallocated'];
            $stmt->date_effective           = $clm->pay_date;

            $stmt->save();
        
        }
    }

    public function persistCashlossTreatyDtlStatement()
    {

        $cashloss = $this->_cashloss;
            
        $cashloss_amt = $cashloss->nett;

        $reference = str_pad($cashloss->dtrans_no,6,'0',STR_PAD_LEFT).$cashloss->account_year;

        $stmt = new TreatyDtl_stmt();
        $stmt->source  = 'TAC';
        $stmt->treaty_code              = $cashloss->treaty_code;
        $stmt->account_year             = $cashloss->account_year;
        $stmt->account_month            = $cashloss->account_month;
        $stmt->uw_year                  = $cashloss->uw_year;
        $stmt->claim_year               = $cashloss->claim_year;
        $stmt->doc_type                 = $cashloss->doc_type;
        $stmt->entry_type_descr         = $cashloss->entry_type_descr;
        $stmt->endt_renewal_no          = $cashloss->endt_renewal_no;
        $stmt->policy_no                = $cashloss->policy_no;
        $stmt->claim_no                 = $cashloss->claim_no;
        $stmt->period_from              = $cashloss->period_from;
        $stmt->period_to                = $cashloss->period_to;
        $stmt->class                    = $cashloss->class;
        $stmt->reinclass                = $cashloss->reinclass;
        $stmt->reference                = $reference;
        $stmt->gross                    = $cashloss_amt;
        $stmt->amount                   = $cashloss_amt;
        $stmt->allocated                = $cashloss->allocated;
        $stmt->unallocated              = $cashloss->unallocated;
        $stmt->date_effective           = $cashloss->date_effective;

        $stmt->save();
    }

    public function persistCashlossTreatyCrStatement()
    {
        $cashloss = $this->_cashloss;
        
        $cashloss_amt = $cashloss->nett;

        $reference = str_pad($cashloss->dtrans_no,6,'0',STR_PAD_LEFT).$cashloss->account_year;

        $stmt = new TreatyCr_stmt();
        $stmt->source  = 'TAC';
        $stmt->treaty_code              = $cashloss->treaty_code;
        $stmt->account_year             = $cashloss->account_year;
        $stmt->account_month            = $cashloss->account_month;
        $stmt->uw_year                  = $cashloss->uw_year;
        $stmt->claim_year               = $cashloss->claim_year;
        $stmt->doc_type                 = $cashloss->doc_type;
        $stmt->entry_type_descr         = $cashloss->entry_type_descr;
        $stmt->endt_renewal_no          = $cashloss->endt_renewal_no;
        $stmt->policy_no                = $cashloss->policy_no;
        $stmt->claim_no                 = $cashloss->claim_no;
        $stmt->period_from              = $cashloss->period_from;
        $stmt->period_to                = $cashloss->period_to;
        $stmt->class                    = $cashloss->class;
        $stmt->reinclass                = $cashloss->reinclass;
        $stmt->reference                = $reference;
        $stmt->broker_branch            = $cashloss->broker_branch;
        $stmt->broker_agent             = $cashloss->broker_agent;
        $stmt->branch                   = $cashloss->branch;
        $stmt->agent                    = $cashloss->agent;
        $stmt->total_amount             = $cashloss_amt;
        $stmt->gross                    = $cashloss_amt;
        $stmt->amount                   = $cashloss_amt;
        $stmt->allocated                = $cashloss->allocated;
        $stmt->unallocated              = $cashloss->unallocated;
        $stmt->date_effective           = $cashloss->date_effective;

        $stmt->save();
    }

    public function persistpaymentsTreatyDtlStatement()
    {
        $payment = $this->_trtpayment;

        $reference = str_pad($payment->dtrans_no,6,'0',STR_PAD_LEFT).$payment->account_year;

        $stmt = new TreatyDtl_stmt();
        $stmt->source  = 'R/I';
        $stmt->treaty_code              = $payment->treaty_code;
        $stmt->account_year             = $payment->account_year;
        $stmt->account_month            = $payment->account_month;
        $stmt->uw_year                  = $payment->uw_year;
        $stmt->doc_type                 = $payment->doc_type;
        $stmt->entry_type_descr         = $payment->entry_type_descr;
        $stmt->endt_renewal_no          = $payment->endt_renewal_no;
        $stmt->class                    = $payment->class;
        $stmt->reinclass                = $payment->reinclass;
        $stmt->reference                = $reference;
        $stmt->gross                    = $payment->nett;
        $stmt->amount                   = $payment->nett;
        $stmt->allocated                = $payment->allocated;
        $stmt->unallocated              = $payment->unallocated;
        $stmt->date_effective           = $payment->date_effective;

        $stmt->save();
    }

    public function persistpaymentsTreatyCrStatement()
    {
        $payment = $this->_trtpayment;
        
        $payment_amt = $payment->nett ;

        $reference = str_pad($payment->dtrans_no,6,'0',STR_PAD_LEFT).$payment->account_year;

        $stmt = new TreatyCr_stmt();
        $stmt->source  = 'R/I';
        $stmt->treaty_code              = $payment->treaty_code;
        $stmt->account_year             = $payment->account_year;
        $stmt->account_month            = $payment->account_month;
        $stmt->uw_year                  = $payment->uw_year;
        $stmt->doc_type                 = $payment->doc_type;
        $stmt->entry_type_descr         = $payment->entry_type_descr;
        $stmt->endt_renewal_no          = $payment->endt_renewal_no;
        $stmt->class                    = $payment->class;
        $stmt->reinclass                = $payment->reinclass;
        $stmt->reference                = $reference;
        $stmt->broker_branch            = $payment->broker_branch;
        $stmt->broker_agent             = $payment->broker_agent;
        $stmt->branch                   = $payment->branch;
        $stmt->agent                    = $payment->agent;
        $stmt->gross                    = $payment_amt;
        $stmt->amount                   = $payment_amt;
        $stmt->total_amount             = $payment_amt;
        $stmt->allocated                = $payment->allocated;
        $stmt->unallocated              = $payment->unallocated;
        $stmt->date_effective           = $payment->date_effective;

        $stmt->save();
    }

    public function reinsetupData($source='U/W')
    {
        $src_data = $this->_debit;

        if($source == 'CLM')
        {
            $src_data = $this->_clm;
        }
        
        $reinsetup = Reinsetup::where('uw_year', $src_data->uw_year)
            ->where('class', $src_data->reinclass)
            ->first();


        if ($src_data->treaty_expiry_midyear == 'Y') {

            $reinsetup = Reinsetup::where('uw_year', $src_data->uw_year)
                ->where('class', $src_data->reinclass)
                ->whereBetween('period_from', [$src_data->period_from, $src_data->period_to])
                ->first();
        }

        return $reinsetup;
    }

    public function checkQuarter($period_from)
    {
        // Create a Carbon instance from the given date string
        $date = Carbon::parse($period_from);
    
        // Get the quarter of the year
        $quarter = $date->quarter;
    
        // Output the result
        return $quarter;
    }

    public function calculateEarnedPremium() : float
    {
        $debit = $this->_debit;

        $treaty = $this->fetchTreatysetup();  
        $period_from_qtr = $this->checkQuarter($debit->period_from);

        switch ($treaty->upr_method) {
            case 'FLATRATE':
                $earned_premium = $this->_writtenPremium * ($treaty->upr_flat_rate/100);
                break;
            case 'EIGHTS':
                $earned_octets = [
                    1 => (7/8),
                    2 => (5/8),
                    3 => (3/8),
                    4 => (1/8),

                ];
                $octet = $earned_octets[$period_from_qtr];

                $earned_premium = $this->_writtenPremium * $octet;
                break;
            case '24THS':
                
                break;
            case 'PRORATA':
            default:
                $treaty_end_date =  Carbon::parse($treaty->period_to);
                $debit_period_from =  Carbon::parse($debit->period_from);
                $isLeapYear = Carbon::createFromDate($treaty->uw_year, 1, 1)->isLeapYear();
                $yearLength = $isLeapYear ? 366 : 365;
            
                $earned_days = $debit_period_from->diffInDays($treaty_end_date);

                if($earned_days < 0)
                {
                    $earned_days = 0;
                }

                $earned_premium = $this->_writtenPremium * ($earned_days / $yearLength);
                break;
        }

        return $earned_premium;
    }

    public function create_UW_treaty_cr_entry()
    {
        $debitmast = DB::select("
            SELECT
                d.policy_no,
                d.endt_renewal_no,
                d.account_year,
                d.account_month,
                d.uw_year,
                d.class,
                d.doc_type,
                d.entry_type_descr,
                d.dtrans_no,
                d.period_from,
                d.period_to,
                d.dola,
                clsyr.reinclass AS reinclass,
                reincls.treaty_expiry_midyear,
                NVL(d.ret_premium, 0) AS ret_premium,
                NVL(d.kr_premium, 0) AS kr_premium,
                NVL(d.quota_premium, 0) AS quota_premium,
                NVL(d.surp_1st_premium, 0) AS surp_1st_premium,
                NVL(d.surp_2nd_premium, 0) AS surp_2nd_premium,
                NVL(d.surp_3rd_premium, 0) AS surp_3rd_premium,
                NVL(d.kr_commission, 0) AS kr_commission,
                NVL(d.quota_commission, 0) AS quota_commission,
                NVL(d.surp_1st_commission, 0) AS surp_1st_commission,
                NVL(d.surp_2nd_commission, 0) AS surp_2nd_commission,
                NVL(d.surp_3rd_commission, 0) AS surp_3rd_commission,
                NVL(d.vat_on_kr_premium, 0) AS vat_on_kr_premium,
                NVL(d.vat_on_quota_premium, 0) AS vat_on_quota_premium,
                NVL(d.vat_on_surp_1st_premium, 0) AS vat_on_surp_1st_premium,
                NVL(d.vat_on_surp_2nd_premium, 0) AS vat_on_surp_2nd_premium,
                NVL(d.vat_on_surp_3rd_premium, 0) AS vat_on_surp_3rd_premium,
                NVL(d.vat_on_kr_commission, 0) AS vat_on_kr_commission,
                NVL(d.vat_on_quota_commission, 0) AS vat_on_quota_commission,
                NVL(d.vat_on_surp_1st_commission, 0) AS vat_on_surp_1st_commission,
                NVL(d.vat_on_surp_2nd_commission, 0) AS vat_on_surp_2nd_commission,
                NVL(d.vat_on_surp_3rd_commission, 0) AS vat_on_surp_3rd_commission
            FROM
                debitmastdtl d
            JOIN classyear clsyr ON clsyr.uw_year = d.uw_year AND clsyr.class = d.class
            JOIN reinclass reincls ON reincls.reinclass = clsyr.reinclass
            WHERE
                d.endt_renewal_no = '$this->endt_renewal_no'
            AND (
                d.kr_premium <> 0
                OR d.quota_premium <> 0
                OR d.ret_premium <> 0
                OR d.surp_1st_premium <> 0
                OR d.surp_2nd_premium <> 0
                OR d.surp_3rd_premium <> 0
            )
        ");

        foreach ($debitmast as $entry) {
            $this->_debit = $entry;
            $this->processUWData();
            $this->persistUWTreatyDtlStatement();
            $this->persistUWTreatyCrStatement();
        }
    }

    public function create_clm_treaty_cr_entry()
    {
        $clmData = DB::select("
            SELECT
                d.account_year,
                d.account_month,
                d.claim_no,
                d.doc_type,
                d.entry_type_descr,
                d.dtrans_no,
                d.pay_date,
                db.uw_year,
                clh.claim_year,
                clh.class,
                clh.policy_no,
                clh.endt_renewal_no,
                db.period_from,
                db.period_to,
                clsyr.reinclass AS reinclass,
                reincls.treaty_expiry_midyear,
                d.pay_type,
                NVL(d.movt_mandatory_recovery, 0)   AS mandatory_recovery,
                NVL(d.movt_quota_recovery, 0)       AS quota_recovery,
                NVL(d.movt_surplus_1_recovery, 0)   AS surplus_1_recovery,
                NVL(d.movt_surplus_2_recovery, 0)   AS surplus_2_recovery,
                NVL(d.movt_surplus_3_recovery, 0)   AS surplus_3_recovery
            FROM
                clpmn d
            JOIN
                clhmn clh ON clh.claim_no = d.claim_no
            JOIN
                debitmast db ON db.endt_renewal_no = clh.endt_renewal_no
            JOIN
                classyear clsyr ON clsyr.uw_year = clh.uw_year AND clsyr.class = clh.class
            JOIN
                reinclass reincls ON reincls.reinclass = clsyr.reinclass
            WHERE
                d.pay_type IN (10, 20)
                AND d.claim_no = '$this->claim_no'
                AND d.doc_type = '$this->doc_type'
                AND d.entry_type_descr = '$this->entry_type_descr'
                AND d.dtrans_no = '$this->dtrans_no'
                AND d.account_year = '$this->account_year'
                AND d.ln_no = '$this->ln_no'
                AND (
                    d.movt_mandatory_recovery <> 0
                    OR d.movt_quota_recovery <> 0
                    OR d.movt_surplus_1_recovery <> 0
                    OR d.movt_surplus_2_recovery <> 0
                    OR d.movt_surplus_3_recovery <> 0
                )
        ");

        foreach ($clmData as $entry) {
            $this->_clm = $entry;
            $this->processClaimsPaidData();

            $this->persistCLMTreatyDtlStatement();
            $this->persistCLMTreatyCrStatement();
        }
    }

    public function create_cashloss_treaty_cr_entry()
    {
        $cashloss = DB::select("
            SELECT
                d.treaty_code,
                d.account_year,
                d.account_month,
                d.doc_type,
                d.entry_type_descr,
                d.broker_branch,
                d.broker_agent,
                d.branch,
                d.agent,
                clh.claim_no,
                clh.policy_no,
                clh.endt_renewal_no,
                d.uw_year,
                clh.claim_year,
                d.date_effective,
                clh.class,
                db.period_from,
                db.period_to,
                clsyr.reinclass AS reinclass,
                reincls.treaty_expiry_midyear,
                NVL(d.nett, 0) AS nett,
                NVL(d.allocated, 0) AS allocated,
                NVL(d.unallocated, 0) AS unallocated
            FROM
                Reiacdet d
            JOIN
                cbmast cb ON cb.dtrans_no = d.dtrans_no and cb.doc_type = d.doc_type and cb.account_year=d.account_year and cb.entry_type_descr=d.entry_type_descr
            JOIN
                clhmn clh ON clh.claim_no = cb.claim_no
            JOIN
                debitmast db ON db.endt_renewal_no = clh.endt_renewal_no
            JOIN
                classyear clsyr ON clsyr.uw_year = clh.uw_year AND clsyr.class = clh.class
            JOIN
                reinclass reincls ON reincls.reinclass = clsyr.reinclass
            WHERE
                d.treaty_code = '$this->treaty_code'
            AND d.doc_type = '$this->doc_type'
            AND d.entry_type_descr = '$this->entry_type_descr'
            AND d.ln_no = '$this->ln_no'
            AND d.dtrans_no = '$this->dtrans_no'
            AND d.account_year = '$this->account_year'
        ");

        foreach ($cashloss as $entry) {
            $this->_cashloss = $entry;

            $this->persistCashlossTreatyDtlStatement();
            $this->persistCashlossTreatyCrStatement();
        }
    }

    public function create_pay_treaty_cr_entry()
    {
        $payments = DB::table('Reiacdet d')
            ->select(
                'd.treaty_code',
                'd.doc_type',
                'd.entry_type_descr',
                'd.dtrans_no',
                'd.account_year',
                'd.account_month',
                'd.broker_branch',
                'd.broker_agent',
                'd.branch',
                'd.agent',
                'd.uw_year',
                'd.nett',
                'd.broker_branch',
                'd.broker_agent',
                'd.branch',
                'd.agent',
                'd.date_effective',
                '999 as class',
                'NVL(d.nett, 0) as nett',
                'NVL(d.allocated, 0) as allocated',
                'NVL(d.unallocated, 0) as unallocated',
            )
            ->where('d.treaty_code', $this->treaty_code)
            ->where('d.doc_type', $this->doc_type)
            ->where('d.entry_type_descr', $this->entry_type_descr)
            ->where('d.dtrans_no', $this->dtrans_no)
            ->where('d.ln_no', $this->ln_no)
            ->where('d.account_year', $this->account_year)
            ->get();

        foreach ($payments as $entry) {
            $this->_trtpayment = $entry;

            $this->persistpaymentsTreatyDtlStatement();
            $this->persistpaymentsTreatyCrStatement();
        }
    }

    public function generate_treaty_debit_credit_notes(Request $request)
    {
        $request->validate([
            'account_year' => 'required',
            'frequency_value' => 'required'
        ]);
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try 
        {
            $pipcnam = Pipcnam::first();

            $frequency_value = $request->frequency_value;
            $this->_account_year = $request->account_year;

            switch ($pipcnam->treaty_cr_dr_frequency ) {
                case 'MONTHLY':
                    $months = [$frequency_value];
                    $account_month = $frequency_value;
                    break;
                case 'QUARTERLY':
                    $months = Period::where('account_year',$this->_account_year)
                        ->where('quarter',$frequency_value)
                        ->pluck('account_month')
                        ->toArray();
                    $account_month = Period::where('account_year',$this->_account_year)
                        ->where('quarter',$frequency_value)
                        ->max('account_month');
                    break;
                case 'HALFWAY':
                    if((int)$frequency_value == 1){
                        $months = Period::where('account_year',$this->_account_year)
                            ->where('account_month','<=',6)
                            ->pluck('account_month')
                            ->toArray();
                        $account_month = 6;
                    }
                    else{
                        $months = Period::where('account_year',$this->_account_year)
                            ->where('account_month','>',6)
                            ->pluck('account_month')
                            ->toArray();
                        $account_month = 12;
                    }

                    break;
                case 'ANNUALLY':
                    $months = Period::where('account_year',$this->_account_year)
                        ->pluck('account_month')
                        ->toArray();
                    $account_month = 12;
                    break;
            }

            $months_str = implode(',',$months);

            $statementExists = Treatymastpart::where('account_year',$this->_account_year)
                ->whereIn('account_month',$months)
                ->exists();

            $unprocessedTransactions = Treatymastpart::where('account_year',$this->_account_year)
                ->whereIn('account_month',$months)
                ->where('dr_cr_generated','N')
                ->count();

            if(!$statementExists)
            {                
                Session::flash('error','Quarterly treaty process has not been run for the selected run period');
                return redirect()->route('deftrtprocess');
            }
            if($unprocessedTransactions == 0)
            {
                Session::flash('error','All Debits/Credits for the selected run period already exists in the system');
                return redirect()->route('deftrtprocess');
            }

            $treatyStatement = DB::select("
                SELECT
                    treaty_code,
                    broker_branch,
                    broker_agent_no,
                    branch,
                    agent_no,
                    account_year,
                    uw_year,
                    SUM(NVL(premium,0)) AS premium,
                    SUM(NVL(commission,0)) AS commission,
                    SUM(NVL(profit_commission,0)) AS profit_commission,
                    SUM(NVL(claims_recovery,0)) AS claims_recovery,
                    SUM(NVL(adjusted_net_comm,0)) AS adjusted_net_comm,
                    SUM(NVL(cashloss,0)) AS cashloss,
                    SUM((NVL(premium,0)-NVL(commission,0))+ (NVL(claims_recovery,0)+NVL(cashloss,0))) AS total_amount
                FROM
                    treatymastpart
                WHERE account_month in ($months_str) and account_year=$this->_account_year
                AND dr_cr_generated <> 'Y'
                GROUP BY
                    treaty_code,
                    broker_branch,
                    broker_agent_no,
                    branch,
                    agent_no,
                    account_year,
                    uw_year
                HAVING (
                        SUM(NVL(claims_recovery,0)) <> 0
                    OR SUM(NVL(premium,0)) <> 0
                    OR SUM(NVL(commission,0)) <> 0
                    OR SUM(NVL(profit_commission,0)) <> 0
                    OR SUM(NVL(adjusted_net_comm,0)) <> 0
                    OR SUM(NVL(cashloss,0)) <> 0
                )
            ");

            foreach ($treatyStatement as $entry) {
                $commonData = [
                    'account_year'  => $entry->account_year,
                    'treaty_code'   => $entry->treaty_code,
                    'branch'        => $entry->branch,
                    'agent'         => $entry->agent_no,
                    'broker_branch' => $entry->broker_branch,
                    'broker_agent_no' => $entry->broker_agent_no,
                    'account_month' => $account_month,
                    'uw_year'       => $entry->uw_year,
                ];

                $premium = $entry->premium;
                $commission = $entry->commission;
                $claims_recovery = $entry->claims_recovery;
                $cashloss = $entry->cashloss; //cashloss is a negative
                $total_amt = ($commission-$premium) + ($claims_recovery+$cashloss);

                $additionalData = [
                    'entry_type_descr' => 'TRT',
                    'gross'         => $total_amt,
                    'nett'          => $total_amt,
                ];
                $data = array_merge($commonData,$additionalData);
                $resp = $this->persistReiacdet($data);
                $reiacdet = $resp['reiacdet'];
                $reference = str_pad($reiacdet->dtrans_no,6,'0',STR_PAD_LEFT).$entry->account_year;
                
                if((float)$entry->premium != 0)
                {
                    $additionalData = [
                        'reference' => $reference,
                        'entry_type_descr' => 'PRM',
                        'total_amount' => $reiacdet['nett'],
                        'gross'         => $entry->premium*-1,
                        'nett'          => $entry->premium*-1,
                    ];
                    $data = array_merge($commonData,$additionalData);

                    $this->persistTreatyCreditors($data);
                }
                // commission
                if((float)$entry->commission != 0)
                {
                    $additionalData = [
                        'reference' => $reference,
                        'entry_type_descr' => 'COM',
                        'total_amount' => $reiacdet['nett'],
                        'gross'         => $entry->commission,
                        'nett'          => $entry->commission,
                    ];
                    $data = array_merge($commonData,$additionalData);

                    $this->persistTreatyCreditors($data);
                }

                // recoveries
                if((float)$entry->claims_recovery != 0)
                {
                    $additionalData = [
                        'reference' => $reference,
                        'entry_type_descr' => 'CLP',
                        'total_amount' => $reiacdet['nett'],
                        'gross'         => $entry->claims_recovery,
                        'nett'          => $entry->claims_recovery,
                    ];
                    $data = array_merge($commonData,$additionalData);

                    $this->persistTreatyCreditors($data);
                }

                // cash loss
                if((float)$entry->cashloss != 0)
                {
                    $additionalData = [
                        'reference' => $reference,
                        'entry_type_descr' => 'CAL',
                        'total_amount' => $reiacdet['nett'],
                        'gross'         => $entry->cashloss,
                        'nett'          => $entry->cashloss,
                    ];
                    $data = array_merge($commonData,$additionalData);

                    $this->persistTreatyCreditors($data);
                }

                // adjusted_net_comm
                if((float)$entry->adjusted_net_comm != 0)
                {
                    $additionalData = [
                        'entry_type_descr' => 'ADC',
                        'gross'         => $entry->adjusted_net_comm,
                        'nett'          => $entry->adjusted_net_comm,
                    ];
                    $data = array_merge($commonData,$additionalData);

                    $this->persistReiacdet($data);
                }

                // profit_commission
                
                if((float)$entry->profit_commission != 0)
                {
                    $additionalData = [
                        'entry_type_descr' => 'PRC',
                        'gross'         => $entry->profit_commission,
                        'nett'          => $entry->profit_commission,
                    ];
                    $data = array_merge($commonData,$additionalData);

                    $this->persistReiacdet($data);
                }

                // set records as processed to avoid reprocessing
                Treatymastpart::where('account_year',$this->_account_year)
                    ->whereIn('account_month',$months)
                    ->update([
                        'dr_cr_generated' => 'Y'
                    ]);
            }

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            Session::flash('success','Successfully Generated Debit/Credit Notes');
            return redirect()->route('deftrtprocess');
        }
        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollBack();
            DB::connection(env('DB_CONNECTION1'))->rollBack();
                
            $error_msg = json_encode($e->getMessage());
            $reference = "Account Year: {$request->account_year} Frequency Value: {$request->frequency_value}";
            $module = __METHOD__;
            $route_name = Route::getCurrentRoute()->getActionName();
            log_error_details($route_name,$error_msg,$reference,$module);
            // dd($e);
            Session::flash('error','Failed to Generate Debit/Credit Notes');
            return redirect()->route('deftrtprocess');
        }
    }

    public function persistTreatyCreditors($data)
    {
        try {
            $period = Period::where('account_year',$data['account_year'])
                ->where('account_month',$data['account_month'])
                ->first();
            
            $crmast = Crmast::where('branch',$data['branch'])
                ->where('agent',$data['agent'])
                ->first();

            $treaty = Treaty::select('treaty_code','part_type')
                ->where('treaty_code',$data['treaty_code'])
                ->first();
            $treatytype = Treatytype::where('part_type',$treaty->part_type)->first();

            $doc_type = $data['nett'] < 0 ? 'CRN' : 'DRN';

            $dtrans_no = substr($data['reference'],0,6);

            switch ($data['entry_type_descr']) {
                case 'PRM':
                    $glhead = $crmast->glhead_prop_premium;
                    $slhead = $crmast->sub_account_prop_premium;
                    break;
                case 'COM':
                    $glhead = $crmast->glhead_prop_commission;
                    $slhead = $crmast->sub_account_prop_comm;
                    break;
                case 'CLP':
                case 'CAL':
                    if($treatytype->treaty_categ == 'QUOTA')
                    {
                        $glhead = $crmast->quota_share_claim_glhead;
                        $slhead = $crmast->quota_share_claim_slhead;
                    }
                    elseif($treatytype->treaty_categ == 'SURP')
                    {
                        $glhead = $crmast->surplus_claim_glhead;
                        $slhead = $crmast->surplus_claim_slhead;
                    }
                    break;
                
                default:
                    
                    break;
            }

            $TreatyCreditor = new TreatyCreditor();
            $TreatyCreditor->dtrans_no            = $dtrans_no;
            $TreatyCreditor->reference            = $data['reference'];
            $TreatyCreditor->account_year         = $data['account_year'];
            $TreatyCreditor->account_month        = $data['account_month'];
            $TreatyCreditor->quarter              = $period->quarter;
            $TreatyCreditor->treaty_code          = $data['treaty_code'];
            $TreatyCreditor->doc_type             = $doc_type;
            $TreatyCreditor->entry_type_descr     = $data['entry_type_descr'];
            $TreatyCreditor->total_amount         = $data['total_amount'];
            $TreatyCreditor->amount               = $data['nett'];
            $TreatyCreditor->branch               = $data['branch'];
            $TreatyCreditor->agent                = $data['agent'];
            $TreatyCreditor->broker_branch        = $data['broker_branch'];
            $TreatyCreditor->broker_agent         = $data['broker_agent_no'];
            $TreatyCreditor->uw_year              = $data['uw_year'];
            $TreatyCreditor->glhead              = $glhead;
            $TreatyCreditor->slhead              = $slhead;

            $TreatyCreditor->save();

        } catch (\Throwable $e) {
            throw $e;
        }
    }

    public function persistReiacdet($data)
    {
        try {
            $period = Period::where('account_year',$data['account_year'])
                ->where('account_month',$data['account_month'])
                ->first();

            if($data['nett'] < 0)
            {
                $doc_type  ='CRN';
                $dtrans_no = Dtran0::first()->credit_no;
                Dtran0::where('rec_no', 0)->increment('credit_no', (int) '1');
            }
            else
            {
                $doc_type  ='DRN';
                $dtrans_no = Dtran0::first()->debit_no;
                Dtran0::where('rec_no', 0)->increment('debit_no', (int) '1');
            }

            $dr_cr = $data['nett'] < 0 ? 'C': 'D';
            $currency_code = Currency::where('base_currency','Y')->firstOrFail()->currency_code;

            $comm_amt = 0;
            switch($data['entry_type_descr'])
            {
                case 'COM':
                    $comm_amt = $data['gross'];
                    break;
            }

            $ln_no = 0;
            $ref_ln_no = 0;
            $item_no = 0;
            $entry_type = 0;
            $type = 30;

            $reiacdet = new Reiacdet();
            $reiacdet->dtrans_no            = $dtrans_no;
            $reiacdet->trans_number         = $dtrans_no;
            $reiacdet->ln_no                = $ln_no;
            $reiacdet->ref_ln_no            = $ref_ln_no;
            $reiacdet->item_no              = $item_no;
            $reiacdet->entry_type           = $entry_type;
            $reiacdet->type                 = $type;
            $reiacdet->account_year         = $data['account_year'];
            $reiacdet->account_month        = $data['account_month'];
            $reiacdet->quarter              = $period->quarter;
            $reiacdet->treaty_code          = $data['treaty_code'];
            $reiacdet->orig                 = 'TAC';
            $reiacdet->doc_type             = $doc_type;
            $reiacdet->entry_type_descr     = $data['entry_type_descr'];
            $reiacdet->dr_cr                = $dr_cr;
            $reiacdet->gross                = $data['gross'];
            $reiacdet->nett                 = $data['nett'];
            $reiacdet->allocated            = 0;
            $reiacdet->comm_amt             = $comm_amt;
            $reiacdet->unallocated          = $data['nett'] ;
            $reiacdet->branch               = $data['branch'];
            $reiacdet->agent                = $data['agent'];
            $reiacdet->broker_branch        = $data['broker_branch'];
            $reiacdet->broker_agent         = $data['broker_agent_no'];
            $reiacdet->uw_year              = $data['uw_year'];
            $reiacdet->currency_code        = $currency_code;
            $reiacdet->currency_rate        = 1;
            $reiacdet->date_effective       = Carbon::today();
            $reiacdet->dola                 = Carbon::today();
            $reiacdet->date_processed       = Carbon::now();
            $reiacdet->time                 = Carbon::now();
            $reiacdet->user_id              = Auth::user()->user_name;
            $reiacdet->save();

            return [
                'reiacdet' => $reiacdet,
                'dtrans_no' => $dtrans_no
            ];

        } catch (\Throwable $e) {
            throw $e;
        }
    }

    public function quarterly_treaty_Datatable(Request $request)
    {
        $statement = Treatymastuwyr::query();

        return datatables::of($statement)
            ->editColumn('premium', function($data){
                return number_format($data->premium,2);
            })
            ->editColumn('commission', function($data){
                return number_format($data->commission,2);
            })
            ->editColumn('claims_os', function($data){
                return number_format($data->claims_os,2);
            })
            ->editColumn('claims_recovery', function($data){
                return number_format($data->claims_recovery,2);
            })
            ->editColumn('management_expense', function($data){
                return number_format($data->management_expense,2);
            })
            ->editColumn('profit_commission', function($data){
                return number_format($data->profit_commission,2);
            })
            ->editColumn('payments', function($data){
                return number_format($data->payments,2);
            })
            ->editColumn('cashloss', function($data){
                return number_format($data->cashloss,2);
            })
            ->editColumn('portfolio_prem_withdrawal', function($data){
                return number_format($data->portfolio_prem_withdrawal,2);
            })
            ->editColumn('portfolio_loss_withdrawal', function($data){
                return number_format($data->portfolio_loss_withdrawal,2);
            })
            ->editColumn('portfolio_prem_entry', function($data){
                return number_format($data->portfolio_prem_entry,2);
            })
            ->editColumn('portfolio_loss_entry', function($data){
                return number_format($data->portfolio_loss_entry,2);
            })
            ->addColumn('treaty_name', function($data){
                $treaty = Treaty::where('treaty_code',$data->treaty_code)->first();

                return $data->treaty_code.'-'.trim($treaty->treaty_description);
            })
            ->make(true);
    }

    public function quarterly_treatyPerClass_datatable(Request $request)
    {
        $statement = Treatymastclassuwyr::query();

        return datatables::of($statement)
            ->editColumn('premium', function($data){
                return number_format($data->premium,2);
            })
            ->editColumn('commission', function($data){
                return number_format($data->commission,2);
            })
            ->editColumn('claims_os', function($data){
                return number_format($data->claims_os,2);
            })
            ->editColumn('claims_recovery', function($data){
                return number_format($data->claims_recovery,2);
            })
            ->editColumn('payments', function($data){
                return number_format($data->payments,2);
            })
            ->editColumn('cashloss', function($data){
                return number_format($data->cashloss,2);
            })
            ->addColumn('treaty_name', function($data){
                $treaty = Treaty::where('treaty_code',$data->treaty_code)->first('treaty_description');

                return $data->treaty_code.'-'.trim($treaty->treaty_description);
            })
            ->make(true);
    }

    public function profit_comm_datatable(Request $request)
    {
        $statement = Treatymastuwyr::query()
            ->where('profit_commission','<>',0);

        return datatables::of($statement)
            ->editColumn('premium', function($data){
                return number_format($data->premium,2);
            })
            ->editColumn('commission', function($data){
                return number_format($data->commission,2);
            })
            ->editColumn('claims_os', function($data){
                return number_format($data->claims_os,2);
            })
            ->editColumn('claims_recovery', function($data){
                return number_format($data->claims_paid,2);
            })
            ->editColumn('management_expense', function($data){
                return number_format($data->management_expense,2);
            })
            ->editColumn('profit_commission', function($data){
                return number_format($data->profit_commission,2);
            })
            ->editColumn('payments', function($data){
                return number_format($data->payments,2);
            })
            ->editColumn('cashloss', function($data){
                return number_format($data->cashloss,2);
            })
            ->addColumn('part_name', function($data){
                $part = Crmast::where('branch',$data->branch)
                    ->where('agent',$data->agent_no)
                    ->first();

                return $part->name;
            })
            ->addColumn('treaty_name', function($data){
                $treaty = Treaty::where('treaty_code',$data->treaty_code)->first();

                return $data->treaty_code.'-'.trim($treaty->treaty_description);
            })
            ->make(true);
    }

    public function adj_comm_datatable(Request $request)
    {
        $statement = DB::select("SELECT d.treaty_code,d.account_year, d.uw_year,
                SUM( NVL(premium, 0)) as premium,
                SUM( NVL(adjusted_comm_amt, 0)) as adjusted_comm_amt,
                SUM( NVL(adjusted_net_comm, 0)) as adjusted_net_comm,
                SUM( NVL(claims_recovery, 0)) as claims_recovery,
                SUM( NVL(commission, 0)) as commission
            FROM Treatymastuwyr d
            GROUP BY d.treaty_code,d.account_year, d.uw_year
            HAVING sum(nvl(d.adjusted_comm_amt,0)) <>0
            ORDER BY account_year DESC
            ");

        return datatables::of($statement)
            ->editColumn('premium', function($data){
                return number_format($data->premium,2);
            })
            ->editColumn('commission', function($data){
                return number_format($data->commission,2);
            })
            ->editColumn('claims_recovery', function($data){
                return number_format($data->claims_paid,2);
            })
            ->editColumn('adjusted_comm_amt', function($data){
                return number_format($data->adjusted_comm_amt,2);
            })
            ->editColumn('adjusted_net_comm', function($data){
                return number_format($data->adjusted_net_comm,2);
            })
            ->addColumn('part_name', function($data){
                $part = Crmast::where('branch',$data->branch)
                    ->where('agent',$data->agent_no)
                    ->first();

                return $part->name;
            })
            ->addColumn('treaty_name', function($data){
                $treaty = Treaty::where('treaty_code',$data->treaty_code)->first();

                return $data->treaty_code.'-'.trim($treaty->treaty_description);
            })
            ->make(true);
    }

    public function treaty_perParticipant_datatable(Request $request)
    {
        $statement = Treatymastpart::query();

        return datatables::of($statement)
            ->editColumn('premium', function($data){
                return number_format($data->premium,2);
            })
            ->editColumn('commission', function($data){
                return number_format($data->commission,2);
            })
            ->editColumn('claims_os', function($data){
                return number_format($data->claims_os,2);
            })
            ->editColumn('claims_recovery', function($data){
                return number_format($data->claims_paid,2);
            })
            ->editColumn('management_expense', function($data){
                return number_format($data->management_expense,2);
            })
            ->editColumn('profit_commission', function($data){
                return number_format($data->profit_commission,2);
            })
            ->editColumn('payments', function($data){
                return number_format($data->payments,2);
            })
            ->editColumn('portfolio_prem_withdrawal', function($data){
                return number_format($data->portfolio_prem_withdrawal,2);
            })
            ->editColumn('portfolio_loss_withdrawal', function($data){
                return number_format($data->portfolio_loss_withdrawal,2);
            })
            ->editColumn('portfolio_prem_entry', function($data){
                return number_format($data->portfolio_prem_entry,2);
            })
            ->editColumn('portfolio_loss_entry', function($data){
                return number_format($data->portfolio_loss_entry,2);
            })
            ->editColumn('cashloss', function($data){
                return number_format($data->cashloss,2);
            })
            ->addColumn('part_name', function($data){
                $part = Crmast::where('branch',$data->branch)
                    ->where('agent',$data->agent_no)
                    ->first();

                return $part->name ?? '-';
            })
            ->addColumn('treaty_name', function($data){
                $treaty = Treaty::where('treaty_code',$data->treaty_code)->first();

                return $data->treaty_code.'-'.trim($treaty->treaty_description);
            })
            ->make(true);
    }

    public function treaty_mdp_datatable(Request $request)
    {
        $statement = Treaty_mdp::query();

        return datatables::of($statement)
            ->editColumn('mdp', function($data){
                return number_format($data->mdp,2);
            })
            ->editColumn('prem_tax', function($data){
                return number_format($data->prem_tax,2);
            })
            ->editColumn('wht_tax', function($data){
                return number_format($data->wht_tax,2);
            })
            ->editColumn('net_mdp', function($data){
                return number_format($data->net_mdp,2);
            })
            ->addColumn('part_name', function($data){
                $part = Crmast::where('branch',$data->branch)
                    ->where('agent',$data->agent)
                    ->first();

                return $part->name ?? '-';
            })
            ->addColumn('treaty_name', function($data){
                $treaty = Treaty::where('treaty_code',$data->treaty_code)->first();

                return $data->treaty_code.'-'.trim($treaty->treaty_description);
            })
            ->make(true);
    }

    public function treaty_prem_adj_datatable(Request $request)
    {
        $statement = Treaty_prem_adj_part::query();

        return datatables::of($statement)
            ->editColumn('adj_premium', function($data){
                return number_format($data->adj_premium,2);
            })
            ->editColumn('xl_annual_prem', function($data){
                return number_format($data->xl_annual_prem,2);
            })
            ->editColumn('mdp', function($data){
                return number_format($data->mdp,2);
            })
            ->editColumn('prem_tax', function($data){
                return number_format($data->prem_tax,2);
            })
            ->editColumn('wht_tax', function($data){
                return number_format($data->wht_tax,2);
            })
            ->editColumn('net_adj_premium', function($data){
                return number_format($data->net_adj_premium,2);
            })
            ->addColumn('part_name', function($data){
                $part = Crmast::where('branch',$data->branch)
                    ->where('agent',$data->agent)
                    ->first();

                return $part->name ?? '-';
            })
            ->addColumn('treaty_name', function($data){
                $treaty = Treaty::where('treaty_code',$data->treaty_code)->first();

                return $data->treaty_code.'-'.trim($treaty->treaty_description);
            })
            ->make(true);
    }

    public function treaty_dr_cr_datatable(Request $request)
    {
        $statement = Reiacdet::query()
            ->where('orig','TAC')
            ->whereIn('doc_type',['DRN','CRN']);

        return datatables::of($statement)
            ->editColumn('gross', function($data){
                return number_format($data->gross,2);
            })
            ->editColumn('nett', function($data){
                return number_format($data->nett,2);
            })
            ->editColumn('allocated', function($data){
                return number_format($data->allocated,2);
            })
            ->editColumn('unallocated', function($data){
                return number_format($data->unallocated,2);
            })
            ->addColumn('reference', function($data){
                $reference = str_pad($data->dtrans_no,6,'0',STR_PAD_LEFT).$data->account_year;
                return formatReference($reference);
            })
            ->addColumn('part_name', function($data){
                $part = Crmast::where('branch',$data->branch)
                    ->where('agent',$data->agent)
                    ->first();

                return $part->name ?? '-';
            })
            ->addColumn('treaty_name', function($data){
                $treaty = Treaty::where('treaty_code',$data->treaty_code)->first();

                return $data->treaty_code.'-'.trim($treaty->treaty_description);
            })
            ->make(true);
    }
}
