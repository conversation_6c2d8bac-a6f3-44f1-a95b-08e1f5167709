<?php
namespace App\Http\Controllers\gb\claims;

// use DB;
use App\Clstatus;
use App\Models\Clsubstatus;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\View;
use Illuminate\Http\Request;

class ClaimSubStatusController extends Controller {

    public function listClaimSubstatus($statusCode){
        
        //list fraud types based on policy type
        $clsubstatus = Clsubstatus::where('rejection_code', $statusCode)->get();
        $clstatus = Clstatus::where('status_code', $statusCode)->first();

        return view('parameters.gb.clstatus_details',['clsubstatus' => $clsubstatus, 'clstatus' => $clstatus]);

    }

    public function listActiveSubStatus($statusCode){
        
        //list only active substatuses
        $clsubstatus = Clsubstatus::where('rejection_code', $statusCode)
                        ->where('status', 'Y')
                        ->get();

        if ($clsubstatus->isEmpty()) {
            return response()->json([
                'message' => 'No active substatus types found for the provided status code.',
                'status' => 0,
            ], 404);
        }
        
        return ['clsubstatus' => $clsubstatus];

    }

    public function storeSubStatus(Request $request){
         if($request->name == null) {
            return response()->json([
                'message' => 'Substatus name is empty!',
                'alert-type' => 'error',
                'status' => 0,
            ], 422);
         } 
         else {
            $data = $request->validate([
                'name' => 'required',
            ]);
         }
         
        try {
            $existingSubStatus = Clsubstatus::where('name', $data['name'])
            ->where('rejection_code', $request->status_code)
            ->exists();

            if ($existingSubStatus ) {
                
                return response()->json([
                    'message' => "Substatus [{$request->name}] already exists",
                    'alert-type' => 'error',
                    'status' => 0,
                ], 409);

            } else {
                $newSubStatus = Clsubstatus::create([
                    'name' => $data['name'],
                    'rejection_code' => $request->status_code,
                ]);

                return response()->json([
                    'message' => 'Substatus created successfully!',
                    'alert-type' => 'success',
                    'status' => 1,
                ]);
            }

        } catch (\Throwable $th) {

            return response()->json([
                'message' => 'Error adding Substatus!',
                'alert-type' => 'error',
                'status' => 0,
                'details' => $th->getMessage(),
            ], 500);
        }
    }

    public function updateSubStatus(Request $request){    
        $data = $request->validate([
            'name' => 'required',
        ]);

        // dd($data);

        try {
            $subStatus = Clsubstatus::where('id', $request->id)
            ->first();
            
            if (!$subStatus) {
                return response()->json([
                    'message' => 'Substatus not found!',
                    'alert-type' => 'error',
                    'status' => 0,
                ], 404);
            }
            $subStatus->update($data);
            
            return response()->json([
                'message' => 'Substatus updated successfully!',
                'alert-type' => 'success',
                'status' => 1,
            ]);
    
        } catch (\Throwable $th) {

            return response()->json([
                'message' => 'Error updating Substatus!',
                'alert-type' => 'error',
                'status' => 0,
                'details' => $th->getMessage(),
            ], 500);
        }
    }

    public function toggleSubstatusActivation(Request $request){ 
        // dd($request);

        $data = $request->validate([
            'status' => 'required|string|size:1|in:Y,N'
        ]);

        try {
            $subStatus = Clsubstatus::where('id', $request->id)->first();
            if($data['status'] === 'Y'){
                $subStatus->update(['status' => 'N']);
            } 
            else if($data['status'] === 'N'){
                $subStatus->update(['status' => 'Y']);
            }

            return response()->json([
                'message' => 'Substatus Status updated successfully!',
                'alert-type' => 'success',
                'status' => 1,
            ]);
    
        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Error updating Status Activation!',
                'alert-type' => 'error',
                'status' => 0,
                'details' => $th->getMessage(),
            ], 500);
        }
    }

    public function deleteSubStatus(Request $request){  

        try {
            $subStatus =  Clsubstatus::where('id', $request->id)
            ->first();

            $subStatus->delete();

            return response()->json([
                'message' => 'Substatus deleted successfully!',
                'alert-type' => 'success',
                'status' => 1,
            ]);

        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Error deleting Substatus!',
                'alert-type' => 'error',
                'status' => 0,
                'details' => $th->getMessage(),
            ], 500);
        }
        
    
    }
}
