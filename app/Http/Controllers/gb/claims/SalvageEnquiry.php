<?php

namespace App\Http\Controllers\gb\claims;

// use Excel;
use App\Clhmn;
use App\Modtl;
use Exception;
use App\Client;
use App\Dtran0;
use App\Tender;

use App\Clparam;

use App\Clmcorr;


use App\Doctype;
use App\Gltaxes;
use App\Pipcnam;
use App\Polsect;
use App\Aimsuser;
use App\Dcontrol;
use App\Modtlend;
use App\Prosched;
use App\Salvages;
use App\Creditclm;
use App\Polmaster;
use App\Models\BundledSalvageItems;
use Carbon\Carbon;
use App\Aimsuprofgb;
use App\Polmasterend;
use App\Tenderbuyers;
use App\Tenderserial;
use App\Sal_categories;
use App\Salvage_buyers;
use App\ClassModel;
use App\Job_description;
use App\Models\Modtlmast;
use App\Third_party_claims;
use App\Salvage_audit_trail;
use Illuminate\Http\Request;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Route;
use Aimsoft\UserManagement\Models\Role;
use App\Http\Controllers\pdfController;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Response;
use App\Classes\Common\FileUploadManager;
use App\Models\Salvage_checklist;
use App\Models\SalvageMemoItems;
use App\Exports\SalvageMemoExport;
use App\Models\Direct_sale_audit;
use App\Models\Dsale_buyer_types;
use Maatwebsite\Excel\Facades\Excel;
use App\Events\DispatchNotificationEvent;

use Illuminate\Support\Facades\Validator;

use function PHPUnit\Framework\throwException;

class SalvageEnquiry extends Controller
{
    /*Define classwise Variables*/
    public $current_year;
    public $current_month;
    public $todays_date;
    public $todays_date_time;
    public $user_name;
    public $email;
    public $jd_code;
    public $dept;

    public function __construct() {
        /* Get username */
        $this->middleware(function ($request, $next) {
            $this->user_name = trim(Auth::user()->user_name);

            return $next($request);
        });
        /* Get Email */
        $this->middleware(function ($request, $next) {
            $this->email = trim(Auth::user()->email);

            return $next($request);
        });
        /* Get Job description code */
        $this->middleware(function ($request, $next) {
            $this->jd_code = trim(Auth::user()->jd_code);

            return $next($request);
        });
        $this->middleware(function ($request, $next) {
            $this->dept = trim(Auth::user()->dept);

            return $next($request);
        });
        /*initialize variables into a class*/
        $this->current_year = Carbon::now()->format('Y');
        $this->current_month = Carbon::now()->format('m');
        $this->todays_date = Carbon::today();
        $this->todays_date_time = Carbon::now();
    }
    /*End Define classwise Variables*/

    /* Helper Methods */
    public function jsonifySalvages(Request $request){

        $serial_no = $request->serial_no;
        $salvage_details = Salvages::whereRaw("trim(serial_no)='" . trim($serial_no) . "'")->get();
        echo $salvage_details;
    }
    /*End Helper Methods*/

    /*General Functions*/
    public function genTenderNo(){
        $start_str = 'ACC';
        $get_tender_count = Tenderserial::Where('tender_year','=',$this->current_year)->count();
        $get_tender_count+=1;
        $serial= str_pad((string) $get_tender_count, 6, '0', STR_PAD_LEFT);
        $tender_no = $start_str.$serial.$this->current_year.$this->current_month;
        return $tender_no;

    }
    public function getClhmnData($claim_no){
        $clhmn = Clhmn::Where('claim_no','=',$claim_no)->first();
        return $clhmn;
    }
    public function getTenderserialData($tender_no){
        $tenderserial = Tenderserial::Where('tender_no','=',$tender_no)->first();
        return $tenderserial;
    }
    public function getSalvageData($reg_no){
        $salvage = Salvages::Where('reg_no',$reg_no)->first();
        return $salvage;
    }
    public function getModtlData($reg_no,$policy_no){
        $vehicle_details = Modtl::Where([
            ['reg_no','like','%'.$reg_no.'%'],
            ['policy_no','=',$policy_no]
        ])->first();
        dd($vehicle_details);
        return $vehicle_details;
    }
    public function ifExistsBuyers($buyer_id){
        $salvage_buyers = Salvage_buyers::Where('buyer_id','=',$buyer_id)->first();

        if(count($salvage_buyers)==1){
            $check_exists = 1;
        }
        else{
            $check_exists=0;
        }
        return $check_exists;
    }
    /*End General Functions*/

    /*Datatable*/
    public function showTenderEnquiry()
    {   
        
        $ReadySalvages = Salvages::Where('tendered','N')
            ->where('retained_by_client', 'N')
            ->where('debited', 'N')
            ->whereNot('status', 803)
            // ->where('status', 802)
            // ->orWhere('status', 805)
            ->get();
        $garages  = Clparam::where('record_type','G')->where('status','ACTIVE')->get();

        $todays_date = $this->todays_date;
        $this->genTenderNo();

        $dsale_buyertypes = Dsale_buyer_types::where('status', 1)->get();

        $salvage_category = Sal_categories::where('status','Y')->get();

        $tpvehicles = DB::select("select * from third_party_claims where tp_reg_no not in (select reg_no from salvages where third_party = 'Y')");

        //can select non_mv items by reg_no but what about those from fire policy
        // $non_motoritems = DB::select("select a.claim_no, a.reg_no from clhmn a join class b on b.class = a.class where b.salvage_parts = 'Y'");
        $non_motoritems = DB::select("
            SELECT a.claim_no, a.reg_no FROM clhmn a 
            LEFT JOIN class b ON a.CLASS = b.CLASS 
            WHERE a.CLAIM_NO NOT IN (
                SELECT claim_no 
                FROM salvages 
                WHERE salvage_type IS NULL 
                AND salvage_type = 1
            )
            AND b.SALVAGE_PARTS = 'Y'            
        ");
        $Scontractor = DB::table('salvage_contractors')->get();

        return view('gb.claims.salvage_enquiry')->with(compact('ReadySalvages','todays_date','garages','Scontractor','tpvehicles','non_motoritems','salvage_category', 'dsale_buyertypes'));
    }

      ##get thirdparty vehicles
      public function tpvehikles(Request $request){

        $check_salvages = Third_party_claims::all();

        return $check_salvages;
    }

     ##save salvage items and TP vehicles
     public function savesalitems(Request $request){

        try{
            if($request->salvage_type == 1){

                ##get salvage details
                for($i = 0; $i < count($request->reg_no); ++$i){

                    $item = $request->reg_no[$i];
                    $dets  = Third_party_claims::whereRaw("trim(tp_reg_no)='" . $item . "'")->first();
                    $category = 1; 
                    $serial_no = $this->generatesalvageserial($category);
                    $claimdets  = Clhmn::where('claim_no',$dets->claim_no)->first();

                    $insert_salvages = new Salvages;
                    $insert_salvages->claim_no=$dets->claim_no;
                    $insert_salvages->serial_no=$serial_no;
                    $insert_salvages->salvage_type=$request->salvage_type;

                    $insert_salvages->reg_no=$request->reg_no[$i];
                    $insert_salvages->chassis_no=$request->chassis_no[$i];
                    $insert_salvages->preacc_val=str_replace(',','',$request->pre_acc_val[$i]);
                    $insert_salvages->salvage_amount=str_replace(',','',$request->sal_amount[$i]);
                    $insert_salvages->orig_salvage_amount = str_replace(',','',$request->sal_amount[$i]);

                    $insert_salvages->policy_no=$claimdets->policy_no;
                    $insert_salvages->dola=Carbon::now();
                    $insert_salvages->user1=Auth::user()->user_name;
                    // $insert_salvages->client_number = $claimdets->client_number;
                    $insert_salvages->client_name = trim($dets->tp_name);
                    $insert_salvages->log_book = trim($dets->tp_cert_no);
                    
                    $insert_salvages->tendered='N';
                    $insert_salvages->retained_by_client = 'N';
                    $insert_salvages->debited = 'N';
                    $insert_salvages->third_party = 'Y';
                    $insert_salvages->status = 801;
                    
                    $insert_salvages->save();                
                }
                
            }
            
            else if($request->salvage_type == 2){

                for($i = 0; $i < count($request->item_name); ++$i){

                    $validator =  Validator::make($request->all(),[
                            'item_name' => 'required',
                            // 'serial_no' => 'required',
                            'claim_no' => 'required',
                            'non_mot_val' => 'required',
                            'non_mot_pre_val' => 'required'
                        ]);
                        
                    if ($validator->fails()) {
                        Session::flash('error', 'some field are missing');
                        return [
                            'code' => -1,
                            'msg' => $validator->errors(),
                        ];
                    }

                    $category = $request->product_category[$i];

                    $serial_no = $this->generatesalvageserial($category);
                   
                    $claimdets  = Clhmn::where('claim_no',$request->claim_no)->first();

                    $insert_salvages = new Salvages;
                    $insert_salvages->claim_no=$request->claim_no[$i];
                    $insert_salvages->serial_no=$serial_no;
                    $insert_salvages->item_name=$request->item_name[$i];
                    $insert_salvages->preacc_val=str_replace(',','',$request->non_mot_pre_val[$i]);
                    $insert_salvages->salvage_amount=str_replace(',','',$request->non_mot_val[$i]);
                    $insert_salvages->orig_salvage_amount = str_replace(',','',$request->non_mot_val[$i]);
                    $insert_salvages->policy_no=$claimdets->policy_no;
                    $insert_salvages->dola=Carbon::now();
                    $insert_salvages->user1=Auth::user()->user_name;
                    $insert_salvages->tendered='N';
                    $insert_salvages->retained_by_client = 'N';
                    $insert_salvages->debited = 'N';
                    $insert_salvages->status = 801;
                    $insert_salvages->salvage_type=$request->salvage_type;

                    $insert_salvages->save();

                }

            }
            else if($request->salvage_type == 3){

                for($i = 0; $i < count($request->item_name); ++$i){

                    $validator =  Validator::make($request->all(),[
                            // 'item_name' => 'required',
                            // 'serial_no' => 'required',
                            'claim_no' => 'required',
                            // 'non_mot_val' => 'required',
                            // 'non_mot_pre_val' => 'required'
                        ]);
                        
                    if ($validator->fails()) {
                        Session::flash('error', 'some field are missing');
                        return [
                            'code' => -1,
                            'msg' => $validator->errors(),
                        ];
                    }

                    $category = $request->product_category[$i];

                    $serial_no = $this->generatesalvageserial($category);
                   
                    $claimdets  = Clhmn::where('claim_no',$request->claim_no)->first();

                    $insert_salvages = new Salvages;
                    $insert_salvages->claim_no=$request->claim_no[$i];
                    $insert_salvages->serial_no=$serial_no;
                    $insert_salvages->item_name='Bundled Salvage';
                    $insert_salvages->policy_no=$claimdets->policy_no;
                    $insert_salvages->dola=Carbon::now();
                    $insert_salvages->salvage_amount=0;
                    $insert_salvages->orig_salvage_amount =0;
                    $insert_salvages->user1=Auth::user()->user_name;
                    $insert_salvages->tendered='N';
                    $insert_salvages->retained_by_client = 'N';
                    $insert_salvages->debited = 'N';
                    $insert_salvages->status = 801;
                    $insert_salvages->salvage_type=$request->salvage_type;

                    $insert_salvages->save();

                }


            }

            ##increment serial number
            $this->updatesalvageserial();

            $returned = [
                        'sal_type'=> $request->salvage_type,
                        'serial_no'=> $serial_no,
                        'msg'=>'success'
                    ];  
            return $returned ;
        
        }catch(\Throwable $th){
           
            DB::rollback();
           
            return [
                'sal_type'=> $request->salvage_type,
                'serial_no'=> $serial_no,
                'msg'=>'error'
            ];
        }

    }

    public function generatesalvageserial($category){

        $serial = Doctype::where('doc_type','SVG')->first();
        $new_serial = str_pad((string) $category, 2, '0', STR_PAD_LEFT).str_pad((string) $serial->serial_no, 6, '0', STR_PAD_LEFT).$this->current_year;
        return  $new_serial ;
    }

    function updatesalvageserial(){
        $new_serial = Doctype::where('doc_type','SVG')->increment('serial_no',1);
        //  return $new_serial;
    }


    

    public function enquireFromTenderserial()
    {
    	$tenderserial = Tenderserial::Select('tender_no','tender_date','tender_due_date')
        ->orderBy('tender_date');

    	return Datatables::of($tenderserial)
        //Make Tender number column a link editable
        
        ->editColumn('tender_no', function ($tenderserial) {
            return '<a href="' . route('salvage.index',["tender_no"=>$tenderserial->tender_no]) .'">'.formatTenderNo($tenderserial->tender_no).'</a>';
        })
        ->editColumn('tender_date', function($tenderserial){
            return formatDate($tenderserial->tender_date);
        })
        ->editColumn('tender_due_date', function($tenderserial){
            return formatDate($tenderserial->tender_due_date);
        })
        ->setRowClass(function($tenderserial){
            return($tenderserial->tender_no ? 'clickable':' ');
        })
        ->rawColumns(['tender_no'])
        ->make(true);
    }
    public function tendered(Request $request){
        $tender_no = $request->get('tender_no');
        // $tenders = Tender::query()->Where('tender_no','=',$tender_no)->get();
        $tenders = Tender::select('s.serial_no', 's.reserve_amt','s.debited', 'tender.*')
            ->join('salvages s', 'tender.serial_no', '=', 's.serial_no')
            ->where('tender_no','=',$tender_no)->get();

        return Datatables::of($tenders)
        ->editColumn('claim_no', function($tenders){
            return formatPolicyOrClaim($tenders->claim_no);
        })
        ->editColumn('reserve_amt', function($tenders){
            $reserve_amount = $tenders->reserve_amt ?? 0;
            $edit_reserve = $tenders->debited == 'Y' ? 
            "<button class='btn-sm btn-default' style='margin-left: 10%' title='Item already debited' disabled> <i class='fa fa-ban'></i> </button>"
            :"<button class='btn-sm btn-default' style='margin-left: 10%' id='edit_expected' data_expected='$reserve_amount' data_claim='$tenders->claim_no' data_serial='$tenders->serial_no'> <i class='fa fa-pencil-square-o'></i> </button>";
            
            return "<span id='expected_frombid'>$reserve_amount</span>" . $edit_reserve;
        })
        ->addColumn('bids', function($tenders){
            $tenderbuyers = Tenderbuyers::Where([
                ['tender_no', '=', $tenders->tender_no],
                ['claim_no', '=', $tenders->claim_no],
                ['serial_no', '=', $tenders->serial_no]
            ])->count();
             if(!empty($tenderbuyers)){
                return $tenderbuyers;
             }
             else{
                return 0;
             }
             
        })
        ->addColumn('item_name', function($tenders){

            $serial = $tenders->serial_no;

            $salvages = Salvages::where('serial_no', $serial )->first();

            if($salvages->reg_no != null){

                return $salvages->reg_no;

            }else{

                return $salvages->item_name;
            }

         })
         ->addColumn('action',function($tenders) {

            $serial = $tenders->serial_no;
            $salvages = Salvages::where('serial_no', $serial)->first();
            $paid = $salvages->paid;
            $debitStatus = $salvages->debited;

            if ($paid !== 'Y') {
                return '

                    <button class="btn bg-info send_sal_invite" title="Send Invites" data_serial='.$serial.'>
                    <i class="fa fa-pencil-square-o "
                    ></i>Send Salvage Invite</button>
                        ';
            }
            else {
                return '
                <button class="btn bg-info" title="Send Invites"  disabled>
                        <i class="fa fa-pencil-square-o"
                        ></i>Send Salvage Invite</button>
                '; 
            }
        })
        ->rawColumns(['reserve_amt', 'action'])

        // ->escapeColumns(['item_name'])
        ->make(true);
    }

    public function bids(Request $request){
        $tender_no = $request->get('tender_no');
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $bids = DB::table($gb .'.tenderbuyers')
                ->join($gb .'.salvage_buyers', function($query) use($tender_no){
                    $query->on('salvage_buyers.buyer_id','=','tenderbuyers.buyer_id')
                    ->Where('tender_no','=',$tender_no);
                })
                ->select('salvage_buyers.buyer_name','tenderbuyers.claim_no','tenderbuyers.reg_no','tenderbuyers.amount_offered')
                ->orderBy('tenderbuyers.buyer_id','desc');

        return Datatables::of($bids)
        ->editColumn('claim_no', function($bids){
            return formatPolicyOrClaim($bids->claim_no);
        })
        ->editColumn('amount_offered', function($bids){
            return number_format($bids->amount_offered);
        })
        ->make(true);
    }

    
    public function veh_bids(Request $request){
        $serial_no = $request->get('serial_no');
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $bids = DB::table($gb .'.tenderbuyers')
                ->join($gb .'.salvage_buyers', function($query) use($serial_no){
                    $query->on('salvage_buyers.buyer_id','=','tenderbuyers.buyer_id')
                    ->Where('serial_no','=',$serial_no);
                })
                ->select('tenderbuyers.buyer_id','salvage_buyers.buyer_name','tenderbuyers.claim_no','tenderbuyers.reg_no','tenderbuyers.amount_offered')
                ->orderBy('tenderbuyers.buyer_id','desc');

        return Datatables::of($bids)
        ->editColumn('claim_no', function($bids){
            return formatPolicyOrClaim($bids->claim_no);
        })
        ->editColumn('amount_offered', function($bids){
            return number_format($bids->amount_offered);
        })
        ->addColumn('action', function($bids) use($serial_no){
            return 
                '
                <a target="_blank" title="Print View Letter" class="btn bg-info" href="/salvage/releaseletter/'.$bids->buyer_id.'/'.Crypt::encrypt($serial_no).'/view/print">
                    <i class="glyphicon glyphicon-print"
                    ></i>View Invite
                </a>';
        })
        ->rawColumns(['action'])
        ->make(true);
    }
    /*
    public function winners(Request $request){
        $tender_no = $request->get('tender_no');
        $jd_code = $this->jd_code;
        $dept = $this->dept;
        $job_description = Job_description::Where([
            ['dept','=',$dept],
            ['jd_code','=',$jd_code],
        ])->first();
        $winners = DB::table('testkeaimsdata.tenderbuyers')
                ->join('testkeaimsdata.salvage_buyers', function($query) use($tender_no){
                    $query->on('salvage_buyers.buyer_id','=','tenderbuyers.buyer_id')
                    ->Where([
                        ['tender_no','=',$tender_no],
                        ['winner','=','Y']
                    ]);
                })
                ->select('salvage_buyers.buyer_name','salvage_buyers.buyer_id','tenderbuyers.tender_no','tenderbuyers.claim_no','tenderbuyers.reg_no','tenderbuyers.amount_offered')
                ->orderBy('tenderbuyers.buyer_id','desc');

        return Datatables::of($winners)
        ->editColumn('claim_no', function($winners){
            return $winners->tender_no;
        })
        ->editColumn('amount_offered', function($winners){
            return number_format($winners->amount_offered);
        })
        ->addColumn('release1',function($winners) use($job_description){
            return '<button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#garage_release_1_modal" 
            data-reg-no='.$winners->reg_no.'
            data-buyer-name='.$winners->buyer_name.'
            data-buyer-id='.$winners->buyer_id.'
            data-signed-by='.$this->user_name.'
            data-signee-position='.$job_description->descriptions.'
             >Letter 1</button>' 
            .'    '.
            '<button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#garage_release_2_modal">Letter 2
            </button>';
        })
        ->rawColumns(['release1'])
        ->make(true);
    }
    */
    /*Datatable*/
    public function index(Request $request){
        $tender_no = $request->get('tender_no');
        $tender_details = Tenderserial::Where('tender_no','=',$tender_no)->first();

        //$this->tendered($tender_no);
        $tenders = Tender::Where('tender_no','=',$tender_no)->get();
        $tender_bids = Tenderbuyers::Where('tender_no','=',$tender_no)->get();

        $items_in_tender = [];

        foreach ($tenders as $tender) {
            $items_in_tender[] = $tender->serial_no;
        }

        $tender_items = Salvages::whereIn('serial_no', $items_in_tender)
        ->get();

        $ReadySalvages = Salvages::Where('tendered','N')
            ->where('retained_by_client', 'N')
            ->where('debited', 'N')
            ->whereNot('status', 803)
            // ->where('status', 802)
            // ->orWhere('status', 805)
            ->get();

        return view('gb.claims.salvage_index')->with(compact('tender_details', 'tender_bids', 'tenders', 'ReadySalvages', 'tender_items'));
    }

    
    public function vehicle_index(Request $request){
        $serial_no = $request->get('serial_no');
        $salvage_details = Salvages::Where('serial_no','=',$serial_no)->first();
        if($salvage_details->salvage_type == 3){
            $items_salvages = BundledSalvageItems::where('claim_no',$salvage_details->claim_no)
                                ->where('sal_serial_no',$request->serial_no)
                                ->count();
        }

        return view('gb.claims.salvage_vehicle_index')->with(compact('salvage_details','items_salvages'));
    }

    public function is_paid(Request $request){
        //check if insured of each salvage vehicle is paid
        try {
            $count = Clmcorr::leftJoin('perilsitems', 'clmcorr.code', '=', 'perilsitems.corrs_type')
            ->join('payreqst', function($join) {
                $join->on('payreqst.peril', '=', 'perilsitems.peril')
                    ->on('payreqst.perilitem', '=', 'perilsitems.item_no')
                    ->on('payreqst.payee_type', '=', 'perilsitems.corrs_type');
            })
            ->where('perilsitems.own_damage', 'Y')
            ->where('clmcorr.insured', 'Y')
            ->where('voucher_raised', 'Y')
            ->where('claim_no', $request->claim_no)
            ->count();

            if($count) {
                return response()->json([$count, 'message' => 'Insured Paid'] );
            } else {
                return response()->json(['error' => 'Insured Not Paid'] );
            }

            
        } catch (\Throwable $th) {
            //throw $th;
            return response()->json(['errors' => $th->getMessage()]);
        }
    }

    public function createTender(Request $request){ 

            try {
                $tender_no = $this->genTenderNo();
                $tender_date = $this->todays_date;
                $tender_due_date = $request->tender_due_date;
                $auction_date = $request->auction_date;
                
                $insert_tenderserial = new Tenderserial;
                $insert_tenderserial->tender_no=$tender_no;
                $insert_tenderserial->tender_date=$tender_date;
                $insert_tenderserial->tender_due_date=$tender_due_date;
                $insert_tenderserial->tender_month=$this->current_month;
                $insert_tenderserial->tender_year=$this->current_year;
    
                $insert_tenderserial->auction_date=$auction_date;
                $insert_tenderserial->save();

                $tender_errors = array();
                        
                for($x=0; $x<count($request->serial_no); ++$x){
                    $selected_salvage = Salvages::where('serial_no', $request->serial_no[$x])->first();
                    
                    //check if insured of each salvage vehicle is paid
                    $count = Clmcorr::leftJoin('perilsitems', 'clmcorr.code', '=', 'perilsitems.corrs_type')
                    ->join('payreqst', function($join) {
                        $join->on('payreqst.peril', '=', 'perilsitems.peril')
                            ->on('payreqst.perilitem', '=', 'perilsitems.item_no')
                            ->on('payreqst.payee_type', '=', 'perilsitems.corrs_type');
                    })
                    ->where('perilsitems.own_damage', 'Y')
                    ->where('clmcorr.insured', 'Y')
                    ->where('voucher_raised', 'Y')
                    ->where('claim_no', $selected_salvage->claim_no)
                    ->count();

                    // check whether claim_no is null
                    if($request->serial_no[$x]){

                        if($count) { 
                            // check reserve_amt
                           $selected_salvage = Salvages::where('serial_no', $request->serial_no[$x])->first();
           
                            $serial_no = $request->serial_no[$x];
                            
                            $insert_tender = new Tender;
                            $insert_tender->tender_no=$tender_no;
                            $insert_tender->claim_no=$selected_salvage->claim_no;
                            $insert_tender->reg_no=  $selected_salvage->reg_no;
                            $insert_tender->serial_no= $serial_no; //leading zeros removed
                            $insert_tender->tender_date=$tender_date;
                            $insert_tender->tender_due_date=$request->tender_due_date;
                            $insert_tender->auction_date=$request->auction_date;
            
                            $insert_tender->amount_paid=0; 
                            $insert_tender->save();
            
                            $update_salvage = Salvages::Where('serial_no','=',$request->serial_no[$x])
                            ->update(['tendered'=>'Y']);  
                        
                       }
                       else {
                           
                           $tender_errors[] = 'The Insured must be paid first before auctioning the Salvage Item - ' .$selected_salvage->reg_no;
                           // return redirect()->back()->with('error','The Insured must be paid first before auctioning the Salvage Item - ' .$reg);
                       } 

                    } 
                }

                if(count($tender_errors)){

                    return redirect()->route('salvage.index',['tender_no' => $tender_no])->with('errors', $tender_errors);
                }
                        
                Session::Flash('success','Tender '.$tender_no.' has been created');
                return redirect()->route('salvage.index',['tender_no' => $tender_no]);
    
            } catch (\Throwable $th) {    
                DB::rollback();                      
                return redirect()->back()->with('error','Failed to upload data');
            }
               
        }
    
    public function addAuctionDate(Request $request) {
        $data = $request->validate([
            'auction_date' => 'required|string',
        ]);

        try {
            $tender_serial = Tenderserial::where('tender_no', $request->tender_num)->update(['auction_date'=> $data['auction_date']]);
            $tender = Tender::where('tender_no', $request->tender_num)->update(['auction_date'=> $data['auction_date']]);

            Session::Flash('success','Auction date has been added');
            return redirect()->route('salvage.index',['tender_no' => $request->tender_num]);
        } catch (\Throwable $th) {
            DB::rollback();            
            return redirect()->back()->with('error','Failed to upload date');
        }
        

    }

    public function addVehicleToTender(Request $request){
        $tender_no =removeFormat($request->tender_no);

        try {
            $unpaidfor_sal = 0;
            for($x=0; $x<count($request->claim_no); ++$x){
                $added_salvage = Salvages::where('serial_no', $request->claim_no[$x])->first();
                //check if insured of each salvage vehicle is paid
                $count = Clmcorr::leftJoin('perilsitems', 'clmcorr.code', '=', 'perilsitems.corrs_type')
                ->join('payreqst', function($join) {
                    $join->on('payreqst.peril', '=', 'perilsitems.peril')
                        ->on('payreqst.perilitem', '=', 'perilsitems.item_no')
                        ->on('payreqst.payee_type', '=', 'perilsitems.corrs_type');
                })
                ->where('perilsitems.own_damage', 'Y')
                ->where('clmcorr.insured', 'Y')
                ->where('voucher_raised', 'Y')
                ->where('claim_no', $added_salvage->claim_no)
                ->count();

                if($request->claim_no[$x]){
                    if($count) { 
                        $getdetails = Salvages::where('serial_no',$request->claim_no[$x])->first();
        
                        $reg =$getdetails->reg_no ?? '';
                        $serial_no =  $request->claim_no[$x];
                        // $getdetails = Salvages::where('serial_no',$request->claim_no[$x])->first();
            
                        $insert_tender = new Tender;
                        $insert_tender->tender_no=$tender_no;
                        $insert_tender->claim_no=$getdetails->claim_no;
                        $insert_tender->reg_no=  $reg;
                        $insert_tender->serial_no=  $serial_no;
                        $insert_tender->tender_date=$this->getTenderserialData($tender_no)->tender_date;
                        $insert_tender->tender_due_date=$this->getTenderserialData($tender_no)->tender_due_date;
                        $insert_tender->auction_date=$this->getTenderserialData($tender_no)->auction_date;
                        $insert_tender->amount_paid=0; 
                        $insert_tender->save();
            
                        $update_salvage = Salvages::Where('serial_no','=',$request->claim_no[$x])
                        ->update(['tendered'=>'Y']);
                        
                    } else { 
                        $insured_errors[] = 'The Insured must be paid first before auctioning the Salvage Item - ' .$request->claim_no[$x];
                        $unpaidfor_sal++;
                    } 
                }
                
            }
            $vehicles_added = count($request->claim_no) - $unpaidfor_sal;

            if($vehicles_added >= 2){
                Session::Flash('success','More Vehicles have been added to tender');
            }
            if($vehicles_added == 1){
                Session::Flash('success','1 More Vehicle has been added to tender');
            }

            if($insured_errors){
                return redirect()->route('salvage.index',['tender_no' => $tender_no])->with('errors', $insured_errors);
            } 
            
            return redirect()->back();

        } catch (\Throwable $th) {
            DB::rollback();
            
            return redirect()->back()->with('error','Failed to upload data');
        }
        
    }

    public function captureBids(Request $request){

        if($request->hasfile('data')){
            //Get uploaded File Details
            $path = $request->file('data')->getRealPath();
            // $excel_data = Excel::load($path, function($reader){})->get();
            $excel_data = FileUploadManager::excelUpload($request->file('data'));
            $extension = File::extension($request->file('data')->getClientOriginalName());
            
            if($extension == "csv" || $extension == "xls" || $extension == "xlsx"){
                if(!empty($excel_data) && $excel_data->count()){
                    foreach ($excel_data as $key => $value) {
                        $value = (object) $value->toArray();
                        if($this->ifExistsBuyers($value->id)==0){
                            //If not exists insert
                            $insert_salvage_buyers = Salvage_buyers::Create([
                                'buyer_id' => $value->id,
                                'buyer_name' => $value->buyer_name,
                                'mobile_no' => $value->mobile_no,
                                'buyer_email' => $value->email,
                            ]);
                        }
                        else{ //Update Details if Exists
                            $update_salvage_buyers = Salvage_buyers::Where('buyer_id', '=', $value->id)
                            ->update([
                                'mobile_no' => $value->mobile_no,
                                'buyer_email'=>$value->email,
                            ]);
                        }
                        
                        $insert_tender_buyers[] = [
                            'tender_no' => removeFormat($request->tender_no),
                            'buyer_id' => $value->id,
                            //'date_received' => $value->date_received,
                            'reg_no' => trim($value->reg_no),
                            'amount_offered' => $value->bid,
                            'location' => $value->location,
                            'claim_no' =>$this->getSalvageData(trim($value->reg_no))->claim_no
                        ];
                        
                        
                    }
                    if ((!empty($insert_tender_buyers))) {
                        DB::transaction(function() use($insert_tender_buyers){
                            //Salvage_buyers::insert($insert_salvage_buyers);
                            Tenderbuyers::insert($insert_tender_buyers);
                        });
                        
                        if($extension == "csv"){
                            return redirect()->back()->with('success','CSV uploaded successfully');
                        }
                        elseif ($extension == "xls" || $extension == "xlsx") {
                            return redirect()->back()->with('success','Excel sheet uploaded successfully');
                        }
                        
                    }
                    else{
                        return redirect()->back()->with('error','Error encountered during upload');
                    }
                }
            }
            else if($extension != "csv" || $extension != "xls" || $extension != "xlsx" ){
                //If file is not excel
                return redirect()->back()->with('error','You can only upload .csv, .xls or .xlsx files');
            }
        }
        else{
            //if not excel sheet has been uploaded
            return redirect()->back()->with('error','Please check your excel sheet something could be wrong');
        }
    }

    # Salvages Start
    public function tenderedVehicles(Request $request) {

        $tenderedVehicles = DB::select("select b.serial_no,b.item_name,b.claim_no from tender a join salvages b
         on a.serial_no = b.serial_no where a.tender_no = '$request->tender_no' ");
        return $tenderedVehicles;
    }

    public function allBuyers(Request $request) {

        if($request->get('term')) {
            $term = $request->get('term'); // Get the search term from the request

            $all_bidders = Salvage_buyers::where('pin_no', 'LIKE', '%' . $term . '%')->get();

            $results = [];

            // return $all_bidders;
            foreach ($all_bidders as $buyer) {
                $results[] = [
                    'text' => $buyer->pin_no, // This will be shown in the autocomplete dropdown
                    'value' => "{$buyer->buyer_name}-{$buyer->pin_no}",
                    'buyer' => $buyer // Optional: if you want to display a label different from the value
                ];
            }
        
            return response()->json($results);
        }

        // populate with the bidder info
         
    }

   public function postBids(Request $request) {

        $tenderNo = $request->tender_no;
        $buyer_id = null;

        try {    
            $request->validate([
                'buyer_pin' => 'required|string',
                'buyer_name' => 'required|string|max:255',
                'buyer_email' => 'required|email|max:255',
                'mobile_no' => 'required|string|max:20',
            ]);
    
            # Check if buyer exists
            // $checkBuyer = Salvage_buyers::where('mobile_no', $request->mobile_no)->first();
            $checkBuyer = Salvage_buyers::where('pin_no', $request->buyer_pin)->first();
    
            if ($checkBuyer === null) {
                # Create new buyer
                $buyer_id = (int)Salvage_buyers::max('buyer_id');
                $buyer_id = $buyer_id + 1;
    
                $buyer = Salvage_buyers::create([
                    'buyer_id' => $buyer_id,
                    'buyer_name' => $request->buyer_name,
                    'buyer_email' => $request->buyer_email,
                    // 'pin_no'=> $validated['buyer_pin'],
                    'pin_no'=> $request->buyer_pin,
                    'mobile_no' => $request->mobile_no,
                ]);
                
            } else {
                  
                $buyer_id = $checkBuyer->buyer_id;
            }


            # Loop through all the variables set
            for($i=0; $i < count($request->serial_no); $i++) {
                # Check for duplicates
                // $checkBid = Tenderbuyers::where('tender_no', $tenderNo)->where('buyer_id', $buyer_id)->where('reg_no', $request->reg_no[$i])->first();
                // if(count($checkBid) < 0){
                $checkBid = Tenderbuyers::where('tender_no', $tenderNo)->where('buyer_id', $buyer_id)->where('serial_no', $request->serial_no[$i])->first();
                // }

                $claim_no = removePolicyOrClaimFormat($request->claim_no[$i]);
                if ($checkBid === null) {
                    # Add Bid
                    # Get Policy No
                    $clhmn = Clhmn::where('claim_no', $claim_no)->where('reg_no', $request->reg_no[$i])->first();
                    $policy_no = $clhmn->policy_no;

                    # Get Vehicle Details
                    $salvage = salvages::where('serial_no', $request->serial_no[$i])->first();
                    ##check if vehicle exists in the system
                    $vehicle = Modtlmast::where('policy_no', $policy_no)->where('reg_no', $request->reg_no[$i])->get();

                    if(count($vehicle)> 0){
                    $make  = $vehicle[0]->make;
                    $man_date  = $vehicle[0]->manufacture_year; //prev man_date
                    $location  = $vehicle[0]->location;
                    }else{
                        $make  ='';
                        $man_date  = '';
                        $location  = '';
                    }
                    
                    // $tp_vehicle = third_party_claims::where('claim_no', $claim_no)->where('tp_reg_no', $request->reg_no[$i])->first();

                    // if(count($vehicle)>0 || count($tp_vehicle)>0 ){
                        $req_no = $request->reg_no[$i];
                        if(strlen($request->reg_no[$i])> 12) {
                            $req_no = substr($req_no, 0, 11);
                        }
                        $serial_no = $request->serial_no[$i];

                        #remove commas from bid amount
                        $bid_amount = (int)str_replace(',','',$request->bid_amount[$i]);
                    // }else{
                    //     $serial_no = $request->reg_no[$i];
                    //     $req_no = '';
                    // }
                    
                    # Create Tender bid
                    $bid = Tenderbuyers::create([
                        'tender_no' => $tenderNo,
                        'buyer_id'  => $buyer_id,
                        'claim_no'  => $claim_no,
                        'reg_no'    => $req_no ,
                        'serial_no' => $serial_no ,
                        'make'      => $make,
                        'manuf_date'=> $man_date,
                        'location'  => $location,
                        'bid_datetime'  => Carbon::now(),
                        'amount_offered' => $bid_amount
                    ]);
                }
            }

            // return Session::Flash('success','Buyer and bids created successfully');
            return response()->json([
                'status'=>1,
                'message' => 'Buyer and bids created successfully',
            ]);
        } catch (\Throwable $th) {
            // return Session::Flash('error', $th->getMessage());

            return response()->json([
                'status'=>0,
                'error' => 'Failed to Upload Buyer',
                'reason' => $th->getMessage()
            ]);
        }

    }

    public function downloadTemplate(Request $request) {
        $file = public_path() . "/downloads/tender_buyers_template.xlsx";

        return Response::download($file);
    }

    public function uploadTemplate(Request $request) {
        $tender_no = $request->input('tender_no');
        $fileData = $request->file('tender_bids_file');

        if ($request->hasfile('tender_bids_file')) {
            # Get file details
            $tenderBidsFile = $request->file('tender_bids_file');

            $path = $request->file('tender_bids_file')->getRealPath();
            $fileExt = File::extension($tenderBidsFile->getClientOriginalName());
            
            $countErrors = 0;
            $errors = array();
            $reg_no_warning = "";

            if ($fileExt == 'csv' || $fileExt == 'xls' || $fileExt == 'xlsx'){
                // $excelData = Excel::load($path, function($reader) {})->get();
                $excelData = FileUploadManager::excelUpload($tenderBidsFile);

                if (!empty($excelData) && $excelData->count()) {
                    // $errors = array();
                    // $mobile_no = array();
                    
                    $count = 0;
                    $inc = 0;

                    # Check for empty cells in file
                    foreach ($excelData as $cell) {
                        $cell = (object) $cell->toArray();
                        $count = $count + 1;

                        if (empty(trim($cell->buyer_name))) {
                            $countErrors++;
                            $errors[$inc++] = "Buyer Name cannot be null in row: " . $count;
                        }
                        if (empty(trim($cell->email_address))) {
                            $countErrors++;
                            $errors[$inc++] = "Buyer E-mail cannot be null in row: " . $count;
                        }
                        if (empty(trim($cell->mobile_no))) {
                            $countErrors++;
                            $errors[$inc++] = "Mobile Number cannot be null in row: " . $count;
                        }
                        if (empty(trim($cell->buyer_pin))) {
                            $countErrors++;
                            $errors[$inc++] = "Buyer Pin No cannot be null in row: " . $count;
                        }

                        //reg_no or serial_no (one must be available)
                        if (empty(trim($cell->reg_no)) && empty(trim($cell->serial_no))) { 
                            $countErrors++;
                            $errors[$inc++] = "Both Reg_no and serial_no cannot be null in row: " . $count;
                        }
                        
                        if (empty(trim($cell->claim_no))) {
                            $countErrors++;
                            $errors[$inc++] = "Claim Number cannot be null in row: " . $count;
                        }
                        if (empty(trim($cell->bid_amount))) {
                            $countErrors++;
                            $errors[$inc++] = "Bid Amount cannot be null in row: " . $count;
                        }
                    }

                        //source of conflict, error redirect in first row
                        if ($countErrors > 0) {
                            $dat = array(
                                'main'  => 'GB',
                                'module' => 'Claims',
                                'submodule' => 'Salvage Enquiry'
                            );
    
                            return redirect()->back()->with('errors', $errors);   
                        } else {
    
                            # Start data upload
                            // DB::beginTransaction();
                            try {
                                # Check if vehicle is listed in tender
                                $count = 0;

                                # Get latest Salvage Buyer Id
    
                                $salvageId = Salvage_buyers::selectRaw('max(buyer_id) as buyer_id')->get();
                                // return $salvageId[0]->buyer_id;
                                $idSalvage = $salvageId[0]->buyer_id;
                                // return $idSalvage;
                                
                                $tryArray = array();

                                foreach ($excelData as $cell) { //since we are in each row , try adding the individual row
                                    $count = $count + 1;
                                    $result = $this->tenderRetrival($cell, $tender_no, $count);
                                    $reg_no_warning = $result['reg_no_warning'];  

                                    if ($result['countErrors'] > 0) {
                                        $errors = array_merge($errors, $result['errors']);
                                        $countErrors += $result['countErrors'];
                                    } else {

                                        # no error add each record here
                                        $buyer_id = 0;
                                        $count++;
    
                                        # Search for buyer in DB
                                        $search = Salvage_buyers::where('mobile_no', $cell['mobile_no'])->first();
    
                                        if ($search === null) {
                                            # create new user
                                            $idSalvage = (int)$idSalvage + 1;
                                            $buyer_id = $idSalvage;
                                            $salvageBuyer = Salvage_buyers::create([
                                                'buyer_id'  => $buyer_id,
                                                'buyer_name' => $cell['buyer_name'],
                                                'mobile_no'  => $cell['mobile_no'],
                                                'buyer_email' => $cell['email_address'],
                                                'pin_no' => $cell['buyer_pin'],
                                            ]);
                                        } else {
                                            # Fetch buyer's id
                                            $salvageBuyer = Salvage_buyers::where('mobile_no', $cell['mobile_no'])->first();
                                            $buyer_id = $salvageBuyer->buyer_id;
                                        }
    
                                        # Prevent Bidding on one vehicle twice by same buyer_id
                                        #if serial exists use it, otherwise use reg_no
                                        
                                        $sliced_serial_no = $cell['serial_no'] ? substr($cell['serial_no'], 1) : null ;

                                        $checkBid = Tenderbuyers::where(
                                                $cell['serial_no'] ? 'serial_no' : 'reg_no',
                                                $sliced_serial_no ?? $cell['reg_no']
                                            )
                                            ->where('buyer_id', $buyer_id)
                                            ->where('tender_no', $tender_no)->get()->count();
                                        
                                        if ($checkBid == 0) {
                                            # Add bid
                                            # Get Policy Number
                                            $claimData = Clhmn::where('claim_no', $cell['claim_no'])->first();
                                            $policy_no = $claimData->policy_no;
                                            
                                            # Fetch Vehicle details
                                            $vehicle = Modtlmast::where('reg_no', $cell['reg_no'])->where('policy_no', $policy_no)->first();
                                            
                                            #if no serial_no use reg_no
                                            $bidded_salvage = Salvages::when($cell['serial_no'], function ($query, $serial_no) {
                                                    return $query->where('serial_no', $serial_no);
                                                })
                                                ->when(!$cell['serial_no'], function ($query) use ($cell) {
                                                    return $query->where('reg_no', $cell['reg_no']);
                                                })
                                            ->first();
    
                                            # Add the bid
                                            $tenderBid = Tenderbuyers::create([
                                                'tender_no'     => $tender_no,
                                                'buyer_id'      => $buyer_id,
                                                'claim_no'      => $cell['claim_no'],
                                                'reg_no'        => $bidded_salvage->reg_no ?? $cell['reg_no'],
                                                'serial_no'     => $bidded_salvage->serial_no ?? $cell['serial_no'],
                                                'make'          => $vehicle->make,
                                                'manuf_date'    => $vehicle->man_date,
                                                'location'      => $vehicle->location,
                                                'amount_offered'=> $cell['bid_amount']
                                            ]);
                                        } 
                                        
                                    }

                                }
    
                                if ($countErrors > 0) {
                                    # Display Errors
                                    // DB::rollback();
                                    return redirect()->back()->with('errors', $errors); 
                                    // return redirect()->back()->with('error','Failed to upload some records');
                                } else {

                                    # No Errors
                                    Session::Flash('success',"All Records uploaded successfully and tender bids created " . ($reg_no_warning ? $reg_no_warning : '')); 
                                    return redirect()->back(); 
                                }

                            } catch (\Throwable $e) {
                                DB::rollback();
                                return redirect()->back()->with('error','Failed to upload data');
                            }
                        }
                    //}
                } else {
                    # Empty File
                    return redirect()->back()->with('error','The uploaded file is empty');
                }
            } else {
                # if file format not csv | xls | xlsx
                return redirect()->back()->with('error','You can only upload excel files - Check template');
            }
        } else {
            # No file choose for upload
            return redirect()->back()->with('error','No file chosen. Please Choose a file to upload');
        }
    }

    public function tenderRetrival($cell, $tender_no, $count) {

        $countErrors = 0;
        $errors = array();
        $reg_no_warning = "";

        #check if reg_no empty the use serial_no
        if (!empty(trim($cell['serial_no']))){
            if(is_numeric($cell['serial_no'])){ //check reg_no format later

                #use reg_no
                // $count++; 
                $foundSerial = Tender::where('serial_no', $cell['serial_no'])->where('tender_no', $tender_no)->first();
                $foundClm = Tender::where('claim_no', $cell['claim_no'])->where('tender_no', $tender_no)->first();

                if ($foundSerial === null) {
                    $countErrors++;
                    $errors[] = "Serial No ". $cell['serial_no'] ." is not listed in tender. Check row: " . $count;
                }

                if ($foundClm === null) {
                    $countErrors++;
                    $errors[] = "Claim Number ". $cell['claim_no'] ." is not listed in tender. Check row: " . $count;
                }
            } else {
                #use reg_no
                $reg_no_warning = " - NB: Serial Number ". $cell['serial_no'] ." did not contain number values. Used reg_no instead";
                $this->useRegNo($cell, $tender_no, $count, $countErrors, $errors, 1);
            }   
        }
        else {
            $reg_no_warning = "- NB: Serial Number was empty. Used registration no instead";
            $this->useRegNo($cell, $tender_no, $count, $countErrors, $errors, 2);
        }  
        return compact('reg_no_warning', 'countErrors', 'errors'); 
    }

    public function useRegNo($cell, $tender_no, $count, &$countErrors, &$errors, $alt_message) { //& global vars 
        if ( !empty(trim($cell['reg_no'])) ){

            if(!is_numeric($cell['reg_no']) ) {

                // $count++; 
                $foundReg = Tender::where('reg_no', $cell['reg_no'])->where('tender_no', $tender_no)->first();
                $foundClm = Tender::where('claim_no', $cell['claim_no'])->where('tender_no', $tender_no)->first();

                if ($foundReg === null) {
                    $countErrors++;
                    $errors[] = "Registration Number ". $cell['serial_no'] ." is not listed in tender. Check row: " . $count;
                }

                if ($foundClm === null) {
                    $countErrors++;
                    $errors[] = "Claim Number ". $cell['claim_no'] ." is not listed in tender. Check row: " . $count;
                }
            } 
            else {
                $countErrors++;
                $errors[] = "Reg Number ". $cell['reg_no'] ." must contain correct values. Check row: " . $count;
            }
           
        } 
        else {
            $countErrors++;
            
            if ($alt_message == 1){
                $errors[] = "Registraion Number ". $cell['reg_no'] ." must not be empty if Serial Number is incorrect. Check row: " . $count;
            }
            else if($alt_message == 2){
                $errors[] = "Registration Number ". $cell['reg_no'] ." must not be empty if Serial Number is empty. Check row: " . $count;
            }
            
        }

        // return $foundReg, $foundSerial, $countErrors, $count, $inc;
    } 

    public function processTender(Request $request) {
        $tender_no = $request->get('tender_no');

        # Get vehicles in tender
        $vehikls = Tender::where('tender_no', $tender_no)->get();

        foreach ($vehikls as $vehikl) {
            $schem = schemaName();
            $gb = $schem['gb'];
            $gl = $schem['gl'];
            $common = $schem['common'];

            DB::beginTransaction();
            
            # Rank buyers per vehikl
            try {
                $tenderProcedure = '' . $gb . '.process_tender_bids';
                $bindings = [
                    'tender_num' => trim($tender_no),
                    'reg_num' => trim($vehikl->serial_no),
                ];
    
                $resp = DB::executeProcedure($tenderProcedure, $bindings);
                DB::commit();
    
                // return $resp;
                $good_responses[] = ['vehikl' => $vehikl->serial_no, 'status' => 'success'];
            } catch (\Throwable $th) {
                DB::rollback();
    
                $error_message = $th->getMessage();
                $bad_responses[] = ['vehikl' => $vehikl->serial_no, 'status' => 'error', 'message' => $error_message];
                // $resp = false;
                // $error = 'Error. Something went wrong..., contact administrator!';
            }
        }

        if (!empty($bad_responses)) {
            return response()->json(['success' => false, 'results' => "Some Bids Failed to be Processed"], 200);
        }
        else {
            return response()->json(['success' => true, 'results' => "Tender No. $tender_no has successfully been processed."], 200);
        }

        Session::Flash('success','Tender No. '.$tender_no.' has successfully been processed.');
        return redirect()->back();
    }

    public function tenderWinners(Request $request) {
        $tender_no = $request->get('tender_no');
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
        $jd_code = $this->jd_code;
        $dept = $this->dept;
        $role = Role::Where('id',Auth::user()->role_id)->first();

        $winners = DB::table($gb .'.tenderbuyers')
            ->join($gb .'.salvage_buyers', function($query) use($tender_no) {
                $query->on('salvage_buyers.buyer_id','=','tenderbuyers.buyer_id')
                    ->Where([
                        ['tender_no','=',$tender_no],
                        ['winner','=','Y'],
                    ]);
                })
            ->select('salvage_buyers.buyer_name','salvage_buyers.buyer_id','tenderbuyers.tender_no','tenderbuyers.claim_no','tenderbuyers.reg_no','tenderbuyers.serial_no','tenderbuyers.amount_offered')
            ->orderBy('tenderbuyers.buyer_id','desc');

        return Datatables::of($winners)
        ->editColumn('claim_no', function($winners){
            return formatPolicyOrClaim($winners->claim_no);
        })
        ->editColumn('amount_offered', function($winners){
            return number_format($winners->amount_offered);
        })
        ->addColumn('release1',function($winners) use($role){
            $padded_serial_no = str_pad($winners->serial_no, strlen($winners->serial_no) + 1, '0', STR_PAD_LEFT);
            $salvage = Salvages::where('serial_no', $padded_serial_no)->where('claim_no', $winners->claim_no)->first();
            $paid = $salvage->paid;
            $debitStatus = $salvage->debited;

            if ($paid == 'Y') {
                return '

                    <button type="button" id="chng_win" class="btn btn-primary btn-xs change-winner" data-toggle="modal" disabled data-target="#change_winner" 
                    data-toggle="tooltip" data-placement="left" title="Change Winner" data-debited='.$salvage->debited.' data-debit_no='.$salvage->dtrans_no.'>
                        <span class="fa fa-exchange"></span>
                    </button>
                    
                    <button type="button" id="debit" class="btn btn-primary btn-xs debit-btn" data-toggle="modal" disabled data-make="'.$salvage->make.'" data-model="'.$salvage->model.'" data-debited='.$salvage->debited.' data-target="#create_debit" data-toggle="tooltip" data-placement="left" title="Debit Salvage"><span class="fa fa-money"></span></button>'
                ;
            } else {
                if ($debitStatus == 'Y') {
                    return '
                        
                        <button type="button" id="chng_win" class="btn btn-primary btn-xs change-winner" data-toggle="modal" data-target="#change_winner" 
                        data-toggle="tooltip" data-placement="left" title="Change Winner" data-debited='.$salvage->debited.' data-debit_no='.$salvage->dtrans_no.'>
                            <span class="fa fa-exchange"></span>
                        </button>
                        
                        <button type="button" id="debit" class="btn btn-primary btn-xs debit-btn" data-toggle="modal" disabled data-make="'.$salvage->make.'" data-model="'.$salvage->model.'" data-debited='.$salvage->debited.' data-target="#create_debit" data-toggle="tooltip" data-placement="left" title="Debit Salvage"><span class="fa fa-money"></span></button>'
                    ;
                } else {
                    return '
                        
                        <button type="button" id="chng_win" class="btn btn-primary btn-xs change-winner" data-toggle="modal" data-target="#change_winner" 
                        data-toggle="tooltip" data-placement="left" title="Change Winner" data-debited='.$salvage->debited.' data-debit_no='.$salvage->dtrans_no.'>
                            <span class="fa fa-exchange"></span>
                        </button>
                        
                        <button type="button" id="debit" class="btn btn-primary btn-xs debit-btn" data-toggle="modal" data-make="'.$salvage->make.'" data-model="'.$salvage->model.'" data-reg_no="'.$salvage->reg_no.'" data-debited='.$salvage->debited.' data-target="#create_debit" data-toggle="tooltip" data-placement="left" title="Debit Salvage"><span class="fa fa-money"></span></button>'
                    ;
                }
            }
        })
        ->rawColumns(['release1'])
        ->make(true);
    }

    public function vehicleWinners(Request $request) {
        $serial_no = $request->get('serial_no');
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
        $jd_code = $this->jd_code;
        $dept = $this->dept;
        $role = Role::Where('id',Auth::user()->role_id)->first();

        $winners = DB::table($gb .'.tenderbuyers')
            ->join($gb .'.salvage_buyers', function($query) use($serial_no) {
                $query->on('salvage_buyers.buyer_id','=','tenderbuyers.buyer_id')
                    ->where('serial_no', $serial_no)
                    ->where('winner','Y');
                })
            ->select('salvage_buyers.buyer_name','salvage_buyers.buyer_id','tenderbuyers.tender_no','tenderbuyers.claim_no','tenderbuyers.serial_no','tenderbuyers.reg_no','tenderbuyers.amount_offered')
            ->orderBy('tenderbuyers.buyer_id','desc');
        

        return Datatables::of($winners)
        ->editColumn('claim_no', function($winners){
            return formatPolicyOrClaim($winners->claim_no);
        })
        ->editColumn('amount_offered', function($winners){
            return number_format($winners->amount_offered);
        })
        ->addColumn('print',function($winners) use($role, $serial_no){
            $padded_serial_no = str_pad($winners->serial_no, strlen($winners->serial_no) + 1, '0', STR_PAD_LEFT);
            $salvage = Salvages::where('serial_no', $padded_serial_no)->where('claim_no', $winners->claim_no)->first();
            $paid = $salvage->paid;
            $debitStatus = $salvage->debited;

            if ($paid == 'Y') {
                return '

                    <a class="btn bg-info" title="Print Acceptance Letter" href="/salvage/releaseletter/'.$winners->buyer_id.'/'.Crypt::encrypt($serial_no).'/acceptance/print" target="blank">
                    <i class="glyphicon glyphicon-print "
                    ></i>Acceptance Letter</a>

                        
                    <a class="btn bg-info" title="Print Release Letter" href="/salvage/releaseletter/'.$winners->buyer_id.'/'.Crypt::encrypt($serial_no).'/release/print" target="blank">
                    <i class="glyphicon glyphicon-print"
                    ></i>Release letter</a>
                   
                    '
                ;
            }else {

                
                return $debitStatus == 'Y' ? 
                '
                    <a class="btn bg-info" title="Print Acceptance Letter" href="/salvage/releaseletter/'.$winners->buyer_id.'/'.Crypt::encrypt($serial_no).'/acceptance/print" target="blank">
                    <i class="glyphicon glyphicon-print "
                    ></i>Acceptance Letter</a>
                        
                    <a class="btn bg-info" title="Salvage not receipted" disabled="disabled">
                    <i class="glyphicon glyphicon-print"
                    ></i>Release letter</a>
                   
                '
                    : 
                '
                    <a class="btn bg-info" title="Salvage not receipted"  disabled="disabled">
                    <i class="glyphicon glyphicon-print "
                    ></i>Acceptance Letter</a>

                        
                    <a class="btn bg-info" title="Salvage not receipted" disabled="disabled">
                    <i class="glyphicon glyphicon-print"
                    ></i>Release letter</a>
                '
                ;
            }
        })
        ->rawColumns(['print'])
        ->make(true);
    }

    public function winnerDetails(Request $request) {

        $tender_no = $request->get('tender_no');
        $serial_no = $request->get('serial_no');
        $padded_serial_no = str_pad($serial_no, strlen($serial_no) + 1, '0', STR_PAD_LEFT);

        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $details = Tenderbuyers::where('tender_no', $tender_no)->where('serial_no', $serial_no)->where('winner', 'Y')->first();

        $buyer = Salvage_buyers::where('buyer_id', $details->buyer_id)->first();
        
        // $runnersup = Tenderbuyers::where('tender_no', $tender_no)->where('reg_no', $reg_no)->whereNull('winner')->get();

        $runnersup = DB::table($gb .'.tenderbuyers as u')
            // ->select(
            //     'u.buyer_id as buyer_id',
            //     'b.buyer_name as buyer_name'
            // )
            ->join($gb .'.salvage_buyers as b', 'b.buyer_id', '=', 'u.buyer_id')
            ->where('u.tender_no', $tender_no)->where('u.serial_no', $serial_no)->whereNull('u.winner')
            ->orderBy('u.winner', 'ASC')
            ->get();

        $salvageDetails = Salvages::where('serial_no', $padded_serial_no)->where('claim_no', $details->claim_no)->first();

        return json_encode([$details, $buyer, $runnersup, $salvageDetails]);
    }

    public function sendViewSalvageMail(Request $request, $serial_no){
		
        $salvage_details = Salvages::Where('serial_no','=',$serial_no)->first();
       
        $bidders = DB::table('tenderbuyers')
            ->join('salvage_buyers', function($query) use($serial_no) {
                $query->on('salvage_buyers.buyer_id','=','tenderbuyers.buyer_id')
                    ->where('serial_no', $serial_no);
                })
            ->select('salvage_buyers.buyer_name','salvage_buyers.buyer_id','tenderbuyers.tender_no','salvage_buyers.buyer_email',
                        'tenderbuyers.claim_no','tenderbuyers.reg_no','tenderbuyers.amount_offered')
            ->orderBy('tenderbuyers.buyer_id','desc')->get();
        
        $company = Pipcnam::first()->company_name;
        if(count($bidders)> 0){

            try{
                $sal_invite_mail = [];
                $no_email_array = [];
                $sent_mail = [];

                foreach ($bidders as $bidder) {
                    
                    $subject = "SALVAGE VIEW INVITATION - $salvage_details->item_name ( $serial_no )";
            
                    $address = $bidder->buyer_email;
                    $bidder_name= $bidder->buyer_name;

                    //include location name in msg body.
                    $message_body = "We are pleased to extend a special invitation to you to view a salvage that you had bid for. Find attached letter to present to the Storage/Location at ".$salvage_details->storage_name;
                    $reference = $salvage_details->item_name;

                    $sender = env('MAIL_FROM_ADDRESS');

                    if ($address) {
                        $sal_invite_mail['address'] = [$address];
                        array_push($sent_mail, $bidder_name);
                    } 
                    else {
                        $no_email_array[] = $bidder_name;
                    }

                    $sal_invite_mail['attachment_args'] = [
                        'bidder_id' => $bidder->buyer_id,
                        'serial_no' => $serial_no,
                        'reference' => $reference,
                        'view'=>'view',
                        'mail'=>'mail'
                    ];

                    $sal_invite_mail['subject'] = $subject;
                    $sal_invite_mail['message_body'] = $message_body;
                    $sal_invite_mail['sender'] = $sender;
                    $sal_invite_mail['company'] = $company;

                    if(isset($sal_invite_mail['address']) && count($sal_invite_mail['address'])){
                        $notificationData = ['sal_invite_mail'  =>  $sal_invite_mail];
                        DispatchNotificationEvent::dispatch($slug = 'salvage-invite',$notificationData);
                    }
                }

                if(count($sent_mail) && !count($no_email_array)) {
                    return 1;
                    
                }
                else if(count($sent_mail)) {
                    // return ['success' => 'Mail sent to Bidders: ' . implode(', ', $sent_mail)];
                    if (count($no_email_array) == 1) {
                        $err_message = 'No email found for bidder: ' . implode(', ', $no_email_array);
                    } 
                    elseif (count($no_email_array) > 1) {
                        $err_message = 'No emails found for bidders: ' . implode(', ', $no_email_array);
                    } 

                    return ['status' => 2, 'err_message' => $err_message];
                }


            }catch(\Throwable $e){
                $codex = json_encode($e->getMessage());
                $error = explode('\n', $codex);
                $error_msg = $error[1];
                $referrence = "salvage serial = '$serial_no' ";
                $route_name = Route::getCurrentRoute()->getActionName();
                log_error_details($route_name,$error_msg,$referrence);
                DB::rollback();
                return ['error' => $codex];
                
            }
        }else{

            return -1;

        }
	}

    public function sendReleaseLetterMail($buyer_id, $serial_no){
        $release_letter_array = [];
		
        $salvage_details = Salvages::Where('serial_no','=',$serial_no)->first();
        $bidder = DB::table('tenderbuyers')
            ->join('salvage_buyers', function($query) use($serial_no) {
                $query->on('salvage_buyers.buyer_id','=','tenderbuyers.buyer_id')
                    ->where('serial_no', $serial_no);
                })
            ->where('salvage_buyers.buyer_id', $buyer_id)
            ->select('salvage_buyers.buyer_name','salvage_buyers.buyer_id','tenderbuyers.tender_no','salvage_buyers.buyer_email',
                        'tenderbuyers.claim_no','tenderbuyers.reg_no','tenderbuyers.amount_offered')
            ->first();
            
            $subject = "SALVAGE RELEASE LETTER: $salvage_details->item_name -  $serial_no ";
    
            $address = $bidder->buyer_email;
            $message_body = "We are pleased to inform you that you emerged the winner in the bid for the subject. Find attached release letter to present to the Garage. ";
        
            $reference = $salvage_details->item_name;
            try{
                $sender = env('MAIL_FROM_ADDRESS');

                $release_letter_array['sender'] = $sender;
                $release_letter_array['address'] = [$address];
                $release_letter_array['subject'] = $subject;
                $release_letter_array['message_body'] = $message_body;   
                // $release_letter_array['company'] = $company;

                $release_letter_array['attachment_args'] = [
                    'bidder_id' => $bidder->buyer_id,
                    'serial_no' => $serial_no,
                    'reference' => $reference,
                    'release'=>'release',
                    'mail'=>'mail'
                ];  

                if(isset($release_letter_array['address']) && count($release_letter_array['address'])){
                    $notificationData = ['release_letter_array'  =>  $release_letter_array];
                    DispatchNotificationEvent::dispatch($slug = 'salvage-release-letter',$notificationData);
                }
                
                return redirect()->back()->with('success','Release letter send successfully');
        
            }catch(\Throwable $e){
                $codex = json_encode($e->getMessage());
                $error = explode('\n', $codex);
                $error_msg = $error[1];
                $referrence = "salvage serial = '$serial_no' ";
                $route_name = Route::getCurrentRoute()->getActionName();
                log_error_details($route_name,$error_msg,$referrence);
                DB::rollback();
                
                return redirect()->back()->with('error','Failed to send email');
                
            }
	}

    public function sendAcceptanceLetterMail($buyer_id, $serial_no){
        $acceptance_letter_array = [];
		
        $salvage_details = Salvages::Where('serial_no','=',$serial_no)->first();
        $bidder = DB::table('tenderbuyers')
            ->join('salvage_buyers', function($query) use($serial_no) {
                $query->on('salvage_buyers.buyer_id','=','tenderbuyers.buyer_id')
                    ->where('serial_no', $serial_no);
                })
            ->where('salvage_buyers.buyer_id', $buyer_id)
            ->select('salvage_buyers.buyer_name','salvage_buyers.buyer_id','tenderbuyers.tender_no','salvage_buyers.buyer_email',
                        'tenderbuyers.claim_no','tenderbuyers.reg_no','tenderbuyers.amount_offered')
            ->first();
            
            $subject = 'SALVAGE ACCEPTANCE LETTER: '. $salvage_details->item_name.' - ' .$serial_no;
    
            $address = $bidder->buyer_email;
            $message_body = "We are pleased to inform you that we accepted your bid for subject salvage. Find attached acceptance letter. ";
        
            $reference = $salvage_details->item_name;
            try{
                $sender = env('MAIL_FROM_ADDRESS');

                $acceptance_letter_array['sender'] = $sender;
                $acceptance_letter_array['address'] = [$address];
                $acceptance_letter_array['subject'] = $subject;
                $acceptance_letter_array['message_body'] = $message_body;   
                // $acceptance_letter_array['company'] = $company;
                $acceptance_letter_array['attachment_args'] = [
                    'bidder_id' => $bidder->buyer_id,
                    'serial_no' => $serial_no,
                    'reference' => $reference,
                    'acceptance'=>'acceptance',
                    'mail'=>'mail'
                ];   

                if(isset($acceptance_letter_array['address']) && count($acceptance_letter_array['address'])){
                    $notificationData = ['acceptance_letter_array'  =>  $acceptance_letter_array];
                    DispatchNotificationEvent::dispatch($slug = 'salvage-acceptance-letter',$notificationData);
                }
                
                return redirect()->back()->with('success','Acceptance letter send successfully');
        
            }catch(\Throwable $e){
                $codex = json_encode($e->getMessage());
                $error = explode('\n', $codex);
                $error_msg = $error[1];
                $referrence = "salvage serial = '$serial_no' ";
                $route_name = Route::getCurrentRoute()->getActionName();
                log_error_details($route_name,$error_msg,$referrence);
                DB::rollback();
                
                return redirect()->back()->with('error','Failed to send email');
                
            }
	

	}

    // salvage memo documents

    public function getSalvageDocs($claim_no) {

        //need to acquire reg_no or chassis_no| prev code used claim_no

        // $salvage_dets = explode(",", $salvage_dets);
        // if ($salvage_dets[1] || $salvage_dets[2]) {
        //     $checklistItems = Salvage_checklist::where('motor', 'Y')->get();
        // }
        // else {
        //     $checklistItems = Salvage_checklist::where('motor', 'N')->get();
        // }

        try {

            $class_no = Clhmn::where('claim_no', $claim_no)->pluck('class')->first();

            $class = ClassModel::where('class', $class_no)->first();

          if($class->motor_policy == 'Y'){
            $checklistItems = Salvage_checklist::where('motor', 'Y')
                ->where('status', 'Y')
                ->get();

          }
            // if (in_array((int)$class_no, $motor_classes)) {  // Convert to integer for comparison
            //     $checklistItems = Salvage_checklist::where('motor', 'Y')->get();
             else {
                $checklistItems = Salvage_checklist::where('non_motor', 'Y')
                    ->where('status', 'Y')
                    ->get();
            }

            // dd('docs', $class_no, $claim_no, $checklistItems);

            foreach ($checklistItems as $checklistItem) {
                $memoItems = SalvageMemoItems::where('claim_no', $claim_no)
                                            ->where('salvage_docs_id', $checklistItem->id)
                                            ->first();

                if (!$memoItems) {
                    SalvageMemoItems::create([
                        'claim_no' => $claim_no,
                        'serial_no' => '', // Set appropriate default values
                        'salvage_docs_id' => $checklistItem->id,
                        'received' => 'N',
                        'comments' => '',
                        'updated_by' => auth()->user()->user_name ?? '', // Default user ID or modify as necessary
                        'updated_at' => Carbon::now(),
                    ]);
                }
            }

            // Fetch all SalvageChecklist items with associated SalvageMemoItems for the specific claim
            $salvage_docs = Salvage_checklist::with(['memo_items' => function ($query) use ($claim_no) {
                $query->where('claim_no', $claim_no);
            }])->whereIn('id', $checklistItems->pluck('id'))->get();

            return compact('salvage_docs', 'claim_no');

        } catch (\Throwable $th) {

            $error_message = array(
                'message' => 'Error Getting Docs!',
                'status'=>0,
                'details'=>$th
            );
            return response()->json($error_message, 500);
        }
    }

    public function updateSalvageDocs(Request $request) {

        $data = $request->validate([
            'claim_no' => 'required|string',
            'serial_no' => 'required|string',
            'updated_by' => 'required|string',
            'documents' => 'required|array',
            'documents.*.id' => 'required|integer|exists:salvage_checklist,id',
            'documents.*.received' => 'required|string|size:1|in:Y,N',
            'documents.*.comments' => 'nullable|string',
        ]);

        try {
            foreach ($data['documents'] as $docData) {
                SalvageMemoItems::where(
                'claim_no', $data['claim_no'],                    
                )
                ->where('salvage_docs_id', $docData['id'])
                ->update([
                    'serial_no' => $data['serial_no'],
                    'received' => $docData['received'],
                    'comments' => $docData['comments'],
                    'updated_by' => $data['updated_by'],
                    'updated_at' => now()
                ]);
            }
    
            return redirect()->back()->with('success', 'Salvage documents updated successfully.');
        } catch (\Throwable $th) {
            return redirect()->back()->withErrors([
                'error' => 'Error updating documents: ' . $th->getMessage(),
            ]);
        }
    }

    //print salvage_docs as pdf
    public function generatePdfMemo($claim_no)
    {
        $parts_array = explode('&', $claim_no);

        $claim_no = $parts_array[0]; 
        $serial_no = $parts_array[1];

        $salvage_item = Salvages::where('claim_no', $claim_no)->first();

        $class_no = Clhmn::where('claim_no', $claim_no)->pluck('class')->first();
        $class = ClassModel::where('class', $class_no)->first();

        if($class->motor_policy == 'Y'){
            
            $salvage_docs = Salvage_checklist::with(['memo_items' => function ($query) use ($claim_no) {
                $query->where('claim_no', $claim_no);
            }])->where('motor', 'Y')->where('status', 'Y')->get();
        }
        else {

            $salvage_docs = Salvage_checklist::with(['memo_items' => function ($query) use ($claim_no) {
                $query->where('claim_no', $claim_no);
            }])->where('non_motor', 'Y')->where('status', 'Y')->get();

        }

        $req_claim = Clhmn::where('claim_no', $claim_no)->first();
        $user_role = Role::Where('id',Auth::user()->role_id)->first();

        $f_name = Auth::user()->first_name;
        $l_name = Auth::user()->last_name;

        $user_names = $f_name .' '.$l_name;
        
        $data = [
            'insured' => $req_claim->policyholder,
            'claim_no' => $claim_no,
            'updated_by' => $user_names,
            'role_name' => $user_role->name,
            'salvage_item_identifier' => $salvage_item->reg_no ?? $salvage_item->item_name,
            'serial_no' => $serial_no,
            'salvage_docs' => $salvage_docs,
            'is_motor' => $salvage_item->reg_no ? 'Y' : 'N'
        ];

        $file = New pdfController();

        return $file->load_salvage_items($data);
    }

    // generate Excel file
    public function generateExcelMemo($claim_no)
    {
        $salvage_docs = Salvage_checklist::with(['memo_items' => function ($query) use ($claim_no) {
            $query->where('claim_no', $claim_no);
        }])->where('motor', 'Y')->get();
    
        // $templatePath = storage_path('public/downloads/Memo_template.xlsx');
        // return (new SalvageMemoExport($salvageDocs, $templatePath))->download('SalvageMemo.xlsx');
        // $export = new SalvageMemoExport($salvage_docs);
        return new SalvageMemoExport($salvage_docs);
    }


    public function changeWinner(Request $request) {
        $tender_no = $request->tender_no;
        $reg_no = $request->reg_no;
        $buyer_id = $request->bidders;
        $change_reason = $request->change_reason;
        $claim_no = $request->claim_no;
        $serial_no = $request->serial_no;
        $padded_serial = str_pad($serial_no, strlen($serial_no) + 1, '0', STR_PAD_LEFT);

        # Remove Existing Winner
        $old_winr = Tenderbuyers::where(trim('tender_no'), $tender_no)->where(trim('serial_no'), $serial_no)->where('winner', 'Y')
        ->update([
            'winner' => null
        ]);

        # Add new winner
        $new_winr = Tenderbuyers::where(trim('tender_no'), $tender_no)->where(trim('serial_no'), $serial_no)->where('buyer_id', $buyer_id)
        ->update([
            'winner' => 'Y',
            'change_reason' => $change_reason
        ]);

        $salB = Salvage_buyers::where('buyer_id', $buyer_id)->first();

        $salvage = Salvages::where('serial_no', $padded_serial)->where('claim_no', $claim_no)->first();
        
        # Update Winner Details
        $updateSalvage = Salvages::where('serial_no', $padded_serial)->where('claim_no', $claim_no)->update([
            'buyer_id' => $salB->buyer_id
        ]);

        if ($salvage->debited == 'N') {
            Session::Flash('success', 'Winner for vehicle. '.$reg_no.' has successfully been updated.');
        } else {
            # Reassign the Debit Note to new Winner
            $slvB = $salB->buyer_name;
            $dtrans_no = $salvage->dtrans_no;

            $creditclm =  Creditclm::where('claim_no', $claim_no)
                ->where('dtrans_no', $dtrans_no)
                ->update([
                    'gross_amount'    => $salvage->salvage_amount,
                    'nett_amount'     => $salvage->salvage_amount,
                    'user_str'        => Auth::user()->user_name,
                    'details'         => 'SALVAGE SERIAL '. $padded_serial. ' SOLD TO '.strtoupper($slvB),
                    'payee'           => strtoupper($slvB),
                    'payee_addr1'     => $salB->address,
                ]);

            Session::Flash('success', 'Winner for Salvage. '.$reg_no.' has successfully been updated.');
            Session::Flash('info', 'Debit note for Salvage. '.$reg_no.' has been re-assigned to ' . $salB->buyer_name);
        }
        return redirect()->back();
    }

    public function salvageVehikls() {
       
        $vehikls = DB::select("Select * from salvages where tendered = 'N' and retained_by_client = 'N' and debited= 'N' 
        and (status = 801 or status = 802 or status = 805 ) ");

        return $vehikls;        
    }

    
    public function salvageContractor(Request $request) {
    
        $scontr = DB::table('salvage_contractors')->where('id',$request->id)->first();
         return response()->json(['scontr'=>$scontr]);
    }


    public function salvageOwner(Request $request) {
        $reg_no = $request->serial_no;
        $claim_no = $request->claim_no;

        $salvage = Salvages::where('claim_no', $claim_no)->first();
        $policy_no = $salvage->policy_no;
        $salvage_amount = $salvage->salvage_amount;

        # Get owner
        $polmaster = Polmasterend::where('policy_no', $policy_no)->first();
        $client_number = $polmaster->client_number;
        $client_name = $polmaster->name;

        $client = Client::where('client_number', $client_number)->first();
        $client_telephone = $client->telephone;
        $client_email = $client->e_mail;

        return $client;
    }

   
    public function retainSalvage(Request $request) {
        
        $serial_no = $request->salvage_serial_no;
        $claim_no = $request->claim_no;
        $salvage_amount = $request->salvage_amount;

        $sellTo = $request->sell_to;
        $policy_no = $request->policy_no;

        $client_name = $request->buyer_name;
        $client_telephone = $request->phone_number;
        $client_email = $request->email;

        // checking if insured is paid
        $count = Clmcorr::leftJoin('perilsitems', 'clmcorr.code', '=', 'perilsitems.corrs_type')
            ->join('payreqst', function($join) {
                $join->on('payreqst.peril', '=', 'perilsitems.peril')
                    ->on('payreqst.perilitem', '=', 'perilsitems.item_no')
                    ->on('payreqst.payee_type', '=', 'perilsitems.corrs_type');
            })
            ->where('perilsitems.own_damage', 'Y')
            ->where('clmcorr.insured', 'Y')
            ->where('voucher_raised', 'Y')
            ->where('claim_no', $claim_no)
            ->count();

        if($sellTo == 'C'){

            $contractorid = $request->contr_name;
        }else{

            $contractorid = '';
        }
        try {

        # Check if client exists in Salvage Buyers
        $checkBuyer = Salvage_buyers::where('mobile_no', trim($client_telephone))->first();

        if ($checkBuyer) {
            # Get Buyer Details
            $salvageBuyer = Salvage_buyers::where('mobile_no', trim($client_telephone))->first();
            
        } else {

            if ($sellTo == 'C') {

                #check if insured is paid for contractor
                if ($count) { 
                    $getcontrname= DB::select("select * from salvage_contractors where id = '$request->contr_name' ");
                    $client_name=$getcontrname[0]->name;
                    $client_telephone=$request->phone_number;
                    $client_email=$request->email;
                }
                else {
                    Session::Flash('error', 'Insured is not paid!');
                    return redirect()->back();
                }
            }

            $this->createbuyer($client_name,$client_telephone,$client_email, $sellTo);
          
        }
            if ($sellTo == 'I') {
                # Salvage Retention by Client

                $salvageBuyer = Salvage_buyers::where('mobile_no', trim($client_telephone))->first();

                $salvageVehikl = Salvages::where('claim_no', $claim_no)->where('serial_no', $serial_no)->update([
                    'status' => 804,
                    'retained_by_client' => 'Y',
                    'buyer_id' => $salvageBuyer->buyer_id,
                    'buyer_email' => $salvageBuyer->buyer_email,
                    'winner' => 'Y',
                    'sold_to' => $sellTo,
                    // 'date_sold' => Carbon::now()
                ]);
                
                Session::Flash('success', 'Item '.$serial_no.' has successfully been retained by client');
                return redirect()->back();
            } 
            else if ($sellTo !== 'I') {

                #check if insured is paid for other
                if($count) { 

                    $sVehikl = Salvages::where('serial_no', $serial_no)->whereRaw("trim(claim_no)='" . $claim_no . "'")->count();
                    $salvageBuyer = Salvage_buyers::where('mobile_no', trim($client_telephone))->first();     
                    
                    $salvageVehikl = Salvages::where('claim_no', $claim_no)->where('serial_no', $serial_no)->update([
                        'status' => 803,
                        'buyer_id' => $salvageBuyer->buyer_id,
                        'buyer_email' => $salvageBuyer->buyer_email,
                        'winner' => 'Y',
                        'retained_by_client' => 'N',
                        'sold_to' => $sellTo,
                        'contractor_id' => $contractorid,
                    ]);
                }
                else {
                    Session::Flash('error', 'Insured is not paid!');
                    return redirect()->back();
                }
         
            }
        
                Session::Flash('success', 'Item '.$serial_no.' has successfully been Booked');
                return redirect()->back();

            } catch (\Throwable $th) {
                Session::Flash('error', 'Fail to book item');
                return redirect()->back();
            }
    }

    public function createbuyer($client_name,$client_telephone,$client_email, $sellTo){
        
        try {
            # Create a new buyer
            $count = Salvage_buyers::max('buyer_id');
            $count = $count + 1;
            $salvageBuyer = Salvage_buyers::create([
                'buyer_id' => $count,
                'buyer_name' => $client_name,
                'mobile_no' => $client_telephone,
                'buyer_email' => $client_email,
            ]);

        } catch (\Throwable $th) {
            throw $th;
        }
                
                     
                   
                     
    }

    public function salvageDirectSold() {
        // $directSold = Salvages::whereNull('receipt_no')->where('tendered', '<>', 'N')->orWhere('status', 803)->orWhere('status', 804)->get();
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $directSold = DB::table( $gb .'.salvages as veh')
            // ->select(
            //     'veh.buyer_id as buyer_id',
            //     'veh.claim_no as claim_no',
            //     'veh.reg_no as reg_no',
            //     'veh.make as make',
            //     'veh.model as model',
            //     'veh.salvage_amount as salvage_amount',
            //     'veh.retained_by_client as retained_by_client',
            //     'sab.buyer_name as buyer_name'
            // )
            ->join($gb . '.salvage_buyers as sab', 'sab.buyer_id', '=', 'veh.buyer_id')
            ->whereNull('veh.receipt_no')
            ->where('veh.tendered', '<>', 'Y')
            ->orWhere('veh.status', 803)
            ->orWhere('veh.status', 804)
            ->get();

            
    	return Datatables::of($directSold)
        ->editColumn('claim_no', function($directSold){
            return formatPolicyOrClaim($directSold->claim_no);
        })
        ->editColumn('salvage_amount', function($directSold){
            return number_format($directSold->salvage_amount);
        })
        ->editColumn('debited', function($directSold){
            if ($directSold->debited == 'Y') {
                return 'fa-check text-success';
            } else {
                return 'fa-times text-danger';
            }
        })
        
        ->editColumn('reg_no', function($directSold){
            if ($directSold->reg_no == null) {
                return $directSold->serial_no;
            } else {
                return $directSold->reg_no;
            }
        })
        ->editColumn('chassis_no', function($directSold){
            if ($directSold->chassis_no == null) {
                return '-';
            } else {
                return $directSold->chassis_no;
            }
        })
        
        ->addColumn('action', function($directSold){
            if ($directSold->debited == 'Y') {
                return '
                 <a href="/print_salvage_debit/'. encrypt($directSold->claim_no) .'/'. encrypt($directSold->entry_type_descr) .'/'. encrypt($directSold->dtrans_no) .'" target="blank">
                 <i class="glyphicon glyphicon-print btn bg-info"
                 ></i></a>
                 ';
            } else {
                return '
                <button type="button" id="cancel_debit" class="btn btn-xs btn-primary canceldebit-btn" 
                 data-serial_no="'.$directSold->serial_no.'"
                 data-reg_no="'.$directSold->reg_no.'"
                 data-claim_no="'.$directSold->claim_no.'"
                 
                 > Cancel Sale </button>
                <button type="button" id="debit_btn" class="btn btn-xs btn-primary debit-btn" data-debited="'.$directSold->debited.'" 
                    data-make="'.$directSold->make.'"
                    data-model="'.$directSold->model.'"
                    data-reg_no="'.$directSold->reg_no.'"
                    data-item_name="'.$directSold->item_name.'"
                > Debit </button>';
            }
        })
        ->make(true);
    }


    public function salvageAmount() {
        $salvages = Salvages::whereNull('receipt_no')->where('debited', 'N')->Where('status', '802')->orWhere('status', '805')->get();

        return Datatables::of($salvages)
        ->editColumn('claim_no', function($salvages){
            return formatPolicyOrClaim($salvages->claim_no);
        })
        ->editColumn('orig_salvage_amount', function($salvages){
            return number_format($salvages->orig_salvage_amount);
        })
        ->editColumn('salvage_amount', function($salvages){
            return number_format($salvages->salvage_amount);
        })
        ->addColumn('action', function($salvages){
            $checkAudit = Salvage_audit_trail::where('claim_no', $salvages->claim_no)->where('claim_item', $salvages->reg_no)->count();

            if ($checkAudit > 3) {
                return '<button type="button" class="btn btn-default btn-xs btn-primary" id="amend_btn" disabled style="color: white!important"><i class="fa fa-pencil-square-o"></i> Amend</button>';
            } else {
                return '<button type="button" class="btn btn-default btn-xs btn-primary" id="amend_btn" data-toggle="modal" data-target"#amend_reserve" style="color: white!important"><i class="fa fa-pencil-square-o"></i> Amend</button>';
            }          
        })
        ->make(true);
    }

    public function editsalvage(Request $request){


        $claim_no = $request->claim_num;
        $policy_no = $request->policy_no;
        $reg_no = $request->reg_no;
        $storage = $request->storage_name;
        $serial_no = $request->serial_no;
        // return $request;
        $claim_no = removePolicyOrClaimFormat($claim_no);
        $policy_no = removePolicyOrClaimFormat($policy_no);

        $salvageDetails = Salvages::where('claim_no', $claim_no)
        ->where('serial_no', $serial_no)->first();

        //tracking columns to be changed
        $changable = [
            'preacc_val'=> str_replace(',','',  $request->pre_val),
            'salvage_amount'=> str_replace(',','',$request->amount),
            'storage_name'=> $storage,
            'garage_name'=> $request->garage,
            'is_vandalized' => $request->is_vandalized,
            'vandalism_amt' => $request->vandalized_amount,
            'vandalism_desc' => $request->vandalism_description ,
        ];

        $changed_keys = [];
        $original_values = [];  
        $new_values = [];

        foreach ($changable as $key => $new_value) {
            $original_value = $salvageDetails->{$key};

            if ((string) $new_value !== (string) $original_value) {
                $changed_keys[] = $key;
                $original_values[] = $original_value;
                $new_values[] = $new_value;
            }
        }

        // end of tracking columns to be changed

        $editsalvage = Salvages::where('claim_no', $claim_no)
            ->where('serial_no', $serial_no)
            ->update([
            'preacc_val'=> str_replace(',','',  $request->pre_val),
            'salvage_amount'=> str_replace(',','',$request->amount),
            'storage_name'=> $storage,
            'garage_name'=> $request->garage,
            'is_vandalized' => $request->is_vandalized,
            'vandalism_amt' => $request->vandalized_amount,
            'vandalism_desc' => $request->vandalism_description ,

        ]);
        
        try{
            $salvageAudit = Salvage_audit_trail::create([
                'claim_no' => $claim_no,
                'policy_no' => $policy_no,
                'claim_item' => $reg_no,
                'table_name' => 'Salvages',
                'field_changed' => json_encode($changed_keys),  
                'old_value' => json_encode($original_values),
                'new_value' => json_encode($new_values),
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                
                'system_user' => Auth::user()->user_name,
                'change_reason' => 'Update',
            ]);
            Session::flash('success','Update Successful' );
            return redirect()->back();
        }catch (\Throwable $e) {
            DB::rollback();
            $salvageAudit = false;
            Session::flash('error','Failed to Update' );
            return redirect()->back();
        } 
    }

    public function amendReserve(Request $request) {
        $claim_no = $request->amend_claim_no;
        $claim_no = removePolicyOrClaimFormat($claim_no);

        $reg_no = $request->amend_reg_no;
        $amount = $request->amend_salvage_amount;
        $changeReason = $request->reserve_reason;

        # Update Reserve Amount
        $updateReserve = Salvages::where('claim_no', $claim_no)->where('reg_no', $reg_no)->update([
            'salvage_amount' => $amount
        ]);

        $salvageDetails = Salvages::where('claim_no', $claim_no)->where('reg_no', $reg_no)->first();

        # Log For Audit Trail
        $salvageAudit = Salvage_audit_trail::create([
            'claim_no' => $salvageDetails->claim_no,
            'policy_no' => $salvageDetails->policy_no,
            'claim_item' => $salvageDetails->reg_no,
            'table_name' => 'Salvages',
            'field_changed' => 'salvage_amount',
            'old_value' => $salvageDetails->orig_salvage_amount,
            'new_value' => $salvageDetails->salvage_amount,
            'date_changed'=>Carbon::now('EAT'),
            'ip_address'=>$request->ip(),
            'system_user' => Auth::user()->user_name,
            'change_reason' => $changeReason,
        ]);

        Session::Flash('success', 'Reserve Amount for vehicle '.$reg_no.' updated successfully.');
        return redirect()->back();
    }

    public function allSalvages() {
        $salvages = Salvages::all();
        $user_name= Auth::user()->user_name;
        $edpermission = Aimsuprofgb::where('aims_user',  $user_name)->first();
        
        return Datatables::of($salvages)
        ->editColumn('claim_no', function($salvages){
            return formatPolicyOrClaim($salvages->claim_no);
        })
        ->editColumn('policy_no', function($salvages){
            return formatPolicyOrClaim($salvages->policy_no);
        })
        
       
        
        ->editColumn('salvage_amount', function($salvages){
            return number_format($salvages->salvage_amount);
        })
        ->editColumn('chassis_no', function($salvages){
            if($salvages->chassis_no){
                return $salvages->chassis_no;
            }
            else {
                return '-';
            }
        })
        
        
        ->editColumn('sold_to', function($salvages){
            if($salvages->tendered == 'Y'){
                return 'Other';
            }else if($salvages->sold_to == 'I' || $salvages->retained_by_client == 'Y'){
                return 'Insured';
            }else if($salvages->sold_to == 'C'){
                return 'Contractor';
            }else if($salvages->sold_to == 'O'){
                return 'Other';
            }
        })
        ->editColumn('preacc_val', function($salvages){
            return number_format($salvages->preacc_val);
        })
        // ->editColumn('garagename', function($salvages){
        //     $garage=Clparam::where('claimant_code',trim($salvages->garage_name))->first();
        //     return $garage->name;
        //     // return trim($salvages->garage_name);
        // })
        
        ->addColumn('edit', function($salvages) use(&$edpermission){ 
            // $garagecode = $salvages->garage_name;   //data-garagecode="'.$garagecode.'"
            $storage = $salvages->storage_name; 
            $vandalized_amt = $salvages->vandalism_amt; 
            $vandalism_desc = $salvages->vandalism_desc; 
            if(Gate::check('amend-salvage-vehicles')){
                
                $view_salvage = $salvages->tendered == 'Y' ? 
                '<a href="'.route('salvage_vehicle.index', ['serial_no'=>$salvages->serial_no]).'" class="btn btn-default btn-sm" style="cursor:pointer;"><i class="fa fa-eye"></i>View</a>' 
                : '';

                return  $salvages->debited === 'N' ? 
                    '<a class="editsalvage btn btn-default btn-sm" data-storagename="'.$storage.'" data-vndlamt="'.$vandalized_amt.'" data-vndldesc="'.$vandalism_desc.'" data-policyno="'.$salvages->policy_no.'" style="cursor:pointer;"><i class="fa fa-edit"></i>edit</a>'.$view_salvage
                    : $view_salvage ;
            }
            else{
                return '<a class="perissiondeny" style="cursor:pointer;"><i class="fa fa-edit"></i>edit</a>';
            }
                
        })
        ->editColumn('item_name', function($salvages){
            // return $salvages->reg_no

            if($salvages->reg_no != null){
                // return 'ok';
                 return $salvages->reg_no;
            }else{
                 return $salvages->item_name;
            }
 
         })

        ->rawColumns(['edit'])
        ->escapeColumns(['item_name'])
        ->make(true);
    }

    // -- Hide if no permissions ||| VERY IMPORTANT NOT TO BE SEEN 
    public function getExpectedAmtInSalvages() {
        $salvages = Salvages::all(); // Or any specific logic to fetch the required data
    
        return Datatables::of($salvages)
            ->editColumn('claim_no', function($salvages) {
                return formatPolicyOrClaim($salvages->claim_no);
            })
            ->editColumn('policy_no', function($salvages) {
                return formatPolicyOrClaim($salvages->policy_no);
            })
            ->editColumn('salvage_amount', function($salvages) {
                return number_format($salvages->salvage_amount);
            })
            ->editColumn('preacc_val', function($salvages) {
                return number_format($salvages->preacc_val);
            })
            ->editColumn('expected_amt', function($salvages) {
                return number_format($salvages->reserve_amt);
            })
            ->editColumn('chassis_no', function($salvages) {
                if($salvages->chassis_no) {
                    return $salvages->chassis_no;
                }
                else {
                    return '-';
                }
                
            })
            //expected_amt...hide if no permissions
            ->addColumn('edit_expected', function($salvages){
                //if permissions given
                return $salvages->debited === 'Y' ? '': '<a data-bs-toggle="modal" data-bs-target="#edit_expected_amt" class="edit_expected_amt btn btn-default btn-sm" data-expectedamt="'.$salvages->reserve_amt.'" data-policyno="'.$salvages->policy_no.'" style="cursor:pointer;"><i class="fa fa-edit"></i>edit</a>';                
            })
            ->rawColumns(['edit_expected'])
            ->make(true);
    }

    public function amendExpected(Request $request) {
        if(Gate::check('capture-salvage-reserve-amount')){

            $expected_amount = (int)str_replace(',','',$request->expected_amount);
            $claim_no = str_replace('/','',$request->amend_claim_no);
            $prev_amount = (int)str_replace(',','',$request->prev_expected_amount);
            $is_from_tender = $request->from_tender;

            DB::beginTransaction();
            try {

                
                DB::table('salvages')
                ->where('serial_no', $request->amend_serial_no)
                ->where('claim_no', $claim_no)
                ->update(['reserve_amt' => $expected_amount]);

                DB::commit();

                $salvageDetails = Salvages::where('claim_no', $claim_no)->where('serial_no', $request->amend_serial_no)->first();
                try {
                    
                    # Log For Audit Trail
                    $salvageAudit = Salvage_audit_trail::create([
                        'claim_no' => $salvageDetails->claim_no,
                        'policy_no' => $salvageDetails->policy_no,
                        'claim_item' => $salvageDetails->serial_no,
                        'table_name' => 'Salvages',
                        'field_changed' => 'salvage_amount',
                        'old_value' => $prev_amount,
                        'new_value' => $salvageDetails->reserve_amt,
                        'date_changed'=>Carbon::now('EAT'),
                        'ip_address'=>$request->ip(),
                        'system_user' => Auth::user()->user_name,
                        'change_reason' => 'N/A',
                    ]);
                } catch (\Throwable $th) {
                    //throw $th;
                    Session::flash('error', 'Audit trail not logged');   
                }
        
                if ( $is_from_tender && $is_from_tender == 'Y') {
                    return response()->json(['success'=> true, 'results' => 'Reserve Amount Edited Successfully'] );
                }
                else {
                    Session::flash('success', 'Expected Amount updated successfully');   
                    return redirect()->back();
                }
        
            } catch (\Throwable $th) {
                DB::rollback();

                if ( $is_from_tender && $is_from_tender == 'Y') {
                    return response()->json(['success'=> false, 'results' => 'Failed to update the Reserve Amount'] );
                }
                else {
                    Session::flash('error', "Failed to update the Expected Amount - ".$th->getMessage());
                    return redirect()->back();
                }

            }
        } else {
            abort(403);
        }
    }


    public function salvageDebit(Request $request) {
        DB::beginTransaction();
        try {
            //code...

        $claim_no = $request->vhk_claim_no;
        $doc_type = $request->doc_type;
        $trans_type = $request->trans_type;

        $payee = $request->payee_name;
        $amount = $request->amount_payable;
        $reg_no = $request->vhk_reg;
        $retained = $request->retained;
        // $tender_no = $request->vhk_tender_no;

        $claim_no = removePolicyOrClaimFormat($claim_no);
        // $tender_no = removeFormat($tender_no);

        # Claim Details
        $clhmn = Clhmn::where('claim_no', $claim_no)->first();

        # Salvage Details
        
        // $tender = Tender::where('tender_no',$tender_no)
        //     ->where('serial_no', $reg_no)
        //     ->where('claim_no', $claim_no)
        //     ->first();

        # Salvage Details
        $salvage = Salvages::where('reg_no', $reg_no)
        ->whereRaw("trim(claim_no)='" . $claim_no . "'")
        ->first();

        $padded_serial_no = str_pad($reg_no, strlen($reg_no) + 1, '0', STR_PAD_LEFT);
        if($salvage == null) {
            $salvage = Salvages::where('serial_no', $padded_serial_no)
                ->whereRaw("trim(claim_no)='" . $claim_no . "'")
                ->first();
        } 

        // if($salvage != null){
        //     $salvage = Salvages::where('reg_no', $reg_no)
        //     ->whereRaw("trim(claim_no)='" . $claim_no . "'")
        //     ->first();

        // }else{
        //     $salvage = Salvages::where('serial_no', $reg_no)
        //     ->whereRaw("trim(claim_no)='" . $claim_no . "'")
        //     ->first();
   
        // }

        if ($salvage->tendered == 'Y') {# Salvage Buyers
            $salvage_buyers = Tenderbuyers::where([
                    ['tender_no', '=', str_replace('/','',  $request->vhk_tender_no)],
                    ['claim_no', '=', $claim_no],
                    ['serial_no', '=', $salvage->serial_no]
            ])->first();
            $salvage_buyers = Salvage_buyers::where('buyer_id', $salvage_buyers->buyer_id)->first();
        }else{
            # Salvage Buyers
            $salvage_buyers = Salvage_buyers::where('buyer_id', $salvage->buyer_id)->first();
        }

        # Polmaster Details
        $polmaster = Polmaster::where('policy_no', $clhmn->policy_no)->first();

        # Polsect  Details
        $polsect = Polsect::where('policy_no', $polmaster->policy_no)->where('endt_renewal_no', $polmaster->endorse_no)->first();

        # Dcontrol
        $dcontrol = Dcontrol::where('policy_no', $polmaster->policy_no)->where('endt_renewal_no', $polmaster->endorse_no)->first();

        # Dtran0
        $dtran = Dtran0::all();
        $dtran = $dtran[0];

        $old_debit_no = $dtran->debit_no;
        $debit_no = $old_debit_no + 1;

        if($request->taxableyes == 'Y'){

            $unallo = str_replace(',','', $request->amountwith_tax);
        }else{

            $unallo = str_replace(',','',  $request->amount_payable);
        }

        # Create Debit
        $creditclm = new Creditclm;

        $creditclm->dr_cr           = 'D';
        $creditclm->doc_type        = 'DRN';
        $creditclm->dtrans_no       = $debit_no;
        $creditclm->account_year    = $clhmn->period_year;
        $creditclm->account_month   = $clhmn->period_month;
        $creditclm->claim_no        = $clhmn->claim_no;
        $creditclm->currency_code        = $polsect->currency_code;

        $creditclm->policy_no       = $polmaster->policy_no;
        $creditclm->endt_renewal_no = $polmaster->endorse_no;
        $creditclm->uw_year         = $polmaster->uw_year;
        $creditclm->class           = $clhmn->class;
        $creditclm->dola            = Carbon::now();

        $creditclm->branch          = $polmaster->branch;
        $creditclm->agent_no        = $polmaster->agent_no;
        $creditclm->effective_date  = $dcontrol->effective_date;
        $creditclm->tran_no         = $polmaster->tran_no;
        $creditclm->sum_insured     =str_replace(',','',  $polmaster->sum_insured);

        $creditclm->gross_amount    = str_replace(',','', $request->amount_payable);
        $creditclm->nett_amount     = $unallo;
        $creditclm->allocated       = 0;
        $creditclm->unallocated     = $unallo;
        $creditclm->user_str        = trim(Auth::user()->user_name);
        $creditclm->type_of_bus     = $dcontrol->type_of_bus;

        $creditclm->taxable= $request->taxableyes;
        // $creditclm->net_amount     = str_replace(',','', $request->net_amount);
        $creditclm->entry_type      = 89;
        $creditclm->vat_rate        = $request->vat_rate;
        $creditclm->vat_amount      = str_replace(',','', $request->vat_amt);
        $creditclm->levy_rate       = 0;
        $creditclm->levy_amount     = 0;
        $creditclm->entry_type_descr = 'SLV';
        $creditclm->client_number   = $polmaster->client_number;
        $creditclm->client_name = $polmaster->name;
        $creditclm->source = 'SLV';

        if ($retained == 'Y') {
            $creditclm->insured         = $polmaster->name;
            $creditclm->client_number   = $polmaster->client_number;
            $creditclm->client_name = $polmaster->name;
            $creditclm->details         = 'SALVAGE Item with Serial number '. $reg_no. ' RETAINED BY CLIENT ' .strtoupper($polmaster->name);
            $creditclm->payee           = strtoupper($polmaster->name);

        } else {

            $slvB = $salvage_buyers->buyer_name;
            $creditclm->details         = 'SALVAGE Item with Serial number '. $reg_no. ' SOLD TO '.strtoupper($slvB);
            $creditclm->payee           = strtoupper($slvB);
            $creditclm->buyer_id = $salvage_buyers->buyer_id;
            $retained = 'N';

        }
        $creditclm->client_retain = $retained;
        $creditclm->payee_addr1     = $salvage_buyers->address;
        $creditclm->item_desc       = $reg_no;
        $creditclm->location        = $polsect->location;

        $actionStatus = $creditclm->save();

        # On Success
        if ($actionStatus) {
            # Update Dtran0
            $updateDtran = Dtran0::where('debit_no', $old_debit_no)->update([
                'debit_no' => $debit_no,
            ]);

            $salvagesx = Salvages::where('reg_no', $reg_no)->where('claim_no', $claim_no)->count();

            if($salvagesx > 0){

                $salvages = Salvages::where('reg_no', $reg_no)->where('claim_no', $claim_no)->update([
                    'buyer_id' => $salvage_buyers->buyer_id,
                    'debited' => 'Y',
                    'dtrans_no' => $debit_no
                ]);

            }else{

                $salvages = Salvages::where('serial_no', $padded_serial_no)->where('claim_no', $claim_no)->update([
                    'buyer_id' => $salvage_buyers->buyer_id,
                    'debited' => 'Y',
                    'dtrans_no' => $debit_no
                ]);

            }
           
            DB::commit();
            Session::Flash('success', 'Debit note for Item '.$reg_no.' has been created successfully.');
           return 1;
        }else{
            throw new Exception('Salvage debit Failed');
                     DB::commit();
        }
        }catch (\Throwable $ex) {
            DB::rollback();
            
            $actionStatus = false;
            return $ex;
        }	
    }
    # Salvage End
    # Salvage End

    
    public function cancel_direct_sale(Request $request){
      
        $claim_no = removePolicyOrClaimFormat($request->cnc_claim_no);
        $reg_no = $request->cnc_reg_no;
        $serial_no = $request->cnc_serial_no;
        
        $sVehikl = Salvages::where('claim_no', $claim_no)->where('reg_no', $reg_no)->count();

        // dd($request->all(),$sVehikl,$reg_no,$claim_no);

        try {
            //code...
            $request->validate([
                'cnc_cancel_reason' => 'required|string',
            ]);

            $cancel_reason = $request->cnc_cancel_reason;
          
            DB::beginTransaction();
        if($sVehikl >0 ){

         
            $salvageVehikl = Salvages::where('claim_no', $claim_no)->where('reg_no', $reg_no)->update([
                'status' => 801,
                'retained_by_client' => 'N',
                'buyer_id' => '',
                'buyer_email' =>'',
                'winner' => 'N',
                'sold_to' =>'',
                'contractor_id' => '',
                
            ]);

        }else{
          
            $salvageVehikl = Salvages::where('claim_no', $claim_no)->where('serial_no', $serial_no)->update([
                'status' => 801,
                'retained_by_client' => 'N',
                'buyer_id' => '',
                'buyer_email' =>'',
                'winner' => 'N',
                'sold_to' =>'',
                'contractor_id' => '',
               
            ]);

        }

        try {
            //code...
            $user_name = Auth::user()->user_name;
            $audit_record = Direct_sale_audit::Create([
                'reg_no' => $reg_no,
                'serial_no' => $serial_no,
                'cancelled' => 'Y',
                'cancel_reason' => $cancel_reason,
                'cancelled_by' => $user_name
            ]);

        } catch (\Throwable $th) {

            throw new \Exception('audit_error');

        }
        
        DB::commit();
        Session::Flash('success', ' Buyer of Item '. ($reg_no ? $reg_no : $serial_no).' has successfully been Cancelled');

    } catch (\Throwable $e) {

        if($e->getMessage() == 'audit_error' ) {
            Session::Flash('error', 'Direct Sale Audit Error');
        }
        else {
            //throw $th;
            Session::Flash('error', ' Buyer of Item '. ($reg_no ? $reg_no : $serial_no).' has failed to be Cancelled');
        }
        DB::rollback();
    }
        return redirect()->back();


    }

      ##non motor schedule items
      public function save_non_motor_items(Request $request){
        try {

            for($i = 0; $i < count($request->salvageitem); ++$i){

                	
                $validator =  Validator::make($request->all(),[
                    'salvageitem' => 'required',
                    'salvage_amt' => 'required',
                    'claim_no' => 'required',
                    ]);
                    
                if ($validator->fails()) {
                    Session::flash('error', 'some field are missing');
                    return [
                        'code' => -1,
                        'msg' => $validator->errors(),
                    ];
                }

                $item = $request->salvageitem[$i];
                $claimdets  = Clhmn::where('claim_no',$request->claim_no)->first();
                $name = Prosched::where('endorse_no',$claimdets->endt_renewal_no)->where('quantity',$item)->where('section_no',$request->section_no[$i])->first();

                $insert_salvages = new Salvages;
                $insert_salvages->claim_no=$request->claim_no;
                $insert_salvages->serial_no=$request->salvageserial[$i];
                $insert_salvages->section_no=$request->salvageitem[$i];
                $insert_salvages->preacc_val=str_replace(',','',$request->salvage_pre_amt[$i]);
                $insert_salvages->salvage_amount=str_replace(',','',$request->salvage_amt[$i]);
                $insert_salvages->item_name=$name->detail_line.'-'.$name->detail_line2;
                $insert_salvages->policy_no=$claimdets->policy_no;
                $insert_salvages->dola=Carbon::now();
                $insert_salvages->user1=Auth::user()->user_name;
                $insert_salvages->tendered='N';
                $insert_salvages->retained_by_client = 'N';
                $insert_salvages->debited = 'N';
                $insert_salvages->status = 801;

                $insert_salvages->save();      

            }

            return 1;


        } catch (\Throwable $th) {

            DB::rollback();
            return 2;
        }

    }

    public function gettaxtypes(Request $request){
        $gettaxtypes = Gltaxes::where('tax_type','VAT')
                        ->where('tax_rate', '>',0)
                        ->first();

        return $gettaxtypes;
    }

    public function add_salvage_items(Request $request){

        $validator =  Validator::make($request->all(),[
			'item_name' => 'required',
			'claim_no' => 'required',
			'serial_no' => 'required',
			'item_serial' => 'required',
			'sal_amt' => 'required',
			'total_sal_amount' => 'required',

		]);
			
		if ($validator->fails()) {
			return [
                    'status' => 0, 
                    'msg'=> 'Some Required fields are missing'
                ];
		}
        
        ##create array for serials
        $serials = array();
        $count = 0;
        for ($i = 0; $i < count($request->item_name); ++$i) {
          
            $item_serial = $request->input('item_serial')[$i];
            array_push($serials, $item_serial);
            $validate_saved_serials = $this->check_serial_no_existing($request->claim_no,$item_serial,$count);

        }

        ## Duplicate Serial Numbers Found on this Claim
        if($validate_saved_serials > 0){
            return [
                'status' => 0, 
                'msg'=> 'Duplicate Serial Numbers Found on this Claim'
            ];
        }

        ##check if array has a duplicate
        if($this->hasDuplicates($serials)){
            return [
                'status' => 0, 
                'msg'=> 'Duplicate Serial Numbers Found on this list'
            ];
        };

        try {
            
            for ($i = 0; $i < count($request->item_name); ++$i) {
                
                $item_name= $request->input('item_name')[$i];
                $item_serial= $request->input('item_serial')[$i];
                $sal_amt= $request->input('sal_amt')[$i];
                $item_no = BundledSalvageItems::max('item_no')+1;

                $save_item = New BundledSalvageItems;
                $save_item->item_no = $item_no;
                $save_item->claim_no = $request->claim_no;
                $save_item->sal_serial_no = $request->serial_no;
                $save_item->item_name = $item_name;
                $save_item->item_serial_no = $item_serial;
                $save_item->salvage_value = str_replace(',','',$sal_amt);
                $save_item->dola = Carbon::now();
                $save_item->user_str = auth()->user()->user_name;
                $save_item->save();

            }
            
            ##update salvgae table amounts 
            $sal_amount = Salvages::where('claim_no',$request->claim_no)
                ->where('serial_no',$request->serial_no)
                ->first();
                
            $sal = Salvages::where('claim_no',$request->claim_no)
                ->where('serial_no',$request->serial_no)
                ->update([
                        'preacc_val' =>$sal_amount->preacc_val + str_replace(',','',$request->total_sal_amount),
                        'salvage_amount' =>$sal_amount->salvage_amount + str_replace(',','',$request->total_sal_amount)
                    ]);

            return [
                'status' => 1, 
                'msg'=> 'Items Saved Successfully'
            ];
            
        } catch (\Throwable $e) {
            return [
                'status' => 0, 
                'msg'=> 'Error may have occurred when saving'
            ];

        }
    }

    public function bundled_items_view(Request $request){

        $bundles_salvage = BundledSalvageItems::where('claim_no',$request->claim_no)
                        ->where('sal_serial_no',$request->serial_no)
                        ->get();

        $salvage_details = Salvages::where('serial_no',$request->serial_no)
        ->where('claim_no',$request->claim_no )->first();

    	return Datatables::of($bundles_salvage)
        
        ->editColumn('dola', function ($bundles_salvage) {
            return formatDate($bundles_salvage->dola);
        })
        
        ->editColumn('salvage_value', function ($bundles_salvage) {

            return number_format($bundles_salvage->salvage_value);
        })

        ->addColumn('Action', function($bundles_salvage) use(&$salvage_details){
            if($salvage_details->reserve_amt > 0){
                return "-";

            }else{
                $delete_item = '<button class="delete-record-btn btn-md " 
                data-item_no ='. $bundles_salvage->item_no.'
                style="cursor:pointer; color: red;">
                <i class="fa fa-trash"></i></button>';

                return $delete_item ;
            }
        })

        ->rawColumns(['Action'])
        ->make(true);

    }


    public function check_serial_no_existing($claim_no,$serial_no,$count){


        $check_serial = BundledSalvageItems::where('claim_no',$claim_no)
                ->where('item_serial_no',$serial_no)
                ->count();

        if($check_serial > 0){
            $count++;
        }

        return $count;
    
    }


    public  function hasDuplicates(array $array): bool {

        $collection = collect($array);

        return $collection->duplicates()->isNotEmpty();
    }


    public function remove_bundled_item(Request $request){

        try {
            $item_removal = $check_serial = BundledSalvageItems::where('claim_no',$request->claim_no)
                ->where('sal_serial_no',$request->serial_no)
                ->where('item_no',$request->item_num)
                ->delete();
            $salvage_value = $check_serial = BundledSalvageItems::where('claim_no',$request->claim_no)
                ->where('sal_serial_no',$request->serial_no)
                ->sum('salvage_value');

            $sal = Salvages::where('claim_no',$request->claim_no)
            ->where('serial_no',$request->serial_no)
            ->update([
                'preacc_val' =>$salvage_value,
                'salvage_amount' =>$salvage_value
            ]);

            Session::flash('success', 'Items Removed Successfully');


        } catch (\Throwable $e) {
            //throw $th;

            DB::rollback();
            Session::flash('error', 'failed to remove item');


        }

        return redirect()->back();
    
    }
    
  
       
    
        
}
