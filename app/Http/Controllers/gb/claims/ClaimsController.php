<?php
namespace App\Http\Controllers\gb\claims;
use DB;
use App;
use PDF;
use File;
use Mail;
use Excel;
use Route;
use Session;
use App\Dept;
use App\User;
use Response;
use App\Acdet;
use App\Agmnf;
use App\Cause;
use App\Clest;
use App\Clhmn;
use App\Clpmn;
use App\Modtl;
use App\Tran0;
use Exception;
use Throwable;
use App\Cbmast;
use App\Clmreq;
use App\County;
use App\Crmast;
use App\Dtran0;
use App\Perils;
use App\Polcmb;
use App\Polsec;
use App\Region;
use App\Reidoc;
use App\Branch;	
use App\Bustype;
use App\Cbtrans;
use App\Classbr;
use App\Clmcorr;
use App\Clmhist;
use App\Clparam;
use App\Clpmnri;
use App\Doctype;
use App\Folders;
use App\Pipcnam;
use App\Polsect;
use App\Reqdocs;
use App\Claimant;
use App\Clestdtl;
use App\Clmmodtl;
use App\Clremast;
use App\Clstatus;
use App\Coinpart;
use App\Courthse;
use App\Currency;
use App\Dcontrol;
use App\District;
use App\Laclmreq;
use App\Modtlend;
use App\Nlparams;
use App\Olbranch;
use App\Payreqst;
use App\Polsched;
use App\Prosched;
use App\Reintray;
use App\Salvages;
use App\Approvals;
use App\Classsect;
use App\Classyear;
use App\Clmexcess;
use App\Clmsummon;
use App\Courtsdet;
use App\Creditclm;
use App\Debitmast;
use App\Financier;
use App\IRAstatus;
use App\Payreqstd;
use App\Polexcess;
use App\Pollimits;
use App\Polmaster;
use App\Polremast;
use App\Reinsetup;
use App\SendEmail;
use Carbon\Carbon;
use Dompdf\Dompdf;
use App\ClassModel;
use App\Client;    
use App\Dvexcesses;
use App\Endtrepart;
use App\Olbnknames;
use App\Polsectend;
use App\SendEmails;
use App\Treatypart;
use App\Aimsuprofgb;
use App\Autolimits;	
use App\IRAcomments;
use App\Natureofjud;
use App\Perilsitems;
use App\Polbenefits;
use App\Treatysetup;
use App\Aims_process;
use App\Aimsuser_web;
use App\Cbdeduct;    
use App\Clmadditions;
use App\Clmdischarge;
use App\Clmhistlegal;
use App\Clmjudgement;
use App\Clmreinalloc;
use App\Madtl;       
use App\PartnerBank;	
use App\Polmasterend;
use App\Polremastend;
use App\Approval_flow;
use App\ClaimSchedule;
use App\Clmrecoveries;
use App\Clpmnperildtl;
use App\Identity_type;
use App\Cbseqno;       
use App\Aimsgrouplimits;
use App\Models\Aimsuser;
use App\Models\Location;
use App\Models\MediPlan;
use App\Claim_sched_hist;
use App\Http\Controllers;
use App\Models\Clmassess;
use App\Models\Modtlhist;
use App\Models\Modtlmast;
use App\Olpaymethd;      
use App\Court_type;       
use App\Models\Clpmnperil;
use App\Models\Modtlpivot;
use App\Models\Clsections;	
use App\Models\Dvclaimform;
use App\SourceCodes;       
use App\Third_party_claims;
use App\workflow\Documents;
use Illuminate\Support\Str;
use App\Escalate_pol;       
use App\Models\Clsclaimform;
use App\Models\Limit_types;	
use App\Policy_notes;       
use Illuminate\Http\Request;
use App\Clmdischarge_voucher;
use App\Models\LocationLevel;
use App\Models\Fnote_comments;
use App\Models\Claims_DFI;
use App\Classes\AddToSendEmail;
use App\Marinemasterpol;  
use App\Acdetallonew;     

/*********************workflow inlcudes*****************/
 use App\workflow\Workflows;
 use App\MailBox;
 use App\workflow\Escalations;
 use App\WorkflowsConfig;
 use App\Escalation_types;



// For garage order
 use App\Clmorders;
//discharge vouchur
use App\Models\PermissionRole;      
use App\Models\Recommendations;

// approvals
use App\Requisition_deductions;
use App\Cl_additions_deductions;
use App\Marinemasterhist;       
use Yajra\Datatables\Datatables;
use App\Events\BlacklistVehicles;
use App\Models\Medical\Medmember;
use App\Models\Medical\Polmedsec;
use App\Models\Medical\Polmedprem;
use App\Events\ClaimProcessedEvent;
use App\Models\RecommendationTypes;
use Illuminate\Support\Facades\Log;
use App\Events\TiraIntegrationEvent;
use App\Http\Controllers\Controller;
use App\Models\Medical\Meddependant;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\View;
use App\Models\Medical\Polmedoptplan;
use App\Classes\Approvals\ApprovalsMgt;
use App\Http\Controllers\pdfController;
use App\Models\ServiceProviderInvoices;
use Illuminate\Support\Facades\Storage;
use App\Classes\Common\FileUploadManager;
use App\Events\DispatchNotificationEvent;
use App\Events\AddProcessSLA;
use Illuminate\Support\Facades\Validator;
use App\Services\IntermediaryQueryService;
use Aimsoft\UserManagement\Models\Permission;
use App\ValueObjects\IntermediaryQueryParams;
use App\Services\UserGroupLimitCheckerService;
use Aimsoft\UserGroupLimit\Models\LimitCategory;
use App\Models\TypeofClaimparam;
class ClaimsController extends Controller {
	/*Define classwise Variables*/
	public $current_year;
	public $current_month;
	public $todays_date;
	public $todays_date_time;
	public $user_name;
	public $NewClaimsParameters;

	private $current_process; 

	public function __construct() {
		/* Get username */
		$this->middleware(function ($request, $next) {
			$this->user_name = trim(Auth::user()->user_name);

			return $next($request);
		});
		/*initialize variables into a class*/
		$this->current_year = Carbon::now()->format('Y');
		$this->current_month = Carbon::now()->format('m');
		$this->todays_date = Carbon::today();
		$this->todays_date_time = Carbon::now();

		$this->NewClaimsParameters = DB::table('new_claims_parameters')->first();	

		// get current process
		$this->current_process = get_process('CLAIM');
	}
	/*End Define classwise Variables*/




	// checkReqDocs func
	public function checkReqDocs(Request $request)
	{
		$req_no = $request->req_no;
		$docs = Payreqstd::where('requisition_no', $req_no)->get()->count();
		return $docs;
	}
	// checkReqDocs func.end

	# Claim Excess Processing
	public function getExcessProcessing(Request $request){
		$claim = Clhmn::where('claim_no', $request->claim_no)->first();
		$cl_end = Dcontrol::where('endt_renewal_no',$claim->endt_renewal_no )->first();
		$getendorsement_no = Dcontrol::where('policy_no',$claim->policy_no )
							->where('trans_type','NIL')->where('cancelled','<>','Y')
							->orderBy('dola','Desc')->first();

		if($getendorsement_no){
			
			if($getendorsement_no->dola > $cl_end->dola ){
				$endorse = $getendorsement_no->endt_renewal_no;
				$trans = $getendorsement_no->trans_type;
				
			}else{
				
				$endorse = $claim->endt_renewal_no ; 
				$trans = $getendorsement_no->trans_type ;
			}
		}else{
			$endorse = $claim->endt_renewal_no ;
		}
		
		$excessProcessing = Polexcess::where('endt_renewal_no', $endorse)->get();

		return $excessProcessing;
	}

	//check existing dv

	public function checkexistingdv(Request $request){
		$checkclaim = Clmdischarge::where('claim_no', $request->claim_no)
								->where('dv_type',$request->val_selected)
								->count();
		return $checkclaim;
	}

	public function getEPRate(Request $request) {
		$rate = Polexcess::where('policy_no', $request->policy_no)->where('item_no', $request->item_no)->where('endt_renewal_no',$request->en)->first();
		return $rate;
	}

	public function getExcessBase(Request $request) {
		$excessBase = Clhmn::where('claim_no', $request->claim_no)->first();

		return $excessBase;
	}

	# End of Claim Excess Processing

	/*Helper methods for Ajax autopopulate*/
	public function jsonifyPolmaster(Request $request) {
		$policy_no = $request->get('policy_no');
		$policy_details = Polmaster::Where('policy_no', '=', $policy_no)->get();
		echo $policy_details;
		//return response()->json($policy_details);
	}
	public function jsonifyPolremast(Request $request) {
		$endt_renewal_no = $request->get('endt_renewal_no');
		$polremast_details = Polremastend::Where('endt_renewal_no', '=', $endt_renewal_no)->get();
		// $polremast_details = Polremast::Where('endt_renewal_no', '=', $endt_renewal_no)->get();
		echo $polremast_details;
	}
	public function jsonifyClass(Request $request) {
		$class = $request->get('class');
		$class_details = ClassModel::Where('class', '=', $class)->get();
		echo $class_details;
		//return response()->json($class_details);
	}
	public function getsummondates(Request $request) {

		$case = $request->get('case_no');
		$claim = $request->get('claim_no');
		$claim_det = Clmsummon::WhereRaw("trim(case_no) = '".$case."' ")
								->where('claim_no',$claim)
								->first();
			// dd( $claim_det);					
		echo $claim_det;
	}

	public function jsonifyDebitmastDetails(Request $request) {
		$endt_no = $request->get('endt_no');
		$acc_date = $request->get('acc_date');
		$debitmast = Debitmast::Where([
			['endt_renewal_no', '=', $endt_no],
			['entry_type_descr', '!=', 'CNC'],
			['dr_cr', '=', 'D'],
			['period_from','<=',$acc_date],//Period From Should always be less or equal to date notified
			['period_to','>=',$acc_date]//Period To Should always be greater or equal to date notified
		])->get(); 

		echo $debitmast;
	}
	public function jsonifyDept(Request $request){
		$dept = $request->get('dept');
		$classee = $request->get('classee');
		$dept_details = Classmodel::Where('class',$classee)->get();
		echo $dept_details;
	}
	public function jsonifyPolsect(Request $request){

		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$endt_no = $request->get('endorse');
		$class = $request->get('class');
		$location = $request->get('location');
		
		if($location != null ){
			$polsect_details = Polsectend::Where([
				['policy_no','=',$policy_no],
				['endt_renewal_no','=',$endt_no],
				['location','=',$location],
				['class','=',$class]
			])->get();
		
		}else{
			$polsect_details = Polsect::Where([
				['policy_no','=',$policy_no],
				// ['endt_renewal_no','=',$endt_no],
				['class','=',$class]
			])
			->orderBy('endt_renewal_no','desc')
			->get();
		}

		echo $polsect_details;
	}
	public function getcmbineddets(Request $request){

		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$endt_no = $request->get('endorse');
		$location = $request->get('location');
		// if($policy_no && $endt_no){
		// 	$polsect_details = Polsect::Where([
		// 		['policy_no','=',$policy_no],
		// 		['endt_renewal_no','=',$endt_no],

		// 	])->get();
		// }
	
		// echo $polsect_details;
	}

	
	public function accidentclass(Request $request){

		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$endt_no = $request->get('endorse');
		$polsect_details = Prosched::whereraw("trim(endorse_no)='" . $endt_no . "'")
							->where('class',$request->class)
							->get();
		
		echo $polsect_details;
	}

	
	public function memberdeps(Request $request){
		
		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$endt_no = $request->get('endorse');
		$member_no = $request->get('member_no');
		$dep_details = DB::table('meddependants')->whereraw("trim(endt_renewal_no)='" . $endt_no . "'")
							->where('member_no',$member_no)
							->get();
		
		echo $dep_details;
	}

	public function sectionsaccident(Request $request){

		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$endt_no = $request->get('endorse');
		$classes = $request->get('classes');
		// $polsect = polsec::whereraw("trim(endt_renewal_no)='" . $endt_no . "'")
		// 			->get();
		
		// $polsec = DB::select("SELECT p.ENDT_RENEWAL_NO ,p.SECTION_NO , c.SECTION_DESCRIPTION , c2.GROUP_DESCRIPTION , p.SUM_INSURED FROM polsec p 
		// JOIN classsect c ON (c.class = p.class AND c.SECTION_NO = p.SECTION_NO)
		// JOIN CLASSGRP c2 ON (c2.CLASS = c.CLASS AND c2.CLASSGRP = c.CLASSGRP)
		// WHERE p.policy_no = '$endt_no' ORDER BY p.ENDT_RENEWAL_NO, c2.GROUP_DESCRIPTION asc");
		
		$polsect = DB::select("SELECT p.ENDT_RENEWAL_NO ,p.SECTION_NO , c.SECTION_DESCRIPTION , c2.GROUP_DESCRIPTION , p.SUM_INSURED FROM polsec p 
		JOIN classsect c ON (c.class = p.class AND c.SECTION_NO = p.SECTION_NO)
		JOIN CLASSGRP c2 ON (c2.CLASS = c.CLASS AND c2.CLASSGRP = c.CLASSGRP)
		WHERE p.policy_no = '$policy_no' and p.endt_renewal_no= '$endt_no' and p.class = '$classes'
		 ORDER BY p.ENDT_RENEWAL_NO, c2.GROUP_DESCRIPTION asc ");
		
		return json_encode($polsect);
		
	}
	public function clientdetails(Request $request){

		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$endt_no = $request->get('endorse');
		$polsect = Polmasterend::whereraw("trim(endorse_no)='" . $endt_no . "'")
							->get();
		
		echo $polsect;
	}
	public function getcombineddets(Request $request){

		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$endt_no = $request->get('endorse');
		$polsect_details = DB::select("select distinct d.description,d.class from polsectend c join class d on d.class = c.class where endt_renewal_no = '$endt_no' and (total_premium is not null or total_premium != '0') ");
		// dd($polsect_details,$endt_no,$policy_no);
		return json_encode($polsect_details);
	}

	public function getaccsecdet(Request $request){

		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$endt_no = $request->get('endorse');
		$polsect_details = Polsched::whereraw("trim(endorse_no)='" . $endt_no . "'")
							->get();
		echo $polsect_details;
	}



	public function getpolicysectiondetails(Request $request){
		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$location = $request->get('location');
		$endorse = $request->get('endt_renewal_no');
		$class = $request->get('classes');
		$policySections = DB::select(" select a.section_no,b.sum_insured,f.group_description, a.section_description from classsect a join polsec b on b.class = a.class and b.section_no = a.section_no
		join classgrp f on (a.classgrp = f.classgrp and a.class= f.class )
        where  b.location = '$location' and b.class ='$class' and b.policy_no  = '$policy_no' and b.endt_renewal_no = '$endorse' ");
		echo json_encode($policySections);
	}
	public function getpolicysecdetails(Request $request){
		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$endorse = $request->get('endt_renewal_no');
		$class = $request->get('classes');
		$policySections = DB::select(" select a.section_no,b.sum_insured, a.section_description from classsect a join polsec b on b.class = a.class and b.section_no = a.section_no
        where b.class ='$class' and policy_no  = '$policy_no' and b.endt_renewal_no = '$endorse' ");
		echo json_encode($policySections);
	}

	public function getsectionamount(Request $request){
		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
	
		$location = $request->get('location');
		$endorse = $request->get('endt_renewal_no');
		$sectionitem = $request->get('sectionitem');

		$policySect = DB::select(" select * from polsec
        where  endt_renewal_no = '$endorse' and section_no ='$sectionitem' ");

		return $policySect;
	}

	public function jsonifyProsched(Request $request){
		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$class = $request->get('class');
		$location = $request->get('location');
		if($policy_no && $location){
			$prosched_details = Prosched::Where([
				['policy_no','=',$policy_no],
				['class','=',$class],
				['quantity','=',$location]
			])->get();
		}
		else{
			$prosched_details = Prosched::Where('policy_no','=',$policy_no)
										->where('class',$class)->get();
		}
		echo $prosched_details;
	}

	public function jsonifyClient(Request $request) {
		$client_number = $request->get('client_number');
		$client_details = Client::Where('client_number', '=', $client_number)->get();
		echo $client_details;
		//return response()->json($client_details);
	}

	public function check_existence_claim(Request $request) {
		// dd(gettype($request->get('acc_date')), $request->get('acc_date'));
	
		$class = $request->classes;
		$classdtl = ClassModel::where('class',$class )->first();

		##for combined cases 
		$forcombined = Dcontrol::where('policy_no',$request->policy_no )->first();

		$comb_class = ClassModel::where('class',$forcombined->class )->pluck('combined')->first();
		
		if($comb_class == 'Y'){
			$location = Polsectend::where('endt_renewal_no',$request->get('endt_renewal_no'))
			->where('class',$class)
			->pluck('location')
			->first();

		}else{
			$location = $request->get('location');

		}
		
		$check_claim['shares_acc_date'] = Clhmn::Where('policy_no', $request->get('policy_no'))
								->where('location',$location)
							    ->where('acc_date',$request->get('acc_date'))
								->get();

		$check_claim_covered = Clhmn::join('dcontrol', 'clhmn.ENDT_RENEWAL_NO', '=', 'dcontrol.ENDT_RENEWAL_NO')
		->where('clhmn.policy_no', $request->policy_no)
		->where('clhmn.location', $location)
		->whereDate('dcontrol.cov_period_from', '<=', date('Y-m-d', strtotime($request->get('acc_date'))))
		->whereDate('dcontrol.cov_period_to', '>=', date('Y-m-d', strtotime($request->get('acc_date'))))
		->select('clhmn.claim_no','clhmn.location','clhmn.policy_no', 'dcontrol.cov_period_from', 'dcontrol.cov_period_to')
		->get();

		if($check_claim_covered) {
			$check_claim['has_cover'] = $check_claim_covered;
		}
	
		return json_encode($check_claim);
	}

	public function prosccedaccdetails(Request $request) {
		$check_claim = Prosched::Where('policy_no', $request->get('policy_no'))
								->where('quantity',$request->get('location'))
								->where('endorse_no',$request->get('endt_renewal_no'))
								->first();

		if($checkclaim == null){

			$check_claim = Polsched::Where('policy_no', $request->get('policy_no'))
					->where('location',$request->get('location'))
					->where('endorse_no',$request->get('endorse'))
					->get();

		}		
		
	
		echo json_encode($check_claim);
	}
	
	public function getpolsched(Request $request) {
	
		$getdetails = Polsched::Where('policy_no', $request->get('policy_no'))
								->where('location',$request->get('location'))
								->where('class',$request->get('class'))
								->where('endorse_no',$request->get('endt_renewal_no'))
								->get();
		// dd($getdetails,$request->all());						
		echo json_encode($getdetails);
	}
	
	public function accdetsched(Request $request) {
	
		$check_claim = Polsched::Where('policy_no', $request->get('policy_no'))
								->where('section_no',$request->get('location'))
								->where('endorse_no',$request->get('endorse'))->first();
		// dd($check_claim);						
		echo json_encode($check_claim);
	}

	
	
	public function medsched(Request $request) {
		if ($request->med_type == 'I') {
			$check_claim = Polmedprem::Where('policy_no', $request->get('policy_no'))
									->where('member_no',$request->get('member_no'))
									->where('endt_renewal_no',$request->get('endorse'))->first();
		}else {
			$check_claim = Polmedoptplan::Where('policy_no', $request->get('policy_no'))
									->where('member_no',$request->get('member_no'))
									->where('endt_renewal_no',$request->get('endorse'))->first();
		}

		echo json_encode($check_claim);
	}

	public function jsonifyAgmnf(Request $request) {

		$branch = (int) $request->get('branch');
		$agent = (int) $request->get('agent');
	
	  //$intermediary_details = Agmnf::Where('branch', (int) $branch)->where('agent', (int) $agent)->get();

		$intermediaryParams = new IntermediaryQueryParams([
			'agentNo' => $agent,
		]);
	
		
		$intermediary_details = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
	

		return response()->json($intermediary_details);
	}

	public function getPolicyEndorsements(Request $request)
	{
		$policy_no = $request->get('policy_no');
		$debitmast = Debitmast::where('policy_no',$policy_no)
								->get();
			return $debitmast;
	}

	public function getCoverDates(Request $request)
	{
		$endt_renewal_no = $request->get('endt_renewal_no');
		$cover_details = Debitmast::where('endt_renewal_no',$endt_renewal_no)->first();
		$count_endorsements = Dcontrol::where('policy_no',$cover_details->policy_no)
			->where('cnc_rfn_endt',$endt_renewal_no)->count();

		$cover_details['counted_cnc'] = $count_endorsements;
		$cover_details = $cover_details->toarray();
		return $cover_details;
	}

	public function jsonifyClhmn(Request $request) {
		$claim_no = $request->get('claim_no');
		$claim_details = Clhmn::Where('claim_no', $claim_no)->get();
		echo $claim_details;
	}

	public function getClhmn(Request $request){
		$claim_no = removePolicyOrClaimFormat($request->get('claim_no'));
		$clhmn = Clhmn::where('claim_no',$claim_no)->first();
	
		return $clhmn;
	}

	public function jsonifyClpmn(Request $request) {
		$claim_no = $request->get('claim_no');
		$dtrans_no = $request->get('dtrans_no');
		$account_year = $request->get('account_year');
		$account_month = $request->get('account_month');
		$entry_type_descr = $request->get('entry_type_descr');
		$doc_type = $request->get('doc_type');
		$ln_no = $request->get('ln_no');
		$pay_type = $request->get('pay_type');

		if($claim_no && $dtrans_no){
			$clpmn_details = Clpmn::join('clhmn', 'clpmn.claim_no', '=', 'clhmn.claim_no')
					->where([
						['clpmn.claim_no', $claim_no],
						['clpmn.dtrans_no', $dtrans_no],
						['clpmn.account_year', $account_year],
						['clpmn.account_month', $account_month],
						['clpmn.entry_type_descr', $entry_type_descr],
						['clpmn.doc_type', $doc_type],
						['clpmn.ln_no', $ln_no],
						['clpmn.pay_type', $pay_type]
					])
					->select('clpmn.*', DB::raw('EXTRACT(YEAR FROM clhmn.acc_date) as claim_year')) // Select all fields from clpmn and claim_year from clhmn
					->get();            
		}
		else{
			$clpmn_details = Clpmn::join('clhmn', 'clpmn.claim_no', '=', 'clhmn.claim_no')
					->where([
						['clpmn.claim_no', $claim_no]
					])
					->select('clpmn.*', DB::raw('EXTRACT(YEAR FROM clhmn.acc_date) as claim_year')) // Select all fields from clpmn and claim_year from clhmn
					->get();  
		}
		echo $clpmn_details;
	}

	public function xloss_details(Request $request) {
		$endt_renewal_no = $request->get('endt_renewal_no');
		$uw_year = $request->get('claim_year');
		$class = $request->get('class');
		$classyear = Classyear::where([
			'uw_year' => $uw_year,
			'class' => $class
		])->first();
		$reinsetup = Reinsetup::where([
			'uw_year' => $uw_year,
			'class' => $classyear->reinclass
		])->first();
		$treatyparts = Treatypart::where([
			'uw_year' => $uw_year, 
			'treaty_code' => $reinsetup->xl_code_1

		])->get();
		$treatyparts_1 = 	DB::select( "select a.*,b.name from treatypart a join
				Crmast b on (b.branch = a.branch and b.agent=a.agent )
				where a.uw_year = '$uw_year' and a.treaty_code ='$reinsetup->xl_code_1'" );

		$treatyparts_2 = 	DB::select( "select a.*,b.name from treatypart a join
				Crmast b on (b.branch = a.branch and b.agent=a.agent )
				where a.uw_year = '$uw_year' and a.treaty_code ='$reinsetup->xl_code_2'" );

		$treatyparts_3 = 	DB::select( "select a.*,b.name from treatypart a join
				Crmast b on (b.branch = a.branch and b.agent=a.agent )
				where a.uw_year = '$uw_year' and a.treaty_code ='$reinsetup->xl_code_3'" );
		
	    $treatyparts_4 = 	DB::select( "select a.*,b.name from treatypart a join
				Crmast b on (b.branch = a.branch and b.agent=a.agent )
				where a.uw_year = '$uw_year' and a.treaty_code ='$reinsetup->xl_code_4'" );

		return response()->json([
			'treatyparts_1' => $treatyparts_1,
			'treatyparts_2' => $treatyparts_2,
			'treatyparts_3' => $treatyparts_3,
			'treatyparts_4' => $treatyparts_4,
		]);
	}

	public function getparticipantsdetails(Request $request) {
		$endt_renewal_no = $request->get('endt_renewal_no');
		

		$participants = DB::select( "select a.*,b.name from Endtrepart a join
		Crmast b on (b.branch = a.branch and b.agent=a.agent )
		where a.endt_renewal_no = '$endt_renewal_no'" );

		return response()->json($participants);
	}


	public function statusupdate(Request $request){

		// return $request;

			$name=strtoupper($request->get('term'));

            $results = array();
            $name = Clstatus::whereRaw("status_description like '%".$name."%'")->get();
            foreach($name as $p){
               $results []= ['value'=> trim($p->status_description).'-'.$p->status_code ,
                            'code'=>$p->status_code];
           }
           return Response::Json($results);
		}

	public function getdvdetails(Request $request){

		$claim_no = $request->get('clms');
		$order_no = $request->get('order_no');
		$dv = Clmdischarge::where('claim_no',$claim_no)->where('order_number',$order_no)->first();
		echo json_encode($dv);

	}


	public function group(Request $request){
		$userid = trim(Auth::user()->user_id);
		$lim_amount = $request->get('lim_amount');
		// $lim_amount = 2500000000;
		$lim_role = $request->get('lim_role');
		$dept = $request->get('dept');
		$groupmember = DB::select("select distinct a.user_id,a.name from aimsusers a 
		join aimsgrouplimits b on b.group_id = a.aims_group where (b.role_type = 'E' or b.role_type = 'C') and b.claim_limit >= '$lim_amount' and b.dept = '$dept' and a.user_id <> '$userid' and a.left_company <> 'Y' ");
		echo json_encode($groupmember);
 
	}

	public function groupabove(Request $request){
	
		$permission = Permission::where('slug','check-claim-requisitions')->first();
        $users = $permission->users;
	
		echo json_encode($users);

	}
		
	public function groupwithin(Request $request){

		$userid = trim(Auth::user()->user_id);
		$group_id = $request->get('group_id');
		$groupmembers = Aimsuser_web::where('aims_group', $group_id)
								->where('user_id','<>',$userid)
								->where('left_company','<>','Y')
								->get();
	
		echo json_encode($groupmembers);
		}
		
	public function statusreject(Request $request){

			$name=strtoupper($request->get('term'));

            $results = array();
			$name = Clstatus::whereRaw("status_description like '%".$name."%'")
									->where('reject', '=','Y')
								->get();
            foreach($name as $p){
               $results []= ['value'=>trim($p->status_description).'-'.$p->status_code,
                            'code'=>$p->status_code];
           }
           return Response::Json($results);
        }

	public function checkclaimfrequency(Request $request){

		$pol_no = $request->policy_no;
		$endt_renewal_no = $request->endt_renewal_no;
		$period_from = formatDateRFC3339($request->period_from);
		$period_to = formatDateRFC3339($request->period_to);
		$acc_date = Carbon::parse($request->acc_date);

		$countx = $this->claim_frequency($pol_no,$period_from,$period_to);
		$num_days_inception = $this->get_days_after_inception($endt_renewal_no,$acc_date);
		$d_to_expiry = $this->days_to_expiry($endt_renewal_no,$acc_date);
		
		##check for change in SUm insured
		$checksum_insured = $this->check_change_sumin_ext($pol_no,$acc_date);
		$trans_types = ['POL','CNC','REN','MAC'];
		$new_parameter_details =DB::table('new_claims_parameters')->first();

		$notify_days= 0;	
		$days_after_ext= 0;	
		$days_to_expiry= 0;	

		if($num_days_inception <= $new_parameter_details->notify_days_after_pol_issuance ){
			$notify_days = $num_days_inception?:0;
		
		}

		if($d_to_expiry <= $new_parameter_details->notify_days_to_pol_expiry ){
			$days_to_expiry = $d_to_expiry?:0;
		}

		if (!in_array($checksum_insured->trans_type, $trans_types)) {
			if($checksum_insured->sum_insured_changed == 'Y'){
				$prev_sum_insured = $checksum_insured->prev_sum_insured?:0;
				$curr_sum_insured = $checksum_insured->sum_insured?:0;
			}
			if($checksum_insured->ext_days <= $new_parameter_details->notify_days_after_pol_issuance){
				$days_after_ext = $acc_date->diffInDays(Carbon::parse($new_parameter_details->effective_date))?:0;
			}
		}


		$array = [
			"claim_frequency"=> (int)$countx->count,
			"days_after_inception"=>$notify_days,
			"days_to_expiry"=>$days_to_expiry,
			"prev_sum_insured"=>(int)$prev_sum_insured,
			"curr_sum_insured"=>(int)$curr_sum_insured,
			"days_after_expiry"=>$days_after_ext
		];
		
		return $array;
	}
	
	
	public function getdebitdetails( Request $request){
		$accidentdate = $request->acc_date;
		$policy_number = $request->policy_no;

		$polmaster = Polmaster::where('policy_no',$policy_number)->first();
		$cls = ClassModel::where('class',$polmaster->class )->first();

		if($cls->cl_past_expiry == 'Y'){

			$accidentdate = $this->check_cover_period($accidentdate,$polmaster);

		}
		
		$datetime = DB::Select("select b.trans_type, a.endt_renewal_no,a.dola,a.period_from,a.period_to,a.sum_insured,a.gross_amount,a.uw_year,
		a.currency_rate,a.currency_code,a.facult_reice, b.onboard_old_policy from debitmast a join dcontrol b on b.endt_renewal_no = a.endt_renewal_no
        where a.policy_no = '$policy_number' and (b.trans_type <> 'RFN' and b.trans_type <> 'CNC' and b.trans_type <> 'NIL' and  b.trans_type <> 'PTA')
		and a.effective_date <= TO_DATE('$accidentdate','YYYY-MM-DD')  and (TO_DATE('$accidentdate','YYYY-MM-DD')
		between a.period_from and a.period_to) and (a.gross_amount > 0 or (a.gross_amount = 0 and b.onboard_old_policy='Y')) order by a.effective_date desc ");

		$zpr_details = $datetime;

		if($cls->open_cover == 'Y'){

			$datetime = DB::Select("select b.trans_type,a.policy_no endt_renewal_no,b.dola,a.period_from,a.period_to,a.sum_insured,a.account_year,b.facin_premium_rate facult_reice,
			a.currency_rate,a.currency_code from MARINE_UTL_DEBITS a
			join dcontrol b on b.endt_renewal_no = a.endt_renewal_no
			where a.policy_no ='$policy_number'
			and (b.trans_type <> 'RFN' and b.trans_type <> 'CNC' and b.trans_type <> 'NIL')
			and b.effective_date <= TO_DATE('$accidentdate','YYYY-MM-DD')  
			and (TO_DATE('$accidentdate','YYYY-MM-DD') between a.period_from and a.period_to) 
			order by a.effective_date desc");


					##getmasterpolicydcontrol
				$masterpol = Dcontrol::where('endt_renewal_no',$datetime[0]->endt_renewal_no)->pluck('master_policy')->first();
				
				$opentype = Marinemasterhist::where('endt_renewal_no',$datetime[0]->endt_renewal_no)->pluck('opentype_code')->first();

				if($opentype == 'UTL'){

					$datetime = DB::select("select b.trans_type, a.endt_renewal_no,a.dola,a.period_from,a.period_to,a.sum_insured,a.gross_amount,a.uw_year,
					a.currency_rate,a.currency_code,a.facult_reice from debitmast a join dcontrol b on b.endt_renewal_no = a.endt_renewal_no
					where a.endt_renewal_no = '$masterpol' and (b.trans_type <> 'RFN' and b.trans_type <> 'CNC' and b.trans_type <> 'NIL')
					and a.gross_amount > 0 order by a.effective_date desc
				");
				}else{

					$datetime = $zpr_details ;
				}
			
	
		}

		return json_encode($datetime);
		 
	}

	public function checkeffectivedate( Request $request){
		
		$accidentdate = $request->acc_date;
		$endorse = $request->endorse;


		$masterpol = Dcontrol::where('endt_renewal_no',$endorse)->first();
			
		$countmarine = Marinemasterhist::where('endt_renewal_no',$endorse)->count();

		$polmaster = Polmaster::where('policy_no',$masterpol->policy_no)->first();
		$cls = ClassModel::where('class',$polmaster->class )->first();

		if($cls->cl_past_expiry == 'Y'){

			$accidentdate = $this->check_cover_period($accidentdate,$polmaster);

		}

		if($countmarine > 0 ){

			$opentype = Marinemasterhist::where('endt_renewal_no',$endorse)->pluck('opentype_code')->first();
 
			if($opentype == 'UTL'){

				$endt_renewal_no = DB::select("select endt_renewal_no from marine_utl_debits where endt_renewal_no in
					(select endt_renewal_no from dcontrol where master_policy = '$masterpol->master_policy'
					and effective_date <= TO_DATE('$accidentdate','YYYY-MM-DD'))  
					and (TO_DATE('$accidentdate','YYYY-MM-DD') between period_from and period_to) 
					group by endt_renewal_no
				");

				$endorse = $endt_renewal_no[0]->endt_renewal_no;
			}
		}
		
		$datetime = DB::select("select a.endt_renewal_no,b.effective_date,b.period_from,a.endorse_date,a.trans_type from 
		dcontrol a join debitmast b ON a.endt_renewal_no = b.endt_renewal_no where a.endt_renewal_no='$endorse' 
		and b.effective_date <= TO_DATE('$accidentdate','YYYY-MM-DD') 
		AND  (TO_DATE('$accidentdate','YYYY-MM-DD') BETWEEN b.PERIOD_FROM AND b.PERIOD_TO )");
	
			
		return json_encode($datetime);
	}


	public function getdebitdetailsbeforerns( Request $request){
		$accidentdate = $request->acc_date;
		$policy_number = $request->policy_no;
		$datetime = DB::Select("select endt_renewal_no,dola,period_from,period_to,sum_insured,gross_amount,uw_year,currency_code,
		currency_rate,a.facult_reice from debitmast where policy_no='$policy_number' 
		and '$accidentdate' between period_from and period_to and entry_type_descr <> 'CNC' 
		 and dr_cr='D' and ROWNUM =1 order by period_to asc");
		 return json_encode($datetime);
	}

	public function gettranstype(Request $request){
		$accidentdate = $request->acc_date;
		$policy_number = $request->policy_no;
		// $transtype = Dcontrol::where('policy_no', $policy_number)->orderBy('time','desc')->first();
		$polmaster = Polmaster::where('policy_no',$policy_number)->first();
		$cls = ClassModel::where('class',$polmaster->class )->first();

		if($cls->cl_past_expiry == 'Y'){

			$accidentdate = $this->check_cover_period($accidentdate,$polmaster);

		}
		
		$transtype = DB::Select("select * from dcontrol where policy_no = '$policy_number' 
			and effective_date <= TO_DATE('$accidentdate','YYYY-MM-DD') 
			order by time DESC fetch next 1 rows only"
		);

		return json_encode($transtype);
	}
	public function getcancelleddate(Request $request){

		$accidentdate = $request->acc_date;
		$policy_number = $request->policy_no;
		$canceldate = Polmaster::where('policy_no', $policy_number)->first();
		return json_encode($canceldate);

	}


	public function jsonifyModtl(Request $request) {

		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$endorse = removePolicyOrClaimFormat($request->get('endorse'));

		$item_no = $request->get('location');
		if($policy_no && $item_no){

			$modtl_details = DB::table('modtlpivot')
            ->join('modtlsumm', function($join){
				$join->on('modtlpivot.policy_no', '=', 'modtlsumm.policy_no');
				$join->on('modtlpivot.endt_renewal_no', '=', 'modtlsumm.endt_renewal_no');
				$join->on('modtlpivot.reg_no', '=', 'modtlsumm.reg_no');
			})
			->Where([
				['modtlpivot.policy_no', $policy_no],
				['modtlpivot.endt_renewal_no', $endorse],
				['item_no', $item_no]
			])->where('status', 'ACT')->get();
		}
		else {
			$modtl_details = Modtlpivot::Where('endt_renewal_no', $endorse)->where('status', 'ACT')->get();
		}

		echo $modtl_details;

	

	}
	public function jsonifyModtlcurrency(Request $request) {
        $policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
        $endorse = removePolicyOrClaimFormat($request->get('endorse'));
        $debit = Debitmast::where('policy_no',$policy_no)->where('endt_renewal_no',$endorse)->first();
        $currency_details = Currency::Where('currency_code',$debit->currency_code)->first();
        $currency = $currency_details->currency;
        return response()->json($currency);

    }

	public function jsonifyCurrency(Request $request){
		$currency_code = $request->get('currency_code');
		$currency_details = Currency::Where('currency_code','=',$currency_code)->get();
		echo $currency_details;
	}
	public function jsonifyClparam(Request $request) {
		$record_type = $request->get('record_type');
		$ass_type = $request->get('in_house');
		
		if ($ass_type == 'Y') {

			$companyAssessors = Permission::with('users')
				->where('name','assess-claim')
				->first();
			
			$clparam_details = $companyAssessors->users()->first();
			
		} else{
			$claimant_code = $request->get('claimant_code');
			$clparam_details = Clparam::Where('record_type', '=',$record_type)
								->Where('status', '=', 'ACTIVE')
								->orderBy('name','asc')->get();
		}
		echo json_encode($clparam_details);


	}
	public function bankName(Request $request) {

		$details = DB::select("select b.bank_code,b.description,a.branch_code,bank_account 
			from partnerbank a join
			Olbnknames b on a.bank_code = b.bank_code 
			where a.partner_number = '$request->client_num'
		");
		return $details;
	}
	
	public function fetchallbanks(Request $request) {

		$details = Olbnknames::All();
		return $details;
		
	}

	public function getclmcorrdetails(Request $request) {
		$record_type = $request->get('record_type');
		$claimant_code = $request->get('claimant_code');
		$clmcorrdet =	clmcorr::where('code',$record_type)->get();
		echo json_encode($clmcorrdet[0]);
	}

	public function getmatchingliability(Request $request) {

		$record_type = $request->get('record_type');
		$claim_no = $request->get('claim_no');		
		$clmcorrdet =   DB::select( " select c.peril,c.peril_description from perils  c join clestdtl
		a on a.peril = c.peril where a.corr_type  = '$record_type' and a.claim_no = '$claim_no'  ");
		echo json_encode($clmcorrdet);
	}


	public function client_bank(Request $request) {
		$cli_num = $request->get('client_num');

		$clidet =	Client::where('client_number',$cli_num)->first();

		echo json_encode($clidet);
	}

	public function jsonifyOlbranch(Request $request) {
		
		if ($request->get('bank_code') && $request->get('branch_code')) {
			$bank_code = $request->get('bank_code');
			$branch_code = $request->get('branch_code');
			$olbranch_details = Olbranch::WhereRaw("trim(bank_code)='".trim($bank_code)."' and trim(branch_code)='".trim($branch_code)."'")->get();
			
			echo $olbranch_details;
		} else {
			$bank_code = $request->get('bank_code');
		
			$olbranch_details = Olbranch::WhereRaw("trim(bank_code)='".trim($bank_code)."'")->get();
			
			echo $olbranch_details;
		}
	}
	public function jsonifyOlbnknames(Request $request) {
		$bank_code = $request->get('bank_code');
		$olbnk_details = Olbnknames::WhereRaw("trim(bank_code)='".trim($bank_code)."'")->get();
		echo $olbnk_details;
	}

	public function getdeductionamountrate( Request $request){

		$getrate= Cbdeduct::where('code',$request->code)->first();
		return json_encode($getrate);

	}
	public function jsonifyClaimant(Request $request) {
		$schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

		$claimant_code = $request->get('claimant_code');
		$claim_no = $request->get('claim_no');
		$peril = $request->get('peril');
		$perilitem = $request->get('perilitem');
		$payitem_marker = $request->get('payitem_marker');
		$amount_marker = $request->get('amount_marker');
		$line_no = $request->get('line_no');

		
		//For Requisition Peril item and pay Amount
		if($payitem_marker=='GET'){
			$claimant_details = DB::table(''.$gb.'.claimant')
			->join(''.$gb.'.clmcorr', function ($query) use ($claim_no, $claimant_code,$peril) {
				$query->on('clmcorr.code', '=', 'claimant.rec_ind')
					->Where([
						['claimant.claimant_code', '=', $claimant_code],
						['claimant.claim_no', '=', $claim_no],
						['claimant.cancelled', '<>','Y'],
						['claimant.peril','=',$peril],
						['claimant.status',0],
					]);
			})
			->join(''.$gb.'.perilsitems', function($query) use ($claim_no, $claimant_code,$peril){
				$query->on('perilsitems.peril','=','claimant.peril')
				->on('perilsitems.item_no','=','claimant.perilitem')
				->Where([
						['claimant.claimant_code', '=', $claimant_code],
						['claimant.claim_no', '=', $claim_no],
						['claimant.cancelled', '<>','Y'],
						['claimant.peril','=',$peril]
					]);
			})
			->orderBy('claimant_name','desc')
			->get();
		
		}
		else if($amount_marker=='GET'){
			$claimant_details = Claimant::Where([
						['claimant.claimant_code', '=', $claimant_code],
						['claimant.claim_no', '=', $claim_no],
						['claimant.peril','=',$peril],
						['claimant.line_no','=',$line_no],
						['claimant.perilitem','=',$perilitem]
			])
			->get();
		
		}
		else{
			$claimant_details = DB::table(''.$gb.'.claimant')
			->join(''.$gb.'.clmcorr', function ($query) use ($claim_no, $claimant_code) {
				$query->on('clmcorr.code', '=', 'claimant.rec_ind')
					->Where([
						['claimant_code', '=', $claimant_code],
						['claim_no', '=', $claim_no],
						['claimant.status',0],
						['claimant.cancelled', '<>', 'Y']
					]);
			})->orderBy('claimant_name','desc')
			->get();
		}
	
		echo $claimant_details;
	}

	/**Loss ratio processing */

	// loss ratio view
	public function deflossratioprocess(){
		
		  return view('gb.claims.loss_ratio_report');
	   }

	//procedure
	public function lossratioproc(Request $request){
        $schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
        DB::beginTransaction();

          try {
           
            //procedure to execute loss ratio processing
            $procedureName = ''.$gb.'.process_lossratio';
          
            $resp= DB::executeProcedure($procedureName);

            DB::commit();

            Session::flash('success','Loss Ratio Process Completed Successfully');
            return redirect()->route('claims.deflossratioprocess');
          }catch (\Throwable $e) {
            //dd($e);
            DB::rollback();

            Session::flash('error','Failed to Process Loss Ratio');
            return redirect()->route('claims.deflossratioprocess');
          }

     }
     //End Loss ratio processing


	 public function getclaimantdetails(Request $request) {
	
		$peril = $request->get('peril');
		$perilitem = $request->get('perilitem');
		$claim_no = $request->get('claim_no');
		$item_name = $request->get('item_name');

		$claimamount = Claimant::where('claim_no',$claim_no)
						->where('peril',$peril)
						->where('perilitem',$perilitem)
						->where('cancelled','N')
						->where('status',0)
						->sum('claim_amount');
			// dd($claimamount);
		return json_encode($claimamount);
	}

	public function jsonifyClesdtl(Request $request) {
		$schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

		$peril = $request->get('peril');
		$correspondent = $request->get('correspondent');
		$perilitem = $request->get('perilitem');
		$claim_no = $request->get('claim_no');
		$item_name = $request->get('item_name');

		// Get peril item name
		if($item_name=='P'){
			$clestdtl_details = DB::table(''.$gb.'.clestdtl')
							->join(''.$gb.'.perilsitems', function($query){
								$query->on('perilsitems.item_no','=','clestdtl.perilitem')
								->on('perilsitems.peril','=','clestdtl.peril');

							})
							->where([
									['clestdtl.claim_no', '=', $claim_no],
									['clestdtl.peril', '=', (int) $peril],
									['clestdtl.corr_type', '=', trim($correspondent)],
									['clestdtl.curr_estimate', '<>', 0]
								])
							->get();
		}
		
		// Get Peril amount
		else{
			$clestdtl_details = Clestdtl::Where([
				['claim_no', '=', $claim_no],
				['peril', '=', (int) $peril],
				['perilitem','=',$perilitem],
				// ['clestdtl.corr_type', '=', $correspondent],

				['curr_estimate', '<>', 0]

				])->get();
		}

		echo $clestdtl_details;
	}

	
	public function jsonifyCourtsdet(Request $request){
		$sn_no = $request->get('sn_no');
		$courtsdet_details = Courtsdet::Where('sn_no','=',$sn_no)->get();
		// dd($sn_no);
		echo $courtsdet_details;
	}
	public function jsonifyCourthse(Request $request){
		$sn_no = $request->get('sn_no');
		$courthse_details = Courthse::Where('sn_no','=',$sn_no)->get();
		echo $courthse_details;
	}
	public function jsonifyNatureofjud(Request $request){
		$nature_code = $request->get('nature_code');
		$natureofjud_details = Natureofjud::Where('nature_code','=',$nature_code)->get();
		echo $natureofjud_details;
	}
	public function jsonifyClmsummon(Request $request){
		$clmsummon = Clmsummon::Where('case_no','=',$request->get('case_no'))->first();
		if(!empty($clmsummon)){
			echo 'false';
		}
		else{
			echo 'true';
		}
	}

	public function getclientbankdet(Request $request){
			 $client_name = $request->get('client_name');
			$get_client_det = Client::where('name', $client_name)->first();
			echo $get_client_det;
	}
	
	public function jsonifyClmcorr(Request $request){
		$code = $request->get('code');
		$clmcorr = Clmcorr::Where('code','=',$code)->get();
		echo $clmcorr;
	}

	public function getclaimant(Request $request){
		$code = $request->get('code');
		$claim_no = $request->get('clms');

		$client = clparam::where('claimant_code',$code)->first();
		echo $client;
	}

	public function getexcessdets( Request $request){

		$exes = $request->get('exes');
		$policy = $request->get('policy');

		$p = Polexcess::where('policy_no',$policy)->where('item_no',$exes)->first();
		echo $p;
	}
	public function checkclestdtl(Request $request){

	
		
		$peril = $request->get('peril');
		$perilitem = $request->get('perilitem');
		$claim_no = $request->get('claim_no');
		$payee_type = $request->get('payee_type');

		$clestdtl = Clestdtl::where('claim_no',$claim_no)->where('peril',$peril)
		->where('perilitem',$perilitem)->where('corr_type',$payee_type)->first();

		echo $clestdtl;

	}

	public function jsonifyPerilsitems(Request $request){
		$peril = $request->get('peril');
		$perilitem = $request->get('perilitem');
		$corr_type = $request->get('corr_type');

		// if($perilitem){
			$peril_items = Perilsitems::Where('peril','=',$peril)
				// ->where('item_no','=',$perilitem)
				->whereRaw("trim(corrs_type)='" . $corr_type . "'")
			->orderBy('item_name', 'ASC')->get();
	
		echo $peril_items;
	}
	

	public function checkperilamount(Request $request){
		$amount = $request->get('amount');
		$peril = $request->get('peril');
		// $peril_item = 24;
		$peril_item = $request->get('peril_item');
		$claim_no = removePolicyOrClaimFormat($request->get('claim_no'));
		
		$perilcount = Payreqst::where('claim_no', $claim_no)
								->where('peril',$peril)
								->where('perilitem',$peril_item)
								->count();
				
		$perilcheck = Payreqst::where('claim_no', $claim_no)
								->where('peril',$peril)
								->where('perilitem',$peril_item)
								->get();
				
		// if ($perilcount>0)	{
		if ($perilcount>0 && $perilcheck[0]->cancelled == null )	{
		   $item = $perilcheck[0]->amount;
		}
		else{
			$item = 0 ; 
		}
		echo $item;
	}
	public function checkifperilexists(Request $request){

		$amount = $request->get('amount');
		$peril =$request->get('peril');
		// $peril_item = 24;
		$peril_item =$request->get('peril_item');

		$claim_no = removePolicyOrClaimFormat($request->get('claim_no'));
		
		$getclaimant = Claimant::where('claim_no', $claim_no)
								->where('peril',$peril)
								->where('perilitem',$peril_item)
								->where('status','=',0)
								->where('cancelled','=','N')
								->count();
								// ->sum('claim_amount');
		
		if($getclaimant > 0){
			$getclaimants = Claimant::where('claim_no', $claim_no)
								->where('peril',$peril)
								->where('perilitem',$peril_item)
								->where('status','=',0)
								->where('cancelled','=','N')
								->sum('claim_amount');
			echo $getclaimants;
		}else{

			echo 0 ;
		}

		
	}

	public function jsonifyCbdeduct(Request $request){
		$code = $request->get('code');
		$cbdeduct = Cbdeduct::Where('code','=',$code)->get();
		return $cbdeduct;
	}
	/*End of Helper methods for Ajax autopopulate*/

	/* Claim Tabs for Datatables */
	public function showPerilsDatatable(Request $request){
		$schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

		$claim_no = $request->get('claim_no');

		// $clestdtls = DB::table(''.$gb.'.clestdtl')
		// 	->join(''.$gb.'.perils', function ($join) use ($claim_no) {
		// 		$join->on('clestdtl.peril', '=', 'perils.peril')
		// 		->Where('clestdtl.claim_no', '=', $claim_no);
		// 	})
		// 	->join(''.$gb.'.Perilsitems', function($join) use ($claim_no){
		// 		$join->on('clestdtl.peril','=','perilsitems.peril')
		// 		->on('clestdtl.perilitem','=','perilsitems.item_no')
		// 		->on('clestdtl.corr_type','=','perilsitems.corrs_type')
		// 		->Where('clestdtl.claim_no', '=', $claim_no);
		// 	})

		// 	->orderBy('effective_date','desc')
		// 	->get();
		
		$clestdtls = DB::select("select * from clestdtl a 
		 join perils b on a.peril = b.peril
		 join Perilsitems c on a.peril = c.peril and a.perilitem = c.item_no and a.corr_type = c.corrs_type
		 where claim_no = '$claim_no' order by effective_date desc
		");

		
			return Datatables::of($clestdtls)
			->editColumn('curr_estimate',function($clestdtls){
				return number_format($clestdtls->curr_estimate,2);
			})
			->make(true);
	}
	/* Claim Tabs for Datatables */
	public function showclaimdetailsDatatable(Request $request){

		$schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

		$claim_no = $request->get('claim_no');		
		$test = Clpmn::where('claim_no',$claim_no )->first();
		$get_acc_date = Pipcnam::all(); 
		$check_cause_code = Clhmn::where('claim_no', $claim_no)->get();

		$accident = ClassModel::where('class',$check_cause_code[0]->class )->first();

		$combined = Dcontrol::where('endt_renewal_no',$check_cause_code[0]->endt_renewal_no )->first();
		$comb = ClassModel::where('class',$combined->class )->first();
		// return $comb->combined;
		// dd(0);

		$non_mot = Polsched::where('policy_no',$test[0]->policy_no)->where('section_no',$test[0]->section)->first();


		if($check_cause_code[0]->cause_code == 0 || $check_cause_code[0]->cause_code == null ){

			$clestdtls = DB::select( "select c.description as narration, f.town,f.name as locname, e.description as classdesc, c.sum_insured,c.reg_no,c.notified_flag,c.town, c.location_3, c.location_2, c.location_1, 
			 c.location from clhmn c join
			class e on c.class = e.class 
			join polsect f on c.endt_renewal_no = f.endt_renewal_no and c.location = f.location where claim_no = '$claim_no'");
			// dd(1);

		}
		else{
			$polsectinfo = Polsectend::where('endt_renewal_no', $check_cause_code[0]->endt_renewal_no)->count();
				
			if($polsectinfo>0){
				if(($get_acc_date > $check_cause_code[0]->acc_date)&&($accident->travel == 'Y')){
					$clestdtls = DB::select( "select c.description as narration,g.detail_line as locname, e.description as classdesc, c.sum_insured,c.reg_no,c.notified_flag,c.town, c.location_3, c.location_2, c.location_1,  
					d.description as causedesc, c.section,c.location from clhmn c join
					class e on c.class = e.class 
					join cause d on c.cause_code = d.cause_code 
					join polsched g on c.endt_renewal_no = g.endorse_no and c.section = g.section_no
					where claim_no = '$claim_no'");	
					// dd(2);
				}
				else if(($get_acc_date > $check_cause_code[0]->acc_date)&&($accident->accident == 'Y')&&($accident->travel != 'Y')&&($check_cause_code[0]->dept == 9)){	
				$clestdtls = DB::select( "select c.description as narration,c.town, c.location_3, c.location_2, c.location_1, g.detail_line as locname, e.description as classdesc, c.sum_insured,c.reg_no,c.notified_flag, 
					d.description as causedesc, c.location from clhmn c join
					class e on c.class = e.class 
					join cause d on c.cause_code = d.cause_code 
					join prosched g on c.endt_renewal_no = g.endorse_no and c.location = g.quantity
					where claim_no = '$claim_no'");
					// dd(9);

					if($clestdtls == null ){
						$clestdtls = DB::select( "select c.description as narration,c.town, c.location_3, c.location_2, c.location_1, c.policyholder as locname, e.description as classdesc, c.sum_insured,c.town,c.reg_no,c.notified_flag, 
						d.description as causedesc, c.location from clhmn c join
						class e on c.class = e.class 
						join cause d on c.cause_code = d.cause_code 
						join polsec g on c.endt_renewal_no = g.endt_renewal_no and c.section = g.section_no
						where claim_no = '$claim_no'");
						// dd(10);
					}	
				}

				else if(($get_acc_date > $check_cause_code[0]->acc_date)&&($accident->accident == 'Y')&&($accident->travel != 'Y')&&($accident->medical != 'Y')){

					$clestdtls = DB::select("select c.description as narration,g.detail_line as locname, e.description as classdesc, c.sum_insured,c.reg_no,c.notified_flag, c.town, c.location_3, c.location_2, c.location_1, 
					d.description as causedesc, c.location from clhmn c join
					class e on c.class = e.class 
					join cause d on c.cause_code = d.cause_code 
					join polsched g on c.endt_renewal_no = g.endorse_no and c.location = g.section_no
					where claim_no = '$claim_no'");

				
					// dd(4);
				}
				else if($comb->combined == 'Y'){

					$clestdtls = DB::select( "select c.description as narration,e.description as classdesc, c.sum_insured,c.reg_no,c.notified_flag,c.town, c.location_3, c.location_2, c.location_1,  
					d.description as causedesc, c.location from clhmn c join
					class e on c.class = e.class 
					join cause d on c.cause_code = d.cause_code 
					 where claim_no = '$claim_no'");
					// dd(5);
				}

				else{
					$clestdtls = DB::select( "select c.description as narration,f.town,f.name as locname,  e.description as classdesc, c.sum_insured,c.reg_no,c.notified_flag, c.town, c.location_3, c.location_2, c.location_1, 
					d.description as causedesc, c.location from clhmn c join
					class e on c.class = e.class 
					join cause d on c.cause_code = d.cause_code 
					join polsectend f on c.endt_renewal_no = f.endt_renewal_no and c.location = f.location
					 where claim_no = '$claim_no'");
					//  dd(6);
					}
			}else{

				
				if($accident->open_cover == 'Y'){

					$clestdtls = DB::select( "select c.description as narration,f.ENDT_RENEWAL_NO, 
					f.type_of_pack as locname, 
					e.description as classdesc, d.description as causedesc,
					c.sum_insured,c.reg_no,c.notified_flag,c.town, c.location_3, c.location_2, c.location_1, c.location from clhmn c 
					join class e on c.class = e.class 
					join cause d on c.cause_code = d.cause_code
					LEFT join madtl f on c.policy_no = f.policy_no and c.location = f.location 
					where claim_no = '$claim_no'" );
				}else{

				
					$clestdtls = DB::select( "select c.description as narration,e.description as classdesc, c.sum_insured,c.reg_no,c.notified_flag, c.town, c.location_3, c.location_2, c.location_1, 
					d.description as causedesc, c.location from clhmn c join
					class e on c.class = e.class 
					join cause d on c.cause_code = d.cause_code 
					where claim_no = '$claim_no'" );
				//  dd(7);
				}

			}
		}

			return Datatables::of($clestdtls)
			->editColumn('clmdetails',function($clestdtls){
				return number_format($clestdtls->sum_insured);
			})
			->addColumn('townlocname',function($clestdtls) use(&$check_cause_code){

				if(!empty($clestdtls->reg_no || $clestdtls->plot_no)){
					return $clestdtls->reg_no;
				}else{
					return $clestdtls->locname;
				}				
			})
			->addColumn('causedesc', function($clestdtls) use(&$check_cause_code){
				if(isset($clestdtls->causedesc)){
					return $clestdtls->causedesc;
				}else{
					return '';
				}
			})
			->addColumn('createdby',function($clestdtls) use(&$test){

				if(!empty($clestdtls->user_str || $clestdtls->created_by)){
					return $clestdtls->user_str;
				}else{
					return $test->user_str;
				}				
			})
			->make(true);
	}


	public function showlimitdetails(Request $request){
	
		
			$claim_no = $request->get('claim_no');
			$endorsement = $request->get('endorse');

			$endorsements = Pollimits::where('endt_renewal_no',$endorsement)->get();
		
		

			return Datatables::of($endorsements)

			->editColumn('amount',function($endorsements){
				return number_format($endorsements->amount);

			})
		
			->escapeColumns([])
			->make(true);
	} 

	public function showClaimantsDatatable(Request $request){
		$schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
		$common = $schem['common'];
		
		$claim_no = $request->get('claim_no');

		$showstatus = Clhmn::where('claim_no', '=', $claim_no)->get(); 
        
		
			$claimants = DB::select("select * from claimant a join clmcorr b on a.rec_ind = b.code where claim_no = '$claim_no' order by a.rec_ind desc");
			// $claimants = DB::table(''.$gb.'.claimant')
			// ->join(''.$gb.'.clmcorr', function($query) use($claim_no){
			// 		$query->on('claimant.rec_ind','=','clmcorr.code')
			// 			->where('claim_no', '=', $claim_no);
			// })
			// ->orderBy('rec_ind','desc')
			// ->get();
		
		

			return Datatables::of($claimants)
			->editColumn('appointed', function($claimants){
				return formatDate($claimants->appointed);
			})
			->editColumn('exp_final_report', function($claimants){
				if( $claimants->expected_report == 'Y'){
					
					return formatDate($claimants->exp_final_report);
				}else{
					return 'N/A';
				}
			})
			
			->editColumn('report_status', function($claimants) {
				if ($claimants->expected_report == 'Y') {
					
					if (!formatDate($claimants->rec_final_report)) {
						
						return '<span style="color: white; background-color: red; padding: 0.4px 10px; border-radius: 8px; display: inline-block; white-space: nowrap;">Not Submitted</span>';

					    
					} elseif ($claimants->rec_final_report) {
						
						return '<span style="color: white; background-color: green; padding: 0.4px 10px; border-radius: 8px; display: inline-block; white-space: nowrap;">Submitted</span>';return '<span style="color: white; background-color: orange; padding: 0.4px 30px; border-radius: 8px; display: inline-block;">Submitted</span>';
                        
					} 
				} else {
				
					return 'N/A';
				}
			})
			->editColumn('rec_final_report', function($claimants){
				if( $claimants->expected_report == 'Y'){
					
					return formatDate($claimants->rec_final_report);
				}else{
					return 'N/A';
				}
			})
			->editColumn('hiddendate', function($claimants){
				return $claimants->appointed;
			})
			->editColumn('claim_amount',function($claimants){
				return number_format($claimants->claim_amount,2);

			})
			->addColumn('action', function($claimants) use(&$showstatus){

				$expected_report = '<button class="fill-expected-btn btn-md " 
				data-exp_final_report = "'.$claimants->exp_final_report.'"
				style="cursor:pointer; color: blue;"><i class="fa fa-edit"></i></button>';
				$delete_claimant = '<button class="delete-claimant-btn btn-md "  style="cursor:pointer; color: red;"><i class="fa fa-trash"></i></button>';
				$show_details = '<button class="view-claimant-btn btn-md " 
				data-claim_no=  "'.$claimants->claim_no.'"
				data-line_no=  "'.$claimants->line_no.'"
				data-claimant_code=  "'.$claimants->claimant_code.'"
				data-toggle="modal"
				data-target = "view_claimant_modal"
				style="cursor:pointer; color: blue;"><i class="fa fa-eye"></i></button>';
				if($claimants->cancelled != 'Y'){
			
					if($showstatus[0]->closed=='Y'){
						return $show_details;
					}
					else if($claimants->rec_final_report == null && $claimants->expected_report == 'Y'){
						return $show_details.$expected_report.$delete_claimant;
					}
					else{ 
						 return $show_details.$delete_claimant;
					}
				}
			})
			->escapeColumns([])
			->make(true);
	} 
	public function legal_recoveries(Request $request) {

		DB::beginTransaction();
	
		try {
			$username = trim(Auth::user()->user_name);
			$max = Clmrecoveries::max('id');
			$count = $max ? $max + 1 : 1;
	
			$recover = new Clmrecoveries;
			$recover->claim_no = removePolicyOrClaimFormat($request->claim_no);
			$recover->id = $count;
			$recover->insured = $request->insured;
			$recover->reg_no = $request->reg_no;
			$recover->reg_date = $request->loss_date;
			$recover->advocate = $request->advocate;
			$recover->issued_on = Carbon::now();
			$recover->tp_insurer = $request->tdinsurer;
			$recover->tp_ad = $request->tdadv;
			$recover->description = $request->case_desc;
			$recover->tp_reg_no = $request->tdreg_no;
			$recover->created_by = $username;
			$recover->date_reg = Carbon::now();
			$recover->legal = $request->legal;
	
			$totalAmount = (float) str_replace(',', '', $request->amount);
	
			if (isset($request->recovery_amount)) {
				foreach ($request->recovery_amount as $recoveryAmount) {
					$totalAmount += (float) str_replace(',', '', $recoveryAmount['rec_amt']);
				}
			}
	
			$recover->amount = (float) $totalAmount;
	
			if ($recover->save()) {
				DB::commit();
				Session::flash('success', 'Recovery details saved successfully');

				############ notify tp of recovery ############
				if(strlen($request->tdinsurer) > 0) {
					$email = $request->tp_email;
					$phone = $request->tp_phone;
					$name = $request->tdinsurer;
					$claim_no = removePolicyOrClaimFormat($request->claim_no);
					$policy_dtls = Clhmn::where('claim_no', $claim_no)->select('policy_no', 'policyholder')->first();
					$policy_no = $policy_dtls->policy_no;
					$client_name = $policy_dtls->policyholder;
	
					if($email || $phone ) {
						$notificationData = [
							'email' => [$email],
							'phone' => [$phone],
							'name' => $name,
							'claim_no' => $claim_no,
							'policy_no' => $policy_no,
							'client_name' => $client_name
						];
	
						try {
							DispatchNotificationEvent::dispatch($slug = 'third-party-recovery-notification', $notificationData);
						} catch (\Throwable $th) {
							// throw new \Exception("tp notification errors");
							Session::flash('warning', "Minor hiccups notifying Third Party on the Recovery.");
						}
						
					}
				}

			} else {
				DB::rollback();
				Session::flash('error', 'Failed to save recovery details');
			}
	
			return redirect()->back();
		} catch (\Throwable $e) {

				DB::rollback();
				$error_msg = json_encode($e->getMessage());
				$reference = $request->claim_no;
				$route_name = Route::getCurrentRoute()->getActionName();
				log_error_details($route_name, $error_msg, $reference);
		
				Session::flash('error', 'Failed to save');
				return redirect()->back();

		}
	}

	public function showDocumentsDatatable(Request $request){
		$claim_no = $request->get('claim_no');
		$documents = Laclmreq::Where('claim_no', '=', $claim_no)
		->orderBy('date_received')
		->get();
		return Datatables::of($documents)
		->editColumn('date_received',function($documents){
			return formatDate($documents->date_received);
		})
		->editColumn('received',function($documents){
			if($documents->received=='Y'){
				return '<i class="fa fa-check"></i>';
			}
			else{
				return '<i class="fa fa-times">';
			}
		})
		->addColumn('view_document', function($documents){
			if($documents->received=='Y'){
				return '<button class="btn btn-default btn-xs btn-black btn-block"
				data-toggle="modal"
				data-document_path="'.$documents->document_path.'"
				data-document_name="'.$documents->clmreq_name.'"
				data-target="#view_document">
				<i class="fa fa-eye"></i> View </button>';
			}
			else{
				return '<button class="btn btn-default btn-xs btn-black btn-block"
				data-toggle="modal"
				data-document_name="'.$documents->clmreq_name.'"
				data-target="#upload_document">
				<i class="fa fa-upload"></i> Upload </button>';
			}
		})
		->rawColumns(['received','view_document'])
		->make(true);
	}
	/* ./Claim Tabs for Datatables */

	/*General functions*/
	public function getClass($class){
		$class = ClassModel::Where('class','=',$class)->first();
		return $class;
	}
	public function getDept($dept){
		$dept = Dept::Where('dept','=',$dept)->first();
		return $dept;
	}

	//Claim Validation queries
	public function getDebitmastDetails($policy_no, $acc_date) {
		$debitmast = Debitmast::Where([
			['policy_no', '=', $policy_no],
			['entry_type_descr', '!=', 'CNC'],
			['dr_cr', '=', 'D'],
			['period_from','<=',$acc_date],//Period From Should always be less or equal to date notified
			['period_to','>=',$acc_date]//Period To Should always be greater or equal to date notified
		])
		->first();
		return $debitmast;
	}
	public function reinRecoveries($policy_no,$class,$uw_year){
		$pipcnam = Pipcnam::All()->first();
		$polmaster = Polmaster::Where('policy_no','=',$policy_no)->first();
		$rein_array['polmaster'] = $polmaster;
		if($pipcnam->reinsure=="Y"){
			$class_year = classyear::Where([
				['class','=',$class],
				['uw_year','=',$uw_year]
			])->first();
			$rein_array['class_year']=$class_year;
		}
		return $rein_array;
	}
	public function clhmnExists($policy_no,$endt_renewal_no){
		$clhmn = Clhmn::Where([
			['policy_no', '=', $policy_no],
			['endt_renewal_no','=',$endt_renewal_no]
		])
		->first();
		if(!empty($clhmn)){
			$acc_date = Carbon::parse($clhmn->acc_date);
			$num_days = $acc_date->diffInDays($this->todays_date_time);
			$exists = array('clhmn'=>$clhmn,'num_days'=>$num_days);
			return $exists;
		}
	}
	//End Claim Validation queries

	// Function for generating claim Number
	public function genClaimNo($claim_branch, $claim_class,$period_year,$period_month) {
		$pipcnam = Pipcnam::All()->first();
		// $branch = str_pad((string) $claim_branch, 3, '0', STR_PAD_LEFT);
		$branch = trim(Branch::where('branch',$claim_branch)->pluck('branch_code')->first());
		$class = str_pad((string) $claim_class, 3, '0', STR_PAD_LEFT);

		if($pipcnam->claim_serial_basis ==  "C"){
			//Get serial from classbr
			$claim_serials = Classbr::Select('claim_serial')->Where('class', '=', $claim_class)->first();
			$claim_serial = str_pad((string) $claim_serials->claim_serial, 6, '0', STR_PAD_LEFT);
			//Update Classbr
			$this->updateClassbr($claim_class);

		}
		else if($pipcnam->claim_serial_basis == "O" ){
			//Get Serial Form Dtran0
			$claim_serials = Dtran0::All()->first();
			$claim_serial = str_pad((string) $claim_serials->claim_serial, 6, '0', STR_PAD_LEFT);
			//update Dtran0
			$this->updateDtran0($claim_serials->claim_serial);
		}
		$month = str_pad((string) $period_month, 2, '0', STR_PAD_LEFT);
		$claim_no = $branch . $class . $pipcnam->claim_code . $claim_serial . $period_year . $month;
		return $claim_no;

	}
	/* End ofFunction for generating claim Number*/

	/* function update classbr after saving claim */
	public function updateClassbr($class) {
		$new_serial = Classbr::Where('class', (int) $class)->increment('claim_serial', (int) '1');
		return $new_serial;
	}

	/* function update classbr after saving claim */

	/*get clpmn transation number*/
	public function getTransactionNo() {
		$trans_no = Tran0::All('tran_no')->first();
		$transaction_no = $trans_no->tran_no;
		$this->updateTran0($transaction_no);
		return $transaction_no;
	}
	/*end of get clpmn transaction number*/

	/* update tran0 */
	public function updateTran0($transaction_no) {
		$new_serial = Tran0::Where('tran_no', (int) $transaction_no)->increment('tran_no', 1);
	}
	/* end of update tran0 */
	public function updateDtran0($claim_serial) {
		$new_serial = Dtran0::Where('claim_serial', (int) $claim_serial)->increment('claim_serial', 1);
	}

	public function getCbseqno($account_year, $dept_code) {
		$dtrans_no = Cbseqno::Where([
			['account_year', '=', $account_year],
			['dept_code', '=', $dept_code],
		])->first();
		$cbseqno = $dtrans_no->dtrans_no;
		return $cbseqno;
	}
	public function updateCbseqno($dtrans_no) {
		$new_dtrans_no = Cbseqno::Where('dtrans_no', (int) $dtrans_no)->increment('dtrans_no', 1);
	}
	public function updateClpmn($claim_no,$dtrans_no){
		$update_clpmn = Clpmn::Where([
			['claim_no','=',$claim_no],
			['dtrans_no','=',$dtrans_no]
		])
		->update(['user_str'=>$this->user_name]);

	}

	public function generateRequisitionNo($dept_code, $dtrans_no, $account_year, $account_month) {
		$department_code = str_pad((string) $dept_code, 3, '0', STR_PAD_LEFT);
		$transaction_no = str_pad((string) $dtrans_no, 6, '0', STR_PAD_LEFT);
		$month = str_pad((string) $account_month, 2, '0', STR_PAD_LEFT);

		$requisition_no = $department_code . $transaction_no . $account_year . $month;
		return $requisition_no;
	}

	public function getClaimDetails($claim_no) {
		$clhmn = Clhmn::Where('claim_no', $claim_no)->first();
		return $clhmn;
	}

	public function getAgmnfDetails($branch, $agent) {
		$agmnf = Agmnf::Where([
			['branch', '=', $branch],
			['agent', '=', $agent],
		])->first();

		// $intermediaryParams = new IntermediaryQueryParams([
		// 	'agentNo' => $agent,
		
		// ]);

		// $intermediary_details  = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
		return $agmnf;
	}

	public function getCbtrans($doc_type, $descr) {
		$cbtrans = Cbtrans::Where([
			['doc_type', '=', $doc_type],
			['descr', '=', $descr],
		])->first();
		return $cbtrans;
	}
	public function getDtran0() {
		$dtran0 = Dtran0::All()->first();
		return $dtran0;
	}

	public function insured_has_reserve($claim_no){
		
		try {
			$count = Clestdtl::leftJoin('perilsitems', 'clestdtl.perilitem', '=', 'perilsitems.item_no')
				->where('perilsitems.own_damage', 'Y')
				->where('clestdtl.corr_type', 'Y')
				->where('clestdtl.curr_estimate', '>', 0)
				->where('clestdtl.claim_no', $claim_no)
				->count();
			
			if($count) {
				return true;
			} else {
				return false;
			}
			

		} catch (\Throwable $th) {
			return $th->getMessage();
		}
	}

	public function insured_has_payment($claim_no){
        //check if insured of each salvage vehicle is paid

        try {

			$count = Payreqst::join('perilsitems', function($join) {
                $join->on('payreqst.peril', '=', 'perilsitems.peril')
                    ->on('payreqst.perilitem', '=', 'perilsitems.item_no')
                    ->on('payreqst.payee_type', '=', 'perilsitems.corrs_type');
            	})
				->leftJoin('clmcorr', 'clmcorr.code', '=', 'perilsitems.corrs_type' )
				->where('perilsitems.own_damage', 'Y')
				->where('clmcorr.insured', 'Y')
				->where('voucher_raised', 'Y')
				->where('claim_no', $claim_no)
				->count();

            if($count) {
                return true;
            } else {
                return false;
            }

            
        } catch (\Throwable $th) {
            //throw $th;
            return $th->getMessage();
        }
    }
	/*End of General functions*/
	public function index(Request $request) {
		

        $username = trim(Auth::user()->user_name);
        $aimsuserid = trim(Auth::user()->user_id);

		$aimsuprof = Aimsuprofgb::whereRaw("trim(aims_user)='" . $username . "'")->get()[0];
		
		$aimsuser_web = Aimsuser_web::whereRaw("trim(user_name)='" . $username . "'")->get()[0];

       
		$dtrans_num = Dtran0::all();

		// return Clmdischarge::where('order_number',80)->get();

		$raisegarageorders = Gate::check('raise_garage_orders');
		$printgarageorders = Gate::check('print_garage_orders');
		$closeclaimstatus =  Gate::check('re-open-claim');
		
		// return $raisegarageorders;
		
		
		$schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

		if (Session::get('peril_marker')) {
			$peril_marker = Session::get('peril_marker');
		}

		if (Session::get('documents_marker')) {
			$documents_marker = Session::get('documents_marker');
		}

		if ($request->get('claim_no')) {
			$claim_no = $request->get('claim_no');
			$clhmn = Clhmn::Where('claim_no', $claim_no)->first();
			$class = $clhmn->class;
		} else {
			Session::flash('error', "You can't visit index page without selecting a claim");
			return redirect()->route('claims.enquiry');
		}
		$currency = Currency::Where('currency_code','=',$clhmn->currency_code)->first();
		$class = ClassModel::Where('class','=',$clhmn->class)->first();
		$clmreqs = Clmreq::Where('class', '=',$clhmn->class)->get();

		// $the_class = Class::Where('class', '=',$clhmn->class)->first();

		$limit_dept =  $class->dept;

		// dd($class);


		$checkgroup = $aimsuser_web->aims_group; 


			$limitgroup = Aimsgrouplimits::where('group_id', $checkgroup)
						->where('dept', $limit_dept)
						->get();
			$existing_group =  $limitgroup->count();

		// $reg_no = Modtl::where('reg_no',$clhmn->reg_no )->get(['cover_type']);

		$cover_type = Modtlpivot::where('reg_no',$clhmn->reg_no )
					->where('endt_renewal_no',$clhmn->endt_renewal_no)
					->get(['covertype'])->first();
	  

		//get chassis_no
		$chassis_no = Modtlmast::where('reg_no',$clhmn->reg_no )->value('chassis_no');

		$polsectss = Polsectend::where('endt_renewal_no', $clhmn->endt_renewal_no)->first();
		$non_mot = Polsched::where('policy_no',$clhmn->policy_no)->where('section_no',$clhmn->section)->first();

		
		$sched = Polsched::where('section_no', $clhmn->location)->where('policy_no',$clhmn->policy_no)->first();
		$travelscheds = Polsched::where('section_no', $clhmn->section)->where('endorse_no',$clhmn->endt_renewal_no)->first();
		$polsec = Polsec::where('section_no', $clhmn->section)->where('endt_renewal_no',$clhmn->endt_renewal_no)->first();

		

		$nameoftraveller = Prosched::where('quantity', $clhmn->location)->where('endorse_no',$clhmn->endt_renewal_no)->first();

		$clestdtls = DB::select("select a.*,b.peril_description,b.category as peril_categ,c.item_name from clestdtl  a 
		 left join perils b on a.peril=b.peril
		 left join Perilsitems c on a.perilitem = c.item_no and a.peril = c.peril
		 where claim_no = '$claim_no' and curr_estimate >= 0 order by effective_date desc
		 ");

		$insured_has_payment = $this->insured_has_payment($claim_no);
		$insured_has_reserve = $this->insured_has_reserve($claim_no);

			
	
			$claimant_clestdtls = $clestdtls[0]->peril;

			$clhmn_claim = Clhmn::where('claim_no', $claim_no)->first();

			$corr = Clmcorr::select('name', 'code', 'check_os_premium')
				->join('clestdtl as b', 'b.corr_type', '=', 'code')
				->where('b.claim_no', $request->claim_no)
				->where('b.curr_estimate', '>', 0);
		
			if ($clhmn_claim->rejected == 'Y') {
				$corr = $corr->where('pay_on_reject', 'Y');
			}
			
			$corr = $corr->get();
			
			// For Claimant
			$clmcorrs = Clmcorr::orderBy('name', 'asc');
			
			if ($clhmn_claim->rejected == 'Y') {
				$clmcorrs = $clmcorrs->where('pay_on_reject', 'Y');
			}
			
			if ($class->medical == 'Y') {
				$clmcorrs = $clmcorrs->where('medical', 'Y');
			}
			
			$clmcorrs = $clmcorrs->get();
			
				$financiers = Financier::All();
			

				



		$clestdtl_sum = Clestdtl::Where('claim_no','=',$clhmn->claim_no)->sum('curr_estimate');
		//dd($clestdtl_sum);


		$sum_insured = Debitmast::where('endt_renewal_no',$clhmn->endt_renewal_no)->first();
		// return $sum_insured;

	

		// $claimants = DB::table(''.$gb.'.claimant')
		// 				->whereRaw("cancelled <> 'Y'")
		// 				// ->or("cancelled <> 'Y'")

		// 			->join(''.$gb.'.clmcorr', function($query) use($claim_no){
		// 				$query->on('claimant.rec_ind','=','clmcorr.code')
		// 				->where('claim_no', '=', $claim_no);
		// 			})
		// 			->orderBy('rec_ind','desc')
		// 			->get();
		$claimants = DB::select( "select * from claimant a join CLMCORR b on b.code=a.rec_ind WHERE claim_no='$claim_no' 
					and (cancelled is null or cancelled<>'Y') and (a.status is null or a.status <> 1) order By a.rec_ind desc ");

					//
		$requisition_claimants = $claimants;
		// return $requisition_claimants;
		/*$legal_claimants = Claimant::Where([
			['claim_no', '=', $claim_no],
			['rec_ind','=','C']
		])->get(); */

		$legal_claimants = Clparam::where('record_type','C')->where('status','ACTIVE')->get();
		$legals = Clparam::all();
		$legal_claimants2 = Clparam::where('record_type','E')->where('status','ACTIVE')->get();

			//get currency rate



		// $salvage_claimants_G = Claimant::Where([
		// 	['claim_no', '=', $claim_no],
		// 	['rec_ind','=','G']
		// ])->get();
		$salvage_claimants_G =DB::select("select * from claimant where claim_no = '$claim_no' and(rec_ind = 'G' or rec_ind = 'O') and cancelled <> 'Y'");

		$garages = Clparam::where('record_type', 'G')->select('claimant_code', 'name')->get();
		$salvage_claimants_A = Claimant::Where([
			['claim_no', '=', $claim_no],
			['rec_ind','=','A']
		])->get();
		//$documents = Laclmreq::Where('claim_no', '=', $claim_no)->get();

		$perils = Perils::where('is_active', 'Y')->where('class',$clhmn->class)->orderBy('peril_description', 'ASC')->get();
		$perilitemsx= Perilsitems::Where('link_w_schedule','=','Y')->get();
		
		$limits = Pollimits::where('endt_renewal_no',$clhmn->endt_renewal_no)->get();

		### End For Tabs ###
		

	
		$olbnknames = Olbnknames::All();
		#### End Claimant ####

		#### For Requisition modal####
		$office_codes = Nlparams::Where('prid', '=', 'OFF')->get();

		$department_codes = Nlparams::WhereRaw("prid='DEP' ")->get();
		$source = SourceCodes::where('source_code','CLM')->first();
		$payee_type = DB::Select("select * from claimant c left join clmcorr s on s.code = c.rec_ind  where claim_no = '$claim_no' ");
		#### End Requisition ####

		#   For Requisition Tab   #
		$payreqsts = Payreqst::Where('claim_no', '=', $claim_no)->orderBy('created_time','desc')->get();

					
		$rests = array();
		foreach($payreqsts as $payre){
			$checreqno = Payreqstd::whereRaw("trim(requisition_no)='".trim($payre->req_no)."'")->first();
			array_push($rests, $checreqno);
		}

		$username = Auth::user()->user_name;

		# End For Requisition Tab #
		#For claimhist#

		$clmhists = DB::select("select * from clmhist d join clstatus x on x.status_code = d.status_code 
		where d.claim_no= '$claim_no' order by d.status_date desc
		");
		// $clmhists = DB::table(''.$gb.'.clmhist')
		// 	->join(''.$gb.'.clstatus', function ($query) use ($claim_no) {
		// 		$query->on('clstatus.status_code', '=', 'clmhist.status_code')
		// 			->Where('claim_no', '=', $claim_no);
		// 	})->orderBy('clmhist.status_date','DESC')->get();


		#End For claimhist#
		#Transactions Tab#
		// $clpmns = Clpmn::Where('claim_no', '=', $claim_no)
		// ->orderBy('pay_time','desc')
		// ->get();
		#End Transactions Tab#
		#For close claim#
		$clstatus = Clstatus::All();
		$salvages = Salvages::Where('claim_no','=',$claim_no)->first();

		#End close claim#
		$courthses=Courthse::All();
		$clmsummons=Clmsummon::Where('claim_no','=',$claim_no)->get();
		$clmrecoveries=Clmrecoveries::Where('claim_no','=',$claim_no)->get();
		$clmdis=Clmdischarge::Where('claim_no','=',$claim_no)->where('doc_type', 'FNT')->count();
		$fnts=Clmdischarge::Where('claim_no','=',$claim_no)->where('doc_type', 'FNT')->where('authorized','Y')->get();

		$natureofjuds = Natureofjud::All();
		#For Requisitions
		$cbdeducts = Cbdeduct::where('doc_type', 'PAY')->get();
		$causes = Cause::All();

		#get clmexcess amounts
		$excesses_amount = Clmexcess::whereRaw("trim(claim_number)='".$claim_no."'")->get();

		// return $clhmn->policy_no;
		$excesses = $this->getlatest_excess($clhmn);

		$benefits = Polbenefits::where('policy_no',$clhmn->policy_no)
									->where('endt_renewal_no', $clhmn->endt_renewal_no)
									->get();

		// return $benefits;
		$updstatus = Clstatus::where('reopen_reason','Y')->get();

		$rejstatus 	= Clstatus::where('reject', '=','Y')->get();


		// return	$rejstatus;
				// $excesses_amount = Clmexcess::whereRaw("trim(claim_number)='".$claim_no."'")->get();

		$totexcess_amt = Clhmn::whereRaw("trim(claim_no)='".$claim_no."'")->first();
		// $totexcess = $totexcess_amt->excess_amount;
			// return($totexcess_amt);
		// $get_class = Clmexcess::where('class_type', $exe_at)->get();

		// max order number for garage order
		$garageordernumber=Clmorders::max('order_number');
			if($garageordernumber==null){
				$garageordernumber=0;
			}
		$garageordernumber +=1;

		$today = Carbon::today();

		$claimantinfo=Claimant::where('claim_no','=',$clhmn->claim_no)->get();
		$clmordersinfo=Clmorders::where('claim_no','=',$clhmn->claim_no)
				//->where('claimant_code','=',$claimantinfo->claimant_code)
						->get();
		$vouchertypes=DB::table('clmdisvouchertypes')->get();

		$currency_all = Currency::all();

			$ordernumber=Clmdischarge::max('order_number');
				if($ordernumber==null){
					$ordernumber=1;
				}else{
					$ordernumber++;
				}
				$showstatus = Clhmn::where('claim_no', '=',$clhmn->claim_no)->get(); 

		$policies_details = Polmaster::where('policy_no' ,$clhmn->policy_no)->get()[0];

		$losstype=DB::table('type_of_loss')->get();
 
		$alldetails = DB::select( "select c.description as narration,  e.description as classdesc, c.sum_insured,c.reg_no,c.notified_flag, 
			d.description as causedesc, c.location,  f.period_to,f.period_from from clhmn c join
			class e on c.class = e.class 
			join cause d on c.cause_code = d.cause_code 
			join polmaster f on c.policy_no = f.policy_no
			 where claim_no = '$clhmn->claim_no'" );

			// return $alldetails[0]->classdesc;
		if ($clhmn->status == 'SE'){
			
			$lookstatus = 'Settled';
		}
		else{
			
			$lookstatus = 'Outstanding';
		}

		$modtl = Modtl::where('policy_no', $clhmn->policy_no)->first();

		
		$process = Aims_process::with(['process_dtl',
				'approval_levels'
			])
			->where('process_code',$this->current_process)
			->first();

		// fetch approvals if any
		$approval_dtl = Approvals::with('approval_flow')
			->where('claim_no',$clhmn->claim_no)
			->orderBy('date_created','DESC')
			->first();

		// $checkpendig_amount = Acdet::where('endt_renewal_no',$clhmn->endt_renewal_no)->where("source",'U/W')->first();
		$check_amt = $this->check_pending_amt($clhmn);

		$checkpendig_amount = $check_amt['acdet'];
		$approval_status = $check_amt['status'];
		$approval_msg = $check_amt['message'];
		
		$clm_assess = Clmassess::where('claim_no',$clhmn->claim_no)->count();
		$assess_count = $clm_assess;
		if ($clm_assess == 0) {
			$clm_assess = 0;
		} else {
			$clm_assess = DB::table('clmassess')->where('claim_no',$clhmn->claim_no)->max('assessment_no');
		}

		$id_types = Identity_type::all();
		
		$clpmnx = clpmn::whereRaw("trim(claim_no)='".$claim_no."'")
		->where('doc_type','EST')->orderBy('pay_date','DESC')->first();

		$add_deduct = Cl_additions_deductions::all();

		if($class->motor_policy == 'Y'){

			$dischargetypes = Clmdischarge_voucher::where('motor','Y')->where('active','Y')->get();

		}else{
			$dischargetypes = Clmdischarge_voucher::where('non_motor','Y')->where('active','Y')->get();

		}

		if($class->schedule_upload_mandatory == 'Y'){

			$schedule = DB::select("	
			SELECT c.claim_no,c.peril,c.perilsitem,c.corr_type,sum(c.amount) sched_amt,
			(
				SELECT d.CURR_ESTIMATE FROM  clestdtl d WHERE d.PERIL = c.peril AND d.PERILITEM = c.perilsitem 
				AND d.CORR_TYPE = c.corr_type and d.claim_no = c.claim_no
			) curr_estimate 
			from claim_schedule c where c.claim_no = '$clhmn->claim_no' AND c.paid = 'N' GROUP BY c.claim_no,c.peril,c.perilsitem,c.corr_type
			");

		}else{

		}
		

		$reinalloc = Clmreinalloc::where('claim_no',$claim_no)->get();

		$bustype = Bustype::where('type_of_bus',$clhmn->type_of_bus)->first();
		$dcontrol = Dcontrol::where('endt_renewal_no',$clhmn->endt_renewal_no)->first();
		$clmforms = Clsclaimform::all();
		$madtl = Madtl::where('policy_no',$clhmn->policy_no)->where('location',$clhmn->location)->orderBy('proposal_date','desc')->first();
		
		$dependant = Meddependant::where('member_no', $clhmn->principle_member)
									->where('dependant_id', $clhmn->dependant)
									->select('firstname', 'surname')
									->first();

		$pipcnam = Pipcnam::All()->first();

		$clampaid=Clhmn::where('client_number',$clhmn->client_number)->sum('local_cost_todate');
		$clamest=Clhmn::where('client_number',$clhmn->client_number)->sum('local_curr_total_estimate');
		$out_bal=Acdet::where('client_number',$clhmn->client_number)->sum('unallocated');
		$gross_premium=Debitmast::where('client_number',$clhmn->client_number)->sum('gross_amount');
		$net_premium=Debitmast::where('client_number',$clhmn->client_number)->sum('nett_amount');
		
		if($gross_premium==0 ){
			$lor = 0;

		}else{
			$lor = (($clampaid+$clamest) / $gross_premium) * 100 ;
			$netloss = (($clampaid+$clamest) / $net_premium) * 100 ;

		}
		$irastatuses = IRAstatus::orderBy('status_code')->get();
		$iracomments = IRAcomments::orderBy('status_code')->get();
	
		$identity = DB::table('identity_type')
							->select('identity_code','identity_descr')
							->whereIn('identity_code', ['1', '2','100000001', 'T'])
							->get();
        $new_parameter_details =DB::table('new_claims_parameters')->first();		
		$driver_police_det = DB::table('clhmn')
						->select('clhmn.driver_name','clhmn.driver_gender','clhmn.driver_age','clhmn.license_no','other_vehicle_reg', 'identity_type.identity_descr', 'clhmn.identity', 'clhmn.identity_no', 'clhmn.driver_dob',
							'clhmn.name_of_police', 'clhmn.police_code','clhmn.contact_police','clhmn.police_station',  'clhmn.no_of_persons_involved')
						->leftJoin('identity_type', 'identity_type.identity_code', '=', 'clhmn.identity')
						->where('claim_no', $request->get('claim_no'))
						->get();
		//dd($driver_police_det );
	   $client_details = Client::Where('client_number', $clhmn->client_number)->first();
	   $revise_estimates = $new_parameter_details->use_revise_estimate;
			
	   $checkcov_type = Dcontrol::where('endt_renewal_no', $clhmn->endt_renewal_no)->first();

	   $cover_dates = Dcontrol::where('policy_no', $clhmn->policy_no)
							   // ->where('endt_renewal_no', $clhmn->endt_renewal_no)
					   ->where(function($query){
						   $query->whereNotIn('cancelled',['Y','y'])
							   ->orWhereNull('cancelled');
					   })
					   ->whereIn('trans_type', ['POL', 'REN','CXT','RNS'])
					   ->where('dcon_no', '<=',$checkcov_type->dcon_no )
					   ->orderBy('dcon_no', 'desc')
					   ->first();

	   $count_dcon_term = Dcontrol::where('policy_no', $clhmn->policy_no)
					   ->whereNotIn('trans_type', ['CNC','RFN','NIL'])
					   ->where(function($query){
						   $query->whereNotIn('cancelled',['Y','y'])
							   ->orWhereNull('cancelled');
					   })
					   ->where('period_to', '=',$cover_dates->cov_period_to )
					   ->count();
	   if($count_dcon_term < 1 ){

		   // $pendig_amount = Acdet::where('policy_no',$clhmn->policy_no)->whereRaw("trim(source)='U/W'")->sum('amount');
		   $ost_amount = Acdet::where('endt_renewal_no',$clhmn->endt_renewal_no)->whereRaw("trim(source)='U/W'")->first();
		   if($ost_amount->unallocated > 0 ){
			   $acdet_amount = 1 ;

		   }else{
			   $dcontrol=Dcontrol::where('endt_renewal_no',$clhmn->endt_renewal_no)->first();
			   $acdet_amount = 2;
		   }

	   }else{
		   
		   $ost_amount = Acdet::where('endt_renewal_no',$clhmn->endt_renewal_no)->whereRaw("trim(source)='U/W'")->first();
		   if($ost_amount->unallocated > 0 ){
			   $acdet_amount = 1 ;
		   }else{
			   $acdet_amount = 0;
		   }
	   }

	   $premium = Acdet::where('endt_renewal_no', $clhmn->endt_renewal_no)
            ->where('source', '=', 'U/W')
            ->sum('allocated');
            
        if ($premium != 0) {
				$receiptdate = Acdetallonew::where('endt_renewal_no', $clhmn->endt_renewal_no)
				->where('doc_type', 'DRN')
				->where('amount', '>', 0)
				->orderBy('allocation_date') // Ensures you get the earliest date
                ->value('allocation_date'); // Fetch only the first allocation_date

        } else {
                $receiptdate = null;
        }
	   
	   $tp_insurers=Crmast::where('partner_type','I')->get();

	   $claimCloseReasons = DB::table('CLSTATUS as c')
					->leftJoin('CLSUBSTATUS as c2', 'c.STATUS_CODE', '=', 'c2.REJECTION_CODE')
					->select('c2.*')
					->where('c.SLUG', 'claim-file-closed')
					->where('c2.status','N')
					->get();
	
        $check_claim = Clhmn::where('claim_no',$claim_no )->first();
		
		$dateReg = Carbon::parse($check_claim->date_reg);

		$diffDays = $dateReg->diffInDays(Carbon::now()); 
	
            if ($diffDays >= (int) $new_parameter_details->claim_docs_submission_days) {

				// $claim_docs = DB::table('EDMS_STAGING_PARAMETERS')
				// ->where('CONTEXT_ID',$claim_no )
				// ->where('MANDATORY', 'Y')
				// ->get();

				$claim_docs = DB::table('edms_staging_parameters')
						->where('context_id', $context_id)
						->where('mandatory', 'Y')
						->select(
							'document_name', 
							'document_alias',
							'document_slug', 
							'mandatory as mandatory_flag', 
							'upload_flag', 
							'docs_integrated_to_edms as integrated', 
							'docs_uploaded_to_staging as uploaded', 
							'docs_received as received', 
							'created_at as upload_timestamp', 
							'updated_at as amend_timestamp'
						)
						->get();
          
            }

		$claimTypes = TypeofClaimparam::orderBy('id')->get();

		return View('gb.claims.index')->with(compact('clhmn','checkpendig_amount','client_details', 'documents','alldetails','polsectss','updstatus','rejstatus','legals','limits','username',
			'perils','sched','benefits', 'travelscheds','non_mot','clestdtls','claimant_clestdtls','sum_insured','claimants', 'olbnknames', 'clmreqs', 'peril_marker', 'documents_marker', 'close_claim_modal','ordernumber',
			'clmcorrs', 'financiers', 'corr','claimant_perils','nameoftraveller', 'office_codes', 'department_codes', 'payee_type', 'payreqsts','lookstatus','polsec',
			'clmhists', 'clstatus', 'clpmns','courthses','legal_claimants', 'legal_claimants2', 'clmsummons','clmrecoveries','natureofjuds','clmdis','fnts','perilitemsx',
			'salvage_claimants_A','salvage_claimants_G','salvages','clestdtl_sum','currency','class','policies_details','rests','losstype','add_deduct','dischargetypes',
			'requisition_claimants','cbdeducts','causes', 'garageordernumber','currency_all','claimantinfo','showstatus','dtrans_num','checkcreditclm','aimsuserid','reinalloc',
			'vouchertypes','clmordersinfo','excesses_amount','get_class','totexcess_amt','excesses','raisegarageorders','printgarageorders','closeclaimstatus','limitgroup','existing_group','limit_dept','cover_type', 'chassis_no', 'today',
			'process','approval_status','approval_msg','approval_dtl', 'clm_assess', 'assess_count', 'id_types','clpmnx','schedule'
			,'bustype','dcontrol', 'clmforms', 'garages','madtl', 'dependant','source','pipcnam','lor','netloss', 'irastatuses', 'iracomments','driver_police_det','new_parameter_details','identity','ost_amount','revise_estimates','acdet_amount',
			'tp_insurers', 'insured_has_payment', 'insured_has_reserve','claimCloseReasons','claim_docs','claimTypes','receiptdate'
		));
	}

	public function clmhistlegal_datatable(Request $request)
    {
			$case_no=$request->get('case_no');
			$schem = schemaName();
			$gb = $schem['gb'];
			$gl = $schem['gl'];
			$common = $schem['common'];
          //dd($case_no);

         //$dcontrol=Dcontrol::where('endt_renewal_no',$endt_no)->get();
         //$dcontrol=$dcontrol[0];
		 
		 $limit_query = DB::table("{$gb}.clmhistlegal")
		 ->join("{$gb}.clstatus", function ($join) use ($case_no) {
			 $join->on('clstatus.status_code', '=', 'clmhistlegal.status_code')
				 ->whereRaw('TRIM(clmhistlegal.case_no) = ?', [trim($case_no)]);
		 })
		 ->get();

         //$limit_query=Clmhistlegal::query()  ->where(DB::raw("TRIM(case_no)"),$case_no);

          //$limit_query=Clmhistlegal::all();
         /*$modtl_query=Modtlend::query()->where('policy_no',$dcontrol->policy_no )
                                       ->where('endt_renewal_no',$dcontrol->endt_renewal_no);*/
	
         return datatables::of($limit_query)
         
         ->make(true);

    }

	public function checkMandatoryDocsUpload(Request $request){
		$entityid = $request->input('entityid');
		$contextid = $request->input('contextid');
		$process_code = $request->input('process_code');
		$dept = $request->input('dept');
		
		
		$upload_status = checkDocUploadStatus($entityid, $contextid, $process_code, $dept);
		
		return response()->json($upload_status);

	}

    public function add_histlegal(Request $request){
        //dd($request);
        $pcode = new Clmhistlegal;

        //$count = Pollimits::where('endt_renewal_no',$request->limit_endt_add)->orderBy('limit_no', 'desc')->first();
        //dd($count);
        //$next = $count['limit_no'] + 1;
        //dd(formatPolicyOrClaim($request->claim_no));
        $pcode->case_no = $request->histlegal_case_no;
        $pcode->claim_no = removePolicyOrClaimFormat($request->claim_no);
        $pcode->status_code = $request->status_code;
        $pcode->status_date = $request->status_date;
        $pcode->due_date = $request->due_date;
        $pcode->status_comment = $request->status_descr;

        $pcode->save();
        DB::commit();
    }

	public function checkexcessdebit(Request $request){
		$claim =$request->claim_no;
		$item_no =  $request->item_no;
		$checkexcesdebit =Clmexcess::where(DB::raw('TRIM(claim_number)'),$claim)
						->where('item_no',$item_no)
						->first();
	  return response()->json($checkexcesdebit);
   }
	
	
    public function checkrecoverydebit(Request $request){
		  
			$claim =$request->claim_no;
			$debit_no =  $request->debit_no;
			$checkdebit = Clmrecoveries::where('claim_no', $claim)
							->where('debit_no',$debit_no)
							->first();
		
	      return response()->json($checkdebit);
					
	}
    public function Debitrecovery(Request $request){


			$user_name = trim(Auth::user()->user_name);

			
			$dtran = Dtran0::all();
			$dtran = $dtran[0];
			$old_debit_no = $dtran->debit_no;
			$debit_no = $old_debit_no + 1;
			$creditclm =new Creditclm;
			$creditclm->dr_cr = 'D';
			$creditclm->doc_type = 'DRN';
			$creditclm->entry_type_descr = 'CRS';
			$creditclm->dtrans_no =$debit_no;
			$creditclm->nett_amount = str_replace(',','',$request->edamount);
			$creditclm->unallocated = str_replace(',','',$request->edamount);
			$creditclm->allocated = 0 ;
			$creditclm ->claim_no = $request->edclaim_no;
			// $creditclm->ref_doc = $request->edid;
			$creditclm->dola = Carbon::today();
			$creditclm->tran_no =$debit_no;
			$creditclm->item_desc =$request->edid;
			$creditclm->recovery_date =$request->recovery_date;
			// $creditclm->issued_on =$request->edissued_on;
			$creditclm->user_str =$user_name; 
			DB::beginTransaction();

			try {
			$creditclm ->save();
			DB::commit();
				$creditclm  = Creditclm::all();
				$updran0 = Dtran0::where('debit_no', $old_debit_no)->update([
					'debit_no' => $debit_no
				]);
			
			return redirect()->back()->with('success','Details has been Debitted successfully.');
			}catch (\Throwable $e) {
				// dd($e);
				DB::rollback();
				$creditclm = Creditclm::all();
				return redirect()->back()->with('error','Details has not Debited.');
			}	
	}
	
	public function fac_part_datatable(Request $request ){

		$claim_no = $request->get('claim_no');
		$endorse_no = $request->get('endorse');
		
		$endtrepart = Endtrepart::where('endt_renewal_no',$endorse_no)
		->get();
		return Datatables::of($endtrepart)

		->editColumn('name', function($endtrepart){
			$part = Crmast::where('branch',$endtrepart->branch)->where('agent',$endtrepart->agent)->first();
			return $part->name;
		})
		
		->editColumn('reice_1', function($endtrepart){
			return ($endtrepart->reice_1.' % ');
		})
		
		->addcolumn('cede', function($endtrepart){

			if($endtrepart->cede_method == 'B'){
				$broker = Crmast::where('branch',$endtrepart->broker_branch)->where('agent',$endtrepart->broker_agent)->first();
				$broker = 'Broker - '.$broker->name;
				
			}else if($endtrepart->cede_method == 'D'){
				$broker = Crmast::where('branch',$endtrepart->branch)->where('agent',$endtrepart->agent)->first();
				$broker = 'Direct - '.$broker->name;

			}
			return $broker;

		})

		->addcolumn('action',function($endtrepart)use(&$claim_no){
			$data = Clhmn::where('claim_no',$claim_no)->first();
			$part = Crmast::where('branch',$endtrepart->branch)->where('agent',$endtrepart->agent)->first();
			return '
			<a class="sendletteremails"
				data-toggle="modal"
				data-branch="'. $endtrepart->branch.'"
				data-agent="'. $endtrepart->agent.'"
				data-email="'.$part->email.'"
				data-claim_no="'.$data->claim_no.'"
				data-name="'.$part->name.'" 
				data-target="#sendmail">
				<i class="fa fa-envelope-o btn bg-default">
				</i>
			</a>';
		})

		->rawColumns(['action','cede'])
		->escapeColumns([])
		->make(true);
	}

	public function getuseremails(Request $request){

		$user = Aimsuser::where("left_company",'=','N')
						->get();
	
		echo json_encode($user);

	}


	public function transactionsdat(Request $request ){

		$claim_no = $request->get('claim_no');
		$endorse_no = $request->get('endorse');
		
		$clpmns = Clpmn::Where('claim_no', '=', $claim_no)
		->orderBy('pay_time','desc')
		->get();
		
		// return($clpmns);

		return Datatables::of($clpmns)

		->editColumn('pay_date', function($clpmns){
			// return 'fdsf';
			return formatDate($clpmns->pay_date);
		})
		->editColumn('hidden', function($clpmns){
		
			return $clpmns->pay_date;
		})
		->editColumn('paytime', function($clpmns){
			return formatTime($clpmns->pay_time);
		})
		->editColumn('entry_type', function($clpmns){
			if($clpmns->entry_type_descr == 'OST'){
				return 'Original estimate';
			}
			else if($clpmns->entry_type_descr == 'EST'){
				return 'Revision';
			}else{
				return $clpmns->entry_type_descr;
			}
			
		})
		->editColumn('reference', function($clpmns){
			
			return ($clpmns->doc_type. '/' .$clpmns->dtrans_no.'/'.$clpmns->account_year);
		})
		->editColumn('payamount',function($clpmns){
			return number_format($clpmns->pay_amount,2);

		})
		->editColumn('osamount',function($clpmns){
			return number_format($clpmns->balance,2);

		})
		
		->addColumn('action', function($clpmns) use(&$endorse_no){
		return '<button class="btn btn-primary btn-xs"
		onclick="getClpmnDetails(`'.$clpmns->claim_no.'`,`'.$clpmns->dtrans_no.'`,`'.$endorse_no.'`,
		`'.$clpmns->account_year.'`,`'.$clpmns->account_month.'`,`'.$clpmns->entry_type_descr.'`,
			`'.$clpmns->doc_type.'`,`'.$clpmns->ln_no.'`,`'.$clpmns->pay_type.'`)">Reinsurance</button>';					
		})


		->rawColumns(['action'])
		->escapeColumns([])
		->make(true);

	}
	public function claimhistdat(Request $request ){

		$schem = schemaName();

		$gb = $schem['gb'];
	
		$claim_no = $request->get('claim_no');
		$endorse_no = $request->get('endorse');
		
		// $clmhist = DB::table(''.$gb.'.clmhist')
		// ->join(''.$gb.'.clstatus', function ($query) use ($claim_no) {
		// 	$query->on('clstatus.status_code', '=', 'clmhist.status_code')
		// 		;
		// })->Where('claim_no', '=', $claim_no)->orderBy('clmhist.status_date','desc')->get();

		$clmhist = DB::select("select c.status_date,b.status,b.status_description,c.claimant_code,c.comment_str,c.user_str from clmhist c join
		 clstatus b on c.status_code = b.status_code where c.claim_no = '$claim_no' order by c.status_date desc");

		 
		// dd($clmhist);
		return Datatables::of($clmhist)

		->editColumn('status_date', function($clmhist){
			
			return formatDate($clmhist->status_date);
		})
		->editColumn('hidden_date', function($clmhist){
			
			return $clmhist->status_date;
		})
	
		->escapeColumns(['status_date','hidden_date'])
		->make(true);

	}
	public function benefitdat(Request $request ){

		$schem = schemaName();

		$gb = $schem['gb'];
	
		$claim_no = $request->get('claim_no');
		$endorse_no = $request->get('endorse');
		$benefits = Polbenefits::where('endt_renewal_no',$endorse_no )->get();
		return Datatables::of($benefits)

		->editColumn('ben_amount', function($benefits){
			return number_format($benefits->benefit_amount);
		})

		->editColumn('outstanding', function($benefits){
			if($benefits->os_amount != null){
				return number_format($benefits->os_amount);

			}else{
				return number_format($benefits->benefit_amount);
	
			}
		})

		// ->editColumn('date', function($benefits){
		// 	return formatDate($benefits->updated_by);
		// })
	
		->escapeColumns('ben_amount','outstanding')
		->make(true);

	}
	
	public function excessdat(Request $request ){


		$claim_no = $request->get('claim_no');
		$policy_no = $request->get('policy_no');
		$location = $request->get('location');
		$class = $request->get('class');
		$excesses_amount = Clmexcess::whereRaw("trim(claim_number)='".$claim_no."'")->get();

		// dd($exc = Polexcess::where('policy_no', $policy_no)
		// ->where('item_no',trim($excesses_amount[1]->excess_type))
		// ->first(),
		// $exc->description
		// 	);
	   //dd($excesses_amount);
		return Datatables::of($excesses_amount)

		->editColumn('desc', function($empty) use($policy_no) {

			$exc = Polexcess::where('policy_no', $policy_no)
								->where('item_no',trim($empty->excess_type))
								->first();
			
			return $exc->description;
		})
		->editColumn('excess_amount', function($empty){
			// return 'fdsf';
			return number_format($empty->excess_amt);
		})	

		->addColumn('action', function($data) {
			return '<a class="btn btn-sm btn-danger " >
						<i class="fa fa-minus edgrp"></i></a> 
					';
					
			// return '<a class="btn btn-sm btn-danger " >
			// 			<i class="fa fa-minus edgrp"></i></a> 
			// 			<button class="btn btn-sm btn-success debt"> Debit</button>
			// 		';
		 })
		
		->rawColumns(['action','desc'])
		->escapeColumns(['desc'])
		->make(true);




	}
	public function legalsumdata(Request $request ){

		$claim_no = $request->get('claim_no');
		$clhmn = Clhmn::Where('claim_no', $claim_no)->first();
		$clmsummons=Clmsummon::Where('claim_no','=',$claim_no)->get();

		// dd($clmsummons);
		return Datatables::of($clmsummons)

		->editColumn('legals',function($clmsummons){

			$legals = Clparam::where('claimant_code',$clmsummons->company_advocate )->first();
			if($clmsummons->company_advocate == $legals->claimant_code){
				return $legals->name;
			}else{
				return $clmsummons->company_advocate;
			}
		})

		->editColumn('amounts',function($clmsummons){

			return number_format($clmsummons->amount);
		})

		->editColumn('case_dates',function($clmsummons){

			return formatDate($clmsummons->case_date);
		})
		->editColumn('court_year',function($clmsummons){

			return ($clmsummons->court_year);
		})

		->editColumn('due_dates',function($clmsummons){

			return formatDate($clmsummons->due_date);
		})
		->editColumn('third_party_ad',function($clmsummons){

			return trim($clmsummons->third_party_ad);
		})

		->addColumn('action', function($clmsummons){
						$closed = '';
						if ($clhmn->closed==`Y`) {
							# code...
						
						return '<button class="btn btn-default btn-md edgrp" data-toggle="modal"
						data-claim-no = "'.formatPolicyOrClaim($clmsummons->claim_no).'"
						data-case-no = "'.$clmsummons->case_no.'"
						data-court = "'.$clmsummons->courthse.'"
						data-court-type = "'.$clmsummons->court_type.'"
						data-claimant-name = "'.$clmsummons->claimant_name.'">
						<i class="fa fa-eye "></i>
						</button>
						<button class="btn btn-default btn-xs btn-black" data-toggle="modal"
						data-claim-no = "'.formatPolicyOrClaim($clmsummons->claim_no).'"
						data-case-no = "'.$clmsummons->case_no.'"
						data-court = "'.$clmsummons->courthse.'"
						data-court-type = "'.$clmsummons->court_type.'"
						data-claimant-name = "'.$clmsummons->claimant_name.'"
						data-target = "#judgement_modal"
						>
						<i class="fa fa-pencil-square-o ">Judgement Update</i>
					</button>';		
						}else{

							return '<button class="btn btn-default btn-md" disabled="disabled"
								<i class="fa fa-eye "></i>
								</button>
							<button class="btn btn-default btn-md" disabled="disabled"
								<i class="fa fa-pencil-square-o ">Judgement Update</i>
								</button>
							';
						}			
		})
		->rawColumns(['action'])
		->escapeColumns([])
		->make(true);   

	}
	public function legalrecdata(Request $request ){

		


	}


	public function create_claim(Request $request) {

		Gate::authorize('register-claim');

		$client = $request->get('client');
		$polno = $request->get('polno');
		
		$clhmn_source = 'CLM';
		
		$reidocs = Reidoc::Select('entry_type_descr', 'description')->where('entry_type_descr', '=', $clhmn_source)->get();
		$polmasters = Polmaster::Where('client_number', '=', $client)->get();
		$polmaster1 = Polmaster::Where('policy_no', '=', $polno)->get();

		foreach($polmaster1 as $p){
			$cls = $p->class;
		}
		 $clsr = ClassModel::Where('class','=', $cls)->get();

		$med_class = $clsr[0]->medical;


		if (count($polmasters) == 0) {
			Session::flash('error', "Selected Client doesn't have any active policies");
			return redirect()->back();
		}


		// altenative code to replace js
		// data to be returned to view directly
		$policy_details = Polmaster::Where('policy_no', $polno)->first();

		$client_details = Client::Where('client_number', $policy_details->client_number)
								->first();
		// $intermediary_details = Agmnf::Where('branch', $policy_details->branch)
		// 							->where('agent', $policy_details->agent_no)
		// 							->first();


		$intermediaryParams = new IntermediaryQueryParams([
			'agentNo' => $policy_details->agent_no,
			
		]);

		$intermediary_details  =  IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
     
		$class_details = ClassModel::Where('class',$policy_details->class)
									->first();

		$causes = Cause::Where('class',$class_details->class)
		                ->orWhere('dept',null)
						->get();
		$debb = DB::table('Debitmast')
						->join('dcontrol', 'Debitmast.endt_renewal_no', '=', 'dcontrol.endt_renewal_no')
						->where('Debitmast.policy_no', $policy_details->policy_no)
						->select('Debitmast.*', 'dcontrol.onboard_old_policy')
						->get();
					
		foreach($debb as $item){
			$item->endt_renewal_no;
		}
		if($item->endt_renewal_no == ''){
			$debitmast = $item;
            Session::flash('error','You cannot Create a New Claim since the policy is not debitted');
		}
		else{

			$debitmast = DB::table('Debitmast')
				->join('dcontrol', 'Debitmast.endt_renewal_no', '=', 'dcontrol.endt_renewal_no')
				->where('Debitmast.policy_no', $policy_details->policy_no)
				->where('entry_type_descr', '<>', 'CNC')
				->select('Debitmast.*', 'dcontrol.onboard_old_policy')
				->get();


			if($class_details->open_cover == 'Y'){
				$marinemasterpol = MarinemasterPol::where('policy_no',$policy_details->policy_no)->first();
				if($marinemasterpol->opentype_code == 'UTL'){
					$debitmast = '';
				}
			}
		
		}
		$unallocated_premium = Acdet::where([
			'endt_renewal_no' => $policy_details->policy_no,
			'doc_type' => 'DRN',
			['unallocated','>', 0],
		])->get();

		$cancellations = Debitmast::where([
				'policy_no'=> $policy_details->policy_no,
				'entry_type_descr'=> 'CNC'
			])
			->get();
		
		$crmast = DB::select("SELECT LPAD(c.branch, 3, 0) || c.agent branch_agent, c.name,c.BRANCH,c.AGENT  FROM crmast c INNER JOIN acctype a ON c.account_type = a.acc_type WHERE a.facultative = 'Y'");

		$unallocated_amount = Acdet::where('policy_no',$policy_details->policy_no)->where("source",'U/W')->sum('unallocated');
			
		$region= Region::all();
		$counties = County::all();

		// $towns = Towns::where('county_id', $id)->get(); // id is from the county when clicked
		$medmembers= DB::table('medmembers')->Where('policy_no', $policy_details->policy_no)
								->where('endt_renewal_no', $policy_details->endorse_no)
								->whereNull('cancelled')
								->get();
		$coinpart = Coinpart::where('policy_no',$policy_details->policy_no)->count();

		$identity = DB::table('identity_type')
								->select('identity_code','identity_descr')
								->whereIn('identity_code', ['1', '2', 'T'])
								->get();
       $new_parameter_details =DB::table('new_claims_parameters')->first();		
	   
	   $locationlevels = LocationLevel::select('level_code','level_name','level_type', 'load_method')->where('status','A')->get(); //where level is active/used(county, region)
       $locations = Location::select('loc_code','description','loc_level')->where('active', 'Y')->where('loc_level',1)->get(); //where location is active/used(nairobi, kampala)
	   $clmcorrs = Clmcorr::orderBy('name', 'asc')->get();


	   ###attach IRA
	   $irastatuses = IRAstatus::orderBy('status_code')->get();
	   $iracomments = IRAcomments::orderBy('status_code')->get();
	   $perils = Perils::where('is_active', 'Y')->where('class',$clhmn->class)->orderBy('peril_description', 'ASC')->get();
	   $claimTypes = TypeofClaimparam::orderBy('id')->get();

		return View('gb.claims.register')->with(compact(
			'reidocs', 'polmasters', 'causes','polmaster1','clsr', 'med_class', 'crmast', 'identity','new_parameter_details',
			'policy_details', 'client_details', 'intermediary_details', 'class_details', 'debitmast','coinpart','clmcorrs',
			'unallocated_premium', 'cancellations','unallocated_amount','region', 'counties', 'medmembers', 'locationlevels', 'locations',
			'irastatuses','iracomments','perils','claimTypes'
		));
	}

	public function check_outstanding_premium(Request $request){
		$endt_renewal_no = $request->endt_renewal_no;
		$unallocated_premium = Acdet::where([
				'endt_renewal_no' => $endt_renewal_no,
				'doc_type' => 'DRN',
				['unallocated','>', 0],
			])->count();

		return response()->json(['unallocated_premium' => $unallocated_premium]);
	}

	public function reinsEnquire($clhmn_item){
		$rein = [];
		$rein['debitmast'] = Debitmast::Where('endt_renewal_no','=',$clhmn_item['endt_renewal_no'])->first();
		$rein['pipcnam'] = Pipcnam::All()->first();
		$rein['polmaster'] = Polmaster::Where('policy_no','=',$clhmn_item['policy_no'])->first();
		$rein['classyear'] = classyear::Where([
			['class','=',$clhmn_item['class']],
			['uw_year','=',$rein['debitmast']->uw_year]
		])->first();
		$rein['polremast'] = Polremast::Where([
			['endt_renewal_no','=',$rein['debitmast']->endt_renewal_no]
			//['location','=',$clhmn_item['location']]
		])->first();

		$rein['reinsetup'] = Reinsetup::Where([
			['uw_year','=',$rein['debitmast']->uw_year],
			['class','=',$rein['classyear']->reinclass]
		])->first();
		$rein['reintray'] = Reintray::Where([
			['location','=',$clhmn_item['location']],
			['endt_renewal_no','=',$rein['debitmast']->endt_renewal_no]
			])->first();
		$rein['clremast'] = Clremast::Where('claim_no','=',$clhmn_item['claim_no'])->first();
		return $rein;
	}
	//Function updates into clpmn and clremast
	public function reinsurance($captured,$estimate,$update_clpmn,$new_claim,$old_dtran_no){
		
		$schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

		$gt_curr = Clhmn::where('claim_no',$captured['claim_no'])->first();
		
		$old_estimate = $gt_curr->curr_total_estimate;

		$now_date = Carbon::today()->format('Y-m-d');
            
        $query = "SELECT account_year, account_month FROM period WHERE TO_DATE(:now_date, 'YYYY-MM-DD') BETWEEN period_from AND period_to";

					// Execute the query with the bound parameter
		$period_leo = DB::select($query, ['now_date' => $now_date]);

            $period_leo = $period_leo[0];
   
            // $year = $today->year;
           // $month = $today->month;
        $month=$period_leo->account_month;
        $year=$period_leo->account_year;


		if($new_claim=='Y' && $update_clpmn=='N'){
			$clhmn_item = [
				'claim_no' => $captured->claim_no,
				'policy_no' => $captured->policy_no,
				'endt_renewal_no' => $captured->endt_renewal_no,
				'class' => $captured->class,
				'location' => $captured->location,
			];
		}
		else{
			//If it is not a new claim
			if($update_clpmn=='Y' && $new_claim=='N'){
				//Reserve allocation
				$captured = Clhmn::Where('claim_no','=',$captured['claim_no'])->first();//overrides submitted array
				$update_clhmn = Clhmn::Where('claim_no', '=', $captured['claim_no'])
				->update([
					'orig_total_estimate' => $estimate,
					'curr_total_estimate' => $estimate,
					'temp_amount_prev'	=> $estimate,
					'temp_amount' => $estimate,
					'local_curr_total_estimate' => $estimate * $gt_curr->currency_rate,
					'tran_no'=>$this->getTransactionNo()
				]);

				$clhmn_item = [
					'policy_no' => $captured->policy_no,
					'claim_no' => $captured->claim_no,
					'endt_renewal_no' => $captured->endt_renewal_no,
					'class' => $captured->class,
					'location' => $captured->location,
				];
			}
			else{
				//Reserve Revision
				//dd($estimate);
				$update_clhmn = Clhmn::Where('claim_no', '=', $captured['claim_no'])
				->update([
					'curr_total_estimate' => $estimate,
					'local_curr_total_estimate' => $estimate * $gt_curr->currency_rate,
					'tran_no'=>$this->getTransactionNo()
				]);
				$captured = Clhmn::Where('claim_no','=',$captured['claim_no'])->first(); //overrides submitted array
				$clhmn_item = [
					'policy_no' => $captured->policy_no,
					'claim_no' => $captured->claim_no,
					'endt_renewal_no' => $captured->endt_renewal_no,
					'class' => $captured->class,
					'location' => $captured->location,
				];


			}
		}

		//Gather Reinsurance Details
		$rein=$this->reinsEnquire($clhmn_item);

		$clpmn_items = [
			'claim_no'=> $captured->claim_no,
			'uw_year'=> $captured->uw_year,
			'branch'=> $captured->branch,
			'agent'=> $captured->agent_no,
			'policy_no' => $captured->policy_no,
			'class' => $captured->class,
			'pay_date'=> $this->todays_date,
			'dtrans_no'=>$old_dtran_no,
			'tran_no'=> $old_dtran_no,
			'ln_no'=>0,
			'payee'=>"ESTIMATE",
			'amount'=>$estimate,//orig_total_estimate,
			'pay_amount'=>$estimate,//$captured->orig_total_estimate,
			'payments_todate'=>$captured->cost_todate,
			'balance'=>$estimate,//$captured->orig_total_estimate,
			'peril'=>$captured->peril,
			'account_month'=>$month,
			'account_year'=>$year,
			'user_str'=>$this->user_name,
			'pay_time'=>$this->todays_date_time,
			'dola'=>$this->todays_date,
			'pay_type'=>30,
			'effective_claim'=> $estimate - $old_estimate ,//$captured->temp_amount, /* - temp_amount_prev */
			'movt_total_estimate'=>$estimate - $old_estimate,
			'temp_amount' => $estimate,//$captured->temp_amount,
			'temp_payment' => 0,//$captured->temp_payment,
			'temp_recovery' => 0,//$captured->temp_recovery,
			'currency_code'=>$captured->currency_code,
			'currency_rate'=>$captured->currency_rate,
			//'LOCAL_AMOUNT'=>AMOUNT * Currency_Rate,
			//'LOCAL_EFFECTIVE_CLAIM'=>EFFECTIVE_CLAIM * Currency_Rate;
			'mandatory_reice' => 0, //$clremast_items['mandatory_reice'],
			'company_reice' => 0, //$clremast_items['company_reice'],
			'quota_share_reice' => 0, //$clremast_items['quota_share_reice'],
			'surplus_reice_1' => 0, //$clremast_items['surplus_reice_1'],
			'surplus_reice_2' => 0, //$clremast_items['surplus_reice_2'],
			'surplus_reice_3' => 0, //$clremast_items['surplus_reice_3'],
			'group_reice' => 0, //$clremast_items['group_reice'],
			'facob_reice' => 0, //$clremast_items['facob_reice'],
			'facult_n_reice' => 0, //$clremast_items['facult_n_reice'],
			'facult_p_reice' => 0, //$clremast_items['facult_p_reice'],
			'mandatory_recovery' => 0, //$clremast_items['mandatory_claim'],
			'company_recovery_1' => 0, //$clremast_items['company_claim'],
			'xloss_1_recovery_1' => 0, //$clremast_items['xloss_1_claim'],
			'xloss_1_recovery_2' => 0,
			'xloss_1_recovery_3' => 0,
			'xloss_2_recovery_1' => 0, //$clremast_items['xloss_2_claim'],
			'xloss_2_recovery_2' => 0,
			'xloss_2_recovery_3' => 0,
			'xloss_3_recovery_1' => 0, //$clremast_items['xloss_3_claim'],
			'xloss_3_recovery_2' => 0,
			'xloss_3_recovery_3' => 0,
			'quota_share_recovery' => 0, //$clremast_items['quota_share_claim'],
			'surplus_1st_recovery' => 0, //$clremast_items['surplus_1st_claim'],
		    'surplus_2nd_recovery' => 0, //$clremast_items['surplus_2nd_claim'],
		 	'surplus_3rd_recovery' => 0, //$clremast_items['surplus_3rd_claim'],
		 	'group_recovery' => 0, //$clremast_items['group_claim'],
		 	'facob_recovery' => 0, //$clremast_items['facob_claim'],
		 	'facult_n_recovery' => 0, //$clremast_items['facult_n_claim'],
		 	'facult_p_recovery' => 0, //$clremast_items['facult_p_claim'],
		];

	
		if($update_clpmn=='Y' && $new_claim=='N'){
			$clpmn_items['doc_type'] = "EST";
			$clpmn_items['entry_type_descr'] = "OST";
		}
		else if($update_clpmn=='N' && $new_claim=='Y'){
			$clpmn_items['doc_type'] = "EST";
			$clpmn_items['entry_type_descr'] = "OST";
		}
		else{
			$clpmn_items['doc_type'] = "EST";
			$clpmn_items['entry_type_descr'] = "EST";
		}
	
			$clpmn_items['reinsured'] ="Y";
	
		if($update_clpmn=='Y'){
			//Update CLPMN
			$update_clpmn = Clpmn::Where([
				['claim_no','=',$captured->claim_no],
				['tran_no','=',$captured->tran_no],
				['pay_type','=',30]

				])->update($clpmn_items);
		}
		//Insert into Clpmn
		else{
			 $insert_clpmn = Clpmn::Insert($clpmn_items);
		}

		// DB::beginTransaction();
        //   try {  
            $procedureName = ''.$gb.'.reinsure_claim';
            $bindings = [
			'g_claim_no'=> $captured->claim_no,
            'g_policy_no'=>$captured->policy_no,
            'g_doc_type'=>$clpmn_items['doc_type'],
            'g_entry_type_descr'=>$clpmn_items['entry_type_descr'],
            'g_pay_type'=>(int)$clpmn_items['pay_type'],
	    	'g_dtrans_no'=>$clpmn_items['dtrans_no'],
	    	'g_account_year'=>$clpmn_items['account_year'],
	    	'g_account_month'=>$clpmn_items['account_month'],
	    	'g_ln_no'=>(int)$clpmn_items['ln_no']
	    	//'g_pay_amount'=>(int)$clpmn_item->pay_amount*$clpmn_item->currency_rate
  
            ];
            // dd($bindings);
            $resp= DB::executeProcedure($procedureName, $bindings);
            // Session::flash('success','Reinsured Successfully');
  
	}

	public function onboard_clpmn_payment($captured,$estimate,$update_clpmn,$new_claim,$old_dtran_no){
		
		$schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

		$gt_curr = Clhmn::where('claim_no',$captured['claim_no'])->first();
		
		$old_estimate = $gt_curr->curr_total_estimate;

		$now_date = Carbon::today()->format('Y-m-d');
            
        $query = "SELECT account_year, account_month FROM period WHERE TO_DATE(:now_date, 'YYYY-MM-DD') BETWEEN period_from AND period_to";

					// Execute the query with the bound parameter
		$period_leo = DB::select($query, ['now_date' => $now_date]);

            $period_leo = $period_leo[0];
   
            // $year = $today->year;
           // $month = $today->month;
        $month=$period_leo->account_month;
        $year=$period_leo->account_year;


		
			$clhmn_item = [
				'claim_no' => $gt_curr->claim_no,
				'policy_no' => $gt_curr->policy_no,
				'endt_renewal_no' => $gt_curr->endt_renewal_no,
				'class' => $gt_curr->class,
				'location' => $gt_curr->location,
			];
		

		//Gather Reinsurance Details
		$rein=$this->reinsEnquire($clhmn_item);
		
		if($gt_curr->total_payment > 0){
			$clpmn_payment = [
				'doc_type' => "DRN",
				'entry_type_descr' => "MIG",
				'reinsured' => "Y",
				'claim_no'=> $gt_curr->claim_no,
				'uw_year'=> $gt_curr->uw_year,
				'branch'=> $gt_curr->branch,
				'agent'=> $gt_curr->agent_no,
				'policy_no' => $gt_curr->policy_no,
				'class' => $gt_curr->class,
				'pay_date'=> $this->todays_date,
				'dtrans_no'=>$old_dtran_no,
				'tran_no'=> $old_dtran_no,
				'ln_no'=>0,
				'payee'=>"MIGRATED PAYMENTS",
				'amount'=>$gt_curr->total_payment,
				'pay_amount'=>$gt_curr->total_payment,
				'payments_todate'=>$gt_curr->total_payment,
				'balance'=>$gt_curr->curr_total_estimate - $gt_curr->total_payment,
				'peril'=>$gt_curr->peril,
				'account_month'=>$month,
				'account_year'=>$year,
				'user_str'=>$this->user_name,
				'pay_time'=>$this->todays_date_time->modify('+5 second'),
				'dola'=>$this->todays_date,
				'pay_type'=>10,
				'effective_claim'=> 0 - $gt_curr->total_payment,
				'movt_total_estimate'=> 0 - $gt_curr->total_payment,
				'temp_amount' => $gt_curr->total_payment,
				'temp_payment' => 0,
				'temp_recovery' => 0,
				'currency_code'=>$gt_curr->currency_code,
				'currency_rate'=>$gt_curr->currency_rate,
				//'LOCAL_AMOUNT'=>AMOUNT * Currency_Rate,
				//'LOCAL_EFFECTIVE_CLAIM'=>EFFECTIVE_CLAIM * Currency_Rate;
				'mandatory_reice' => 0, 
				'company_reice' => 0,
				'quota_share_reice' => 0, 
				'surplus_reice_1' => 0, 
				'surplus_reice_2' => 0, 
				'surplus_reice_3' => 0, 
				'group_reice' => 0, 
				'facob_reice' => 0, 
				'facult_n_reice' => 0, 
				'facult_p_reice' => 0, 
				'mandatory_recovery' => 0, 
				'company_recovery_1' => 0, 
				'xloss_1_recovery_1' => 0, 
				'xloss_1_recovery_2' => 0,
				'xloss_1_recovery_3' => 0,
				'xloss_2_recovery_1' => 0, 
				'xloss_2_recovery_2' => 0,
				'xloss_2_recovery_3' => 0,
				'xloss_3_recovery_1' => 0, 
				'xloss_3_recovery_2' => 0,
				'xloss_3_recovery_3' => 0,
				'quota_share_recovery' => 0, 
				'surplus_1st_recovery' => 0, 
				'surplus_2nd_recovery' => 0, 
				'surplus_3rd_recovery' => 0, 
				'group_recovery' => 0, 
				'facob_recovery' => 0, 
				'facult_n_recovery' => 0, 
				'facult_p_recovery' => 0, 
			];

			
			$insert_clpmn_pay = Clpmn::Insert($clpmn_payment);

			$update_clhmn = Clhmn::Where('claim_no', '=', $captured['claim_no'])
				->update([
					'curr_total_estimate' => $gt_curr->curr_total_estimate - $gt_curr->total_payment,
					'temp_amount_prev'	=> $gt_curr->curr_total_estimate ,
					'temp_amount' => $gt_curr->curr_total_estimate - $gt_curr->total_payment,
					'local_curr_total_estimate' => ($gt_curr->curr_total_estimate - $gt_curr->total_payment) * $gt_curr->currency_rate
				]);
			

			$procedureName = ''.$gb.'.reinsure_claim';
				$bindings = [
				'g_claim_no'=> $gt_curr->claim_no,
				'g_policy_no'=>$gt_curr->policy_no,
				'g_doc_type'=>$clpmn_payment['doc_type'],
				'g_entry_type_descr'=>$clpmn_payment['entry_type_descr'],
				'g_pay_type'=>(int)$clpmn_payment['pay_type'],
				'g_dtrans_no'=>$clpmn_payment['dtrans_no'],
				'g_account_year'=>$clpmn_payment['account_year'],
				'g_account_month'=>$clpmn_payment['account_month'],
				'g_ln_no'=>(int)$clpmn_payment['ln_no']
	
				];

				// dd($bindings);
				$resp1 = DB::executeProcedure($procedureName, $bindings);
				
		}
	
	}

	public function onboard_clpmn_recovery($captured,$estimate,$update_clpmn,$new_claim,$old_dtran_no){
		
		$schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

		$gt_curr = Clhmn::where('claim_no',$captured['claim_no'])->first();
		
		$old_estimate = $gt_curr->curr_total_estimate;

		$now_date = Carbon::today()->format('Y-m-d');
            
        $query = "SELECT account_year, account_month FROM period WHERE TO_DATE(:now_date, 'YYYY-MM-DD') BETWEEN period_from AND period_to";

					// Execute the query with the bound parameter
		$period_leo = DB::select($query, ['now_date' => $now_date]);

            $period_leo = $period_leo[0];
   
            // $year = $today->year;
           // $month = $today->month;
        $month=$period_leo->account_month;
        $year=$period_leo->account_year;


		
			$clhmn_item = [
				'claim_no' => $captured->claim_no,
				'policy_no' => $captured->policy_no,
				'endt_renewal_no' => $captured->endt_renewal_no,
				'class' => $captured->class,
				'location' => $captured->location,
			];
		

		//Gather Reinsurance Details
		$rein=$this->reinsEnquire($clhmn_item);

		if($gt_curr->total_recovery < 0){
			$clpmn_receipt = [
				'doc_type' => "CRN",
				'entry_type_descr' => "MIG",
				'reinsured' => "Y",
				'claim_no'=> $gt_curr->claim_no,
				'uw_year'=> $gt_curr->uw_year,
				'branch'=> $gt_curr->branch,
				'agent'=> $gt_curr->agent_no,
				'policy_no' => $gt_curr->policy_no,
				'class' => $gt_curr->class,
				'pay_date'=> $this->todays_date,
				'dtrans_no'=>$old_dtran_no,
				'tran_no'=> $old_dtran_no,
				'ln_no'=>1,
				'payee'=>"MIGRATED RECEIPTS",
				'amount'=>$gt_curr->total_recovery,//orig_total_estimate,
				'pay_amount'=>$gt_curr->total_recovery,//$captured->orig_total_estimate,
				'payments_todate'=>$gt_curr->cost_todate,
				'balance'=>$estimate - $gt_curr->total_payment,//$captured->orig_total_estimate,
				'peril'=>$gt_curr->peril,
				'account_month'=>$month,
				'account_year'=>$year,
				'user_str'=>$this->user_name,
				'pay_time'=>$this->todays_date_time->modify('+10 second'),
				'dola'=>$this->todays_date,
				'pay_type'=>20,
				'effective_claim'=> 0 ,
				'movt_total_estimate'=>0,
				'temp_amount' => $estimate,
				'temp_payment' => 0,
				'temp_recovery' => 0,
				'currency_code'=>$gt_curr->currency_code,
				'currency_rate'=>$gt_curr->currency_rate,
				//'LOCAL_AMOUNT'=>AMOUNT * Currency_Rate,
				//'LOCAL_EFFECTIVE_CLAIM'=>EFFECTIVE_CLAIM * Currency_Rate;
				'mandatory_reice' => 0, 
				'company_reice' => 0,
				'quota_share_reice' => 0, 
				'surplus_reice_1' => 0, 
				'surplus_reice_2' => 0, 
				'surplus_reice_3' => 0, 
				'group_reice' => 0, 
				'facob_reice' => 0, 
				'facult_n_reice' => 0, 
				'facult_p_reice' => 0, 
				'mandatory_recovery' => 0, 
				'company_recovery_1' => 0, 
				'xloss_1_recovery_1' => 0, 
				'xloss_1_recovery_2' => 0,
				'xloss_1_recovery_3' => 0,
				'xloss_2_recovery_1' => 0, 
				'xloss_2_recovery_2' => 0,
				'xloss_2_recovery_3' => 0,
				'xloss_3_recovery_1' => 0, 
				'xloss_3_recovery_2' => 0,
				'xloss_3_recovery_3' => 0,
				'quota_share_recovery' => 0, 
				'surplus_1st_recovery' => 0, 
				'surplus_2nd_recovery' => 0, 
				'surplus_3rd_recovery' => 0, 
				'group_recovery' => 0, 
				'facob_recovery' => 0, 
				'facult_n_recovery' => 0, 
				'facult_p_recovery' => 0, 
			];
		
			
			$insert_clpmn_rec = Clpmn::Insert($clpmn_receipt);
			
	
			$procedureName = ''.$gb.'.reinsure_claim';
				$bindings = [
				'g_claim_no'=> $gt_curr->claim_no,
				'g_policy_no'=>$gt_curr->policy_no,
				'g_doc_type'=>$clpmn_receipt['doc_type'],
				'g_entry_type_descr'=>$clpmn_receipt['entry_type_descr'],
				'g_pay_type'=>(int)$clpmn_receipt['pay_type'],
				'g_dtrans_no'=>$clpmn_receipt['dtrans_no'],
				'g_account_year'=>$clpmn_receipt['account_year'],
				'g_account_month'=>$clpmn_receipt['account_month'],
				'g_ln_no'=>(int)$clpmn_receipt['ln_no']
	
				];
				
				$resp2= DB::executeProcedure($procedureName, $bindings);
				// Session::flash('success','Reinsured Successfully');
		}
	
	}

	public function authorizePayment(Request $request)
    {

            $user = Auth::user()->user_name;
            $today = Carbon::today();

			$req_no = removeRequsitionFormat($request->auth_req_no);
			

			// return $req_no;
			try{
            $payreqst = Payreqst::where('req_no',$req_no)
                                ->update([
                                    'authorized'=>trim($user),
									'authorized_date'=>$today,
									'approved_by'=>trim($user),
                                    'approved_date'=>$today,
                                    'due_date'=>$request->pay_date
								]);
								
			// dd($payreqst);

			Session::Flash('success','Requisition authorized successfully');
			}
			catch (\Throwable $e) {
				// dd($e);
				DB::rollback();
	
				Session::flash('error','Failed to Approve Requisition');
	
			  }

    }
	public function saveClaim(Request $request) {
		#### Markers For modals ####

		$peril_marker = 1;
		####   End markers      ####
		##Account Periods
		$acc_date = new Carbon($request->acc_date);
		$dtran = Dtran0::all();
		$period_month=$dtran[0]->account_month;
        $period_year=$dtran[0]->account_year;
		$claim_year = $accDate->year;
		
		$policy_details = Polmaster::Where('policy_no', '=', $request->policy_no)->first();
		
		$motor_policy = $this->getClass($request->class)->motor_policy;
		$request->request->add(['motor_policy' => $motor_policy]);
		$new_cl_params =DB::table('new_claims_parameters')->first();
		
		if($request->total_payment > 0){
			$tot_payment = removeFormat($request->total_payment);
		}else{	
			$tot_payment = 0;
		}
	

		if($request->total_receipt > 0){
			$tot_receipt = removeFormat($request->total_receipt)*-1;
		}else{
			$tot_receipt = 0;
		}
		
		
	
		if($new_cl_params->est_during_claim_reg == 'Y' ){

			$validator =  Validator::make($request->all(),[
				'location' => 'required',
				'risk_item'=>'required_if:motor_policy,Y',
				'endt_renewal_no' => 'required',
				'description' => 'required',
				'cause_code' => 'required',
				// 'town' => 'required',
				'corrtype_.*' =>'required',
				'peril_allocation.*' =>'required',
				'peril_item_allocation.*' =>'required',
				'orig_estimate_allocation.*' =>'required',
			]);

			$pipcnam = Pipcnam::All()->first();
			if($pipcnam->ira_section_activated == 'Y'){

				$validator1 =  Validator::make($request->all(),[
				
					'ira_peril.*'=>'required',
					'ira_docstatus.*'=>'required',
					'ira_comments.*'=>'required',

				]);

				if ($validator1->fails()) {
					Session::flash('error', 'Check on IRA Required fields which are missing');
					return redirect()->back();
				}
			}

		}else{

			$validator =  Validator::make($request->all(),[
				'location' => 'required',
				'risk_item'=>'required_if:motor_policy,Y',
				'endt_renewal_no' => 'required',
				'description' => 'required',
				'cause_code' => 'required',
				// 'town' => 'required'
	
			]);

		}
		if ($validator->fails()) {
			Session::flash('error', 'Some Required fields are missing');
			return redirect()->back();
		}
		

		$claim_no = "";

		DB::beginTransaction();
		try{
			$claim_no = $this->genClaimNo($request->branch,$request->class,$period_year,$period_month);
			$dfi_checklist = $this->addClaimDFI($request->all(),$claim_no);

			$usernames=trim(Auth::user()->user_name);
			$trans_no = $this->getTransactionNo();

			$insert_clhmn = Clhmn::create([
				###### Set initial variables ######
				'notified_flag' => "Y",
				'amend_mkr' => 1, // Set the Claims Facing File marker
				'uw_mkr' => 1,    //Set the Claims Facing U/Writing marker
				'status' => "OS", //Set the Status Marker to Outstanding
				'revived' => "N", //Set the Claim Revived During Month Marker*/
				'total_recovery' => $tot_receipt,
				'total_payment' => $tot_payment,
				'cost_todate' => $tot_payment + $tot_receipt,
				'closed_nc' => "N",
				'status_code' => 001,
				'status_date' => $request->date_notified,
				'binder' => $policy_details->binder,
				'binder_pol_no' => $policy_details->binder_pol_no,
				//'force_change'=>
				'temp_amount'=>0,#$request->orig_total_estimate,
				'temp_payment'=>0,
				'temp_recovery'=>0,
				'created_by'=>$usernames,
				'claim_no' =>$claim_no,
				'tran_no' =>$trans_no,
				'claim_year' => $claim_year,
				'trans_type' => 'CLM',
				'source' => 'CLM',
				'type_of_bus'=>$request->type_of_bus,
				'section'=>$request->sectionitem[0],
				'policy_no' => $request->policy_no,
				'sum_insured' => removeFormat($request->sum_insured),
				//From Debitmast
				'endt_renewal_no' =>removeFormat($request->endt_renewal_no),
				'uw_year' => $request->uw_year,
				'pol_date1' => $request->period_from,
				'pol_date2' => $request->period_to,
				'period_month'=>$period_month,
				'period_year'=>$period_year,
				'class' => $request->class,
				'dept' => $this->getClass($request->class)->dept,
				'client_number' => $request->client_number,
				'policyholder' => $request->policy_holder,
				'agent_no' => $request->agent_no,
				'branch' => $request->branch,
				'reg_by' => $this->user_name,
				'date_reg' => $this->todays_date,
				'time_reg' => $this->todays_date_time,
				'dola' => $this->todays_date,
				'user_str' => $this->user_name,
				'time' => $this->todays_date_time,
				'location' => $request->location,
				// 'town' => $request->town,
				'reg_no' => $request->risk_item,
				'written_off' => $request->written_off,
				'currency_code' => $request->currency_code,
				'currency_rate'=>$request->currency_rate,
				'acc_date' => $request->acc_date,
				'time_acc' => $request->acc_date . ' ' . $request->time_acc,
				'date_notified' => $request->date_notified,
				'time_notified' => $request->date_notified . ' ' . $request->time_notified,
				'cause_code' => $request->cause_code,
				'description' => $request->description,
				'broker_ref' => $request->broker_ref,

				'excess' => $request->excess,
				'excess_type' => $excess_type,
				'excess_paid' => $excess_paid,
				'excess_per' => $excess_per,
				'excess_amount' => $excess_amount,

				'med_type' => $request->med_type,
				'principle_member' => $request->med_member,
				'dependant' => $request->dependant,
				
				'liability_admitted' => $request->liability_admitted,
				'date_liab_admitted' => $date_liab_admitted,
				'liability_amount' => $liability_amount,
				'orig_total_estimate' => 0,#$request->orig_total_estimate,
				'temp_amount_prev' => 0,#$request->orig_total_estimate,
				'curr_total_estimate' => 0, #$request->orig_total_estimate,
				
				'type_of_claim' => $request->typeclaim,
				'tp_reg_no' =>$request->tpregno,
				'tp_name' => $request->tpname,
				'tp_phone_no' => $request->tpphone,
				'tp_insurer' => $request->tpinsuer,
				'tp_cert_no' => $request->tpcertno,
				'tp_driver_name' => $request->tp_driver_name,
				'tp_driver_address' => $request->tp_driver_address,
				'tp_driver_mobile' => $request->tp_driver_mobile,

				'driver_name' => $request->driver_name,
				'identity' => $request->identity,
				'identity_no' => $request->identity_no,
				'driver_dob' => $request->driver_dob,
				'other_vehicle_reg' => $request->other_vehicle_reg,
				'name_of_police' => $request->name_of_police,
				'contact_police' => $request->contact_police,
				'police_station' => $request->police_station,
				'police_code'=> $request->police_code,
				'license_no'=> $request->license_no,
				'no_of_persons_involved' => $request->no_of_persons_involved,
				'driver_gender'=> $request->driver_gender,
				'driver_age'=> $request->driver_age,

				//new location levels
				'location_1' => $request->input('level_1'),
				'location_2' => $request->input('level_2'),
				'location_3' => $request->input('level_3'),
				'location_4' => $request->input('level_4'),
				'location_5' => $request->input('level_5'),
				'location_6' => $request->input('level_6'),

				// Location labels  
				'loclabel_1' => $request->input('loclevel_1'),
				'loclabel_2' => $request->input('loclevel_2'),
				'loclabel_3' => $request->input('loclevel_3'),
				'loclabel_4' => $request->input('loclevel_4'),
				'loclabel_5' => $request->input('loclevel_5'),
				'loclabel_6' => $request->input('loclevel_6'),

			]);
			
			//Inserts into clpmn && clremast
			//$insert_clremast = $this->reinsurance($request,$debit,$insert_clhmn);
			$estimate = 0;
			$update_clpmn = 'N';
			$new_claim = 'Y';
			$old_dtran_no=$trans_no;
			$insert_clremast = $this->reinsurance($insert_clhmn,$estimate,$update_clpmn,$new_claim,$old_dtran_no);
			

			## Insert section items in another table
			if($request->sectionitem != null){

				for($i = 0; $i < count($request->sectionitem); ++$i){
					$section_item = $request->input('sectionitem')[$i];
					$this->save_section_items($claim_no,$section_item);	

                }
			}
			
			if($dfi_checklist != null){

				foreach ($dfi_checklist as $key => $value) {
			
					##insert into DFI table;
					$createdfi = Claims_DFI::create([
						'claim_no'=>$claim_no,
						'dfi_message'=>$value
					]);
				}
			}

			// update claim status
			$clmhistData = [
				'claim_no' => $claim_no,
				'status_code' => 001, // claim registered
				'slug' => 'claim-registered',
				'overide_status_desc' => 'Y',
				'additional_comment' => 'New Claim Registered',
				'status_date' => $request->date_notified,
			];
			ClaimProcessedEvent::dispatch((object)$clmhistData);

			//Update stolen vehicle Details
			$causes = Cause::where('cause_code',$request->cause_code)->first();

			if($this->getDept($this->getClass($request->class)->dept)->motor=='Y' and $causes->stolen=='Y')
			{
				$insert_clmmodtl = Clmmodtl::Create([
					'claim_no' =>$insert_clhmn->claim_no,
					'policy_no' =>$insert_clhmn->policy_no,
					'reg_no' =>$insert_clhmn->reg_no,
					'sum_insured' =>$insert_clhmn->sum_insured,
					'dola' =>$this->todays_date,
					'user_str'=>$this->user_name,
					'insured'=>$insert_clhmn->policyholder,
					'date_loss'=>$insert_clhmn->date_acc,
				]);

				$modtlmast = Modtlmast::where('reg_no', $request->risk_item)
					->update([
						'status' => 'STL',
					]);
			}

			if($request->tpname !=null || $request->tpinsuer != null){


				$reg_no = Clhmn::where('claim_no' , $claim_no)->first();

				$reg_no = $reg_no->reg_no;
				$tp_reg_no= $request->tpregno;
				$tp_name= $request->tpname;
				$tp_phone_no= $request->tpphone;
				$tp_insurer= $request->tpinsuer;
				$tp_cert_no= $request->tpcertno;
				$tp_driver_name = $request->tp_driver_name;
				$tp_driver_address =  $request->tp_driver_address;
				$tp_driver_mobile =  $request->tp_driver_mobile;

				$branch= $request->tpbranch;
				$agent= $request->tpagent;
				$claim_type = $request->typeclaim;
				$this->thirdpartyclaim($claim_no,$reg_no,$tp_reg_no,$tp_name,$tp_phone_no,$tp_insurer,$tp_cert_no,$branch,$agent,$claim_type,$tp_driver_name,$tp_driver_address,$tp_driver_mobile  );				
			}

			$update_clpmn = Clpmn::Where('claim_no','=',$claim_no)
							->update(['user_str'=>$this->user_name]);

			$workflow = ClassModel::where('class',$request->class)->get();
			$this->add_claim_to_workflow($claim_no,$workflow[0]->claim_workflow_id,$request->pid);

			// DB::commit();
			## insert estimates 
			if($new_cl_params->est_during_claim_reg == 'Y' ){
				// dd('stop here');
						$request->merge(['claim_no' => $claim_no]);
						$request->merge(['tran_no' => $trans_no]);
						$saveclaim = $this->savePeril($request);
			}

      $notificationData = ['claim_no'  =>  $claim_no];
      DispatchNotificationEvent::dispatch($slug = 'claim-registration',$notificationData);
	  DispatchNotificationEvent::dispatch($slug = 'claim-registration-notify-intermediary',$notificationData);
      
      DB::commit();
      Session::Flash('success', 'Claim: ' . formatPolicyOrClaim("".$claim_no."") . ' has been registered');

      // redirection
      if($new_cl_params->est_during_claim_reg == 'Y' ){
						return [
							'claim_no'=>$claim_no,
							'resp' => 1,
							'msg'=>'Record Saved Successfully'
						];
      }

      return redirect()->route('claims.index', ['claim_no' => $claim_no]);

				// if($saveclaim['resp'] == 1 ){
        //   // dd('next stop');
				// 	DB::commit();

				// 	Session::flash('success','Reserve Registered');

				// 		return [
				// 			'claim_no'=>$claim_no,
				// 			'resp' => 1,
				// 			'msg'=>'Record Saved Successfully'
				// 		];
				// }else{
				// 	DB::rollback();
				// 	Session::flash('error','Failed to Register Reserve');
				// 	return [
				// 		'claim_no'=>$claim_no,
				// 		'resp' => 2,
				// 		'msg' => 'Failed to Register a Reserve'
				// 	];
				// }

			// } else{
        
			// 	DB::commit();

      //   // Dispatch Notification Event
      //   $notificationData = ['claim_no'  =>  $claim_no];
      //   DispatchNotificationEvent::dispatch($slug = 'register-claim',$notificationData);

			// 	Session::Flash('success', 'Claim: ' . formatPolicyOrClaim("".$claim_no."") . ' has been registered');
			// 	return redirect()->route('claims.index', ['claim_no' => $claim_no]);

			// }
		}
		catch (\Throwable $e) {
		
			DB::rollback();

			$error_msg = json_encode($e->getMessage());
			$reference = "claim_no: {$claim_no}";
			$module = __METHOD__;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$reference,$module);

			Session::flash('error','Failed to Register claim');
			if($new_cl_params->est_during_claim_reg == 'Y' ){

				return [
					'claim_no'=>$claim_no,
					'resp' => 2,
					'msg' => 'Failed to Register a Reserve'
				];

			}else{

				return redirect()->back();
			}

          }
	}


	public function save_section_items($claim_no, $section_item){
		$clhmn = Clhmn::where('claim_no',$claim_no )->first();

		$sec =new Clsections;
		$sec->claim_no = $claim_no ;
		$sec->section_no = $section_item;
		$sec->policy_no = $clhmn->policy_no;
		$sec->endt_renewal_no = $clhmn->endt_renewal_no;
		$sec->location = $clhmn->location;

		$sec->save();

	}

	public function thirdpartyclaim($claim_no,$reg_no,$tp_reg_no,$tp_name,$tp_phone_no,$tp_insurer,$tp_cert_no,$branch,$agent,$claim_type,$tp_driver_name,$tp_driver_address,$tp_driver_mobile ){

				
		try{
			$branch = (int)$branch;
			$agent = (int)$agent;

			$crmast = Crmast::where('branch',$branch)
							->where('agent',$agent)
							->where('partner_type','I')
							->first();

			$maxclaim = Third_party_claims::where('claim_no',$claim_no)->max('id');

			$tp = New Third_party_claims;
			$tp->claim_no = $claim_no;
			$tp->id = $maxclaim + 1 ;
			$tp->reg_no = trim($reg_no);
			$tp->tp_reg_no = trim($tp_reg_no);
			$tp->tp_name =$tp_name;
			$tp->tp_phone_no = $tp_phone_no;
			$tp->tp_driver_name = $tp_driver_name;
			$tp->tp_driver_address =  $tp_driver_address;
			$tp->tp_driver_mobile = $tp_driver_mobile;
			$tp->tp_insurer = trim($crmast->name);
			$tp->tp_cert_no = trim($tp_cert_no);
			$tp->branch = trim($branch);
			$tp->agent = trim($agent);
			$tp->created_by = trim(Auth::user()->user_name);
			$tp->created_date = Carbon::now();

			$tp->save();

			Clhmn::where('claim_no',$claim_no)->update([
				'type_of_claim'=> $claim_type
			]);

		}
		catch (\Throwable $e) {
			DB::rollback();
          
			$codex = json_encode($e->getMessage());
			$error = explode('\n', $codex);
			$error_msg = $error[1];
			$reference = "claim_no: {$claim_no}";
			$module = __METHOD__;
			$route_name = Route::getCurrentRoute()->getActionName();
			
			log_error_details($route_name,$error_msg,$reference,$module);
			
			Session::flash('error','Failed to Register Third Party Vehicle');
			return redirect()->back();
		
          }	
		
	}

	public function addtpdetsform(Request $request){
		try {
			//code...
			$claim_no = $request->claim_no;
			$reg_no = $request->reg_no;
			$tp_reg_no= $request->tpregno;
			$tp_name= $request->tpname;
			$tp_phone_no= $request->tpphone;
			$tp_insurer= $request->tpinsuer;
			$tp_cert_no= $request->tpcertno;
			$branch= $request->branch;
			$agent= $request->agent;
			$tp_driver_name = $request->tp_driver_name;
			$tp_driver_address =  $request->tp_driver_address;
			$tp_driver_mobile =  $request->tp_driver_mobile;

			$claim_type = $request->typeclaim;
		
			$this->thirdpartyclaim($claim_no,$reg_no,$tp_reg_no,$tp_name,$tp_phone_no,$tp_insurer,$tp_cert_no,$branch,$agent,$claim_type,$tp_driver_name,$tp_driver_address,$tp_driver_mobile );

			$clhmn = Clhmn::where('claim_no',$request->claim_no)
					->update([
						'tp_name'=>trim($request->edtpname),
						'tp_phone_no'=> $request->tpphone,
						'tp_insurer'=>trim($crmast->name),
						'tp_cert_no'=> $request->tpcertno,
						'tp_driver_name' => $tp_driver_name,
						'tp_driver_address' => $tp_driver_address,
						'tp_driver_mobile' => $tp_driver_mobile,

					]);


			return 1;

		} catch (\Throwable $e) {

			

			$codex = json_encode($e->getMessage());
			$error = explode('\n', $codex);
			$error_msg = $error[1];
			$referrence = "claim no = '$claim_no',reg no: '$reg_no' " ;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);
			// dd($e);
			DB::rollback();
			return 2;
		}


	}

	public function edittpdetsform( Request $request){
		DB::beginTransaction();

		try{
			$edtip = Third_party_claims::where('claim_no',$request->claim_no)
			->max('id');

			$id = $request->id ;
			$branch= (int) $request->branch;
			$agent=(int) $request->agent;

			$crmast = Crmast::where('branch',$branch)
			->where('agent',$agent)
			->where('partner_type','I')
			->first();

			$edtp = Third_party_claims::where('claim_no',$request->claim_no)
			->where('id',trim($id) )
			->update([
				'changed_by'=>trim(Auth::user()->user_name),
				'changed_date'=>Carbon::now(),
				'tp_name'=>trim($request->edtpname),
				'tp_phone_no'=> $request->tpphone,
				'tp_driver_name' => $request->tp_driver_n,
				'tp_driver_address' => $request->tp_driver_addr,
				'tp_driver_mobile' => $request->tp_driver_mob,
				'tp_insurer'=>trim($crmast->name),
				'tp_cert_no'=> $request->tpcertno,
				'tp_reg_no'=> $request->tpregno,
				'branch'=>$branch, 
				'agent'=>$agent
			]);

			#update type of claim in clhmn
			$clhmn = Clhmn::where('claim_no',$request->claim_no)
			->update([
				'tp_name'=>trim($request->edtpname),
				'tp_phone_no'=> $request->tpphone,
				'tp_insurer'=>trim($crmast->name),
				'tp_cert_no'=> $request->tpcertno,
				'tp_reg_no'=> $request->tpregno,
				'type_of_claim' => $request->typeclaim,
				'tp_driver_name' => $request->tp_driver_n,
				'tp_driver_address' => $request->tp_driver_addr,
				'tp_driver_mobile' => $request->tp_driver_mob,

			]);
		
			DB::Commit();
			return 1;
		} catch(\Throwable $e) {
			
			$codex = json_encode($e->getMessage());
			$error = explode('\n', $codex);
			$error_msg = $error[1];
			$referrence = $reg_no;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);
			
			DB::rollback(); 
			return 2;
		}
	}

	public function updateClaim(Request $request)
	{
		$claim_no = removePolicyOrClaimFormat($request->claim_no);
		DB::Transaction(function() use ($request,&$claim_no){
			
			$clhmn = Clhmn::where('claim_no',$claim_no)->update([
				'claim_no' =>$claim_no,
				'type_of_bus'=>$request->type_of_bus,
				'policy_no' => removePolicyOrClaimFormat($request->policy_no),
				'sum_insured' => removeFormat($request->sum_insured),
				'endt_renewal_no' =>removePolicyOrClaimFormat($request->endt_renewal_no),
				'dola' => $this->todays_date,
				'user_str' => $this->user_name,
				'time' => $this->todays_date_time,
				'location' => $request->location,
				'town' => $request->town,
				'reg_no' => $request->risk_item,
				'cause_code' => $request->cause_code,
				'description' => $request->description,
			]);
		});
		Session::Flash('success', 'Claim: ' . formatPolicyOrClaim($claim_no) . ' has been updated');
		return redirect()->back(); //route('claims.index', ['claim_no' => formatPolicyOrClaim($request->claim_no)]);
	}

	public function add_claim_to_workflow($claim_no,$id,$process_id,$claim_reopen='N'){
		//$endt_renewal_no = $claim_no;
		

        $dcontrol=Clhmn::where('claim_no', $claim_no)
                          ->get();           
        $dcontrol=$dcontrol[0];

        //check availability in mailbox
        $mailbox_chck=MailBox::where('endt_renewal_no',$claim_no)
                             //->where('policy_no',$dcontrol->policy_no)
                             ->where('process_id',$process_id)
                            // ->where('status','incoming')
                             ->count();

        //if process id is not in mailbox add it else ignore it  
                         

        if($mailbox_chck==0 && !empty($process_id) && $claim_reopen='N'){
 
            $workflows=Workflows::where('id',$id)->get();
            $workflow=$workflows[0];

            

            $escalations=Escalations::where('category',$workflow->id )
                                ->orderBy('code', 'Asc')
                                ->take(1)
                                ->get();


            $escalations=$escalations[0];



            $mail_box_current_stage=MailBox::where('business_process',$workflow->id)
                               ->where('endt_renewal_no',$dcontrol->claim_no)
                               ->where('policy_no',$dcontrol->policy_no)
                               ->orderBy('stage','Desc')
                               ->take(1)
                               ->get();
			$mail_box_current_count=MailBox::where('business_process',$workflow->id)
                               ->where('endt_renewal_no',$dcontrol->claim_no)
                               ->where('policy_no',$dcontrol->policy_no)
                               ->orderBy('stage','Desc')
                               ->take(1)
                               ->count();
            
            if($mail_box_current_count>0){
                $previous_stage=$mail_box_current_stage[0]->stage;

                $escalations_rec=Escalations::where('code',$previous_stage)
                                    ->where('category',$workflow->id)
                                    ->get();


                $escalations_rec=$escalations_rec[0];

                $workflow_config=WorkflowsConfig::where('workflow_id',$id)
                                            ->where('escalation_id',$escalations_rec->id)
                                            ->get();

                 

             /*   if($workflow_config[0]->type=='condition'){

                    $curr_mailbox=MailBox::where('business_process',$workflow->id)
                               ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                               ->where('policy_no',$dcontrol->policy_no)
                               ->orderBy('stage','Desc')
                               ->take(1)
                               ->get();
                
                    $workflow_chosen=[];

                    foreach ($workflow_config as $key => $workflowconfig) {
               
                        if($workflowconfig->type=='condition'){

                            if($workflowconfig->yes_no==$curr_mailbox[0]->yes_no){

                                array_push($workflow_chosen, $workflowconfig);
                            }

                        }

                    }


                    $workflow_config=$workflow_chosen[0];



                }else{ */

                    $workflow_config=$workflow_config[0];

              //  }

                $escalations=Escalations::where('id',$workflow_config->send_to)
                                         ->where('category',$id)
                                         ->get();
           
                $escalations=$escalations[0];

                $next_stage=$escalations->code;


                //dd($next_stage);
           
            }else{

                $next_stage=$escalations->code;
            }
        
            
            //dd(count($mail_box_current_stage));
			$countx = MailBox::max('id');
            $mail= new MailBox;
            $mail->policy_no=$dcontrol->policy_no;
            $mail->endt_renewal_no=$claim_no;
            // $mail->id=$countx + 1;
            $mail->reference_number=$dcontrol->client_number;
            $mail->workflow_id=$workflow->name;
            $mail->stage=$next_stage;

           if(trim($escalations->activity_type) == 'End' || trim($escalations->activity_type) == 'end'){
            //if(empty($next_stage)){
                $mail->status='closed';
            }else{
                $mail->status='incoming';
            }

            $mail->location=1;
            $mail->date_sent= Carbon::today();
            //$mail->date_sent=date('d-m-Y');
            $mail->time_sent= Carbon::now()->toTimeString();
            //$mail->time_sent=date('H:i:s');
            $mail->user_group=$escalations->escalate_to;
            $mail->link_redirect=$escalations->link_redirect;
            $mail->business_process=$workflow->id;
            $mail->from_user_id=Auth::user()->user_id;
            $mail->process_id=$process_id;
           
			
			try{
				$mail->save();

			}catch (\Throwable $e) {

				// dd($e);
			}
            


            //update the previous stage to closed;
            if(count($mail_box_current_stage)>0){

                $mailbox_latest=MailBox::where('business_process',$workflow->id)
                               ->where('endt_renewal_no',$dcontrol->claim_no)
                               ->where('policy_no',$dcontrol->policy_no)
                               ->where('stage',$next_stage)
                               ->get(['stage']);

                $mailbox_previous=MailBox::where('business_process',$workflow->id)
                               ->where('endt_renewal_no',$dcontrol->claim_no)
                               ->where('policy_no',$dcontrol->policy_no)
                               ->where('stage','<',$mailbox_latest[0]->stage)
                               ->orderBy('stage','Desc')
                               ->take(1)
                               ->get();
                    

                $previous_upd=MailBox::where('id',$mailbox_previous[0]->id)->update([
                                    'status'=>'closed'
                                ]);

            }

        }

        if($claim_reopen == 'Y'){


        	$reopenk=MailBox::where('endt_renewal_no',$dcontrol->claim_no)
                             ->where('process_id',$process_id)->get();
                            // dd($reopenl);

            //dd($reopenk);

           /* $ch = Mailbox::where('endt_renewal_no',$dcontrol->claim_no)
            			   ->where('stage','<',$reopenk[0]->stage)
            			   ->orderBy('stage','Desc')
            			   ->take(1)
            			   ->update(['status'=>'incoming']); */

           // $upd = Mailbox::			   

           	$reopenl=MailBox::where('id',$reopenk[0]->id)->delete();

           	$ch = Mailbox::where('endt_renewal_no',$dcontrol->claim_no)
            			   //->where('stage','<',$reopenk[0]->stage)
            			   ->orderBy('stage','Desc')
            			   ->take(1)->get();
            			   
            $upd = Mailbox::where('id',$ch[0]->id)
            			   ->update(['status'=>'incoming']);
             /* $schem = schemaName();
              $gb = $schem['gb'];
              $pushdata=DB::table(''.$gb.'.mailbox')->where('id','=',$reopenk[0]->id)->delete(); */
                         

                                
        }
      
    }

	public function savePeril(Request $request) {
		$pipcnam = Pipcnam::all();
		$class = $request->class;
		$policy_no = $request->policy_no;
		$claim_no = removePolicyOrClaimFormat($request->claim_no);

		$now_date = Carbon::today()->format('Y-m-d');
            
        $query = "SELECT account_year, account_month FROM period WHERE TO_DATE(:now_date, 'YYYY-MM-DD') BETWEEN period_from AND period_to";

					// Execute the query with the bound parameter
		$period_leo = DB::select($query, ['now_date' => $now_date]);

            $period_leo = $period_leo[0];
   
            // $year = $today->year;
           // $month = $today->month;
        $month=$period_leo->account_month;
        $year=$period_leo->account_year;

		$clhmnx = Clhmn::where('claim_no',$claim_no )->first();

		$new_claim = 'N';
		$update_clpmn = 'Y';
		$captured = ['claim_no'=>$claim_no];
		$documents_marker = 1;
		//dd(count($request->input('peril')));
		// DB::transaction(function() use($claim_no,$request,$update_clpmn,$new_claim,$captured){
			// DB::beginTransaction();
			try{
				for ($i = 0; $i < count($request->peril_allocation); ++$i) {
					$perilitemsx=Perilsitems::where('peril',$request->input('peril_allocation')[$i])
											->where('item_no',$request->input('peril_item_allocation')[$i])
											->get();
					$perilitemsx = $perilitemsx[0];
					
					$clhmnx = Clhmn::where('claim_no',$claim_no)->get();
					$clhmnx = $clhmnx[0];
					// $input_limit = $request->input('limit')[$i];
					// if($input_limit == null){
					// 	$input_limit = 0;
					// }
									
					$insert_clestdtl = new Clestdtl;
					$insert_clestdtl->claim_no = $claim_no;
					$insert_clestdtl->peril = $request->input('peril_allocation')[$i];
					$insert_clestdtl->perilitem = $request->input('peril_item_allocation')[$i];
					$insert_clestdtl->corr_type =  $request->input('corrtype_')[$i];
					$insert_clestdtl->benefit = 'N';
					// $insert_clestdtl->limit_no = $input_limit;
					$insert_clestdtl->effective_date = $this->todays_date;
					$insert_clestdtl->tran_no = $request->tran_no;
					$insert_clestdtl->orig_estimate =str_replace(',','', $request->input('orig_estimate_allocation')[$i]);
					$insert_clestdtl->curr_estimate = str_replace(',','',$request->input('orig_estimate_allocation')[$i]);
					$insert_clestdtl->doc_type = 'EST';
					$insert_clestdtl->indice = (int) '1';
					$insert_clestdtl->ln_no = (int) '1';
					$insert_clestdtl->reference = 'OST';
					$insert_clestdtl->currency_code = $clhmnx->currency_code;
					$insert_clestdtl->currency_rate = $clhmnx->currency_rate;
					$insert_clestdtl->local_curr_estimate =str_replace(',','', $request->input('orig_estimate_allocation')[$i])* $clhmnx->currency_rate;
					$insert_clestdtl->local_orig_estimate =str_replace(',','', $request->input('orig_estimate_allocation')[$i]);
					$insert_clestdtl->payments_todate=$clhmnx->total_payment;

					$insert_clestdtl->save();
						
					## update clpmnperildtl
					$insert_clpmnperildtl = new Clpmnperildtl;
					$insert_clpmnperildtl->claim_no = $claim_no;
					$insert_clpmnperildtl->policy_no = $clhmnx->policy_no;
					$insert_clpmnperildtl->doc_type = 'EST';
					$insert_clpmnperildtl->entry_type_descr = 'OST';
					$insert_clpmnperildtl->pay_type = 30;
					$insert_clpmnperildtl->class = $clhmnx->class;
					$insert_clpmnperildtl->pay_amount =str_replace(',','', $request->input('orig_estimate_allocation')[$i]);
					$insert_clpmnperildtl->amount = str_replace(',','',$request->input('orig_estimate_allocation')[$i]);
					$insert_clpmnperildtl->pay_date = $this->todays_date;
					$insert_clpmnperildtl->dola = $this->todays_date;
					$insert_clpmnperildtl->payee = trim($perilitemsx->item_name).' ESTIMATE';
					$insert_clpmnperildtl->dtrans_no = $request->tran_no;
					$insert_clpmnperildtl->account_year = $year;
					$insert_clpmnperildtl->account_month = $month;
					$insert_clpmnperildtl->peril = $request->input('peril_allocation')[$i];
					$insert_clpmnperildtl->user_str = $this->user_name;
					$insert_clpmnperildtl->pay_time = Carbon::now();
					$insert_clpmnperildtl->branch = $clhmnx->branch;
					$insert_clpmnperildtl->agent = $clhmnx->agent_no;
					$insert_clpmnperildtl->effective_date = $clhmnx->date_reg;
					$insert_clpmnperildtl->balance =str_replace(',','', $request->input('orig_estimate_allocation')[$i])* $clhmnx->currency_rate;
					$insert_clpmnperildtl->payments_todate = $clhmnx->total_payment;
					$insert_clpmnperildtl->uw_year = $clhmnx->uw_year;
					$insert_clpmnperildtl->location =$clhmnx->location;
					$insert_clpmnperildtl->section = $clhmnx->section;
					$insert_clpmnperildtl->curr_total_estimate = $request->input('orig_estimate_allocation')[$i]* $clhmnx->currency_rate;
					$insert_clpmnperildtl->closing_balance = $request->input('orig_estimate_allocation')[$i]* $clhmnx->currency_rate;
					$insert_clpmnperildtl->perilitem = $request->input('peril_item_allocation')[$i];
					$insert_clpmnperildtl->ln_no =(int) '1';
					$insert_clpmnperildtl->currency_code =$clhmnx->currency_code;
					$insert_clpmnperildtl->currency_rate = $clhmnx->currency_rate;
					$insert_clpmnperildtl->local_amount = $request->input('orig_estimate_allocation')[$i]* $clhmnx->currency_rate;
					// $insert_clpmnperildtl->payments_todate=0;
					$insert_clpmnperildtl->corr_type = $request->input('corrtype_')[$i];

					$insert_clpmnperildtl->save();
					

					if($this->NewClaimsParameters->capture_payment_sla == 'Y'){
						## insert processed event 
						$add_process_to_sla = [
							'unique_item' => [	'claim_no'=>$claim_no,
												'peril'=> $request->input('peril_allocation')[$i],
												'perilitem'=>$request->input('peril_item_allocation')[$i],
												'corr_type'=>$request->input('corrtype_')[$i]
											],
							'slug' => 'claim-payment-process',
							'activity_slug' => 'reserve',
						];
						AddProcessSLA::dispatch((object)$add_process_to_sla);
					}
				}	

				$old_dtran_no = $request->tran_no;

				##IRA STATUS
				$pipcnam = Pipcnam::All()->first();
				if($pipcnam->ira_section_activated == 'Y'  && !empty($request->ira_status)){
					$irastatuses = $request->ira_status;
					foreach ($irastatuses as $index => $irastatus_code) {
							$peril = $request->ira_peril[$index];
							$ira_docstatus = $request->ira_docstatus[$index];
							$ira_comment_code = $request->ira_comments[$index];
							$doc_type = 'OST';
							$this->UpdateCreateClestIRA($claim_no,$peril,$doc_type,$request->tran_no,$ira_docstatus,$irastatus_code,$ira_comment_code);
					};
					
				}

				$this->reinsurance($captured,str_replace(',','',$request->distributed_amount),$update_clpmn,$new_claim,$old_dtran_no);
				if($request->total_payment > 0){
				$this->onboard_clpmn_payment($captured,str_replace(',','',$request->distributed_amount),$update_clpmn,$new_claim,$old_dtran_no);
				}
				if($request->total_receipt > 0){
			    $this->onboard_clpmn_recovery($captured,str_replace(',','',$request->distributed_amount),$update_clpmn,$new_claim,$old_dtran_no);
				}
				$update_clpmn = Clpmn::Where('claim_no','=',$claim_no)
								->update(['user_str'=>$this->user_name]);

				// update claim status
				$clmhistData = [
					'claim_no' => $claim_no,
					'slug' => 'claim-amount-reserved',
					'overide_status_desc' => 'Y',
					'additional_comment' => 'claim amount reserved '. $request->distributed_amount,
				];
				ClaimProcessedEvent::dispatch((object)$clmhistData);

				$workflow = ClassModel::where('class',$class)->get();
				$this->add_claim_to_workflow($claim_no,$workflow[0]->claim_workflow_id,$request->input('pid'));
				$clpmn = Clpmn::where('claim_no',$claim_no)
					->where('entry_type_descr','OST')
					->first(['curr_facult_p_estimate','uw_year','movt_quota_estimate','movt_surplus_1_estimate','movt_surplus_2_estimate','movt_surplus_3_estimate']); 

				$bustype = Bustype::where('type_of_bus',$clhmnx->type_of_bus)->first();
				if($bustype->co_insure == 'Y' && $bustype->leader == 'Y' ){

					$this->coinsurancenotification($clhmnx,str_replace(',','',$request->distributed_amount));
				}
				$check_xloss = Clpmn::where('claim_no',$claim_no)->where('entry_type_descr','OST')->pluck('curr_xloss_1_estimate')->first(); 
				
				$sum_clestdtl = Clestdtl::where('claim_no',$claim_no)->sum('curr_estimate');
				$pipcnam = Pipcnam::All()->first();
				$max_reserve_allocation = $pipcnam->claim_reserve_limit;

				if( ($sum_clestdtl*$clhmnx->currency_rate)  > $max_reserve_allocation){
					$notificationData = [
						'clhmn' => $clhmnx,
						'sum_clestdtl' => $sum_clestdtl,
						'max_reserve_allocation' => $max_reserve_allocation,
					];
					DispatchNotificationEvent::dispatch($slug = 'reserve-limit',$notificationData);
					// $this->sendmaxreservelimit($clhmnx,$sum_clestdtl,$max_reserve_allocation);
				}
				if($check_xloss > 0){
					$notificationData = [
						'clhmn' => $clhmnx,
						'check_xloss' => $check_xloss
					];
					DispatchNotificationEvent::dispatch($slug = 'xol-estimate-claim-notification',$notificationData);
					// $this->sendxlossnotification($clhmnx,$check_xloss);
				}

				if($clpmn->curr_facult_p_estimate > 0){
					$notificationData = [
						'clhmnx' => $clhmnx,
						'clestdtl' => $insert_clestdtl,
						'revise' => 'Y',
					];
					DispatchNotificationEvent::dispatch($slug = 'facultative-estimate-claim-notification',$notificationData);
					// $this->sendfacnotification($clhmnx, $insert_clestdtl, 'N');
				}

				$notificationData = [
					'clpmn' => $clpmn,
				];
				DispatchNotificationEvent::dispatch($slug = 'cash-loss-alert',$notificationData);
				// $this->sendCashLossNotification($clpmn);
				$new_cl_params =DB::table('new_claims_parameters')->first();	
		
				if($new_cl_params->est_during_claim_reg == 'N' ){
					Session::Flash('success', 'Claim reserve allocation posted!');
					DB::commit();
					return [
						'claim_no' => $claim_no,'fac'=>$check_fac
					];
				}else{
					// DB::commit();
					return [
						'claim_no' => $claim_no,'fac'=>$check_fac,
						'resp'=>1
					];
				}
			}catch (\Throwable $e) {
				
				DB::rollback();
				$error_msg = json_encode($e->getMessage());
				$reference = "claim_no: {$request->claim_no}";
				$module = __METHOD__;
				$route_name = Route::getCurrentRoute()->getActionName();
				log_error_details($route_name,$error_msg,$reference,$module);

			

            	Session::flash('error','Failed to Add');

				if($new_cl_params->est_during_claim_reg == 'N' ){
					Session::Flash('error', 'Claim reserve allocation Failed to be Posted!');

					return [
						'claim_no' =>$claim_no,
						'fac'=>$check_fac
					];
				}else{
					
					throw $e;
				}
          }
		  
	
	}
	public function claimDocumentStore(Request $request){
		$claim_no = removeFormat($request->claim_no);
		$document = $request->document_path;
		$encrypt_name = md5_file($document->getRealPath());
		$document_extention = $document->guessExtension();
		$document_name = $encrypt_name . $claim_no . '.' . $document_extention;
		

		DB::transaction(function() use($request,$claim_no,$document_name,$document){
			$update_laclmreq = Laclmreq::Where([
				['claim_no','=', $claim_no],
				['class','=', $request->class],
				['clmreq_name', '=', $request->clmreq_name]
			])
			->update([
				'claim_no' => $claim_no,
				'class' => $request->class,
				'policy_no' => $request->policy_no,
				'clmreq_name' => $request->clmreq_name,
				'received' => 'Y',
				'date_received' => $this->todays_date_time,
				'document_path' => 'edms/' . $document_name,
				'received_by' => $this->user_name
			]);
			//store Document in edms folder
			$store_document = $document->move('edms/', $document_name);
		});
		return redirect()->back()->with('success', $request->clmreq_name.' has been uploaded');
	}
	public function claimDocumentsStore(Request $request) {
		/*items for index*/
		$claim_no = removePolicyOrClaimFormat($request->input('claim_no'));
		$class = $request->input('class');
		$policy_no = $request->input('policy_no');
		$reserve_amount = $request->input('reserve_amount');


		for ($i = 0; $i < count($request->clmreq_name); ++$i) {
			//dd($request->document_path[$i]);
			$check_exists = Laclmreq::where('claim_no',$claim_no)->where('class',$class)->where('clmreq_name',$request->clmreq_name[$i])->count();

			if ($request->clmreq_name[$i] && $check_exists<1) {
				//Edms
				$folder_id = $request->folder_id[$i];
				$objects_id = Folders::Where('id','=',$folder_id)->first();
				$object_id=$objects_id->object_id;

				if(!empty($request->document_path[$i])){
					$document = $request->document_path[$i];
					//$document_name = $claim_no.'-'.$document->getClientOriginalName();
					//$document->move('uploads/claim_documents/',$document_name);
					$encrypt_name = md5_file($document->getRealPath());
					$document_extention = $document->guessExtension();
					$document_name = $encrypt_name . $claim_no . '.' . $document_extention;
					$store_document = $document->move('edms/', $document_name);
					$received = 'Y';
					$received_by = $this->user_name;
					$date_received = $this->todays_date_time;
				}
				else{
					$document_name = "";
					$document_extention = "";
					$received = 'N';
					$received_by = "";
					$date_received = "";
				}

				DB::transaction(function() use($request,$i,$claim_no,$document_name,$document_extention,
					$object_id,$received,$received_by,$date_received){
					$insert_lacremreq = new Laclmreq;
					$insert_lacremreq->claim_no = $claim_no;
					$insert_lacremreq->class = $request->class;
					$insert_lacremreq->policy_no = $request->policy_no;
					$insert_lacremreq->clmreq_name = $request->clmreq_name[$i];
					$insert_lacremreq->received = $received;
					$insert_lacremreq->date_received = $date_received;
					$insert_lacremreq->document_path = 'edms/' . $document_name;
					$insert_lacremreq->received_by = $received_by;
					$insert_lacremreq->save();

					$insert_documents = new Documents;
					$insert_documents->folder_id=$request->folder_id[$i];
					$insert_documents->index_name=$claim_no;
					$insert_documents->name=$document_name;
					$insert_documents->extension=$document_extention;
					$insert_documents->created_at=$this->todays_date;
					$insert_documents->updated_at=$this->todays_date;
					//$insert_documents->object_id=$object_id;
					$insert_documents->save();

				});


			}

		}
		return redirect()->back()->with('success', 'Claim Documents have been uploaded');
	}

	public function checkIfExistsClestdl($claim_no,$peril,$perilitem,$correspondent_type){
		
	
		$clestdtls = Clestdtl::Where([
			['claim_no','=',$claim_no],
			['peril','=',$peril],
			['perilitem','=',$perilitem],
			['corr_type','=',$correspondent_type]
		])->get();

		if($clestdtls->count()==1){
			$clestdtl_exists = 1;
		}
		else{
			$clestdtl_exists = 0;
		}
		return $clestdtl_exists;
	}

	public function reviseClaimEstimate(Request $request) {
		$schem = schemaName();
		// dd($request);
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

		$claim_no = removePolicyOrClaimFormat($request->claim_no);
		$estimate = str_replace(',','',$request->distributed_amount_revision);

		//Reinsurance variables
		$new_claim = 'N';
		$update_clpmn = 'N';
		$captured = ['claim_no' => $claim_no];
		//$estimate = 500;

		$now_date = Carbon::today()->format('Y-m-d');
            
        $query = "SELECT account_year, account_month FROM period WHERE TO_DATE(:now_date, 'YYYY-MM-DD') BETWEEN period_from AND period_to";

					// Execute the query with the bound parameter
		$period_leo = DB::select($query, ['now_date' => $now_date]);

        $period_leo = $period_leo[0];
   
            // $year = $today->year;
           // $month = $today->month;
        $month=$period_leo->account_month;
        $year=$period_leo->account_year;

	
			// DB::beginTransaction();
			DB::connection(env('DB_CONNECTION'))->beginTransaction();
			DB::connection(env('DB_CONNECTION1'))->beginTransaction();
		// DB::transaction(function() use($update_clpmn,$claim_no,$estimate,$request,$new_claim,$captured){
			try{

				$clhmnx = Clhmn::where('claim_no',$claim_no )->first();
			for ($i = 0; $i < count($request->peril_revision); ++$i) {

				$correspondent_type= $request->input('corr_type')[$i];

				if($this->checkIfExistsClestdl($claim_no,$request->input('peril_revision')[$i],$request->input('peril_item_revision')[$i],$correspondent_type)==0){
					
					$insert_clestdtl = new Clestdtl;
					$insert_clestdtl->claim_no = $claim_no;
					$insert_clestdtl->peril = $request->input('peril_revision')[$i];
					$insert_clestdtl->perilitem = $request->input('peril_item_revision')[$i];
					$insert_clestdtl->corr_type = $request->input('corr_type')[$i];
					$insert_clestdtl->benefit = $request->input('use_benefit')[$i];
					$insert_clestdtl->effective_date = $this->todays_date;
					$insert_clestdtl->tran_no = $request->tran_no;
					$insert_clestdtl->orig_estimate = str_replace(',','',$request->input('orig_estimate_revision')[$i]);
					$insert_clestdtl->curr_estimate = str_replace(',','',$request->input('orig_estimate_revision')[$i]);
					$insert_clestdtl->doc_type = 'EST';
					$insert_clestdtl->indice = (int) '1';
					$insert_clestdtl->ln_no = (int) '1';
					$insert_clestdtl->reference = 'OST';
					$insert_clestdtl->currency_code = $clhmnx->currency_code;
					$insert_clestdtl->currency_rate = $clhmnx->currency_rate;
					$insert_clestdtl->local_orig_estimate =str_replace(',','', $request->input('orig_estimate_allocation')[$i]);
					$insert_clestdtl->local_curr_estimate = str_replace(',','',$request->input('orig_estimate_revision')[$i])* $clhmnx->currency_rate;
					$insert_clestdtl->payments_todate=0;
					$insert_clestdtl->save();

					if($this->NewClaimsParameters->capture_payment_sla == 'Y'){

						$add_process_to_sla = [
							'unique_item' => [	
									'claim_no'=>$claim_no,
									'peril'=> $request->input('peril_revision')[$i],
									'perilitem'=>$request->input('peril_item_revision')[$i],
									'corr_type'=>$request->input('corr_type')[$i]
								],
							'slug' => 'claim-payment-process',
							'activity_slug' => 'reserve',
							'cancel' => 'N',
						];
						AddProcessSLA::dispatch((object)$add_process_to_sla);
					}
				}
				else{

					$fin_curr_bal = str_replace(',','',$request->input('orig_estimate_revision')[$i]);
					$use_benefit = $request->input('use_benefit')[$i];

					$fetch_curr_estimate = Clestdtl::Where([
						['claim_no', '=', $claim_no],
						['peril','=',$request->input('peril_revision')[$i]],
						['perilitem','=',$request->input('peril_item_revision')[$i]],
						['corr_type','=',$request->input('corr_type')[$i]]
					])->sum('curr_estimate');

					$update_clestdtl = Clestdtl::Where([
						['claim_no', '=', $claim_no],
						['peril','=',$request->input('peril_revision')[$i]],
						['perilitem','=',$request->input('peril_item_revision')[$i]],
						['corr_type','=',$request->input('corr_type')[$i]]
					])
					->update([
						'effective_date' => $this->todays_date,
						'curr_estimate' => $fin_curr_bal,
						'benefit' => $use_benefit,
						'doc_type' => 'EST',
						'indice' => (int) '1',
						'ln_no' => (int) '1',
						'reference' => 'EST'
					]);
					$get_clestdtl = Clestdtl::Where([
						['claim_no', '=', $claim_no],
						['peril','=',$request->input('peril_revision')[$i]],
						['perilitem','=',$request->input('peril_item_revision')[$i]],
						['corr_type','=',$request->input('corr_type')[$i]]
					])
					->get();
					$get_clestdtl=$get_clestdtl[0];

					##update SLA if amount is different
					if($fetch_curr_estimate <> $fin_curr_bal ){

						if($this->NewClaimsParameters->capture_payment_sla == 'Y'){

							$add_process_to_sla = [
								'unique_item' => [	
										'claim_no'=>$claim_no,
										'peril'=> $request->input('peril_revision')[$i],
										'perilitem'=>$request->input('peril_item_revision')[$i],
										'corr_type'=>$request->input('corr_type')[$i]
									],
								'slug' => 'claim-payment-process',
								'activity_slug' => 'reserve',
								'cancel' => 'N',
							];
							AddProcessSLA::dispatch((object)$add_process_to_sla);
						}
					}
				}

				##update clmperildtl
				$insert_clpmnperildtl = new Clpmnperildtl;
				$insert_clpmnperildtl->claim_no = $claim_no;
				$insert_clpmnperildtl->policy_no = $clhmnx->policy_no;
				$insert_clpmnperildtl->doc_type = 'EST';
				$insert_clpmnperildtl->entry_type_descr = 'EST';
				$insert_clpmnperildtl->pay_type = 30;
				$insert_clpmnperildtl->class = $clhmnx->class;
				$insert_clpmnperildtl->pay_amount = str_replace(',','',$request->input('orig_estimate_revision')[$i]);
				$insert_clpmnperildtl->amount = str_replace(',','',$request->input('orig_estimate_revision')[$i]);
				$insert_clpmnperildtl->pay_date = $this->todays_date;
				$insert_clpmnperildtl->dola = $this->todays_date;
				$insert_clpmnperildtl->payee = trim($perilitemsx->item_name).' ESTIMATE';
				$insert_clpmnperildtl->dtrans_no = $request->tran_no;
				$insert_clpmnperildtl->account_year = $year;
				$insert_clpmnperildtl->account_month = $month;
				$insert_clpmnperildtl->peril = $request->input('peril_revision')[$i];
				$insert_clpmnperildtl->user_str = $this->user_name;
				$insert_clpmnperildtl->pay_time = Carbon::now();
				$insert_clpmnperildtl->branch = $clhmnx->branch;
				$insert_clpmnperildtl->agent = $clhmnx->agent_no;
				$insert_clpmnperildtl->effective_date = $clhmnx->date_reg;
				$insert_clpmnperildtl->balance = str_replace(',','',$request->input('orig_estimate_revision')[$i]) * $clhmnx->currency_rate;
				$insert_clpmnperildtl->payments_todate = $clhmnx->total_payment;
				$insert_clpmnperildtl->uw_year = $clhmnx->uw_year;
				$insert_clpmnperildtl->location =$clhmnx->location;
				$insert_clpmnperildtl->section = $clhmnx->section;
				$insert_clpmnperildtl->curr_total_estimate = str_replace(',','',$request->input('orig_estimate_revision')[$i]) * $clhmnx->currency_rate;
				$insert_clpmnperildtl->closing_balance = str_replace(',','',$request->input('orig_estimate_revision')[$i]) * $clhmnx->currency_rate;
				$insert_clpmnperildtl->perilitem =  $request->input('peril_item_revision')[$i];
				$insert_clpmnperildtl->ln_no =(int) '1';
				$insert_clpmnperildtl->currency_code = $clhmnx->currency_code;
				$insert_clpmnperildtl->currency_rate = $clhmnx->currency_rate;
				$insert_clpmnperildtl->local_amount =  str_replace(',','',$request->input('orig_estimate_revision')[$i]) * $clhmnx->currency_rate;
				$insert_clpmnperildtl->corr_type = $request->input('corr_type')[$i];
				$insert_clpmnperildtl->save();


			
			}		

			$pipcnam = Pipcnam::All()->first();
			if($pipcnam->ira_section_activated == 'Y'  && !empty($request->ira_status)){
				$irastatuses = $request->ira_status;
				foreach ($irastatuses as $index => $irastatus_code) {
						$peril = $request->ira_peril[$index];
						$ira_docstatus = $request->ira_docstatus[$index];
						$ira_comment_code = $request->ira_comments[$index];
						$doc_type = 'EST';
						$this->UpdateCreateClestIRA($claim_no,$peril,$doc_type,$request->tran_no,$ira_docstatus,$irastatus_code,$ira_comment_code);
				};
				
			}

			$old_dtran_no = $request->tran_no;

			$revise_estimate = $this->reinsurance($captured,$estimate,$update_clpmn,$new_claim,$old_dtran_no);
			$user = trim(Auth::user()->user_name);
			
			$clpmn_item = Clpmn::Where('claim_no','=',$claim_no)
								->whereRaw("trim(user_str)='$user'")
								->orderBy('pay_time','desc')
								->first();

			// update claim status
			$clmhistData = [
				'claim_no' => $clpmn_item->claim_no,
				'slug' => 'claim-revision-made',
				'overide_status_desc' => 'Y', 
				'additional_comment' => "CLAIM REVISION MADE. CURRENT ESTIMATE:".number_format($clpmn_item->curr_total_estimate)
			];
			
			ClaimProcessedEvent::dispatch((object)$clmhistData);

			$sum_clestdtl = Clestdtl::where('claim_no',$claim_no)->sum('curr_estimate');
			$pipcnam = Pipcnam::All()->first();
			$max_reserve_allocation = $pipcnam->claim_reserve_limit;

			if( ($sum_clestdtl * $clhmnx->currency_rate)  > $max_reserve_allocation){
				$notificationData = [
					'clhmn' => $clhmnx,
					'sum_clestdtl' => $sum_clestdtl,
					'max_reserve_allocation' => $max_reserve_allocation,
				];
				DispatchNotificationEvent::dispatch($slug = 'reserve-limit',$notificationData);
				// $this->sendmaxreservelimit($clhmnx,$sum_clestdtl,$max_reserve_allocation);
			}

			$check_xloss = Clpmn::where('claim_no',$claim_no)->where('entry_type_descr','EST')
			->orderBy('pay_time','desc')
			->pluck('curr_xloss_1_estimate')
			->first(); 


			if($check_xloss > 0){
				$notificationData = [
					'clhmn' => $clhmnx,
					'check_xloss' => $check_xloss
				];
				DispatchNotificationEvent::dispatch($slug = 'xol-estimate-claim-notification',$notificationData);
				// $this->sendxlossnotification($clhmnx,$check_xloss);
			}

			$bustype = Bustype::where('type_of_bus',$clhmnx->type_of_bus)->first();
			
			if($bustype->co_insure == 'Y' && $bustype->leader == 'Y' ){
				$notificationData =[
					'clhmn' => $clhmnx,
					'amount' => str_replace(',','',$clpmn_item->curr_total_estimate)
				];
				DispatchNotificationEvent::dispatch($slug='coinsurance-notification',$notificationData);
				// $this->coinsurancenotification($clhmnx,str_replace(',','',$clpmn_item->curr_total_estimate));
			}

			$clpmn = Clpmn::where('claim_no',$claim_no)
				->where('entry_type_descr','OST')
				->first(['curr_facult_p_estimate','uw_year','movt_quota_estimate','movt_surplus_1_estimate','movt_surplus_2_estimate','movt_surplus_3_estimate']); 

			if($clpmn->curr_facult_p_estimate > 0){
				$notificationData = [
					'clhmnx' => $clhmnx,
					'clestdtl' => $get_clestdtl,
					'revise' => 'Y',
				];
				DispatchNotificationEvent::dispatch($slug = 'facultative-estimate-claim-notification',$notificationData);
				// $this->sendfacnotification($clhmnx, $get_clestdtl,'Y');
			}

			$notificationData = [
				'clpmn' => $clpmn_item,
			];
			DispatchNotificationEvent::dispatch($slug = 'cash-loss-alert',$notificationData);
			// $this->sendCashLossNotification($clpmn_item);

			// DB::commit();
			DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();


		}catch (\Throwable $e) {
			dd($e);
			// DB::rollback();
			DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();			
			$error_msg = json_encode($e->getMessage());
			$reference = "claim_no: {$request->claim_no}";
            $module = __METHOD__;
			$route_name = Route::getCurrentRoute()->getActionName();

			log_error_details($route_name,$error_msg,$reference,$module);
            Session::flash('error','Failed to Add');
        }
		
		Session::Flash('success', 'Claim reserve has been revised '. $bustype->co_insure );

		return redirect()->back();
	}

	public function sendxlossnotification($clhmnx,$check_xloss){

		$process = 'xol-estimate-claim-notification';

			$policy_holder = Clhmn::where('claim_no',$clhmnx->claim_no)->first();
			$claim_no  = formatPolicyOrClaim($clhmnx->claim_no);
			$policy_no  = formatPolicyOrClaim($clhmnx->policy_no);
			$subject = "XOL Claim Estimate Revision Notification";
			$insured = $clhmnx->policyholder;

			$message = "<html>
			<body>
				<p>This is to notify you that claim transaction estimate made has XOL. </p>
				<ul>
					<li>Policy Number: $policy_no </li>
					<li>Claim Number: $claim_no </li>
					<li>Insured: $insured  </li>
				</ul>
			</body>
			</html>";

			sendprocessnotifications($process,$message,$subject);


	}
	
	public function sendmaxreservelimit($clhmnx,$sum_clestdtl,$max_reserve_allocation){

		$process = 'reserve-limit';

		$policy_holder = Clhmn::where('claim_no',$clhmnx->claim_no)->first();
		$claim_no  = formatPolicyOrClaim($clhmnx->claim_no);
		$policy_no  = formatPolicyOrClaim($clhmnx->policy_no);
		$subject = "Reserve Limit Exceeded";
		$insured = $clhmnx->policyholder;
		$claim_amount = number_format($sum_clestdtl);
		$max_reserve = number_format($max_reserve_allocation);
		$curr = Currency::where('currency_code',$clhmnx->currency_code)->pluck('currency')->first();
		$rate = $clhmnx->currency_rate;

		$message = "<html>
		<body>
			<p>This is to notify you that claim transaction with an amount above the set reserve limit of $max_reserve.</p>
			<ul>
				<li>Policy Number: $policy_no </li>
				<li>Claim Number: $claim_no </li>
				<li>Insured: $insured  </li>
				<li>Current Reserve Limit:UGX $max_reserve  </li>
				<li>Current Estimate amount:$curr $claim_amount  </li>
				<li>Currency Rate: $rate  </li>
			</ul>
		</body>
		</html>";

		sendprocessnotifications($process,$message,$subject);


	}

	public function coinsurancenotification($clhmn,$amount){

		##coinsurance participants
		$getcoinpart = Coinpart::where('endt_renewal_no',$clhmn->endt_renewal_no)->get();
		$claim_no = formatPolicyOrClaim($clhmn->claim_no);
		$policy_no = formatPolicyOrClaim($clhmn->policy_no);
		$Loss_date = formatDate($clhmn->acc_date);
		$claim_amount = number_format($amount);
		$pipcnam = Pipcnam::all();
		$company = $pipcnam[0]->company_name;
		
		foreach($getcoinpart as $part){

			// $getpart = Agmnf::select('name','email')
			// 	->where('branch',$part->branch)
			// 	->where('agent',$part->agent)
			// 	->first();
				
			$intermediaryParams = new IntermediaryQueryParams([
				'agentNo' => $part->agent,
				
			]);

		   $getpart  =  IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
			
			$share =number_format( $amount*($part->reice/100));			
			$percentage = $part->reice;			
			$Subject  = 'Notification: Your Share of Claim Amount';
			$message = '
			<html>
			<body>
			<p>I hope this message finds you well. We would like to inform you about your share of the recent claim filed under your coinsurance policy with '.$company.'. Our records indicate that a claim has been processed, and it is essential for you to be aware of your financial responsibility in this matter.<br>
			Claim Details:</p>
			<ul>Policyholder: '.$clhmn->policyholder.'</ul>
			<ul>Policy Number: '.$policy_no.'</ul>
			<ul>Claim Number:'. $claim_no.'</ul>
			<ul>Date of Loss/Incident: '.$Loss_date.'</ul>
			<ul>Claim Amount: '.$claim_amount.'</ul>
			<ul>Your Coinsurance Percentage: '.$percentage.'</ul>
			<ul>Your Share of Claim Amount: '.$share.'</ul>
			<p>Please note that your coinsurance percentage represents the portion of the claim amount for which you are responsible. In accordance with the terms of your policy, your share of the claim amount is calculated based on this percentage.
			Should you have any questions or require clarification regarding the claim or your share of the amount, please do not hesitate to contact our dedicated customer service team on +256(0)20-0841000.
			We appreciate your prompt attention to this matter and value your continued trust in '.$company.'. Thank you for being a valued participant in our insurance program.</p>
			</body>
			</html>			
			';


			if(!empty($getpart->email)){

				
				$emailid = (int) SendEmail::max('emailid') + 1 ;
			
				$sendemail = new Sendemail;
				$sendemail->category = $Subject ;
				$sendemail->receiver =$getpart->email;
				$sendemail->message =$message;
				$sendemail->creator = 'Claim Department';
				$sendemail->status = 'SEND';
				$sendemail->save();

				
			}

		}

	}

	public function sendfacnotification($clhmnx, $clesdtl, $revise){
		$policy_holder = Clhmn::where('claim_no',$clhmnx->claim_no)->first();
		$claim_no  = formatPolicyOrClaim($clhmnx->claim_no);
		$policy_no  = formatPolicyOrClaim($clhmnx->policy_no);
		$subject = "Claim Estimate Revision Notification";
		$currency = Currency::where('currency_code', $clhmnx->currency_code)->first();
		$currency_used = $currency->currency;
		$insured = $clhmnx->policyholder;
		$company = trim(Pipcnam::first()->company_name);
		
		$name =  Auth::user()->user_name;

		$prticipants = Endtrepart::where('policy_no', $clhmnx->policy_no)->where('endt_renewal_no', $clhmnx->endt_renewal_no)->get();
		foreach ($prticipants as $prticipant) {
			$part_name = Crmast::where('branch', $prticipant->branch)->where('agent', $prticipant->agent)->first()->name;
			$reserve = $clesdtl->curr_estimate;
			$percentage = $prticipant->reice_1;
			$amount = number_format($percentage*$reserve/100, 2);
			$reserve = number_format($reserve, 2);


			if ($revise != 'Y') {
				$message = "<html>
				<body>
					<p>I trust this message finds you well. We have intimated a claim under <b>Policy Number: $policy_no</b>
					and we have initiated an initial claim reserve of $currency_used: $reserve. The claim number is <b>$claim_no</b>.</p>
	
					<p>Your ($part_name) share of $percentage% amounts to <b>$currency_used: $amount</b>. Kindly consider creating a corresponding 
					reserve on your end at your earliest convenience. </p>

					<p>Best Regards,</p><br>
					<span>$company</span>
				</body>
				</html>";
			}else {
				$message = "<html>
				<body>
					<p>I trust this message finds you well. We have revised the claim reserve for the claim under <b>Policy Number: $policy_no</b>
					 to $currency_used: $reserve. The claim number is <b>$claim_no</b>.</p>
	
					<p>Your ($part_name) share of $percentage% amounts to <b>$currency_used: $amount</b>. Kindly consider revising your reserves accordingly on your end
					at your earliest convenience. </p>
					
					<p>Best Regards,</p><br>
					<span>$company</span>
				</body>
				</html>";
			}

			//$email = Agmnf::where('branch', $prticipant->branch)->where('agent', $prticipant->agent)->first()->email;

			$intermediaryParams = new IntermediaryQueryParams([
				'agentNo' => $prticipant->agent,
				
			]);

		    $agent  =  IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
			
			$emailaddress = trim($agent->email);
			$sendemail = new Sendemail;
			$sendemail->category = $subject;
			$sendemail->receiver = $emailaddress;
			$sendemail->message = $message;
			$sendemail->creator = $name;
			$sendemail->save();
		}
	}

	public function sendCashLossNotification($clpmn)
	{
		$hasTreatyCession = false;
		if($clpmn->movt_quota_estimate != 0 || $clpmn->movt_surplus_1_estimate != 0 || $clpmn->movt_surplus_2_estimate || $clpmn->movt_surplus_3_estimate)
		{
			$hasTreatyCession = true;
		}

		if(!$hasTreatyCession)
		{
			return true;
		}

		$classyear = Classyear::where([
				'uw_year' => $clpmn->uw_year,
				'class' => $clpmn->class
			])->first();

		$reinsetup = Reinsetup::where([
			'uw_year' => $clpmn->uw_year,
			'class' => $classyear->reinclass
		])->first();

		$treatyCodes = [];
		if($clpmn->curr_quota_estimate != 0)
		{
			array_push($treatyCodes,[
				'treatyCode' =>$reinsetup->quota_code,
				'reserveAmount' =>$clpmn->curr_quota_estimate,
			]);
		}
		if($clpmn->curr_surplus_1st_estimate != 0)
		{
			array_push($treatyCodes,[
				'treatyCode' =>$reinsetup->surplus_1_code,
				'reserveAmount' =>$clpmn->curr_surplus_1st_estimate,
			]);
		}
		if($clpmn->curr_surplus_2nd_estimate != 0)
		{
			array_push($treatyCodes,[
				'treatyCode' =>$reinsetup->surplus_2_code,
				'reserveAmount' =>$clpmn->curr_surplus_2nd_estimate,
			]);
		}
		if($clpmn->curr_surplus_3rd_estimate != 0)
		{
			array_push($treatyCodes,[
				'treatyCode' =>$reinsetup->surplus_3_code,
				'reserveAmount' =>abs($clpmn->curr_surplus_3rd_estimate),
			]);
		}
		
		$cashLossExceeded = false;
		foreach ($treatyCodes as $key => $value) {
								
			$treaty = Treatysetup::where('uw_year',$clpmn->uw_year)
				->where('treaty_code',$value['treatyCode'])
				->select(['treaty_code','treaty_name','cash_loss'])
				->first();

			if($value['reserveAmount'] <= (float)$treaty->cash_loss)
			{
				continue;
			}
			else 
			{
				$cashLossExceeded = true;
			}
		}

		if(!$cashLossExceeded)
		{
			return false;
		}

		$message = "
			<html>
				<body>
					<p>Claim  {$clpmn->claim_no} is above cash loss limit.</p>
						<ul>";
							foreach ($treatyCodes as $key => $value) {
								
								$treaty = Treatysetup::where('uw_year',$clpmn->uw_year)
									->where('treaty_code',$value['treatyCode'])
									->select(['treaty_code','treaty_name','cash_loss'])
									->first();

								$message .="<li> <strong>{$treaty->treaty_name}</strong>
									<ul>
										<li> <strong>Reserve Amount: </strong>{$value['reserveAmount']}</li>
										<li> <strong>Cash Loss Limit: </strong>{$treaty->cash_loss}</li>
									</ul>
								</li>";
							}

		$message .="
					</ul>
				</body>
			</html>";

		$subject ="Reinsurance Estimate Above Cash Loss Limit";

		$process = 'cash-loss-alert';

		sendprocessnotifications($process,$message,$subject);
	}

	  /**check expected reports  */
	  public function check_expected_report(Request $request){
    
		$expectedrep = clmcorr::where('code',$request->get('rec_ind'))
									->get( ['expected_report']);
		
	
		echo json_encode($expectedrep[0]);
	
	}
		 /**end of expected results */
		 
	public function appoinClaimant(Request $request) {

		//dd($request->all());
		DB::beginTransaction();
		try{

			// $process = 'Appointing Claimant';
			$claim_no = removePolicyOrClaimFormat($request->claim_no);

			$claim_codes = $request->claimant_code;

			if($claim_codes != null){
				$claimant_name = Clparam::where('claimant_code', $request->claimant_code)->first();
				$name = trim($claimant_name->name);
			}

			else if( $request->claimant_name != null){
				
				$name = trim($request->claimant_name);

				if($request->bank_code == null ){
					Session::flash('error', 'missing Bank details');
					return redirect()->back();

				}else if($request->bank_branch_code == null ){

					Session::flash('error', 'Missing Bank Branch Details');
					return redirect()->back();

				}else if($request->bank_account_no == null){
					Session::flash('error', 'No Bank Account Number');
					return redirect()->back();
				}

			}
		
			$rec_ind = $request->rec_ind;


			$check = Claimant::where(['claim_no'=>$claim_no, 'rec_ind' =>$rec_ind ])->count();
			// $claimant_check = Claimant::count();

			$clmcorr_det = Clmcorr::where('code', $rec_ind)->first();


			// return $claimant_check;
			$clparam_detail = Clparam::Where('claimant_code','=',$request->claimant_code)->first();
			// dd(count($check));
			$count = 0;
			// $x = ['N', 'E', 'Y'];
			if ($clmcorr_det->insured == 'Y'){
				
				
					$count = $check + 1;
					// return $count;
					$code = str_pad ( $count  , 4 , "0",  STR_PAD_LEFT);
					$code = $rec_ind.$code;

					// $this->notify_client_claimprocess($claim_no, $process);
					// $this->notify_intermediary_claimprocess($claim_no, $process);
			}
			else if($clmcorr_det->claimant== 'Y'){

					$count = $check + 1;
					$code = str_pad ( $count  , 4 , "0",  STR_PAD_LEFT);
					$code = $rec_ind.$code;

					// $this->notify_client_claimprocess($claim_no, $process);
					// $this->notify_intermediary_claimprocess($claim_no, $process);
				
			}else{
	
				$code = $claim_codes;
				$count = $clparam_detail->code;	
			}

			// return  $count;


			$pipcnam = Pipcnam::All()->first();
			$clhmn = Clhmn::where('claim_no',$claim_no)->first();

			$policy_no = $clhmn->policy_no;

			//Service Providers Status Codes
			if($request->rec_ind=='A'){ //Assessor
				$status_code = 2;
			}
			else if($request->rec_ind=='B'){ //Investigator
				$status_code = 3;
			}
			else if($request->rec_ind=='C'){ //Advocate
				$status_code = 4;
			}
			else if($request->rec_ind=='D'){//Doctor
				$status_code = 5;
			}

			//dd($code);
			/*End Status code*/
			if(($request->client_bank_code == null) && ($request->client_bank == null)){

				$bunk_branch = $request->bank_branch_code;
				$bunk_cd = $request->bank_code;
			}
			else {
				$bunk_branch = $request->client_bank_code;
				$bunk_cd = $request->client_bank;
			}

			$balance = str_replace(',','',$request->claim_amount);

				// return $request;
				$claimant_check = Claimant::where('claim_no', $claim_no)
							->where('claimant_code', $code)
							->where('peril', $request->peril)
							->where('perilitem', $request->perilitem)
							// ->where('claim_amount', trim($balance))
							->count();

				$count  = $claimant_check + 1;

			
				$deduct_bal = Clestdtl::where('claim_no', $claim_no)
										->where('peril', $request->peril)
										->where('perilitem', $request->perilitem)
										->get();
				
				$insert_claimant = Claimant::Create([
					'claim_no' => $claim_no,
					'claimant_code' => $code,
					'peril' => $request->peril,
					'perilitem' => $request->perilitem,
					'rec_ind' => $request->rec_ind,
					'claimant_no' => $count,
					'policy_no' => $policy_no,
					'claimant_name' => trim($name),
					'exp_pre_report' => $request->exp_pre_report,
					'exp_final_report' => $request->exp_final_report,
					'claim_amount' =>$balance,
					'bank_code' => $bunk_cd,
					'bank_branch_code' => $bunk_branch,
					'status' => 0,
					'cancelled' => 'N',
					'line_no' => $count,
					'bank_account_no' => $request->bank_account_no,
					'email' => $request->email,
					'sp_reference' => $request->sp_reference,
					'mobile_no' => $request->mobile,
					'appointed' => Carbon::now(),
					'created_by' => $this->user_name,
					'dola' => Carbon::now()
				]);
				if($this->NewClaimsParameters->capture_payment_sla == 'Y'){

					$add_process_to_sla = [
						'unique_item' => [	
								'claim_no'=>$claim_no,
								'peril'=> $request->peril,
								'perilitem'=>$request->perilitem,
								'corr_type'=>$request->rec_ind
							],
						'slug' => 'claim-payment-process',
						'activity_slug' => 'appoint',
						'cancel' => 'N',
					];
					AddProcessSLA::dispatch((object)$add_process_to_sla);

				}
				// update claim status
				$clmhistData = [
					'claim_no' => $claim_no,
					'slug' => 'claimant-appointed',
					'claimant_code' => $claim_codes,
					'overide_status_desc' => 'Y',
					'additional_comment' => "{$clmcorr_det->name} APPOINTED"
				];
				ClaimProcessedEvent::dispatch((object)$clmhistData);

					if(!empty($request->email) && $clmcorr_det->send_notification == 'Y'){
						$data = array('name'=>trim($name));
						$name = ucwords(strtolower(trim($name)));
						$company_name = ucwords(strtolower(trim($pipcnam->company_name)));
						$insured = ucwords(strtolower(trim($clhmn->policyholder)));
						$pre_date = formatDate($request->exp_pre_report);
						$exp_date = formatDate($request->exp_final_report);
						$formatedclaim = formatpolicyorclaim($clhmn->claim_no);

						$notificationData = [
							'name' => $name,
							'mobile' =>  $request->mobile,
							'receiver' => strtolower($request->email),
							'company_name' => $company_name,
							'insured' => $insured,
							'pre_date' => $pre_date,
							'exp_date' => $exp_date,
							'formatedclaim' => $formatedclaim,
							'exp_final_report' => $request->exp_final_report,
							'data' => $data,
							
						];
						DispatchNotificationEvent::dispatch($slug = 'service-appointment-notification',$notificationData);
							
					}

					DB::commit();
				return [
					'status' => 1
				];

			}catch (\Throwable $e) {
				dd($e);
				DB::rollback();
				Session::flash('error','Failed to Appoint');
				return [
					'status' => 0,
				];
			}
		
			
			
		
	}
	
	public function updatereport(Request $request){

		try {
			
			$check_req = Payreqst::where('claim_no',$request->input('claim_no_fill'))
				->where('claimant_code',$request->input('claimant_code_fill'))
				->where('line_no',$request->input('linss_no'))
				->whereRaw("(cancelled<>'Y' OR cancelled is null OR cancelled=' ')")
				->count();

	
		if($check_req > 0){
			return redirect()->back()->with('error', 'Cannot Update Report Submission Date. Claimant was Cancelled');

		}else{
			$del = Claimant::where('claim_no',$request->input('claim_no_fill'))
					->where('claimant_code',$request->input('claimant_code_fill'))
					->where('line_no',$request->input('linss_no'))
					->update([
						'rec_final_report' => $request->sub_date
					]);

			
				return redirect()->back()->with('success', 'Report Date Submitted successfully.');
		

		}

		} catch (\Throwable $th) {
		
			DB::Rollback();
			// dd($th);
			return redirect()->back()->with('error', 'Report Submission Date failed to be Saved.');

		}
		
		

		
	}

	public function deleteClaimant(Request $request){
		//dd($request);
		$check_req = Payreqst::where('claim_no',$request->input('claim_no_del'))
					->where('claimant_code',$request->input('claimant_code_del'))
					->where('peril',$request->input('peril_del'))
					->where('perilitem',$request->input('peril_item_del'))
					->where('line_no',$request->input('linss_no'))
					->whereRaw("(cancelled<>'Y' OR cancelled is null OR cancelled=' ')")
					->count();

		//dd($check_req);

		if($check_req > 0){
			return redirect()->back()->with('error', 'Cannot Cancel. Claimant has a pending requisition');
		}else{
			$del = Claimant::where('claim_no',$request->input('claim_no_del'))
							->where('claimant_code',$request->input('claimant_code_del'))
							->where('peril',$request->input('peril_del'))
							->where('perilitem',$request->input('peril_item_del'))
							->where('line_no',$request->input('linss_no'))
							->update([
								'claim_amount' => 0,
								'cancelled'=>'Y',
								'cancelled_by'=>trim(Auth::user()->user_name)

							]);

			##update Process SLA
			$corr_type = Claimant::where('claim_no',$request->input('claim_no_del'))
							->where('claimant_code',$request->input('claimant_code_del'))
							->where('peril',$request->input('peril_del'))
							->where('perilitem',$request->input('peril_item_del'))
							->where('line_no',$request->input('linss_no'))
							->pluck('rec_ind');
			
			if($this->NewClaimsParameters->capture_payment_sla == 'Y'){

				$add_process_to_sla = [
					'unique_item' => [	
							'claim_no'=>$request->input('claim_no_del'),
							'peril'=> $request->input('peril_del'),
							'perilitem'=>$request->input('peril_item_del'),
							'corr_type'=>$corr_type[0]
						],
					'slug' => 'claim-payment-process',
					'activity_slug' => 'appoint',
					'cancel' => 'Y',
				];
				AddProcessSLA::dispatch((object)$add_process_to_sla);
			}
			if($del){
				return redirect()->back()->with('success', 'Claimant Cancelled successfully.');
			}else{
				return redirect()->back()->with('error', 'Claimant failed to Cancelled.');
			}

		}

		

		
	}

	public function deleteexcess( Request $request){

	
		if($request->get('delete')){

			$crm=$request->get('delclaimno');
		$ed = Clmexcess::whereRaw("trim(claim_number)='".$crm."'")
							->where('excess_type',$request->clm_type)
							->where('excess_amt',str_replace(',','',$request->delamount))
							->whereRaw("trim(excess_based)='".$request->delbasedon."'")
							->where('item_no',$request->delitemno)
							->delete();
		
							
		$dut = Clmexcess::whereRaw("trim(claim_number)='".$crm."'")->sum('excess_amt');

		if ($dut){
			$exno = Clhmn::whereRaw("trim(claim_no)='".$crm."'")
							->update([
							'excess'=> 'N',
							'excess_amount'=> $dut
								]);
											
			}
	//  Session::flash('success','Deletion Successful');
	 return redirect()->back()->with('success','Excess details has been deleted successfully.');
	}
	else{
		return redirect()->back()->with('error','Excess details has not been deleted successfully.');

	}
}
	public function deleterecovery( Request $request){
		try{

			$crm = $request->get('delclay');
			$debit_no = $request->get('delid');
			// dd($request);
		$ed = Clmrecoveries::where("claim_no", $crm)
							->where('debit_no',$debit_no)
							->delete();

	 Session::flash('success','Cancelled Successful');
	 return redirect()->back();

	}catch (\Throwable $e) {
		// dd($e);
		DB::rollback();
		$ed = Clmrecoveries::all();
		return redirect()->back()->with('error','Failed to delete');
	}	
}
	public function updatestatus( Request $request){

		// return $request;
		$username = trim(Auth::user()->user_name);

		$confirm_close =  $request->get('closeclaim');
		try {
			$curr_est =Clhmn::where('claim_no', $request->get('claim_no'))->get();

		if( $confirm_close == 'Y' && $curr_est[0]->curr_total_estimate !=0){

			return redirect()->back()->with('error','Cannot close claim file.There are Pending Amounts.');

		}
		// else if( ($curr_est[0]->curr_total_estimate ==0) &&( $confirm_close== "Y")){
			
		
		// }

		else{

			// $con =Clhmn::where('claim_no', $request->get('claim_no'))->update([
			// 	'rejected'=> 'Y',
			// 	'closed_by' => $username,
			// 	'closed' => 'Y',
			// 	'date_rejected' => Carbon::now()
			// ]);

				$updstat = new Clmhist;
				$updstat->claim_no= $request->get('claim_no');
				$updstat->status_code= $request->get('updstat');
				$updstat->status_date = Carbon::now();
				$updstat->user_str = $username;
				$updstat->dola = Carbon::now();
				$updstat->comment_str = $request->get('comment');
				$updstat->status = $request->get('curr_stat');
	
					$updstat->save();
					DB::commit();
						
					return redirect()->back()->with('success','Claim Status has been Updated successfully.');
			}
		// return('zzzzzz');			
		}catch (\Throwable $e) {
			// dd($e);
			DB::rollback();
			$add_excess = Clmhist::all();
			return redirect()->back()->with('error','Failed.');
		}	


}

		public function rejectclaim( Request $request){
			$username = trim(Auth::user()->user_name);

			//check
			if($request->uses_substatus === 'Y'){
				try {
					$validated = $request->validate([
						'substatus' => 'required'
					]);
					$substatus = $validated['substatus'];
				} catch (\Throwable $th) {
					return redirect()->back()->with('error','Reason for rejection types are required.');
				}
				
			} else {
				$substatus = null;
			}

			try {
				$request->validate([
					'rejectd' => 'required'
				]);
			} catch (\Throwable $th) {
				return redirect()->back()->with('error','Reason for rejecting claim is required.');
			}

			//  return $request;
			$rejected =Clhmn::where('claim_no', $request->get('claims_no'))->update([
				'rejected'=> 'Y',
				'rejected_by' => $username,
				'date_rejected' => Carbon::now(),
				'substatus' => $substatus
			]);

			
			$reject = new Clmhist;
			$reject->claim_no= $request->get('claims_no');
			$reject->status_code= $request->get('rejectd');
			$reject->substatus_code= $request->get('substatus');
			$reject->status_date = Carbon::now();
			$reject->user_str = $username;
			$reject->dola = Carbon::now();
			$reject->comment_str = $request->get('description');
			$reject->status = $request->get('curr_stat');
			$reject->claim_amount_rejected = $request->get('reject_amt');
	
			//  return $reject;

			try {
				$reject->save();
				DB::commit();

			
				return redirect()->back()->with('success','Claim Status has been Rejected successfully.');
				}catch (\Throwable $e) {
					// dd($e);
					DB::rollback();
					$reject = Clmhist::all();
					$rejected = Clhmn::all();
					return redirect()->back()->with('error','Failed.');
				}	
	} 	
	public function debitteexcess( Request $request){

			// return $request;
			$user_name = trim(Auth::user()->user_name);
   			# Dtran0
			   $dtran = Dtran0::all();
			   $dtran = $dtran[0];
	   
			   $old_debit_no = $dtran->debit_no;
			   $debit_no = $old_debit_no + 1;

			$creditclm =new Creditclm;
			$creditclm->dr_cr = $request->debdr;
			$creditclm->doc_type = $request->debdoctype;
			$creditclm->dtrans_no =$debit_no;
			$creditclm->account_year = $request->period_year;
			$creditclm->account_month = $request->period_month;
			$creditclm->endt_renewal_no = $request->endorse_no;
			$creditclm ->claim_no = $request->claim_no;
			$creditclm->entry_type_descr = 'EXC';
			// $creditclm ->treaty_code = $request-> 
			$creditclm->class = $request->class;
			$creditclm->client_number =$request->client_num;
			$creditclm->payee =$request->debpayee;
			$creditclm->policy_no =$request->policy_no;
			$creditclm->dola = Carbon::today();
			$creditclm->branch =$request->branch;
			$creditclm->agent_no =$request->agent_no;
			$creditclm->tran_no =$debit_no;
			$creditclm ->location =$request->location;
			$creditclm ->type =$request->debclaimtype;
			$creditclm ->unallocated =str_replace(',','',$request->debamount);
			$creditclm ->allocated = 0;

			// $creditclm ->trans_type =$request->
			// $creditclm ->despatch_date =$request->
			// $creditclm ->debit_credit_no =$request->
			// $creditclm ->sum_insured =$request->
			// $creditclm ->gross_amount =$request->
			// $creditclm ->reice =$request->
			// $creditclm ->stamp_duty =$request->
			// $creditclm ->comm_amount =$request->
			$creditclm ->nett_amount =str_replace(',','',$request->debamount);
			// $creditclm ->effective_date =$request->
			// $creditclm ->quake_premium =$request->
			$creditclm->user_str =$user_name; 
			// $creditclm ->period_to =$request->
			// $creditclm ->period_from =$request->
			// $creditclm ->type_of_bus =$request->
			// $creditclm ->subcode =$request->
			// $creditclm ->
			// $creditclm ->


			DB::beginTransaction();

			try {
			$creditclm ->save();
			DB::commit();
				$creditclm  = Creditclm::all();

				$updran0 = Dtran0::where('debit_no', $old_debit_no)->update([
					'debit_no' => $debit_no
				]);

			return redirect()->back()->with('success','Excess details has been Debitted successfully.');
			}catch (\Throwable $e) {
				// dd($e);
				DB::rollback();
				$add_excess = Creditclm::all();
				return redirect()->back()->with('error','Excess details has not been Debited successfully.');
			}	
		}

	public function processExcess(Request $request){
	
		$claim_no = removePolicyOrClaimFormat($request->excess_claim_no);
		$next = Clmexcess::max('item_no');
		$count = $next + 1;		
			$add_excess = new Clmexcess;       
			$add_excess->claim_number = $claim_no;
			$add_excess->item_no = $count;
			$add_excess->excess_type =  $request->excess_type;
			$add_excess->excess_amt = str_replace(',','',$request->excess_amount);
			$add_excess->excess_rate = $request->excess_per;
			$add_excess->excess_based =  $request->excess_paid;
		//  $add_excess->save();

			DB::beginTransaction();

			try {
				
			$add_excess->save();

			DB::commit();

				$add_excess = Clmexcess::all();

				$dut = Clmexcess::whereRaw("trim(claim_number)='".$claim_no."'")->sum('excess_amt');
					if ($dut >0){
						$exno = Clhmn::whereRaw("trim(claim_no)='".$claim_no."'")
										->update([
										'excess'=> 'Y',
										'excess_amount'=> $dut
											]);

					// dd($exno);
					return redirect()->back()->with('success','Excess details has been updated successfully.');
					}

			}catch (\Throwable $e) {
				// dd($e);
				DB::rollback();

				$add_excess = Clmexcess::all();
				return redirect()->back()->with('error','Excess details has not been updated successfully.');

			}

		// return redirect()->back()->with('error','Excess details has been updated successfully.');
	}

	public function admitLiability(Request $request){
		$claim_no = removePolicyOrClaimFormat($request->liability_claim_no);
		$clhmn = Clhmn::where('claim_no',$claim_no)->update([
			'liability_admitted' => $request->liability_admitted,
			'date_liab_admitted' => $request->date_liab_admitted,
			'liability_amount' => str_replace(',','',$request->liability_amount)
		]);

		return redirect()->back()->with('success','Liability acceptance has been updated successfully.');
	}

	public function raiseRequisition(Request $request) {


		$claim_no = removePolicyOrClaimFormat($request->claim_no);
		$dtran0 = $this->getDtran0();
		$clhmn = $this->getClaimDetails($claim_no);
		$agmnf = $this->getAgmnfDetails($clhmn->branch, $clhmn->agent_no);

		$reqnumbers = DB::select("select * from payreqst where claim_no = '$claim_no' and
						 claimant_code = '$request->claimant_code' and line_no = '$request->lin_no' and peril = '$request->peril' and perilitem ='$request->payitem' and (cancelled is null or cancelled <> 'Y')");
		
		if(count($reqnumbers)>0){

			Session::flash('error', 'Similar Record exists');
			return 1;
			// return redirect()->back();
		}
		else{
			$requirement = Clhmn::where('claim_no',$claim_no)->first();

		if($request->update_benefits == 'Y'){

			foreach($request->addmore as $value){
			
				$curr_benefit = str_replace(',','',$value['currentbenefit']);
				$ben = str_replace(',','',$value['benefitamount']);
				$benamount = $curr_benefit - $ben;
				$name = trim(Auth::user()->name);
				// return $benamount ;
				$polbenefit = Polbenefits::where('policy_no', $requirement->policy_no)
				->where('endt_renewal_no', $requirement->endt_renewal_no)
				->where('benefit_id',$value['benefit'])
				->update([
					'os_amount'=> $benamount,
					// 'dola'=> Carbon::now(),
					// 'updated_by'=>$name
				]);

				$ben_description = Polbenefits::where('policy_no', $requirement->policy_no)
				->where('endt_renewal_no', $requirement->endt_renewal_no)
				->where('benefit_id',$value['benefit'])->first();

				$clients = Polmaster::where('policy_no',$requirement->policy_no)->first();
				$emailaddr = Client::where('client_number',trim($clients->client_number))->first();
				$desc  = trim($ben_description->benefit_description);
				$category = 'BENEFIT UTILIZATION';
				$mess = "Dear Client, your benefit ('$desc') under policy ('$requirement->policy_no') has been fully utilised with your recent claim ('$request->claim_no'). We advice you to reinstate the same to enjoy the benefit in future.";
				$mess2 = "Dear Team, Policy  '$requirement->policy_no' has fully utilized benefit ('$desc'). Please advice client to reinstate.";

				if($benamount == 0 ){

          

					$getbranch = $requirement->branch;
					$getmail = Department_emails::where('branch',$getbranch)->where('dept','CLM')->first();
					
					## mail to client
					if(!empty(trim($emailaddr->e_mail))){
						$emailaddr = $emailaddr->e_mail;
						$this->sendmail($category,$emailaddr,$mess,$name);
					}
					
					## send mail to claim department
					if(count($getmail->email) > 0){
						$mess = $mess2;
						$emailaddr = trim($getmail->email);
						$this->sendmail($category,$emailaddr,$mess,$name);
					}
					
				}
				
				
			}
		}

		$now_date = Carbon::today()->format('Y-m-d');
            
        $query = "SELECT account_year, account_month FROM period WHERE TO_DATE(:now_date, 'YYYY-MM-DD') BETWEEN period_from AND period_to";

					// Execute the query with the bound parameter
		$period_leo = DB::select($query, ['now_date' => $now_date]);

            $period_leo = $period_leo[0];
   
            // $year = $today->year;
           // $month = $today->month;
		$account_month=$period_leo->account_month;
        $account_year=$period_leo->account_year;

		// $account_month = $dtran0->account_month;
		// $account_year = $dtran0->account_year;
		
		$dtrans_no = $this->getCbseqno($account_year, $request->dept_code);
		// dd($dtrans_no);

		$requisition_no = $this->generateRequisitionNo($request->dept_code, $dtrans_no, $account_year, $account_month);
		$descr = 'CLP';
		$doc_type = 'PAY';
		$cbtrans = $this->getCbtrans($doc_type, $descr);
		$debit_account = $cbtrans->debit_account;
		$credit_account = $cbtrans->credit_account;
		$gross_amount=str_replace(',','',$request->gross_amount );

		$deduction_codes = $request->deduction_code;
	
		$total_deductions_amt = 0;
		$total_additions_amt = 0;
		$no_amount = 0;



		foreach($deduction_codes as $i => $deduction_code){
			$deduct=Cbdeduct::where('code',$deduction_code)->first(); 
			$add_deduct = $deduct->add_deduct;
			
			if($add_deduct=='A'){
				$additions_amt = (int) $request->deduction_amount[$i];
				$total_additions_amt= $total_additions_amt + $additions_amt;
				
			}
			else if($add_deduct=='D'){
				$deductions_amt = (int) $request->deduction_amount[$i];
				$total_deductions_amt= $total_deductions_amt + $deductions_amt;
			}
			else{
				$no_amount= $no_amount;
			}
			
		}

		// dd($request->all(), $requisition_no,$request->dept_code,$dtrans_no);

		$amount = $gross_amount + $total_additions_amt - $total_deductions_amt + $no_amount;

		// deductions.end
		$payreqst_items = [
			'claim_no' => $claim_no,
			'policy_no' => $clhmn->policy_no,
			'branch' => $clhmn->branch,
			'agent' => $clhmn->agent_no,
			'client_number' => $clhmn->client_number,
			'location' => $clhmn->location,
			'dep_no' => $clhmn->dep_no,
			'currency_code' => $clhmn->currency_code,
			'currency_rate' => $clhmn->currency_rate,
			'uw_year' => $clhmn->uw_year,
			'class' => $clhmn->class,
			'type_of_bus' => $clhmn->type_of_bus,
			'local_amount' =>  $amount * $clhmn->currency_rate,
			'local_gross_amount' => $gross_amount * $clhmn->currency_rate,
			'unallocated_total' =>  $amount,
			'local_unallocated_total' =>  $amount * $clhmn->currency_rate,
			'req_no' => $requisition_no,
			'dtrans_no' => $dtrans_no,
			'debit_account' => $debit_account,
			'credit_account' => $credit_account,
			'account_year' => $account_year,
			'account_month' => $account_month,
			'doc_type' => 'PRQ',
			'dept_code' => $request->dept_code,
			'effective_date' => $this->todays_date,
			#'cheque_no'
			#cheque_date
			'name' => $request->claimant_name,
			'slhead1' => $request->claimant_code,
			'claimant_code' => $request->claimant_code,
			'claimant_name' => $request->claimant_name,
			'gross_amount' => $gross_amount,
			'amount' => $amount,
			#'premium_amount'
			#'excess_amount'
			'created_time' => $this->todays_date_time,
			'created_by' => $this->user_name,
			#username shortcut 'created_by'=>$request->User()->user_name;
			'created_date' => $this->todays_date,
			#'changed_by'
			#'changed_date'=>
			'dola' => $this->todays_date,
			'entry_type_descr' => 'CLP',
			#ref_doc_type
			#ref_doc
			#debit_subaccount
			#credit_subaccount
			'peril' => $request->peril,
			'perilitem' => $request->payitem,
			'line_no' => $request->lin_no,
			'analyse' => $request->analyse,
			#subledger
			#checked_by
			#manager
			#authorized
			'source_code' => 'CLM',
			#status
			#rent_amount
			#service_charge_amount
			#ten_lease_code
			'offcd' => $request->offcd,
			'subledger_code'=>$request->subledger_code,
			'subledger_account'=>$request->req_sledger_ac,
			'subledger'=>$request->subledger, 

			#slhead
			#checked_date
			#authorized_date
			'payments_todate' => 0,
			'narration' => $request->narration,
			#reconciled
			#lclient_no
			'multiclaims' => 'N',
			#'allocated_total'
			#'total_no_allocated'
			#deduction_code_1-10
			#deduction_amount_1-10
			'payee_bank_account' => $request->payee_bank_account,
			'payee_bank_code' => $request->payee_bank_code,
			'payee_bank_name' => $request->payee_bank_name,
			'payee_bank_branch_code' => $request->payee_bank_branch_code,
			'payee_bank_branch' => $request->payee_bank_branch_name,
			'final_settle' => $request->final_settle,
			'multi_claimants' => 'N',
			#account_premium_amount
			'payee_type' => $request->payee_type,
		
			##add to workflow.table escalation =>code & category
			'escalate_id' => 10,
			'workflow_id' => 900,
			'sal_dtrans_no' => $request->sdtrans,
			'loss_type' => $request->type_of_loss,
			'offset_premium'=>$request->offset_premium

		];
			##check if schedule exists
			$classmodel = ClassModel::where('class',$clhmn->class)->first();

			// if($classmodel->schedule_upload_mandatory == 'Y'){
			// 	$update_schedule = update_schedule_as_paid($requisition_no,'Y',$claim_no,$request->peril,$request->payitem,$request->payee_type);
			// }


			##insert deductions 
			// if($request->offset_premium == 'Y'){
			// 	$total_prem = 0;

			// 	for ($i = 0; $i < count($request->debit_note_no); ++$i) {
					
			// 		##get line number
			// 		$ln_no = Requisition_deductions::where('req_no',$requisition_no)->max('ln_no');
			// 		$ln_no = $ln_no + 1;
					
			// 		$prdeduction = new Requisition_deductions;
			// 		$prdeduction->trans_no = $request->debit_note_no[$i];					
			// 		$prdeduction->reference = $request->debit_note_no[$i];					
			// 		$prdeduction->req_no = 	$requisition_no	;			
			// 		$prdeduction->endt_renewal_no = $request->endorse_no[$i];					
			// 		$prdeduction->claim_no = $claim_no;				
			// 		$prdeduction->policy_no = $request->policy_no[$i];					
			// 		$prdeduction->amount =  $request->unallocated[$i];					
			// 		$prdeduction->ln_no =  $ln_no;					
			// 		$prdeduction->uw_year =  $request->uw_year[$i];					
			// 		$prdeduction->currency_rate =  $requirement->currency_rate;					
			// 		$prdeduction->source ='CLM';				
			// 		$prdeduction->doc_type ='CRN'; 					
			// 		$prdeduction->entry_type = 'CLP';					
			// 		$prdeduction->dola = Carbon::now();					
			// 		$prdeduction->user_str = trim(Auth::user()->user_name);	
					
			// 		try {
			// 			$prdeduction->save();

			// 		} catch (\Throwable $e) {

			// 			DB::rollback();
			// 			$error_msg = json_encode($e->getMessage());
			// 			$referrence = $requisition_no; 
			// 			$route_name = Route::getCurrentRoute()->getActionName();
			// 			log_error_details($route_name,$error_msg,$referrence);
			// 			Session::flash('error',$e->getMessage());
			// 			return 3;
			// 		}

			// 		$total_prem = $total_prem + str_replace(',', '',$request->unallocated[$i]);
									
			// 	}
			// 		//dd($total_prem);
			// 	$payreqst_items['amount'] = $gross_amount - $total_prem;
			// 	$payreqst_items['nett_amount'] = $gross_amount - $total_prem;
			// 	$payreqst_items['local_amount'] = $gross_amount - ($total_prem*$requirement->currency_rate);
			// 	$payreqst_items['local_nett_amount'] = $gross_amount - ($total_prem*$requirement->currency_rate);
			// 	$payreqst_items['unallocated_total'] = $gross_amount - $total_prem;
			// 	$payreqst_items['local_unallocated_total'] = $gross_amount - ($total_prem*$requirement->currency_rate);
			// }
			

		for($i = 0; $i < count($request->deduction_amount); ++$i){
			$l=$i+1;
			$payreqst_items['deduction_amount_'.$l] = $request->input('deduction_amount')[$i];
			$payreqst_items['deduction_code_'.$l] = $request->input('deduction_code')[$i];
		}
		//   dd($payreqst_items);
		try{

				DB::transaction(function () use ($requisition_no,$agmnf,$payreqst_items,$request,$claim_no) {
				
					$insert_payreqst = payreqst::Create($payreqst_items);

					$this->updateCbseqno($insert_payreqst->dtrans_no);

					##update claim history
					$clmhistData = [
						'claim_no' =>$claim_no,
						'slug' => 'requisition-raised',
						'overide_status_desc' => 'Y',
						'additional_comment' => "Raised Req_no ".formatRequisitionNo($requisition_no)
					];
					ClaimProcessedEvent::dispatch((object)$clmhistData);
				});

				$process = 'Requisition Processing';
				$claimant= Claimant:: where('claim_no',$claim_no)->get();
				foreach($claimant as $claimant){
					$rec_ind= $claimant->rec_ind;
					$clmcorr_det = Clmcorr::where('code', $rec_ind)->first();
					if ($clmcorr_det->insured == 'Y') {
						$this->notify_client_claimprocess($claim_no, $process);
						$this->notify_intermediary_claimprocess($claim_no, $process);
					}
				}
			Session::flash('success', 'Requisition no ' . formatRequisitionNo($requisition_no) . ' has been raised');
			return 2;
			// return redirect()->back();
		
			}catch (\Exeption $e){
			
				DB::rollback();
				// report($e);
				$error_msg = json_encode($e->getMessage());
				$referrence = $requisition_no; 
				$route_name = Route::getCurrentRoute()->getActionName();

				log_error_details($route_name,$error_msg,$referrence);

				// dd($e,$request->all(),$requisition_no);
				// show the errors
				Session::flash('error',$e->getMessage());
				return 3;
				// return redirect()->back();
			}
		}
	}

	public function updateRequisitionGrossAmount(Request $request) {
		$claim_no = removePolicyOrClaimFormat($request->claim_no);
		$req_no = removeRequsitionFormat($request->req_no);
		$new_gross_amount = $request->new_gross_amount;
		$requisition_details = Payreqst::Where([
			['claim_no', '=', $claim_no],
			['req_no', '=', $req_no],
		])->first();

		$update_requisition_amount = Payreqst::Where([
			['claim_no', '=', $claim_no],
			['req_no', '=', $req_no],
		])
			->update([
				'gross_amount' => $new_gross_amount,
				'amount' => $new_gross_amount,
				'local_gross_amount' => $new_gross_amount * $requisition_details->currency_rate,
				'local_amount' => $new_gross_amount * $requisition_details->currency_rate
			]);
		

		##update claimant table
		$update_claimant = Claimant::where('claim_no',$requisition_details->claim_no)
		->where('claimant_code',$requisition_details->claimant_code)
		->where('line_no',$requisition_details->line_no)
		->update([
			'claim_amount'=> $new_gross_amount
		]);

		
		Session::flash('success', 'Gross amount for requisition no: ' . formatRequisitionNo($req_no) . ' has been changed');
		return redirect()->back();
	}

	public function requisitions_dat(Request $request){

		$payreqst = Payreqst::Where('claim_no', '=', $request->claim_no)
				->where('cancelled', null)
				->orderBy('created_time','desc')->get();
		return Datatables::of($payreqst)

		->editColumn('req_no',function($payreqst){
	
			return (
				formatRequisitionNo($payreqst->req_no)
				// '<input type="hidden" class="claim_req_no" value="{{ $payreqst->req_no }}">'
			);
			
		})
		->editColumn('gross_amount',function($payreqst){
	
			return (number_format($payreqst->gross_amount));
			
		})
		->editColumn('dola',function($payreqst){
	
			// return $payreqst->dola;
			return (formatDate($payreqst->dola));
			
		})
		->editColumn('created_time',function($payreqst){
	
			// return $payreqst->created_time;
			return (substr($payreqst->created_time,10));
			
		})

		->addColumn('action', function($payreqst){

			$clhmn = Clhmn::where('claim_no',$payreqst->claim_no)->first();

			if($clhmn->closed=='Y' || $payreqst->cancelled=='Y'){

				$document_button = '<button 
										class="btn btn-default btn-black btn-xs "
										data-toggle="tooltip"
										title="Claim file was closed by ' . htmlspecialchars($clhmn->closed_by) . '">
										<i class="fa fa-file"> </i>	Docs
									</button>';
				$cancel_button =  '<button 
										class="btn btn-default btn-black btn-xs "
										disabled="disabled">
										<i class="fa fa-times"> </i>	
										Cancel									
								</button>';

				$print_button = '<button 
										class="btn btn-default btn-black btn-xs "
										disabled="disabled">
										<i class="fa fa-print"> </i>	
										Print									
								</button>';

			}else if($clhmn->closed!='Y' && $payreqst->payments_todate>0 && $payreqst->cancelled!='Y'){

				$document_button = '<button 
										class="btn btn-default btn-black btn-xs "
										data-toggle="tooltip"
										title="Payments have already been made"
									>
										<i class="fa fa-file"> </i>	Docs
									</button>';
				$cancel_button = '<button 
									data-toggle="tooltip"
									title="Payments have already been made"
									>
										<i class="fa fa-times"> </i>	
										Cancel									
										</button>' ;
				$print_button =  '<button 
									data-toggle="tooltip"
									title="Cannot Print,payment has already been made"
									>
										<i class="fa fa-print"> </i>	
										Print									
								</button>';
				

			}else{
			
				// $document_button = '<button 
				// 						class="btn btn-default btn-black btn-xs updateReqdetails"
										 
				// 						 data-reqs-no = "'.$payreqst->req_no.'"
				// 						 >
				// 						<i class="fa fa-file"> </i>	Docs
				// 					</button>';
									
				$document_button = '<a 
									class="btn btn-default btn-black btn-xs"
									href="' . route('claims.req_process',["req_no"=>$payreqst->req_no]) .'">
									<i class="fa fa-file"> </i>	Docs
									</a>';

				$cancel_button = '<button 
										data-toggle="modal"
										data-claim-no="'.formatPolicyOrClaim($payreqst->claim_no).'"
										data-req-no="'.formatRequisitionNo($payreqst->req_no).'"
										data-gross-amount="'.number_format($payreqst->gross_amount).'"
										data-target="#cancel_requisition">
										<i class="fa fa-times"> </i>	
										Cancel									
								</button>';
				$print_button =  '<button 
									onClick="checkprintReq(`'.$payreqst->doc_type.'`,`'.$payreqst->req_no.'`)"
									>
										<i class="fa fa-print"> </i>	
										Print									
								</button>';
		
			}
			
			return $document_button. $cancel_button.$print_button   ;			
		})

		->rawColumns(['action'])
		->escapeColumns([])
		->make(true);

	}


	public function updateclaimhist($claim_hist_no,$claim_hist_code){

		$insert_clmhist = Clmhist::Create([
			'claim_no' => $claim_hist_no,
			'status_code' => $claim_hist_code,
			'status_date' => $request->date_notified,
			'dola' => Carbon::now(),
			'user_str' =>Auth::user()->user_name,
		]);
	} 

	
	##get currency rate
	public function getcurrencyrate(Request $request){

		$currency_rate = Currrate::where('currency_code',$request->currencyvalue)->first();
		return $currency_rate;
		
	}

	Public function checkreqs(Request $request){
		$requisition_dets = Payreqst::where('req_no',$request->req_no)->first();
		return $requisition_dets;
	}
	
	Public function checkrequisitiondocs(Request $request){
		$requisition_dets = Payreqstd::where('requisition_no',$request->req_no)->count();
		return $requisition_dets;
	}
	
	public function getDischargeVoucher(Request $request)
	{
		try {
			// return $request;
			$username = trim(Auth::user()->user_name);
			$claim_no = trim($request->claim_no);
			$order_number = trim($request->order_no);
			$dv_type = trim($request->dv_type);

			$discharge_voucher = Clmdischarge::whereRaw("trim(claim_no) = '" .$claim_no. "' and trim(dv_type) = '" .$dv_type. "' and trim(order_number) = '" .$order_number. "'")->first();

			$discharge_voucher_excess = Dvexcesses::whereRaw("trim(dv_type) = '" .$dv_type. "' and trim(voucher_order_no) = '" .$order_number. "'")->get();

			return [
				'voucher' => $discharge_voucher,
				'voucher_excess' => $discharge_voucher_excess
			];
		} catch (\Throwable $th) {
			//throw $th;
		}
	}


	// dischargeVoucherTable  0108025000018202004
	// public function dischargeVoucherTable(Request $request)
	// {	
	// 	$claim_no = $request->claim_no;
	// 	$username = trim(Auth::user()->user_name);
		
	// 	// $clmdischarge_info=Clmdischarge::where('claim_no','=',$claim_no)->get();
	// 	$clmdischarge_info=Clmdischarge::whereRaw("trim(claim_no)='" . $claim_no . "'")->get();
	// 	$aimsuprof = Aimsuprofgb::whereRaw("trim(aims_user)='" . $username . "'")->get()[0];
		
	// 	// return $request;
		

	// 	// to be changed to point discharge
	// 	// $printDischargeVoucher = $aimsuprof->print_garage_orders;

	// 	return DataTables::of($clmdischarge_info)

	// 			->addIndexColumn()
	// 			->addColumn('claimant_name', function ($data){
	// 				if($data->claimant_name != null ){
	// 					return $data->claimant_name;
	// 				}else{
	// 					$clm_name= Clparam::whereRaw("trim(claimant_code)='" . $data->claimant_code . "'")->first();
	// 					return $clm_name->name;
	// 				}
					
	// 			})
	// 			->editColumn('apply_under_insurance', function($data){
	// 				if ($data->apply_under_insurance == 'N'){
	// 					return 'Not applied';
	// 				}elseif($data->apply_under_insurance == 'Y'){
	// 					return 'Applied';
	// 				}else{
	// 					return 'N/A';
	// 				}
	// 			})
	// 			->addColumn('edit', function($data){
	// 				if($data->approved == 'Y'){
	// 					return '<a class="btn btn-sm btn-primary"
	// 								disabled= `disabled` ">
	// 								Edit
	// 							</a>';
	// 				}else{
	// 					return '<a class="btn btn-sm btn-primary"
	// 								onclick="editDischargeVoucher(`'. $data->order_number .'`, `'. $data->claim_no .'`,`'. $data->dv_type .'`)">
	// 								Edit
	// 							</a>';
	// 				}
					
	// 			})


	// 			->addColumn('action', function($data){
					
	// 				// if($printDischargeVoucher=='Y')
	// 				$ids= trim($data->user_id);
					
	// 				$user = Aimsuser_web::whereRaw("trim(user_name)='".$ids."'")->first();
	// 				$auth = Auth::user()->user_name;
	// 				$authid = Auth::user()->user_id;
					
	// 				// if(($data->dv_type == 1)||($data->dv_type == 8)||($data->dv_type == 6)||($data->dv_type == 61)){
	// 				// 	return '
	// 				// 	<button class="btn btn-success btn-sm"
	// 				// 		disabled="disabled"
	// 				// 		data-toggle="modal"
	// 				// 		title="Approved"
	// 				// 		data-document_path="'. $data->order_number.'"
	// 				// 		data-document_name="'.$data->claim_no.'"
	// 				// 		data-target="#approve_mod">
	// 				// 		<i class="fa fa-check"></i></button>

	// 				// 	<button class="btn btn-default btn-sm accept_offer"
	// 				// 		title="Accept/Decline Offer"
	// 				// 		data-toggle="modal"
	// 				// 		data-order_no="'. $data->order_number.'"
	// 				// 		data-claim_no="'.$data->claim_no.'"
	// 				// 		data-target="#offeraccept_modal">
	// 				// 		<i class="fa fa-reply"></i></button>

	// 				// 	<button class="btn btn-default btn-sm"
	// 				// 		disabled="disabled"
	// 				// 		data-toggle="modal"
	// 				// 		title="Re-escalate"
	// 				// 		data-order_no="'. $data->order_number.'"
	// 				// 		data-claim_no="'.$data->claim_no.'"
	// 				// 		data-target="#escalate_mod">
	// 				// 		<i class="fa fa-transfer"></i></button>
							
	// 				// 	<a href="/printDischargeVoucher/'. $data->order_number .'/'. $data->claim_no .'" target="blank">
	// 				// 	<i class="glyphicon glyphicon-print btn bg-info"
	// 				// 	></i></a>';

	// 				// }
	// 				// else if($data->approved=='Y') {
	// 				// return '
	// 				// <button class="btn btn-success btn-sm"
	// 				// 	disabled="disabled"
	// 				// 	data-toggle="modal"
	// 				// 	title="Approve"
	// 				// 	data-document_path="'. $data->order_number.'"
	// 				// 	data-document_name="'.$data->claim_no.'"
	// 				// 	data-target="#approve_mod">	
	// 				// 	<i class="fa fa-check"></i> </button>
	// 				// <button class="btn btn-default btn-sm"
	// 				// 	disabled="disabled"
	// 				// 	title="Re-escalate"
	// 				// 	data-toggle="modal"
	// 				// 	data-order_no="'. $data->order_number.'"
	// 				// 	data-claim_no="'.$data->claim_no.'"
	// 				// 	data-target="#escalate_mod">
	// 				// 	<i class="fa fa-refresh"></i></button>

	// 				// <button class="btn btn-default btn-sm"
	// 				// 	disabled="disabled"
	// 				// 	title="Accept/Decline Offer"
	// 				// 	data-toggle="modal"
	// 				// 	data-order_no="'. $data->order_number.'"
	// 				// 	data-claim_no="'.$data->claim_no.'"
	// 				// 	data-target="#offeraccept_modal">
	// 				// 	<i class="fa fa-reply"></i></button>
						
	// 				// <a href="/printDischargeVoucher/'. $data->order_number .'/'. $data->claim_no .'" target="blank">
	// 				// <i class="glyphicon glyphicon-print btn bg-info"
	// 				// ></i></a>';
					
	// 				// }else{

						

	// 					if((trim($authid)== trim($data->escalated_to)) ){
	// 						return '
	// 						<span class="text-danger"
	// 						data-toggle="modal"
	// 						title="Approve"
	// 						data-order_no="'. $data->order_number.'"
	// 						data-claimno="'.$data->claim_no.'"
	// 						data-user_id="'.$user->user_id.'"
	// 						data-claimant_code="'.$data->claimant_code.'"
	// 						data-target="#approvemod">
	// 						<i class="fa fa-check"></i> 
	// 						</span>

	// 						<span class="text-info accept_offer ml-3"
	// 						title="Accept/Decline Offer"
	// 						data-toggle="modal"
	// 						data-order_no="'. $data->order_number.'"
	// 						data-claim_no="'.$data->claim_no.'"
	// 						data-target="#offeraccept_modal">
	// 						<i class="fa fa-reply"></i></span>

	// 						<span class="text-primary ml-3"					
	// 							data-toggle="modal"
	// 							title="Re-escalate"
	// 							data-order_no="'. $data->order_number.'"
	// 							data-document_name="'.$data->claim_no.'"
	// 							data-target="#escalate_mod">
	// 							<i class="fa fa-refresh"></i></span>
	// 						<i class="noprint glyphicon glyphicon-print"></i>';
		
	// 					}else{
							
	// 						return '
	// 						<span class="text-danger"
	// 						data-toggle="modal"
	// 						disabled="disabled"
	// 						title="Approve"
	// 						data-order_no="'. $data->order_number.'"
	// 						data-claim_no="'.$data->claim_no.'"
	// 						data-user_id="'.$user->user_id.'"
	// 						data-target="#approvemod">
	// 						<i class="fa fa-check"></i> </span>

	// 						<span class="text-info ml-3"
	// 							disabled="disabled"
	// 							title="Accept/Decline Offer"
	// 							data-toggle="modal"
	// 							data-order_no="'. $data->order_number.'"
	// 							data-claim_no="'.$data->claim_no.'"
	// 							data-target="#offeraccept_modal">
	// 							<i class="fa fa-reply"></i></span>

	// 						<span class="text-primary ml-3"
							
	// 							data-toggle="modal"
	// 							title="Re-escalate"
	// 							data-order_no="'. $data->order_number.'"
	// 							data-document_name="'.$data->claim_no.'"
	// 							data-target="#escalate_mod">
	// 							<i class="fa fa-refresh"></i> </span>
	// 						<i class="noprint glyphicon glyphicon-print"></i>';
	// 					}
	// 				// }

					
	// 			})
	// 			->rawColumns(['edit', 'action'])
	// 			->make(true);
	// 	return $request;
	// }
	// dischargeVoucherTable.end


	public function dischargeVoucherTable(Request $request)
	{	
		$claim_no = $request->claim_no;
		$username = trim(Auth::user()->user_name);
		$clmdischarge_info=Clmdischarge::whereRaw("trim(claim_no)='" . $claim_no . "'")
		->where('doc_type','DSV')
		->orderBy('dola','desc')
		->get();
		
		return DataTables::of($clmdischarge_info)

				->addIndexColumn()
				->addColumn('claimant_name', function ($data){
					if($data->claimant_name != null ){
						return $data->claimant_name;
					}else{
						$clm_name= Clparam::whereRaw("trim(claimant_code)='" . $data->claimant_code . "'")->first();
						return $clm_name->name;
					}
					
				})
				->editColumn('order_no', function($data){

						$user = Aimsuser_web::whereRaw("trim(user_name)='".$data->user_id."'")->first();
						$time =$data->order_date;
						$date = new Carbon( $time );   
						$year = $date->format('Y');
						$mnth = $date->format('m');
						$order_num =  $data->branch.'/'.(int)substr($data->order_number, 0, 3).'/'. $mnth.'/'.$year;
						return $order_num;
						
				})
				->editColumn('dv_type', function($data){

						$type = Clmdischarge_voucher::where('type_id',$data->dv_type)->first();
						
						return $type->type_name;

				})

				->editColumn('approved_by', function($data){

					$user = Aimsuser_web::whereRaw("trim(user_id)='".$data->approved_by."'")->first();
					return $user->user_name;

			    })
				
				->editColumn('dolas', function($clmdischarge_info){

					return formatDate($clmdischarge_info->dola);

				})
				
				->addColumn('edit', function($data){
					if($data->approved == 'Y'){
						return '<a class="btn btn-sm btn-primary"
									disabled= `disabled` ">
									Edit
								</a>';
					}else{
						return '<a class="btn btn-sm btn-primary"
									onclick="editDischargeVoucher(`'. $data->order_number .'`, `'. $data->claim_no .'`,`'. $data->dv_type .'`)">
									Edit
								</a>';
					}
					
				})

				->addColumn('action', function($data){
					
					// if($printDischargeVoucher=='Y')
					$ids= trim($data->user_id);
					
					$user = Aimsuser::whereRaw("trim(user_name)='".$ids."'")->first();
					$auth = Auth::user()->user_name;
					$authid = Auth::user()->user_id;

					$dv_type = Clmdischarge_voucher::where('type_id', $data->dv_type)->first();

					$reescalate = '<button class="btn btn-default btn-sm"
									data-toggle="modal"
									data-order_no="'. $data->order_number.'"
									data-claim_no="'.$data->claim_no.'"
									data-target="#escalate_mod">
									<i class="fa fa-transfer"></i> Re-escalate </button>';

					$printdv = '<a href="/printDischargeVoucher/'. $data->order_number .'/'. encrypt($data->claim_no) .'" target="blank">
						<i class="glyphicon glyphicon-print btn bg-info"></i></a>
					';

					$approve = '<button class="btn btn-danger btn-sm"
							data-toggle="modal"
							data-order_no="'. $data->order_number.'"
							data-claimno="'.$data->claim_no.'"
							data-user_id="'.$user->user_id.'"
							data-claimant_code="'.$data->claimant_code.'"
							data-target="#approvemod">
							<i class="fa fa-transfer"></i>Approve 
						</button>';
						
					$canceldv = '<button class="btn btn-default btn-sm canceldischarge"
							data-toggle="modal"
							data-order_no="'. $data->order_number.'"
							data-claimno="'.$data->claim_no.'"
							data-user_id="'.$user->user_id.'"
							data-target="#cancel_dv">
							<i class="fa fa-trash"></i>Cancel 
						</button>';
					$sendletter = '
								<a class="sendletteremails"
								data-toggle="modal"
								data-order_no="'. $data->order_number.'"
								data-claim_no="'.$data->claim_no.'"
								data-user_id="'.$user->user_id.'" 
								data-target="#sendmail">
								<i class="fa fa-envelope-o btn bg-danger"></i></a>
						';
					
					if( $data->cancelled =='Y' ){
						return 'cancelled';
					}else if( trim($authid)== trim($data->escalated_to) && ($dv_type->escalate == 'Y') && ($data->approved != 'Y') ){

						return $printdv.$approve.$reescalate.$canceldv;
						
					}else if($dv_type->escalate == 'Y' && $data->approved !='Y' ){

						return $reescalate .$printdv.$canceldv ;

					}
					else if($data->sendmail !='Y' && $dv_type->attachment_send == 'Y') 

					{
						return $printdv.$sendletter.$canceldv;

					}else{
						return $printdv.$canceldv;
					}
				
				})
				

				->rawColumns(['edit', 'action'])
				->make(true);
		return $request;
	}
	
	
	public function sendmailfunction(Request $request){

		// dd($request);
		$voucher = Clmdischarge::whereRaw("trim(claim_no) = '" .$request->claim_no. "' and trim(order_number) = '". $request->mail_order_no . "'")
        						->first();

		$getpolicy_no = DB::select("select c.first_name,c.e_mail,d.branch,d.preview from clhmn a join polmaster b on a.policy_no = b.policy_no 
						left join client c on b.client_number  = c.client_number
						join clmdischarge d on a.claim_no = d.claim_no
						where a.claim_no = '$request->claim_no' and d.order_number = '$request->mail_order_no'");

		$order_no = $request->mail_order_no;				
		$time =$getpolicy_no[0]->order_date;

		$date = new Carbon( $time );   
		$year = $date->format('Y');
		$mnth = $date->format('m');

		$reference =  $getpolicy_no[0]->branch.'/'.(int)substr($order_no, 0, 3).'/'. $mnth.'/'.$year;

		#### discharge types #### 

		$getdvtype_details = Clmdischarge_voucher::where('type_id',$voucher->dv_type)->first();

		if($getdvtype_details->signature == 'Y'){

			$UserGroupLimitCheckerService = new UserGroupLimitCheckerService();

			$users = $UserGroupLimitCheckerService
					->setPermission('claim-documents-signatory')
					->fetchUsersWithPermission()->first();
				
			$manager_name = $users['name'];
			$signature = $users['signature'];
			
		}
			
	
		##send Mail with Attachment
		$file = View::make('dompdf_templates.discharge_vouchers.motor.new_letter_templates',
					 compact('voucher','getpolicy_no','reference','signature','title','manager_name','getdvtype_details','message_body'))
					->render();
        
        $pdf = App::make('dompdf.wrapper');
        $pdf->loadHTML($file)->setPaper('a4', 'portrait');
		
		$subject = trim($getdvtype_details->type_name);

		$address = $request->email;
		$message_body = $request->mesagebody;
		$cc = $request->mail_cc;

		$claimforms = Dvclaimform::where('claim_no', $request->claim_no)
						->where('dv_type',$voucher->dv_type)
						->where('order_number', $request->mail_order_no)
						->get();

		$mimes = [];
		$claim_form_paths = [];
		$names = [];
		if ($claimforms != null) {
			foreach ($claimforms as $form) {
				$claimform = Clsclaimform::where('id', $form->claim_form)->first();
				$binaryData = base64_decode($claimform->document);
				$claim_form_path = 'attachments/Claim_form'.$form->claim_no.'-'.$form->claim_form.'-'.$form->order_number.'.pdf';
				Storage::put($claim_form_path, $binaryData);
				$mime = $claimform->file_type;
				$name = $claimform->name;
				array_push($mimes, $mime);
				array_push($claim_form_paths, $claim_form_path);
				array_push($names, $name);
			}
		}

		try{
			$sender = env('MAIL_FROM_ADDRESS');

			Mail::send('dompdf_templates.discharge_vouchers.motor.sendletter_templates',
				['address'=>$address,'subject'=>$subject,'reference'=>$reference,'voucher'=>$voucher,'getpolicy_no'=>$getpolicy_no,'title'=>$title, 'manager_name'=>$manager_name,'message_body'=>$message_body], function($message) 
				use ($claim_form_paths, $mimes,$names, $address,$subject,$reference,$pdf,$voucher,$getpolicy_no,$signature,$title, $manager_name,$cc,$message_body)
			{
				$message->to($address);
				$message->subject($subject);
				$message->from($sender);		
				$message->cc($cc);		
				$message->attachData($pdf->output(), $reference.".pdf");
				if ($claim_form_paths != null) {
					for ($i=0; $i < count($claim_form_paths); $i++) { 
						$message->attach(storage_path('app/' . $claim_form_paths[$i]), [
							'as' => $names[$i].'.pdf',
							'mime' => $mimes[$i],
						]);
					}
				}
				
			});
			if ($claimforms != null) {
				for ($i=0; $i < count($claimforms); $i++) { 
					Storage::delete($claim_form_paths[$i]);
				}
			}
			$upd = Clmdischarge::whereRaw("trim(claim_no) = '" .$request->claim_no. "' and trim(order_number) = '". $request->mail_order_no . "'")
				->update([
					'sendmail'=>'Y'
				]);

			return 1;
	
		}catch(\Throwable $e){
			$codex = json_encode($e->getMessage());
			$error = explode('\n', $codex);
			$error_msg = $error[1];
			$referrence = "claim no = '$request->claim_no' ";
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);
			DB::rollback();
			return 2;
			
		}
	

	}

	public function ApproveRequisition(Request $request) {
		$claim_no = removePolicyOrClaimFormat($request->claim_no);
		$req_no = removeRequsitionFormat($request->req_no);
		$approve_requisition = Payreqst::Where([
			['claim_no', '=', $claim_no],
			['req_no', '=', $req_no],
		])
			->update([
				'approved_by' => $this->user_name,
				'approved_date' => $this->todays_date,
				'due_date' => $request->due_date,
			]);
		Session::Flash('success', 'Requisition no: ' . formatRequisitionNo($req_no) . ' has been approved');
		return redirect()->back();

	}

	public function cancelRequisition(Request $request) {
		try{
			$cancel_requisition = Payreqst::Where('req_no', '=',removeFormat($request->req_no))
			->update([
				'cancelled_by' => $this->user_name,
				'cancelled' =>'Y',
				'cancel_date'=>Carbon::now(),
				'reason_cancelled'=>$request->cancel_reason,
			]);


			$claim_no = Payreqst::where('req_no',$req_no)->pluck('claim_no')->first();
				// update claim status
					$clmhistData = [
						'claim_no' => $claim_no,
						'status_code' => 001, // claim registered
						'slug' => 'requisition-cancelled',
						'overide_status_desc' => 'Y',
						'additional_comment' => 'Cancelled Req no '. formatRequisitionNo($req_no). " ".$request->cancel_reason ,
						'status_date' => Carbon::now(),
					];
					ClaimProcessedEvent::dispatch((object)$clmhistData); 

			##getclhmn
			$prqst = Payreqst::where('req_no',removeFormat($request->req_no))->first();
			$clhmn = Clhmn::where('claim_no',$prqst->claim_no)->first();
			##check if schedule exists
			$classmodel = ClassModel::where('class',$clhmn->class)->first();

			if($classmodel->schedule_upload_mandatory == 'Y'){
				$update_schedule = update_schedule_as_paid($prqst->req_no,'N',$prqst->claim_no,$prqst->peril,$prqst->perilitem,$prqst->payee_type);
			}

			if($this->NewClaimsParameters->capture_payment_sla == 'Y'){

			$add_process_to_sla = [
				'unique_item' => [	
						'claim_no'=>$prqst->claim_no,
						'peril'=> $prqst->peril,
						'perilitem'=>$prqst->perilitem,
						'corr_type'=>$prqst->payee_type
					],
				'slug' => 'claim-payment-process',
				'activity_slug' => 'payment-allocation',
				'cancel' => 'Y',
			];
			AddProcessSLA::dispatch((object)$add_process_to_sla);
			}
			return redirect()->back()->with('success', 'Requisition no: ' . formatRequisitionNo($request->req_no) . ' has been cancelled');

		} catch (\Exeption $e) {
			DB::rollback();
			
			Session::flash('error','Failed to Cancel.');
			return redirect()->back()->with('error','Failed to Cancel.');
		}
		
	}

	public function approvegarage(Request $request) {
		
		$order_no=$request->order_no;
		$user_id=$request->user_id;
		$claim_no=$request->claim_no;
		$auth = Auth::user()->user_id;

		if($user_id == $auth){
			return redirect()->back()->with('error', 'You cannot Approve');

		}else{

			$clmorder = Clmorders::where('claim_no',$claim_no)
			->where('order_number',$order_no)
			->update([
					 'approved'=> 'Y',
					 'app_by'=>Auth::user()->user_id
			]);
		
			$name =  Auth::user()->user_name;
			$sender_id =  Auth::user()->user_id;
			
			$recieverdet = Aimsuser_web::where('user_id',$user_id)->first();
			$reciever = trim($recieverdet->user_name);
			$sent_id = trim($recieverdet->user_id);
			$count = Escalate_pol::where('sent_by',$sender_id)->max('escalate_id');
			$next = $count + 1;
	
			$emailaddr  = $recieverdet->email;
			$req_num = $request->req_no;
	
			$escalate = new Escalate_pol ;
			$escalate->escalate_id = $next;
		
			$escalate->type = 'GRG';
			$escalate->description = 'GARAGE APPROVED';
			$mess = "Garage order '$order_no' with claim number '$claim_no' Has been approved. You can now proceed to Print the order.Thank You. ";
			$category = 'GARAGE APPROVED';
			$escalate->sent_by =$name;
			$escalate->claim_no =$request->claim_no;
			$escalate->sent_to =$sent_id;
			$escalate->sent_by =$sender_id;
			$escalate->user_name = $reciever;
			$escalate->created_at =  Carbon::now();
			$escalate->save();

			$sendemail = new Sendemail;
			$sendemail->category = $category ;
			$sendemail->receiver =$emailaddr;
			$sendemail->message =$mess;
			$sendemail->creator = $name;
			$sendemail->save();
		}

		return redirect()->back()->with('success', 'Successfully Approved');
	}

	public function approvedv(Request $request) {
		
		$order_no=$request->order_no;
		$user_id=$request->user_id;
		$claim_no=$request->claim_no;
		$claimant_code=$request->claimant_code;
		$auth = Auth::user()->user_id;

		if($user_id == $auth){
			return redirect()->back()->with('error', 'You cannot Approve');

		}else{
			
			$name =  Auth::user()->user_name;
			$sender_id =  Auth::user()->user_id;	
			$recieverdet = Aimsuser_web::where('user_id',$user_id)->first();
			$reciever = trim($recieverdet->user_name);
			$sent_id = trim($recieverdet->user_id);
			$emailaddr  = $recieverdet->email;
			$req_num = $request->req_no;

			
			$clmdischarge = Clmdischarge::where('claim_no',$claim_no)
						->where('order_number',$order_no)
						->where('claimant_code',$claimant_code)
						->first();

			$disch_type = Clmdischarge_voucher::where('type_id',$clmdischarge->dv_type)
						->first();
			
			$clmhistData = [
				'claim_no' => $claim_no,
				'slug' => 'dvletter-approved',
				'overide_status_desc' => 'Y',
				'additional_comment' => $disch_type->type_name.": ".$order_no.":  has been approved"
			];

			
			
			if($request->approve == 'approve'){

				$clmorder = Clmdischarge::where('claim_no',$claim_no)
				->where('order_number',$order_no)
				->update([
						'approved'=> 'Y',
						'approved_by'=>Auth::user()->user_id
				]);

				$approval = 'DV APPROVED';
				$mess = "Dear $reciever ,Discharge Voucher '$order_no' with claim number '$claim_no' Has been approved. You can now proceed to Print the order.<br>Thank You. ";
				$category = 'DV APPROVED';
				
				ClaimProcessedEvent::dispatch((object)$clmhistData);
			}
			else if($request->decline == 'decline'){
				$mess = "Discharve Voucher '$order_no' with claim number '$claim_no' Has been Declined.<br>Thank You. ";
				$category = 'DV DECLINED';
				$approval = 'DV DECLINED';
				$clmhistData['slug'] = 'dvletter-declined';
				$clmhistData['additional_comment'] = $disch_type->type_name.": ".$order_no." has been declined";
	
				ClaimProcessedEvent::dispatch((object)$clmhistData);
			}
		
			$count = Escalate_pol::where('sent_by',$sender_id)->max('escalate_id');
			$next = $count + 1;
			$escalate = new Escalate_pol ;
			$escalate->escalate_id = $next;
			$escalate->type = 'DSV';
			$escalate->description = $approval;
			$escalate->sent_by =$name;
			$escalate->claim_no =$request->claim_no;
			$escalate->sent_to =$sent_id;
			$escalate->sent_by =$sender_id;
			$escalate->user_name = $reciever;
			$escalate->created_at =  Carbon::now();
			$escalate->save();

			$sendemail = new Sendemail;
			$sendemail->category = $category ;
			$sendemail->receiver =$emailaddr;
			$sendemail->message =$mess;
			$sendemail->creator = $name;
			$sendemail->save();

		}
		return redirect()->back()->with('success', 'Message Sent Successfully');
	}


	public function escalaterequisition(Request $request ) {

		$name =  Auth::user()->user_name;
		$sender_id =  Auth::user()->user_id;

		$count = Escalate_pol::where('sent_by',$sender_id)->max('escalate_id');
		$next = $count + 1;
		$user_id = $request->escalate_doc;
		$recieverdet = Aimsuser_web::where('user_id',$user_id)->first();
		$reciever = trim($recieverdet->user_name);
		$sent_id = trim($recieverdet->user_id);
		$emailaddr  = $recieverdet->email;
		$req_num = $request->req_no;
		$order_no = $request->order_no;
		$pay = Payreqst::where('req_no',$req_num)->first();
		$claim_no = $pay->claim_no;

		$escalate = new Escalate_pol ;
		$escalate->escalate_id = $next;
		
		if($req_num !=null){
			$escalate->req_no =$req_num;
			$escalate->type = 'REQ';
			$escalate->description = 'REQUISITION APPROVAL';
			$mess = "Dear $reciever , Kindly Approve the Requisition with Referrence number '$req_num'. and claim no  '$claim_no'. Thank You. ";
			$category = 'REQUISITION APPROVAL';

			##update requisition no
			Payreqst::where('req_no',$req_num)->update([
				'escalated_to'=> $user_id
			]);

		}
		elseif($order_no !=null){

			$escalate->type = 'DSV';
			$escalate->description = 'DISCHARGE VOUCHER';
			$mess = "Dear $reciever , Kindly Approve the Discharge voucher Ref no. '$ref_no' with claim number '$claim_no'.Thank You. ";
			$category = 'DISCHARGE VOUCHER APPROVAL';

			##update clmdischarge 
			 Clmdischarge::where('claim_no',$claim_no)->where('order_number',$order_no)->update([
							'escalated_to'=> $user_id
					]);
		}
	
		else{
			
			$garagenum = $request->garage_order_order_no;
			$escalate->type = 'GRG';
			$escalate->description = 'GARAGE APPROVAL';
			$mess = "Dear $reciever , Kindly Approve the Garage order '$garagenum' with claim number '$request->claim_no'.Thank You. ";
			$category = 'GARAGE APPROVAL';
		}
		$escalate->sent_by =$name;
		$escalate->claim_no =$request->claim_no;
		$escalate->sent_to =$sent_id;
		$escalate->sent_by =$sender_id;
	
		$escalate->user_name = $reciever;
		$escalate->created_at =  Carbon::now();
		$escalate->save();
		$sendemail = new Sendemail;
		$sendemail->category = $category ;
		$sendemail->receiver =$emailaddr;
		$sendemail->message =$mess;
		$sendemail->creator = $name;
		$sendemail->save();
		Session::flash('success','Document has been re escalated');
		return redirect()->back()->with('success','Request for Re-escalation  has been Sent Succesfully');
	}

	public function CloseOrOpenClaimFile(Request $request) {
		$claim_no = removePolicyOrClaimFormat($request->claim_no);
		if(empty($request->status) || $request->status=='N'){
			$est = Clhmn::where('claim_no',$claim_no)->get(['curr_total_estimate']);
			// return $est[0]->curr_total_estimate;
			if($est[0]->curr_total_estimate > 0){
				return redirect()->back()->with('error', 'Cannot close claim with an outstanding Reserve');
			}else{
				//Close Claim File
				$status = 'Y';
				$status_code = 47;
			}
			
		}
		else{
			//Re-open Claim File
			$status='N';
			$status_code = 900;
		}

		DB::transaction(function () use ($request, $claim_no, $status,$status_code) {

			$clmhist = new Clmhist;
			$clmhist->claim_no = $claim_no;
			$clmhist->closed = $status;
			$clmhist->status_code = $status_code;
			$clmhist->comment_str = $request->comment;
			$clmhist->status_date = $this->todays_date_time;
			$clmhist->user_str = $this->user_name;
			$clmhist->dola = $this->todays_date;
			$clmhist->closed_by = $this->user_name;
			$clmhist->substatus_code =  $request->Closure_reason;
			$clmhist->save();

			if ($status == 'Y') {
				$update_clhmn = Clhmn::Where('claim_no', '=', $claim_no)
					->update([
						'closed_year' => $this->getDtran0()->account_year,
						'closed_month' => $this->getDtran0()->account_month,
						'closed_date' => $this->todays_date,
						'closed_by' => $clmhist->closed_by,
						'closed' => $status,
					]);
			} else {
				$update_clhmn = Clhmn::Where('claim_no', '=', $claim_no)
					->update([
						'revived' => 'Y',
						'revived_period' => $this->getDtran0()->uw_period,
						'revived_date' => $this->todays_date_time,
						'closed' => $status,
						##change rejected status if previously rejected
						'rejected'=>'N',
						'rejected_by'=>'',

						//Set attributes to null when claim file is reopened.
						'closed_by' => '',
						'closed_date' => '',
						'closed_year' => '',
						'closed_month' => '',
					]);
			}
		});

		$cls = (int)substr($claim_no, 3, 3);
		$workflow = ClassModel::where('class',$cls)->get();
		

		if ($status == 'Y') {
			$this->add_claim_to_workflow($claim_no,$workflow[0]->claim_workflow_id,$request->pid);
			return redirect()->back()->with('success', 'Claim file has been Closed');
		} else {
			$this->add_claim_to_workflow($claim_no,$workflow[0]->claim_workflow_id,$request->pid,'Y');
			return redirect()->back()->with('success', 'Claim file has been reopened');
		}

	}

	public function legal(Request $request){
	
		try {
			DB::beginTransaction();

			$claim_no = removePolicyOrClaimFormat($request->claim_no);
			$clparam_details = Clparam::Where('claimant_code','=',$request->claimant_code)->first();
			$clmcorr_details = Clmcorr::Where('code','=',$clparam_details->record_type)->first();
			$sltype=$clmcorr_details->sltype;
			$case_no = trim($request->case_no);

			// advocates
			$thirdp1 = $request->thirdp;
			$thirdp2 = $request->thirdp2;
			$thirdp3 = $request->thirdp3;
			$thirdp4 = $request->thirdp4;
			$thirdp5 = $request->thirdp5;
			// plantiffs
			$plantiff_name1 = $request->plantiff_name1;
			$plantiff_name2 = $request->plantiff_name2;
			$plantiff_name3 = $request->plantiff_name3;
			$plantiff_name4 = $request->plantiff_name4;
			$plantiff_name5 = $request->plantiff_name5;


			// defendants
			$defendant_name1 = $request->defendant_name1;
			$defendant_name2 = $request->defendant_name2;
			$defendant_name3 = $request->defendant_name3;
			$defendant_name4 = $request->defendant_name4;
			$defendant_name5 = $request->defendant_name5;
	
			$check_clmsummon = Clmsummon::whereRaw("trim(case_no)='".$case_no."'")
											->where('claim_no',$claim_no)
											->where('court_year',$request->case_year)
											->whereRaw("trim(court_type)='".$request->court_type."'")
											->count();
			
			if($check_clmsummon > 0){
				Session::flash('error','case number '.$case_no.' already exists in this court house. Choose another number');
				return redirect()->back();
			}

			$insert_clmsummon = new Clmsummon;

			$insert_clmsummon->claim_no= $claim_no;
			$insert_clmsummon->email= $request->pol_holder_email;
			$insert_clmsummon->amount=str_replace(',','', $request->amount);
			$insert_clmsummon->case_no = $request->case_no; 
			$insert_clmsummon->court_year = $request->case_year; 
			$insert_clmsummon->courthse = $request->courthse;
			$insert_clmsummon->court_type = $request->court_type;
			$insert_clmsummon->claimant_name = $request->claimant_name;
			// $insert_clmsummon->claimant_name2 = $request->claimant_name2;
			$insert_clmsummon->case_date = $request->case_date;
			$insert_clmsummon->due_date = Carbon::parse($request->case_date)->addDays(7);
			$insert_clmsummon->case_desc = $request->case_desc;
			$insert_clmsummon->sltype = $sltype;
            $insert_clmsummon->slhead = $request->claimant_code;
            // $insert_clmsummon->claimant_no = $request->claimant_code;
            $insert_clmsummon->company_advocate = $request->claimant_code;
            $insert_clmsummon->third_party_ad = $thirdp1;
			$insert_clmsummon->appointed = $this->todays_date;
			$insert_clmsummon->created_by = $this->user_name;
			$insert_clmsummon->created_time = $this->todays_date_time;
			$insert_clmsummon->created_date = $this->todays_date;

			// $insert_clmsummon->claimant_no2 = (int) $request->claimant_code2;
			$insert_clmsummon->reg_no = $request->reg_no;
			$insert_clmsummon->judge_name = $request->judge_name;

			$insert_clmsummon->third_party_ad2 = $thirdp2;
			$insert_clmsummon->third_party_ad3 = $thirdp3;
			$insert_clmsummon->third_party_ad4 = $thirdp4;
			$insert_clmsummon->third_party_ad5 = $thirdp5;

			$insert_clmsummon->plantiff_name1 = $plantiff_name1;
			$insert_clmsummon->plantiff_name2 = $plantiff_name2;
			$insert_clmsummon->plantiff_name3 = $plantiff_name3;
			$insert_clmsummon->plantiff_name4 = $plantiff_name4;
			$insert_clmsummon->plantiff_name5 = $plantiff_name5;

			$insert_clmsummon->defendant_name1 = $defendant_name1;
			$insert_clmsummon->defendant_name2 = $defendant_name2;
			$insert_clmsummon->defendant_name3 = $defendant_name3;
			$insert_clmsummon->defendant_name4 = $defendant_name4;
			$insert_clmsummon->defendant_name5 = $defendant_name5;

			$insert_clmsummon->save();

			$case_no = $request->case_no.$request->case_year;


			$process = 'legal-claim-reg-notification';

			$policy_holder = Clhmn::where('claim_no',$claim_no)->first();
			$claim_no  = formatPolicyOrClaim($claim_no);
			$policy_no  = formatPolicyOrClaim($policy_holder->policy_no);
			$subject = "New Legal Claim Registration Notification";
			$insured = $policy_holder->policyholder;

			$message = "<html>
			<body>
				<p>This is to notify you that a New Legal Claim Has been Booked in the system. </p>
				<ul>
					<li>Policy Number: $policy_no </li>
					<li>Claim Number: $claim_no </li>
					<li>Insured: $insured  </li>
					<li>Case Number: $case_no </li>
				</ul>
			</body>
			</html>";

			// sendprocessnotifications($process,$message,$subject);
			$notificationData = [
				'claim_no' => $claim_no,
				'case_no' => $case_no,
			];
			DispatchNotificationEvent::dispatch($slug = 'legal-claim-reg-notification',$notificationData);
			

			DB::commit();
			Session::flash('success','Legal Details updated successfully');
			return redirect()->back()->with('success','Legal Details updated successfully');
		} catch (\Exeption $e) {
			DB::rollback();
			// report($e);
			return $e->getMessage();
			Session::flash('error','Fatal error occurred.');
			return redirect()->back()->with('error','Fatal error occurred.');
		}


		DB::transaction(function() use($request,$claim_no,$sltype){
			
		});

	}

	public function judgement(Request $request){
		$claim_no = removePolicyOrClaimFormat($request->claim_no);
		/*Compute expiry Date based judgement date*/
		$jd_date = new Carbon($request->jd_date);
		$expiry_date = $jd_date->addDays($request->stay_pferiod);

		//dd($expiry_date);

		DB::transaction(function() use($request,$claim_no,$expiry_date){
			$insert_clmjudgement = new Clmjudgement;
			$insert_clmjudgement->claim_no=$claim_no;
			$insert_clmjudgement->case_no=$request->case_no;
			$insert_clmjudgement->nature_of_jd=$request->nature_of_jd;
			$insert_clmjudgement->jd_amount=$request->jd_amount;
			$insert_clmjudgement->jd_date=$request->jd_date;
			$insert_clmjudgement->stay_period_expire=$expiry_date;
			$insert_clmjudgement->stay_period=$request->stay_period;
			$insert_clmjudgement->rec_acction=$request->rec_acction;
			$insert_clmjudgement->ruling=$request->ruling;
			$insert_clmjudgement->created_date=$this->todays_date;
			$insert_clmjudgement->save();

		});
		return redirect()->back()->with('success','Judgement Details have been posted successfully');
	}

	public function checkWriteOff(Request $request)
	{
		return Gate::authorize('write-off-vehicle');

	}
	


	public function postWriteOff(Request $request)
	{
		$claim_no = removePolicyOrClaimFormat($request->write_claim_no);
		
		$clhmn = Clhmn::where('claim_no', $claim_no)->first();

		//chassis gotten from modtlmast and not modtlhist
		$getvehchassis = Modtlmast::where('endt_renewal_no', $clhmn->endt_renewal_no)
			->orWhere('reg_no', $request->write_reg_no)
			->value('chassis_no');

		// dd($getvehchassis);
		$reg_no = str_replace(' ', '', $clhmn->reg_no);

		
		$clhmn_upd = Clhmn::where('claim_no',$claim_no)
		->update(['written_off'=>$request->write_off]);

		$modtlmast = Modtlmast::where('reg_no', $reg_no)
		//->orWhere('chassis_no', $getvehchassis)//chassis_no added
		->update([
			'status' => 'WRO'
		]);

		$modtlhist = Modtlhist::where('reg_no', $reg_no)
		//->orWhere('chassis_no', $getvehchassis)//and chassis_no
		->update([
			'status' => 'WRO'
		]);


		$blacklist = [
			'chassis_no' => $getvehchassis,//chassis_no
			'policy_no' => $clhmn->policy_no,
			'endt_renewal_no' => $clhmn->endt_renewal_no,
			'claim_no' => $clhmn->claim_no,
			'reg_no' => $reg_no,
		];

		
		BlacklistVehicles::dispatch((object)$blacklist);

		return $res = ['status' => 1];

	}

	public function salvage(Request $request){
		
		$claim_no = removePolicyOrClaimFormat($request->claim_no);
		$claim_details = $this->getClaimDetails($claim_no);

		# Vehikl Details
		$vehikl = Modtlpivot::where('policy_no', $claim_details->policy_no)->where('reg_no', $claim_details->reg_no)->first();

		# Client Details
		$polmaster = Polmaster::where('policy_no', $claim_details->policy_no)->first();
		$category = 1;

		$serial_no = $this->generatesalvageserial($category);

		$is_vandalized = $request->is_vandalized;

		if($is_vandalized == 'Y' && $request->vandalized_amount == 0) {
			Session::flash('error','Vandalised amount must be greater than zero');
			return redirect()->back();
		}

		if($is_vandalized == 'Y' && strlen(trim( $request->vandalism_description)) == 0 && (empty($request->vandalized_amount))) {
			Session::flash('error','Please add the vandalised amount and details if '.$request->reg_no.' has been vandalised' );
			return redirect()->back();
		}

		$existing_salvage = Salvages::where('reg_no', $claim_details->reg_no )->where('chassis_no', $vehikl->chassis_no)->first();

		if($existing_salvage) {
			Session::flash('error',$request->reg_no.' is already salvaged.');
			return redirect()->back();
		} 
		else {
			DB::transaction(function() use($request, $claim_no, $claim_details, $polmaster, $vehikl,$serial_no, $is_vandalized ){
			
				$insert_salvages = new Salvages;
				$insert_salvages->claim_no=$claim_no;
				$insert_salvages->reg_no=$request->reg_no;
				$insert_salvages->chassis_no=$request->chassis_no;
	
				$insert_salvages->serial_no=$serial_no;
				$insert_salvages->item_name=$request->reg_no;
				// $insert_salvages->garage_name=$request->garage_name;
				// $insert_salvages->garage_code=$request->garage_name;
	
				$insert_salvages->storage_name=$request->storage_name;
	
				$insert_salvages->preacc_val=str_replace(',','',$request->preacc_val);
				$insert_salvages->salvage_amount=str_replace(',','',$request->salvage_amount);
				$insert_salvages->reserve_amt=str_replace(',','',$request->salvage_amount);
				$insert_salvages->policy_no=$claim_details->policy_no;
				$insert_salvages->dola=$this->todays_date;
				$insert_salvages->declared_date=$request->declared_date;
				$insert_salvages->access_time=$this->todays_date_time;
				$insert_salvages->user1=$this->user_name;
				$insert_salvages->month_added=$this->current_month;
				$insert_salvages->year_added=$this->current_year;
				$insert_salvages->tendered='N';
	
				$insert_salvages->orig_salvage_amount = str_replace(',','',$request->salvage_amount);
				$insert_salvages->make = $vehikl->make;
				$insert_salvages->model = $vehikl->model;
				$insert_salvages->manuf_year = $vehikl->manufacture_year;
				$insert_salvages->client_number = $polmaster->client_number;
				$insert_salvages->client_name = $polmaster->name;
				$insert_salvages->log_book = $vehikl->logbook_no;

				$insert_salvages->is_vandalized = $is_vandalized;
				$insert_salvages->vandalism_amt = $request->vandalized_amount;
				$insert_salvages->vandalism_desc = $request->vandalism_description;
	
				$insert_salvages->retained_by_client = 'N';
				$insert_salvages->debited = 'N';
				$insert_salvages->status = 801;
			
				$insert_salvages->save();
	
				$update_clhmn = Clhmn::Where('claim_no','=',$claim_no)
								->update(['written_off'=>'Y']);
	
	
			});
	
			$this->updatesalvageserial();
	
			Session::flash('success',$request->reg_no.' has been Salvaged');
			return redirect()->back();

		}
	}

	public function generatesalvageserial($category){

        $serial = Doctype::where('doc_type','SVG')->first();
        $new_serial = str_pad((string) $category, 2, '0', STR_PAD_LEFT).str_pad((string) $serial->serial_no, 6, '0', STR_PAD_LEFT).$this->current_year;
        return  $new_serial ;
    }

	function updatesalvageserial(){
        $new_serial = Doctype::where('doc_type','SVG')->increment('serial_no',1);
        //  return $new_serial;
    }

		
	// garage order
	public function addGarageOrder(Request $request)
	{
		try{
			$get_escdetails = $this->escalaterequisition($request, 1);
			
		

			DB::beginTransaction();

			// check if this fields exists
			$validator = Validator::make($request->all(), [
				'garage_claim_no' => 'required',
				'garage_order_currency' => 'required',
				'garage_order_currency_rate' => 'required',
				'garage_order_order_no' => 'required',
				'garage_order_provider_acc' => 'required',
				'garage_order_date' => 'required',
				'garage_order_total_costs' => 'required',
				

			]);

			// show the user the fields that are necessity
			if ($validator->fails()) {

		    	Session::flash('error', $validator->errors()->first());
				return back();
				
			}

			// check if under insurannce is applied
			$apply_under_insurance = 'N';
			if ( $this->IsNumNull($request->garage_order_underinsurance_rate_amnt) > 0) {

				$apply_under_insurance = 'Y';
				
			}

			// compute the next order number;
			$ordernumber=Clmorders::max('order_number');
			if($ordernumber==null){
				$ordernumber=0;
			}

			$username = trim(Auth::user()->user_id);

			$ordernumber += 1;
			//define some vars to be reused 
			$labour_charges = $this->IsNumNull(str_replace(',','',$request->garage_order_labour_charges));
			$cost_of_parts = $this->IsNumNull(str_replace(',','',$request->garage_order_cost_of_parts));
			$other_costs = $this->IsNumNull(str_replace(',','',$request->garage_order_other_costs));
			$contribution = $this->IsNumNull(str_replace(',','',$request->garage_order_contribution_net_amount));
			$voluntary_excess = $this->IsNumNull(str_replace(',','',$request->garage_order_vat_less_excess));
			$compulsory_excess = $this->IsNumNull(str_replace(',','',$request->garage_order_less_excess));
			$vat_amount = $this->IsNumNull(str_replace(',','',$request->garage_order_vat));
			$total = $this->IsNumNull(str_replace(',','',$request->garage_order_due_by_insurer));
			$curr_rate = $this->IsNumNull(str_replace(',','',$request->garage_order_currency_rate));


		    $clm = new Clmorders([
		        'CLAIM_NO' => $this->sanitizeAccNumber($request->garage_claim_no),
		        'CLAIMANT_CODE' => $this->sanitizeAccNumber($request->garage_order_provider_acc),
		        'CLAIMANT_NAME' => $request->garage_order_claimant_name,
		        'ORDER_NUMBER' => $ordernumber,
		        'LABOUR_CHARGES' => (float) $labour_charges,
		        'COST_OF_PARTS' => (float) $cost_of_parts,
		        'OTHER_COSTS' => (float) $other_costs,
		        'CONTRIBUTION' => (float) $contribution,
		        'VAT_RATE' => (float) $this->IsNumNull($request->garage_order_vat_rate),
		        'VAT_AMOUNT' => (float) $vat_amount,
		        'CURRENCY_CODE' => $request->garage_order_currency,
		        'CURR_RATE' => (float) $this->IsNumNull($request->garage_order_currency_rate),
		        'ORDER_DATE' => $request->garage_order_date,
		        'THIRD_PARTY_OWNER' => $request->garage_order_third_party_owner,
		        'REG_NO_FOR_REPAIR' => $request->garage_order_reg_number,
		        'VEHICLE_OWNER' => $request->garage_order_owner,
		        'QUOTATION' => $request->garage_order_quotation_no,
		        'VEHICLE_MAKE' => $request->garage_order_make,
		        'REG_NO_3RD_PARTY' => $request->garage_order_vehicle_reg_no,
		        'APPLY_UNDER_INSURANCE' => $apply_under_insurance,
		        'UNDER_INSURANCE_RATE' => (float) $this->IsNumNull($request->garage_order_underinsurance_rate),
		        'UNDER_INSURANCE_AMOUNT' => (float) $this->IsNumNull($request->garage_order_underinsurance_rate_amnt),
		        'ORDER_AMOUNT' => (float) $this->sanitizeAccNumber($request->garage_order_total_costs),
		        'ORDER_JOB' => $request->garage_order_worktobedone,
		        'COMPULSORY_EXCESS' => (float) $compulsory_excess,
		        'VOLUNTARY_EXCESS' => (float) $voluntary_excess,
		        'TOTAL_EXCESS' => (float) $compulsory_excess + $voluntary_excess,
		        'CLAIMANT_NAME' => $request->garage_order_claimant_name,
		        'COMPULSORY_EXCESS_RATE' => (float) $request->garage_order_less_excess_rate,
		        'DOLA' => Carbon::today(),
		        'ORDER_AMOUNT_FOREIGN' => (float) $request->garage_order_total_costs * $curr_rate,
		        'windscreen_amount' => (float) $request->windscreen_amount * $curr_rate	,
		        'LABOUR_CHARGES_FOREIGN' => (float) $labour_charges * $curr_rate,
		        'CONTRIBUTION_FOREIGN' => (float) $contribution * $curr_rate,
		        'VOLUNTARY_EXCESS_FOREIGN' => (float) $voluntary_excess * $curr_rate,
		        'TOTAL_FOREIGN' => (float) $total * $curr_rate,
		        'VAT_AMOUNT_FOREIGN' => (float) $vat_amount * $curr_rate,
		        'OTHER_COSTS_FOREIGN' => (float) $other_costs * $curr_rate,
		        'COMPULSORY_EXCESS_FOREIGN' => (float) $compulsory_excess * $curr_rate,
		        'TOTAL_EXCESS_FOREIGN' => (float) ($compulsory_excess + $voluntary_excess) * $curr_rate,
		        //'AGREE_NAME' => 'default',
		        'user_id' => $username,
		       	'APPROVED'=>'N',
		        'COST_OF_PARTS_FOREIGN' => (float) $cost_of_parts * $curr_rate
		      ]);
			$clm->CLAIMANT_NAME=substr(trim($request->garage_order_claimant_name),0,24);

		    if ($clm) {

		    	$clm->save();

				DB::commit();
				// show the user that the message is saved
				Session::flash('success', 'Garage Order Added');
				return back();


		    } else {
		    	// show the errors
		    	Session::flash('error', 'Error Occured While Adding Garage Order');
				return back();
		    }			
	
		}catch (\Exeption $e){

			DB::rollback();
			report($e);

		    // show the errors
			Session::flash('error',$e->getMessage());
			return back();

		}
	}
	// end of garage order

	// garageOrderTable
	public function garageOrderTable(Request $request)
	{
		$claim_no = $request->claim_no;
		$username = trim(Auth::user()->user_name);

		$clmordersinfo=Clmorders::where('claim_no','=',$claim_no)->get();
		$aimsuprof = Aimsuprofgb::whereRaw("trim(aims_user)='" . $username . "'")->get()[0];

		$printgarageorders = $aimsuprof->print_garage_orders;

		return DataTables::of($clmordersinfo)
				->addIndexColumn()
				->editColumn('apply_under_insurance', function($data){
					if ($data->apply_under_insurance == 'N'){
						return 'Not applied';
					}elseif($data->apply_under_insurance == 'Y'){
						return 'Applied';
					}else{
						return 'N/A';
					}
				})
				->editColumn('doladate', function($data){
					 
					return formatDate($data->dola);
					
				})
				->editColumn('app_by', function($data){
					$username = Aimsuser_web::where('user_id',$data->app_by )->first();
					 if($data->app_by == null){
							return 'Null';
					 }else{
						return $username->user_name;
					 }
				
					
				})
				->addColumn('action', function($data){
					$printgarageorders = 'Y';
					if($printgarageorders=='Y'){

					if($data->approved=='Y'){
						
						return '<button class="btn btn-success btn-sm"
						disabled="disabled"
						data-toggle="modal"
						data-document_path="'. $data->order_number.'"
						data-document_name="'.$data->claim_no.'"
						data-target="#view_document">
						<i class="fa fa-edit"></i> Approve </button>
						
						<a href="/printGarageOrderDocument/'. $data->order_number .'/'. $data->claim_no .'" target="blank">
						<i class="glyphicon glyphicon-print btn bg-info"></i> 
					</a>';
					}
					else{
						return '<button class="btn btn-default btn-xs btn-black btn-block"
						data-toggle="modal"
						data-order-no="'. $data->ordePermissionr_number.'"
						data-claim-no="'.$data->claim_no.'"
						data-user-id="'.$data->user_id.'"
						data-target="#approve_garage">
						<i class="fa fa-edit"></i>Approve</button>
						<a>
						<i class="glyphicon glyphicon-print btn bg-info"></i></a>
						';
					}
				}
					else{
						return '<i class="nopermission glyphicon glyphicon-print btn bg-info"></i>';
					}
					
				})
				->make(true);
		return $request;
	}
	// garageOrderTable.end

	// additional garage order funcs
	public function sanitizeAccNumber($value='')
	{
		$num = preg_replace('/[^\d]+/', '', $value);
		return $num;
	}

	public function IsNumNull($value)
	{
		if($value == null){
			return 0;
		}else{
			return $value;
		}
	}
	//  end additional garage order funcs

	public function get_req_no(Request $request){

		$req_docs = Payreqst::where('req_no',trim($request->req_no))
						->where('cancelled','=',null)
						->first();
		return $req_docs;

	}




	// test_nik
	public function showReqDocumentsDatatable(Request $request){

	    $req_docs = Payreqstd::where('requisition_no',trim($request->req_no))->get();

	    // return $req_docs;

		return Datatables::of($req_docs)
						->make(true);
	}

	// Raise approval for claim with outstaniding premium
	public function raise_claim_approval(Request $request){
		$new_approval = $request->appr;

		// return $request;

		DB::beginTransaction();
		try{
			$claim = Clhmn::where('claim_no', $request->appr_claim_no)->first();
			$resp = $this->check_pending_amt($claim);
			$acdet= $resp['acdet'];
			$apprObj = new ApprovalsMgt();
			
			if($request->re_escalate == 'Y'){
				$apprObj->re_escalate_approval($request);
				Session::flash('success','Successfully re-escalated approval');
			}
			else{
				$dtran = $this->generate_approval_serial();

				$approval = new Approvals;
				$approval->approval_id = $dtran['approval_id'];
				$approval->process_code = $request->process_code;
				$approval->claim_no = $request->appr_claim_no;
				$approval->type = 'CLM';
				$approval->description = $request->appr_notes;
				$approval->account_year = $dtran['account_year'];
				$approval->user_id = Auth::user()->user_id;
				$approval->dola = Carbon::now();
				$approval->date_created = Carbon::now();
				$approval->save();

				for($i=0;$i < count($new_approval);$i++){

					$flow = new Approval_flow;
					$flow->approval_id = $dtran['approval_id'];
					$flow->approval_level = $new_approval[$i]['level_id'];
					$flow->approver_id = $new_approval[$i]['approver'];
					$flow->status = 'P';
					$flow->dola = Carbon::now();

					//set next level details
					$start = null;
					$final = null;
					
					if($i == 0){
						$start = 'Y';

						// notify the first approver
						$category = 'OUTSTANDING PREMIUM APPROVAL';
						$approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
						$email = $approver->email;
						$name = Auth::user()->name;
						$message = "Kindly Approve this claim: ".formatPolicyOrClaim($request->appr_claim_no).".
							It has an outstanding premium of ".number_format($acdet->unallocated).".";

						$apprObj->send_email($category,$email,$message,$name);
					}
					
					if(($i +1) == count($new_approval)){
						$final = 'Y';
						$next_level = null;
						$next_approver = null;
					}
					else{
						$next_level = $new_approval[$i+1]['level_id'];
						$next_approver = $new_approval[$i+1]['approver'];
					}

					$flow->start_approval = $start;
					$flow->end_approval = $final;
					$flow->next_approval_level = $next_level;
					$flow->next_approver_id = $next_approver;

					$flow->save();
					
				}

				// update dtrans
				Dtran0::first()->increment('approval_serial', (int) '1');

				Session::flash('success','Successfully raised approval');
			}
			DB::commit();
			

			return redirect()->back();
		}
		catch(\Throwable $e){
			DB::rollback();
			// dd($e);
			Session::flash('error','Failed to raise an approval');

			return redirect()->back();
		}
		
	}

	public function fetch_approval_dtl(Request $request){
		$appr_id = $request->approval_id;
		$curr_user = Auth::user()->user_name;
		$approvers = array();

		try{
	
			$appr_dtl = Approvals::with('approval_flow')->where('approval_id',$appr_id)->first();

			foreach($appr_dtl->approval_flow as $approver){
				if($approver->start_approval == 'Y'){

					$resp = [
						'msg' => 'Success',
						'prev_approver' => null,
						'approval_status' => $approver->status,
						'code' => 200,
					];
				}
				else{
					$prev_approver = $approver->where('next_approval_level',$approver->approval_level)->first();
	
					if($prev_approver->status == 'R'){
						throw new \Exception("Approval was Rejected");
					}
					else{
						$resp = [
							'msg' => 'Success',
							'prev_approver' => $prev_approver,
							'approval_status' => $approver->status,
							'code' => 200,
						];	
					}
				}
			}

			return response()->json($resp, 200);

		}
		catch(\Throwable $e){
			
			$resp = [
				'msg' => $e->getMessage(),
				'prev_status' => null,
				'approval_status' => null,
				'code' => 400,
			];

			return response()->json($resp, 400);
		}

	}

	public function update_approval(Request $request){

		$data = [
			'approval_id' => $request->approval_id,
			'level_id' => $request->level_id,
			'status' => $request->status,
		];
		
		DB::beginTransaction();
		try{

			$apprObj = new ApprovalsMgt();
			$resp = $apprObj->change_approval_status($data);
	
			DB::commit();
			return redirect()->back()->with('success','Approval has been Succesfully processed');
		}
		catch(\Throwable $e){
			// dd($e);
			DB::rollback();
			return redirect()->back()->with('error','Failed to process Approval');
		}

	}

	public function generate_approval_serial(){
		$dtran = Dtran0::first();
		$approval_serial = $dtran->approval_serial;

		$pad_approval_id = str_pad((string) $approval_serial, 6, '0', STR_PAD_LEFT);
		$approval_id = "{$pad_approval_id}{$dtran->account_year}";

		return [
			'approval_id' =>$approval_id,
			'account_year' => $dtran->account_year,
		];
	}

	public function check_pending_amt($clhmn){
		$acdet = Acdet::where('endt_renewal_no',$clhmn->endt_renewal_no)->where("source",'U/W')->first();
		$checkpendig_amount = (int)$acdet->unallocated;
		$check_approval = Approvals::where('claim_no',$clhmn->claim_no)
			->orderBy('date_created','DESC')
			->first();

		$status = 'A';

		if($checkpendig_amount > 0){
			$status = 'N';
			$msg = 'Claim has an outstanding premium';
		}

		if($checkpendig_amount > 0 and isset($check_approval)){
			$status = 'N';
			if($check_approval->status == 'A'){
				$status = 'A';
				
			}
			elseif($check_approval->status == 'R'){
				$status = 'R';
				$msg = 'Claim Approval was rejected';
			}
			elseif($check_approval->status == 'P'){
				$status = 'P';
				$msg = 'Claim has a Pending approval';
			}
		}

		return [
			'status' => $status,
			'message' => $msg,
			'acdet' => $acdet,
			'pending_amount' => $checkpendig_amount,
		];
	}


	public function checkexistingrequisition(Request $request){

		$req_no = removeRequsitionFormat($request->requisition_no);
		$count = Payreqstd::where('requisition_no',$req_no)->count();

		if($count >0){
			return 1;
		}else{
			return 0;
		}
	}



	public function checkretainedsalvage(Request $request){

		$retained = DB::select("select a.dtrans_no,b.unallocated,a.third_party from salvages a join 
		creditclm b on a.dtrans_no =b.dtrans_no where a.claim_no = '$request->claim_no' and
		( a.retained_by_client = 'Y' or a.third_party = 'Y' ) and a.debited = 'Y' and b.unallocated > 0 	
		order by b.dtrans_no desc");

		
		return $retained;
	}


	public function req_process(Request $request){
		
		
		Gate::authorize('access-claim-requisition-details');

		$schem = schemaName();
		$gb = $schem['gb'];
		$gl = $schem['gl'];
		$common = $schem['common'];

		$requisitions =$request->get('req_no');
		// $requisitions = removeRequsitionFormat($request->get('requisition_no'));

		$req_details = Payreqst::where('req_no',$requisitions)->get();
		$claimant_code = $req_details[0]->claimant_code;
		$user_details = DB::table('aimsusers')
						->select('user_id', 'user_name')
						->get();
		
		$code = substr($claimant_code,0,1);

        $service_providers = Clmcorr::where('claimant','<>','Y')->where('insured','<>','Y')->get(['code']);

        $service_codes = [];

        foreach ($service_providers as $index => $value) {

            array_push($service_codes,$service_providers[$index]->code);

        }

        if(in_array($code,$service_codes)){
            $process = 'Service provider';
        }else{
            $process = 'client';

        }
		// dd($req_details[0]->claimant_code);

		
		$req_docs = Payreqstd::where('requisition_no',$requisitions)->get();
		$reqdoc = Reqdocs::all();
		$today = Carbon::today()->format('Y-m-d');
		$office = Nlparams::where('prid','OFF')->get();
		$cbdeduct = Cbdeduct::where('doc_type', 'PAY')->get();
		$requisition = Payreqst::where('req_no', $requisitions)->first();

		$amount = 0;
		foreach($req_docs as $req){
			$amount = $amount+$req->entry_type_amount;
		}
		$dat=array(
		'main' => 'Front Office', 
		'module' => 'Payments',
		'submodule'=>'Requisitions',
		'submod1'=>formatRequisitionNo($requisitions)
		);
		
		$aims_user = Aimsuser_web::where('user_id',$req_details[0]->recommended_to)->first();
		$dept = Classmodel::where('class',$req_details[0]->class)->first();
		$user_group = Auth::user()->aims_group;
		$grouplimit = Aimsgrouplimits::where('group_id',$user_group)
						// ->where('approve_fnote','Y')
						->where('dept',$dept->dept)
						->first();

		##get current status and display next action
		$get_wkflow = Escalations::where('code',$req_details[0]->escalate_id)
								->where('category',$req_details[0]->workflow_id)
								->first();
								
		$getnext_wkflow = Workflowsconfig::where('escalation_id',$get_wkflow->id)
							->first();

		##check if there are premium deduction
		$re = Payreqst::where('req_no',$requisitions)->first();
		if($re->offset_premium == 'Y'){
			$premdedutions = Requisition_deductions::where('req_no',$requisitions)->get();
		}

		$s_provider_invoices = ServiceProviderInvoices::where('foreign_unallocated', '>', 0)
								->whereRaw("trim(s_provider_code)='" . trim($req_details[0]->claimant_code) ."'")
								->whereRaw("trim(currency)='" . trim($req_details[0]->currency_code) ."'")
								// ->where('cancelled','<>','Y')
								->get();
		$escalations
		 = DB::select("select a.description,a.code from escalations a join workflowsconfig b on a.id = b.escalation_id where a.id = '$getnext_wkflow->send_to' ");
       // CLAIMS REQUISITION APPROVALS

        $user_id = trim(Auth::user()->user_id);
        $approval_process = Aims_process::where('slug',trim('claim-requisition'))->first();
        $process_code= trim($approval_process->process_code);
    
        $approval_process = Aims_process::with(['process_dtl','approval_levels'])->where('process_code',trim($process_code))->first();

		if ($approval_process) {
            $filteredApprovalLevels = $approval_process->approval_levels->filter(function ($level) use ($requisition) {
                return $requisition->local_amount >= $level->min_limit;
            });

            $approval_process->setRelation('approval_levels', $filteredApprovalLevels);
        }
        // fetch approvals if any
        $approval_dtl = Approvals::with('approval_flow')
                        ->where('req_no',trim($requisition->req_no))
                        ->where('type', 'REQAP')
                        ->orderBy('date_created','DESC')
                        ->first();

        $check_approval = Approvals::where('req_no',trim($requisition->req_no))
                        ->where('type', 'REQAP')->orderBy('date_created','DESC')->first();

        if(isset($check_approval)){
            // $status = 'N';
            if($check_approval->status == 'A'){
                $status = 'A';
                
            }
            elseif($check_approval->status == 'R'){
                $status = 'R';
                $msg = 'Requisition Approval was rejected';
            }
            elseif($check_approval->status == 'P'){
                $status = 'P';
                $msg = 'Requisition has a Pending approval';
            }
        }

        $approval_status = $status ;
        $approval_msg = $msg;

        //!Reversal approval 
        $process_2 = Aims_process::where('slug',trim('claim-requisition'))->first();
        $process_code_2= trim($process_2->process_code);
    
        $process_2 = Aims_process::with(['process_dtl',
                'approval_levels'
            ])
            ->where('process_code',trim($process_code_2))
            ->first();

        // fetch approvals if any
        $approval_dtl_2 = Approvals::with('approval_flow')
                                    ->where('req_no',trim($requisition->req_no))
                                    ->where('type', 'REQAP')
                                    ->orderBy('date_created','DESC')
                                    ->first();

        $check_approval_2 = Approvals::where('req_no',trim($requisition->req_no))
                                    ->where('type', 'REQAP')
                                    ->orderBy('date_created','DESC')
                                    ->first();

		$req_approved_by = Payreqst::where('req_no', $requisition->req_no)->first();
		
        if(isset($check_approval_2)){
            // $status = 'N';
            if($check_approval_2->status == 'A'){
                $status_2 = 'A';
                
            }
            elseif($check_approval_2->status == 'R'){
                $status_2 = 'R';
                $msg_2 = 'Requisition Approval was rejected';
            }
            elseif($check_approval_2->status == 'P'){
                $status_2 = 'P';
                $msg_2 = 'Requisition has a Pending approval';
            }
        }
        $approval_status_2 = $status_2 ;
        $approval_msg_2 = $msg_2;

	   // END CLAIMS REQUISITION APPROVALS

		return view('gb.claims.requisition_details',compact('req_details','user_details', 'req_docs','reqdoc','requisitions','requisition','premdedutions',
		'today','amount','office','cbdeduct','aims_user','grouplimit','escalations','process','s_provider_invoices','approval_process','approval_dtl','approval_status_2','approval_msg_2','approval_status','req_approved_by'))
		->with('users',$users)
		->with('dat',$dat);
	}


	public function requisitions_enquiry(Request $request){

		Gate::authorize('access-claim-requisition-enquiry');

		$requisitions = Payreqst::where('entry_type_descr','CLP')
								->where("voucher_raised", null)
								->where("cancelled", null)
								->where("checked_by", null);
		
		$aimsuser = Aimsuser::where('left_company','N')->get();
			
		return View('gb.claims.requisition_enquiry')->with(compact('requisitions','aimsuser'));
	}

		## uncheched requisition enquiries data table
	public function allclaimrequisitions( Request $request){

		$requisitions = Payreqst::where('doc_type','PRQ')
			->where("source_code", 'CLM')
			->where("migrated","<>","Y")
			->orderBy('created_date','desc');
        
	    
			
		

		return DataTables::of($requisitions)
		->editColumn('req_no',function($requisitions){
			return '<a href="' . route('claims.req_process',["req_no"=>$requisitions->req_no]) .'">'.formatRequisitionNo($requisitions->req_no).'</a>'    ;
		})

		->editColumn('curr',function($requisitions){
			$insured = DB::select("select * from clhmn where claim_no = '$requisitions->claim_no'");
			$currency = Currency::where('currency_code',$insured->currency_code)->first();
			return ($currency->currency);
		})

		->editColumn('claim_no',function($requisitions){
			return formatPolicyOrClaim($requisitions->claim_no);
		})

		->editColumn('amount',function($requisitions){
			return number_format($requisitions->amount);
		})

		->editColumn('created_date',function($requisitions){
			return formatDate($requisitions->created_date);
			// return formatDate($requisitions->created_date);
		})
		
		->editColumn('hidden',function($requisitions){
			return $requisitions->created_date;
		})
		
		->addColumn('status',function($requisitions){
		
			if($requisitions->cancelled == 'Y'){
				
				return 'Cancelled';

			}else if($requisitions->voucher_raised == 'Y'){

				return 'Paid';
				
			}else if($requisitions->clauthorized_by != null ){
			
				return 'Pending Finance Approval';

			}else if($requisitions->checked_date == 'Y'){
				
				return 'Pending Authorization';

			}else{

				$docs_count = Payreqstd::where('requisition_no',$requisitions->req_no)->count();
				if ($docs_count > 0){
				return 'Pending Checking';
			    }else{
				return 'Pending Support Document';
				}

			}
		})
		
		->setRowClass(function($requisitions){
			return($requisitions->req_no ? 'clickable':' ');
		})

		->rawColumns(['req_no','curr'])
		->escapeColumns(['req_no','curr'])
		->make(true);

	}

		## uncheched requisition enquiries data table
	public function unchecked_req_dat( Request $request){

		$requisitions = Payreqst::where('doc_type','PRQ')
			->where("source_code", 'CLM')
			->where("checked_by", null)
			->where("cancelled", null)
			->where("migrated","<>","Y")
			->join('payreqstd','payreqstd.requisition_no','=','payreqst.req_no')
			->orderBy('created_date','desc');
			// ->get();
		

		return DataTables::of($requisitions)
		->editColumn('req_no',function($requisitions){
			return '<a href="' . route('claims.req_process',["req_no"=>$requisitions->req_no]) .'">'.formatRequisitionNo($requisitions->req_no).'</a>'    ;
		})

		->editColumn('insured',function($requisitions){
			$insured = Clhmn::where('claim_no',$requisitions->claim_no)->first();
			return ($insured->policyholder);
		})

		->editColumn('curr',function($requisitions){
			$insured = DB::select("select * from clhmn where claim_no = '$requisitions->claim_no'");
			$currency = Currency::where('currency_code',$insured->currency_code)->first();
			return ($currency->currency);
		})

		->editColumn('claim_no',function($requisitions){
			return formatPolicyOrClaim($requisitions->claim_no);
		})

		->editColumn('amount',function($requisitions){
			return number_format($requisitions->amount);
		})

		->editColumn('created_date',function($requisitions){
			return formatDate($requisitions->created_date);
			// return formatDate($requisitions->created_date);
		})
		
		->editColumn('hidden',function($requisitions){
			return $requisitions->created_date;
		})
		
		->setRowClass(function($requisitions){
            return($requisitions->req_no ? 'clickable':' ');
		})

		->rawColumns(['req_no','curr'])
		->escapeColumns(['req_no','curr'])
		->make(true);

	}

	##unapproved requisition enquiries data table
	public function checked_req_dat( Request $request){

		$requisitions = Payreqst::where('doc_type','PRQ')
			->where("source_code", 'CLM')
			->where("checked_by", '<>', null)
			->where("approved_by", null)
			->where("authorized", null)
			->where("cancelled", null)
			->where("clauthorized_by", null)
			->where("migrated",'<>','Y')
			->orderBy('created_date','desc');
			// ->get();
		

		return DataTables::of($requisitions)
		->editColumn('req_no',function($requisitions){
			return '<a href="' . route('claims.req_process',["req_no"=>$requisitions->req_no]) .'">'.formatRequisitionNo($requisitions->req_no).'</a>'    ;
		})

		->editColumn('insured',function($requisitions){
			$insured = Clhmn::where('claim_no',$requisitions->claim_no)->first();
			return ($insured->policyholder);
		})

		->editColumn('curr',function($requisitions){
			$insured = Clhmn::where('claim_no',$requisitions->claim_no)->first();
			$currency = Currency::where('currency_code',$insured->currency_code)->first();
			return ($currency->currency);
		})

		->editColumn('claim_no',function($requisitions){
			return formatPolicyOrClaim($requisitions->claim_no);
		})

		->editColumn('amount',function($requisitions){
			return number_format($requisitions->amount);
		})

		->editColumn('created_date',function($requisitions){
			return formatDate($requisitions->created_date);
		})
		
		->editColumn('hidden',function($requisitions){
			return $requisitions->created_date;
		})
		
		->setRowClass(function($requisitions){
			return($requisitions->req_no ? 'clickable':' ');
		})

		->rawColumns(['req_no','curr'])
		->escapeColumns(['req_no','curr'])
		->make(true);

	}

	public function check_requisition(Request $request){
		$req_no = $request->req_no;
		try{
			
			##function for updating req workflow
			$this->reqWorkflow($req_no);			
			Session::Flash('success','Requisition Actioned successfully');
		}
		catch (\Throwable $e) {
				// dd($e);
			$error_msg = json_encode($e->getMessage());
			$referrence = $req_no;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);

			DB::rollback();
			Session::flash('error','Failed to Action Requisition');

		}
	}

	public function reqWorkflow($req_no){
		$user = Auth::user()->user_name;
	
		$today = Carbon::today();
		##get escalation id
		$get_nextesc = Payreqst::where('req_no',$req_no)->first();
	
		$id = $get_nextesc->escalate_id;
	
		$wrkflow = $get_nextesc->workflow_id;
	
		##update the next step of the workflow
		$esc_id = DB::select("select b.send_to,b.workflow_id,a.finance_process,a.code  from escalations a 
										join workflowsconfig b on (b.workflow_id = a.category and a.id =b.escalation_id)
										where a.category = '$wrkflow' and a.code = '$id'");
	
		##update with the next object workflow
		$escalate = Workflowsconfig::where('workflow_id',$esc_id[0]->workflow_id)
								->where('escalation_id',$esc_id[0]->send_to)
								->first();
	
		$checks = Escalations::where('category',$esc_id[0]->workflow_id )
						->where('id',$esc_id[0]->send_to)->first();
		
		try {

			//code...
			if($checks->finance_process == 'Y'){
				$payreqst = Payreqst::where('req_no',$req_no)
							->update([
								'clauthorized_by'=>trim($user),
								'clauthorized_date'=>$today,
								'escalate_id'=>$checks->code,
								'due_date'=>$request->pay_date
							]);
				##update escalate pol
				$update = Escalate_pol::where('req_no',$req_no)->update([
					'approved'=>'Y',
					'approved_date'=>Carbon::now()
				]);

				$claim_no = Payreqst::where('req_no',$req_no)->pluck('claim_no')->first();
				// update claim status
					$clmhistData = [
						'claim_no' => $claim_no,
						'status_code' => 001, // claim registered
						'slug' => 'requisition-approved',
						'overide_status_desc' => 'Y',
						'additional_comment' => 'Approved Req no (Operation)'. formatRequisitionNo($req_no) ,
						'status_date' => Carbon::now(),
					];
                   
					ClaimProcessedEvent::dispatch((object)$clmhistData); 

						// $requisition = Payreqst::where('req_no', $req_no)->first();
						// $dtran = Dtran0::first();
						// $old_credit_no = $dtran->credit_no;
						// $credit_no = $old_credit_no + 1;

						// $creditclm = new Creditclm;
						// $creditclm->dr_cr = 'C';
						// $creditclm->doc_type = 'CRN';
						// $creditclm->entry_type_descr = 'CLP';
						// $creditclm->dtrans_no = $credit_no;
						// $creditclm->nett_amount = $requisition->gross_amount*-1;
						// $creditclm->unallocated = $requisition->gross_amount*-1;
						// $creditclm->allocated = 0;
						// $creditclm->claim_no = $requisition->claim_no;
						// $creditclm->dola = Carbon::today();
						// $creditclm->tran_no = $credit_no;
						// $creditclm->recovery_date = $request->recovery_date;
						// $creditclm->user_str = 'AUTOGENERATED';
						// $creditclm->REQUISITION_NO = $requisition->req_no;
						// $creditclm->policy_no = $requisition->policy_no;
						// $creditClm->account_year=Carbon::now()->format('Y');
						// $creditClm->account_month=Carbon::now()->format('m');
						// $creditClm->class=$requisition->class;
						// $creditClm->branch=$requisition->branch;
						// $creditClm->effective_date=Carbon::today();
						// $creditClm->despatch_date=Carbon::today();
						// $creditClm->currency_code=$requisition->currency_code;
						// $creditClm->currency_code=$requisition->currency_rate;
						// $creditClm->details=$requisition->narration;
						// $creditClm->payee=$requisition->name;

						// $creditclm->save();
						// // Update Dtran0 credit number
						// Dtran0::where('credit_no', $old_credit_no)->update(['credit_no' => $credit_no]);


			}else{
		
				$payreqst = Payreqst::where('req_no',$req_no)
							->update([
								'checked_by'=>trim($user),
								'checked_date'=>$today,
								'escalate_id'=>$checks->code,
								'due_date'=>$request->pay_date,
							]);

				$claim_no = Payreqst::where('req_no',$req_no)->pluck('claim_no')->first();
				// update claim status
					$clmhistData = [
						'claim_no' => $claim_no,
						'status_code' => 001, // claim registered
						'slug' => 'requisition-checked',
						'overide_status_desc' => 'Y',
						'additional_comment' => 'checked Req no '. formatRequisitionNo($req_no) ,
						'status_date' => Carbon::now(),
					];
					ClaimProcessedEvent::dispatch((object)$clmhistData);
				
				if($get_nextesc->source_code == 'CLM'){
		
					$permission = Permission::where('slug','approve-claim-requisition')->first();
        			$getuserdetails = $permission[0]->users;

					$emailaddr = trim($getuserdetails->email);
					$sendto_name = trim($getuserdetails->user_name);
					$sendto_id = $getuserdetails->user_id;
					$category = 'Requisition Approval';
					$mess = 'Kindly Authorize Requisition no (' .formatRequisitionNo($req_no) . ')   Thank You';
	   
					## send email notification
					$this->sendnotifyemail($category,$emailaddr,$mess,$name,$req_no,$sendto_id,$sendto_name);
				}
			
			}
			DB::commit();
		} catch (\Throwable $e) {
			DB::rollback();
			//throw $th;		
			$error_msg = json_encode($e->getMessage());
			$referrence = $emailaddr;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);
			
		  session::flash("Failed to send Emails");
		}
	
	}

	public function sendnotifyemail($category,$emailaddr,$mess,$name,$req_no){

		try {

			##update escalate pol
			// $update = Escalate_pol::where('req_no',$req_no)->update([
			// 	'approved'=>'Y',
			// 	'approved_date'=>Carbon::now()
			// ]);
			
			##auto escalate requisition
			$pay = Payreqst::where('req_no',$req_no)->first();
			$claim_no = $pay->claim_no;
			$sender_id =Auth::user()->user_id;
			
			## Insert new record 
			$count = Escalate_pol::where('sent_by',$sender_id)->max('escalate_id');
			$next = $count + 1;

			$escalate = new Escalate_pol ;
			$escalate->escalate_id = $next;
			$escalate->req_no =$req_no;
			$escalate->type = 'REQ';
			$escalate->description = $category;
			$escalate->sent_by =$sender_id;
			$escalate->claim_no =$claim_no;
			$escalate->sent_to =$sendto_id;
			$escalate->user_name = $sendto_name;
			$escalate->created_at =  Carbon::now();
			$escalate->save();
	
			//code...
			$sendemail = new Sendemail;       
			$sendemail->category = $category;
			$sendemail->receiver = $emailaddr;
			$sendemail->message = $mess;
			$sendemail->creator = Auth::user()->user_name;
			$sendemail->status = 'SEND';
			$sendemail->createdate = Carbon::today(); 
			$sendemail->save();
	
		} catch (\Throwable $e) {
	
			$error_msg = json_encode($e->getMessage());
			$referrence = $sendemail->receiver;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);
			
			// dd($e);
		  session::flash("Failed to send Emails");
		}
	
	}

	public function claimAssessment(Request $request){
		DB::beginTransaction();
		try{
			$clm_assess = DB::table('clmassess')->where('claim_no',$request->claim_no)->max('assessment_no');
			$claim_no = removePolicyOrClaimFormat($request->claim_no);
			$assessment_no = $claim_no.((int)$clm_assess +1);
				
			$insert_assess = Clmassess::Create([
				'claim_no' => $claim_no,
				'assessment_no' => $assessment_no,
				'date_received' => Carbon::parse($request->received_date),
				'amount' => (float) str_replace(',','',$request->assessment_amount),
				'approved_claim_amount' => (float) str_replace(',','',$request->claim_amount),
				'assessment_summary' => $request->assess_summary,
				'assessor' => $request->claimasessor,
				'inhouse_assessor' => $request->in_house,
				'reassessment' => $request->reassess,
				'approval_date' => $request->approved_date,
				'approved_by' => $request->approved_by,
				'identity_type' => $request->identitytype,
				'id_number' => $request->id_number
			]);

			// integrate to TIRAMIS
			$tiradata = [
				'claim_no' => $claim_no,
				'assessment_no' => $assessment_no
			];

			// TiraIntegrationEvent::dispatch($tiradata,$source='ASM');

			DB::commit();
			return [
				'status' => 1
			];
			return redirect()->back()->with('success', 'Assessment report has been added successfully.');

		}catch (\Throwable $e) {
			DB::rollback();
			
			Session::flash('error','Failed to add assessment');
			return [
				'status' => 0,
			];
		}
	}

	public function claimEditAssessment(Request $request){
		DB::beginTransaction();
		try{
			$claim_no = removePolicyOrClaimFormat($request->claim_no);
			if($request->reassess == 'Y'){
				$clm_assess = DB::table('clmassess')->where('claim_no',$claim_no)->count();
				$assessment_no = $claim_no.((int)$clm_assess +1);
				$insert_assess = Clmassess::insert([
					'claim_no' => $claim_no,
					'assessment_no' => $request->assessment_number,
					'claim_no' => $claim_no,
					'assessment_no' => $assessment_no,
					'date_received' => Carbon::parse($request->received_date)->format('Y-m-dh:i:s'),
					'amount' => (float) str_replace(',','',$request->assessment_amount),
					'approved_claim_amount' => (float) str_replace(',','',$request->claim_amount),
					'assessment_summary' => $request->assess_summary,
					'assessor' => $request->claimasessor,
					'inhouse_assessor' => $request->in_house,
					'reassessment' => $request->reassess,
					'approval_date' => Carbon::parse($request->approved_date)->format('Y-m-dh:i:s'),
					'approved_by' => $request->approved_by,
					'identity_type' => $request->identitytype,
					'id_number' => $request->id_number
				]);

				// integrate to TIRAMIS
				$tiradata = [
					'claim_no' => $claim_no,
					'assessment_no' => $assessment_no
				];

				// TiraIntegrationEvent::dispatch($tiradata,$source='ASM');
			}
			else{
				$assessment_no = $request->assessment_number;
				
				
				$insert_assess = Clmassess::where('claim_no', $claim_no)
					->where('assessment_no', $request->assessment_number)
					->update([
						'claim_no' => $claim_no,
						'assessment_no' => $assessment_no,
						'date_received' => Carbon::parse($request->received_date)->format('Y-m-dh:i:s'),
						'amount' => (float) str_replace(',','',$request->assessment_amount),
						'approved_claim_amount' => (float) str_replace(',','',$request->claim_amount),
						'assessment_summary' => $request->assess_summary,
						'assessor' => $request->claimasessor,
						'inhouse_assessor' => $request->in_house,
						'reassessment' => $request->reassess,
						'approval_date' => Carbon::parse($request->approved_date)->format('Y-m-dh:i:s'),
						'approved_by' => $request->approved_by,
						'identity_type' => $request->identitytype,
						'id_number' => $request->id_number
					]);
			}

			DB::commit();
			return [
				'status' => 1
			];
			return redirect()->back()->with('success', 'Assessment report has been saved successfully.');

			}catch (\Throwable $e) {
				DB::rollback();
			
				Session::flash('error','Failed to save assessment');
				return [
					'status' => 0,
				];
			}
	}
	
	public function processOffer(Request $request){
		DB::beginTransaction();
		try{
			$claim_no = removePolicyOrClaimFormat($request->claim_no);
			$order_no = $request->order_no;
				
				$insert_assess = Clmdischarge::where('claim_no', $claim_no)
				->where('order_number', $order_no)
				->update([
					'amount_offered' => (float) str_replace(',','',$request->amount_offered),
					'response_date' => $request->response_date,
					'reconciled_amt' => (float) str_replace(',','',$request->reconciled_amount),
					'reconciliation_date' => $request->reconciliation_date,
					'offer_accepted' => $request->accept_offer,
					'reconciled_summary' => $request->recon_summary
				]);
				// integrate to TIRAMIS
				$tiradata = [
					'claim_no' => $claim_no
				];

				// TiraIntegrationEvent::dispatch($tiradata,$source='DSV');
				DB::commit();

				
				return [
					'status' => 1
				];
				return redirect()->back()->with('success', 'Offer details added successfully.');

			}catch (\Throwable $e) {
				DB::rollback();
			
				Session::flash('error','Failed to add offer');
				return [
					'status' => 0,
				];
			}
	}

	public function assess_dat(Request $request ){

		$claim_no = $request->get('claim_no');

		$assess = Clmassess::whereRaw("trim(claim_no)='".$claim_no."'")->get();

		$claimant = Clparam::where('record_type', 'A')->get();
		$companyAssessors = Permission::with('users')
                    ->where('name','assess-claim')
                    ->first();
                
    	$companyAssessors = $companyAssessors->users()->get();
		// dd($companyAssessors);

		return Datatables::of($assess)

		

		->addColumn('action', function($data) use($claimant, $companyAssessors) {
			
			return '<button 
							class="btn btn-sm btn-info edit_assess" 
							data-assamount="'.$data->amount.'"
							data-daterec="'.$data->date_received.'"
							data-assessor="'.$data->assessor.'"
							data-claimamount="'.$data->approved_claim_amount.'"
							data-inhouse="'.$data->inhouse_assessor.'"
							data-assummary="'.$data->assessment_summary.'"
							data-appby="'.$data->approved_by.'"
							data-appdate="'.$data->approval_date.'"
							data-idtype="'.$data->identity_type.'"
							data-idnumber="'.$data->id_number.'"
						>
							<i class="fa fa-edit"></i>
						</button> 
						';
		 })
		 ->editColumn('assessor', function($d) use($claimant, $companyAssessors){
			if ($d->inhouse_assessor == 'Y') {
				foreach($companyAssessors as $clmt){
					if ($clmt->user_id == $d->assessor) {
						return $clmt->name;
					}
				}
			} else {
				foreach($claimant as $clmt){
					if ($clmt->claimant_code == $d->assessor) {
						return $clmt->name;
					}
				}
			}
			
		 })
		
		->rawColumns(['action'])
		->make(true);




	}

	## send facultative mail function
	public function sendfacmailfunction(Request $request){

		$date = new Carbon( $time );   
		$year = $date->format('Y');
		$mnth = $date->format('m');

		$branch = $request->mail_branch;
		$agent = $request->mail_agent;
		$claim_no = $request->claim_no;

		##send Mail with Attachment
		$file = New pdfController();
		$details = $file->single_fac_participants($claim_no,$branch,$agent);
		
		$file =View::make('dompdf_templates.claimdocuments.fac_participants',$details);

		// dd($details,$file);

        $pdf = App::make('dompdf.wrapper');
        $pdf->loadHTML($file)->setPaper('a4', 'portrait');
	
		$subject = 'FACULTATIVE LOSS ADVICE SLIP';

		$address = $request->email;
		$message_body = $request->mesagebody;
		if($request->mail_cc != null){
			$cc = $request->mail_cc;
		}else{
			$cc = '';
		}
	
		$reference = $claim_no;
		try{
			$sender = env('MAIL_FROM_ADDRESS');
			$company = trim(Pipcnam::first()->company_name);

			Mail::send('dompdf_templates.claimdocuments.sendletter_templates',
				['address'=>$address,'subject'=>$subject,'reference'=>$reference,'getpolicy_no'=>$getpolicy_no,'title'=>$title, 'manager_name'=>$manager_name,'message_body'=>$message_body,'sender'=>$sender], function($message) 
				use ($address,$subject,$reference,$pdf,$title,$cc,$message_body,$sender)
			{
				$message->to($address);
				$message->subject($subject);
				$message->from($sender);		
				$message->cc($cc);		
				$message->attachData($pdf->output(), $reference.".pdf");
				
			});
			return 1;
	
		}catch(\Throwable $e){

			$codex = json_encode($e->getMessage());
			$error = explode('\n', $codex);
			$error_msg = $error[1];
			$referrence = "claim no = '$request->claim_no' ";
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);
			DB::rollback();
			return 2;
			
		}
	

	}

	## file note 
	public function filenote(Request $request){

		
		if($request->asseamount != null){
			$ass_amt =str_replace(',','', $request->asseamount);
		}else{
			$ass_amt = str_replace(',','',$request->assamount);
		}

		if($request->totloss == 'Y'){
			$totloss =  'Y';
			$ins = $request->insura;
			if( (str_replace(',','',$request->totlossamt)) > (str_replace(',','',$request->finsamount))){
				$totamt = $request->totlossamt;

			}else{
				$totamt = $request->finsamount;
			}

		}else{
			$totloss =  'N';
			$ins= $request->insuranceamt;
			if($request->finsamount > 0){
				$totamt = $request->finsamount;
			
			}else if($request->totlossamt > 0 ){
				$totamt = $request->totlossamt;
	
			}
			else{
			$totamt = $request->totamt;
			}
		}

		// if($request->submit == true){

	
				$checkamount =  round(str_replace(',','',$totamt),2);
				

				try {

				
					// return $request;
					
					$note = Clmdischarge::where('claim_no', $request->claim_no)->where('doc_type','FNT')
											->max('order_number');
					$count = $note+1;
					$clhmn = clhmn::where('claim_no', $request->claim_no)->first();

					$policy_holder = polmaster::where('endorse_no',$clhmn->endt_renewal_no)->first();
					$class = classmodel::where('class',$policy_holder->class)->first();
				
					##check if amount is supposed to be surpase recommendation and straight to approvals
					// $rec = DB::select(DB::raw("SELECT distinct * from aimsgroup a 
					// join aimsgrouplimits b on a.aims_group= b.group_id 
					// where a.aims_group = 'GRP007' and b.dept = '$clhmn->dept'"));
			
					// if(round(str_replace(',','',$totamt),2)  >= $rec[0]->claim_limit){
					// 	$approved = 'N';
					// }else{
					// 	$approved = 'Y';
					// }
					$approved = 'Y';

					foreach ($request->add_deduct as $value){

						
						$bendescr = Cl_additions_deductions::where('id',$value['additions'])->first();
						$clmaddition = new Clmadditions;
						$clmaddition->FNOTE_NUMBER  = $count;
						$clmaddition->add_deduct_id  = (float) $value['additions'];
						$clmaddition->description  = $bendescr->description;
						$clmaddition->CLAIM_NO  = $request->claim_no;
						$clmaddition->amtx  = (float) str_replace(',','', $value['addition_amt']);
						$clmaddition->save();

						
					}


					// return $value;
					$filenote = new Clmdischarge;
					$filenote->order_number = $count;
					$filenote->claim_no = $request->claim_no;
					if(str_replace(',','',$request->claim_amount) > 0){
						$filenote->claim_amt =round( str_replace(',','',$request->claim_amount),2);
					}else{
						$filenote->claim_amt =0;
					}
					$filenote->assesed_amt = $ass_amt;
					##$filenote->adjusted_amt = round(str_replace(',','',$request->adjustamt),2);
					
					$filenote->wd_amount = (float)str_replace(',','',$request->wd_amount);
					$filenote->od_amount = (float)str_replace(',','',$request->od_amount);
					
					##$filenote->depriciated_amount =round( str_replace(',','',$request->depramount),2);
					$filenote->under_insurance = $request->uin;
					$filenote->gpa = $request->gpacomput;
					$filenote->pa = $request->pacomput;
					$filenote->wca = $request->wcacomput;
					$filenote->tpd = $request->tpdcomput;
					$filenote->ttd = $request->ttdcomput;
					$filenote->your_ref = $request->fdescription;
					$filenote->currency_code = $clhmn->currency_code;
					$filenote->curr_rate = $clhmn->currency_rate;

					// $filenote->wca = $request->wcacomput;
					$filenote->pa_amount = (float)str_replace(',','', $request->pa_amount);
					$filenote->gpa_amount = (float)str_replace(',','',$request->gpa_amount);
					$filenote->wca_amount = (float)str_replace(',','',$request->wca_amount);
					$filenote->tpd_amount = (float)str_replace(',','',$request->tpd_amount);
					$filenote->ttd_amount = (float)str_replace(',','',$request->ttd_amount);

					$filenote->d_monthly = (float)str_replace(',','', $request->d_monthly);
					$filenote->tpd_monthly =(float) str_replace(',','',$request->tpd_monthly);
					$filenote->ppd_monthly = (float)str_replace(',','',$request->ppd_monthly);
					$filenote->ttd_monthly = (float)str_replace(',','',$request->ttd_monthly);
					$filenote->ppd_percent = (float)str_replace(',','',$request->ppd_percent);

					$filenote->temp_percent =  (float)str_replace(',','',$request->temp_percent);
					$filenote->temp_days =  (float)str_replace(',','',$request->temp_days);
					$filenote->temp_monthly = (float)str_replace(',','',$request->temp_monthly);
					$filenote->ttd_days = (float)str_replace(',','',$request->ttd_days);

					$filenote->marketval_amt =(float) str_replace(',','',$ins);
					$filenote->insurance_amt =(float)str_replace(',','',$request->totinsamt);
					$filenote->payable_amt = (float)str_replace(',','',$totamt);
					$filenote->vat_amt =(float)str_replace(',','', $request->vatamt);
					$filenote->comments = $request->comments;
					$filenote->escalated_to = $request->esc_fnotes;
					$filenote->net_ratio = $request->nloss_ratio;
					$filenote->gross_ratio = $request->gloss_ratio;
					$filenote->assessmement_report_date = $request->assreportdate;
					$filenote->doc_type = 'FNT';
					$filenote->workflow_id = 800;
					$filenote->workflow_stage = 801;
					$filenote->approved = $approved;
					$filenote->user_id = trim(Auth::user()->user_name);
					$filenote->dola = Carbon::now();
					$filenote->total_loss = $totloss;
					$filenote->exgratia_per = (float)str_replace(',','', $request->exgratia_percentage);
					$filenote->dv_type = $request->request_type;

					$filenote->clmexcess_0 = $request->excess[0]['ex_type'];
					$filenote->clmexcess_1 = $request->excess[1]['ex_type'];
					$filenote->clmexcess_2 = $request->excess[2]['ex_type'];
					$filenote->clmexcess_3 = $request->excess[3]['ex_type'];
					$filenote->clmexcess_4 = $request->excess[4]['ex_type'];
					
					$filenote->limit_0 =  $request->docs_availed[0];
					$filenote->limit_1 =  $request->docs_availed[1];
					$filenote->limit_2 =  $request->docs_availed[2];
					$filenote->limit_3 =  $request->docs_availed[3];
					$filenote->limit_4 =  $request->docs_availed[4];
					$filenote->limit_5 =  $request->docs_availed[5];

					$filenote->clmexcess_amt0 =(float)str_replace(',','',$request->excess[0]['ex_amt']);
					$filenote->clmexcess_amt1 =(float)str_replace(',','',$request->excess[1]['ex_amt']);
					$filenote->clmexcess_amt2 =(float)str_replace(',','',$request->excess[2]['ex_amt']);
					$filenote->clmexcess_amt3 =(float)str_replace(',','',$request->excess[3]['ex_amt']);
					$filenote->clmexcess_amt4 =(float)str_replace(',','',$request->excess[4]['ex_amt']);
					$filenote->save();
				
					if($filenote->under_insurance == 'Y' && $recipient_email) {

						$notification_data = [
							'claim_no' => $clhmn->claim_no,
							'policy_holder' => $policy_holder->policy_no,
							'class' => $class->class,
							'order_number' => $filenote->order_number,
							'claim_amount' => $filenote->claim_amt,
							'client' =>$clhmn->policyholder,
						];

						DispatchNotificationEvent::dispatch($slug = 'notify-underwriter-claim-underinsured',$notification_data);
					}


					for ($i=0; $i < 5; $i++) { 
							
						if(str_replace(',','',$request->excess[$i]['ex_amt']) != null){
							$deduct_type = 'EXC';
							$claim_no = $request->claim_no;
							$deduction_amt = str_replace(',','',$request->excess[$i]['ex_amt']);
							$excess_type = $request->excess[$i]['ex_type'];
						}
						
					}
		
					if( $request->esc_fnotes != null){
						
						$sender_id =  Auth::user()->user_id;
						$name =  Auth::user()->user_name;
						$user_id = $request->esc_fnotes;

						$count = Escalate_pol::where('sent_by',$sender_id)->max('escalate_id');
						$next = $count + 1;
						$recieverdet = Aimsuser::where('user_id',$user_id)->first();
						$reciever = trim($recieverdet->user_name);
						$sent_id = trim($recieverdet->user_id);
						$emailaddr  = $recieverdet->email;
				
						$escalate = new Escalate_pol ;
						$escalate->escalate_id = $next;
						$escalate->type = 'FNT';
						$escalate->description = 'FILE NOTE Recommendation';
						$mess = "Kindly Approve the Recommend File Note with claim number '$request->claim_no'
								<table>
									<tr>
										<td  style='width:10%;'>
										<b>Class:</b>
										</td>	
										<td>
										'$class->description'
										</td>
									</tr>
									
									<tr>
										<td  style='width:10%;'>
										<b>Insured:</b>
										</td>	
										<td>
										'$clhmn->policyholder'
										</td>
									</tr>
									<tr>
										<td  style='width:10%;'>
										<b>Description:</b>
										</td>	
										<td>
										'$request->comments'
										</td>
									<tr>
									</tr>
										<td  style='width:10%;'> 
										<b>Payable Amount:</b>
										</td>	
										<td>
										'$totamt'
										</td>
									</tr>
								</table>
								.Thank You. ";
							
						$category = "FILE NOTE RECOMMENDATION";
						$escalate->claim_no =$request->claim_no;
						$escalate->sent_to =$sent_id;
						$escalate->sent_by =$sender_id;
						$escalate->user_name = $reciever;
						$escalate->created_at =  Carbon::now();
						$escalate->save();
			
						$notificationData = [
							'category' => $category,
							'message'=> $mess,
							'reciever_email' => $emailaddr,
							
						];
						DispatchNotificationEvent::dispatch($slug = 'escalate-filenote-notification',$notificationData);

						$clmorder = clmdischarge::where('claim_no', $request->claim_no)
						->where('doc_type', 'FNT')
						->where('order_number', $note + 1)
						->update([
							'escalate_id' => $next,
							'escalated_to' => $sent_id,
							'escalated_by'=> $sender_id
						]);
					}


					$clmhistData = [
						'claim_no' => $claim_no,
						'slug' => 'filenote-issued',
						'overide_status_desc' => 'Y',
						'additional_comment' => " File Note Reference {$request->fdescription}"
					];
					ClaimProcessedEvent::dispatch((object)$clmhistData);

					DB::commit();
					Session::flash('success', 'File Note was Added');
					return redirect()->back();
				} catch (\Throwable $th) {
					// dd($th);
					DB::rollback();
					Session::flash('error', 'Fatal error occurred');
					return redirect()->back();
				}
	
	}
	
	/* Claim Tabs for Datatables */
	public function filenotes_dt(Request $request){
	
		$claim_no = $request->claim_no;
		$username = trim(Auth::user()->user_name);
		
		$filenotes = Clmdischarge::where('claim_no', $claim_no)->where('doc_type','FNT')
		->orderBy('dola','desc')
		->get();
	
		return DataTables::of($filenotes)

		->editColumn('claim_amt',function($filenotes){
			return number_format($filenotes->claim_amt,2);

		})
	
		->editColumn('payable_amt',function($filenotes){
			return number_format($filenotes->payable_amt,2);

		})
		->editColumn('approved_by',function($filenote){
			$user = Aimsuser::where('user_id',$filenote->approved_by)->first();
			return $user->user_name;
		})
		->editColumn('authorized_by',function($filenote){
			$user = Aimsuser::where('user_id',$filenote->authorized_by)->first();
			return $user->user_name;
		})

		->editColumn('status',function($filenotes){

			$department = $clhmn->dept;
			$amount = $filenotes->payable_amt;

			
			$UserGroupLimitCheckerService = new UserGroupLimitCheckerService();

			$status = $UserGroupLimitCheckerService
					->setLimitCategory('file-notes')
					->setClass($department)
					->setAmount($amount)
					->CheckUserGroupLimit();

					// if($filenotes->approved == "Y" && $filenotes->authorized == null ){
					// 	return 'Pending Approval';
					// }else
			if(trim($filenotes->authorized) == 'Y'){
				return 'Approved';
			}else if(trim($filenotes->approved) == 'Y'){
				return 'Pending Approval';
			}else if(trim($filenotes->approved) == 'X'){
				return 'Declined';
			}else{
				return 'Pending Recommendation';
			}	
		})

		->addColumn('Print_fnote', function($filenotes){

			$current_stage = DB::select("SELECT e.description,w.send_to  FROM WORKFLOWSCONFIG w 
			left JOIN ESCALATIONS e ON w.ESCALATION_ID = e.ID 
			WHERE w.WORKFLOW_ID  = '$filenotes->workflow_id' and w.escalation_id = '$filenotes->workflow_stage'");

			$clhmn = Clhmn::where('claim_no',$filenotes->claim_no)->first();
			
			$department = $clhmn->dept;
			$amount = $filenotes->payable_amt;

			
			$UserGroupLimitCheckerService = new UserGroupLimitCheckerService();

			$status = $UserGroupLimitCheckerService
					->setLimitCategory('file-notes')
					->setClass($department)
					->setAmount($amount)
					->CheckUserGroupLimit();
			if($status == 1){
				$stage = 'Recommend and Approve';
			}else{
				$stage = $current_stage[0]->description;
			}
			

			$comments = '<a class="btn btn-sm view_comments_details" 
			data-order_number="'. $filenotes->order_number.'"
			data-your_ref="'. $filenotes->your_ref.'"
			data-claim_no="'. $filenotes->claim_no.'">
			<i class="fa fa-eye"></i></a>';


			$checkers =   '<a class="btn btn-sm "
			data-order_number="'. $filenotes->order_number.'"
			data-claim_no="'. $filenotes->claim_no.'"
			data-amount="'. $filenotes->payable_amt.'"
			onClick =  frontfilenote("'. $filenotes->order_number.'")>
			<i class="fa fa-print" style="font-size: large; font-weight: 600;"></i></a>
			
			<a class="btn btn-sm btn-primary escalations"
			data-order_number="'. $filenotes->order_number.'"
			data-claim_no="'. $filenotes->claim_no.'"">Escalate</a>

			<a class="btn btn-sm btn-default authorizefnote"
				data-order_number="'. $filenotes->order_number.'"
				data-claim_no="'. $filenotes->claim_no.'"
				data-user_id="'. $filenotes->user_id.'"
				data-workflow_id="'. $filenotes->workflow_id.'"
				data-workflow_stage="'. $filenotes->workflow_stage.'"
				data-stage="'. $stage.'"
				data-send_to="'. $current_stage[0]->send_to.'"
				data-amount="'. $filenotes->payable_amt.'"
				data-status="'. $status.'"
				data-your_ref="'. $filenotes->your_ref.'">'.$stage.'
			</a>
			
			';
			
			
			if($filenotes->authorized == 'Y'){

				return '<a class="btn btn-sm "
				data-order_number="'. $filenotes->order_number.'"
				data-claim_no="'. $filenotes->claim_no.'"
				data-amount="'. $filenotes->payable_amt.'"
				onClick =  frontfilenote("'. $filenotes->order_number.'")>
				<i class="fa fa-print" style="font-size: large; font-weight: 600;"></i></a>'.$comments;
				

			}else if($filenotes->escalated_to == Auth::user()->user_id){

				return $checkers.$comments;

			}else{

				return '<a class="btn btn-sm "
				data-order_number="'. $filenotes->order_number.'"
				data-claim_no="'. $filenotes->claim_no.'"
				data-amount="'. $filenotes->payable_amt.'"
				onClick =  frontfilenote("'. $filenotes->order_number.'")>
				<i class="fa fa-print" style="font-size: large; font-weight: 600;"></i></a>
				
				<a class="btn btn-sm btn-primary escalations"
				data-order_number="'. $filenotes->order_number.'"
				data-claim_no="'. $filenotes->claim_no.'"">Escalate</a>
				'.$comments;
			}


		})

		->rawColumns(['Print_fnote'])
		->escapeColumns(['Print_fnote'])	
		->make(true);
	}

	public function getreinsurance(){
		$agmnf =DB::select("select * from crmast where (acc_type = 3 or acc_type = 5)");

		return $agmnf; 
		
	}

	public function addDischargeVoucher(Request $request){

		try {
			//code...
			DB::beginTransaction();
			
			$validator =  Validator::make($request->all(),[
				'discharge_type' => 'required',
				'claim_no' => 'required',
			]);

				
			if ($validator->fails()) {
				Session::flash('error', 'some field are missing');


				return [
					'code' => -1,
					'msg' => $validator->errors(),
				];
			}

			##check for reminder letters
			$vouchertypes = DB::table('clmdisvouchertypes')->where('type_id',$request->discharge_type)->first();
			

			// $v_type = ['second-reminder-letter','first-reminder-letter','third-reminder-letter'];
			
			// dd($vouchertypes->slug,$request->all());

			if ($vouchertypes->slug == 'second-reminder-letter'||$vouchertypes->slug == 'first-reminder-letter'||$vouchertypes->slug == 'third-reminder-letter' ){

				##check if there is existing dev type
				$countclmdischarge = Clmdischarge::where('claim_no',$request->claim_no)->where('doc_type','DSV')
								->where('dv_type',$request->discharge_type)
								->where('cancelled',null)
								->count();
				if($countclmdischarge >  0){

					return [
						'code' => -2,
						'msg' => 'There Exist another active '.$vouchertypes->type_name.'. Kindy Cancel the existing one First if you wish to re issue a new one ',
					];
				}else{

					##check if first reminder is existing

					if ($vouchertypes->slug == 'second-reminder-letter'){
							$freminder = DB::select("
								SELECT * FROM CLMDISCHARGE c 
								LEFT JOIN CLMDISVOUCHERTYPES d ON d.TYPE_ID  = c.DV_TYPE 
								WHERE c.CLAIM_NO  = '$request->claim_no' AND 
								c.DOC_TYPE  = 'DSV'
								AND d.slug = 'first-reminder-letter'
								AND c.CANCELLED IS null
							");
						
							if(count($freminder) < 1){
								return [
									'code' => -2,
									'msg' => 'No active First Reminder Letter. Kindy issue First Reminder Letter before issuing '.$vouchertypes->type_name.'.'
								];
							}
						}
					
					
					if ($vouchertypes->slug == 'third-reminder-letter'){
							$freminder = DB::select("
								SELECT * FROM CLMDISCHARGE c 
								LEFT JOIN CLMDISVOUCHERTYPES d ON d.TYPE_ID  = c.DV_TYPE 
								WHERE c.CLAIM_NO  = '$request->claim_no' AND 
								c.DOC_TYPE  = 'DSV'
								AND d.slug = 'second-reminder-letter'
								AND c.CANCELLED IS null
							");
						
						if(count($freminder) < 1){
							return [
								'code' => -2,
								'msg' => 'No active Second Reminder Letter. Kindy issue Second Reminder Letter before issuing '.$vouchertypes->type_name.'.'
							];
						}
					}
					



				}
				


			}
			

			
			$username = trim(Auth::user()->user_name);
			$clhmn = Clhmn::where("claim_no" , trim($request->claim_no))->first();

			if( $request->disclaimant_name != null){
				$name = $request->disclaimant_name;

			}else{
				$name = null;
			}
			$rec_ind = $request->reccs_ind;
		$check = Claimant::where(['claim_no'=>$claim_no, 'rec_ind' =>$rec_ind ])->count();
		// $claimant_check = Claimant::count();

		$clmcorr_det = Clmcorr::where('code', $rec_ind)->first();


		// return $claimant_check;
		$clparam_detail = Clparam::Where('claimant_code','=',$request->disclaimant_code)->first();
		// dd(count($check));
			$count = 0;
			// $x = ['N', 'E', 'Y'];
			if ($clmcorr_det->insured == 'Y'){
				
					// dd("Y");
					$count = $check + 1;
					// return $count;
					$code = str_pad ( $count  , 4 , "0",  STR_PAD_LEFT);
					$code = $rec_ind.$code;
			}
			else if($clmcorr_det->claimant== 'Y'){
					$count = $check + 1;
					$code = str_pad ( $count  , 4 , "0",  STR_PAD_LEFT);
					$code = $rec_ind.$code;
				
			}else{
				$code = $claim_codes;
				$count = $clparam_detail->code;	
				$name = $clparam_detail->name;	
			}

				
			if ( strtolower($request->submit) == 'create') {

				$ordernumber=Clmdischarge::max('order_number');
						if($ordernumber==null){
							$ordernumber=1;
						}else{
							$ordernumber++;
						}
			
				try{

				$mydischarge = Clmdischarge::create([
					'dv_type' => $request->discharge_type,
					'your_ref' => $request->your_ref,
					'order_date' => $request->order_date,
					'claim_no' => $request->claim_no,
					'order_number' => $ordernumber,
					'dola' => Carbon::now(), 
					'dvtype_descr' => $request->dvtype_descr,
					'escalated_to' => $request->escalate_doc,
					'preview' => $request->sendcontext,
					'user_id' =>	$username,
					'currency_code' => $request->currency_code, 
					'curr_rate' =>$request->currency_rate, 	
					'doc_type'=>'DSV',
					'branch' => Auth::user()->branch,
					'claimant_code' => $code,
					'claimant_name' => $name,
					'fnoteref' => $request->fnoteref,
					'garage_name' => $request->garage_name,
					'garage_contacts' => $request->garage_contacts,
					'type_of_loss' => $request->type_of_loss,
				]);

				if ($request->has('claimform')) {
					for ($i=0; $i < count($request->claimform); $i++) { 
						Dvclaimform::create([
							'claimant_code' => $code,
							'claim_no' => $request->claim_no,
							'order_number' => $ordernumber,
							'dv_type' => $request->discharge_type,
							'claim_form' => (int)$request->claimform[$i],
						]);

					};
				}
				
		
					$process = 'claim-discharge-notification';

					$policy_holder = Clhmn::where('claim_no',$request->claim_no)->first();
					$claim_no  = $request->claim_no;

					##clmdisvouchertypes
					$vouchertypes=DB::table('clmdisvouchertypes')->where('type_id',$request->discharge_type)->first();

		
					$subject = $vouchertypes->type_name. " Issuance Notification";

					$insured = $policy_holder->policyholder;

					$message = "<html>
					<body>
						<p>This is to notify you that a $vouchertypes->type_name Has been Booked. </p>
						<ul>
							<li>Claim Number: ".formatPolicyOrClaim($claim_no)." </li>
							<li>Insured: $insured  </li>
						</ul>
					</body>
					</html>";

					$recipient_email = Aimsuser::where('user_id',$request->escalate_doc)->first()->email;

					if ($recipient_email != null) {

					// sendprocessnotifications($process,$message,$subject);
					$notificationData = [
						'claim_no' => $claim_no,
						'discharge_type' => $request->discharge_type,
						'order_no' => $ordernumber,
						'doc_type'=>'DSV',
						'recipient'=>$recipient_email,
					];
					DispatchNotificationEvent::dispatch($slug = 'claim-discharge-notification',$notificationData);
				}
					$clmhistData = [
						'claim_no' => $claim_no,
						'slug' => 'dvletter-issued',
						'overide_status_desc' => 'Y',
						'additional_comment' => "{$subject} with Ref no: {$request->your_ref} Issued"
					];
					ClaimProcessedEvent::dispatch((object)$clmhistData);

					

					DB::commit();

				
				}catch(throwable $e ){
					dd($e);
					DB::rollback();
					return [
						'code' => -1,
						'msg' => 'Discharge voucher not saved',
					];
				}

				
			}

			if( $request->escalate_doc != null){

				$sender_id =  Auth::user()->user_id;
				$branch =  Auth::user()->branch;
				$user_id = $request->escalate_doc;
				
				$count = Escalate_pol::where('sent_by',$sender_id)->max('escalate_id');
				$next = $count + 1;
				$recieverdet = Aimsuser::where('user_id',$user_id)->first();
				$order_num = $branch.'/'.(int)substr($request->order_number, 0, 3).'/'. $this->getDtran0()->account_month.'/'.$this->getDtran0()->account_year;
				
	
				$reciever = trim($recieverdet->user_name);
				$sent_id = trim($recieverdet->user_id);
				$emailaddr  = $recieverdet->email;
		
				$escalate = new Escalate_pol ;
				$escalate->escalate_id = $next;
				$escalate->type = 'DSV';
				$escalate->description = 'DISCHARGE APPROVAL';
				$mess = "Kindly Approve the Discharge voucher Ref no. '$order_num' with claim number '$request->claim_no'.Thank You. ";
				$category = 'DISCHARGE VOUCHER APPROVAL';
				
				$escalate->claim_no =$request->claim_no;
				$escalate->sent_to =$sent_id;
				$escalate->sent_by =$sender_id;
				$escalate->user_name = $reciever;
				$escalate->created_at =  Carbon::now();
				$escalate->save();
	
				$sendemail = new Sendemail;
				$sendemail->category = $category ;
				$sendemail->receiver =$emailaddr;
				$sendemail->message =$mess;
				$sendemail->creator = $name;
				$sendemail->save();
			}

			DB::commit();
			Session::flash('success', 'Discharge Voucher was Added');
			
			return [
				'code' => 1,
				'msg' => 'Discharge vourcher saved',
			];
		} catch (\Throwable $e) {
			
			$codex = json_encode($e->getMessage());
			$error = explode('\n', $codex);
			$error_msg = $error[1];
			$referrence = $request->claim_no;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);

			DB::rollback();

			dd($e);
			Session::flash('error', 'Fatal error occurred');
			return [
				'code' => -1,
				'msg' => 'Fatal error occurred',
			];
		}
	
	}
		//auth f note
		public function send_filenote_approval($request){

			$clhmn = Clhmn::where('claim_no',$request->claim_no)->first();
			$class = Classmodel::where('class',$clhmn->class)->first();
	
			$sender_id =  Auth::user()->user_id;
							$name =  Auth::user()->user_name;
							$user_id = $request->select_approver;
	
							$count = Escalate_pol::where('sent_by',$sender_id)->max('escalate_id');
							$next = $count + 1;
							$recieverdet = Aimsuser::where('user_id',$user_id)->first();
							$reciever = trim($recieverdet->user_name);
							$sent_id = trim($recieverdet->user_id);
							$emailaddr  = $recieverdet->email;
					
							$escalate = new Escalate_pol ;
							$escalate->escalate_id = $next;
							$escalate->type = 'FNT';
							$escalate->description = 'FILE NOTE APPROVAL';
							$mess = "Kindly Approve File Note with claim number '$request->claim_no'
									<table>
										<tr>
											<td  style='width:10%;'>
											<b>Class:</b>
											</td>	
											<td>
											'$class->description'
											</td>
										</tr>
										
										<tr>
											<td  style='width:10%;'>
											<b>Insured:</b>
											</td>	
											<td>
											'$clhmn->policyholder'
											</td>
										</tr>
										<tr>
											<td  style='width:10%;'>
											<b>Description:</b>
											</td>	
											<td>
											'$request->comment'
											</td>
										<tr>
									</table>
									.Thank You. ";
								
							$category = "FILE NOTE APPROVAL";
							$escalate->claim_no =$request->claim_no;
							$escalate->sent_to =$sent_id;
							$escalate->sent_by =$sender_id;
							$escalate->user_name = $reciever;
							$escalate->created_at =  Carbon::now();
							$escalate->save();
				
							$notificationData = [
								'category' => $category,
								'message'=> $mess,
								'reciever_email' => $emailaddr,
								
							];
							DispatchNotificationEvent::dispatch($slug = 'escalate-filenote-notification',$notificationData);
	
							$clmorder = clmdischarge::where('claim_no', $request->claim_no)
							->where('doc_type', 'FNT')
							->where('order_number', $request->order_no)
							->update([
								'escalate_id' => $next,
								'escalated_to' => $sent_id,
								'escalated_by'=> $sender_id
							]);
		}
	
		public function authfilenote(Request $request){
	
			try {
					
				$auth = Auth::user()->user_id;
				$name = trim(Auth::user()->user_name);
				$user_id= $request->userid;
	
				if($user_id == $name){
					return 0;
		
				}else{
					
					$sender_id =  Auth::user()->user_id;	
					$recieverdet = Aimsuser::whereRaw("trim(user_name)='".$user_id."'")->first();
					$reciever = trim($recieverdet->user_name);
					$sent_id = trim($recieverdet->user_id);
					$emailaddr  = $recieverdet->email;
	
					if($request->fclaim_no == null){
						$claim_no = $request->claim_no;
					}else{
						$claim_no  = $request->fclaim_no;
					}
						
					if($request->action_status == 2){
	
						if($request->statusx == 1){
	
							$clmorder = clmdischarge::where('claim_no', $claim_no )
							->where('doc_type', 'FNT')
							->where('order_number', $request->order_no)
							->update([
						
								'action'=> '2',
								'approved'=>'X',
								'approved_by'=>Auth::user()->user_id,
								'approved_date'=>Carbon::now(),
								'authorize_comment'=>$request->comment,
	
							]);
	
							$clmorder = clmdischarge::where('claim_no', $claim_no )
							->where('doc_type', 'FNT')
							->where('order_number', $request->order_no)->first();
							
							$escalatepol = Escalate_pol::where('claim_no', $claim_no)
						
									->where('type','FNT')
									->where('escalate_id',$clmorder->escalate_id)
									->where('sent_to',$clmorder->escalated_to)
									->where('re_escalate_date','=',null)
									->update([
											'approved'=>'Y',
											'approved_by'=>Auth::user()->user_id,
											'approved_date'=>Carbon::now(),
											'read'=>1,
									]);
	
	
							$approval = 'FILENOTE Declined';
							$mess = "File Note'$request->order_no' with claim number '$claim_no' Has been Authorized and Approved. You can now proceed.<br>Thank You. ";
							$category = 'FILENOTE APPROVED';
	
						}
						
						
					}
					else if($request->next_stage == null || $request->statusx == 1 ){
	
						if($request->statusx == 1){
	
							$clmorder = clmdischarge::where('claim_no', $claim_no )
							->where('doc_type', 'FNT')
							->where('order_number', $request->order_no)
							->update([
						
									'action'=> '1',
									'authorized'=> 'Y',
									'authorized_by'=>Auth::user()->user_id,
									'authorized_date'=>Carbon::now(),
									'authorize_comment'=>$request->comment,
									'approved'=>'Y',
									'approved_by'=>Auth::user()->user_id,
									'approved_date'=>Carbon::now(),
									'workflow_stage'=>''
							]);
	
	
							
							$clmorder = clmdischarge::where('claim_no', $claim_no )
							->where('doc_type', 'FNT')
							->where('order_number', $request->order_no)->first();
							
							$escalatepol = Escalate_pol::where('claim_no', $claim_no)
						
									->where('type','FNT')
									->where('escalate_id',$clmorder->escalate_id)
									->where('sent_to',$clmorder->escalated_to)
									->where('re_escalate_date','=',null)
									->update([
											'approved'=>'Y',
											'approved_by'=>Auth::user()->user_id,
											'approved_date'=>Carbon::now(),
											'read'=>1,
									]);
	
						}else{
	
							$clmorder = clmdischarge::where('claim_no', $claim_no )
							->where('doc_type', 'FNT')
							->where('order_number', $request->order_no)
							->update([
						
									'action'=> '1',
									'authorized'=> 'Y',
									'authorized_by'=>Auth::user()->user_id,
									'authorized_date'=>Carbon::now(),
									'authorize_comment'=>$request->comment,
									'workflow_stage'=>''
							]);
	
	
						
							$clmorder = clmdischarge::where('claim_no', $claim_no )
							->where('doc_type', 'FNT')
							->where('order_number', $request->order_no)->first();
							
							$escalatepol = Escalate_pol::where('claim_no', $claim_no)
						
									->where('type','FNT')
									->where('escalate_id',$clmorder->escalate_id)
									->where('sent_to',$clmorder->escalated_to)
									->where('re_escalate_date','=',null)
									->update([
											'approved'=>'Y',
											'approved_by'=>Auth::user()->user_id,
											'approved_date'=>Carbon::now(),
											'read'=>1,
									]);
	
	
						}
						
						
					
						$approval = 'FILENOTE APPROVED';
						$mess = "File Note'$request->order_no' with claim number '$claim_no' Has been Authorized and Approved. You can now proceed.<br>Thank You. ";
						$category = 'FILENOTE APPROVED';
	
					}else{
	
						$clmorder = clmdischarge::where('claim_no', $claim_no )
							->where('doc_type', 'FNT')
							->where('order_number', $request->order_no)
							->update([
									'approved'=>'Y',
									'approved_by'=>Auth::user()->user_id,
									'approved_date'=>Carbon::now(),
									// 'escalated_to'=>$request->select_approver,
									// 'escalated_by'=>Auth::user()->user_id,
									'workflow_stage'=>802
	
									
							]);
	
	
							
							$clmorder = ClmDischarge::where('claim_no', $claim_no)
							->where('doc_type', 'FNT')
							->where('order_number', $request->order_no)
							->first();
						
							Escalate_pol::where('claim_no', $claim_no)
								->where('type', 'FNT')
								->where('escalate_id', $clmorder->escalate_id)
								->where('sent_to', $clmorder->escalated_to)
								->whereNull('re_escalate_date')
								->update([
									'approved' => 'Y',
									'approved_by' => Auth::user()->user_id,
									'approved_date' => Carbon::now(),
									'read' => 1,
								]);
				
	
	
							if ($request->select_approver !== null) {
						
								$this->send_filenote_approval($request);
			
							}
			
					
						$approval = 'FILENOTE RECOMMENDED';
						$mess = "File Note'$request->order_no' with claim number '$claim_no' Has been RECOMMENDED.<br>Thank You. ";
						$category = 'FILENOTE RECOMMENDED';
	
	
					}
				}
					
				$notificationData = [
					'category' => $category,
					'message'=> $mess,
					'reciever_email' => $emailaddr,
					
				];
				DispatchNotificationEvent::dispatch($slug = 'approved-filenote-notification',$notificationData);
						
				return 1;
	
			} catch (\Throwable $th) {
				
				DB::rollback();
				return -1;
			}
	
			
		}
			//fnote escalation
		public function fnote_esc_form(Request $request){
	
	
				try{
				
				$sender_id =  Auth::user()->user_id;
				$name =  Auth::user()->user_name;
				$user_id = $request->escgrp;
				$count = Escalate_pol::where('sent_by',$sender_id)->max('escalate_id');
				$next = $count + 1;
				$recieverdet = Aimsuser_web::where('user_id',$user_id)->first();
				$reciever = trim($recieverdet->user_name);
				$sent_id = trim($recieverdet->user_id);
				$emailaddr  = $recieverdet->email;
	
				$clhmn= Clhmn::where('claim_no',$request->esc_claim_no)->first();
	
				$clmdischarge= clmdischarge::where('claim_no',$request->esc_claim_no)
											->where('doc_type','FNT')
											->where('order_number',$request->esc_order_num)
											->first();
				
	
				if($request->esc_claim_no == true){
					
					$type = 'FNT';
					$table1 = "<table>
								<tr>
									<td  style='width:10%;'>
									Insured:
									</td>	
									<td>
									'$clhmn->policyholder'
									</td>
								</tr>
								<tr>
									<td  style='width:10%;'>
									Description:
									</td>	
									<td>
									'$clmdischarge->comments'
									</td>
								</tr>
								<tr>
									<td  style='width:10%;'>
									Payable Amount:
									</td>	
									<td>
									'$clmdischarge->payable_amt'
									</td>
								</tr>
							</table>";
					if($request->action == 1){
						$description = 'FILE NOTE APPROVAL';
						$mess = " Kindly Approve the Approve File Note  '$request->esc_order_num' 
						with claim number '$request->esc_claim_no'. claim details are as follows
						<br>$table1<br>
						.Thank You. ";
					}else{
						$description = 'FILE NOTE RECOMMENDATION';
						$mess = " Kindly Recommend the File Note  '$request->esc_order_num' 
						with claim number '$request->esc_claim_no'
						<br>$table1<br>
						.Thank You. ";
					}
	
					##update clmdischarge
					$update_clmdischarge = Clmdischarge::where('claim_no', $request->esc_claim_no)
													->where('doc_type','FNT')
													->where('order_number',$request->esc_order_num)
													->update([
															
														'escalated_to' => $request->escgrp,
														'action' => $request->action,
														'escalated_by' => $sender_id,
														'escalate_id' => $next
	
													]);
				
				}else if($request->req_no == true){
					
					$type = 'REQ';
					$payreq = Payreqst::where('req_no',$request->req_no)->first();
	
					$table = "<table>
									<tr>
										<td  style='width:10%;'>
											Payee:
										</td>	
										<td>
											'$payreq->name'
										</td>
									</tr>
									<tr>
										<td  style='width:10%;'>
											Description:
										</td>	
										<td>
											'$payreq->narration'
										</td>
									</tr>
									<tr>
										<td  style='width:10%;'>
											Payable Amount:
										</td>	
										<td>
											'$payreqst->gross_amount'
										</td>
									</tr>
								</table> ";
					
					if($request->action == 1){
						$description = 'requisition APPROVAL';
						$mess = " Kindly Approve the Approve Requisition  '$request->req_no'
						with claim number '$payreq->claim_no'.Thank You. Requisition Details:<br>
						$table
						<br> ";
					}elseif($request->action == 2){
						$description = 'REQUISITION RECOMMENDATION';
						$mess = " Kindly Recommend the Requisition  '$request->req_no'.
						with claim number '$payreq->claim_no'.Thank You.<br>
						$table
						<br> ";
					}elseif($request->action == 3){
	
						$description = 'AUTHORIZE REQUISITION';
						$mess = " Kindly Authorize the Requisition  '$request->req_no'.
						with claim number '$payreq->claim_no'.Thank You.<br>
						$table
						<br> ";
						
					}
	
					$update_payreqst = payreqst::where('req_no', $request->req_no)
										->update([
												escalated_to => $request->escgrp,
												action => $request->action,
												escalated_by => $sender_id,
										]);
					}
				
					$escalate = new Escalate_pol ;
					$escalate->escalate_id = $next;
					$escalate->type = $type;
					$escalate->description = $description;
					$category = $description;
					
					$escalate->claim_no =$request->esc_claim_no;
					$escalate->sent_to =$sent_id;
					$escalate->sent_by =$sender_id;
					$escalate->user_name = $reciever;
					$escalate->created_at =  Carbon::now();
					$escalate->save();
	
				
					$notificationData = [
						'category' => $category,
						'message'=> $mess,
						'reciever_email' => $emailaddr,
						
					];
					DispatchNotificationEvent::dispatch($slug = 'escalate-filenote-notification',$notificationData);
	
				
				Session::flash('success','Escalation made Successfully');
				return redirect()->back();
				}catch (\Throwable $e) {
				// dd($e);
				DB::rollback();
	
				Session::flash('error','Failed to escalate');
				return redirect()->back();
				}
	
		}
	public function userClaimReserveLimitChecker(Request $request){

		$department = $request->dept;
		$amount = $request->amount;

		$UserGroupLimitCheckerService = new UserGroupLimitCheckerService();

		$status = $UserGroupLimitCheckerService
				->setLimitCategory('claim-reserve')
				->setClass($department)
				->setAmount($amount)
				->CheckUserGroupLimit();

		return json_encode($status);

	}

	public function show_claimsched_hist_dat(Request $request){
		
		$schedule = Claim_sched_hist::where('claim_no',$request->claim_no)
									->where('batch_no',$request->batch_no);
	
		return DataTables::of($schedule)
		->make(true);
	}
	
	public function showClaimSchedule(Request $request){
		
		$schedule = ClaimSchedule::where('claim_no',$request->claim_no);
	
		return DataTables::of($schedule)

		->editColumn('paid',function($schedule){
			if ($schedule->paid == 'Y'){
					return 'paid';
			}else{
					return 'Not Paid';
			}

		})
		
		->addColumn('action', function($schedule){
				
			return '<button class="btn btn-sm btn-info ammend_schedule" 
					data-tin_number="'.$schedule->member_number.'"
					data-amount="'.$schedule->amount.'"
					data-name="'.$schedule->name.'">
					<i class="fa fa-edit"></i> </button> 
				';
		})
		->rawColumns(['action'])
		->escapeColumns(['action'])	
		->make(true);
	}

	public function showClaimMedSchedule(Request $request){
		
		$schedule = ClaimSchedule::where('claim_no',$request->claim_no)
						->where('corr_type', $request->corr_type);

		return DataTables::of($schedule)

		->editColumn('paid',function($schedule){
			if ($schedule->paid == 'Y'){
					return 'paid';
			}else{
					return 'Not Paid';
			}

		})
		->editColumn('staff_no',function($schedule){
			$clhmn = Clhmn::where('claim_no', $schedule->claim_no)->first();
			$section = Polmedsec::where('endt_renewal_no', $clhmn->endt_renewal_no)
						->where('member_no', $clhmn->principle_member)
						->where('section_code', $schedule->staff_no)
						->first();
			return $section->description;

		})
		->editColumn('amount',function($schedule){
			return number_format($schedule->amount,2);

		})
		
		->addColumn('action', function($schedule){
			if ($schedule->paid == 'N') {	
				return '<button class="btn btn-sm btn-info ammend_schedule" 
						data-tin_number="'.$schedule->member_number.'"
						data-amount="'.$schedule->amount.'"
						data-staff="'.$schedule->staff_no.'"
						data-name="'.$schedule->name.'"
						data-peril="'.$schedule->peril.'"
						data-perilit="'.$schedule->perilsitem.'"
						data-corr="'.$schedule->corr_type.'">
						<i class="fa fa-edit"></i> </button> 
					';
			}
			
		})
		->rawColumns(['action'])
		->escapeColumns(['action'])	
		->make(true);
	}
	
	
	public function claimsched_hist_dat(Request $request){
		
		$claim_no = $request->claim_no;
		$schedule = DB::select("select batch_no,created_at,sum(amount) amount from claim_sched_hist where claim_no = '$claim_no' group by batch_no,created_at order by batch_no desc");
	
		return DataTables::of($schedule)

		->editColumn('batch_no',function($schedule){
			
			return str_pad ( $schedule->batch_no  , 4 , "0",  STR_PAD_LEFT);

		})

		->editColumn('amount',function($schedule){

			return number_format($schedule->amount);
			
		})
		
		->editColumn('created_at',function($schedule){

			return formatDate($schedule->created_at);
			
		})
		
		->addColumn('action', function($schedule){
				
			return '<button class="btn btn-sm btn-default view_details" 
					data-batch_no="'.$schedule->batch_no.'"
					data-claim_no="'.$schedule->claim_no.'">
					<i class="fa fa-eye"></i> View Details</button> 
				';
		})
		->rawColumns(['action'])
		->escapeColumns(['action'])	
		->make(true);
	}


	public function schedules($claim_no)
	{
		$clhmn = Clhmn::where('claim_no',$claim_no)->first();

		$current_estimate = DB::select("SELECT c.CURR_ESTIMATE  FROM clestdtl c
		JOIN PERILSITEMS p ON c.PERIL =p.PERIL AND p.ITEM_NO = c.PERILITEM 
		WHERE c.CLAIM_NO = '$claim_no' AND p.link_w_schedule = 'Y'");

		$schedule_reserve = ClaimSchedule::where('claim_no',$claim_no)
						->sum('amount');
		
		#### For Claimant ####
		$clmcorrs = Clmcorr::orderBy('name', 'asc')->get();
		$perils = Perils::where('is_active', 'Y')->where('class',$clhmn->class)->orderBy('peril_description', 'ASC')->get();

		return View('gb.claims.schedule_page')->with(compact('clhmn','current_estimate','schedule_reserve','clmcorrs','perils'));

	}

	public function medschedules($claim_no)
	{
		$clhmn = Clhmn::where('claim_no',$claim_no)->first();
		
		$current_estimate = DB::select("SELECT sum(c.CURR_ESTIMATE) as CURR_ESTIMATE  FROM clestdtl c
		JOIN PERILSITEMS p ON c.PERIL =p.PERIL AND p.ITEM_NO = c.PERILITEM 
		WHERE c.CLAIM_NO = '$claim_no' AND p.link_w_schedule = 'Y'");

		$schedule_reserve = ClaimSchedule::where('claim_no',$claim_no)
						->sum('amount');
		
		#### For Claimant ####
		$clmcorrs = Clmcorr::orderBy('name', 'asc')->get();
		$perils = Perils::where('is_active', 'Y')->where('class',$clhmn->class)->orderBy('peril_description', 'ASC')->get();
		$inpat_outpat = $clhmn->med_type;
		if ($inpat_outpat == 'O') {
			$secplans = Polmedoptplan::where('endt_renewal_no', $clhmn->endt_renewal_no)
							->where('member_no', $clhmn->principle_member)
							->get()
							->pluck('plan_code');
		} else {
			$secplans = Medmember::where('endt_renewal_no', $clhmn->endt_renewal_no)
							->where('member_no', $clhmn->principle_member)
							->get()
							->pluck('plan_code');
		}

		$sections = Polmedsec::where('endt_renewal_no', $clhmn->endt_renewal_no)
								->where('member_no', $clhmn->principle_member)
								->whereIn('plan_code', $secplans)
								->get();
		
		$schedule = $data = $data = ClaimSchedule::where('claim_schedule.claim_no', $claim_no)
										->groupBy(['claim_schedule.corr_type', 'claim_schedule.peril', 'claim_schedule.perilsitem', 'perils.peril_description', 'perilsitems.item_name'])
										->join('perils', 'claim_schedule.peril', '=', 'perils.peril')
										->join('perilsitems', function($join) {
											$join->on('claim_schedule.peril', '=', 'perilsitems.peril')
												->on('claim_schedule.perilsitem', '=', 'perilsitems.item_no');
										})
										->selectRaw('claim_schedule.corr_type, perils.peril_description, perilsitems.item_name, SUM(claim_schedule.amount) as amount')
										->get();
	
	
		return View('gb.claims.med_schedule_page')->with(compact('schedule','clhmn','current_estimate','schedule_reserve','clmcorrs','perils', 'sections'));

	}


	public function schedule_download(Request $request)
	{
	  $file = public_path()."/downloads/agric_schedule_template.csv";
	  $headers = array('Content-Type: application/csv',);
	  return Response::download($file, 'agric_schedule_template.csv',$headers);
	}

	public function cl_schedule_download_temp(Request $request){

		$file = public_path()."/downloads/claim_schedule_template.csv";
		$headers = array('Content-Type: application/csv',);
		return Response::download($file, 'claim_schedule_download_temp.csv',$headers);
  
	}

	public function save_claim_schedule(Request $request){

		
			if ($request->hasfile('data')) {
				$prosched = prosched::where('endorse_no',$request->endt_renewal_no)->first();
				$path = $request->file('data')->getRealPath(); 
				$extension = File::extension($request->file('data')->getClientOriginalName());

				$FileData = $request->file('data');

				
				if($extension == "csv" || $extension == "xls" || $extension == "xlsx"){
					
					$excel_data = FileUploadManager::excelUpload($request->file('data'));

					if (!empty($excel_data) && $excel_data->count()) {

						$member_numbers = array();
						$errors = array();
						$count_errors = 0;
						$z = 0;
						$member_count = 0;
						$count = 0;
						$all_prosched = array();
						$total_sum_insured = 0;

						$item_no = Prosched::where('endorse_no',$request->endt_renewal_no)
                                     ->Where(function($query){
                                          $query->Where('delete_str','<>','Y')
                                              ->OrWhereNull('delete_str');
                                      })
                                     ->count();
              
              			$errors[$z++] = "<ul>";
						
						  $claim_no = $request->claim_no;
						  $batch_no = DB::Select("select max(batch_no) from Claim_sched_hist where claim_no= '$claim_no'");

						  $batch_no =  Claim_sched_hist::Where('claim_no',$claim_no)->max('batch_no');
						  
						  $batch_no = $batch_no + 1;
						  
						  $date = Carbon::today();

						foreach ($excel_data as $value) {
							
							$prosched_count = 0;

							$prosched = Prosched::where('endorse_no', $request->endt_renewal_no)
										->where('location', $request->location)
										->whereRaw("trim(head1) ='".trim($value['member_number'])."'")
										->first();

							if ($prosched != null) {

								$prosched_count = $prosched->count() ;
							}


							$member_numbers[$member_count++] = trim($value['member_number']);
                  
							$item_no = $item_no + 1;
							$count = $count + 1;
							$amount = (float) str_replace(',', '', $value['amount']);

							$checkifexists = ClaimSchedule::where('claim_no',$request->claim_no)
											->where('member_number', trim($value['member_number']))
											->where('paid','=','Y')
											->count();
											
							if($prosched_count < 1){
								$count_errors++;
								$errors[$z++] = "<li>Member with Member number ".trim($value['member_number'])." Does Not exists in row ".$count."</li>";
							}else{
			
								if(($checkifexists > 0) ){
									$count_errors++;
									$errors[$z++] = "<li>A member already exists with Member number ".trim($value['member_number'])." in row ".$count."</li>";
								} 

								if($amount <= 0){
									$count_errors++;
									$errors[$z++] = "<li>Amount of member with tin number ".trim($value['member_number'])." cannot be zero or less than zero in row ".$count."</li>";
								}
								
								if($amount > str_replace(',', '',$prosched->amounta)){

									$count_errors++;
									$errors[$z++] = "<li>Amount of member with tin number ".trim($value['member_number'])." is Greater than what was captured in U/W in row ".$count."</li>";
									
								}

								if (empty($value['member_number'])) {
									$count_errors++;
									$errors[$z++] = "<li>Member Number is not captured at row ".$count."</li>";
									
								}

								if (empty($value['member_name'])) {
									$count_errors++;
									$errors[$z++] = "<li>Member name is not captured at row ".$count."</li>";
								}

								if($count_errors < 1){

									$total_amount += $amount;
										
									$prosched_array = [
										
										'name' => $prosched->detail_line,
										'location' => $request->location,
										'claim_no' => $request->claim_no,
										'corr_type' => $request->corrtype,
										'peril' => $request->peril_allocation,
										'perilsitem' => $request->peril_item_allocation,
										'paid' => 'N',
										'staff_no' =>$prosched->s_code,
										'amount' =>str_replace(',', '',$value['amount']),
										'member_number' => $value['member_number'],
										'batch_no' => $batch_no,
										'created_at' =>$date,
										'dola' =>Carbon::now()
									];

									array_push($all_prosched, $prosched_array);

								}

								$total_amount += $amount;
							
							}
										
						}
					
						$duplicates = collect($member_numbers)->duplicates();

						if ($duplicates->isNotEmpty()) {
		  
							$count_errors++;
							$errors[$z++] = "<li>Duplicate TIN numbers identified. Validate the TIN Numbers</li>";
						} 

						// if(str_replace(',', '',$request->reserve_amount) < $total_amount){
						// 	$count_errors++;
						// 	$errors[$z++] = "<li>Schedule total Amount Should match Reserve Amount. Kindly adjust your estimates</li>";
						// }
						
					}

					if($count_errors > 0){

						$claim_no = $request->claim_no;

						return view::make('gb.claims.modals.cl_upload_schedule_errors',compact('errors','endt_renewal_no'));
					  } 
					  else{

						DB::beginTransaction();

						try {

							$claim_no = $request->claim_no;

							$delete_unpaid = ClaimSchedule::where('claim_no',$request->claim_no)
											->where('paid','=','N')
											->delete();
						
							$create_prosched = ClaimSchedule::insert($all_prosched);
							$create_prosched_history = Claim_sched_hist::insert($all_prosched);
							
							$itemsum = array_column($all_prosched,'amount');

							$tot_amt = array_sum($itemsum);
							##insert estimate and Reserves
							$insert_estimate = $this->estimate_insert($all_prosched[0],$tot_amt);
							
							Session::flash("success","Member Schedule Successfully uploaded");

							return redirect()->back()->with('success','Member Schedule Successfully uploaded');
		
						} catch (\Throwable $th) {
							
							DB::rollback(); 
					  		return redirect()->back()->with('error','Please check file and try again');
						}

					  }
				}

			}else{
				return redirect()->back()->with('error','You can only upload .csv. Please use the template file format...');
			}
		  
	}

	public function save_claim_med_schedule(Request $request){
		$all_prosched = [];
		
		$claim_no = $request->claim_no;
		$clhmn = Clhmn::where('claim_no', $claim_no)->first();
		$member = $clhmn->principle_member;
		$dependant = $clhmn->dependant;
		
		$prosched = Meddependant::where('member_no', $member)->where('dependant_id', $dependant)->first();
		$batch_no = DB::Select("select max(batch_no) from Claim_sched_hist where claim_no= '$claim_no'");

		$batch_no =  Claim_sched_hist::Where('claim_no',$claim_no)->max('batch_no');
		
		$batch_no = $batch_no + 1;
		
		$date = Carbon::today();

		$total_amount = 0;

		$count_exist_items = ClaimSchedule::where('claim_no', $request->claim_no)
				->where('peril',$request->peril_allocation)
				->where('perilsitem',$request->peril_item_allocation)
				->where('corr_type',$request->corrtype)
				->get();

		$max = 0;
		if (count($count_exist_items) > 0) {
			$arr = collect($count_exist_items)->pluck('member_number');
			$substr = $dependant.$request->corrtype;
			foreach ($arr as $item) {
			
				$sequencePosition = strpos($item, $substr);
				
				$substringStart = $sequencePosition + strlen($substr);
    
				$substring = substr($item, $substringStart);
				if ((int)$substring > $max) {
					$max = (int)$substring;
				}
			}

		}
		$max += 1;

		for ($i=0; $i < count($request->med_item); $i++) { 
			$exists = ClaimSchedule::where('claim_no', $request->claim_no)
							->where('peril',$request->peril_allocation)
							->where('perilsitem',$request->peril_item_allocation)
							->where('corr_type',$request->corrtype)
							->where('staff_no',$request->med_item[$i])
							->exists();
			if($exists){
				continue;
			}
			$prosched_array = [
			
				'name' => $prosched->firstname." ".$prosched->surname,
				'location' => $request->location,
				'claim_no' => $request->claim_no,
				'corr_type' => $request->corrtype,
				'peril' => $request->peril_allocation,
				'perilsitem' => $request->peril_item_allocation,
				'paid' => 'N',
				'staff_no' =>$request->med_item[$i],
				'amount' =>str_replace(',', '',$request->item_estimate[$i]),
				'member_number' => $dependant.$request->corrtype.($max+$i),
				'batch_no' => $batch_no,
				'created_at' =>$date,
				'dola' =>Carbon::now()
			];

			$total_amount += str_replace(',', '',$request->item_estimate[$i]);

			array_push($all_prosched, $prosched_array);
		}

			


		DB::beginTransaction();

		try {

			$claim_no = $request->claim_no;

			if (count($all_prosched) > 0) {
		
				$create_prosched = ClaimSchedule::insert($all_prosched);
				$create_prosched_history = Claim_sched_hist::insert($all_prosched);
				
		
				$tot_amt = ClaimSchedule::where('claim_no', $request->claim_no)
								->where('peril',$request->peril_allocation)
								->where('perilsitem',$request->peril_item_allocation)
								->where('corr_type',$request->corrtype)
								->sum('amount');
	
	
				##insert estimate and Reserves
				$insert_estimate = $this->estimate_insert($all_prosched[0],$tot_amt);
			}
			
			Session::flash("success","Schedule Successfully saved");

			return redirect()->back()->with('success','Schedule Successfully saved');

		} catch (\Throwable $th) {
			DB::rollback(); 
			return redirect()->back()->with('error','Please check data and try again');
		}

	}

	public function get_schedule_items(Request $request){

		$endorse_no = $request->endorse_no;
		$tin_number = $request->tin_number;

		$getprosched = Prosched::where('endorse_no',$endorse_no)
						->where('head1',$tin_number)
						->first();
		return $getprosched;
	}

	public function get_schedule_med_items(Request $request){

		$endorse_no = $request->endorse_no;
		$section = $request->section_no;

		$claim_no = $request->claim_no;
		$clhmn = Clhmn::where('claim_no', $claim_no)->first();
		$getprosched = Polmedsec::where('endt_renewal_no',$endorse_no)
						->where('member_no',$clhmn->principle_member)
						->where('section_code',$section)
						->first();

		return $getprosched;
	}

	public function checkimedlimit(Request $request){
		$endorse_no = $request->endorse_no;
		$section = $request->section_no;
		$reserve_amount = $request->reserve_amount;

		$claim_no = $request->claim_no;
		$clhmn = Clhmn::where('claim_no', $claim_no)->first();
		$getprosched = Polmedsec::where('endt_renewal_no',$endorse_no)
						->where('member_no',$clhmn->principle_member)
						->where('section_code',$section)
						->first();

		$member = Medmember::where('member_no',$getprosched->member_no)->first();

		// $plan = MediPlan::where('plan_code',$plan)->first();
		if ($member->limit_per == 'F') {
			$claims = Clhmn::where('endt_renewal_no', $endorse_no)
					->where('member_no', $clhmn->member_no)
					->get()->pluck('claim_no')
					->toArray();

			$total_amount = ClaimSchedule::whereIn('claim_no', $claims)
							->where('staff_no', $section)
							->sum('amount');
		}else {
			$claims = Clhmn::where('endt_renewal_no', $endorse_no)
						->where('principle_member', $clhmn->principle_member)
						->where('dependant', $clhmn->dependant)
						->get()->pluck('claim_no')
						->toArray();

			$total_amount = ClaimSchedule::whereIn('claim_no', $claims)
								->where('staff_no', $section)
								->sum('amount');
		}

		$limit = 0;
		if (is_numeric(str_replace(',', '',$getprosched->coverage))) {
			$limit = str_replace(',', '',$getprosched->coverage);
		}else{
			$plan = $member->plan_code;
			$limit = MediPlan::where('plan_code',$plan)->first()->plan_limit;
		}

		$remaining_limit = $limit-$total_amount;

		if ($remaining_limit < $reserve_amount) {
			$res = ['status'=>0, 'balance' => $remaining_limit];
		} else {
			$res = ['status'=>1, 'balance' => $remaining_limit];
		}
		

		return $res;
	}

	
	public function edit_schedule_med_item(Request $request){
		DB::beginTransaction();
		try {
			$claim_no = $request->claim_no;
			$member = $request->member;
			$section = $request->section;
			$clhmn = Clhmn::where('claim_no', $claim_no)->first();

			$getprosched = ClaimSchedule::where('claim_no', $claim_no)
							->where('member_number',$member)
							->where('staff_no',$section)
							->where('corr_type',$request->corr)
							->update([
								'amount'=>str_replace(',', '',$request->amount)
							]);
	
			$tot_amt = ClaimSchedule::where('claim_no', $claim_no)
							->where('peril',$request->peril)
							->where('perilsitem',$request->perilsitem)
							->where('corr_type',$request->corr)
							->sum('amount');
	
			$prosched = ClaimSchedule::where('claim_no', $claim_no)
							->where('corr_type',$request->corr)
							->where('peril',$request->peril)
							->where('perilsitem',$request->perilsitem)
							->first()->toArray();

			$insert_estimate = $this->estimate_insert($prosched,$tot_amt);

			return redirect()->back()->with('success','Update Successfull');
		} catch (\Throwable $th) {
			DB::rollback();

			return redirect()->back()->with('error','Update Failed');
		}
	}

	

	public function getscheduleperilsitems(Request $request){

		$peril = $request->get('peril');
		$perilitem = $request->get('perilitem');
		$corr_type = $request->get('corr_type');
		$peril_items = Perilsitems::Where('peril','=',$peril)
			->where('link_w_schedule','=','Y')
			->whereRaw("trim(corrs_type)='" . $corr_type . "'")
		->orderBy('item_name', 'ASC')->get();
		echo $peril_items;
	}


	public function estimate_insert($all_prosched,$tot_amt){
		##check if item exists in clestdtl
		$clestdtl_count = Clestdtl::where('claim_no',$all_prosched['claim_no'])->count();
		
		if($clestdtl_count < 1 ){

			##insert as original estimate

			$insert_original_est = $this->OSTestimate($all_prosched,$tot_amt);
		}else{
			## insert as revision estimate
			$insert_est = $this->ESTestimate($all_prosched,$tot_amt);
		}
	}

	public function OSTestimate($all_prosched,$tot_amt){

		$pipcnam = Pipcnam::all();
		$claim_no = removePolicyOrClaimFormat($all_prosched['claim_no']);

		$clhmnx = Clhmn::where('claim_no',$claim_no )->first();
		$class = $clhmnx->class;
		$policy_no = $clhmnx->policy_no;
		$new_claim = 'N';
		$update_clpmn = 'Y';
		$captured = ['claim_no'=>$claim_no];
		$documents_marker = 1;

		$peril = $all_prosched['peril'];
		$perilsitem = $all_prosched['perilsitem'];
		$corr_type = $all_prosched['corr_type'];

		$now_date = Carbon::today()->format('Y-m-d');
            
        $query = "SELECT account_year, account_month FROM period WHERE TO_DATE(:now_date, 'YYYY-MM-DD') BETWEEN period_from AND period_to";

					// Execute the query with the bound parameter
		$period_leo = DB::select($query, ['now_date' => $now_date]);

            $period_leo = $period_leo[0];
   
            // $year = $today->year;
           // $month = $today->month;
        $month=$period_leo->account_month;
        $year=$period_leo->account_year;	
			
		
			$perilitemsx=Perilsitems::where('peril',$peril)
									->where('item_no',$perilsitem)
									->get();
			$perilitemsx = $perilitemsx[0];

				
			$clhmnx = Clhmn::where('claim_no',$claim_no)->get();
			$clhmnx = $clhmnx[0];	
			$insert_clestdtl = new Clestdtl;
			$insert_clestdtl->claim_no = $claim_no;
			$insert_clestdtl->peril = $peril;
			$insert_clestdtl->perilitem = $perilsitem;
			$insert_clestdtl->corr_type =  $corr_type;
			$insert_clestdtl->benefit = 'N';
			$insert_clestdtl->effective_date = $this->todays_date;
			$insert_clestdtl->tran_no = $request->tran_no;
			$insert_clestdtl->orig_estimate =str_replace(',','', $tot_amt);
			$insert_clestdtl->curr_estimate = str_replace(',','',$tot_amt);
			$insert_clestdtl->doc_type = 'EST';
			$insert_clestdtl->indice = (int) '1';
			$insert_clestdtl->ln_no = (int) '1';
			$insert_clestdtl->reference = 'OST';
			$insert_clestdtl->currency_code = $clhmnx->currency_code;
			$insert_clestdtl->currency_rate = $clhmnx->currency_rate;
			$insert_clestdtl->local_curr_estimate =str_replace(',','', $tot_amt)* $clhmnx->currency_rate;
			$insert_clestdtl->local_orig_estimate =str_replace(',','', $tot_amt);
			$insert_clestdtl->payments_todate=$clhmnx->total_payment;

			$insert_clestdtl->save();
					
				## update clpmnperildtl
			$insert_clpmnperildtl = new Clpmnperildtl;
			$insert_clpmnperildtl->claim_no = $claim_no;
			$insert_clpmnperildtl->policy_no = $clhmnx->policy_no;
			$insert_clpmnperildtl->doc_type = 'EST';
			$insert_clpmnperildtl->entry_type_descr = 'OST';
			$insert_clpmnperildtl->pay_type = 30;
			$insert_clpmnperildtl->class = $clhmnx->class;
			$insert_clpmnperildtl->pay_amount =str_replace(',','', $tot_amt);
			$insert_clpmnperildtl->amount = str_replace(',','',$tot_amt);
			$insert_clpmnperildtl->pay_date = $this->todays_date;
			$insert_clpmnperildtl->dola = $this->todays_date;
			$insert_clpmnperildtl->payee = trim($perilitemsx->item_name).' ESTIMATE';
			$insert_clpmnperildtl->dtrans_no = $request->tran_no;
			$insert_clpmnperildtl->account_year = $year;
			$insert_clpmnperildtl->account_month = $month;
			$insert_clpmnperildtl->peril = $peril[$i];
			$insert_clpmnperildtl->user_str = $this->user_name;
			$insert_clpmnperildtl->pay_time = Carbon::now();
			$insert_clpmnperildtl->branch = $clhmnx->branch;
			$insert_clpmnperildtl->agent = $clhmnx->agent_no;
			$insert_clpmnperildtl->effective_date = $clhmnx->date_reg;
			$insert_clpmnperildtl->balance =str_replace(',','', $tot_amt)* $clhmnx->currency_rate;
			$insert_clpmnperildtl->payments_todate = $clhmnx->total_payment;
			$insert_clpmnperildtl->uw_year = $clhmnx->uw_year;
			$insert_clpmnperildtl->location =$clhmnx->location;
			$insert_clpmnperildtl->section = $clhmnx->section;
			$insert_clpmnperildtl->curr_total_estimate = $tot_amt* $clhmnx->currency_rate;
			$insert_clpmnperildtl->closing_balance = $tot_amt* $clhmnx->currency_rate;
			$insert_clpmnperildtl->perilitem = $perilsitem;
			$insert_clpmnperildtl->ln_no =(int) '1';
			$insert_clpmnperildtl->currency_code =$clhmnx->currency_code;
			$insert_clpmnperildtl->currency_rate = $clhmnx->currency_rate;
			$insert_clpmnperildtl->local_amount = $tot_amt* $clhmnx->currency_rate;
			$insert_clpmnperildtl->corr_type = $corr_type;

			$insert_clpmnperildtl->save();	


			$old_dtran_no = $clhmnx->tran_no;
			$this->reinsurance($captured,str_replace(',','',$tot_amt),$update_clpmn,$new_claim,$old_dtran_no);

			$update_clpmn = Clpmn::Where('claim_no','=',$claim_no)
							->update(['user_str'=>$this->user_name]);

			// update claim status
			$clmhistData = [
				'claim_no' => $claim_no,
				'slug' => 'claim-revision-made',
				'overide_status_desc' => 'Y',
				'additional_comment' => "CLAIM REVISION MADE. CURRENT ESTIMATE:". number_format($tot_amt)
			];
			ClaimProcessedEvent::dispatch((object)$clmhistData);

			$workflow = ClassModel::where('class',$class)->get();

			$this->add_claim_to_workflow($claim_no,$workflow[0]->claim_workflow_id,10);
		
			DB::commit();
	}

	public function ESTestimate($all_prosched,$tot_amt){

		$schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];


		$claim_no = removePolicyOrClaimFormat($all_prosched['claim_no']);

		$new_claim = 'N';
		$update_clpmn = 'N';
		$captured = ['claim_no' => $claim_no];
		
		$peril = $all_prosched['peril'];
		$perilsitem = $all_prosched['perilsitem'];
		$corr_type = $all_prosched['corr_type'];

		$clhmnx = Clhmn::where('claim_no',$claim_no )->first();
		$clestdetails = Clestdtl::where('claim_no',$claim_no)->get();

		$class = ClassModel::where('class', $clhmnx->class)->first();

		$now_date = Carbon::today()->format('Y-m-d');
            
        $query = "SELECT account_year, account_month FROM period WHERE TO_DATE(:now_date, 'YYYY-MM-DD') BETWEEN period_from AND period_to";

					// Execute the query with the bound parameter
		$period_leo = DB::select($query, ['now_date' => $now_date]);

            $period_leo = $period_leo[0];
   
            // $year = $today->year;
           // $month = $today->month;
        $month=$period_leo->account_month;
        $year=$period_leo->account_year;

			foreach($clestdetails as $clst ) {
	
				$correspondent_type= $clst->corr_type;

				if($this->checkIfExistsClestdl($claim_no,
						$peril,$perilsitem,$corr_type)==0){
						
						$insert_clestdtl = new Clestdtl;
						$insert_clestdtl->claim_no = $claim_no;
						$insert_clestdtl->peril = $peril;
						$insert_clestdtl->perilitem = $perilsitem;
						$insert_clestdtl->corr_type = $corr_type;
						$insert_clestdtl->benefit = 'N';
						$insert_clestdtl->effective_date = $this->todays_date;
						$insert_clestdtl->tran_no = $request->tran_no;
						$insert_clestdtl->orig_estimate = str_replace(',','',$tot_amt);
						$insert_clestdtl->curr_estimate = str_replace(',','',$tot_amt);
						$insert_clestdtl->doc_type = 'EST';
						$insert_clestdtl->indice = (int) '1';
						$insert_clestdtl->ln_no = (int) '1';
						$insert_clestdtl->reference = 'OST';
						$insert_clestdtl->currency_code = $clhmnx->currency_code;
						$insert_clestdtl->currency_rate = $clhmnx->currency_rate;
						$insert_clestdtl->local_orig_estimate =str_replace(',','', $tot_amt);
						$insert_clestdtl->local_curr_estimate = str_replace(',','',$tot_amt)* $clhmnx->currency_rate;
						$insert_clestdtl->payments_todate=0;
						$insert_clestdtl->save();

					}
					else{

						if(($clst->peril == $peril) && ($clst->corr_type == $corr_type ) 
							&& ($clst->perilitem == $perilsitem) ){
								$fin_curr_bal = $tot_amt;
						}else{

							$fin_curr_bal = $clst->curr_estimate;

						}
						
						$update_clestdtl = Clestdtl::Where([
							['claim_no', '=', $claim_no],
							['peril','=',$clst->peril],
							['perilitem','=',$clst->perilitem],
							['corr_type','=',$clst->corr_type]
						])
						->update([
							'effective_date' => $this->todays_date,
							'curr_estimate' => $tot_amt,
							'benefit' => $use_benefit,
							'doc_type' => 'EST',
							'indice' => (int) '1',
							'ln_no' => (int) '1',
							'reference' => 'EST'
						]);
						$get_clestdtl = Clestdtl::Where([
							['claim_no', '=', $claim_no],
							['peril','=',$clst->peril[$i]],
							['perilitem','=',$clst->perilitem],
							['corr_type','=',$clst->corr_type]
						])
						->get();
						
						$get_clestdtl=$get_clestdtl[0];
					}


					##update clmperildtl
					$insert_clpmnperildtl = new Clpmnperildtl;
					$insert_clpmnperildtl->claim_no = $claim_no;
					$insert_clpmnperildtl->policy_no = $clhmnx->policy_no;
					$insert_clpmnperildtl->doc_type = 'EST';
					$insert_clpmnperildtl->entry_type_descr = 'EST';
					$insert_clpmnperildtl->pay_type = 30;
					$insert_clpmnperildtl->class = $clhmnx->class;
					$insert_clpmnperildtl->pay_amount = $clst->curr_estimate;
					$insert_clpmnperildtl->amount = $clst->curr_estimate;
					$insert_clpmnperildtl->pay_date = $this->todays_date;
					$insert_clpmnperildtl->dola = $this->todays_date;
					$insert_clpmnperildtl->payee = trim($perilitemsx->item_name).' ESTIMATE';
					$insert_clpmnperildtl->dtrans_no = $clhmnx->tran_no;
					$insert_clpmnperildtl->account_year = $year;
					$insert_clpmnperildtl->account_month = $month;
					$insert_clpmnperildtl->peril =$clst->peril;
					$insert_clpmnperildtl->user_str = $this->user_name;
					$insert_clpmnperildtl->pay_time = Carbon::now();
					$insert_clpmnperildtl->branch = $clhmnx->branch;
					$insert_clpmnperildtl->agent = $clhmnx->agent_no;
					$insert_clpmnperildtl->effective_date = $clhmnx->date_reg;
					$insert_clpmnperildtl->balance = $clst->curr_estimate * $clhmnx->currency_rate;
					$insert_clpmnperildtl->payments_todate =str_replace(',','', $clhmnx->total_payment);
					$insert_clpmnperildtl->uw_year = $clhmnx->uw_year;
					$insert_clpmnperildtl->location =$clhmnx->location;
					$insert_clpmnperildtl->section = $clhmnx->section;
					$insert_clpmnperildtl->curr_total_estimate = $clst->curr_estimate * $clhmnx->currency_rate;
					$insert_clpmnperildtl->closing_balance = $clst->curr_estimate * $clhmnx->currency_rate;
					$insert_clpmnperildtl->perilitem =  $clst->perilitem;
					$insert_clpmnperildtl->ln_no =(int) '1';
					$insert_clpmnperildtl->currency_code = $clhmnx->currency_code;
					$insert_clpmnperildtl->currency_rate = $clhmnx->currency_rate;
					$insert_clpmnperildtl->local_amount =  $clst->curr_estimate * $clhmnx->currency_rate;
					$insert_clpmnperildtl->corr_type = $clst->corr_type;
					$insert_clpmnperildtl->save();
			
			}


			
			$clestdtl_estimate = Clestdtl::where('claim_no',$claim_no)->sum('curr_estimate');

			$estimate = $clestdtl_estimate;
			$old_dtran_no = $clhmnx->tran_no;
			$revise_estimate = $this->reinsurance($captured,$estimate,$update_clpmn,$new_claim,$old_dtran_no);
			$clpmn_item = Clpmn::Where('claim_no','=',$claim_no)
								->orderBy('pay_time','desc')
								->first();

			//End Reinsurance Procedure
			$procedure_name = ''.$gb.'.update_clpmn';
			$bindings = [
				'new_claim_no' =>$clpmn_item->claim_no,
				'new_dtrans_no' =>$clpmn_item->dtrans_no,
				'new_g_user'=>$this->user_name
			];
			$resp = DB::executeProcedure($procedure_name,$bindings);

			$clmhistData = [
				'claim_no' => $claim_no,
				'slug' => 'claim-revision-made',
				'overide_status_desc' => 'N',
				'additional_comment' => "CLAIM REVISION MADE. CURRENT ESTIMATE:". number_format($clpmn_item->curr_total_estimate)
			
			];
			ClaimProcessedEvent::dispatch((object)$clmhistData);

			DB::commit();
	}

	public function xol_fac_debitnotes_dt(Request $request){
            
		$reinalloc = Clmreinalloc::where('claim_no',$request->claim_no)
					->orderBy('trans_date','Desc')
					->get();

		return Datatables::of($reinalloc)

			// ->editColumn('amount',function($reinalloc){
				
			// 	if($reinalloc->reinalloc < 0){
			// 		$amount = $reinalloc->reinalloc * -1;
			// 	}else{
			// 		$amount = $reinalloc->reinalloc;
			// 	}
			// 	return number_format($amount);

			// })
			->editColumn('trans_date',function($reinalloc){
				return formatDate($reinalloc->trans_date);
			})
			
			->editColumn('participants',function($reinalloc){
				$participant  = Crmast::where('branch',$reinalloc->branch)->where('agent',$reinalloc->agent)->first();
				return $participant->name;
			})
			
			->addColumn('print_debitnotes',function($reinalloc){
				if($reinalloc->doc_type == 'DRN'){

					return '<a href="/print_fac_xol_debits/'. encrypt($reinalloc->claim_no) .'/'. encrypt($reinalloc->entry_type_descr) .'/'. encrypt($reinalloc->dtrans_no) .'" target="blank">
					<i class="glyphicon glyphicon-print btn bg-info"
					></i></a>';

				}else{

					return '';
				}
			})
			
			->rawColumns(['print_debitnotes'])
			->make(true);
	}
    
	public function templimitsforapproval(Request $request){
		
		$category = LimitCategory::select('aims_limits.amount',"LISTAGG(group_limit_categories.group_id,',') AS GroupIds")
					->join('group_limit_categories',function($join){
						$join->on('limit_categories.id','=','group_limit_categories.limit_category_id');
					})
					->join('group_limits',function($join){
						$join->on('group_limit_categories.id','=','group_limits.group_limit_category_id');
					})
					->join('aims_limits',function($join){
						$join->on('group_limits.limit_id','=','aims_limits.id');
					})
					->where([
						'limit_categories.slug' => $request->limit_category,
						'group_limits.dept_id' => $request->class,
						['aims_limits.amount', '<=',(int)$request->amount],
					])
					->groupBy('aims_limits.amount')
					->orderBy('aims_limits.amount','desc')
					// ->pluck('group_limit_categories.group_id','aims_limits.amount')
					->first()
					;
		
		$groupIds = explode(',', $category->groupids);

        $users = Aimsuser::select('user_id','user_name','first_name','last_name','email','role_id')
						->whereIn('role_id',$groupIds)
                        ->Active()
                        ->ExceptSelf()
                        ->get()
						->toArray();

		return $users;

	}

	public function discharge_cancellation(Request $request){

		try {

			Clmdischarge::where('claim_no', $request->dv_cnc_claim_no)
			->where('order_number',$request->dv_cnc_orderno)->update([
				'cancelled'=>'Y',
				'cancelled_by'=>Auth::user()->user_name,
				'cancelled_date'=>Carbon::now()
			]);

			

			Session::flash('success','Cancelled Successfully');

		} catch (\Throwable $th) {
			
			Session::flash('error','Failed to Cancel');

		}

		return  redirect()->back();
	}

	public function updateclaimdata(Request $request){

		try {

			$losstype = Third_party_claims::where('claim_no',$request->claim_no)->count();

			if($losstype > 0 && $request->claim_loss_type == 1){

				Session::flash('error','You cannot Change type of Claim to Normal Claim as there is are existing third party details');
				return redirect()->back();
			}else{

				Clhmn::where('claim_no',$request->claim_no)->update([
					'type_of_claim' => $request->claim_loss_type
	
				]);
			}
			
			Session::flash('success','Details Updated Successfully');
			return redirect()->back();
			
		} catch (\Throwable $e) {

			$error_msg = json_encode($e->getMessage());
			$referrence = $request->claim_no;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);
			DB::rollback();

			Session::flash('error','Failed to save');
			return redirect()->back();
		}
	}

	public function check_existence_limit(Request $request){
		$schem = schemaName();
		$gb = $schem['gb'];
		$gl = $schem['gl'];
		$common = $schem['common'];

		$checkinperilitem = Perilsitems::where('peril',$request->peril)
		->where('item_no',$request->perilitem)
		->where('corrs_type',$request->corr_type)
		->first();

		$limitflag = $checkinperilitem->use_limit;
		$claim_no =removePolicyOrClaimFormat($request->claim_no);

		if($checkinperilitem->use_limit == 'N' || $checkinperilitem->use_limit == null ){
						
			return ([-1]);
		}else if($checkinperilitem->use_limit == 'S'){
			$clhmn = Clhmn::where('claim_no',$claim_no)->first();

			$clestdtl = Clestdtl::where('peril',$request->peril)
			->where('claim_no',$clhmn->claim_no)
			->first();
			$getperil = Perils::where('peril',$checkinperilitem->peril)->first();

			$checkperildtl = DB::select(" SELECT sum(nvl(c.curr_estimate,0)) as curr_estimate FROM PERILSITEMS a 
					LEFT JOIN CLESTDTL c ON c.PERIL = a.PERIL AND c.PERILITEM  = a.ITEM_NO 
					WHERE a.USE_LIMIT  = 'S'
					AND c.CLAIM_NO = '$clhmn->claim_no'"
			);
			
			$current_limit = ((int)$clhmn->sum_insured - (int)$checkperildtl[0]->curr_estimate);
			
			return ([$current_limit,'',$limitflag]);

		}else{
			
			$limits = Perils::where('peril',$request->peril)
			->where('link_limit','Y')
			->where('class', $request->class)
			->get();
				
			if(count($limits)== 0){
			
				return ([-1,$limits]);

			}else{

				$clhmn = Clhmn::where('claim_no',$claim_no)->first();

				$policy_no = $clhmn->policy_no;
				$period_from = $clhmn->pol_date1;
				$period_to = $clhmn->pol_date2;

				
				##get limit amount 
					if( $limits[0]->link_limit == 'Y'){
						# code...
						foreach($limits as $li){

							$lim = Autolimits::where('class',$clhmn->class)
											->where('limit_no',$li->limit_code)
											->first();
		
									
									## get latest endorsement number from dcontrol
							$endt = Dcontrol::where('policy_no',$policy_no)
											// ->where('endt_renewal_no', $clhmn->endt_renewal_no)
												->where('period_from' ,$period_from) 
												->where('period_to' ,$period_to)
												->where('effective_date','<=',$clhmn->acc_date )
								->where('period_to','>=',$clhmn->acc_date)
												->orderBy('dcon_no', 'desc')
												->first();
							
							if($endt->trans_type == 'INS'){
		
								## cases where there is an INS type 
								$endorsement = Dcontrol::where('dcontrol.policy_no',$policy_no)
								->join('debitmast', function($join){
									$join->on('dcontrol.endt_renewal_no', '=', 'debitmast.endt_renewal_no');
								})
								->where(function($query){
										$query->where('dcontrol.trans_type','POL')
										->orWhere('dcontrol.trans_type','EXT')
										->orWhere('dcontrol.trans_type','REN')
										->orWhere('dcontrol.trans_type','NIL');
								})
								->where('dcontrol.effective_date','<=',$clhmn->acc_date )
								->where('dcontrol.period_to','>=',$clhmn->acc_date)
								// ->where('cov_period_from' ,$period_from) 
								// ->where('cov_period_to' ,$period_to)
								// ->max('endt_renewal_no');
								->orderBy('dcontrol.dcon_no', 'desc')
								->get();

								$endorsement = $endorsement[0]->endt_renewal_no;
								
					
							}else{
		
								$endorsement = Dcontrol::where('dcontrol.policy_no',$policy_no)
								->join('debitmast', function($join){
									$join->on('dcontrol.endt_renewal_no', '=', 'debitmast.endt_renewal_no');
								})
								->where(function($query){
										$query->where('dcontrol.trans_type','POL')
										->orWhere('dcontrol.trans_type','EXT')
										->orWhere('dcontrol.trans_type','NIL')
										->orWhere('dcontrol.trans_type','REN');
								})
								->where('dcontrol.effective_date','<=',$clhmn->acc_date )
								->where('dcontrol.period_to','>=',$clhmn->acc_date)
								// ->where('period_from' ,$period_from) 
								// ->where('period_to' ,$period_to)
								->orderBy('dcontrol.dcon_no', 'desc')
								
								->get();


								$endorsement = $endorsement[0]->endt_renewal_no;

							}

							$limit_count = Pollimits::where('endt_renewal_no',$endorsement)
							->where('limit_no',$lim->limit_no)
							->count();

							if($limit_count>0){
							##get latest limits from pollimits within the policy cover period
							$limit = Pollimits::where('endt_renewal_no',$endorsement)
							->where('limit_no',$lim->limit_no)
							->first();

							}
							else{
								$limit = Pollimits::where('endt_renewal_no',$clhmn->endt_renewal_no)
							->where('limit_no',$lim->limit_no)
							->first();
							}

						
							
							
							##get limit type
							$type = Limit_types::where('type_id',$lim->limit_types)->first();
							// dd($endorsement);

							// dd($lim,$type,$lim->limit_type);

							##get cost to date of the policy
							try {  
						
								##use oracle function to get the limit amount based on type of limit 
								$w_acc_date = $clhmn->acc_date;
								$w_policy_no = $clhmn->policy_no;
								$w_claim_no = $clhmn->claim_no;
								$w_location= $clhmn->location;
								$w_reg_no =$clhmn->reg_no;
								$w_limit_id =$lim->limit_no;
								$w_limit_amount =$limit->amount;
								$w_type_of_limit =$type->type_id;
								$w_uw_year =$clhmn->uw_year;
								$w_corr_type =$request->corr_type;
								$w_peril =$request->peril;
								$w_peril_item =$request->perilitem;


								// dd($w_acc_date,$w_policy_no,$w_claim_no
								// ,$w_location,$w_reg_no,$w_limit_id,$w_limit_amount,$w_type_of_limit,$w_uw_year,
								// $w_corr_type,$w_peril,$w_peril_item);

								$result = DB::selectOne("select compute_limits_func('$w_acc_date','$w_policy_no','$w_claim_no'
								,'$w_location','$w_reg_no','$w_limit_id','$w_limit_amount','$w_type_of_limit','$w_uw_year',
								'$w_corr_type','$w_peril','$w_peril_item') as value from dual");
								
								$limitamount1 =  $result->value;
								$limit_no = $lim->limit_no;
								$limitflag = $limitflag;
								

								// dd($limitamount1,$limit_no,$limitflag);
		
							}catch (\Throwable $e) {
									
								$codex = json_encode($e->getMessage());
								$error = explode('\n', $codex);
								$error_msg = $error[1];
								$referrence = $clhmn->claim_no;
								$route_name = Route::getCurrentRoute()->getActionName();
								log_error_details($route_name,$error_msg,$referrence);
		
								Session::flash('error','Failed');
		
							}

						}
					}
					if( $limits[0]->link_limit_2 == 'Y'){
						# code...
						foreach($limits as $li){

							$lim2 = Autolimits::where('class',$clhmn->class)
							->where('limit_no',$li->limit_code_2)
							->first();

									## get latest endorsement number from dcontrol
							$endt = Dcontrol::where('policy_no',$policy_no)
											
												->where('period_from' ,$period_from) 
												->where('period_to' ,$period_to)
												->orderBy('dcon_no', 'desc')
												->first();
							
							if($endt->trans_type == 'INS'){

								## cases where there is an INS type 
								$endorsement = Dcontrol::where('policy_no',$policy_no)
								->where(function($query){
										$query->where('trans_type','POL')
										->orWhere('trans_type','EXT')
										->orWhere('trans_type','NIL')
										;
								})
								->where('effective_date','<=',$clhmn->acc_date ) 
								->where('period_to','>=',$clhmn->acc_date)
								// ->where('period_from' ,$period_from) 
								// ->where('period_to' ,$period_to)
								->orderBy('dcon_no', 'desc')
								->get();


								$endorsement = $endorsement[0]->endt_renewal_no;
							}else{

								
								$endorsement = Dcontrol::where('policy_no',$policy_no)
								->where(function($query){
										$query->where('trans_type','POL')
										->orWhere('trans_type','EXT')
										->orWhere('trans_type','NIL')
										->orWhere('trans_type','REN');
								})
								->where('effective_date','<=',$clhmn->acc_date )
								->where('period_to','>=',$clhmn->acc_date)
								// ->where('period_from' ,$period_from) 
								// ->where('period_to' ,$period_to)
								->orderBy('dcon_no', 'desc')
								->get();

								$endorsement = $endorsement[0]->endt_renewal_no;
							}

							##get latest limits from pollimits within the policy cover period
							$limit = Pollimits::where('endt_renewal_no',$endorsement)
												->where('limit_no',$lim2->limit_no)
												->first();
							
							##get limit type
							$type = Limit_types::where('type_id',$lim2->limit_types)->first();
							
							##get cost to date of the policy
							try {  

								##use oracle function to get the limit amount based on type of limit 
								$w_acc_date2 = $clhmn->acc_date;
								$w_policy_no2 = $clhmn->policy_no;
								$w_claim_no2 = $clhmn->claim_no;
								$w_location2= $clhmn->location;
								$w_reg_no2 =$clhmn->reg_no;
								$w_limit_id2 =$lim->limit_no;
								$w_limit_amount2 =$limit->amount;
								$w_type_of_limit2 =$type->type_id;
								$w_uw_year2 =$clhmn->uw_year;
								$w_corr_type2 =$request->corr_type;
								$w_peril2 =$request->peril;
								$w_peril_item2 =$request->perilitem;
								
								$result2 = DB::selectOne("select compute_limits_func('$w_acc_date2','$w_policy_no2','$w_claim_no2'
								,'$w_location2','$w_reg_no2','$w_limit_id2','$w_limit_amount2','$w_type_of_limit2','$w_uw_year2',
								'$w_corr_type2','$w_peril2','$w_peril_item2') as value from dual");

								$limitamount2= (int)$result2->value;

							}catch (\Throwable $e) {
									
								$codex = json_encode($e->getMessage());
								$error = explode('\n', $codex);
								$error_msg = $error[1];
								$referrence = $clhmn->claim_no;
								$route_name = Route::getCurrentRoute()->getActionName();
								log_error_details($route_name,$error_msg,$referrence);

								Session::flash('error','Failed');
								
							}
				
						}

					}

					if($limitamount2 !== null ){
							
						if( $limitamount2 >= $limitamount1 ){

							return([$limitamount1,$lim->limit_no,$limitflag,$diff]);
	
						}else if((int)$limitamount1 > (int)$limitamount2 ){

							return([$limitamount2,$lim->limit_no,$limitflag,$diff]);

						}

					}else{
							
						return([$limitamount1,$lim->limit_no,$limitflag,$diff]);

					}
			}
			
		}
			
	}

	public function insertlimits($claim_no){

		$clhmn = Clhmn::where('claim_no',$claim_no)->first();

		$policy_no = $clhmn->policy_no;
		$period_from = $clhmn->pol_date1;
		$period_to = $clhmn->pol_date2;

		##get latest endorsement within the policy period
			
		$endt = Dcontrol::where('policy_no',$policy_no)					
				->where('period_from' ,$period_from) 
				->where('period_to' ,$period_to)
				->orderBy('dtrans_no', 'desc')
				->first();

		if($endt->trans_type == 'INS'){

			## cases where there is an INS type 
			// $endorsement = Dcontrol::where('policy_no',$policy_no)
			// ->where(function($query){
			// 		$query->where('trans_type','POL')
			// 		->orWhere('trans_type','EXT')
			// 		->orWhere('trans_type','NIL');
			// })
			// ->where('cov_period_from' ,$period_from) 
			// ->where('cov_period_to' ,$period_to)
			// ->max('endt_renewal_no');
			
			$endorsement = Dcontrol::where('policy_no',$policy_no)
			->where(function($query){
					$query->where('trans_type','POL')
					->orWhere('trans_type','EXT')
					->orWhere('trans_type','NIL')
					;
			})
			->where('effective_date','<=',$clhmn->acc_date ) 
			// ->where('period_from' ,$period_from) 
			// ->where('period_to' ,$period_to)
			->orderBy('dtrans_no', 'desc')
			
			->get();


			$endorsement = $endorsement[0]->endt_renewal_no;

		}else{

			// $endorsement = Dcontrol::where('policy_no',$policy_no)
			// ->where(function($query){
			// 		$query->where('trans_type','POL')
			// 		->orWhere('trans_type','EXT')
			// 		->orWhere('trans_type','NIL');
			// })
			// ->where('period_from' ,$period_from) 
			// ->where('period_to' ,$period_to)
			// ->orderBy('dtrans_no', 'desc')
			// ->max('endt_renewal_no');
			
			$endorsement = Dcontrol::where('policy_no',$policy_no)
			->where(function($query){
					$query->where('trans_type','POL')
					->orWhere('trans_type','EXT')
					->orWhere('trans_type','NIL')
					->orWhere('trans_type','REN');
			})
			->where('effective_date','<=',$clhmn->acc_date ) 
			// ->where('period_from' ,$period_from) 
			// ->where('period_to' ,$period_to)
			->orderBy('dtrans_no', 'desc')			
			->get();

			$endorsement = $endorsement[0]->endt_renewal_no;
			
		}

		$pollimits = Pollimits::where('policy_no',$policy_no)
					->where('endt_renewal_no',$endorsement)
					->get();
		
		try {
			//code...

			foreach($pollimits as $pol){
					
				$climit = new Claim_limits;
				$climit->claim_no = $claim_no;
				$climit->limit_no = $pol->limit_no;
				$climit->description = trim($pol->detail_line);
				$climit->orig_amount = $pol->amount;
				$climit->policy_no =$clhmn->policy_no ;
				$climit->claim_year =$clhmn->claim_year ;
				$climit->uw_year =$clhmn->uw_year ;
				$climit->acc_date =$clhmn->acc_date ;
				$climit->dola=$this->todays_date_time;
				$climit->save();

			}

		} catch (\Throwable $e) {
			
			$codex = json_encode($e->getMessage());
			$error = explode('\n', $codex);
			$error_msg = $error[1];
			$referrence = $claim_no;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);

			Session::flash('error','Failed');
		}

		
		
	}

	public function updateclaimlimit($claim_no,$corrtype,$peril,$perilitem,$perilitemsx,$limit_no,$limitamount,$current_amount,$prev_estimate){

		try {
			if($perilitemsx->use_limit == 'Y' && ((int)($prev_estimate) != (int) $current_amount)  ){

				$saved_amount = ((int)$limitamount+(int)($prev_estimate)) - (int) $current_amount;


				##update claim limit
				$claim_limit = Claim_limits::where('claim_no',$claim_no)
									->where('limit_no',$limit_no)
									->update([
										'os_amount'=>$saved_amount,
										'dola'=>$this->todays_date_time
									]);
				
			if($saved_amount == 0){

				$clhmn = Clhmn::where('claim_no',$claim_no)->first();
				$getlimit = Claim_limits::where('claim_no',$claim_no)
									->where('limit_no',$limit_no)
									->first();
				$policy_no = formatPolicyOrClaim($clhmn->policy_no);
				$claim_no = formatPolicyOrClaim($claim_no);
				$clients = Polmaster::where('policy_no',$policy_no)->first();
				$desc  = trim($getlimit->description);
				$category = 'BENEFIT UTILIZATION';
				$mess2 = "Dear Team, Policy  '$policy_no' with claim no '$claim_no' has fully utilized benefit ('$desc'). Please advice client to reinstate.";
				$getbranch = $requirement->branch;
				$getmail = Department_emails::where('branch',$clhmn->branch)->where('dept','U/W')->first();
								
				## send mail to UW department
				$mess = $mess2;
				$name = trim(Auth::user()->user_name);
				$emailaddr = trim($getmail->email);
				$this->sendmail($category,$emailaddr,$mess,$name);	
			}							
		}
		} catch (\Throwable $e) {
				
			$codex = json_encode($e->getMessage());
			$error = explode('\n', $codex);
			$error_msg = $error[1];
			$referrence = $clhmn->claim_no;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);

			Session::flash('error','Failed to update limits');
		}
	}



	public function check_if_escalation_exists(Request $request){

		$check_exist = Escalate_pol::where('claim_no',$request->claim_no)
					->where('escalate_type',$request->esc_level)
					->where('type',$request->type)
					->where('read',null)
					->orderBy('created_at','desc')
					->first();
		
		if($check_exist == null){
			return 1;

		}else{

			return $check_exist;

		}
	
	}

	public function getescalation_grp (Request $request){

		$user_id = Auth::user()->user_id;
		$UserGroupLimitCheckerService = new UserGroupLimitCheckerService();

		$type = 'approve-claim-re-opening';

		$get_esc = $UserGroupLimitCheckerService
		->setPermission($type)
		->fetchUsersWithPermission();

		return $get_esc; 

	}
	

	public function escalate4approval (Request $request){

		$esc_user = $request->escalate_doc;
		$claim_no = $request->claim_no;
		$esc_types = $request->esc_types;
		$esc_level = $request->esc_level;
		$esc_btn = $request->Escalate;

		// dd($request->all());

		if($esc_user != null){

			##re-escalation
			if($esc_btn == 'Re_escalate' ){

				$escalate_pol = Escalate_pol::where('type',trim($esc_types))
											->where('claim_no',$claim_no)
											->where('escalate_type',$esc_level)
											->where('read',null)
											->update([
													're_escalate_date'=>Carbon::now()
											]);

			}  

			$clstatus = Clstatus::where('status_code',$request->reopen)->first();

			##update clhmn with temporary status
			if($request ->esc_types = 'CLF'){

				$updatestatus = Clhmn::where('claim_no',$claim_no)->update([
					'revive_status_temp'=> $request->reopen
				]);
			}
		

			$user_id = Auth::user()->user_id;
			$desription = Escalation_types::where('type', trim($esc_types))->first();
			$count = Escalate_pol::where('sent_by',$user_id)->max('escalate_id');
			$next = $count + 1;
			$recieverdet = Aimsuser_web::where('user_id',$esc_user)->first();

			$reciever = trim($recieverdet->user_name);
			$sent_id = trim($recieverdet->user_id);
			$emailaddr  = $recieverdet->email;

			$escalate = new Escalate_pol ;
			$escalate->escalate_id = $next;
			$escalate->type = $esc_types;
			$escalate->escalate_type = $esc_level;
			$escalate->description = $desription->description.' APPROVAL';
			$mess = "Kindly Approve the request with claim number '$claim_no' reason for re-opening claim:  '$clstatus->description'.Thank You. ";
			$category = $desription->description.' APPROVAL' ;
			
			$escalate->claim_no =$claim_no;
			$escalate->sent_to =$sent_id;
			$escalate->sent_by =$user_id;
			$escalate->user_name = $reciever;
			$escalate->created_at =  Carbon::now();
			$escalate->save();

			$this->sendemailfunction($category,$mess,$emailaddr);
			Session::flash('Success','Request sent Successfully');
			return redirect()->back();

		}else{
			
			Session::flash('Error','no user indicated to escalate to');
			return redirect()->back();
		}
	
	}

	public function sendemailfunction($category,$mess,$emailadd){
				
		$sendemail = new Sendemail;
		$sendemail->category = $category ;
		$sendemail->receiver =$emailaddr;
		$sendemail->message =$mess;
		$sendemail->creator = Auth::user()->user_name;

		try {
		$sendemail->save();

		} catch (\Throwable $e) {
			$codex = json_encode($e->getMessage());
			$error = explode('\n', $codex);
			$error_msg = $error[1];
			$referrence = $emailaddr;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);

			Session::flash('Error','Failed to send email');
		}

	}

	public function approve_request(Request $request){


		$claim_no = $request->claim_no;

	

		if($request->approve == true){

			try{

				## re-opening claim 
				if($request->sctype == 'CLF'){
					
					$status = 'N';
				
					$escalatepol = Escalate_pol::where('claim_no', $claim_no)
									->where('escalate_type',10)	
									->where('type',trim($request->sctype))
									->where('re_escalate_date','=',null)
									->update([
											'approved'=>'Y',
											'approved_by'=>Auth::user()->user_id,
											'approved_date'=>Carbon::now(),
											'read'=>1,
									]);

					$this->re_openclaim($claim_no);
					Session::flash("success",'Completed Successfully');
					return redirect()->back();
				}
				## close claim 
				else if($request->sctype == 'CLS'){
					
					$escalatepol = Escalate_pol::where('claim_no', $claim_no)
									->where('escalate_type',10)	
									->where('type',trim($request->sctype))
									->where('re_escalate_date','=',null)
									->update([
											'approved'=>'Y',
											'approved_by'=>Auth::user()->user_id,
											'approved_date'=>Carbon::now(),
											'read'=>1,

									]);

					Session::flash("success",'Completed Successfully');
					return redirect()->back();
				}
				## liability admission 
				else if($request->sctype == 'LBA'){

					$escalatepol = Escalate_pol::where('claim_no', $claim_no)
					->where('escalate_type',10)	
					->where('type',trim($request->sctype))
					->where('re_escalate_date','=',null)
					->update([
							'approved'=>'Y',
							'approved_by'=>Auth::user()->user_id,
							'approved_date'=>Carbon::now(),
							'read'=>1,

					]);
					Session::flash("success",'Completed Successfully');
					return redirect()->back();
					
				}
				## outstanding premium
				else if($request->sctype == 'OPR'){

					$escalatepol = Escalate_pol::where('claim_no', $claim_no)
					->where('escalate_type',10)	
					->where('type',trim($request->sctype))
					->where('re_escalate_date','=',null)
					->update([
							'approved'=>'Y',
							'approved_by'=>Auth::user()->user_id,
							'approved_date'=>Carbon::now(),
							'read'=>1,
					]);

					Session::flash("success",'Completed Successfully');
					return redirect()->back();
					
				}

			}catch (\Throwable $e) {
				
				$codex = json_encode($e->getMessage());
				$error = explode('\n', $codex);
				$error_msg = $error[1];
				$referrence = $claim_no;
				$route_name = Route::getCurrentRoute()->getActionName();
				log_error_details($route_name,$error_msg,$referrence);

				DB::rollback();
				Session::flash('Error','Failed to Approve');

				return redirect()->back();

			}
		}
		else{

			$escalatepol = Escalate_pol::where('claim_no', $claim_no)
			->where('escalate_type',10)	
			->where('type',trim($request->sctype))
			->where('re_escalate_date','=',null)
			->update([
					'approved'=>'X',
					'approved_by'=>Auth::user()->user_id,
					'approved_date'=>Carbon::now(),
					'read'=>1,
			]);
			Session::flash("success",'Completed Successfully');

			return redirect()->back();
		}

	}


	public function re_openclaim( $claim_no){

		$clhmn = Clhmn::Where('claim_no', '=', $claim_no)->first();
		

		$update_clhmn = Clhmn::Where('claim_no', '=', $claim_no)
		->update([
			'revived' => 'Y',
			'revived_period' => $this->getDtran0()->uw_period,
			'revived_date' => $this->todays_date_time,
			'closed' => '',
			##change rejected status if previously rejected
			'rejected'=>'N',
			'rejected_by'=>'',

			//Set attributes to null when claim file is reopened.
			'closed_by' => '',
			'closed_date' => '',
			'closed_year' => '',
			'closed_month' => '',
		]);

		$clstatus = Clstatus::where('status_code',$clhmn->revive_status_temp)->first();

		// update claim status
		$clmhistData = [
			'claim_no' => $claim_no,
			'slug' => 'claim-file-re-opened',
			'overide_status_desc' => 'Y',
			'additional_comment' => $clstatus->status_description,
		];
		ClaimProcessedEvent::dispatch((object)$clmhistData);



	}

	public function show_fnote_comments(Request $request){

		$comments = Fnote_comments::where('claim_no',$request->claim_no)
					->where('order_no',$request->order_no)
					->orderBy('created_at','desc')
					->get();
		
		return Datatables::of($comments)
		->editColumn('created_at',function($comments){
			return formatDate($comments->created_at);
			})
		->make(true);

	}

	public function filenote_comments(Request $request){

		try {

			$comments = New Fnote_comments;
			$comments->order_no = $request->order_no;
			$comments->claim_no = $request->claim_no;
			$comments->comments = $request->remarks;
			$comments->created_by = Auth::user()->user_name;
			$comments->updated_by =Auth::user()->user_name;
			$comments->save();

			return 1;
		} catch (\Throwable $e) {
			return 0;
		}
		

	}

	public function get_insured_details(Request $request){

		$client_details = Client::where('client_number',$request->client_num)->first();

		if(empty($client_details)){

			$client_details = Clparam::where('claimant_code',$request->client_num)->first();

		}
		return $client_details;

	}

	public function settle_os_premium(Request $request){

		$insured = Clmcorr::where('code',$request->rec_ind)
							->where('insured','Y')
							->count();
		$unallocated = 0;

		if($insured > 0){

			$clhmn = Clhmn::where('claim_no',$request->claim_no)->first();
			##check for OS amount Acdet

			$unallocated = Acdet::where('client_number',$clhmn->client_number)
						->where('source','U/W')
						->sum('unallocated');
								

		}

		return $unallocated; 
	}

	public function fetchclaimantdetails(Request $request){

		$details = DB::Select(" 
			SELECT c.CLAIMANT_CODE ,c.CLAIMANT_NAME,p.PERIL_DESCRIPTION,d.ITEM_NAME,e.NAME ,c.EMAIL,c.TELEPHONE,c.SP_REFERENCE,
			c.EXP_PRE_REPORT pre_report,c.EXP_FINAL_REPORT fin_report,c.CREATED_BY,c.DOLA ,c.CANCELLED,c.CANCELLED_BY,c.STATUS,c.CLAIM_AMOUNT 
			FROM claimant c
			JOIN PERILS p ON p.peril = c.PERIL 
			JOIN PERILSITEMS d ON d.PERIL  = c.PERIL AND d.ITEM_NO  = c.PERILITEM 
			JOIN clmcorr e ON e.CODE  = c.REC_IND 
			WHERE c.CLAIM_NO  = '$request->claim_no' 
			AND c.CLAIMANT_CODE  = '$request->claimant_code'
			AND c.LINE_NO  = '$request->line_no'
		");

		return $details[0];
		
	}

	public function getmarinedetails( Request $request)
	{

		$policy_no = removePolicyOrClaimFormat($request->get('policy_no'));
		$endt_no = $request->get('endorse');
		$class = $request->get('class');
		$location = $request->get('location');
		$accdate = $request->get('acc_date');

		##getmasterpolicydcontrol
		$masterpol = Dcontrol::where('endt_renewal_no',$endt_no)->pluck('master_policy')->first();
		
		$opentype = Marinemasterhist::where('endt_renewal_no',$endt_no)->pluck('opentype_code')->first();

		if($opentype == 'UTL'){

			// dd($request->all(),$accdate,$masterpol);

			$location = DB::select("select location from marine_utl_debits where endt_renewal_no in
				(select endt_renewal_no from dcontrol where master_policy = '$masterpol'
				and effective_date <= TO_DATE('$accdate','YYYY-MM-DD'))  and (TO_DATE('$accdate','YYYY-MM-DD')
				between period_from and period_to) 
				group by location
			");

		}else{

			$getendorsements = Dcontrol::where('master_policy',$masterpol)->select('endt_renewal_no')->get();
 
			$location = DB::select("select location from madtl where endt_renewal_no in 
				(select endt_renewal_no from dcontrol where master_policy = '$masterpol'
				and effective_date <= TO_DATE('$accdate','YYYY-MM-DD'))  and (TO_DATE('$accdate','YYYY-MM-DD')
				between period_from and period_to) 
				group by location
			");

		}
		
		
		$results = array();

		foreach($location as $loc){

			$madtldetails = DB::select("select location,sum_insured,type_of_pack, case
			 when commercial_invoice is not null then commercial_invoice
			 when PROFORMA_INVOICE is not null then PROFORMA_INVOICE
			 else PROFORMA_INVOICE end as serial_no
				 from madtl where policy_no = '$masterpol'
				and location = '$loc->location' and rownum = 1 order by proposal_date desc 
			");

			// dd($madtldetails[0],$loc->location);
			array_push($results, $madtldetails[0]);

			
		}
	
		return $results;

	}
	public function claimsRecommendations(Request $request)
	{ 
		$validatedData = $request->validate([
			'title' => 'required',
			'notes' => 'required',
			'source' => 'required',
			'slug' => 'required',
			'reconote_type' => 'required'
		]);

		DB::beginTransaction();

		try {
			$recoType= RecommendationTypes::where('source',$request->source)
			->where('reco_type', $request->reconote_type)
			->first();
			if ($recoType->assignment_required == 'Y' && $recoType->addressed_to == null) {
				DB::rollback();
				return response()->json(['error' => 'Recommendation type parameter Addressed to not set'], 404);
			} else {
				$note_id = (int) Policy_notes::max('note_id') + 1;
				
				Policy_notes::create([
					'note_id' => $note_id,
					'policy_no' => $request->input('policy_no'),
					'reference_no' => $request->input('reference'),
					'title' => $validatedData['title'],
					'notes' => $validatedData['notes'],
					'source' => $validatedData['source'],
					'status' => 'OPEN',
					'created_by' => auth()->user()->user_name,
					'slug' => Str::slug(strtolower($validatedData['slug'])),
					'addressed_to' => $recoType->addressed_to,
					'reconote_type'=> $validatedData['reconote_type']
				]);
				
				
				if ($recoType->assignment_required == 'Y' && $recoType->addressed_to != null) {
					$current_user = Auth::user()->name;
					$reco_addressedTo_name = User::where('user_name', $recoType->addressed_to)->value('name');
					$reco_addressedTo_email = User::where('user_name', $recoType->addressed_to)->value('email');
					$category = 'RECOMMENDATIONS';

					$notificationData = [
						'reco_raiser'=> $current_user,
						'addressed_to_email' => $reco_addressedTo_email,
                        'reco_addressedTo_name' => $reco_addressedTo_name,
						'category' => $category,
						'policy_no'=> $request->policy_no,
						'reference_no'=> $request->reference
					];
					DispatchNotificationEvent::dispatch($slug = 'raise-recommendation',$notificationData);
				}
				DB::commit();
				return response()->json(['success' => 'Recommendation/Note submitted successfully']);
			}
		}catch (\Exception $e) {
			DB::rollback();
			return response()->json(['error' => 'Failed to submit Recommendation/Note'], 500);
		}
	}

	
	public function getlatest_excess($clhmn){

		$acc_date = formatDateRFC3339($clhmn->acc_date);
		$endorse_no = DB::Select("
		select endt_renewal_no from dcontrol 
        where policy_no = '$clhmn->policy_no' and trans_type = 'NIL'
		and effective_date <= TO_DATE('$acc_date','YYYY-MM-DD')  and (TO_DATE('$acc_date','YYYY-MM-DD')
		 between period_from and period_to) and ROWNUM =1 order by dcon_no desc ");

			
		if(count($endorse_no)> 0){

			$count = Polexcess::where('policy_no', $clhmn->policy_no)
							->where('endt_renewal_no',$endorse_no[0]->endt_renewal_no)
							->where('class',$clhmn->class)
							->count();
			if ($count > 0){

				$excesses = Polexcess::where('policy_no', $clhmn->policy_no)
							->where('endt_renewal_no',$endorse_no[0]->endt_renewal_no)
							->where('class',$clhmn->class)
							->get();
			}else{
				$excesses = Polexcess::where('policy_no', $clhmn->policy_no)
							->where('endt_renewal_no',$clhmn->endt_renewal_no)
							->where('class',$clhmn->class)
							->get();
			}
		}else{
			$excesses = Polexcess::where('policy_no', $clhmn->policy_no)
						->where('endt_renewal_no',$clhmn->endt_renewal_no)
						->where('class',$clhmn->class)
						->get();
		}
		
		return $excesses;
	}

	public function showRecommendations(Request $request)
{
   
    if ($request->policy_no == $request->reference) {
        $recommendations = DB::table('POLICY_NOTES')
            ->select('title', 'status', 'created_at', 'notes', 'note_id', 'addressed_to', 'assigned_to', 'created_by', 'source', 'policy_no', 'reference_no', 'slug', 'reconote_type', 'date_assigned', 'cancelled_by', 'commented_by')
            ->where('policy_no', $request->policy_no)
            ->where(function ($query) use ($request) {
                $query->whereNotNull('addressed_to')
                    ->orWhere('source', $request->source);
            })
            ->orderBy('created_at', 'DESC')
            ->get();
    } else {
        $recommendations = DB::table('POLICY_NOTES')
            ->select('title', 'status', 'created_at', 'notes', 'note_id', 'addressed_to', 'assigned_to', 'created_by', 'source', 'policy_no', 'reference_no', 'slug', 'reconote_type', 'date_assigned', 'cancelled_by', 'commented_by')
            ->where('reference_no', $request->reference)
            ->where('source', $request->source)
            ->orderBy('created_at', 'DESC')
            ->get();
    }


    function getStatusStyle($status)
    {
        switch (strtolower(trim($status))) {
            case 'closed':
                return ['background-color' => 'grey', 'color' => 'white'];
            case 'open':
                return ['background-color' => 'green', 'color' => 'white'];
            case 'assigned':
                return ['background-color' => 'yellow', 'color' => 'black'];
            case 'cancelled':
                return ['background-color' => 'red', 'color' => 'white'];
            default:
                return ['background-color' => 'lightgrey', 'color' => 'black'];
        }
    }


    $recommendations = $recommendations->map(function ($recommendation) {
        $userName = trim(Auth::user()->user_name);
        $recotype = RecommendationTypes::where('source', $recommendation->source)
            ->where('reco_type', trim($recommendation->reconote_type))
            ->first();

        $addressedTo = trim($recotype->addressed_to);

        $action_permission = DB::table('permissions')
            ->select('id', 'name', 'slug')
            ->where('id', $recotype->assignee_permission)
            ->first();

    
        $statusStyle = getStatusStyle($recommendation->status);
        $statusStyleInline = 'background-color: ' . $statusStyle['background-color'] . '; color: ' . $statusStyle['color'] . '; padding: 0.1em 0.5em; border-radius: 1em;';

        return [
            'title' => $recommendation->title,
            'status' => $recommendation->status,
            'created_at' => formatDate($recommendation->created_at),
            'notes' => strip_tags($recommendation->notes),
            'note_id' => $recommendation->note_id,
            'created_by' => $recommendation->created_by,
            'source' => $recommendation->source,
            'policy_no' => $recommendation->policy_no,
            'reference_no' => $recommendation->reference_no,
            'slug' => $recommendation->slug,
            'addressed_to' => $addressedTo,
            'reconote_type' => $recommendation->reconote_type,
            'assigned_to' => $recommendation->assigned_to,
            'date_assigned' => $recommendation->date_assigned,
            'cancelled_by' => $recommendation->cancelled_by,
            'commented_by' => $recommendation->commented_by,
			'details' => 'Source: <strong style="font-size: 0.9em;">' . $recommendation->source . '</strong><br>Status: <span style="' . $statusStyleInline . '; font-size: 0.8em;">' . strtolower($recommendation->status) . '</span>',
            'permissions' => [
                'raise_recommendation_permission' => Auth::user()->user_name == $recommendation->created_by ? Gate::check('raise-recommendation') : false,
                'action_on_recommendation_permission' => Auth::user()->user_id == $recommendation->assigned_to
                    ? ($action_permission ? Gate::check(trim($action_permission->slug)) : false)
                    : false,
                'addressed_to' => $userName == $addressedTo,
            ]
        ];
    });

 
    return DataTables::of($recommendations)
        ->addColumn('action', function ($row) {
            $buttons = '<button class="btn btn-xs btn-primary view-btn" style="margin-right: 5px;"><i class="fa fa-eye"></i> View</button>';

            $permissions = $row['permissions'];
            $status = $row['status'];

            if ($status !== 'cancelled' && $status !== 'closed') {
                if ($permissions['raise_recommendation_permission'] && $permissions['addressed_to'] && $permissions['action_on_recommendation_permission']) {
                    $buttons .= '<button class="btn btn-xs btn-success update-btn" style="margin-right: 5px;"><i class="fa fa-pencil"></i> Edit</button>';
                    $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                    $buttons .= $status === 'assigned'
                        ? '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Re-assign</button>'
                        : '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Assign</button>';
                    $buttons .= '<button class="btn btn-xs btn-info action-btn" style="margin-right: 5px;"><i class="fa fa-check"></i> Action</button>';
                } elseif ($permissions['raise_recommendation_permission'] && $permissions['addressed_to']) {
                    $buttons .= '<button class="btn btn-xs btn-success update-btn" style="margin-right: 5px;"><i class="fa fa-pencil"></i> Edit</button>';
                    $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                    $buttons .= $status === 'assigned'
                        ? '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Re-assign</button>'
                        : '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Assign</button>';
                } elseif ($permissions['raise_recommendation_permission'] && $permissions['action_on_recommendation_permission']) {
                    $buttons .= '<button class="btn btn-xs btn-success update-btn" style="margin-right: 5px;"><i class="fa fa-pencil"></i> Edit</button>';
                    $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                    $buttons .= $status === 'assigned'
                        ? '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Re-assign</button>'
                        : '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Assign</button>';
                    $buttons .= '<button class="btn btn-xs btn-info action-btn" style="margin-right: 5px;"><i class="fa fa-check"></i> Action</button>';
                } elseif ($permissions['raise_recommendation_permission']) {
                    $buttons .= '<button class="btn btn-xs btn-success update-btn" style="margin-right: 5px;"><i class="fa fa-pencil"></i> Edit</button>';
                    $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                } elseif ($permissions['addressed_to'] && $permissions['action_on_recommendation_permission']) {
                    $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                    $buttons .= $status === 'assigned'
                        ? '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Re-assign</button>'
                        : '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Assign</button>';
                    $buttons .= '<button class="btn btn-xs btn-info action-btn" style="margin-right: 5px;"><i class="fa fa-check"></i> Action</button>';
                } elseif ($permissions['addressed_to']) {
                    $buttons .= '<button class="btn btn-xs btn-danger cancel-btn" style="margin-right: 5px;"><i class="fa fa-times"></i> Cancel</button>';
                    $buttons .= $status === 'assigned'
                        ? '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Re-assign</button>'
                        : '<button class="btn btn-xs btn-secondary assign-btn" style="margin-right: 5px;"><i class="fa fa-user"></i> Assign</button>';
                } elseif ($permissions['action_on_recommendation_permission']) {
                    $buttons .= '<button class="btn btn-xs btn-info action-btn" style="margin-right: 5px;"><i class="fa fa-check"></i> Action</button>';
                }
            }

            return $buttons;
        })
        ->escapeColumns([])
        ->make(true);
}
	public function cancelRecommendation(Request $request)
    { 
		$request->validate([
			'cancellationreason' => 'required',
		]);
	
		DB::beginTransaction();
	
		try {
			$user_name = trim(Auth::user()->user_name);
	
			
			DB::table('POLICY_NOTES')
				->where('reference_no', $request->recom_ref)
				->where('policy_no', $request->reco_pol_no)
				->where('source', $request->reco_source)
				->where('note_id', $request->reco_note_id)
				->update([
					'status' => 'cancelled',
					'cancellation_reason' => $request->cancellationreason,
					'cancelled_by' => $user_name,
					'cancelled_date' => now()->toDateString()
					
				]);
	
		       
			$recommendation = Policy_notes::where('reference_no', $request->recom_ref)
				->where('policy_no', $request->reco_pol_no)
				->where('source', $request->reco_source)
				->where('note_id', $request->reco_note_id)
				->first();
				
			if (!$recommendation) {
				throw new \Exception('Recommendation not found.');
			}
	
			$recoRaise_email = User::where('user_name', $recommendation->created_by)->value('email');
			$category = 'CANCEL RECOMMENDATION';
			$recoRaiser_name = User::where('user_name', $recommendation->created_by)->value('first_name');
     
			$notificationData = [
				'recoRaise_email'=> $recoRaise_email,
				'current_user' => $user_name,
				'recoRaiser_name' => $recoRaiser_name,
				'category' => $category,
				'cancellationreason' => $request->cancellationreason,
				'policy_no'=> $request->reco_pol_no,
				'reference_no'=> $request->recom_ref
			];
	
			DispatchNotificationEvent::dispatch($slug = 'cancel-recommendation', $notificationData);
			DB::commit();
			return response()->json(['status' => 1, 'message' => 'Recommendation Cancelled successfully']);
	
		} catch (\Exception $e) {
			
			DB::rollback();
			return response()->json(['status' => 0, 'error' => ' Failed to cancel.'], 500);
		}
   }
 
	function claim_payment_table(Request $request){

		$payments = Cbmast::where('claim_no',$request->claim_no)
		->where('source_code','CLM')
		->where('doc_type','PAY')
		->where('entry_type_descr','CLP')
		->orderBy('dtrans_no','DESC')
		->orderBy('account_year','DESC');

		return Datatables::of($payments)
		->editColumn('nett_amount',function($payments){
		return number_format($payments->nett_amount,2);
		})
		->addColumn('reference',function($payments){
			$reference = str_pad($payments->dtrans_no,6,'0',STR_PAD_LEFT).$payments->account_year;
			// return $reference;
			return formatReference($reference);
		})
		->addColumn('requisition',function($payments){
			$reference = $payments->dept_code.$payments->payrqst_no;
			// return $reference;
			return formatRequisitionNo($reference);
		})
		->addColumn('pay_method',function($payments){
				$pay_method = Olpaymethd::where('pay_method',$payments->pay_method)->first();

			return $pay_method->description;
		})
		->addColumn('acc_period',function($payments){
			
			$acc_period = $payments->account_year. '/'.$payments->account_month;
			return $acc_period;
		})

		->make(true); 
	}
	function UpdateCreateClestIRA($claim_no,$peril,$doc_type,$dtrans_no,$ira_docstatus=null,$irastatus_code=null,$ira_comment_code=null){
		// dd($claim_no,$peril,$ira_docstatus,$irastatus_code,$ira_comment_code);
		switch ($doc_type) {
			case 'EST':
			case 'OST':
				$clest = Clest::where('claim_no',$claim_no)
								->where('peril', $peril)
								->first();
			
				$curr_estimate = Clestdtl::where('claim_no',$claim_no)->where('peril',$peril)->sum('curr_estimate');
				$curr_estimate = $curr_estimate ? $curr_estimate : 0;
				$ira_comment = IRAcomments::where('irastatus',$irastatus_code)->where('status_code',$ira_comment_code)->first();
				if ($clest) {
					$updclest =  Clest::where('claim_no',$claim_no)
						->where('peril', $peril)->update([
						'curr_estimate' => $curr_estimate,
						'est_date' => Carbon::now(),
						'user_str' => trim(Auth::user()->user_name),
						'doc_status' => $ira_docstatus,
						'curr_status_code' => $irastatus_code,
						'status_date' => Carbon::now(),
						'indice' =>0,
						'status_comment_code' => $ira_comment_code,
						'status_comment' => $ira_comment->comment_name,
					]);
				} else {
					// Create a new record
					$newclest = Clest::create([
						'claim_no' => $claim_no,
						'peril' => $peril,
						'orig_estimate' => $curr_estimate,
						'curr_estimate' => $curr_estimate,
						'est_date' => Carbon::now(),
						'user_str' => trim(Auth::user()->user_name),
						'doc_status' => $ira_docstatus,
						'curr_status_code' => $irastatus_code,
						'status_date' => Carbon::now(),
						'indice' =>0,
						'status_comment_code' => $ira_comment_code,
						'status_comment' => $ira_comment->comment_name,
					]);
				}
				break;
			
			case 'PAY':
				$clests = Clest::where('claim_no',$claim_no)->get();
				foreach ($clests as $key => $clest) {
					$curr_estimate = Clestdtl::where('claim_no',$clest->claim_no)->where('peril',$clest->peril)->sum('curr_estimate');

					$updclest = Clest::where('claim_no',$clest->claim_no)
										->where('peril', $clest->peril)->update([
											'curr_estimate' =>$curr_estimate,
											]);
				}
				
				break;
			default:
				# code...
				break;
		}

		$clpmnperilModel = Clpmnperil::where('claim_no',$claim_no)
						->where('peril', $peril)
						->where('doc_type', $doc_type)
						->where('dtrans_no', $dtrans_no);
		$clhmn = Clhmn::where('claim_no',$claim_no)->first();

		$now_date = Carbon::today()->format('Y-m-d');
            
            $query = "SELECT account_year, account_month FROM period WHERE TO_DATE(:now_date, 'YYYY-MM-DD') BETWEEN period_from AND period_to";

					// Execute the query with the bound parameter
		$period_leo = DB::select($query, ['now_date' => $now_date]);

            $period_leo = $period_leo[0];
   
            // $year = $today->year;
           // $month = $today->month;
           $month=$period_leo->account_month;
           $year=$period_leo->account_year;
						
		if($clpmnperilModel->exists())
		{
			Clpmnperil::where('claim_no',$claim_no)
				->where('peril', $peril)
				->where('doc_type', $doc_type)
				->where('dtrans_no', $dtrans_no)
				->update([
					'pay_amount' => $curr_estimate,
					'account_year' => $year,
					'account_month' => $month,
					'doc_status' => $ira_docstatus,
					'curr_status_code' => $irastatus_code,
					'status_date' => Carbon::now(),
					'status_comment_code' => $ira_comment_code,
					'status_comment' => $ira_comment->comment_name,
					'curr_total_estimate' => $curr_estimate,
					'local_curr_estimate' => 	$curr_estimate,

				]);
		}
		else 
		{
			$clpmnperil = new Clpmnperil();
			$clpmnperil->policy_no = $clhmn->policy_no;
			$clpmnperil->claim_no = $claim_no;
			$clpmnperil->peril = $peril;
			$clpmnperil->doc_type = $doc_type;
			$clpmnperil->dtrans_no = $dtrans_no;
			$clpmnperil->pay_amount = $curr_estimate;
			$clpmnperil->account_year = $year;
			$clpmnperil->account_month = $month;
			$clpmnperil->doc_status = $ira_docstatus;
			$clpmnperil->curr_status_code = $irastatus_code;
			$clpmnperil->status_date = Carbon::now();
			$clpmnperil->status_comment_code = $ira_comment_code;
			$clpmnperil->status_comment = $ira_comment->comment_name;
			$clpmnperil->class = $chlmn->class;
			$clpmnperil->branch = $chlmn->branch;
			$clpmnperil->agent = $chlmn->agent_no ;
			$clpmnperil->dola =  Carbon::now();
			$clpmnperil->uw_year = $chlmn->uw_year ;
			$clpmnperil->location = $chlmn->location;
			$clpmnperil->section =  $chlmn->section;
			$clpmnperil->ln_no = 1 ;
			$clpmnperil->curr_total_estimate = $curr_estimate;
			$clpmnperil->local_curr_estimate = 	$curr_estimate;	
			$clpmnperil->save();
		}
	}
	
public function updateRecommendationNotes(Request $request) {

    $validatedData = $request->validate([
        'title2' => 'required',
        'update_notes_1' => 'required',
        'note_id' => 'required',
		'reference_no' => 'required',
		'policy_no' => 'required',
		'source' => 'required',
    ]);

    try {
		DB::beginTransaction();
	
		$recommendation = DB::table('POLICY_NOTES')
			->where('note_id', $validatedData['note_id'])
			->first();
	
		if ($recommendation && $recommendation->status == 'cancelled') {
			DB::rollback();
			return response()->json(0);
		}
		DB::table('POLICY_NOTES')
			->where('note_id', $validatedData['note_id'])
			->where('reference_no', $validatedData['reference_no'])
			->where('policy_no', $validatedData['policy_no'])
			->where('source', $validatedData['source'])
			->update([
				'title' => $validatedData['title2'],
				'notes' => $validatedData['update_notes_1'],
			]);
	
		DB::commit();
		return response()->json(1); 
	
	} catch (\Exception $e) {
		DB::rollback();
		return response()->json(0); 
	}
}
   
public function viewRecommendationStatus(Request $request)
{
   
    $recommendations = Policy_notes::where('note_id', $request->note_id)
        ->where('reference_no', $request->reference_no)
        ->where('policy_no', $request->policy_no)
        ->where('source', $request->source)
        ->get();

   
    if ($recommendations->isEmpty()) {
        return response()->json(0);
    }

    $recommendations = $recommendations->map(function ($recommendation) {
      
        $addressedTo = Aimsuser::where('user_name', trim($recommendation->addressed_to))->value('name');
        $createdBy = Aimsuser::where('user_name', trim($recommendation->created_by))->value('name');
        $assignedTo = Aimsuser::where('user_id', trim($recommendation->assigned_to))->value('name');
        $cancelledBy = Aimsuser::where('user_name', trim($recommendation->cancelled_by))->value('name');
        $commentedBy = Aimsuser::where('user_name', trim($recommendation->commented_by))->value('name');
        $updated_by = Aimsuser::where('user_name', trim($recommendation->updated_by))->value('name');
     
        return [
            'title' => $recommendation->title,
            'status' => $recommendation->status,
            'created_at' => formatDate($recommendation->created_at),
            'notes' => strip_tags($recommendation->notes),
            'note_id' => $recommendation->note_id,
            'created_by' => $createdBy, 
            'source' => $recommendation->source,
            'policy_no' => $recommendation->policy_no,
            'reference_no' => $recommendation->reference_no,
            'slug' => $recommendation->slug,
            'addressed_to' => $addressedTo,
            'reconote_type' => $recommendation->reconote_type,
            'assigned_to' => $assignedTo, 
            'date_assigned' => $recommendation->date_assigned,
            'cancelled_by' => $cancelledBy,
			'cancellation_reason'=>$recommendation->cancellation_reason,
			'cancelled_date'=>$recommendation->cancelled_date, 
			'comments'=> $recommendation->comments,
			'comment_date'=> $recommendation->comment_date,
			'updated_by'=> $updated_by ,
			'updated_at'=> formatDate($recommendation->updated_at),
            'commented_by' => $commentedBy  
        ];
    });

    return response()->json($recommendations);
}
public function update_driver_police_det(Request $request) {
	
	$validatedData = $request->validate([
		'driver_name' => 'required',
		'driver_gender' => 'nullable',
		'driver_age' => 'nullable',
		'license_no' => 'nullable',
		'identity' => 'nullable',
		'other_vehicle_reg'=> 'nullable',
		'identity_no' => 'nullable',
		'driver_dob' => 'nullable|date',
		'name_of_police' => 'nullable',
		'contact_police' => 'nullable',
		'police_station' => 'nullable',
		'police_code' => 'nullable',
		'no_of_persons_involved' => 'nullable|integer',
		'claim_no' => 'required',
	]);

	try {
		DB::beginTransaction();
		$updateData = [
			'driver_name' => $validatedData['driver_name'],
			'driver_gender' => $validatedData['driver_gender'],
			'driver_age' => $validatedData['driver_age'],
			'identity' => $validatedData['identity'] ,
			'license_no' => $validatedData['license_no'],
			'other_vehicle_reg' => $validatedData['other_vehicle_reg'] ,
			'identity_no' => $validatedData['identity_no'],
			'driver_dob' => $validatedData['driver_dob'],
			'name_of_police' => $validatedData['name_of_police'],
			'contact_police' => $validatedData['contact_police'],
			'police_code'=>$validatedData['police_code'],
			'police_station' => $validatedData['police_station'],
			'no_of_persons_involved' => $validatedData['no_of_persons_involved'],
		];

		DB::table('clhmn')
			->where('claim_no', $validatedData['claim_no'])
			->update($updateData);

		DB::commit();

		return redirect()->back()->with('success', 'Driver & Police details updated successfully');

	} catch (\Exception $e) {
		DB::rollback();
		return redirect()->back()->with('error', 'Failed to update Driver & Police details: ' . $e->getMessage());
	}
}



	public function check_active_certificate(Request $request){
		
		$clhmn = Clhmn::where('claim_no',$request->claim_no)->first();
		##get polmaster period to 
		$polmaster = Polmaster::where('policy_no',$clhmn->policy_no)->first();

		##active certs after loss date
		$certNo = DB::table('certalloc')
				->select('cert_no')
				->WhereRaw("trim(reg_no) = '".$clhmn->reg_no."' ")
				->where('policy_no', $clhmn->policy_no)
				->whereBetween('period_to', [$clhmn->acc_date, $polmaster->cov_period_to])
				->where('cert_status', '<>', 99)
				->first();

		if(empty($certNo)){
			$certNo = 0;
		}
		
		return $certNo;
		
	}
	public function declineReqstn(Request $request)
{  
    $request->validate([
        'req_no' => 'required',
        'declinereason' => 'required',
    ]);

    DB::beginTransaction();

    try {
      
        Payreqst::where('req_no', $request->req_no)->update([
            'declined' => 'Y',
            'declined_by' => Auth::user()->user_name,
            'declined_date' => now()->toDateString(),
            'dola' => now()->toDateString(),
            'escalate_id' => '10',
            'declinereason' => $request->declinereason,
        ]);

       
        $payreqst = Payreqst::where('req_no', $request->req_no)->first();

        if (!$payreqst) {
            throw new \Exception('Payreqst not found.');
        }

       
        $reqRaise_email = User::where('user_name', $payreqst->created_by)->value('email');
        $reqRaiser_name = User::where('user_name', $payreqst->created_by)->value('first_name');

        if (!$reqRaise_email || !$reqRaiser_name) {
            throw new \Exception('User details not found.');
        }

       
        $category = 'DECLINE REQUISITION';
        $sendingDate = Carbon::now()->toDateString();
        $message = 'The requisition ' . $payreqst->req_no . ' has been declined due to ' . $payreqst->declinereason . '. The same will be escalated for actions.';
       
		$notificationData = [
			'reqRaise_email'=> $reqRaise_email,
			'declined_by' => Auth::user()->user_name,
			'reqRaiser_name' => $reqRaiser_name,
			'category' => $category,
			'declinereason' =>  $request->declinereason,
			'req_no'=> $request->req_no
			
		];

		DispatchNotificationEvent::dispatch($slug = 'decline-requisition', $notificationData);

        DB::commit(); 

        return response()->json(['status' => 1, 'message' => 'Requisition declined successfully']);

    } catch (\Exception $e) {
        DB::rollBack();
        return response()->json(['status' => 0, 'error' => 'Failed to decline Requisition. ' . $e->getMessage()], 500);
    }
}
	public function reqDeclinedDetails(Request $request)
    {  

        $req_no = $request->get('req_no');
        $req_details = Payreqst::where('req_no', $req_no)->get();
        return response()->json($req_details);
    }

		public function escalateReqstn(Request $request)
	{
        
		DB::beginTransaction(); 

		try {
			$name = Auth::user()->user_name;
			$sender_id = Auth::user()->user_id;

			$count = Escalate_pol::where('sent_by', $sender_id)->max('escalate_id');
			$next = $count + 1;
			$user_id = $request->user_id;
			$recieverdet = User::where('user_id', $user_id)->first();

			if (!$recieverdet) {
				throw new \Exception('Receiver not found'); 
			}

			$reciever = trim($recieverdet->user_name);
			$sent_id = trim($recieverdet->user_id);
			$req_num = $request->req_no;

			$escalate = new Escalate_pol();
			$escalate->escalate_id = $next;
			
			if ($req_num !== null) {
				$escalate->req_no = $req_num;
				$escalate->type = 'REQ';
				$escalate->description = 'Kindly check on declined requisition Number.' . $req_num . ' and action on the same...';

				Payreqst::where('req_no', $req_num)->update([
					'escalated_to' => $user_id,
					'declined' => 'N'
				]);

				$escalate->sent_by = $name;
				$escalate->claim_no = $request->claim_no;
				$escalate->sent_to = $sent_id;
				$escalate->sent_by = $sender_id;
				$escalate->user_name = $reciever;
				$escalate->created_at = Carbon::now();
				$escalate->save();



				$category = 'ESCALATE REQUISITION';
				$reciever_email = trim($recieverdet->email);
				$message = 'Kindly check on declined requisition Number.' . $req_num . 'The same has been escalated to you for checking...';
			

				$notificationData = [
					'message'=> $message,
					'category' => $category,
					'reciever_email' => $reciever_email,
					'req_num' => $req_num,
				];
				DispatchNotificationEvent::dispatch($slug = 'escalate-requisition',$notificationData);

			}
			DB::commit(); 

			return response()->json(['status' => 1, 'message' => 'Requisition Escalated successfully']);

		} catch (\Exception $e) {
			dd($e);
			DB::rollBack(); 
			return response()->json(['status' => 0, 'error' => 'Failed to Escalate Requisition.'], 500);
		}
	}

	public function check_cover_period($accidentdate,$polmaster){

		$carbonDate = Carbon::parse($accidentdate);

		##get claim loss year 
		$year = $carbonDate->year;
		##fetch from polmasterend the year before or equal to the claim year 
		$polmasterend = DB::select("Select * from dcontrol where policy_no = '$polmaster->policy_no' 
					and account_year <= '$year' 
					and trans_type in ('POL','REN','EXT','RNS')
					order by effective_date DESC fetch next 1 rows only");
		##confirm cover period between 
		if ($accidentdate > $polmasterend[0]->cov_period_to){

			##period to
			$period_to = Carbon::parse($polmasterend[0]->cov_period_to);
			$p = Carbon::parse($polmasterend[0]->cov_period_to);

			// $diffInMonths = $carbonDate->floatDiffInMonths($period_to);
			

			##get maintenance period months from polsectend
			$maintenance_period = Polsectend::where('endt_renewal_no',$polmasterend[0]->endt_renewal_no)
						->pluck('maintenance_period')
						->first();
			##check if cover is within the maintenance period
			##add period maintenance month
			$newDate = $p->addMonths((int)$maintenance_period);

			if($accidentdate > $newDate->toDateString()){
				$newDate = $accidentdate;
			}else{
				$newDate = $period_to->toDateString();
			}
		}else{
			$newDate = $accidentdate;
		}

		return $newDate;

	}

	
	public function assignRecommendation(Request $request)
	{ 
		DB::beginTransaction();

		try {
			$recommendation = DB::table('POLICY_NOTES')
				->where('reference_no', $request->reference_no)
				->where('policy_no', $request->policy_no)
				->where('source', $request->source)
				->where('note_id', $request->note_id)
				->first();

			if (!$recommendation) {
				throw new \Exception('Recommendation not found.');
			}

			$recoType = RecommendationTypes::where('source',$recommendation->source)
			->where('reco_type', $recommendation->reconote_type)
			->first();

			if (!$recoType) {
				throw new \Exception('Recommendation Type Parameter Not Set.');
			}

			if ($recoType->assignment_required == 'Y') {
				if ($recoType->addressed_to !== null) {
					DB::table('POLICY_NOTES')
						->where('reference_no', $request->reference_no)
						->where('policy_no', $request->policy_no)
						->where('source', $request->source)
						->where('note_id', $request->note_id)
						->update([
							'addressed_to' => $recoType->addressed_to,
							'assigned_to' => $request->assigned_to,
							'status' => 'assigned',
							'date_assigned'=> now()->toDateString()
						]);
					$assigner_name = User::where('user_name', $recoType->addressed_to)->value('name');
					$assigned_email = User::where('user_id', $request->assigned_to)->value('email');
					$category = 'ASSIGN RECOMMENDATION';
					$assigned_name = User::where('user_id', $request->assigned_to)->value('name');

					$notificationData = [
						'assigner_name'=> $assigner_name,
						'assigned_email' => $assigned_email,
                        'category' => $category,
						'assigned_name' => $assigned_name,
						'policy_no'=> $request->policy_no,
						'reference_no'=> $request->reference_no
					];
					DispatchNotificationEvent::dispatch($slug = 'assign-recommendation',$notificationData);
					Session::flash('success', 'Recommendation assigned successfully.');
				} else {
					Session::flash('error', 'Recommendation addressed to Parameter not set.');
					DB::rollback();
					return redirect()->back();
				}
			} else {
				Session::flash('info', 'Assignment not required.');
			}

			DB::commit();
			return redirect()->back();
		} catch (\Exception $e) {
			DB::rollback();
			Session::flash('error', 'Failed to Assign Recommendation: ' . $e->getMessage());
			return redirect()->back();
		}
	}
	public function recommendationAction(Request $request)
	{
		$request->validate([
			'comments' => 'required',
		]);
	
		DB::beginTransaction();
	
		try {
			$user_name = trim(Auth::user()->user_name);
	
			
			DB::table('POLICY_NOTES')
				->where('reference_no', $request->action_ref_no)
				->where('policy_no', $request->action_pol_no)
				->where('source', $request->action_src_no)
				->where('note_id', $request->note_id)
				->update([
					'status' => 'closed',
					'comments' => $request->comments,
					'comment_date' => now()->toDateString(),
					'commented_by' => $user_name
				]);
	
		
			$recommendation = Policy_notes::where('reference_no', $request->action_ref_no)
				->where('policy_no', $request->action_pol_no)
				->where('source', $request->action_src_no)
				->where('note_id', $request->note_id)
				->first();
	
			if (!$recommendation) {
				throw new \Exception('Recommendation not found.');
			}
	
		
			$recoRaise_email = User::where('user_name', $recommendation->created_by)->value('email');
			$category = 'RECOMMENDATION ACTION';
			$recoRaiser_name = User::where('user_name', $recommendation->created_by)->value('first_name');
            $addressed_toEmail=User::where('user_name', $recommendation->addressed_to)->value('email');

			$notificationData = [
				'recoRaise_email'=> $recoRaise_email,
				'addressed_toEmail' => $addressed_toEmail,
				'category' => $category,
				'recoRaiser_name' => $recoRaiser_name,
				'current_user'=>$user_name,
				'policy_no'=>  $request->action_pol_no,
				'reference_no'=>  $request->action_ref_no,
				'comments' => $request->comments
			];
			DispatchNotificationEvent::dispatch($slug = 'recommendation-action',$notificationData);
			DB::commit();
			return response()->json(['status' => 1, 'message' => 'Actioned successfully']);
	
		} catch (\Exception $e) {
			DB::rollback();
			return response()->json(['status' => 0, 'error' => 'Action Failed.'], 500);
		}
	}


		public function getUserData(Request $request)
	{  
		
		DB::beginTransaction();

		try {
			
			$validatedData = $request->validate([
				'reference_no' => 'required|string',
				'policy_no' => 'required|string',
				'source' => 'required|string',
				'note_id' => 'required|integer',
			]);

			
			$recommendation = Policy_notes::where('reference_no', $request->reference_no)
				->where('policy_no', $request->policy_no)
				->where('source', $request->source)
				->where('note_id', $request->note_id)
				->first();

			if (!$recommendation) {
				DB::rollBack(); 
				return response()->json(['error' => 'Recommendation not found'], 404);
			}

	
			$recotype = RecommendationTypes::where('source',$recommendation->source)
			->where('reco_type', $recommendation->reconote_type)
			->first();

			if (!$recotype) {
				DB::rollBack(); 
				return response()->json(['error' => 'Recommendation type Parameter not set'], 404);
			}

			$permission_id = $recotype->assignee_permission;
			
			if (!$permission_id) {
				DB::rollBack(); 
				return response()->json(['error' => 'No assignee permission set for this recommendation type'], 400);
			}

			
			$roles_with_permission = DB::table('permission_role')
				->where('permission_id', $permission_id)
				->pluck('role_id');

			if ($roles_with_permission->isEmpty()) {
				DB::rollBack();
				return response()->json(['error' => 'No roles found with the specified permission'], 404);
			}

		
			$users_with_permission = DB::table('aimsusers')
				->whereIn('role_id', $roles_with_permission)
				->get();

			if ($users_with_permission->isEmpty()) {
				DB::rollBack(); 
				return response()->json(['error' => 'No users found with the specified permission'], 404);
			}
		
			DB::commit(); 
		
			return response()->json(['users' => $users_with_permission]);

		} catch (ValidationException $e) {
			DB::rollBack();
			return response()->json(['error' => 'Validation failed', 'messages' => $e->errors()], 422);
		} catch (QueryException $e) {
			DB::rollBack(); 
			return response()->json(['error' => 'Database query error. Please try again later.'], 500);
		} catch (Exception $e) {
			DB::rollBack();
			return response()->json(['error' => 'An unexpected error occurred. Please try again later.'], 500);
		}
	}

	public function fetch_perils_class(Request $request){
		
		$perils = Perils::where('is_active', 'Y')->where('class',$request->class)->orderBy('peril_description', 'ASC')->get();
		return $perils;
		
	}


	public function claim_frequency($pol_no,$period_from,$period_to){
		
		$countx = DB::Select("select count(*) count from clhmn where policy_no = '$pol_no' and
			pol_date1 between to_date('$period_from','YYYY-MM-DD')
			and to_date('$period_to','YYYY-MM-DD')
		");

		return $countx[0];
	}

	public function check_change_sumin_ext($policy_no,$acc_date){

		$fetch_rec	 = DB::Select("SELECT d.ENDT_RENEWAL_NO,d.TRANS_TYPE ,
		LAG(d.TRANS_TYPE ,1) OVER (PARTITION BY d.policy_no ORDER BY d.period_from,d.period_to,d.dola  ) AS prev_entry_type,
		d.POLICY_NO,d.dola,d.EFFECTIVE_DATE,d.PERIOD_FROM,d.PERIOD_TO,
		(d.period_to-d.period_from) as ext_days,
		LAG(d.period_from,1) OVER (PARTITION BY d.policy_no ORDER BY d.period_from,d.period_to,d.dola  ) AS prev_period_from,
		LAG(d.period_to,1) OVER (PARTITION BY d.policy_no ORDER BY d.period_from,d.period_to,d.dola  ) AS prev_period_to,
		LAG(d2.sum_insured,1) OVER (PARTITION BY d.policy_no ORDER BY d.period_from,d.period_to,d.dola  ) AS prev_sum_insured,
		d2.SUM_INSURED,
		CASE WHEN d.period_from >= LAG(d.period_from,1) OVER (PARTITION BY d.policy_no ORDER BY d.period_from,d.period_to,d.dola )
			AND d.period_to <= LAG(d.period_to,1) OVER (PARTITION BY d.policy_no ORDER BY d.period_from,d.period_to,d.dola )
			AND d2.sum_insured <> LAG(d2.sum_insured,1) OVER (PARTITION BY d.policy_no ORDER BY d.period_from,d.period_to,d.dola )
			THEN 'Y' ELSE 'N' END Sum_insured_changed
		FROM DCONTROL d 
		JOIN DEBITMAST d2 ON d.ENDT_RENEWAL_NO = d2.ENDT_RENEWAL_NO 
		WHERE d.POLICY_NO  = '$policy_no'
		AND d.effective_date <= TO_DATE('$acc_date','YYYY-MM-DD HH24:MI:SS')
		and (TO_DATE('$acc_date','YYYY-MM-DD HH24:MI:SS')between d.period_from and d.period_to)
		ORDER BY d.DOLA DESC FETCH FIRST ROWS only");

		return $fetch_rec[0];
		
	}

	public function get_days_after_inception($endt_renewal_no,$acc_date){

		$effective_date = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();
		$effective_date = Carbon::parse($effective_date->effective_date);
		$num_days = $acc_date->diffInDays($effective_date);
		return $num_days; 

	}
	
	public function days_to_expiry($endt_renewal_no,$acc_date){

		$effective_date = Dcontrol::where('endt_renewal_no',$endt_renewal_no)->first();
		$cov_period_to = Carbon::parse($effective_date->cov_period_to);
		$days_to_expiry = $acc_date->diffInDays($cov_period_to);
		return $days_to_expiry; 

	}

	public function addClaimDFI($request,$claim_no){
		
		$acc_date = Carbon::parse($request['acc_date']);

		$errors = array();
		$count_errors = 0;
		$z = 0;
		## check claim frequency
		$pol_no = $request['policy_no'];
		$period_from = $request['period_from'];
		$period_to = $request['period_to'];

		$checkfrequency  = $this->claim_frequency($pol_no,$period_from,$period_to);
		if($checkfrequency->count > 0 ){
			$errors[$z++] = "Policy ".$pol_no." has ".$checkfrequency->count." claims within the same cover period";
		}

		$currency = Currency::where('currency_code',$request['currency_code'])->first();

		## check OS premium
		$unallocated_amount = Acdet::where('policy_no',$request['policy_no'])->where("source",'U/W')->sum('unallocated');
		if($unallocated_amount > 0 ){
			$errors[$z++] = " This Policy ".$request['policy_no']." has an Outstanding Premium
				 of ".$currency->currency." ".$unallocated_amount ;
		}

		##claim reported after issuance of cover
		$num_days = $this->get_days_after_inception($request['endt_renewal_no'],$acc_date);
        $new_parameter_details =DB::table('new_claims_parameters')->first();		

		if($num_days <= $new_parameter_details->notify_days_after_pol_issuance ){

			$errors[$z++] = "Policy attached to this claim was incepted ".$num_days." ago.";

		}
		
		$days_to_expiry = $this->days_to_expiry($request['endt_renewal_no'],$acc_date);
		if($days_to_expiry <= $new_parameter_details->notify_days_to_pol_expiry ){

			$errors[$z++] = " The Policy ".$pol_no." is due for renewal in ".$days_to_expiry." to policy expiration";
		}

		## insured first issuance
		// $issuance = Clhmn::where('client_number',$request['client_number'])
		// 			->where('claim_no','<>',$claim_no)
		// 			->count();
		// if($issuance == 0 ){
		// 	$errors[$z++] = "First issuance of Claim ";
		// }

		## existence of similar record of claim 
		$check_claim = Clhmn::Where('policy_no', $request['policy_no'])
						->where('location',$request['location'])
						->where('acc_date',$request['acc_date'])->count();

		if($check_claim >0 ){
			$errors[$z++] = "Multiple Claims with same Loss Details Registered under policy ".$request['policy_no'] ;
		}

		##check for change in SUm insured
		$checksum_insured = $this->check_change_sumin_ext($request['policy_no'],$request['acc_date']);

		$trans_types = ['POL','CNC','REN','MAC'];

		if (!in_array($checksum_insured->trans_type, $trans_types)) {

			if($checksum_insured->sum_insured_changed == 'Y'){

				$errors[$z++] = "Sum Insured of the policy on this claim changed from ".number_format($checksum_insured->prev_sum_insured)." 
				 to ".number_format($checksum_insured->sum_insured);

			}
			
			if($checksum_insured->ext_days <= $new_parameter_details->notify_days_after_pol_issuance){

				$days_after_ext = $acc_date->diffInDays($new_parameter_details->effective_date);

				$errors[$z++] = " The policy was extended ".$days_after_ext." 
				days ago" ;

			}
		}

		##check change of covertype motor policy
		if($request['motor_policy']=='Y'){
			$check_change_cover_type = $this->change_cov_type($request);
			if($check_change_cover_type->cover_changed == 'Y'){
				
				$errors[$z++] = " There was a change of cover for vehicle  ".$request["risk_item"]." 
				from".trim(check_change_cover_type->cover_description)." to ".$trim(cover_description->prev_cover_type) ;
			}
		}

		return $errors;		
	}


	public function check_change_cov_type(Request $request){

		$check_change = $this->change_cov_type($request);
		return $check_change;

	}
	public function change_cov_type($request){

		$pol_no = $request["policy_no"];
		$acc_date = $request["acc_date"];
		$reg_no = $request["risk_item"];


		$check_cover = DB::Select("SELECT m.REG_NO,c.COVER_DESCRIPTION,m.COVERTYPE,m.ENDT_RENEWAL_NO,
			LAG(c.COVER_DESCRIPTION, 1) OVER(PARTITION BY d.policy_no ORDER BY d.period_from, d.period_to, d.dola) AS prev_cover_type,
			CASE WHEN m.COVERTYPE IS NOT NULL AND m.COVERTYPE <> LAG(m.COVERTYPE, 1)
			OVER(PARTITION BY d.policy_no ORDER BY d.period_from, d.period_to, d.dola)
			THEN 'Y' ELSE 'N' END cover_changed
			FROM MODTLHIST m 
			LEFT JOIN DEBITMAST d ON d.ENDT_RENEWAL_NO = m.ENDT_RENEWAL_NO 
			JOIN COVERTYPE c ON m.COVERTYPE = c.COVER
			WHERE m.POLICY_NO = '$pol_no'
			AND d.effective_date <= TO_DATE('$acc_date', 'YYYY-MM-DD')
			AND TO_DATE('$acc_date', 'YYYY-MM-DD') BETWEEN d.period_from AND d.period_to
			AND m.REG_NO = '$reg_no' ORDER BY d.DOLA desc FETCH FIRST ROWS only"
		);
		return $check_cover[0];
}
	public function check_undebited_recovery(Request $request) {

		$exist_Rec = Clmrecoveries::where('claim_no', $request->claim_no)
			->where(function($query) {
				$query->where('debited', '<>', 'Y')
					  ->orWhereNull('debited');
			})->first();
	
		if ($exist_Rec === null) {
			return response()->json(0); 
		} else {
			return response()->json($exist_Rec);
		}
	}

	public function check_undebited_excess(Request $request){
		//dd($request->all());
		$exist_excess =  Clmexcess::where(DB::raw('TRIM(claim_number)'), $request->claim_no)
			->where(function($query) {
				$query->where('debited', '<>', 'Y')
					  ->orWhereNull('debited');
			})->first();
	
		if ($exist_excess === null) {
			return response()->json(0); 
		} else {
			return response()->json($exist_excess);
		}
		
	}

	public function check_claim_status(Request $request)
	{
		$claim_no = $request->claim_no;

	
		$claim_status = DB::table('clmhist as d')
			->join('clstatus as x', 'x.status_code', '=', 'd.status_code')
			->join('clhmn as c', 'c.claim_no', '=', 'd.claim_no')
			->where('d.claim_no', $claim_no)
			->orderBy('d.status_date', 'DESC')
			->get();

	
		$clestdtls = DB::select("
			SELECT a.*, b.peril_description, b.category as peril_categ, c.item_name 
			FROM clestdtl a
			LEFT JOIN perils b ON a.peril = b.peril
			LEFT JOIN Perilsitems c ON a.perilitem = c.item_no AND a.peril = c.peril
			WHERE claim_no = ? AND curr_estimate >= 0
			ORDER BY effective_date DESC
		", [$claim_no]);

		
		$claimants = DB::select("
			SELECT * FROM claimant a 
			JOIN CLMCORR b ON b.code = a.rec_ind 
			WHERE claim_no = ? 
			AND (cancelled IS NULL OR cancelled <> 'Y') 
			AND (a.status IS NULL OR a.status <> 1) 
			ORDER BY a.rec_ind DESC
		", [$claim_no]);

	
		return response()->json([
			
			'claimants' => $claimants,
			'claim_status' => $claim_status,
			'clestdtls' => $clestdtls
		]);
	}


	public function notify_client_claimprocess($claim_no, $process)
	{
		try {
			if ($process == 'Requisition Processing'|| $process == 'Payment Process') {

	
				$clhmn = Clhmn::where('claim_no', $claim_no)->first();
				$client = Client::where('client_number', $clhmn->client_number)->first();

				$intermediaryParams = new IntermediaryQueryParams([
					'agentNo' => $clhmn->agent_no,
					'additionalFields' => ['cell_phone', 'email']
				]);

				$agent = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
				$agent_email = $agent->email ?? null;
				$agent_cellphone = $agent->cell_phone ?? null;
				$client_email = $client->e_mail;
				$client_mobile = $client->mobile_no ?? null;

		
				$notificationData = [
					'process' => $process,
					'claim_no' => $clhmn->claim_no,
					'policy_no' => $clhmn->policy_no,
					'endt_renewal_no' => $clhmn->endt_renewal_no,
					'client_name' => $client->name,
					'agent_name' => $agent->name,
					'client_email' => $client_email,
					'client_mobile' => $client_mobile,
					'agent_mobile' => $agent_cellphone
				];

			
				if ($agent_email !== null) {
					$notificationData['agent_email'] = $agent_email;
				}

			
				DispatchNotificationEvent::dispatch($slug = 'notify-client-claim-process', $notificationData);

			}

		} catch (\Exception $e) {
	
	//dd($e);
		}
	}

	public function notify_intermediary_claimprocess($claim_no, $process)
	{

		try {
			if ($process == 'Requisition Processing' || $process == 'Payment Process') {

	
				$clhmn = Clhmn::where('claim_no', $claim_no)->first();
				$client = Client::where('client_number', $clhmn->client_number)->first();

				$intermediaryParams = new IntermediaryQueryParams([
					'agentNo' => $clhmn->agent_no,
					'additionalFields' => ['cell_phone', 'email']
				]);

				$agent = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
				$agent_email = $agent->email ?? null;
				$agent_cellphone = $agent->cell_phone ?? null;
				$client_email = $client->e_mail;
				$client_mobile = $client->mobile_no ?? null;

		
				$notificationData = [
					'process' => $process,
					'claim_no' => $clhmn->claim_no,
					'policy_no' => $clhmn->policy_no,
					'endt_renewal_no' => $clhmn->endt_renewal_no,
					'client_name' => $client->name,
					'agent_name' => $agent->name,
					'client_email' => $client_email,
					'client_mobile' => $client_mobile,
					'agent_mobile' => $agent_cellphone
				];

			
				if ($agent_email !== null) {
					$notificationData['agent_email'] = $agent_email;
				}


				DispatchNotificationEvent::dispatch($slug = 'notify-agent-claim-process', $notificationData);

			}

		} catch (\Exception $e) {
			//dd($e);
	
		}
	}

	public function addtpdetsummary() {

        $clhmns = DB::Select('select a.policy_no,a.policyholder,a.claim_no,a.reg_no,a.tp_driver_name,a.tp_driver_mobile,a.tp_driver_address,b.tp_reg_no,b.tp_name,b.id,
        b.tp_phone_no,b.tp_insurer,b.tp_cert_no, b.agent, b.branch,a.type_of_claim
         from third_party_claims b join clhmn a on a.claim_no = b.claim_no ');
        
          
        return response()->json($clhmns);

	}

	public function getnewclaimparams()
    {
		$data = DB::table('new_claims_parameters')->first();	
        return response()->json($data);
    }

        
}

