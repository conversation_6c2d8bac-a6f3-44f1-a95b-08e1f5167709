<?php

namespace App\Http\Controllers\gb\claims;

use DB;
use id;
use Session;
use App\Dtran0;
use App\Region;
use App\Clmcorr;
use App\Clparam;
use App\Nlslmst;
use App\Pipcnam;
use App\Regions;
use App\Currency;
use App\Nlparams;
use App\Olbranch;
use App\Payreqstd;
use App\SendEmail;
use Carbon\Carbon;
use App\Olbnknames;
use App\PartnerBank;
use App\Escalate_pol;
use App\Identity_type;
use App\Models\Aimsuser;
use App\Models\PaymentMode;
use App\Models\LocationLevel;
use App\Models\Location;
use App\Models\Claimant_log;
use Illuminate\Http\Request;
use App\Models\Spstatusaudit;
use App\Models\CompanyModule;
use App\Models\CustomerGroup;
use App\Events\AuditActionEvent;
use Yajra\Datatables\Datatables;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use App\Models\ServiceProviderInvoices;
use App\Models\ServiceProviderAllocations;
use App\Http\Requests\SaveServiceProviderInvoice;
use App\Traits\GenerateOnboardingNumber;

use App\Http\Controllers\gb\underwriting\IntergrationController;

class ServiceProvidersEnquiry extends Controller
{
	use GenerateOnboardingNumber;
	/*Define classwise Variables*/
    public $current_year;
    public $current_month;
    public $todays_date;
    public $todays_date_time;
    public $user_name;  

	public function __construct()
    {
        /* Get username */
         $this->middleware(function ($request, $next) {
            $this->user_name = trim(Auth::user()->user_name);

            return $next($request);
        });
        /*initialize variables into a class*/
        $this->current_year = Carbon::now()->format('Y');
        $this->current_month = Carbon::now()->format('m');
        $this->todays_date = Carbon::today();
        $this->todays_date_time = Carbon::now();
    }

	/*Helper methods*/
	public function getClparamCode($record_type)
	{
		$code = Clparam::Where('record_type','=',$record_type)
				->max('code');
		$curr_code = $code+1;
		return $curr_code;
				
	}
	public function genClaimantCode($record_type,$code)
	{
		$claimant_code = $record_type.str_pad((string)$code, 4,'0',STR_PAD_LEFT);
		return $claimant_code;
	}
	/*End of Helper methods*/
	public function show()
	{
		$clmcorrs = Clmcorr::All();
		$olbnknames = Olbnknames::All();
		$idtypes = Identity_type::all();
		$regions = Regions::all();
		$sp_approval = DB::table('new_claims_parameters')->pluck('sp_approval')->first();
		// dd($idtypes);
		return view('gb.claims.sproviders_enquiry')->with(compact('clmcorrs','olbnknames', 'idtypes','regions', 'sp_approval'));
	}


	public function serviceProvidersEnquiry(Request $request)
	{

		$schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
		$clparam = DB::table(''.$gb.'.clparam')  
				->join(''.$gb.'.clmcorr','clparam.record_type','=','clmcorr.code')
				->select('clmcorr.name as clmcorr_name','clparam.name as clparam_name','clparam.claimant_code',	 
							'clparam.status','clparam.erp_vendor_code','clparam.pin_number','clparam.mobile','clparam.approved','clparam.partnernumber')
				->orderBy('clparam.record_type');

		return Datatables::of($clparam)

			->addColumn('Action', function($clparam){
				$clparam->status == "ACTIVE" ? $action = "Deactivate" : $action = "Activate";
				$clparam->status == "ACTIVE" ? $icon = "times" : $icon = "check";

				if($clparam->approved == 'Y'){
					$activate_deactivate = '<span 
											class="btn btn-xs btn-danger deactivate"
											data-spcode='.$clparam->claimant_code.'
											data-target="#acivate_serviceProvider" 
										>
										<i class="fa fa-'.$icon.'"></i>
										'.$action.'
										</span>';
				}else{
					$activate_deactivate = '';
				}

				return '
				
				<a href="'.route('claims.edit_provider_details',['claimant_code'=>$clparam->claimant_code] ).'"
					class="btn btn-xs btn-primary"
				>
				<i class="fa fa-edit"></i>
				Edit
				</a>
				'.$activate_deactivate.'
				<a href="'.route('claims.view_provider_details',['claimant_code'=>$clparam->claimant_code] ).'"
					class="btn btn-xs" style="background-color: grey; color: white;"
				>
				<i class="fa fa-eye"></i>
				View
				</a>';
			})			
			->editColumn('status', function ($clparam) {
				if($clparam->status == 'ACTIVE'){
					return  '<a class="" style="width:76px; cursor:text; color:#50b848; font-weight:600;"> Active</a>';
				}else{
					return  '<a class="" style="width:76px; cursor:text; color:#8F8F8F"; font-weight:600;> Inactive</i></a>';
				}
			})
			->rawColumns(['Action', 'status'])			
        ->make(true);
	}

	public function aprove_decline_sp(Request $request){
        $user = trim(Auth::user()->user_id);
        $username = trim(Auth::user()->user_name);
        $claimant_code = $request->claimant_code;

        $get_sent_id = Escalate_pol::whereRaw("trim(claimant_code)='" . $claimant_code. "'")
						->orderBy('created_at', 'ASC')
						->get();

        $us = $get_sent_id[0]->user_name;
        $reciv = $get_sent_id[0]->sent_by;

		$oldclaimant_dets = Clparam::whereRaw("trim(claimant_code)='" . $claimant_code. "'")->first();
        if($request->declined == true){
			$status = "SERVICE PROVIDER REJECTED";

                    $update_escalate = Escalate_pol::where('claimant_code',$claimant_code)
                    ->where('re_escalate_date',null)
                    ->update([
                        'declined_date' => Carbon::now()
                    ]);
           
            $messagge = "Claimant '$claimant_code' Has Been Declined. $comm. Thank You. ";

            $comm = $request->comment;
            $escalate = new Escalate_pol ;
    
            $count = Escalate_pol::where('sent_by', $user )->max('escalate_id');
            $next = $count + 1;
    
            $escalate->escalate_id = $next;
            $escalate->escalate_type = 3;
            $escalate->claimant_code = $claimant_code;
            $escalate->sent_by =$user;
            $escalate->sent_to =$reciv;
            $escalate->type = 'SP';
            $escalate->description = "REJECTED";
            $escalate->user_name = $username;
            $escalate->comments = $comm;
            $escalate->approved = 'D';
            $escalate->approved_by= $user;
            $escalate->approved_date= Carbon::now();
            $escalate->description= 'Service Provider Declined';
            $escalate->approver_remarks =$request->comment;
            $escalate->created_at =  Carbon::now();
    
            $escalate->save();

			$claimant_dets = $oldclaimant_dets;
			$claimant_dets->approved = 'N';	
			$claimant_dets->save();

        }
        else if($request->approved == true){

			$status = "SERVICE PROVIDER APPROVED";
            // return $request;
            $signature = DB::table('aimsusers')->where('user_name', $username)->first()->signature;
            
            $update_pol = Escalate_pol::whereRaw("trim(claimant_code)='" . $claimant_code. "'")
                                    ->where("re_escalate_date" ,null)
                                    ->update([
                                        'approved'=> 'Y',
                                        'approved_by'=> $user,
                                        'approved_date'=> Carbon::now(),
                                        'description'=>  $status,
                                        'approver_remarks' =>$request->comment
									]);

				$claimant_dets = $oldclaimant_dets;
				$claimant_dets->status = 'ACTIVE';	
				$claimant_dets->approved = 'Y';	
                $claimant_dets->save();
        
            $messagge = "Claimant '$claimant_code' Has Been Approved. <br> $comm. Thank You. ";
        }
 
        
       
        DB::commit();

        //send email to user
        $email = Aimsuser::where('user_id', $reciv)->first();
        $emailaddr = $email->email;
        $reciever = $email->surname;
        $name = trim(Auth::user()->user_name);
      

        $sendemail = new Sendemail;
        $sendemail->category = $status;
        $sendemail->receiver =$emailaddr;
        $sendemail->message =$messagge;
        $sendemail->creator = $name;

		$changes = $claimant_dets->getChanges();

		$this->log_data($changes,$oldclaimantdets,$name,"SERVICE PROVIDER APPROVAL",$claimant_code);
  
     
        try{
            $sendemail->save();
            
            DB::commit();
			
			return redirect()->back()->with('success','Updated successfully.');


        }catch (\Exception $e) {
            DB::rollback();
			return redirect()->back()->with('error','Update Failed.');

        }

    }
	
	public function serviceProvidersAccounts(Request $request)
	{

		$spaccounts = PartnerBank::where('partner_number', $request->claimant_code)->get();

		if(count($spaccounts)< 1){

			$clparamccounts = Clparam::whereRaw("trim(claimant_code)='".trim($request->claimant_code)."'")->get();


			return Datatables::of($clparamccounts)
			->editColumn('bank_code', function ($clparamccount) {
				if(($clparamccount->bank_code != "")){
					$bank = Olbnknames::whereRaw("trim(bank_code)='".$clparamccount->bank_code."'")->first();
					return trim($bank->bank_code).'-'.$bank->description;
				}else{
					return "N/A";
				}

			})
			->editColumn('branch_code', function ($clparamccount) {
				if(($clparamccount->bank_code != "") and ($clparamccount->branch_code != "")){
					$branch = Olbranch::whereRaw("trim(bank_code)='".$clparamccount->bank_code."'")->whereRaw("trim(branch_code)='".$clparamccount->branch_code."'")->first();
					return trim($branch->branch_code).'-'.$branch->branch;
				}else{
					return "N/A";
				}

			})
			->editColumn('in_use', function ($clparamccount) {
				if($clparamccount->bank_account_no != ""){
					return "ACTIVE";
				}else{
					return "N/A";
				}

			})
			->editColumn('bank_account_no', function ($clparamccount) {
				if($clparamccount->bank_account_no != ""){
					return $clparamccount->bank_account_no;
				}else{
					return "N/A";
				}
			})
			->editColumn('bank_account_name', function ($clparamccount) {
				if($clparamccount->bank_account_name != ""){
					return $clparamccount->bank_account_name;
				}else{
					return "N/A";
				}
			})
			->editColumn('currency_type', function ($clparamccount) {
				if($clparamccount->currency_type != ""){
					return $clparamccount->bankCurrency->currency;
				}else{
					return "N/A";
				}
			})
			->addColumn('action', function ($spaccount) {
				return '<span data-toggle="modal" data-target="#edit_spaccount" id="open_espaccount" onclick= "open_spaccount(`'.$spaccount->item_no.'`)" ><i class="glyphicon glyphicon-edit"></i></span>';
			})
			->make(true);
		}
		else{
			return Datatables::of($spaccounts)
			->editColumn('bank_code', function ($spaccount) {

			$bank = Olbnknames::whereRaw("trim(bank_code)='".$spaccount->bank_code."'")->first();
			return trim($bank->bank_code).'-'.$bank->description;

			})
			->editColumn('branch_code', function ($spaccount) {

			$branch = Olbranch::whereRaw("trim(bank_code)='".$spaccount->bank_code."'")->whereRaw("trim(branch_code)='".$spaccount->branch_code."'")->first();
			return trim($branch->branch_code).'-'.$branch->branch;

			})
			->editColumn('currency_type', function ($spaccount) {

				$currency = Currency::where('currency_code',$spaccount->currency_type)->first();
				return trim($currency->description);

			})
			->editColumn('in_use', function ($spaccount) {
				if($spaccount->in_use == "Y"){
					return "ACTIVE";
				}elseif($spaccount->in_use == "N"){
					return "INACTIVE";
				}else{
					return "N/A";
				}

			})
			->addColumn('action', function ($spaccount) {
				return '<span data-toggle="modal" id="open_espaccount" data-itemno="'.$spaccount->item_no.'" ><i class="glyphicon glyphicon-edit"></i></span>';
			})

			->make(true);
		}

	}

	public function check_s_providers(Request $request){

		// $check_clparam = Clparam::where("pin_number" ,$request->pin_number)->where("record_type",$request->rec_type)->get();
		$check_clparam =DB::select("select * from clparam where record_type = '$request->rec_type' 
		and erp_vendor_code ='$request->erp_vendor_code' ");
		return count($check_clparam);

	}
	public function check_pin_providers(Request $request){

		// $check_clparam = Clparam::where("pin_number" ,$request->pin_number)->where("record_type",$request->rec_type)->get();
		$check_clparam =DB::select("select * from clparam where record_type = '$request->rec_type' 
		and pin_number ='$request->pin_number' ");
		return count($check_clparam);

	}

	
	public function view_provider_details(Request $request){
		// dd($request->all());

		$details = Clparam::where('claimant_code', $request->claimant_code)->first();
		$bnkname = Olbnknames::all();
		$branch = Olbranch::whereRaw("trim(bank_code)='".$details->bank_code."'")
						->whereRaw("trim(branch_code)='".$details->branch_code."'")
						->first();
		$clientbank =PartnerBank::where('partner_number',$request->claimant_code)->count();
		$escalate_pol =Escalate_pol::where('claimant_code',$request->claimant_code)->first();
		
		return view('gb.claims.claimant_det_page')->with(compact('details','escalate_pol','olbnknames','branch','bnkname','clmcorr','clientbank'));


	}
	public function add_provider_details(Request $request){

		$clmcorrs = Clmcorr::where('claimant', 'N')
                    ->where('insured', 'N')
                    ->where('third_party', 'N')
                    ->get();
		$olbnknames = Olbnknames::All();
		$idtypes = Identity_type::all();
		$regions = Regions::all();
		$currencies = Currency::all();
		$payment_methods = PaymentMode::select('paymode_code', 'paymode_name')->get();
		$customer_groups = CustomerGroup::select('group_code','group_desc')->get();
        $company_modules = CompanyModule::select('company_module_code','company_module_name')->get();
		$locationlevels = LocationLevel::select('level_code','level_name','level_type','load_method')->where('status','A')->get();
        $locations = Location::select('loc_code','description','loc_level')->where('active', 'Y')->where('loc_level',1)->get();
		$sp_approval = DB::table('new_claims_parameters')->pluck('sp_approval')->first();
          

		$save_method='add';
		
		return view('gb.claims.modals.sproviders_form')->with(compact('clmcorrs','olbnknames','payment_methods', 'idtypes','regions','save_method','currencies','customer_groups','company_modules','locationlevels','locations', 'sp_approval'));


	}
	public function edit_provider_details(Request $request){

		$sp = Clparam::where('claimant_code', $request->claimant_code)->first();
		$sp_approval = DB::table('new_claims_parameters')->pluck('sp_approval')->first();
		// $record_data=$sp;//for location
		$clientbank =PartnerBank::where('partner_number',$request->claimant_code)->count();


		$clmcorrs = Clmcorr::All();
		$olbnknames = Olbnknames::All();
		$idtypes = Identity_type::all();
		$regions = Regions::all();
		$currencies = Currency::all();
		$payment_methods = PaymentMode::select('paymode_code', 'paymode_name')->get();
		$customer_groups = CustomerGroup::select('group_code','group_desc')->get();
        $company_modules = CompanyModule::select('company_module_code','company_module_name')->get();
		$locationlevels = LocationLevel::select('level_code','level_name','level_type','load_method')->where('status','A')->get();
        $locations = Location::select('loc_code','description','loc_level')->where('active', 'Y')->where('loc_level',1)->get();

		$save_method='update';
		
		return view('gb.claims.modals.sproviders_form')->with(compact('clmcorrs','olbnknames','payment_methods', 'idtypes','regions','save_method','sp', 'sp_approval', 'record_data','currencies','customer_groups','company_modules','locationlevels','locations'));


	}

	public function branchName(Request $request) {
		$details = ClParam::where('claimant_code', $request->code)->first();
		$bank = $details->bank_code;
		$brnchCode = $details->bank_branch_code;

		$branch = Olbranch::where('bank_code', $bank)->where('branch_code', $brnchCode)->first();

		return $branch;
	}

	public function accountName(Request $request) {
		$details = ClParam::where('claimant_code', $request->code)->first();
		$account = $details->bank_account_no;
		return $account;
	}

	public function branches(Request $request) {
		$branches = Olbranch::where('bank_code', $request->bank_code)->get();

		return $branches;
	}

	//SAVE NEW SERVICE PROVIDER
	public function addServiceProvider(Request $request)
	{


		$team = $request->get('erp_vendor_code');

		$clmcorr = Clmcorr::Where('code','=',$request->record_type)->first();
		$curr_code=$this->getClparamCode($request->record_type);
		// $claimant_code =$this->genClaimantCode($request->record_type,$curr_code);
		//Generate on boarding number
		$response=$this->generateOnboardingNumber('S');

		$claimant_code='';

		if (isset($response['error'])) {
			return response()->json(['error' => $response['error']], 400);
		}
		$claimant_code=$response;

		//Generate on boarding number

		$pipcnam = Pipcnam::All()->first();
		$nlparams = Nlparams::WhereRaw("(prid='SLT' and prsno='$clmcorr->sltype')")->first();
		
		if($request->provider_type == 'I' ){

			$this->validate($request,[
				'name' => 'required',
				'record_type' => 'required',
				'mobile'=>'required',
				'provider_type'=>'required',
				'dob'=>'required',
			]);

			// passport or ID number mandatory
			if($request->id_type == '1' || $request->id_type == '2' ) {
				$this->validate($request,[
					'identity_number' => 'required',
				]);
			}

		}
		else if($request->provider_type == 'C'){

			$this->validate($request,[
				'name' => 'required',
				'record_type' => 'required',
				'mobile'=>'required',
				// 'pin_number'=>'required',
				'dob'=>'required',
				'provider_type'=>'required',
			]);
		}
	

		if($request->get('erp_vendor_code')!= null){

			$chparam =DB::select("select * from clparam where record_type = '$request->rec_type' 
			and erp_vendor_code ='$request->erp_vendor_code' ");

			if(count($chparam)>0){
				return 2;
			}
			
		}

		try {

			$check_clparam =DB::select("select * from clparam where vat_no = '$request->vat_no' and record_type='$request->rec_type' ");

			if(count($check_clparam) >0){
				return 2;
			}

			DB::transaction(function() use ($request,$curr_code,$claimant_code,$pipcnam,$clmcorr,$nlparams){

				$insert_clparam = new Clparam;
				$insert_clparam->code=$curr_code;
				$insert_clparam->slhead=$claimant_code;
				$insert_clparam->claimant_code=$claimant_code;
				$insert_clparam->date_appointed=$this->todays_date;
				$insert_clparam->dola=$this->todays_date;
				$insert_clparam->offcd=$pipcnam->offcd;
				$insert_clparam->erp_vendor_code=$request->get('erp_vendor_code');
				$insert_clparam->record_type=$request->record_type;
				$insert_clparam->name=$request->name;
				$insert_clparam->contact_telephone=$request->contact_telephone;
				$insert_clparam->mobile=$request->mobile;
				$insert_clparam->e_mail=$request->e_mail;

				$insert_clparam->location_1   = $request->input('level_1');
				$insert_clparam->location_2   = $request->input('level_2');
				$insert_clparam->location_3 = $request->input('level_3');
				$insert_clparam->location_4  = $request->input('level_4');
				$insert_clparam->location_5  = $request->input('level_5');
				$insert_clparam->location_6   = $request->input('level_6');

				// Location labels  
				$insert_clparam->loclabel_1   = $request->input('loclevel_1');
				$insert_clparam->loclabel_2   = $request->input('loclevel_2');
				$insert_clparam->loclabel_3 = $request->input('loclevel_3');
				$insert_clparam->loclabel_4  = $request->input('loclevel_4');
				$insert_clparam->loclabel_5  = $request->input('loclevel_5');
				$insert_clparam->loclabel_6   = $request->input('loclevel_6');

				//status
				$sp_approval = DB::table('new_claims_parameters')->pluck('sp_approval')->first();
				$insert_clparam->status = $sp_approval == 'Y' ? "INACTIVE" : "ACTIVE";
				
				$insert_clparam->dob=$request->dob;
				$insert_clparam->addr1=$request->addr1;
				$insert_clparam->id_number=$request->identity_number;
				$insert_clparam->identity_type=$request->id_type;
				$insert_clparam->provider_type=$request->provider_type;
				$insert_clparam->pin_number=$request->pin_number;
				$insert_clparam->vat_no=$request->vat_no;
				$insert_clparam->incorporation_cert=$request->incorporation_cert;
				$insert_clparam->business_number=$request->business_number;
				$insert_clparam->valuer=$request->valuer;
				$insert_clparam->bank_code=$request->bank_code[0];
				$insert_clparam->bank_branch_code=$request->bank_branch_code[0];
				$insert_clparam->bank_account_no=trim($request->bank_account_no[0]);
				$insert_clparam->bank_account_name=$request->bank_account_name[0];
				$insert_clparam->consent_mkt=$request->send_mkt_material;
				$insert_clparam->consent_internal=$request->share_kyc_internally;
				$insert_clparam->consent_out_ke=$request->share_kyc_externally;
				$insert_clparam->consent_child=$request->share_child_kyc;
				$insert_clparam->consent_3rd_party=$request->kyc_details_from3rdparty;
				$insert_clparam->consent_analysis=$request->use_kyc_for_research;
				// $insert_clparam->d365_fo_integration = $request->d365_fo_integration;
				$insert_clparam->company_to_integrate_fo = $request->company_to_integrate_fo;
				// $insert_clparam->d365_crm_integration = $request->d365_crm_integration;
				$insert_clparam->customer_group  =  $request->customer_group;
				$insert_clparam->save();

				$getpartnerno = new IntergrationController;
				$partner_type = $request->provider_type;
				$partner_code = $claimant_code;
				$partner_group = 'sp';

				$resp = $getpartnerno->getPartnerData($partner_group,$partner_type,$partner_code);
				
				
				
				if($resp->status == 0){
					Session::flash('error','Partner number for: '.$claimant_code.' failed to generate. Please try again later.');
				}else{
					Session::flash('success','Partner number for: '.$claimant_code.' generated successfully');
				}

				// $data=$this->generateClaimantPartnerNo($request, $claimant_code);

				// if($data['status']==2){

				// 	return response()->json(['data' => $data]);

				// }
				
				foreach ($request->bank_code as $key => $bank_code) {
					if($request->bank_code[$key] != null){
						$primary_account = ($key == 0) ? 'Y' : 'N';

						$holder = 'SP';
						$in_use ='Y';

						addbank($claimant_code,$key,$request->bank_code[$key],$request->bank_branch_code[$key],$request->bank_account_no[$key]
						,$request->bank_account_name[$key],$request->currency_type[$key],$holder,$in_use,$primary_account,$request->payment_mode[$key]);

					}
				}

				$count = Escalate_pol::max('escalate_id');
				$next = (int)$count + 1;
				$name =  Auth::user()->user_name;
				$sender_id =  Auth::user()->user_id;
				$user_id = $request->escalate_doc;
	
				if ($user_id) {
					$recieverdet = Aimsuser::where('user_id', $user_id)->first();
				}
	
				$reciever = trim($recieverdet->user_name);
				$sent_id = trim($recieverdet->user_id);
				$emailaddr  = $recieverdet->email;
				$req_num = $req_no;
	
				$mess = "Dear $reciever , Kindly Approve the service provider with claimant code '$claimant_code'.Thank You. ";
				
	
	
				$escalate = new Escalate_pol;
				$escalate->escalate_id = $next;
				$escalate->claimant_code = $claimant_code;
				$escalate->sent_by = $name;
				$escalate->sent_to = $sent_id;
				$escalate->sent_by = $sender_id;
				$escalate->type = 'SP';
				$escalate->description = 'SERVICE PROVIDER APPROVAL';
				$escalate->user_name = $reciever;
				$escalate->created_at =  Carbon::now();
				$escalate->save();
	
				$category = 'SERVICE PROVIDER APPROVAL';
	
				if(!empty($emailaddr)){
					
					$sendemail = new SendEmail;
					$sendemail->category = $category;
					$sendemail->receiver = $emailaddr;
					$sendemail->message = $mess;
					$sendemail->creator = $name;
					$sendemail->status = 'SEND';
					$sendemail->createdate = Carbon::today(); 
					$sendemail->save();
					
				}

				
			
		});
			//Mark onboarding number as allocated
			$this->updateAllocated('S',$claimant_code);
			
			return 1;
		}
		catch (\Throwable $e) {
			DB::rollback();

			return 0;
			Session::flash('error','Failed to Create Service Provider');
			// return redirect()->back();
		}
	}

	public function get_s_provider_enquiry(Request $request)
	{
			// return($request);
		$clparam =  Clparam::where('CLAIMANT_CODE',$request->s_provider_code)->first();
		return [
			'clparam'=> Clparam::where('CLAIMANT_CODE',$request->s_provider_code)->first(),
			'clmcorr' => Clmcorr::Where('code','=',$clparam->record_type)->first()
		];
	}
	public function getaccountdetails(Request $request)
	{
			// return($request);
		$clientbank =  PartnerBank::where('partner_number',trim($request->claimant_code))->where('item_no',trim($request->itemno))->first();
		return [
			'clientbank'=> $clientbank
		];
	}
	// get_s_provider_enquiry > end



	public function editsprovideraccount(Request $request)
	{
		$clparam_old = Clparam::where('claimant_code',$request->claimant_code)->first();
		$clientbank_old = PartnerBank::where('partner_number',$request->claimant_code)->where('item_no', $request->item_no)->first();
		$clientbank_count = PartnerBank::where('partner_number',$request->claimant_code)->count();
		if($request->item_no == null){
			$clientbank_count = PartnerBank::where('partner_number',$request->claimant_code)->max('item_no');
			

			if($request->bank_code != null){

				$holder = 'SP';
				$in_use ='Y';
				$clientbank=addbank($request->claimant_code,$clientbank_count,$request->bank_code,
				$request->bank_branch_code,$request->bank_account_no
				,$request->bank_account_name,$request->currency_type
				,$holder,$in_use, $request->primary,$request->payment_mode);
				// $clientbank = new PartnerBank;
				// $clientbank->partner_number = $request->claimant_code; 
				// $clientbank->item_no = $clientbank_count + 1;
				// $clientbank->bank_code=$request->bank_code;
				// $clientbank->branch_code=$request->bank_branch_code;
				// $clientbank->bank_account_no=trim($request->bank_account_no);
				// $clientbank->bank_account=trim($request->bank_account_no);
				// $clientbank->bank_account_name=$request->bank_account_name;
				// $clientbank->currency_type=$request->currency_type;
				// $clientbank->in_use = 'Y';
				// $clientbank->holder = 'SP';
				// $clientbank->save();

			}

			if($request->primary == 'Y' && $clientbank){
				$insert_clparam = Clparam::where('claimant_code',$request->claimant_code)->update([
					'bank_code' => $request->bank_code,
					'bank_branch_code' => $request->bank_branch_code,
					'bank_account_no' => $request->bank_account_no,
					'bank_account_name' => $request->bank_account_name

				]);

				// Update the primary account column
				PartnerBank::where('partner_number', $request->claimant_code)
					->where('item_no', $clientbank->item_no)
					->update(['primary_account' => 'Y']);
			
				// Set the primary_account to 'N' for all other banks of the same claimant code
				PartnerBank::where('partner_number', $request->claimant_code)
					->where('item_no', '!=', $clientbank->item_no)
					->update(['primary_account' => 'N']);
				
            }

			$changed_data = $clientbank->getAttributes();
			$process_slug = 'service-provider-onboarding';
			$activity_slug = 'update';
			$unique_item = $request->claimant_code;
			$old_data = $clientbank_old;
			$ip =$request->ip();

			log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

			if ($clientbank->save()) {
				return response()->json(['success' => true, 'message' => 'Account number '.$request->bank_account_no. ' has been added']);
				
			}else{
				return response()->json(['error' => true, 'message' => 'Error occured while adding extra account number '.$request->bank_account_no]);
	
			}
		}
		else{
			if($clientbank_count == 1 || $request->primary == 'Y'){
				$insert_clparam = Clparam::where('claimant_code',$request->claimant_code)->update([
					'bank_code' => $request->bank_code,
					'bank_branch_code' => $request->bank_branch_code,
					'bank_account_no' => $request->bank_account_no,
					'bank_account_name' => $request->bank_account_name
				]);
				// Update the primary account column
				PartnerBank::where('partner_number', $request->claimant_code)
					->where('item_no', $request->item_no)
					->update(['primary_account' => 'Y']);
			
				// Set the primary_account to 'N' for all other banks of the same claimant code
				PartnerBank::where('partner_number', $request->claimant_code)
					->where('item_no', '!=', $request->item_no)
					->update(['primary_account' => 'N']);

			}
			//update in client bank account details
			$clientbank = PartnerBank::where('partner_number',$request->claimant_code)->where('item_no', $request->item_no)->update([
				'bank_code' => $request->bank_code,
				'branch_code' => $request->bank_branch_code,
				'bank_account_no' => $request->bank_account_no,
				'bank_account' => $request->bank_account_no,
				'bank_account_name' => $request->bank_account_name,
				'in_use' => $request->in_use,
				'currency_type' => $request->currency_type,
				'primary_account' => $request->primary,
				'payment_mode' => $request->payment_mode,

			]);

			$updated_clientbank = PartnerBank::where('partner_number',$request->claimant_code)->where('item_no', $request->item_no)->first();
			
			$changed_data = $updated_clientbank->getAttributes();
			$process_slug = 'service-provider-onboarding';
			$activity_slug = 'update';
			$unique_item = $request->claimant_code;
			$old_data = $clientbank_old;
			$ip =$request->ip();

			log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

			
			
			if ($clientbank) {
				return response()->json(['success' => true, 'message' => 'Account has been updated to ' . $request->bank_account_no]);
				
			}else{
				return response()->json(['error' => true, 'message' => 'Error occured while updating account number ' . $request->bank_account_no]);

			}
		}
	}

	public function editServiceProvider(Request $request){

		$clmcorr = Clmcorr::Where('code','=',$request->record_type)->first();
		$curr_code=$this->getClparamCode($request->record_type);
		$claimant_code =$this->genClaimantCode($request->record_type,$curr_code);
		$pipcnam = Pipcnam::All()->first();
		$nlparams = Nlparams::WhereRaw("(prid='SLT' and prsno='$clmcorr->sltype')")->first();
		$clparam_old = Clparam::where('claimant_code',$request->claimant_code)->first();
		$insert_clparam = Clparam::where('claimant_code',$request->claimant_code)->update([

			'code' => $curr_code,
			'slhead' => $request->claimant_code,
			'date_appointed' => $this->todays_date,
			'dola' => $this->todays_date,
			'offcd' => $pipcnam->offcd,
			'erp_vendor_code' => $request->get('erp_vendor_code'),
			'record_type' => $request->record_type,
			'name' => $request->name,
			'contact_telephone' => $request->contact_telephone,
			'mobile' => $request->mobile,
			'e_mail' => $request->e_mail,
			'dob' =>$request->dob,
			'addr1' => $request->addr1,
			'valuer' => $request->valuer,
			'pin_number' => $request->pin_number,
			'vat_no' => $request->vat_no,
			'business_number' => $request->business_number,
			'provider_type' => $request->provider_type,
			'identity_type' => $request->id_type,
			'id_number' => $request->identity_number,
			'bank_code' => $request->bank_code,
			'bank_branch_code' => $request->bank_branch_code,
			'bank_account_no' => $request->bank_account_no,
			'bank_account_name' => $request->bank_account_name,
			'consent_mkt' => $request->send_mkt_material,
			'consent_internal' => $request->share_kyc_internally,
			'consent_out_ke' => $request->share_kyc_externally,
			'consent_child' => $request->share_child_kyc,
			'consent_3rd_party' => $request->kyc_details_from3rdparty,
			'consent_analysis' => $request->use_kyc_for_research,
			// 'd365_fo_integration' => $request->d365_fo_integration,
			'company_to_integrate_fo' => $request->company_to_integrate_fo,
			// 'd365_crm_integration' => $request->d365_crm_integration,
			'customer_group'  =>  $request->customer_group,
			'location_1'   => $request->input('level_1'),
			'location_2'   => $request->input('level_2'),
			'location_3' => $request->input('level_3'),
			'location_4'  => $request->input('level_4'),
			'location_5'  => $request->input('level_5'),
			'location_6'   => $request->input('level_6'),
			'loclabel_1'   => $request->input('loclevel_1'),
			'loclabel_2'   => $request->input('loclevel_2'),
			'loclabel_3' => $request->input('loclevel_3'),
			'loclabel_4'  => $request->input('loclevel_4'),
			'loclabel_5'  => $request->input('loclevel_5'),
			'loclabel_6'   => $request->input('loclevel_6'),
		]);

		$updated_clparam = Clparam::where('claimant_code', $request->claimant_code)->first();

		$conditionColumns = [
			'claimant_code' => $request->claimant_code
		];
		AuditActionEvent::dispatch((new Clparam)->getTable(),$conditionColumns,$action_type='UPDATE');
		
		//log changes to the new logs table
		$changed_data = $updated_clparam->getAttributes();
		$process_slug = 'service-provider-onboarding';
		$activity_slug = 'update';
		$unique_item = $request->claimant_code;
		$old_data = $clparam_old;
		$ip =$request->ip();

		log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);
		if ($insert_clparam) {
			return 1;
			
		}else{
			return 0;

		}
	}

	public function activateServiceProvider(Request $request){
		DB::beginTransaction();
		try {
			$clparam_old = Clparam::where('claimant_code',$request->claimant_code)->first();
			$insert_clparam = $clparam_old;
			$insert_clparam->status = $clparam_old->status == "ACTIVE" ? "INACTIVE" : "ACTIVE";
			$insert_clparam->save();
	
				$conditionColumns = [
					'claimant_code' => $request->claimant_code
				];
				AuditActionEvent::dispatch((new Clparam)->getTable(),$conditionColumns,$action_type='UPDATE');
	
				$changes = $insert_clparam->getChanges();
				$this->log_data($changes,$clparam_old,Auth::user()->user_name, "ACTIVATION/DEACTIVATION",$request->claimant_code);

				Spstatusaudit::create([
					'claimant_code' => $request->claimant_code,
					'old_status' => $clparam_old->status,
					'current_status' => $clparam_old->status == "ACTIVE" ? "INACTIVE" : "ACTIVE",
					'reason' => $request->deactivate_reason,
					'user_str'  => Auth::user()->user_name,
				]);

				DB::commit();
	
				
			if ($insert_clparam) {
				Session::flash('success','Service provider has been updated');
				
			}else{
				Session::flash('error occured while updating');

			}
			return redirect()->route('claims.s_providers_enquiry');
		} catch (\Throwable $th) {
			//throw $th;
			DB::rollback();
		}
	}

	public function ServiceProviderInvoices()
    {
		$service_providers = DB::table('clparam')
		->select('*')
		->get();
		$currencies = Currency::all();
		$pipcnam = Pipcnam::all()->first();
		return view('gb.claims.service_provider_invoices',[
            'service_providers'=>$service_providers,
			'currencies' => $currencies,
			'pipcnam'=>$pipcnam
        ]);
	}

	public function ServiceProviderInvoicesEnquiry(Request $request)
    {
		Gate::authorize('view-service-provider-invoice');
        if ($request->ajax()) {
            $data = DB::table('s_provider_invoices')
					->join('clparam', function ($join) {
						$join->on('s_provider_invoices.s_provider_code', 'clparam.claimant_code');
					})
                    ->select('s_provider_code','clparam.name','invoice_number','foreign_amount_payable','foreign_allocated','foreign_unallocated','currency','cancelled');

            return DataTables::of($data)
                    ->addColumn('action',function($data){
                        $btn = '';

                        if (Gate::check('edit-service-provider-invoice') && $data->cancelled <> 'Y') {
                            $btn  = '<a style=" href="#" id="view-sprovider-invoicedtls" data-id="' . $data->s_provider_code . '" data-number="' . $data->invoice_number . '" class="btn btn-xs btn-default"><i class="fa fa-eye"></i> View Details</a>';
							
							$btn .= '&nbsp;&nbsp';
                            
							$btn .= '<a style=" href="#" id="edit-sprovider-invoice" data-id="' . $data->s_provider_code . '" data-number="' . $data->invoice_number . '" class="btn btn-xs btn-primary"><i class="fa fa-pencil-square-o"></i> Edit</a>';
                        
                            $btn .= '&nbsp;&nbsp';
						}else{

							$btn  = '<a class="btn btn-xs btn-default" disabled><i class="fa fa-eye"></i> View Details</a>';
							
							$btn .= '&nbsp;&nbsp';
                            
							$btn .= '<a class="btn btn-xs btn-primary" disabled><i class="fa fa-pencil-square-o"></i> Edit</a>';
                        
                            $btn .= '&nbsp;&nbsp';
							$btn .= '<a class="btn btn-xs btn-danger" disabled ><i class="fa fa-minus-square-o"></i> Cancel</a>';


						}

						if (Gate::check('cancel-service-provider-invoice') && $data->cancelled <> 'Y') {
                            $btn .= '<a href="#" id="cancel-sprovider-invoice" data-id="' . $data->s_provider_code . '" data-number="' . $data->invoice_number . '" class="btn btn-xs btn-danger"><i class="fa fa-minus-square-o"></i> Cancel</a>';
                        }
                        return $btn;
                    })
					->editColumn('currency',function($data){
	
						$currency = Currency::where('currency_code',$data->currency)->first();
						return $currency->currency;
						
					})
                    ->make(true);
        }

        return view('gb.claims.service_provider_invoices');
    }

	public function ServiceProviderInvoicesStore(SaveServiceProviderInvoice $request)
    {
        Gate::authorize('create-service-provider-invoice');

        $status = 0;
		

        $loggedInUser = Auth()->user()->user_name;

        $data = $request->validated();

		$dtrano = Dtran0::first();

		$foreign_invoice_amount = str_replace(',','',$data['foreign_invoice_amount']);
		$foreign_tax_amount = str_replace(',','',$data['foreign_tax_amount']);
		$foreign_amount_payable = str_replace(',','',$data['foreign_amount_payable']);
		

        DB::beginTransaction();
        try {

            $invoice = new ServiceProviderInvoices();
			$invoice->s_provider_code = $data['s_provider_code'];
			$invoice->invoice_date = $data['invoice_date'];
            $invoice->invoice_number = $data['invoice_number'];
			$invoice->description = $data['description'];
			$invoice->fdn_number = $data['fdn_number'];
			$invoice->invoice_amount = $foreign_invoice_amount * $data['currency_rate'] ;
			$invoice->tax_amount = $foreign_tax_amount * $data['currency_rate'] ;
			$invoice->currency = $data['currency'];
			$invoice->currency_rate = $data['currency_rate'];
			$invoice->amount_payable = $foreign_amount_payable * $data['currency_rate'];
			$invoice->allocated = 0;
			$invoice->foreign_allocated = 0;
			$invoice->unallocated = $foreign_invoice_amount * $data['currency_rate'];
			$invoice->foreign_unallocated = $foreign_invoice_amount;
			$invoice->foreign_amount_payable = $foreign_amount_payable;
			$invoice->foreign_invoice_amount = $foreign_invoice_amount;
			$invoice->foreign_tax_amount = $foreign_tax_amount;
			$invoice->account_month = $dtrano->account_month;
			$invoice->account_year = $dtrano->account_year;

            $invoice->created_by = $loggedInUser;
            $invoice->updated_by = $loggedInUser;
			
            $invoice->save();

            DB::commit();
            $status = 1;
        } catch (\Throwable $th) {
            throw $th;
            DB::rollBack();
        }

        return array('status' => $status);
    }

	/**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function ServiceProviderInvoiceDetails(Request $request)
    {
        
		$clparam = Clparam::Select('name','claimant_code','pin_number','mobile')
							->where('claimant_code',$request->s_provider_code)
							->first();
							
		$sp_invoice = ServiceProviderInvoices::select('foreign_amount_payable','currency_rate',
		'currency','foreign_allocated','foreign_unallocated','description','invoice_date')
											->where('s_provider_code',$request->s_provider_code)
											->where('invoice_number',$request->invoice_number)
											->first();
		$currency = Currency::select('currency')->where('currency_code',$sp_invoice->currency)->first();
		
		$name = $clparam->name;
		$pin_number = $clparam->pin_number;
		$mobile = $clparam->mobile;
		$amount_payable = $sp_invoice->foreign_amount_payable;
		$currency_rate = $sp_invoice->currency_rate;
		$currency = $currency->currency;
		$allocated = $sp_invoice->foreign_allocated;
		$unallocated = $sp_invoice->foreign_unallocated;
		$description = $sp_invoice->description;
		$invoice_date = $sp_invoice->invoice_date;
		$invoice_number = $request->invoice_number;
		$sp_number = $request->s_provider_code;
		
       
		return view('gb.claims.service_provider_invoicedtls')
					->with(compact('name','pin_number','mobile','amount_payable','currency_rate','currency','allocated',
				'unallocated','description','invoice_date','invoice_number','sp_number'));
        
    }

	 public function getSpAllocationData(Request $request)
    {
		$providerCode = $request->s_provider_code;
		$refNumber = $request->invoice_number;
		$doc_type = 'INV';
		
		$sp_allocations = DB::select("SELECT REF_NUMBER,DOC_TYPE,FOREIGN_AMOUNT,ALLOCATION_NUMBER,REF_ALLOCATION_NUMBER  
		FROM S_PROVIDER_ALLOCATIONS WHERE ALLOCATION_NUMBER  IN (SELECT ALLOCATION_NUMBER FROM S_PROVIDER_ALLOCATIONS spa 
		WHERE trim(REF_NUMBER) ='$refNumber' AND DOC_TYPE ='$doc_type' AND S_PROVIDER_CODE ='$providerCode')");
			

		return DataTables::of($sp_allocations)
        ->addColumn('action', function ($sp_allocations) {
            return '<button class="btn btn-primary">Action</button>';
        })
        ->rawColumns(['action'])
        ->make(true);
        

       
                
    }

	 public function s_provider_requisition_details(Request $request)
    {
		$providerCode = trim($request->s_provider_code);
		$refNumber = trim($request->invoice_number);
		$doc_type = 'INV';
		
		$sp_allocations = DB::select("SELECT a.requisition_no,b.claim_no,b.name payee,b.amount from payreqstd a 
			join payreqst b on a.requisition_no = b.req_no
			where a.claimant_code = '$providerCode' 
			and a.entry_type_reference = '$refNumber' 
			and b.cancelled is null
		");
			

		return DataTables::of($sp_allocations)
		->editColumn('requisition_no', function ($sp_allocations) {
				
			return formatRequisitionNo($sp_allocations->requisition_no);
		})
			
		->editColumn('claim_no', function ($sp_allocations) {
				
			return formatPolicyOrClaim($sp_allocations->claim_no);
		})
		->editColumn('amount', function ($sp_allocations) {
				
			return number_format($sp_allocations->amount);
		})
		
        ->rawColumns(['action'])
        ->make(true);
        

       
                
    }

	/**
     * Show the form for editing the specified resource.
     *
     * @param  string  $s_provider_code
     * @param  string  $invoice_number
     * @return \Illuminate\Http\Response
     */
    public function ServiceProviderInvoicesEdit($s_provider_code,$invoice_number)
    {
        Gate::authorize('edit-service-provider-invoice');

		$invoice = ServiceProviderInvoices::where('s_provider_code', $s_provider_code)
                                          ->where('invoice_number', $invoice_number)
                                          ->first();
										  	
        return $invoice;
    }


	/**
     * Update the specified resource in storage.
     *
     * @param  string  $s_provider_code
     * @param  string  $invoice_number
     * @return \Illuminate\Http\Response
     */
    public function ServiceProviderInvoicesUpdate(SaveServiceProviderInvoice $request,$s_provider_code,$invoice_number)
    {
        Gate::authorize('edit-service-provider-invoice');

        $status = 0;

        $data = $request->validated();
        $loggedInUser = Auth()->user()->user_name;

		DB::beginTransaction();

		$foreign_invoice_amount = str_replace(',','',$data['foreign_invoice_amount']);
		$foreign_tax_amount = str_replace(',','',$data['foreign_tax_amount']);
		$foreign_amount_payable = str_replace(',','',$data['foreign_amount_payable']);

        try {
			$invoice = ServiceProviderInvoices::where('s_provider_code', $s_provider_code)
			->where('invoice_number', $invoice_number)
			->update([
				'invoice_date' => $data['invoice_date'],
				'fdn_number' => $data['fdn_number'],
				'description' => $data['description'],
				'invoice_amount' => $foreign_invoice_amount * $data['currency_rate'] ,
				'tax_amount' => $foreign_tax_amount * $data['currency_rate'] ,
				'currency' => $data['currency'],
				'currency_rate' => $data['currency_rate'],
				'amount_payable' => $foreign_amount_payable * $data['currency_rate'],
				'unallocated'=> $foreign_invoice_amount * $data['currency_rate'],
				'foreign_unallocated' => $foreign_invoice_amount,
				'foreign_amount_payable' => $foreign_amount_payable,
				'foreign_invoice_amount'=> $foreign_invoice_amount,
				'foreign_tax_amount' => $foreign_tax_amount,
				'updated_by' => $loggedInUser
			]);

			DB::commit();
            $status = 1;
        } catch (\Throwable $th) {
			
			DB::rollBack();
            throw $th;
        }

        return array('status' => $status);
    }

	/**
     * Check if invoice is in use
     *
     * @param  string  $s_provider_code
     * @param  string  $invoice_number
     * @return \Illuminate\Http\Response
     */
	public function CheckInvoiceUsage(Request $request, $s_provider_code, $invoice_number)
	{
		
		// Check if it exists in payreqstd
		$isUsed = Payreqstd::whereRaw("trim(claimant_code)='" . trim($s_provider_code) ."'")
							->whereRaw("trim(entry_type_reference)='" . trim($invoice_number) ."'")
							->exists();

		if ($isUsed) {
			return response()->json(['error' => 'Invoice is being used','status' => 0]);
		}

		return response()->json(['success' => 'Invoice is not used','status' => 1]);
	}


	/**
     * cancel the specified resource in storage.
     *
     * @param  string  $s_provider_code
     * @param  string  $invoice_number
     * @return \Illuminate\Http\Response
     */
    public function ServiceProviderInvoicesCancel($s_provider_code,$invoice_number)
    {
        Gate::authorize('cancel-service-provider-invoice');

        $status = 0;
        $loggedInUser = Auth()->user()->user_name;

		DB::beginTransaction();

        try {
			$invoice = ServiceProviderInvoices::where('s_provider_code', $s_provider_code)
			->where('invoice_number', $invoice_number)
			->update([
				'cancelled' => 'Y',
				'cancelled_by'=> $loggedInUser,
				'cancelled_date' => Carbon::now()
			]);

			DB::commit();
            $status = 1;
        } catch (\Throwable $th) {
			
			DB::rollBack();
            throw $th;
        }

        return array('status' => $status);
    }

	public function get_sp_invoicedtls(Request $request) {

		if(!empty( $request->invoice_no) && !empty($request->claimant_code)){

	
		$fetch_invoice = ServiceProviderInvoices::where('invoice_number', $request->invoice_no)
		->where('s_provider_code', $request->claimant_code)
		->get(['s_provider_code','invoice_number','foreign_unallocated', 'unallocated'])->first();

		// check if documents have been uploaded for this invoice.
		$entityid = $request->claimant_code;
		$contextid = $request->claimant_code.$request->invoice_no;
		$process_code = 78;
		$dept = 0;
		
		if(!empty($request->invoice_no)){
			$mandatoryDocsUploadStatus = $this->checkMandatoryDocsUpload($entityid, $contextid, $process_code, $dept);

			$upload_status = 0;
			$upload_status = ($mandatoryDocsUploadStatus['status'] == 0) ? -1 : 1;
		}else{
			$upload_status = 1;
		}

		#check if exists on another  requisition not posted
		$inv_check = Payreqstd::whereRaw("trim(claimant_code)='" . trim($fetch_invoice->s_provider_code) ."'")
			->whereRaw("trim(entry_type_reference)='" . trim($fetch_invoice->invoice_number) ."'")
			->where('requisition_no', '<>', $request->req_no)->get();

		$result = Payreqstd::select('p2.REQ_NO', 'payreqstd.ENTRY_TYPE_REFERENCE', 'p2.CLAIMANT_CODE')
				->join('payreqst as p2', 'p2.REQ_NO', '=', 'payreqstd.REQUISITION_NO')
				->where('payreqstd.CLAIMANT_CODE', $request->claimant_code)
				->whereRaw("trim(ENTRY_TYPE_REFERENCE)='".$request->invoice_no."'")
				->where('payreqstd.REQUISITION_NO', '<>', $request->req_no)
				->where(function($query) {
					$query->where('p2.VOUCHER_RAISED', 'N')
							->orWhereNull('p2.VOUCHER_RAISED');
				})
				->where(function($query) {
					$query->whereNull('p2.CANCELLED')
							->orWhere('p2.CANCELLED', 'N');
				})
				->get()[0];

			// If mandatory documents for the service provider have not been uploaded
			if( $upload_status == -1 ){
			
				return response()->json(['error' => 'Kindly upload the invoice documents for this invoice for you to proceed', 'status' => -1]);
				
			}
			if ($result ) {
			
				return response()->json(['error' => 'Invoice is being used', 'status' => 0, 'result' => $result]);
				
			}
			else{

				return $fetch_invoice;
				
			}

		}else{

			return response()->json(['error' => 'No Invoice is has been Selected ', 'status' => -2]);

		}

	}
	public function claimant_data()
	{
		Gate::authorize('on-board-claimant');
		$clmcorrs = Clmcorr::where('third_party','=','Y')->get();
		$payment_methods = PaymentMode::select('paymode_code', 'paymode_name')->get();
		$olbnknames = Olbnknames::All();
		$idtypes = Identity_type::all();
		return view('gb.claims.claimant_enquiry')->with(compact('clmcorrs','olbnknames','idtypes','payment_methods'));
	}
	public function get_claimant_data(Request $request)
	{

		$schem = schemaName();
        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
		//$clparam = Clparam::Select('name','claimant_code','pin_number','mobile')
		$clparam = DB::table(''.$gb.'.clparam')  
				->join(''.$gb.'.clmcorr','clparam.record_type','=','clmcorr.code')
				->select('clmcorr.name as clmcorr_name','clparam.name as clparam_name','clparam.claimant_code','clparam.erp_vendor_code','clparam.pin_number','clparam.mobile','clparam.e_mail as e_mail','clparam.id_number as id_number','clparam.partnernumber')
				->where('third_party','=','Y')
				->orderBy('clparam.record_type');
				

		return Datatables::of($clparam)
	
			->addColumn('Action', function($clparam){
				return '
				<span 
					onclick= "open_serviceProvider(`'.$clparam->claimant_code.'`)"
				>
				<i class="fa fa-eye"></i>
				</span>
				';
			})
		->rawColumns(['Action'])			
        ->make(true);
	}

	public function claimant_details($claimant_code){

		$details = Clparam::where('claimant_code', $claimant_code)->first();
		$bnkname = Olbnknames::all();
		// $bnkname = Olbnknames::whereRaw("trim(bank_code)='".trim($details->bank_code)."'")->first();
		$branch = Olbranch::whereRaw("trim(bank_code)='".$details->bank_code."'")->whereRaw("trim(branch_code)='".$details->branch_code."'")->first();
		$clmcorr= Clmcorr::where('third_party', 'Y')->get();
		$clientbank =PartnerBank::where('partner_number',$claimant_code)->count();
		$payment_methods = PaymentMode::select('paymode_code', 'paymode_name')->get();
		$idtypes = Identity_type::all();
		return view('gb.claims.claimant_details')->with(compact('details','olbnknames','branch','bnkname','clmcorr','clientbank','idtypes','payment_methods'));

	}

	public function get_claimant_bankdets(Request $request){

			$banks= Clparam::where('claimant_code',$request->claimant_code)->first();
			$bnkcode =trim($banks->bank_code); 
			$bnkbranch =trim($banks->bank_branch_code); 
			$bankdets = DB::select("select a.description,b.branch from olbnknames a
			join olbranch b on a.bank_code = b.bank_code
			where b.bank_code = '$bnkcode' and b.branch_code = '$bnkbranch' ");

			// dd($banks,$bankdets,$bnkcode,$bnkbranch);
			return Datatables::of($bankdets)
			->editColumn('bank_codex', function ($bankdets) {
				if(($bankdets->branch == null )){
					return "N/A";
				}else{
					return Trim($bankdets->branch);
				}

			})
			
			->editColumn('description', function ($bankdets) {
				if(($bankdets->description == null )){
					return "N/A";
				}else{
					return Trim($bankdets->description);
				}

			})
			
			->editColumn('bank_account_no', function ($bankdets)use(&$banks) {
				if(($banks->bank_account_no == null )){
					return "N/A";
				}else{
					return Trim($banks->bank_account_no);
				}

			})
			->editColumn('bank_account_name', function ($bankdets)use(&$banks) {
				if(($banks->bank_account_name == null )){
					return "N/A";
				}else{
					return Trim($banks->bank_account_name);
				}

			})

			->addColumn('action', function ($bankdets)use(&$banks) {
				return '<span data-toggle="modal" 
				id="open_espaccount" onclick= "editbank(`'.$banks->claimant_code.'`,`'.$banks->bank_code.'`,`'.$banks->bank_branch_code.'`)" >
				<i class="glyphicon glyphicon-edit"></i></span>';
			})
			// data-target="#edit_spaccount" 
			->make(true);

	}



	public function getauditlog(Request $request){

		$clparamccounts = Claimant_log::whereRaw("trim(claimant_id)='".trim($request->claimant_code)."'")
							->orderBy('updated_at','Desc')->get();
		return Datatables::of($clparamccounts)
		->editColumn('updated_at', function ($clparamccounts) {
			return formatDate($clparamccounts->updated_at);
		})
		->make(true);
	}

	public function claimant_audit_log($user,$process,$new,$old,$claimant_code){


		
			$old_data = json_encode($old);
			$new_data = json_encode($new);

			$new = new Claimant_log;
			$new->changed_by = $user;
			$new->process = $process;
			$new->old_values =$old_data;
			$new->new_values = $new_data;
			$new->claimant_id = $claimant_code;
			$new->save();

	}

	//get changed fields,old data and new data 
	
	public function editbankdetails(Request $request){
	
		try {
			//code...
			$oldclaimantdets = Clparam::where('claimant_code',$request->claimant_code)->first();
		
			##update bank details in clparam
			$claimant_dets = Clparam::where('record_type',$oldclaimantdets->record_type)->where('code',$oldclaimantdets->code)->first();
			$claimant_dets->payment_mode = trim($request->payment_mode);
			$claimant_dets->bank_code = trim($request->bank);
			$claimant_dets->bank_branch_code = trim($request->bank_branch);
			$claimant_dets->bank_account_name = trim($request->account_name);
			$claimant_dets->bank_account_no = trim($request->account_number);
			
			$claimant_dets->save();

			$mm = $claimant_dets->getChanges();

			$user = Auth::user()->user_name;
			$process = 'Update Bank Details';
			$claimant_code = $request->claimant_code;

			 ###log changes to the new logs table 
			 $changed_data = $claimant_dets->getAttributes();
			 $process_slug = 'service-provider-onboarding';
			 $activity_slug = 'update';
			 $unique_item = $claimant_code;
			 $old_data = $oldclaimantdets;
			 $ip =$request->ip();


			##update banks in claimant banks
			$clientbank =PartnerBank::where('partner_number',$request->claimant_code)->update([
				'bank_code' => trim($request->bank),
				'branch_code' => trim($request->bank_branch),
				'bank_account_no' => trim($request->account_number),
				'bank_account' => trim($request->account_number),
				'bank_account_name' => trim($request->account_number),

			]);
		
			$yy = $this->log_data($mm,$oldclaimantdets,$user,$process,$claimant_code);

			log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

			Session::flash('success,'.$request->name.' details has been updated');
			return redirect()->back();

		} catch (\Throwable $th) {
			//throw $th;
			Session::flash('error,'.$request->name.' details have not been updated');
			return redirect()->back();
		}

	}


	public function log_data($mm,$oldclaimantdets,$user,$process,$claimant_code){

		$old_data = array();
		$new_data = array();
		$fields =array();

		$count =0;

		foreach($mm as $key => $value){
	
			if (trim($value) != trim($oldclaimantdets->$key)){
				array_push($fields,$key);
				array_push($new_data,$value);
				array_push($old_data,$oldclaimantdets->$key);

				$count = $count+1;

			}
		}
		##merge and create json formats for old and new data 

		$old = array_combine($fields, $old_data);
		$new = array_combine($fields, $new_data);

		if($count > 0 ){
			$xx= $this->claimant_audit_log($user,$process,$new,$old,$claimant_code);

		}

	}


	public function editclaimantdetails(Request $request){
		try {
			$user = Auth::user()->user_name;
			##get original data 
			$oldclaimantdets = Clparam::where('claimant_code',$request->claimant_code)->first();
	
			##get new data
			$claimant_dets = Clparam::where('record_type',$oldclaimantdets->record_type)->where('code',$oldclaimantdets->code)->first();
			$claimant_dets->name = $request->name;
			// $claimant_dets->record_type = $request->record_type;
			$claimant_dets->claimant_type = $request->claimant_type;
			$claimant_dets->e_mail=$request->e_mail;
			$claimant_dets->identity_type=$request->id_type;
			$claimant_dets->id_number=$request->identity_number;
			$claimant_dets->pin_number = $request->pin_number;
			$claimant_dets->mobile = $request->mobile;
			$claimant_dets->updated_at = Carbon::now();
			$claimant_dets->updated_by = $user;
			$claimant_dets->save();

			// $claimant_dets = Clparam::where('record_type',$oldclaimantdets->record_type)->where('code',$oldclaimantdets->code)->update([
			// 	'name'=> $request->name,
			// 	'record_type'=>$request->record_type,
			// 	'claimant_type'=>$request->claimant_type,
			// 	'e_mail'=>$request->e_mail,
			// 	'id_no'=>$request->id_no,
			// 	'pin_number'=>$request->pin_number,
			// 	'mobile'=>$request->mobile
			// ]);
			
			// $conditionColumns = [
			// 	'claimant_code' => $request->record_type
			// ];
			// AuditActionEvent::dispatch((new Clparam)->getTable(),$conditionColumns,$action_type='UPDATE');

			$mm = $claimant_dets->getChanges();
			$process = 'Update Claimant Details';
			$claimant_code = $request->claimant_code;
			// $yy = $this->log_data($mm,$oldclaimantdets,$user,$process,$claimant_code);

			//log changes to the new logs table
			$changed_data = $claimant_dets->getAttributes();
			$process_slug = 'service-provider-onboarding';
			$activity_slug = 'update';
			$unique_item = $claimant_code;
			$old_data = $oldclaimantdets;
			$ip =$request->ip();

			log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

			Session::flash('success,'.$request->name.' details has been updated');
			return redirect()->back()->with('success', $request->name.' details has been updated');

		} catch (\Throwable $e) {
			
			Session::flash('error,'.$request->name.' failed to be updated');
			DB::rollback();
			return redirect()->back()->with('error', $request->name.' failed to be updated');
		}

	}

	public function addclaimant(Request $request){
		$key = 0; 
		$clmcorr = Clmcorr::Where('code','=',$request->record_type)->first();
		$curr_code=$this->getClparamCode($request->record_type);
		$claimant_code =$this->genClaimantCode($request->record_type,$curr_code);
		$pipcnam = Pipcnam::all()->first();

		
		try {
			$insert_clparam = new Clparam;

			$insert_clparam->code=$curr_code;
			$insert_clparam->slhead=$claimant_code;
			$insert_clparam->claimant_code=$claimant_code;
			$insert_clparam->claimant_type = $request->claimant_type;
			$insert_clparam->date_appointed=$this->todays_date;
			$insert_clparam->dola=$this->todays_date;
			$insert_clparam->offcd=$pipcnam->offcd;
			$insert_clparam->record_type=$request->record_type;
			$insert_clparam->name=$request->name;
			$insert_clparam->mobile=$request->mobile;
			$insert_clparam->e_mail=$request->e_mail;
			$insert_clparam->identity_type=$request->id_type;
			$insert_clparam->id_number=$request->identity_number;
			$insert_clparam->addr1=$request->addr1;
			$insert_clparam->pin_number=$request->pin_number;
			$insert_clparam->payment_mode=$request->payment_mode;
			$insert_clparam->bank_code=$request->bank_code;
			$insert_clparam->bank_branch_code=$request->bank_branch_code;
			$insert_clparam->bank_account_no=trim($request->bank_account_no);
			$insert_clparam->bank_account_name=$request->bank_account_name;
			$insert_clparam->status='ACTIVE';
			$insert_clparam->save();

			$data=$this->generateClaimantPartnerNo($request, $claimant_code);

			if($data['status']==2){

				return response()->json(['data' => $data]);

			}
					
			foreach ($request->bank_code as $key => $bank_code) {
				if($request->bank_code[$key] != null){
					##insert into partner bank
					addbank($claimant_code,$key,$request->bank_code[$key]
						,$request->bank_branch_code[$key],trim($request->bank_account_no[$key])
						,$request->bank_account_name[$key],$request->currency_type[$key] ,'SP','Y');
				}
			}
				
			// Session::flash('success',$clmcorr->name.' '.$request->name.' has been created');
			// return redirect()->back();

			$data = [
				'status' => 1,
				'message' => $clmcorr->name . ' ' . $request->name . ' has been created',
			];
	
			return response()->json(['data' => $data]);
	

		} catch (\Throwable $th) {

			// dd($th);
			// throw $th;
			DB::rollback();
			$data = [
				'status' => 0,
				'message' => 'Failed to create claimant ' . $request->name,
			];	
			return response()->json(['data' => $data]);
		}

	}

	public function generateClaimantPartnerNo($request, $claimant_code){
        // $id_number = $request->input('identity_number');

        $tp =  Clparam::select('claimant_type')->where('claimant_code',$claimant_code)->first();
        $partner_type = $tp->claimant_type;
        $partner_code = $claimant_code;
        $partner_group = 'sp';

        $getpartner = new IntergrationController;

        $resp = $getpartner->getPartnerData($partner_group,$partner_type,$partner_code);
        
        if ($resp->statusCode == 200) {
            $data = [
                'status' => 1,
                'message' => 'Partner number for: '.$claimant_code.' generated successfully'
            ];


        }elseif($resp->statusCode == 400){
            $data = [
                'status' => 2,
                'message' => 'Status Code: '.$resp->statusCode . ' Partner number for: '.$claimant_code.' failed to generate. Please try again later.'
            ];
            
        }else{
            $data = [
                'status' => 2,
                'message' => 'Partner number for: '.$claimant_code.' failed to generate. Please try again later.'
            ];
            
        }
        return $data;
        
    }

	public function addspbanks($claimant_code,$key,$bank_code,$bank_branch_code,$bank_account_no,$bank_account_name){
		try {
			//code...
			$clientbank = new PartnerBank;
			$clientbank->partner_number = $claimant_code; 
			$clientbank->item_no = $key + 1;
			$clientbank->bank_code=$bank_code;
			$clientbank->branch_code=$bank_branch_code;
			$clientbank->bank_account_no=$bank_account_no;
			$clientbank->bank_account=$bank_account_no;
			$clientbank->bank_account_name=$bank_account_name;
			$clientbank->in_use = 'Y';
			$clientbank->holder = 'SP';
			$clientbank->save();

		} catch (\Throwable $th) {
			//throw $th;
			DB::rollback();
			Session::flash('error','Failed to Add Bank');

		}
		
	}

	public function checkMandatoryDocsUpload($entityid, $contextid, $process_code, $dept){
		
		$upload_status = checkDocUploadStatus($entityid, $contextid, $process_code, $dept);
		
		return $upload_status;
       

	}

}
 
