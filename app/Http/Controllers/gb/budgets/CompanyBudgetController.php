<?php

namespace App\Http\Controllers\gb\budgets;

use App\Budget_channel;
use App\BudgetLevel;
use App\Budget_uw;
use App\Budget_uw_forecast;
use App\BudgetHierarchy;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Escalate_pol;
use App\Aimsuser_web;
use App\SendEmail;
use PhpOffice\PhpSpreadsheet\IOFactory;


use Session;
use DB;
use Illuminate\Support\Carbon;
// use Auth;
use DataTables;
use App\Models\Admin\CompanyProfile\CompanyProfile;
use App\Models\Admin\Configuration\CRMProcesses;
// use App\Models\Admin\Configuration\Approval_levels;
// use App\Models\Admin\Configuration\Approvers;
// use App\Models\Admin\Configuration\ApprovalMatrix;
// use App\Models\Admin\Configuration\Process_approval;
// use App\Models\Admin\Configuration\Approval_flow;
// use App\Models\Admin\Configuration\Approvals;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Aimsoft\UserManagement\Models\Permission;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

use App\Aims_process;
use App\Process_approval_dtl;
use App\Approval_level;
use App\Approval_matrix;
use App\Approver_matrix;
use App\User;
use App\Approvals;
use App\Approval_flow;
use App\Classes\Approvals\ApprovalsMgt;
use App\Classes\Approvals\ApprovalsPo;

class CompanyBudgetController extends Controller {
    /**
     * 
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    
    public function channelBudgets(Request $request){

        return view('gb.budgets.budgets', ['budget_id'=>$request->budget_id]);

    }
    
    public function budgetparams(Request $request){
        
        return view('gb.budgets.budgets', ['budget_id'=>$request->budget_id]);
        
    }
    
    //budegt parameters screen
    
    public function budgetlevels(Request $request){
        
        $tables = DB::select("SELECT table_name FROM user_tables ORDER BY table_name ASC");
        $categorys = DB::table('budget_category')->get();
        $bhlevels = DB::table('budget_hierarchy')->get();

        return view('gb.budgets.budgetparams', compact('categorys','bhlevels','tables'));
    }

    public function getcolumns(Request $request){

        $table = $request->table;
        $columns = Schema::getColumnListing($table);

        sort($columns); 
        return response()->json([
            'columns' => $columns
        ]);
    }

      //budegt parameters dt
    public function budgethierarchydt(Request $request){
        
        $data = DB::table('budget_hierarchy')
        ->where('status','A')
        ->get();

        return Datatables::Of($data)
        ->addColumn('action', function ($categ) {
            return '<a class="btn btn-xs" id="btn-edit"></a>';
        })
        ->make(true);
    }


    public function deletebudgethierarchy(Request $request){

        $data = DB::table('budget_hierarchy')->where('id',$request->id)->update([
            'status' => 'P'
        ]);
   
    }

    public function budgethierarchylevelsdt(Request $request){
        
        $data = DB::table('budget_hierarchy_levels')->get();

        return Datatables::Of($data)
        ->addColumn('action', function ($categ) {
            return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
        })
        ->make(true);
    }

    public function budgetcompanydt(Request $request){
        
        $data = DB::table('budget_company')
        ->where('deleted','N')
        // ->whereRaw("UPPER(status) LIKE '%ACTIVE%'")
        ->get();

        return Datatables::Of($data)
        ->addColumn('budget_amountp_pola', function ($row) {
            $amount1a = DB::table('budget_uw')
                ->where('budget_id', $row->budget_id)
                ->where('year', $row->year)
                ->where('level1', 0)
                ->where('businesstype', 'POL')
                ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum1a')
                ->first();

            return $amount1a->total_sum1a ?? 0;
        })
        ->addColumn('budget_amountr_rena', function ($row) {
            $amount1b = DB::table('budget_uw')
                ->where('budget_id', $row->budget_id)
                ->where('year', $row->year)
                ->where('level1', 0)
                ->where('businesstype', 'REN')
                ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum1b')
                ->first();
            
            return $amount1b->total_sum1b ?? 0; // Return 0 if the result is null
        })
        ->addColumn('actiona', function ($row) {
            return '<a class="btn btn-xs bg-danger" id="btn-edit" data-id="' . $row->budget_id . '"><i class="fa fa-trash" aria-hidden="true"></i> delete</a>';
        })
        ->rawColumns(['actiona']) // Ensure the action column is treated as raw HTML
        ->make(true);
    }


    public function budgetcompanypendingdt(Request $request)
    {
        $data = DB::table('budget_company')
            ->where('deleted','N')
            ->whereRaw("UPPER(status) LIKE '%PENDING%'")
            ->get();
    
        return Datatables::Of($data)
            ->addColumn('budget_amount_pol', function ($row) {
                $amount1a = DB::table('budget_uw')
                    ->where('budget_id', $row->budget_id)
                    ->where('year', $row->year)
                    ->where('level1', 0)
                    ->where('businesstype', 'POL')
                    ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum1a')
                    ->first();
                // dd($amount1a,$row->budget_id,$row->year);
                return $amount1a->total_sum1a ?? 0; // Return 0 if the result is null
            })
            ->addColumn('budget_amount_ren', function ($row) {
                $amount1b = DB::table('budget_uw')
                    ->where('budget_id', $row->budget_id)
                    ->where('year', $row->year)
                    ->where('level1', 0)
                    ->where('businesstype', 'REN')
                    ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum1b')
                    ->first();
                
                return $amount1b->total_sum1b ?? 0; // Return 0 if the result is null
            })
            ->addColumn('action', function ($categ) {
                return '<a class="btn btn-xs bg-danger" id="btn-edit"><i class="fa fa-trash" aria-hidden="true"></i> delete </a>';
            })
            ->make(true);
    }

    //save params 
    public function postbudgethierarchylevels(Request $request){


        $levelIds = $request->level_id;
        $bhcategoryIds = $request->bhcategory_id;

        $countBefore = count($bhcategoryIds);
        $uniqueCategoryIds = array_unique($bhcategoryIds);
        $countAfter = count($uniqueCategoryIds);
        $hasDuplicates = $countBefore !== $countAfter;

        if ($hasDuplicates) {
            $result = array("status" => 2);
            return $result;
        }

        $endElement = end($bhcategoryIds);

        
        $count = count($request->level_id);

        $descriptions = [];
        foreach ($bhcategoryIds as $categoryId) {
            $category = DB::table('budget_category')->where('category_id', $categoryId)->first();
            if ($category) {
                $description = $category->description;
                $descriptions[] = $description;
            }
        }
        
        $concatenatedString = implode('-', $descriptions);
        
        $data = DB::table('budget_hierarchy')->insert([
            'description' => $concatenatedString,
            'levels' => $count,
        ]);

        $max = DB::table('budget_hierarchy')->max('id');
        $objs = DB::table('budget_hierarchy')->where('id', $max)->pluck('levels');
        $value = intval($objs[0]);

        $bhdata = [];
        for ($i = 0; $i < $value; $i++) {
            $bhdata[] = [
                'hierarchy_id' => $max,
                'budget_level' => $levelIds[$i],
                'category_id' => $bhcategoryIds[$i]
            ];
        }
        DB::table('budget_hierarchy_levels')->truncate();
        DB::table('budget_hierarchy_levels')->insert($bhdata);
    }

    public function postbudgetcompanyform(Request $request){
     

        $exists = DB::table('budget_company')->where('status','PENDING')->where('deleted','N')->first();

        $user = Auth::user()->user_name;

        $refocus = DB::table('budget_company')
            ->where('status', 'ACTIVE')
            ->where('year', $request->budget_company_year)
            ->count();

        if($exists){
            $result = array("status" => 0);
            return $result;
        }

    
        $doc_type = DB::table('doctype')->where('doc_type', 'BGT')->first();


        $budget_id = DB::table('budget_company')->insert([
            'hierarchy_id' => $request->bcompany_id,
            'year' => $request->budget_company_year,
            'amount' => 0,
            'dola' => Carbon::now(),
            'refocus' => $request->period,
            'usr' => $user,
            'threshold' => $request->threshold,
            'email' => $request->email,
            'edit' => 'Y',
            'budget_id' => $doc_type->serial_no
        ]);
        
        $bus_types = [1, 2];

   
        $branches = DB::table('branch')->where('is_active', 'Y')->get();
        foreach($branches as $bra) {
            foreach($bus_types as $bus_type) {
                DB::table('budgettracker')->insert([
                    'budget_id' => $budget_id,
                    'branch' => $bra->branch,
                    'bus_type' => $bus_type 
                ]);
            }
        }
      
        
        DB::table('doctype')->where('doc_type', 'BGT')
        ->update(['serial_no' => $doc_type->serial_no + 1]);

        
        Session::Flash('success', 'Budget Added Successfully. Check Draft Budgets ');

    }

    public function posteditbudgetcompanyform(Request $request){

        $id = $request->edit_budget_idnew;
        $attributes = DB::table('budget_company')
                    ->where('budget_id',$id)
                    ->update([
                        'deleted' => 'Y',
                        'dola' => Carbon::now()
                    ]);
        Session::Flash('success', 'Budget Deleted Successfully');
        return redirect()->back();

    }
    
    
    public function getbudgetlevels(Request $request){
        
        $levels = BudgetLevel::all();
        return Datatables::Of($levels)
        ->addColumn('action', function ($categ) {
            return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
        })
        ->make(true);
    }

    public function approvebudget(Request $request){

        $budget_id = $request->approvebdgt;
        // dd($budget_id);

        $levels = BudgetLevel::all();
        return Datatables::Of($levels)
        ->addColumn('action', function ($categ) {
            return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
        })
        ->make(true);
    }
    
    public function postbudgetlevel(Request $request){
        $attributes = DB::table('budget_level')->insert([
            'level_id' => $request->level_id,
            'level_name' => $request->description,
        ]);
        Session::Flash('success', 'Category Level Added Successfully');
    }

    // budget categories 
    
    public function getbudgetcategories(Request $request){

        $levels = DB::table('budget_category')->get();
        return Datatables::Of($levels)
            ->addColumn('action', function ($categ) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->make(true);
    }

    public function  postbudgetcategories(Request $request){

        $attributes = DB::table('budget_category')->insert([
            'category_id' => $request->category_id,
            'description' => $request->description,
            'table' => $request->db_table,
            'column' => strtoupper($request->db_column),
        ]);
        Session::Flash('success', 'Category Level Added Successfully');
    }

    public function updatebudgetlevel(Request $request){
        // dd($request->all());
        
        $old_category_lev = trim($request -> old_level_id);
        $categlevel = DB::table('budget_category')->where('category_id', $old_category_lev)
                        ->update([
                            'category_id' => $request->edit_level_id,
                            'description' => $request->edit_descr,
                            'table' => $request->edit_db_table,
                            'column' => strtoupper($request->edit_db_column),
                        ]);
        Session::Flash('success', 'Budget Category Level Updated Successfully');
    }

     // batch escalation
     public function escalateBudget(Request $request) {
            // dd($request->all());
            $budget_id = number_format($request->esc_budget);
            
            try  {
                if ($budget_id !== null){
                    $sender_id =  Auth::user()->user_id;
                    $name =  Auth::user()->user_name;
                    $esc_to = $request->esc_to;
                    
                    $count = Escalate_pol::where('sent_by',$sender_id)->max('escalate_id');
                    $next = $count + 1;
                    $recieverdet = Aimsuser_web::where('user_id',$esc_to)->first();
                    
                    $reciever = trim($recieverdet->user_name);
                    $sent_id = trim($recieverdet->user_id);
                    $emailaddr  = $recieverdet->email;
                    $budget_id = number_format($request->esc_budget);

                    $escalate = new Escalate_pol ;
                    $escalate->escalate_id = $next;
                    $escalate->type = 'BDGUW';
                    $escalate->description = 'Budget APPROVAL';
                    $mess = "Kindly Approve this Budget <strong>'$amnts'</strong>.Thank You. ";
                    $category = 'Budget APPROVAL';
                    
                    $escalate->req_no =$budget_id;
                    $escalate->sent_to =$sent_id;
                    $escalate->sent_by =$sender_id;
                    $escalate->user_name = $name;
                    $escalate->created_at =  Carbon::now();
                    $escalate->save();

                    $sendemail = new SendEmail;
                    $sendemail->category = $category ;
                    $sendemail->receiver =$emailaddr;
                    $sendemail->message =$mess;
                    $sendemail->creator = $name;
                    $sendemail->save();
                }
                $res = ['status'=> 1];
            } catch (\Throwable $e) {
                $res = ['status' => 0];
            }
            return $res;
        }
     // batch escalation
     public function escalateBudgetEditing(Request $request) {

            $budget_id = number_format($request->esc_budget);
            
            try  {
                if ($budget_id !== null){
                    $sender_id =  Auth::user()->user_id;
                    $name =  Auth::user()->user_name;
                    $esc_to = $request->esc_to;
                    
                    $count = Escalate_pol::where('sent_by',$sender_id)->max('escalate_id');
                    $next = $count + 1;
                    $recieverdet = Aimsuser_web::where('user_id',$esc_to)->first();
                    
                    $reciever = trim($recieverdet->user_name);
                    $sent_id = trim($recieverdet->user_id);
                    $emailaddr  = $recieverdet->email;
                    $budget_id = number_format($request->esc_budget);

                    $escalate = new Escalate_pol ;
                    $escalate->escalate_id = $next;
                    $escalate->type = 'EBDGUW';
                    $escalate->description = 'Budget APPROVAL';
                    $mess = "Kindly Approve this Editing of Budget <strong>'$amnts'</strong>.Thank You. ";
                    $category = 'Budget APPROVAL';
                    
                    $escalate->req_no =$budget_id;
                    $escalate->sent_to =$sent_id;
                    $escalate->sent_by =$sender_id;
                    $escalate->user_name = $name;
                    $escalate->created_at =  Carbon::now();
                    $escalate->save();

                    $sendemail = new SendEmail;
                    $sendemail->category = $category ;
                    $sendemail->receiver =$emailaddr;
                    $sendemail->message =$mess;
                    $sendemail->creator = $name;
                    $sendemail->save();
                }
                $res = ['status'=> 1];
            } catch (\Throwable $e) {
                $res = ['status' => 0];
            }
            return $res;
        }

	 		// Session::Flash('success','Escalated successfully');
 

    public function validatebudgetcategories(Request $request){

        $lev = trim($request -> category_id);
        $id_exist = DB::table('budget_category')->where('category_id', $lev) -> exists();
        $result = array('status' => $id_exist);
        return $result;
    }

    public function lockBudget(Request $request){
        $budget_id = $request->budget_id;
            try  {
                if ($budget_id !== null){
                    $obj = DB::table('budget_company') 
                    ->where('budget_id', $budget_id)
                    ->update([
                        'locked' => 'Y',
                        'edit' => 'N',
                        'dola' => Carbon::now()
                    ]);

                    $count = Escalate_pol::where('req_no', $budget_id)
                    ->where('type', 'EBDGUW')
                    ->count();

                    if($count>0){

                        $checkexist_bgteditescalation = Escalate_pol::where('req_no', $budget_id)
                        ->where('type', 'EBDGUW')
                        ->delete();

                        $obj = DB::table('budget_company') 
                        ->where('budget_id', $budget_id)
                        ->increment('refocus', 1, [
                            'locked' => 'Y',
                            'edit' => 'N',
                            'dola' => Carbon::now()
                        ]);

                    }

                  
                }
                $res = ['status'=> 1];
            } catch (\Throwable $e) {
                $res = ['status' => 0];
            }
            return $res;
        }

    public function checkSetParams(Request $request) 
    {
        $allowed = "N";
        
        $allowed = Gate::allows('set-gl-parameters') ? "Y" : "N";

        // Return the result as a JSON response
        return response()->json(["status" => $allowed]);

    }

    //budget home 
    public function mainBudgets(Request $request){
        $budget_id = $request->budget_id;
        $parts = explode('/', $budget_id);
        $budgetid = $parts[0];


        if (!$budgetid){
           $budgetid= $request->edit_budget_id;
        }

        $levels = DB::table('budget_category')->get();

        // dd($levels);

        $obj = DB::table('budget_company')
                ->where('budget_id',$budgetid)        
                ->pluck('hierarchy_id');

        $obj2 = DB::table('budget_hierarchy_levels')
                ->where('hierarchy_id',$obj)        
                ->get('category_id');
        
        $obj3 = $obj2->pluck('category_id')->toArray();

        $drilldown = DB::table('budget_category')
                ->whereIn('category_id', $obj3)
                ->get();


        $budget = DB::table('budget_company')
           ->where('budget_id', $budgetid)
           ->select('hierarchy_id', 'budget_id','locked','running_budget')
           ->first();
           
        $hierarchyId = $budget->hierarchy_id;
        $budgetId = $budget->budget_id;
        $locked = $budget->locked;

        $obj=DB::table('budget_hierarchy_levels')->where('hierarchy_id',$hierarchyId)->get();
        $budgetLevels = [];
        $categoryIds = [];

        $permission = Permission::where('slug','approve-budget')->first();
        $authorize_users = $permission->users;
     
        foreach ($obj as $item) {
            $budgetLevels[] = $item->budget_level;
            $categoryIds[] = $item->budget_level;
        }

        $selectInputs = [];
        $categoryIdsToFetch = [];

        foreach ($categoryIds as $categoryId) {

            $levelInfo = DB::table('budget_hierarchy_levels')->where('budget_level', $categoryId)->orderBy('CATEGORY_ID', 'asc')->first();
            $categoryInfo = DB::table('budget_category')->where('CATEGORY_ID', $levelInfo->category_id)->orderBy('CATEGORY_ID', 'asc')->first();

            if (!$categoryInfo) {
                continue; 
            }
        
            $table = $categoryInfo->table;


            if ($table === 'INTERMEDIARY') {
                
                $results = DB::table('INTERMEDIARY a')
                    ->select('a.NAME as description', 'a.intermediary_number')
                    ->distinct()
                    ->get();
            } else {
                $results = DB::table($table)->select('*')->get();
            }
            

            
            $selectInputs[$categoryId] = $results;
            $categoryIdsToFetch[] = $levelInfo->category_id;
        }

        $obj3 = DB::table('budget_category')
        ->whereIn('category_id', $categoryIdsToFetch)
        ->get();
        


        $budgetno = DB::table('budget_company')
           ->where('budget_id', $budgetid)
           ->get();

        $chans = DB::table('dist_channel')->get();
        $brans = DB::table('branch')->get();

        
        $checkexist_escalation = Escalate_pol::where('req_no', $budgetid)
                                            ->where('type', 'BDGUW')
                                            ->orderBy('created_at','desc')
                                            ->first();
        
        $checkexist_bgteditescalation = Escalate_pol::where('req_no', $budgetid)
                                            ->where('type', 'EBDGUW')
                                            ->orderBy('created_at','desc')
                                            ->first();


        $totalPolSum = DB::table('budget_uw')
        ->where('budget_id', $budgetid)
        ->whereRaw("UPPER(REPLACE(BUSINESSTYPE, ' ', '')) LIKE '%POL%'")
        ->selectRaw('SUM(JANUARY) + SUM(FEBRUARY) + SUM(MARCH) + SUM(APRIL) + SUM(MAY) + SUM(JUNE) + SUM(JULY) + SUM(AUGUST) + SUM(SEPTEMBER) + SUM(OCTOBER) + SUM(NOVEMBER) + SUM(DECEMBER) as total_sum')
        ->first();

        $totalRenSum = DB::table('budget_uw')
        ->where('budget_id', $budgetid)
        ->whereRaw("UPPER(REPLACE(BUSINESSTYPE, ' ', '')) LIKE '%REN%'")
        ->selectRaw('SUM(JANUARY) + SUM(FEBRUARY) + SUM(MARCH) + SUM(APRIL) + SUM(MAY) + SUM(JUNE) + SUM(JULY) + SUM(AUGUST) + SUM(SEPTEMBER) + SUM(OCTOBER) + SUM(NOVEMBER) + SUM(DECEMBER) as total_sum')
        ->first();

        		//!approval 
		$user_id = trim(Auth::user()->user_id);
		$process = Aims_process::where('slug','receipt-reversals')->first();
		$process_code= trim($process->process_code);
	
		$process = Aims_process::with(['process_dtl',
				'approval_levels'
			])
			->where('process_code',trim($process_code))
			->first();
			
		// fetch approvals if any
        if($budget->running_budget == 'Y' && $budget->locked == 'Y'){

            $approval_dtl = Approvals::with('approval_flow')
                ->where('req_no',trim($budget_id))
                ->where('type', 'BGTREF')
                ->orderBy('date_created','DESC')
                ->first();

            $check_approval = Approvals::where('req_no',trim($budget_id))
                        ->where('type', 'BGTREF')
                        ->orderBy('date_created','DESC')
                        ->first();

        }else{

            $approval_dtl = Approvals::with('approval_flow')
                            ->where('req_no',trim($budget_id))
                            ->where('type', 'BGTAPR')
                            ->orderBy('date_created','DESC')
                            ->first();

            $check_approval = Approvals::where('req_no',trim($budget_id))
                        ->where('type', 'BGTAPR')
                        ->orderBy('date_created','DESC')
                        ->first();

        }



		if(isset($check_approval)){
			// $status = 'N';
			if($check_approval->status == 'A'){
				$status = 'A';
				
			}
			elseif($check_approval->status == 'R'){
				$status = 'R';
				$msg = 'Requisition Approval was rejected';
			}
			elseif($check_approval->status == 'P'){
				$status = 'P';
				$msg = 'Requisition has a Pending approval';
			}
		}

		$approval_status = $status ;
		$approval_msg = $msg;


        $currentDate = Carbon::now();
        $lastDayOfMonth = $currentDate->endOfMonth();
        $threeDaysBeforeEndOfMonth = $lastDayOfMonth->subDays(3);
        $isWithinThreeDays = $currentDate->lte($threeDaysBeforeEndOfMonth);

        if ($isWithinThreeDays) {
            $forecast_bgt = 'Y';
        } else {
            $forecast_bgt = 'N';
        }
        


        return view('gb.budgets.uwbudget',compact('locked','checkexist_escalation','chans','brans','levels','budgetLevels','selectInputs','budgetId','authorize_users','budgetno','obj3','obj','budget_id','budgetid','drilldown','totalPolSum','totalRenSum','checkexist_bgteditescalation','process','approval_status','approval_msg','approval_dtl','status','budget','forecast_bgt'));
      
    }

    public function budgetapprove(Request $request) {

        $budget_id = $request->budgetaprvm; 

        DB::beginTransaction();
    
        try {
            DB::table('budget_company')
                ->where('budget_id','<>', $budget_id)
                ->update(['running_budget' => 'N']);

            DB::table('budget_company')
                ->where('budget_id', $budget_id)
                ->update(['running_budget' => 'Y']);

            DB::table('budget_company')
                ->where('budget_id', $budget_id)
                ->update(['status' => 'ACTIVE']);

            DB::table('budget_uw')
                ->where('budget_id','<>', $budget_id)
                ->update(['running_budget' => 'N']);

            DB::table('budget_uw')
                ->where('budget_id', $budget_id)
                ->update(['running_budget' => 'Y']);

            DB::table('escalate_pol')
                ->where('req_no', $budget_id)
                ->where('type', 'BDGUW')
                ->where('approved', null)
                ->update([  'approved' => 'Y',
                            'approved_by' => Auth::user()->user_id,
                            'approved_date' => Carbon::now()
                        ]);

            DB::commit();
            $res = ['status' => 1];

        } catch (\Throwable $th) {
            DB::rollback();
            $res = [
                'status' => 0,
                'message' => "An error occurred, please try again",
            ];
        }
        return $res;
    }

    public function ebudgetapprove(Request $request) {

        $budget_id = $request->ebudgetaprvm; 

        DB::beginTransaction();
    
        try {
            DB::table('budget_company')
                ->where('budget_id', $budget_id)
                ->update([
                    'edit' => 'Y',
                    'locked' => 'N',
                    'approved_by'=> Auth::user()->user_name
                ]);

            DB::table('budget_uw')
                ->where('budget_id', $budget_id)
                ->update([
                    'edit' => 'Y'
                ]);

            DB::table('escalate_pol')
                ->where('req_no', $budget_id)
                ->where('type', 'EBDGUW')
                ->where('approved', null)
                ->update([  'approved' => 'Y',
                            'approved_by' => Auth::user()->user_id,
                            'approved_date' => Carbon::now()
                        ]);

            DB::commit();
            $res = ['status' => 1];

        } catch (\Throwable $th) {
            DB::rollback();
            $res = [
                'status' => 0,
                'message' => "An error occurred, please try again",
            ];
        }
        return $res;
    }

    public function budgetestoption(Request $request){
        
        $selectedValues = $request->input('selectedValues');
        $level1 = null;
        $level2 = null;
        $level3 = null;
        $bgid= null;
        
        foreach ($selectedValues as $categoryId => $selectedOptions) {
            if (isset($selectedOptions[0]) && !empty($selectedOptions[0])) {
                $parts = explode(' - ', $selectedOptions[0]);
                switch ($categoryId) {
                    case 2:
                        $level1 = $parts[0];
                        $bgid = $parts[1];
                        break;
                    case 3:
                        $level2 = $parts[0];
                        break;
                    case 4:
                        $level3 = $parts[0];
                        break;
                }
            }
        }

        $new_business_amounts = $request->new_business_amounts;
        $renewal_business_amounts = $request->renewal_business_amounts;
    
        $result = DB::table('budget_company')
            ->where('budget_id', $bgid)
            ->select('year', 'hierarchy_id')
            ->first();


        $budget1 = DB::table('dist_channel')
            ->whereRaw("UPPER(description) LIKE '%$level1%'")
            ->pluck('dist_type')
            ->map(function ($value) {
                return intval($value);
            })
            ->first();

        $budget2 = DB::table('branch')
            ->whereRaw("UPPER(description) LIKE '%$level2%'")
            ->pluck('branch')
            ->map(function ($value) {
                return intval($value);
            })
            ->first();

        $budget3 = DB::table('class')
            ->whereRaw("UPPER(description) LIKE '%$level3%'")
            ->pluck('class')
            ->map(function ($value) {
                return intval($value);
            })
            ->first();

        $months = [
            'january', 'february', 'march', 'april', 'may', 'june',
            'july', 'august', 'september', 'october', 'November', 'December'
        ];
        
        $insertData = [];
        
        foreach ($months as $index => $month) {
            $amount = $new_business_amounts[$index];
            $insertData[$month] = $amount;
        }
        
        $insertData['budget_id'] = $bgid;
        $insertData['businesstype'] = "POL";
        $insertData['year'] = $result->year;
        $insertData['level1'] = 0;
        $insertData['level2'] =$budget1;
        $insertData['level3'] =$budget2;
        $insertData['level4'] =$budget3;
        
        $insertData2 = [];
        
        foreach ($months as $index => $month) {
            $amount = $renewal_business_amounts[$index];
            $insertData2[$month] = $amount;
        }
        
        $insertData2['budget_id'] = $bgid;
        $insertData2['businesstype'] = "REN";
        $insertData2['year'] = $result->year;
        $insertData2['level1'] = 0;
        $insertData2['level2'] =$budget1;
        $insertData2['level3'] =$budget2;
        $insertData2['level4'] =$budget3;
        
        // dd($insertData,$insertData2);
        DB::table('budget_uw')->insert($insertData);
        DB::table('budget_uw')->insert($insertData2);
    }

    public function postbudgetestimate(Request $request){
        
            $attributes = DB::table('budget_uw')->insert([
                'year' => $request->est_year,
                'january' =>  $request->january,
                'february' =>  $request->february,
                'march' =>  $request->march,
                'april' =>  $request->april,
                'may' =>  $request->may,
                'june' =>  $request->june,
                'july' =>  $request->july,
                'august' =>  $request->august,
                'september' =>  $request->september,
                'october' =>  $request->october,
                'november' =>  $request->november,
                'december' =>  $request->december,
            ]);

        Session::Flash('success', 'Estimate Added Successfully');
    }

    //main datatable
    public function maindatatable(Request $request){

        $selectedValues = $request->selectedValues;

        $level1 = null;
        $level2 = null;
        $level3= null;
        $level4= null;
        $bgid= $request->checkbgtid;

        $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
        $sections = explode("-", $hierarchy->description);
        $levels = $sections;

        

        foreach ($selectedValues as $categoryId => $selectedOptions) {

                $parts = $selectedOptions[0];
                switch ($categoryId) {
                    case 1:
                        $level1 = $parts;
                        break;
                    case 2:
                        $level2 = $parts;
                        break;
                    case 3:
                        $level3 = $parts;
                        break;
                    case 4:
                        $level4 = $parts;
                        break;
                    case 5:
                        $level5 = $parts;
                        break;
                }

        }

   

        $result = DB::table('budget_company')
            ->where('budget_id', $bgid)
            ->select('year', 'hierarchy_id')
            ->first();
 

            if (count($levels) > 0) {
                if ($levels[0] == "BRANCH") {

                    $budget1 = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level2%'")
                    ->pluck('branch')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();


                }
            }


            if (count($levels) > 1) { 

                if ($levels[1] == "INTERMEDIARY") {
                    
                    $budget2 = DB::table('intermediary')
                        ->whereRaw("UPPER(REPLACE(name, ' ', '')) LIKE '%$level3%'")
                        ->pluck('intermediary_number')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
            }

            if (count($levels) > 2 ) { 

                if ($levels[2] == "DEPARTMENTS") {
                    $budget3 = DB::table('dept')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level4%'")
                        ->pluck('dept')
                        ->map(function ($value) {
                            return intval($value);
                        });
                }
                   
                
            }

            if (count($levels) > 3 ) { 

                if ($levels[3] == "CLASS") {
                    $budget4 = DB::table('dept')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level5%'")
                        ->pluck('dept')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
                   
                
            }

        $budget3 = $budget3??null;


        $levels = DB::table('budget_uw')
                    ->where('budget_id',$bgid)
                    ->where('level2',$budget1)
                    ->where('level3',$budget2)
                    ->where('level4',$budget3)
                    // ->where('level5',$budget4)
                    ->get();

                    // dd($levels);

        return Datatables::of($levels)
                ->make(true);
       

    }
    //main datatable
    public function fmaindatatable(Request $request){

        $selectedValues = $request->selectedValues;


        $level1 = null;
        $level2 = null;
        $level3= null;
        $level4= null;
        $bgid= $request->checkbgtid;

        $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
        $sections = explode("-", $hierarchy->description);
        $levels = $sections;

        

        foreach ($selectedValues as $categoryId => $selectedOptions) {

                $parts = $selectedOptions[0];
                switch ($categoryId) {
                    case 1:
                        $level1 = $parts;
                        break;
                    case 2:
                        $level2 = $parts;
                        break;
                    case 3:
                        $level3 = $parts;
                        break;
                    case 4:
                        $level4 = $parts;
                        break;
                    case 5:
                        $level5 = $parts;
                        break;
                }

        }

   

        $result = DB::table('budget_company')
            ->where('budget_id', $bgid)
            ->select('year', 'hierarchy_id')
            ->first();
   

            if (count($levels) > 0) {
                if ($levels[0] == "BRANCH") {

                    $budget1 = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level2%'")
                    ->pluck('branch')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();


                }
            }


            if (count($levels) > 1) { 

                if ($levels[1] == "INTERMEDIARY") {
                    
                    $budget2 = DB::table('intermediary')
                        ->whereRaw("UPPER(REPLACE(name, ' ', '')) LIKE '%$level3%'")
                        ->pluck('intermediary_number')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
            }

            if (count($levels) > 2 ) { 

                if ($levels[2] == "DEPARTMENTS") {
                    $budget3 = DB::table('dept')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level4%'")
                        ->pluck('dept')
                        ->map(function ($value) {
                            return intval($value);
                        });
                }
                   
                
            }

        $budget3 = $budget3??null;
   

        $levels = DB::table('budget_uw_forecast')
                    ->where('budget_id',$bgid)
                    ->where('level2',$budget1)
                    ->where('level3',$budget2)
                    ->where('level4',$budget3)
                    // ->where('level5',$budget4)
                    ->get();

        return Datatables::of($levels)
                ->make(true);
       

    }

    // datatable update budget from the datatable
    public function updatebudgtesdt(Request $request){

        $data = json_decode($request->input('updatedCells'), true);

        $level1 = null;
        $level2 = null;
        $level3 = null;
        $bgid= null;
        $values = $request->input('values');

        foreach ($values as $categoryId => $selectedOptions) {
            if (isset($selectedOptions[0]) && !empty($selectedOptions[0])) {
                $parts = explode(' - ', $selectedOptions[0]);
                switch ($categoryId) {
                    case 2:
                        $level1 = $parts[0];
                        $bgid = $parts[1];
                        break;
                    case 3:
                        $level2 = $parts[0];
                        break;
                    case 4:
                        $level3 = $parts[0];
                        break;
                }
            }
        }

        $result = DB::table('budget_company')
            ->where('budget_id', $bgid)
            ->select('year', 'hierarchy_id')
            ->first();

        $year = (int) $result->year;
        
        $budget1 = DB::table('dist_channel')
            ->whereRaw("UPPER(description) LIKE '%$level1%'")
            ->pluck('dist_type')
            ->map(function ($value) {
                return intval($value);
            })
            ->first();

        $budget2 = DB::table('branch')
            ->whereRaw("UPPER(description) LIKE '%$level2%'")
            ->pluck('branch')
            ->map(function ($value) {
                return intval($value);
            })
            ->first();

        $budget3 = DB::table('class')
            ->whereRaw("UPPER(description) LIKE '%$level3%'")
            ->pluck('class')
            ->map(function ($value) {
                return intval($value);
            })
            ->first();

        $recordpol = DB::table('budget_uw')
            ->where('budget_id', $bgid)
            ->where('businesstype', 'POL')
            ->where('year', $year)
            ->where('level2', $budget1)
            ->where('level3', $budget2)
            ->where('level4', $budget3)
            ->first();
    
        if (!$recordpol) {
            $polData = [
                'budget_id' => $bgid,
                'businesstype' => 'POL',
                'year' => $year,
                'level2' => $budget1,
                'level3' => $budget2,
                'level4' => $budget3,
            ];

            DB::table('budget_uw')->insert($polData);
        }

        $recordren = DB::table('budget_uw')
            ->where('budget_id', $bgid)
            ->where('businesstype', 'REN')
            ->where('year', $year)
            ->where('level2', $budget1)
            ->where('level3', $budget2)
            ->where('level4', $budget3)
            ->first();

        if (!$recordren) {
            $renData = [
                'budget_id' => $bgid,
                'businesstype' => 'REN',
                'year' => $year,
                'level2' => $budget1,
                'level3' => $budget2,
                'level4' => $budget3,
            ];
            
            DB::table('budget_uw')->insert($renData);
        }

        foreach ($data as $item) {
            $rowIndex = $item['rowIndex'];
            $cellIndex = $item['cellIndex'];
            $cellValue = $item['cellValue'];
    
            $column = null;
            switch ($cellIndex) {
                case 1:
                    $column = 'january';
                    break;
                case 2:
                    $column = 'february';
                    break;
                case 3:
                    $column = 'march';
                    break;
                case 4:
                    $column = 'april';
                    break;
                case 5:
                    $column = 'may';
                    break;
                case 6:
                    $column = 'june';
                    break;
                case 7:
                    $column = 'july';
                    break;
                case 8:
                    $column = 'august';
                    break;
                case 9:
                    $column = 'september';
                    break;
                case 10:
                    $column = 'october';
                    break;
                case 11:
                    $column = 'november';
                    break;
                case 12:
                    $column = 'december';
                    break;
            }
    
            if ($column !== null) {
                $businesstype = $rowIndex === 1 ? 'POL' : ($rowIndex === 2 ? 'REN' : null);
                if ($businesstype !== null) {
                    if ($businesstype === 'POL') {
                        DB::table('budget_uw')
                            ->where('budget_id', $bgid)
                            ->where('businesstype', 'POL')
                            ->where('year', $year)
                            ->where('level2', $budget1)
                            ->where('level3', $budget2)
                            ->where('level4', $budget3)
                            ->update([$column => $cellValue]);
                        }else{
                            DB::table('budget_uw')
                            ->where('budget_id', $bgid)
                            ->where('businesstype', 'REN')
                            ->where('year', $year)
                            ->where('level2', $budget1)
                            ->where('level3', $budget2)
                            ->where('level4', $budget3)
                            ->update([$column => $cellValue]);
                        }
                }
            }
        }
    }

    public function fetchamount(Request $request){

        $selectedValues = $request->fetchvalues['fetchvalues'];
        $result = $request->fetchvalues['checkbgtid'];

        $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
        $sections = explode("-", $hierarchy->description);
        $levels =  $sections;


        $level1 = null;
        $level2 = null;
        $level3 = null;
        $level4 = null;
        $level5 = null;
        $bgid= $request->fetchvalues['checkbgtid'];

        

        foreach ($selectedValues as $categoryId => $selectedOptions) {

                $parts = $selectedOptions[0];
                switch ($categoryId) {
                    case 1:
                        $level1 = $parts;
                        break;
                    case 2:
                        $level2 = $parts;
                        break;
                    case 3:
                        $level3 = $parts;
                        break;
                    case 4:
                        $level4 = $parts;
                        break;
                    case 5:
                        $level5 = $parts;
                        break;
                }

        }

   

        $result = DB::table('budget_company')
            ->where('budget_id', $bgid)
            ->select('year', 'hierarchy_id')
            ->first();
   

            if (count($levels) > 0) {
                if ($levels[0] == "BRANCH") {

                    $valueData0 = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level2%'")
                    ->pluck('branch')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();


                } elseif ($levels[0] == "PRINCIPAL OFFICER") {

                    $fileN = str_replace(' ', '', strtoupper($level2));
                    $user = DB::table('aimsusers')->whereRaw("UPPER(REPLACE(NAME, ' ', '')) LIKE '%$fileN%'")->first();
                   

                    $valueData0 =$user->user_id;
                }
            }


            if (count($levels) > 1) { 

                if ($levels[1] == "INTERMEDIARY") {
                    $valueData1 = DB::table('intermediary')
                        ->whereRaw("UPPER(REPLACE(name, ' ', '')) LIKE '%$level3%'")
                        ->pluck('intermediary_number')
                        ->map(function ($value) {
                            return strval($value);
                        })
                        ->first();
                }
            }

            if (count($levels) > 2 ) { 

                if ($levels[2] == "DEPARTMENTS") {
                    $valueData2 = DB::table('dept')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level4%'")
                        ->pluck('dept')
                        ->map(function ($value) {
                            return intval($value);
                        });
                }
                   
                
            }

            if (count($levels) > 3 ) { 

                if ($levels[3] == "CLASS") {
                    $valueData3 = DB::table('dept')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level5%'")
                        ->pluck('dept')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
                   
                
            }



        $valueData3 = $valueData3??null;


        $result = DB::table('budget_company')
            ->where('budget_id', $bgid)
            ->select('year', 'hierarchy_id')
            ->first();

        $year = (int) $result->year;

        $amount1a = DB::table('budget_uw')
            ->where('budget_id', $bgid)
            ->where('year', $year)
            ->where('level2', $valueData0)
            ->where('businesstype', 'POL')
            ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum1a')
            ->first();


        $amount1b = DB::table('budget_uw')
            ->where('budget_id', $bgid)
            ->where('year', $year)
            ->where('level2', $valueData0)
            ->where('businesstype', 'REN')
            ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum1b')
            ->first();
        
        $amount2a =  DB::table('budget_uw')
            ->where('budget_id', $bgid)
            ->where('year', $year)
            ->where('level2', $valueData0)
            ->where('level3', $valueData1)
            ->where('businesstype', 'POL')
            ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum2a')
            ->first();
        
        $amount2b =  DB::table('budget_uw')
            ->where('budget_id', $bgid)
            ->where('year', $year)
            ->where('level2', $valueData0)
            ->where('level3', $valueData1)
            ->where('businesstype', 'REN')
            ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum2b')
            ->first();

        $amount3a = DB::table('budget_uw')
            ->where('budget_id', $bgid)
            ->where('year', $year)
            ->where('level2', $valueData0)
            ->where('level3', $valueData1)
            ->where('level4', $valueData2)
            ->where('businesstype', 'POL')
            ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum3a')
            ->first();

        $amount3b = DB::table('budget_uw')
            ->where('budget_id', $bgid)
            ->where('year', $year)
            ->where('level2', $valueData0)
            ->where('level3', $valueData1)
            ->where('level4', $valueData2)
            ->where('businesstype', 'REN')
            ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum3b')
            ->first();


        $amount4a = DB::table('budget_uw')
            ->where('budget_id', $bgid)
            ->where('year', $year)
            ->where('level2', $valueData0)
            ->where('level3', $valueData1)
            ->where('level4', $valueData2)
            ->where('level5', $valueData3)
            ->where('businesstype', 'POL')
            ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum4a')
            ->first();

    
        $amount4b = DB::table('budget_uw')
            ->where('budget_id', $bgid)
            ->where('year', $year)
            ->where('level2', $valueData0)
            ->where('level3', $valueData1)
            ->where('level4', $valueData2)
            ->where('level5', $valueData3)
            ->where('businesstype', 'REN')
            ->selectRaw('SUM(COALESCE(JANUARY, 0) + COALESCE(FEBRUARY, 0) + COALESCE(MARCH, 0) + COALESCE(APRIL, 0) + COALESCE(MAY, 0) + COALESCE(JUNE, 0) + COALESCE(JULY, 0) + COALESCE(AUGUST, 0) + COALESCE(SEPTEMBER, 0) + COALESCE(OCTOBER, 0) + COALESCE(NOVEMBER, 0) + COALESCE(DECEMBER, 0)) AS total_sum4b')
            ->first();

       
        return response()->json([$amount1a,$amount1b, $amount2a,$amount2b, $amount3a,$amount3b,$amount4a, $amount4b ]);
    }

    public function companybudget(Request $request){
        
        $bgid = $request->budgetId;
        
        $amount1 = DB::table('budget_company')
            ->where('budget_id', $bgid)
            ->pluck('amount');

        return response()->json([$amount1]);
    }

    public function budgetdrilldown(Request $request){
        // dd($request->all());
        $budget_id = $request->input('bgtid');
        $obj = DB::table('budget_company')
                ->where('budget_id',$budget_id)        
                ->pluck('hierarchy_id');

        $obj2 = DB::table('budget_hierarchy_levels')
                ->where('hierarchy_id',$obj)        
                ->get('category_id');
        
        $obj3 = $obj2->pluck('category_id')->toArray();

        $obj3 = DB::table('budget_category')
                ->whereIn('category_id', $obj3)
                // ->select('description') 
                ->get();
                
        // dd($obj3);
        return view('gb.budgets.budgetdrilldown',compact('obj3','obj','budget_id'));
        
    }

    public function cbgtbudgetdrillpol(Request $request){

        
        $data = DB::table('budget_summary_view')
                ->selectRaw('MONTH AS month, SUM(AMOUNT) AS estimate,BUSINESSTYPE AS businesstype,BUDGET_ID AS budget_id,YEAR AS year')
                ->where('budget_id', $request->budget_val)
                ->where('businesstype', 'POL')
                ->groupBy('month','businesstype','budget_id','year')
                ->orderByRaw("CASE 
                WHEN MONTH = '1' THEN 1 
                WHEN MONTH = '2' THEN 2 
                WHEN MONTH = '3' THEN 3 
                WHEN MONTH = '4' THEN 4 
                WHEN MONTH = '5' THEN 5 
                WHEN MONTH = '6' THEN 6 
                WHEN MONTH = '7' THEN 7 
                WHEN MONTH = '8' THEN 8 
                WHEN MONTH = '9' THEN 9 
                WHEN MONTH = '10' THEN 10 
                WHEN MONTH = '11' THEN 11 
                ELSE 12 
            END")
            ->get();

        foreach ($data as $item) {
            $performance = DB::table('debitmast')
            ->select(DB::raw('SUM(GROSS_AMOUNT) as total'))
            ->where('debitmast.ACCOUNT_YEAR', $item->year)
            ->where('debitmast.ACCOUNT_MONTH', $item->month)
            ->first();

            $performance0 = DB::table('debitmastdtl')
            ->select(DB::raw('SUM(GROSS_AMOUNT) as total'))
            ->where('debitmastdtl.ACCOUNT_YEAR', $item->year-1)
            ->where('debitmastdtl.ACCOUNT_MONTH', $item->month)
            ->first();
        
            $item->performance = $performance->total??0;

            $item->performance0 = $performance0->total??0;

            $item->variance = $item->performance - $item->estimate;

            $item->perfom_aginst_bgt = ($item->performance/$item->estimate)*100;


            $item->month_name = Carbon::createFromDate(null, $item->month ?? 1, 1)->format('F');
        }


        return datatables::of($data)->make(true);
        
    }

    public function cbgtbudgetdrillren(Request $request){
        // dd($request->all());
        $data = DB::table('budget_summary_view')
                ->selectRaw('MONTH AS month, SUM(AMOUNT) AS estimate,BUSINESSTYPE AS businesstype,BUDGET_ID AS budget_id,YEAR AS year')
                ->where('budget_id', $request->budget_val)
                ->where('businesstype', 'REN')
                ->groupBy('month','businesstype','budget_id','year')
                ->orderByRaw("CASE 
                WHEN MONTH = '1' THEN 1 
                WHEN MONTH = '2' THEN 2 
                WHEN MONTH = '3' THEN 3 
                WHEN MONTH = '4' THEN 4 
                WHEN MONTH = '5' THEN 5 
                WHEN MONTH = '6' THEN 6 
                WHEN MONTH = '7' THEN 7 
                WHEN MONTH = '8' THEN 8 
                WHEN MONTH = '9' THEN 9 
                WHEN MONTH = '10' THEN 10 
                WHEN MONTH = '11' THEN 11 
                ELSE 12 
            END")
            ->get();

                
        foreach ($data as $item) {
            $performance = DB::table('debitmast')
            ->select(DB::raw('SUM(GROSS_AMOUNT) as total'))
            ->where('debitmast.ACCOUNT_YEAR', $item->year)
            ->where('debitmast.ACCOUNT_MONTH', $item->month)
            ->first();

            $performance0 = DB::table('debitmastdtl')
            ->select(DB::raw('SUM(GROSS_AMOUNT) as total'))
            ->where('debitmastdtl.ACCOUNT_YEAR', $item->year-1)
            ->where('debitmastdtl.ACCOUNT_MONTH', $item->month)
            ->first();
        
            $item->performance = $performance->total??0;

            $item->performance0 = $performance0->total??0;

            $item->variance = $item->performance - $item->estimate;

            $item->perfom_aginst_bgt = ($item->performance/$item->estimate)*100;

            $item->month_name = Carbon::createFromDate(null, $item->month ?? 1, 1)->format('F');
        }


        return datatables::of($data)->make(true);
        
    }

    public function budgetdrilldt(Request $request){

        $obj1 = $request->description1; // 2 for branch / 3 for channel / 4 for class   12 for depat
        
        $obj2 = $request->description2; // 
        $obj3 = $request->hirerachy_val;
        $obj4 = $request->budget_val;
        $hierachy_id = (int)trim($obj3, '[]"');
        $bdid = (int)trim($obj4, '[]"');

        $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
        $sections = explode("-", $hierarchy->description);
        $levels = $sections;

        $categoryInfo = DB::table('budget_category')->where('category_id', $obj1)->first();
        $table = $categoryInfo->table;
        $column = $categoryInfo->column;
        $cat_id = $categoryInfo->category_id;

        if($table == 'INTERMEDIARY'){

            $fileN = str_replace(' ', '', strtoupper($obj2));

            $user = DB::table('intermediary')->whereRaw("UPPER(REPLACE(NAME, ' ', '')) LIKE '%$fileN%'")->first();

            $results = DB::table($table)->where('intermediary_number', 'like', "%$user->intermediary_number%")->get($column)->first();
        }else{
            $results = DB::table($table)->where('description', 'like', "%$obj2%")->get($column)->first();
        }

        $valuesArray = array();

        foreach ($results as $key => $value) {
            $valuesArray[] = $value;
        }
     

        
        $budgetlevel = DB::table('BUDGET_HIERARCHY_LEVELS')->where('category_id', $obj1)->first();
        $budgetcategoryInfo = DB::table('budget_category')->where('category_id', $budgetlevel->category_id)->first();


        if ($budgetlevel->budget_level == 2) {
            $data = DB::table('budget_summary_view')
            ->selectRaw('MONTH AS month, SUM(AMOUNT) AS estimate,BUSINESSTYPE AS businesstype,BUDGET_ID AS budget_id,YEAR AS year')
            ->where('budget_id', $bdid)
            ->where('businesstype', 'REN')
            ->whereIn('level2', $valuesArray)
            ->groupBy('month','businesstype','budget_id','year')
            ->orderByRaw("CASE 
                                WHEN MONTH = '1' THEN 1 
                                WHEN MONTH = '2' THEN 2 
                                WHEN MONTH = '3' THEN 3 
                                WHEN MONTH = '4' THEN 4 
                                WHEN MONTH = '5' THEN 5 
                                WHEN MONTH = '6' THEN 6 
                                WHEN MONTH = '7' THEN 7 
                                WHEN MONTH = '8' THEN 8 
                                WHEN MONTH = '9' THEN 9 
                                WHEN MONTH = '10' THEN 10 
                                WHEN MONTH = '11' THEN 11 
                                ELSE 12 
                            END")
            ->get();

        

            $column = ($budgetcategoryInfo->table == "BRANCH") ? "BRANCH" : (($budgetcategoryInfo->table == "BRANCH") ? "BRANCH" : "PRINCIPAL_OFFICER");


            foreach ($data as $item) {

                if($column == "BRANCH"){
                  
                    
                    $performance = DB::table('debitmast_history')
                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                    ->whereIn('BRANCH', $valuesArray)
                    ->where('ACCOUNT_YEAR', $item->year)
                    ->where('TYPE_OF_BUS', 2)
                    ->where('ACCOUNT_MONTH', $item->month)
                    ->first();

                    $performance0 = DB::table('debitmast_history')
                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                    ->whereIn('BRANCH', $valuesArray)
                    ->where('ACCOUNT_YEAR', $item->year -1)
                    ->where('TYPE_OF_BUS', 2)
                    ->where('ACCOUNT_MONTH', $item->month)
                    ->first();

            
                
                    $item->performance = $performance->total??0;

                    $item->performance0 = $performance0->total??0;

                    $item->variance = $item->performance - $item->estimate;

                    // Check if $item->estimate is not zero to avoid DivisionByZeroError
                    if ($item->estimate != 0) {
                        $item->perfom_aginst_bgt = ($item->performance/$item->estimate)*100;
                    } else {
                        // Handle the case where $item->estimate is zero
                        $item->perfom_aginst_bgt = 0; // or any other appropriate action
                    }
                

                }


                $item->month_name = Carbon::createFromDate(null, $item->month ?? 1, 1)->format('F');
            }
        }
         elseif ($budgetlevel->budget_level == 3) {
            $data = DB::table('budget_summary_view')
            ->selectRaw('MONTH AS month, SUM(AMOUNT) AS estimate,BUSINESSTYPE AS businesstype,BUDGET_ID AS budget_id,YEAR AS year')
            ->where('budget_id', $bdid)
            ->where('businesstype', 'REN')
            ->whereIn('level3', $valuesArray)
            ->groupBy('month','businesstype','budget_id','year')
            ->orderBy('month')
            ->get();
        
            $column = ($budgetcategoryInfo->table == "INTERMEDIARY") ? "INTERMEDIARY_NUMBER" : (($budgetcategoryInfo->table == "INTERMEDIARY") ? "INTERMEDIARY_NUMBER" : "CHANNEL");

            foreach ($data as $item) {

                if($column == "INTERMEDIARY_NUMBER"){
                    
                    
                    $performance = DB::table('debitmast_history')
                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                    ->whereIn('INTERMEDIARY_NUMBER', $valuesArray)
                    ->where('ACCOUNT_YEAR', $item->year)
                    ->where('TYPE_OF_BUS', 2)
                    ->where('ACCOUNT_MONTH', $item->month)
                    ->first();

                    $performance0 = DB::table('debitmast_history')
                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                    ->whereIn('INTERMEDIARY_NUMBER', $valuesArray)
                    ->where('ACCOUNT_YEAR', $item->year -1)
                    ->where('TYPE_OF_BUS', 2)
                    ->where('ACCOUNT_MONTH', $item->month)
                    ->first();

            
                
                    $item->performance = $performance->total??0;

                    $item->performance0 = $performance0->total??0;

                    $item->variance = $item->performance - $item->estimate;

                    // Check if $item->estimate is not zero to avoid DivisionByZeroError
                    if ($item->estimate != 0) {
                        $item->perfom_aginst_bgt = ($item->performance/$item->estimate)*100;
                    } else {
                        // Handle the case where $item->estimate is zero
                        $item->perfom_aginst_bgt = 0; // or any other appropriate action
                    }
 
                }                


                $item->month_name = Carbon::createFromDate(null, $item->month ?? 1, 1)->format('F');
            }
        }
        
        elseif ($budgetlevel->budget_level == 4) {
            $data = DB::table('budget_summary_view')
            ->selectRaw('MONTH AS month, SUM(AMOUNT) AS estimate,BUSINESSTYPE AS businesstype,BUDGET_ID AS budget_id,YEAR AS year')
            ->where('budget_id', $bdid)
            ->where('businesstype', 'REN')
            ->whereIn('level4', $valuesArray)
            ->groupBy('month','businesstype','budget_id','year')
            ->orderByRaw("CASE 
                                WHEN MONTH = '1' THEN 1 
                                WHEN MONTH = '2' THEN 2 
                                WHEN MONTH = '3' THEN 3 
                                WHEN MONTH = '4' THEN 4 
                                WHEN MONTH = '5' THEN 5 
                                WHEN MONTH = '6' THEN 6 
                                WHEN MONTH = '7' THEN 7 
                                WHEN MONTH = '8' THEN 8 
                                WHEN MONTH = '9' THEN 9 
                                WHEN MONTH = '10' THEN 10 
                                WHEN MONTH = '11' THEN 11 
                                ELSE 12 
                            END")
            ->get();
        
            $column = ($budgetcategoryInfo->table == "DEPT") ? "DEPT" : (($budgetcategoryInfo->table == "DEPT") ? "DEPT" : "BRANCH");

            foreach ($data as $item) {

                if($column == "DEPT"){
                    
                    
                    $performance = DB::table('debitmast_history')
                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                    ->whereIn('DEPT', $valuesArray)
                    ->where('ACCOUNT_YEAR', $item->year)
                    ->where('TYPE_OF_BUS', 2)
                    ->where('ACCOUNT_MONTH', $item->month)
                    ->first();

                    $performance0 = DB::table('debitmast_history')
                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                    ->whereIn('DEPT', $valuesArray)
                    ->where('ACCOUNT_YEAR', $item->year -1)
                    ->where('TYPE_OF_BUS', 2)
                    ->where('ACCOUNT_MONTH', $item->month)
                    ->first();

                
                    $item->performance = $performance->total??0;

                    $item->performance0 = $performance0->total??0;

                    $item->variance = $item->performance - $item->estimate;

                    // Check if $item->estimate is not zero to avoid DivisionByZeroError
                    if ($item->estimate != 0) {
                        $item->perfom_aginst_bgt = ($item->performance/$item->estimate)*100;
                    } else {
                        // Handle the case where $item->estimate is zero
                        $item->perfom_aginst_bgt = 0; // or any other appropriate action
                    }

 
                }               


                $item->month_name = Carbon::createFromDate(null, $item->month ?? 1, 1)->format('F');
            }
        }
        


        return datatables::of($data)->make(true);
            
    }

    public function budgetdrilldtpol(Request $request){

        $obj1 = $request->description1; // 2 for branch / 3 for channel / 4 for class   12 for depat
        
        $obj2 = $request->description2; // 
        $obj3 = $request->hirerachy_val;
        $obj4 = $request->budget_val;
        $hierachy_id = (int)trim($obj3, '[]"');
        $bdid = (int)trim($obj4, '[]"');

        $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
        $sections = explode("-", $hierarchy->description);

        $levels = $sections;

        $categoryInfo = DB::table('budget_category')->where('category_id', $obj1)->first();
        $table = $categoryInfo->table;
        $column = $categoryInfo->column;
        $cat_id = $categoryInfo->category_id;


        if($table == 'INTERMEDIARY'){

            $fileN = str_replace(' ', '', strtoupper($obj2));

            $user = DB::table('intermediary')->whereRaw("UPPER(REPLACE(NAME, ' ', '')) LIKE '%$fileN%'")->first();

            $results = DB::table($table)->where('INTERMEDIARY_NUMBER', 'like', "%$user->intermediary_number%")->get($column)->first();
         
    
        }else{

            $results = DB::table($table)->where('description', 'like', "%$obj2%")->get($column)->first();
        }
     

        $valuesArray = array();
  

        foreach ($results as $key => $value) {
            $valuesArray[] = $value;
        }
        
        $budgetlevel = DB::table('BUDGET_HIERARCHY_LEVELS')->where('category_id', $obj1)->first();
        $budgetcategoryInfo = DB::table('budget_category')->where('category_id', $budgetlevel->category_id)->first();



        if ($budgetlevel->budget_level == 2) {
            $data = DB::table('budget_summary_view')
            ->selectRaw('MONTH AS month, SUM(AMOUNT) AS estimate,BUSINESSTYPE AS businesstype,BUDGET_ID AS budget_id,YEAR AS year')
            ->where('budget_id', $bdid)
            ->where('businesstype', 'POL')
            ->whereIn('level2', $valuesArray)
            ->groupBy('month','businesstype','budget_id','year')
            ->orderByRaw("CASE 
                                WHEN MONTH = '1' THEN 1 
                                WHEN MONTH = '2' THEN 2 
                                WHEN MONTH = '3' THEN 3 
                                WHEN MONTH = '4' THEN 4 
                                WHEN MONTH = '5' THEN 5 
                                WHEN MONTH = '6' THEN 6 
                                WHEN MONTH = '7' THEN 7 
                                WHEN MONTH = '8' THEN 8 
                                WHEN MONTH = '9' THEN 9 
                                WHEN MONTH = '10' THEN 10 
                                WHEN MONTH = '11' THEN 11 
                                ELSE 12 
                            END")
            ->get();


            $column = ($budgetcategoryInfo->table == "BRANCH") ? "BRANCH" : (($budgetcategoryInfo->table == "BRANCH") ? "BRANCH" : "PRINCIPAL_OFFICER");

            foreach ($data as $item) {

                if($column == "BRANCH"){
                    

                    $performance = DB::table('debitmast_history')
                                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                                    ->whereIn('BRANCH', $valuesArray)
                                    ->where('ACCOUNT_YEAR', $item->year)
                                    ->where('ACCOUNT_MONTH', $item->month)
                                    ->where('TYPE_OF_BUS', 1)
                                    ->first();

                    
                 
           

                    $performance0 = DB::table('debitmast_history')
                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                    ->whereIn('BRANCH', $valuesArray)
                    ->where('ACCOUNT_YEAR', $item->year -1)
                    ->where('ACCOUNT_MONTH', $item->month)
                    ->where('TYPE_OF_BUS', 1)
                    ->first();
                
                    $item->performance = $performance->total??0;

                    $item->performance0 = $performance0->total??0;

                    $item->variance = $item->performance - $item->estimate;

                    // Check if $item->estimate is not zero to avoid DivisionByZeroError
                    if ($item->estimate != 0) {
                        $item->perfom_aginst_bgt = ($item->performance/$item->estimate)*100;
                    } else {
                        // Handle the case where $item->estimate is zero
                        $item->perfom_aginst_bgt = 0; // or any other appropriate action
                    }


                }               


                $item->month_name = Carbon::createFromDate(null, $item->month ?? 1, 1)->format('F');
            }
        }

         elseif ($budgetlevel->budget_level == 3) {
            $data = DB::table('budget_summary_view')
            ->selectRaw('MONTH AS month, SUM(AMOUNT) AS estimate,BUSINESSTYPE AS businesstype,BUDGET_ID AS budget_id,YEAR AS year')
            ->where('budget_id', $bdid)
            ->where('businesstype', 'POL')
            ->whereIn('level3', $valuesArray)
            ->groupBy('month','businesstype','budget_id','year')
            ->orderByRaw("CASE 
                                WHEN MONTH = '1' THEN 1 
                                WHEN MONTH = '2' THEN 2 
                                WHEN MONTH = '3' THEN 3 
                                WHEN MONTH = '4' THEN 4 
                                WHEN MONTH = '5' THEN 5 
                                WHEN MONTH = '6' THEN 6 
                                WHEN MONTH = '7' THEN 7 
                                WHEN MONTH = '8' THEN 8 
                                WHEN MONTH = '9' THEN 9 
                                WHEN MONTH = '10' THEN 10 
                                WHEN MONTH = '11' THEN 11 
                                ELSE 12 
                            END")
            ->get();
  
            $column = ($budgetcategoryInfo->table == "INTERMEDIARY") ? "INTERMEDIARY_NUMBER" : (($budgetcategoryInfo->table == "INTERMEDIARY") ? "INTERMEDIARY_NUMBER" : "CHANNEL");
       

            foreach ($data as $item) {

                if($column == "INTERMEDIARY_NUMBER"){
                  
                    $performance = DB::table('debitmast_history')
                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                    ->whereIn('INTERMEDIARY_NUMBER', $valuesArray)
                    ->where('ACCOUNT_YEAR', $item->year)
                    ->where('ACCOUNT_MONTH', $item->month)
                    ->where('TYPE_OF_BUS', 1)
                    ->first();

                    $performance0 = DB::table('debitmast_history')
                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                    ->whereIn('INTERMEDIARY_NUMBER', $valuesArray)
                    ->where('ACCOUNT_YEAR', $item->year -1)
                    ->where('ACCOUNT_MONTH', $item->month)
                    ->where('TYPE_OF_BUS', 1)
                    ->first();

            
                
                    $item->performance = $performance->total??0;

                    $item->performance0 = $performance0->total??0;

                    $item->variance = $item->performance - $item->estimate;

                    // Check if $item->estimate is not zero to avoid DivisionByZeroError
                    if ($item->estimate != 0) {
                        $item->perfom_aginst_bgt = ($item->performance/$item->estimate)*100;
                    } else {
                        // Handle the case where $item->estimate is zero
                        $item->perfom_aginst_bgt = 0; // or any other appropriate action
                    }
                            
 
                }

                $item->month_name = Carbon::createFromDate(null, $item->month ?? 1, 1)->format('F');
            }
        }
        
        elseif ($budgetlevel->budget_level == 4) {
            $data = DB::table('budget_summary_view')
            ->selectRaw('MONTH AS month, SUM(AMOUNT) AS estimate,BUSINESSTYPE AS businesstype,BUDGET_ID AS budget_id,YEAR AS year')
            ->where('budget_id', $bdid)
            ->where('businesstype', 'POL')
            ->whereIn('level4', $valuesArray)
            ->groupBy('month','businesstype','budget_id','year')
            ->orderByRaw("CASE 
                                WHEN MONTH = '1' THEN 1 
                                WHEN MONTH = '2' THEN 2 
                                WHEN MONTH = '3' THEN 3 
                                WHEN MONTH = '4' THEN 4 
                                WHEN MONTH = '5' THEN 5 
                                WHEN MONTH = '6' THEN 6 
                                WHEN MONTH = '7' THEN 7 
                                WHEN MONTH = '8' THEN 8 
                                WHEN MONTH = '9' THEN 9 
                                WHEN MONTH = '10' THEN 10 
                                WHEN MONTH = '11' THEN 11 
                                ELSE 12 
                            END")
            ->get();

        
        
            $column = ($budgetcategoryInfo->table == "DEPT") ? "DEPT" : (($budgetcategoryInfo->table == "DEPT") ? "DEPT" : "BRANCH");

            foreach ($data as $item) {

                if($column == "DEPT"){
                    
                    $performance = DB::table('debitmast_history')
                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                    ->whereIn('DEPT', $valuesArray)
                    ->where('ACCOUNT_YEAR', $item->year)
                    ->where('ACCOUNT_MONTH', $item->month)
                    ->where('TYPE_OF_BUS', 1)
                    ->first();


                    $performance0 = DB::table('debitmast_history')
                    ->select(DB::raw('SUM(NETT_AMOUNT) as total'))
                    ->whereIn('DEPT', $valuesArray)
                    ->where('ACCOUNT_YEAR', $item->year -1)
                    ->where('ACCOUNT_MONTH', $item->month)
                    ->where('TYPE_OF_BUS', 1)
                    ->first();

 
                    $item->performance = $performance->total??0;

                    $item->performance0 = $performance0->total??0;

                    $item->variance = $item->performance - $item->estimate;

                    if ($item->estimate != 0) {
                        $item->perfom_aginst_bgt = ($item->performance/$item->estimate)*100;
                    } else {
                        $item->perfom_aginst_bgt = 0; 
                    }


                }
                        


                $item->month_name = Carbon::createFromDate(null, $item->month ?? 1, 1)->format('F');
            }
        }
        
        return datatables::of($data)->make(true);
            
    }

    public function getseconddropdown(Request $request)
    {
        $desc = $request->description;
        $obj = DB::table('budget_category')
            ->where('category_id', $desc)
            ->select('table', 'column')
            ->first();
    
        // Check if the object is null before accessing its properties
        if (!$obj) {
            return response()->json([]);
        }
    
        $tableName = $obj->table;
        $columnName = $obj->column;
    
        // Check if table name or column name is empty
        if (!$tableName || !$columnName) {
            return response()->json([]);
        }
    
        if ($tableName == 'INTERMEDIARIES') {
            $items = DB::table('INTERMEDIARY_BRANCH as ib')
                ->join('INTERMEDIARIES as i', 'ib.INTERMEDIARY_NUMBER', '=', 'i.INTERMEDIARY_NUMBER')
                ->join('AIMSUSERS as a', 'i.PRINCIPAL_OFFICER', '=', 'a.USER_ID')
                ->select('a.NAME as description', 'i.PRINCIPAL_OFFICER')
                ->where('ib.STATUS', 'Y')
                ->distinct()
                ->get();
        } else {
            $items = DB::table($tableName)->get(); // Changed variable name to $items
        }
    
        return response()->json($items);
    }
    

    //hierarchy datatables
    public function gethierarcgy(Request $request){


        $hierarchy = BudgetHierarchy::all();

        return Datatables::Of($hierarchy)
            ->addColumn('action', function ($categ) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })

            ->editColumn('parent_level_id', function ($categ) {
                $level = trim($categ -> parent_level_id);
                $categlev = BudgetLevel::where('level_id', $level) -> first();
                return $categlev -> description;
            })

            ->editColumn('child_level_id', function ($categ) {
                $level = trim($categ -> child_level_id);
                $categlev = BudgetLevel::where('level_id', $level) -> first();
                return $categlev -> description;
            })

            ->make(true);
    }

    

    public function channelbudgetPerMonth(Request $request){
        $channel = $request->channel_id;

       
        $data = DB::table('budget_channel_pm_estimate')
                    ->get();

        return Datatables::of($data)
        ->editColumn('amount', function($d){
            return number_format($d->amount);
        })
        ->editColumn('channel', function($d){
            $channels = DB::table('channel_budget_params')
            ->where('id',$d->channel_id)
            ->first();
            return $channels->name;
        })
        ->make(true);
    }

    
    public function readCSV($csvFile)
    {
        // get the file to a variable
        $csv = array_map('str_getcsv', file($csvFile));

        // loop converting this to an assoc array
        array_walk($csv, function (&$a) use ($csv) {
            $a = array_combine($csv[0], $a);
            $csv = array_change_key_case($csv, CASE_LOWER);
        });

        # remove column header
        array_shift($csv);

        return $csv;
    }




    public function budgetupload(Request $request){

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try {

            $selectedValues = $request->selectedUploadValues;
            $budget_id = $request->budgetuploadid;
            $fileData = $request->file('budget');
            $businessType = $request->businesstype;
            $fileExtension = $fileData->getClientOriginalExtension();
            $fileName = array_slice(explode(".", $fileData->getClientOriginalName()), 0, -1);
            $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
            $sections = explode("-", $hierarchy->description);
            $levels = array_slice($sections, 0, -1);
            $level1 = null;
            $level2 = null;
            $level3 = null;
            $level4 = null;
            $bgid= $budget_id;

            $fileN = str_replace(' ', '', strtoupper($fileName[0]));

            $level2_val = DB::table('INTERMEDIARY_BRANCH as ib')
            ->join('INTERMEDIARIES as i', 'ib.INTERMEDIARY_NUMBER', '=', 'i.INTERMEDIARY_NUMBER')
            ->join('AIMSUSERS as a', 'i.PRINCIPAL_OFFICER', '=', 'a.USER_ID')
            ->select('a.user_id as principal_officer')
            ->where('ib.STATUS', 'Y')
            ->whereRaw("UPPER(REPLACE(a.NAME, ' ', '')) LIKE '%$fileN%'")
            ->distinct()
            ->first();
        

            // dd($level2_val->principal_officer,$fileName[0],$fileN);




            $selectedValues = json_decode($selectedValues, true);

            $year = DB::table('budget_company')
                    ->where('budget_id', $budget_id)
                    ->first();
        
            $yearValue = $year->year; 
            

            if ($fileExtension == 'xlsx') {
                $spreadsheet = IOFactory::load($fileData);
                $worksheets = $spreadsheet->getAllSheets();
            
                // Define an array of business types
                $businessTypes = ["POL", "REN"];
                $businessTypeIndex = 0; // Initialize the index to 0
            
                foreach ($worksheets as $worksheet) {
                    $channeldata = [];
                    $departmentdata = [];
                    $sheetName = null;
                    $branch = null;
                    $foundDepartment = false;
            
                    $sheetName = trim(preg_replace('/[\\[\\]:*?\\\\\/]/', '', $worksheet->getTitle()));
                    $sheetData = $worksheet->toArray();
            
                    $sheetName = str_replace(' ', '', strtoupper($sheetName));
            
                    $branch = DB::table('branch')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$sheetName%'")
                        ->pluck('branch')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
            
                    foreach ($sheetData as $value) {
                        if (empty(str_replace('"', '', $value[0])) || str_replace('"', '', $value[0]) == null || str_replace('"', '', $value[0]) == "DESCRIPTION") {
                            continue;
                        }
            
                        $desc = str_replace('"', '', $value[0]); 
                        $acctype = str_replace('"', '', $value[1]); 
                        $months = array_slice($value, 2, 12);
                        $months = array_map(function($month) {
                            return str_replace('"', '', $month);
                        }, $months);
            
                        if ($desc == 'CLASS') {
                            $foundDepartment = true;
                        } elseif ($desc == 'DESCRIPTION') {
                            $foundDepartment = false;
                        }
            
                        if ($foundDepartment) {
                            if ($acctype !== "CODE") {
                                $departmentdata[] = $desc . "-" . $acctype . "-" . implode('-', $months);
                            }
                        } else {
                            if ($acctype !== null && $desc !== "") {
                                $channeldata[] = $desc . "-" . $acctype . "-" . implode('-', $months);
                            }
                        }
                    }
            
                    foreach ($channeldata as $ddata) {
                        $explodedData = explode('-', $ddata);
                        $explodedData = array_map('trim', $explodedData);
            
                        // Check if the exploded data has the expected number of elements
                        if (count($explodedData) !== 14) {
                            continue;
                        }
            
                        foreach ($departmentdata as $item) {
                            $explodeddeptData = explode('-', $item);
                            $explodeddeptData = array_map('trim', $explodeddeptData);
            
                            // Check if the department data has the expected number of elements
                            if (count($explodeddeptData) !== 14) {
                                continue;
                            }
            
                            // Initialize the insert data array with common fields
                            $insertData = [
                                'budget_id' => $bgid,
                                'businesstype' => $request->bus_type,
                                'year' => $yearValue,
                                'level1' => 0,
                                'level2' => $level2_val->principal_officer,
                                'level3' => $branch,
                                'level4' => $explodedData[1],
                                'level5' => $explodeddeptData[1],
                            ];
            
                            // Calculate the budget for each month
                            $months = ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december'];
                            for ($i = 2; $i <= 13; $i++) {
                                $insertData[$months[$i - 2]] = (floatval($explodeddeptData[$i]) / 100) * floatval($explodedData[$i]);
                            }
            
                            // Insert data into database
                            DB::table('budget_uw')->insert($insertData);
                            // DB::table('budget_uw_forecast')->insert($insertData);
                        }
                    }
            
                    // Move to the next business type for the next worksheet
                    $businessTypeIndex = ($businessTypeIndex + 1) % count($businessTypes);
                }
            } else {
                return ['status' => 0];
            }
            

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            $res = ['status' => 1];

        } catch (\Throwable $th) {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            $res = $res = ['status' => 0];
        }
        return $res;
        
    }

    public function budgetuploadr(Request $request){

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();


        try {

            $selectedValues = $request->selectedUploadValues;
            $budget_id = $request->budgetuploadidr;
            $fileData = $request->file('budgetr');
            $fileExtension = $fileData->getClientOriginalExtension();
            $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
            $sections = explode("-", $hierarchy->description);
            $levels = array_slice($sections, 0, -1);

            $level1 = null;
            $level2 = null;
            $level3 = null;
            $level4 = null;
            $bgid= $budget_id;

            // $attributes = DB::table('budget_company')
            // ->where('budget_id', $bgid)
            // ->increment('refocus', 1, ['edit' => 'N']);

            $selectedValues = json_decode($selectedValues, true);

            foreach ($selectedValues as $categoryId => $selectedOptions) {

                    $parts = explode(' - ', $selectedOptions[0]);
                    switch ($categoryId) {
                        case 0:
                            $level1 = $parts[0];
                            // $bgid = $parts[1];
                            break;
                        case 1:
                            $level2 = $parts[0];
                            break;
                        case 2:
                            $level3 = $parts[0];
                            break;
                    }

            }

            if (!empty($levels)) {
                if ($levels[0] == "BRANCH") {

                    $valueData0 = DB::table('branch')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level1%'")
                        ->pluck('branch')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();

                } elseif ($levels[0] == "CHANNEL") {
                    $valueData0 = DB::table('dist_channel')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level1%'")
                        ->pluck('dist_type')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                }
            }

            if (count($levels) > 1) { 
                if ($levels[1] == "BRANCH") {
                    $valueData1 = DB::table('branch')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level2%'")
                        ->pluck('branch')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } elseif ($levels[1] == "CHANNEL") {
                    $valueData1 = DB::table('dist_channel')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level2%'")
                        ->pluck('dist_type')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                }
            }


            $year = DB::table('budget_company')
                    ->where('budget_id', $budget_id)
                    ->first();
        
            $yearValue = $year->year; 

            if ($fileExtension == 'xlsx') {
                $spreadsheet = IOFactory::load($fileData);
                $worksheets = $spreadsheet->getAllSheets();
            
                foreach ($worksheets as $worksheet) {

                    $sheetName = str_replace(' ', '', $worksheet->getTitle());
                    $sheetData = $worksheet->toArray();

                    if($levels[1] == "CHANNEL"){

                        $channel = DB::table('dist_channel')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$sheetName%'")
                        ->pluck('dist_type')
                        ->map(function ($value) {
                            return intval($value);
                        });

                    }

                    $level2= $valueData0;
                    $level3= $channel[0];                    

                    // Skip the first row (header row)
                    $skipFirstRow = true;
            
                    // Get the current business type from the array
                    $businessType = $businessTypes[$businessTypeIndex];
            
                    foreach ($sheetData as $row) {
                        if ($skipFirstRow) {
                            $skipFirstRow = false;
                            continue;
                        }
                        
                        $update_bgt =  DB::table('budget_uw')
                        ->where('budget_id', $bgid)
                        ->where('level2', $level2)
                        ->where('level3', $level3)
                        ->where('level4', $row[1])
                        ->where('businesstype', $row[2])
                        ->update([
                            'JANUARY' => $row[3],
                            'FEBRUARY' => $row[4],
                            'MARCH' => $row[5],
                            'APRIL' => $row[6],
                            'MAY' => $row[7],
                            'JUNE' => $row[8],
                            'JULY' => $row[9],
                            'AUGUST' => $row[10],
                            'SEPTEMBER' => $row[11],
                            'OCTOBER' => $row[12],
                            'NOVEMBER' => $row[13],
                            'DECEMBER' => $row[14]
                        ]);
                        
                      
                    }
            
                    $businessTypeIndex++;
                }
        
            } else {
                return $res = ['status' => 0];
            }

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            $res = ['status' => 1];

        } catch (\Throwable $th) {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            $res = $res = ['status' => 0];
        }
        return $res;
        
    }
    

    
    public function editRefocusNumber(Request $request){
        $refocus_times = $request->refocustimes;
        $edit = DB::table('company_profiles')->update(['budget_refocus'=>$refocus_times]);
        if ($edit) {
            return $res = ['status'=>1];
        } else {
            return $res = ['status'=>0];
        }
        
    }

    public function newBudget(Request $request){

        DB::beginTransaction();
        
        try {
            $validated = $request->validate([
                'acyear' => 'required',
                'budget' => 'required',
                'brbudget' => 'required',
                'agbudget' => 'required',
                'description' => 'required',
            ]);
            $acyear = $request->acyear;
            $status = 'PENDING';

            $budgets = DB::table('company_budget')
                    ->where('year', $acyear)
                    ->where('status', '<>' , 'CANCELLED')
                    ->get();
            // if(count($budgets) > 10){
            if(count($budgets) > 0){
                $res = [
                    'status' => 0,
                    'message' => "Budget for that year exists, please try a different year",
                ];
            }else{
    
                $upload = DB::table('company_budget')->insert(
                    array(
                        'rate_year' => $request->prevacyear,
                        'year' => $acyear,
                        'description'=> $request->description,
                        'dola' => Carbon::now(),
                        'user_string' => Auth::user()->username,
                        'amount' => str_replace(",", "", $request->budget),
                        'new_branch_target' => $request->brbudget,
                        'new_agent_target' => $request->agbudget,
                        'status' => $status,
                        'refocus' => 0,
                    )
                );
                $conn = new \GuzzleHttp\Client();
                // $api= apiEndpointExec("2AR007")->getData();
                
                $url = ''.ENV('API_URL').'budgetcompanypm/';
                $response = $conn->get($url);

                $pmperformance = json_decode($response->getBody()->getContents());

                $budget = DB::table('company_budget')
                        ->where('year',$acyear)
                        ->where('status','PENDING')
                        ->first();

                $company_budget = $budget->amount;
                $company_budget_pm = $budget->amount;
                $company_per_month = array();
                
                if (count($pmperformance) == 0) {
                    for ($i=1; $i<=12; $i++) {
                        $month_rate = 100/12;
                        $month_target = ($month_rate/100)*$company_budget;
                        $upload = [
                            'budget_id' => $budget->budget_id,
                            'dola' => Carbon::now(),
                            'amount' => $month_target,
                            'month_rate' => $month_rate,
                            'month'=> $i,
        
                            ];
                        array_push($company_per_month,$upload);
                    }
                }else{
                    for ($i=1; $i<=12; $i++) {
                        if (!in_array($i,array_column($pmperformance, 'month'))) {
                            $month_rate = $request->brbudget;
                            $month_target = ($month_rate/100)*$company_budget;
                            $upload = [
                                'budget_id' => $budget->budget_id,
                                'dola' => Carbon::now(),
                                'amount' => $month_target,
                                'month_rate' => $month_rate,
                                'month'=> $i,
            
                                ];
                            array_push($company_per_month,$upload);
                            $company_budget_pm = $company_budget_pm - $month_target;
                        }
                    }
    
                    $company_month_total = 0;
                    foreach ($pmperformance as $month) {
                        $company_month_total += $month->gross_amount;
                    }
    
                    for ($i=1; $i<=12; $i++) {
                        if (in_array($i,array_column($pmperformance, 'month'))) {
                            $month = collect($pmperformance)->where('month',$i);
                            foreach ($month as $m) {
                                $month_target = ($m->gross_amount/$company_month_total)*$company_budget_pm;
                                $month_rate = ($month_target/$company_budget)*100;
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'dola' => Carbon::now(),
                                    'amount' => $month_target,
                                    'month_rate' => $month_rate,
                                    'month'=> $i,
                
                                ];
                                array_push($company_per_month, $upload);
                            }
                        }
                    }
                    
                }
                DB::table('company_budget_pm')->insert($company_per_month);
        
                $conn = new \GuzzleHttp\Client();
                $url=''.ENV('API_URL').'budgetbranch';
                $response = $conn->get($url);
                $branches = json_decode($response->getBody()->getContents());
                // dd($branches);

                $url = ''.ENV('API_URL').'branch/';
                $branch_names = $conn->get($url);
                $branch_names = json_decode($branch_names->getBody()->getContents());

                $company_budget = str_replace(",", "", $request->budget);
                $branches_total_amount = 0;
                for ($i=0; $i < count($branches); $i++) { 
                    $branch_amount =  abs($branches[$i]->gross_amount);
                    $branches_total_amount += $branch_amount;
                }
        

                $branch_budgets_upload = array();

                if (collect($branches)->count() == 0) {
                    foreach ($branch_names as $br) {
                        $branch_rate = 100/collect($branch_names)->count();
                        $branch_target = ($branch_rate/100)*str_replace(",", "", $request->budget);
                        $upload = [
                            'budget_id' => $budget->budget_id,
                            'branch_id' => $br->branch,
                            'dola' => Carbon::now(),
                            'amount' => $branch_target,
                            'branch_rate' => $branch_rate,
        
                        ];
                        array_push($branch_budgets_upload,$upload);
                    }
                    // dd($branch_budgets_upload);
                    DB::table('branch_budget')->insert($branch_budgets_upload);

                } else {
                
                    foreach ($branch_names as $br) {
                        $br_perf_exists = collect($branches)->where('branch',$br->branch)->count();
                        if ($br_perf_exists == 0) {
                            $branch_rate = $request->brbudget;
                            $branch_target = ($branch_rate/100)*str_replace(",", "", $request->budget);
                            $upload = [
                                'budget_id' => $budget->budget_id,
                                'branch_id' => $br->branch,
                                'dola' => Carbon::now(),
                                'amount' => $branch_target,
                                'branch_rate' => $branch_rate,
            
                            ];
                            array_push($branch_budgets_upload,$upload);
                             $company_budget = $company_budget - $branch_target;
                        }
    
                    }
    
                    foreach ($branches as $branch) {
                        if (!isset($branch->gross_amount) || $branch->gross_amount == 0) {
                            $branch_rate = $request->brbudget;
                            $branch_target = ($branch_rate/100)*str_replace(",", "", $request->budget);
                            $upload = [
                                'budget_id' => $budget->budget_id,
                                'branch_id' => $branch->branch,
                                'dola' => Carbon::now(),
                                'amount' => $branch_target,
                                'branch_rate' => $branch_rate,
            
                            ];
                            array_push($branch_budgets_upload,$upload);
                             $company_budget = $company_budget - $branch_target;
                        }
                    }
    
                    foreach ($branches as $branch) {
                        if(isset($branch->gross_amount) && $branch->gross_amount != 0){
                            $branch_target = (abs($branch->gross_amount)/$branches_total_amount)*$company_budget;
                            $branch_rate = ($branch_target/str_replace(",", "", $request->budget))*100;
                            $upload = [
                                'budget_id' => $budget->budget_id,
                                'branch_id' => $branch->branch,
                                'dola' => Carbon::now(),
                                'amount' => $branch_target,
                                'branch_rate' => $branch_rate,
                            ];
                            array_push($branch_budgets_upload,$upload);
                        }
                    }
                    // dd($branch_budgets_upload);
                    DB::table('branch_budget')->insert($branch_budgets_upload);
                }


                $agents = new \GuzzleHttp\Client();
                $url = ''.ENV('API_URL').'intermediaries/';
                $intermediaries = $agents->get($url);
                $intermediaries = json_decode($intermediaries->getBody()->getContents());

                $url=''.ENV('API_URL').'budgetagent';
                $intermed = $agents->get($url);
                $agents = json_decode($intermed->getBody()->getContents());

                $branchbudget = DB::table('branch_budget')->where('budget_id',$budget->budget_id)->get();
                $agent_budgets_upload = array();
                
                foreach ($branchbudget as $branch) {
                    $brintermediaries = collect($intermediaries)->where('branch', $branch->branch_id);
                    
                    $bragentperf = collect($agents)->where('branch', $branch->branch_id);
                    $brbudget = collect($branchbudget)
                                ->where('branch_id', $branch->branch_id)
                                ->first();

                    $branch_budget = $brbudget->amount;
                    $branch_target = $brbudget->amount;
                    $branchagent_total = 0;
                    foreach ($agents as $agent) {
                        if($branch->branch_id == $agent->branch){
                            $branchagent_total += abs($agent->gross_amount);
                        }
                    }

                    if (collect($bragentperf)->count() == 0) {
                        foreach ($brintermediaries as $inter) {
                            $agent_rate = 100/count($brintermediaries);
                            $agent_target = ($agent_rate/100)*$branch_budget;

                            $upload = [
                                'agent_id' => $inter->agent,
                                'branch_id' => $inter->branch,
                                'budget_id' => $budget->budget_id,
                                'dola' => Carbon::now(),
                                'amount' => $agent_target,
                                'agent_rate' => $agent_rate,
                            ];
                            array_push($agent_budgets_upload, $upload);
                        }
                    } else {
                        foreach ($brintermediaries as $inter) {
                            $agts = collect($agents)->where('branch', $branch->branch_id)->toArray();
                            if(!in_array($inter->agent, array_column($agts, 'agent_no'), true)){
                                $agent_rate = $request->agbudget;
                                $agent_target = ($agent_rate/100)*$branch_target;
    
                                $branch_budget = $branch_budget - $agent_target;
    
                                $upload = [
                                    'agent_id' => $inter->agent,
                                    'branch_id' => $inter->branch,
                                    'budget_id' => $budget->budget_id,
                                    'dola' => Carbon::now(),
                                    'amount' => $agent_target,
                                    'agent_rate' => $agent_rate,
                                ];
                                array_push($agent_budgets_upload, $upload);
                            }
                        }
                        // dd();
    
                        foreach ($agents as $agent) {
                            if(($branch->branch_id == $agent->branch) && ((!isset($agent->gross_amount) || $agent->gross_amount == 0))){
                                $agent_rate = $request->agbudget;
                                $agent_target = ($agent_rate/100)*$branch_target;
    
                                $branch_budget = $branch_budget - $agent_target;
    
                                $upload = [
                                    'agent_id' => $agent->agent_no,
                                    'branch_id' => $agent->branch,
                                    'budget_id' => $budget->budget_id,
                                    'dola' => Carbon::now(),
                                    'amount' => $agent_target,
                                    'agent_rate' => $agent_rate,
                                ];
                                array_push($agent_budgets_upload, $upload);
                            }
                        }
    
                        foreach ($agents as $agent) {
                            if(($branch->branch_id == $agent->branch) && ((isset($agent->gross_amount) && $agent->gross_amount != 0))){
                                $agent_budget = (abs($agent->gross_amount)/$branchagent_total)*$branch_budget;
                                $agent_rate = ($agent_budget/$branch_target)*100;
                            
                                $upload = [
                                    'agent_id' => $agent->agent_no,
                                    'branch_id' => $agent->branch,
                                    'budget_id' => $budget->budget_id,
                                    'dola' => Carbon::now(),
                                    'amount' => $agent_budget,
                                    'agent_rate' => $agent_rate,
                                ];
                                array_push($agent_budgets_upload, $upload);
                            }
                        }
                    }
                }
                // dd($agent_budgets_upload);
                DB::table('agent_budget')->insert($agent_budgets_upload);



                
           //company class budgets

                $conn = new \GuzzleHttp\Client();
                $url = ''.ENV('API_URL').'class/';
                $classes = $conn->get($url);
                $classes = json_decode($classes->getBody()->getContents());
                // dd($classes);
                $url=''.ENV('API_URL').'budgetclass';
                $classperf = $conn->get($url);
                $classperf = json_decode($classperf->getBody()->getContents());
                // $brclasses = collect($classperf)->where('bclass', 100)->sum('gross_amount');
                // dd($brclasses);
                $class_budgets_upload = array();

                $class_sum = 0;
                $class_performance = array();
                foreach ($classes as $class) {
                    foreach ($classperf as $cp) {
                        if ($class->class_field == $cp->bclass) {
                            $class_sum += abs($cp->gross_amount);
                        }
                    }
                    array_push($class_performance, ['class'=>$class->class_field, 'amount'=>$class_sum]);
                    $class_sum = 0;

                }

                // dd($class_performance);
                $class_budgets_upload = array();
                $company_budget = str_replace(",", "", $request->budget);
                foreach ($class_performance as $cp) {
                    if ($cp['amount'] == 0) {
                        $class_rate = $request->brbudget;
                        $class_target = ($class_rate/100)*str_replace(",", "", $request->budget);
                        $upload = [
                            'budget_id' => $budget->budget_id,
                            'class' => $cp['class'],
                            'dola' => Carbon::now(),
                            'amount' => $class_target,
                            'class_rate' => $class_rate,
        
                        ];
                        array_push($class_budgets_upload,$upload);
                         $company_budget = $company_budget - $class_target;
                    }

                }
                $class_total_performance = collect($classperf)->sum('gross_amount');
                // dd($class_total_performance);
                foreach ($class_performance as $cp) {
                    if($cp['amount'] > 0){
                        $class_target = (abs($cp['amount'])/$class_total_performance)*$company_budget;
                        $class_rate = ($class_target/str_replace(",", "", $request->budget))*100;
                        $upload = [
                            'budget_id' => $budget->budget_id,
                            'class' => $cp['class'],
                            'dola' => Carbon::now(),
                            'amount' => $class_target,
                            'class_rate' => $class_rate,
                        ];
                        array_push($class_budgets_upload,$upload);
                    }
                }
                DB::table('class_budget')->insert($class_budgets_upload);
                // dd($class_budgets_upload);

                
                $branchbudget = DB::table('branch_budget')->where('budget_id',$budget->budget_id)->get();
                // dd($branch_names);
                
                $brbgt_sum = 0;
                $class_branch_upload = array();
                foreach($branch_names as $branch){
                    $brbgt = collect($branchbudget)->where('branch_id', $branch->branch)->first()->amount;
                    
                    // dd($brbgt);
                    $brclasses = collect($classperf)->where('branch', $branch->branch);
                    
                    $class_sum = 0;
                    $sum = 0;
                    $class_performance = array();
                    foreach ($classes as $class) {
                        foreach ($brclasses as $cp) {
                            if ($class->class_field == $cp->bclass) {
                                $class_sum += abs($cp->gross_amount);
                                $sum += $class_sum;
                            }
                        }
                        array_push(
                            $class_performance, 
                            [
                                'class'=>$class->class_field, 
                                'amount'=>$class_sum,
                                'branch'=>$branch->branch,
                            ]
                        );
                        $class_sum = 0;
                    }

                    $sum_amount = array_sum(array_column($class_performance,'amount'));
                    // dd($sum_amount);
                    if ($sum_amount == 0) {
                        foreach($class_performance as $cp){
                            $class_target = ($brbgt/count($class_performance));
                            $class_rate = ($class_target/$brbgt)*100;
                            $upload = [
                                'budget_id' => $budget->budget_id,
                                'branch_id' => $branch->branch,
                                'class' => $cp['class'],
                                'dola' => Carbon::now(),
                                'amount' => $class_target,
                                'class_rate' => $class_rate
                            ];
                            array_push($class_branch_upload,$upload);
                        }
                    }else{
                        $brbgt_sum = $brbgt;
                        foreach($class_performance as $cp){
                            if($cp['amount'] == 0){
                                $class_rate = $request->brbudget;
                                $class_target = ($class_rate/100)*$brbgt;
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'branch_id' => $branch->branch,
                                    'class' => $cp['class'],
                                    'dola' => Carbon::now(),
                                    'amount' => $class_target,
                                    'class_rate' => $class_rate
                                ];
                                array_push($class_branch_upload,$upload);
                                 $brbgt_sum = $brbgt_sum - $class_target;
                            }
                        }
    
                        foreach ($class_performance as $cp) {
                            if($cp['amount'] > 0){
                                $class_target = (abs($cp['amount'])/$sum)*$brbgt_sum;
                                $class_rate = ($class_target/$brbgt)*100;
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'class' => $cp['class'],
                                    'branch_id' => $branch->branch,
                                    'dola' => Carbon::now(),
                                    'amount' => $class_target,
                                    'class_rate' => $class_rate,
                                ];
                                array_push($class_branch_upload,$upload);
                            }
                        }
                    }
                }
                DB::table('branch_class_budget')->insert($class_branch_upload);


                
                $agentbudget = DB::table('agent_budget')->where('budget_id',$budget->budget_id)->get();
                
                $agbgt_sum = 0;
                $class_agent_upload = array();
                foreach ($agentbudget as $agbgt) {
                    $branch_id = $agbgt->branch_id;
                    $agent_id = $agbgt->agent_id;
                    $agbgt = $agbgt->amount;
                    $agclasses = collect($classperf)
                                                    ->where('branch', $branch_id)
                                                    ->where('agent_no', $agent_id);
                    
                    $class_sum = 0;
                    $sum = 0;
                    $class_performance = array();
                    foreach ($classes as $class) {
                        foreach ($agclasses as $cp) {
                            if ($class->class_field == $cp->bclass) {
                                $class_sum += abs($cp->gross_amount);
                                $sum += $class_sum;
                            }
                        }
                        array_push(
                            $class_performance, 
                            [
                                'class'=>$class->class_field, 
                                'amount'=>$class_sum,
                                'branch'=>$branch_id,
                                'agent'=>$agent_id,
                            ]
                        );
                        $class_sum = 0;
                    }

                    $sum_amount = array_sum(array_column($class_performance,'amount'));
                    if ($sum_amount == 0) {
                        foreach($class_performance as $cp){
                            $class_target = ($agbgt/count($class_performance));
                            $class_rate = ($class_target/$agbgt)*100;
                            $upload = [
                                'budget_id' => $budget->budget_id,
                                'branch_id' => $cp['branch'],
                                'agent_id' =>  $cp['agent'],
                                'class' => $cp['class'],
                                'dola' => Carbon::now(),
                                'amount' => $class_target,
                                'class_rate' => $class_rate
                            ];
                            array_push($class_agent_upload,$upload);
                        }
                    }else{
                        $agbgt_sum = $agbgt;
                        foreach($class_performance as $cp){
                            if($cp['amount'] == 0){
                                $class_rate = $request->brbudget;
                                $class_target = ($class_rate/100)*$agbgt;
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'branch_id' =>  $cp['branch'],
                                    'agent_id' =>  $cp['agent'],
                                    'class' => $cp['class'],
                                    'dola' => Carbon::now(),
                                    'amount' => $class_target,
                                    'class_rate' => $class_rate
                                ];
                                array_push($class_agent_upload,$upload);
                                $agbgt_sum = $agbgt_sum - $class_target;
                            }
                        }
    
                        foreach ($class_performance as $cp) {
                            if($cp['amount'] > 0){
                                $class_target = (abs($cp['amount'])/$sum)*$agbgt_sum;
                                $class_rate = ($class_target/$agbgt)*100;
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'class' => $cp['class'],
                                    'branch_id' =>  $cp['branch'],
                                    'agent_id' =>  $cp['agent'],
                                    'dola' => Carbon::now(),
                                    'amount' => $class_target,
                                    'class_rate' => $class_rate,
                                ];
                                array_push($class_agent_upload,$upload);
                            }
                        }
                    }
                    DB::table('agent_class_budget')->insert($class_agent_upload);
                    $class_agent_upload = array();
                }


                $branchespm = new \GuzzleHttp\Client();
                $url=''.ENV('API_URL').'budgetbranchpm';
                $response = $branchespm->get($url);
                $branchespm = json_decode($response->getBody()->getContents(), true);
                // dd($branchespm);

                $branch_month_budget = array();
                $branch_targetpm = 0;
                foreach($branchbudget as $branch) {
                    $brbudget = collect($branchbudget)
                                ->where('branch_id', $branch->branch_id)
                                ->first();
                    $branch_budget = $brbudget->amount;
                    $branch_targetpm = $brbudget->amount;
                    $months = collect($branchespm)->where('branch',$branch->branch_id)->toArray();

                    if (count($months) == 0) {
                        for ($i=1; $i<=12; $i++) {
                            $month_rate = 100/12;
                            $month_target = ($month_rate/100)*$branch_budget;
                            $upload = [
                                'budget_id' => $budget->budget_id,
                                'branch_id' => $branch->branch_id,
                                'dola' => Carbon::now(),
                                'month_amount' => $month_target,
                                'month_rate' => $month_rate,
                                'month'=> $i,
            
                                ];
                            array_push($branch_month_budget,$upload);
                        }
                    } else {
                        for ($i=1; $i<=12; $i++) {
                                if (!in_array($i,array_column($months, 'month'))) {
                                    $month_rate = $request->brbudget;
                                    $month_target = ($month_rate/100)*$branch_budget;
                                    $upload = [
                                        'budget_id' => $budget->budget_id,
                                        'branch_id' => $branch->branch_id,
                                        'dola' => Carbon::now(),
                                        'month_amount' => $month_target,
                                        'month_rate' => $month_rate,
                                        'month'=> $i,
                    
                                        ];
                                    array_push($branch_month_budget,$upload);
                                    $branch_targetpm = $branch_targetpm - $month_target;
                                }
                        }

                        
                        $branch_month_total = 0;
                        foreach ($months as $month) {
                            $branch_month_total += abs($month['gross_amount']);
                        }
                        for ($i=1; $i<=12; $i++) {
                            if (in_array($i,array_column($months, 'month'))) {
                                $month = collect($months)->where('month',$i)->toArray();
                                foreach ($month as $m) {
                                    $month_target = (abs($m['gross_amount'])/$branch_month_total)*$branch_targetpm;
                                    $month_rate = ($month_target)/$branch_budget*100;
                                    $upload = [
                                        'budget_id' => $budget->budget_id,
                                        'branch_id' => $branch->branch_id,
                                        'dola' => Carbon::now(),
                                        'month_amount' => $month_target,
                                        'month_rate' => $month_rate,
                                        'month'=> $i,
                    
                                        ];
                                    array_push($branch_month_budget,$upload);
                                }
                            }
                        }
                    }
                }
                DB::table('branch_month_budget')->insert($branch_month_budget);

                $agentspm = new \GuzzleHttp\Client();
                $url=''.ENV('API_URL').'budgetagentpm';
                $response = $agentspm->get($url);
                $agentspm = json_decode($response->getBody()->getContents(), true);
                // dd(collect($agentspm)
                // ->where('agent_no',3)
                // ->where('branch', 101));


                $agnt_budget = DB::table('agent_budget')
                        ->where('budget_id', $budget ->budget_id)
                        ->get();
                // dd($agnt_budget);

                $agent_month_budget = array();
                $branch_targetpm = 0;
                foreach ($agnt_budget as $agn_bgt) {
                    $agent_budget = $agn_bgt->amount;
                    $agent_targetpm = $agn_bgt->amount;
                    $months = collect($agentspm)
                            ->where('agent_no',1)
                            ->where('branch', 103)
                            ->toArray();
                    

                            // dd($months);
                    $agent_month_total = 0;
                    foreach ($months as $month) {
                        $agent_month_total += abs($month['gross_amount']);
                    }

                    if ($agent_month_total==0) {
                        
                        for ($i=1; $i<=12; $i++) {
                            $month_rate = 100/12;
                            $month_target = ($month_rate/100)*$agent_budget;
                            // dd($agent_budget);
                            $upload = [
                                'budget_id' => $budget->budget_id,
                                'branch_id' => $agn_bgt->branch_id,
                                'agent_id' => $agn_bgt->agent_id,
                                'dola' => Carbon::now(),
                                'month_amount' => $month_target,
                                'month_rate' => $month_rate,
                                'month'=> $i,
                                ];
                                array_push($agent_month_budget, $upload);
                        }
                    } else {
                        for ($i=1; $i<=12; $i++) {
                            if (!in_array($i,array_column($months, 'month')) || in_array(0,array_column($months, 'gross_amount'))) {
                                $month_rate = $request->agbudget;
                                $month_target = ($month_rate/100)*$agent_budget;
                                // dd($agent_budget);
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'branch_id' => $agn_bgt->branch_id,
                                    'agent_id' => $agn_bgt->agent_id,
                                    'dola' => Carbon::now(),
                                    'month_amount' => $month_target,
                                    'month_rate' => $month_rate,
                                    'month'=> $i,
                                    ];
                                    array_push($agent_month_budget, $upload);
                                $agent_targetpm = $agent_targetpm - $month_target;
                            }
                        }

                        
                        
                        for ($i=1; $i<=12; $i++) {
                            if (in_array($i,array_column($months, 'month'))) {
                                $month = collect($months)->where('month',$i)->toArray();
                                foreach ($month as $m) {
                                    if ($m['gross_amount'] != 0) {
                                        $month_target = (abs($m['gross_amount'])/$agent_month_total)*$agent_targetpm;
                                        $month_rate = ($month_target/$agent_budget)*100;
                                        $upload = [
                                            'budget_id' => $budget->budget_id,
                                            'branch_id' => $agn_bgt->branch_id,
                                            'agent_id' => $agn_bgt->agent_id,
                                            'dola' => Carbon::now(),
                                            'month_amount' => $month_target,
                                            'month_rate' => $month_rate,
                                            'month'=> $i,
                        
                                        ];
                                        array_push($agent_month_budget, $upload);
                                    }
                                }
                            }
                        }
                    }
                }
                // dd(count($agent_month_budget));
                // $foundItems = array();

                // foreach($agent_month_budget as $item)
                // {
                //     $find = TRUE;
                //     if(isset($item['branch_id']) && $item['branch_id'] == 101 && $item['agent_id'] == 3)
                //     {
                //         $find = TRUE;
                //     } else {
                //         $find = FALSE;
                //     }
                //     if($find)
                //     {
                //         array_push($foundItems, $item);
                //     }
                // }
                // dd($foundItems);
                
                DB::table('agent_month_budget')->insert($agent_month_budget);
                 
                DB::commit();
                $res = ['status'=> 1];
            }
        } catch (\Throwable $th) {
            DB::rollback();
            
            dd($th);
            $res = [
                'status'=> 0,
                'message' => "An error ocurrred, please try again",
            ];
        }
        return $res;
        
    }

    public function previousBudget(Request $request){
        try {
            $prevyear['year'] = $request->prevyear;;
    
            $budgetprev = new \GuzzleHttp\Client();
            // $url= apiEndpointExec("2AR0010");

            $url=''.ENV('API_URL').'budgetprevyear/';
            
            
            $response = $budgetprev->post($url,  ['form_params'=>$prevyear]);

            $res = ['status' => 1];
        } catch (\Throwable $th) {
            $res = ['status' => 0];
        }
        return $res;
    }

    public function getChannelBudgets(Request $request){

        $budget_id = $request->budget_id;

        $budgets = DB::table('budget_channel')
                    ->where('budget_id', $budget_id)
                    ->get();

        return Datatables::of($budgets)
        ->editColumn('amount', function($d){
            return number_format($d->amount);
        })
        ->editColumn('channel', function($d){
            $channels = DB::table('channel_budget_params')
            ->where('id',$d->channel_id)
            ->first();
            return $channels->name;
        })

        ->addColumn('actions', function($d){
            return '<a href="'. route('budget.details', ["channel_id"=>$d->channel_id,"budget_id"=>$d->budget_id]).'">
                        <buttton class="btn btn-sm alert-info">
                        <span class="fa fa-sort-amount-asc"></span> Details
                        </button>
                    </a>'
                    ;
        })
        ->rawColumns(['actions'])
        ->make(true);
    }

    public function masterBudget(){
        
        $channels = DB::table('channel_budget_params')->get();
        $bhlevels = DB::table('budget_hierarchy')->where('status','A')->get();

        return view('gb.budgets.masterbudget', compact('channels','bhlevels'));

    }
    
    public function masterBudgetDatatable(){
        $masterbudget = DB::table('masterbudget')
                    ->get();

        return Datatables::of($masterbudget)
        ->editColumn('amount', function($d){
            return number_format($d->amount);
        })

        ->addColumn('actions', function($d){
            return '<a href="'. route('channel.budgets', ["budget_id"=>$d->budget_id]).'">
                        <buttton class="btn btn-sm alert-info">
                        <span class="fa fa-sort-amount-asc"></span> Details
                        </button>
                    </a>'
                    ;
        })
        ->rawColumns(['actions'])
        ->make(true);
    }

    public function newChannelBudget(Request $request){
        DB::beginTransaction();
        try {
                $channel_year = $request->channel_year;
                $budgets = DB::table('masterbudget')
                ->where('year', $channel_year)
                ->where('status', '<>' , 'ACTIVE')
                ->get();

                if(count($budgets) > 0){
                    $res = [
                        'status' => 0,
                        'message' => "Budget for that year exists, please try a different year",
                    ];
                }else{

                    $channel_year = $request->channel_year;
                    $channel_budget = $request -> channel_budget; 
                    $budgetsum = array_sum($request->channel_budget);
                    $id = DB::table('masterbudget')->max('budget_id') + 1;
                    $user = Auth::user()->user_name;

                    $mbudget = [
                        "budget_id" => $id,
                        "amount" => $budgetsum,
                        "year" => $request->channel_year,
                        "user_string" => $user,
                        "dola" => Carbon::now(),
                    ];

                    DB::table('masterbudget')->insert($mbudget);

                    
                    $budgets =  array();
                    for ($i = 0; $i < count($request->channel_budget); $i++) {
                        $budget = [
                            "budget_id" => $id,
                            "channel_id" => $request->channel_id[$i],
                            "amount" => $request->channel_budget[$i],
                            "year" => $request->channel_year,
                            "dola" => Carbon::now(),
                        ];

                        array_push($budgets, $budget);
                    }

                    DB::table('budget_channel')->insert($budgets);

                }


                DB::commit();
                $res = ['status' => 1];
            }

            catch (\Throwable $th) {
               
                DB::rollback();
                $res = ['status' => 0];
            }
        
            return $res;
    }

    public function getChannelPerfomanceYear(){


        // $channel_id = $request->channel_id;
        
        // $channel = DB::table('dist_channel')
        //         ->where('dis_type',$channel_id)
        //         ->first();
        // dd($channel);

        $retail_performance = DB::table('debitmast')
            ->join('intermediary', function ($join) {
                $join->on('debitmast.branch', '=', 'intermediary.branch')
                    ->on('debitmast.agent_no', '=', 'intermediary.agent');
            })
            ->join('dist_channel', 'intermediary.distribution_channel', '=', 'dist_channel.dist_type')
            ->where('dist_channel.dist_type', 1)
            ->select(DB::raw('SUM(debitmast.gross_amount) as total_sum'))
            ->first();
            dd($retail_performance);
        
        $broker_performance = DB::table('debitmast')
            ->join('intermediary', function ($join) {
                $join->on('debitmast.branch', '=', 'intermediary.branch')
                    ->on('debitmast.agent_no', '=', 'intermediary.agent');
            })
            ->whereIn('intermediary.acc_type', [3])
            ->select(DB::raw('SUM(debitmast.gross_amount) as total_sum'))
            ->first();
            // dd($broker_performance);

        $facultative_performance = DB::table('debitmast')
            ->join('intermediary', function ($join) {
                $join->on('debitmast.branch', '=', 'intermediary.branch')
                ->on('debitmast.agent_no', '=', 'intermediary.agent');
            })
            ->whereIn('intermediary.acc_type', [8])
            ->select(DB::raw('SUM(debitmast.gross_amount) as total_sum'))
            ->first();
            // dd($facultative_performance);
            
            
        $bancassuarance_performance = DB::table('debitmast')
            ->join('intermediary', function ($join) {
                $join->on('debitmast.branch', '=', 'intermediary.branch')
                    ->on('debitmast.agent_no', '=', 'intermediary.agent');
            })
            ->whereIn('intermediary.acc_type', [6])
            ->select(DB::raw('SUM(debitmast.gross_amount) as total_sum'))
            ->first();
            // dd($bancassuarance_performance);

        return Datatables::of($budgets)
        ->editColumn('amount', function($d){
            return number_format($d->amount);
        })
        ->addColumn('actions', function($d){
            return '<a href="'. route('budget.details', ["budget_id"=>$d->budget_id]).'">
                        <buttton class="btn btn-sm alert-info">
                        <span class="fa fa-sort-amount-asc"></span> Details
                        </button>
                    </a>'
                    ;
        })
        ->rawColumns(['actions'])
        ->make(true);
    }

    public function getCompanyBudgets(){
        $budgets = DB::table('company_budget')
                    ->where('status', 'ACTIVE')
                    ->get();

        return Datatables::of($budgets)
        ->editColumn('amount', function($d){
            return number_format($d->amount);
        })
        ->addColumn('actions', function($d){
            return '<a href="'. route('budget.details', ["budget_id"=>$d->budget_id]).'">
                        <buttton class="btn btn-sm alert-info">
                        <span class="fa fa-sort-amount-asc"></span> Details
                        </button>
                    </a>'
                    ;
        })
        ->rawColumns(['actions'])
        ->make(true);
    }

    public function pendingBudgets(){
        $budgets = DB::table('company_budget')
                    ->where('status', 'PENDING')
                    ->get();

        return Datatables::of($budgets)
        ->editColumn('amount', function($d){
            return number_format($d->amount);
        })
        ->addColumn('actions', function($d){
            return '<a href="'. route('budget.details', ["budget_id"=>$d->budget_id]).'">
                    <buttton class="btn btn-sm alert-info">
                        <span class="fa fa-sort-amount-asc"></span> Details
                    </button>
                    </a>'
                    ;
        })
        ->rawColumns(['actions'])
        ->make(true);
    }

    public function getBudgetDetails(Request $request){
        $budget_id = $request->budget_id;
        $branchbudgets = DB::table('branch_budget')->select('branch_id', 'amount')->where('budget_id', $budget_id)->get();

        // $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;

        return view('gb.budgets.budgetdetails',
                        ["budget_id"=> $budget_id,
                        "branchbudgets"=>$branchbudgets,
                        "budget_refocus"=>$budget_refocus,
                        ]);
    }

    public function budgetPerMonth(Request $request){
        $budget_id = $request->budget_id;
        $bgt = DB::table('budget_channel_month')->where('id',$budget_id)->first();
        $year = $bgt->year;
        $refocusbudgets = DB::table('masterbudget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED')
                                ->orWhere('status','PENDING');
                        })
                        ->get();

        $activeBudget = DB::table('masterbudget')
                        ->where('year',$year)
                        ->where('status','ACTIVE')
                        ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;

        $pmbudgets = DB::table('budget_channel_month')
                        ->where('budget_id',$r0_budgetid)
                        ->orderBy('month', 'ASC')
                        ->get();

        $dt = Datatables::of($pmbudgets)
                ->editColumn('month', function($d){
                    if ($d->month == 1) {
                        return "JANUARY";
                    }else if($d->month == 2) {
                        return "FEBRUARY";
                    }else if($d->month == 3) {
                        return "MARCH";
                    }else if($d->month == 4) {
                        return "APRIL";
                    }else if($d->month == 5) {
                        return "MAY";
                    }else if($d->month == 6) {
                        return "JUNE";
                    }else if($d->month == 7) {
                        return "JULY";
                    }else if($d->month == 8) {
                        return "AUGUST";
                    }else if($d->month == 9) {
                        return "SEPTEMBER";
                    }else if($d->month == 10) {
                        return "OCTOBER";
                    }else if($d->month == 11) {
                        return "NOVEMBER";
                    }else{
                        return "DECEMBER";
                    }
                    
                })
                ->editColumn('month_rate', function($d){
                    return number_format($d->month_rate, 2, '.', ',');
                })
                ->addColumn('original_budget',function($d) use($r0_budgetid){
                    $orig_budget = DB::table('company_budget_pm')
                        ->where('month',$d->month)
                        ->where('budget_id',$r0_budgetid)->first();
        
                    return number_format($orig_budget->amount,2,'.',',');				
                });
        
                // $company_params = CompanyProfile::first();
                $budget_refocus = (int)$company_params->budget_refocus;
                $refocus_budget_id =  DB::table('company_budget')
                                            ->where('year',$year)->get();
                $refocus_budget =  DB::table('company_budget_pm')->get();
                for ($i=1; $i <= $budget_refocus; $i++) { 
                    $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                        $refocus_budget_id = collect($refocus_budget_id)
                                            ->where('refocus',$i)->first();
                        if(isset($refocus_budget_id)){
                            $refocus_budget = collect($refocus_budget)
                                            ->where('budget_id',$refocus_budget_id->budget_id)
                                            ->where('month',$d->month)
                                            ->first();
                            return number_format($refocus_budget->amount, 2, '.', ',');
                        }else {
                            return 0;
                        }
                    });
                }
                return $dt ->make(true);
    }

    public function getBranchBudget(Request $request){
        
        $budget_id = $request->budget_id;
        $branch = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'branch/';
        $branches = $branch->get($url);
        $branches = json_decode($branches->getBody()->getContents());

        $budget_year = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        // dd($budget_year);
        $year = $budget_year->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        // ->where(function($d){
                        //     $d->where('status','ACTIVE')
                        //         ->orWhere('status','REFOCUSED');
                        // })
                        ->get();
        // $activeBudget = DB::table('company_budget')
        //                 ->where('year',$year)
        //                 ->where('status','ACTIVE')
        //                 ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;
        // $budget = DB::table('branch_budget')->where('budget_id',$activeBudget->budget_id)->get();
        $budget = DB::table('branch_budget')->where('budget_id',$budget_id)->get();
        // dd($budget);

        $dt =  Datatables::of($budget)
        ->editColumn('branch_rate', function($d){
            return round($d->branch_rate, 3);
        })
        ->addColumn('branchname',function($d) use(&$branches){
            foreach($branches as $branch){
                if($d->branch_id == $branch->branch ){
                    return $branch->description;
                }
            }				
        })
        ->addColumn('original_budget',function($d) use($r0_budgetid){
            $orig_budget = DB::table('branch_budget')
                ->where('branch_id',$d->branch_id)
                ->where('budget_id', $r0_budgetid)
                ->first();

            return number_format($orig_budget->amount,2,'.',',');				
        })
        ->addColumn('permonth', function($d){
            return 
                '<a href="' .route("budget.permonth",["branch_id"=>$d->branch_id, "budget_id"=>$d->budget_id]) .'">
                    <button class="btn btn-sm alert-primary btn-sm">
                    <span class="fa fa-calendar"></span>
                    Per month
                    </button>
                    </a>

                    <a href="' .route("budget.perclass",["branch_id"=>$d->branch_id, "budget_id"=>$d->budget_id]) .'">
                    <button class="btn btn-sm alert-primary btn-sm">
                    <span class="fa fa-calendar"></span>
                    Per class
                    </button>
                    </a>'
                    ;
        })
        ->editColumn('amount', function($d){
            return number_format($d->amount, 2, '.', ',');
        });

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;

        for ($i=1; $i <= $budget_refocus; $i++) { 
            $dt->addColumn('refocus'.$i,function($d) use($year, $i){
                $refocus_budget_id =  DB::table('company_budget')
                                    ->where('year',$year)
                                    ->where('refocus',$i)->first();
                if(isset($refocus_budget_id)){
                    $refocus_budget =  DB::table('branch_budget')
                                    ->where('branch_id',$d->branch_id)
                                    ->where('budget_id',$refocus_budget_id->budget_id)->first();
                    return number_format($refocus_budget->amount, 2, '.', ',');
                }else {
                    return 0;
                }
            });
        }
       return $dt->escapeColumns(['branchname'])
            ->rawColumns(['permonth'])
            ->make(true);
    }

    public function getAgentBudget(Request $request){
        $budget_id = $request->budget_id;
        // $budget = DB::table('agent_budget')->where('budget_id',$budget_id)->get();
        
        $agents = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'intermediaries/';
        $intermediaries = $agents->get($url);
        $intermediaries = json_decode($intermediaries->getBody()->getContents());
        // dd($intermediaries);
        $branch = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'branch/';
        $branches = $branch->get($url);
        $branches = json_decode($branches->getBody()->getContents());

        $budget_year = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year = $budget_year->year;

        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        // ->where(function($d){
                        //     $d->where('status','ACTIVE')
                        //         ->orWhere('status','REFOCUSED');
                        // })
                        ->get();

        // $activeBudget = DB::table('company_budget')
        //                 ->where('year',$year)
        //                 ->where('status','ACTIVE')
        //                 ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;
        // $budget = DB::table('agent_budget')->where('budget_id',$activeBudget->budget_id)->get();
        $budget = DB::table('agent_budget')->where('budget_id',$budget_id)->get();
        // dd($budget);

        $dt = Datatables::of($budget)
        ->editColumn('agent_rate', function($d){
            return round($d->agent_rate, 3);
        })
        ->addColumn('agentname',function($d) use(&$intermediaries){
            foreach($intermediaries as $agent){
                if(($d->agent_id == $agent->agent) && ($d->branch_id == $agent->branch)){
                    return $agent->name;
                }
            }				
        })
        ->addColumn('branchname',function($d) use(&$branches){
            foreach($branches as $branch){
                if($d->branch_id == $branch->branch ){
                    return $branch->description;
                }
            }				
        })
        ->addColumn('permonth', function($d){
            return 
                '<a href="' .route("agentbudget.permonth",["agent_id"=>$d->agent_id, "budget_id"=>$d->budget_id,"branch_id"=>$d->branch_id]) .'">
                    <button class="btn btn-sm alert-primary btn-sm">
                    <span class="fa fa-calendar"></span>
                    Details
                    </button>
                    </a>

                    <a href="' .route("agentbudget.perclass",["agent_id"=>$d->agent_id, "budget_id"=>$d->budget_id,"branch_id"=>$d->branch_id]) .'">
                    <button class="btn btn-sm alert-primary btn-sm">
                    <span class="fa fa-calendar"></span>
                    Per class
                    </button>
                    </a>'
                    ;
        })
        ->addColumn('original_budget',function($d) use($r0_budgetid){
            $orig_budget = DB::table('agent_budget')
                ->where('agent_id',$d->agent_id)
                ->where('branch_id',$d->branch_id)
                ->where('budget_id',$r0_budgetid)->first();

            return number_format($orig_budget->amount,2,'.',',');				
        });

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        $refocus_budget_id =  DB::table('company_budget')
                                    ->where('year',$year)->get();
        $refocus_budget =  DB::table('agent_budget')->get();
        for ($i=1; $i <= $budget_refocus; $i++) { 
            $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                $refocus_budget_id = collect($refocus_budget_id)
                                    ->where('refocus',$i)->first();
                if(isset($refocus_budget_id)){
                    $refocus_budget = collect($refocus_budget)
                                    ->where('agent_id',$d->agent_id)
                                    ->where('branch_id',$d->branch_id)
                                    ->where('budget_id',$refocus_budget_id->budget_id)->first();
                    return number_format($refocus_budget->amount, 2, '.', ',');
                }else {
                    return 0;
                }
            });
        }
        
        return $dt->escapeColumns(['agentname','branchname'])
        ->rawColumns(['permonth'])
        ->make(true);
    }

    public function getClassBudget(Request $request){
        $budget_id = $request->budget_id;
        // $budget = DB::table('agent_budget')->where('budget_id',$budget_id)->get();
        
        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'class/';
        $classes = $conn->get($url);
        $classes = json_decode($classes->getBody()->getContents());

        $budget_year = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year = $budget_year->year;

        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        // ->where(function($d){
                        //     $d->where('status','ACTIVE')
                        //         ->orWhere('status','REFOCUSED');
                        // })
                        ->get();

        // $activeBudget = DB::table('company_budget')
        //                 ->where('year',$year)
        //                 ->where('status','ACTIVE')
        //                 ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;
        // $budget = DB::table('agent_budget')->where('budget_id',$activeBudget->budget_id)->get();
        $budget = DB::table('class_budget')->where('budget_id',$budget_id)->get();
        // dd($budget);

        $dt = Datatables::of($budget)
        ->editColumn('class_rate', function($d){
            return round($d->class_rate, 3);
        })
        ->addColumn('classname',function($d) use(&$classes){
            foreach($classes as $class){
                if(($d->class == $class->class_field)){
                    return $class->description;
                }
            }				
        })
        ->addColumn('details', function($d){
            return 
                // '<a href="' .route("agentbudget.permonth",["class_id"=>$d->class, "budget_id"=>$d->budget_id]) .'">
                //     <button class="btn btn-sm alert-primary btn-sm">
                //     <span class="fa fa-calendar"></span>
                //     Details
                //     </button>
                //     </a>
                '
                    <a href="' .route("class_br_budget",["class_id"=>$d->class, "budget_id"=>$d->budget_id]) .'">
                    <button class="btn btn-sm alert-primary btn-sm">
                    <span class="fa fa-calendar"></span>
                    Per Branch
                    </button>
                    </a>
                    <a href="' .route("class_ag_budget",["class_id"=>$d->class, "budget_id"=>$d->budget_id]) .'">
                    <button class="btn btn-sm alert-primary btn-sm">
                    <span class="fa fa-calendar"></span>
                    Per Agent
                    </button>
                    </a>'
                    ;
        })
        ->addColumn('original_budget',function($d) use($r0_budgetid){
            $orig_budget = DB::table('class_budget')
                ->where('class', $d->class)
                ->where('budget_id',$r0_budgetid)->first();

            return number_format($orig_budget->amount,2,'.',',');				
        });

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        $refocus_budget_id =  DB::table('company_budget')
                                    ->where('year',$year)->get();
        $refocus_budget =  DB::table('class_budget')->get();
        for ($i=1; $i <= $budget_refocus; $i++) { 
            $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                $refocus_budget_id = collect($refocus_budget_id)
                                    ->where('refocus',$i)->first();
                if(isset($refocus_budget_id)){
                    $refocus_budget = collect($refocus_budget)
                                    ->where('class',$d->class)
                                    ->where('budget_id',$refocus_budget_id->budget_id)->first();
                    return number_format($refocus_budget->amount, 2, '.', ',');
                }else {
                    return 0;
                }
            });
        }
        
        return $dt->escapeColumns(['classname'])
        ->rawColumns(['permonth'])
        ->make(true);
    }

    public function companyPerformance(Request $request){
        $budget_id = $request->budget_id;
        $companybudget = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year['year'] = $companybudget->year;
        $budget_amount = $companybudget->amount;

        $conn = new \GuzzleHttp\Client();
        $url=''.ENV('API_URL').'budgetprevyear/';
        $response = $conn->post($url,  ['form_params'=>$year]);

        $url = ''.ENV('API_URL').'budgetcompanypm/';
        $performance = $conn->get($url);
        $performance = json_decode($performance->getBody()->getContents());

        $pm_performance = array();
        for ($i=1; $i <= 12 ; $i++) { 
            $exists = collect($performance)
                ->where('month',$i)
                ->count();
            if ($exists==0) {
                array_push($pm_performance, 0);
            }else {
                $amount = collect($performance)
                        ->where('month',$i)->first();
                $amount = abs($amount->gross_amount);
                array_push($pm_performance, $amount);
            }
        }
        $pm_performance = array_map('intval',$pm_performance);

        $url = ''.ENV('API_URL').'budgetbranch/';
        $branchperformance = $conn->get($url);
        $branchperformance = json_decode($branchperformance->getBody()->getContents());
        $company_performance = 0;
        foreach($branchperformance as $p){
            $company_performance += abs($p->gross_amount);
        }
        $variance = number_format((($company_performance-$budget_amount)/$budget_amount), 2, '.', ',')*100;
        $agentbudget = DB::table('agent_budget')->where('budget_id',$budget_id)->first();

        $monthbudgets = DB::table('company_budget_pm')
                        ->where('budget_id',$budget_id)
                        ->orderBy('month','ASC')
                        ->get();
        
        $monthbudgets = collect($monthbudgets)->pluck('amount');
        $monthbudgets = array_map('intval',json_decode($monthbudgets));

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        return view('gb.budgets.com_performance', [
            'budget_id'=>$budget_id,
            'budget_amount'=>number_format($budget_amount),
            'company_performance'=>number_format($company_performance),
            'year'=>$companybudget->year,
            'variance'=>$variance,
            'budget_refocus'=>$budget_refocus,
            'pm_performance'=>$pm_performance,
            'monthbudgets'=>$monthbudgets
        ]);
    }

    public function branchPerformanceDetails(Request $request){
        $budget_id = $request->budget_id;
        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        return view('gb.budgets.branchperformance', [
            'budget_refocus'=>$budget_refocus,
            'budget_id'=>$budget_id
        ]);
    }

    public function classPerformanceDetails(Request $request){
        $budget_id = $request->budget_id;
        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        return view('gb.budgets.classperformance', [
            'budget_refocus'=>$budget_refocus,
            'budget_id'=>$budget_id
        ]);
    }

    public function agentPerformanceDetails(Request $request){
        $budget_id = $request->budget_id;
        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        return view('gb.budgets.agentperformance', [
            'budget_refocus'=>$budget_refocus,
            'budget_id'=>$budget_id
        ]);
    }

    public function companyPerformancePM(Request $request){
        $budget_id = $request->budget_id;
        $bgt = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        
        // dd($monthbudgets);


        $budget_year = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year = $budget_year->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED');
                        })->get();

        $activeBudget = DB::table('company_budget')
                        ->where('year',$year)
                        ->where('status','ACTIVE')
                        ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;
        $monthbudgets = DB::table('company_budget_pm')
                        ->where('budget_id',$r0_budgetid)
                        ->orderBy('month','ASC')
                        ->get();


        $conn = new \GuzzleHttp\Client();
        
        $currentyear['year'] = $bgt->year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $currentperformance = $conn->post($url,  ['form_params'=>$currentyear]);

        $url = ''.ENV('API_URL').'budgetcompanypm/';
        $performance = $conn->get($url);
        $performance = json_decode($performance->getBody()->getContents());
        // dd($performance);

        $prevyear['year'] = $bgt->rate_year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $previousperformance = $conn->post($url,  ['form_params'=>$prevyear]);

        $url = ''.ENV('API_URL').'budgetcompanypm/';
        $prevperformance = $conn->get($url);
        $prevperformance = json_decode($prevperformance->getBody()->getContents());
        // dd($prevperformance);
        
        $dt = Datatables::of($monthbudgets)
            ->addColumn('prevperformance', function($d) use(&$prevperformance){
                foreach ($prevperformance as $performance) {
                   if ($performance->month == $d->month) {
                       return number_format(abs($performance->gross_amount),2,'.',',');
                   }
                }
                $exists = collect($prevperformance)->where('month',$d->month)->count();
                if ($exists==0) {
                    return 0;
                }
            })
            ->addColumn('performance', function($d) use(&$performance){
                foreach ($performance as $perf) {
                   if ($perf->month == $d->month) {
                       return number_format(abs($perf->gross_amount),2,'.',',');
                   }
                }
                $exists = collect($performance)->where('month',$d->month)->count();
                if ($exists==0) {
                    return 0;
                }
            });
            $currentbudget = DB::table('company_budget')
                                        ->where('status','ACTIVE')->first();
            $currentpmbudget = DB::table('company_budget_pm')
                                    ->where('budget_id',$currentbudget->budget_id)->get();

            $dt->addColumn('variance',function($d) use(&$performance,$currentpmbudget){
                $month = collect($currentpmbudget)->where('month',$d->month)->first();
                foreach($performance as $perf){
                    if($d->month == $perf->month ){
                        $variance = ((abs($perf->gross_amount) - $month->amount)/$month->amount)*100;
                        // dd($variance);
                        if($variance < 0){
                            return '<span class="fa fa-arrow-down text-danger"></span>
                                    <span>'.number_format(($variance*-1), 2, '.', ',').'</span>';
                        }else{
                            return '<span class="fa fa-arrow-up text-success"></span>
                                    <span>'.number_format($variance, 2, '.', ',').'</span>';
                        }
                    }
                }
                $exists = collect($performance)->where('month',$d->month)->count();
                if ($exists==0) {
                    return '<span class="fa fa-arrow-down text-danger"></span>
                             <span>100</span>';
                }				
            })
            ->editColumn('month', function($d){
                if ($d->month == 1) {
                    return "JANUARY";
                }else if($d->month == 2) {
                    return "FEBRUARY";
                }else if($d->month == 3) {
                    return "MARCH";
                }else if($d->month == 4) {
                    return "APRIL";
                }else if($d->month == 5) {
                    return "MAY";
                }else if($d->month == 6) {
                    return "JUNE";
                }else if($d->month == 7) {
                    return "JULY";
                }else if($d->month == 8) {
                    return "AUGUST";
                }else if($d->month == 9) {
                    return "SEPTEMBER";
                }else if($d->month == 10) {
                    return "OCTOBER";
                }else if($d->month == 11) {
                    return "NOVEMBER";
                }else{
                    return "DECEMBER";
                }
            })
            ->addColumn('original_budget',function($d) use($r0_budgetid){
                $orig_budget = DB::table('company_budget_pm')
                    ->where('month',$d->month)
                    ->where('budget_id',$r0_budgetid)->first();
    
                return number_format($orig_budget->amount,2,'.',',');				
            });
    
            $company_params = CompanyProfile::first();
            $budget_refocus = (int)$company_params->budget_refocus;
            $refocus_budget_id =  DB::table('company_budget')
                                        ->where('year',$year)->get();
            $refocus_budget =  DB::table('company_budget_pm')->get();
            for ($i=1; $i <= $budget_refocus; $i++) { 
                $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                    $refocus_budget_id = collect($refocus_budget_id)
                                        ->where('refocus',$i)->first();
                    if(isset($refocus_budget_id)){
                        $refocus_budget = collect($refocus_budget)
                                        ->where('month',$d->month)
                                        ->where('budget_id',$refocus_budget_id->budget_id)->first();
                        return number_format($refocus_budget->amount, 2, '.', ',');
                    }else {
                        return 0;
                    }
                });
            }
            
            return $dt->rawColumns(['variance'])
            ->make(true);
    }

    public function branchPerformance(Request $request){
        $budget_id = $request->budget_id;
        $bgt = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        // $branchbudgets = DB::table('branch_budget')->where('budget_id',$budget_id)->get();
        
        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'branch/';
        $branches = $conn->get($url);
        $branches = json_decode($branches->getBody()->getContents());
        
        $currentyear['year'] = $bgt->year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $currentperformance = $conn->post($url,  ['form_params'=>$currentyear]);

        $url = ''.ENV('API_URL').'budgetbranch/';
        $branchperformance = $conn->get($url);
        $branchperformance = json_decode($branchperformance->getBody()->getContents());

        $prevyear['year'] = $bgt->rate_year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $previousperformance = $conn->post($url,  ['form_params'=>$prevyear]);

        
        $url = ''.ENV('API_URL').'budgetbranch/';
        $branchprevperformance = $conn->get($url);
        $branchprevperformance = json_decode($branchprevperformance->getBody()->getContents());

        // dd($branchperformance);

        $year = $bgt->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED');
                        })->get();

        $activeBudget = DB::table('company_budget')
                        ->where('year',$year)
                        ->where('status','ACTIVE')
                        ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;
        $branchbudgets = DB::table('branch_budget')->where('budget_id',$r0_budgetid)->get();
        

        $dt = Datatables::of($branchbudgets)
        ->addColumn('prevperformance',function($d) use(&$branchprevperformance){
            foreach($branchprevperformance as $performance){
                if($d->branch_id == $performance->branch ){
                    return number_format(abs($performance->gross_amount), 2, '.', ',');
                }
            }
            $exists = collect($branchprevperformance)->where('branch',$d->branch_id)->count();
            if ($exists==0) {
                return 0;
            }					
        })
        ->addColumn('branchname',function($d) use(&$branches){
            foreach($branches as $branch){
                if($d->branch_id == $branch->branch ){
                    return $branch->description;
                }
            }				
        })
        ->addColumn('performance',function($d) use(&$branchperformance){
            foreach($branchperformance as $performance){
                if($d->branch_id == $performance->branch ){
                    return number_format(abs($performance->gross_amount), 2, '.', ',');
                }
            }
            $exists = collect($branchperformance)->where('branch',$d->branch_id)->count();
            if ($exists==0) {
                return 0;
            }				
        });
        $currentbudget = DB::table('company_budget')
                                        ->where('status','ACTIVE')->first();
        $currentpmbudget = DB::table('branch_budget')
                                    ->where('budget_id',$currentbudget->budget_id)->get();
        $dt->addColumn('variance',function($d) use(&$branchperformance, $currentpmbudget){
            $br = collect($currentpmbudget)->where('branch_id',$d->branch_id)->first();
            // dd($br->amount);
            foreach($branchperformance as $performance){
                if($d->branch_id == $performance->branch ){
                    // dd($performance->gross_amount);
                    $variance = ((abs($performance->gross_amount) - $br->amount)/$br->amount)*100;
                    if($variance < 0){
                        return '<span class="fa fa-arrow-down text-danger"></span>
                                <span>'.(number_format(($variance*-1), 2, '.', ',')).'</span>';
                    }else{
                        return '<span class="fa fa-arrow-up text-success"></span>
                                <span>'.(number_format($variance, 2, '.', ',')).'</span>';
                    }
                }
            }	
            $exists = collect($branchperformance)->where('branch',$d->branch_id)->count();
            if ($exists==0) {
                return '<span class="fa fa-arrow-down text-danger"></span>
                         <span>100</span>';
            }			
        })
        ->addColumn('permonth',function($d) use(&$branchperformance, $budget_id){
            foreach($branchperformance as $performance){
                if($d->branch_id == $performance->branch ){
                    $variance = ((abs($performance->gross_amount) - $d->amount)/$d->amount);
                    if($variance  == NULL){
                        return ;
                    }else{
                        return '<a href="' .route("branch.performancepm",["budget_id"=>$budget_id,"branch_id"=>$d->branch_id]) .'">
                                    <button class="btn btn-sm alert-primary btn-sm">
                                    <span class="fa fa-calendar"></span>
                                    Details
                                    </button>
                                </a>'
                                ;
                    }
                }
            }		
        })
        ->addColumn('original_budget',function($d) use($r0_budgetid){
            $orig_budget = DB::table('branch_budget')
                ->where('branch_id',$d->branch_id)
                ->where('budget_id',$r0_budgetid)->first();

            return number_format($orig_budget->amount,2,'.',',');				
        });

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        $refocus_budget_id =  DB::table('company_budget')
                                    ->where('year',$year)->get();
        $refocus_budget =  DB::table('branch_budget')->get();
        for ($i=1; $i <= $budget_refocus; $i++) { 
            $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                $refocus_budget_id = collect($refocus_budget_id)
                                    ->where('refocus',$i)->first();
                if(isset($refocus_budget_id)){
                    $refocus_budget = collect($refocus_budget)
                                    ->where('branch_id',$d->branch_id)
                                    ->where('budget_id',$refocus_budget_id->budget_id)->first();
                    return number_format($refocus_budget->amount, 2, '.', ',');
                }else {
                    return 0;
                }
            });
        }
        return $dt->rawColumns(['variance','permonth'])
        ->make(true);
    }

    public function agentPerformance(Request $request){
        $budget_id = $request->budget_id;
        $bgt = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        // $budget = DB::table('agent_budget')->where('budget_id',$budget_id)->get();

        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'intermediaries/';
        $intermediaries = $conn->get($url);
        $intermediaries = json_decode($intermediaries->getBody()->getContents());
        // dd($intermediaries);
        $url = ''.ENV('API_URL').'branch/';
        $branches = $conn->get($url);
        $branches = json_decode($branches->getBody()->getContents());

        $currentyear['year'] = $bgt->year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $currentperformance = $conn->post($url,  ['form_params'=>$currentyear]);

        $url = ''.ENV('API_URL').'budgetagent/';
        $agentperformance = $conn->get($url);
        $agentperformance = json_decode($agentperformance->getBody()->getContents());

        $prevyear['year'] = $bgt->rate_year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $previousperformance = $conn->post($url,  ['form_params'=>$prevyear]);

        $url = ''.ENV('API_URL').'budgetagent/';
        $agentprevperformance = $conn->get($url);
        $agentprevperformance = json_decode($agentprevperformance->getBody()->getContents());

        $year = $bgt->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED');
                        })->get();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;
        $budget = DB::table('agent_budget')->where('budget_id',$r0_budgetid)->get();

        $dt =  Datatables::of($budget)
        ->editColumn('agent_rate', function($d){
            return round($d->agent_rate, 3);
        })
        ->addColumn('agentname',function($d) use(&$intermediaries){
            foreach($intermediaries as $agent){
                if(($d->agent_id == $agent->agent) && ($d->branch_id == $agent->branch)){
                    return $agent->name;
                }
            }				
        })
        ->addColumn('prevperformance',function($d) use(&$agentprevperformance){
            foreach($agentprevperformance as $performance){
                if(($d->agent_id == $performance->agent_no) && ($d->branch_id == $performance->branch)){
                    return number_format(abs($performance->gross_amount), 2, '.', ',');
                }
            }
            $exists = collect($agentprevperformance)
                    ->where('branch',$d->branch_id)
                    ->where('agent_no',$d->agent_id)
                    ->count();
            if ($exists==0) {
                return 0;
            }			
        })
        ->addColumn('branchname',function($d) use(&$branches){
            foreach($branches as $branch){
                if($d->branch_id == $branch->branch ){
                    return $branch->description;
                }
            }				
        })
        ->addColumn('performance',function($d) use(&$agentperformance){
            foreach($agentperformance as $performance){
                if(($d->agent_id == $performance->agent_no) && ($d->branch_id == $performance->branch)){
                    return number_format(abs($performance->gross_amount), 2, '.', ',');
                }
            }
            $exists = collect($agentperformance)
                    ->where('branch',$d->branch_id)
                    ->where('agent_no',$d->agent_id)
                    ->count();
            if ($exists==0) {
                return 0;
            }			
        });
        $currentbudget = DB::table('company_budget')
                                        ->where('status','ACTIVE')->first();
        $currentpmbudget = DB::table('agent_budget')
                                    ->where('budget_id',$currentbudget->budget_id)->get();
        $dt->addColumn('variance',function($d) use(&$agentperformance,$currentpmbudget){
            $br = collect($currentpmbudget)->where('agent_id',$d->agent_id)->where('branch_id',$d->branch_id)->first();
            foreach($agentperformance as $performance){
                if(($d->agent_id == $performance->agent_no) && ($d->branch_id == $performance->branch)){
                    $variance = ((abs($performance->gross_amount) - $br->amount)/$br->amount)*100;
                    if($variance < 0){
                        return '<span class="fa fa-arrow-down text-danger"></span>
                                <span>'.(number_format(($variance*-1), 2, '.', ',')).'</span>';
                    }else{
                        return '<span class="fa fa-arrow-up text-success"></span>
                                <span>'.(number_format($variance, 2, '.', ',')).'</span>';
                    }
                }
            }	
            $exists = collect($agentperformance)
                    ->where('branch',$d->branch_id)
                    ->where('agent_no',$d->agent_id)->count();
            if ($exists==0) {
                return '<span class="fa fa-arrow-down text-danger"></span>
                         <span>100</span>';
            }				
        })
        ->addColumn('permonth',function($d) use(&$agentperformance, $budget_id){
            foreach($agentperformance as $performance){
                if(($d->agent_id == $performance->agent_no) && ($d->branch_id == $performance->branch)){
                    $variance = ((abs($performance->gross_amount) - $d->amount)/$d->amount);
                    if($variance  == NULL){
                        return ;
                    }else{
                        return 
                        '<a href="'.route("agent.performancepm",["agent_id"=>$d->agent_id, "budget_id"=>$budget_id, "branch_id"=>$d->branch_id]).'">
                                    <button class="btn btn-sm alert-primary btn-sm">
                                    <span class="fa fa-calendar"></span>
                                    Per month
                                    </button>
                                </a>
                            <a href="'.route("agent.performanceclass",["agent_id"=>$d->agent_id, "budget_id"=>$budget_id, "branch_id"=>$d->branch_id]).'">
                                <button class="btn btn-sm alert-primary btn-sm">
                                <span class="fa fa-calendar"></span>
                                Per class
                                </button>
                            </a>'
                                ;
                    }
                }
            }		
        })
        
        ->addColumn('original_budget',function($d) use($r0_budgetid){
            $orig_budget = DB::table('agent_budget')
                ->where('agent_id',$d->agent_id)
                ->where('branch_id',$d->branch_id)
                ->where('budget_id',$r0_budgetid)->first();

            return number_format($orig_budget->amount,2,'.',',');				
        });

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        $refocus_budget_id =  DB::table('company_budget')
                                    ->where('year',$year)->get();
        $refocus_budget =  DB::table('agent_budget')->get();
        for ($i=1; $i <= $budget_refocus; $i++) { 
            $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                $refocus_budget_id = collect($refocus_budget_id)
                                    ->where('refocus',$i)->first();
                if(isset($refocus_budget_id)){
                    $refocus_budget = collect($refocus_budget)
                                    ->where('agent_id',$d->agent_id)
                                    ->where('branch_id',$d->branch_id)
                                    ->where('budget_id',$refocus_budget_id->budget_id)->first();
                    return number_format($refocus_budget->amount, 2, '.', ',');
                }else {
                    return 0;
                }
            });
        }
        return $dt->escapeColumns(['agentname','branchname','performance','variance'])
        ->rawColumns(['variance','permonth'])
        ->make(true);

    }

    public function classPerformance(Request $request){
        $budget_id = $request->budget_id;
        $bgt = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        
        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'classes/';
        $classes = $conn->get($url);
        $classes = json_decode($classes->getBody()->getContents());
        
        $currentyear['year'] = $bgt->year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $currentperformance = $conn->post($url,  ['form_params'=>$currentyear]);

        $url = ''.ENV('API_URL').'budgetclass/';
        $classperformance = $conn->get($url);
        $classperformance = json_decode($classperformance->getBody()->getContents());

        $prevyear['year'] = $bgt->rate_year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $previousperformance = $conn->post($url,  ['form_params'=>$prevyear]);

        
        $url = ''.ENV('API_URL').'budgetclass/';
        $classprevperformance = $conn->get($url);
        $classprevperformance = json_decode($classprevperformance->getBody()->getContents());


        $year = $bgt->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED');
                        })->get();

        $activeBudget = DB::table('company_budget')
                        ->where('year',$year)
                        ->where('status','ACTIVE')
                        ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;
        $classbudgets = DB::table('class_budget')->where('budget_id',$r0_budgetid)->get();
        

        $dt = Datatables::of($classbudgets)
        ->addColumn('prevperformance',function($d) use(&$classprevperformance){
            foreach($classprevperformance as $performance){
                if($d->class == $performance->bclass ){
                    return number_format(abs($performance->gross_amount), 2, '.', ',');
                }
            }
            $exists = collect($classprevperformance)->where('bclass',$d->class)->count();
            if ($exists==0) {
                return 0;
            }					
        })
        ->addColumn('class',function($d) use(&$classes){
            foreach($classes as $class){
                if($d->class == $class->class_field ){
                    return $class->description;
                }
            }				
        })
        ->addColumn('performance',function($d) use(&$classperformance){
            foreach($classperformance as $performance){
                if($d->class == $performance->bclass ){
                    return number_format(abs($performance->gross_amount), 2, '.', ',');
                }
            }
            $exists = collect($classperformance)->where('bclass',$d->class)->count();
            if ($exists==0) {
                return 0;
            }				
        });
        $currentbudget = DB::table('company_budget')
                                        ->where('status','ACTIVE')->first();
        $currentclbudget = DB::table('class_budget')
                                    ->where('budget_id',$currentbudget->budget_id)->get();
        $dt->addColumn('variance',function($d) use(&$classperformance, $currentclbudget){
            $cl = collect($currentclbudget)->where('class',$d->class)->first();
            // dd($br->amount);
            foreach($classperformance as $performance){
                if($d->class == $performance->bclass ){
                    // dd($performance->gross_amount);
                    $variance = ((abs($performance->gross_amount) - $cl->amount)/$cl->amount)*100;
                    if($variance < 0){
                        return '<span class="fa fa-arrow-down text-danger"></span>
                                <span>'.(number_format(($variance*-1), 2, '.', ',')).'</span>';
                    }else{
                        return '<span class="fa fa-arrow-up text-success"></span>
                                <span>'.(number_format($variance, 2, '.', ',')).'</span>';
                    }
                }
            }	
            $exists = collect($classperformance)->where('bclass',$d->class)->count();
            if ($exists==0) {
                return '<span class="fa fa-arrow-down text-danger"></span>
                         <span>100</span>';
            }			
        })
        // ->addColumn('permonth',function($d) use(&$branchperformance, $budget_id){
        //     foreach($branchperformance as $performance){
        //         if($d->branch_id == $performance->branch ){
        //             $variance = ((abs($performance->gross_amount) - $d->amount)/$d->amount);
        //             if($variance  == NULL){
        //                 return ;
        //             }else{
        //                 return '<a href="' .route("branch.performancepm",["budget_id"=>$budget_id,"branch_id"=>$d->branch_id]) .'">
        //                             <button class="btn btn-sm alert-primary btn-sm">
        //                             <span class="fa fa-calendar"></span>
        //                             Details
        //                             </button>
        //                         </a>'
        //                         ;
        //             }
        //         }
        //     }		
        // })
        ->addColumn('original_budget',function($d) use($r0_budgetid){
            $orig_budget = DB::table('class_budget')
                ->where('class',$d->class)
                ->where('budget_id',$r0_budgetid)->first();

            return number_format($orig_budget->amount,2,'.',',');				
        });

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        $refocus_budget_id =  DB::table('company_budget')
                                    ->where('year',$year)->get();
        $refocus_budget =  DB::table('class_budget')->get();
        for ($i=1; $i <= $budget_refocus; $i++) { 
            $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                $refocus_budget_id = collect($refocus_budget_id)
                                    ->where('refocus',$i)->first();
                if(isset($refocus_budget_id)){
                    $refocus_budget = collect($refocus_budget)
                                    ->where('class',$d->class)
                                    ->where('budget_id',$refocus_budget_id->budget_id)->first();
                    return number_format($refocus_budget->amount, 2, '.', ',');
                }else {
                    return 0;
                }
            });
        }
        return $dt->rawColumns(['variance'])
        ->make(true);
    }

    public function classAgentBudget(Request $request){
        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'class/';
        $classes = $conn->get($url);
        $classes = json_decode($classes->getBody()->getContents());

        $class = collect($classes)->where('class_field', $request->class_id)->first();
        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        return view("admin.budget.classagentbudget",
                    [
                        'class' => $class,
                        'budget_id' => $request->budget_id,
                        'budget_refocus' => $budget_refocus
                    ]
                    );
    } 

    public function classBranchBudget(Request $request){
        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'class/';
        $classes = $conn->get($url);
        $classes = json_decode($classes->getBody()->getContents());

        $class = collect($classes)->where('class_field', $request->class_id)->first();
        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        return view("admin.budget.classbranchbudget",
                    [
                        'class' => $class,
                        'budget_id' => $request->budget_id,
                        'budget_refocus' => $budget_refocus
                    ]
                    );
    }

    public function getClassAgentBudget(Request $request){
        $budget_id = $request->budget_id;
        $class_id = $request->class_id;
        // dd($class_id);
        
        $agents = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'intermediaries/';
        $intermediaries = $agents->get($url);
        $intermediaries = json_decode($intermediaries->getBody()->getContents());
        $branch = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'branch/';
        $branches = $branch->get($url);
        $branches = json_decode($branches->getBody()->getContents());

        $budget_year = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year = $budget_year->year;

        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        // ->where(function($d){
                        //     $d->where('status','ACTIVE')
                        //         ->orWhere('status','REFOCUSED');
                        // })
                        ->get();

        // $activeBudget = DB::table('company_budget')
        //                 ->where('year',$year)
        //                 ->where('status','ACTIVE')
        //                 ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;
        // $budget = DB::table('agent_budget')->where('budget_id',$activeBudget->budget_id)->get();
        $budget = DB::table('agent_class_budget')->where('budget_id',$budget_id)
                            ->where('class', $class_id)->get();
        // dd($budget);

        $dt = Datatables::of($budget)
        ->editColumn('class_rate', function($d){
            return round($d->class_rate, 3);
        })
        ->addColumn('agentname',function($d) use(&$intermediaries){
            foreach($intermediaries as $agent){
                if(($d->agent_id == $agent->agent) && ($d->branch_id == $agent->branch)){
                    return $agent->name;
                }
            }				
        })
        ->addColumn('branchname',function($d) use(&$branches){
            foreach($branches as $branch){
                if($d->branch_id == $branch->branch ){
                    return $branch->description;
                }
            }				
        })
        ->addColumn('original_budget',function($d) use($r0_budgetid){
            $orig_budget = DB::table('agent_class_budget')
                ->where('agent_id',$d->agent_id)
                ->where('class',$d->class)
                ->where('branch_id',$d->branch_id)
                ->where('budget_id',$r0_budgetid)->first();

            return number_format($orig_budget->amount,2,'.',',');				
        });

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        $refocus_budget_id =  DB::table('company_budget')
                                    ->where('year',$year)->get();
        $refocus_budget =  DB::table('agent_class_budget')->get();
        for ($i=1; $i <= $budget_refocus; $i++) { 
            $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                $refocus_budget_id = collect($refocus_budget_id)
                                    ->where('refocus',$i)->first();
                if(isset($refocus_budget_id)){
                    $refocus_budget = collect($refocus_budget)
                                    ->where('agent_id',$d->agent_id)
                                    ->where('class',$d->class)
                                    ->where('branch_id',$d->branch_id)
                                    ->where('budget_id',$refocus_budget_id->budget_id)->first();
                    return number_format($refocus_budget->amount, 2, '.', ',');
                }else {
                    return 0;
                }
            });
        }
        // dd($dt);
        return $dt->escapeColumns(['agentname','branchname'])
        ->make(true);
    }

    public function getClassBranchBudget(Request $request){
        $budget_id = $request->budget_id;
        $class_id = $request->class_id;

        $branch = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'branch/';
        $branches = $branch->get($url);
        $branches = json_decode($branches->getBody()->getContents());

        $budget_year = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year = $budget_year->year;

        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->get();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;
        $budget = DB::table('branch_class_budget')->where('budget_id',$budget_id)
                            ->where('class', $class_id)->get();

        $dt = Datatables::of($budget)
        ->editColumn('class_rate', function($d){
            return round($d->class_rate, 3);
        })
        ->addColumn('branchname',function($d) use(&$branches){
            foreach($branches as $branch){
                if($d->branch_id == $branch->branch ){
                    return $branch->description;
                }
            }				
        })
        ->addColumn('original_budget',function($d) use($r0_budgetid){
            $orig_budget = DB::table('branch_class_budget')
                ->where('class',$d->class)
                ->where('branch_id',$d->branch_id)
                ->where('budget_id',$r0_budgetid)->first();

            return number_format($orig_budget->amount,2,'.',',');				
        });

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        $refocus_budget_id =  DB::table('company_budget')
                                    ->where('year',$year)->get();
        $refocus_budget =  DB::table('agent_class_budget')->get();
        for ($i=1; $i <= $budget_refocus; $i++) { 
            $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                $refocus_budget_id = collect($refocus_budget_id)
                                    ->where('refocus',$i)->first();
                if(isset($refocus_budget_id)){
                    $refocus_budget = collect($refocus_budget)
                                    ->where('class',$d->class)
                                    ->where('branch_id',$d->branch_id)
                                    ->where('budget_id',$refocus_budget_id->budget_id)->first();
                    return number_format($refocus_budget->amount, 2, '.', ',');
                }else {
                    return 0;
                }
            });
        }
        // dd($dt);
        return $dt->escapeColumns(['branchname'])
        ->make(true);
    }

    public function budgetBranchPerMonth(Request $request){
        $branch_id = $request->branch_id;
        $budget_id = $request->budget_id;

        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'branch/';
        $branches = $conn->get($url);
        $branches = json_decode($branches->getBody()->getContents());
        $branches = collect($branches)->where('branch',$branch_id)->first();
        if ($branches) {
            $branchname = $branches->description;
        } else {
            $branchname = '';
        }

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        
        return view('gb.budgets.budgetbranchpermonth',
                    [
                        'branch'=>$branch_id, 
                        'budget'=>$budget_id, 
                        'branchname'=>$branchname,
                        'budget_refocus'=>$budget_refocus,
                    ]);
    }

    public function budgetBranchPerClass(Request $request){
        $branch_id = $request->branch_id;
        $budget_id = $request->budget_id;

        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'branch/';
        $branches = $conn->get($url);
        $branches = json_decode($branches->getBody()->getContents());
        $branches = collect($branches)->where('branch',$branch_id)->first();
        if ($branches) {
            $branchname = $branches->description;
        } else {
            $branchname = '';
        }

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        
        return view('gb.budgets.budgetbranchperclass',
                    [
                        'branch'=>$branch_id, 
                        'budget'=>$budget_id, 
                        'branchname'=>$branchname,
                        'budget_refocus'=>$budget_refocus,
                    ]);
    }

    public function budgetAgentPerMonth(Request $request){
        $agent_id = $request->agent_id;
        $budget_id = $request->budget_id;
        $branch_id = $request->branch_id;

        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'intermediaries/';
        $intermediaries = $conn->get($url);
        $intermediaries = json_decode($intermediaries->getBody()->getContents());

        $intermediaries = collect($intermediaries)->where('agent',$agent_id)->first();
        if ($intermediaries) {
            $agentname = $intermediaries->name;
        }else {
            $agentname = '';
        }
        

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        return view('gb.budgets.budgetagentpermonth',
                    [
                        'agent'=>$agent_id, 
                        'budget'=>$budget_id, 
                        'agentname'=>$agentname,
                        'branch'=>$branch_id,
                        'budget_refocus'=>$budget_refocus,
                    ]);
    }

    public function budgetAgentPerClass(Request $request){
        $agent_id = $request->agent_id;
        $budget_id = $request->budget_id;
        $branch_id = $request->branch_id;

        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'intermediaries/';
        $intermediaries = $conn->get($url);
        $intermediaries = json_decode($intermediaries->getBody()->getContents());

        $intermediaries = collect($intermediaries)->where('agent',$agent_id)->first();
        if ($intermediaries) {
            $agentname = $intermediaries->name;
        }else {
            $agentname = '';
        }
        

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        return view('gb.budgets.budgetagentperclass',
                    [
                        'agent'=>$agent_id, 
                        'budget'=>$budget_id, 
                        'agentname'=>$agentname,
                        'branch'=>$branch_id,
                        'budget_refocus'=>$budget_refocus,
                    ]);
    }

    public function getBudgetBranchPM($branch_id,$budget_id){
       
        $bgt = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year = $bgt->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED')
                                ->orWhere('status','PENDING');
                        })->get();

        $activeBudget = DB::table('company_budget')
                        ->where('year',$year)
                        ->where('status','ACTIVE')
                        ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;

        $branch_budgets = DB::table('branch_month_budget')
                        ->where('branch_id',$branch_id)
                        ->where('budget_id',$r0_budgetid)
                        ->orderBy('month', 'ASC')
                        ->get();

        $dt = Datatables::of($branch_budgets)
                ->editColumn('month', function($d){
                    if ($d->month == 1) {
                        return "JANUARY";
                    }else if($d->month == 2) {
                        return "FEBRUARY";
                    }else if($d->month == 3) {
                        return "MARCH";
                    }else if($d->month == 4) {
                        return "APRIL";
                    }else if($d->month == 5) {
                        return "MAY";
                    }else if($d->month == 6) {
                        return "JUNE";
                    }else if($d->month == 7) {
                        return "JULY";
                    }else if($d->month == 8) {
                        return "AUGUST";
                    }else if($d->month == 9) {
                        return "SEPTEMBER";
                    }else if($d->month == 10) {
                        return "OCTOBER";
                    }else if($d->month == 11) {
                        return "NOVEMBER";
                    }else{
                        return "DECEMBER";
                    }
                    
                })
                ->editColumn('month_rate', function($d){
                    return number_format($d->month_rate, 2, '.', ',');
                })
                ->addColumn('original_budget',function($d) use($r0_budgetid){
                    $orig_budget = DB::table('branch_month_budget')
                        ->where('branch_id',$d->branch_id)
                        ->where('month',$d->month)
                        ->where('budget_id',$r0_budgetid)->first();
        
                    return number_format($orig_budget->month_amount,2,'.',',');				
                });
        
                $company_params = CompanyProfile::first();
                $budget_refocus = (int)$company_params->budget_refocus;
                $refocus_budget_id =  DB::table('company_budget')
                                            ->where('year',$year)->get();
                $refocus_budget =  DB::table('branch_month_budget')->get();
                for ($i=1; $i <= $budget_refocus; $i++) { 
                    $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                        $refocus_budget_id = collect($refocus_budget_id)
                                            ->where('refocus',$i)->first();
                        if(isset($refocus_budget_id)){
                            $refocus_budget = collect($refocus_budget)
                                            ->where('branch_id',$d->branch_id)
                                            ->where('month',$d->month)
                                            ->where('budget_id',$refocus_budget_id->budget_id)->first();
                            return number_format($refocus_budget->month_amount, 2, '.', ',');
                        }else {
                            return 0;
                        }
                    });
                }
                return $dt ->make(true);
    }

    public function getBudgetBranchClass($branch_id,$budget_id){
       
        
        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'classes/';
        $classes = $conn->get($url);
        $classes = json_decode($classes->getBody()->getContents());

        $bgt = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year = $bgt->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED')
                                ->orWhere('status','PENDING');
                        })->get();

        $activeBudget = DB::table('company_budget')
                        ->where('year',$year)
                        ->where('status','ACTIVE')
                        ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;

        $branch_budgets = DB::table('branch_class_budget')
                        ->where('branch_id',$branch_id)
                        ->where('budget_id',$r0_budgetid)
                        ->get();

        $dt = Datatables::of($branch_budgets)
                ->addColumn('class', function($d) use(&$classes){
                    foreach ($classes as $class) {
                        if ($d->class == $class->class_field) {
                            return $class->description;
                        }
                    }
                })
                ->editColumn('class_rate', function($d){
                    return number_format($d->class_rate, 2, '.', ',');
                })
                ->addColumn('original_budget',function($d) use($r0_budgetid){
                    $orig_budget = DB::table('branch_class_budget')
                        ->where('branch_id',$d->branch_id)
                        ->where('class',$d->class)
                        ->where('budget_id',$r0_budgetid)->first();
        
                    return number_format($orig_budget->amount,2,'.',',');				
                });
        
                $company_params = CompanyProfile::first();
                $budget_refocus = (int)$company_params->budget_refocus;
                $refocus_budget_id =  DB::table('company_budget')
                                            ->where('year',$year)->get();
                $refocus_budget =  DB::table('branch_class_budget')->get();
                for ($i=1; $i <= $budget_refocus; $i++) { 
                    $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                        $refocus_budget_id = collect($refocus_budget_id)
                                            ->where('refocus',$i)->first();
                        if(isset($refocus_budget_id)){
                            $refocus_budget = collect($refocus_budget)
                                            ->where('branch_id',$d->branch_id)
                                            ->where('class',$d->class)
                                            ->where('budget_id',$refocus_budget_id->budget_id)->first();
                            return number_format($refocus_budget->amount, 2, '.', ',');
                        }else {
                            return 0;
                        }
                    });
                }
                return $dt ->make(true);
    }

    public function getBudgetAgentPM($agent_id, $budget_id, $branch_id){
        
        $agents = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'intermediaries/';
        $intermediaries = $agents->get($url);
        $intermediaries = json_decode($intermediaries->getBody()->getContents());

        $branch = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'branch/';
        $branches = $branch->get($url);
        $branches = json_decode($branches->getBody()->getContents());

        $bgt = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year = $bgt->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED')
                                ->orWhere('status','PENDING');
                        })->get();

        $activeBudget = DB::table('company_budget')
                        ->where('year',$year)
                        ->where('status','ACTIVE')
                        ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;

        $agent_budgets = DB::table('agent_month_budget')
                        ->where('agent_id',$agent_id)
                        ->where('budget_id',$r0_budgetid)
                        ->where('branch_id',$branch_id)
                        ->orderBy('month', 'ASC')
                        ->get();

        $dt =  Datatables::of($agent_budgets)
                ->addColumn('agentname', function($d) use(&$intermediaries){
                    foreach ($intermediaries as $agent) {
                        if ($d->agent_id == $agent->agent) {
                            return $agent->name;
                        }
                    }
                })
                ->addColumn('branchname', function($d) use(&$branches){
                    foreach ($branches as $branch) {
                        if ($d->branch_id == $branch->branch) {
                            return $branch->description;
                        }
                    }
                })
                ->editColumn('month', function($d){
                    if ($d->month == 1) {
                        return "JANUARY";
                    }else if($d->month == 2) {
                        return "FEBRUARY";
                    }else if($d->month == 3) {
                        return "MARCH";
                    }else if($d->month == 4) {
                        return "APRIL";
                    }else if($d->month == 5) {
                        return "MAY";
                    }else if($d->month == 6) {
                        return "JUNE";
                    }else if($d->month == 7) {
                        return "JULY";
                    }else if($d->month == 8) {
                        return "AUGUST";
                    }else if($d->month == 9) {
                        return "SEPTEMBER";
                    }else if($d->month == 10) {
                        return "OCTOBER";
                    }else if($d->month == 11) {
                        return "NOVEMBER";
                    }else{
                        return "DECEMBER";
                    }
                    
                })
                ->editColumn('month_rate', function($d){
                    return number_format($d->month_rate, 2, '.', ',');
                })
                ->addColumn('original_budget',function($d) use($r0_budgetid){
                    $orig_budget = DB::table('agent_month_budget')
                        ->where('agent_id',$d->agent_id)
                        ->where('branch_id',$d->branch_id)
                        ->where('month',$d->month)
                        ->where('budget_id',$r0_budgetid)->first();
        
                    return number_format($orig_budget->month_amount,2,'.',',');				
                });
        
                $company_params = CompanyProfile::first();
                $budget_refocus = (int)$company_params->budget_refocus;
                $refocus_budget_id =  DB::table('company_budget')
                                            ->where('year',$year)->get();
                $refocus_budget =  DB::table('agent_month_budget')->get();
                for ($i=1; $i <= $budget_refocus; $i++) { 
                    $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                        $refocus_budget_id = collect($refocus_budget_id)
                                            ->where('refocus',$i)->first();
                        if(isset($refocus_budget_id)){
                            $refocus_budget = collect($refocus_budget)
                                            ->where('agent_id',$d->agent_id)
                                            ->where('branch_id',$d->branch_id)
                                            ->where('month',$d->month)
                                            ->where('budget_id',$refocus_budget_id->budget_id)->first();
                            return number_format($refocus_budget->month_amount, 2, '.', ',');
                        }else {
                            return 0;
                        }
                    });
                }
                return $dt ->make(true);
    }

    public function getBudgetAgentClass($agent_id, $budget_id, $branch_id){
        
        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'classes/';
        $classes = $conn->get($url);
        $classes = json_decode($classes->getBody()->getContents());

        // $branch = new \GuzzleHttp\Client();
        // $url = ''.ENV('API_URL').'branch/';
        // $branches = $branch->get($url);
        // $branches = json_decode($branches->getBody()->getContents());

        $bgt = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year = $bgt->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED')
                                ->orWhere('status','PENDING');
                        })->get();

        $activeBudget = DB::table('company_budget')
                        ->where('year',$year)
                        ->where('status','ACTIVE')
                        ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;

        $agent_budgets = DB::table('agent_class_budget')
                        ->where('agent_id',$agent_id)
                        ->where('budget_id',$r0_budgetid)
                        ->where('branch_id',$branch_id)
                        ->get();

        $dt =  Datatables::of($agent_budgets)
                ->addColumn('class', function($d) use(&$classes){
                    foreach ($classes as $class) {
                        if ($d->class == $class->class_field) {
                            return $class->description;
                        }
                    }
                })
                ->editColumn('class_rate', function($d){
                    return number_format($d->class_rate, 2, '.', ',');
                })
                ->addColumn('original_budget',function($d) use($r0_budgetid){
                    $orig_budget = DB::table('agent_class_budget')
                        ->where('agent_id',$d->agent_id)
                        ->where('branch_id',$d->branch_id)
                        ->where('class',$d->class)
                        ->where('budget_id',$r0_budgetid)->first();
        
                    return number_format($orig_budget->amount,2,'.',',');				
                });
        
                $company_params = CompanyProfile::first();
                $budget_refocus = (int)$company_params->budget_refocus;
                $refocus_budget_id =  DB::table('company_budget')
                                            ->where('year',$year)->get();
                $refocus_budget =  DB::table('agent_class_budget')->get();
                for ($i=1; $i <= $budget_refocus; $i++) { 
                    $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                        $refocus_budget_id = collect($refocus_budget_id)
                                            ->where('refocus',$i)->first();
                        if(isset($refocus_budget_id)){
                            $refocus_budget = collect($refocus_budget)
                                            ->where('agent_id',$d->agent_id)
                                            ->where('branch_id',$d->branch_id)
                                            ->where('class',$d->class)
                                            ->where('budget_id',$refocus_budget_id->budget_id)->first();
                            return number_format($refocus_budget->amount, 2, '.', ',');
                        }else {
                            return 0;
                        }
                    });
                }
                return $dt ->make(true);
    }

    public function branchPerformancePM($budget_id, $branch_id){
        $br_budget = DB::table('branch_budget')->where('budget_id', $budget_id)->where('branch_id',$branch_id)->first();
        // dd($br_budget->amount);
        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'branch/';
        $branches = $conn->get($url);
        $branches = json_decode($branches->getBody()->getContents());

        $branches = collect($branches)->where('branch',$branch_id)->first();
        $branchname = $branches->description;

        $budget = DB::table('branch_month_budget')
                    ->select('month_amount')
                    ->where('budget_id',$budget_id)
                    ->where('branch_id', $branch_id)
                    ->orderBy('month', 'ASC')
                    ->get();
        $budget = collect($budget)->pluck('month_amount');
        $budget = array_map('intval',json_decode($budget));
        // dd($budget);

        $year = DB::table('company_budget')->where('budget_id',$budget_id)->first();

        
        $prevyear = $year->rate_year;
        // dd($prevyear);
        $previousyear['year'] = $prevyear;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $previousperformance = $conn->post($url,  ['form_params'=>$previousyear]);

        $url = ''.ENV('API_URL').'budgetbranchpm/';
        $branchprevperformance = $conn->get($url);
        $branchprevperformance = json_decode($branchprevperformance->getBody()->getContents());
        $branchprevperformance = collect($branchprevperformance)->where('branch',$branch_id);

        $year = $year->year;
        $currentyear['year'] = $year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $currentperformance = $conn->post($url,  ['form_params'=>$currentyear]);

        $url = ''.ENV('API_URL').'budgetbranchpm/';
        $branchperformance = $conn->get($url);
        $branchperformance = json_decode($branchperformance->getBody()->getContents());
        $branchperformance = collect($branchperformance)->where('branch',$branch_id);
        // dd($branchperformance);branchprevperformance
        $perf = array();
        $prevperf = array();
        for ($i=1; $i <= 12 ; $i++) { 
            $exists = collect($branchperformance)
                ->where('month',$i)
                ->count();
            $prevexists = collect($branchprevperformance)
                ->where('month',$i)
                ->count();
            if ($exists==0) {
                array_push($perf, 0);
            }else {
                $amount = collect($branchperformance)
                        ->where('month',$i)->first();
                $amount = abs($amount->gross_amount);
                array_push($perf, $amount);
            }

            
            if ($prevexists==0) {
                array_push($prevperf, 0);
            }else {
                $amount = collect($branchprevperformance)
                        ->where('month',$i)->first();
                $amount = abs($amount->gross_amount);
                array_push($prevperf, $amount);
            }
        }
        $br_performance = array_sum($perf);
        $perf = array_map('intval',$perf);
        $prevperf = array_map('intval',$prevperf);
        // dd(json_encode($perf));
        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;

        $variance = (($br_performance-$br_budget->amount)/$br_budget->amount)*100;
        return view('gb.budgets.branchperformancepm',
                     [
                        "budget_id"=>$budget_id,
                        "branch_id"=>$branch_id,
                        "branchname"=>$branchname,
                        "budget_refocus"=>$budget_refocus,
                        "budget"=>$budget,
                        "performance"=>$perf,
                        "previousperformance"=>$prevperf,
                        "br_budget"=> number_format($br_budget->amount, 2, '.',','),
                        "br_performance"=> number_format($br_performance, 2, '.',','),
                        "variance"=>number_format($variance, 2, '.', ',')
                     ] );
    }

    public function getBranchPerformancePM($budget_id, $branch_id){

        $bgt = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year = $bgt->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED');
                        })->get();

        $activeBudget = DB::table('company_budget')
                        ->where('year',$year)
                        ->where('status','ACTIVE')
                        ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;

        $branchpmbudgets = DB::table('branch_month_budget')
                        ->where('branch_id',$branch_id)
                        ->where('budget_id',$r0_budgetid)
                        ->orderBy('month','ASC')
                        ->get();  

        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'branch/';
        $branches = $conn->get($url);
        $branches = json_decode($branches->getBody()->getContents());
        
        $currentyear['year'] = $bgt->year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $currentperformance = $conn->post($url,  ['form_params'=>$currentyear]);

        $url = ''.ENV('API_URL').'budgetbranchpm/';
        $branchperformance = $conn->get($url);
        $branchperformance = json_decode($branchperformance->getBody()->getContents());
        $branchperformance = collect($branchperformance)->where('branch',$branch_id);

        $prevyear['year'] = $bgt->rate_year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $previousperformance = $conn->post($url,  ['form_params'=>$prevyear]);

        $url = ''.ENV('API_URL').'budgetbranchpm/';
        $branchprevperformance = $conn->get($url);
        $branchprevperformance = json_decode($branchprevperformance->getBody()->getContents());
        $branchprevperformance = collect($branchprevperformance)->where('branch',$branch_id);
        // dd($branchperformance);
        $dt =  Datatables::of($branchpmbudgets)
        ->addColumn('branchname',function($d) use(&$branches){
            foreach($branches as $branch){
                if($d->branch_id == $branch->branch ){
                    return $branch->description;
                }
            }				
        })
        ->addColumn('performance',function($d) use(&$branchperformance){
            foreach($branchperformance as $performance){
                if($d->month == $performance->month ){
                    return number_format(abs($performance->gross_amount), 2, '.', ',');
                }
            }
            $exists = collect($branchperformance)
                    ->where('branch',$d->branch_id)
                    ->where('month',$d->month)
                    ->count();
            if ($exists==0) {
                return 0;
            }				
        })
        ->addColumn('prevperformance',function($d) use(&$branchprevperformance){
            foreach($branchprevperformance as $performance){
                if($d->month == $performance->month ){
                    return number_format(abs($performance->gross_amount), 2, '.', ',');
                }
            }
            $exists = collect($branchprevperformance)
                    ->where('branch',$d->branch_id)
                    ->where('month',$d->month)
                    ->count();
            if ($exists==0) {
                return 0;
            }				
        });
        $currentbudget = DB::table('company_budget')
                                        ->where('status','ACTIVE')->first();
        $currentpmbudget = DB::table('branch_month_budget')
                                    ->where('budget_id',$currentbudget->budget_id)->get();
           
        $dt->addColumn('variance',function($d) use(&$branchperformance,$currentpmbudget){
            $br = collect($currentpmbudget)->where('branch_id',$d->branch_id)
                        ->where('month',$d->month)
                        ->first();
            foreach($branchperformance as $performance){
                if($d->month == $performance->month ){
                    $variance = ((abs($performance->gross_amount) - $br->month_amount)/$br->month_amount)*100;
                    if($variance < 0){
                        return '<span class="fa fa-arrow-down text-danger"></span>
                                <span>'.(number_format(($variance*-1), 2, '.', ',')).'</span>';
                    }else if($variance > 0){
                        return '<span class="fa fa-arrow-up text-success"></span>
                                <span>'.(number_format($variance, 2, '.', ',')).'</span>';
                    }else{
                        return '__';
                    }
                }
            }	
            $exists = collect($branchperformance)->where('month',$d->month)->count();
            if ($exists==0) {
                return '<span class="fa fa-arrow-down text-danger"></span>
                         <span>100</span>';
            }					
        })
        ->editColumn('month', function($d){
            if ($d->month == 1) {
                return "JANUARY";
            }else if($d->month == 2) {
                return "FEBRUARY";
            }else if($d->month == 3) {
                return "MARCH";
            }else if($d->month == 4) {
                return "APRIL";
            }else if($d->month == 5) {
                return "MAY";
            }else if($d->month == 6) {
                return "JUNE";
            }else if($d->month == 7) {
                return "JULY";
            }else if($d->month == 8) {
                return "AUGUST";
            }else if($d->month == 9) {
                return "SEPTEMBER";
            }else if($d->month == 10) {
                return "OCTOBER";
            }else if($d->month == 11) {
                return "NOVEMBER";
            }else{
                return "DECEMBER";
            }
            
        })->addColumn('original_budget',function($d) use($r0_budgetid){
            $orig_budget = DB::table('branch_month_budget')
                ->where('branch_id',$d->branch_id)
                ->where('month',$d->month)
                ->where('budget_id',$r0_budgetid)->first();

            return number_format($orig_budget->month_amount,2,'.',',');				
        });

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        $refocus_budget_id =  DB::table('company_budget')
                                    ->where('year',$year)->get();
        $refocus_budget =  DB::table('branch_month_budget')->get();
        for ($i=1; $i <= $budget_refocus; $i++) { 
            $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                $refocus_budget_id = collect($refocus_budget_id)
                                    ->where('refocus',$i)->first();
                if(isset($refocus_budget_id)){
                    $refocus_budget = collect($refocus_budget)
                                    ->where('branch_id',$d->branch_id)
                                    ->where('month',$d->month)
                                    ->where('budget_id',$refocus_budget_id->budget_id)->first();
                    return number_format($refocus_budget->month_amount, 2, '.', ',');
                }else {
                    return 0;
                }
            });
        }
        return $dt->rawColumns(['variance'])
        ->make(true);
    }

    public function agentPerformancePM( $agent_id, $budget_id, $branch_id){
        $ag_budget = DB::table('agent_budget')
                    ->where('budget_id', $budget_id)
                    ->where('branch_id',$branch_id)
                    ->where('agent_id',$agent_id)
                    ->first();
        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'intermediaries/';
        $intermediaries = $conn->get($url);
        $intermediaries = json_decode($intermediaries->getBody()->getContents());
        // dd($budget_id);
        $intermediaries = collect($intermediaries)
                                ->where('agent', $agent_id)
                                ->where('branch', $branch_id)
                                ->first();

        $agentname = $intermediaries->name;

        $budget = DB::table('agent_month_budget')
                    ->select('month_amount')
                    ->where('budget_id',$budget_id)
                    ->where('branch_id', $branch_id)
                    ->where('agent_id', $agent_id)
                    ->orderBy('month', 'ASC')
                    ->get();
        $budget = collect($budget)->pluck('month_amount');
        $budget = array_map('intval',json_decode($budget));
        // dd($budget);

        $year = DB::table('company_budget')->where('budget_id',$budget_id)->first();
        $year = $year->year;
        $currentyear['year'] = $year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $currentperformance = $conn->post($url,  ['form_params'=>$currentyear]);

        $url = ''.ENV('API_URL').'budgetagentpm/';
        $agentperformance = $conn->get($url);
        $agentperformance = json_decode($agentperformance->getBody()->getContents());
        $agentperformance = collect($agentperformance)->where('branch',$branch_id)->where('agent_no',$agent_id);
        // dd($agentperformance);
        $perf = array();
        for ($i=1; $i <= 12 ; $i++) { 
            $exists = collect($agentperformance)
                ->where('month',$i)
                ->count();
            if ($exists==0) {
                array_push($perf, 0);
            }else {
                $amount = collect($agentperformance)
                        ->where('month',$i)->first();
                $amount = abs($amount->gross_amount);
                array_push($perf, $amount);
            }
        }
        
        $perf = array_map('intval',$perf);
        $ag_performance = array_sum($perf);
        // dd(json_encode($perf));

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;

        $variance = (($ag_performance-$ag_budget->amount)/$ag_budget->amount)*100;

        return view('gb.budgets.agentperformancepm',
                    [
                        "budget_id"=>$budget_id,
                        "branch_id"=>$branch_id,
                        "agent_id"=>$agent_id,
                        "agentname"=>$agentname,
                        "budget_refocus"=>$budget_refocus,
                        "budget"=>$budget,
                        "performance"=>$perf,
                        "ag_budget"=>number_format($ag_budget->amount,2,'.',','),
                        "ag_performance"=>number_format($ag_performance,2,'.',','),
                        "variance"=>number_format($variance,2,'.',',')
                    ] 
                );
    }


    public function getAgentPerformancePM($agent_id, $budget_id, $branch_id){
        // dd(ENV('API_URL'));
        $bgt = DB::table('company_budget')->where('budget_id',$budget_id)->first();

        $year = $bgt->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED');
                        })->get();

        $activeBudget = DB::table('company_budget')
                        ->where('year',$year)
                        ->where('status','ACTIVE')
                        ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;

        $budget = DB::table('agent_month_budget')
                        ->where('branch_id',$branch_id)
                        ->where('agent_id',$agent_id)
                        ->where('budget_id',$r0_budgetid)
                        ->orderBy('month','ASC')
                        ->get();  


        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'intermediaries/';
        $intermediaries = $conn->get($url);
        $intermediaries = json_decode($intermediaries->getBody()->getContents());
        $url = ''.ENV('API_URL').'branch/';
        $branches = $conn->get($url);
        $branches = json_decode($branches->getBody()->getContents());

        $currentyear['year'] = $bgt->year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $currentperformance = $conn->post($url,  ['form_params'=>$currentyear]);

        $url = ''.ENV('API_URL').'budgetagentpm/';
        $agentperformance = $conn->get($url);
        $agentperformance = json_decode($agentperformance->getBody()->getContents());
        
        $prevyear['year'] = $bgt->rate_year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $previousperformance = $conn->post($url,  ['form_params'=>$prevyear]);

        $url = ''.ENV('API_URL').'budgetagentpm/';
        $agentprevperformance = $conn->get($url);
        $agentprevperformance = json_decode($agentprevperformance->getBody()->getContents());

        $dt =  Datatables::of($budget)
        ->addColumn('agentname',function($d) use(&$intermediaries){
            foreach($intermediaries as $agent){
                if(($d->agent_id == $agent->agent) && ($d->branch_id == $agent->branch)){
                    return $agent->name;
                }
            }				
        })
        ->addColumn('branchname',function($d) use(&$branches){
            foreach($branches as $branch){
                if($d->branch_id == $branch->branch ){
                    return $branch->description;
                }
            }				
        })
        ->addColumn('performance',function($d) use(&$agentperformance){
            foreach($agentperformance as $performance){
                if(($d->agent_id == $performance->agent_no) && ($d->branch_id == $performance->branch) && ($d->month == $performance->month)){
                    return number_format(abs($performance->gross_amount), 2, '.', ',');
                }
            }
            $exists = collect($agentperformance)
                    ->where('branch',$d->branch_id)
                    ->where('agent_no',$d->agent_id)
                    ->where('month',$d->month)
                    ->count();
            if ($exists==0) {
                return 0;
            }			
        })
        ->addColumn('prevperformance',function($d) use(&$agentprevperformance){
            foreach($agentprevperformance as $performance){
                if(($d->agent_id == $performance->agent_no) && ($d->branch_id == $performance->branch) && ($d->month == $performance->month)){
                        return number_format($performance->gross_amount, 2, '.', ',');
                }
            }
            $exists = collect($agentprevperformance)
                    ->where('branch',$d->branch_id)
                    ->where('agent_no',$d->agent_id)
                    ->where('month',$d->month)
                    ->count();
            if ($exists==0) {
                return 0;
            }			
        });
        $currentbudget = DB::table('company_budget')
                                        ->where('status','ACTIVE')->first();
        $currentpmbudget = DB::table('agent_month_budget')
                                    ->where('budget_id',$currentbudget->budget_id)->get();
        $dt->addColumn('variance',function($d) use(&$agentperformance,$currentpmbudget){
            $br = collect($currentpmbudget)->where('agent_id',$d->agent_id)
                                            ->where('branch_id',$d->branch_id)
                                            ->where('month',$d->month)
                                            ->first();
            foreach($agentperformance as $performance){
                if(($d->agent_id == $performance->agent_no) && ($d->branch_id == $performance->branch) && ($d->month == $performance->month)){
                    $variance = (($performance->gross_amount - $br->month_amount)/$br->month_amount)*100;
                    if($variance < 0){
                        return '<span class="fa fa-arrow-down text-danger"></span>
                                <span>'.(number_format(($variance*-1), 2, '.', ',')).'</span>';
                    }else{
                        return '<span class="fa fa-arrow-up text-success"></span>
                                <span>'.(number_format($variance, 2, '.', ',')).'</span>';
                    }
                }
            }	
            $exists = collect($agentperformance)->where('month',$d->month)->count();
            if ($exists==0) {
                return '<span class="fa fa-arrow-down text-danger"></span>
                         <span>100</span>';
            }				
        })
        ->editColumn('month', function($d){
            if ($d->month == 1) {
                return "JANUARY";
            }else if($d->month == 2) {
                return "FEBRUARY";
            }else if($d->month == 3) {
                return "MARCH";
            }else if($d->month == 4) {
                return "APRIL";
            }else if($d->month == 5) {
                return "MAY";
            }else if($d->month == 6) {
                return "JUNE";
            }else if($d->month == 7) {
                return "JULY";
            }else if($d->month == 8) {
                return "AUGUST";
            }else if($d->month == 9) {
                return "SEPTEMBER";
            }else if($d->month == 10) {
                return "OCTOBER";
            }else if($d->month == 11) {
                return "NOVEMBER";
            }else{
                return "DECEMBER";
            }
            
        })->addColumn('original_budget',function($d) use($r0_budgetid){
            $orig_budget = DB::table('agent_month_budget')
                ->where('branch_id',$d->branch_id)
                ->where('agent_id',$d->agent_id)
                ->where('month',$d->month)
                ->where('budget_id',$r0_budgetid)->first();

            return number_format($orig_budget->month_amount,2,'.',',');				
        });

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        $refocus_budget_id =  DB::table('company_budget')
                                    ->where('year',$year)->get();
        $refocus_budget =  DB::table('agent_month_budget')->get();
        for ($i=1; $i <= $budget_refocus; $i++) { 
            $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                $refocus_budget_id = collect($refocus_budget_id)
                                    ->where('refocus',$i)->first();
                if(isset($refocus_budget_id)){
                    $refocus_budget = collect($refocus_budget)
                                    ->where('branch_id',$d->branch_id)
                                    ->where('agent_id',$d->agent_id)
                                    ->where('month',$d->month)
                                    ->where('budget_id',$refocus_budget_id->budget_id)->first();
                    return number_format($refocus_budget->month_amount, 2, '.', ',');
                }else {
                    return 0;
                }
            });
        }
        return $dt->escapeColumns(['agentname','branchname','performance','variance','prevperformance',])
        ->rawColumns(['variance'])
        ->make(true);
    }

    public function getAgentPerformanceClass($agent_id, $budget_id, $branch_id){
        $bgt = DB::table('company_budget')->where('budget_id',$budget_id)->first();

        $year = $bgt->year;
        $refocusbudgets = DB::table('company_budget')
                        ->where('year',$year)
                        ->where(function($d){
                            $d->where('status','ACTIVE')
                                ->orWhere('status','REFOCUSED');
                        })->get();

        $activeBudget = DB::table('company_budget')
                        ->where('year',$year)
                        ->where('status','ACTIVE')
                        ->first();
        $refocus0 =  collect($refocusbudgets)->where('refocus',0)->first();
        $r0_budgetid = $refocus0->budget_id;

        $budget = DB::table('agent_class_budget')
                        ->where('branch_id',$branch_id)
                        ->where('agent_id',$agent_id)
                        ->where('budget_id',$r0_budgetid)
                        ->get();  


        $conn = new \GuzzleHttp\Client();
        $url = ''.ENV('API_URL').'classes/';
        $classes = $conn->get($url);
        $classes = json_decode($classes->getBody()->getContents());

        $currentyear['year'] = $bgt->year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $currentperformance = $conn->post($url,  ['form_params'=>$currentyear]);

        $url = ''.ENV('API_URL').'budgetclass/';
        $classperformance = $conn->get($url);
        $classperformance = json_decode($classperformance->getBody()->getContents());
        
        $prevyear['year'] = $bgt->rate_year;
        $url=''.ENV('API_URL').'budgetprevyear/';
        $previousperformance = $conn->post($url,  ['form_params'=>$prevyear]);

        $url = ''.ENV('API_URL').'budgetclass/';
        $agentprevperformance = $conn->get($url);
        $agentprevperformance = json_decode($agentprevperformance->getBody()->getContents());

        $dt =  Datatables::of($budget)
        ->addColumn('class',function($d) use(&$classes){
            foreach($classes as $class){
                if(($d->class == $class->class_field) && ($d->branch_id == $class->branch)){
                    return $agent->name;
                }
            }				
        })
        ->addColumn('performance',function($d) use(&$classperformance){
            foreach($classperformance as $performance){
                if(($d->agent_id == $performance->agent_no) && ($d->branch_id == $performance->branch) && ($d->month == $performance->month)){
                    return number_format(abs($performance->gross_amount), 2, '.', ',');
                }
            }
            $exists = collect($classperformance)
                    ->where('branch',$d->branch_id)
                    ->where('agent_no',$d->agent_id)
                    ->where('month',$d->month)
                    ->count();
            if ($exists==0) {
                return 0;
            }			
        })
        ->addColumn('prevperformance',function($d) use(&$agentprevperformance){
            foreach($agentprevperformance as $performance){
                if(($d->agent_id == $performance->agent_no) && ($d->branch_id == $performance->branch) && ($d->month == $performance->month)){
                        return number_format($performance->gross_amount, 2, '.', ',');
                }
            }
            $exists = collect($agentprevperformance)
                    ->where('branch',$d->branch_id)
                    ->where('agent_no',$d->agent_id)
                    ->where('month',$d->month)
                    ->count();
            if ($exists==0) {
                return 0;
            }			
        });
        $currentbudget = DB::table('company_budget')
                                        ->where('status','ACTIVE')->first();
        $currentpmbudget = DB::table('agent_month_budget')
                                    ->where('budget_id',$currentbudget->budget_id)->get();
        $dt->addColumn('variance',function($d) use(&$classperformance,$currentpmbudget){
            $br = collect($currentpmbudget)->where('agent_id',$d->agent_id)
                                            ->where('branch_id',$d->branch_id)
                                            ->where('month',$d->month)
                                            ->first();
            foreach($classperformance as $performance){
                if(($d->agent_id == $performance->agent_no) && ($d->branch_id == $performance->branch) && ($d->month == $performance->month)){
                    $variance = (($performance->gross_amount - $br->month_amount)/$br->month_amount)*100;
                    if($variance < 0){
                        return '<span class="fa fa-arrow-down text-danger"></span>
                                <span>'.(number_format(($variance*-1), 2, '.', ',')).'</span>';
                    }else{
                        return '<span class="fa fa-arrow-up text-success"></span>
                                <span>'.(number_format($variance, 2, '.', ',')).'</span>';
                    }
                }
            }	
            $exists = collect($classperformance)->where('month',$d->month)->count();
            if ($exists==0) {
                return '<span class="fa fa-arrow-down text-danger"></span>
                         <span>100</span>';
            }				
        })
        ->editColumn('month', function($d){
            if ($d->month == 1) {
                return "JANUARY";
            }else if($d->month == 2) {
                return "FEBRUARY";
            }else if($d->month == 3) {
                return "MARCH";
            }else if($d->month == 4) {
                return "APRIL";
            }else if($d->month == 5) {
                return "MAY";
            }else if($d->month == 6) {
                return "JUNE";
            }else if($d->month == 7) {
                return "JULY";
            }else if($d->month == 8) {
                return "AUGUST";
            }else if($d->month == 9) {
                return "SEPTEMBER";
            }else if($d->month == 10) {
                return "OCTOBER";
            }else if($d->month == 11) {
                return "NOVEMBER";
            }else{
                return "DECEMBER";
            }
            
        })->addColumn('original_budget',function($d) use($r0_budgetid){
            $orig_budget = DB::table('agent_month_budget')
                ->where('branch_id',$d->branch_id)
                ->where('agent_id',$d->agent_id)
                ->where('month',$d->month)
                ->where('budget_id',$r0_budgetid)->first();

            return number_format($orig_budget->month_amount,2,'.',',');				
        });

        $company_params = CompanyProfile::first();
        $budget_refocus = (int)$company_params->budget_refocus;
        $refocus_budget_id =  DB::table('company_budget')
                                    ->where('year',$year)->get();
        $refocus_budget =  DB::table('agent_month_budget')->get();
        for ($i=1; $i <= $budget_refocus; $i++) { 
            $dt->addColumn('refocus'.$i,function($d) use($year, $i,$refocus_budget_id, $refocus_budget){
                $refocus_budget_id = collect($refocus_budget_id)
                                    ->where('refocus',$i)->first();
                if(isset($refocus_budget_id)){
                    $refocus_budget = collect($refocus_budget)
                                    ->where('branch_id',$d->branch_id)
                                    ->where('agent_id',$d->agent_id)
                                    ->where('month',$d->month)
                                    ->where('budget_id',$refocus_budget_id->budget_id)->first();
                    return number_format($refocus_budget->month_amount, 2, '.', ',');
                }else {
                    return 0;
                }
            });
        }
        return $dt->escapeColumns(['agentname','branchname','performance','variance','prevperformance',])
        ->rawColumns(['variance'])
        ->make(true);
    }


    public function budgetEscalate(Request $request){
        try{
            $process = 'budget';
            $process = CRMProcesses::where('description', 'iLIKE', '%'.$process.'%')->first();
            $process_id = $process->process_code;

            $approvers = $request->approver;
            $levels = $request->level;
            // dd($levels);
            $user = Auth()->user()->username;
            $budget_id = $request->budget_id;
            $year = $request->bgt_year;

            $approval_flows = array();
            $app_mail = array();
            $approval = Approvals::create([
                'process_code'=>$process_id,
                'process_number'=>$budget_id,
                'description'=>$request->escalate_narrate,
                'status'=>'pending',
                'account_year'=>$year,
                'user'=>$user,
                'dola'=>Carbon::now(),

            ]);

            $approv = Approvals::where('process_number', $budget_id)->where('status','pending')->first();
            $approval_id = $approv->approval_id;
            for($i=0; $i<count($levels); $i++){
                $mess_status = NULL;
                if ($i == 0) {
                    $mess_status = "SEND";
                }
                $flow = [
                    'approval_id' => $approval_id,
                    'approval_level' => $i+1,
                    'approver_id' => $approvers[$i],
                    'status' => "pending",
                    'dola' => Carbon::now(),
                ];
                if(($i+1) == count($levels)){
                    $flow = array_merge($flow, array(
                        'next_approval_level' => null,
                        'next_approver' => null,
                        )
                    );
                }else{
                    $flow = array_merge($flow,array(
                        'next_approval_level' => $i+2,
                        'next_approver' => $approvers[$i+1],
                    ));
                }
                array_push($approval_flows, $flow);
                $mail = [
                    'reference_number' => $budget_id,
                    'process_id' => $process_id,
                    'created_at' => Carbon::now(),
                    'to_user_id' => $approvers[$i],
                    'status' => $mess_status,
                    'redirect_path' => 'budget.details'
                ];
                array_push($app_mail, $mail);
            }
            // dd($approval_flows);
            $apprv_flow = Approval_flow::create($approval_flows);
            DB::table('approval_mailbox')->insert($app_mail);
            $res = ['status'=>1];
        }catch(Throwable $th){
            $res = ['status'=>0];
        }

        return $res;
    }

    public function pendingBudgetApprove(Request $request){
        try{
            $budget_id = $request->budget_id;
            $approve = $request->app_budget;
            $user = Auth()->user()->id;
            $budget = DB::table('company_budget')->where('budget_id',$budget_id)->first();
            $acyear = $budget->year;
            $approval = Approvals::where('process_number', $budget_id)->first();
            $flow =  Approval_flow::where('approval_id', $approval->approval_id)->get();
            $hierachy = collect($flow)->where('approver_id', $user)->first();
            if($approve == 'Y'){
                if (is_null($hierachy->next_approval_level)) {
                    Approval_flow::where('approval_id', $approval->approval_id)
                                    ->where('approver_id', $user)->update(['status'=> 'approved']);
                    DB::table('approval_mailbox')->where('to_user_id', $user)
                                ->where('reference_number', $budget_id)->update(['status' => 'READ']);
                    Approvals::where('process_number', $budget_id)->update(['status' => 'approved']);
                    if($budget->refocus != 0){
                        DB::table('company_budget')->where('year',$acyear)->where('status','ACTIVE')
                        ->update([
                            'status'=>'REFOCUSED'
                        ]);
                    }
                                                
                    DB::table('company_budget')->where('budget_id', $budget_id)
                                                ->update([
                                                    'status'=>'ACTIVE'
                                                ]);
                }else{
                    Approval_flow::where('approval_id', $approval->approval_id)
                                ->where('approver_id', $user)->update(['status' => 'approved']);
                    DB::table('approval_mailbox')->where('to_user_id', $user)
                                ->where('reference_number', $budget_id)->update(['status' => 'READ']);
                    
                    DB::table('approval_mailbox')->where('to_user_id', $hierachy->next_approver)
                                ->where('reference_number', $budget_id)->update(['status' => 'SEND']);
                }
            }else{
                
                Approval_flow::where('approval_id', $approval->approval_id)
                            ->where('approver_id', $user)->update(['status' => 'rejected']);
                DB::table('approval_mailbox')->where('to_user_id', $user)
                            ->where('reference_number', $budget_id)->update(['status' => 'READ']);
                Approvals::where('process_number', $budget_id)->update(['status' => 'rejected']);

                DB::table('company_budget')->where('budget_id', $budget_id)
                                ->update([
                                    'status'=>'REJECTED'
                                ]);
            }
            $res = ['status'=>1];
        }catch(Throwable $th){
                $res = ['status'=>0];
        }
        return $res;
    }

    public function checkuploadedlevels(Request $request){

        $selectedValues = $request->selectedUploadValues;

        $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
        $sections = explode("-", $hierarchy->description);
        $levels = array_slice($sections, 0, -2);

        $level1 = null;
        $level2 = null;
        $level4 = null;
        $bgid= $request->bgtid;


        foreach ($selectedValues as $categoryId => $selectedOptions) {

                $parts = explode(' - ', $selectedOptions[0]);
                switch ($categoryId) {
                    case 0:
                        $level1 = $parts[0];
                        break;
                    case 1:
                        $level2 = $parts[0];
                        break;
                    case 2:
                        $level3 = $parts[0];
                        break;
                }

        }

        if (!empty($levels)) {
            if ($levels[0] == "BRANCH") {

                $valueData0 = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level1%'")
                    ->pluck('branch')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();

                $records = DB::table('budget_uw')
                    ->where('budget_id', (int)$bgid)
                    ->where('level2', $valueData0)
                    ->pluck('level3')
                    ->unique();
                            

            } elseif ($levels[0] == "CHANNEL") {
                $valueData0 = DB::table('dist_channel')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level1%'")
                    ->pluck('dist_type')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();

                $records = DB::table('budget_uw')
                    ->where('budget_id', $bgid)
                    ->where('level2', $valueData0)
                    ->pluck('level3')
                    ->unique();
            }
        }

        if (count($levels) > 1) { 
            if ($levels[1] == "BRANCH") {
                $valueData1 = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level2%'")
                    ->pluck('branch')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();
                    
            } elseif ($levels[1] == "CHANNEL") {
                $valueData1 = DB::table('dist_channel')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level2%'")
                    ->pluck('dist_type')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();
            }
        }

        $values = $records->values()->all();


        if ($levels[0] == "BRANCH") {
            $desc = DB::table('dist_channel')
                ->whereIn('dist_type',$values)
                ->pluck('description');
                
        } elseif ($levels[0] == "CHANNEL") {
            $desc = DB::table('branch')
                ->whereIn('branch',$values)
                ->pluck('description');

        }
        
        $desc = $desc->values()->all();
        $desc = array_map(function ($value) {
            return str_replace(' ', '', $value);
        }, $desc);

        return $desc;
        

    }


    public function checkuploadedsecondlevel(Request $request)
    {
        $bgid = $request->bgtid;

        $master = DB::table('budget_company')
            ->where('budget_id', $bgid)
            ->first();

        $principal_officer = DB::table('aimsusers')
            ->whereRaw("UPPER(REPLACE(name, ' ', '')) LIKE ?", ["%" . strtoupper(str_replace(' ', '', $request->principal_off)) . "%"])
            ->pluck('user_id')
            ->map(function ($value) {
                return intval($value);
            })
            ->first();

            if($master->running_budget == 'Y'){

                $records = DB::table('budgettracker')
                ->where('budget_id', (int)$bgid)
                ->where('principal_officer', $principal_officer)
                ->where('bus_type', $request->bus_type)
                ->where('forecast_status', 'Y')
                ->pluck('branch')
                ->unique()
                ->toArray();

            }else{

                $records = DB::table('budgettracker')
                ->where('budget_id', (int)$bgid)
                ->where('principal_officer', $principal_officer)
                ->where('bus_type', $request->bus_type)
                ->where('budget_status', 'Y')
                ->pluck('branch')
                ->unique()
                ->toArray();

            }



        $branches = DB::table('branch')->whereIn('branch', $records)->get();

        $desc = $branches->pluck('description')->map(function ($value) {
            return str_replace(' ', '', $value);
        })->values()->all();


        return $desc;
    }

    public function total_sum_of_drilldown(Request $request){

        $selectedValues = $request->selectedUploadValues;

        $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
        $sections = explode("-", $hierarchy->description);
        $levels = array_slice($sections, 0, -1);


        $level1 = null;
        $level2 = null;
        $level4 = null;
        $bgid= $request->bgtid;


        foreach ($selectedValues as $categoryId => $selectedOptions) {

                $parts = explode(' - ', $selectedOptions[0]);
                switch ($categoryId) {
                    case 0:
                        $level1 = $parts[0];
                        break;
                    case 1:
                        $level2 = $parts[0];
                        break;
                    case 2:
                        $level3 = $parts[0];
                        break;
                }

        }

        $records = DB::table('budget_uw')
        ->where('budget_id', (int)$bgid)
        ->pluck('level2')
        ->unique();

        $values = $records->values()->all();

        if ($levels[0] == "BRANCH") {
            $desc = DB::table('branch')
                ->whereIn('branch',$values)
                ->pluck('description');

                
        } elseif ($levels[0] == "CHANNEL") {
            $desc = DB::table('dist_channel')
                ->whereIn('dist_type',$values)
                ->pluck('description');

        }

        $desc = $desc->values()->all();
        $desc = array_map(function ($value) {
            return str_replace(' ', '', $value);
        }, $desc);

        return $desc;
        

    }

    public function downloadBudgetTemplate(Request $request)
    {


        $budget_id = $request->downloadbgtid;
        $parts = explode('/', $budget_id);
        $budgetid = $parts[0];

        $budget = DB::table('budget_company')->where('budget_id',$budgetid)->first();

        $principal = DB::table('intermediary')
                    ->whereRaw("lower(REPLACE(principal_officer, ' ', '')) LIKE '%$request->budgetcat_0%'")
                    ->get()[0];
        
        $branch_off = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$request->budgetcat_1%'")
                    ->first();
        
        $branches = DB::table('branch')->get();




        $header = [
            'DESCRIPTION',
            'ACTTYPE',
            'JANUARY',
            'FEBRUARY',
            'MARCH',
            'APRIL',
            'MAY',
            'JUNE',
            'JULY',
            'AUGUST',
            'SEPTEMBER',
            'OCTOBER',
            'NOVEMBER',
            'DECEMBER',
        ];

        $columnWidths = [
            'DESCRIPTION' => 25,
            'ACTTYPE' => 6,
            'JANUARY' => 10,
            'FEBRUARY' => 10,
            'MARCH' => 10,
            'APRIL' => 10,
            'MAY' => 10,
            'JUNE' => 10,
            'JULY' => 10,
            'AUGUST' => 10,
            'SEPTEMBER' => 10,
            'OCTOBER' => 10,
            'NOVEMBER' => 10,
            'DECEMBER' => 10,
        ];


        $header2 = [
            'CLASS',
            'CODE',
            'JANUARY RATE',
            'FEBRUARY RATE',
            'MARCH RATE',
            'APRIL RATE',
            'MAY RATE',
            'JUNE RATE',
            'JULY RATE',
            'AUGUST RATE',
            'SEPTEMBER RATE',
            'OCTOBER RATE',
            'NOVEMBER RATE',
            'DECEMBER RATE'
        ];

        $columnWidths2 = [
            'CLASS' => 25,
            'CODE' => 6,
            'JANUARY RATE' => 15,
            'FEBRUARY RATE' => 15,
            'MARCH RATE' => 15,
            'APRIL RATE' => 15,
            'MAY RATE' => 15,
            'JUNE RATE' => 15,
            'JULY RATE' => 15,
            'AUGUST RATE' => 15,
            'SEPTEMBER RATE' => 15,
            'OCTOBER RATE' => 15,
            'NOVEMBER RATE' => 15,
            'DECEMBER RATE' => 15
        ];


        $spreadsheet = new Spreadsheet();
        foreach ($branches as $item) {
            
            // Create a new sheet for each branch
            $sheet = $spreadsheet->createSheet();
            
            $sheet->setTitle($item->description); // Set sheet title using branch description
            
            $colIndex = 1;
            foreach ($header as $columnName) {
                $sheet->setCellValueByColumnAndRow($colIndex, 1, $columnName);
                $sheet->getStyleByColumnAndRow($colIndex, 1)->getFont()->setBold(true);
                $sheet->getColumnDimensionByColumn($colIndex)->setWidth($columnWidths[$columnName]);
                $colIndex++;
            }


            $structure1 =  DB::table('intermediary')->get();

            $rowIndex = 2;
            foreach ($structure1 as $row) {
                $colIndex = 1;
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->description);
                $colIndex++;

                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->acc_type);
                $colIndex++;


                $rowIndex++;
            }

            $structure =  DB::table('dept')->get();;

            $rowIndex = $rowIndex + 2;

            $colIndex = 1;
            foreach ($header2 as $columnName) {
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $columnName);
                $sheet->getStyleByColumnAndRow($colIndex, $rowIndex)->getFont()->setBold(true);
                $sheet->getColumnDimensionByColumn($colIndex,$rowIndex)->setWidth($columnWidths2[$columnName]);
                $colIndex++;
            }
            $rowIndex++;

            foreach ($structure as $row) {
                $colIndex = 1;
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->description);
                $colIndex++;

                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->dept);
                $colIndex++;


                $rowIndex++;
            }
            
        }
      

        $spreadsheet->removeSheetByIndex(0);

        $writer = new Xlsx($spreadsheet);

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename={$request->budgetcat_0}.xlsx");
        header('Cache-Control: max-age=0');

        // Output the generated .xlsx file to the browser
        $writer->save('php://output');

        exit;
    }

    public function downloadCurrentBudgetTemplate(Request $request)
    {

        $mybranch = $request->downloadcpgt;
        $bgt = $request->downloadbudget;

        $branches = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$mybranch%'")
                    ->first();

        $channels = DB::table('dist_channel')->get();
                        
        foreach($branches as $branch){


            $header = [
                'DESCRIPTION',
                'DEPT',
                'BUSINESSTYPE',
                'JANUARY',
                'FEBRUARY',
                'MARCH',
                'APRIL',
                'MAY',
                'JUNE',
                'JULY',
                'AUGUST',
                'SEPTEMBER',
                'OCTOBER',
                'NOVEMBER',
                'DECEMBER',
            ];

            $columnWidths = [
                'DESCRIPTION' => 25,
                'DEPT' => 6,
                'BUSINESSTYPE' => 6,
                'JANUARY' => 10,
                'FEBRUARY' => 10,
                'MARCH' => 10,
                'APRIL' => 10,
                'MAY' => 10,
                'JUNE' => 10,
                'JULY' => 10,
                'AUGUST' => 10,
                'SEPTEMBER' => 10,
                'OCTOBER' => 10,
                'NOVEMBER' => 10,
                'DECEMBER' => 10,
            ];


            $spreadsheet = new Spreadsheet();
            foreach ($channels as $item) {
                
                // Create a new sheet for each branch
                $sheet = $spreadsheet->createSheet();
                
                $sheet->setTitle($item->description); // Set sheet title using branch description
                
                $colIndex = 1;
                foreach ($header as $columnName) {
                    $sheet->setCellValueByColumnAndRow($colIndex, 1, $columnName);
                    $sheet->getStyleByColumnAndRow($colIndex, 1)->getFont()->setBold(true);
                    $sheet->getColumnDimensionByColumn($colIndex)->setWidth($columnWidths[$columnName]);
                    $colIndex++;
                }


                $structure =  DB::table('dept')->get();;

                $rowIndex = 2;
                foreach ($structure as $row) {
                    $myrows = DB::table('budget_uw')
                        ->where('budget_id', (int)$bgt)
                        ->where('level2', $branch)
                        ->where('level3', $item->dist_type)
                        ->where('level4', $row->dept)
                        ->get();
    
                    foreach ($myrows as $myrow) {

                        $colIndex = 1;
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->description);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->dept);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->businesstype);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->january);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->february);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->march);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->april);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->may);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->june);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->july);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->august);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->september);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->october);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->november);
                        $colIndex++;
                
                        $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $myrow->december);
                        $colIndex++;
                
                        $rowIndex++;
                    }
                }
                
                
            }
      


            $spreadsheet->removeSheetByIndex(0);

            $writer = new Xlsx($spreadsheet);

            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="'.$mybranch.'-BUDGET.xlsx"');
            header('Cache-Control: max-age=0');
    
            // Output the generated .xlsx file to the browser
            $writer->save('php://output');
    
            exit;



        }


    }
    

    
    public function downloadForecastTemplate(Request $request)
    {


        $budget_id = $request->downloadbudgetid;
        $parts = explode('/', $budget_id);
        $budgetid = $parts[0];

        $today = Carbon::now()->month;

        $budget = DB::table('budget_company')->where('budget_id',$budgetid)->first();
   
        $branch_off = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$request->budgetcat_0%'")
                    // ->pluck('offcd')
                    // ->map(function ($value) {
                    //     return intval($value);
                    // })
                    ->first();

        $dist_channel = DB::table('dist_channel')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$request->budgetcat_1%'")
                    // ->pluck('dist_type')
                    // ->map(function ($value) {
                    //     return intval($value);
                    // })
                    ->first();

        $branch_agent = [];
        $intermediary = DB::table('intermediary')->where('distribution_channel', $dist_channel->dist_type)->get();

        foreach ($intermediary as $ag) {
            $branch_agent[] = $ag->branch .'/'. $ag->agent;
        }
        
        

        $bus_type = [
            'POL',
            'REN'
        ];

        $header = [
            'DESCRIPTION',
            'DEPT',
            'JANUARY',
            'FEBRUARY',
            'MARCH',
            'APRIL',
            'MAY',
            'JUNE',
            'JULY',
            'AUGUST',
            'SEPTEMBER',
            'OCTOBER',
            'NOVEMBER',
            'DECEMBER',
            '',
        ];
        
        // Loop to add "ACTUAL" for each month up to $today
        for ($i = 1; $i <= $today; $i++) {
            $header[] = strtoupper(date('F', mktime(0, 0, 0, $i, 1))) . ' ACTUAL';
        }

        $header[] = 'YTD ACTUAL';
        
        // Now $header contains all the necessary headers
        

        $columnWidths = [
            'DESCRIPTION' => 25,
            'DEPT' => 6,
            'JANUARY' => 10,
            'FEBRUARY' => 10,
            'MARCH' => 10,
            'APRIL' => 10,
            'MAY' => 10,
            'JUNE' => 10,
            'JULY' => 10,
            'AUGUST' => 10,
            'SEPTEMBER' => 15,
            'OCTOBER' => 15,
            'NOVEMBER' => 15,
            'DECEMBER' => 15,
            '' => 10,
            'JANUARY ACTUAL' => 20,
            'FEBRUARY ACTUAL' => 20,
            'MARCH ACTUAL' => 20,
            'APRIL ACTUAL' => 20,
            'MAY ACTUAL' => 20,
            'JUNE ACTUAL' => 20,
            'JULY ACTUAL' => 20,
            'AUGUST ACTUAL' => 20,
            'SEPTEMBER ACTUAL' => 20,
            'OCTOBER ACTUAL' => 20,
            'NOVEMBER ACTUAL' => 20,
            'DECEMBER ACTUAL' => 20,
            'YTD ACTUAL' => 20,
        ];


        $spreadsheet = new Spreadsheet();
        foreach ($bus_type as $item) {
            
            // Create a new sheet for each branch
            $sheet = $spreadsheet->createSheet();
            
            $sheet->setTitle($item); // Set sheet title using branch description
            
            $colIndex = 1;
            foreach ($header as $columnName) {
                $sheet->setCellValueByColumnAndRow($colIndex, 1, $columnName);
                $sheet->getStyleByColumnAndRow($colIndex, 1)->getFont()->setBold(true);
                $sheet->getColumnDimensionByColumn($colIndex)->setWidth($columnWidths[$columnName]);
                $colIndex++;
            }


            $structure =  DB::table('dept')->get();;

            $rowIndex = 2;
            foreach ($structure as $row) {
  

                $classes = DB::table('class')->where('dept', $row->dept)->get();

                $valuesArray = [];
                foreach ($classes as $class) {
                    $valuesArray[] = $class->class;
                }
                
 


                for ($month = 1; $month <= 12; $month++) {
                    ${"performance$month"} = DB::table('debitmast')
                        ->selectRaw('SUM(GROSS_AMOUNT) AS performance')
                        ->where('ENTRY_TYPE_DESCR', 'POL')
                        ->where('account_year', $budget->year)
                        ->where('account_month', $month)
                        ->whereIn('class', $valuesArray)
                        ->where(function ($query) use ($branch_agent) {
                            foreach ($branch_agent as $branch_agent_combo) {
                                list($branch, $agent) = explode('/', $branch_agent_combo);
                                $query->orWhere(function ($q) use ($branch, $agent) {
                                    $q->where('BRANCH', $branch)
                                        ->where('AGENT_NO', $agent);
                                });
                            }
                        });
                    ${"performance$month"} = ${"performance$month"}->first();
                }


   

                $colIndex = 1;
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->description);
                $colIndex++;

                $colIndex = 1;
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->description);
                $colIndex++;

                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $row->dept);
                $colIndex++;

                $colIndex=$colIndex+13;

                for ($i = 1; $i <= $today; $i++) {
                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, ${"performance$i"}->performance ?? 0);
                    $colIndex++;
                }

                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $performanceytd->performance ?? 0);
                $colIndex++;
                 


                $rowIndex++;
            }
            
        }
      

        $spreadsheet->removeSheetByIndex(0);

        $writer = new Xlsx($spreadsheet);

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename={$dist_channel->description}.xlsx");
        header('Cache-Control: max-age=0');

        // Output the generated .xlsx file to the browser
        $writer->save('php://output');

        exit;
    }



    public function computeForecasts(Request $request)
    {
        $growthRate = 0.15; // Example growth rate assumption (5%)
        $inflationRate = 0.62;
        // Validate the request parameters
        // $request->validate([
        //     'alpha' => 'required|numeric', // Example validation rule for alpha parameter
        //     // 'data' => 'required|array', // Example validation for input data
        // ]);

        // // Extract parameters from the request
        // $alpha = $request->input('alpha');

        // Dummy data for demonstration
        $data = [100, 120, 130, 140, 150, 160, 170, 180, 190, 200];

        // Compute forecasts using different methods
        $exponentialSmoothing = $this->exponentialSmoothing($data, $alpha,$growthRate,$inflationRate);
        $simpleMovingAverage = $this->simpleMovingAverage($data, $windowSize = 3);
        $weightedMovingAverage = $this->weightedMovingAverage($data, $weights = [0.2, 0.3, 0.5]);
        $naiveForecast = $this->naiveForecast($data);
        $seasonalNaiveForecast = $this->seasonalNaiveForecast($data, $seasonality = 4);
        $holtsLinearTrend = $this->holtsLinearTrend($data, $alpha, $beta = 0.5);
        $holtWinters = $this->holtWinters($data, $alpha, $beta = 0.5, $gamma = 0.5, $seasonality = 4);
    

        // Return the computed forecasts
        return response()->json([
            'exponential_smoothing' => $exponentialSmoothing,
            'simple_moving_average' => $simpleMovingAverage,
            'weighted_moving_average' => $weightedMovingAverage,
            'naive_forecast' => $naiveForecast,
            'seasonal_naive_forecast' => $seasonalNaiveForecast,
            'holtsLinearTrend' => $holtsLinearTrend,
            'holtWinters' => $holtWinters,
        ]);
    }

    private function exponentialSmoothing($data, $alpha, $growthRate, $inflationRate)
    {
        $n = count($data);
        $forecasts = [];
    
        // Initial forecast (same as the first actual value)
        $forecasts[] = $data[0];
    
        // Apply exponential smoothing formula for subsequent periods
        for ($i = 1; $i < $n; $i++) {
            // Adjust the data with growth rate and inflation rate
            $adjustedData = $data[$i] * (1 + $growthRate) * (1 + $inflationRate);
    
            // Apply exponential smoothing formula
            $forecast = $alpha * $adjustedData + (1 - $alpha) * $forecasts[$i - 1];
            $forecasts[] = $forecast;
        }
    
        return $forecasts;
    }
    

    private function simpleMovingAverage($data, $windowSize)
    {
        $n = count($data);
        $forecasts = [];

        // Calculate moving averages for each period
        for ($i = $windowSize - 1; $i < $n; $i++) {
            $sum = array_sum(array_slice($data, $i - $windowSize + 1, $windowSize));
            $average = $sum / $windowSize;
            $forecasts[] = $average;
        }

        return $forecasts;
    }

    private function weightedMovingAverage($data, $weights)
    {
        $n = count($data);
        $m = count($weights);
        $forecasts = [];

        // Calculate weighted moving averages for each period
        for ($i = $m - 1; $i < $n; $i++) {
            $weightedSum = 0;
            for ($j = 0; $j < $m; $j++) {
                $weightedSum += $weights[$j] * $data[$i - $j];
            }
            $forecasts[] = $weightedSum;
        }

        return $forecasts;
    }

    private function naiveForecast($data)
    {
        $n = count($data);
        $forecasts = [];

        // Use the last observed value as the forecast for all future periods
        $lastValue = end($data);
        for ($i = 0; $i < $n; $i++) {
            $forecasts[] = $lastValue;
        }

        return $forecasts;
    }

    private function seasonalNaiveForecast($data, $seasonality)
    {
        $n = count($data);
        $forecasts = [];

        // Use the value from the same season in the previous year as the forecast
        for ($i = 0; $i < $n; $i++) {
            $forecasts[] = $data[$i % $seasonality];
        }

        return $forecasts;
    }

    private function holtsLinearTrend($data, $alpha, $beta)
    {
        $n = count($data);
        $forecasts = [];
        
        // Initial level and trend estimates
        $level = $data[0];
        $trend = $data[1] - $data[0];
        
        // Compute forecasts
        foreach ($data as $index => $value) {
            if ($index === 0) {
                $forecasts[] = $value;
                continue;
            }
            
            // Update level and trend estimates
            $prevLevel = $level;
            $prevTrend = $trend;
            $level = $alpha * $value + (1 - $alpha) * ($prevLevel + $prevTrend);
            $trend = $beta * ($level - $prevLevel) + (1 - $beta) * $prevTrend;
            
            // Forecast the next value
            $forecasts[] = $level + $trend;
        }
        
        return $forecasts;
    }

    private function holtWinters($data, $alpha, $beta, $gamma, $seasonality)
    {
        $n = count($data);
        $forecasts = [];
        $level = $data[0];
        $trend = $data[1] - $data[0];
        $seasonal = array_slice($data, 0, $seasonality);
        
        // Initialize seasonal indices
        $seasonalIndices = [];
        for ($i = 0; $i < $seasonality; $i++) {
            $seasonalIndices[] = $data[$i] / array_sum($seasonal);
        }
        
        // Compute forecasts
        foreach ($data as $index => $value) {
            if ($index === 0) {
                $forecasts[] = $value;
                continue;
            }
            
            // Update level, trend, and seasonal estimates
            $prevLevel = $level;
            $prevTrend = $trend;
            $prevSeasonal = $seasonal;
            $level = $alpha * ($value - $seasonalIndices[$index % $seasonality]) + (1 - $alpha) * ($prevLevel + $prevTrend);
            $trend = $beta * ($level - $prevLevel) + (1 - $beta) * $prevTrend;
            $seasonalIndices[$index % $seasonality] = $gamma * ($value - $level) + (1 - $gamma) * $seasonalIndices[$index % $seasonality];
            
            // Forecast the next value
            $forecasts[] = $level + $trend + $seasonalIndices[$index % $seasonality];
        }
        
        return $forecasts;
    }

    
    public function postassumptionsform(Request $request)
    {
    dd($request->all());
        // Check if there is a pending budget
        $pendingBudgetExists = DB::table('budget_company')
            ->where('status', 'PENDING')
            ->where('budget_id', '<>',$request->budget_id)
            ->where('deleted', 'N')
            ->exists();
    
        // If there is a pending budget, return error status
        if($pendingBudgetExists){
            $result = array("status" => 0);
            return $result;
        }
    
        // Get the logged-in user's username
        $user = Auth::user()->user_name;
    
        // Insert assumptions data into the database
        $inserted = DB::table('Assumptions')->insert([
            'AssumptionType' => $request->assumption_type,
            'Budget_id' => $request->budget_id,
            'Description' => $request->description,
            'Value' => $request->value,
            'StartDate' => $request->start_date,
            'EndDate' => $request->end_date,
            'LastUpdated' => now(),
            'Notes' => $request->notes
        ]);

    
    
        // If insertion is successful, display success message
        if($inserted){
            Session::flash('success', 'Assumption added successfully.');
        } else {
            Session::flash('error', 'Failed to add assumption. Please try again.');
        }
    
        return redirect()->back();
    }

    //main datatable
    public function assumptionsdatatable(Request $request)
    {

        $bgid= $request->checkbgtid;
        $result = DB::table('budget_company')
            ->where('budget_id', $bgid)
            ->first();

        $levels = DB::table('assumptions')
                    ->where('budget_id',$bgid)
                    ->get();

        return Datatables::of($levels)
                ->make(true);
        

    }

        
    
    public function download_actuals(Request $request)
    {
       
        // Retrieve the budget ID
        $bgid = $request->budgetuploadid;
        $master_budget = DB::table('budget_company')->where('budget_id', $bgid)->first();
        
        // Extract year and month from the provided date
        list($start_year, $start_month) = explode('-', $request->actualsdate);
        
        // Retrieve principal officer and branch names
        $branch_name = $request->fcategoryur_0;
    
        
        // Find the branch ID
        $branch = DB::table('branch')
            ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE ?", ["%" . strtoupper(str_replace(' ', '', $branch_name)) . "%"])
            ->pluck('branch')
            ->map(fn($value) => intval($value))
            ->first();
        
 
        // Retrieve account types and departments
        $acc_types = DB::table('intermediary')->get();
       
        $depts = DB::table('dept')->pluck('description')->toArray();
        
        // Retrieve data from debitmast_history table and sort it
        $data = DB::table('debitmast_history')
            ->where('branch', $branch)
            ->where('type_of_bus', $request->bus_type)
            ->where(function ($query) use ($start_year, $start_month) {
                $query->where('account_year', '>', $start_year)
                    ->orWhere(function ($query) use ($start_year, $start_month) {
                        $query->where('account_year', '=', $start_year)
                              ->where('account_month', '>=', $start_month);
                    });
            })
            ->orderBy('account_year')
            ->orderBy('account_month')
            ->groupBy('account_year', 'account_month', 'dept', 'acc_type', 'type_of_bus','branch','agent')
            ->select(DB::raw("account_month || '/' || account_year as accountperiod"), 'account_year', 'account_month', 'dept','branch','agent', 'acc_type', 'type_of_bus', DB::raw('SUM(nett_amount) as nett_amount'))
            ->get();
        
        $unique_account_periods = $data->map(fn($item) => 'ACTUAL ' . $item->accountperiod)->unique()->sort()->values();
        
        // Generate current year's monthly periods
        $current_year = $master_budget->year;
        $current_year_periods = collect(range(1, 12))->map(fn($month) => sprintf("%02d/%d", $month, $current_year));
        
        // Set headers with department names followed by unique account periods, a blank column, and current year's monthly periods
        $header = array_merge(['DEPARTMENTS'], $unique_account_periods->toArray(), [''], $current_year_periods->toArray());
        
        // Set column widths
        $columnWidths = array_fill(0, count($header), 20);
        
        // Create a new Spreadsheet
        $spreadsheet = new Spreadsheet();
        
        // Loop through each account type and create a sheet for each
        foreach ($acc_types as $acctype) {
            
            $filtered_data = $data->where('branch', $acctype->branch)->where('agent', $acctype->agent);

  

            $sheet = $spreadsheet->createSheet();
            $sheet->setTitle(str_replace(' ', '', $acctype->name));
            
            $colIndex = 1;
            $rowIndex = 1;
            
            // Set header row for account periods, blank column, and current year's monthly periods
            foreach ($header as $head) {
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, str_replace(' ', '', $head));
                $sheet->getStyleByColumnAndRow($colIndex, $rowIndex)->getFont()->setBold(true);
                $sheet->getColumnDimensionByColumn($colIndex)->setWidth($columnWidths[$colIndex - 1]);
                $colIndex++;
            }
            
            // Populate data rows
            $rowIndex++;
            
            foreach ($depts as $dept) {
                $colIndex = 1;
                $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $dept);
                $colIndex++;
                
                foreach ($unique_account_periods as $account_period) {
                    $dept_code = DB::table('dept')->where('description', $dept)->first();
                    $account_period_without_actual = str_replace('ACTUAL ', '', $account_period);
    
                    $amount = number_format($filtered_data
                        ->where('accountperiod', $account_period_without_actual)
                        ->where('dept', $dept_code->dept)
                        ->first()
                        ->nett_amount ?? 0);
                    
                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $amount);
                    $colIndex++;
                }
                
                // Skip a column for the blank space
                $colIndex++;
                
                // Populate current year's monthly periods
                foreach ($current_year_periods as $current_period) {
                    list($month, $year) = explode('/', $current_period);
                    
                    $dept_code = DB::table('dept')->where('description', $dept)->first();
                    
                    $amount = number_format($filtered_data
                        ->where('account_year', $year)
                        ->where('account_month', $month)
                        ->where('dept', $dept_code->dept)
                        ->first()
                        ->nett_amount ?? 0);
                   
                    
                    $sheet->setCellValueByColumnAndRow($colIndex, $rowIndex, $amount);
                    $colIndex++;
                }
                
                $rowIndex++;
            }
        }
        
        $spreadsheet->removeSheetByIndex(0);
 
        // Write the spreadsheet to the output
        $writer = new Xlsx($spreadsheet);
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=\"{$branch_name}_{$request->bus_type}.xlsx\"");
        header('Cache-Control: max-age=0');
        $writer->save('php://output');
    }
    
    

    public function confirmforecast(Request $request)
    {
        // Parse the analysis date
        $analysis_date = $request->analysis_date;
        $date_parts = explode('-', $analysis_date);
        $start_year = $date_parts[0];
        $start_month = $date_parts[1];
        
        // Retrieve the necessary data from the database
        $budget = DB::table('budget_uw')->where('budget_id', $request->budgetuploadid)->get();
        $forecast = DB::table('budget_uw_forecast')->where('budget_id', $request->budgetuploadid)->get();
        $assumptions = DB::table('Assumptions')->where('budget_id', $request->budgetuploadid)->get();
    
        // Retrieve historical data starting from the specified date
        $historic_data = DB::table('debitmast_history')
            ->where(function ($query) use ($start_year, $start_month) {
                $query->where('account_year', '>', $start_year)
                    ->orWhere(function ($query) use ($start_year, $start_month) {
                        $query->where('account_year', '=', $start_year)
                              ->where('account_month', '>=', $start_month);
                    });
            })
            ->get();
    
        // Define an array to map month numbers to names
        $months = [
            1 => 'JANUARY',
            2 => 'FEBRUARY',
            3 => 'MARCH',
            4 => 'APRIL',
            5 => 'MAY',
            6 => 'JUNE',
            7 => 'JULY',
            8 => 'AUGUST',
            9 => 'SEPTEMBER',
            10 => 'OCTOBER',
            11 => 'NOVEMBER',
            12 => 'DECEMBER'
        ];
    
        // Array to hold all updates for batch processing
        $updates = [];
    
        // Process each historical data record
        foreach ($historic_data as $data) {
            $businesstype = $data->type_of_bus == 1 ? 'POL' : 'REN';
            $month_name = $months[$data->account_month] ?? '';
    
            if (empty($month_name)) {
                continue; // Skip if month name is not valid
            }
    
            foreach ($assumptions as $assumption) {
                if ($assumption->assumptiontype == "M") {
                    $growth_rate = $assumption->Value / 100;
                    $forecasted_value = $data->nett_amount * (1 + $growth_rate);
    
                    // Prepare the update data
                    $updates[] = [
                        'budget_id' => $request->budgetuploadid,
                        'level2' => $data->intermediary_number,
                        'level3' => $data->branch,
                        'level4' => $data->acc_type,
                        'level5' => $data->class,
                        'businesstype' => $businesstype,
                        'month_name' => $month_name,
                        'forecasted_value' => $forecasted_value
                    ];
                }
                // Add more assumption types as needed
            }
        }
    
        // Perform the batch update
        foreach ($updates as $update) {
            
            DB::table('budget_uw_forecast')
                ->updateOrInsert(
                    [
                        'budget_id' => $update['budget_id'],
                        'level2' => $update['level2'],
                        'level3' => $update['level3'],
                        'level4' => $update['level4'],
                        'level5' => $update['level5'],
                        'businesstype' => $update['businesstype']
                    ],
                    [$update['month_name'] => $update['forecasted_value']]
                );
        }
    
        // Return a response indicating success
        return response()->json(['message' => 'Forecast confirmed successfully.']);
    }
    


    public function upload_template(Request $request)
    {
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try {
            
      
            $budget_id = $request->fpbudgetuploadid;
            $fileData = $request->file('forecastfile');
            $businessType = $request->bus_types;
            $principal_officer = $request->fpcategoryur_0;
            $branch = $request->fpcategoryur_1;

            $fileExtension = $fileData->getClientOriginalExtension();
            $fileName = array_slice(explode(".", $fileData->getClientOriginalName()), 0, -1);
            $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
            $sections = explode("-", $hierarchy->description);
            $levels = array_slice($sections, 0, -1);
            $level1 = null;
            $level2 = null;
            $level3 = null;
            $level4 = null;
         

            $fileN = str_replace(' ', '', strtoupper($fileName[0]));

            $fileN = preg_replace('/\([^)]*\)/', '', $fileN);

            $parts = explode('_', $fileN);
            $branch = $parts[0]; 
            $businessType = $parts[1]; 


            if($businessType == '1'){
                $type_of_bus = 'POL';
            }else{
                $type_of_bus = 'REN';
            }
            

            $branch_id = DB::table('branch')
            ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%" . strtoupper(str_replace(' ', '', $branch)) . "%'")
            ->pluck('branch')
            ->first();


            $year = DB::table('budget_company')
                    ->where('budget_id', $budget_id)
                    ->first();
        
            $yearValue = $year->year; 
            
            if ($fileExtension == 'xlsx') {
                $spreadsheet = IOFactory::load($fileData);
                $worksheets = $spreadsheet->getAllSheets();
                
            
            
                foreach ($worksheets as $worksheet) {
                   
                    $channeldata = [];
                    $departmentdata = [];
                    $sheetName = null;
                    $branch = null;
                    $foundDepartment = false;
                    $numElements=0;
            
                    $sheetName = trim(preg_replace('/[\\[\\]:*?\\\\\/]/', '', $worksheet->getTitle()));
                    $sheetData = $worksheet->toArray();
            
                    $sheetName = str_replace(' ', '', strtoupper($sheetName));
                    
                    $acctype = DB::table('intermediary')
                        ->whereRaw("UPPER(REPLACE(name, ' ', '')) LIKE '%$sheetName%'")
                        ->pluck('intermediary_number')
                        ->map(function ($value) {
                            return strval($value);
                        })
                        ->first();


           
                        foreach ($sheetData as $value) {
                            
                            if (empty(str_replace('"', '', $value[0])) || str_replace('"', '', $value[0]) == null || str_replace('"', '', $value[0]) == "DEPARTMENTS") {
                                continue;
                            }
                
                            $desc = str_replace('"', '', $value[0]);

                            $numElements = count($value);
                    
                          
                            $months = array_slice($value, $numElements-12, $numElements);


                            $months = array_map(function($month) {
                                return str_replace('"', '', $month);
                            }, $months);

                        
                            if ($acctype !== null && $desc !== "") {
                                $departmentdata[] = $desc . "-" . $acctype . "-" . implode('-', $months);
                            }
                           
                        }



                    foreach ($departmentdata as $ddata) {
                        $explodedData = explode('-', $ddata);
                        $explodedData = array_map('trim', $explodedData);

                        $dep = DB::table('dept')->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%" . strtoupper(str_replace(' ', '', $explodedData[0])) . "%'")->first();

                        // Check if the exploded data has the expected number of elements
                        if (count($explodedData) !== 14) {
                            continue;
                        }

                        $max_forecast = DB::table('budget_uw_forecast')->where('budget_id', $budget_id)
                        ->where('running_budget', 'Y')
                        ->first();

               
                        // Initialize the insert data array with common fields
                        $insertData = [
                            'budget_id' => $budget_id,
                            'businesstype' => $type_of_bus,
                            'year' => $yearValue,
                            'level1' => 0,
                            'level2' => $branch_id,
                            'level3' => $explodedData[1],
                            'level4' => $dep ->dept,
                            'level9' => intval($max_forecast->level9)+1,
                            'account_month' => Carbon::now()->format('m')
                        ];

                        // dd($budget_id);



                   

                        $start = 2;

                        // Calculate the budget for each month
                        $months = ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december'];
                        for ($i = 0; $i <= 11; $i++) {
                            $insertData[$months[$i]] = floatval(str_replace(',', '', $explodedData[$start]));
                            $start++;
                        }

                        if($year->running_budget == 'Y'){

                            $budgetexists = DB::table('budgettracker')
                            ->where('budget_id', (int)$budget_id)
                            ->where('branch', $branch_id)
                            ->where('bus_type', '1')
                            ->where('budget_status', 'Y')
                            ->count();

                            // dd("here", $branch_id, $budgetexists, $budget_id);


                            if($budgetexists > 0){

                                Budget_uw_forecast::insert($insertData);

                                $tracker = DB::table('budgettracker')
                                ->where('budget_id', (int)$budget_id)
                                ->where('branch', $branch_id)
                                ->where('bus_type', $request->bus_types)
                                ->update(['forecast_status'=> 'Y']);

                            }
                            // else{
                            //     return;
                            // }


                        }else{
                            
                            
                            Budget_uw::insert($insertData);

                            // dd('budget_id=>'.$budget_id,'branch_id=>'.$branch_id, 'bustype=>'.$request->bus_types);

                            $tracker = DB::table('budgettracker')
                            ->where('budget_id', (int)$budget_id)
                            ->where('branch', $branch_id)
                            ->where('bus_type', $bustype)
                            ->update(['budget_status'=> 'Y']);


                        }
                       
                    }
            
                    // Move to the next business type for the next worksheet
                    // $businessTypeIndex = ($businessTypeIndex + 1) % count($businessTypes);
                }
            } else {
                return ['status' => 0];
            }
            

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::flash('success', 'Uploaded successfully.');
            return redirect()->back();

        } catch (\Throwable $th) {

            dd($th);
      
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::flash('error', 'Error occured.');
            return redirect()->back();
        }
        return $res;

    }

    public function initialdatatable(Request $request){

        $selectedValues = $request->selectedValues;


        $level1 = null;
        $level2 = null;
        $level3= null;
        $level4= null;
        $bgid= $request->checkbgtid;

        $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
        $sections = explode("-", $hierarchy->description);
        $levels = $sections;

        

        foreach ($selectedValues as $categoryId => $selectedOptions) {

                $parts = $selectedOptions[0];
                switch ($categoryId) {
                    case 1:
                        $level1 = $parts;
                        break;
                    case 2:
                        $level2 = $parts;
                        break;
                    case 3:
                        $level3 = $parts;
                        break;
                    case 4:
                        $level4 = $parts;
                        break;
                    case 5:
                        $level5 = $parts;
                        break;
                }

        }

   

        $result = DB::table('budget_company')
            ->where('budget_id', $bgid)
            ->select('year', 'hierarchy_id')
            ->first();
   

            if (count($levels) > 0) {
                if ($levels[0] == "BRANCH") {

                    $budget1 = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level2%'")
                    ->pluck('branch')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();


                } elseif ($levels[0] == "PRINCIPAL OFFICER") {

                    $fileN = str_replace(' ', '', strtoupper($level2));
                    $user = DB::table('aimsusers')->whereRaw("UPPER(REPLACE(NAME, ' ', '')) LIKE '%$fileN%'")->first();
                    $budget1 = $user->user_id;
                }
            }


            if (count($levels) > 1) { 

                if ($levels[1] == "BRANCH") {
                    $budget2 = DB::table('branch')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level3%'")
                        ->pluck('branch')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
                elseif ($levels[1] == "INTERMEDIARY") {
                    $budget2 = DB::table('acctype')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level3%'")
                        ->pluck('acc_type')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
            }

            if (count($levels) > 2 ) { 

                if ($levels[2] == "INTERMEDIARY") {
                    $budget3 = DB::table('acctype')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level4%'")
                        ->pluck('acc_type')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
                elseif ($levels[2] == "CLASS") {
                    $budget3 = DB::table('dept')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level4%'")
                        ->pluck('dept')
                        ->map(function ($value) {
                            return intval($value);
                        });
                }
                   
                
            }

            if (count($levels) > 3 ) { 

                if ($levels[3] == "CLASS") {
                    $budget4 = DB::table('dept')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level5%'")
                        ->pluck('dept')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
                   
                
            }

        $budget3 = $budget3??null;
   

        $levels = DB::table('budget_uw')
        ->select('budget_id', 'businesstype', 
                 DB::raw('SUM(january) AS january'), 
                 DB::raw('SUM(february) AS february'), 
                 DB::raw('SUM(march) AS march'), 
                 DB::raw('SUM(april) AS april'), 
                 DB::raw('SUM(may) AS may'), 
                 DB::raw('SUM(june) AS june'), 
                 DB::raw('SUM(july) AS july'), 
                 DB::raw('SUM(august) AS august'), 
                 DB::raw('SUM(september) AS september'), 
                 DB::raw('SUM(october) AS october'), 
                 DB::raw('SUM(november) AS november'), 
                 DB::raw('SUM(december) AS december'))
        ->where('budget_id', $bgid)
        ->groupBy('budget_id', 'businesstype')
        ->get();
    
    
    
        return Datatables::of($levels)->make(true);
    
       

    }
    

    public function forecastdatatable(Request $request){

        $selectedValues = $request->selectedValues;


        $level1 = null;
        $level2 = null;
        $level3= null;
        $level4= null;
        $bgid= $request->checkbgtid;

        $hierarchy = DB::table('budget_hierarchy')->where('status','A')->get('description')[0];
        $sections = explode("-", $hierarchy->description);
        $levels = $sections;

        

        foreach ($selectedValues as $categoryId => $selectedOptions) {

                $parts = $selectedOptions[0];
                switch ($categoryId) {
                    case 1:
                        $level1 = $parts;
                        break;
                    case 2:
                        $level2 = $parts;
                        break;
                    case 3:
                        $level3 = $parts;
                        break;
                    case 4:
                        $level4 = $parts;
                        break;
                    case 5:
                        $level5 = $parts;
                        break;
                }

        }

   

        $result = DB::table('budget_company')
            ->where('budget_id', $bgid)
            ->select('year', 'hierarchy_id')
            ->first();
   

            if (count($levels) > 0) {
                if ($levels[0] == "BRANCH") {

                    $budget1 = DB::table('branch')
                    ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level2%'")
                    ->pluck('branch')
                    ->map(function ($value) {
                        return intval($value);
                    })
                    ->first();


                } elseif ($levels[0] == "PRINCIPAL OFFICER") {

                    $fileN = str_replace(' ', '', strtoupper($level2));
                    $user = DB::table('aimsusers')->whereRaw("UPPER(REPLACE(NAME, ' ', '')) LIKE '%$fileN%'")->first();
                    $budget1 = $user->user_id;
                }
            }


            if (count($levels) > 1) { 

                if ($levels[1] == "BRANCH") {
                    $budget2 = DB::table('branch')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level3%'")
                        ->pluck('branch')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
                elseif ($levels[1] == "INTERMEDIARY") {
                    $budget2 = DB::table('acctype')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level3%'")
                        ->pluck('acc_type')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
            }

            if (count($levels) > 2 ) { 

                if ($levels[2] == "INTERMEDIARY") {
                    $budget3 = DB::table('acctype')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level4%'")
                        ->pluck('acc_type')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
                elseif ($levels[2] == "CLASS") {
                    $budget3 = DB::table('dept')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level4%'")
                        ->pluck('dept')
                        ->map(function ($value) {
                            return intval($value);
                        });
                }
                   
                
            }

            if (count($levels) > 3 ) { 

                if ($levels[3] == "CLASS") {
                    $budget4 = DB::table('dept')
                        ->whereRaw("UPPER(REPLACE(description, ' ', '')) LIKE '%$level5%'")
                        ->pluck('dept')
                        ->map(function ($value) {
                            return intval($value);
                        })
                        ->first();
                } 
                   
                
            }

        $budget3 = $budget3??null;
   

        $levels = DB::table('budget_uw_forecast')
        ->select('budget_id','account_month','businesstype', 
                 DB::raw('SUM(january) AS january'), 
                 DB::raw('SUM(february) AS february'), 
                 DB::raw('SUM(march) AS march'), 
                 DB::raw('SUM(april) AS april'), 
                 DB::raw('SUM(may) AS may'), 
                 DB::raw('SUM(june) AS june'), 
                 DB::raw('SUM(july) AS july'), 
                 DB::raw('SUM(august) AS august'), 
                 DB::raw('SUM(september) AS september'), 
                 DB::raw('SUM(october) AS october'), 
                 DB::raw('SUM(november) AS november'), 
                 DB::raw('SUM(december) AS december'))
        ->where('budget_id', $bgid)
        ->groupBy('budget_id', 'businesstype','account_month')
        ->get();
    
    
    
        return Datatables::of($levels)->make(true);
    
       

    }

    public function checkMandatoryDocs(Request $request){
        
        $entityId = $request->entityId;
        $contextId = $request->contextId;
        $processCode = $request->processCode;
        $dept = $request->deptId;
        // dd($entityId, $contextId, $processCode, $dept);

        $upload_status = checkDocUploadStatus($entityId, $contextId, $processCode, $dept);
        // dd($upload_status);

        return $upload_status;

    }
    




}
  
