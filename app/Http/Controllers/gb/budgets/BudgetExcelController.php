<?php

namespace App\Http\Controllers\gb\budgets;

use App\Budget_channel;
use App\BudgetLevel;
use App\BudgetHierarchy;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

use Session;
use DB;
use Illuminate\Support\Carbon;
// use Auth;
use DataTables;
use App\Models\Admin\CompanyProfile\CompanyProfile;
use App\Models\Admin\Configuration\CRMProcesses;
use App\Models\Admin\Configuration\Approval_levels;
use App\Models\Admin\Configuration\Approvers;
use App\Models\Admin\Configuration\ApprovalMatrix;
use App\Models\Admin\Configuration\Process_approval;
use App\Models\Admin\Configuration\Approval_flow;
use App\Models\Admin\Configuration\Approvals;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use App\Exports\ExcelDataExport;
use Response;

class BudgetExcelController extends Controller {
    
   public function downloadExcel(){
    $file = public_path() . "/downloads/Templatebudget.xlsx";

    return Response::download($file);
    }
}

