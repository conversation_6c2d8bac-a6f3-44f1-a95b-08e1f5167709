<?php

namespace App\Http\Controllers\Admin\Configuration\Budgets;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use DB;
use Illuminate\Support\Carbon;
use Auth;
use DataTables;
use App\Models\Admin\CompanyProfile\CompanyProfile;

class RefocusBudgetController extends Controller
{
    public function refocusBudget(Request $request){
        // try{
            $acyear = $request->refocusyear;
            $cbudget = str_replace(",","",$request->refocusbgt);
            $brbudget = $request->refocusbrate;
            $agbudget = $request->refocusarate;
            $description = $request->refocusnarration;

            $budget_refocus = DB::table('company_budget')->where('year', $acyear)->max('refocus');
            $company_params = CompanyProfile::first();
            $refocus_number = (int)$company_params->budget_refocus;
            $budget_exists = DB::table('company_budget')
                            ->where('year', $acyear)
                            ->where('refocus', $budget_refocus)
                            ->where('status', 'PENDING')
                            ->count();
            $prevacyear = DB::table('company_budget')
                        ->where('year', $acyear)
                        ->where('status', 'ACTIVE')
                        ->first();
            $prevyear = $prevacyear->rate_year;
            if ($budget_exists > 0) {
                $res = [
                    'status' => 0,
                    'message' => "An existing  budget refocus for that year has not been processed, Please wait for approval or cotact the administrator",
                ];
            }else {
                if ($budget_refocus <= $refocus_number) {
                    $budget_refocus += 1;

                    $conn = new \GuzzleHttp\Client();
                    $previosyear['year'] = $prevyear;
                    $url=''.ENV('API_URL').'budgetprevyear/';
                    $performance = $conn->post($url,  ['form_params'=>$previosyear]);

                    $upload = DB::table('company_budget')->insert(
                        array(
                            'rate_year' => $prevyear,
                            'year' => $acyear,
                            'description'=> $description,
                            'dola' => Carbon::now(),
                            'user_string' => Auth::user()->username,
                            'amount' => $cbudget,
                            'new_branch_target' => $brbudget,
                            'new_agent_target' => $agbudget,
                            'status' => "PENDING",
                            'refocus' => $budget_refocus,
                        )
                    );

                    // dd("ian");

                    $url=''.ENV('API_URL').'budgetcompanypm';
                    $response = $conn->get($url);
                    $pmperformance = json_decode($response->getBody()->getContents());

                    // dd(count($pmperformance));
                    
                    $budget = DB::table('company_budget')
                            ->where('year',$acyear)
                            ->where('refocus',$budget_refocus)
                            ->first();
                    $company_budget = $budget->amount;
                    $company_budget_pm = $budget->amount;
                    $company_per_month = array();
                    
                    if (count($pmperformance)==0) {
                        for ($i=1; $i<=12; $i++) {
                            $month_rate = 100/12;
                            $month_target = ($month_rate/100)*$company_budget;
                            $upload = [
                                'budget_id' => $budget->budget_id,
                                'dola' => Carbon::now(),
                                'amount' => $month_target,
                                'month_rate' => $month_rate,
                                'month'=> $i,
            
                                ];
                            array_push($company_per_month,$upload);
                        }
                    } else {
                        for ($i=1; $i<=12; $i++) {
                            if (!in_array($i,array_column($pmperformance, 'month'))) {
                                $month_rate = $brbudget;
                                $month_target = ($month_rate/100)*$company_budget;
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'dola' => Carbon::now(),
                                    'amount' => $month_target,
                                    'month_rate' => $month_rate,
                                    'month'=> $i,
                
                                    ];
                                array_push($company_per_month,$upload);
                                $company_budget_pm = $company_budget_pm - $month_target;
                            }
                        }
    
                        $company_month_total = 0;
                        foreach ($pmperformance as $month) {
                            $company_month_total += $month->gross_amount;
                        }
                        // dd($company_month_total);
                        for ($i=1; $i<=12; $i++) {
                            if (in_array($i,array_column($pmperformance, 'month'))) {
                                $month = collect($pmperformance)->where('month',$i);
                                foreach ($month as $m) {
                                    $month_target = ($m->gross_amount/$company_month_total)*$company_budget_pm;
                                    $month_rate = ($month_target/$company_budget)*100;
                                    $upload = [
                                        'budget_id' => $budget->budget_id,
                                        'dola' => Carbon::now(),
                                        'amount' => $month_target,
                                        'month_rate' => $month_rate,
                                        'month'=> $i,
                    
                                    ];
                                    array_push($company_per_month, $upload);
                                }
                            }
                        }
                    }
                    // dd($company_per_month);
                    DB::table('company_budget_pm')->insert($company_per_month);
            
                    $branches = new \GuzzleHttp\Client();
                    $url=''.ENV('API_URL').'budgetbranch';
                    $response = $branches->get($url);
                    $branches = json_decode($response->getBody()->getContents());

                    $url = ''.ENV('API_URL').'branch/';
                    $branch_names = $conn->get($url);
                    $branch_names = json_decode($branch_names->getBody()->getContents());

                    $company_branch_budget = $company_budget;
                    $branches_total_amount = 0;
                    for ($i=0; $i < count($branches); $i++) { 
                        $branch_amount =  abs($branches[$i]->gross_amount);
                        $branches_total_amount += $branch_amount;
                    }
            

                    $branch_budgets_upload = array();
                    if (collect($branches)->count() == 0) {
                        foreach ($branch_names as $br) {
                            $branch_rate = 100/collect($branch_names)->count();
                            $branch_target = ($branch_rate/100)*$cbudget;
                            $upload = [
                                'budget_id' => $budget->budget_id,
                                'branch_id' => $br->branch,
                                'dola' => Carbon::now(),
                                'amount' => $branch_target,
                                'branch_rate' => $branch_rate,
            
                            ];
                            array_push($branch_budgets_upload,$upload);
                        }
                       DB::table('branch_budget')->insert($branch_budgets_upload);
                        
                    } else {
                
                        foreach ($branch_names as $br) {
                            $br_perf_exists = collect($branches)->where('branch',$br->branch)->count();
                            if ($br_perf_exists == 0) {
                                $branch_rate = $brbudget;
                                $branch_target = ($branch_rate/100)*$cbudget;
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'branch_id' => $br->branch,
                                    'dola' => Carbon::now(),
                                    'amount' => $branch_target,
                                    'branch_rate' => $branch_rate,
                
                                ];
                                array_push($branch_budgets_upload,$upload);
                                 $company_branch_budget = $company_branch_budget - $branch_target;
                            }
        
                        }

                        foreach ($branches as $branch) {
                            if (!isset($branch->gross_amount) || $branch->gross_amount == 0) {
                                $branch_rate = $brbudget;
                                $branch_target = ($branch_rate/100)*$company_budget;
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'branch_id' => $branch->branch,
                                    'dola' => Carbon::now(),
                                    'amount' => $branch_target,
                                    'branch_rate' => $branch_rate,
                
                                ];
                                array_push($branch_budgets_upload,$upload);
                                $company_branch_budget = $company_branch_budget - $branch_target;
                            }
                        }
                        // dd($company_branch_budget);
                        foreach ($branches as $branch) {
                            if(isset($branch->gross_amount) && $branch->gross_amount != 0){
                                $branch_target = (abs($branch->gross_amount)/$branches_total_amount)*$company_branch_budget;
                                $branch_rate = ($branch_target/$cbudget)*100;
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'branch_id' => $branch->branch,
                                    'dola' => Carbon::now(),
                                    'amount' => $branch_target,
                                    'branch_rate' => $branch_rate,
                                ];
                                array_push($branch_budgets_upload,$upload);
                            }
                        }
                        // dd($company_branch_budget);
                        DB::table('branch_budget')->insert($branch_budgets_upload);
                    }


                    $agents = new \GuzzleHttp\Client();
                    $url = ''.ENV('API_URL').'intermediaries/';
                    $intermediaries = $agents->get($url);
                    $intermediaries = json_decode($intermediaries->getBody()->getContents());
                    
                    $url=''.ENV('API_URL').'budgetagent';
                    $intermed = $agents->get($url);
                    $agents = json_decode($intermed->getBody()->getContents());

                    $branchbudget = DB::table('branch_budget')->where('budget_id',$budget->budget_id)->get();
                    $agent_budgets_upload = array();
                    foreach ($branchbudget as $branch) {
                        $brintermediaries = collect($intermediaries)->where('branch', $branch->branch_id);
                        $brcbudget = collect($branchbudget)
                                    ->where('branch_id', $branch->branch_id)
                                    ->first();
                        $bagents = collect($agents)->where('branch', $branch->branch_id);
                        
                        // dd($bagents->count());
    
                        $branch_budget = $brcbudget->amount;
                        $branch_target = $brcbudget->amount;
                        $branchagent_total = 0;
                        foreach ($bagents as $agent) {
                            if($branch->branch_id == $agent->branch){
                                $branchagent_total += abs($agent->gross_amount);
                            }
                        }

                        if (collect($bagents)->count() == 0) {
                            foreach ($brintermediaries as $inter) {
                                $agent_rate = 100/count($brintermediaries);
                                $agent_target = ($agent_rate/100)*$branch_budget;
    
                                $upload = [
                                    'agent_id' => $inter->agent,
                                    'branch_id' => $inter->branch,
                                    'budget_id' => $budget->budget_id,
                                    'dola' => Carbon::now(),
                                    'amount' => $agent_target,
                                    'agent_rate' => $agent_rate,
                                ];
                                array_push($agent_budgets_upload, $upload);
                            }
                        } else {
                            foreach ($brintermediaries as $inter) {
                                $agts = collect($agents)->where('branch', $branch->branch_id)->toArray();
                                if(!in_array($inter->agent, array_column($agts, 'agent_no'), true)){
                                    $agent_rate = $agbudget;
                                    $agent_target = ($agent_rate/100)*$branch_target;
        
                                    $branch_budget = $branch_budget - $agent_target;
        
                                    $upload = [
                                        'agent_id' => $inter->agent,
                                        'branch_id' => $inter->branch,
                                        'budget_id' => $budget->budget_id,
                                        'dola' => Carbon::now(),
                                        'amount' => $agent_target,
                                        'agent_rate' => $agent_rate,
                                    ];
                                    array_push($agent_budgets_upload, $upload);
                                }
                            }
        
                            foreach ($agents as $agent) {
                                if(($branch->branch_id == $agent->branch) && ((!isset($agent->gross_amount) || $agent->gross_amount == 0))){
                                    $agent_rate = $agbudget;
                                    $agent_target = ($agent_rate/100)*$branch_target;
        
                                    $branch_budget = $branch_budget - $agent_target;
        
                                    $upload = [
                                        'agent_id' => $agent->agent_no,
                                        'branch_id' => $agent->branch,
                                        'budget_id' => $budget->budget_id,
                                        'dola' => Carbon::now(),
                                        'amount' => $agent_target,
                                        'agent_rate' => $agent_rate,
                                    ];
                                    array_push($agent_budgets_upload, $upload);
                                }
                            }
        
                            foreach ($agents as $agent) {
                                if(($branch->branch_id == $agent->branch) && ((isset($agent->gross_amount) && $agent->gross_amount != 0))){
                                    $agent_budget = (abs($agent->gross_amount)/$branchagent_total)*$branch_budget;
                                    $agent_rate = ($agent_budget/$branch_target)*100;
                                
                                    $upload = [
                                        'agent_id' => $agent->agent_no,
                                        'branch_id' => $agent->branch,
                                        'budget_id' => $budget->budget_id,
                                        'dola' => Carbon::now(),
                                        'amount' => $agent_budget,
                                        'agent_rate' => $agent_rate,
                                    ];
                                    array_push($agent_budgets_upload, $upload);
                                }
                            }
                        }
                    }
                    DB::table('agent_budget')->insert($agent_budgets_upload);


                    $branchespm = new \GuzzleHttp\Client();
                    $url=''.ENV('API_URL').'budgetbranchpm';
                    $response = $branchespm->get($url);
                    $branchespm = json_decode($response->getBody()->getContents(), true);
                    
                    $branch_month_budget = array();
                    $branch_targetpm = 0;
                    foreach($branchbudget as $branch) {
                        $brcbudget = collect($branchbudget)
                                    ->where('branch_id', $branch->branch_id)
                                    ->first();
                        $branch_budget = $brcbudget->amount;
                        $branch_targetpm = $brcbudget->amount;
                        $months = collect($branchespm)->where('branch',$branch->branch_id)->toArray();

                        if (count($months) == 0) {
                            for ($i=1; $i<=12; $i++) {
                                $month_rate = 100/12;
                                $month_target = ($month_rate/100)*$branch_budget;
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'branch_id' => $branch->branch_id,
                                    'dola' => Carbon::now(),
                                    'month_amount' => $month_target,
                                    'month_rate' => $month_rate,
                                    'month'=> $i,
                
                                    ];
                                array_push($branch_month_budget,$upload);
                            }
                        } else {
                            for ($i=1; $i<=12; $i++) {
                                    if (!in_array($i,array_column($months, 'month'))) {
                                        $month_rate = $brbudget;
                                        $month_target = ($month_rate/100)*$branch_budget;
                                        $upload = [
                                            'budget_id' => $budget->budget_id,
                                            'branch_id' => $branch->branch_id,
                                            'dola' => Carbon::now(),
                                            'month_amount' => $month_target,
                                            'month_rate' => $month_rate,
                                            'month'=> $i,
                        
                                            ];
                                        array_push($branch_month_budget,$upload);
                                        $branch_targetpm = $branch_targetpm - $month_target;
                                    }
                            }
    
                            
                            $branch_month_total = 0;
                            foreach ($months as $month) {
                                $branch_month_total += $month['gross_amount'];
                            }
                            for ($i=1; $i<=12; $i++) {
                                if (in_array($i,array_column($months, 'month'))) {
                                    $month = collect($months)->where('month',$i)->toArray();
                                    foreach ($month as $m) {
                                        $month_target = ($m['gross_amount']/$branch_month_total)*$branch_targetpm;
                                        $month_rate = ($month_target/$branch_budget)*100;
                                        $upload = [
                                            'budget_id' => $budget->budget_id,
                                            'branch_id' => $branch->branch_id,
                                            'dola' => Carbon::now(),
                                            'month_amount' => $month_target,
                                            'month_rate' => $month_rate,
                                            'month'=> $i,
                        
                                            ];
                                        array_push($branch_month_budget,$upload);
                                    }
                                }
                            }
                        }
                    }
                    DB::table('branch_month_budget')->insert($branch_month_budget);

                    $agentspm = new \GuzzleHttp\Client();
                    $url=''.ENV('API_URL').'budgetagentpm';
                    $response = $agentspm->get($url);
                    $agentspm = json_decode($response->getBody()->getContents(), true);

                    $agnt_budget = DB::table('agent_budget')
                            ->where('budget_id', $budget ->budget_id)
                            ->get();
                    $agent_month_budget = array();
                    $branch_targetpm = 0;
                    foreach ($agnt_budget as $agn_bgt) {
                        $agent_budget = $agn_bgt->amount;
                        $agent_targetpm = $agn_bgt->amount;
                        $months = collect($agentspm)
                                ->where('agent_no',$agn_bgt->agent_id)
                                ->where('branch', $agn_bgt->branch_id)
                                ->toArray();
                        
                        $agent_month_total = 0;
                        foreach ($months as $month) {
                            $agent_month_total += $month['gross_amount'];
                        }
    
                        if ($agent_month_total==0) {
                            
                            for ($i=1; $i<=12; $i++) {
                                $month_rate = 100/12;
                                $month_target = ($month_rate/100)*$agent_budget;
                                // dd($agent_budget);
                                $upload = [
                                    'budget_id' => $budget->budget_id,
                                    'branch_id' => $agn_bgt->branch_id,
                                    'agent_id' => $agn_bgt->agent_id,
                                    'dola' => Carbon::now(),
                                    'month_amount' => $month_target,
                                    'month_rate' => $month_rate,
                                    'month'=> $i,
                                    ];
                                    array_push($agent_month_budget, $upload);
                            }
                        } else {
                            for ($i=1; $i<=12; $i++) {
                                if (!in_array($i,array_column($months, 'month')) || in_array(0,array_column($months, 'gross_amount'))) {
                                    // echo $i."yes\n";
                                    $month_rate = $agbudget;
                                    $month_target = ($month_rate/100)*$agent_budget;
                                    // dd($agent_budget);
                                    $upload = [
                                        'budget_id' => $budget->budget_id,
                                        'branch_id' => $agn_bgt->branch_id,
                                        'agent_id' => $agn_bgt->agent_id,
                                        'dola' => Carbon::now(),
                                        'month_amount' => $month_target,
                                        'month_rate' => $month_rate,
                                        'month'=> $i,
                                        ];
                                        array_push($agent_month_budget, $upload);
                                    $agent_targetpm = $agent_targetpm - $month_target;
                                }
                            }
    
                            
                            
                            for ($i=1; $i<=12; $i++) {
                                if (in_array($i,array_column($months, 'month'))) {
                                    $month = collect($months)->where('month',$i)->toArray();
                                    foreach ($month as $m) {
                                        if ($m['gross_amount'] != 0) {
                                            $month_target = ($m['gross_amount']/$agent_month_total)*$agent_targetpm;
                                            $month_rate = ($month_target/$agent_budget)*100;
                                            $upload = [
                                                'budget_id' => $budget->budget_id,
                                                'branch_id' => $agn_bgt->branch_id,
                                                'agent_id' => $agn_bgt->agent_id,
                                                'dola' => Carbon::now(),
                                                'month_amount' => $month_target,
                                                'month_rate' => $month_rate,
                                                'month'=> $i,
                            
                                            ];
                                            array_push($agent_month_budget, $upload);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    DB::table('agent_month_budget')->insert($agent_month_budget);

                    
                    $res = ['status'=> 1];
                }else {
                    $res = [
                        'status' => 0,
                        'message' => "Budget Cannot be refocused more than ".$refocus_number." times",
                    ];
                }
            }
        // }catch(\Throwable $th){
        //     $res = [
        //         'status'=> 0,
        //         'message' => "An error ocurrred, please try again",
        //     ];
        // }
        return $res;
    }

}

?>