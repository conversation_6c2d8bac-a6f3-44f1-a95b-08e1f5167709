<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\ObjectParams;
use App\EdmsObjects;
use View;
use Illuminate\Support\Facades\DB;

//modals
use App\Polmaster;

class ObjectParamsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    public function index($id)
    {
        $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $objectParams = ObjectParams::where('object_id', '=', $id)->get();

       /* $objectD = ObjectParams::where('object_id', '=', $id)->first();
        $table_name = $objectD->t_name;

        $sql = "select COLUMN_NAME from all_tab_columns where owner = 'DEVGBDATA' and table_name = $table_name";

        

        $columns = DB::select("select column_name from all_tab_columns where owner = 'DEVGBDATA' and table_name = '$table_name'");
        */
        $count_objects = ObjectParams::where('object_id', '=', $id)->count();
        
        if ( $count_objects>0) {

             $objectD = ObjectParams::where('object_id', '=', $id)->first();

             $table_name = $objectD->t_name;

              // get all columns for specific table
             $columns = DB::select("select column_name from all_tab_columns where owner = '".$gb."' and table_name = '$table_name'");

        } else {

             $objectD = [];

             $table_name = '';

             //get all tables

            $columns = DB::select("select table_name from all_tab_columns where owner = '".$gb."'");

        }
 






        //return $columns;

        $object = EdmsObjects::where('id', '=', $id)->get();

        foreach ($object as $key => $value) {
            $object_id = $value['id'];
            $object_description =  $value['description'];
        }

        $data = array(
        'level_one'  => '',
        'level_two'  => '',
        'module'  => 'Dashboard',
        'page_title'  => 'edms',
        'submodule' => 'Document Source');
        
         return View::make('folders.object_paramsee')->with('data',$data)->with('objectParams',$objectParams)->with('object_id',$object_id)->with('object_description',$object_description)->with('columns',$columns);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    public function parameters()
    {
        $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $driver = DB::getDriverName();
        if ($driver === 'pgsql') {
            $tables = DB::select("
                SELECT table_schema as tablespace_name, table_name
                FROM information_schema.tables
                WHERE table_schema = ?
                AND table_type = 'BASE TABLE'", [$gb]);
        } else {
            $tables = DB::select("
                SELECT tablespace_name, table_name
                FROM all_tables
                WHERE owner = ?", [strtoupper($gb)]);
        }

        return View::make('folders.tables')->with('tables',$tables);
        
    }


    public function parameter_cols(Request $request)
    {
        $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $table_name = $request->get('tab');
        
        str_replace("\"","",$table_name);

        $columns = DB::select("select COLUMN_NAME from all_tab_columns where owner = '".$gb."' and table_name = '$table_name'");
        echo json_encode($columns);
        //return View::make('folders.columns')->with('columns',$columns);
        
    }



    public function parameter_data(Request $request)
    {
        $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $table_name = $request->get('tab');
        $column_name = strtolower($request->get('col'));

        try{
            $results = DB::select("select $column_name FROM ".$common.".$table_name");
        }
        catch(\Exception $e){
            //echo $e;
            $results = DB::select("select $column_name FROM ".$gb.".$table_name");
        }

        $r = $results->every(100);

        return $r;

        // foreach ($results as $key => $value) {
        //     //$d = '$value->'.$column_name;
        //     echo $value->$column_name."<br/>";
        // }

        //return View::make('folders.tabledata')->with('results',$results)->with('column_name',$column_name);
        
        
    }




    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $source = $request->get('source');

        if ($source == 'user') {
             $objectParams = new ObjectParams([
            'object_id' =>  $request->get('object_id'),
            'description' =>  $request->get('description'),
            'source' =>  'user',
            't_name' =>  'none',
            'c_name' =>  'none',
            'data_type' =>  $request->get('type_of_da'),
            'required_param' =>  $request->get('required_data')
        ]);
        }
        else{
             $objectParams = new ObjectParams([
            'object_id' =>  $request->get('object_id'),
            'description' =>  $request->get('description'),
            'source' =>  'system',
            't_name' =>  $request->get('tableget'),
            'c_name' =>  $request->get('columnget'),
            'data_type' =>  'select',
            'required_param' =>  $request->get('required_data')
        ]);
        }
       

        $objectParams->save();
        return redirect()->back();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
         $objectParams = ObjectParams::where('id', $request->get('param_id'))
         ->update(['required_param' => $request->get('required_param'),
                    'description' => $request->get('description'),
                    'data_type' => $request->get('data_type'),
                    'c_name' => $request->get('columnget')]);

         return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
