<?php

namespace App\Http\Controllers\parameters\gl;

use App\Acctype;
use App\Agmnf;
use App\Bankbranches;
use App\Banks;
use App\Branch;
use App\Cbdeduct;
use App\Cbseqno;
use App\Cbtrans;
use App\Client;
use App\Currency;
use App\Currrate;
use App\Doctype;
use App\FacNotification;
use App\FOparams;
use App\Http\Controllers\Controller;
use App\Nlctrl;
use App\Nldept;
use App\Nlintbra;
use App\Nlparams;
use App\Nlslmst;
use App\Nlslparams;
use App\Olpaymethd;
use App\Pipcnam;
use App\Reqdocs;
use App\Reqtaxes;
use App\SourceCodes;
use App\User;
use App\Userlimits;
use Auth;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Response;
use Session;
use View;
use Yajra\Datatables\Datatables;
use App\Services\IntermediaryQueryService;
use App\ValueObjects\IntermediaryQueryParams;

use App\Recserials;

class FOParamsController extends Controller
{

    public function view()
    {

        $pip = Pipcnam::all();

        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
        );

        return view('parameters.gl.fo_mainparampage', [
            'pip' => $pip, 'dat' => $dat,

        ]);
    }
    // requisition taxes
    public function viewReqTaxes(Request $request)
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Requisition Taxes',
        );
        return view::make('parameters.gl.fo_reqtaxes')
            ->with('dat', $dat);
    }

    public function getReqTaxes(Request $request)
    {
        $taxes = Reqtaxes::all();
        return Datatables::Of($taxes)
            ->addColumn('add_deduct', function ($tax) {
                if ($tax->add_deduct == 'D') {
                    return "Deduction";
                } else {
                    return "Addition";
                }
            })->addColumn('action', function ($tax) {
            return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
        })
            ->addIndexColumn()
            ->make(true);
    }

    public function updateReqTaxes(Request $request)
    {
        try {
            DB::beginTransaction();
            $count = Reqtaxes::where("id", (int) $request->id)->count();
            //dd($count);

            if ($count < 0) {
                return [
                    'code' => -1,
                    'message' => 'The field was not found, make sure ID is passed correctly',
                ];
                Session::Flash('error', 'The field was not found, make sure ID is passed correctly');
            } else {
                $req_tax = Reqtaxes::where("id", (int) $request->id)->first();
                $update = $req_tax->update([
                    'code' => $request->edit_code,
                    'rate' => $request->edit_tax_rate,
                    'descriptions' => $request->edit_name,
                    'add_deduct' => $request->edit_add_deduct,
                ]);
                if (!$update) {
                    return [
                        'code' => -1,
                        'message' => 'error while updating',
                    ];
                }

                Session::Flash('success', 'Requisition tax updated successfully');
            }
            DB::commit();
            return [
                'code' => 1,
                'message' => 'success',
            ];
        } catch (\Exeption $e) {
            DB::rollback();
            report($e);
            return [
                'code' => -1,
                'message' => $e->getMessage(),
            ];

        }

    }

    public function postNewReqTax(Request $request)
    {
        try {
            DB::beginTransaction();
            $count = Reqtaxes::where(DB::raw('TRIM(code)'), trim($request->tax_code))->count();

            // if($count > 0){
            //   Session::Flash('error','Cannot insert duplicate tax code');
            //   DB::rollback();
            //   return[
            //     'code' => -1,
            //     'message' => 'Cannot insert duplicate tax code'
            //   ];
            // }else{
            $req_doc = Reqtaxes::create([
                'code' => $request->tax_code,
                'rate' => $request->tax_rate,
                'descriptions' => $request->tax_name,
                'add_deduct' => $request->add_deduct,
            ]);

            DB::commit();
            return [
                'code' => 1,
                'message' => 'Document added successfully',
            ];
            Session::Flash('success', 'Document added successfully');
            // }
            //$count > 0 if.end

        } catch (\Exeption $e) {
            DB::rollback();
            report($e);
            return [
                'code' => -1,
                'message' => $e->getMessage(),
            ];

        }

    }
    // requisition taxes.end

    public function index()
    {
        $dat = array(
            'main' => 'Parameters',
            'module' => 'Front Office',
        );
        return view::make('parameters.gl.fo_params')
            ->with('dat', $dat);
    }

    public function currencySetup()
    {
        return view('parameters.gl.fo_currency');
    }

    public function getCurrency()
    {
        $currency = Currency::get();

        return Datatables::of($currency)->addColumn('action', function ($dept) {
            return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
        })->make(true);
    }

    public function getCurrencyCode()
    {
        $currency = Currency::all();
        $maxCode = array();

        foreach ($currency as $key) {
            # Add Occupation codes into array
            $push = array_push($maxCode, $key->currency_code);
        }

        # Find the largest number
        $code = max($maxCode);
        $code = $code + 1;
        $currency_code = sprintf("%01d", $code);

        return $currency_code;
    }

    public function baseCurrencyCheck()
    {
        $baseCurrency = Currency::where('base_currency', 'Y')->count();

        return $baseCurrency;
    }

    public function baseCurrencyDelete()
    {
        $deleteBase = Currency::where('base_currency', 'Y')->update([
            'base_currency' => 'N',
        ]);

        return $deleteBase;
    }

    // public function deleteCurrency() {
    //   $delete = Currency::where('base_currency', 'N')->delete();

    //   return $delete;
    // }

    public function postCurrency(Request $request)
    {
        $postCurrency = Currency::create([
            'currency_code' => $request->currency_code,
            'currency' => $request->currency,
            'description' => $request->description,
            'rate_allowance' => $request->rate_allowance,
            'base_currency' => $request->base_currency,
        ]);

        return $postCurrency;
    }

    public function updateCurrency(Request $request)
    {
        $updateCurrency = Currency::where('currency_code', $request->currency_code)->update([
            'currency' => $request->currency,
            'description' => $request->description,
            'rate_allowance' => $request->rate_allowance,
            'base_currency' => $request->base_currency,
        ]);

        return $request->currency_code;
    }

//department codes functions
    public function departments()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
        );
        return view::make('parameters.gl.fo_departments')
            ->with('dat', $dat);
    }

    public function getDepts(Request $request)
    {
        $departments = Nlparams::where('prid', 'DEP')
            ->orderBy('prsno')
            ->get();
        return Datatables::of($departments)
            ->addColumn('action', function ($dept) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->make(true);
    }

    public function validateParam(Request $request)
    {
        $param_id = $request->get('param_id');
        $param_code = $request->get('param_code');
        $count = Nlparams::where('prid', $param_id)
            ->whereRaw("trim(prsno)='" . $param_code . "'")
            ->count();
        //dd($count);
        if ($count > 0) {
            $result = array('status' => 1);
        } else {
            $result = array('status' => 0);
        }

        return $result;
    }

    public function postNewDept(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $create_dept = Nlparams::create([
                'prid' => $request->dept_id,
                'prsno' => $request->dept_code,
                'prdesc' => $request->dept_name,
            ]);

            //dd($create_dept);

            Session::Flash('success', 'Department added successfully');
        });
    }

    public function editDept(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $dept_code = $request->old_code;

            $update_dept = Nlparams::where('prid', $request->dept_id)
                ->whereRaw("trim(prsno)='" . $dept_code . "'")
                ->update([
                    'prid' => $request->dept_id,
                    'prsno' => $request->edit_code,
                    'prdesc' => $request->edit_name,
                ]);

            Session::Flash('success', 'Department updated successfully');
        });
    }
    //end of departments

    //office codes/branches
    public function offices()
    {
        $dat = array(
            'main' => 'Parameters',
            'module' => 'Front Office',
            'submodule' => 'Offices',
        );
        return view::make('parameters.gl.fo_offices')
            ->with('dat', $dat);
    }

    public function getOffices()
    {
        $offices = Nlparams::where('prid', 'OFF')
            ->orderBy('prsno')
            ->get();
        return Datatables::of($offices)
            ->addColumn('action', function ($off) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->make(true);
    }

    public function controlAccounts(Request $request)
    {
        $control_accounts = Nlparams::where('prid', "GLH")
            ->get();
        return $control_accounts;
    }

    public function postNewOffice(Request $request)
    {
        DB::Transaction(function () use ($request) {
            if ($request->affect_oldest == 'Y') {
                $account_period = str_pad($request->account_period, 6, '0', STR_PAD_LEFT);
            } else {
                $account_period = $request->account_period;
            }

            $create_office = Nlparams::create([
                'prid' => $request->office_id,
                'prsno' => $request->office_code,
                'prdesc' => $request->office_name,
                'automatic' => $request->automatic,
                'control_account' => $request->control_account,
                'gl_link' => $request->gl_link,
                'affect_oldest' => $request->affect_oldest,
                'period_year' => substr($account_period, 0, 4),
                'period_month' => substr($account_period, 4, 7),
            ]);
            //dd($create_office);
            Session::Flash('success', 'Office added successfully');
        });
    }

    public function officeDetails(Request $request)
    {
        $prid = $request->get('prid');
        $prsno = $request->get('prsno');
        $office = Nlparams::where('prid', $prid)
            ->whereRaw("trim(prsno)='" . $prsno . "'")
            ->get();
        return $office;
    }

    public function editOffice(Request $request)
    {
        DB::Transaction(function () use ($request) {
            if ($request->affect_oldest == 'Y') {
                $account_period = str_pad($request->account_period, 6, '0', STR_PAD_LEFT);
            } else {
                $account_period = $request->account_period;
            }
            //dd($request->old_code);

            $update_office = Nlparams::where('prid', $request->office_id)
                ->whereRaw("trim(prsno)='" . $request->old_code . "'")
                ->update([
                    'prid' => $request->office_id,
                    'prsno' => $request->office_code,
                    'prdesc' => $request->office_name,
                    'automatic' => $request->automatic,
                    'control_account' => $request->control_account,
                    'gl_link' => $request->gl_link,
                    'affect_oldest' => $request->affect_oldest,
                    'period_year' => substr($account_period, 0, 4),
                    'period_month' => substr($account_period, 4, 7),
                ]);
            //dd($office);
            //return $update_office;
            Session::Flash('success', 'Office Updated successfully');
        });
    }
    /* end of office code maintenance */

    /*restrict banks to cost center functions */
    public function banks(Request $request)
    {

        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Restrict Banks to Cost Centers',
        );
        return view::make('parameters.gl.fo_restrict')
            ->with('dat', $dat);
    }

    public function getBanks(Request $request)
    {
        $banks = Nlparams::where('prid', 'BNK')
            ->orderBy('prsno')
            ->get();
        return Datatables::of($banks)
            ->addColumn('action', function ($bnk) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('prsno', function ($prsno) {
                return formatGlhead($prsno->prsno);
            })
            ->editColumn('category', function ($cat) {
                $offcd = Nlparams::where('prid', 'OFF')
                    ->whereRaw("trim(prsno)='" . $cat->category . "'")
                    ->first();
                if ($offcd) {
                    return $offcd->prdesc;
                }
            })
            ->make(true);
    }

    public function getCostCentres(Request $request)
    {
        $cost_centres = Nlparams::where('prid', 'OFF')
            ->get();
        return $cost_centres;
    }

    public function postRestrict(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $prsno = removeGlheadFormat($request->bank_code);
            $restrict = Nlparams::where('prid', 'BNK')
                ->where('prsno', $prsno)
                ->update([
                    'category' => $request->category,
                ]);

            Session::Flash('success', 'Account restricted to cost centre successfully');
        });
    }
    /*end of restrict banks to cost center */

    /*doc type functions*/
    public function docTypes()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Doc Types Parameters',
        );
        return view::make('parameters.gl.fo_doctypes')
            ->with('dat', $dat);
    }

    public function getDocTypes(Request $request)
    {
        $doctypes = Doctype::all();

        return Datatables::of($doctypes)
            ->addColumn('action', function ($doc) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->make(true);
    }

    public function validateDocType(Request $request)
    {
        $doc_type = $request->get('doc_type');
        $count = Doctype::where('doc_type', $doc_type)
            ->count();
        if ($count > 0) {
            $result = array('status' => 1);
        } else {
            $result = array('status' => 0);
        }

        return $result;
    }

    public function postDocType(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $create_doc = Doctype::create([
                'doc_type' => $request->doc_type,
                'description' => $request->description,
                'auto' => $request->auto,
                'serial_no' => $request->serial_no,
                'credit_serial_no' => $request->credit_serial_no,
            ]);

            Session::Flash('success', 'Document type added successfully');
            //dd($create_doc);
        });
    }

    public function editDocType(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $doc_type = $request->old_doc;
            $data = [
                'doc_type' => $request->doc_type,
                'description' => $request->description,
                'auto' => $request->auto,
                'serial_no' => $request->serial_no,
                'credit_serial_no' => $request->credit_serial_no,
            ];

            $doctype = Doctype::where('doc_type', $doc_type)
                ->update($data);

            Session::Flash('success', 'Document type updated successfully');
            //dd($create_doc);
        });
    }
    /*end of doc type functions*/

    /*****receipt serials*****/
    public function recSerials()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Receipt Numbers Parameters',
        );
        return view::make('parameters.gl.fo_recserials')
            ->with('dat', $dat);
    }

    public function getRecSerials(Request $request)
    {
        $serials = Recserials::all();

        return Datatables::of($serials)
            ->addColumn('action', function ($doc) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->addColumn('description', function($off){
                $nlparams = Nlparams::where('prid','OFF')
                                    ->whereRaw("trim(prsno) = '".$off->offcd."' ")
                                    ->get()[0];
                                    
                return $nlparams->prdesc;
            })
            ->make(true);
    }

    public function getOffcd(Request $request){
        $nlparams = Nlparams::where('prid', 'OFF')->get();
        return $nlparams;
    }

    public function validateRecSerial(Request $request)
    {
        $offcd = $request->get('offcd');
        $count = Recserials::where('offcd', $offcd)
                            ->where('doc_type','REC')
                            ->count();
        if ($count > 0) {
            $result = array('status' => 1);
        } else {
            $result = array('status' => 0);
        }

        return $result;
    }

    public function postNewSerial(Request $request)
    {
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try {

            $create_serail = Recserials::create([
                'doc_type' => $request->doc_type,
                'offcd' => $request->offcd,
                'serial_no' => $request->serial_no
            ]);

            DB::connection(env('DB_CONNECTION1'))->commit();

            Session::Flash('success', 'Serial added successfully');
        }

        catch (\Throwable $e) {

            DB::connection(env('DB_CONNECTION1'))->rollback();

            $result = array("status" => 0);
            Session::Flash('error', 'An error occured while adding parameter');
            // dd($e);
            return $e;
        }
    }

    public function updateRecSerial(Request $request)
    {
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try {

            $update_serial = Recserials::where('doc_type', $request->doc_type)
                                        ->where('offcd', $request->old_offcd)
                                        ->update([
                                        'offcd' => $request->offcd,
                                        'serial_no' => $request->serial_no
                                    ]);

            DB::connection(env('DB_CONNECTION1'))->commit();

            Session::Flash('success', 'Serial updated successfully');
        }

        catch (\Throwable $e) {

            DB::connection(env('DB_CONNECTION1'))->rollback();

            $result = array("status" => 0);
            Session::Flash('error', 'An error occured while updating parameter');
            // dd($e);
            return $e;
        }

    }
    /*****end of receipt serials*****/

    /*receipt trans types*/
    public function recTransTypes()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Receipt Transaction Types',
        );

        return view::make('parameters.gl.fo_rectranstypes')
            ->with('dat', $dat);
    }

    public function getTransTypes(Request $request)
    {

        $doc_type = $request->get('doc_type');
        $transactions = Cbtrans::where('doc_type', $doc_type)
            ->get();
        return Datatables::of($transactions)
            ->addColumn('action', function ($tran) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
        /*->editColumn('debit_account',function($dr){
        if(trim($dr->debit_account) != ""){
        return formatGlhead($dr->debit_account);
        }
        })*/
            ->editColumn('credit_account', function ($cr) {
                if (trim($cr->credit_account) != "") {
                    return formatGlhead($cr->credit_account);
                }
            })
            ->editColumn('analyse_by', function ($ana) {
                if ($ana->analyse_by == 'A') {
                    return 'ACCOUNT TYPE';
                } else if ($ana->analyse_by == 'I') {
                    return 'INTERMEDIARY';
                } else if ($ana->analyse_by == 'B') {
                    return 'BRANCH';
                } else if ($ana->analyse_by == 'C') {
                    return 'CLASS';
                }else if ($ana->analyse_by == 'T') {
                    return 'TREATY';
                }
            })
            ->make(true);
    }

    public function getSource(Request $request)
    {
        $source = SourceCodes::all();
        return $source;
    }

    public function getSltypes(Request $request)
    {
        $sltypes = Nlparams::where('prid', 'SLT')
            ->get();
        return $sltypes;
    }

    public function getDrAccounts(Request $request)
    {
        $nlctrl = Nlctrl::first();

        $dr_accounts = Nlslmst::where('sltype', '03')
            ->where('offcd', $nlctrl->offcd)
            ->get(['slhead', 'name']);
        //->get();
        return $dr_accounts;
    }

    public function getCrAccounts(Request $request)
    {
        return Nlparams::where('prid', 'GLH')->whereRaw("(bank_flag = 'N' or bank_flag is null)") ->get(['prsno', 'prdesc']);
    }

    public function validateTransType(Request $request)
    {
        $doc_type = $request->get('doc_type');
        $trans_type = $request->get('trans_type');
        $source = $request->get('source');
        $count = Cbtrans::where(DB::raw('TRIM(source_code)'), $source)
            //where('source_code',$source)
            ->where(DB::raw('TRIM(doc_type)'), $doc_type)
            ->where(DB::raw('TRIM(descr)'), $trans_type)
            ->count();
        //dd($count);
        if ($count > 0) {
            $result = array('status' => 1);
        } else {
            $result = array('status' => 0);
        }

        return $result;
    }

    public function postTransType(Request $request)
    {

        DB::Transaction(function () use ($request) {
            $data = [
                'prsno' => '03',
                'doc_type' => $request->trans_doc,
                'source_code' => $request->trans_source,
                'descr' => $request->trans_type,
                'description' => $request->trans_descr,
                'subledger' => $request->trans_subledger,
                'subledger_code' => $request->trans_sltype,
                'debit_account' => $request->trans_dr_account,
                'credit_account' => $request->trans_cr_account,
                'excess_flag' => $request->trans_excess,
                'active_flag' => $request->active_flag,
                'analyse' => $request->analyse_trans,
                'analyse_by' => $request->analyse_by,
                'use_debit_account' =>  $request->use_debit_account,
            ];

            $cbtrans = Cbtrans::insert($data);

            Session::Flash('success', 'Transaction type added successfully');
        });
    }

    public function getTransDetails(Request $request)
    {
        $source = $request->get('source');
        $doc_type = $request->get('doc_type');
        $trans_type = $request->get('trans_type');
        $trans_details = Cbtrans::where(DB::raw('TRIM(source_code)'), $source)
            ->where(DB::raw('TRIM(doc_type)'), $doc_type)
            ->where(DB::raw('TRIM(descr)'), $trans_type)
            ->get();
        return $trans_details;
    }

    public function updateTransType(Request $request)
    {
        $source = $request->old_source;
        $doc_type = $request->trans_doc;
        $trans_type = $request->old_trans;
        $oldrecord = Cbtrans::where(DB::raw('TRIM(source_code)'), $source)
            ->where(DB::raw('TRIM(doc_type)'), $doc_type)
            ->where(DB::raw('TRIM(descr)'), $trans_type)
            ->first();
        if($request->trans_cr_account != $oldrecord->credit_account){
            $data1 = array();
            $audit1 = array(
            'sourcecode'=>$oldrecord->source_code,
            'doc_type'=>$oldrecord->doc_type,
            'descr'=>$oldrecord->descr,
            'field_changed'=>'credit account',
            'old_value'=>$oldrecord->credit_account,
            'new_value'=>$request->trans_cr_account,
            'date_changed'=>Carbon::now('EAT'),
            'ip_address'=>$request->ip(),
            'sys_user'=>Auth::user()->user_name
            );
            array_push($data1, $audit1);
            DB::table('cbtrans_audit_trail')->insert($data1);
        }
        if($request->trans_source != $oldrecord->source_code){
            $data1 = array();
            $audit1 = array(
            'sourcecode'=>$oldrecord->source_code,
            'doc_type'=>$oldrecord->doc_type,
            'descr'=>$oldrecord->descr,
            'field_changed'=>'source code',
            'old_value'=>$oldrecord->source_code,
            'new_value'=>$request->trans_source,
            'date_changed'=>Carbon::now('EAT'),
            'ip_address'=>$request->ip(),
            'sys_user'=>Auth::user()->user_name
            );
            array_push($data1, $audit1);
            DB::table('cbtrans_audit_trail')->insert($data1);
        }
        if($request->analyse_trans != $oldrecord->analyse){
            $data1 = array();
            $audit1 = array(
            'sourcecode'=>$oldrecord->source_code,
            'doc_type'=>$oldrecord->doc_type,
            'descr'=>$oldrecord->descr,
            'field_changed'=>'analyse',
            'old_value'=>$oldrecord->analyse,
            'new_value'=>$request->analyse_trans,
            'date_changed'=>Carbon::now('EAT'),
            'ip_address'=>$request->ip(),
            'sys_user'=>Auth::user()->user_name
            );
            array_push($data1, $audit1);
            DB::table('cbtrans_audit_trail')->insert($data1);
        }
        if($request->analyse_by != $oldrecord->analyse_by){
            $data1 = array();
            $audit1 = array(
            'sourcecode'=>$oldrecord->source_code,
            'doc_type'=>$oldrecord->doc_type,
            'descr'=>$oldrecord->descr,
            'field_changed'=>'analyse by',
            'old_value'=>$oldrecord->analyse_by,
            'new_value'=>$request->analyse_by,
            'date_changed'=>Carbon::now('EAT'),
            'ip_address'=>$request->ip(),
            'sys_user'=>Auth::user()->user_name
            );
            array_push($data1, $audit1);
            DB::table('cbtrans_audit_trail')->insert($data1);
        }
        if($request->active_flag != $oldrecord->active_flag){
            $data1 = array();
            $audit1 = array(
            'sourcecode'=>$oldrecord->source_code,
            'doc_type'=>$oldrecord->doc_type,
            'descr'=>$oldrecord->descr,
            'field_changed'=>'Active flag',
            'old_value'=>$oldrecord->active_flag,
            'new_value'=>$request->active_flag,
            'date_changed'=>Carbon::now('EAT'),
            'ip_address'=>$request->ip(),
            'sys_user'=>Auth::user()->user_name
            );
            array_push($data1, $audit1);
            DB::table('cbtrans_audit_trail')->insert($data1);
        }

        if($request->use_debit_account != $oldrecord->use_debit_account){
            $data1 = array();
            $audit1 = array(
                'sourcecode'=>$oldrecord->source_code,
                'doc_type'=>$oldrecord->doc_type,
                'descr'=>$oldrecord->descr,
                'field_changed'=>'use debit account',
                'old_value'=>$oldrecord->use_debit_account,
                'new_value'=>$request->use_debit_account,
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'sys_user'=>Auth::user()->user_name
            );
            
            array_push($data1, $audit1);
            DB::table('cbtrans_audit_trail')->insert($data1);
        }

        if($request->trans_dr_account != $oldrecord->debit_account){
            $data1 = array();
            $audit1 = array(
                'sourcecode'=>$oldrecord->source_code,
                'doc_type'=>$oldrecord->doc_type,
                'descr'=>$oldrecord->descr,
                'field_changed'=>'debit account',
                'old_value'=>$oldrecord->debit_account,
                'new_value'=>$request->trans_dr_account,
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'sys_user'=>Auth::user()->user_name
            );
            array_push($data1, $audit1);
            DB::table('cbtrans_audit_trail')->insert($data1);
        }

        $data = [
            'doc_type' => $request->trans_doc,
            'source_code' => $request->trans_source,
            'descr' => $request->trans_type,
            'description' => $request->trans_descr,
            'subledger' => $request->trans_subledger,
            'subledger_code' => $request->trans_sltype,
            'debit_account' => $request->trans_dr_account,
            'credit_account' => $request->trans_cr_account,
            'excess_flag' => $request->trans_excess,
            'active_flag' => $request->active_flag,
            'analyse' => $request->analyse_trans,
            'analyse_by' => $request->analyse_by,
            'auto_break_down' => $request->break_down,
            'use_debit_account' => $request->use_debit_account
        ];

        $cbtrans = Cbtrans::where(DB::raw('TRIM(source_code)'), $source)
            ->where(DB::raw('TRIM(doc_type)'), $doc_type)
            ->where(DB::raw('TRIM(descr)'), $trans_type)->update($data);

        Session::Flash('success', 'Transaction type updated successfully');

        return $cbtrans;
        // DB::Transaction(function() use ($request){
        //   $source = $request->old_source;
        //   $doc_type = $request->trans_doc;
        //   $trans_type = $request->old_trans;

        //   $data = [
        //     'doc_type'=>$request->trans_doc,
        //     'source_code'=>$request->trans_source,
        //     'descr'=>$request->trans_type,
        //     'description'=>$request->trans_descr,
        //     'subledger'=>$request->trans_subledger,
        //     'subledger_code'=>$request->trans_sltype,
        //     'debit_account'=>$request->trans_dr_account,
        //     'credit_account'=>$request->trans_cr_account,
        //     'excess_flag'=>$request->trans_excess,
        //     'active_flag'=>$request->active_flag
        //   ];
        //   return $data;

        //   $cbtrans = Cbtrans::whereRaw("trim(source_code)='".$source."'")
        //                     ->whereRaw("trim(doc_type)='".$doc_type."'")
        //                     ->whereRaw("trim(descr)='".$trans_type."'")->get();

        //                     //->update($data);

        //   Session::Flash('success','Transaction type updated successfully');
        // });
    }
    /*end of rec trans types*/

    /*pay trans types */
    public function payTransTypes()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Payment Transaction Types',
        );

        return view::make('parameters.gl.fo_paytranstypes')
            ->with('dat', $dat);
    }

    public function postPayTransType(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $data = [
                'prsno' => '03',
                'doc_type' => $request->trans_doc,
                'source_code' => $request->trans_source,
                'descr' => $request->trans_type,
                'description' => $request->trans_descr,
                'subledger' => $request->trans_subledger,
                'subledger_code' => $request->trans_sltype,
                'debit_account' => $request->trans_dr_account,
                'credit_account' => $request->trans_cr_account,
                'excess_flag' => $request->trans_excess,
                'analyse' => $request->analyse_trans,
                'analyse_by' => $request->analyse_by,
                'commission_flag' => $request->comm_flag,
                'withholding_tax_flag' => $request->withholding_flag,
                'clm_service_provider_flag' => $request->trans_claim,
                'req_fo' => $request->req_fo,
                'pay_fo' => $request->pay_fo,
            ];

            $cbtrans = Cbtrans::insert($data);

            Session::Flash('success', 'Transaction type added successfully');
        });
    }

    public function updatePayTransType(Request $request)
    {
        DB::Transaction(function () use ($request) {
       
            $source = $request->old_source;
            $doc_type = $request->trans_doc;
            $trans_type = $request->old_trans;

           $oldrecord = Cbtrans::where(DB::raw('TRIM(source_code)'), $source)
                            ->where(DB::raw('TRIM(doc_type)'), $doc_type)
                            ->where(DB::raw('TRIM(descr)'), $trans_type)
                            ->first();
            if($request->trans_cr_account != $oldrecord->credit_account){
                $data1 = array();
                $audit1 = array(
                    'sourcecode'=>$oldrecord->source_code,
                    'doc_type'=>$oldrecord->doc_type,
                    'descr'=>$oldrecord->descr,
                    'field_changed'=>'credit account',
                    'old_value'=>$oldrecord->credit_account,
                    'new_value'=>$request->trans_cr_account,
                    'date_changed'=>Carbon::now('EAT'),
                    'ip_address'=>$request->ip(),
                    'sys_user'=>Auth::user()->user_name
                );
                array_push($data1, $audit1);
                DB::table('cbtrans_audit_trail')->insert($data1);
            }
            if($request->trans_source != $oldrecord->source_code){
                $data1 = array();
                $audit1 = array(
                    'sourcecode'=>$oldrecord->source_code,
                    'doc_type'=>$oldrecord->doc_type,
                    'descr'=>$oldrecord->descr,
                    'field_changed'=>'source code',
                    'old_value'=>$oldrecord->source_code,
                    'new_value'=>$request->trans_source,
                    'date_changed'=>Carbon::now('EAT'),
                    'ip_address'=>$request->ip(),
                    'sys_user'=>Auth::user()->user_name
                );
                array_push($data1, $audit1);
                DB::table('cbtrans_audit_trail')->insert($data1);
            }
            if($request->analyse_trans != $oldrecord->analyse){
                $data1 = array();
                $audit1 = array(
                    'sourcecode'=>$oldrecord->source_code,
                    'doc_type'=>$oldrecord->doc_type,
                    'descr'=>$oldrecord->descr,
                    'field_changed'=>'analyse',
                    'old_value'=>$oldrecord->analyse,
                    'new_value'=>$request->analyse_trans,
                    'date_changed'=>Carbon::now('EAT'),
                    'ip_address'=>$request->ip(),
                    'sys_user'=>Auth::user()->user_name
                );
                array_push($data1, $audit1);
                DB::table('cbtrans_audit_trail')->insert($data1);
            }
            if($request->analyse_by != $oldrecord->analyse_by){
                $data1 = array();
                $audit1 = array(
                    'sourcecode'=>$oldrecord->source_code,
                    'doc_type'=>$oldrecord->doc_type,
                    'descr'=>$oldrecord->descr,
                    'field_changed'=>'analyse by',
                    'old_value'=>$oldrecord->analyse_by,
                    'new_value'=>$request->analyse_by,
                    'date_changed'=>Carbon::now('EAT'),
                    'ip_address'=>$request->ip(),
                    'sys_user'=>Auth::user()->user_name
                );
                array_push($data1, $audit1);
                DB::table('cbtrans_audit_trail')->insert($data1);
            }


            $data = [
                'doc_type' => $request->trans_doc,
                'source_code' => $request->trans_source,
                'descr' => $request->trans_type,
                'description' => $request->trans_descr,
                'subledger' => $request->trans_subledger,
                'subledger_code' => $request->trans_sltype,
                'debit_account' => $request->trans_dr_account,
                'credit_account' => $request->trans_cr_account,
                'excess_flag' => $request->trans_excess,
                'analyse' => $request->analyse_trans,
                'analyse_by' => $request->analyse_by,
                'commission_flag' => $request->comm_flag,
                'withholding_tax_flag' => $request->withholding_flag,
                'clm_service_provider_flag' => $request->trans_claim,
                'req_fo' => $request->edit_req_fo,
                'pay_fo' => $request->edit_pay_fo,
            ];

            $cbtrans = Cbtrans::where(DB::raw('TRIM(source_code)'), $source)
                ->where(DB::raw('TRIM(doc_type)'), $doc_type)
                ->where(DB::raw('TRIM(descr)'), $trans_type)
                ->update($data);

            Session::Flash('success', 'Transaction type updated successfully');
        });
    }
    /*end of pay trans types */

    /*source codes*/
    public function SourceCodes()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Source Codes',
        );
        $department_codes = Nlparams::WhereRaw("prid='DEP' ")->get(['prsno', 'prdesc']);
        return view::make('parameters.gl.fo_source')
        ->with(compact('dat','department_codes'));
    }

    public function getSources(Request $request)
    {
        $sources = DB::select("SELECT a.*,n.PRDESC as default_dept_description  FROM SOURCE a 
                                LEFT JOIN NLPARAMS n ON trim(a.DEFAULT_DEPT)  = trim(n.PRSNO)");
        return Datatables::of($sources)
            ->addColumn('action', function ($src) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->make(true);
    }

    public function validateSource(Request $request)
    {
        $source_code = $request->get('source_code');
        $count = SourceCodes::where(DB::raw('TRIM(source_code)'), $source_code)->count();
        if ($count > 0) {
            $result = array('status' => 1);
        } else {
            $result = array('status' => 0);
        }

        return $result;
    }

    public function postNewSource(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $source = SourceCodes::create([
                'source_code' => $request->source_code,
                'name' => $request->source_descr,
                'default_dept' => $request->s_code,
            ]);

            Session::Flash('success', 'Source code added successfully');
        });
    }

    public function updateSource(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $old_source = $request->get('old_source');
            $source_upd = SourceCodes::where(DB::raw('TRIM(source_code)'), $old_source)
                ->update([
                    'source_code' => $request->edit_scode,
                    'name' => $request->edit_sdescr,
                    'default_dept' => $request->dept_code,
                ]);

            Session::Flash('success', 'Source code updated successfully');
        });
    }
    /*end of source codes*/

    /*pay methods*/
    public function payMethods()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Pay Methods',
        );

        return view::make('parameters.gl.fo_paymethods')
            ->with('dat', $dat);
    }

    public function getPayMethods(Request $request)
    {
        $pay_methods = Olpaymethd::all();
        return Datatables::of($pay_methods)
            ->addColumn('action', function ($pmthd) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('glhead', function ($glh) {
                if (trim($glh->glhead) != "") {
                    return formatGlhead($glh->glhead);
                }
            })
            ->make(true);
    }

    public function validatePmethod(Request $request)
    {
        $pay_method = $request->get('pay_method');
        $count = Olpaymethd::where(DB::raw('TRIM(pay_method)'), $pay_method)
            ->count();
        if ($count > 0) {
            $result = array('status' => 1);
        } else {
            $result = array('status' => 0);
        }

        return $result;
    }

    public function getGlhead(Request $request)
    {
        $accounts = Nlparams::where('prid', 'BNK')
            ->get();
        return $accounts;
    }

    public function newPmethod(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $pay_method = Olpaymethd::create([
                'pay_method' => $request->pay_method,
                'description' => $request->pay_descr,
                'account_no' => $request->account_no,
                'glhead' => $request->glhead,
                'sub_account' => $request->sub_account,
                'refrence_flag' => $request->refrence_flag,
                'backdate_flag' => $request->backdate_flag,
                'postdate_flag' => $request->postdate_flag,
                'eft' => $request->eft,
                'rtgs' => $request->rtgs,
                'cheque' => $request->cheque,
                'default_method' => $request->default_pay_method,
            ]);

            Session::Flash('success', 'Pay method added successfully');
        });
    }

    public function editPmethod(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $old_method = $request->old_pmethod;

            $pmethod_upd = Olpaymethd::where(DB::raw('TRIM(pay_method)'), $old_method) // where('pay_method',$old_method)
                ->update([
                    'pay_method' => $request->edit_pmethod,
                    'description' => $request->edit_pdescr,
                    'account_no' => $request->edit_acc,
                    'glhead' => $request->edit_glhead,
                    'sub_account' => $request->edit_sub,
                    'refrence_flag' => $request->refrence_flag,
                    'backdate_flag' => $request->backdate_flag,
                    'postdate_flag' => $request->postdate_flag,
                    'cheque' => $request->cheque,
                    'eft' => $request->eft,
                    'rtgs' => $request->rtgs,
                    'default_method' => $request->default_pay_method,

                ]);

            Session::Flash('success', 'Pay method updated successfully');
        });
    }
    /*end of pay methods*/

    /*cost/revenue centres*/
    public function costCentres()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Cost/Revenue Centres',
        );
        return view::make('parameters.gl.fo_costcentres')
            ->with('dat', $dat);
    }

    public function getCRcentres(Request $request)
    {
        $cr_centres = Nldept::all();
        return Datatables::of($cr_centres)
            ->addColumn('action', function ($crev) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->make(true);
    }

    public function validateCentre(Request $request)
    {
        $centre_code = $request->get('centre_code');
        $count = Nldept::where('gl_dept', $centre_code)
            ->count();
        if ($count > 0) {
            $result = array('status' => 1);
        } else {
            $result = array('status' => 0);
        }

        return $result;
    }

    public function validateCentreName(Request $request)
    {
        $centre_name = $request->get('centre_name');
        $count = Nldept::where(DB::raw('TRIM(description)'), $centre_name)
            ->count();
        if ($count > 0) {
            $result = array('status' => 1);
        } else {
            $result = array('status' => 0);
        }

        return $result;
    }

    public function postCostCentre(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $count = Nldept::where('gl_dept', $request->centre_code)
                ->count();
            $count1 = Nldept::where(DB::raw('TRIM(description)'), $request->centre_name)
                ->count();

            if ($count > 0 || $count1 > 0) {
                Session::Flash('error', 'Cannot insert duplicates');
            } else {
                $cost_centre = Nldept::create([
                    'gl_dept' => $request->centre_code,
                    'description' => $request->centre_name,
                    'gl_class' => $request->centre_code * 10,
                ]);

                Session::Flash('success', 'Cost/Revenue centre added successfully');
            }
        });
    }

    public function updateCostCentre(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $count = Nldept::where('gl_dept', $request->edit_ccode)
                ->where('gl_dept', '!=', $request->old_ccode)
                ->count();
            $count1 = Nldept::where(DB::raw('TRIM(description)'), $request->edit_cname)
                ->where('gl_dept', '!=', $request->old_ccode)
                ->count();

            if ($count > 0 || $count1 > 0) {
                Session::Flash('error', 'Cannot insert duplicates');
            } else {
                $cost_centre = Nldept::where('gl_dept', $request->old_ccode)
                    ->update([
                        'gl_dept' => $request->edit_ccode,
                        'description' => $request->edit_cname,
                        'gl_class' => $request->edit_ccode * 10,
                    ]);

                Session::Flash('success', 'Cost/Revenue centre updated successfully');
            }
        });
    }
    /*end of cost/revenue centres*/

    //bank names functions*/
    public function olBankNames(Request $request)
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Bank Codes Maintenance',
        );
        return view::make('parameters.gl.fo_bankcodes')
            ->with('dat', $dat);
    }

    public function getBankCodes(Request $request)
    {
        $banks = Banks::all();
        return Datatables::Of($banks)
            ->addColumn('action', function ($bcode) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->make(true);
    }

    public function postBankCode(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $count = Banks::where(DB::raw('TRIM(bank_code)'), $request->bank_code)
                ->count();

            if ($count > 0) {
                Session::Flash('error', 'Cannot insert duplicate bank code');
            } else {
                $cost_centre = Banks::create([
                    'bank_code' => $request->bank_code,
                    'description' => $request->bank_name,
                ]);

                Session::Flash('success', 'Bank Code added successfully');
            }
        });
    }

    public function updateBankCode(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $count = Banks::where(DB::raw('TRIM(bank_code)'), $request->edit_bcode)
                ->where('bank_code', '!=', $request->old_bcode)
                ->count();
            //dd($count);

            if ($count > 0) {
                Session::Flash('error', 'Cannot insert duplicate bank code');
            } else {
                $bank = Banks::where(DB::raw('TRIM(bank_code)'), $request->old_bcode)
                    ->update([
                        'bank_code' => $request->edit_bcode,
                        'description' => $request->edit_bname,
                        'swift_code' => $request->edit_swift_code,
                        'iban_number' => $request->edit_iban_number,
                    ]);

                Session::Flash('success', 'Bank Code updated successfully');
            }
        });
    }
    /*end of bank names*/

    // BANK BRANCH FUNCTIONS

    public function showBankBranch(Request $request)
    {

        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Bank Branch Maintenance',
        );
        return view::make('parameters.gl.fo_bankbranches')
            ->with('dat', $dat);
    }

    public function getBankBranches(Request $request)
    {

        $bankbraches = Bankbranches::all();

        return Datatables::Of($bankbraches)
            ->addColumn('bank_name', function ($bankbraches) {
                $bankname = Banks::where(DB::raw('TRIM(bank_code)'), trim($bankbraches->bank_code))->get()[0]->description;

                return $bankname;
            })
            ->addColumn('action', function ($bankbraches) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->make(true);
    }

    public function editbankbranch(Request $request)
    {
        return Bankbranches::where(DB::raw('TRIM(bank_code)'), trim($request->oldbankcode))
            ->where(DB::raw('TRIM(branch_code)'), trim($request->oldbranchcode))
            ->update(
                [
                    'branch' => $request->editbranch_name,
                    'gl_bank_account' => $request->editbank_account,
                    'branch_code' => $request->editbranch_code,
                    'swift_code' => $request->edit_swift_code,
                    'iban_number' => $request->edit_iban_number,
                ]
            );
        Session::Flash('success', 'Bank Code updated successfully');
    }

    public function bankDropdown()
    {
        return Banks::all();
    }

    public function postBankBranch(Request $request)
    {
        Bankbranches::create([
            'bank_code' => $request->bank_name,
            'branch_code' => $request->branch_code,
            'branch' => $request->branch_name,
            'gl_bank_account' => $request->bank_account,
        ]);
        Session::Flash('success', 'Bank Code added successfully');

    }

    // END OF BANK BRANCH FUNCTIONS

    /*requisition documents*/
    public function reqSupportDocs(Request $request)
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Requisition Support Documents',
        );
        return view::make('parameters.gl.fo_reqdocs')
            ->with('dat', $dat);
    }

    public function getSupportDocs(Request $request)
    {
        $documents = Reqdocs::all();
        return Datatables::Of($documents)
            ->addColumn('action', function ($reqd) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->make(true);
    }

    public function postNewReqDoc(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $count = Reqdocs::where(DB::raw('TRIM(code)'), $request->doc_code)
                ->count();

            if ($count > 0) {
                Session::Flash('error', 'Cannot insert duplicate document code');
            } else {
                $req_doc = Reqdocs::create([
                    'code' => $request->doc_code,
                    'description' => $request->doc_name,
                ]);

                Session::Flash('success', 'Document added successfully');
            }
        });
    }

    public function updateReqDoc(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $count = Reqdocs::where(DB::raw('TRIM(code)'), $request->edit_dcode)
                ->where(DB::raw('TRIM(code)'), '!=', $request->old_dcode)
                ->count();
            //dd($count);

            if ($count > 0) {
                Session::Flash('error', 'Cannot insert duplicate document code');
            } else {
                $req_doc = Reqdocs::where(DB::raw('TRIM(code)'), $request->old_dcode)
                    ->update([
                        'code' => $request->edit_dcode,
                        'description' => $request->edit_dname,
                    ]);

                Session::Flash('success', 'Requisition support document updated successfully');
            }
        });
    }

    /*end of requisition documents*/

    /*additions/deductions*/
    public function cbDeducts(Request $request)
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Additions/Deductions',
        );
        return view::make('parameters.gl.fo_cbdeduct')
            ->with('dat', $dat);
    }

    public function getCbDeducts(Request $request)
    {
        $cbdeducts = Cbdeduct::all();
        return Datatables::Of($cbdeducts)
            ->addColumn('action', function ($ded) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->make(true);
    }

    public function getGlAccounts(Request $request)
    {
        return Nlparams::where('prid','GLH')->where('status','<>','D')
        //->whereRaw(" (bank_flag = 'N' or bank_flag is null) ")
        ->get(['prsno', 'prdesc']);
    }

    public function getEntryTypes(Request $request)
    {
        $entry_types = Cbtrans::where('doc_type', 'PAY')
            ->get();
        return $entry_types;
    }

    public function postCbDeduct(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $count = Cbdeduct::where(DB::raw('TRIM(code)'), $request->deduct_code)
                ->count();
            if ($count > 0) {
                Session::Flash('error', 'Cannot insert duplicate codes');
            } else {
                $cbdeduct = Cbdeduct::create([
                    'code' => $request->deduct_code,
                    'description' => $request->deduct_descr,
                    'percentage_flag' => $request->percentage_flag,
                    'add_deduct' => $request->add_deduct,
                    'percentage_basis' => $request->percentage_basis,
                    'of_percentage' => $request->of_percentage,
                    'percentage' => $request->percentage,
                    'default_amount' => $request->default_amount,
                    'affect_gl' => $request->affect_gl,
                    'glhead' => $request->glhead,
                    'doc_type' => $request->doctype, 
                    'entry_type_descr' => $request->entry_type_descr,
                    'affect_sl' => $request->affect_sl,
                    'sltype' => $request->sltype,
                ]);
                if ($request->add_deduct == "A") {
                    Session::Flash('success', "Addition added successfully");
                } else {
                    Session::Flash('success', "Deduction added successfully");
                }

            }
        });
    }

    public function getDeductDetails(Request $request)
    {
        $code = $request->get('code');
        $deduction = Cbdeduct::where(DB::raw('TRIM(code)'), $code)
            ->get();
        return $deduction;
    }

    public function updateCbDeduct(Request $request)
    {
        $count = Cbdeduct::where(DB::raw('TRIM(code)'), $request->edit_dcode)
            ->where(DB::raw('TRIM(code)'), '!=', $request->old_code)
            ->count();
        if ($count > 0) {
            Session::Flash('error', 'Cannot insert duplicate codes');
        } else {
            $cbdeduct = Cbdeduct::where(DB::raw('TRIM(code)'), $request->old_code)
                ->update([
                    'code' => $request->edit_dcode,
                    'description' => $request->edit_ddescr,
                    'percentage_flag' => $request->edit_pflag,
                    'add_deduct' => $request->edit_adeduct,
                    'percentage_basis' => $request->edit_pbasis,
                    'of_percentage' => $request->edit_of_percentage,
                    'percentage' => $request->edit_percentage,
                    'default_amount' => $request->edit_amount,
                    'affect_gl' => $request->edit_affect_gl,
                    'glhead' => $request->edit_glhead,
                    'entry_type_descr' => $request->edit_entry_type,
                    'affect_sl' => $request->edit_affect_sl,
                    'sltype' => $request->edit_sltype,
                    'doc_type' => $request->edit_doctype,
                ]);
            Session::Flash('success', 'Addition/Deduction updated successfully');
        }
    }
    /*end of additions/deductions*/

    /*inter branch transfers*/
    public function interBranch(Request $request)
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Inter Branch Transfers',
        );
        return view::make('parameters.gl.fo_interbranch')
            ->with('dat', $dat);
    }

    public function getInterBranch(Request $request)
    {
        $inter_branch = Nlintbra::all();
        return Datatables::Of($inter_branch)
            ->addColumn('action', function ($intbra) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('offcd', function ($off) {
                $offices = Nlparams::where('prid', 'OFF')
                    ->whereRaw("trim(prsno)='" . $off->offcd . "'")
                    ->get();
                foreach ($offices as $key) {
                    return $off->offcd . "  " . $key->prdesc;
                }
            })
            ->editColumn('offcd1', function ($off1) {
                $offices1 = Nlparams::where('prid', 'OFF')
                    ->whereRaw("trim(prsno)='" . $off1->offcd1 . "'")
                    ->get();
                foreach ($offices1 as $key1) {
                    return $off1->offcd1 . "  " . $key1->prdesc;
                }
            })
            ->editColumn('glhead', function ($glh) {
                $account = Nlparams::where('prid', 'GLH')
                    ->whereRaw("trim(prsno)='" . $glh->glhead . "'")
                    ->get();
                foreach ($account as $acc) {
                    return formatGlhead($glh->glhead) . "  " . $acc->prdesc;
                }
            })
            ->make(true);
    }

    public function getBranches(Request $request)
    {
        $branches = Nlparams::where('prid', 'OFF')->get();
        return $branches;
    }

    public function validateInterBranch(Request $request)
    {
        $offcd = $request->get('offcd');
        $offcd1 = $request->get('offcd1');
        $count = Nlintbra::where('offcd', $offcd)
            ->where('offcd1', $offcd1)
            ->count();
        if ($count > 0) {
            $result = array('status' => 1);
        } else {
            $result = array('status' => 0);
        }

        return $result;
    }

    public function postInterBranch(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $inter_branch = Nlintbra::create([
                'offcd' => $request->offcd,
                'offcd1' => $request->offcd1,
                'offcd_filler' => $request->offcd1,
                'glhead' => $request->inter_glhead,
            ]);

            Session::Flash('success', 'Record added successfully');
        });
    }

    public function validateEditedInter(Request $request)
    {
        $offcd = $request->get('offcd');
        $offcd1 = $request->get('offcd1');
        $old_offcd1 = $request->get('old_offcd1');

        $count = Nlintbra::where('offcd', $offcd)
            ->where('offcd1', $offcd1)
            ->where('offcd1', '<>', $old_offcd1)
            ->count();
        //dd($count);

        if ($count > 0) {
            $result = array('status' => 1);
        } else {
            $result = array('status' => 0);
        }

        return $result;
    }

    public function updateInterBranch(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $nlintbra = Nlintbra::where('offcd', $request->old_offcd)
                ->where('offcd1', $request->old_offcd1)
                ->update([
                    'offcd1' => $request->edit_offcd1,
                    'offcd_filler' => $request->edit_offcd1,
                    'glhead' => $request->edit_glhead,
                ]);
            //dd($nlintbra);
            Session::Flash('success', 'Record updated successfully');
        });
    }
    /*end of interbranch transfers*/

    /*branch credit limit functions*/
    public function branchCreditLimit()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Branch Credit Limit Setup',
        );
        return view::make('parameters.gl.fo_branchcredit')
            ->with('dat', $dat);
    }

    public function getBranchLimit(Request $request)
    {
        $limits = Branch::all();

        return Datatables::Of($limits)
            ->addColumn('action', function ($intbra) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('offcd', function ($off) {
                $name = Nlparams::where('prid', 'OFF')
                    ->whereRaw("trim(prsno)='" . trim($off->offcd) . "'")
                    ->get();
                foreach ($name as $key) {
                    return $off->offcd . "  " . $key->prdesc;
                }
            })
            ->make(true);
    }

    public function getBranchDetails(Request $request)
    {
        $branch = $request->get('branch');
        $branch_details = Branch::where(DB::raw('TRIM(branch)'), $branch)
            ->get();
        return $branch_details;
    }

    public function updateBranchk(Request $request)
    {
        DB::Transaction(function () use ($request) {

            $prev_data = Branch::where(DB::raw('TRIM(branch)'), $request->branch)->first();

            $branchk = Branch::where(DB::raw('TRIM(branch)'), $request->branch)->first();
            $branchk->offcd = $request->offcd;
            $branchk->branch_credit_days =  $request->credit_days;
            $branchk->clauses_auto = $request->clauses_auto;
            $branchk->branch_credit_limit = $request->credit_limit;
            $branchk->branch_credit_amt = $request->current_credit;
            $branchk->save();

            $changed_data = $branchk->getAttributes();
            $process_slug = 'branch-credit-control';
            $activity_slug = 'update';
            $unique_item = $request->branch;
            $old_data = $prev_data; 
            $ip =$request->ip();
            log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);
            
            Session::Flash('success', 'Branch credit details updated successfully');
        });
    }
    /*end of branch credit limits*/

    /*intermediary credit limits*/
    public function agCreditLimit()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Intermediary Credit Limit Setup',
        );
        return view::make('parameters.gl.fo_agcredit')
            ->with('dat', $dat);
    }

    public function getAgLimit(Request $request)
    {
        //$limits = Agmnf::all();
        $limits = Agmnf::select(['branch', 'agent', 'name', 'acc_type', 'credit_limit_amt', 'credit_auth_amt']);

        return Datatables::Of($limits)
            ->addColumn('action', function ($intag) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('branch', function ($br) {
                return str_pad($br->branch, 3, '0', STR_PAD_LEFT);
            })
            ->editColumn('agent', function ($ag) {
                return str_pad($ag->agent, 5, '0', STR_PAD_LEFT);
            })
            ->editColumn('acc_type', function ($acc) {
                $acc_name = Acctype::where('acc_type', $acc->acc_type)
                    ->get();
                foreach ($acc_name as $name) {
                    return $acc->acc_type . "  " . $name->description;
                }
            })
            ->make(true);
    }

    public function updateAgCredit(Request $request)
    {
        DB::Transaction(function () use ($request) {

            try {
                //code...
           
                // $prev_data = Agmnf::where('branch', $request->branch)
                //     ->where('agent', $request->agent)->first();

                
                $intermediaryParams = new IntermediaryQueryParams([
                    'branch' => $request->branch,
                    'agentNo' => $request->agent,
                ]);
                $prev_data  =IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();
                    
                $agmnf = Agmnf::where('branch', $request->branch)
                    ->where('agent', $request->agent)
                    ->update([ 
                        'credit_limit_amt' => $request->credit_limit,
                        'credit_auth_amt' => $request->credit_authorized
                    ]);
     
                // $ag = Agmnf::where('branch', $request->branch)->where('agent', $request->agent)->first();

                $intermediaryParams = new IntermediaryQueryParams([
                    'branch' => $request->branch,
                    'agentNo' => $request->agent,
                ]);
                $ag  =IntermediaryQueryService::getIntermediaryByBranchAndAgent($intermediaryParams)->first();
                $changed_data = $ag->getAttributes();
                $process_slug = 'intermediary-credit-control';
                $activity_slug = 'update';
                $unique_item = $request->branch.$request->agent;
                $old_data = $prev_data; 
                $ip =$request->ip();
                log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

                Session::Flash('success', 'Intermediary Limits Updated Successfully');

            } catch (\Throwable $th) {
                DB::rollback();
                Session::Flash('error', 'Failed to Updated Intermediary');

            }

        });
    }
    /*end of intermediary credit limit*/

    /*client credit limit*/
    public function clientCreditLimit()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Client Credit Control Limit Setup',
        );
        return view::make('parameters.gl.fo_clientcredit')
            ->with('dat', $dat);
    }

    public function getClientLimit(Request $request)
    {
        $limits = Client::all();

        return Datatables::Of($limits)
            ->addColumn('action', function ($intag) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('branch', function ($br) {
                $branch = Branch::where(DB::raw('TRIM(branch)'), $br->branch)
                    ->get();
                foreach ($branch as $bran) {
                    return $br->branch . "  " . $bran->description;
                }
            })
            ->make(true);
    }

    public function updateClientLimit(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $prev_data = Client::where(DB::raw('TRIM(client_number)'), $request->client_number)->first();

            $client = Client::where(DB::raw('TRIM(client_number)'), $request->client_number)->first();
            $client->client_credit_limit = $request->credit_limit;
            $client->client_credit_amt = $request->credit_amount;
            $client->save();
        
            $changed_data = $client->getAttributes();
            $process_slug = 'client-credit-control';
            $activity_slug = 'update';
            $unique_item = $client->client_number;
            $old_data = $prev_data; 
            $ip =$request->ip();
            log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

            Session::Flash('success', 'Client credit limits updated successfully');
        });

    }
    /*end of client credit limit*/

    /*currency rates maintenance*/
    public function currencyRates()
    {

        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Currency Rates Maintenance',
        );
        $user = Auth::user();
        $username = trim($user->user_name);
        $currency_file = DB::select(" select upd_currency_file from aimsuprof where aims_user = ?", [$username]);
        foreach ($currency_file as $i) {
            $i->upd_currency_file;
        }
        $rights_curr_rate = $i->upd_currency_file;
        $currencies = Currency::all();

        return view::make('parameters.gl.fo_currrate')
            ->with('dat', $dat)
            ->with('currencies', $currencies)
            ->with('rights_curr_rate', $rights_curr_rate);
    }

    public function getCurrencyRates(Request $request)
    {
        $rates = Currrate::orderBy('rate_date')->get();

        return Datatables::of($rates)
            ->addColumn('action', function ($intbra) {

                return '<a class="btn btn-xs btn-edit" data-id="' . $intbra->currency_code . '" data-date="' . $intbra->rate_date . '"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('currency_code', function ($curr) {
                $name = Currency::where('currency_code', $curr->currency_code)
                    ->get();
                foreach ($name as $key) {
                    return $curr->currency_code . "  " . $key->description;
                }
            })
            ->editColumn('rate_date', function ($rd) {
                return formatDate($rd->rate_date);
            })
            ->make(true);
    }

    //monthly
    public function getmonthlycurrencyrates(Request $request)
    {
        $rates = DB::table('CURRRATE_MONTH')->get();

        return Datatables::Of($rates)
            ->addColumn('action', function ($intbra) {
                return '<a class="btn btn-xs btn-editM" data-id="' . $intbra->currency_code . '"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('currency_code', function ($curr) {
                $name = Currency::where('currency_code', $curr->currency_code)
                    ->get();
                foreach ($name as $key) {
                    return $curr->currency_code . "  " . $key->description;
                }
            })
            ->editColumn('rate_date', function ($rd) {
                // return $rd->rate_period;
                return formatDate($rd->rate_period);
            })
            ->make(true);
    }
    //en of monthly

    public function getCurrencies(Request $request)
    {
        //$currencies = Currency::all();
        //return response()->json(['currencies' => $currencies]);
        $currency = Currency::orderBy('currency_code','ASC')->get();	
		return $currency;
    }

    public function geteditcurrrate(Request $request)
    {

        $curr = Currrate::where('currency_code', $request->id)
            ->where('rate_date', $request->date)
            ->first();

        return response()->json(['curr' => $curr]);
    }

    public function updatedailycurrrate(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $today = Carbon::today();

            $curr_rate = Currrate::where('currency_code', $request->curr_code)
                ->where('rate_date', $request->date_new)
                ->update([
                    'currency_rate' => $request->rate_new,
                    'rate_date' => $today,
                ]);

            Session::flash('success', 'Currency rate updated successfully');

        });

        return redirect()->back();
    }

    public function geteditmcurrrate(Request $request)
    {
        $mcurr = DB::table('CURRRATE_MONTH')->where('currency_code', $request->id)->first();

        return response()->json(['mcurr' => $mcurr]);
    }

    public function updatemonthlycurrrate(Request $request)
    {
        $today = Carbon::today();
        $number = cal_days_in_month(CAL_GREGORIAN, $today->month, $today->year);

        $startday = new Carbon('first day of this month');

        if ($startday->month < 10) {
            $rate_period = '0' . $startday->month . $startday->year;
        } else {
            $rate_period = $startday->month . $startday->year;
        }
        DB::table('currrate_month')->update([
            'currency_code' => $request->monthcurr_code,
            'rate_period' => $rate_period,
            'currency_rate' => $request->month_rate_new,
        ]);

        Session::Flash('success', 'Currency rates added successfully');

        return redirect()->back();
    }

    public function checkCurrDate(Request $request)
    {
        $currecy = $request->get('currency');
        $today = Carbon::today();
        $count = Currrate::where('currency_code', $currency)
            ->where('rate_date', $today)
            ->count();

        if ($count > 0) {
            $result = array('status' => 1);
        } else {
            $result = array('status' => 0);
        }

        return $result;
    }

    public function postCurrRate(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $today = Carbon::today();
            $user = Auth::user();
            $username = trim($user->user_name);

            $curr_rate = Currrate::create([
                'currency_code' => $request->curr_code,
                'currency_rate' => $request->rate_new,
                'rate_date' => $today,
                'user_str'=>$username,
                'dola'=>Carbon::now(),
                'created_on'=>Carbon::now(),
                'created_by'=>$username
            ]);

            Session::Flash('success', 'Currency rate added successfully');
        });

    }

    public function postupdatedcurrrate(Request $request)
    {
        // Get the input data from the request
        $currencyCode = $request->updatecurr_code;
        $newRate = $request->updaterate_new;

        // Parse the input date with the expected format
        $parsedDate = \Carbon\Carbon::createFromFormat('Y-m-d', $request->updaterate_date);

        $today = Carbon::today();

        if ($parsedDate < $today) {
            Session::flash('error', 'Updating rates for past dates is not allowed');
            return;
        }

        $existingRecord = Currrate::where('currency_code', $currencyCode)
                                    ->whereDate('rate_date', '=', $parsedDate->toDateString())
                                    ->first();
    

        if ($existingRecord) {
            $oldrecord=$existingRecord;
            if($newRate != $oldrecord->currency_rate){
                $data1 = array();
                $audit1 = array(
                    'currency_code'=>$currencyCode,
                    'rate_date'=>$parsedDate->toDateString(),
                    'field_changed'=>'currency rate',
                    'old_value'=>$oldrecord->currency_rate,
                    'new_value'=>$newRate,
                    'date_changed'=>Carbon::now('EAT'),
                    'ip_address'=>$request->ip(),
                    'sys_user'=>Auth::user()->user_name,
                    'table_name'=>"Currrate"
                );
                array_push($data1, $audit1);
                DB::table('currate_audit_trail')->insert($data1);
            }
            
            Currrate::where('currency_code', $currencyCode)
                                    ->whereDate('rate_date', '=', $parsedDate->toDateString())->update([
                                        'currency_rate' => $newRate,
                                    ]);
    
            Session::flash('success', 'Currency rate updated successfully');
        } else {
            Session::flash('error', 'No matching record found for the given criteria');
        }
    
        // Redirect or return a response as needed
    }

    public function postmonthcurrrate(Request $request)
    {
        $today = Carbon::today();
        $endOfMonth = $today->copy()->endOfMonth();
        $startday = new Carbon('first day of this month');

        $user = Auth::user();
        $username = trim($user->user_name);
        
        while ($today->lte($endOfMonth)) {
            $curr_rate = Currrate::create([
                'currency_code' => $request->monthcurr_code,
                'currency_rate' => $request->month_rate_new,
                'rate_date' => $today,
                'user_str'=>$username,
                'dola'=>Carbon::now(),
                'created_on'=>Carbon::now(),
                'created_by'=>$username,
            ]);
            
            $today->addDay(); // Move to the next day
        }

        // Insert into the monthlycurr_rate table
        // $rate_period = $today->format('Ym');
        if ($startday->month < 10) {
            $rate_period = '0' . $startday->month . $startday->year;
        } else {
            $rate_period = $startday->month . $startday->year;
        }

        $monthlycurr_rate = DB::table('currrate_month')->insert([
            'currency_code' => $request->monthcurr_code,
            'rate_period' => $rate_period,
            'currency_rate' => $request->month_rate_new,
        ]);

        Session::flash('success', 'Currency rates added successfully');
    }

    public function postupdatedmonthcurrrate(Request $request)
    {
        // Get the input data
        $currencyCode = $request->input('updatedmonthcurr_code');
        $newRate = intval($request->input('updatedmonth_rate_new'));

        $user = Auth::user();
        $username = trim($user->user_name);

        // Get the start and end date of the current month

        $startOfMonth = Carbon::today();
        $endOfMonth = $startOfMonth->copy()->endOfMonth();


        // Loop through the days of the current month
        $count=0;
        while ($startOfMonth->lte($endOfMonth)) {
            // Check if a record already exists for this date and currency code
            $oldrecord = Currrate::where('currency_code', $currencyCode)
                                       ->where('rate_date', $startOfMonth->toDateString())->first();

            if($newRate != $oldrecord->currency_rate && $count==0){
                $data1 = array();
                $audit1 = array(
                    'currency_code'=>$currencyCode,
                    'rate_date'=>$startOfMonth->toDateString(),
                    'field_changed'=>'currency rate',
                    'old_value'=>$oldrecord->currency_rate,
                    'new_value'=>$newRate,
                    'date_changed'=>Carbon::now('EAT'),
                    'ip_address'=>$request->ip(),
                    'sys_user'=>Auth::user()->user_name,
                    'table_name'=>"Currrate"
                );
                array_push($data1, $audit1);
                DB::table('currate_audit_trail')->insert($data1);
            }
            $existingRecord = Currrate::where('currency_code', $currencyCode)
                                       ->where('rate_date', $startOfMonth->toDateString())
                                       ->update(['currency_rate' => $newRate,'user_str'=>$username,'dola'=>Carbon::now()]);
    

            $startOfMonth->addDay();
            $count++;
        }

        // Insert into the monthlycurr_rate table
        $ratePeriod = $startOfMonth->format('Ym');
        if ($startOfMonth->month < 10) {
            $ratePeriod = '0' . $startOfMonth->month . $startOfMonth->year;
        }

        $oldrecord=  DB::table('currrate_month')->where('currency_code' , $currencyCode)->first();
        if($newRate != $oldrecord->currency_rate){
            $data1 = array();
            $audit1 = array(
                'currency_code'=>$currencyCode,
                'rate_date'=>$startOfMonth->toDateString(),
                'field_changed'=>'currency rate',
                'old_value'=>$oldrecord->currency_rate,
                'new_value'=>$newRate,
                'date_changed'=>Carbon::now('EAT'),
                'ip_address'=>$request->ip(),
                'sys_user'=>Auth::user()->user_name,
                'table_name'=>"currrate_month"
            );
            array_push($data1, $audit1);
            DB::table('currate_audit_trail')->insert($data1);
        }
        
        DB::table('currrate_month')->where('currency_code' , $currencyCode)->update(['currency_rate' => $newRate]);
    
        Session::flash('success', 'Currency rates updated successfully');
    }
    

    /*end of currency rates maintenance*/

    /*user credit/approval limits */
    public function userLimits()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'User Approval/Credit Limits',
        );
        return view::make('parameters.gl.fo_userlimits')
            ->with('dat', $dat);
    }

    public function getUserLimits(Request $request)
    {
        $limits = Userlimits::all();

        return Datatables::Of($limits)
            ->addColumn('action', function ($intbra) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('user_credit_limit', function ($amt) {
                return number_format($amt->user_credit_limit, 2);
            })
            ->editColumn('user_credit_amt', function ($amt) {
                return number_format($amt->user_credit_amt, 2);
            })
            ->editColumn('approval_amt_limit', function ($amt) {
                return number_format($amt->approval_amt_limit, 2);
            })
            ->make(true);
    }

    public function getAimsUsers(Request $request)
    {
        $users = User::all();
        return $users;
    }

    public function validateAimsUser(Request $request)
    {
        $user_name = $request->get('user_name');
        $count = userLimits::where(DB::raw('TRIM(user_name)'), $user_name)
            ->count();
        if ($count > 0) {
            $result = array("status" => 1);
        } else {
            $result = array("status" => 0);
        }
        return $result;
    }

    public function postUserLimit(Request $request)
    {
        DB::Transaction(function () use ($request) {


            $user_limit = New Userlimits();
            $user_limit->user_name = $request->user_name;
            $user_limit->user_credit_limit = $request->credit_limit;
            $user_limit->user_credit_amt = $request->credit_amount;
            $user_limit->approval_amt_limit = $request->approval_limit;
            
            $user_limit->save();

            $prev_data = '';
            $changed_data = $user_limit->getAttributes();
            $process_slug = 'user-credit-control';
            $activity_slug = 'add';
            $unique_item = $request->user_name;
            $old_data = $prev_data; 
            $ip =$request->ip();
            log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);

            Session::Flash('success', 'User limit added successfully');

        });
    }

    public function updateUserLimit(Request $request)
    {
        DB::Transaction(function () use ($request) {

            try {
                //code...
          
                $user_name = $request->edit_uname;
                $credit_limit = str_replace(',', '', $request->edit_climit);
                $approval_limit = str_replace(',', '', $request->edit_approval);

                $prev_data = Userlimits::where(DB::raw('TRIM(user_name)'), $user_name)->first();

                $user_limit = Userlimits::where(DB::raw('TRIM(user_name)'), $user_name)->first();
                $user_limit->user_credit_limit = $credit_limit;
                $user_limit->approval_amt_limit = $approval_limit;
                $user_limit->save();

                // dd($user_name ,$user_limit);

                $changed_data = $user_limit->getAttributes();
                $process_slug = 'user-credit-control';
                $activity_slug = 'update';
                $unique_item = $request->user_name;
                $old_data = $prev_data; 
                $ip =$request->ip();
                log_data($changed_data,$old_data,$process_slug,$activity_slug,$unique_item,$ip);
            
            Session::Flash('success', 'User limit updated successfully');
            } catch (\Throwable $th) {

               DB::rollback();
               dd($th);
               
            }
        });
    }
    /*end of user approval/credit limits*/

    /*payment requisition serials*/
    public function reqSerials()
    {
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Payment Requisition Serials',
        );
        return view::make('parameters.gl.fo_reqserials')
            ->with('dat', $dat);
    }

    public function getReqSerials(Request $request)
    {
        $year = Carbon::today()->format('Y');
        $serials = Cbseqno::where('account_year', $year)
            ->get();

        return Datatables::Of($serials)
            ->addColumn('action', function ($seq) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->make(true);
    }

    public function addReqSerial(Request $request)
    {

        //check if edit

        if (isset($request->edit)) {
            //edit here

            Cbseqno::where('dtrans_no', $request->dtransno)
                ->where('account_year', $request->accountyear)
                ->update(
                    [
                        'auto_serial' => $request->autoserial,
                        'dept_code' => $request->dept_code,
                    ]
                );
            return [
                'code' => 2,
                'message' => 'Serial updated successfully',
            ];
        } else {

            $Cbseqno = new Cbseqno();

            $dtrans = Cbseqno::where('account_year', $request->accountyear)
                ->max('dtrans_no') + 1;
            $Cbseqno->account_year = $request->accountyear;
            $Cbseqno->account_month = $request->account_month;
            $Cbseqno->dept_code = $request->dept_code;
            $Cbseqno->auto_serial = $request->autoserial;
            $Cbseqno->dtrans_no = $dtrans;
            //start save
            try {
                DB::beginTransaction();

                $Cbseqno->save() .
                DB::commit();
                return [
                    'code' => 1,
                    'message' => 'Serial added successfully',
                ];
                Session::Flash('success', 'Document added successfully');

            } catch (\Exeption $e) {
                DB::rollback();
                report($e);
                return [
                    'code' => -1,
                    'message' => $e->getMessage(),
                ];

            }
            //end save
        }

    }

    /*end of payment requisition serials*/

    public function facnotification(Request $request)
    {

        $dat = array(
            'main' => 'Front Office',
            'module' => 'Parameters',
            'submodule' => 'Facultative Notifation',
        );
        return view::make('parameters.gl.fo_fac_notification')
            ->with('dat', $dat);
    }
    public function getfacnotification(Request $request)
    {
        $setup = FacNotification::all();
        return Datatables::Of($setup)
            ->addColumn('action', function ($tax) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->addIndexColumn()
            ->make(true);
    }

    public function updatefacnotification(Request $request)
    {
        FacNotification::where('email', $request->old_email)
            ->update([
                'email' => $request->editemail,
                'no_of_days' => $request->editdays,
            ]);

    }
    public function links()
    {
        $links = FOparams::where('main_menu', 'F')
            ->where('parent_code', 0)
            ->get();

        return $links;
    }

    public function links1()
    {
        $links1 = FOparams::where('parent_code', '!=', 0)
            ->orderBy('aims_program_code', 'asc')->get();
        return $links1;
    }

    public function viewfolinks()
    {
        $links = $this->links();

        $links1 = $this->links1();

        $dat = array(
            'main' => 'Parameters',
            'module' => 'GL',
            'submodule' => 'Links List',
        );

        return view('parameters.gl.fo_linkslist', [
            'links' => $links,
            'links1' => $links1,
            'dat' => $dat,
        ]);
    }

    public function getFOParamMenu(Request $request)
    {
        $links = FOparams::orderBy('aims_program_type', 'aims_program_code')
            ->get();
        return Datatables::Of($links)
            ->addColumn('action', function ($lnk) {
                return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
            })
            ->editColumn('aims_program_type', function ($type) {
                if ($type->aims_program_type == 'M') {
                    return "Menu";
                } else {
                    return "Screen";
                }
            })
            ->editColumn('main_menu', function ($main) {
                if ($main->main_menu == 'G') {
                    return "General Ledger";
                } else {
                    return "Front Office";
                }
            })
            ->editColumn('parent_code', function ($par) {
                if ($par->parent_code == 0) {
                    return 'Level One Menu';
                } else {
                    $parent = FOparams::where('aims_program_code', $par->parent_code)
                        ->get();
                    foreach ($parent as $pare) {
                        return $pare->aims_program_name;
                    }
                }
            })
            ->make(true);
    }

    public function addfrom_viewlink(Request $request)
    {
        $menu = new FOparams;
        $num = FOparams::orderBy('aims_program_code', 'desc')->first();

        $next_code = $num->aims_program_code + 1;

        $menu->aims_program_type = $request->input('linktype');
        $menu->aims_program_name = $request->input('menuname');
        $menu->aims_program_code = $next_code;
        $menu->program = $request->input('progroute');
        $menu->parent_code = $request->input('linkparent');
        $menu->aims_sys = $request->input('sysname');
        $menu->main_menu = $request->input('syscateg');
        //$menu->save();

        DB::beginTransaction();

        try {
            $menu->save();
            DB::commit();

            $links = $this->links();

            $links1 = $this->links1();

            Session::flash('success', 'Link Added Successfully');
            return view('parameters.gl.fo_linkslist', [
                'links' => $links,
                'links1' => $links1,
            ]);

        } catch (\Throwable $e) {
            // dd($num);
            DB::rollback();

            $links = $this->links();

            $links1 = $this->links1();

            Session::flash('error', 'Link Failed to Add');
            return view('parameters.gl.linkslist', [
                'links' => $links,
                'links1' => $links1,
            ]);

        }
    }

    public function edit_menulink(Request $request)
    {

        DB::beginTransaction();

        try {
            if ($request->get('change_parent') == 'Y') {
                $ed = FOparams::where('aims_program_code', $request->get('edmenucode'))->update([
                    'aims_program_name' => $request->get('edmenuname'),
                    'program' => $request->get('edroutepath'),
                    'parent_code' => $request->get('edlinkparent'),
                    'aims_sys' => $request->get('edsysname'),
                    'main_menu' => $request->get('edsyscateg'),
                ]);
            } else {
                $ed = FOparams::where('aims_program_code', $request->get('edmenucode'))->update([
                    'aims_program_name' => $request->get('edmenuname'),
                    'program' => $request->get('edroutepath'),
                ]);
            }

            DB::commit();

            $links = $this->links();

            $links1 = $this->links1();

            Session::flash('success', 'Link Editted Successfully');
            return view('parameters.gl.fo_linkslist', [
                'links' => $links,
                'links1' => $links1,
            ]);

        } catch (\Throwable $e) {

            DB::rollback();

            $links = $this->links();

            $links1 = $this->links1();

            Session::flash('error', 'Link Edit Failed');
            return view('parameters.gl.fo_linkslist', [
                'links' => $links,
                'links1' => $links1,
            ]);

        }
    }

    public function delete_menulink(Request $request)
    {
        DB::beginTransaction();

        try {
            $del = FOparams::where('aims_program_code', $request->get('delmenucode'))->delete();

            DB::commit();

            $links = $this->links();

            $links1 = $this->links1();

            Session::flash('success', 'Link Deleted Successfully');
            return view('parameters.gl.fo_linkslist', [
                'links' => $links,
                'links1' => $links1,
            ]);

        } catch (\Throwable $e) {
            DB::rollback();

            $links = $this->links();

            $links1 = $this->links1();

            Session::flash('error', 'Link Failed to Delete');
            return view('parameters.gl.fo_linkslist', [
                'links' => $links,
                'links1' => $links1,
            ]);
        }
    }

    public function fetch_paramlinks(Request $request)
    {
        $param = FOparams::where('aims_sys', $request->get('sysname'))->where('main_menu', $request->get('syscateg'))->where('aims_program_type', 'M')->get();

        //dd($param);
        echo $param;
    }

    public function overrecipt(Request $request)
    {
      return view('parameters.gl.overrecipt');
    }



    public function get_gl_subaccount(Request $request)
    {
            $nlparam = Nlparams::where('prsno', $request->account)->first();

            if($nlparam->slhead_flag == "Y"){
                $pipcnam = Pipcnam::first();
                if ($pipcnam->broker_slhead_on == 'A') {
                    $slaccount = Acctype::where('glhead', $request->account)->first();

                }else{
                    // $slaccount =  Agmnf::where('glhead', $request->account)->first();
                    $intermediaryParams = new IntermediaryQueryParams([
                        'conditions' => function ($query) {
                            return $query->where('intermediary_attribute_types.slug', 'comission-glhead');
                        }
                    ]);
                
                    $slaccount =IntermediaryQueryService::getAllIntermediariesWithAttributeDetails($intermediaryParams)
                                                        ->where('intermediary_attribute.value',$request->account)
                                                        ->first();
                }
                $subledger = Nlslparams::where('glhead',  $request->account)->get();
            }
            return response()->json(['subledger'=>$subledger, 'slhead_flag'=>$nlparam->slhead_flag, 'slaccount'=>$slaccount]);
    }


    public function get_req_subaccount(Request $request)
    {
        $nlparam = Nlparams::where('prsno', $request->account)->first();

        if($nlparam->slhead_flag == "Y"){
            $pipcnam = Pipcnam::first();
            if ($pipcnam->broker_slhead_on == 'A') {
                $slaccount = Acctype::where('glhead', $request->account)->first();
            }else{
                $intermediaryParams = new IntermediaryQueryParams([
                    'conditions' => function ($query) {
                        return $query->where('intermediary_attribute_types_slug', 'commission-glhead');
                    }
                ]);
            
                $slaccount =IntermediaryQueryService::getAllIntermediariesWithAttributeDetails($intermediaryParams)
                                                        ->where('intermediary_attribute.value',$request->account)
                                                        ->first();
                // $slaccount =  Agmnf::where('glhead', $request->account)->first();
            }
            $subledger = Nlslparams::where('glhead', $request->account)
                                    ->where('slhead', $request->sub_account)
                                    ->get();
        }
        return response()->json(['subledger'=>$subledger, 'slhead_flag'=>$nlparam->slhead_flag, 'slaccount'=>$slaccount]);
            
    }

}
