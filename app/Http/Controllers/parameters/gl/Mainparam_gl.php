<?php

namespace App\Http\Controllers\parameters\gl;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

use App\GLparams;
use App\Pipcnam;

use Yajra\Datatables\Datatables;
use Session;
use Auth;

class Mainparam_gl extends Controller
{

    public function index(){

        $pip = Pipcnam::all();

        $dat=array(
          'main' => 'Parameters', 
          'module' => 'GL',
        );

         return view('parameters.gl.mainparampage',[
            'pip'=>$pip,'dat'=>$dat

         ]);
    }

    public function getGlParamMenu(Request $request)
    {
        $links = GLparams::orderBy('aims_program_code', 'desc')
                        ->get();
        return Datatables::Of($links)
        ->addColumn('action', function ($lnk) {
            return '<a class="btn btn-xs" id="edgrp"><i class="glyphicon glyphicon-edit"></i></a> <a class="btn btn-xs" id="eddel"><i class="glyphicon glyphicon-minus"></i></a>';
        })
        ->editColumn('aims_program_type',function($type){
            if($type->aims_program_type == 'M'){
                return "Menu";
            }
            else{
                return "Screen";
            }
        })
        ->editColumn('main_menu',function($main){
            if($main->main_menu == 'G'){
                return "General Ledger";
            }
            else{
                return "Front Office";
            }
        })
        ->editColumn('parent_code',function($par){
            if($par->parent_code == 0){
                return 'Level One Menu';
            }
            else{
                $parent = GLparams::where('aims_program_code',$par->parent_code)
                                    ->get();
                foreach($parent as $pare){
                    return $pare->aims_program_name;
                }
            }
        })
        ->make(true);
    }

    Public function links()
    {
            $links = GLparams::whereRaw("(main_menu = ? OR main_menu = ?) AND parent_code = ?", ['G', 'F', 0])->get();
        return $links;
    }

    public function links1()
    {
        $links1 = GLparams::where('parent_code','!=',0)
                    ->orderBy('aims_program_code', 'asc')->get();
        return $links1;
    }

    public function fetch_paramlinks(Request $request){
        $param = GLparams::where('aims_sys',$request->get('sysname'))->where('main_menu',$request->get('syscateg'))->where('aims_program_type','M')->get();

        //dd($param);
        echo $param;
    }

    public function view_paramlinks(){
        $links = $this->links();      

        $links1 = $this->links1();

        $dat=array(
          'main' => 'Parameters', 
          'module' => 'GL',
          'submodule'=>'Links List'
        );

        return view('parameters.gl.linkslist',[
         'links'=>$links,
         'links1'=>$links1,
         'dat'=>$dat
        ]);
    }

    public function add_paramlink(Request $request){
        $menu = new GLparams;
        //$num = GLparams::count();
        //$next_code = $num+1;

        $num = GLparams::orderBy('aims_program_code', 'desc')->first();
        $next_code = $num->aims_program_code+1;

        $menu->aims_program_type = $request->input('linktype');
        $menu->aims_program_name = $request->input('menuname');
        $menu->aims_program_code = $next_code;
        $menu->program=$request->input('progroute');
        $menu->parent_code=$request->input('linkparent');
        $menu->aims_sys=$request->input('sysname');
        $menu->main_menu=$request->input('syscateg');
        //$menu->save();


        DB::beginTransaction();

        try {
            $menu->save();
            DB::commit();

            Session::flash('success','Link Added Successfully');
            return redirect()->route('gl.all');

        }catch (\Throwable $e) {
            
            DB::rollback();

            Session::flash('error','Link Failed to Add');
            return redirect()->route('gl.all');
        }

        
    }

    public function addfrom_viewlink(Request $request){
        $menu = new GLparams;
        $num = GLparams::orderBy('aims_program_code', 'desc')->first();
        
        $next_code = $num->aims_program_code+1;
       
        $menu->aims_program_type = $request->input('linktype');
        $menu->aims_program_name = $request->input('menuname');
        $menu->aims_program_code = $next_code;
        $menu->program=$request->input('progroute');
        $menu->parent_code=$request->input('linkparent');
        $menu->aims_sys=$request->input('sysname');
        $menu->main_menu=$request->input('syscateg');
        //$menu->save();

        DB::beginTransaction();

            try {
                $menu->save();
                DB::commit();

                $links = $this->links();      

                $links1 = $this->links1();

                Session::flash('success','Link Added Successfully');
                return view('parameters.gl.linkslist',[
                    'links'=>$links,
                    'links1'=>$links1
                ]);

            }catch (\Throwable $e) {
               // dd($num);
                DB::rollback();

                $links = $this->links();      

                $links1 = $this->links1(); 

                Session::flash('error','Link Failed to Add');
                return view('parameters.gl.linkslist',[
                    'links'=>$links,
                    'links1'=>$links1
                ]);

            }
    }

    public function edit_menulink(Request $request){

             DB::beginTransaction();

                try {
                    if($request->get('change_parent')=='Y'){
                        $ed = GLparams::where('aims_program_code',$request->get('edmenucode'))->update([
                            'aims_program_name'=>$request->get('edmenuname'),
                            'program'=>$request->get('edroutepath'),
                            'parent_code'=>$request->get('edlinkparent'),
                            'aims_sys'=>$request->get('edsysname'),
                            'main_menu'=>$request->get('edsyscateg')
                          ]);
                    }else{
                        $ed = GLparams::where('aims_program_code',$request->get('edmenucode'))->update([
                            'aims_program_name'=>$request->get('edmenuname'),
                            'program'=>$request->get('edroutepath')
                          ]);
                    }

                    DB::commit();

                    $links = $this->links();      

                    $links1 = $this->links1();

                    Session::flash('success','Link Editted Successfully');
                    return view('parameters.gl.linkslist',[
                        'links'=>$links,
                        'links1'=>$links1
                    ]);

                }catch (\Throwable $e) {
                
                    DB::rollback();

                    $links = $this->links();      

                    $links1 = $this->links1();


                    Session::flash('error','Link Edit Failed');
                    return view('parameters.gl.linkslist',[
                        'links'=>$links,
                        'links1'=>$links1
                    ]);

                }
    }

    public function check_children(Request $request){
        $menu1 = GLparams::where('parent_code',$request->get('progcode'))->count();

        echo $menu1;
    }

    public function delete_menulink(Request $request){
            DB::beginTransaction();

            try {
                    $del = GLparams::where('aims_program_code',$request->get('delmenucode'))->delete();

                    DB::commit();

                    $links = $this->links();      

                    $links1 = $this->links1();


                    Session::flash('success','Link Deleted Successfully');
                    return view('parameters.gl.linkslist',[
                        'links'=>$links,
                        'links1'=>$links1
                    ]);

                }catch (\Throwable $e) {
                    DB::rollback();

                    $links = $this->links();      

                    $links1 = $this->links1();


                    Session::flash('error','Link Failed to Delete');
                    return view('parameters.gl.linkslist',[
                        'links'=>$links,
                        'links1'=>$links1
                    ]);
                }
    }
}