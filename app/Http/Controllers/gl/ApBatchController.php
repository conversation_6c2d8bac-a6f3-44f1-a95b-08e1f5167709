<?php

namespace App\Http\Controllers\gl;

use App\ApCrnInvItem;
use App\Apcrnotes;
use App\ApCrnTax;
use App\ApInvoiceDetails;
use App\ApInvoiceHeader;
use App\Appaymentheader;
use App\Appyinvoices;
use App\Appytaxes;
use App\Apstatus;
use App\ApTax;
use App\ApTransTypes;
use App\Apvendors;
use App\ApWhtCertalloc;
use App\ApWhtCertMast;
use App\ArRecHeader;
use App\BankTrans;
use App\Batch_details;
use App\Batch_entry;
use App\Batch_list;
use App\Cbdeduct;
use App\Cbmast;
use App\Cbmastana;
use App\Cbmastchq;
use App\PeriodModule;
use App\Classes\gl\fo\ClaimsClass;
use App\Classes\gl\GenerateBatch;
use App\Clpmn;
use App\Currency;
use App\Curr_ate;
use App\Glbank;
use App\Glbatch_status;
use App\Glsource;
use App\Gltaxes;
use App\Gltaxtypes;
use App\Gltaxtypesana;
use App\Http\Controllers\Controller;
use App\Http\Controllers\gl\fo\PAYController;
use App\Nlctrl;
use App\Nlparams;
use App\Olpaymethd;
use App\Payreqst;
use App\EscalatePeriod;
use Auth;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use NumberFormatter;
use Redirect;
use Session;
use View;
use Yajra\Datatables\Datatables;
use App\Apcrnotedetails;
use App\Apcrninv_items;
use App\Appyinv_items;
use App\Nlmstdtl;
use App\POInvoice_header;
use App\POInvoice_dtls;
use App\Classes\gl\ReverseBatch;
use App\EscalateReverseBatch;
use App\Arcustomers;
use App\ArInvoiceHeader;
use App\Classes\gl\GenerateEntryId;
use Aimsoft\UserManagement\Models\Permission;
use Illuminate\Support\Facades\Gate;
use App\Aimsuser_web;
use App\Fa_disposal_dtls;
use App\Fa_disposal_header;
use App\Fa_disposal_dtls_items;
use App\FAHeaderDtl;
use App\GLBatchParam;
use App\FADisposalBuyers;

class ApBatchController extends Controller
{
    public function ApnlBatch()
    {
        $glsources = Glsource::all();
        $period = Nlctrl::where('nlctrl', 0)->first();
        $offices = Nlparams::where('prid', 'OFF')->get();
        $trans_types = ApTransTypes::all();
        $status = Apstatus::all();
        $glstatus = Glbatch_status::all();
        $glbtcparam = GLBatchParam::all()->first();

        $dat = array(
            'main' => 'Accounts Payable',
            'module' => 'Ap Batch Processing',
        );
        return view::make('gl.transactions.apnlbatches', compact('glsources', 'period', 'offices', 'trans_types', 'glstatus','glbtcparam'))->with('dat', $dat);
    }

    public function getNlBatch()
    {

        $start_date = (!empty($_GET["from_date"])) ? ($_GET["from_date"]) : ('');
        $to_date = (!empty($_GET["to_date"])) ? ($_GET["to_date"]) : ('');
        $source = (!empty($_GET["source"])) ? ($_GET["source"]) : ('');
        $status = (!empty($_GET["status"])) ? ($_GET["status"]) : ('');

        if ($source != "" || $start_date != "" || $to_date != "" || $status != "") {

            $nlbatches = Batch_list::whereRaw("batch_source =  '" . $source . "'
            or (created_date between '" . $start_date . "' and '" . $to_date . "')
            or batch_status = '" . $status . "' ")->get();
        } else {
            $nlbatches = Batch_list::where('batch_source', 'AP')
                ->orderBy('created_date', "DESC")
                ->orderBy('batch_no', "DESC")
                ->get();
        }

        return Datatables::of($nlbatches)
            ->editColumn('acvdt', function ($dt) {
                return formatDate($dt->created_date);
            })
            ->editColumn('dola', function ($dt) {
                return formatDate($dt->changed_date);
            })
            ->editColumn('local_batch_amount', function ($dt) {
                return number_format($dt->local_batch_amount, 2);
            })
            ->editColumn('batch_status', function ($st) {
                $status = Glbatch_status::where('status_code', $st->batch_status)->get()[0];
                return $status->status_description;
            })
            ->addIndexColumn()
            ->addColumn('action', function ($row) {
                $btn = '<a href="javascript:void(0)" data-toggle="tooltip"  data-id="' . $row->batch_no . '" data-status ="' . $row->batch_status . '" data-original-title="View" class="edit"><i class="fa fa-pencil-square-o"></i></a>';
                return $btn;
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    public function batchEntry(Request $request)
    {
        $batch_no = $request->batch_no;
        $year = $request->period_year;
        $month = $request->period_month;
        // $batch_type = $request->batch_type;
       
        $currencies = Currency::all();
        $rates = Curr_ate::where('rate_date', Carbon::today())->get();
        $offices = Nlparams::where('prid', 'OFF')->get();
        $vendors = Apvendors::all();
        $crd = Apcrnotes::all();
        $glbtcparam = GLBatchParam::all()->first();
        $batch = Batch_list::whereRaw("trim(batch_no) = '" . $batch_no . "'")
        ->where('account_year', $year)
        ->where('account_month', $month)
        ->get()[0];
        
        if ($batch->original_batch != ''){
            $batch_w = Batch_list::whereRaw("trim(batch_no) = '" . $batch->original_batch . "'")
            ->get()[0];
        }
        
        else if($batch->reversal_batch != ''){
            $batch_w = Batch_list::whereRaw("trim(batch_no) = '" . $batch->reversal_batch . "'")
            ->get()[0];

            $rev_escalation = EscalateReverseBatch::where('batch_no', $batch->batch_no)->get()[0];

            $rev_escalated_to = Aimsuser_web::where('user_id', $rev_escalation->escalted_to )->get()[0]['user_name'];

            $rev_escalated_by = Aimsuser_web::where('user_id', $rev_escalation->escalted_by )->get()[0]['user_name'];
        }

        $batch_type = $batch->batch_type;

        $permission = Permission::where('slug','approve-batch-posting')->first();
        $users = $permission->users;

        $permission = Permission::where('slug','authorize-batch-posting')->first();
        $authorize_users = $permission->users;

        if (Gate::check('post-batch')) {
            $userlimit = "Y";
        }else{
            $userlimit = "N";
        }


        switch ($batch_type) {
            case 'INV':
                $headers = APInvoiceHeader::where('batch_no', null)
                                            ->where('invoice_status', '006')->get();
                break;

            case 'CRN':
                $headers = Apcrnotes::where('batch_no', null)
                ->where('credit_note_status', '006')->get();
                break;
            case 'PAY':
                $headers = Appaymentheader::where('batch_no', null)
                ->where('py_header_status', '006')
                ->get();
                break;

            case 'REV':
                $headers = Appaymentheader::where('batch_no', null)->whereNotNull('original_header_no')
                ->where('py_header_status', '006')->get();
                break;

            case 'FAD':
                $headers = Fa_disposal_header::where('batch_no', null)
                ->where('status', '006')
                ->get();
                break;

            default:
                break;
        }

        $nlcontrol = Nlctrl::where('nlctrl', 0)->first();
        $periodescalate = EscalatePeriod::where('batch_no', $batch_no)
                                            ->where('account_year', $batch->account_year)
                                            ->where('account_month', $batch->account_month)
                                            ->first();
        $periodexists = EscalatePeriod::where('batch_no', $batch_no)
        ->where('account_year', $batch->account_year)
        ->where('account_month', $batch->account_month)
        ->count();

        $periodmodule = PeriodModule::where('account_year', $year)
        ->where('account_month', $month)
        ->where('module_id', 'GL')
        ->first();

        $status = Glbatch_status::where('status_code', $batch->batch_status)->get()[0];

         // reversebatch
        $reverseclass = new ReverseBatch($batch_no, $batch->account_year, $batch->account_month);
        $reversedetails =  $reverseclass->get_values();
       
        return view::make('gl.transactions.apbatch_entry', compact('batch_w', 'batch', 'currencies', 'rates', 'offices', 'status', 'batch_type', 'headers', 'vendors', 'users', 'authorize_users', 'nlcontrol', 'periodescalate', 'periodexists', 'periodmodule', 'reversedetails', 'userlimit','glbtcparam'));
    }

    public function getNlBatchd(Request $request)
    {
        $batch_no = $request->get('batch_no');

        $year = $request->get('period_year');
        $month = $request->get('period_month');
        $status = $request->get('status');
        $batch_type = $request->batch_type;
        // dd($batch_type);

        $nlbatchd = Batch_entry::whereRaw("trim(batch_no) = '" . $batch_no . "' and trim(batch_type) ='" . $batch_type . "' ")->orderByRaw('entry_id', 'DESC')
        // ->where('account_year',$year)
        // ->where('account_month',$month)
            ->get();

        return Datatables::of($nlbatchd)
            ->editColumn('currency_code', function ($data) {
                $curr = Currency::where('currency_code', $data->currency_code)->first();
                return $curr->currency;
            })
            ->editColumn('created_date', function ($dt) {
                return formatDate($dt->changed_date);
            })
            ->editColumn('foreign_dr_amount', function ($dt) {
                return number_format($dt->foreign_dr_amount, 2);
            })
            ->editColumn('foreign_cr_amount', function ($dt) {
                return number_format($dt->foreign_cr_amount, 2);
            })
            ->editColumn('foreign_out_of_balance', function ($dt) {
                return number_format($dt->foreign_out_of_balance, 2);
            })

            ->editColumn('batch_status', function ($st) {
                $status = Glbatch_status::where('status_code', $st->batch_status)->get()[0];
                return $status->status_description;
            })
            ->editColumn('status', function ($bst) {
                $status = Glbatch_status::where('status_code', $bst->status)->get()[0];
                return $status->status_description;
            })
            ->editColumn('vendor_no', function ($vd) {
                $get_vendor = Apvendors::where('vendor_id', $vd->vendor_no)->get()[0];
                return $get_vendor->vendor_name;
            })
            ->addIndexColumn()
            ->addColumn('action', function ($row) {
                // $btn = '<a href="javascript:void(0)" data-toggle="tooltip"  data-id="' . $row->batch_no . '" data-entry="' . $row->entry_id . '" data-original-title="View" class="edit"><i class="fa fa-pencil-square-o"></i></a>';

                $btn = ' <a href="javascript:void(0)" data-toggle="tooltip"  data-id="' . $row->batch_no . '" data-entry="' . $row->entry_id . '" data-invoice="' . $row->entry_header . '" data-vendor="' . $row->vendor_no . '" data-original-title="delete" class="delete"> <i class="fa fa-trash-o"></i></a>';

                return $btn;
            })
            ->rawColumns(['action'])
        /*->editColumn('acvdt',function($dt){
        return formatDate($dt->acvdt);
        })*/
            ->make(true);
    }

    public function batchDetails(Request $request)
    {
        $dat = array(
            'main' => 'Accounts Payable',
            'module' => 'Ap Batch Processing',
            'submodule' => 'Ap Batch Details',
            'submod1' => 'Ap Batch Entry Details',
        );
        $datails = Batch_details::all();
        return view('gl.transactions.apbatch_details')->with('dat', $dat);
    }

    public function batchDatatable(Request $request)
    {
        if ($request->ajax()) {
            $data = Batch_details::whereRaw("trim(batch_no) = '" . $request->batch_no . "' and entry_id='" . $request->entry_id . "'")->get();
            return Datatables::of($data)
                ->editColumn('foreign_dr_amount', function ($dt) {
                    return number_format($dt->foreign_dr_amount, 2);
                })
                ->editColumn('local_dr_amount', function ($dt) {
                    return number_format($dt->local_dr_amount, 2);
                })
                ->editColumn('foreign_cr_amount', function ($dt) {
                    return number_format($dt->foreign_cr_amount, 2);
                })
                ->editColumn('local_cr_amount', function ($dt) {
                    return number_format($dt->local_cr_amount, 2);
                })
                ->editColumn('n_balance', function ($nb) {
                    if ($nb->n_balance == 'D') {
                        return "Debit";
                    } else {
                        return "Credit";
                    }
                    return number_format($dt->local_cr_amount, 2);
                })

                ->addColumn('pr_desc', function ($row) {
                    $nlparam = Nlparams::where('prid', 'GLH')->where('prsno', $row->glhead)->first();
                    
                    if ($nlparam) {
                    return $nlparam->prdesc;
                    } else {
                    $glbank = Glbank::where('prid', 'BNK')->where('prsno', $row->glhead)->first();
                    return $glbank->prdesc;
                    }
                })
                ->rawColumns(['action'])
                ->make(true);
        }
        return view('gl.transactions.apbatch_details');
    }

    public function getglbatch()
    {

        $batch_no = session()->get('batch_no');
        $year = Session::get('account_year');
        $month = Session::get('account_month');
        // dd($batch_no, $year, $month);
        $batch = Batch_list::whereRaw("trim(batch_no) = '" . $batch_no . "'")
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->get()[0];

        $dat = array(
            'main' => 'Accounts Payable',
            'module' => 'Ap Batch Processing',
            'submodule' => 'Ap Batch Details',
        );

        return view::make('gl.transactions.apbatch_entry', compact('batch'))->with('dat', $dat);
    }

    public function get_curr_rate(Request $request)
    {
        $currency = $request->currency;

        $curr_code = Currency::where('currency_code', $currency)->first();

        return response()->json(['data' => $curr_code]);
    }

    public function AddEntrydtl(Request $request)
    {
        $count = Batch_entry::where('batch_no', $request->batch_no)->count();
        $batch = Batch_list::where('batch_no', $request->batch_no)->get()[0];
        
        // DB::beginTransaction();
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try {
            $entry_id_class = new GenerateEntryId($request->batch_no, 0);
            $next_entry_id = $entry_id_class->get_next_entry($request->batch_no);
            
            $entry = Batch_entry::create([
                'batch_no' => $request->batch_no,
                'entry_id' =>  $next_entry_id,
                'entry_description' => $request->entry_desc,
                'batch_source' => $request->batch_source,
                'batch_type' => $request->batch_type,
                'offcd' => $request->office_c,
                'currency_code' => $request->currency,
                'currency_rate' => $request->curr_rate,
                'foreign_dr_amount' => 0,
                'offcd' => $request->office_c,
                'foreign_cr_amount' => 0,
                'local_dr_amount' => 0,
                'local_cr_amount' => 0,
                'foreign_out_of_balance' => 0,
                'local_out_of_balance' => 0,
                'entry_header' => $request->entry_header,
                'vendor_no' => $request->vendor_no,
                'status' => "Open",
                'batch_date' => $batch->created_date,
                'transaction_date' => $request->t_date,
                'created_date' => Carbon::now(),
                'created_by' => Auth::user()->user_name,
                'changed_by' => Auth::user()->user_name,
                'changed_date' => Carbon::now(),
            ]);
           
            // get debit
            switch ($request->batch_source) {
                case 'AP':
                    $invoice_header = ApInvoiceHeader::where('invoice_no', $request->entry_header)->where('vendor_id', $request->vendor_no)->first();
                    $vendor = Apvendors::where('vendor_id', $request->vendor_no)->first();

                    $buyers = FADisposalBuyers::where('disposal_no', $request->entry_header)->first();
                    $buyer = $buyers ? $buyers->name : '';
                    
                    break;
                case 'AR':
                    $invoice_header = ArInvoiceHeader::where('invoice_no', $request->entry_header)->where('customer_id', $request->customer_no)->first();
                    $customer = Arcustomers::where('customer_id', $request->customer_no)->first();
                    break;
                default:
                    break;
            }
            
            $count_entry = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();
            
            ##get item no 
            $entry_id_clss = new GenerateEntryId($request->batch_no, $next_entry_id);

            $next_item_no = $entry_id_clss->get_next_item_no($request->batch_no, $next_entry_id);

            switch ($request->batch_type) {
                case 'INV':
                     Batch_details::create([
                        'batch_no' => $request->batch_no,
                        'entry_id' => $entry->entry_id,
                        'item_no' => $next_item_no,
                        'batch_source' => $request->batch_source,
                        'batch_type' => $request->batch_type,
                        'offcd' => $request->office_c,
                        'item_description' => $invoice_header->invoice_description,
                        'entry_type_descr' => $request->batch_type,
                        'glhead' => $invoice_header->control_account,
                        'slhead'=>$invoice_header->slhead,
                        'subledger'=>$invoice_header->slhead_flag,
                        'entry_header' => $request->entry_header,
                        'payee_name' => $vendor->vendor_name,
                        'n_balance' => 'C',
                        'foreign_cr_amount' => $invoice_header->foreign_amount_payable,
                        'foreign_dr_amount' => 0,
                        'local_dr_amount' => 0,
                        'local_cr_amount' => $invoice_header->amount_payable,//$invoice_header->amount_payable * $invoice_header->currency_rate,
                        'currency_code' => $invoice_header->currency_code,
                        'currency_rate' => $invoice_header->currency_rate,
                        'transaction_date' => $entry->transaction_date,
                        'created_date' => Carbon::now(),
                        'created_by' => Auth::user()->user_name,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    if ($invoice_header->lob_trans == 'Y') {
                        $payreqst = Payreqst::where('req_no', trim($invoice_header->invoice_no))->get()[0];

                        $src = 'GB';

                        $transtype = $payreqst->entry_type_descr;

                        if ($payreqst->analyse == 'Y') {
                            $count_item = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();
                            for ($i = 1; $i < 11; $i++) {
                                $code = 'deduction_code_' . $i;
                                $deduction_amount = 'deduction_amount_' . $i;
                                $w_deduction_code = $payreqst->$code;
                                if (!empty($w_deduction_code) && $payreqst->$deduction_amount != 0) {
                                    $cbdeduct = Cbdeduct::where('code', '=', $w_deduction_code)->first();

                                    if ($cbdeduct->add_deduct == 'A') {
                                        $dr_cr = 'C';
                                        $foreign_dr_amount = 0;
                                        $local_dr_amount = 0;
                                        $foreign_cr_amount = $payreqst->$deduction_amount;
                                        $local_cr_amount = $payreqst->$deduction_amount * $payreqst->currency_rate;
                                    } else {
                                        $dr_cr = 'D';
                                        $foreign_cr_amount = 0;
                                        $local_cr_amount = 0;
                                        $foreign_dr_amount = $payreqst->$deduction_amount;
                                        $local_dr_amount = $payreqst->$deduction_amount * $payreqst->currency_rate;
                                    }

                                    Batch_details::create([
                                        'batch_no' => $request->batch_no,
                                        'entry_id' => $entry->entry_id,
                                        'item_no' => $entry_id_clss->get_next_item_no($request->batch_no, $next_entry_id),
                                        'batch_source' => $request->batch_source,
                                        'batch_type' => $request->batch_type,
                                        'offcd' => $request->office_c,
                                        'item_description' => $cbdeduct->description,
                                        'entry_type_descr' => $request->batch_type,
                                        'glhead' => $cbdeduct->glhead,
                                        'entry_header' => $request->entry_header,
                                        'payee_name' => $vendor->vendor_name,
                                        'n_balance' => $dr_cr,
                                        'foreign_cr_amount' => $foreign_cr_amount,
                                        'foreign_dr_amount' => $foreign_dr_amount,
                                        'local_dr_amount' => $local_dr_amount,
                                        'local_cr_amount' => $local_cr_amount,
                                        'currency_code' => $invoice_header->currency_code,
                                        'currency_rate' => $invoice_header->currency_rate,
                                        'transaction_date' => $entry->transaction_date,
                                        'created_date' => Carbon::now(),
                                        'created_by' => Auth::user()->user_name,
                                        'changed_by' => Auth::user()->user_name,
                                        'changed_date' => Carbon::now(),
                                    ]);
                                }
                            }
                        }
                    } 
                    
                    else {
                        $src = 'GL';
                        $transtype = 'INV';
                    }
                    // get credit
                    $invoice_details = ApInvoiceDetails::where('invoice_no', $request->entry_header)->where('vendor_id', $request->vendor_no)->get();
                    
                    foreach ($invoice_details as $key => $value) {
                        // insert retained amount / deductions
                        if ($value->retained_amount != 0) {
                            $count_item = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();
                            Batch_details::create([
                                'batch_no' => $request->batch_no,
                                'entry_id' => $entry->entry_id,
                                'item_no' => $entry_id_clss->get_next_item_no($request->batch_no, $next_entry_id),
                                'batch_source' => $request->batch_source,
                                'batch_type' => $request->batch_type,
                                'offcd' => $request->office_c,
                                'item_description' => 'ITEM TOTAL RETAINED AMOUNT',
                                'entry_type_descr' => $request->batch_type,
                                'glhead' => $value->item_control_account,
                                'slhead'=>$value->slhead,
                                'subledger'=>$value->slhead_flag,
                                'payee_name' => $vendor->vendor_name,
                                'entry_header' => $request->entry_header,
                                'n_balance' => 'D',
                                'foreign_cr_amount' => 0,
                                'foreign_dr_amount' => $value->retained_amount,
                                'local_dr_amount' => $value->retained_amount * $invoice_header->currency_rate,
                                'local_cr_amount' => 0,
                                'currency_code' => $invoice_header->currency_code,
                                'currency_rate' => $invoice_header->currency_rate,
                                'transaction_date' => $entry->transaction_date,
                                'created_date' => Carbon::now(),
                                'created_by' => Auth::user()->user_name,
                                'changed_by' => Auth::user()->user_name,
                                'changed_date' => Carbon::now(),
                            ]);
                        }

                        // insert nett amount /
                        $count_item_nett_amount = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();

                        Batch_details::create([
                            'batch_no' => $request->batch_no,
                            'entry_id' => $entry->entry_id,
                            'item_no' => $entry_id_clss->get_next_item_no($request->batch_no, $next_entry_id),
                            'batch_source' => $request->batch_source,
                            'batch_type' => $request->batch_type,
                            'offcd' => $request->office_c,
                            'item_description' => $value->item_description,
                            'entry_type_descr' => $request->batch_type,
                            'glhead' => $value->item_control_account,
                            'slhead'=>$value->slhead,
                            'subledger'=>$value->slhead_flag,
                            'payee_name' => $vendor->vendor_name,
                            'entry_header' => $request->entry_header,
                            'n_balance' => 'D',
                            'foreign_cr_amount' => 0,
                            'foreign_dr_amount' => $value->net_of_retained_amount,
                            'local_dr_amount' => $value->net_of_retained_amount * $invoice_header->currency_rate,
                            'local_cr_amount' => 0,
                            'currency_code' => $invoice_header->currency_code,
                            'currency_rate' => $invoice_header->currency_rate,
                            'transaction_date' => $entry->transaction_date,
                            'created_date' => Carbon::now(),
                            'created_by' => Auth::user()->user_name,
                            'changed_by' => Auth::user()->user_name,
                            'changed_date' => Carbon::now(),
                        ]);

                        // insert taxes

                        if ($value->taxable == 'Y') {
                            $get_taxes = ApTax::where('invoice_no', $request->entry_header)->where('vendor_id', $request->vendor_no)->where('item_no', $value->item_no)->get();
                            foreach ($get_taxes as $key => $tax) {
                                $taxes = Gltaxes::where('tax_code', $tax->tax_code)->get()[0];
                                $tax_types = Gltaxtypes::where('tax_type', $taxes->tax_type)->get()[0];

                                if ($tax_types->analyse == 'Y') {
                                    $taxtypeana = Gltaxtypesana::whereRaw("trim(tax_type) = '" . $tax_types->tax_type . "' and
                                                    entry_type_description='" . $transtype . "' and tax_code='" . $tax->tax_code . "' ")->get()[0];
                                   
                                    $glh = $taxtypeana->glhead;
                                } 
                                
                                else {
                                    $glh = $tax_types->glhead;
                                }

                                if ($tax->add_deduct == 'A') {
                                    $normal_dr_cr = 'D';
                                    $tax_foreign_cr_amount = 0;
                                    $tax_foreign_dr_amount = $tax->tax_amount;
                                } else {
                                    $normal_dr_cr = 'C';
                                    $tax_foreign_cr_amount = $tax->tax_amount * -1;
                                    $tax_foreign_dr_amount = 0;
                                }

                                $count_tax_items = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();

                                Batch_details::create([
                                    'batch_no' => $request->batch_no,
                                    'entry_id' => $entry->entry_id,
                                    'item_no' =>$entry_id_clss->get_next_item_no($request->batch_no, $next_entry_id),
                                    'batch_source' => $request->batch_source,
                                    'batch_type' => $request->batch_type,
                                    'offcd' => $request->office_c,
                                    'item_description' => $taxes->tax_description,
                                    'entry_type_descr' => $request->batch_type,
                                    'glhead' => $glh,
                                    'entry_header' => $request->entry_header,
                                    'payee_name' => $vendor->vendor_name,
                                    'n_balance' => $normal_dr_cr,
                                    'foreign_cr_amount' => $tax_foreign_cr_amount,
                                    'foreign_dr_amount' => $tax_foreign_dr_amount,
                                    'local_dr_amount' => $tax_foreign_dr_amount * $invoice_header->currency_rate,
                                    'local_cr_amount' => $tax_foreign_cr_amount * $invoice_header->currency_rate,
                                    'currency_code' => $invoice_header->currency_code,
                                    'currency_rate' => $invoice_header->currency_rate,
                                    'transaction_date' => $entry->transaction_date,
                                    'created_date' => Carbon::now(),
                                    'created_by' => Auth::user()->user_name,
                                    'changed_by' => Auth::user()->user_name,
                                    'changed_date' => Carbon::now(),
                                ]);
                            }
                        }
                       
                    }

                    $get_batch_entry_sum = Batch_details::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as local_cr_amount,sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, entry_id")->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->get()[0];

                    Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->update([
                        'foreign_cr_amount' => $get_batch_entry_sum->f_cr_amount,
                        'foreign_dr_amount' => $get_batch_entry_sum->f_dr_amount,
                        'local_cr_amount' => $get_batch_entry_sum->local_cr_amount,
                        'local_dr_amount' => $get_batch_entry_sum->local_dr_amount,
                        'foreign_out_of_balance' => abs($get_batch_entry_sum->f_dr_amount - $get_batch_entry_sum->f_cr_amount),
                        'local_out_of_balance' => abs($get_batch_entry_sum->local_dr_amount - $get_batch_entry_sum->local_cr_amount),
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    $total_local_batch_amount = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->whereRaw("deleted != 'Y' or deleted is null")->sum('local_dr_amount');
                    $entries_count = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->count();

                    $batches = Batch_list::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->update([
                        'no_entries' => $entries_count,
                        'local_batch_amount' => $total_local_batch_amount,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    // dd($request->entry_header, $request->vendor_no, $entry->batch_no);
                    $update_apheader = ApInvoiceHeader::where('invoice_no', $request->entry_header)->where('vendor_id', $request->vendor_no)->update([
                        'batch_no' => $entry->batch_no,
                    ]);
                    break;

                case 'CRN':

                    $credit_note_header = Apcrnotes::where('credit_note_no', $request->entry_header)->where('vendor_id', $request->vendor_no)->first();

                    Batch_details::create([
                        'batch_no' => $request->batch_no,
                        'entry_id' => $entry->entry_id,
                        'item_no' => $next_item_no,
                        'batch_source' => $request->batch_source,
                        'batch_type' => $request->batch_type,
                        'offcd' => $request->office_c,
                        // 'item_description' => 'CREDIT NOTE GROSS AMOUNT',
                        'item_description' => $credit_note_header->narration,
                        'entry_type_descr' => $request->batch_type,
                        'glhead' => $credit_note_header->control_account,
                        'slhead'=>$credit_note_header->slhead,
                        'subledger'=>$credit_note_header->slhead_flag,
                        'entry_header' => $request->entry_header,
                        'payee_name' => $vendor->vendor_name,
                        'n_balance' => 'D',
                        'foreign_cr_amount' => 0,
                        'foreign_dr_amount' => $credit_note_header->foreign_nett_amount,
                        'local_dr_amount' => $credit_note_header->local_nett_amount,
                        'local_cr_amount' => 0,
                        'currency_code' => $credit_note_header->currency_code,
                        'currency_rate' => $credit_note_header->currency_rate,
                        'transaction_date' => $entry->transaction_date,
                        'created_date' => Carbon::now(),
                        'created_by' => Auth::user()->user_name,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    $credit_items = ApCrnInvItem::where('credit_note_no', $request->entry_header)->where('vendor_id', $request->vendor_no)->get();

                    foreach ($credit_items as $key => $value) {

                        // insert nett amount /
                        $count_credit_item = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();
                        $get_description = ApInvoiceDetails::where('vendor_id', $value->vendor_id)->where("invoice_no", $value->invoice_no)->where('item_no', $value->item_no)->get()[0];

                        Batch_details::create([
                            'batch_no' => $request->batch_no,
                            'entry_id' => $entry->entry_id,
                            'item_no' => $entry_id_clss->get_next_item_no($request->batch_no, $next_entry_id),
                            'batch_source' => $request->batch_source,
                            'batch_type' => $request->batch_type,
                            'offcd' => $request->office_c,
                            'item_description' => $get_description->item_description,
                            'entry_type_descr' => $request->batch_type,
                            'glhead' => $value->item_control_account,
                            'slhead'=>$value->slhead,
                            'subledger'=>$value->slhead_flag,
                            'entry_header' => $request->entry_header,
                            'payee_name' => $vendor->vendor_name,
                            'n_balance' => 'C',
                            'foreign_cr_amount' => $value->applied_foreign_amount,
                            'foreign_dr_amount' => 0,
                            'local_dr_amount' => 0,
                            'local_cr_amount' => $value->applied_local_amount,
                            'currency_code' => $credit_note_header->currency_code,
                            'currency_rate' => $credit_note_header->currency_rate,
                            'transaction_date' => $entry->transaction_date,
                            'created_date' => Carbon::now(),
                            'created_by' => Auth::user()->user_name,
                            'changed_by' => Auth::user()->user_name,
                            'changed_date' => Carbon::now(),
                        ]);

                        // insert taxes
                        $src = 'GL';
                        $transtype = 'CRN';

                        if ($value->foreign_applied_tax_amount != 0) {
                            $get_taxes1 = ApCrnTax::where('credit_note_no', $credit_note_header->credit_note_no)->where('invoice_no', $get_description->invoice_no)->where('vendor_id', $credit_note_header->vendor_id)->where('item_no', $value->item_no)->get();
                            foreach ($get_taxes1 as $key => $tax) {
                                $taxes = Gltaxes::where('tax_code', $tax->tax_code)->get()[0];
                                $tax_types = Gltaxtypes::where('tax_type', $taxes->tax_type)->get()[0];

                                if ($tax_types->analyse == 'Y') {
                                    $taxtypeana = Gltaxtypesana::whereRaw("trim(tax_type) = '" . $tax_types->tax_type . "' and
                                                    entry_type_description='" . $transtype . "'  and tax_code='" . $tax->tax_code . "' ")->get()[0];
                                    $glh = $taxtypeana->glhead;
                                } 
                                
                                else {
                                    $glh = $tax_types->glhead;
                                }

                                if ($tax->add_deduct == 'A') {
                                    $normal_dr_cr = 'C';
                                    $tax_foreign_cr_amount = $tax->applied_amount;
                                    $tax_foreign_dr_amount = 0;
                                } else {
                                    $normal_dr_cr = 'D';
                                    $tax_foreign_dr_amount = $tax->applied_amount * -1;
                                    $tax_foreign_cr_amount = 0;
                                }
                                $count_tax_items = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();

                                Batch_details::create([
                                    'batch_no' => $request->batch_no,
                                    'entry_id' => $entry->entry_id,
                                    'item_no' => $entry_id_clss->get_next_item_no($request->batch_no, $next_entry_id),
                                    'batch_source' => $request->batch_source,
                                    'batch_type' => $request->batch_type,
                                    'offcd' => $request->office_c,
                                    'item_description' => $taxes->tax_description,
                                    'entry_type_descr' => $request->batch_type,
                                    // 'glhead' => $tax_types->glhead,
                                    'glhead' => $glh,
                                    'entry_header' => $request->entry_header,
                                    'payee_name' => $vendor->vendor_name,
                                    'n_balance' => $normal_dr_cr,
                                    'foreign_cr_amount' => $tax_foreign_cr_amount,
                                    'foreign_dr_amount' => $tax_foreign_dr_amount,
                                    'local_dr_amount' => $tax_foreign_dr_amount * $credit_note_header->currency_rate,
                                    'local_cr_amount' => $tax_foreign_cr_amount * $credit_note_header->currency_rate,
                                    'currency_code' => $credit_note_header->currency_code,
                                    'currency_rate' => $credit_note_header->currency_rate,
                                    'transaction_date' => $entry->transaction_date,
                                    'created_date' => Carbon::now(),
                                    'created_by' => Auth::user()->user_name,
                                    'changed_by' => Auth::user()->user_name,
                                    'changed_date' => Carbon::now(),
                                ]);
                            }
                        }

                        Apcrnotes::where('credit_note_no', $request->entry_header)->where('vendor_id', $request->vendor_no)->update([
                            'batch_no' => $entry->batch_no,
                        ]);
                    }

                    $get_batch_entry_sum = Batch_details::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as local_cr_amount,sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, entry_id")->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->get()[0];

                    Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->update([
                        'foreign_cr_amount' => $get_batch_entry_sum->f_cr_amount,
                        'foreign_dr_amount' => $get_batch_entry_sum->f_dr_amount,
                        'local_cr_amount' => $get_batch_entry_sum->local_cr_amount,
                        'local_dr_amount' => $get_batch_entry_sum->local_dr_amount,
                        'foreign_out_of_balance' => abs($get_batch_entry_sum->f_dr_amount - $get_batch_entry_sum->f_cr_amount),
                        'local_out_of_balance' => abs($get_batch_entry_sum->local_dr_amount - $get_batch_entry_sum->local_cr_amount),
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    $total_local_batch_amount = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->whereRaw("deleted != 'Y' or deleted is null")->sum('local_dr_amount');
                    $entries_count = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->count();

                    $batches = Batch_list::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->update([
                        'no_entries' => $entries_count,
                        'local_batch_amount' => $total_local_batch_amount,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    break;

                case 'PAY':
                    $payment_header = Appaymentheader::where('payment_header_no', $request->entry_header)->first();
                    $get_gl_bnk_acc = Glbank::where('bank_acc_code', $payment_header->bank_acc_code)->get()[0];
                    
                    Batch_details::create([
                        'batch_no' => $request->batch_no,
                        'entry_id' => $entry->entry_id,
                        'item_no' => $next_item_no,
                        'batch_source' => $request->batch_source,
                        'batch_type' => $request->batch_type,
                        'offcd' => $request->office_c,
                        'item_description' => $payment_header->narration,
                        'entry_type_descr' => $request->batch_type,
                        'bank_acc_code' => $payment_header->bank_acc_code,
                        'glhead' => $get_gl_bnk_acc->prsno,
                        'slhead'=>$payment_header->slhead,
                        'subledger'=>$payment_header->slhead_flag,
                        'entry_header' => $request->entry_header,
                        'payee_name' => $vendor->vendor_name,
                        'cheque_no'=>$payment_header->payment_reference,
                        'n_balance' => 'C',
                        'foreign_cr_amount' => $payment_header->foreign_nett_amount,
                        'foreign_dr_amount' => 0,
                        'local_dr_amount' => 0,
                        'local_cr_amount' => $payment_header->local_nett_amount,
                        'currency_code' => $payment_header->currency_code,
                        'currency_rate' => $payment_header->currency_rate,
                        'transaction_date' => $entry->transaction_date,
                        'created_date' => Carbon::now(),
                        'created_by' => Auth::user()->user_name,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    $payment_invoice = Appyinvoices::where('payment_header_no', $request->entry_header)->get();

                    foreach ($payment_invoice as $key => $value) {
                        $invoice_count = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();
                        
                        if($payment_header->prepayment == 'Y')
                        {
                            $get_invoice_description = POInvoice_header::where('vendor_id', $value->vendor_id)->where("invoice_no", $value->invoice_no)->get()[0];
                            $invoice_description = $get_invoice_description->invoice_descr;
                        }

                        else
                        {
                            $get_invoice_description = APInvoiceHeader::where('vendor_id', $value->vendor_id)->where("invoice_no", $value->invoice_no)->get()[0];
                            $invoice_description = $get_invoice_description->invoice_description;
                        }
                       
                        $payvendor = Apvendors::where('vendor_id', $value->vendor_id)->first();
                        $payment_debit_entry_details = Batch_details::create([
                            'batch_no' => $request->batch_no,
                            'entry_id' => $entry->entry_id,
                            'item_no' => $entry_id_clss->get_next_item_no($request->batch_no, $next_entry_id),
                            'batch_source' => $request->batch_source,
                            'batch_type' => $request->batch_type,
                            'offcd' => $request->office_c,
                            'item_description' => $invoice_description,
                            'entry_type_descr' => $request->batch_type,
                            'glhead' => $value->control_account,
                            'slhead'=>$value->slhead,
                            'subledger'=>$value->slhead_flag,
                            'entry_header' => $request->entry_header,
                            'payee_name' => $payvendor->vendor_name,
                            'n_balance' => 'D',
                            'foreign_cr_amount' => 0,
                            'foreign_dr_amount' => $value->foreign_allocated_amount,
                            'local_dr_amount' => $value->local_allocated_amount,
                            'local_cr_amount' => 0,
                            'currency_code' => $payment_header->currency_code,
                            'currency_rate' => $payment_header->currency_rate,
                            'transaction_date' => $entry->transaction_date,
                            'created_date' => Carbon::now(),
                            'created_by' => Auth::user()->user_name,
                            'changed_by' => Auth::user()->user_name,
                            'changed_date' => Carbon::now(),
                            'invoice_no' => $value->invoice_no
                        ]);

                        $count_payment_taxes = Appytaxes::where('payment_header_no', $payment_header->payment_header_no)->where('invoice_no', $value->invoice_no)->count();
                        if ($count_payment_taxes > 0) {
                            $get_payment_taxes = Appytaxes::where('payment_header_no', $payment_header->payment_header_no)->where('invoice_no', $value->invoice_no)->get();
                            foreach ($get_payment_taxes as $key => $tax) {
                                $taxes = Gltaxes::where('tax_code', $tax->tax_code)->get()[0];
                                $tax_types = Gltaxtypes::where('tax_type', $taxes->tax_type)->get()[0];
                                $cert_no = '';

                                $src = 'GL';

                                $transtype = 'PAY';

                                if ($tax_types->analyse == 'Y') {
                                    $taxtypeana = Gltaxtypesana::whereRaw("trim(tax_type) = '" . $tax_types->tax_type . "' and
                                                    entry_type_description='" . $transtype . "' and tax_code='" . $tax->tax_code . "' ")->get()[0];
                                    $glh = $taxtypeana->glhead;
                                } 

                                else {
                                    $glh = $tax_types->glhead;
                                }

                                if($tax_types->tax_type == 'WHT')
                                {
                                    $cert_no = $value->wht_cert_no;
                                }

                                if ($tax->add_deduct == 'A') {
                                    $normal_dr_cr = 'D';
                                    $tax_foreign_dr_amount = $tax->foreign_applied_amount;
                                    $tax_foreign_cr_amount = 0;
                                } else {
                                    $normal_dr_cr = 'C';
                                    $tax_foreign_cr_amount = $tax->foreign_applied_amount * -1;
                                    $tax_foreign_dr_amount = 0;
                                }
                                $count_payment_items = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();

                                Batch_details::create([
                                    'batch_no' => $request->batch_no,
                                    'entry_id' => $entry->entry_id,
                                    'item_no' => $entry_id_clss->get_next_item_no($request->batch_no, $next_entry_id),
                                    'batch_source' => $request->batch_source,
                                    'batch_type' => $request->batch_type,
                                    'offcd' => $request->office_c,
                                    'item_description' => $taxes->tax_description,
                                    'entry_type_descr' => $request->batch_type,
                                    'glhead' => $glh,
                                    'entry_header' => $request->entry_header,
                                    'payee_name' => $payvendor->vendor_name,
                                    'n_balance' => $normal_dr_cr,
                                    'foreign_cr_amount' => $tax_foreign_cr_amount,
                                    'foreign_dr_amount' => $tax_foreign_dr_amount,
                                    'local_dr_amount' => $tax_foreign_dr_amount * $payment_header->currency_rate,
                                    'local_cr_amount' => $tax_foreign_cr_amount * $payment_header->currency_rate,
                                    'currency_code' => $payment_header->currency_code,
                                    'currency_rate' => $payment_header->currency_rate,
                                    'transaction_date' => $entry->transaction_date,
                                    'created_date' => Carbon::now(),
                                    'created_by' => Auth::user()->user_name,
                                    'changed_by' => Auth::user()->user_name,
                                    'changed_date' => Carbon::now(),
                                    'wht_cert_no' => $cert_no,
                                    'invoice_no' => $value->invoice_no
                                ]);
                            }
                        }

                    }

                    $get_batch_entry_sum = Batch_details::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as local_cr_amount,sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, entry_id")->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->get()[0];

                    Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->update([
                        'foreign_cr_amount' => $get_batch_entry_sum->f_cr_amount,
                        'foreign_dr_amount' => $get_batch_entry_sum->f_dr_amount,
                        'local_cr_amount' => $get_batch_entry_sum->local_cr_amount,
                        'local_dr_amount' => $get_batch_entry_sum->local_dr_amount,
                        'foreign_out_of_balance' => abs($get_batch_entry_sum->f_dr_amount - $get_batch_entry_sum->f_cr_amount),
                        'local_out_of_balance' => abs($get_batch_entry_sum->local_dr_amount - $get_batch_entry_sum->local_cr_amount),
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    $total_local_batch_amount = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->whereRaw("deleted != 'Y' or deleted is null")->sum('local_dr_amount');
                    $entries_count = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->count();

                    $batches = Batch_list::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->update([
                        'no_entries' => $entries_count,
                        'local_batch_amount' => $total_local_batch_amount,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    Appaymentheader::where('payment_header_no', $request->entry_header)->update([
                        'batch_no' => $request->batch_no,
                    ]);

                    break;

                case 'REV':
                    $payment_header = Appaymentheader::where('payment_header_no', $request->entry_header)->first();

                    $original_header = Appaymentheader::where('payment_header_no', $payment_header->original_header_no)
                    ->where('vendor_id', $payment_header->vendor_id)->first();

                    $find_batch_details = Batch_details::where('batch_no',  $original_header->batch_no)
                    ->where('entry_header', $payment_header->original_header_no)->where('batch_type', $original_header->doc_type)->get();

                    foreach ($find_batch_details as $key => $value) {
                        $n_bal = $value->n_balance == 'D' ? 'C' : 'D';

                        if ($value->n_balance == 'D') {
                            $reverse_items_n_balance = 'C';
                            $forein_item_dr_amount = 0;
                            $local_item_dr_amount = 0;
                            $forein_item_cr_amount = $value->foreign_dr_amount;
                            $local_item_cr_amount = $value->local_dr_amount;
                        } 
                        
                        elseif ($value->n_balance == 'C') {
                            $reverse_items_n_balance = 'D';
                            $forein_item_dr_amount = $value->foreign_cr_amount;
                            $local_item_dr_amount = $value->local_cr_amount;
                            $forein_item_cr_amount = 0;
                            $local_item_cr_amount = 0;
                        } 
                        
                        else {
                            $reverse_items_n_balance = '';
                            $forein_dr_amount = 0;
                            $local_dr_amount = 0;
                        }

                        $rev_entry_details = Batch_details::create([
                            'batch_no' => $request->batch_no,
                            'entry_id' => $entry->entry_id,
                            'item_no' => $value->item_no,
                            'batch_source' => $request->batch_source,
                            'batch_type' => $request->batch_type,
                            'offcd' => $request->office_c,
                            'item_description' => $value->item_description,
                            'entry_type_descr' => $request->batch_type,
                            'bank_acc_code' => $value->bank_acc_code,
                            'glhead' => $value->glhead,
                            'entry_header' => $request->entry_header,
                            // 'payee_name' => $value->payee_name,
                            // 'cheque_no'=>$value->cheque_no,
                            'n_balance' => $reverse_items_n_balance ,
                            'foreign_cr_amount' => $forein_item_cr_amount,
                            'foreign_dr_amount' =>$forein_item_dr_amount,
                            'local_dr_amount' => $local_item_dr_amount,
                            'local_cr_amount' => $local_item_cr_amount,
                            'currency_code' => $value->currency_code,
                            'currency_rate' => $value->currency_rate,
                            'transaction_date' => $value->transaction_date,
                            'created_date' => Carbon::now(),
                            'created_by' => Auth::user()->user_name,
                            'changed_by' => Auth::user()->user_name,
                            'changed_date' => Carbon::now(),
                            'slhead'=>$value->slhead,
                            'subledger'=>$value->slhead_flag
                        ]);
                    }

                    $get_batch_entry_sum = Batch_details::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as local_cr_amount,sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, entry_id")->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->get()[0];
                    
                    Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->update([
                        'foreign_cr_amount' => $get_batch_entry_sum->f_cr_amount,
                        'foreign_dr_amount' => $get_batch_entry_sum->f_dr_amount,
                        'local_cr_amount' => $get_batch_entry_sum->local_cr_amount,
                        'local_dr_amount' => $get_batch_entry_sum->local_dr_amount,
                        'foreign_out_of_balance' => abs($get_batch_entry_sum->f_dr_amount - $get_batch_entry_sum->f_cr_amount),
                        'local_out_of_balance' => abs($get_batch_entry_sum->local_dr_amount - $get_batch_entry_sum->local_cr_amount),
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);
    
                    $total_local_batch_amount = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->whereRaw("deleted != 'Y' or deleted is null")->sum('local_dr_amount');
                    $entries_count = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->count();
                    
                    $batches = Batch_list::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->update([
                        'no_entries' => $entries_count,
                        'local_batch_amount' => $total_local_batch_amount,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);
    
                    $upd_pay = Appaymentheader::where('payment_header_no', $request->entry_header)->update([
                        'batch_no' => $request->batch_no,
                    ]);

                    break;
                case 'FAD':
                    $disposal_no = trim( $request->entry_header);
                    
                    $disposal_header = Fa_disposal_header::where('disposal_no', $disposal_no)->first();
                    
                    $disposal_dtls = Fa_disposal_dtls_items::where('disposal_no', $disposal_no)->get();

                    $disposal_accounts = Fa_disposal_dtls_items::where('disposal_no', $disposal_no)->get();
                    // DB::select(DB::raw("select distinct(control_account), dr_cr, ACC_DESCRIPTION from fa_disposal_dtls_items where
                    // disposal_no = '$disposal_no' "));
                    
                    foreach ($disposal_accounts as $acc) {
                        $normal_dr_cr = $acc->dr_cr == 'CR' ? 'C' : 'D';

                        if ($normal_dr_cr == 'D') {
                            $total_dr = $acc->foreign_amount;
                            
                            $local_total_dr = $acc->local_amount;
                            
                            $total_cr = 0;
                            
                            $local_total_cr = 0;
                        } 
                        
                        else {
                            $total_cr = $acc->foreign_amount;
                            
                            $local_total_cr = $acc->local_amount;
                            
                            $total_dr = 0;
                            
                            $local_total_dr = 0;;
                        }
                        
                        $entry_dtls = Batch_details::create([
                            'batch_no' => $request->batch_no,
                            'entry_id' => $entry->entry_id,
                            'item_no' => $entry_id_clss->get_next_item_no($request->batch_no, $next_entry_id),
                            'batch_source' => $request->batch_source,
                            'batch_type' => $request->batch_type,
                            'offcd' => $request->office_c,
                            'item_description' => $acc->acc_description,
                            'glhead' => $acc->control_account,
                            'entry_header' => $request->entry_header,
                            'entry_type_descr' => $request->batch_type,
                            'payee_name' => $buyer,
                            'cheque_no'=>$disposal_header->payment_reference,
                            'n_balance' => $normal_dr_cr,
                            'foreign_cr_amount' => $total_cr,
                            'foreign_dr_amount' => $total_dr,
                            'local_dr_amount' => $local_total_dr,
                            'local_cr_amount' => $local_total_cr,
                            'currency_code' => $disposal_header->currency_code,
                            'currency_rate' => $disposal_header->currency_rate,
                            'transaction_date' => $entry->transaction_date,
                            'created_date' => Carbon::now(),
                            'created_by' => Auth::user()->user_name,
                            'changed_by' => Auth::user()->user_name,
                            'changed_date' => Carbon::now(),
                        ]);
                    }
    
                    $get_batch_entry_sum = Batch_details::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as local_cr_amount, 
                        sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, entry_id")
                        ->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->get()[0];
                    
                    Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->update([
                        'foreign_cr_amount' => $get_batch_entry_sum->f_cr_amount,
                        'foreign_dr_amount' => $get_batch_entry_sum->f_dr_amount,
                        'local_cr_amount' => $get_batch_entry_sum->local_cr_amount,
                        'local_dr_amount' => $get_batch_entry_sum->local_dr_amount,
                        'foreign_out_of_balance' => abs($get_batch_entry_sum->f_dr_amount - $get_batch_entry_sum->f_cr_amount),
                        'local_out_of_balance' => abs($get_batch_entry_sum->local_dr_amount - $get_batch_entry_sum->local_cr_amount),
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    $total_local_batch_amount = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->whereRaw("deleted != 'Y' or deleted is null")->sum('local_dr_amount');
                    $entries_count = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->count();
                    
                    $batches = Batch_list::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->update([
                        'no_entries' => $entries_count,
                        'local_batch_amount' => $total_local_batch_amount,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);
                    
                    $upd_disposal = Fa_disposal_header::where('disposal_no', $disposal_no)->update([
                        'batch_no' => $request->batch_no,
                    ]);

                    break;

                default:
                    # code...
                    break;
            }
          
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
           
            return redirect()->route('ap_batch_entrydtl', ['batch_no' => $entry->batch_no, 'entry_no' => $entry->entry_id]);
        } catch (\Exception $e) {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            // dd($e);
        }
    }

    public function batch_entrydtl(Request $request)
    {
        $batch_no = $request->batch_no;
        $entry_no = $request->entry_no;
        // dd($entry_no);
        // $month = $request->period_month;

        $currencies = Currency::all();
        $rates = Curr_ate::all();

        $entry = Batch_entry::whereRaw("trim(batch_no) = '" . $batch_no . "' and entry_id = '" . $entry_no . "'")
            ->get()[0];
        $batch = Batch_list::where('batch_no', $batch_no)->first();

        $accounts = Nlparams::whereRaw("trim(prid) = 'GLH'")->get();

        // foreach ($accounts as $key => $value) {
        //     dd($value);
        // }

        // dd($accounts);

        $short_code = Currency::where('currency_code', $entry->currency_code)->first();

        // $dat = array(
        //     'main' => 'Accounts Payable',
        //     'module' => 'Ap Batch Processing',
        //     'submodule' => 'Ap Batch Details',
        //     'submod1' => 'Ap Batch Entry Details',
        // );

        //dd($batch);

        return view::make('gl.transactions.apbatchentry_details', compact('entry', 'short_code', 'accounts', 'batch'));
    }

    public function get_ratetb(Request $request)
    {
        $currency = $request->currency;

        $curr_code = Curr_ate::where('currency_code', $currency)->where('rate_date', Carbon::today())->first();

        return response()->json(['rate' => $curr_code]);
    }

    public function AddEntryBatchDetails(Request $request)
    {
        $batch_no = $request->batch_no;
        $entry_id = $request->entry_id;
        $dr_cr = $request->n_balance;
        $count = Batch_details::whereRaw("trim(batch_no) = '" . $batch_no . "' and entry_id='" . $entry_id . "'")->count();

        $debit_amount = 0;
        $credit_amount = 0;
        if ($dr_cr == 'D') {
            $debit_amount = $request->amount;
        } elseif ($dr_cr == 'C') {
            $credit_amount = $request->amount;
        } else {
            $debit_amount = 0;
            $credit_amount = 0;
        }

        $get_batch_entry = Batch_entry::whereRaw("trim(batch_no) = '" . $batch_no . "' and entry_id='" . $entry_id . "'")->first();

        //    $get_batch_entry1 = Batch_entry::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as locar_cr_amount,sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, 'entry_id")->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '".$batch_no."' and entry_id='".$entry_id."'");

        //    dd($get_batch_entry1);

        $entries = Batch_entry::whereRaw("trim(batch_no) = '" . $batch_no . "'")->count();
        $get_batch_list = Batch_list::whereRaw("trim(batch_no) = '" . $batch_no . "'")->first();
        // dd($get_batch_list);

        DB::beginTransaction();

        try {
            $details = Batch_details::create([
                'batch_no' => $batch_no,
                'entry_id' => $entry_id,
                'item_no' => $count + 1,
                'batch_source' => $request->batch_source,
                'batch_type' => $request->batch_type,
                'offcd' => $request->office_c,
                'item_description' => $request->desc,
                'glhead' => $request->account,
                'n_balance' => $request->n_balance,
                'foreign_cr_amount' => $credit_amount,
                'foreign_dr_amount' => $debit_amount,
                'local_dr_amount' => $debit_amount * $request->c_rate,
                'local_cr_amount' => $credit_amount * $request->c_rate,
                'currency_code' => $request->c_code,
                'currency_rate' => $request->c_rate,
                'created_date' => Carbon::now(),
                'created_by' => Auth::user()->user_name,
                'changed_by' => Auth::user()->user_name,
                'changed_date' => Carbon::now(),

            ]);

            // update batch entry table
            $total_foreign_cr_amount = $details->foreign_cr_amount + $get_batch_entry->foreign_cr_amount;
            $total_foreign_dr_amount = $details->foreign_dr_amount + $get_batch_entry->foreign_dr_amount;
            $total_local_dr_amount = $details->local_dr_amount + $get_batch_entry->local_dr_amount;
            $total_local_cr_amount = $details->local_cr_amount + $get_batch_entry->local_cr_amount;
            // dd($total_foreign_cr_amount, $total_foreign_dr_amount, $total_local_dr_amount, $total_local_cr_amount);
            $foreign_out_of_balance = $total_foreign_dr_amount - $total_foreign_cr_amount;
            $local_out_of_balance = $total_local_dr_amount - $total_local_cr_amount;

            $entry_upd = Batch_entry::whereRaw("trim(batch_no) = '" . $details->batch_no . "' and entry_id='" . $details->entry_id . "'")->update([
                'foreign_cr_amount' => $total_foreign_cr_amount,
                'foreign_dr_amount' => $total_foreign_dr_amount,
                'local_cr_amount' => $total_local_cr_amount,
                'local_dr_amount' => $total_local_dr_amount,
                'foreign_out_of_balance' => abs($foreign_out_of_balance),
                'local_out_of_balance' => abs($local_out_of_balance),
                'changed_by' => Auth::user()->user_name,
                'changed_date' => Carbon::now(),
            ]);

            // update batches
            $total_local_amount = Batch_entry::whereRaw("trim(batch_no) = '" . $details->batch_no . "'")->whereRaw("deleted != 'Y' or deleted is null")->sum('local_dr_amount');

            $batches = Batch_list::whereRaw("trim(batch_no) = '" . $details->batch_no . "'")->update([
                'no_entries' => $entries,
                'local_batch_amount' => $total_local_amount,
                'changed_by' => Auth::user()->user_name,
                'changed_date' => Carbon::now(),
            ]);
            DB::commit();
            // all good
            Session::flash('success', 'Batch entry details inserted successfully');
            Session::flash('success', 'Batch entry and batch list updated success');
            return redirect()->route('ap_batch_entrydtl', ['batch_no' => $details->batch_no, 'entry_no' => $details->entry_id]);
        } catch (\Exception $e) {
            // dd($e);
            DB::rollback();
            // something went wrong
        }
    }

    public function EditEntryBatchDetails(Request $request)
    {
        $editdetails = Batch_details::where('batch_no', $request->batch_no)->where('item_no', $request->entry_no)->first();

        return response()->json(['editdetails' => $editdetails]);
    }

    public function EditEntryDetails(Request $request)
    {
        $dr_cr = $request->editn_balance;
        $debit_amount = 0;
        $credit_amount = 0;
        if ($dr_cr == 'D') {
            $debit_amount = $request->amount;
        } elseif ($dr_cr == 'C') {
            $credit_amount = $request->amount;
        } else {
            $debit_amount = 0;
            $credit_amount = 0;
        }

        $get_batch_entry = Batch_entry::whereRaw("trim(batch_no) = '" . $request->editbatch_no . "' and entry_id='" . $request->editentry_id . "'")->first();

        DB::beginTransaction();

        try {
            if ($request->update) {

                $editdetails = Batch_details::where("batch_no", $request->editbatch_no)->where("entry_id", $request->editentry_id)->where('item_no', $request->edititem_no)->update([
                    'item_description' => $request->editdesc,
                    'glhead' => $request->editaccount,
                    'n_balance' => $request->editn_balance,
                    'foreign_cr_amount' => $credit_amount,
                    'foreign_dr_amount' => $debit_amount,
                    'local_dr_amount' => $debit_amount * $request->editc_rate,
                    'local_cr_amount' => $credit_amount * $request->editc_rate,

                    'currency_code' => $request->editc_code,
                    'currency_rate' => $request->editc_rate,
                    'changed_by' => Auth::user()->user_name,
                    'changed_date' => Carbon::now(),
                ]);

                // dd($editdetails);
                $get_batch_entry1 = Batch_details::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as local_cr_amount,sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, entry_id")->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '" . $request->editbatch_no . "' and entry_id='" . $request->editentry_id . "'")->get()[0];

                $entry_upd = Batch_entry::whereRaw("trim(batch_no) = '" . $request->editbatch_no . "' and entry_id='" . $request->editentry_id . "'")->update([
                    'foreign_cr_amount' => $get_batch_entry1->f_cr_amount,
                    'foreign_dr_amount' => $get_batch_entry1->f_dr_amount,
                    'local_cr_amount' => $get_batch_entry1->local_cr_amount,
                    'local_dr_amount' => $get_batch_entry1->local_dr_amount,
                    'foreign_out_of_balance' => abs($get_batch_entry1->f_dr_amount - $get_batch_entry1->f_cr_amount),
                    'local_out_of_balance' => abs($get_batch_entry1->local_dr_amount - $get_batch_entry1->local_cr_amount),
                    'changed_by' => Auth::user()->user_name,
                    'changed_date' => Carbon::now(),
                ]);

                Batch_details::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as local_cr_amount,sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, entry_id")->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '" . $request->editbatch_no . "' and entry_id='" . $request->editentry_id . "'")->get()[0];

                $batches = Batch_list::whereRaw("trim(batch_no) = '" . $request->editbatch_no . "'")->update([
                    'local_batch_amount' => $total_local_amount,
                    'changed_by' => Auth::user()->user_name,
                    'changed_date' => Carbon::now(),
                ]);
            } else if ($request->delete) {
                $deleted = Batch_details::where("batch_no", $request->editbatch_no)->where("entry_id", $request->editentry_id)->where('item_no', $request->edititem_no)->delete();

                $count = Batch_details::where("batch_no", $request->editbatch_no)->where("entry_id", $request->editentry_id)->where('item_no', '>=', $request->edititem_no)->get();

                foreach ($count as $key => $value) {

                    $update_entry_details = Batch_details::where("batch_no", $request->editbatch_no)->where("entry_id", $request->editentry_id)->where('item_no', $value->item_no)->update([
                        'item_no' => $value->item_no - 1,
                    ]);
                }

                $update_entry_batch = Batch_details::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as local_cr_amount,sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, entry_id")->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '" . $request->editbatch_no . "' and entry_id='" . $request->editentry_id . "'")->get()[0];
                $entry_upd_from_details = Batch_entry::whereRaw("trim(batch_no) = '" . $request->editbatch_no . "' and entry_id='" . $request->editentry_id . "'")->update(
                    [
                        'foreign_cr_amount' => $update_entry_batch->f_cr_amount,
                        'foreign_dr_amount' => $update_entry_batch->f_dr_amount,
                        'local_cr_amount' => $update_entry_batch->local_cr_amount,
                        'local_dr_amount' => $update_entry_batch->local_dr_amount,
                        'foreign_out_of_balance' => abs($update_entry_batch->f_dr_amount - $update_entry_batch->f_cr_amount),
                        'local_out_of_balance' => abs($update_entry_batch->local_dr_amount - $update_entry_batch->local_cr_amount),
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]
                );

                $get_batch_amount = Batch_entry::whereRaw("batch_no = '" . $request->editbatch_no . "'")->sum('local_dr_amount');

                $update_batch = Batch_list::whereRaw("batch_no = '" . $request->editbatch_no . "'")->update([
                    'local_batch_amount' => $get_batch_amount,
                ]);
            }

            DB::commit();
            Session::flash('success', 'Batch entry details updated successfully');
            return redirect()->route('ap_batch_entrydtl', ['batch_no' => $request->editbatch_no, 'entry_no' => $request->editentry_id]);
        } catch (\Exception $e) {
            dd($e);
            DB::rollback();
            // something went wrong
        }
    }

    public function GetEditEntry(Request $request)
    {
        // dd($request->entry_id, $request->batch_no);
        $get_edit = Batch_entry::where('batch_no', $request->batch_no)->where('entry_id', $request->entry_id)->first();

        return response()->json(['edit' => $get_edit]);
    }

    public function UpdateEntry(Request $request)
    {
        $dat = array(
            'main' => 'Accounts Payable',
            'module' => 'Ap Batch Processing',
            'submodule' => 'Ap Batch Details',
            'submod1' => 'Ap Batch Entry Details',
        );
        // dd($request->editbatch_no, $request->editentry_id);
        $edit_entry = Batch_entry::whereRaw("batch_no = '" . $request->editbatch_no . "' and entry_id = '" . $request->editentry_id . "'")->update([
            'entry_description' => $request->editentry_desc,
            'currency_code' => $request->editcurrency,
            'currency_rate' => $request->editcurr_rate,
            'batch_date' => $request->editbatch_date,
        ]);

        Session::flash('success', 'Batch entry updated successfully');
        // return redirect()->back();
        return redirect()->route('ap_batch_entrydtl', ['batch_no' => $request->editbatch_no, 'entry_no' => $request->editentry_id])->wit('dat', $dat);
    }

    public function DeleteEntry(Request $request)
    {
        $currencies = Currency::all();
        $rates = Curr_ate::where('rate_date', Carbon::today())->get();
        $apheader = ApInvoiceHeader::all();

        // Batch_entry::where('batch_no',$request->batch_no)->where('entry_no', $request->entry_no)->delete();

        DB::beginTransaction();

        try {
            $delete = Batch_entry::where('batch_no', $request->batch_no)->where('entry_id', $request->entry_no)->delete();
            $delete_entry_detals = Batch_details::where('batch_no', $request->batch_no)->where('entry_id', $request->entry_no)->delete();
            // dd($delete);
            $count = Batch_entry::whereRaw("batch_no = '" . $request->batch_no . "' and entry_id >= '" . $request->entry_no . "'")->get();
            // $count = Batch_details::whereRaw("batch_no = '".$request->batch_no."' and entry_id >= '".$request->entry_no."'")->get();

            foreach ($count as $key => $value) {
                $count = Batch_entry::whereRaw("batch_no = '" . $request->batch_no . "' and entry_id = '" . $value->entry_id . "'")->update([
                    'entry_id' => $value->entry_id - 1,
                ]);

                $update_entry_details = Batch_details::whereRaw("batch_no = '" . $request->batch_no . "' and entry_id = '" . $value->entry_id . "'")->update([
                    'entry_id' => $value->entry_id - 1,
                ]);
            }

            $get_batch_amount = Batch_entry::whereRaw("batch_no = '" . $request->batch_no . "'")->sum('local_dr_amount');
            $count_batch_entries = Batch_entry::whereRaw("batch_no = '" . $request->batch_no . "'")->count();

            $update_batch = Batch_list::whereRaw("batch_no = '" . $request->batch_no . "'")->update([
                'local_batch_amount' => $get_batch_amount,
                'no_entries' => $count_batch_entries,
            ]);
            // ApInvoiceHeader::where('invoice_no', $request->entry_header)->where('vendor_id', $request->vendor_no)->update([
            //     'batch_no' => $entry->batch_no,
            // ]);

            // dd($request->batch_type, $request->invoice_no, $request->vendor_no, $request->batch_no);

            switch ($request->batch_type) {
                case 'INV':
                    $update_apheader = ApInvoiceHeader::where('invoice_no', $request->invoice_no)->where('vendor_id', $request->vendor_no)->update([
                        'batch_no' => '',
                    ]);
                    break;
                case 'CRN':
                    $update_apheader = Apcrnotes::where('credit_note_no', $request->invoice_no)->where('vendor_id', $request->vendor_no)->update([
                        'batch_no' => '',
                    ]);
                    break;
                case 'PAY':
                    $update_arheader = Appaymentheader::where('payment_header_no', $request->invoice_no)->update([
                        'batch_no' => '',
                    ]);
                    break;

                case 'REV':
                    $update_header = Appaymentheader::where('payment_header_no', $request->invoice_no)->update([
                        'batch_no' => '',
                    ]);
                    break;

                case 'FAD':
                    $disposal_header = Fa_disposal_header::where('disposal_no', trim($request->invoice_no))->update([
                        'batch_no' => '',
                    ]);
                    break;

                default:
                    # code...
                    break;
            }

            DB::commit();
            // all good
            return response()->json(['status' => 1]);
        } catch (\Exception $e) {
            Session::flash('error', 'something went wrong');
            DB::rollback();
            // something went wrong
        }
    }

    public function CancelBatch(Request $request)
    {
        $batch_type = $request->batch_type;
        $batch_no = $request->batch_no;
        $batch_src = $request->batch_src;
        $deleted_batch = Batch_list::where('batch_no', $request->batch_no)
            ->where('account_year', $request->account_year)
            ->where('account_month', $request->account_month)
            ->update(['batch_status' => '003']);

        switch ($batch_type) {
            case 'BNF':
                BankTrans::where('batch_no', $batch_no)->update([
                    'batch_no' => '',
                    'transaction_status' => '003',
                ]);

                break;
            case 'BNT':
                BankTrans::where('batch_no', $batch_no)->update([
                    'batch_no' => '',
                    'transaction_status' => '003',
                ]);

                break;
            case 'BNC':
                BankTrans::where('batch_no', $batch_no)->update([
                    'batch_no' => '',
                    'transaction_status' => '003',
                ]);

                break;

            case 'INV':
                if ($batch_src == 'AP') {
                    APInvoiceHeader::where('batch_no', $batch_no)->update([
                        'batch_no' => '',
                        'invoice_status' => '005',
                    ]);
                } else {
                    ArInvoiceHeader::where('batch_no', $batch_no)->update([
                        'batch_no' => '',
                        'invoice_status' => '005',
                    ]);
                }

                break;

            case 'CRN':

                if ($batch_src == 'AP') {
                    Apcrnotes::where('batch_no', $batch_no)->update([
                        'batch_no' => '',
                        'credit_note_status' => '005',
                    ]);
                } else {
                    Arcrnotes::where('batch_no', $batch_no)->update([
                        'batch_no' => '',
                        'credit_note_status' => '005',
                    ]);
                }

                break;
            case 'PAY':

                Appaymentheader::where('batch_no', $batch_no)->update([
                    'batch_no' => '',
                    'py_header_status' => '005',
                ]);
                break;

            case 'REC':

                ArRecHeader::where('batch_no', $batch_no)->update([
                    'batch_no' => '',
                    'rec_header_status' => '005',
                ]);
                break;

            default:
                # code...
                break;
        }

        return response()->json(['status' => '1']);
    }

    public function validateBatchStatus(Request $request)
    {

        $status;
        $accounts = [];
        $z = 0;
        $count = 0;
        $get_batch = Batch_list::where('batch_no', $request->batch_no)->get()[0];
        $get_batch_entry_out_of_balance = Batch_entry::where('batch_no', $request->batch_no)->sum('foreign_out_of_balance');
        $get_batch_entries = Batch_entry::where('batch_no', $request->batch_no)->get();

        if ($request->status_code == '001') {
            if ($get_batch->local_batch_amount == 0 || $get_batch->local_batch_amount < 0) {

                $status = 0;
            } else if (number_format($get_batch_entry_out_of_balance,2) != 0) {

                $status = 1;
            } else if (count($get_batch_entries) == 0) {
                $status = 2;
            } else if (count($get_batch_entries) > 0) {
                foreach ($get_batch_entries as $entry) {
                    $get_entry_details = Batch_details::where('batch_no', $request->batch_no)->where('entry_id', $entry->entry_id)->get();
                    if (count($get_entry_details) == 0) {
                        $status = 3;
                        break;
                    } else {
                        foreach ($get_entry_details as $detail) {
                            if ($detail->foreign_cr_amount == 0 && $detail->foreign_dr_amount == 0) {
                                $status = 4;
                                break;
                            } else {

                                $nlparams1 = Nlparams::where('prid', 'GLH')->where('prsno', $detail->glhead)->count();
                                if ($nlparams1 == 0) {
                                    $status = 5;
                                    $count++;
                                    $accounts[$z++] = "Invalid account number " . $detail->glhead . "";
                                }
                            }
                        }
                    }
                }
            }
        } else {
            $status = 6;
        }

        return response()->json(['status' => $status, 'accounts' => $accounts]);
    }

    public function PostBatch(Request $request)
    {
        //dd($request->all());
        $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $user = Auth::user()->user_name;
        $batch_no = $request->batch_no;
        $period_month = $request->period_month;
        $period_year = $request->period_year;


        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try {
            $this->updatePeriod($batch_no,$period_month,$period_year);
            $procedureName = '' . $gl . '.post_gl_batch';
            $bindings = [
                'w_batch_no' => $batch_no,
                'w_user' => $user,
            ];
            $resp = DB::executeProcedure($procedureName, $bindings);
            //check if batch is payment batch
            $glbatch = Batch_list::where('batch_no', $batch_no)->get()[0];

            if ($glbatch->batch_type == "PAY" && $glbatch->batch_source == "AP") {
                $this->update_chq_and_certs($batch_no);
            }

            if ($glbatch->batch_type == "REV" && $glbatch->batch_source == "AP" && $glbatch->doc_type == 'PAY') {
                $this->update_chq_and_certs($batch_no);
            }

            if($glbatch->batch_type == 'FAD' && $glbatch->batch_source == 'AP'){
                $batch_entries = Batch_entry::Where('batch_no', $batch_no)->get();
                foreach ($batch_entries as $entry) {
                    $disposal_no = $entry->entry_header;
                    $disposal_header = Fa_disposal_header::where('disposal_no', $disposal_no)->first();

                    $assets = Fa_disposal_dtls::where('disposal_no', $disposal_no)->get();
                   
                    foreach ($assets as $asset) {
                        $upd_asset = FAHeaderDtl::where('asset_code', $asset->asset_code)
                        ->where('item_code', $asset->item_code)->update([
                            'status' => 'D'
                        ]);
                    }
                }
            }
           
            // dd('THE END');
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            return response()->json(['batch_no' => $glbatch->batch_no, 'period_year' => $glbatch->account_year, 
            'period_month' => $glbatch->account_month, 'batch_type' => $glbatch->batch_type,'status' => 1]);


        } catch (\Throwable $e) {

            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            $glbatch = Batch_list::where('batch_no', $batch_no)->get()[0];
         
            return response()->json(['batch_no' => $glbatch->batch_no, 'period_year' => $glbatch->account_year, 
            'period_month' => $glbatch->account_month, 'batch_type' => $glbatch->batch_type,'status' => 0]);

        }

    }

    public function genCBPayment($batch_no)
    {
        //get all payment headers in the batch
        $batch_entries = Batch_entry::where('batch_no', $batch_no)->get();
        foreach ($batch_entries as $payment) {
            //get the invoices per payment header
            $py_invoices = Appyinvoices::where('payment_header_no', $payment->entry_header)
                ->get();
            //check if invoice is from LOB
            if (!empty($py_invoices)) {
                foreach ($py_invoices as $invoice) {

                    $ap_inv = APInvoiceHeader::whereRaw("trim(invoice_no) = '" . $invoice->invoice_no . "' ")
                        ->get()[0];
                    //create cbmast
                    if (!empty($ap_inv) && $ap_inv->lob_trans == 'Y') {
                        $create_cbmast = $this->createCbmast($batch_no, $payment->entry_header, $invoice->invoice_no, $payment->entry_id);

                        return $create_cbmast;
                    }
                }
            }
        }
    }

    public function createCbmast($batch_no, $payment_header_no, $invoice_no, $entry_id)
    {
        $payment_header = Appaymentheader::where('payment_header_no', $payment_header_no)
            ->get()[0];

        $ap_invoice = Appyinvoices::where('payment_header_no', $payment_header_no)
            ->where('invoice_no', $invoice_no)
            ->get()[0];

        $batch = Batch_list::where('batch_no', $batch_no)
            ->get()[0];
        //dd($batch);

        $requisition = Payreqst::where('req_no', $invoice_no)
            ->get()[0];

        if ($ap_invoice->foreign_allocated_amount < $requisition->gross_amount) {
            $part_pay_reqnt = 'Y';
        } else {
            $part_pay_reqnt = 'N';
        }

        $payController = new PAYController;

        $voucher_no = $payController->genPvoucher();
        $today = carbon::today();
        $time = carbon::now();
        $user = trim(Auth::user()->user_name);

        $words = new NumberFormatter("en", NumberFormatter::SPELLOUT);
        $amount_in_words = strtoupper($words->format($ap_invoice->foreign_nett_amount));

        $dtrans = STR_PAD($requisition->dtrans_no, 6, '0', STR_PAD_LEFT);
        $d_year = $requisition->account_year;
        $d_month = STR_PAD($requisition->account_month, 2, '0', STR_PAD_LEFT);
        $payrqst_no = $dtrans . $d_year . $d_month;

        $reference = STR_PAD($voucher_no, 6, '0', STR_PAD_LEFT) . $batch->account_year;

        if ($batch->original_batch != null) {
            $entry = 'PAY';
        } else {
            $entry = $requisition->entry_type_descr;
        }

        $cbmast_create = Cbmast::create([
            'dtrans_no' => $voucher_no,
            'account_year' => $batch->account_year,
            'r_reference' => $reference,
            'account_month' => $batch->account_month,
            'effective_date' => $payment_header->effective_date,
            'cheque_no' => $payment_header->payment_reference,
            'cheque_date' => $batch->created_date,
            'receipt_date' => $payment_header->created_date,
            'dola' => $today,
            'line_no' => 0,
            'doc_type' => 'PAY',
            'payrqst_no' => $payrqst_no,
            'offcd' => $requisition->offcd,
            'agent_no' => str_pad($requisition->agent, 5, '0', STR_PAD_LEFT),
            'branch' => $requisition->branch,
            'currency_code' => $payment_header->currency_code,
            'currency_rate' => $payment_header->currency_rate,
            'break_down' => 'Y',
            'dept_code' => $requisition->dept_code,
            'debit_account' => $requisition->debit_account,
            'credit_account' => $payment_header->gl_account,
            'bank_account' => $payment_header->bank_acc_code,
            'pay_method' => $payment_header->payment_mode,
            'claimant_code' => $requisition->claimant_code,
            'peril' => $requisition->peril,
            'batch_no' => $batch->batch_no,
            'client_number' => $requisition->client_number,
            'class' => $requisition->class,
            'credit_note_no' => $requisition->credit_note_no,
            'claim_no' => $requisition->claim_no,
            'entry_type_descr' => $entry,
            'entry_type_descr1' => $requisition->entry_type_descr,
            'excess_amount' => $requisition->excess_amount,
            'final_settle' => $requisition->final_settle,
            'gross_amount' => $ap_invoice->local_allocated_amount,
            'amount' => $ap_invoice->local_nett_amount,
            'foreign_amount' => $ap_invoice->foreign_nett_amount,
            'part_pay_reqnt' => $part_pay_reqnt,
            'foreign_gross_amount' => $ap_invoice->foreign_allocated_amount,
            'name' => $requisition->name,
            'narration' => $requisition->narration,
            'amount_in_words' => $amount_in_words,
            'policy_no' => $requisition->policy_no,
            'source_code' => $requisition->source_code,
            'subledger_code' => $requisition->subledger_code,
            'slhead' => '02',
            'slhead1' => $requisition->slhead1,
            'type_of_bus' => $requisition->type_of_bus,
            'uw_year' => $requisition->uw_year,
            'multiclaims' => $requisition->multiclaims,
            'created_by' => $user,
            'created_date' => $batch->created_date,
            'created_time' => $time,
            'foreign_nett_amount' => $requisition->nett_amount,
            'nett_amount' => $requisition->nett_amount,
            'ln_no' => $requisition->line_no,
        ]); //end of create cbmast
        //dd($cbmast_create);

        if ($cbmast_create) {
            $cbmast = Cbmast::where('doc_type', 'PAY')
                ->where('dtrans_no', $voucher_no)
                ->where('account_year', $batch->account_year)
                ->where('account_month', $batch->account_month)
                ->where('offcd', $requisition->offcd)
                ->first();
            //dd($cbmast);

            $next_serial = $payController->updateDoctype();
            //dd($next_serial);

            //payment analysis
            $batch_entry = Batch_details::where('batch_no', $batch_no)
                ->where('entry_id', $entry_id)
                ->get();
            foreach ($batch_entry as $entry) {
                if ($entry->n_balance == 'D') {
                    $amount = $entry->local_dr_amount;
                    $fc_amount = $entry->foreign_dr_amount;
                } else {
                    $amount = $entry->local_cr_amount;
                    $fc_amount = $entry->foreign_cr_amount;
                }
                $cbmastana = Cbmastana::create([
                    'offcd' => $cbmast->offcd,
                    'inter_branch_offcd' => $cbmast->offcd,
                    'doc_type' => $cbmast->doc_type,
                    'entry_type_descr' => $cbmast->entry_type_descr,
                    'trans_date' => $entry->transaction_date,
                    'reference' => $reference,
                    'item_no' => $entry->item_no,
                    'entry_type_descr1' => $requisition->entry_type_descr,
                    'source_code' => $requisition->source_code,
                    'narration' => $entry->item_description,
                    'glhead' => $entry->glhead,
                    'currency_code' => $payment_header->currency_code,
                    'currency_rate' => $payment_header->currency_rate,
                    'amount' => $amount,
                    'foreign_amount' => $fc_amount,
                    'dr_cr' => $entry->n_balance,
                ]);

            }

            //update payreqst payments_todate
            if ($batch->original_batch != null) {
                $paid_amount = $ap_invoice->foreign_allocated_amount * -1;
            } else {
                $paid_amount = $ap_invoice->foreign_allocated_amount;
            }

            $payreqst_update = $payController->updatePayreqst($requisition->req_no, $paid_amount, $payment_header->payment_reference,$user);

            //check if payment method is cheque
            $olpaymethod = Olpaymethd::where('pay_method', $payment_header->payment_mode)
                ->get()[0];
            //dd($olpaymethod);
            //update cheque serial
            if (!empty($olpaymethod) && $olpaymethod->cheque == 'Y') {
                $next_cheque = $payController->updateChequeno($payment_header->payment_method, $$payment_header->gl_account, $payment_header->payment_reference);

                //create cheque details
                $create_cbmastchq = Cbmastchq::create([
                    'doc_type' => $cbmast->doc_type,
                    'offcd' => $cbmast->offcd,
                    'entry_type_descr' => $cbmast->entry_type_descr,
                    'reference' => $reference,
                    'line_no' => $cbmast->line_no,
                    'amount' => $cbmast->amount,
                    'printed_cheque' => $cbmast->printed_cheque,
                    'pay_method' => $cbmast->pay_method,
                    'cancelled' => $cbmast->cancelled,
                    'cheque_printed_on' => $cbmast->cheque_printed_on,
                    'cheque_printed_by' => $cbmast->cheque_printed_by,
                    'name' => $cbmast->name,
                    'cheque_no' => trim($cbmast->cheque_no),
                    'cheque_date' => $cbmast->cheque_date,
                ]);

            }

            //update clpmn and clhmn claims payment
            if ($cbmast->source_code == 'CLM') {
                if ($cbmast->multiclaims == 'Y') {
                    $claimsClass = new ClaimsClass();
                    $createClpmn = $claimsClass->multiclaimPayment($cbmast, $payreqst);

                    $clhmnallo = Clhmnallo::where('reference', $reference)
                        ->where('offcd', $cbmast->offcd)
                        ->where('doc_type', $cbmast->doc_type)
                        ->where('entry_type_descr', $cbmast->entry_type_descr)
                        ->get();
                    //dd($cbmast);
                } else {
                    //dd('here');
                    $claimsClass = new ClaimsClass();
                    $createClpmn = $claimsClass->updateClaimPayment($cbmast->offcd, $cbmast->doc_type, $cbmast->dtrans_no, $cbmast->account_year, $cbmast->account_month, $cbmast->entry_type_descr);
                    $clpmn = Clpmn::where('claim_no', $cbmast->claim_no)->get();

                    if ($requisition->sal_dtrans_no != null) {
                        $createCrn = $claimsClass->createCRN($requisition->sal_dtrans_no, $requisition->deduction_code_1, $cbmast);

                    }
                    //dd($clpmn);
                }

            } //end if souce code = clm

            if ($cbmast->source_code == 'U/W' || $cbmast->source_code == 'FAC') {
                $acdetClass = new AcdetClass();
                //return dd($cbmast->entry_type_descr);
                $createAcdet = $acdetClass->createAcdet($cbmast->offcd, $cbmast->doc_type, $cbmast->dtrans_no, $cbmast->account_year, $cbmast->account_month, $cbmast->entry_type_descr);
            }

            //call claim reinsurance procedure

            /* For clpmn */
            if ($cbmast->source_code == 'CLM') {
                $schem = schemaName();

                $gb = $schem['gb'];

                // dd($cbmast);
                if ($cbmast->multiclaims != "Y") {

                    $clpmn_item = Clpmn::Where('claim_no', '=', $cbmast->claim_no)
                        ->orderBy('pay_time', 'desc')->first();
                    //Calls procedure for reinsurance

                    DB::beginTransaction();
                    try {
                        $procedureName = '' . $gb . '.reinsure_claim';
                        $bindings = [
                            'g_claim_no' => $clpmn_item->claim_no,
                            'g_policy_no' => $clpmn_item->policy_no,
                            'g_doc_type' => $clpmn_item->doc_type,
                            'g_entry_type_descr' => $clpmn_item->entry_type_descr,
                            'g_pay_type' => (int) $clpmn_item->pay_type,
                            'g_dtrans_no' => $clpmn_item->tran_no,
                            'g_account_year' => $clpmn_item->account_year,
                            'g_account_month' => $clpmn_item->account_month,
                            'g_ln_no' => (int) $clpmn_item->ln_no,
                            //'g_pay_amount'=>(int)$clpmn_item->pay_amount*$clpmn_item->currency_rate

                        ];
                        //dd($bindings);
                        $resp = DB::executeProcedure($procedureName, $bindings);

                        DB::commit();

                        //Session::flash('success','Reinsured Successfully');
                        //return redirect()->route('defcopytreatysetup');

                    } catch (\Throwable $e) {
                        //dd($e);
                        $codex = json_encode($e->getMessage());
                        $error = explode('\n', $codex);
                        $error_msg = $error[1];
                        $referrence = "reinsure_claim2 : trans no: '$clpmn_item->tran_no' Doc type : '$clpmn_item->doc_type' ";
                        $route_name = Route::getCurrentRoute()->getActionName();
                        $this->log_error_details($route_name, $error_msg, $referrence);
                        DB::rollback();

                        Session::flash('error', 'Failed to Reinsure');
                        //return redirect()->route('defcopytreatysetup');
                    }

                    //End Reinsurance Procedure
                    DB::transaction(function () use ($clpmn_item, $gb) {
                        $procedure_name = '' . $gb . '.update_clpmn';
                        $bindings = [
                            'new_claim_no' => $clpmn_item->claim_no,
                            'new_dtrans_no' => $clpmn_item->dtrans_no,
                            'new_g_user' => Auth::user()->surname,
                        ];
                        $resp = DB::executeProcedure($procedure_name, $bindings);
                    });
                } //end of non multiclaims
                else if ($cbmast->multiclaims == "Y") {
                    foreach ($clhmnallo as $clhm) {
                        $clpmn_item = Clpmn::Where('claim_no', '=', $clhm->claim_no)
                            ->orderBy('pay_time', 'desc')->first();
                        //dd($clpmn_item);
                        DB::transaction(function () use ($clpmn_item, $gb) {
                            $procedure_name = '' . $gb . '.update_clpmn';
                            $bindings = [
                                'new_claim_no' => $clpmn_item->claim_no,
                                'new_dtrans_no' => $clpmn_item->dtrans_no,
                                'new_g_user' => Auth::user()->surname,
                            ];
                            $resp = DB::executeProcedure($procedure_name, $bindings);
                        });
                    }
                } //end of multiclaims
            }
            /*End for clpmn*/

            return $cbmast;

        }

    }

    public function UpdateBatchStatus(Request $request)
    {
        if ($request->status_code == 002) {
            Batch_list::where('batch_no', $request->batch_no)
                ->where('account_year', $request->account_year)
                ->where('account_month', $request->account_month)
                ->update(['batch_status' => '001']);

            Batch_entry::where('batch_no', $request->batch_no)
                ->update(['batch_status' => '001', 'status' => '001']);
        } else {
            Batch_list::where('batch_no', $request->batch_no)
                ->where('account_year', $request->account_year)
                ->where('account_month', $request->account_month)
                ->update(['batch_status' => '002']);

            Batch_entry::where('batch_no', $request->batch_no)
                ->update(['batch_status' => '002', 'status' => '002']);
        }

        return response()->json(['status' => 1]);
    }

    public function get_invoices(Request $request)
    {
        switch ($request->batch_type) {
            case 'INV':
                $result = APInvoiceHeader::where('invoice_no', $request->invoice_no)->get()[0];
                break;

            case 'CRN':
                $result = Apcrnotes::where('credit_note_no', $request->invoice_no)->get()[0];
                break;
            case 'PAY':
                $result = Appaymentheader::where('payment_header_no', $request->invoice_no)->get()[0];
                break;

            case 'REV':
                $result = Appaymentheader::where('payment_header_no', trim($request->invoice_no))->get()[0];
                break;

            case 'FAD':
                $result = Fa_disposal_header::where('disposal_no', trim($request->disposal_no))->where('status', '006')->get()[0];
                break;

            default:
                # code...
                break;
        }

        return response()->json(['result' => $result]);
    }

    public function get_vendor_info(Request $request)
    {
        switch ($request->batch_type) {
            case 'INV':
                $result = APInvoiceHeader::where('vendor_id', $request->vendor_no)
                                            ->where('invoice_status', '006')
                                            ->where('batch_no', null)
                                            ->get();
                break;

            case 'CRN':
                $result = Apcrnotes::where('vendor_id', $request->vendor_no)
                ->where('credit_note_status', '006')
                ->where('batch_no', null)
                ->get();
                break;
            case 'PAY':
                $result = APInvoiceHeader::all();
                break;

            default:
                # code...
                break;
        }

        return response()->json(['result' => $result]);
    }

    public function editApBatch(Request $request)
    {
        $getbatch = Batch_list::where("batch_no", $request->batch_no)->first();
        return response()->json(['getbatch' => $getbatch]);
    }

    public function UpdateApBatches(Request $request)
    {
        Batch_list::where('batch_no', $request->batch_no)->update([
            'batch_source' => $request->b_source,
            'batch_title' => $request->b_title,
            'batch_description' => $request->narration,
            'account_year' => $request->period_year,
            'account_month' => $request->period_month,
            'batch_type' => $request->batch_type,
            'changed_by' => Auth::user()->user_name,
            'changed_date' => Carbon::now(),
            'no_entries' => 0,
            'local_batch_amount' => 0,
            'expected_batch_total' => $request->exp_amount,
            'batch_status' => '001',

        ]);
        return redirect()->route('apbatch_entry', ['batch_no' => $request->batch_no, 'period_year' => $request->period_year, 'period_month' => $request->period_month, 'batch_type' => $request->batch_type])
            ->with('success', 'Batch updated successfully');
    }

    function updateInvAmounts($batch_no)
    {
        $batch_entries = Batch_entry::where('batch_no', $batch_no)->get();
        if(count($batch_entries) > 0)
        {
            foreach ($batch_entries as $entry) 
            {
                $crn_no = trim($entry->entry_header);

                $crnote = Apcrnotes::where('credit_note_no', $crn_no)->get()[0];
                #find invoices
                $invoices = Apcrnotedetails::where('credit_note_no', $crnote->credit_note_no)
                ->where('vendor_id', $crnote->vendor_id)->get();

                if(count($invoices) > 0)
                {
                    foreach ($invoices as $inv) 
                    {
                        $invoice_head = DB::table('ap_invoice_header')->where('invoice_no', $inv->invoice_no)
                        ->where('vendor_id', $inv->vendor_id)->get()[0];

                        #update invoice header
                        $invoice_upd = DB::table('ap_invoice_header')->where('invoice_no', $inv->invoice_no)
                        ->where('vendor_id', $inv->vendor_id)
                            ->update([
                            'foreign_allocated' => $invoice_head->foreign_allocated + $inv->foreign_nett_amount,
                            'local_allocated' => $invoice_head->local_allocated + $inv->foreign_nett_amount,
                            'foreign_unallocated' => $invoice_head->foreign_unallocated - $inv->local_nett_amount,
                            'local_unallocated' => $invoice_head->local_unallocated - $inv->local_nett_amount,
                            'foreign_unallocated_gross' => $invoice_head->foreign_unallocated_gross - $inv->applied_foreign_amount,
                            'local_unallocated_gross' => $invoice_head->local_unallocated_gross - $inv->applied_local_amount
                        ]);

                        #update invoice details
                        $items = Apcrninv_items::where('invoice_no', $inv->invoice_no)->where('credit_note_no', $inv->credit_note_no)
                        ->where('vendor_id', $inv->vendor_id)->get();

                        if(count($items) > 0)
                        {
                            foreach ($items as $item) 
                            {
                                $inv_item = DB::table('ap_invoice_details')->where('invoice_no', $item->invoice_no)->where('item_no', $item->item_no)
                                ->where('vendor_id', $item->vendor_id)->get()[0];

                                $item_upd = DB::table('ap_invoice_details')->where('invoice_no', $item->invoice_no)->where('vendor_id', $item->vendor_id)
                                    ->where('item_no', $item->item_no)->update([
                                        'foreign_allocated' => $inv_item->foreign_allocated + $item->foreign_nett,
                                        'local_allocated' => $inv_item->local_allocated + $item->local_nett,
                                        'foreign_unallocated' => $inv_item->foreign_unallocated - $item->foreign_nett,
                                        'local_unallocated' => $inv_item->local_unallocated - $item->local_nett,
                                        'foreign_item_bal' => $inv_item->foreign_item_bal - $item->applied_foreign_amount,
                                        'local_item_bal' =>  $inv_item->local_item_bal - $item->applied_local_amount,
                                ]); 
                            }
                        }
                    }
                }
            }
        }
    }

    function updateInvAmounts_pay($batch_no)
    {
        $batch_entries = Batch_entry::where('batch_no', $batch_no)->get();

        if(count($batch_entries) > 0)
        {
            foreach ($batch_entries as $entry) 
            {
                $pay_no = trim($entry->entry_header);

                $payment = Appaymentheader::where('payment_header_no', $pay_no)->get()[0];
                #find invoices
                $invoices = Appyinvoices::where('payment_header_no', $payment->payment_header_no)
                ->where('vendor_id', $payment->vendor_id)->get();
                
                if(count($invoices) > 0)
                {
                    foreach ($invoices as $inv) 
                    {
                        ##prepayments
                        if($payment->prepayment == 'Y')
                        {
                            $this->updateInvAmounts_prepapayments($inv, $pay_no);
                        }

                        else
                        {
                            $invoice_head = DB::table('ap_invoice_header')->where('invoice_no', $inv->invoice_no)
                            ->where('vendor_id', $inv->vendor_id)->get()[0];

                            #update invoice details
                            $items = Appyinv_items::where('invoice_no', $inv->invoice_no)->where('payment_header_no', $inv->payment_header_no)
                            ->where('vendor_id', $inv->vendor_id)->get();

                            if(count($items) > 0)
                            {
                                foreach ($items as $item) 
                                {
                                    $inv_item = DB::table('ap_invoice_details')->where('invoice_no', $item->invoice_no)->where('item_no', $item->item_no)
                                    ->where('vendor_id', $item->vendor_id)->get()[0];

                                    $item_upd = DB::table('ap_invoice_details')->where('invoice_no', $item->invoice_no)->where('vendor_id', $item->vendor_id)
                                        ->where('item_no', $item->item_no)->update([
                                            'foreign_allocated' => $inv_item->foreign_allocated + $item->applied_foreign_amount,
                                            'local_allocated' => $inv_item->local_allocated + $item->applied_local_amount,
                                            'foreign_unallocated' => $inv_item->foreign_unallocated - $item->applied_foreign_amount,
                                            'local_unallocated' => $inv_item->local_unallocated - $item->applied_local_amount,
                                            'foreign_item_bal' => $inv_item->foreign_item_bal - $item->foreign_taxable_amount,
                                            'local_item_bal' =>  $inv_item->local_item_bal - $item->local_taxable_amount,
                                    ]); 
                                }

                                $total_allo = Appyinv_items::where('invoice_no', $inv->invoice_no)->where('payment_header_no', $inv->payment_header_no)
                                ->where('vendor_id', $inv->vendor_id)->sum('applied_foreign_amount');

                                $total_allo_l = Appyinv_items::where('invoice_no', $inv->invoice_no)->where('payment_header_no', $inv->payment_header_no)
                                ->where('vendor_id', $inv->vendor_id)->sum('applied_local_amount');

                                $inv_gross_bal = Appyinv_items::where('invoice_no', $inv->invoice_no)->where('payment_header_no', $inv->payment_header_no)
                                ->where('vendor_id', $inv->vendor_id)->sum('foreign_taxable_amount');

                                $inv_gross_bal_l = Appyinv_items::where('invoice_no', $inv->invoice_no)->where('payment_header_no', $inv->payment_header_no)
                                ->where('vendor_id', $inv->vendor_id)->sum('local_taxable_amount');
                            
                                #update invoice header
                                $invoice_upd = DB::table('ap_invoice_header')->where('invoice_no', $inv->invoice_no)
                                ->where('vendor_id', $inv->vendor_id)
                                    ->update([
                                    'foreign_allocated' => $invoice_head->foreign_allocated + $total_allo,
                                    'local_allocated' => $invoice_head->local_allocated + $total_allo_l,
                                    'foreign_unallocated' => $invoice_head->foreign_unallocated - $total_allo,
                                    'local_unallocated' => $invoice_head->local_unallocated - $total_allo_l,
                                    'foreign_unallocated_gross' => $invoice_head->foreign_unallocated_gross - $inv_gross_bal,
                                    'local_unallocated_gross' => $invoice_head->local_unallocated_gross - $inv_gross_bal_l
                                ]);
                            }
                        }
                    }
                }
            }
        }
    }

    function updateInvAmounts_prepapayments($inv, $pay_header_no)
    {
        $invoice_head = POInvoice_header::where('invoice_no', $inv->invoice_no)
        ->where('vendor_id', $inv->vendor_id)->get()[0];

        #update invoice details
        $items = Appyinv_items::where('invoice_no', $inv->invoice_no)->where('payment_header_no', $inv->payment_header_no)
        ->where('vendor_id', $inv->vendor_id)->get();

        if(count($items) > 0)
        {
            foreach ($items as $item) 
            {
                $inv_item = POInvoice_dtls::where('invoice_no', $item->invoice_no)->where('item_no', $item->item_no)
                ->where('vendor_id', $item->vendor_id)->get()[0];

                $item_upd = POInvoice_dtls::where('invoice_no', $item->invoice_no)->where('vendor_id', $item->vendor_id)
                ->where('item_no', $item->item_no)->update([
                    'allocated' => $inv_item->allocated + $item->applied_foreign_amount,
                    'local_allocated' => $inv_item->local_allocated + $item->applied_local_amount,
                    'unallocated' => $inv_item->unallocated - $item->applied_foreign_amount,
                    'local_unallocated' => $inv_item->local_unallocated - $item->applied_local_amount,
                    'item_bal' => $inv_item->item_bal - $item->foreign_taxable_amount,
                    'local_item_bal' =>  $inv_item->local_item_bal - $item->local_taxable_amount,
                ]); 
            }

            // $item_upd1 = POInvoice_dtls::where('invoice_no', $item->invoice_no)->where('vendor_id', $item->vendor_id)
            // ->pluck('allocated');

            $total_allo = Appyinv_items::where('invoice_no', $inv->invoice_no)->where('payment_header_no', $inv->payment_header_no)
            ->where('vendor_id', $inv->vendor_id)->sum('applied_foreign_amount');
            
            $total_allo_l = Appyinv_items::where('invoice_no', $inv->invoice_no)->where('payment_header_no', $inv->payment_header_no)
            ->where('vendor_id', $inv->vendor_id)->sum('applied_local_amount');

            // $inv_gross_bal = Appyinv_items::where('invoice_no', $inv->invoice_no)->where('payment_header_no', $inv->payment_header_no)
            // ->where('vendor_id', $inv->vendor_id)->sum('foreign_taxable_amount');

            // $inv_gross_bal_l = Appyinv_items::where('invoice_no', $inv->invoice_no)->where('payment_header_no', $inv->payment_header_no)
            // ->where('vendor_id', $inv->vendor_id)->sum('local_taxable_amount');

            if($invoice_head->unallocated - $total_allo > 0)
            {
                $invoice_status = '008';
            }

            else if($invoice_head->unallocated - $total_allo == 0)
            {
                $invoice_status = '007';
            }
            // dd($invoice_status, $invoice_head->unallocated );
        
            #update invoice header(PO invoice)
            $invoice_upd = POInvoice_header::where('invoice_no', $inv->invoice_no)
            ->where('vendor_id', $inv->vendor_id)
                ->update([
                'allocated' => $invoice_head->allocated + $total_allo,
                'local_allocated' => $invoice_head->local_allocated + $total_allo_l,
                'unallocated' => $invoice_head->unallocated - $total_allo,
                'local_unallocated' => $invoice_head->local_unallocated - $total_allo_l,
                // 'unallocated_gross' => $invoice_head->unallocated_gross - $inv_gross_bal,
                // 'local_unallocated_gross' => $invoice_head->local_unallocated_gross - $inv_gross_bal_l,
                'status' => $invoice_status
            ]);

            // $invoice_upd1 = POInvoice_header::where('invoice_no', $inv->invoice_no)
            // ->where('vendor_id', $inv->vendor_id)->first();

            // dd($invoice_upd1, $item_upd1);
        }
    }

    public function GlcreateBatch(Request $request)
    {
        $period_year = $request->period_year;
        $period_month1 = $request->period_month;
        $period_month = str_pad($period_month1, 2, 0, STR_PAD_LEFT);
        $batch_src = 'AP';

        $getsources = Glsource::where('source_code', $batch_src)->first();

        $new_batch = new GenerateBatch($period_year, $period_month, $batch_src);
        $batch_no = $new_batch->generate(); 
        // create new batch
        DB::beginTransaction();

        try {
            $new_batch = Batch_list::create([
                'batch_no' => $batch_no,
                'batch_source' => "AP",
                'batch_title' => $request->b_title,
                'batch_description' => $request->narration,
                'account_year' => $period_year,
                'account_month' => $period_month,
                'batch_type' => $request->batch_type,
                'created_date' => Carbon::now(),
                'created_by' => Auth::user()->user_name,
                'no_entries' => 0,
                'local_batch_amount' => 0,
                'expected_batch_total' => $request->exp_amount,
                'batch_status' => '001',
                'doc_type' => $request->batch_type == 'REV' ? 'PAY' : $request->batch_type
            ]);


            $upd_glsrc = Glsource::where('source_code', $batch_src)->update([
                'batch_serial' => $getsources->batch_serial + 1,
            ]);

            DB::commit();

            // return to batch entry page
            $dat = array(
                'main' => 'Accounts Payable',
                'module' => 'Ap Batch Processing',
                'submodule' => 'Ap Batch Details',
            );

            
            return redirect()->route('apbatch_entry', ['batch_no' => $new_batch->batch_no, 'period_year' => $new_batch->account_year, 'period_month' => $new_batch->account_month, 'batch_type' => $new_batch->batch_type])
                ->with('success', 'Batch created successfully');
        } catch (\Exception $e) {
            DB::rollback();
            Session::flash('error', 'An error Occured while creating batch!');
            return redirect()->back();
        }
    }

    function update_chq_and_certs($batch_no){
        /**WHT CERTS  and cheques*/
        $year = Carbon::now()->year;

        $batch_entries = Batch_entry::Where('batch_no', $batch_no)->get();
        
        foreach ($batch_entries as $entry) {
            $pay_header_no = $entry->entry_header;
            $payHeader = Appaymentheader::where('payment_header_no', $pay_header_no)->get()[0];

            //cbmastcheque
            $pay_method = Olpaymethd::whereRaw("trim(pay_method) = '" . $payHeader->payment_mode . "' ") -> get()[0];
       
            if($pay_method->cheque == 'Y' || $pay_method->eft == 'Y' || $pay_method->rtgs == 'Y')
            {
                if($payHeader->payment_reference != null)
                {
                    if($glbatch->original_batch == null)
                    {
                        $cbmastchq = Cbmastchq::create([
                            'offcd'=>$payHeader->office_code,
                            'doc_type'=>'PAY',
                            'entry_type_descr'=>'AP',
                            'reference'=>$payHeader->payment_header_no,
                            'line_no'=>0,
                            'pay_method'=>$payHeader->payment_mode,
                            'cheque_date'=>Carbon::now(),
                            'cheque_no'=>$payHeader->payment_reference,
                            'name'=>$payHeader->payee,
                            'amount'=>$payHeader->foreign_nett_amount,
                        ]);
                    }
                }
            }

            $py_invs = Appyinvoices::where('payment_header_no', $pay_header_no)->get();
            
            foreach ($py_invs as $inv) {
                if($payHeader->doc_type == 'PAY'){
                    if( $payHeader->prepayment == 'Y')
                    {
                        $invoice_header = POInvoice_header::where('vendor_id', $inv->vendor_id)
                        ->where('invoice_no', $inv->invoice_no)->first();
                    }

                    else
                    {
                        $invoice_header = DB::table('ap_invoice_header')->where('vendor_id', $inv->vendor_id)
                        ->where('invoice_no', $inv->invoice_no)->first();
                    }
                    /**check if invoice has wht */
                    $tax_grp = $invoice_header->tax_group;
                    
                    $taxtypes = DB::table('gltaxgroupdtl')->where('group_code', $tax_grp)->get();
                    
                    foreach ($taxtypes as $type) 
                    {
                        $gl_tax = DB::table('gltaxes')->where('tax_type', 'WHT')->where('tax_code', $type->tax_code)->first();
                    
                        $rate1 = $gl_tax->tax_rate;

                        if($rate1 > 0)
                        {
                            $check = Appytaxes::where('payment_header_no', $pay_header_no)->where('invoice_no', $inv->invoice_no)
                            ->where('vendor_id', $inv->vendor_id)->where('tax_code', $type->tax_code)->get()[0];
                            
                            if($check) 
                            {
                                $batch = ApWhtCertMast::where('unallocated', '>', 0)
                                ->orderBy('batch_no', 'asc')->get()[0];
                            
                                $wht_cert = ApWhtCertalloc::where('allocated', '<>', 'Y')->where('batch_no', $batch->batch_no)
                                ->where('cancelled', '<>', 'Y')->orderBy('cert_no', 'asc')->first();
                                
                                $wht_cert_no = $wht_cert->cert_no;
                                /*update whtcertmast and whtcertallo**/
                                $unallocated_certs = ApWhtCertalloc::where('allocated', '<>', 'Y')
                                ->where('batch_no', $wht_cert->batch_no)->count();
                                
                                $allocated_certs = ApWhtCertalloc::where('allocated', 'Y')
                                ->where('batch_no', $wht_cert->batch_no)->count();
                                
                                $upd_certmast = ApWhtCertMast::where('batch_no', $wht_cert->batch_no)
                                    ->update([
                                        'allocated' => $allocated_certs + 1,
                                        'unallocated' => $unallocated_certs - 1,
                                    ]);

                                $upd_wht_cert = ApWhtCertalloc::where('batch_no', $wht_cert->batch_no)
                                    ->where('cert_no', $wht_cert_no)
                                    ->update([
                                        'allocated' => 'Y',
                                        'allocated_by' => trim(auth()->id()),
                                        'date_allocated' => Carbon::now(),
                                    ]);

                                //$upd_invvoice
                                $inv_header = Appyinvoices::where('payment_header_no', $inv->payment_header_no)->where('invoice_no', $inv->invoice_no)
                                ->where('vendor_id', $inv->vendor_id)->update([
                                    'wht_cert_no' => $wht_cert_no
                                ]);
                            
                                ##upd nlmstdtl
                                $wht_tax_type = Gltaxtypes::where('tax_type', 'WHT')->get()[0];
                                
                                $upd = Nlmstdtl::where('batch_no', $batch_no)->where('reference', $pay_header_no )
                                    ->where('invoice_no',  $inv->invoice_no)->where('glhead', $wht_tax_type->glhead)
                                    ->update([
                                        'wht_cert_no' => $wht_cert_no
                                ]);
                            }
                        }
                    }
                }

                else if($payHeader->doc_type == 'REV'){
                    ##cancel wht cert no
                    $cert = ApWhtCertalloc::where('cert_no', trim($inv->wht_cert_no))->get()[0];
                    if ($cert) {
                        $upd_wht_cert = ApWhtCertalloc::where('cert_no', $cert->cert_no)->where('cancelled', '<>', 'Y')
                            ->update([
                                'cancelled' => 'Y',
                                'who_cancelled' => trim(auth()->id()),
                                'why_cancelled' => $reason,
                            ]);
                    }
                }
            }
        }
        /**End WHT CERTS */
    }

    public function updatePeriod($batch_no,$period_month,$period_year){
        $data = Batch_list::where('batch_no', $batch_no)->update([
            'account_month' => $period_month,
            'account_year' => $period_year
        ]);
        
    }
}
