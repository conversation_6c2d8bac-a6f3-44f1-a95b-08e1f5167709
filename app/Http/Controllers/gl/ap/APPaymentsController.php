<?php

namespace App\Http\Controllers\gl\ap;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\Datatables\Datatables;

use View;
use Session;
use Auth;
use DB;
use DateTime;
use Carbon\Carbon;
use Response;
use App\Apvendorinvoices;
use App\Appaymentheader;
use App\Apstatus;
use App\Bankaccounts;
use App\Apcrnotes;
use App\Apcrnotedetails;
use App\Currency;
use App\Olpaymethd;
use App\Nlparams;
use App\Banks;
use App\Apvendors;
use App\Appyinvoices;
use App\Appytaxes;
use App\Appyinv_items;
use App\Appyheadervendors;
use App\ApTransTypes;
use App\Nlctrl;
use App\Payreqst;
use App\AllowedTransactions;
use App\Payreqtaxes;
use App\ApWhtCertMast;
use App\ApWhtCertalloc;
use App\Chequemast;
use NumberFormatter;
use App\POInvoice_header;
use App\POInvoice_dtls;
use App\Apdoctypes;
use App\Nlslparams;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Validator;
use App\Eftmast;
use App\RTGSMast;
use App\Aims_process;
use App\Approvals;

class APPaymentsController extends Controller
{
    public function paymentHeaders()
    {
        $doctype = ApTransTypes::where('doc_type', 'PAY')->first();

        $acc_period = Nlctrl::first();

        $account_month = $acc_period->account_month;

        $account_year = $acc_period->account_year;

        return view::make('gl.ap.ap_pyheaders', compact('doctype', 'account_month', 'account_year'));
    }
    
    public function getPaymentheaders()
    {
        $headers = Appaymentheader::orderBy('created_date', 'DESC')->get();

        return Datatables::Of($headers)
        ->addColumn('action', function ($head) {
                return '<a data-class_id="'.$head->payment_header_no.'" class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })

        ->addColumn('effective_date1', function ($head){
                $date = new DateTime($head->effective_date);
                $format_date =  $date->format('d/m/Y');
                return $format_date;
            })

        ->addColumn('payment_mode', function ($head){

                $mode = Olpaymethd::whereRaw("trim(pay_method) = '" . $head->payment_mode . "' ")->first();
                return $mode->description;
            })

        ->addColumn('currency_descr', function  ($head){
                $currency = Currency::where('currency_code', $head->currency_code)->first();
                return $currency->description;
            })

        ->editColumn('foreign_payment_amount', function ($head){
                $foreign_payment_amount = number_format($head->foreign_payment_amount, 2);
                return $foreign_payment_amount;
            })

        ->editColumn('local_payment_amount', function ($head){
                $local_payment_amount = number_format($head->local_payment_amount, 2);
                return $local_payment_amount;
            })
            ->make(true);
    }

    public function documentTypes(Request $request)
	{
        $old_doc = trim($request->edit_doc_type);
        $old_doc_type = ApTransTypes::whereRaw("trim(doc_type) = '" . $old_doc . "' ")->first();
        
        $all_doc_types = ApTransTypes::whereNotIn('doc_type', [$old_doc])->get();
        // dd($all_doc_types);
		return ['old_doc_type' => $old_doc_type, 'all_doc_types' => $all_doc_types];
	}

    public function  getCrvendordetails(Request $request)
    {
		$effective_date = $request->effective_date;

        $vendor_id = trim($request->vendor_id);

        $vendor = Apvendors::where('vendor_id', $vendor_id)->first();

        $default_currency_code = $vendor->base_currency;
        $default_currency =  Currency::where('currency_code', $default_currency_code)->first();
		$currencies = Currency::whereNotIn('currency_code', [ $default_currency_code])->get();

    
        if($default_currency->base_currency == "Y")
        {
            $currency_rate = 1;
        }

        if($default_currency->base_currency == "N")
        {
            $curr_rate = DB::table('currrate')->where('currency_code', $default_currency_code)
												->where('rate_date', $effective_date)->first();
			if($curr_rate)
			{
				$currency_rate = $curr_rate->currency_rate;
			}

			else
			{
				$err = "No rate";
			}
		}

        return ['err' => $err, 'currency_rate' => $currency_rate, 'currencies' => $currencies, 'default_currency' => $default_currency];
    }

    public function getPyoffices(Request $request)
    {
        if($request->edit_office_code)
        {
            $edit_office_code = trim($request->edit_office_code);

            $old_office_code = Nlparams::where('prid', 'OFF')->whereRaw("trim(prsno) = '" . $edit_office_code . "' ")->first();

            $offices = Nlparams::where('prid', 'OFF') 
                            ->whereNotIn('prsno', [$edit_office_code])
                            ->get();
            
            return ['old_office_code' => $old_office_code, 'offices' => $offices];
        }

        else
        {
            $office_codes = Nlparams::where('prid', 'OFF')->get();
            return $office_codes;
        }
    }

    public function getPycurrency(Request $request)
    {
        $default_curr = Currency::where('base_currency', 'Y')->first();

        $default_currency_code = trim($default_curr->currency_code);

        $currencies = Currency::whereNotIn('currency_code', [ $default_currency_code])->get();

        return ['currencies' => $currencies, 'default_curr' => $default_curr];
    }

    public function pyModes(Request $request)
    {
        // if($request->edit_payment_mode)
        // {
        //     $edit_pay_method = trim($request->edit_payment_mode);

        //     $old_pay_method = Olpaymethd::whereRaw("trim(pay_method) = '" . $edit_pay_method . "' ")->first(); 
            
        //     $paymodes = Olpaymethd::whereNotIn('pay_method', [$edit_pay_method])->get();

        //     $ref_flag = $old_pay_method->refrence_flag;
            
        //     return ['ref_flag' => $ref_flag, 'old_pay_method' => $old_pay_method, 'paymodes' => $paymodes];
        // }

        // if($request->select_edit_pay_method)
        // {
        //     $pay_method = trim($request->select_edit_pay_method);

        //     $mode = Olpaymethd::whereRaw("trim(pay_method) = '" . $pay_method . "' ")->first();

        //     $select_ref_flag = $mode->refrence_flag;
        //     return response()->json($select_ref_flag);
        // }

        if($request->has('pay_method') && $request->has('bank_acc_code') && $request->has('currency_code'))
        {
            $currency = Currency::whereRaw("trim(currency_code) = '".$request->currency_code."' ")->get()[0];
            
            $pay_method = trim($request->pay_method);

            $mode = Olpaymethd::whereRaw("trim(pay_method) = '" . $pay_method . "' ")->first();

            // $cheque = '';

            $ref_flag = $mode->refrence_flag;
            // dd($pay_method,$mode,$mode->cheque);
            if($mode->cheque == 'Y' && $ref_flag == 'Y')
            {
            
                $cheque_no = Chequemast::where('bank_acc_code',trim($request->bank_acc_code))
                ->whereRaw("(cheque_cancelled = 'N' or cheque_cancelled is null) and (cheque_issued = 'N' or cheque_issued is null) ")
                ->selectRaw("min(to_number(cheque_no)) as cheque_no")
                ->get()[0];
               
               $cheque = 'Y';
            }else if($mode->eft == 'Y' && $ref_flag == 'Y')
            {
          
                $cheque_no = Eftmast::where('bank_acc_code',trim($request->bank_acc_code))
                ->whereRaw("(eft_cancelled = 'N' or eft_cancelled is null) and (eft_issued = 'N' or eft_issued is null) ")
                ->selectRaw("min(to_number(eft_no)) as cheque_no")
                ->get()[0];
             
            // $cheque = 'Y';
               $cheque = 'N';
            }else if($mode->rtgs == 'Y' && $ref_flag == 'Y')
            {
         
                $cheque_no = RTGSMast::where('bank_acc_code',trim($request->bank_acc_code))
                ->whereRaw("(rtgs_cancelled = 'N' or rtgs_cancelled is null) and (rtgs_issued = 'N' or rtgs_issued is null) ")
                ->selectRaw("min(to_number(rtgs_no)) as cheque_no")
                ->get()[0];
                
            // $cheque = 'Y';
               $cheque = 'N';
            }

            else
            {
                $cheque = 'N';

                $cheque_no = null;
            }
            return ['ref_flag'=>$ref_flag, 'cheque'=>$cheque, 'cheque_no'=>$cheque_no->cheque_no, 'currency'=>$currency];
        }

        else
        {
            $paymodes = Olpaymethd::all();
            return $paymodes;
        }
    }

    public function getPycurrencyrate(Request $request)
	{
		if($request->edit_currency_code && $request->edit_effective_date)
		{
			$edit_currency_code = trim($request->edit_currency_code);

			$edit_effective_date =  $request->edit_effective_date;

			$base_currency =  Currency::where('currency_code', $edit_currency_code)
									->where('base_currency', 'Y')->first();
			
			if($base_currency)
			{
				$edit_currency_rate = 1;
			}

			else
			{
				$curr_rate = DB::table('currrate')->where('currency_code', $edit_currency_code)
                                                ->where('rate_date', $edit_effective_date)->first();
                                 
													
				if($curr_rate)
				{
					$edit_currency_rate = $curr_rate->currency_rate;
				}

				else
				{
					$err = "No rate";
				}
			}

			return ['err' => $err, 'edit_currency_rate' => $edit_currency_rate];
		}

        $currency_code = trim($request->selected_currency);

        $effective_date =  $request->effective_date;

		$base_currency =  Currency::where('currency_code', $currency_code)
                                ->where('base_currency', 'Y')->first();
									
        if($base_currency)
        {
            $currency_rate = 1;
		}
		
		else
		{
			$curr_rate = DB::table('currrate')->where('currency_code', $currency_code)
                                            ->where('rate_date', $effective_date)->first();
                                                
			if($curr_rate)
			{
                $currency_rate = $curr_rate->currency_rate;
			}

			else
			{
				$err = "No rate";
            }
        }    
        return ['currency_rate' => $currency_rate, 'err' => $err];
    }

    public function getPyvendorids(Request $request)
	{
		$vendors = Apvendors::all();
		return $vendors;
    }
    
    public function getPybanks(Request $request)
    {
        if($request->edit_bank_acc_code && $request->edit_payment_mode)
        {
            $pay_mode = trim($request->edit_payment_mode);

            $bank_acc_code = trim($request->edit_bank_acc_code);

            $old_glbank = DB::table('glbanks')->where('prid', 'BNK') 
                    ->whereRaw("trim(bank_acc_code) = '" . $bank_acc_code . "' ")->first();

            $other_glbnks = DB::table('glbanks')->where('prid', 'BNK') 
                            ->whereNotIn('bank_acc_code', [$bank_acc_code])
                            ->get();

            $bank_code = $old_glbank->bank_code;

            $branch_code = $old_glbank->bank_branch_code;

            $bank = Banks::whereRaw("trim(bank_code) = '" . $bank_code . "' ")->first();

            $branch = DB::table('bankbranches')->where('branch_code', $branch_code)
                        ->whereRaw("trim(bank_code) = '" . $bank_code . "' ")->first();

            $old_pay_method = Olpaymethd::whereRaw("trim(pay_method) = '" . $pay_mode . "' ")->get()[0]; 

            $ref_flag = $old_pay_method->refrence_flag;

            $grp_code = $old_glbank->group_code;
            
            $allowed_pay_methods = AllowedTransactions::whereRaw("trim(group_code) = '" . $grp_code . "' ") 
                                ->whereNotIn('pay_mode', [$pay_mode])->pluck('pay_mode');

            $paymodes = [];

            foreach ($allowed_pay_methods as $method) 
            {
                $paymode = Olpaymethd::whereRaw("trim(pay_method) = '" . $method . "' ")->get()[0];
                array_push($paymodes, $paymode);
            }

            return  [
                        'ref_flag' => $ref_flag, 
                        'old_pay_method' => $old_pay_method, 
                        'paymodes' => $paymodes,
                        'branch' => $branch, 
                        'old_glbank' => $old_glbank, 
                        'bank' => $bank, 
                        'other_glbnks' => $other_glbnks
                    ];
        }

        // $banks  = Glbanks::where('prid', 'BNK')->get();
        $banks  = DB::table('glbanks') ->where('prid', 'BNK')->get();
        return $banks;
    }

    public function getPybankDetails(Request $request)
    {
        if($request->bank_acc_code)
        {
            $bank_acc_code = trim($request->bank_acc_code);

            $gl_bank = DB::table('glbanks')->where('prid', 'BNK')->whereRaw("trim(bank_acc_code) = '" . $bank_acc_code . "' ")->first();
            
            $bank_code = $gl_bank->bank_code;

            $branch_code = $gl_bank->bank_branch_code;

            $grp_code = $gl_bank->group_code;

            $allowed_pay_methods = AllowedTransactions::whereRaw("trim(group_code) = '" . $grp_code . "' ")->pluck('pay_mode');

            $paymodes = [];

            foreach ($allowed_pay_methods as $method) 
            {
                $paymode = Olpaymethd::whereRaw("trim(pay_method) = '" . $method . "' ")->get()[0];
                array_push($paymodes, $paymode);
            }

            $bank = Banks::whereRaw("trim(bank_code) = '" . $bank_code . "' ")->first();

            $branch = DB::table('bankbranches')->where('branch_code', $branch_code)
            ->whereRaw("trim(bank_code) = '" . $bank_code . "' ")->first();

            $slheads = Nlslparams::whereRaw("trim(glhead) = '" . $gl_bank->prsno . "' ")->where('status', 'A')->get(['slhead', 'name', 'glhead', 'sub_account_code']);
                        
            return [
                'branch' => $branch, 'gl_bank' => $gl_bank, 
                'bank' => $bank, 'paymodes' => $paymodes,
                'slheads' => $slheads
            ];
        }

        if($request->edit_bank_acc_code)
        {
            $bank_acc_code = trim($request->edit_bank_acc_code);

            $gl_bank = DB::table('glbanks')->where('prid', 'BNK')->whereRaw("trim(bank_acc_code) = '" . $bank_acc_code . "' ")->first();
            
            $bank_code = $gl_bank->bank_code;

            $branch_code = $gl_bank->bank_branch_code;

            $bank = Banks::whereRaw("trim(bank_code) = '" . $bank_code . "' ")->first();

            $branch = DB::table('bankbranches')->where('branch_code', $branch_code)
                        ->whereRaw("trim(bank_code) = '" . $bank_code . "' ")->first();

            $grp_code = $gl_bank->group_code;

            $allowed_pay_methods = AllowedTransactions::whereRaw("trim(group_code) = '" . $grp_code . "' ")->pluck('pay_mode');

            $paymodes = [];

            foreach ($allowed_pay_methods as $method) 
            {
                $paymode = Olpaymethd::whereRaw("trim(pay_method) = '" . $method . "' ")->get()[0];
                array_push($paymodes, $paymode);
            }
            return ['branch' => $branch, 'gl_bank' => $gl_bank, 'bank' => $bank, 'paymodes' => $paymodes];
        }
    }

    public function postPymentheader(Request $request)
    {
        $this->validate($request, [
            'foreign_payment_amount' => 'required',
            'bank_code' => 'required',
            'payment_mode' => 'required',
            'currency_code' => 'required',
            'office_code' => 'required',
            // 'payment_reference' => 'required',
            'currency_rate' => 'required',
            'effective_date' => 'required',
            'account_month' => 'required',
			'account_year' => 'required',
            'doc_type' => 'required',
            'bank_acc_code' => 'required',
            'bank_branch_code' => 'required',
            'gl_account' => 'required',
            'bank_account_no' => 'required',
            'account_name' => 'required',
            'narration' => 'required|min:3',
            'vendor_id'=>'required',
            'payee'=>'required',
            'dept' => 'required',
            'prepayment' => 'required',
            // 'slhead' => 'required'
        ]);

        $currency_rate = $request->currency_rate;

        $foreign_payment_amnt = $request->foreign_payment_amount;
        $foreign_payment_amount = str_replace(',', '', $foreign_payment_amnt);

        $local_payment_amount = $foreign_payment_amount * $currency_rate;

        $today = Carbon::today();

        $year = $today->format('Y');

        $serial_no = $this->generate_payment_serial();
            
        $payment_header_no = 'PAY' . $year . STR_PAD($serial_no, 6, '0', STR_PAD_LEFT);

        $curr = Currency::where('currency_code', $request->currency_code)->get()[0];

        $amnt_words = numberTowords(0);

        $vendor = Apvendors::where('vendor_id', $request->vendor_id)->first();

        $pay_method = Olpaymethd::where('pay_method', $request->payment_mode)->first();

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try 
        {
            if($vendor->approved == 0)
            {
                return response()->json(['status' => -1]);
            }

            else if($vendor->approved == 1)
            {
                ##update chequemast
                if(!empty($request->cheque_no))
                {
                    ##check if currency is foreign
                    if($curr->base_currency == 'N')
                    {
                        return response()->json(['status' => -2, 'currency'=>$curr]);
                    }

                    elseif ($curr->base_currency == 'Y')
                    {
                        $cheque_no = ltrim( $request->cheque_no, 0 );
                       
                    
                        if($pay_method->cheque == "Y") {

                            $upd = Chequemast::where('bank_acc_code', trim($request->bank_acc_code))
                            ->where('cheque_no', $cheque_no)
                            ->update([
                                'cheque_issued' => 'Y',
                                'date_issued' => Carbon::now()
                            ]);
    
    
                        }else if($pay_method->eft == "Y") {
    
                            $upd = Eftmast::where('bank_acc_code', trim($request->bank_acc_code))
                            ->where('eft_no', $cheque_no)
                            ->update([
                                'eft_issued' => 'Y',
                                'date_issued' => Carbon::now()
                            ]);
    
                        }else if($pay_method->rtgs == "Y") {

                            $upd = RTGSMast::where('bank_acc_code', trim($request->bank_acc_code))
                            ->where('rtgs_no', $cheque_no)
                            ->update([
                                'rtgs_issued' => 'Y',
                                'date_issued' => Carbon::now()
                            ]);
    
                        }



                    }
                }

                ##allow payment in foreign currency if payment method is EFT
                if ($curr->base_currency == 'Y')
                {
                    $foreign_pay = 'N';
                }
         
                else if($curr->base_currency == 'N')
                {
                    $foreign_pay = 'Y';
                }
                ##end allow payment in foreign currency if payment method is EFT

                $post_py = Appaymentheader::create([
                    'payment_header_no' => $payment_header_no,
                    'bank_code' => trim($request->bank_code),
                    'payment_mode' => trim($request->payment_mode),
                    'payment_reference' => $request->payment_reference,
                    'local_payment_amount' => $local_payment_amount,
                    'foreign_payment_amount' => $foreign_payment_amount,
                    'local_nett_amount' => 0,
                    'foreign_nett_amount' => 0,
                    'currency_code' => trim($request->currency_code),
                    'currency_rate' => $currency_rate,
                    'effective_date' => $request->effective_date,
                    'foreign_unallocated_amount' => $foreign_payment_amount,
                    'local_unallocated_amount' => $local_payment_amount,
                    'foreign_applied_amount' => 0,
                    'local_applied_amount' => 0,
                    'created_by' => trim(auth()->id()),
                    'created_date' => Carbon::now(),
                    'py_header_status' => '001',
                    'office_code' => trim($request->office_code),
                    'account_month' => $request->account_month,
                    'account_year' => $request->account_year,
                    'doc_type' => $request->doc_type,
                    'bank_acc_code' => trim($request->bank_acc_code),
                    'bank_branch_code' => trim($request->bank_branch_code),
                    'bank_account_no' =>  trim($request->bank_account_no),
                    'gl_account' =>  trim($request->gl_account),
                    'account_name' =>  trim($request->account_name),
                    'narration' => $request->narration,
                    'payee'=>$request->payee,
                    'vendor_id' => $request->vendor_id,
                    'amnt_in_words' => $amnt_words,
                    'dep_code' => trim($request->dept),
                    'prepayment' => $request->prepayment,
                    // 'payee_bank_code' => trim($request->payee_bank_code),
                    'payee_country' => $request->payee_country,
                    'payee_bank_name' => $request->payee_bank_name,
                    'payee_bnkbranch_code' => trim($request->payee_bnkbranch_code),
                    'payee_bnkbranch_name' => $request->payee_bnkbranch_name,
                    'payee_account_name' => $request->payee_account_name,
                    'payee_account_no' => trim($request->payee_account_no),
                    'swift_code' => trim($request->swift_code),
                    'foreign_pay_flag' => $foreign_pay,
                    // 'slhead_flag' => $request->slhead_flag,
                    // 'slhead' => $request->slhead
                ]);

                $this->upd_payment_serial();
                DB::connection(env('DB_CONNECTION'))->commit();
                DB::connection(env('DB_CONNECTION1'))->commit();
                return response()->json(['status' => 1, 'payment_header_no' => $payment_header_no]);
            }
        } 
        
        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();

            $error_msg = json_encode($e->getMessage());
            $reference = "vendor_id: {$request->vendor_id},Payee {$request->vendor_id}";
            $module = __METHOD__;
            $route_name = Route::getCurrentRoute()->getActionName();

            log_error_details($route_name,$error_msg,$reference,$module);
            return response()->json(['status' => 0]);
        }
    }

    public function updatePymentheader(Request $request)
    {
        $this->validate($request, [
            'edit_foreign_payment_amount' => 'required',
            'edit_bank_code' => 'required',
            'edit_payment_mode' => 'required',
            'edit_currency_code' => 'required',
            'edit_currency_rate' => 'required',
            'edit_effective_date' => 'required',
            'edit_office' => 'required',
            'edit_account_month' => 'required',
			'edit_account_year' => 'required',
            'edit_doc_type' => 'required',
            'edit_bank_acc_code' => 'required',
            'edit_bank_branch_code' => 'required',
            'edit_gl_account' => 'required',
            'edit_bank_account_no' => 'required',
            'edit_account_name' => 'required',
            'edit_narration' => 'required|min:3',
            'dept' => 'required'
        ]);

        $payment_header_no = trim($request->edit_payment_header_no);

        $currency_rate = $request->edit_currency_rate;

        $foreign_payment_amnt = $request->edit_foreign_payment_amount;
        $foreign_payment_amount = str_replace(',', '', $foreign_payment_amnt);

        $local_payment_amount = $foreign_payment_amount * $currency_rate;

        $total_foreign_applied_amount = Appyinvoices::where('payment_header_no', $payment_header_no) 
        ->sum('foreign_allocated_amount');

        $total_local_applied_amount = Appyinvoices::where('payment_header_no', $payment_header_no) 
        ->sum('local_allocated_amount');
        
        $total_foreign_unallocated_amount = $foreign_payment_amount - $total_foreign_applied_amount; 
        
        $total_local_unallocated_amount = $local_payment_amount - $total_local_applied_amount;

        $foreign_py_taxes = Appyinvoices::where('payment_header_no', $payment_header_no)
                                    ->sum('foreign_total_taxes');

        $local_py_taxes = Appyinvoices::where('payment_header_no', $payment_header_no)
                                ->sum('local_total_taxes');

        $py_nett_amount_f1 =  Appyinvoices::where('payment_header_no', $payment_header_no)
        ->sum('foreign_nett_amount');
        
        $py_nett_amount_l1 =  Appyinvoices::where('payment_header_no', $payment_header_no)
        ->sum('local_nett_amount');

        $py_nett_amount_l = number_format((float)$py_nett_amount_l1, 2, '.', '');
        $py_nett_amount_f = number_format((float)$py_nett_amount_f1, 2, '.', '');

        $curr = Currency::where('currency_code', $request->edit_currency_code)->get()[0];

        $amnt_words = numberTowords($py_nett_amount_f);

        $update_header = Appaymentheader::where('payment_header_no', $payment_header_no)
        ->update([
            'bank_code' => trim($request->edit_bank_code),
            'payment_mode' => trim($request->edit_payment_mode),
            'payment_reference' => $request->edit_payment_reference,
            'local_payment_amount' => $local_payment_amount,
            'foreign_payment_amount' => $foreign_payment_amount,
            'currency_code' => trim($request->edit_currency_code),
            'currency_rate' => $currency_rate,
            'effective_date' => $request->edit_effective_date,
            'changed_by' => trim(auth()->id()),
            'dola' => Carbon::now(),
            'foreign_unallocated_amount' => $total_foreign_unallocated_amount,
            'local_unallocated_amount' =>  $total_local_unallocated_amount,
            'foreign_applied_amount' => $total_foreign_applied_amount,
            'local_applied_amount' =>  $total_local_applied_amount,
            'office_code' => trim($request->edit_office),
            'account_month' => $request->edit_account_month,
            'account_year' => $request->edit_account_year,
            // 'doc_type' => $request->edit_doc_type,
            'foreign_nett_amount' => $py_nett_amount_f,
            'local_nett_amount' => $py_nett_amount_l,
            'bank_acc_code' => trim($request->edit_bank_acc_code),
            'bank_branch_code' => trim($request->edit_bank_branch_code),
            'bank_account_no' =>  trim($request->edit_bank_account_no),
            'gl_account' =>  trim($request->edit_gl_account),
            'account_name' =>  trim($request->edit_account_name),
            'narration' => $request->edit_narration,
            'amnt_in_words' => $amnt_words,
            'dep_code' => trim($request->dept)
        ]);
        return response()->json(['status' => 1, 'payment_header_no' => $payment_header_no]);
    }

    public function pyheaderDetails(Request $request)
    {   
        $pyheader = Appaymentheader::where('payment_header_no', trim($request->payment_header_no))->first();

        $py_status_code = $pyheader->py_header_status;

        $py_status = Apstatus::where('status_code', $py_status_code)->first();

        $currency =  Currency::where('currency_code', $pyheader->currency_code)->first();

        $bank_code = $pyheader->bank_code;

        $bank_acc_code = $pyheader->bank_acc_code;

        $glbank = DB::table('glbanks')->where('prid', 'BNK') 
        ->whereRaw("trim(bank_acc_code) = '" . $bank_acc_code . "' ")->first();

        //$bank = Nlparams::where('prid', 'BNK')->whereRaw("trim(prsno) = '" . $bank_code . "' ")->first();

        $payment_mode = Olpaymethd::whereRaw("trim(pay_method) = '" . $pyheader->payment_mode . "' ")->first();
		
        $format_effective_date = new DateTime($pyheader->effective_date);
        
		$effective_date =  $format_effective_date->format('d/m/Y');

        $vendor = Apvendors::whereRaw("trim(vendor_id) = '" . $pyheader->vendor_id . "' ")->first();

        $doctype = ApTransTypes::where('doc_type', 'INV')->first();

        $acc_period = Nlctrl::first();

        $account_month = $acc_period->account_month;

        $account_year = $acc_period->account_year;

       // AP Payment approvals
       $user_id = trim(Auth::user()->user_id);
       $approval_process = Aims_process::where('slug', 'ap-payments-approval')->first();

       if ($approval_process) {
            $process_code = trim($approval_process->process_code);

            $approval_process = Aims_process::with(['process_dtl', 'approval_levels'])
                ->where('process_code', $process_code)
                ->first();

                if ($approval_process) {
                    $filteredApprovalLevels = $approval_process->approval_levels->filter(function ($level) use ($pyheader) {
                        return $pyheader->local_nett_amount >= $level->min_limit;
                    });
                
                    if ($filteredApprovalLevels->count() < 2) {
                        $filteredApprovalLevels = $approval_process->approval_levels->take(2);
                    }
                
                    $approval_process->setRelation('approval_levels', $filteredApprovalLevels);
                }
                
       }
        // fetch approvals if any
        $approval_dtl = Approvals::with('approval_flow')
                                    ->where('req_no',trim($pyheader->payment_header_no))
                                    ->where('type', 'APAPR')
                                    ->orderBy('date_created','DESC')
                                    ->first();

        $check_approval = Approvals::where('req_no',trim($pyheader->payment_header_no))
                                    ->where('type', 'APAPR')
                                    ->orderBy('date_created','DESC')
                                    ->first();


        if(isset($check_approval)){
            if($check_approval->status == 'A'){
                $status = 'A';              
            }
            elseif($check_approval->status == 'R'){
                $status = 'R';
                $msg = 'Payment Approval was rejected';
            }
            elseif($check_approval->status == 'P'){
                $status = 'P';
                $msg = 'Payment has a Pending approval';
            }
        }

        $approval_status = $status ;
        $approval_msg = $msg;


        return view::make('gl.ap.ap_pyheaderdetails', compact('glbank','py_status', 'dat', 'pyheader', 'effective_date', 
        'currency', 'payment_mode', 'account_year', 'account_month', 'doctype', 'vendor','process','approval_process','approval_dtl','approval_status_2','approval_msg_2','approval_status'));
    }

    public function getPyvendorbanks(Request $request)
	{
        $vendor_id = trim($request->vendor_id);

        $payment_header_no = trim($request->payment_header_no);

        $vendor_exist = Appyinvoices::where('vendor_id', $vendor_id)->where('payment_header_no', $payment_header_no)->first();

        if($vendor_exist)
        {
            $vendor_exist = 'Y';
        }

        else
        {
            $vendor_exist = 'N';
        }

        $vendor = Apvendors::where('vendor_id', $vendor_id)->first();

        $default_bank = Bankaccounts::where('entity_id', $vendor_id)->where('default_flag', 'Y')->where('status_code', 1)->first();

        $all_banks = Bankaccounts::where('entity_id', $vendor_id)->where('entity_type', 'VEN')
        ->where('status_code', 1)->get();

        $banks = array();

        foreach ($all_banks as $bnk) {
            $bank = Banks::whereRaw("trim(bank_code) = '" . $bnk->bank_code . "' ")->first();
            if($bank){
                array_push( $banks, $bank);
            }
        }
        return ['default_bank' => $default_bank->bank_code, 'vendor_exist' => $vendor_exist, 'banks' => $banks];
    }

    public function pyHeadervendors(Request $request)
    {
        $payment_header_no = trim($request->payment_header_no);
    
        $vendors = Appyheadervendors::where('payment_header_no', $payment_header_no)->get();
    
        return Datatables::Of($vendors)
        ->addColumn('action', function ($vendor) {
                return '<a data-class_id="'.$vendor->payment_header_no.'" class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a data-class_id="'.$head->payment_header_no.'" class="btn btn-xs" id="btn-view"><i class="glyphicon glyphicon-eye-open"></i></a>';
            })

        ->addColumn('vendor_name', function ($vendor){
                $ven = Apvendors::where('vendor_id', $vendor->vendor_id)->first();
                return $ven->vendor_name;
            })

            ->make(true);


    }

    public function viewPyinvoicedetails(Request $request)
    {
        $payment_header_no = trim($request->payment_header_no);

        $vendor_id = trim($request->vendor_id);

        $invoice_no = trim($request->invoice_no);

        $payment_header = Appaymentheader::where('payment_header_no', $payment_header_no)->first();

        $vendor = Apvendors::where('vendor_id', $vendor_id)->first();

        $py_invoice = Appyinvoices::where('vendor_id', $vendor_id)->where('payment_header_no', $payment_header_no)
        ->where('invoice_no', $invoice_no)->first();

        $control_account = Nlparams::where('prid', 'GLH')->whereRaw("trim(prsno) = '" .$py_invoice->control_account. "' ")->first();

        $slparam = Nlslparams::whereRaw("trim(slhead) = '" . $py_invoice->slhead . "' ")->first();
        
        return view::make('gl.ap.ap_pyinvdetails', compact('vendor', 'py_invoice', 'payment_header', 'slparam', 'control_account'));
    }

    public function getPyinvoices(Request $request)
    {
        $payment_header_no = trim($request->payment_header_no);

        $invoices = Appyinvoices::where('payment_header_no', $payment_header_no)->get();
        return Datatables::Of($invoices)
        ->addColumn('action', function ($invoice) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
            })

        ->editColumn('foreign_invoice_amount', function ($invoice){
                $foreign_invoice_amount = number_format($invoice->foreign_invoice_amount, 2);
                return $foreign_invoice_amount;
            })

        ->editColumn('foreign_allocated_amount', function ($invoice){
                $foreign_allocated_amount = number_format($invoice->foreign_allocated_amount, 2);
                return $foreign_allocated_amount;
            })

        ->addColumn('vendor_name', function ($vendor){
                $ven = Apvendors::where('vendor_id', $vendor->vendor_id)->first();
                return $ven->vendor_name;
            })

            ->make(true);
    }

    public function viewPyinvitemdetails(Request $request)
    {
        $invoice_no = trim($request->invoice_no);
        $vendor_id = trim($request->vendor_id);
        $item_no = trim($request->item_no);
        $payment_header_no = trim($request->payment_header_no);

        $item = Appyinv_items::where('invoice_no', $invoice_no)->where('item_no', $item_no)->where('vendor_id', $vendor_id)
        ->where('payment_header_no', $payment_header_no)->first();
        
        $vendor = Apvendors::where('vendor_id', $vendor_id)->first();

        $control_account = Nlparams::where('prid', 'GLH')->whereRaw("trim(prsno) = '" .$item->item_control_account. "' ")->first();

        $slparam = Nlslparams::whereRaw("trim(slhead) = '" . $item->slhead . "' ")->first();

        return view::make('gl.ap.ap_pyinvitem_details', compact('item', 'vendor', 'slparam', 'control_account')); 
    }

    public function  fetchPyveninvoices(Request $request)
    {
        $invoice_arr = $request->invoice_no;

        $vendor_id = trim($request->vendor_id);

        $payment_header_no = trim($request->payment_header_no);

        $payment_header = Appaymentheader::where('payment_header_no', $payment_header_no)->first();

        $currency_code = $payment_header->currency_code;
        
        ##if prepayment, return invoices from po
        if($payment_header->prepayment == 'Y')
        {
            $result = POInvoice_header::where('vendor_id', $vendor_id)
            ->whereIn('status', ['001', '008'])->whereIn('currency_code', [$currency_code])->get();
        }

        else
        {
            $result = DB::table('ap_invoice_header')->where('vendor_id', $vendor_id)
            ->whereIn('invoice_status', ['004'])->whereIn('currency_code', [$currency_code])->get();
        }

        if(!$request->has('invoice_no')){
            return $result;
        }

        else{
            $vendor_invoices = [];
            foreach ($result as $inv) 
            {
                if(!in_array($inv->invoice_no, $invoice_arr))
                {
                    if($payment_header->prepayment == 'Y')
                    {
                        $description = $inv->invoice_descr;
                    }

                    else
                    {
                        $description = $inv->invoice_description;
                    }
                array_push( $vendor_invoices, ['invoice_no' => $inv->invoice_no, 'description'=>$description]);
                }
            }                  
            return $vendor_invoices;
        }
    }

    public function  pyvenInvoicedetails(Request $request)
    {
        $vendor_id = trim($request->vendor_id);

        $payment_header_no = trim($request->payment_header_no);

        $invoice_no = trim($request->selected_invoice_no);

        $payment_header = Appaymentheader::where('payment_header_no', $payment_header_no)->get()[0];
        
        if($payment_header->prepayment == 'Y')
        {
            $vendor_invoice =POInvoice_header::where('vendor_id', $vendor_id)->where('invoice_no', $invoice_no)
            ->get()[0];
        }

        else
        {
            $vendor_invoice = DB::table('ap_invoice_header')->where('vendor_id', $vendor_id)->where('invoice_no', $invoice_no)
            ->get()[0];
        }

        #check if invoice exists on payment header 
        $invoice_exist = Appyinvoices::where('payment_header_no', $payment_header_no)->where('invoice_no', $invoice_no)
        ->where('vendor_id', $vendor_id)->get();

        if(count($invoice_exist) > 0)
        {
            $exists = 'Y';
            return [ 'vendor_invoice' => $vendor_invoice, 'exists' => $exists];
        }

        else
        {
            $exists = 'N';
            return ['vendor_invoice' => $vendor_invoice, 'exists' => $exists];
        }
    }

    public function postPyvendorinv(Request $request)
    {
        $input = $request->all();

        $this->validate($request, [
            'vendor_id' => 'required',
            'invoice_no' => 'required',
            'foreign_invoice_amount' => 'required',
            'foreign_allocated_amount' => 'required',
            // 'vendor_bank_code' => 'required',
            'foreign_allocated_amount' => 'required'
        ]);

        $vendor_id = trim($request->vendor_id);

        $payment_header_no = trim($request->payment_header_no);

        $payment_header = Appaymentheader::where('payment_header_no', $payment_header_no)->first();

        $payment_mode = $payment_header->payment_mode;

        $payment_reference = $payment_header->payment_reference;

        $foreign_payment_amount = $payment_header->foreign_payment_amount;

        $local_payment_amount = $payment_header->local_payment_amount;

        $header_currency_rate = $payment_header->currency_rate;

        $vendor_bank_code = trim($request->vendor_bank_code);

        $condition = $input['invoice_no'];

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            if($payment_header->prepayment == 'Y')
            {
                $this->prepaymentInvoice($request->all(),  $vendor_id, $payment_header, $payment_header_no);
            }

            else{
                foreach ($condition as $key => $cond) 
                {
                    $invoice_no = $input['invoice_no'][$key];

                    $invoice_header = DB::table('ap_invoice_header')->where('vendor_id', $vendor_id) 
                    ->where('invoice_no', $invoice_no)->first();

                    $invoice_currency_rate = $invoice_header->currency_rate;

                    $invoice_currency_code = $invoice_header->currency_code;
                    
                    $foreign_allocated_amnt =$input['foreign_allocated_amount'][$key];
                    $foreign_allocated_amount = str_replace(',', '', $foreign_allocated_amnt);

                    $local_allocated_amount = $foreign_allocated_amount * $invoice_currency_rate;

                    $foreign_invoice_amount = $invoice_header->foreign_unallocated;
                
                    $local_invoice_amount = $invoice_header->local_unallocated;

                    $foreign_unallocated_amount = $foreign_invoice_amount - $foreign_allocated_amount;

                    $local_unallocated_amount = $local_invoice_amount - $local_allocated_amount;
            
                    $original_foreign_total_taxes =  $invoice_header->foreign_total_taxes;

                    $original_local_total_taxes =  $invoice_header->total_taxes;

                    $office_code = $invoice_header->office_code;
                
                    $control_account = $invoice_header->control_account;

                    $f_nett_of_retained = $invoice_header->foreign_nett_amount;

                    $l_nett_of_retained = $invoice_header->nett_amount;

                    $inv = new Appyinvoices;
                    $inv->vendor_id = $vendor_id;
                    $inv->invoice_no = $invoice_no;
                    $inv->payment_header_no = $payment_header_no;
                    $inv->foreign_invoice_amount = $foreign_invoice_amount;
                    $inv->local_invoice_amount = $local_invoice_amount;
                    $inv->foreign_allocated_amount = $foreign_allocated_amount;
                    $inv->local_allocated_amount = $local_allocated_amount;
                    $inv->foreign_unallocated_amount = $foreign_unallocated_amount;
                    $inv->local_unallocated_amount = $local_unallocated_amount;
                    $inv->currency_rate = $invoice_currency_rate;
                    $inv->vendor_bank_code = $vendor_bank_code;
                    $inv->payment_mode = $payment_mode;
                    $inv->payment_reference = $payment_reference;
                    $inv->foreign_total_taxes = 0;
                    $inv->local_total_taxes = 0;
                    $inv->currency_code = $invoice_currency_code;
                    $inv->office_code = $office_code;
                    $inv->control_account = $control_account;
                    $inv->created_by = auth()->id();
                    $inv->created_date = Carbon::now();
                    $inv->wht_cert_no = $wht_cert_no; 
                    $inv->account_year = $invoice_header->account_year;
                    $inv->account_month = $invoice_header->account_month;
                    $inv->slhead = $invoice_header->slhead;
                    $inv->slhead_flag = $invoice_header->slhead_flag;
                    $inv->save();

                    $all_items = DB::table('ap_invoice_details')->where('invoice_no', $invoice_no)
                    ->where('vendor_id', $vendor_id)->get();
                                    
                    foreach ($all_items as $item)
                    {
                        $item_no = $item->item_no;
                        $taxable = $item->taxable;
                        $foreign_total_cost = $item->foreign_unallocated;
                        $local_total_cost = $item->local_unallocated;
                        $foreign_tax_amount = $item->foreign_tax_amount;
                        $local_tax_amount = $item->local_tax_amount;
                        $item_control_account = $item->item_control_account;
                        $taxable = $item->taxable;
                        $item_gross_bal = $item->foreign_item_bal;
                        $item_f_applied_amnt =  ($foreign_allocated_amount /  $foreign_invoice_amount ) * ($item->foreign_unallocated);
                        $item_l_applied_amnt = ($local_allocated_amount /  $local_invoice_amount ) * ($item->local_unallocated);
                        //dd($item_f_applied_amnt,  $item_l_applied_amnt, $foreign_allocated_amount, $item->foreign_unallocated);

                        $f_item_nett_of_retained = $item->net_of_retained_amount;

                        $lob_trans = $invoice_header->lob_trans;

                        if($taxable == 'Y')
                        {
                            $taxable_amnt = (($foreign_allocated_amount) / $foreign_invoice_amount) * $item->foreign_item_bal;
                            /**lob invoices */
                            if($lob_trans == 'Y')
                            {
                                $req = Payreqst::whereRaw("trim(req_no) = '" . $invoice_no . "' ")->first();

                                $payreqtaxes = Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req->req_no . "' ")
                                                //->whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                                                //->whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                                            ->whereRaw("trim(item_no) = '" . $item_no . "' ")->get();

                                foreach ($payreqtaxes as $payreqtax) 
                                {
                                    $tax_code = $payreqtax->tax_code;

                                    $gltax = DB::table('gltaxes')->where('tax_code', $tax_code)->first();

                                    $tax_type = $gltax->tax_type;

                                    $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'PAY')->first();

                                    if($transtype)
                                    {
                                        $add_deduct = $payreqtax->add_deduct;

                                        $rate = $payreqtax->tax_rate;

                                        if($rate == 0)
                                        {

                                        }

                                        else
                                        {
                                            if($add_deduct == 'A')
                                            {
                                                $tax_amount1 =  ($rate / 100) * $taxable_amnt;
                                            }
                    
                                            if($add_deduct == 'D')
                                            {
                                                $tax_amount1 =  (($rate / 100) * $taxable_amnt) * (-1);
                                            }

                                            //save into tax table
                                            $ap_py_tax = Appytaxes::create([
                                                'vendor_id' => $vendor_id,
                                                'payment_header_no' => $payment_header_no,
                                                'item_no' => $item_no,
                                                'invoice_no' => $invoice_no,
                                                'tax_code' => $tax_code,
                                                // 'foreign_tax_amount' => $taxable_amnt,
                                                // 'local_tax_amount' => $taxable_amnt * $invoice_currency_rate,
                                                'tax_rate' => $rate,
                                                'foreign_applied_amount' => $tax_amount1,
                                                'local_applied_amount' => $tax_amount1 * $invoice_currency_rate,
                                                // 'foreign_tax_balance' => $f_tax_balance,
                                                // 'local_tax_balance' => $l_tax_balance,
                                                'add_deduct' =>$add_deduct,
                                                'created_by' => trim(auth()->id()),
                                                'created_date' => Carbon::now()
                                            ]); 
                                        }
                                    }
                                }
                            }
                        
                            else
                            {
                                $tax_group_code = $invoice_header->tax_group;

                                $tax_types = DB::table('gltaxgroupdtl')->where('group_code', $tax_group_code)->get();
                                
                                foreach ($tax_types as $type) 
                                {
                                    $tax_type = $type->tax_type;
                                    $tax_code = $type->tax_code;
                                
                                    $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)->first();
                                    
                                    $rate = $gltax->tax_rate;
                                    
                                    //trans type
                                    $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'PAY')->first();
                                    
                                    if($transtype)
                                    {
                                        if($rate == 0)
                                        {
                                            
                                        }

                                        else
                                        {
                                            $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                                    
                                            $add_deduct = $add_deduct_check->add_deduct;
                                
                                            if($add_deduct == 'A')
                                            {
                                                $tax_amount1 =  ($rate / 100) * $taxable_amnt;
                                            }
                    
                                            if($add_deduct == 'D')
                                            {
                                                $tax_amount1 =  (($rate / 100) * $taxable_amnt) * (-1);
                                            }
                                        
                                            //save into tax table
                                            $ap_py_tax = Appytaxes::create([
                                                'vendor_id' => $vendor_id,
                                                'payment_header_no' => $payment_header_no,
                                                'item_no' => $item_no,
                                                'invoice_no' => $invoice_no,
                                                'tax_code' => $tax_code,
                                                // 'foreign_tax_amount' => $taxable_amnt,
                                                // 'local_tax_amount' => $taxable_amnt * $invoice_currency_rate,
                                                'tax_rate' => $rate,
                                                'foreign_applied_amount' => $tax_amount1,
                                                'local_applied_amount' => $tax_amount1 * $invoice_currency_rate,
                                                // 'foreign_tax_balance' => $f_tax_balance,
                                                // 'local_tax_balance' => $l_tax_balance,
                                                'add_deduct' =>$add_deduct,
                                                'created_by' => trim(auth()->id()),
                                                'created_date' => Carbon::now()
                                            ]); 
                                        }
                                    }
                                }
                            }
                        }
        
                        $item_total_applied_taxes_f = Appytaxes::where('payment_header_no', $payment_header_no)->where('invoice_no', $invoice_no)
                                                    ->where('item_no', $item_no)->where('vendor_id', $vendor_id)->sum('foreign_applied_amount');
        
                        $item_total_applied_taxes_l = $item_total_applied_taxes_f * $invoice_currency_rate;
        
                        $foreign_item_balance = $foreign_total_cost - $item_f_applied_amnt;
        
                        $local_item_balance =  $foreign_item_balance * $invoice_currency_rate;

                        $item_f_nett =  $item_total_applied_taxes_f + $item_f_applied_amnt;

                        $item_l_nett =  $item_f_nett * $invoice_currency_rate;
        
                        $py_inv_item = Appyinv_items::create([
                            'payment_header_no' => $payment_header_no,
                            'item_no' => $item_no,
                            'vendor_id' => $vendor_id,
                            'invoice_no' => $invoice_no,
                            'local_item_balance' => $local_item_balance,
                            'foreign_item_balance' => $foreign_item_balance,
                            'foreign_total_cost' => $foreign_total_cost,
                            'applied_foreign_amount' => $item_f_applied_amnt,
                            'applied_local_amount' => $item_l_applied_amnt,
                            'local_total_cost' => $local_total_cost,
                            'local_taxable_amount' => $taxable_amnt * $invoice_currency_rate,
                            'foreign_taxable_amount' => $taxable_amnt,
                            // 'local_tax_amount' => $local_tax_amount,
                            // 'foreign_tax_amount' => $foreign_tax_amount,
                            'foreign_applied_tax_amount' => $item_total_applied_taxes_f,
                            'local_applied_tax_amount' => $item_total_applied_taxes_l,
                            // 'foreign_tax_balance' => $foreign_tax_amount - $item_total_applied_taxes_f,
                            // 'local_tax_balance' => $local_tax_amount - $item_total_applied_taxes_l, 
                            'created_date' => Carbon::now(),
                            'created_by' => trim(auth()->id()),
                            'item_control_account' => $item_control_account,
                            'foreign_nett_amount' => $item_f_nett,
                            'local_nett_amount' => $item_l_nett,
                            'taxable' => $taxable,
                            'slhead' => $item->slhead,
                            'slhead_flag' => $item->slhead_flag
                        ]);
                    }

                    //update invoice 
                    $total_foreign_applied_tax = Appyinv_items::where('payment_header_no', $payment_header_no) 
                    ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)
                    ->sum('foreign_applied_tax_amount');

                    $total_local_applied_tax = $total_foreign_applied_tax * $invoice_currency_rate;

                    $f_inv_nett_amount = Appyinv_items::where('payment_header_no', $payment_header_no) 
                    ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('foreign_nett_amount');

                    $l_inv_nett_amount =  Appyinv_items::where('payment_header_no', $payment_header_no) 
                    ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('local_nett_amount');

                    $inv_bal = Appyinv_items::where('payment_header_no', $payment_header_no) 
                    ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('foreign_item_balance');

                    $inv_bal_l = Appyinv_items::where('payment_header_no', $payment_header_no) 
                    ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('local_item_balance');

                    $update_invoice = Appyinvoices::where('invoice_no', $invoice_no)
                                    ->where('payment_header_no', $payment_header_no)->where('vendor_id', $vendor_id)
                                        ->update([
                                            'foreign_total_taxes' => $total_foreign_applied_tax,
                                            'local_total_taxes' => $total_local_applied_tax,
                                            // 'foreign_tax_balance' => $f_inv_tax_bal,
                                            // 'local_tax_balance' => $l_inv_tax_bal,
                                            'foreign_nett_amount' => $f_inv_nett_amount,
                                            'local_nett_amount' => $l_inv_nett_amount,
                                            'foreign_unallocated_amount' => $inv_bal,
                                            'local_unallocated_amount' => $inv_bal_l
                                        ]);
                }
            }

            $py_foreign_applied_amount = Appyinvoices::where('payment_header_no', $payment_header_no) ->sum('foreign_allocated_amount');

            $py_local_applied_amount = Appyinvoices::where('payment_header_no', $payment_header_no)->sum('local_allocated_amount');

            $foreign_py_taxes = Appyinvoices::where('payment_header_no', $payment_header_no)->sum('foreign_total_taxes');

            $local_py_taxes = Appyinvoices::where('payment_header_no', $payment_header_no)
            ->sum('local_total_taxes');

            $py_foreign_unallocated_amount = $foreign_payment_amount - $py_foreign_applied_amount; 
            
            $py_local_unallocated_amount = $local_payment_amount - $py_local_applied_amount;

            $py_nett_amount_f1 =  Appyinvoices::where('payment_header_no', $payment_header_no)
            ->sum('foreign_nett_amount');

            $py_nett_amount_l1 =  Appyinvoices::where('payment_header_no', $payment_header_no)
            ->sum('local_nett_amount');

            $curr = Currency::where('currency_code', $payment_header->currency_code)->get()[0];

            $py_nett_amount_l = number_format((float)$py_nett_amount_l1, 2, '.', '');
            $py_nett_amount_f = number_format((float)$py_nett_amount_f1, 2, '.', '');
    
            // $words = new NumberFormatter("en", NumberFormatter::SPELLOUT);
    
            // $amnt_words = strtoupper($words->format($py_nett_amount_f)).' '.trim(strtoupper($curr->description));
            $amnt_words = numberTowords($py_nett_amount_f);

            $update_head = Appaymentheader::where('payment_header_no', $payment_header_no)
            ->update([
                'foreign_unallocated_amount' => $py_foreign_unallocated_amount,
                'local_unallocated_amount' =>  $py_local_unallocated_amount,
                'foreign_applied_amount' => $py_foreign_applied_amount,
                'local_applied_amount' =>  $py_local_applied_amount,
                'foreign_total_taxes' => $foreign_py_taxes,
                'local_total_taxes' => $local_py_taxes,
                'foreign_nett_amount' => $py_nett_amount_f,
                'local_nett_amount' => $py_nett_amount_l,
                'amnt_in_words' =>  $amnt_words
            ]);
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully added');
        } 
        
        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed to add, try again');
        }
    }

    public function getPyinveditdetails(Request $request)
    {
        $payment_header_no = trim($request->edit_payment_header_no);

        $vendor_id = trim($request->edit_vendor_id);

        $invoice_no = trim($request->edit_invoice_no);

        $vendor = Apvendors::where('vendor_id', $vendor_id)->first();

        $old_bank_code = trim($request->edit_bank_code);

        $old_bank = Banks::whereRaw("trim(bank_code) = '" . $old_bank_code . "' ")->first();

        $all_banks = Bankaccounts::where('entity_id', $vendor_id)->where('entity_type', 'VEN')->where('status_code', 1)
        ->whereNotIn('bank_code', [ $old_bank_code])->get();
        $bank_codes = [];

        foreach ($all_banks as $bnk) 
        {
            $bnk = Banks::whereRaw("trim(bank_code) = '" . $bnk->bank_code. "' ")->first();

            array_push($bank_codes, $bnk);
        }

        $edit_payment_header = Appaymentheader::where('payment_header_no', $payment_header_no)->first();

        $edit_vendor = Apvendors::where('vendor_id', $vendor_id)->first();

        if($edit_payment_header->prepayment == 'Y')
        {
            $edit_py_invoice = Appyinvoices::where('vendor_id', $vendor_id)->where('payment_header_no', $payment_header_no)
            ->where('invoice_no', $invoice_no)->get()[0];

            $vendor_invoice = POInvoice_header::where('vendor_id', $vendor_id)->where('invoice_no', $invoice_no)
            ->get()[0];

            $edit_py_invoice['description'] = $vendor_invoice->invoice_descr;
        }

        else
        {
            $vendor_invoice = DB::table('ap_invoice_header')->where('vendor_id', $vendor_id)->where('invoice_no', $invoice_no)
            ->get()[0];

            $edit_py_invoice = Appyinvoices::where('vendor_id', $vendor_id)->where('payment_header_no', $payment_header_no)
            ->where('invoice_no', $invoice_no)->get()[0];

            $edit_py_invoice['description'] = $vendor_invoice->invoice_description;
        }

        return  ['edit_payment_header' => $edit_payment_header, 'edit_vendor' => $edit_vendor, 'edit_py_invoice' => $edit_py_invoice, 'old_bank' => $old_bank, 'bank_codes' => $bank_codes];
        
    }

    public function updatePyvendorinv(Request $request)
    {
        $this->validate($request, [
            'edit_vendor_id' => 'required',
            'edit_invoice_no' => 'required',
            'edit_foreign_invoice_amount' => 'required',
            'edit_foreign_allocated_amount' => 'required',
            'edit_bank_code' => 'required',
            //'edit_payment_header_no' => 'edit_payment_header_no'
        ]);

        $vendor_id = trim($request->edit_vendor_id);

        $payment_header_no = trim($request->edit_payment_header_no);

        $payment_header = Appaymentheader::where('payment_header_no', $payment_header_no)->first();

        $payment_mode = $payment_header->payment_mode;

        $payment_reference = $payment_header->payment_reference;

        $foreign_payment_amount = $payment_header->foreign_payment_amount;

        $local_payment_amount = $payment_header->local_payment_amount;

        $header_currency_rate = $payment_header->currency_rate;

        $vendor_bank_code = trim($request->edit_bank_code);

        $invoice_no = trim($request->edit_invoice_no);

        $invoice_header = Appyinvoices::where('invoice_no', $invoice_no)->where('payment_header_no', $payment_header_no)
        ->where('vendor_id', $vendor_id)->first();

        $invoice_currency_rate = $invoice_header->currency_rate;

        $invoice_currency_code = $invoice_header->currency_code;
        
        $foreign_allocated_amount = str_replace(',', '', $request->edit_foreign_allocated_amount);

        $local_allocated_amount = $foreign_allocated_amount * $invoice_currency_rate;

        if($payment_header->prepayment == 'Y')
        {
            $invoice = POInvoice_header::where('vendor_id', $vendor_id)->where('invoice_no', $invoice_no)->first();
            $foreign_invoice_amount = $invoice->unallocated;
            $local_invoice_amount = $invoice->local_unallocated;
        }

        else
        {
            $invoice = DB::table('ap_invoice_header')->where('vendor_id', $vendor_id)->where('invoice_no', $invoice_no)->first();
            $foreign_invoice_amount = $invoice->foreign_unallocated;
            $local_invoice_amount = $invoice->local_unallocated;
        }
        $foreign_unallocated_amount = $foreign_invoice_amount - $foreign_allocated_amount;

        $local_unallocated_amount = $local_invoice_amount - $local_allocated_amount;

        $f_nett_of_retained = $invoice_header->foreign_nett_amount;

        $l_nett_of_retained = $invoice_header->nett_amount;

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {

            $all_items = Appyinv_items::where('payment_header_no', $payment_header_no) 
            ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->get();

            foreach ($all_items as $item)
            {
                $item_no = $item->item_no;

                if($payment_header->prepayment == 'Y')
                {
                    $find_item = POInvoice_dtls::where('vendor_id', $vendor_id)->where('invoice_no', $invoice_no)
                    ->get()[0];
                    $foreign_total_cost = $find_item->unallocated;
                    $local_total_cost = $find_item->local_unallocated;
                    $foreign_tax_amount = $find_item->tax_amt;
                    $local_tax_amount = $find_item->local_tax_amt;

                    $item_f_applied_amnt =  ($foreign_allocated_amount /  $foreign_invoice_amount ) * ($foreign_total_cost);
                    $item_l_applied_amnt = ($local_allocated_amount /  $local_invoice_amount ) * ($local_total_cost);
                    $taxable_amnt = (($foreign_allocated_amount) / $foreign_invoice_amount) * $find_item->foreign_item_bal;
                }

                else
                {
                    $find_item = DB::table('ap_invoice_details')->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)
                    ->where('item_no', $item_no)->first();

                    $foreign_total_cost = $find_item->foreign_unallocated;
                    $local_total_cost = $find_item->local_unallocated;
                    $foreign_tax_amount = $find_item->foreign_tax_amount;
                    $local_tax_amount = $find_item->local_tax_amount;

                    $item_f_applied_amnt =  ($foreign_allocated_amount /  $foreign_invoice_amount ) * ($foreign_total_cost);
                    $item_l_applied_amnt = ($local_allocated_amount /  $local_invoice_amount ) * ($local_total_cost);
                    $taxable_amnt = (($foreign_allocated_amount) / $foreign_invoice_amount) * $find_item->foreign_item_bal;
                }

                $taxable = $find_item->taxable;


                if($taxable == 'Y')
                {
                    $tax_group_code = $invoice_header->tax_group;

                    $tax_types = DB::table('gltaxgroupdtl')->where('group_code', $tax_group_code)->get();
                    
                    foreach ($tax_types as $type) 
                    {
                        $tax_type = $type->tax_type;
                        $tax_code = $type->tax_code;
                    
                        $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)->first();
                        
                        $rate = $gltax->tax_rate;
                        
                        //trans type
                        $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'PAY')->first();
                        
                        if($transtype)
                        {
                            if($rate == 0)
                            {
                                
                            }

                            else
                            {
                                $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                        
                                $add_deduct = $add_deduct_check->add_deduct;
                    
                                if($add_deduct == 'A')
                                {
                                    $tax_amount1 =  ($rate / 100) * $taxable_amnt;
                                }
        
                                if($add_deduct == 'D')
                                {
                                    $tax_amount1 =  (($rate / 100) * $taxable_amnt) * (-1);
                                }

                                ##Delete existing taxes
                                $delete_taxes = Appytaxes::where('payment_header_no', $payment_header_no)->where('vendor_id', $vendor_id)
                                ->where('invoice_no', $invoice_no)->where('item_no', $item_no)->delete();
                            
                                //save into tax table
                                $ap_py_tax = Appytaxes::create([
                                    'vendor_id' => $vendor_id,
                                    'payment_header_no' => $payment_header_no,
                                    'item_no' => $item_no,
                                    'invoice_no' => $invoice_no,
                                    'tax_code' => $tax_code,
                                    // 'foreign_tax_amount' => $taxable_amnt,
                                    // 'local_tax_amount' => $taxable_amnt * $invoice_currency_rate,
                                    'tax_rate' => $rate,
                                    'foreign_applied_amount' => $tax_amount1,
                                    'local_applied_amount' => $tax_amount1 * $invoice_currency_rate,
                                    // 'foreign_tax_balance' => $f_tax_balance,
                                    // 'local_tax_balance' => $l_tax_balance,
                                    'add_deduct' =>$add_deduct,
                                    'created_by' => trim(auth()->id()),
                                    'created_date' => Carbon::now()
                                ]); 
                            }
                        }
                    }
                }

                #upd item
                $item_total_applied_taxes_f = Appytaxes::where('payment_header_no', $payment_header_no)->where('invoice_no', $invoice_no)
                ->where('item_no', $item_no)->where('vendor_id', $vendor_id)->sum('foreign_applied_amount');

                $item_total_applied_taxes_l = $item_total_applied_taxes_f * $invoice_currency_rate;

                $foreign_item_balance = $foreign_total_cost - $item_f_applied_amnt;

                $local_item_balance =  $foreign_item_balance * $invoice_currency_rate;

                $item_f_nett =  $item_total_applied_taxes_f + $item_f_applied_amnt;

                $item_l_nett =  $item_f_nett * $invoice_currency_rate;

                $py_inv_item = Appyinv_items::where('payment_header_no', $payment_header_no)->where('invoice_no', $invoice_no)
                    ->where('item_no', $item_no)->where('vendor_id', $vendor_id)
                    ->update([
                        'local_item_balance' => $local_item_balance,
                        'foreign_item_balance' => $foreign_item_balance,
                        'foreign_total_cost' => $foreign_total_cost,
                        'applied_foreign_amount' => $item_f_applied_amnt,
                        'applied_local_amount' => $item_l_applied_amnt,
                        'local_total_cost' => $local_total_cost,
                        'local_taxable_amount' => $taxable_amnt * $invoice_currency_rate,
                        'foreign_taxable_amount' => $taxable_amnt,
                        'foreign_applied_tax_amount' => $item_total_applied_taxes_f,
                        'local_applied_tax_amount' => $item_total_applied_taxes_l,
                        'dola' => Carbon::now(),
                        'changed_by' => trim(auth()->id()),
                        'foreign_nett_amount' => $item_f_nett,
                        'local_nett_amount' => $item_l_nett,
                ]);
            }

            ##update invoice 
            $total_foreign_applied_tax = Appyinv_items::where('payment_header_no', $payment_header_no) 
            ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('foreign_applied_tax_amount');

            $total_local_applied_tax = Appyinv_items::where('payment_header_no', $payment_header_no) 
            ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('local_applied_tax_amount');

            $f_inv_nett_amount = Appyinv_items::where('payment_header_no', $payment_header_no) 
            ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('foreign_nett_amount');

            $l_inv_nett_amount =  Appyinv_items::where('payment_header_no', $payment_header_no) 
            ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('local_nett_amount');

            $inv_bal = Appyinv_items::where('payment_header_no', $payment_header_no) 
            ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('foreign_item_balance');

            $inv_bal_l = Appyinv_items::where('payment_header_no', $payment_header_no) 
            ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('local_item_balance');

            $update_invoice = Appyinvoices::where('invoice_no', $invoice_no)
                ->where('payment_header_no', $payment_header_no)->where('vendor_id', $vendor_id)
                ->update([
                    'foreign_total_taxes' => $total_foreign_applied_tax,
                    'local_total_taxes' => $total_local_applied_tax,
                    'foreign_nett_amount' => $f_inv_nett_amount,
                    'local_nett_amount' => $l_inv_nett_amount,
                    'foreign_allocated_amount' => $foreign_allocated_amount,
                    'local_allocated_amount' => $local_allocated_amount,
                    'foreign_unallocated_amount' => $foreign_unallocated_amount,
                    'local_unallocated_amount' => $local_unallocated_amount,
                    'vendor_bank_code' => $vendor_bank_code,
                    'changed_by' => auth()->id(),
                    'dola' => Carbon::now(),
            ]);

            $py_foreign_applied_amount = Appyinvoices::where('payment_header_no', $payment_header_no) 
            ->sum('foreign_allocated_amount');

            $py_local_applied_amount = Appyinvoices::where('payment_header_no', $payment_header_no) 
            ->sum('local_allocated_amount');

            $foreign_py_taxes = Appyinvoices::where('payment_header_no', $payment_header_no)
            ->sum('foreign_total_taxes');

            $local_py_taxes = Appyinvoices::where('payment_header_no', $payment_header_no)
            ->sum('local_total_taxes');

            $py_foreign_unallocated_amount = $foreign_payment_amount - $py_foreign_applied_amount; 
            
            $py_local_unallocated_amount = $local_payment_amount - $py_local_applied_amount;

            $py_nett_amount_f1 =  Appyinvoices::where('payment_header_no', $payment_header_no)
            ->sum('foreign_nett_amount');

            $py_nett_amount_l1 =  Appyinvoices::where('payment_header_no', $payment_header_no)
            ->sum('local_nett_amount');

            $curr = Currency::where('currency_code', $payment_header->currency_code)->get()[0];

            $py_nett_amount_l = number_format((float)$py_nett_amount_l1, 2, '.', '');
            $py_nett_amount_f = number_format((float)$py_nett_amount_f1, 2, '.', '');
    
            // $words = new NumberFormatter("en", NumberFormatter::SPELLOUT);
    
            // $amnt_words = strtoupper($words->format($py_nett_amount_f)).' '.trim(strtoupper($curr->description));
            $amnt_words = numberTowords($py_nett_amount_f);
            
            $update_head = Appaymentheader::where('payment_header_no', $payment_header_no)
                ->update([
                        'foreign_unallocated_amount' => $py_foreign_unallocated_amount,
                        'local_unallocated_amount' =>  $py_local_unallocated_amount,
                        'foreign_applied_amount' => $py_foreign_applied_amount,
                        'local_applied_amount' =>  $py_local_applied_amount,
                        'foreign_total_taxes' => $foreign_py_taxes,
                        'local_total_taxes' => $local_py_taxes,
                        'foreign_nett_amount' => $py_nett_amount_f,
                        'local_nett_amount' => $py_nett_amount_l,
                        'amnt_in_words' =>  $amnt_words
            ]);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            
            return 1;
        } 
        
        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
           return 0;
        }
    }

    public function  removePyvendorinv(Request $request)
    {
        $invoice_no = trim($request->delete_invoice_no);

        $payment_header_no = trim($request->delete_payment_header_no);

        $vendor_id =  trim($request->delete_vendor_id);

        try 
        {
            $delete_invoice = Appyinvoices::where('payment_header_no', $payment_header_no)->where('vendor_id', $vendor_id)
            ->where('invoice_no', $invoice_no)->delete();

            $delete_items = Appyinv_items::where('payment_header_no', $payment_header_no)->where('vendor_id', $vendor_id)
            ->where('invoice_no', $invoice_no)->delete();

            $delete_taxes = Appytaxes::where('payment_header_no', $payment_header_no)->where('vendor_id', $vendor_id)
            ->where('invoice_no', $invoice_no)->delete();

            $payment_header = Appaymentheader::where('payment_header_no', $payment_header_no)->first();
            
            $foreign_payment_amount = $payment_header->foreign_payment_amount;

            $local_payment_amount = $payment_header->local_payment_amount;

            $header_currency_rate = $payment_header->currency_rate;

            $py_foreign_applied_amount = Appyinvoices::where('payment_header_no', $payment_header_no) 
            ->sum('foreign_allocated_amount');                                                

            $py_local_applied_amount = Appyinvoices::where('payment_header_no', $payment_header_no) 
            ->sum('local_allocated_amount');

            $foreign_py_taxes = Appyinvoices::where('payment_header_no', $payment_header_no)
            ->sum('foreign_total_taxes');

            $local_py_taxes = Appyinvoices::where('payment_header_no', $payment_header_no)
            ->sum('local_total_taxes');

            $py_foreign_unallocated_amount = $foreign_payment_amount - $py_foreign_applied_amount; 
            
            $py_local_unallocated_amount = $local_payment_amount - $py_local_applied_amount;

            $py_nett_amount_f1 =  Appyinvoices::where('payment_header_no', $payment_header_no)
            ->sum('foreign_nett_amount');

            $py_nett_amount_l1 =  Appyinvoices::where('payment_header_no', $payment_header_no)
            ->sum('local_nett_amount');

            $curr = Currency::where('currency_code', $payment_header->currency_code)->get()[0];

            $py_nett_amount_l = number_format((float)$py_nett_amount_l1, 2, '.', '');
            $py_nett_amount_f = number_format((float)$py_nett_amount_f1, 2, '.', '');
    
            // $words = new NumberFormatter("en", NumberFormatter::SPELLOUT);
    
            // $amnt_words = strtoupper($words->format($py_nett_amount_f)).' '.trim(strtoupper($curr->description));
            $amnt_words = numberTowords($py_nett_amount_f);
                                
            $update_head = Appaymentheader::where('payment_header_no', $payment_header_no)
                ->update([
                        'foreign_unallocated_amount' => $py_foreign_unallocated_amount,
                        'local_unallocated_amount' =>  $py_local_unallocated_amount,
                        'foreign_applied_amount' => $py_foreign_applied_amount,
                        'local_applied_amount' =>  $py_local_applied_amount,
                        'foreign_total_taxes' => $foreign_py_taxes,
                        'local_total_taxes' => $local_py_taxes,
                        'foreign_nett_amount' => $py_nett_amount_f,
                        'local_nett_amount' => $py_nett_amount_l,
                        'amnt_in_words' =>  $amnt_words
            ]);

            DB::commit();
            return 1;
        } 
        
        catch (\Throwable $th) 
        {
            DB::rollback();
            return 0;

        }
    }

    public function getPyinvoiceitems(Request $request)
    {
        $invoice_no = trim($request->invoice_no);
        $vendor_id = trim($request->vendor_id);
        $payment_header_no = trim($request->payment_header_no);

        $all_items = Appyinv_items::where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)
                                    ->where('payment_header_no', $payment_header_no)->get();
        return Datatables::Of($all_items)
			->editColumn('foreign_total_cost', function ($item){
                $foreign_total_cost = number_format($item->foreign_total_cost, 2);
                return $foreign_total_cost;
            })

        ->editColumn('applied_foreign_amount', function ($item){
                $applied_foreign_amount = number_format($item->applied_foreign_amount, 2);
                return $applied_foreign_amount;
            })

        ->editColumn('foreign_item_balance', function ($item){
                $foreign_item_balance = number_format($item->foreign_item_balance, 2);
                return $foreign_item_balance;
            })

        ->editColumn('foreign_tax_amount', function ($item){
                $foreign_tax_amount = number_format($item->foreign_tax_amount, 2);
                return $foreign_tax_amount;
            })

        ->editColumn('foreign_applied_tax_amount', function ($item){
                $foreign_applied_tax_amount = number_format($item->foreign_applied_tax_amount, 2);
                return $foreign_applied_tax_amount;
            })

        ->editColumn('foreign_tax_balance', function ($item){
                $foreign_tax_balance = number_format($item->foreign_tax_balance, 2);
                return $foreign_tax_balance;
            })

        ->make(true);
    }

    public function getPyinvitemtaxes(Request $request)
    {
        $invoice_no = trim($request->invoice_no);
        $vendor_id = trim($request->vendor_id);
        $item_no = trim($request->item_no);
        $payment_header_no = trim($request->payment_header_no);

        $taxes = Appytaxes::where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->where('item_no', $item_no)
                                    ->where('payment_header_no', $payment_header_no)->get();

        return Datatables::Of($taxes)
        ->editColumn('foreign_tax_amount', function ($tax){
                $tax_amount = number_format($tax->foreign_tax_amount, 2);
                return $tax_amount;
            })

        ->editColumn('foreign_applied_amount', function ($tax){
                $applied_amount = number_format($tax->foreign_applied_amount, 2);
                return $applied_amount;
            })

        ->editColumn('foreign_tax_balance', function ($tax){
                $applied_amount = number_format($tax->foreign_tax_balance, 2);
                return $applied_amount;
            })

        ->editColumn('tax_code', function ($tax){
                $tax_code1 = number_format($tax->tax_code);
                $tax_code = DB::table('gltaxgroupdtl')->where('tax_code', $tax_code1)->first();
                return $tax_code->tax_type;
            })

        ->editColumn('add_deduct', function ($tax){
                if($tax->add_deduct == 'A')
                {
                    return 'ADDITION';
                }

                else if($tax->add_deduct == 'D')
                {
                    return 'DEDUCTION';
                }

                else
                {
                    return $tax->add_deduct;
                }
            })

        ->editColumn('tax_rate', function ($tax){
                return $tax->tax_rate .'%';
            })

        ->make(true);
    }

    public function readyTopostpyheader(Request $request)
    {
        $payment_header_no = trim($request->payment_header_no);

        $payment_header = Appaymentheader::where('payment_header_no', $payment_header_no)->first();

        $foreign_unallocated_amount = $payment_header->foreign_unallocated_amount;

        $payment_header_status = $payment_header->py_header_status;

        $foreign_payment_amount = $payment_header->foreign_payment_amount;

        if($payment_header_status == '001'  && $foreign_payment_amount > 0 && $foreign_unallocated_amount <= 0)
        {
            $post_payment_header =  Appaymentheader::where('payment_header_no', $payment_header_no)
                                    ->update([
                                            'py_header_status' => '006',
                                            'changed_by' => trim(auth()->id()),
                                            'dola' => Carbon::now()
                                        ]);

            Session::Flash('success', 'Payment Header is ready to be posted'); 
        }
    }

    public function reopenPyheader(Request $request)
    {
        $payment_header_no = trim($request->payment_header_no);
        
        $payment_header = Appaymentheader::where('payment_header_no', $payment_header_no)->first();

        $payment_header_status = $payment_header->py_header_status;
        
        $batch_no = $payment_header->batch_no;
    
        if($payment_header_status == '006' && !$batch_no)
        {
            $reopen =   Appaymentheader::where('payment_header_no', $payment_header_no)
                        ->update([
                                'py_header_status' => '001',
                                'changed_by' => trim(auth()->id()),
                                'dola' => Carbon::now()
                            ]);

            Session::Flash('success', 'Successfully reopened'); 
        }
    }

    public function cancelPyheader(Request $request)
    {
        $payment_header_no = trim($request->payment_header_no);

        $payment_header = Appaymentheader::where('payment_header_no', $payment_header_no)->first();

        $payment_header_status = $payment_header->py_header_status;

        try 
        {
            if($payment_header_status == '001')
            {
                $cancel_pyheader =  Appaymentheader::where('payment_header_no', $payment_header_no)
                ->update([
                    'py_header_status' => '005',
                    'changed_by' => trim(auth()->id()),
                    'dola' => Carbon::now()
                ]);
                
                //upd chequemast
                $pay_method = Olpaymethd::whereRaw("trim(pay_method) = '" . $payment_header->payment_mode . "' ")->get()[0];
               
                if($pay_method->cheque == 'Y')
                {
                    if($payment_header->payment_reference != null)
                    {
                        $upd = Chequemast::where('bank_acc_code', trim($payment_header->bank_acc_code))
                        ->where('cheque_no', $payment_header->payment_reference)
                        ->update([
                            'cheque_cancelled' => 'Y',
                            'date_cancelled' => Carbon::now(),
                            'cancelled_by' => trim(auth()->id()),
                            'cancel_reason' => 'Payment cancelled'
                        ]);
                    }
                }

                if($pay_method->eft == 'Y')
                {
                    if($payment_header->payment_reference != null)
                    {
                        $upd = Eftmast::where('bank_acc_code', trim($payment_header->bank_acc_code))
                        ->where('eft_no', $payment_header->payment_reference)
                        ->update([
                            'eft_cancelled' => 'Y',
                            'date_cancelled' => Carbon::now(),
                            'cancelled_by' => trim(auth()->id()),
                            'cancel_reason' => 'Payment cancelled'
                        ]);
                    }
                }

                if($pay_method->rtgs == 'Y')
                {
                    if($payment_header->payment_reference != null)
                    {
                        $upd = RTGSMast::where('bank_acc_code', trim($payment_header->bank_acc_code))
                        ->where('rtgs_no', $payment_header->payment_reference)
                        ->update([
                            'rtgs_cancelled' => 'Y',
                            'date_cancelled' => Carbon::now(),
                            'cancelled_by' => trim(auth()->id()),
                            'cancel_reason' => 'Payment cancelled'
                        ]);
                    }
                }
                DB::commit();
                Session::Flash('success', 'Cancelled successfully'); 
            }
        } 
        
        catch (\Throwable $th) 
        {
            DB::rollback();
            Session::Flash('error', 'Failed'); 
        }
    }

    public function getPyVendordetails(Request $request)
    {
        $vendor_id = trim($request->vendor_id);
        if(!empty($vendor_id))
        {
            return Apvendors::where('vendor_id', $vendor_id)->get()[0];
        }
    }

    public function getApChequeno(Request $request)
    {
        $pay_method = $request->get('pay_method');
        $prsno = $request->get('cr_account');

        $bank = Glbank::where('prsno',$prsno)->get()[0];

        $cheque = Chequemast::where('bank_acc_code',$bank->bank_acc_code)
                            ->whereRaw("(cheque_cancelled = 'N' or cheque_cancelled is null) and (cheque_issued = 'N' or cheque_issued is null) ")
                            //->orderBy('cheque_no','ASC')
                            ->selectRaw("min(to_number(cheque_no)) as cheque_no")
                            ->get()[0];
        return $cheque;
    }

    public function getPyheader(Request $request)
    {
        $vendor_id = trim($request->vendor_id);
        $header_no = trim($request->header_no);
        if(!empty($header_no))
        {
            $header = Appaymentheader::where('payment_header_no', $header_no)->get()[0];

            $glbank = DB::table('glbanks')->where('prid', 'BNK') 
                    ->whereRaw("trim(bank_acc_code) = '" . $header->bank_acc_code . "' ")->first();

            $office_code = Nlparams::where('prid', 'OFF')->whereRaw("trim(prsno) = '" . $header->office_code . "' ")->first();

            $physical_bank = Banks::whereRaw("trim(bank_code) = '" . $header->bank_code . "' ")->get()[0];

            $branch = DB::table('bankbranches')->where('branch_code', $header->bank_branch_code)
                ->whereRaw("trim(bank_code) = '" . $header->bank_code . "' ")->first();
                    
            $pay_method = Olpaymethd::whereRaw("trim(pay_method) = '" . $header->payment_mode . "' ")->get()[0];

            return ['pay_method' => $pay_method, 
                    'branch' => $branch, 
                    'physical_bank' => $physical_bank, 
                    'office_code' => $office_code,
                    'glbank' => $glbank,
                    'header' => $header
                ];
        }
    }

    function prepaymentInvoice($request,  $vendor_id, $payment_header, $payment_header_no)
    {
        for($i = 0; $i < count($request['invoice_no']); $i++) 
        {
            $invoice_no = $request['invoice_no'][$i];

            $invoice_header = POInvoice_header::where('vendor_id', $vendor_id)->where('invoice_no', $invoice_no)->get()[0];

            $currency_rate = $invoice_header->currency_rate;

            $foreign_allocated_amount = str_replace(',', '', $request['foreign_allocated_amount'][$i]);
           
            $local_allocated_amount = $foreign_allocated_amount * $currency_rate;

            $foreign_invoice_amount = $invoice_header->amt_payable;
          
            $local_invoice_amount = $invoice_header->local_amt_payable;

            $foreign_unallocated_amount = $foreign_invoice_amount - $foreign_allocated_amount;
            
            $local_unallocated_amount = $local_invoice_amount - $local_allocated_amount;

            $original_foreign_total_taxes =  $invoice_header->total_taxes;

            $original_local_total_taxes =  $invoice_header->local_total_taxes;

            $f_nett_of_retained = $invoice_header->taxable_amt;
           
            $l_nett_of_retained = $invoice_header->local_taxable_amt;

            $invoice_items = POInvoice_dtls::where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id) ->get();
                            
            foreach ($invoice_items as $item)
            {
                $item_no = $item->item_no;
                $foreign_total_cost = $item->amt_payable;

                $item_f_applied_amnt =  ($foreign_allocated_amount / $foreign_invoice_amount ) * ($item->amt_payable);
               
                $f_item_nett_of_retained = $item->net_of_retained_amount;

                if($item->taxable == 'Y')
                {
                    $taxable_amnt = (($foreign_allocated_amount) / $foreign_invoice_amount) * $item->taxable_amt;
                                       
                    $tax_group_code = $invoice_header->tax_group;

                    $tax_types = DB::table('gltaxgroupdtl')->where('group_code', $tax_group_code)->get();
                    
                    foreach ($tax_types as $type) 
                    {
                        $tax_type = $type->tax_type;
                        $tax_code = $type->tax_code;
                    
                        $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)->first();
                        
                        $rate = $gltax->tax_rate;
                        
                        $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'PAY')->first();
                        
                        if($transtype)
                        {
                            if($rate > 0)
                            {
                                $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                        
                                $add_deduct = $add_deduct_check->add_deduct;
                    
                                if($add_deduct == 'A')
                                {
                                    $tax_amount1 = ($rate / 100) * $taxable_amnt;
                                }
        
                                if($add_deduct == 'D')
                                {
                                    $tax_amount1 =  (($rate / 100) * $taxable_amnt) * (-1);
                                }
                                
                                $ap_py_tax = Appytaxes::create([
                                    'vendor_id' => $vendor_id,
                                    'payment_header_no' => $payment_header_no,
                                    'item_no' => $item_no,
                                    'invoice_no' => $invoice_no,
                                    'tax_code' => $tax_code,
                                    'tax_rate' => $rate,
                                    'foreign_applied_amount' => $tax_amount1,
                                    'local_applied_amount' => $tax_amount1 * $currency_rate,
                                    'add_deduct' =>$add_deduct,
                                    'created_by' => trim(auth()->id()),
                                    'created_date' => Carbon::now()
                                ]); 
                            }
                        }
                    }
                }

                $item_total_applied_taxes_f = Appytaxes::where('payment_header_no', $payment_header_no)->where('invoice_no', $invoice_no)
                ->where('item_no', $item_no)->where('vendor_id', $vendor_id)->sum('foreign_applied_amount');
              
                $foreign_item_balance = $foreign_total_cost - $item_f_applied_amnt;
                
                $item_f_nett =  $item_total_applied_taxes_f + $item_f_applied_amnt;
               
                $create_item = Appyinv_items::create([
                    'payment_header_no' => $payment_header_no,
                    'item_no' => $item_no,
                    'vendor_id' => $vendor_id,
                    'invoice_no' => $invoice_no,
                    'local_item_balance' => $foreign_item_balance * $currency_rate,
                    'foreign_item_balance' => $foreign_item_balance,
                    'foreign_total_cost' =>  $item->amt_payable,
                    'applied_foreign_amount' => $item_f_applied_amnt,
                    'applied_local_amount' => $item_f_applied_amnt * $currency_rate,
                    'local_total_cost' => $item->local_amt_payable,
                    'local_taxable_amount' => $taxable_amnt * $currency_rate,
                    'foreign_taxable_amount' => $taxable_amnt,
                    'foreign_applied_tax_amount' => $item_total_applied_taxes_f,
                    'local_applied_tax_amount' => $item_total_applied_taxes_f * $currency_rate,
                    'created_date' => Carbon::now(),
                    'created_by' => trim(auth()->id()),
                    'item_control_account' => $item->control_account,
                    'foreign_nett_amount' => $item_f_nett,
                    'local_nett_amount' => $item_f_nett * $currency_rate,
                    'taxable' => $item->taxable
                ]);
            }

            $total_foreign_applied_tax = Appyinv_items::where('payment_header_no', $payment_header_no) 
            ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('foreign_applied_tax_amount');

            $f_inv_nett_amount = Appyinv_items::where('payment_header_no', $payment_header_no) 
            ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('foreign_nett_amount');

            $inv_bal = Appyinv_items::where('payment_header_no', $payment_header_no) 
            ->where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)->sum('foreign_item_balance');

            $inv = new Appyinvoices;
            $inv->vendor_id = $vendor_id;
            $inv->invoice_no = $invoice_no;
            $inv->payment_header_no = $payment_header->payment_header_no;
            $inv->foreign_invoice_amount = $foreign_invoice_amount;
            $inv->local_invoice_amount = $local_invoice_amount;
            $inv->foreign_allocated_amount = $foreign_allocated_amount;
            $inv->local_allocated_amount = $local_allocated_amount;
            $inv->foreign_unallocated_amount = $foreign_unallocated_amount;
            $inv->local_unallocated_amount = $local_unallocated_amount;
            $inv->currency_rate = $currency_rate;
            $inv->vendor_bank_code = $request['vendor_bank_code'];
            $inv->payment_mode = $payment_header->payment_mode;
            $inv->payment_reference = $payment_header->payment_reference;
            $inv->foreign_total_taxes = 0;
            $inv->local_total_taxes = 0;
            $inv->currency_code = $invoice_header->currency_code;
            $inv->office_code = $invoice_header->offcd;
            $inv->control_account = $invoice_header->control_account;
            $inv->created_by = auth()->id();
            $inv->created_date = Carbon::now();
            $inv->wht_cert_no = $wht_cert_no; 
            $inv->account_year = $invoice_header->account_year;
            $inv->account_month = $invoice_header->account_month;
            $inv->foreign_total_taxes = $total_foreign_applied_tax;
            $inv->local_total_taxes = $total_foreign_applied_tax * $currency_rate;
            $inv->foreign_nett_amount = $f_inv_nett_amount;
            $inv->local_nett_amount = $f_inv_nett_amount * $currency_rate;
            $inv->foreign_unallocated_amount = $inv_bal;
            $inv->local_unallocated_amount = $inv_bal * $currency_rate;
            $inv->save();
        }
    }

    function generate_payment_serial()
    {
        $record = Apdoctypes::where('doc_type', 'PAY')->get()[0];
        return $record->serial_no;
    }

    public function upd_payment_serial()
    {
        $new_serial = Apdoctypes::where('doc_type', 'PAY')->increment('serial_no', 1);
    }

    public function reverse_payment(Request $request)
    {
        // dd($request->all());
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try 
        {
            $pay_no = trim($request->payment_header_no);
            
            $today = Carbon::today();

            $year = $today->format('Y');

            $serial_no = $this->generate_payment_serial();
            
            $new_header_no = 'PAY' . $year . STR_PAD($serial_no, 6, '0', STR_PAD_LEFT);;
           
            $payment = Appaymentheader::where('payment_header_no', $pay_no)->get()[0];

            $new_trans = Appaymentheader::create([
                'payment_header_no' => $new_header_no,
                'bank_code' => trim($payment->bank_code),
                'payment_mode' => trim($payment->payment_mode),
                'payment_reference' => $payment->payment_reference,
                'local_payment_amount' => $payment->local_payment_amount,
                'foreign_payment_amount' => $payment->foreign_payment_amount,
                'local_nett_amount' => $payment->local_nett_amount,
                'foreign_nett_amount' => $payment->foreign_nett_amount,
                'currency_code' => trim($payment->currency_code),
                'currency_rate' => $payment->currency_rate,
                'effective_date' => $payment->effective_date,
                'foreign_unallocated_amount' => $payment->foreign_unallocated_amount,
                'local_unallocated_amount' => $payment->local_unallocated_amount,
                'foreign_applied_amount' => $payment->foreign_applied_amount,
                'local_applied_amount' => $payment->local_applied_amount,
                'created_by' => trim(auth()->id()),
                'created_date' => Carbon::now(),
                'py_header_status' => '006',
                'office_code' => trim($payment->office_code),
                'account_month' => $payment->account_month,
                'account_year' => $payment->account_year,
                'doc_type' => 'REV',
                'bank_acc_code' => trim($payment->bank_acc_code),
                'bank_branch_code' => trim($payment->bank_branch_code),
                'bank_account_no' => trim($payment->bank_account_no),
                'gl_account' => trim($payment->gl_account),
                'account_name' => trim($payment->account_name),
                'narration' => $payment->narration,
                'payee' => $payment->payee,
                'vendor_id' => $payment->vendor_id,
                'amnt_in_words' => $payment->amnt_in_words,
                'dep_code' => trim($payment->dep_code),
                'foreign_total_taxes' => $payment->foreign_total_taxes,
                'local_total_taxes' => $payment->local_total_taxes,
                'foreign_nett_amount' => $payment->foreign_nett_amount,
                'local_nett_amount' => $payment->local_nett_amount,
                'original_header_no' => $payment->payment_header_no
            ]);

            ##invoices
            $invoices = Appyinvoices::where('payment_header_no', $pay_no)->where('vendor_id', trim($payment->vendor_id))->get();
            foreach ($invoices as $invoice) {
                $inv = new Appyinvoices;
                $inv->vendor_id = $payment->vendor_id;
                $inv->invoice_no = $invoice->invoice_no;
                $inv->payment_header_no = $new_header_no;
                $inv->foreign_invoice_amount = $invoice->foreign_invoice_amount;
                $inv->local_invoice_amount = $invoice->local_invoice_amount;
                $inv->foreign_allocated_amount = $invoice->foreign_allocated_amount;
                $inv->local_allocated_amount = $invoice->local_allocated_amount;
                $inv->foreign_unallocated_amount = $invoice->foreign_unallocated_amount;
                $inv->local_unallocated_amount = $invoice->local_unallocated_amount;
                $inv->currency_rate = $invoice->currency_rate;
                $inv->vendor_bank_code = $invoice->vendor_bank_code;
                $inv->payment_mode = $invoice->payment_mode;
                $inv->payment_reference = $invoice->payment_reference;
                $inv->foreign_total_taxes = $invoice->foreign_total_taxes;
                $inv->local_total_taxes = $invoice->local_total_taxes;
                $inv->currency_code = $invoice->currency_code;
                $inv->office_code = $invoice->office_code;
                $inv->control_account = $invoice->control_account;
                $inv->created_by = $invoice->created_by;
                $inv->created_date = $invoice->created_date;
                // $inv->wht_cert_no = $wht_cert_no;
                $inv->account_year = $invoice->account_year;
                $inv->account_month = $invoice->account_month;
                $inv->foreign_total_taxes = $invoice->foreign_total_taxes;
                $inv->local_total_taxes = $invoice->local_total_taxes;
                $inv->foreign_nett_amount = $invoice->foreign_nett_amount;
                $inv->local_nett_amount = $invoice->local_nett_amount;
                $inv->foreign_unallocated_amount = $invoice->foreign_unallocated_amount;
                $inv->local_unallocated_amount = $invoice->local_unallocated_amount;
                $inv->slhead = $invoice->slhead;
                $inv->slhead_flag = $invoice->slhead_flag;
                $inv->save();

                ##items
                $items = Appyinv_items::where('payment_header_no', $pay_no)->where('vendor_id', trim($payment->vendor_id))
                    ->where('invoice_no', $invoice->invoice_no)->get();

                foreach ($items as $item) {
                    $new_item = Appyinv_items::create([
                        'payment_header_no' => $new_header_no,
                        'item_no' => $item->item_no,
                        'vendor_id' => $item->vendor_id,
                        'invoice_no' => $item->invoice_no,
                        'local_item_balance' => $item->local_item_balance,
                        'foreign_item_balance' => $item->foreign_item_balance,
                        'foreign_total_cost' => $item->foreign_total_cost,
                        'applied_foreign_amount' => $item->applied_foreign_amount,
                        'applied_local_amount' => $item->applied_local_amount,
                        'local_total_cost' => $item->local_total_cost,
                        'local_taxable_amount' => $item->local_taxable_amount,
                        'foreign_taxable_amount' => $item->$foreign_taxable_amount,
                        'foreign_applied_tax_amount' => $item->foreign_applied_tax_amount,
                        'local_applied_tax_amount' => $item->local_applied_tax_amount,
                        'created_date' => $item->created_date,
                        'created_by' => $item->created_by,
                        'item_control_account' => $item->created_by,
                        'foreign_nett_amount' => $item->foreign_nett_amount,
                        'local_nett_amount' => $item->local_nett_amount,
                        'taxable' => $item->$taxable,
                        'slhead' => $item->slhead,
                        'slhead_flag' => $item->$slhead_flag
                    ]);

                    ##taxes
                    $taxes = Appytaxes::where('payment_header_no', $pay_no)->where('vendor_id', trim($payment->vendor_id))
                    ->where('invoice_no', $invoice->invoice_no)->where('item_no', $item->item_no)->get();

                    if (count($taxes) > 0) {
                        foreach ($taxes as $tax) {
                            $tax = Appytaxes::create([
                                'vendor_id' => $tax->vendor_id,
                                'payment_header_no' => $new_header_no,
                                'item_no' => $item->item_no,
                                'invoice_no' => $tax->invoice_no,
                                'tax_code' => $tax->tax_code,
                                'tax_rate' => $tax->tax_rate,
                                'foreign_applied_amount' => $tax->foreign_applied_amount,
                                'local_applied_amount' => $tax->local_applied_amount,
                                'add_deduct' => $tax->add_deduct,
                                'created_by' => $tax->created_by,
                                'created_date' => $tax->created_date,
                            ]);
                        }
                    }
                }

                ##cancel wht cert no
                $cert = ApWhtCertalloc::where('cert_no', trim($invoice->wht_cert_no))->get()[0];

                if ($cert) {
                    $upd_wht_cert = ApWhtCertalloc::where('cert_no', $cert->cert_no)->where('cancelled', '<>', 'Y')
                        ->update([
                            'cancelled' => 'Y',
                            'who_cancelled' => trim(auth()->id()),
                            'why_cancelled' => $reason,
                        ]);
                }

            }

            $upd_pay = Appaymentheader::where('payment_header_no', $pay_no)
            ->update([
                'reversal_header_no' => $new_header_no,
                'py_header_status' => '007'
            ]);

            $this->upd_payment_serial();

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            return response()->json(['status' => 1, 'payment_header_no' => $new_header_no]);
        } 
        
        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            return response()->json(['status' => 0]);
        }
    }
}
