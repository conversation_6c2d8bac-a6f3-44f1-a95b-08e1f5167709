<?php

namespace App\Http\Controllers\gl;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\Datatables\Datatables;

use Carbon\Carbon;

use View;
use App\Nlbatch;
use App\Nlsldtlb;
use App\Nlslparams;
use App\Currency;
use App\Doctype;
use App\Nlparams;
use App\Nlslmst;
use App\Fatypes;
use App\Currate;
use App\Nlctrl;
use DB;
use Auth;

class TransProccessingController extends Controller
{
    public function yearlyopeningbalances()
    {

        return view('gl.transactions.y_opening_bal');
    }
    public function officecodes(Request $request)
    {
        $offices = Nlparams::where('prid', 'OFF')
            ->get();
        return $offices;
    }
    public function getCurrency(Request $request)
    {
        return Currency::all();
    }
    public function getCurrencyrate(Request $request)
    {
        $currency = $request->get('currency');

        $rate_date = carbon::today();
        //echo $offcd;
        $rate_details = Currate::where('currency_code', $currency)
            ->where('rate_date', $rate_date)->get();
        return $rate_details;
    }
    public function storeyearlyopeningbalances(Request $request)
    {


        $acvtp = trim(Nlparams::where('prid', 'VTP')->max('prsno'));



        $transaction_data['batch_no'] = $request->bnum;
        $transaction_data['offcd'] = $request->off_cd;
        $transaction_data['prsno'] = $acvtp;
        $transaction_data['acvtp'] = $acvtp;
        $transaction_data['acvno'] = $acvtp;

        $transaction_data['period_year'] = $request->year;
        $transaction_data['period_month'] = $request->month;
        $transaction_data['acvdt'] = $request->v_date;


        $transaction_data['CLOSING_BALANCE_ONLY'] = str_replace(',', '', $request->closingbal);
        $transaction_data['currency_code'] = $request->currency;
        $transaction_data['currency_rate'] = $request->currency_rate;
        $transaction_data['FOREIGN_ACVTOT'] =  str_replace(',', '', $document->batchtotal);
        $transaction_data['ACVTOT'] =  str_replace(',', '', $request->local_equivalent);

        $transaction_data['ACNAR1'] = $request->narration[0];
        $transaction_data['ACNAR2'] = $request->narration[1];
        $transaction_data['ACNAR3'] = $request->narration[2];
        $transaction_data['dola'] = Carbon::today()->toDateString();

        try {
            Nlbatch::insert($transaction_data);
            DB::commit();
        } catch (\Throwable $e) {
            return $e;
            DB::rollback();
        }
    }
    public function journalentries()
    {
        return view('gl.transactions.journal_entries');
    }
    public function storejournalentry(Request $request)
    {

            $document = Doctype::whereRaw("TRIM(doc_type) = ?", ['JV'])->get()[0];
        /*If DOCTYPE:DOC_TYPE=W_DOC_TYPE And DOCTYPE:AUTO="Y" Then
                NLBATCH:BATCH_NO="JV"+str(DOCTYPE:SERIAL_NO,"999")+"/"+
                   str(NLCTRL:Account_Year[3,4],"99")/addonly*/
                   
        if (trim($document->auto) == 'Y') {
            
            $accountYear=Nlctrl::first()->account_year;
            $year=substr($accountYear, -2);
        
          
            $transaction_data['batch_no'] ='JV'.str_pad($document->serial, 3,'0',STR_PAD_LEFT)."/".$year;
            
        }

        $acvtp = trim(Nlparams::where('prid', 'VTP')->max('prsno'));

       




        //  $transaction_data['batch_no'] = $request->bnum;
        $transaction_data['offcd'] = $request->off_cd;
        $transaction_data['prsno'] = $acvtp;
        $transaction_data['acvtp'] = $acvtp;
        $transaction_data['acvno'] = $acvtp;

        $transaction_data['period_year'] = $request->year;
        $transaction_data['period_month'] = $request->month;
        $transaction_data['acvdt'] = $request->v_date;


        $transaction_data['CLOSING_BALANCE_ONLY'] = str_replace(',', '', $request->closingbal);
        $transaction_data['currency_code'] = $request->currency;
        $transaction_data['currency_rate'] = $request->currency_rate;
        $transaction_data['FOREIGN_ACVTOT'] =  str_replace(',', '', $document->batchtotal);
        $transaction_data['ACVTOT'] =  str_replace(',', '', $request->local_equivalent);

        $transaction_data['ACNAR1'] = $request->narration[0];
        $transaction_data['ACNAR2'] = $request->narration[1];
        $transaction_data['ACNAR3'] = $request->narration[2];
        $transaction_data['dola'] = Carbon::today()->toDateString();

        try {
            Nlbatch::insert($transaction_data);
            DB::commit();
        } catch (\Throwable $e) {
            return $e;
            DB::rollback();
        }
    }
}
