<?php

namespace App\Http\Controllers\gl\fo;

use DB;
use Auth;
use File;
use View;


use Excel;
use Route;
use Session;
use App\User;
use DateTime;
use Response;
use App\Acdet;
use App\Agmnf;
use App\Clhmn;
use App\Creditclm;

use Exception;
use App\Cbmast;
use App\Client;
use App\Crmast;
use App\Dtran0;
use App\Glbank;
use App\Nlctrl;
use App\Perils;
use App\Polana;
use App\Cbseqno;
use App\Cbtrans;
use App\Clmcorr;
use App\Clparam;
use App\Commana;
use App\Commdet;
use App\Currate;
use App\Doctype;
use App\Eftmast;
use App\Gltaxes;
use App\Nlsldtl;
use App\Nlslmst;
use App\Reqdocs;
use App\Cbdeduct;
use App\Commbtch;
use App\Currency;
use App\Dcontrol;
use App\Laclmreq;
use App\Nlparams;
use App\Olbranch;
use App\Payreqst;
use App\Reiacdet;
use App\Reqtaxes;
use App\RTGSMast;
use App\Approvals;
use App\Apvendors;
use App\Clhmnallo;
use App\Comesadet;
use App\Commbatch;
use App\Debitmast;
use App\Payreqstd;

use App\Polmaster;
use App\SendEmail;
use Carbon\Carbon;
use App\Chequemast;
use App\ClassModel;
use App\Comesa_ana;
use App\Commanaend;
use App\FacUploads;
use App\Gltaxtypes;
use App\Olbnknames;
use App\Olpaymethd;
use App\Treatypart;
use App\Comesabatch;
use App\Gltaxgroups;
use App\Payreqstana;
use App\Payreqtaxes;
use App\Perilsitems;
use App\Treatysetup;
use App\Agmnf_master;
use App\Aims_process;
use App\Aimsuser_web;
use App\Clmdischarge;
use App\Edms_staging;
use App\Escalate_pol;
use App\Gltaxgroupdt;
use App\AccountGroups;
use App\Approval_flow;
use App\Approval_level;
use App\Apvendorgroups;
use App\Facuploadbatch;
use App\New_log_errors;
use App\Approval_matrix;
use App\Approver_matrix;
use App\FacNotification;
use App\Models\Aimsuser;
use App\workflow\Documents;
use App\AllowedTransactions;
use App\Models\TreatyPolana;
use Illuminate\Http\Request;
use App\Mail\RequisitionMail;
use App\Process_approval_dtl;
use App\Requisition_deductions;

use Yajra\Datatables\Datatables;
use App\Events\DocReuploadToEdms;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Mail;
use App\Classes\Approvals\ApprovalsPo;
use App\Classes\Approvals\ApprovalsMgt;
use App\Classes\gl\fo\RequisitionClass;
use App\Models\ServiceProviderInvoices;
use App\Classes\Common\FileUploadManager;
use App\Events\DispatchNotificationEvent;
use App\Services\IntermediaryQueryService;
use App\ValueObjects\IntermediaryQueryParams;

class REQController extends Controller
{

    public function reqTaxes(Request $request)
    {
        $req_tax = Reqtaxes::where('id', $request->id)->first();
        return $req_tax;
    }
    public function getClientDetails(Request $request)
    {

        return Client::where('client_number', $request->client_number)->first();
    }

    public function getRequisitionTaxes(Request $request)
    {
        $req_taxes = Reqtaxes::all();
        return $req_taxes;
    }


    //START OF UNPOSTED REQUISITION

    public function commrequisition(Request $request)
    {
        /*$requisition = Payreqst::where('account_year','>',2016)
                                ->where('payments_todate',0)
                                ->where('cancelled',null)->get(); */
        $code = 'CB';
        $requisition = Payreqst::where('payments_todate', 0)
        ->whereRaw("trim(source_code)='" . $code . "'")
        ->where('entry_type_descr', 'COP')
        ->where('cancelled', null)
        ->where('checked_by', '<>', null)
        ->whereRaw(" (voucher_raised = 'N' or voucher_raised is null) ")
        ->orderBy('effective_date','desc');
        return Datatables::of($requisition)
            /*->editColumn('req_no', function ($req) {
                return formatRequisitionNo($req->req_no);
            })*/
            ->editColumn('amount', function ($amt) {
                return number_format($amt->amount, 2);
            })
            ->make(true);
    }
    public function claimrequisition(Request $request)
    {
       
        $code = 'CLM';

        $requisition = DB::select("select distinct * from PAYREQST a 
        inner join ESCALATIONS b on (a.ESCALATE_ID = b.CODE and a.WORKFLOW_ID = b.category and b.FINANCE_PROCESS = 'Y' )
         where PAYMENTS_TODATE = 0 and trim(a.source_code)='$code' and a.CANCELLED is null and (a.DECLINED != 'Y' or a.DECLINED is null)
         and (voucher_raised = 'N' or voucher_raised is null) order by effective_date DESC");
       
        return Datatables::of($requisition)
            /*->editColumn('req_no', function ($req) {
                return formatRequisitionNo($req->req_no);
            })*/
            ->editColumn('amount', function ($amt) {
                return number_format($amt->amount, 2);
            })
            ->editColumn('escalated_to', function ($req) {
                return   Aimsuser::where('user_id', '=', $req->escalated_to)->get()[0]->user_name;
            })

            ->make(true);
    }

    public function otherequisition(Request $request)
    {
        /*$requisition = Payreqst::where('account_year','>',2016)
                                ->where('payments_todate',0)
                                ->where('cancelled',null)->get(); */
        $code = 'CLM';
        $othercode = 'CB';
        $req_fos = Cbtrans::where('req_fo','Y')->pluck('descr');
        $requisition = Payreqst::where('payments_todate', 0)
            ->whereRaw("trim(source_code)!='" . $code . "'")
            ->whereRaw("trim(source_code)!='" . $othercode . "'")
            ->orWhereIn('entry_type_descr',$req_fos)
            ->where('cancelled', null)
            ->whereRaw(" (voucher_raised = 'N' or voucher_raised is null) ")->get();
        return Datatables::of($requisition)
            /*->editColumn('req_no', function ($req) {
                return formatRequisitionNo($req->req_no);
            })*/
            ->editColumn('amount', function ($amt) {
                return number_format($amt->amount, 2);
            })
            ->make(true);
    }

    public function comesarequisition(Request $request)
    {

        $code = 'CB';
        $requisition = Payreqst::where('payments_todate', 0)
        ->whereRaw("trim(source_code)='" . $code . "'")
        ->where('entry_type_descr', 'PTA')
        ->where('cancelled', null)
        ->where('checked_by', '<>', null)
        ->orderBy('effective_date','desc');
        return Datatables::of($requisition)
            /*->editColumn('req_no', function ($req) {
                return formatRequisitionNo($req->req_no);
            })*/
            ->editColumn('amount', function ($amt) {
                return number_format($amt->amount, 2);
            })
            ->make(true);
    }

    //END OF POSTED REQUISITION

    public function getEscalationUsers(Request $request)
    {
        $u_id = Auth::user()->user_id;
        // return DB::select(DB::raw("select * from aimsusers where (aims_group = 'GRP004' or aims_group = 'GRP003') and left_company <> 'Y' and user_id <> '$u_id' "));
        return  DB::select("select * from aimsusers a join aimsuprofgb b on a.user_name = b.aims_user where b.approve_rfn_req = 'Y' and a.left_company <> 'Y' and a.user_id <> '$u_id' ");
    }

    public function get_acdet_details(Request $request)
    {

        $acdet_data = Acdet::where('endt_renewal_no', $request->endt_renewal_no)
            ->where('reference', $request->reference)
            ->where('doc_type', 'CRN')
            // ->where('unallocated','>',0)
            ->get()[0];
        return $acdet_data;
    }

    /// POSTED REQUISITION DATATABLE ROUTES

    //claim
    public function postedClmRequisition()
    {
        /*$requisition = Payreqst::where('account_year','>',2016)
                                ->where('payments_todate',0)
                               ->where('cancelled',null)->get(); */
        $code = 'CLM';
        $requisition = Payreqst::where(['voucher_raised'=> 'Y'])->whereRaw("trim(source_code)='" . $code . "'");

        return Datatables::of($requisition)
            /*->editColumn('req_no', function ($req) {
                return formatRequisitionNo($req->req_no);
            })*/
            ->editColumn('amount', function ($amt) {
                return number_format($amt->amount, 2);
            })
            ->make(true);
    }

    //comm
    public function postedCommRequisition()
    {
        /*$requisition = Payreqst::where('account_year','>',2016)
                                ->where('payments_todate',0)
                               ->where('cancelled',null)->get(); */
        $code = 'CB';
        $requisition = Payreqst::where('voucher_raised', 'Y')
            ->whereRaw("trim(source_code)='" . $code . "'")
            ->take(1500)
            ->get(
                [
                    'req_no',
                    'batchno',
                    'doc_type',
                    'account_year',
                    'account_month',
                    'name',
                    'amount',
                    'source_code',

                ]
            );

        return Datatables::of($requisition)
            /*->editColumn('req_no', function ($req) {
                return formatRequisitionNo($req->req_no);
            })*/
            ->editColumn('amount', function ($amt) {
                return number_format($amt->amount, 2);
            })
            ->make(true);
    }

    public function searchcommreq(Request $request)
    {
        $from = $request->get('from');
        $to =  $request->get('to');

        $code = 'CB';
        $requisition = Payreqst::where('voucher_raised', 'Y')
            ->whereRaw("trim(source_code)='" . $code . "'")
            ->whereBetween('created_date', [$from, $to])
            ->take(1000)
            ->get(
                [
                    'req_no',
                    'batchno',
                    'doc_type',
                    'account_year',
                    'account_month',
                    'name',
                    'amount',
                    'source_code',

                ]
            );

        return Datatables::of($requisition)
            /*->editColumn('req_no', function ($req) {
                return formatRequisitionNo($req->req_no);
            })*/
            ->editColumn('amount', function ($amt) {
                return number_format($amt->amount, 2);
            })
            ->make(true);
    }

    public function uw_requisitions(Request $request)
    {
      
        $code='CLM';
        $othercode='CB';
        $requisition = Payreqst::leftJoin('polmaster', function($join) {
           $join->on('payreqst.policy_no', '=', 'polmaster.policy_no');
         })
                                ->where('payments_todate',0)
                                ->whereRaw("trim(source_code)!='".$code."'")
                                ->whereRaw("trim(entry_type_descr)!='COP'")
                                ->where(function ($query) {
									$query->where("source_code",'U/W')
										->orWhere("source_code",'FAC')
										->orWhere("source_code",'R/I');
								})
                                ->where(function ($query) {
									$query->where("cancelled",'N')
										->orWhereNull("cancelled");
								})
                                ->where(function ($query) {
									$query->where('voucher_raised', '!=', 'Y')
										->orWhereNull('voucher_raised');
								})
                                ->get();
        return Datatables::of($requisition)
        ->editColumn('req_no',function($req){
            return formatRequisitionNo($req->req_no);
        })
        ->editColumn('amount',function($amt){
            return number_format($amt->amount,2);
            
        })
        ->editColumn('approved_date',function($date){
           //if(!empty($date->approved_date)){
             return [
                   'display' => formatDate($date->approved_date),
                   'timestamp' => $date->approved_date,
             ];
           //}
        })

        ->editColumn('cancelled', function($req){
            return $req->cancelled == 'Y' ? 'YES' : 'NO';
        })
        ->make(true); 
    }

        //END OF POSTED REQUISITION

    //others
    public function postedRequisition()
    {
        /*$requisition = Payreqst::where('account_year','>',2016)
                                ->where('payments_todate',0)
                               ->where('cancelled',null)->get(); */
        $code = 'CLM';
        $otherCode = 'CB';

        $requisition = Payreqst::where('voucher_raised', 'Y')
            ->whereRaw("trim(source_code)!='" . $code . "'")
            ->take(800)
            ->orwhereRaw("trim(source_code)!='" . $othercode . "'");


        return Datatables::of($requisition)
            /*->editColumn('req_no', function ($req) {
                return formatRequisitionNo($req->req_no);
            })*/
            ->editColumn('amount', function ($amt) {
                return number_format($amt->amount, 2);
            })
            ->make(true);
    }
    public function searchotherreq(Request $request)
    {
        /*$requisition = Payreqst::where('account_year','>',2016)
                                ->where('payments_todate',0)
                               ->where('cancelled',null)->get(); */


        $from = $request->get('from');
        $to =  $request->get('to');
        $code = 'CLM';
        $otherCode = 'CB';


        $requisition = Payreqst::where('voucher_raised', 'Y')
            ->whereRaw("trim(source_code)!='" . $code . "'")
            ->whereBetween('created_date', [$from, $to])
            ->take(600)
            ->orwhereRaw("trim(source_code)!='" . $othercode . "'");


        return Datatables::of($requisition)
            /*->editColumn('req_no', function ($req) {
                return formatRequisitionNo($req->req_no);
            })*/
            ->editColumn('amount', function ($amt) {
                return number_format($amt->amount, 2);
            })
            ->make(true);
    }

    public function add_deduct(Request $request)
    {

        $add_deduct = Cbdeduct::where('code', '=', $request->code)->get()[0]->add_deduct;

        return $add_deduct;
    }

    public function showRequisitions(Request $request)
    {
        $branch = str_pad(Auth::user()->branch, 3, '0', STR_PAD_LEFT);

        $today = Carbon::today();
        $office = Nlparams::where('prid', 'OFF')
            ->whereRaw("trim(prsno)='" . $branch . "'")
            ->get();

        $cbdeduct = Cbdeduct::where('doc_type', 'PAY')->get();


        $dat = array(
            'main' => 'Front Office',
            'module' => 'Payments',
            'submodule' => 'Requisitions'
        );

        $tax_groups = Gltaxgroups::all();
        return view::make('gl.fo.requisitions', compact('today', 'office', 'cbdeduct', 'tax_groups'))
            ->with('dat', $dat);
    }


    public function getRefundUnallocated(Request $request)
    {
        $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        if($request->onAccount=='N'){
          $acdet = DB::table('' . $gb . '.acdet')
            ->join('' . $gb . '.client', function ($join) {
                $join->on('client.client_number', '=', 'acdet.client_number');
            })
            ->where('acdet.endt_renewal_no', '!=', 'null')
            ->where('acdet.client_number', '!=', 'null')
            ->where('unallocated', '<', '-1')
            ->where('acdet.doc_type', '=', 'REC')
            ->get(['acdet.reference', 'client.name', 'acdet.unallocated', 'acdet.client_number', 'doc_type']);
        }else{
           
          
            $sql="select a.reference ,a.currency_code,a.currency_rate, a.policyholder,a.unallocated,a.foreign_unallocated,
            a.allocated,a.foreign_allocated,a.endt_renewal_no from acdet a join cbmast c on LPAD(c.dtrans_no, 6,0)|| c.account_year
            =a.reference 
            and a.entry_type_descr=c.entry_type_descr and a.doc_type=c.doc_type and a.branch=c.branch
            and a.agent=TO_NUMBER(c.agent_no) where a.unallocated<0 and c.doc_type='REC'
            and c.entry_type_descr!=c.doc_type and c.on_account='A' and 
            (c.cancelled is null or c.cancelled='N')";
            
            $receipts=DB::select($sql);

          
          
                            
            return $receipts;
        }
       
      

        return $acdet;

        // return Acdet::where('unallocated','<','-1')
        //                  ->where('endt_renewal_no','!=','null')
        //                  ->where('client_number','!=','null')
        //              ->get(['reference','policyholder','unallocated','client_number']);
    }

    public function acdetfetchclientname(Request $request)
    {

        if($request->onaccount=='Y'){
            $acdet_rec = Acdet::where('reference', '=', $request->reciept_no)
                                ->where('doc_type', '=', "REC")
                                ->where('unallocated', '<', '-1')
                                ->get()[0];
            $debit_note= Acdet::where('reference', '=', $request->reciept_no)
                                ->where('doc_type', '=', "DRN")
                                ->get()[0];
                               
            $acdet_rec->name=$acdet_rec->policyholder;
            $acdet_rec->policy_no=$debit_note->policy_no;
            $acdet_rec->endt_renewal_no=$debit_note->endt_renewal_no;

            return $acdet_rec;  
               
                    
        }else{
            //['client_number','unallocated','currency_code','currency_rate']
            $acdet_rec = Acdet::where('reference', '=', $request->reciept_no)
            ->where('unallocated', '<', '-1')
            ->get()[0];
            $client_number = $acdet_rec->client_number;
            $unallocated = $acdet_rec->unallocated;

            $client = Client::where('client_number', $client_number)->get()[0];
            $client->branch = $acdet_rec->branch;
            $client->agent = $acdet_rec->agent;
            $client->endt_renewal_no = removePolicyOrClaimFormat($acdet_rec->endt_renewal_no);
            $client->policy_no = removePolicyOrClaimFormat($acdet_rec->policy_no);
            $client->unallocated = $unallocated;
            $client->currency_code = $acdet_rec->currency_code;
            $client->currency_rate = $acdet_rec->currency_rate;
            return $client;
        }
       
    }


    public function postedReqs(Request $request)
    {
        $branch = str_pad(Auth::user()->branch, 3, '0', STR_PAD_LEFT);

        $today = Carbon::today();
        $office = Nlparams::where('prid', 'OFF')
            ->whereRaw("trim(prsno)='" . $branch . "'")
            ->get();

        $cbdeduct = Cbdeduct::where('doc_type', 'PAY')->get();

        $dat = array(
            'main' => 'Front Office',
            'module' => 'Payments',
            'submodule' => 'Posted Requisitions'
        );


        return view::make('gl.fo.posted_requisitions', compact('today', 'office', 'cbdeduct'))
            ->with('dat', $dat);
    }

    public function checkRaiseReq(Request $request)
    {
        $allowed = Gate::allows('raise-fo-payment-requisition') ? "Y" : "N";

		// Return the result as a JSON response
		return response()->json(["status" => $allowed]);

    }

    public function uwReqControls(Request $request)
    {


        $polno = $request->policy_no;
        $schem = schemaName();



        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
        /// STATUS CODES

        ///  STATUS  || MEANING

        /// | 1        || SHOW MODAL
        /// | 0        ||DONT SHOW MODAL SHOW ERROR MESSAGE
        /// | 2        ||SHOW APPROVALS BTN
        /// | 3        || WAITING TO BE APPROVED

        //check if ipf


        $raisedReq =  Payreqst::where('policy_no', '=', $request->policy_no)
            ->where('claim_no', $request->endt_no)
            ->whereNull("cancelled")
            ->whereNull('voucher_raised')
            //     ->where('CANCELLED_BY','!=',null)
            ->get();



        if (count($raisedReq) > 0) {
            $response[] = [
                'status' => 3,
                'message' => "There is already an existing requisition "
            ];
        } else {

            $dcontrol = Dcontrol::where('endt_renewal_no', $request->endt_no)
                ->where('policy_no', $request->policy_no)->get(['ipf_flag', 'req_approvedby', 'req_approved']);
            $dcontrol = $dcontrol[0];

            $ipf_flag = $dcontrol->ipf_flag;


            if ($ipf_flag == 'Y') {
                $response[] = [
                    'status' => 1,
                    'message' => ''
                ];
            } else {

                //CHECk IF THERE ARE CLAIMS

                $claims = DB::table('' . $gb . '.clhmn')
                    ->join('' . $gb . '.polmaster', function ($join) {
                        $join->on('clhmn.policy_no', '=', 'polmaster.policy_no');
                    })
                    ->where('clhmn.policy_no', '=', $polno)
                    ->whereRaw("(clhmn.acc_date > polmaster.period_from or clhmn.acc_date = polmaster.period_from)")
                    ->get();

                $claims_count = count($claims);




                if ($claims_count > 0  && $dcontrol->req_approved != 'Y'  && !isset($dcontrol->req_approvedby)) {

                    $response[] = [
                        'status' => 2,
                        'message' => 'The policy  has lodged claims.It Needs to be Approved '
                    ];
                } else if ($claims_count > 0 && isset($dcontrol->req_approvedby) && $dcontrol->req_approved != 'Y' && $dcontrol->req_approved != 'N') {

                    $user = Aimsuser_web::whereRaw("trim(user_name) = '" . trim($dcontrol->req_approvedby) . "'")->first()->name;

                    $response[] = [
                        'status' => 3,
                        'message' => "Requisition awaiting approval by " . $user . ""
                    ];
                } else if ($claims_count > 0 && isset($dcontrol->req_approvedby) && $dcontrol->req_approved == 'N') {

                    $user = Aimsuser_web::whereRaw("trim(user_name) = '" . trim($dcontrol->req_approvedby) . "'")->first()->name;

                    $response[] = [
                        'status' => 3,
                        'message' => "Requisition was Declined"
                    ];
                } else if ($claims_count > 0 && $dcontrol->req_approved == 'Y') {

                    //get outstanding
                    $acdet_rec = Acdet::where('policy_no', $request->policy_no)
                        ->where('source', 'U/W')
                        ->where('doc_type', 'DRN')
                        ->sum('unallocated');

                    $client_check = (int)Acdet::where('client_number', $request->client_no)
                        ->where('source', 'U/W')
                        ->where('doc_type', 'DRN')
                        ->sum('unallocated');

                    if ($acdet_rec > 0) {

                        $response[] = [
                            'status' => 3,
                            'message' => "There is an outstanding premium on the policy"
                        ];
                    } else if ($client_check > 0) {
                        //check if there is outstanding balances on the other policies for the same client

                        $response[] = [
                            'status' => 3,
                            'message' => "The client has other outstanding balances"
                        ];
                    } else {
                        $response[] = [
                            'status' => 1,
                            'message' => ''
                        ];
                    }
                } else {
                    // $response[] = ['status'=>1,
                    //     'message'=>''];

                    //get outstanding
                    $acdet_rec = Acdet::where('policy_no', $request->policy_no)
                        ->where('source', 'U/W')
                        ->where('doc_type', 'DRN')
                        ->sum('unallocated');

                    $client_check = (int)Acdet::where('client_number', $request->client_no)
                        ->where('source', 'U/W')
                        ->where('doc_type', 'DRN')
                        ->sum('unallocated');

                    if ($acdet_rec > 0) {


                        $response[] = [
                            'status' => 3,
                            'message' => "There is an outstanding premium on the policy"
                        ];
                    } else if ($client_check > 0) {
                        //check if there is outstanding balances on the other policies for the same client    

                        $response[] = [
                            'status' => 3,
                            'message' => "The client has other outstanding balances"
                        ];
                    } else {
                        $response[] = [
                            'status' => 1,
                            'message' => ''
                        ];
                    }
                }
            }
        }

        echo json_encode($response);
    }




    public function approve(Request $request)
    {
        $user = Aimsuser_web::where('user_id', $request->approver)->first();


        $this->uwreqapprovalMsg($request->approver, 0, $request->endt_renewal_no);
        return Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)
            ->update([
                'req_approvedby' => $user->user_name,
                'req_requestedby' => Auth::user()->user_name
            ]);
    }

    public function uwreqapprovalMsg($user_id, $req_no, $endt_renewal_no)
    {

        $user = Aimsuser_web::where('user_id', $user_id)->get();
        $name =  Auth::user()->user_name;
        $sender_id =  Auth::user()->user_id;


        $count = Escalate_pol::where('sent_by', $sender_id)->max('escalate_id');
        $next = $count + 1;
        //$user_id = $request->escalate_doc;
        $recieverdet = Aimsuser_web::where('user_id', $user_id)->first();
        $reciever = trim($recieverdet->user_name);
        $sent_id = $user_id;
        $emailaddr  = $recieverdet->email;

        //$order_no = $request->order_no;


        $escalate = new Escalate_pol;
        $escalate->endorse_no = $endt_renewal_no;
        $escalate->escalate_id = $next;


        $escalate->req_no = $req_num;
        $escalate->type = 'U/W REQ';
        $escalate->description = 'REQUISITION WITH CLAIMS APPROVAL';
        $mess = "Dear $reciever , Kindly Approve the Requisition with outstanding claims.Thank You. ";
        $category = 'REQUISITION WITH CLAIMS APPROVAL';

        $escalate->sent_by = $name;
        // $escalate->claim_no =$request->claim_no;
        $escalate->sent_to = $sent_id;
        $escalate->sent_by = $sender_id;

        $escalate->user_name = $reciever;
        $escalate->created_at =  Carbon::now();


        try {
            $escalate->save();
        } catch (Exception $e) {
        }
        $sendemail = new Sendemail;
        $sendemail->category = $category;
        $sendemail->receiver = $emailaddr;
        $sendemail->message = $mess;
        $sendemail->creator = $name;

        $sendemail->save();
    }

    public function approvereq(Request $request)
    {

        $dcontrol = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)
            ->where('policy_no', $request->policy_no)->get()[0];

        $user_id = Aimsuser_web::whereRaw("trim(user_name) = '" . trim($dcontrol->req_requestedby) . "'")->first()->user_id;
        $this->uwreqapprovedMsg($user_id, null, $request->endt_renewal_no);
        return Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)
            ->where('policy_no', $request->policy_no)
            ->update([
                'req_approvedby' => Auth::user()->user_name,
                'req_approved' => 'Y'

            ]);
    }

    public function uwreqapprovedMsg($user_id, $req_no, $endt_renewal_no)
    {

        $user = Aimsuser_web::where('user_id', $user_id)->get();
        $name =  Auth::user()->user_name;
        $sender_id =  Auth::user()->user_id;


        $count = Escalate_pol::where('sent_by', $sender_id)->max('escalate_id');
        $next = $count + 1;
        //$user_id = $request->escalate_doc;
        $recieverdet = Aimsuser_web::where('user_id', $user_id)->first();
        $reciever = trim($recieverdet->user_name);
        $sent_id = $user_id;
        $emailaddr  = $recieverdet->email;

        //$order_no = $request->order_no;


        $escalate = new Escalate_pol;
        $escalate->endorse_no = $endt_renewal_no;
        $escalate->escalate_id = $next;


        $escalate->req_no = $req_num;
        $escalate->type = 'U/W REQ';
        $escalate->description = 'REQUISITION APPROVED';
        $mess = "Your requisition has been approved.Thank You. ";
        $category = 'REQUISITION APPROVAL';

        $escalate->sent_by = $name;
        // $escalate->claim_no =$request->claim_no;
        $escalate->sent_to = $sent_id;
        $escalate->sent_by = $sender_id;

        $escalate->user_name = $reciever;
        $escalate->created_at =  Carbon::now();


        try {
            $escalate->save();
        } catch (Exception $e) {
        }
        $sendemail = new Sendemail;
        $sendemail->category = $category;
        $sendemail->receiver = $emailaddr;
        $sendemail->message = $mess;
        $sendemail->creator = $name;

        $sendemail->save();
    }


    public function declinereq(Request $request)
    {

        $dcontrol = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)
            ->where('policy_no', $request->policy_no)->get()[0];

        $user = Aimsuser_web::where("user_name", '=', $dcontrol->req_requestedby)->first()->user_id;

        $this->uwDeclinereqapprovalMsg($user, $request->declinereason, $dcontrol->endt_renewal_no);

        return Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)
            ->where('policy_no', $request->policy_no)
            ->update([
                'req_approvedby' => Auth::user()->user_name,
                'req_approved' => 'N',
                'req_decline_reason' => $request->declinereason
            ]);
    }
    public function uwDeclinereqapprovalMsg($user_id, $message, $endt_renewal_no)
    {

        $user = Aimsuser_web::where('user_id', $user_id)->get();
        $name =  Auth::user()->user_name;
        $sender_id =  Auth::user()->user_id;


        $count = Escalate_pol::where('sent_by', $sender_id)->max('escalate_id');
        $next = $count + 1;
        //$user_id = $request->escalate_doc;
        $recieverdet = Aimsuser_web::where('user_id', $user_id)->first();
        $reciever = trim($recieverdet->user_name);
        $sent_id = $user_id;
        $emailaddr  = $recieverdet->email;

        //$order_no = $request->order_no;


        $escalate = new Escalate_pol;
        $escalate->endorse_no = $endt_renewal_no;
        $escalate->escalate_id = $next;


        $escalate->req_no = $req_num;
        $escalate->type = 'U/W REQ';
        $escalate->description = 'Requisition Declined  ';
        $mess = "Reason has been Declined Reason: $message  ";
        $category = 'REQUISITION DENIED';

        $escalate->sent_by = $name;
        // $escalate->claim_no =$request->claim_no;
        $escalate->sent_to = $sent_id;
        $escalate->sent_by = $sender_id;

        $escalate->user_name = $reciever;
        $escalate->created_at =  Carbon::now();


        try {
            $escalate->save();
        } catch (Exception $e) {
            // return $e;  
        }
        $sendemail = new Sendemail;
        $sendemail->category = $category;
        $sendemail->receiver = $emailaddr;
        $sendemail->message = $mess;
        $sendemail->creator = $name;

        $sendemail->save();
    }
    public function checkCancelReq(Request $request)
    {
        $allowed = Gate::allows('cancel-fo-requisition') ? "Y" : "N";

		// Return the result as a JSON response
		return response()->json(["status" => $allowed]);

    }

    public function checkApproveReq(Request $request)
    {
        $allowed = Gate::allows('approve-payment') ? "Y" : "N";

		// Return the result as a JSON response
		return response()->json(["status" => $allowed]);

    }



    public function checkAuthorizePay(Request $request)
    {
        $allowed = Gate::allows('authorize-payment') ? "Y" : "N";

		// Return the result as a JSON response
		return response()->json(["status" => $allowed]);

    }

    public function checkMakePayment(Request $request)
    {
        $allowed = Gate::allows('make-fo-payment') ? "Y" : "N";

		// Return the result as a JSON response
		return response()->json(["status" => $allowed]);

    }


    //ajax helper routes
    public function getCbdeduct(Request $request)
    {
        $cbdeduct = Cbdeduct::where('doc_type', 'PAY')->get();
        return $cbdeduct;
    }

    public function getEntries(Request $request)
    {
        if($request->has('flag') == 'U/W'){
            $cbtrans = Cbtrans::where('doc_type', 'PAY')->whereIn('descr', ['REF', 'FAC','TRT'])->get();
            return $cbtrans;
        }

        else{
            $cbtrans = Cbtrans::where('doc_type', 'PAY')->where('req_fo','Y')->get();
            return $cbtrans;
        }
    }

    public function getDepartments(Request $request)
    {
        $departments = Nlparams::where('prid', 'DEP')->get();
        return $departments;
    }

    public function getSubledgers(Request $request)
    {
        $subledgers = Nlparams::where('prid', 'SLT')->get();
        return $subledgers;
    }

    public function getBanknames(Request $request)
    {
        $olbnknames = Olbnknames::all();
        return $olbnknames;
    }

    public function getCurrencies(Request $request)
    {
        $currencies = Currency::orderBy('currency_code')->get();
        return $currencies;
    }

    public function storeRequisition(Request $request)
    {

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try {
            //DB::Transaction(function () use ($request) {
            $debitmast = Debitmast::where('endt_renewal_no', $request->endt_renewal_no)->first();

            $source_code = "";
            if ($request->source_code == 'FAC') {
                $source_code = 'CRD';
            } 
            
            else {
                $source_code = $request->source_code;
            }

            $credit_note_no = $request->credit_note_no;
            
            if ($request->entry_type_descr == 'RRF') {
                $credit_note_no = $request->rec_no;
            }

            if ($source_code == 'CRD' || $source_code == 'U/W') {
                $acdet = Acdet::where('source', $source_code)
                    //->where('reference', $credit_note_no)
                    ->whereRaw("trim(reference)='" . $credit_note_no . "'")
                    ->where('doc_type', 'CRN')
                    ->get()[0];

                if ($request->entry_type_descr == 'RRF') {
                    $acdet = Acdet::where('source', 'CB')
                        //->where('reference', $credit_note_no)
                        ->whereRaw("trim(reference)='" . $credit_note_no . "'")
                        ->where('doc_type', 'REC ')
                        ->get()[0];
                }
            }

            ###new 
            if($request->on_acc=="Y"){
                $on_account='A';
            }
            
            else{
                $on_account='P';
            }

            $currency_code = $request->currency_code;

            $currency_rate = $request->currency_rate;

            if ($request->analysefac == 'Y' && $request->analyseoptions != 'B') {
                $currency_code = $request->faccurrency_code;
                $currency_rate = $request->fac_currency_rate;
            }


            $payreqst_data = [
                'offcd' => $request->offcd,
                'dept_code' => $request->dept_code,
                'entry_type_descr' => $request->entry_type_descr,
                'branch' => $request->branch,
                'agent' => $request->agent,
                'name' => $request->name,
                'class' => $request->class,
                'uw_year' => $request->uw_year,
                'source_code' => $request->source_code,
                'subledger' => $request->subledger,
                'subledger_code' => $request->subledger_code,
                'slhead1' => $request->slhead1,
                'client_number' => $request->client_number,
                'policy_no' => removePolicyOrClaimFormat($request->policy_no), //*
                'claim_no' => $request->endt_renewal_no, //*
                'gross_amount' => str_replace(',', '', $request->gross_amount),
                'amount' => str_replace(',', '', $request->amount),
                'nett_amount' => str_replace(',', '', $request->amount),
                'local_gross_amount' => str_replace(',', '', $request->local_gross_amount),
                'local_amount' => str_replace(',', '', $request->amount) * $currency_rate,
                'local_nett_amount' => str_replace(',', '', $request->amount) * $currency_rate,
                'payee_bank_account' => $request->payee_bank_account,
                'payee_bank_code' => $request->payee_bank_code,
                'payee_bank_name' => $request->payee_bank_name,
                'payee_bank_branch_code' => $request->payee_bank_branch_code,
                'payee_bank_branch' => $request->payee_bank_branch,
                'vat_no' => $request->vat_no,
                'pin_no' => $request->vat_no,
                'analyse_comm' => $request->analyse_comm,
                'analyse' => $request->analyse,
                'final_settle' => $request->final_settle,
                'currency_code' => $currency_code,
                'currency_rate' => $currency_rate,
                'narration' => $request->narration,
                'credit_note_no' => substr($credit_note_no, 0, 6),
                'ref_doc_type' => $acdet->doc_type,
                'pay_method' => $request->pay_method,
                'analyse_policy' => 'N',
                'on_account'=>$on_account,
                'unallocated_total' =>   str_replace(',', '', $request->amount),
                'local_unallocated_total' =>   str_replace(',', '', $request->amount) * $currency_rate,
                'tax_group' => (int)$request->tax_group,
                'break_down' => $request->break_down,

                'pay_method' => $pay_method,
                'credit_account' => $cr_account,
                'debit_account' => $dr_account,
                'debit_subaccount' => $subaccount,
                'cheque_no' => $cheque_no,
            ];   

            if ($request->source_code == 'CLM') {
                $payreqst_data['multiclaims'] = 'Y';
            }

            if ($request->source_code == 'R/I') {
                $payreqst_data['uw_year'] = $request->treaty_year;
                $payreqst_data['treaty_code'] = $request->treaty_type;
                $payreqst_data['quarter'] = $request->treaty_quarter;
                $payreqst_data['analyse_policy'] = 'Y';

                if($request->treaty_analyzed == 'B'){

                    $broker_agent = $request->agent;

                    $broker_branch = (int)($request->branch);

                    $branch =  null;

                    $agent_no = null;

                    $payreqst_data['branch'] = $branch;

                    $payreqst_data['agent'] = $agent_no;

                    $payreqst_data['broker_branch'] = $broker_branch;

                    $payreqst_data['broker_agent'] = $broker_agent;
                }

                elseif ($request->treaty_analyzed == 'P') {
                    $branch =  (int)($request->branch);

                    $agent1 =  $request->agent;
                    
                    $agent_no = $agent1;

                    $payreqst_data['branch'] = $branch;
                    
                    $payreqst_data['agent'] = $agent_no;
                }
            }

            if ($request->analyse == 'Y') {
                $w_total_deduction = 0;
                $total_addition = 0;
                $total_deduction = 0;

                for ($i = 0; $i < count($request->deduction); $i++) {
                    $n = $i + 1;

                    //check if deduction or addition
                    $deduct = Cbdeduct::where('code', '=', $request->deduction[$i])->get()[0]->add_deduct;

                    if ($deduct == 'A') {
                        $add_amount = $request->deduction_amount[$i];
                        //$amount=$payreqst_data['local_gross_amount'];
                        // $w_total_deduction = $amount+$add_amount;
                        $total_addition += $add_amount;
                    }

                    if ($deduct == 'D') {
                        $add_amount = $request->deduction_amount[$i];
                        $amount = $payreqst_data['local_gross_amount'];
                        $total_deduction += $add_amount;
                        //$w_total_deduction = $amount-$add_amount;
                    }

                    $payreqst_data['deduction_amount_' . $n] = $request->deduction_amount[$i];
                    $payreqst_data['deduction_code_' . $n] = $request->deduction[$i];
                }

                //total deduction and addition
                $w_total_deduction = ($payreqst_data['gross_amount'] + $total_addition) - $total_deduction;
                $payreqst_data['nett_amount'] = $w_total_deduction;
                $payreqst_data['amount'] = $w_total_deduction;
                $payreqst_data['local_amount'] = $w_total_deduction * $currency_rate;
            }

            if ($request->analysefac && $source_code == 'CRD') {
                $payreqst_data['analyse_policy'] = $request->analysefac;
            }

            if($request->fac_analyzed_by == 'B'){
                $broker_agent = $request->agent;

                $broker_branch = (int)($request->branch);

                $branch =  null;

                $agent_no = null;

                $payreqst_data['branch'] = $branch;

                $payreqst_data['agent'] = $agent_no;

                $payreqst_data['broker_branch'] = $broker_branch;

                $payreqst_data['broker_agent'] = $broker_agent;
            }

            elseif ($request->fac_analyzed_by == 'P') {
                $branch =  (int)($request->branch);

                $agent =  $request->agent;

                $payreqst_data['branch'] = $branch;
                
                $payreqst_data['agent'] = $agent;
            }

            if ($request->analysefac == 'Y' && $request->analyseoptions == "B") {
                $payreqst_data['batch_no'] = $request->analysefacbatch;
            }

            

            $reqClass = new RequisitionClass();

            $create_payreqst = $reqClass->createPayreqst($payreqst_data);

            $req_no = $create_payreqst['req_no'];
            $payreqst = Payreqst::where('req_no', $req_no)->first();
            $reference = str_pad($payreqst->dtrans_no, 6, '0', STR_PAD_LEFT) . $payreqst->account_year;

            //analyse requisition manually
            if($request->break_down == 'Y'){
                for($i = 0; $i < count($request->glhead); $i++){
                    $payreqstana = Payreqstana::create([
                        'requisition_no' => $req_no,
                        'offcd' => $request->offcd,
                        'entry_type_descr' => $request->entry_type_descr,
                        'doc_type' => 'PRQ',
                        'glhead' => $request->glhead[$i],
                        'narration' => $request->narration_ana[$i],
                        'reference' => $reference,
                        'item_no' => $i + 1,
                        'entry_type_descr1' => $request->entry_type_descr,
                        'trans_date' => Carbon::today(),
                        'dr_cr' => 'D',
                        'amount' => str_replace(',', '', $request->cbmastana_amt[$i]) * $currency_rate,
                        'foreign_amount' => str_replace(',', '', $request->cbmastana_amt[$i]),
                        'currency_code' => $currency_code,
                        'currency_rate' => $currency_rate,
                        'source_code' => $request->source_code,
                    ]);
                }
            }

            //multi claims requisitions 
            if (isset($request->multi_claim_no)) {
                for ($i = 0; $i < count($request->multi_claim_no); $i++) {
                    $item = $i + 1;
                    $clhmnallo_data = [
                        'doc_type' => $payreqst->doc_type,
                        'offcd' => $payreqst->offcd,
                        'entry_type_descr' => $payreqst->entry_type_descr,
                        'reference' => $reference,
                        'ln_no' => 0,
                        'total_amount' => $payreqst->gross_amount,
                        'foreign_total_amount' => $payreqst->amount,
                        'debit_account' => $request->multi_dr_account[$i],
                        'credit_account' => $request->multi_cr_account[$i],
                        'currency_code' => $payreqst->currency_code,
                        'currency_rate' => $payreqst->currency_rate,
                        'account_year' => $payreqst->account_year,
                        'dtrans_no' => $payreqst->dtrans_no,
                        'account_month' => $payreqst->account_month,
                        'claim_no' => $request->multi_claim_no[$i],
                        'item_no' => $item,
                        'class' => $request->multi_class[$i],
                        'foreign_amount' => $request->multi_amount[$i],
                        'amount' => $request->multi_amount[$i] * $payreqst->currency_rate,
                        'claimant_code' => $request->multi_claimant[$i],
                        'peril' => $request->multi_peril[$i],
                        'perilitem' => $request->multi_perilitem[$i],
                        'requisition_no' => $payreqst->req_no,
                    ];
                    //dd($clhmnallo_data);
                    $clhmnallo = Clhmnallo::insert($clhmnallo_data);
                }
            }

            if($request->entry_type_descr=='REF' && $request->source_code ='U/W'){
                $this->rfn_req_doc($req_no, $credit_note_no, $request, $request->source_code);
            }

            ##analsysed FAC and treaty
            if ($request->analysefac == 'Y') {
                if($request->fac_analyzed_by == 'B'){
                    $broker_agent = $request->agent;
                    
                    $broker_branch = (int)($request->branch);
    
                    $branch =  null;
    
                    $agent_no = null;
    
                    $payreqst_data['branch'] = $branch;
    
                    $payreqst_data['agent'] = $agent_no;
    
                    $payreqst_data['broker_branch'] = $broker_branch;
    
                    $payreqst_data['broker_agent'] = $broker_agent;
                }
    
                elseif ($request->fac_analyzed_by == 'P') {
                    $branch =  (int)($request->branch);
    
                    $agent =  $request->agent;
    
                    $payreqst_data['branch'] = $branch;
                    
                    $payreqst_data['agent'] = $agent;
                }

                
                $req_no = $create_payreqst['req_no'];
                $payreqst = Payreqst::where('req_no', $req_no)->first();

                $reference = str_pad($payreqst['dtrans_no'], 6, '0', STR_PAD_LEFT) . $payreqst['account_year'];
                
                if($request->analyseoptions == "B"){

                    $facult_batch = FacUploads::where('batch_no', $request->analysefacbatch)->get();

                    $selectedendorsments = [];
                    $selectedreferences = [];
                    
                    foreach ($facult_batch as $record) {
                        $selectedendorsments[] = $record->endt_renewal_no;
                        $selectedreferences[] = $record->reference;
                    } 
                    
                    $processed = Facuploadbatch::where('batch_no', $request->analysefacbatch)
                                            ->update([
                                                'processed' => 'Y'
                                            ]);
                    

                }else{

                    $selectedendorsments = explode(',', $request->selectedendorsments[0]);

                    $selectedreferences = explode(',', $request->selected_reference[0]);

                }
            
                  
                for ($i = 0; $i < count($selectedendorsments); $i++) {
                    $selected_ref = str_replace('/', '', $selectedreferences[$i]);
                    //GET THE RECORD FROM ACDET
                    $acdet = Acdet::where('source', 'CRD')->where('doc_type', 'CRN')
                    ->where('endt_renewal_no', $selectedendorsments[$i])->where('reference', $selected_ref)->get()[0];

                    if($request->fac_analyzed_by == 'B'){
                        $broker_branch = (int)($request->branch);
                       
                        $broker_agent = $request->agent;
                    }

                    if($request->fac_analyzed_by == 'P'){
                        $broker_branch = null;

                        $broker_agent = null;
                    }
                    
                    //INSERT TO POLANA
                    $polana_data =[
                        'offcd' => $payreqst_data['offcd'],
                        'req_no' => $req_no,
                        'doc_type' => 'PRQ',
                        'entry_type_descr' => $payreqst_data['entry_type_descr'],
                        'entry_type_descr1' => $payreqst_data['entry_type_descr'],
                        'reference' => $reference,
                        'ln_no' => $i + 1,
                        'line_no' => $payreqst['line_no'],
                        'policy_no' => $acdet->policy_no,
                        'endt_renewal_no' => $selectedendorsments[$i],
                        'item_no' => $i + 1,
                        'dr_cr' => 'D',
                        'total_amount' => $payreqst_data['amount'],
                        'policy_amount' => abs($acdet->foreign_unallocated),
                        'unallocated_amount' => abs($acdet->foreign_unallocated),
                        'allocated_amount' => 0,
                        'branch' => $acdet->branch,
                        'agent' => $acdet->agent,
                        'ref_dtrans_no' => substr($acdet->reference, 0, 6),
                        'ref_account_year' => substr($acdet->reference, 6, 4),
                        'ref_doc_type' => $acdet->doc_type,
                        'client_number' => $acdet->client_number,
                        'broker_branch'=>$broker_branch,
                        'broker_agent'=>$broker_agent,
                    ];


                    $savepolana=Polana::insert($polana_data);
                } 
            }

            ##insert treaty_polana
            if($request->source_code == 'R/I'){
                if($request->analyzed_treaty_rec == 'Y'){
                    $itemno = 1;
                    if (count($request->selectedReferences) > 0) {
                        for ($i=0; $i < count($request->selectedReferences); $i++) {
                            $tamount = floatval(str_replace(',', '', $request->treatyamount[$i]));


                            if($request->treaty_analyzed == 'B'){

                                $tbroker_branch = (int)($request->branch);

                                $tbroker_agent = $request->agent;

                                $item = Reiacdet::where('doc_type', 'CRN')
                                ->where('quarter', intval($request->treaty_quarter))
                                ->where('account_year', intval($request->treaty_year))
                                ->where('treaty_code', $request->treaty_type)
                                ->where('dtrans_no', $request->selectedReferences[$i])
                                ->where('broker_branch', $tbroker_branch)
                                ->where('broker_agent', $tbroker_agent)
                                ->first();

                                $branch =  $item->branch;

                                $agent = $item->agent;

                                $ref_dtrans = $item->trans_number;
                            }
                
                            elseif ($request->treaty_analyzed == 'P') {
                                
                                
                                $branch =(int)substr($request->branch);

                                $agent = $request->agent;

                                $item = Reiacdet::where('doc_type', 'CRN')
                                ->where('quarter', intval($request->treaty_quarter))
                                ->where('account_year', intval($request->treaty_year))
                                ->where('treaty_code', $request->treaty_type)
                                ->where('dtrans_no', $request->selectedReferences[$i])
                                ->where('branch', $branch)
                                ->where('agent', $agent)
                                ->first();

                                $tbroker_branch = $item->broker_branch;

                                $tbroker_agent = $item->broker_agent;

                                $ref_dtrans = $item->trans_number;

                            }
                
                            $polana = TreatyPolana::create([
                                'req_no'=> $req_no,
                                'item_no'=>$itemno,
                                'branch'=> $branch,
                                'agent'=> $agent,
                                'doc_type'=>'PRQ',
                                'policy_no'=>$request->policy_no,
                                'claim_no'=>$request->claim_no,
                                'foreign_amount'=>$tamount,
                                'amount'=>$tamount * $request->currency_rate,
                                'currency_rate'=>$request->currency_rate,
                                'currency_code'=>$request->currency_code,
                                'line_no'=>0,
                                'dr_cr'=> 'D',
                                'entry_type_descr'=>$request->entry_type_descr,
                                'broker_branch'=>$tbroker_branch,
                                'broker_agent'=>$tbroker_agent,
                                'ref_dtrans'=>intval($ref_dtrans),
                            ]);

       

                            $itemno = $itemno+1;
                        }
                    }
                }
            } //end insert treaty polana
            

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            $req_details = Payreqst::where('req_no', $req_no)->get();
            echo $req_details;

            Session::Flash('success', 'Requisition generated successfully');
            ##end
        }

        catch (\Throwable $e) {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
           
            Session::Flash('error', $e->getMessage());
			$error_msg = json_encode($e->getMessage());
            $referrence = "{$request->entry_type_descr},{$request->name}";
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);
        }
    }

    public function reqView(Request $request)
    {

        $req_no = $request->get('req_no');
        $req_details = DB::table('payreqst')
            ->where('payreqst.req_no', $req_no)
            ->leftjoin('nlparams as debit', function ($join) {
                $join->on('payreqst.debit_account', '=', 'debit.prsno')
                    ->where('debit.prid', 'GLH');
            })
            ->leftjoin('nlparams as credit', function ($join) {
                $join->on('payreqst.credit_account', '=', 'credit.prsno')
                    ->where('credit.prid', 'GLH');
            })
            ->select(
                'payreqst.*', // Select all columns from payreqst
                'debit.prdesc as debit_prdesc', // Select debit account details
                'credit.prdesc as credit_prdesc' // Select credit account details
            )
            ->get();
        $req_docs = Payreqstd::where('requisition_no', $req_no)->get();
        $reqdoc = Reqdocs::all();
        $today = Carbon::today()->format('Y-m-d');
        $office = Nlparams::where('prid', 'OFF')->get();
        $cbdeduct = Cbdeduct::where('doc_type', 'PAY')->get();

        $amount = 0;
        foreach ($req_docs as $req) {
            $amount = $amount + $req->entry_type_amount;
        }

        $dat = array(
            'main' => 'Front Office',
            'module' => 'Payments',
            'submodule' => 'Requisitions',
            'submod1' => formatRequisitionNo($req_no)
        );

        $cbtrans = Cbtrans::whereRaw("trim(source_code) = '".trim($req_details[0]->source_code)."' ")
        ->where('descr', trim($req_details[0]->entry_type_descr))->where('doc_type', 'PAY')->get()[0];

        $requisition = Payreqst::where('req_no', $req_no)->first();
        
        return view::make('gl.fo.requisition_details', compact('req_details', 'req_docs', 'reqdoc', 'today', 'amount', 
        'office', 'cbdeduct', 'cbtrans', 'requisition'))->with('dat', $dat);
    }

 
    public function reqDetails(Request $request)
    {
        
        $requisitions = removeRequsitionFormat($request->get('requisition_no'));

        $req_details = DB::table('payreqst')
            ->where('payreqst.req_no', $requisitions)
            ->leftjoin('nlparams as debit', function ($join) {
                $join->on('payreqst.debit_account', '=', 'debit.prsno')
                    ->where('debit.prid', 'GLH');
            })
            ->leftjoin('nlparams as credit', function ($join) {
                $join->on('payreqst.credit_account', '=', 'credit.prsno')
                    ->where('credit.prid', 'GLH');
            })
            ->select(
                'payreqst.*', // Select all columns from payreqst
                'debit.prdesc as debit_prdesc', // Select debit account details
                'credit.prdesc as credit_prdesc' // Select credit account details
            )
            ->get();
        $req_docs = Payreqstd::where('requisition_no', $requisitions)->get();
        $reqdoc = Reqdocs::all();
        $today = Carbon::today()->format('Y-m-d');
        $office = Nlparams::where('prid', 'OFF')->get();
        $cbdeduct = Cbdeduct::where('doc_type', 'PAY')->get();
        $bankbranch = Olbranch::all();


        $amount = 0;
        foreach ($req_docs as $req) {
            $amount = $amount + $req->entry_type_amount;
        }
        $dat = array(
            'main' => 'Front Office',
            'module' => 'Payments',
            'submodule' => 'Requisitions',
            'submod1' => formatRequisitionNo($requisitions)
        );

        $cbtrans = Cbtrans::whereRaw("trim(source_code) = '".trim($req_details[0]->source_code)."' ")
        ->where('descr', trim($req_details[0]->entry_type_descr))->where('doc_type', 'PAY')->get()[0];

        $requisition = Payreqst::where('req_no', $requisitions)->first();

        $branch = $req_details[0]->branch;
        $agent = $req_details[0]->agent;
        $agent_no = str_pad($req_details[0]->branch, 3,'0',STR_PAD_LEFT).str_pad($req_details[0]->agent, 5,'0',STR_PAD_LEFT);
        
       
       $claimant_code = $req_details[0]->claimant_code;

       if($claimant_code){

        $code = substr($claimant_code,0,1);
        $process = get_cabinet_entity($code);

       }

        // Payment Requisitions approvals
        $user_id = trim(Auth::user()->user_id);
        $approval_process = Aims_process::where('slug', 'payments-approval')->first();

        if ($approval_process) {
            $process_code = trim($approval_process->process_code);

            $approval_process = Aims_process::with(['process_dtl', 'approval_levels'])
                ->where('process_code', $process_code)
                ->first();

            if ($approval_process) {
                $filteredApprovalLevels = $approval_process->approval_levels->filter(function ($level) use ($requisition) {
                    return $requisition->local_amount >= $level->min_limit;
                });

                $approval_process->setRelation('approval_levels', $filteredApprovalLevels);
            }
        }


        // fetch approvals if any
        $approval_dtl = Approvals::with('approval_flow')
                    ->where('req_no',trim($requisition->req_no))
                    ->where('type', 'PYQAP')
                    ->orderBy('date_created','DESC')
                    ->first();
        $check_approval = Approvals::where('req_no',trim($requisition->req_no))
                        ->where('type', 'PYQAP')->orderBy('date_created','DESC')->first();

        if(isset($check_approval)){
            // $status = 'N';
            if($check_approval->status == 'A'){
                $status = 'A';
                
            }
            elseif($check_approval->status == 'R'){
                $status = 'R';
                $msg = 'Requisition Approval was rejected';
            }
            elseif($check_approval->status == 'P'){
                $status = 'P';
                $msg = 'Requisition has a Pending approval';
            }
        }

        $approval_status = $status ;
        $approval_msg = $msg;

        //!Reversal approval 
        $process_2 = Aims_process::where('slug',trim('claim-requisition'))->first();
        $process_code_2= trim($process_2->process_code);
    
        $process_2 = Aims_process::with(['process_dtl',
                'approval_levels'
            ])
            ->where('process_code',trim($process_code_2))
            ->first();

        
            
        // fetch approvals if any
        $approval_dtl_2 = Approvals::with('approval_flow')
                                    ->where('req_no',trim($requisition->req_no))
                                    ->where('type', 'PYQAP')
                                    ->orderBy('date_created','DESC')
                                    ->first();

        $check_approval_2 = Approvals::where('req_no',trim($requisition->req_no))
                                    ->where('type', 'PYQAP')
                                    ->orderBy('date_created','DESC')
                                    ->first();


        if(isset($check_approval_2)){
            // $status = 'N';
            if($check_approval_2->status == 'A'){
                $status_2 = 'A';
                
            }
            elseif($check_approval_2->status == 'R'){
                $status_2 = 'R';
                $msg_2 = 'Requisition Approval was rejected';
            }
            elseif($check_approval_2->status == 'P'){
                $status_2 = 'P';
                $msg_2 = 'Requisition has a Pending approval';
            }
        }
        $approval_status_2 = $status_2 ;
        $approval_msg_2 = $msg_2;
        
        return view::make('gl.fo.requisition_details', compact('req_details', 'req_docs','bankbranch', 'reqdoc', 'today', 'amount',
         'office', 'cbdeduct', 'cbtrans', 'requisition','agent_no','process','approval_process','approval_dtl','approval_status_2','approval_msg_2','approval_status'))->with('dat', $dat);
    }

    public function postedReqDetails(Request $request)
    {

        $requisitions = removeRequsitionFormat($request->get('requisition_no'));

        $req_details = Payreqst::where('req_no', $requisitions)->get();
        $req_docs = Payreqstd::where('requisition_no', $requisitions)->get();
        $reqdoc = Reqdocs::all();
        $today = Carbon::today()->format('Y-m-d');
        $office = Nlparams::where('prid', 'OFF')->get();
        $cbdeduct = Cbdeduct::where('doc_type', 'PAY')->get();

        $amount = 0;

        foreach ($req_docs as $req) {
            $amount = $amount + $req->entry_type_amount;
        }


        $dat = array(
            'main' => 'Front Office',
            'module' => 'Payments',
            'submod1' => formatRequisitionNo($requisitions)
        );
        return view::make('gl.fo.posted_req_details', compact('req_details', 'req_docs', 'reqdoc', 'today', 'amount', 'office', 'cbdeduct'))->with('dat', $dat);
    }

    public function getDebitAcc(Request $request)
    {
        $entry = $request->descr;
        return Cbtrans::where('doc_type', 'PAY')
            ->where('descr', $entry)->get()[0];
    }

    public function getRecDebitAcc(Request $request)
    {
        
        return Payreqst::whereRaw("trim(req_no)='" . $request->reqno . "'")->get()[0];

    }


    //ajax helper functions


    //get requisition details
    public function getRequisitionDetails(Request $request)
    {
        return Payreqst::whereRaw("trim(req_no)='" . $request->req_no . "'")->get(['entry_type_descr', 'approved_by', 'pay_method'])[0];
    }

    public function getEndorsements(Request $request)
    {
        $results = array();
        $source_code = $request->get('source_code');
        $term = $request->term;






        if ($source_code == 'U/W') {
            $endorsements = Acdet::where('doc_type', 'CRN')
                ->where('endt_renewal_no', 'LIKE', '%' . $term . '%')
                ->where('source', 'U/W')
                ->distinct()
                ->get();
        } else {

            $endorsements = Acdet::where('doc_type', 'CRN')
                ->where('endt_renewal_no', 'LIKE', '%' . $term . '%')
                ->where('source', 'CRD')
                ->distinct()
                ->get();
        }

        foreach ($endorsements as $endorsement) {

            $results[] = [
                'value' => $endorsement->endt_renewal_no . ' ' . $endorsement->unallocated,
                'text' => $endorsement->endt_renewal_no
            ];
        }

        return Response::Json($results);
        //  return $endorsements;
    }
    public function getREQEndorsements(Request $request)
    {
        $results = array();
        $source_code = $request->get('source_code');
        $term = removePolicyOrClaimFormat($request->term);


        $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];


        if ($source_code == 'U/W') {
            $endorsements = Acdet::where('doc_type', 'CRN')
                ->where('endt_renewal_no', 'LIKE', '%' . $term . '%')
                ->where('source', 'U/W')
                ->where('unallocated', '<', 0)
                ->distinct(['endt_renewal_no'])
                ->get(['endt_renewal_no']);
        } else {


            $endorsements = DB::table($gb . '.acdet')->select(['acdet.endt_renewal_no', 'acdet.source', 'acdet.doc_type'])
                ->join($gb . '.debitmast', function ($join) {
                    $join->on('acdet.endt_renewal_no', '=', 'debitmast.endt_renewal_no');
                })

                ->where('acdet.endt_renewal_no', 'like', '%' . $term . '%')
                ->where('acdet.source', 'CRD')
                ->where('acdet.doc_type', 'CRN')
                ->where('acdet.unallocated', '<', 0)
                ->distinct()
                ->get();
        }





        // $endorsements = Acdet::where('doc_type','CRN')
        //                              ->where('endt_renewal_no','LIKE','%'.$term.'%')
        //                             ->where('source',$source_code)
        //                             ->distinct(['endt_renewal_no'])
        //                             ->get(['endt_renewal_no']);

        foreach ($endorsements as $endorsement) {

            $results[] = [
                'value' => $endorsement->endt_renewal_no,
                'text' => $endorsement->endt_renewal_no
            ];
        }

        return Response::Json($results);
    }

    public function getnewEntry(Request $request)
    {
        $entry = $request->get('entry');
        $cbtrans_details = Cbtrans::where('doc_type', 'PAY')
            ->where('descr', $entry)->get();
        echo $cbtrans_details;
    }

    public function getEndorsement(Request $request)
    {

        $endt = $request->get('endt');
        $endorsement_details = Dcontrol::where('endt_renewal_no', $endt)->get();
        echo $endorsement_details;
    }

    public function getCreditnote(Request $request)
    {
        $endt = $request->get('endt');
        $source = $request->get('source');

        if ($source == 'U/W') {
            $acdet = Acdet::where('endt_renewal_no', $endt)
                ->where('source', 'U/W')
                ->where('unallocated', '<', 0)
                ->where('doc_type', 'CRN')
                ->get();
        } else {

            // $acdet = Acdet::where('endt_renewal_no', $endt)
            //     ->where('source', 'CRD')
            //     ->where('unallocated', '<', 0)
            //     ->where('doc_type', 'CRN')->get();
            $acdet = DB::select("select * from acdet where SUBSTR(reference,0,6)  not in
            (select LPAD(credit_note_no, 6, '0') AS WHOLENAME from payreqst where claim_no = '$endt' and payreqst.entry_type_descr = 'FAC' 
            and (cancelled = 'N' or cancelled is null ) and ( voucher_raised = 'N' or voucher_raised is null)  ) 
            and endt_renewal_no = '$endt' and acdet.source = 'CRD' and acdet.unallocated < 0 ");
        }

        return $acdet;
    }

    public function getCrnDetails(Request $request)
    {
        $source = $request->get('source');
        $reference = $request->get('reference');
        if ($source == 'FAC') {
            $source = 'CRD';
        }
        $credit_note = Acdet::where('source', $source)
            ->where('reference', $reference)
            ->get()[0];
        return $credit_note;
    }
    public function fetch_agent(Request $request)
    {
        $branch = (int) $request->branch;
        $agent = (int) $request->agent;
        return Crmast::where('branch', $branch)
            ->where('agent', $agent)->get()[0];
    }

    public function getParticipant(Request $request)
    {

        $reference = $request->get('reference');
        //dd($reference);
        $participant = Acdet::where('source', 'CRD')
            ->where('reference', $reference)
            ->get();

        $participant = $participant[0];

        $agent_name = Crmast::where('branch', $participant->branch)
            ->where('agent', $participant->agent)
            ->get()[0];

        return $agent_name;

        //return ;
        //dd($participant);
    }

    public function getBranchagent(Request $request)
    {
        $branch = $request->get('branch');
        $agent = $request->get('agent');
        // $agent_details = Agmnf::where('branch', $branch)
        //     ->where('agent', $agent)->get();
        $intermediaryParams = new IntermediaryQueryParams([
            'agentNo' => $agent,
        ]);
        $agent_details  =IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->get();
        echo $agent_details;
    }

    public function getTreatyTypes(Request $request)
    {
        $year = $request->get('uw_year');
        $treaty_types = Treatysetup::where('uw_year', $year)->get();
        return $treaty_types;
    }

    public function getTreatyPart(Request $request)
    {
        $treaty_code = trim($request->get('treaty_code'));
        $year = $request->get('uw_year');

        $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];

        $reinsurer_details = DB::table('' . $gb . '.crmast')
            ->join('' . $gb . '.treatypart', function ($query) use ($treaty_code, $year) {
                $query->on('crmast.branch', '=', 'treatypart.branch')
                    ->on('crmast.agent', '=', 'treatypart.agent')
                    ->where([
                        ['treatypart.treaty_code', '=', $treaty_code],
                        ['treatypart.uw_year', '=', $year]
                    ]);
            })
            ->get();

        return $reinsurer_details;
    }

    public function getParticipantDetails(Request $request)
    {
        $account = $request->get('branch_agent');

        $branch = substr($account, 0, 3);
        $agent = substr($account, 3, 8);

        $agent_name = Crmast::where('branch', $branch)
            ->where('agent', $agent)
            ->get()[0];

        return $agent_name;
    }

    public function getCommbatch(Request $request)
    {
        $comm_batch = Commbtch::where('req', null)
            ->where('approve', 'Y')->get();
        echo $comm_batch;
    }

    public function getSubsidiary(Request $request)
    {
        $sub_code = $request->get('sub_code');
        $offcd = $request->get('offcd');
        $subsidiary = Nlslmst::where('prid', 'SLT')
            ->where('sltype', $sub_code)
            ->where('offcd', $offcd)->get();
        echo $subsidiary;
    }

    public function getPayeebank(Request $request)
    {
        $code = $request->get('bank');
        $bank = STR_PAD($code, 3, ' ', STR_PAD_RIGHT);

        $bank_branch = Olbranch::where('bank_code', $bank)->get();

        echo $bank_branch;
    }

    public function getCurrency(Request $request)
    {
        $currency = $request->get('currency_code');
        $currency_details = Currency::where('currency_code', $currency)->get();

        echo $currency_details;
    }

    public function getCurrate(Request $request)
    {
        $currency = $request->get('currency_code');
        $today = Carbon::today();
        $currency_rate = Currate::where('currency_code', $currency)
            ->where('rate_date', $today)->get();

        echo $currency_rate;
    }

    public function postCurrencyrate(Request $request)
    {
        $currency = $request->get('currency_code');
        $rate = $request->get('new_rate');
        $rate_date = carbon::today();

        $create_currrate = Currate::create([
            'currency_code' => $currency,
            'rate_date' => $rate_date,
            'currency_rate' => $rate,
        ]);

        echo $create_currrate;
    }

    public function getAcdetdetails(Request $request)
    {
        $reference = $request->get('reference');
        $acdet_details = Acdet::where('reference', $reference)->get();
        echo $acdet_details;
    }

    public function getBatchdetails(Request $request)
    {
        $gbatch = $request->get('gbatch');
        $batch_details = Commbtch::where('gbatch', $gbatch)->get();
        echo $batch_details;
    }

    public function getAgentdetails(Request $request)
    {
        $branch = $request->get('branch');
        $agent = $request->get('agent');
        // $agmnf_details = Agmnf::where('branch', $branch)
        //     ->where('agent', $agent)->get();
        $intermediaryParams = new IntermediaryQueryParams([
            'agentNo' => $agent,
        ]);
        $agent_details  =IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->get();
        echo $agmnf_details;
    }

    public function getNlslmstdetails(Request $request)
    {
        $offcd = $request->get('offcd');
        $sltype = $request->get('sltype');
        $slhead = $request->get('slhead');

        $nlslmst_details = Nlslmst::where('prid', 'SLT')
            ->where('offcd', $offcd)
            ->where('sltype', $sltype)
            ->where('slhead', $slhead)->get();

        echo $nlslmst_details;
    }

    public function getMaindeduction(Request $request)
    {
        $code = $request->get('code');
        $main_deduction = Cbdeduct::where('code', $code)->get();
        echo $main_deduction;
    }

    public function getOtherdeductions(Request $request)
    {
        $code = $request->get('code');
        $other_deductions = Cbdeduct::where('code', $code)->get();
        echo $other_deductions;
    }

    public function postReqDeduction(Request $request){
        // dd($request);
        $req_no = removeRequsitionFormat($request->add_deduct_req_no);
        $payreqst = Payreqst::where('req_no', $req_no)->first();
    
        try{
            for($i = 1; $i < 11; $i++){
                $code = 'deduction_code_'.$i;
                $ded_amount = 'deduction_amount_'.$i;
                // dd($code, $ded_amount);
                $cbdeduct = Cbdeduct::where('code', $request->deduct_code)->first();
                // dd($request->deduct_code, $request->deduct_amount);
                if(empty($payreqst->$code)){
                    $amount = str_replace(",", "", $request->deduct_amount);
                    $payreqst->$code = $request->deduct_code;
                    $payreqst->$ded_amount = $amount;
                    
                    if($cbdeduct->add_deduct = 'D'){
                        $payreqst->amount = $payreqst->amount - $amount;
                        $payreqst->nett_amount = $payreqst->nett_amount - $amount;
                        $payreqst->local_amount = $payreqst->local_amount - ($amount*$payreqst->currency_rate);
                        $payreqst->local_nett_amount = $payreqst->local_nett_amount - ($amount*$payreqst->currency_rate);
                    }
                    else{
                        $payreqst->amount = $payreqst->amount + $amount;
                        $payreqst->nett_amount = $payreqst->nett_amount + $amount;
                        $payreqst->local_amount = $payreqst->local_amount + ($amount*$payreqst->currency_rate);
                        $payreqst->local_nett_amount = $payreqst->local_nett_amount + ($amount*$payreqst->currency_rate);
                    }
                    break;
                }
            }
           
            $payreq_upd = $payreqst->save();
        
            Session::Flash("success", "Addition/Deduction added successfully");
        }
        catch(\Throwable $e){
            // dd($e);
            Session::Flash("error", "Failed to add");
        }   
    }

    //requisition documents functions
    public function getReqdocuments(Request $request)
    {
        $req_docs = Reqdocs::all();
        echo $req_docs;
    }

    public function getOtherreqdocuments(Request $request)
    {
        $req_docs = Reqdocs::all();
        echo $req_docs;
    }

    public function getSubdetails(Request $request)
    {
        $offcd = $request->get('offcd');
        $sltype = $request->get('sltype');
        $slhead = $request->get('slhead');

        $subledger_details = Nlslmst::where('prid', 'SLT')
            ->where('sltype', $sltype)
            ->where('offcd', $offcd)
            ->where('slhead', $slhead)->get();
        echo $subledger_details;
    }

    public function getSubdocuments(Request $request)
    {
        $offcd = $request->get('offcd');
        $sltype = $request->get('sltype');
        $slhead = $request->get('slhead');

        $sub_docs = Nlsldtl::where('prid', 'SLT')
            ->where('sltype', $sltype)
            ->where('offcd', $offcd)
            ->where('slhead', $slhead)->get();
        echo $sub_docs;
    }

    public function getOthersubdocuments(Request $request)
    {
        $offcd = $request->get('offcd');
        $sltype = $request->get('sltype');
        $slhead = $request->get('slhead');

        $osub_docs = Nlsldtl::where('prid', 'SLT')
            ->where('sltype', $sltype)
            ->where('offcd', $offcd)
            ->where('slhead', $slhead)->get();
        echo $osub_docs;
    }

    public function getMaindocument(Request $request)
    {
        $offcd = $request->get('offcd');
        $sltype = $request->get('sltype');
        $slhead = $request->get('slhead');
        $doc_type = $request->get('doc_type');

        $main_doc = Nlsldtl::where('prid', 'SLT')
            ->where('sltype', $sltype)
            ->where('offcd', $offcd)
            ->where('slhead', $slhead)
            ->where('doc_type', $doc_type)->get();
        echo $main_doc;
    }

    public function getOtherdocument(Request $request)
    {
        $offcd = $request->get('offcd');
        $sltype = $request->get('sltype');
        $slhead = $request->get('slhead');
        $doc_type = $request->get('doc_type');

        $other_doc = Nlsldtl::where('prid', 'SLT')
            ->where('sltype', $sltype)
            ->where('offcd', $offcd)
            ->where('slhead', $slhead)
            ->where('doc_type', $doc_type)->get();
        echo $other_doc;
    }

    //post requisition documents
    public function storeReqdocs(Request $request)
    {
        // dd($request->all());
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try {
           
            $user_id = Aimsuser::where('user_id', trim($request->escalate_doc))->first()->user_id;
            
            $req_no = trim($request->req_no_doc);
            $offcd = trim($request->doc_offcd);
            $currency_rate = $request->req_rate;
            $currency_code = trim($request->req_curr_code);
            $entry_type_code = trim($request->req_doc_type);
            $entry_type_reference = trim($request->doc_ref);
            $getpayreqst = Payreqst::where('req_no', $req_no)->first();

            //dd($getpayreqst, $user_id, $req_no, $entry_type_code, $offcd, $entry_type_reference,  $currency_rate,  $currency_code);

            if ($user_id) {
                // $this->escalate($user_id, $req_no, $request->endt_renewal_no);
            }

            //$requisition_no = $request->requisition_number;
            $offcd = $request->doc_offcd;
            $currency_rate = $request->req_rate;
            $currency_code = $request->req_curr_code;

            $ref_amount = str_replace(',', '', $request -> ref_amount);
            $doc_amount = str_replace(',', '', $request -> doc_amount);
            $gross_amount = str_replace(',', '', $request -> ref_amount);

            $item_no =  Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ") -> count() + 1;

            $check_exist_doc = Laclmreq::where('source','REQ')
            ->whereRaw("trim(req_no) = '" . $req_no . "' ")
            ->count();
            

            $count = $check_exist_doc +1;

            $x = Payreqstd::create([
                'offcd' => $offcd,
                'requisition_no' => $req_no,
                'entry_type_code' => $entry_type_code,
                'entry_type_reference' => $entry_type_reference,
                'entry_type_date' => $request->ref_date,
                'entry_type_amount' => $doc_amount,
                'item_no' => $item_no,
                'ref_item_no' => $request->item_no,
                'ref_period_year' => $request->period_year,
                'ref_period_month' => $request->period_month,
                'ref_amount' => $ref_amount,
                'currency_code' => $currency_code,
                'currency_rate' => $currency_rate,
                'local_entry_type_amount' => $doc_amount * $currency_rate,
                'local_ref_amount' => $ref_amount * $currency_rate,
                'vat_no' => $request->vat_no,
                'pin_no' => $request->pin_no,
                'date_received' => $request->date_received,
                'debit_account' => $request->debit_account,
                'credit_account' => $request->credit_account,
                // 'entry_type_nett' => $gross,
                // 'local_entry_type_nett' => $gross * $currency_rate,
                'embedded_img_no'=>$count,
                'total_taxes'=>0,
                'local_total_taxes'=>0,
                'dv_number'=> $dv_no ,
                'taxable'=>$request->taxable,
                'claimant_code'=>$getpayreqst->claimant_code
            ]);

            $src_code = trim($getpayreqst -> source_code);

            $claimant_code = trim($getpayreqst -> claimant_code);

            if($src_code == 'CLM')
            {
                $clmcorr = Clmcorr::where('code', $getpayreqst -> payee_type) -> first();
                if($clmcorr -> insured == 'Y')
                {
                    $check = Client::where('client_number', $getpayreqst -> client_number) -> first();
                    $tax_grp = $check -> tax_group;
                }
                    
                else
                {
                    $check = Clparam::where('claimant_code', $claimant_code) -> first();
                    $tax_grp = $check -> tax_group;
                }
            }

            if($src_code == 'R/I' || $src_code == 'FAC')
            {
                $tax_grp = $getpayreqst->tax_group;
            }

            if ($request->taxable == 'Y') 
            {
                if($tax_grp != null)
                {
                    $tax_types = Gltaxgroupdt::where('group_code', $tax_grp) -> get();

                    foreach ($tax_types as $type) 
                    {
                        $tax_type = $type -> tax_type;

                        $tax_code = $type -> tax_code;

                        $gltax= Gltaxes::where('tax_type', $tax_type) -> where('tax_code', $tax_code)-> first();

                        $rate = $gltax -> tax_rate;

                        /**Check if tax is applied on nett amnt or on another tax */
                        $basis = Gltaxtypes::where('tax_type', $tax_type) -> first();

                        if($basis -> basis == 'N')
                        {
                            $check_tx = Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                                        -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                                        -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                                        -> where('tax_code', $tax_code)
                                        -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                                        -> first();

                            if($check_tx)
                            {
                                
                            }

                            else
                            {
                                $taxable_amount = $gross_amount;
                                $add_deduct = $basis -> add_deduct;
                                if($add_deduct == 'A')
                                {
                                    $tax_amount = ($taxable_amount * $rate)/100;
                                }
            
                                if($add_deduct == 'D')
                                {
                                    $tax_amount = (($taxable_amount * $rate)/100) * (-1);
                                }

                                /**payreqtaxes */
                                $tax  = new Payreqtaxes;
                                $tax -> requisition_no = $req_no;
                                $tax -> entry_type_code = $entry_type_code;
                                $tax -> item_no = $item_no;
                                $tax -> tax_code = $tax_code;
                                $tax -> taxable_amount = $taxable_amount;
                                $tax -> tax_amount = $tax_amount;
                                $tax -> tax_rate = $rate ;
                                $tax -> created_by = trim(auth() -> id());
                                $tax -> created_date = Carbon::now();
                                $tax -> add_deduct = $add_deduct;
                                $tax -> entry_type_reference = $entry_type_reference;
                                $tax -> tax_type = $gltax->tax_type;
                                $tax -> save();
                            }
                        }

                        elseif ($basis -> basis == 'T') 
                        {
                            /**Check tax to apply to */
                            $get_tax_code = $basis -> tax_code;
                            
                            $check_tax = Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                                        -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                                        -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                                        -> where('tax_code', $get_tax_code)
                                        -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                                        -> first();
                                        
                            if($check_tax)
                            {
                                $taxable_amount = abs($check_tax -> tax_amount);
                                $add_deduct = $basis -> add_deduct;
                                if($add_deduct == 'A')
                                {
                                    $tax_amount = ($taxable_amount * $rate)/100;
                                }
            
                                if($add_deduct == 'D')
                                {
                                    $tax_amount = (($taxable_amount * $rate)/100) * (-1);
                                }

                                $tax  = new Payreqtaxes;
                                $tax -> requisition_no = $req_no;
                                $tax -> entry_type_code = $entry_type_code;
                                $tax -> item_no = $item_no;
                                $tax -> tax_code = $tax_code;
                                $tax -> taxable_amount = $taxable_amount;
                                $tax -> tax_amount = $tax_amount;
                                $tax -> tax_rate = $rate ;
                                $tax -> created_by = trim(auth() -> id());
                                $tax -> created_date = Carbon::now();
                                $tax -> add_deduct = $add_deduct;
                                $tax -> entry_type_reference = $entry_type_reference;
                                $tax -> tax_type = $check_tax->tax_type;
                                $tax -> save();
                            }

                            else
                            {
                                $taxable_amount = $gross_amount;
                                
                                $add_deduct = $basis -> add_deduct;
                                if($add_deduct == 'A')
                                {
                                    $tax_amount = ($taxable_amount * $rate)/100;
                                }
            
                                if($add_deduct == 'D')
                                {
                                    $tax_amount = (($taxable_amount * $rate)/100) * (-1);
                                }

                                /**payreqtaxes */
                                $tx = new Payreqtaxes;
                                $tx -> requisition_no = $req_no;
                                $tx -> entry_type_code = $entry_type_code;
                                $tx -> item_no = $item_no;
                                $tx -> tax_code = $get_tax_code;
                                $tx -> taxable_amount = $taxable_amount;
                                $tx -> tax_amount = $tax_amount;
                                $tx -> tax_rate = $rate ;
                                $tx -> created_by = trim(auth() -> id());
                                $tx -> created_date = Carbon::now();
                                $tx -> add_deduct = $add_deduct;
                                $tx -> entry_type_reference = $entry_type_reference;
                                $tax -> tax_type = $gltax->tax_type;
                                $tx -> save();

                                if($tx)
                                {
                                    $taxable_amount = abs($tx -> taxable_amount);
                                    $add_deduct = $basis -> add_deduct;
                                    if($add_deduct == 'A')
                                    {
                                        $tax_amount = ($taxable_amount * $rate)/100;
                                    }
                
                                    if($add_deduct == 'D')
                                    {
                                        $tax_amount = (($taxable_amount * $rate)/100) * (-1);
                                    }

                                    $new_tax = new Payreqtaxes;
                                    $new_tax -> requisition_no = $req_no;
                                    $new_tax -> entry_type_code = $entry_type_code;
                                    $new_tax -> item_no = $item_no;
                                    $new_tax -> tax_code = $tax_code;
                                    $new_tax -> taxable_amount = $taxable_amount;
                                    $new_tax -> tax_amount = $tax_amount;
                                    $new_tax -> tax_rate = $rate ;
                                    $new_tax -> created_by = trim(auth() -> id());
                                    $new_tax -> created_date = Carbon::now();
                                    $new_tax -> add_deduct = $add_deduct;
                                    $new_tax -> entry_type_reference = $entry_type_reference;
                                    $tax -> tax_type = $tx->tax_type;
                                    $new_tax -> save();
                                }
                            }
                            
                        }
                    }
                }
            }

            $doc_taxes = Payreqtaxes::where('requisition_no', $req_no) -> where('item_no', $item_no)
                    -> where('entry_type_reference', $entry_type_reference) 
                    -> where('entry_type_code',  $entry_type_code) -> sum('tax_amount');

            $local_doc_taxes =  $doc_taxes * $currency_rate;
                    
            $doc_nett = $ref_amount +  $doc_taxes;
        
            $doc_local_nett = $doc_nett * $currency_rate;

            /**update doc */
            $update_doc =  Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                            -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                            -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                            -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                            -> update([
                                'entry_type_nett' => $doc_nett,
                                'taxable'=>$request->taxable,
                                'local_entry_type_nett' => $doc_local_nett,
                                'total_taxes' => $doc_taxes,
                                'local_total_taxes' => $local_doc_taxes,
                            ]);

            if(!empty($request->document_path[0]) && $src_code == 'CLM')
            {

                $getpayreqst = Payreqst::where('req_no', $req_no)->first();
                $code = substr($getpayreqst->claimant_code,0,1);

                // $service_providers = Clmcorr::where('claimant','<>','Y')->where('insured','<>','Y')->get(['code']);
                $process = get_cabinet_entity($code);

                if($process == 'client'){

                    $entity_id = $getpayreqst->client_number;

                    $client = Client::where('client_number',$entity_id)->first();
                    $entity_name = $client->name;

                    $context_id = $getpayreqst->req_no;

                    $document = $request->document_path[0];
                    $dept = $getpayreqst->source_code;
                    $reqdoctype = Reqdocs::where('code',$entry_type_code)->first();
                    $description = $reqdoctype->description;

                    $this->upload_reqdoc_to_edms($entity_id,$entity_name,$context_id,$document,$dept,$description);

                }


                $document = $request->document_path[0];
                $encrypt_name = md5_file($document->getRealPath());
                $document_extention = $document->guessExtension();
                $document_name = substr($encrypt_name . $req_no . '.' . $document_extention, 0, 10);
                $store_document = $document->move('edms/', $document_name);
                $received = 'Y';
                $received_by = Auth::user()->user_name;
                $date_received = Carbon::now();

                $insert_lacremreq = new Laclmreq;
                $insert_lacremreq->claim_no = $request->claim_number;
                $insert_lacremreq->class =  $getpayreqst->class ;
                $insert_lacremreq->source =  'REQ' ;
                $insert_lacremreq->ln_no = $count ;
                $insert_lacremreq->policy_no =  $getpayreqst->policy_no;
                $insert_lacremreq->req_no =  $request->req_no_doc;
                $insert_lacremreq->clmreq_name = 'Requistion documents';
                $insert_lacremreq->received = $received;
                $insert_lacremreq->date_received = $date_received;
                $insert_lacremreq->document_path = 'edms/' . $document_name;
                $insert_lacremreq->received_by = $received_by;
                $insert_lacremreq->save();
                // dd($insert_lacremreq);
                $insert_documents = new Documents;
                $insert_documents->folder_id=$request->folder_id[$i];
                $insert_documents->index_name=$request->requisition_number;
                $insert_documents->name=$document_name;
                $insert_documents->extension=$document_extention;
                $insert_documents->created_at=$date_received;
                $insert_documents->updated_at=$date_received;
                $insert_documents->save();
            }
            else
            {
                $document_name = "";
                $document_extention = "";
                $received = 'N';
                $received_by = "";
                $date_received = "";
            }

            $total_nett = Payreqstd::where('requisition_no', $req_no) -> sum('entry_type_nett');

            $local_nett_amount = Payreqstd::where('requisition_no', $req_no) -> sum('local_entry_type_nett');

            $total_taxes = Payreqtaxes::where('requisition_no', $req_no) -> sum('tax_amount');

            $payreqst =  Payreqst::where('req_no', $req_no)->first();

            $payreqst_amnt = $payreqst -> amount;

            $allocated = Payreqstd::where('requisition_no', $req_no) -> sum('ref_amount');

            $local_allocated = $allocated * $currency_rate;

            $unallocated_total = $payreqst_amnt - $allocated;

            $local_unallocated_total = $unallocated_total * $currency_rate;
            // dd($total_nett, $local_nett_amount, $total_taxes, $payreqst, $payreqst_amnt, $allocated, $local_allocated, $local_unallocated_total);

            Payreqst::where('req_no', $req_no)->update([
                'user_str' => trim(Auth::user()->user_name),
                'total_taxes' => $total_taxes,
                'local_total_taxes' => $total_taxes * $currency_rate,
                'nett_amount' => $total_nett,
                'local_nett_amount' => $local_nett_amount,
                'escalated_to'=>$request->escalate_doc,
                'allocated_total' =>  $allocated,
                'local_allocated_total' => $local_allocated,
                'unallocated_total' => $unallocated_total,
                'local_unallocated_total'=>$local_unallocated_total,
            ]);

            $count = Escalate_pol::max('escalate_id');
            $next = (int)$count + 1;
            $name =  Auth::user()->user_name;
            $sender_id =  Auth::user()->user_id;
            $user_id = $request->escalate_doc;

            if ($user_id) {
                $recieverdet = Aimsuser::where('user_id', $user_id)->first();
            }

            $reciever = trim($recieverdet->user_name);
            $sent_id = trim($recieverdet->user_id);
            $emailaddr  = $recieverdet->email;
            $req_num = $req_no;

            if($getpayreqst->entry_type_descr == 'CLP')
            {
                $mess = "Dear $reciever , Kindly Approve the Requisition with Reference number '$req_num' and claim number '$getpayreqst->claim_no'.Thank You. ";

            }

            else
            {
                $mess = "Dear $reciever , Kindly Approve the Requisition with Reference number '$req_num'.Thank You. ";
            }


            $escalate = new Escalate_pol;
            $escalate->escalate_id = $next;
            $escalate->req_no = $req_num;
            $escalate->endorse_no  = $request->endt_ren_no;
            $escalate->sent_by = $name;
            $escalate->claim_no = $request->claim_no;
            $escalate->sent_to = $sent_id;
            $escalate->sent_by = $sender_id;
            $escalate->type = 'REQ';
            $escalate->description = 'REQUISITION APPROVAL';
            $escalate->user_name = $reciever;
            $escalate->created_at =  Carbon::now();
            $escalate->save();

            $category = 'REQUISITION APPROVAL';

            if(!empty($emailaddr)){
                $this->sendemails($category,$emailaddr,$mess,$name);
                
            }
            
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            
            Session::Flash('success', 'Documents for requisition ' . $request->doc_req_no . ' added successfully');
            
            return response()->json([
                'code' => 1,
                'message' => 'success',
                'req_no'=>$req_no,
                'item_no'=>$item_no,
                'offcd'=>$offcd,
                'entry_type_code'=>$entry_type_code,
                'entry_type_ref'=>$entry_type_reference,
                'taxable'=>$request->taxable,
            ]);
        } 
                
        catch (\Exception $e) { 

            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            
            $error_msg = json_encode($e->getMessage());
            $referrence = $request->doc_req_no;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);

            Session::Flash('error', 'Failed to save the Supporting Document');
          
        }
    }

    // Supporting document upload to edms
    public function upload_reqdoc_to_edms($entity_id,$entity_name,$context_id,$document,$dept,$description){
        do {
            $code = rand(100,999);
            $processcode = trim($request->process_code);
            $documentid = str_pad($request->documentcode, 3, "0", STR_PAD_LEFT);
            $docserial = str_pad($code,4,"0",STR_PAD_LEFT);
            $documentID = 'NDI'.$processcode.$documentid.$docserial;
         } while ( Edms_staging::where( 'document_id', $documentID )->exists() );

        //get the process code
         $processcode = 66;
         $document_code = 101;

        // get document file size
        $file_size = $document->getSize();

        // get document file type
         $file_type = $document->getClientMimeType();

         $type = pathinfo($document, PATHINFO_EXTENSION);
         $data = file_get_contents($document);
 
         // convert document to base64
         $img_base64 = base64_encode($data);
 
         $base64_string = $img_base64;
        

         $data = array([
            'document_id'=> trim($documentID),
            'document_code'=> $document_code,
            'entity_id'=> $entity_id,
            'context_id'=> $context_id,
            'dept'=> $dept,
            'process_code'=> $processcode,
            'document_description'=> trim($description),
            'entity_name' => $entity_name,
            'base64_string' => $base64_string,
            'file_type' => $file_type,
            'doc_uploaded'=>'Y',
            'integrated'=>'N',
            'file_size' => $file_size,
            'upload_timestamp'=> Carbon::now()->format('Y-m-d H:i:s'),
            'created_by'=> trim(Auth::user()->user_id),
        ]);

        // dd($data);
       
        DB::beginTransaction();

        try {

            Edms_staging::insert($data);

            

            DB::commit();
            
            $result = event(new DocReuploadToEdms(trim($documentID),trim($document_code)));
            

            $error = Edms_staging::where('context_id',$context_id)->first();
            
            if($error->error_comments != null){
                $error_message = $error -> error_comments;
                $error_text = substr($error_message, 5);
                Session::Flash('error', "Failed to upload to edms. Please try again later");


            }else{
                // update the integrated field in edms staging to 'Y' in case it has not been updated
                Edms_staging::where('document_id',trim($documentID))->update(['integrated'=>'Y']);
                Session::Flash('success', "Successfully saved the document for upload to Edms','process_redirect");

            }
            

        } catch (\Throwable $th) {

            DB::rollback();
            Session::Flash('error', "Failed to upload to edms. Please try again later");


        }  

    }

    public function sendemails($category,$emailaddr,$mess,$name){

            

        try {
            //code...	
            $sendemail = new Sendemail;
            $sendemail->category = $category;
            $sendemail->receiver = $emailaddr;
            $sendemail->message = $mess;
            $sendemail->creator = $name;
            $sendemail->status = 'SEND';
            $sendemail->createdate = Carbon::today(); 
            $sendemail->save();

       
        } catch (\Throwable $e) {

            $error_msg = json_encode($e->getMessage());
            $referrence = $category;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);

            session::flash("Failed to send Emails");

        }
      
    }

    public function escalateReq(Request $request)
    {


        try {
            $this->escalate($request->escalate, $request->req_no, $request->endt_renewal_no);
            $dcontrol = Dcontrol::where('endt_renewal_no', $request->endt_renewal_no)->update(['req_approved' => null]);
            Session::Flash('success', "Requisition escalated successfully");
        } catch (Exception $e) {
            Session::Flash('error', "Error escalating");
        }
    }
    public function escalate($user_id, $req_no, $endt_renewal_no)
    {

        $user = Aimsuser_web::where('user_id', $user_id)->get();
        $name =  Auth::user()->user_name;
        $sender_id =  Auth::user()->user_id;
        $count = Escalate_pol::where('sent_by', $sender_id)->max('escalate_id');
        $next = $count + 1;
        //$user_id = $request->escalate_doc;
        $recieverdet = Aimsuser_web::where('user_id', $user_id)->first();
        $reciever = trim($recieverdet->user_name);
        $sent_id = $user_id;
        $emailaddr  = $recieverdet->email;
        $req_num = $req_no;
        //$order_no = $request->order_no;


        $escalate = new Escalate_pol;
        $escalate->endorse_no = $endt_renewal_no;
        $escalate->escalate_id = $next;

        if ($req_num != null) {
            $escalate->req_no = $req_num;
            $escalate->type = 'REQ';
            $escalate->description = 'REQUISITION APPROVAL';
            $mess = "Dear $reciever , Kindly Approve the Requisition with Referrence number '$req_num'.Thank You. ";
            $category = 'REQUISITION APPROVAL';
        }
        $escalate->sent_by = $name;
        // $escalate->claim_no =$request->claim_no;
        $escalate->sent_to = $sent_id;
        $escalate->sent_by = $sender_id;

        $escalate->user_name = $reciever;
        $escalate->created_at =  Carbon::now();

        try {
            $escalate->save();
        } catch (Exception $e) {
        }
        $sendemail = new Sendemail;
        $sendemail->category = $category;
        $sendemail->receiver = $emailaddr;
        $sendemail->message = $mess;
        $sendemail->creator = $name;
        $sendemail->save();
    }
    public function cancelReqlMsg($user_id, $req_no, $endt_renewal_no)
    {


        $user = Aimsuser_web::where('user_id', $user_id)->get();

        $name =  Auth::user()->user_name;
        $sender_id =  Auth::user()->user_id;

        $count = Escalate_pol::where('sent_by', $sender_id)->max('escalate_id');
        $next = $count + 1;
        //$user_id = $request->escalate_doc;
        $recieverdet = Aimsuser_web::where('user_id', $user_id)->first();
        $reciever = trim($recieverdet->user_name);
        $sent_id = $user_id;
        $emailaddr  = $recieverdet->email;
        $req_num = $req_no;
        //$order_no = $request->order_no;


        $escalate = new Escalate_pol;
        $escalate->endorse_no = $endt_renewal_no;
        $escalate->escalate_id = $next;

        if ($req_num != null) {
            $escalate->req_no = $req_num;
            $escalate->type = 'REQ';
            $escalate->description = 'REQUISITION Cancelled';
            $mess = "Dear $reciever , The requisition with requisition number '$req_num'.Was Cancelled Thank You. ";
            $category = 'REQUISITION APPROVAL';
        }
        $escalate->sent_by = $name;
        // $escalate->claim_no =$request->claim_no;
        $escalate->sent_to = $sent_id;
        $escalate->sent_by = $sender_id;

        $escalate->user_name = $reciever;
        $escalate->created_at =  Carbon::now();

        try {
            $escalate->save();
        } catch (Exception $e) {
        }
        $sendemail = new Sendemail;
        $sendemail->category = $category;
        $sendemail->receiver = $emailaddr;
        $sendemail->message = $mess;
        $sendemail->creator = $name;
        $sendemail->save();
    }

    public function approveRequisition(Request $request)
    {
        //dd($request);
        DB::Transaction(function () use ($request) {
            $req_no = removeRequsitionFormat($request->get('req_no'));
            $today = Carbon::today();

            $user = trim(Auth::user()->user_name);

            // return redirect()->route('payment.post', ['request' => $request]);

            $payreqst = Payreqst::where('req_no',$req_no)
                                ->update([
                                    'approved_by'=>trim($user),
                                    'approved_date'=>$today
                                ]); 
            Session::Flash('success',"Requisition approved successfully");
        });
        return '';
    }

    public function authorizePayment(Request $request)
    {
        DB::Transaction(function () use ($request) {
            $user = Auth::user()->user_name;
            $today = Carbon::today();

            $req_no = removeRequsitionFormat($request->auth_req_no);

            $requisition = $payreqst = Payreqst::where('req_no', $req_no)->first();

            $user_id = Aimsuser_web::whereRaw("trim(user_name) = '" . trim($requisition->created_by) . "'")->first()->user_id;

            $this->uwreqapprovedMsg($user_id, null, $requisition->claim_no);

            $payreqst = Payreqst::where('req_no', $req_no)
                ->update([
                    'authorized' => trim($user),
                    'authorized_date' => $today,
                    'due_date' => $request->pay_date
                ]);

            Session::Flash('success', 'Requisition authorized successfully');
        });
    }

    public function postpayaccounts(Request $request)
    {
        
        if ($request->has('subaccount')) {
            $subaccount = $request->input('subaccount');
        } 
        
        else {
            $subaccount = '';

        }
       
        DB::Transaction(function () use ($request) {
            $user = Auth::user()->user_name;
            $today = Carbon::today();

            $req_no = removeRequsitionFormat($request->w_auth_req_no);

            $payreqst = Payreqst::where('req_no', $req_no)->first();

                $user_id = Aimsuser_web::whereRaw("trim(user_name) = '" . trim($payreqst->created_by) . "'")->first()->user_id;

                $this->uwreqapprovedMsg($user_id, null, $payreqst->claim_no);
    
                $payreqst = Payreqst::where('req_no', $req_no)
                    ->update([
                        'pay_method' => $request->pay_method,
                        'credit_account' => $request->cr_account,
                        'debit_account' => $request->dr_account,
                        'cheque_no' => $request->cheque_no,
                        'debit_subaccount' => $request->subaccount,
                        'authorized' => trim($user),
                        'authorized_date' => $today,
                        'due_date' => $request->w_pay_date,
                        'subledger' => $request->subaccount_flag
                    ]);

                    $pay_method = Olpaymethd::where('pay_method', $request->pay_method)->first();

                    $bank = Glbank::where('prsno', $request->cr_account)->first();

                                        
                    if($pay_method->cheque == "Y"){
                        
                        $cheque_issue = Chequemast::where('bank_acc_code', $bank->bank_acc_code)
                                                    ->where('cheque_no', $request->cheque_no)
                                                    ->update([
                                                        "cheque_issued" => 'Y',
                                                        "date_issued" => Carbon::now()
                                                    ]);

                    }else if($pay_method->eft == "Y"){

                        $cheque_issue = Eftmast::where('bank_acc_code', $bank->bank_acc_code)
                                                    ->where('eft_no', $request->cheque_no)
                                                    ->update([
                                                        "eft_issued" => 'Y',
                                                        "date_issued" => Carbon::now()
                                                    ]);

                    }else if($pay_method->rtgs == "Y"){

                        $cheque_issue = RTGSMast::where('bank_acc_code', $bank->bank_acc_code)
                                                    ->where('rtgs_no', $request->cheque_no)
                                                    ->update([
                                                        "rtgs_issued" => 'Y',
                                                        "date_issued" => Carbon::now()
                                                    ]);

                    }

            Session::Flash('success', 'Requisition accounts updated successfully');
        });
    }



    public function fetchTaxtypes(Request $request)
    {
        $tax_types = DB::table('gltaxtypes') -> get();
        return $tax_types;
    }

    public function getSelectTaxes(Request $request)
    {
        $select_tax_type = trim($request -> select_tax_type);

        $taxes = Gltaxes::where('tax_type', $select_tax_type) -> get();

        return $taxes;
    }

    public function  getDocsTaxRate(Request $request)
    {
        $select_tax_code = trim($request -> select_tax_code);
        $select_tax_type = trim($request -> select_tax_type);
        $req_no = trim($request->req_no);
        $entry_type_code = trim($request->entry_type_code);
        $item_no = trim($request -> item_no);
        $entry_type_reference = trim($request -> entry_type_reference);

        $tax = Gltaxes::where('tax_type', $select_tax_type) 
                    -> where('tax_code', $select_tax_code) -> first();

        $tax_rate = $tax -> tax_rate;

        $basis = Gltaxtypes::where('tax_type', $select_tax_type) -> first();
        
        if($basis -> basis == 'N')
        {
            $tax_basis = 'N';
        }

        elseif ($basis -> basis == 'T') 
        {
            $get_tax_code = $basis -> tax_code;
            $check_tax =  Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                        -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                        -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                        -> where('tax_code', $get_tax_code)
                        -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") -> first();
                        
            if($check_tax)
            {
                $taxable_amount = abs($check_tax -> tax_amount);
            }

            else
            {   
                $gltax = Gltaxes::where('tax_code', $get_tax_code) -> first();
            }
        }

        return ['tax_rate' => $tax_rate, 'gltax' => $gltax, 'tax_basis' => $tax_basis, 'taxable_amount' => $taxable_amount];
    }

    public function postReqdoctax(Request $request)
    {
        // dd($request -> all());
        $this -> validate($request, [
            'tax_code'=> 'required',
            'tax_type'=> 'required',
            'taxable_amount'=> 'required',
            'tax_rate'=> 'required',
        ]);

        $req_no = trim($request->requisition_no);
        $entry_type_code = trim($request->entry_type_code);
        $item_no = trim($request -> item_no);
        $tax_code = trim($request -> tax_code);
        $taxable_amount = str_replace(',', '', $request -> taxable_amount);
        $tax_rate = $request -> tax_rate;
        $entry_type_reference = trim($request -> entry_type_reference);
        $currency_rate = trim($request -> currency_rate);
        $offcd = trim($request -> offcd);
        $tax_code = trim($request -> tax_code);
        $tax_type = trim($request -> tax_type);

        $add_deduct_check = Gltaxtypes::where('tax_type', $tax_type) -> first();
        
        $add_deduct = $add_deduct_check -> add_deduct;

        if($add_deduct == 'A')
        {
            $tax_amount = ($taxable_amount * $tax_rate)/100;
        }

        if($add_deduct == 'D')
        {
            $tax_amount = (($taxable_amount * $tax_rate)/100) * (-1);
        }

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try {

            $tax  = new Payreqtaxes;
            $tax -> requisition_no = $req_no;
            $tax -> entry_type_code = $entry_type_code;
            $tax -> item_no = $item_no;
            $tax -> tax_code = $tax_code;
            $tax -> taxable_amount = $taxable_amount;
            $tax -> tax_amount = $tax_amount;
            $tax -> tax_rate = $tax_rate;
            $tax -> created_by = trim(auth() -> id());
            $tax -> created_date = Carbon::now();
            $tax -> add_deduct = $add_deduct;
            $tax -> entry_type_reference = $entry_type_reference;
            $tax -> tax_type = $tax_type;
            $tax -> save();

            if($tax)
            {
                $get_doc = Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ") -> whereRaw("trim(offcd) = '" . $offcd . "' ") 
                            -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                            -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                            -> whereRaw("trim(item_no) = '" . $item_no . "' ") -> first();

                $doc_gross = $get_doc ->  entry_type_amount;

                $doc_taxes = Payreqtaxes::where('requisition_no', $req_no) -> where('item_no', $item_no)
                                    -> where('entry_type_reference', $entry_type_reference) 
                                    -> where('entry_type_code',  $entry_type_code) -> sum('tax_amount');

                $local_doc_taxes =  $doc_taxes * $currency_rate; 
                            
                $doc_nett = $doc_gross +  $doc_taxes;

                $doc_local_nett = $doc_nett * $currency_rate;

                /**update doc */
                $update_doc =  Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                                -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                                -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                                -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                                -> update([
                                    'entry_type_nett' => $doc_nett,
                                    'local_entry_type_nett' => $doc_local_nett,
                                    'total_taxes' => $doc_taxes,
                                    'local_total_taxes' => $local_doc_taxes,
                                    'taxable' => 'Y'
                                ]);
                if($update_doc)
                {
                    $total_nett = Payreqstd::where('requisition_no', $req_no) -> sum('entry_type_nett');

                    $local_nett_amount = Payreqstd::where('requisition_no', $req_no) -> sum('local_entry_type_nett');

                    $total_taxes = Payreqtaxes::where('requisition_no', $req_no) -> sum('tax_amount');

                    Payreqst::where('req_no', $req_no)->update([
                        'user_str' => Auth::user()->user_name,
                        'total_taxes' => $total_taxes,
                        'local_total_taxes' => $total_taxes * $currency_rate,
                        'nett_amount' => $total_nett,
                        'local_nett_amount' =>  $local_nett_amount,
                        'escalated_to'=>$request->escalate_doc,
                    ]);
                    Session::Flash('success', 'Tax successfully added');
                }
            }

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
        }
        catch (\Exception $e) { 
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
        }
    }

    public function updateReqdoc(Request $request)
    {
        // dd($request -> all());
        $ref_amount = str_replace(',', '', $request -> ref_amount);
        $doc_amount = str_replace(',', '', $request -> doc_amount);

        $offcd = $request->doc_offcd;
        $currency_rate = $request->req_rate;
        $currency_code = $request->req_curr_code;
        $req_no = trim($request->req_no_doc);
        $entry_type_code = trim($request->req_doc_type);
        $item_no = trim($request->item_no);
        $entry_type_reference = trim($request->doc_ref);

        $getpayreqst = Payreqst::where('req_no', $req_no) -> first();

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try {
            $doc = Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ") -> whereRaw("trim(offcd) = '" . $offcd . "' ") 
                                -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                                ->whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                                ->whereRaw("trim(item_no) = '" . $item_no . "' ") -> first();
            $old_taxable = $doc -> taxable;

            $update_doc = Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ") -> whereRaw("trim(offcd) = '" . $offcd . "' ") 
                            -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                            ->whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                            ->whereRaw("trim(item_no) = '" . $item_no . "' ") 
                            -> update([
                                'entry_type_amount' => $doc_amount,
                                'ref_amount' => $ref_amount,
                                'local_entry_type_amount' => $doc_amount * $currency_rate,
                                'local_ref_amount' => $ref_amount * $currency_rate,
                                'date_received' => $request->date_received,
                                'debit_account' => $request->debit_account,
                                'credit_account' => $request->credit_account,
                                // 'taxable'=>$request->taxable,
                                'offcd' => $offcd,
                                'entry_type_date' => $request->ref_date,
                                'ref_period_year' => $request->period_year,
                                'ref_period_month' => $request->period_month,
                                'vat_no' => $request->vat_no,
                                'pin_no' => $request->pin_no,
                                'debit_account' => $request->debit_account,
                                'credit_account' => $request->credit_account,
                            ]);
                            
            if($update_doc)
            {
                $src_code = trim($getpayreqst -> source_code);
                $claimant_code = trim($getpayreqst -> claimant_code);
                if($src_code == 'CLM')
                {
                    $check = Clparam::where('claimant_code', $claimant_code) -> first();
                    $tax_grp = $check -> tax_group;
                }

                else{
                    $tax_grp = $getpayreqst->tax_group;
                }
                
                if ($request->taxable == 'Y') 
                {
                    if($tax_grp != null)
                    {
                        $tax_types = Gltaxgroupdt::where('group_code', $tax_grp) -> get();
                      
                        foreach ($tax_types as $type) 
                        {
                            $tax_type = $type -> tax_type;

                            $tax_code = $type -> tax_code;

                            $gltax = Gltaxes::where('tax_type', $tax_type) -> where('tax_code', $tax_code)-> first();

                            $tax_rate = $gltax -> tax_rate;

                            /**Check if tax is applied on nett amnt or on another tax */
                            $basis = Gltaxtypes::where('tax_type', $tax_type) -> first();
                            
                            if($basis -> basis == 'N')
                            {
                                $taxable_amount = $ref_amount;
                                
                                $add_deduct = $basis -> add_deduct;
                                if($add_deduct == 'A')
                                {
                                    $tax_amount = ($taxable_amount * $tax_rate)/100;
                                }
            
                                if($add_deduct == 'D')
                                {
                                    $tax_amount = (($taxable_amount * $tax_rate)/100) * (-1);
                                }
                               
                                if($old_taxable == 'N')
                                {
                                    $tax  = new Payreqtaxes;
                                    $tax -> requisition_no = $req_no;
                                    $tax -> entry_type_code = $entry_type_code;
                                    $tax -> item_no = $item_no;
                                    $tax -> tax_code = $tax_code;
                                    $tax -> taxable_amount = $taxable_amount;
                                    $tax -> tax_amount = $tax_amount;
                                    $tax -> tax_rate = $tax_rate;
                                    $tax -> created_by = trim(auth() -> id());
                                    $tax -> created_date = Carbon::now();
                                    $tax -> add_deduct = $add_deduct;
                                    $tax -> entry_type_reference = $entry_type_reference;
                                    $tax -> save();
                                }

                                elseif ($old_taxable == 'Y') 
                                {
                                    $update_tax =  Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                                                    -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                                                    -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                                                    -> where('tax_code', $tax_code)
                                                    -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                                                    -> update([
                                                        'tax_amount' => $tax_amount,
                                                        'taxable_amount' => $taxable_amount,
                                                        'dola' => Carbon::now(),
                                                        'changed_by' => auth() -> id()
                                                    ]);
                                }
                            }

                            elseif ($basis -> basis == 'T') 
                            {
                                /**Check tax to apply to */
                                $get_tax_code = $basis -> tax_code;
                                $check_tax = Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                                                -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                                                -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                                                -> where('tax_code', $get_tax_code)
                                                -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                                                -> first();
                                if($check_tax)
                                {
                                    $taxable_amount = abs($check_tax -> tax_amount);
                                    $add_deduct = $basis -> add_deduct;
                                    if($add_deduct == 'A')
                                    {
                                        $tax_amount = ($taxable_amount * $tax_rate)/100;
                                    }
                
                                    if($add_deduct == 'D')
                                    {
                                        $tax_amount = (($taxable_amount * $tax_rate)/100) * (-1);
                                    }

                                    if($old_taxable = '' || $old_taxable = 'N')
                                    {
                                        $tax  = new Payreqtaxes;
                                        $tax -> requisition_no = $req_no;
                                        $tax -> entry_type_code = $entry_type_code;
                                        $tax -> item_no = $item_no;
                                        $tax -> tax_code = $tax_code;
                                        $tax -> taxable_amount = $taxable_amount;
                                        $tax -> tax_amount = $tax_amount;
                                        $tax -> tax_rate = $tax_rate;
                                        $tax -> created_by = trim(auth() -> id());
                                        $tax -> created_date = Carbon::now();
                                        $tax -> add_deduct = $add_deduct;
                                        $tax -> entry_type_reference = $entry_type_reference;
                                        $tax -> save();
                                    }

                                    elseif ($old_taxable = 'Y') 
                                    {
                                        $update_tax =  Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                                                        ->whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                                                        ->whereRaw("trim(item_no) = '" . $item_no . "' ")
                                                        -> where('tax_code', $tax_code)
                                                        -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                                                        -> update([
                                                            'tax_amount' => $tax_amount,
                                                            'taxable_amount' => $taxable_amount,
                                                            'dola' => Carbon::now(),
                                                            'changed_by' => auth() -> id()
                                                        ]);
                                    }
                                }

                                else
                                {
                                    $taxable_amount = $ref_amount;
                                    $add_deduct = $basis -> add_deduct;
                                    if($add_deduct == 'A')
                                    {
                                        $tax_amount = ($taxable_amount * $rate)/100;
                                    }
                
                                    if($add_deduct == 'D')
                                    {
                                        $tax_amount = (($taxable_amount * $rate)/100) * (-1);
                                    }

                                    /**payreqtaxes */
                                    $tx  = new Payreqtaxes;
                                    $tx -> requisition_no = $req_no;
                                    $tx -> entry_type_code = $entry_type_code;
                                    $tx -> item_no = $item_no;
                                    $tx -> tax_code = $get_tax_code;
                                    $tx -> taxable_amount = $taxable_amount;
                                    $tx -> tax_amount = $tax_amount;
                                    $tx -> tax_rate = $tax_rate;
                                    $tx -> created_by = trim(auth() -> id());
                                    $tx -> created_date = Carbon::now();
                                    $tx -> add_deduct = $add_deduct;
                                    $tx -> entry_type_reference =  $entry_type_reference;
                                    $tx -> save();

                                    if($tx)
                                    {
                                        $taxable_amount = abs($tx -> taxable_amount);
                                        $add_deduct = $basis -> add_deduct;
                                        if($add_deduct == 'A')
                                        {
                                            $tax_amount = ($taxable_amount * $tax_rate)/100;
                                        }
                    
                                        if($add_deduct == 'D')
                                        {
                                            $tax_amount = (($taxable_amount * $tax_rate)/100) * (-1);
                                        }

                                        $new_tax  = new Payreqtaxes;
                                        $new_tax -> requisition_no = $req_no;
                                        $new_tax -> entry_type_code = $entry_type_code;
                                        $new_tax -> item_no = $item_no;
                                        $new_tax -> tax_code = $tax_code;
                                        $new_tax -> taxable_amount = abs($taxable_amount);
                                        $new_tax -> tax_amount = $tax_amount;
                                        $new_tax -> tax_rate = $rate ;
                                        $new_tax -> created_by = trim(auth() -> id());
                                        $new_tax -> created_date = Carbon::now();
                                        $new_tax -> add_deduct = $add_deduct;
                                        $new_tax -> entry_type_reference = $entry_type_reference;
                                        $new_tax -> save();
                                    }
                                }
                                
                            }
                        }
                    }
                }

                elseif ($request->taxable == 'N') 
                {
                    /***delete taxes */
                    $delete_taxes = Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                                    -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                                    -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                                    -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                                    -> delete();
                }

                $doc_taxes = Payreqtaxes::where('requisition_no', $req_no) -> where('item_no', $item_no)
                            -> where('entry_type_reference', $entry_type_reference) 
                            -> where('entry_type_code',  $entry_type_code) -> sum('tax_amount');
                $local_doc_taxes =  $doc_taxes * $currency_rate;
                        
                $doc_nett = $ref_amount +  $doc_taxes;
            
                $doc_local_nett = $doc_nett * $currency_rate;

                /**update doc */
                $update_doc =  Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                            -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                            -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                            -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                            -> update([
                                'entry_type_nett' => $doc_nett,
                                'taxable'=>$request->taxable,
                                'local_entry_type_nett' => $doc_local_nett,
                                'total_taxes' => $doc_taxes,
                                'local_total_taxes' => $local_doc_taxes
                            ]);
                        
                $total_nett = Payreqstd::where('requisition_no', $req_no) -> sum('entry_type_nett');

                $local_nett_amount = Payreqstd::where('requisition_no', $req_no) -> sum('local_entry_type_nett');

                $total_taxes = Payreqtaxes::where('requisition_no', $req_no) -> sum('tax_amount');

                $payreqst =  Payreqst::where('req_no', $req_no)->first();

                $payreqst_amnt = $payreqst -> amount;

                $allocated = Payreqstd::where('requisition_no', $req_no) -> sum('ref_amount');

                $local_allocated = $allocated * $currency_rate;

                $unallocated_total = $payreqst_amnt - $allocated;

                $local_unallocated_total = $unallocated_total * $currency_rate;
            
                Payreqst::where('req_no', $req_no) -> update([
                    'user_str' => Auth::user()->user_name,
                    'total_taxes' => $total_taxes,
                    'local_total_taxes' => $total_taxes * $currency_rate,
                    'nett_amount' => $total_nett,
                    'local_nett_amount' => $local_nett_amount,
                    'escalated_to'=>$request->escalate_doc,
                    'allocated_total' =>  $allocated,
                    'local_allocated_total' => $local_allocated,
                    'unallocated_total' => $unallocated_total,
                    'local_unallocated_total'=>$local_unallocated_total,
                ]);
            }
            
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', "Requisition document successfully updated");
        } 
        
        catch (\Throwable $th) {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
        }
    }

    public function updateReqdoctax(Request $request)
    {
        // dd($request -> all());
        $this -> validate($request, [
            'edit_tax_rate' => 'required',
            'edit_taxable_amount' => 'required',
        ]);

        $req_no = trim($request->requisition_no);

        $entry_type_code = trim($request->entry_type_code);

        $item_no = trim($request -> item_no);

        $tax_code = trim($request -> edit_tax_code);

        $taxable_amount = str_replace(',', '', $request -> edit_taxable_amount);
        
        $tax_rate = $request -> edit_tax_rate;

        $entry_type_reference = trim($request -> entry_type_reference);

        $currency_rate = trim($request -> currency_rate);

        $offcd = trim($request -> offcd);

       
        
        if($request -> edit_add_deduct == 'D')
        {
            $tax_amount = (($taxable_amount * $tax_rate)/100) * (-1);
        }

        if($request -> edit_add_deduct == 'A')
        {
            $tax_amount = (($taxable_amount * $tax_rate)/100);
        }

        $update_tax =  Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                        ->whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                        ->whereRaw("trim(item_no) = '" . $item_no . "' ")
                        -> where('tax_code', $tax_code)
                        -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                        -> update([
                            'tax_rate' => $tax_rate,
                            'tax_amount' => $tax_amount,
                            'dola' => Carbon::now(),
                            'changed_by' => auth() -> id()
                        ]);
        $get_doc = Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ") -> whereRaw("trim(offcd) = '" . $offcd . "' ") 
                    -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                    ->whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                    ->whereRaw("trim(item_no) = '" . $item_no . "' ") -> first();
        $doc_gross = $get_doc ->  entry_type_amount;

        $doc_taxes = Payreqtaxes::where('requisition_no', $req_no) -> where('item_no', $item_no)
                                -> where('entry_type_reference', $entry_type_reference) 
                                -> where('entry_type_code',  $entry_type_code) -> sum('tax_amount');
                                
        $local_doc_taxes =  $doc_taxes * $currency_rate; 

        $doc_nett = $doc_gross +  $doc_taxes;
        
        $doc_local_nett = $doc_nett * $currency_rate;

        /**update doc */
        $update_doc =  Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                        ->whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                        ->whereRaw("trim(item_no) = '" . $item_no . "' ")
                        -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                        -> update([
                            'entry_type_nett' => $doc_nett,
                            'local_entry_type_nett' => $doc_local_nett,
                            'total_taxes' => $doc_taxes,
                            'local_total_taxes' => $local_doc_taxes
                        ]);

        $total_nett = Payreqstd::where('requisition_no', $req_no) -> sum('entry_type_nett');

        $local_nett_amount = Payreqstd::where('requisition_no', $req_no) -> sum('local_entry_type_nett');

        $total_taxes = Payreqtaxes::where('requisition_no', $req_no) -> sum('tax_amount');

        Payreqst::where('req_no', $req_no)->update([
            'user_str' => Auth::user()->user_name,
            'total_taxes' => $total_taxes,
            'local_total_taxes' => $total_taxes * $currency_rate,
            'nett_amount' => $total_nett,
            'local_nett_amount' =>  $local_nett_amount,
            'escalated_to'=>$request->escalate_doc,
        ]);
        Session::Flash('success', 'Tax successfully updated');
    }


    public function checkreqPaym(Request $request)
    {

        $user_grp = Auth::user()->role_id;

        $user_grp = trim($user_grp);

        $check_req = Payreqstd::where('requisition_no',$request->req_no)->count();

        // $usergrp = Aimsgroup::whereRaw("trim(aims_group) = '" . $user_grp . "'")
        //     ->first();
        if ($check_req>0 ) {

            if($request->act < 30){

                if ( Gate::check('check-claim-requisitions') && ( $check_req>0 ))  {
                    $allowed = "Y";
                }
                else if(Gate::check('check-claim-requisitions') && ( $check_req <= 0 )){
                    $allowed = "N";
                }   
            } 
             else if($request->act >= 30){
                if (Gate::check('approve-claim-requisition') && ( $check_req>0 ))  {
                    $allowed = "Y";
                }
                else if(Gate::check('approve-claim-requisition') && ( $check_req <= 0 )){
                    $allowed = "N";
                }
            }
          

        }

        // dd($usergrp->auth_req,$usergrp->check_req,$check_req);

        $result = array("status" => $allowed);
        return $result;

    }

    public function deleteReqdoctax(Request $request)
    {
        $req_no = trim($request->requisition_no);
        $entry_type_code = trim($request->entry_type_code);
        $item_no = trim($request -> item_no);
        $tax_code = trim($request -> tax_code);
        $entry_type_reference = trim($request -> entry_type_reference);
        $currency_rate = trim($request -> currency_rate);
        $offcd = trim($request -> offcd);

        $get_doc = Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ") -> whereRaw("trim(offcd) = '" . $offcd . "' ") 
                -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                -> whereRaw("trim(item_no) = '" . $item_no . "' ") -> first();
        $doc_gross = $get_doc ->  entry_type_amount;
        // dd($tax_code, $req_no, $entry_type_code, $item_no, $entry_type_reference);
        DB::beginTransaction();
        try 
        {
            $delete_tax =  Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                        -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                        -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                        -> where('tax_code', $tax_code)
                        -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                        -> delete();

            $doc_taxes = Payreqtaxes::where('requisition_no', $req_no) -> where('item_no', $item_no)
                                -> where('entry_type_reference', $entry_type_reference) 
                                -> where('entry_type_code',  $entry_type_code) -> sum('tax_amount');
            $local_doc_taxes =  $doc_taxes * $currency_rate;
                              
            $doc_nett = $doc_gross +  $doc_taxes;
            
            $doc_local_nett = $doc_nett * $currency_rate;

            /**update doc */
            $update_doc =  Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                            -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                            -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                            -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                            -> update([
                                'entry_type_nett' => $doc_nett,
                                'local_entry_type_nett' => $doc_local_nett,
                                'total_taxes' => $doc_taxes,
                            'local_total_taxes' => $local_doc_taxes
                            ]);

            $total_nett = Payreqstd::where('requisition_no', $req_no) -> sum('entry_type_nett');

            $local_nett_amount = Payreqstd::where('requisition_no', $req_no) -> sum('local_entry_type_nett');

            $total_taxes = Payreqtaxes::where('requisition_no', $req_no) -> sum('tax_amount');

            Payreqst::where('req_no', $req_no)->update([
                'user_str' => Auth::user()->user_name,
                'total_taxes' => $total_taxes,
                'local_total_taxes' => $total_taxes * $currency_rate,
                'nett_amount' => $total_nett,
                'local_nett_amount' =>  $local_nett_amount,
                'escalated_to'=>$request->escalate_doc,
            ]);
            DB::commit();
            Session::Flash('success', 'Removed Successfully');
        } 
        
        catch (\Throwable $e) 
        {
            $error_msg = json_encode($e->getMessage());
            $referrence = $req_no;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);

            DB::rollback();
            Session::flash('error','Failed, try again');
        }
        
        
    }



    public function getReqDetails(Request $request)
    {
        $req_no = $request->get('req_no');
        $payreqst = Payreqst::where('req_no', $req_no)
            ->first();
        return $payreqst;
    }

    public function getPerils(Request $request)
    {
        $peril = $request->get('peril');
        $perils = Perils::where('peril', $peril)
            ->first();
        return $perils;
    }

    public function getPerilItems(Request $request)
    {
        $peril = $request->get('peril');
        $perilitem = $request->get('perilitem');

        $perilsItems = Perilsitems::where('peril', $peril)
            ->where('item_no', $perilitem)
            ->first();
        return $perilsItems;
    }

    public function countClhmnallo(Request $request)
    {
        $req_no = removeRequsitionFormat($request->get('req_no'));
        $payreqst = Payreqst::where('req_no', $req_no)->first();
        $reference = str_pad($payreqst->dtrans_no, 6, '0', STR_PAD_LEFT) . $payreqst->account_year;

        $count = Clhmnallo::where('offcd', $payreqst->offcd)
            ->where('doc_type', $payreqst->doc_type)
            ->where('entry_type_descr', $payreqst->entry_type_descr)
            ->where('reference', $reference)
            ->count();
        $result = array('count' => $count);

        return $result;
    }

    public function getClhmnalloData(Request $request)
    {
        $req_no = removeRequsitionFormat($request->get('req_no'));
        $item_no = $request->get('item_no');
        $payreqst = Payreqst::where('req_no', $req_no)->first();
        $reference = str_pad($payreqst->dtrans_no, 6, '0', STR_PAD_LEFT) . $payreqst->account_year;

        $clhmnallo = Clhmnallo::where('offcd', $payreqst->offcd)
            ->where('doc_type', $payreqst->doc_type)
            ->where('entry_type_descr', $payreqst->entry_type_descr)
            ->where('reference', $reference)
            ->where('item_no', $item_no)
            ->get();

        return $clhmnallo;
    }

    public function reqAmend(Request $request)
    {
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try{
            $user = Auth::user()->user_name;
            $time = Carbon::now();
            $today = Carbon::today();

            $req_no = removeRequsitionFormat($request->req_no);
            $payreqst = Payreqst::where('req_no', $req_no)->first();
            $reference = str_pad($payreqst->dtrans_no, 6, '0', STR_PAD_LEFT) . $payreqst->account_year;
            //dd($payreqst);

            $payreqst_data = [
                'offcd' => $request->offcd,
                'dept_code' => $request->dept_code,
                'entry_type_descr' => $request->entry_type_descr,
                'name' => $request->name,
                'source_code' => $request->source_code,
                'subledger' => $request->subledger,
                'subledger_code' => $request->subledger_code,
                'slhead1' => $request->slhead1,
                'gross_amount' => $request->gross_amount,
                'amount' => $request->gross_amount,
                'local_gross_amount' =>$request->gross_amount * $request->currency_rate,
                'local_amount' => $request->gross_amount * $request->currency_rate,
                'payee_bank_account' => $request->payee_bank_account,
                'payee_bank_code' => $request->payee_bank_code,
                'payee_bank_name' => $request->payee_bank_name,
                'payee_bank_branch_code' => $request->payee_bank_branch_code,
                'payee_bank_branch' => $request->payee_bank_branch,
                'vat_no' => $request->vat_no,
                'pin_no' => $request->vat_no,
                'analyse_comm' => $request->analyse_comm,
                'analyse' => $request->analyse,
                'final_settle' => $request->final_settle,
                'currency_code' => $request->currency_code,
                'currency_rate' => $request->currency_rate,
                'narration' => $request->narration,
                'changed_by' => $user,
                'changed_time' => $time,
                'dola' => $today,
                'pay_method' => $request->pay_method,
                'cheque_no' => $request->cheque_no,
                'debit_account' => $request->dr_account,
                'credit_account' => $request->cr_account,
                'debit_subaccount' => $request->subaccount
            ];

            if ($request->source_code == 'CLM') {
                if (count($request->multi_claim_no) == 0) {
                } else {
                    $payreqst_data['multiclaims'] = 'Y';
                    $payreqst_data['gross_amount'] = $request->gross_multi;
                    $payreqst_data['amount'] = $request->gross_multi;

                    $payreqst_data['local_gross_amount'] = $request->gross_multi * $request->currency_rate;
                    $payreqst_data['local_amount'] = $request->gross_multi * $request->currency_rate;

                    $clhmnallo = Clhmnallo::where('offcd', $payreqst->offcd)
                        ->where('doc_type', $payreqst->doc_type)
                        ->where('reference', $reference)
                        ->where('entry_type_descr', $payreqst->entry_type_descr)
                        ->delete();
                }
            }

            if ($request->source_code == 'U/W') {
                $payreqst_data['branch'] = $request->branch;
                $payreqst_data['agent'] = $request->agent;
                $payreqst_data['class'] = $request->class;
                $payreqst_data['uw_year'] = $request->uw_year;
                $payreqst_data['client_number'] = $request->client_number;
                $payreqst_data['policy_no'] = removePolicyOrClaimFormat($request->policy_no);
                $payreqst_data['claim_no'] = $request->endt_renewal_no;
                $payreqst_data['credit_note_no'] = (int)substr($request->credit_note_no, 0, 6);
            }

            if ($request->analyse == 'Y') {
                if ($request->source_code == 'CLM' && count($request->multi_claim_no) > 0) {
                    $w_amount = $request->gross_multi;
                } else {
                    $w_amount = $request->gross_amount;
                }

                for ($i = 0; $i < count($request->deduction); $i++) {
                    $n = $i + 1;
                    if ($request->deduction[$i] != "") {
                        $cbdeduct = Cbdeduct::where('code', $request->deduction[$i])->first();
                        if ($cbdeduct->add_deduct == 'A') {
                            $w_amount = $w_amount + $request->deduction_amount[$i];
                        } else {
                            $w_amount = $w_amount - $request->deduction_amount[$i];
                        }
                    }

                    //deduction fields
                    $payreqst_data['deduction_amount_' . $n] = $request->deduction_amount[$i];
                    $payreqst_data['deduction_code_' . $n] = $request->deduction[$i];
                    $payreqst_data['amount'] = $w_amount;
                    $payreqst_data['local_amount'] = $w_amount * $request->currency_rate;
                    $payreqst_data['nett_amount'] = $w_amount * $request->currency_rate;
                    //dd($w_total_deduction);                   
                }
            } else if ($request->analyse == 'N') {
                for ($i = 1; $i < 11; $i++) {
                    $payreqst_data['deduction_amount_' . $i] = "";
                    $payreqst_data['deduction_code_' . $i] = "";
                }
            }

            //update payreqst
            $update_req = Payreqst::where('req_no', $req_no)
                ->update($payreqst_data);

            if ($update_req) {
                //$req_no = $create_payreqst['req_no'];
                $payreqst = Payreqst::where('req_no', $req_no)->first();
                $reference = str_pad($payreqst->dtrans_no, 6, '0', STR_PAD_LEFT) . $payreqst->account_year;
                //dd($payreqst->ln_no);
                
                //multi claims requisitions 
                if ($request->multi_claim_no !== null) {
                    if (count($request->multi_claim_no) > 0) {

                        $payreqst_data['multiclaims'] = 'Y';
                        for ($i = 0; $i < count($request->multi_claim_no); $i++) {
                            $item = $i + 1;
                            $clhmnallo_data = [
                                'doc_type' => $payreqst->doc_type,
                                'offcd' => $payreqst->offcd,
                                'entry_type_descr' => $payreqst->entry_type_descr,
                                'reference' => $reference,
                                'ln_no' => 0,
                                'total_amount' => $payreqst->gross_amount,
                                'foreign_total_amount' => $payreqst->amount,
                                'debit_account' => $request->multi_dr_account[$i],
                                'credit_account' => $request->multi_cr_account[$i],
                                'currency_code' => $payreqst->currency_code,
                                'currency_rate' => $payreqst->currency_rate,
                                'account_year' => $payreqst->account_year,
                                'dtrans_no' => $payreqst->dtrans_no,
                                'account_month' => $payreqst->account_month,
                                'claim_no' => $request->multi_claim_no[$i],
                                'item_no' => $item,
                                'class' => $request->multi_class[$i],
                                'foreign_amount' => $request->multi_amount[$i],
                                'amount' => $request->multi_amount[$i] * $payreqst->currency_rate,
                                'claimant_code' => $request->multi_claimant[$i],
                                'peril' => $request->multi_peril[$i],
                                'perilitem' => $request->multi_perilitem[$i],
                                'requisition_no' => $payreqst->req_no,
                            ];
                            //dd($clhmnallo_data);
                            $clhmnallo = Clhmnallo::insert($clhmnallo_data);
                        }
                    }
                }
                $req_details = Payreqst::where('req_no', $req_no)->get();

                Session::Flash('success', 'Requisition: ' . formatRequisitionNo($req_no) . ' updated successfully');
            }

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            $resp = array("status" => 1);
            return response()->json($resp);

        }catch(\Throwable $e){

            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            $reject = array("status" => 0);
            return response()->json($reject);
        }
    }

    public function reqCancel(Request $request)
    {  
        DB::Transaction(function () use ($request) {
            $req_no = $request->get('req_no');
            $cancel_reason =  $request->get('cancel_reason');
            
            $user = '';

            if (Auth::check()) {
                $user = Auth::user()->user_name;
            }
            
            $time = Carbon::now();
            $today = Carbon::today();
            
            $payreqst2 = Payreqst::where('req_no', $req_no)->update([
                'cancelled' => 'Y',
                'cancelled_by' => $user,
                'cancel_date' => $today,
                'cancel_time' => $time,
                'dola' => $today,
                'reason_cancelled'=>  $cancel_reason 
            ]);
            $payreqst = Payreqst::where('req_no', $req_no)->first();
            $reqRaise_email = Aimsuser::where('user_name',$payreqst->created_by)->value('email');
            $reqRaiser_name = Aimsuser::where('user_name',$payreqst->created_by)->value('first_name');

            $category = 'CANCEL REQUISITION';
			$notificationData = [
				'reqRaise_email'=> $reqRaise_email,
				'reqRaiser_name' => $reqRaiser_name,
				'category' => $category,
				'cancelled_by'=> Auth::user()->first_name,
			    'reason_cancelled'=>  $cancel_reason ,
                'req_no' => $request->get('req_no')
			];
			DispatchNotificationEvent::dispatch($slug = 'cancel-requisition',$notificationData);

            $payreqst = Payreqst::where('req_no', $req_no)->first();
            //send message
            $user = Aimsuser_web::whereRaw("trim(user_name) = '" . $payreqst->created_by . "'")->first()->user_id;
            $this->cancelReqlMsg($user, $req_no, $payreqst->claim_no);

            if($payreqst->source_code == 'CLM'){

                // If($payreqst->clauthorized_by !== null){
                //     $requisition = Payreqst::where('req_no', $req_no)->first();
                //     $dtran = Dtran0::first();
                //     $old_debit_no = $dtran->debit_no;
                //     $debit_no = $old_debit_no + 1;

                //     $creditclm = new Creditclm;
                //     $creditclm->dr_cr = 'D';
                //     $creditclm->doc_type = 'DRN';
                //     $creditclm->entry_type_descr = 'CLP';
                //     $creditclm->dtrans_no = $debit_no;
                //     $creditclm->nett_amount = $requisition->gross_amount;
                //     $creditclm->unallocated = $requisition->gross_amount;
                //     $creditclm->allocated = 0;
                //     $creditclm->claim_no = $requisition->claim_no;
                //     $creditclm->dola = Carbon::today();
                //     $creditclm->tran_no = $debit_no;
                //     $creditclm->recovery_date = $request->recovery_date;
                //     $creditclm->user_str = 'AUTOGENERATED';
                //     $creditclm->REQUISITION_NO = $requisition->req_no;
                //     $creditclm->policy_no = $requisition->policy_no;
                //     $creditClm->account_year=Carbon::now()->format('Y');
                //     $creditClm->account_month=Carbon::now()->format('m');
                //     $creditClm->class=$requisition->class;
                //     $creditClm->branch=$requisition->branch;
                //     $creditClm->effective_date=Carbon::today();
                //     $creditClm->despatch_date=Carbon::today();
                //     $creditClm->currency_code=$requisition->currency_code;
                //     $creditClm->currency_code=$requisition->currency_rate;
                //     $creditClm->details=$requisition->narration;
                //     $creditClm->payee=$requisition->name;


                //     $creditclm->save();
                //     // Update Dtran0 credit number
                //     Dtran0::where('debit_no', $old_debit_no)->update(['debit_no' => $debit_no]);


                // }

                ##check if schedule exists
                $clhmn = Clhmn::where('claim_no',$payreqst->claim_no)->first(); 
			    $classmodel = ClassModel::where('class',$clhmn->class)->first();

                if($classmodel->schedule_upload_mandatory == 'Y'){

                    update_schedule_as_paid($req_no,'N',$payreqst->claim_no,$payreqst->peril,$payreqst->perilitem,$payreqst->payee_type);

                }

                $payrestd = Payreqstd::where('REQUISITION_NO', $req_no)->where('claimant_code',$payreqst->claimant_code)->get();

                foreach($payrestd as $item){
                    $entryTypeRef = trim($item->entry_type_reference);

                    $existsInInvoice = ServiceProviderInvoices::where('invoice_number', $entryTypeRef)->exists();

                    if($existsInInvoice){
                        $s_provider_invoice_cancellation = ServiceProviderInvoices::where('invoice_number', $entryTypeRef)
                                                ->where('s_provider_code',$payreqst->claimant_code)
                                                ->update([
                                                    'cancelled'=>'Y',
                                                    'cancelled_by'=> $user,
                                                    'cancelled_date'=> $today,
                                                    'cancelled_reason'=> $cancel_reason,
                                                ]);
                    }

                }

            }


            if ($payreqst->batchno != null) {
                $commbatch_upd = Commbatch::where('gbatch', $payreqst->batchno)
                    ->update([
                        'cancelled' => 'Y',
                        'cancelled_by' => $user,
                    ]);
                $commana_upd = Commana::where('batchno', $payreqst->batchno)
                    ->update([
                        'requisition_no' => '',
                        'batchno' => 0
                    ]);
                $commdet_upd = Commdet::where('requisition_no', $payreqst->req_no)
                    ->update([
                        'requisition_no' => '',
                        'req' => 'N'
                    ]);
            }

            if ($payreqst->multiclaims == "Y") {
                $reference = str_pad($payreqst->dtrans_no, 6, '0', STR_PAD_LEFT) . $payreqst->account_year;
                $clhmnallo = Clhmnallo::where('offcd', $payreqst->offcd)
                    ->where('doc_type', $payreqst->doc_type)
                    ->where('reference', $reference)
                    ->where('entry_type_descr', $payreqst->entry_type_descr)
                    ->get();
                foreach ($clhmnallo as $clhmnallo1) {
                    $clhmnallo = Clhmnallo::where('offcd', $payreqst->offcd)
                        ->where('doc_type', $payreqst->doc_type)
                        ->where('reference', $reference)
                        ->where('entry_type_descr', $payreqst->entry_type_descr)
                        ->update([
                            'cancelled' => 'Y',
                        ]);
                }
            }

            if ($payreqst->entry_type_descr == 'PTA') {
                $comesabatch_upd = Comesabatch::where('gbatch', $payreqst->batchno)
                    ->update([
                        'cancelled' => 'Y',
                        'cancelled_by' => $user,
                    ]);
                $comesaana_upd = Comesa_ana::where('batchno', $payreqst->batchno)
                    ->update([
                        'requisition_no'=>null,
                        'batchno' => 0
                    ]);
                $comesadet_upd = Comesadet::where('requisition_no', $payreqst->req_no)
                    ->update([
                        'requisition_no'=>null
                    ]);
            }

            if ($payreqst->batch_no != null){
                Facuploadbatch::where('batch_no',$payreqst->batch_no)
                ->update([
                    'cancelled' => 'Y'
                ]);

                FacUploads::where('batch_no',$payreqst->batch_no)
                ->update([
                    'cancelled' => 'Y'
                ]);

            }
            
            echo $payreqst2;
        });
    }

    public function getClhmnallo(Request $request)
    {
        $doc_type = $request->get('doc_type');
        $dtrans_no = $request->get('dtrans');
        $year = $request->get('year');
        $entry = $request->get('entry_type');
        $offcd = $request->get('offcd');
        $reference = str_pad($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;

        $clhmnallo = Clhmnallo::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('reference', $reference)
            ->where('entry_type_descr', $entry)
            ->get();
        return Datatables::of($clhmnallo)
            ->editColumn('reference', function ($ref) {
                return formatReference($ref->reference);
            })
            ->editColumn('claim_no', function ($clm) {
                return formatPolicyorClaim($clm->claim_no);
            })
            ->editColumn('foreign_amount', function ($famt) {
                return number_format($famt->foreign_amount, 2);
            })
            ->editColumn('amount', function ($amt) {
                return number_format($amt->amount, 2);
            })
            ->make(true);
    }

    public function getCurrencydetails(Request $request)
    {
        $currency = $request->get('currency_code');
        $currency_details = Currency::where('currency_code', $currency)->get();

        echo $currency_details;
    }

    public function getPaymethods()
    {
        // $pay_method= Olpaymethd::where('default_method','=','Y')->get();
        $pay_method = Olpaymethd::all();
        echo  $pay_method;
    }

    public function getREQPaymethod(Request $request)
    {
        
        $req_no = str_replace('/','',$request->req_no);
        $pay_method = Payreqst::where('req_no', $req_no)->get();
        echo  $pay_method;
    }

    public function getpayGlhead(Request $request)
    {
        $method = $request->get('method');
        $pay_glhead = Olpaymethd::where('pay_method', $method)->get();
        echo $pay_glhead;
    }

    public function getDebitaccounts()
    {
        $dr_accounts = Nlparams::where('prid', 'GLH')
        ->whereRaw("(bank_flag = 'N' or bank_flag is null)")
        ->get(['prsno', 'prdesc', 'prid', 'slhead_flag']);
        echo  $dr_accounts;
    }

    public function getDebitaccount(Request $request)
    {
        $prsno = $request->get('prsno');
        $dr_account = Nlparams::where('prsno',$request->prsno)->get()[0];
        echo  $dr_account;
    }

    public function getCreditaccounts(Request $request)
    {
        ##only return banks with the operating currency and payment method selected
		$group_codes = AllowedTransactions::whereRaw("trim(pay_mode) = ?", [$request->pay_method])->distinct()->pluck('group_code');
		
		$group_codes_array = $group_codes->toArray();

		$glbanks = array();

        if($request->offset_prem == 'Y'){

            $nctrl = Nlctrl::pluck('offset_premium_account')->first();

            $nlparams = Nlparams::where('prid', 'GLH')
                // ->where('bank_flag', null)
                // ->orwhere('bank_flag', 'N')
                ->where('prsno',$nctrl)
			    ->get(['prsno', 'prdesc']);

        }else{

            $nlparams = Nlparams::where('prid', 'GLH')->where('bank_suspense_account', 'Y')
			->get(['prsno', 'prdesc']);

            $pettycash = Nlparams::where('prid', 'GLH')->where('petty_cash_flag', 'Y')
					->pluck('prsno');

            $glbanks_list = Glbank::where('operating_currency', $request->currency_code)
            ->whereIn('group_code', $group_codes_array)->whereNotIn('prsno',$pettycash)
            ->get(['prsno', 'prdesc', 'bank_acc_code', 'operating_currency', 'group_code', 'default_method']);
    
            foreach ($glbanks_list as $value) {
                array_push($glbanks, $value);
            }

        }

	
		
		foreach ($nlparams as $nlparam) {
			$nlparam['group_code'] = '';
			$nlparam['operating_currency'] = '';
			$nlparam['bank_acc_code'] = '';
            $nlparam['default_method'] = '';

			array_push($glbanks, $nlparam);
		}

		return collect($glbanks);

        //$pay_method = $request->get('pay_method');
        /*if($pay_method == 4 || $pay_method == 7){
            $cr_accounts = Nlparams::where('prid','GLH')->get();
        }
        else{*/

        // if($request->curr_code){
        //     $cr_accounts = GLBank::where('operating_currency',$request->curr_code)
        //     ->whereIn('group_code', function($query) use ($pay_method) {
        //     $query->select('group_code')
        //         ->from('allowed_transactions')
        //         ->where('pay_mode', $pay_method);
        //     })->get();
        // }

        // else {
        //     $cr_accounts = GLBank::where('operating_currency', 0)
        //     ->whereIn('group_code', function($query) use ($pay_method) {
        //     $query->select('group_code')
        //         ->from('allowed_transactions')
        //         ->where('pay_mode', $pay_method);
        //     })->get();
        // }
        
       
        
         //}

        echo  $cr_accounts;
    }

    public function getcommCreditaccounts(Request $request)
    {
            $currency = Commana::where('batchno','0')
                                ->where('requisition_no', null)
                                ->first();

            $cr_accounts = GLBank::whereRaw("operating_currency = '".$currency->currency_code."' or operating_currency is null ")
                                    ->get();
   
        echo  $cr_accounts;
    }

    public function getcommpaymethod(Request $request)
    {
 
        $prsno = trim($request->prsno);

        $gl_bank = DB::table('glbanks')->where('prid', 'BNK')->whereRaw("trim(prsno) = '" . $prsno . "' ")->first();

        $grp_code = $gl_bank->group_code;

        $allowed_pay_methods = AllowedTransactions::whereRaw("trim(group_code) = '" . $grp_code . "' ")->pluck('pay_mode');

        $paymodes = [];

        foreach ($allowed_pay_methods as $method) 
        {
            $paymode = Olpaymethd::whereRaw("trim(pay_method) = '" . $method . "' ")->get()[0];
            array_push($paymodes, $paymode);
        }

        // dd($paymodes);

        return [ 
            'paymodes' => $paymodes
        ];
    }

    public function getCreditaccount(Request $request)
    {
        $prsno = $request->get('prsno');

      
        $nlparam = Nlparams::where('prid', 'GLH')->where('prsno', $prsno)->get(['prsno', 'prdesc', 'bank_suspense_account'])->first();
       
        if ($nlparam->bank_suspense_account == 'Y' ||$request->offset_premium == 'Y') {

           echo  $nlparam ;
        }

        else {
            echo GLBank::where('prsno',  $request->prsno)->get(['prsno', 'prdesc'])->first();
        }
    }

    //generate cheque_no
    public function getChequeno(Request $request)
    {
        $pay_method = $request->get('pay_method');
        $prsno = $request->get('cr_account');

        $bank = Glbank::where('prsno',$prsno)->get()[0];

        $cheque = Chequemast::where('bank_acc_code',$bank->bank_acc_code)
                            ->whereRaw("(cheque_cancelled = 'N' or cheque_cancelled is null) and (cheque_issued = 'N' or cheque_issued is null) ")
                            //->orderBy('cheque_no','ASC')
                            ->selectRaw("min(to_number(cheque_no)) as cheque_no")
                            ->get()[0];
                            //dd($cheque);
        return $cheque; 
    }

    //generate cheque_no
    public function geteftnumber(Request $request)
    {
        $pay_method = $request->get('pay_method');
        $prsno = $request->get('cr_account');

        $bank = Glbank::where('prsno',$prsno)->get()[0];

        $eft = Eftmast::where('bank_acc_code',$bank->bank_acc_code)
                            ->whereRaw("(eft_cancelled = 'N' or eft_cancelled is null) and (eft_issued = 'N' or eft_issued is null) ")
                            //->orderBy('cheque_no','ASC')
                            ->selectRaw("min(to_number(eft_no)) as eft_no")
                            ->get()[0];

        return $eft; 
    }

    //generate cheque_no
    public function getrtgsnumber(Request $request)
    {
        $pay_method = $request->get('pay_method');
        $prsno = $request->get('cr_account');

        $bank = Glbank::where('prsno',$prsno)->get()[0];

        $rtgs = RTGSMast::where('bank_acc_code',$bank->bank_acc_code)
                            ->whereRaw("(rtgs_cancelled = 'N' or rtgs_cancelled is null) and (rtgs_issued = 'N' or rtgs_issued is null) ")
                            //->orderBy('cheque_no','ASC')
                            ->selectRaw("min(to_number(rtgs_no)) as rtgs_no")
                            ->get()[0];
                            //dd($cheque);
        return $rtgs; 
    }

    public function getEntrytype(Request $request)
    {

        $entry_type = $request->get('entry_type');
        $entry_details = Cbtrans::where('doc_type', 'PAY')
            ->where('descr', $entry_type)->get();
        echo  $entry_details;
    }

      /**Requisition taxes */
      public function reqDocTaxes_datatable(Request $request)
      {
          
          $item_no = trim($request->item_no);
          $req_no = trim($request->req_no);
          $entry_type_code = trim($request->entry_type_code);
          
          $doc_taxes = Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                      ->whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                      ->whereRaw("trim(item_no) = '" . $item_no . "' ") -> get();
          
          return Datatables::Of($doc_taxes)
              ->addColumn('action', function ($tax) {
                  return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
              })
  
              -> editColumn('tax_amount', function ($tax){
                  $tax_amount = number_format($tax -> tax_amount, 2);
                  return $tax_amount;
              })
  
              -> editColumn('taxable_amount', function ($tax){
                  $taxable_amount = number_format($tax -> taxable_amount, 2);
                  return $taxable_amount;
              })
  
              -> addColumn('tax_code_descr', function ($tax){
                  $tax_code1 = number_format($tax -> tax_code);
                  $tax_code = DB::table('gltaxgroupdtl') -> where('tax_code', $tax_code1) -> first();
                  return $tax_code -> tax_type;
              })
  
              -> addColumn('tax_rate_1', function ($tax){
                  return $tax -> tax_rate .'%';
              })
              -> make(true);
      }

      public function reqDocsdatatable(Request $request)
      {
          $req_no = $request->get('req_no');
          $req_docs = Payreqstd::where('requisition_no', $req_no)->get();
          
          return Datatables::Of($req_docs)
                  -> addColumn('action', function ($doc) {
                  
                      return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
                  })
              -> addColumn('entry_type', function ($doc){
                  $entry_type = Reqdocs::where('code', $doc -> entry_type_code) -> first();
                  return $entry_type -> description;
              })
              
              -> editColumn('entry_type_date', function ($doc){
                  $date = new DateTime($doc -> entry_type_date);
                  $format_date =  $date->format('d/m/Y');
                  return $format_date;
              })
  
              -> editColumn('entry_type_amount', function ($doc){
                  $entry_type_amount = number_format($doc -> entry_type_amount, 2);
                  return $entry_type_amount;
              })
  
              -> editColumn('entry_type_nett', function ($doc){
                  $entry_type_nett = number_format($doc -> entry_type_nett, 2);
                  return $entry_type_nett;
              })
              
              -> editColumn('vat_rate', function ($doc){
                  $vat_rate = $doc -> vat_rate.'%';
                  return $vat_rate;
              })
  
              -> editColumn('vat_amount', function ($doc){
                  $vat_amount = number_format($doc -> vat_amount, 2);
                  return $vat_amount;
              })
              
              -> editColumn('wtx_rate', function ($doc){
                  $wtx_rate = $doc -> wtx_rate.'%';
                  return $wtx_rate;
              })
              -> editColumn('wtx_amount', function ($doc){
                  $wtx_amount = number_format($doc -> wtx_amount, 2);
                  return $wtx_amount;
              })
  
              -> editColumn('wtx_on_vat_rate', function ($doc){
                  $wtx_on_vat_rate = number_format($doc -> wtx_on_vat_rate, 2);
                  return $wtx_on_vat_rate;
              })
  
              -> editColumn('wtx_on_vat_amount', function ($doc){
                  $wtx_on_vat_amount = number_format($doc -> wtx_on_vat_amount, 2);
                  return $wtx_on_vat_amount;
              })
  
              -> editColumn('date_received', function ($doc){
                  $date = new DateTime($doc -> date_received);
                  $format_date =  $date->format('d/m/Y');
                  return $format_date;
              })
              -> make(true);
      }

      
    public function reqDocDetails(Request $request)
    {
        // dd($request -> all());
        $offcd = trim($request->offcd);
        $item_no = trim($request->item_no);
        $req_no = trim($request->req_no);
        $entry_type_code = trim($request->entry_type_code);
        $entry_type_reference = trim($request->entry_type_ref);
        $req_doc = Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ") -> whereRaw("trim(offcd) = '" . $offcd . "' ") 
                    -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                    ->whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                    ->whereRaw("trim(item_no) = '" . $item_no . "' ") -> first();
        
        $reqdoc = Reqdocs::all();
        $today = Carbon::today()->format('Y-m-d');
        $office = Nlparams::where('prid', 'OFF')->get();
        $cbdeduct = Cbdeduct::where('doc_type', 'PAY')->get();

        $requisition = Payreqst::where('req_no', $req_no)->first();

        $cbtrans = Cbtrans::where('source_code', trim($requisition->source_code))
        ->where('descr', trim($requisition->entry_type_descr))
        ->where('doc_type', 'PAY')->get()[0];


        $dat = array(
            'main' => 'Front Office',
            'module' => 'Payments',
            'submodule' => 'Requisitions',
            'submod1' => formatRequisitionNo($req_no)

            // 'main' => 'Front Office',
            // 'module' => 'Payments',
            // 'submod1' => formatRequisitionNo($req_no)
        );
        return view::make('gl.fo.requisitiondocs_details', compact('requisition', 'req_doc', 'cbtrans', 'today', 'amount', 'office', 'cbdeduct'))->with('dat', $dat);
    }



    public function getReqDocDetails(Request $request)
    {
        $offcd = trim($request->offcd);
        $item_no = trim($request->item_no);
        $req_no = trim($request->req_no);
        $entry_type_code = trim($request->entry_type_code);
        $entry_type_reference = trim($request->entry_type_reference);

        $office = Nlparams::where('prid', 'OFF') -> whereRaw("trim(prsno)='" . $offcd . "'") -> first();
        $doc_type = Reqdocs::where('code', $entry_type_code) -> first();
      
        $req_doc = Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ") -> whereRaw("trim(offcd) = '" . $offcd . "' ") 
                    -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                    -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                    -> whereRaw("trim(item_no) = '" . $item_no . "' ") -> first();
        
        return ['doc_type' => $doc_type, 'req_doc' => $req_doc, 'office' => $office];
    }



    public function getDepartment(Request $request)
    {

        $code = $request->get('dept_code');

        $dept_code = STR_PAD($code, 8, ' ', STR_PAD_RIGHT);

        $dept_details = Nlparams::where('prid', 'DEP')
            ->where('prsno', $dept_code)->get();
        echo $dept_details;
    }

    public function getOffice(Request $request)
    {
        $offcd = $request->get('offcd');
        $prsno = STR_PAD($offcd, 8, ' ', STR_PAD_RIGHT);
        $office_details = Nlparams::where('prid', 'OFF')
            ->where('prsno', $prsno)->get();
        echo $office_details;
    }

    public function getUnallocatedSum(Request $request)
    {

        return Acdet::where('policy_no', removePolicyOrClaimFormat($request->policy_no))
            ->where('doc_type', 'DRN')
            ->where('source', 'U/W')
            // ->get();
            ->sum('unallocated');
    }



    public function facanaendt(Request $request)
    {
        if ($request->analyse_by == 'B') {
            $data = $request->selected_participants;

            $endorsements = array();

            foreach ($data as $dat) {
                $broker_branch = $request->branch;

                $broker_agent = $request->agent;

                $branch = $dat['branch'];

                $agent = $dat['agent'];

                $sql = "select  c.name, a.currency_code,a.endt_renewal_no,a.policy_no,a.source,a.doc_type,
                a.branch,a.agent,a.unallocated,a.client_number,a.foreign_unallocated,a.gross,
                a.nett,a.comm_amt,a.reference,a.dola from acdet a 
                join crmast c on c.branch = a.branch and c.agent = a.agent
                where a.source='CRD' and
                a.doc_type='CRN' and a.broker_branch='".$broker_branch."' and a.broker_agent='".$broker_agent."' and a.foreign_unallocated < 0 and
                a.currency_code='".$request->currency_code."'
                and a.branch = '".$branch."' and a.agent = '".$agent."'
                and not exists (select 
                p.endt_renewal_no from polana p join payreqst t on t.req_no=p.req_no where 
                p.doc_type='PRQ' and p.branch=a.branch and p.agent=a.agent and 
                p.endt_renewal_no=a.endt_renewal_no and 
                (t.cancelled='N' or t.cancelled is null) and 
                (t.voucher_raised='N' or t.voucher_raised is null) ) order by dola asc";

                $part_endorsements = DB::select($sql);

                foreach ($part_endorsements as $value) {
                    array_push($endorsements, $value);
                }
            }
        }

        else{
            $sql = "select c.name, a.currency_code,a.endt_renewal_no,a.policy_no,a.source,a.doc_type,
                    a.branch,a.agent,a.unallocated,a.client_number,a.foreign_unallocated,a.gross,
                    a.nett,a.comm_amt,a.reference,a.dola from acdet a 
                    join crmast c on c.branch = a.branch and c.agent = a.agent
                    where a.source='CRD' and
                    a.doc_type='CRN' and a.branch='".$request->branch."' and a.agent='".$request->agent."' and a.foreign_unallocated < 0 and
                    a.currency_code='".$request->currency_code."' and not exists (select 
                    p.endt_renewal_no from polana p join payreqst t on t.req_no=p.req_no where 
                    p.doc_type='PRQ' and p.branch=a.branch and p.agent=a.agent and 
                    p.endt_renewal_no=a.endt_renewal_no and 
                    (t.cancelled='N' or t.cancelled is null) and 
                    (t.voucher_raised='N' or t.voucher_raised is null) ) order by dola asc";
            $endorsements = DB::select($sql);
        }
        
        /*$endorsements = DB::table($gb . '.acdet')->select(['acdet.currency_code', 
        'acdet.endt_renewal_no','acdet.policy_no', 'acdet.source', 'acdet.doc_type', 'acdet.branch', 
        'acdet.unallocated',
        'acdet.endt_renewal_no',
        'acdet.client_number',
        'acdet.foreign_unallocated',
        'acdet.gross',
        'acdet.nett',
        'acdet.comm_amt',
        'acdet.reference',
        'acdet.dola',
        'acdet.agent'])
            ->join($gb . '.debitmast', function ($join) {
                $join->on('acdet.endt_renewal_no', '=', 'acdet.endt_renewal_no');
            })
            // ->join($gb . '.polana', function ($join) {
            //     $join->on('polana.endt_renewal_no', '=', 'acdet.endt_renewal_no');
            // })
            // ->join($gb . '.payreqst', function ($join) {
            //     $join->on('payreqst.req_no', '=', 'polana.req_no');
            // })
            ->where('acdet.currency_code', $request->currency_code)
            ->where('acdet.source', 'CRD')
            ->where('acdet.branch', $request->branch)
            ->where('acdet.agent', $request->agent)
            ->where('acdet.doc_type', 'CRN')
            ->where('acdet.unallocated', '<', 0)
            ->where('acdet.foreign_unallocated', '<', 0)
            ->orderBy('dola','asc')
            ->distinct()
            ->get();*/
           
            
            return Datatables::of($endorsements)
            ->addColumn('fully_paid', function ($endorsement) {
                return 'Y';
            }) 
            ->addColumn('check_endt', function ($endorsement) {
                return '';
            }) 
            ->addColumn('foreign_unallocated', function ($endorsement) {
                return abs($endorsement->foreign_unallocated);
            }) 
            ->addColumn('comm_amt', function ($endorsement) {
                return abs($endorsement->comm_amt);
            }) 
            ->addColumn('reference', function ($endorsement) {
                return formatReference($endorsement->reference);
            }) 
            ->addColumn('nett', function ($endorsement) {
                return abs($endorsement->nett);
            }) 
            ->addColumn('gross', function ($endorsement) {
                return abs($endorsement->gross);
            }) 
            ->addColumn('policy_holder', function ($endorsement) {
               return Client::where('client_number', $endorsement->client_number)->first()->name;
            }) ->make(true);
    }

    public function getfacDebitNote(Request $request)
    {

        $acdet= Acdet::where('endt_renewal_no', $request->endt_renewal_no)
                    ->where('branch', $request->branch)
                    ->where('agent', $request->agent)
             ->where('doc_type', 'CRN')
            ->where('source', 'CRD')
            ->get([
                'unallocated',
                'foreign_unallocated',
                'currency_code',
                'currency_rate',
                'doc_type',
                'source'
            ]);

            $acdet[0]->unallocated=abs($acdet[0]->unallocated);
            $acdet[0]->foreign_unallocated=abs($acdet[0]->foreign_unallocated);
            return $acdet;
    }


    public function getfacallocation(Request $request)
    {
       
        $allocations=Polana::where('req_no',$request->req_no)
                            ->where('entry_type_descr','FAC')
                            ->where('doc_type','PRQ')
                            ->get();
                            
        return Datatables::of($allocations)
                        ->addColumn('policy_no', function ($allocation) {
                            return formatPolicyOrClaim($allocation->policy_no);
                        }) 
                        ->addColumn('endt_renewal_no', function ($allocation) {
                            return formatPolicyOrClaim($allocation->endt_renewal_no);
                        }) 
                         ->addColumn('total_amount', function ($allocation) {
                            return number_format($allocation->total_amount);
                        }) 
                        ->addColumn('reference', function ($allocation) {
                            $reference = str_pad($allocation['ref_dtrans_no'], 6, '0', STR_PAD_LEFT) . $allocation['ref_account_year'];
                            return formatReference($reference);
                         
                        }) 
                        ->addColumn('policy_amount', function ($allocation) {
                            return number_format($allocation->policy_amount);
                        }) 
                        ->make(true);
    }

    public function deleteReqdoc(Request $request)
    {
        // dd($request->all());
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try {
            $req_no = trim($request->req_no);
            $entry_type_code = trim($request->entry_type_code);
            $item_no = trim($request -> item_no);
            $entry_type_reference = trim($request -> entry_type_reference);
            $offcd = trim($request -> offcd);
            $currency_rate = trim($request -> currency_rate);
            // dd($req_no, $entry_type_code, $entry_type_reference,  $offcd , $item_no, $currency_rate);
            $delete_doc = Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ") -> whereRaw("trim(offcd) = '" . $offcd . "' ") 
                            -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                            -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                            -> whereRaw("trim(item_no) = '" . $item_no . "' ") 
                            -> delete();
            
            $delete_taxes =  Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                            -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                            -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                            -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                            -> delete();
        
            /******Renumber items*******/
            $get_count = Payreqstd::whereRaw("trim(requisition_no) = '".$req_no."' and  trim(item_no) >= '". $item_no."'") -> get();
            
            foreach ($get_count as $key => $value) 
            {
                $update_no = Payreqstd::whereRaw("trim(requisition_no) = '".$req_no."' and  trim(item_no) = '". $value -> item_no."'")
                            -> update([
                                'item_no' => $value -> item_no - 1
                            ]);
                
                $update_taxes = Payreqtaxes::where('requisition_no', $req_no) -> where('item_no', $value -> item_no)
                                -> update([
                                    'item_no' => $value -> item_no - 1
                                ]);
            }
          
            $total_nett = Payreqstd::where('requisition_no', $req_no) -> sum('entry_type_nett');
           
            $local_nett_amount = Payreqstd::where('requisition_no', $req_no) -> sum('local_entry_type_nett');
           
            $total_taxes = Payreqtaxes::where('requisition_no', $req_no) -> sum('tax_amount');

            $payreqst =  Payreqst::where('req_no', $req_no)->first();

            $payreqst_amnt = $payreqst -> amount;
            
            $allocated = Payreqstd::where('requisition_no', $req_no) -> sum('ref_amount');
           
            $local_allocated = $allocated * $currency_rate;
            
            $unallocated_total = $payreqst_amnt - $allocated;

            $local_unallocated_total = $unallocated_total * $currency_rate;
            // dd(  $local_unallocated_total);
            Payreqst::where('req_no', $req_no) -> update([
                'user_str' => Auth::user()->user_name,
                'total_taxes' => $total_taxes,
                'local_total_taxes' => $total_taxes * $currency_rate,
                'nett_amount' => $total_nett,
                'local_nett_amount' => $local_nett_amount,
                'escalated_to'=>$request->escalate_doc,
                'allocated_total' =>  $allocated,
                'local_allocated_total' => $local_allocated,
                'unallocated_total' => $unallocated_total,
                'local_unallocated_total'=>$local_unallocated_total,
            ]);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            return 1;
        } 

        catch (\Throwable $e) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            // dd($e);
            $error_msg = json_encode($e->getMessage());
            $referrence = $request->req_no;
			$route_name = Route::getCurrentRoute()->getActionName();
			log_error_details($route_name,$error_msg,$referrence);
            Session::flash('error','Failed, try again');
            return 2;
        }
    }

    private function rfn_req_doc($req_no, $credit_note_no, $request, $source_code )
    {
        // dd($request->all(),$req_no, $credit_note_no);
        $acdet = Acdet::where('source', $source_code)->whereRaw("trim(reference)='".$credit_note_no."'")
        ->where('doc_type', 'CRN')->get()[0];

        $req = Payreqst::where('req_no', $req_no)->get()[0];

        $item_no = 1;
        $entry_type_code = 'CRN';
        $entry_type_reference = $acdet->reference;
        $gross_amount = str_replace(',', '', $request->gross_amount);
        $currency_rate = $acdet->currency_rate;
       
        //add the items to payreqstd
        $doc = Payreqstd::create([
            'offcd' => $request->offcd,
            'requisition_no' => $req_no,
            'entry_type_code' => $entry_type_code,
            'entry_type_reference' => $entry_type_reference,
            'entry_type_date' => $acdet->date_processed,
            'item_no' => $item_no,
            'ref_item_no' => 1,
            'ref_period_year' => $acdet->account_year,
            'ref_period_month' => $acdet->account_month,
            'currency_code' => $acdet->currency_code,
            'currency_rate' => $acdet->currency_rate,
            'date_received' =>  Carbon::today()->format('Y-m-d'),
            'taxable' =>  'Y',
            'entry_type_amount' => str_replace(',', '', $request->gross_amount),
            'ref_amount' => str_replace(',', '', $request->gross_amount),
            'local_entry_type_amount' => str_replace(',', '', $request->gross_amount) * $currency_rate,
            'local_ref_amount' =>  str_replace(',', '', $request->gross_amount) * $currency_rate
        ]);

        /***Taxes to payreqtaxes */
        $tax_grp = $req->tax_group;
       
        $tax_types = Gltaxgroupdt::where('group_code', $tax_grp)->get();
       
        foreach ($tax_types as $type) 
        {
            $tax_type = $type->tax_type;

            $tax_code = $type->tax_code;

            $gltax= Gltaxes::where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();

            $rate = $gltax->tax_rate;

            /**Check if tax is applied on nett amnt or on another tax */
            $basis = Gltaxtypes::where('tax_type', $tax_type)->first();

            if($basis->basis == 'N')
            {
                $check_tx = Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                            -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                            -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                            -> where('tax_code', $tax_code)
                            -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                            -> first();

                if($check_tx)
                {
                    
                }

                else
                {
                    $taxable_amount = $gross_amount;
                    $add_deduct = $basis -> add_deduct;
                    if($add_deduct == 'A')
                    {
                        $tax_amount = ($taxable_amount * $rate)/100;
                    }

                    if($add_deduct == 'D')
                    {
                        $tax_amount = (($taxable_amount * $rate)/100) * (-1);
                    }

                    /**payreqtaxes */
                    $tax  = new Payreqtaxes;
                    $tax -> requisition_no = $req_no;
                    $tax -> entry_type_code = $entry_type_code;
                    $tax -> item_no = $item_no;
                    $tax -> tax_code = $tax_code;
                    $tax -> taxable_amount = $taxable_amount;
                    $tax -> tax_amount = $tax_amount;
                    $tax -> tax_rate = $rate ;
                    $tax -> created_by = trim(auth() -> id());
                    $tax -> created_date = Carbon::now();
                    $tax -> add_deduct = $add_deduct;
                    $tax -> entry_type_reference = $entry_type_reference;
                    $tax -> tax_type = trim($gltax->tax_type);
                    $tax -> save();
                }
            }

            elseif ($basis -> basis == 'T') 
            {
                /**Check tax to apply to */
                $get_tax_code = $basis -> tax_code;
                
                $check_tax = Payreqtaxes::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
                            -> whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
                            -> whereRaw("trim(item_no) = '" . $item_no . "' ")
                            -> where('tax_code', $get_tax_code)
                            -> whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
                            -> first();
                        
                if($check_tax)
                {
                    $taxable_amount = abs($check_tax -> tax_amount);
                    $add_deduct = $basis -> add_deduct;
                    if($add_deduct == 'A')
                    {
                        $tax_amount = ($taxable_amount * $rate)/100;
                    }

                    if($add_deduct == 'D')
                    {
                        $tax_amount = (($taxable_amount * $rate)/100) * (-1);
                    }

                    $tax  = new Payreqtaxes;
                    $tax -> requisition_no = $req_no;
                    $tax -> entry_type_code = $entry_type_code;
                    $tax -> item_no = $item_no;
                    $tax -> tax_code = $tax_code;
                    $tax -> taxable_amount = $taxable_amount;
                    $tax -> tax_amount = $tax_amount;
                    $tax -> tax_rate = $rate ;
                    $tax -> created_by = trim(auth() -> id());
                    $tax -> created_date = Carbon::now();
                    $tax -> add_deduct = $add_deduct;
                    $tax -> entry_type_reference = $entry_type_reference;
                    $tax -> tax_type = trim($check_tax->tax_type);
                    $tax -> save();
                }

                else
                {
                    $taxable_amount = $gross_amount;
                    
                    $add_deduct = $basis -> add_deduct;
                    if($add_deduct == 'A')
                    {
                        $tax_amount = ($taxable_amount * $rate)/100;
                    }

                    if($add_deduct == 'D')
                    {
                        $tax_amount = (($taxable_amount * $rate)/100) * (-1);
                    }

                    /**payreqtaxes */
                    $tx = new Payreqtaxes;
                    $tx -> requisition_no = $req_no;
                    $tx -> entry_type_code = $entry_type_code;
                    $tx -> item_no = $item_no;
                    $tx -> tax_code = $get_tax_code;
                    $tx -> taxable_amount = $taxable_amount;
                    $tx -> tax_amount = $tax_amount;
                    $tx -> tax_rate = $rate ;
                    $tx -> created_by = trim(auth() -> id());
                    $tx -> created_date = Carbon::now();
                    $tx -> add_deduct = $add_deduct;
                    $tx -> entry_type_reference = $entry_type_reference;
                    $tx -> tax_type = trim($gltax->tax_type);
                    $tx -> save();

                    if($tx)
                    {
                        $taxable_amount = abs($tx -> taxable_amount);
                        $add_deduct = $basis -> add_deduct;
                        if($add_deduct == 'A')
                        {
                            $tax_amount = ($taxable_amount * $rate)/100;
                        }
    
                        if($add_deduct == 'D')
                        {
                            $tax_amount = (($taxable_amount * $rate)/100) * (-1);
                        }

                        $new_tax = new Payreqtaxes;
                        $new_tax -> requisition_no = $req_no;
                        $new_tax -> entry_type_code = $entry_type_code;
                        $new_tax -> item_no = $item_no;
                        $new_tax -> tax_code = $tax_code;
                        $new_tax -> taxable_amount = $taxable_amount;
                        $new_tax -> tax_amount = $tax_amount;
                        $new_tax -> tax_rate = $rate ;
                        $new_tax -> created_by = trim(auth() -> id());
                        $new_tax -> created_date = Carbon::now();
                        $new_tax -> add_deduct = $add_deduct;
                        $new_tax -> entry_type_reference = $entry_type_reference;
                        $new_tax -> tax_type = trim($tx->tax_type);
                        $new_tax -> save();
                    }
                }
                
            }
        }

        $doc_taxes = Payreqtaxes::where('requisition_no', $req_no)->where('item_no', $item_no)
            ->where('entry_type_reference', $entry_type_reference) 
            ->where('entry_type_code',  $entry_type_code)->sum('tax_amount');
        
        $local_doc_taxes =  $doc_taxes * $currency_rate;
       
        $doc_nett = $gross_amount +  $doc_taxes;

        $doc_local_nett = $doc_nett * $currency_rate;
       
        /**update doc */
        $update_doc =  Payreqstd::whereRaw("trim(requisition_no) = '" . $req_no . "' ")
        ->whereRaw("trim(entry_type_code) = '" . $entry_type_code . "' ") 
        ->whereRaw("trim(item_no) = '" . $item_no . "' ")
        ->whereRaw("trim(entry_type_reference) = '" . $entry_type_reference . "' ") 
        ->update([
            'entry_type_nett' => $doc_nett,
            'local_entry_type_nett' => $doc_local_nett,
            'total_taxes' => $doc_taxes,
            'local_total_taxes' => $local_doc_taxes,
        ]);
        
        /***End Taxes to payreqtaxes */

        $upd_req = Payreqst::where('req_no', $req_no)->update([
            'user_str' => Auth::user()->user_name,
            'total_taxes' => $doc_taxes,
            'local_total_taxes' => $local_doc_taxes,
            'nett_amount' => $doc_nett,
            'local_nett_amount' => $doc_local_nett,
            'unallocated_total' => 0,
            'local_unallocated_total'=>0,
            'escalated_to'=>$request->escalate_doc,
            'escalate_id' => 10,
            'workflow_id' => 900,
            'allocated_total' => $doc_nett,
            'local_allocated_total'=> $doc_local_nett,
        ]);
        //end of add items
    }

    public function get_treaty_analysis(Request $request)
    { 
        if($request->has('doc_type') == 'PAY'){
            $analysis = TreatyPolana::where('reference', $request->reference)->where('doc_type','PAY')->get();
            
            $cbmast = Cbmast::Where('dtrans_no', (int)substr($request->reference, 0, -4))->where('doc_type', 'PAY')->get()[0];
               
            return Datatables::of($analysis)
                ->editColumn('amount', function ($allocation) {
                    return number_format($allocation->amount);
                }) 

                ->addColumn('name', function ($broker) use ($cbmast) {
                    if($cbmast->broker_branch != null  && $cbmast->broker_agent != null ){
                        $name0 = DB::select(" SELECT  b.name
                        FROM crmast b where b.agent = '$broker->agent' and b.branch = '$broker->branch' ")[0];

                        return $name0->name;
                    }

                    else{
                        $name0 = DB::select(" SELECT  b.name
                        FROM crmast b where b.agent = '$broker->broker_agent' and b.branch = '$broker->broker_branch' ")[0];

                        return $name0->name;
                    }
                }) 
                ->make(true);
        }

        else {
            $analysis = TreatyPolana::where('req_no', $request->req_no)->where('entry_type_descr','TRT')
            ->where('doc_type','PRQ')->get();

            $req = Payreqst::Where('req_no', $request->req_no)->get()[0];
           
            return Datatables::of($analysis)
                ->editColumn('amount', function ($allocation) {
                    return number_format($allocation->amount);
                }) 

                ->addColumn('name', function ($broker) use ($req) {
                    if($req->broker_branch == null  && $req->broker_agent == null ){
                        $name0 = DB::select(" SELECT  b.name
                        FROM crmast b where b.agent = '$broker->broker_agent' and b.branch = '$broker->broker_branch' ")[0];

                        return $name0->name;
                    }

                    else{
                        $name0 = DB::select(" SELECT  b.name
                        FROM crmast b where b.agent = '$broker->agent' and b.branch = '$broker->branch' ")[0];

                        return $name0->name;
                    }
                }) 
                ->make(true);
        }
        
    }

    public function getGlAccounts(Request $request){
        return Nlparams::where('prid','GLH')->where('status','<>','D')->whereRaw(" (bank_flag = 'N' or bank_flag is null) ")
        ->get(['prsno', 'prdesc']);
    }

    public function getPayreqstana(Request $request){
        $payreqana = Payreqstana::where('requisition_no', $request->get('req_no'))
                    ->join('nlparams', 'payreqstana.glhead', '=', 'nlparams.prsno')
                    ->select('payreqstana.*', 'nlparams.prdesc') // Include fields from both tables
                    ->get();
        
        return Datatables::of($payreqana)
            ->editColumn('amount', function ($amt) {
                return number_format($amt->amount, 2);
            })
            ->editColumn('foreign_amount', function ($amt) {
                return number_format($amt->foreign_amount, 2);
            })
            ->make(true);
    }

    public function declinedReq(Request $request)
    {
        $get_req = Payreqst::where('req_no', $request->req_no)->update([
            'declined'=>'Y'
        ]);
        $route = 'requisition.details';
        return redirect()->route($route, ['req_no' => $request->req_no]);
    }


    public function actionClaimReq(Request $request){

        $req_no = $request->req_no;
        $user_id =  Auth::user()->user_id;
        $escalation = Escalate_pol::where(['sent_to'=>$user_id, 'req_no'=>$req_no, 'type'=>'REQ'])->update([
            'read'=>'1',
        ]);
        return redirect()->route('claims.req_process', ['req_no'=>$req_no]);

    }

    public function get_fac_broker_part(Request $request) {
        $branch =  $request->branch;
		$agent =  $request->agent;
        
        if ($request->analyse_by == 'B') {
            $results = Acdet::join('crmast', function ($join) {
                $join->on('acdet.BRANCH', '=', 'crmast.BRANCH')
                    ->on('acdet.AGENT', '=', 'crmast.AGENT');
            })
            ->where('BROKER_BRANCH',  $branch)->where('BROKER_AGENT', $agent)
            ->where('SOURCE', 'CRD')->where('DOC_TYPE', 'CRN')
            ->where('UNALLOCATED', '<', 0)
            ->groupBy('crmast.BRANCH', 'crmast.AGENT', 'crmast.NAME')
            ->select(
                'crmast.BRANCH',
                'crmast.AGENT',
                'crmast.NAME',
                'SUM(abs(acdet.FOREIGN_UNALLOCATED)) as unallocated'
            )->get();

            return Datatables::of($results)
            ->addColumn('check_part', function ($endorsement) {
                return '';
            }) 
            ->make(true);
        }
    }

    public function fac_uploads(Request $request)
    {
      
        $batch = Facuploadbatch::where(function ($query) {
                                    $query->where('cancelled', '!=', 'Y')
                                        ->orWhereNull('cancelled');
                                })
                                ->get();

        return Datatables::of($batch)
        ->addColumn('action',function($batch){
            return '<a class="btn btn-xs" id="fac_btn_view" style="color:blue;" >View</a>';
        })
        ->editColumn('cancel',function($batch){
          if($batch->processed=='Y' ||$batch->cancelled=='Y'){
            return '<button class="btn btn-danger btn-xs cancelupload" disabled><i class="fa fa-times"> Cancel</i></button>';
          }else{
            return '<button class="btn btn-danger btn-xs cancelupload" ><i class="fa fa-times"> Cancel</i></button>';
          }
            
        })->rawColumns(['cancel','action'])
            ->make(true); 
    }

    public function getFacParticipants(Request $request){

        if ($request->analyse_by == 'B') {
            $agents = Crmast::where('ri_broker', 'Y')->get();
        }

        else if ($request->analyse_by == 'P') {
            $agents = Crmast::whereRaw("(ri_broker is null or ri_broker ='N')")->get();
        }
        

        foreach($agents as $agent){
            $results[] = [
            'value'=>$agent->branch.$agent->agent.' '.$agent->name,'branch'=>$agent->branch,'agent'=>$agent->agent,'name'=>$agent->name];
        }
        
        return Response::Json($results);

    }

    public function downloadFACTemp(Request $request)
    {
        $file = public_path()."/downloads/fac_template.xlsx";
        $headers = array('Content-Type: application/xlsx',);
        return Response::download($file, 'fac_template.xlsx',$headers);
    }

    public function Upload_fac(Request $request)
    {
        
        if ($request->hasfile('fac_data')) {
            //Get uploaded File Details
            $path = $request->file('fac_data')->getRealPath();
            $extension = File::extension($request->file('fac_data')->getClientOriginalName());
            $today = Carbon::today();
            $user = Auth::user()->user_name;
            $file_name = $request->file('fac_data')->getClientOriginalName();

            $branch_agent = str_replace("/", "", $request->upd_fac_part);

            $branch = (int) substr($branch_agent, 0, 2);
            $agent = (int) substr($branch_agent, 2, 4);

            $count_errors = 0;



            if ($extension == "xls" || $extension == "xlsx") {
                

              $excel_data = FileUploadManager::excelUpload($request->file('fac_data'));

                if (!empty($excel_data)) {
                    $errors = array();
                    $references = array();
                    $z = 0;
                    $count = 0;
      


                    foreach ($excel_data as $value) {

                        $count = $count + 1;

                        $policy_no = trim($value['policy_no']);
                        $endt_renewal_no = trim($value['endt_renewal_no']);
                        $reference = trim($value['reference']);
                        $amount = intval(trim(str_replace(',', '', $value['amount'])));

                        $reference_number = (int)$reference;
                        $credit_note_no = (int)substr($reference, 0, -4);




                        $result = DB::select("
                            SELECT CASE WHEN EXISTS (
                                SELECT 1 FROM PAYREQST WHERE CREDIT_NOTE_NO = :credit_note_no
                                UNION
                                SELECT 1 FROM PAYREQST 
                                WHERE REQ_NO IN (SELECT REQ_NO FROM polana WHERE REF_DTRANS_NO = :credit_note_no) 
                                AND (CANCELLED IS NULL OR CANCELLED = 'N')
                                UNION SELECT 1 FROM FAC_UPLOADS WHERE REFERENCE = :reference AND CANCELLED IS NULL
                            ) THEN 1 ELSE 0 END AS transaction_exists
                            FROM DUAL
                        ", [
                            'credit_note_no' => $credit_note_no,
                            'reference' => $reference,
                        ]);


                        if ($request->analyse_by == 'B') {
                            $valid = Acdet::join('crmast', function ($join) use ($branch, $agent) {
                                    $join->on('acdet.BRANCH', '=', 'crmast.BRANCH')
                                        ->on('acdet.AGENT', '=', 'crmast.AGENT');
                                })
                                ->where('BROKER_BRANCH',  $branch)
                                ->where('BROKER_AGENT', $agent)
                                ->where('SOURCE', 'CRD')
                                ->where('DOC_TYPE', 'CRN')
                                ->where('UNALLOCATED', '<', 0)
                                ->where('REFERENCE', $reference)
                                ->groupBy('crmast.BRANCH', 'crmast.AGENT', 'crmast.NAME')
                                ->select(
                                    'crmast.BRANCH',
                                    'crmast.AGENT',
                                    'crmast.NAME',
                                    'SUM(abs(acdet.FOREIGN_UNALLOCATED)) as unallocated'
                                )->first();
                        
                            if (empty($valid)) {
                                $count_errors++;
                                $errors[$z++] = "Reference " . $reference . " provided in row " . $count . " does not belong to the selected Broker ";
                            } 

                            $analyseby = 'Broker';

                            $acdet_valid = Acdet::where('reference', $reference)
                            ->where('endt_renewal_no', $endt_renewal_no)
                            ->exists();


                        } else {
                            $sql = "SELECT c.name, a.currency_code, a.endt_renewal_no, a.policy_no, a.source, a.doc_type,
                                        a.branch, a.agent, a.unallocated, a.client_number, a.foreign_unallocated, a.gross,
                                        a.nett, a.comm_amt, a.reference, a.dola
                                    FROM acdet a
                                    JOIN crmast c ON c.branch = a.branch AND c.agent = a.agent
                                    WHERE a.source = 'CRD'
                                        AND a.doc_type = 'CRN'
                                        AND a.branch = '" . $branch . "'
                                        AND a.agent = '" . $agent . "'
                                        AND a.foreign_unallocated < 0
                                        AND a.reference = '" . $reference . "'
                                        AND NOT EXISTS (
                                            SELECT p.endt_renewal_no
                                            FROM polana p
                                            JOIN payreqst t ON t.req_no = p.req_no
                                            WHERE p.doc_type = 'PRQ'
                                                AND p.branch = a.branch
                                                AND p.agent = a.agent
                                                AND p.endt_renewal_no = a.endt_renewal_no
                                                AND (t.cancelled = 'N' OR t.cancelled IS NULL)
                                                AND (t.voucher_raised = 'N' OR t.voucher_raised IS NULL)
                                        )
                                    ORDER BY dola ASC";
                        
                            $valid = DB::select($sql);
                        
                            if (empty($valid)) {
                                $count_errors++;
                                $errors[$z++] = "Reference " . $reference . " provided in row " . $count . " does not belong to the selected Participant ";
                            }

                            $analyseby = 'Participant';

                            $acdet_valid = Acdet::where('reference', $reference)
                            ->where('branch', $branch)
                            ->where('agent', $agent)
                            ->where('endt_renewal_no', $endt_renewal_no)
                            ->exists();
                        }
                        

                                        
                       
                        $transactionExists = $result[0]->transaction_exists;


                    
                        if (in_array(array($reference), $references)) {
                            $count_errors++;
                            $errors[$z++] = "Duplicate reference " . $reference." in the uploaded file";
                        }

                        array_push($references, array($reference));

                        if (empty($policy_no)) {
                            $count_errors++;
                            $errors[$z++] = "Policy number cannot be null in row " . $count;
                        }
                        if (empty($endt_renewal_no)) {
                            $count_errors++;
                            $errors[$z++] = "Endorsement number cannot be null in row " . $count;
                        }
                        if (empty($reference)) {
                            $count_errors++;
                            $errors[$z++] = "Reference number cannot be null in row " . $count;
                        }
                        if (empty(trim($amount)) || trim($amount) < 1) {
                            $count_errors++;
                            $errors[$z++] = "Amount must be greater than zero in row " . $count;
                        }

                        if ($acdet_valid == false) {
                            $count_errors++;
                            $errors[$z++] = "Reference ". $reference." or Endorsement ".$endt_renewal_no." or both don't belong to the selected ".$analyseby;
                        }

                        if ($transactionExists == 1) {
                            $count_errors++;
                            $errors[$z++] = "Reference " . $reference." exists in another requisition or another uploaded batch";
                            
                        }

                    }

                    if ($count_errors > 0) {
                        $dat = array(
                            'main' => 'Underwriting',
                            'module' => 'UW Requisition',
                            'submodule' => 'New Requisition'
                        );

                        return view::make('gl.fo.upload_fac_errors', compact('errors'))
                            ->with('dat', $dat);
                    } else {

                        $serial_no = $this->genBatch();
                        
                        $month = Carbon::today()->format('m');
                        $year = Carbon::today()->format('Y');
                        $batch_no = 'FAC' . str_pad($serial_no, 6, '0', STR_PAD_LEFT) . $year . $month;

                        DB::connection(env('DB_CONNECTION'))->beginTransaction();
                        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
                        try {
                            //insert data into table
                            $batch_data = [
                                'batch_no' => $batch_no,
                                'account_year' => $year,
                                'account_month' => $month,
                                'file_name' => $file_name,
                                'date_uploaded' => Carbon::now(),
                                'user_name' => trim($user),
                                'processed' => 'N',
                                'total_local_amount' => 0,
                                'total_foreign_amount' => 0,
                                'branch' => $branch,
                                'agent' => $agent,
                                'analysis' => $request->analyse_by
                            ];
                          
                            //return $this->updateBatchNo();
                            try{
                             $file_batch = Facuploadbatch::insert($batch_data);
                            }catch(\Throwable $e){
                                DB::connection(env('DB_CONNECTION'))->rollback();
                                DB::connection(env('DB_CONNECTION1'))->rollback();
                                return redirect()->route("req_enquiry")->with('error', 'Failed to upload data');
                            }
                            if ($file_batch) {

                                $new_serial = $this->updateBatchNo();

                                foreach ($excel_data as $upload) {


                                    $policy_no = trim($upload['policy_no']);
                                    $endt_renewal_no = trim($upload['endt_renewal_no']);
                                    $reference = trim($upload['reference']);
                                    $amount = intval(trim(str_replace(',', '', $upload['amount'])));

                                    $item = FacUploads::where('batch_no',$batch_no)->count();
                                    $item = $item + 1;
                             

                                  $file_data = [
                                        'batch_no' => $batch_no,
                                        'item_no' => $item,
                                        'policy_no' =>$policy_no,
                                        'endt_renewal_no' =>$endt_renewal_no,
                                        'local_amount' => $amount,
                                        'date_uploaded' => Carbon::today(),
                                        'reference' => $reference,
                                        'branch' => $branch,
                                        'agent' => $agent
                                    ];

                                    $fac_data = FacUploads::insert($file_data);
                                }
                                if ($fac_data) {

                                    $total_amount = FacUploads::where('batch_no', $batch_no)->sum('local_amount');

                                    $update_batch = Facuploadbatch::where('batch_no', $batch_no)->update([
                                        'total_local_amount' => $total_amount
                                    ]);
                                    DB::connection(env('DB_CONNECTION'))->commit();
                                    DB::connection(env('DB_CONNECTION1'))->commit();
                                }
                            }

                            return redirect()->route("req_enquiry")->with('success', 'CSV file uploaded successfully');
                        } catch (\Throwable $e) {
                            DB::connection(env('DB_CONNECTION'))->rollback();
                            DB::connection(env('DB_CONNECTION1'))->rollback();
                            return redirect()->route("req_enquiry")->with('error', 'Failed to upload data');
                        }
                    }
                } else {
                    return redirect()->back()->with('error', 'The uploaded file is empty');
                }
            } else if ($extension != "csv" /*|| $extension != "xls" || $extension != "xlsx"*/) {
                return redirect()->back()->with('error', 'You can only upload .csv files');
            }
        } else {
            return redirect()->back()->with('error', 'No file chosen. Please Choose a file to upload');
        }
    }

    function getfacendorsements(Request $request)
	{
        $branch_agent = str_replace("/", "", $request->branch_agent);
        $branch = (int) substr($branch_agent, 0, 2);
        $agent = (int) substr($branch_agent, 2, 4);
        $batches = Facuploadbatch::where('branch', $branch)
                                    ->where('agent', $agent)
                                    ->where('analysis', $request->analyze)
                                    ->where(function ($query) {
                                        $query->where('processed', '=', 'N')
                                            ->orWhereNull('processed');
                                    })
                                    ->where(function ($query) {
                                        $query->where('cancelled', '=', 'N')
                                            ->orWhereNull('cancelled');
                                    })
                                    ->get(['batch_no']);
    
		return $batches;
	}

    function getfacult_batch(Request $request)
	{
        $batch_no = $request->batch_no;
		$amount = Facuploadbatch::where('batch_no', $batch_no)->get(['total_local_amount']);
		return $amount;
	}

    function genBatch()
	{
		$batch = Doctype::where('doc_type', 'FAC')->first();
		$dtrans = $batch->serial_no;
		return $dtrans;
	}

    function updateBatchNo()
	{
		$new_serial = Doctype::where('doc_type','FAC')->increment('serial_no',1);
		return $new_serial;
	}
    
    public function gettreatyCRNs(Request $request)
    {
        
       
        if($request->doc_type =='TAC'){
            $doc_type = 'DRN';
            $sign = '>';
            $entry_type = 'TRT';
        }

        else if ($request->doc_type == 'PRC') {
            $doc_type = 'DRN';
            $sign = '>';
            $entry_type = 'PRC';
        }
        
        else{
            $doc_type = 'CRN';
            $sign = '<';
            $entry_type = 'TRT';
        }
       
        if($request->treaty_analyzed == 'P'){
            $branch = $request->branch;
            $agent = $request->agent;

            $items = Reiacdet::where('doc_type', $doc_type)
            ->where('quarter', intval($request->treaty_quarter))
            ->where('account_year', intval($request->treaty_year))
            ->where('treaty_code', $request->treaty_type)
            ->where('branch', $branch)
            ->where('agent', $agent)
            ->where('unallocated',$sign, 0)
            ->where('entry_type_descr', $entry_type)
            ->get(['account_year','currency_rate','date_processed','trans_number','allocated','unallocated', 'branch', 'agent', 'broker_branch', 'broker_agent']);
        
         return Datatables::of($items)
            ->addColumn('check_br_ag',function($data){
            })

            ->addColumn('payee_name', function ($data) {
                return  Crmast::where('agent', $data->broker_agent)
                ->where('branch', $data->broker_branch)
                ->where('ri_broker', 'Y')
                ->first()->name;
            })
            
            ->editColumn('unallocated',function($data){
                return abs($data->unallocated);
            })
            ->make(true);
        }
        
        else{
            $broker_branch = $request->branch;
            $broker_agent = $request->agent;
            
            $items = Reiacdet::where('doc_type', $doc_type)
            ->where('quarter', intval($request->treaty_quarter))
            ->where('account_year', intval($request->treaty_year))
            ->where('treaty_code', $request->treaty_type)
            ->where('broker_branch', $broker_branch)
            ->where('broker_agent', $broker_agent)
            ->where('unallocated',$sign, 0)
            ->where('entry_type_descr', $entry_type)
            ->get(['account_year','currency_rate','date_processed','trans_number','allocated','unallocated','agent', 'branch',  'broker_branch', 'broker_agent']);

            return Datatables::of($items)
                ->addColumn('check_br_ag',function($data){

                })
                ->addColumn('payee_name', function ($data) {
                    return  Crmast::where('agent', $data->agent)
                    ->where('branch', $data->branch)
                    ->first()->name;
                })
                ->editColumn('unallocated',function($data){
                    return abs($data->unallocated);
                })
                ->make(true);
        }


    }

    
    public function updateChecker(Request $request)
    {

        // $getpayreqst = Payreqst::where('req_no', $request->req_text_id) -> first();
    
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try {

                Payreqst::where('req_no', $request->req_text_id) -> update([
                    'checked_by' => Auth::user()->user_name,
                    'checked_date' => Carbon::today()
                ]);
           
            
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', "Requisition successfully Checked");
        } 
        
        catch (\Throwable $th) {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
        }
    }

}
