<?php

namespace App\Http\Controllers\gl\fo;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\Datatables\Datatables;

use Carbon\Carbon;

use View;
use App\Cbmast;
use App\Acdet;
use App\Acdetallo;
use App\Acdetallonew;
use App\Clhmnallo;
use App\Nlparams;
use App\Olbnknames;
use App\Crmast;
use App\Agmnf;
use Auth;
use DB;
use App\Nlctrl;
use App\Currency;

use App\Services\IntermediaryQueryService;
use App\ValueObjects\IntermediaryQueryParams;


class Receipt_datatable extends Controller
{
	public function show(Request $request)
    {
        // $nlparams = Nlparams::where('prid','OFF')->get();
        $sledger = Nlparams::where('prid','SLT')->get();

        $month = Carbon::today()->format('m');
        $year = Carbon::today()->format('Y');    

    	$dat=array(
    	'main' => 'Front Office', 
    	'module' => 'Receipts'
    	);
        $userBranch = str_pad(Auth::user()->branch, 3,'0',STR_PAD_LEFT);

            
        $nlparams = Nlparams::where('prid','OFF')
                    ->whereRaw("trim(prsno)='".$userBranch."'")
                    ->get();


        $banks=Olbnknames::all();

        $nlctrl = (int)Nlctrl::where('nlctrl', 0)->get()[0]['receipt_backdate_days'];

    	return view::make('gl.fo.receipts',compact('nlparams','sledger','month','year', 'nlctrl'))
        ->with('dat',$dat)->with('banks',$banks);
    } 

	public function index()
	{

		$receipts = Cbmast::where('doc_type','REC')
                            ->where('entry_type_descr','!=','REC')
                            ->where('source_code','<>','COR') //exclude comm recovery receipts
                            ->where('pay_method','<>',null)
                            ->orderBy('created_time','DESC');
                            // ->orderBy('dtrans_no','DSC');
                            
                            
        return Datatables::of($receipts)
        ->editColumn('foreign_amount',function($amt){
            return number_format($amt->foreign_amount,2);
        })
        ->editColumn('amount',function($amt){
            return number_format($amt->amount,2);
        })
        ->editColumn('receipt_date',function($amt){
            
            return formatDate($amt->receipt_date);
        
        })
        ->addColumn('reference', function ($receipt) {
            $padded_dtrans_no = str_pad($receipt->dtrans_no, 6, '0', STR_PAD_LEFT);
            return $padded_dtrans_no .'/'. $receipt->account_year;
        })
        ->addColumn('currency', function($curr) {
            $currency = Currency::where('currency_code', $curr->currency_code)->value('currency');
            return $currency;
        })
        ->addColumn('cancelled',function($cancelled){
            if($cancelled->cancelled == 'Y'){
                return "REVERSED";
            }
            else{
                return 'NO';
            }
        })
        ->filterColumn('reference', function ($query, $keyword) {
            $query->whereRaw("lpad(dtrans_no,6,0)|| '/'|| account_year like ?", ["%{$keyword}%"]);
            $query->orwhereRaw("lpad(dtrans_no,6,0)|| account_year like ?", ["%{$keyword}%"]);
        })

        ->addColumn('agent',function($agt){
            if($agt->source_code == "R/I" || $agt->source_code == "FAC"){
                $agent = Crmast::where('branch', $agt->branch)->where('agent',$agt->agent_no)->get()[0];
                return $agent->name;
            }

            else{
               // $agent = Agmnf::where('branch', $agt->branch)->where('agent',$agt->agent_no)->get()[0];

                $intermediaryParams = new IntermediaryQueryParams([
                    'agentNo' => $agt->agent_no
                
                ]);
            
                $agent  =  IntermediaryQueryService ::getIntermediaryByAgentNo($intermediaryParams)->first();
                return $agent->name;
            }
        })

        ->make(true);    
	}

	public function cancelled()
	{
    
		$receipts = Cbmast::where('doc_type','REC')
                            ->where('entry_type_descr','!=','REC')
                            ->where('source_code','<>','COR')
                            ->where('pay_method','<>',null)
                            ->where('cancelled','=','Y')
                            ->orderBy('created_time','DESC');
                            // ->orderBy('dtrans_no','DSC');
                            

        return Datatables::of($receipts)
        ->editColumn('foreign_amount',function($amt){
            return number_format($amt->foreign_amount,2);
        })
        ->editColumn('amount',function($amt){
            return number_format($amt->amount,2);
        })
        ->editColumn('receipt_date',function($amt){
            
            return formatDate($amt->receipt_date);
        
        })
        ->addColumn('reference', function ($receipt) {
            $padded_dtrans_no = str_pad($receipt->dtrans_no, 6, '0', STR_PAD_LEFT);
            return $padded_dtrans_no .'/'. $receipt->account_year;
        })
        ->addColumn('currency', function($curr) {
            $currency = Currency::where('currency_code', $curr->currency_code)->value('currency');
            return $currency;
        })
        ->filterColumn('reference', function ($query, $keyword) {
            $query->whereRaw("lpad(dtrans_no,6,0)|| '/'|| account_year like ?", ["%{$keyword}%"]);
            $query->orwhereRaw("lpad(dtrans_no,6,0)|| account_year like ?", ["%{$keyword}%"]);
        })

        ->addColumn('agent',function($agt){
            if($agt->source_code == "R/I" || $agt->source_code == "FAC"){
                $agent = Crmast::where('branch', $agt->branch)->where('agent',$agt->agent_no)->get()[0];
                return $agent->name;
            }

            else{
               // $agent = Agmnf::where('branch', $agt->branch)->where('agent',$agt->agent_no)->get()[0];
                $intermediaryParams = new IntermediaryQueryParams([
                    'agentNo' => $agt->agent_no
                
                ]);
            
                $agent  =  IntermediaryQueryService ::getIntermediaryByAgentNo($intermediaryParams)->first();
                return $agent->name;
            }
        })

        ->make(true);    
	}
	public function notcancelled()
	{
    
		$receipts = Cbmast::where('doc_type','REC')
                            ->where('entry_type_descr','!=','REC')
                            ->where('source_code','<>','COR')
                            ->where('pay_method','<>',null)
                            ->where('cancelled','=',null)
                            ->orderBy('created_time','DESC');
                            // ->orderBy('dtrans_no','DSC');

        return Datatables::of($receipts)
        ->editColumn('foreign_amount',function($amt){
            return number_format($amt->foreign_amount,2);
        })
        ->editColumn('amount',function($amt){
            return number_format($amt->amount,2);
        })
        ->editColumn('receipt_date',function($amt){
            
            return formatDate($amt->receipt_date);
        
        })
        ->addColumn('reference', function ($receipt) {
            $padded_dtrans_no = str_pad($receipt->dtrans_no, 6, '0', STR_PAD_LEFT);
            return $padded_dtrans_no .'/'. $receipt->account_year;
        })
		->addColumn('currency', function($curr) {
            $currency = Currency::where('currency_code', $curr->currency_code)->value('currency');
            return $currency;
        })
        ->filterColumn('reference', function ($query, $keyword) {
            $query->whereRaw("lpad(dtrans_no,6,0)|| '/'|| account_year like ?", ["%{$keyword}%"]);
            $query->orwhereRaw("lpad(dtrans_no,6,0)|| account_year like ?", ["%{$keyword}%"]);
        })

        ->addColumn('agent',function($agt){
            if($agt->source_code == "R/I" || $agt->source_code == "FAC"){
                $agent = Crmast::where('branch', $agt->branch)->where('agent',$agt->agent_no)->get()[0];
                return $agent->name;
            }

            else{
               // $agent = Agmnf::where('branch', $agt->branch)->where('agent',$agt->agent_no)->get()[0];
                $intermediaryParams = new IntermediaryQueryParams([
                    'agentNo' => $agt->agent_no
                
                ]);
            
                $agent  =  IntermediaryQueryService ::getIntermediaryByAgentNo($intermediaryParams)->first();
                return $agent->name;
            }
        })

        ->make(true);    
	}

    public function recoveryReceipts()
    {
        $receipts = Cbmast::where('doc_type','REC')
                            ->where('entry_type_descr','!=','REC')
                            ->where('source_code','COR')
                            ->where('pay_method','<>',null)
                            ->orderBy('created_time','DESC');
                            // ->orderBy('dtrans_no','DSC');

        return Datatables::of($receipts)
        ->editColumn('foreign_amount',function($amt){
            return number_format($amt->foreign_amount,2);
        })
        ->editColumn('amount',function($amt){
            return number_format($amt->amount,2);
        })
        ->editColumn('cancelled',function($cancelled){
            if($cancelled->cancelled == 'Y'){
                return "CANCELLED";
            }
            else{
                return 'NO';
            }
        })
        ->addColumn('currency', function($curr) {
            $currency = Currency::where('currency_code', $curr->currency_code)->value('currency');
            return $currency;
        })
        ->editColumn('receipt_date',function($amt){
            
            return formatDate($amt->receipt_date);
        
        })
        ->addColumn('reference', function ($receipt) {
            $padded_dtrans_no = str_pad($receipt->dtrans_no, 6, '0', STR_PAD_LEFT);
            return $padded_dtrans_no .'/'. $receipt->account_year;
        })
        ->filterColumn('reference', function ($query, $keyword) {
            $query->whereRaw("lpad(dtrans_no,6,0)|| '/'|| account_year like ?", ["%{$keyword}%"]);
            $query->orwhereRaw("lpad(dtrans_no,6,0)|| account_year like ?", ["%{$keyword}%"]);
        })

        ->addColumn('agent',function($agt){
            if($agt->source_code == "R/I" || $agt->source_code == "FAC"){
                $agent = Crmast::where('branch', $agt->branch)->where('agent',$agt->agent_no)->get()[0];
                return $agent->name;
            }

            else{
               // $agent = Agmnf::where('branch', $agt->branch)->where('agent',$agt->agent_no)->get()[0];
                $intermediaryParams = new IntermediaryQueryParams([
                    'agentNo' => $agt->agent_no
                
                ]);
            
                $agent  =  IntermediaryQueryService ::getIntermediaryByAgentNo($intermediaryParams)->first();
                return $agent->name;
            }
        })

        ->make(true);    
    }

    public function getAllocation(Request $request)
    {
    $ref = $request->get('ref');
    $acdet = Acdetallonew::where('reference',$ref)->get();
    
    return Datatables::of($acdet)
    ->editColumn('reference',function($ref){
        return formatReference($ref->reference);
    })
    ->editColumn('amount',function($amt){
        return number_format($amt->amount,2);
    })
    ->editColumn('balance_before',function($bb){
        return number_format($bb->balance_before,2);
    })
    ->editColumn('balance_after',function($ba){
        return number_format($ba->balance_after,2);
    })
    ->editColumn('ref_reference',function($rref){
        return formatReference($rref->ref_reference);
    })
    ->editColumn('ref_balance_before',function($rbb){
        return number_format($rbb->ref_balance_before,2);
    })
    ->editColumn('ref_balance_after',function($rba){
        return number_format($rba->ref_balance_after,2);
    })
    ->make(true); 
       
    }

    public function getAllocations(Request $request)
    {
        $ref = $request->get('ref');
        $query = "SELECT * FROM Acdetallonew WHERE allocation_no in (SELECT allocation_no FROM Acdetallonew WHERE doc_type = 'REC' AND reference = ? )";
        $accdetallonew = DB::select($query, [$ref]);
       
        return Datatables::of($accdetallonew)
            ->editColumn('reference',function($ref){
                return formatReference($ref->reference);
            })
            ->editColumn('amount',function($amt){
                return number_format($amt->amount,2);
            })
            ->make(true); 
       
    }

    public function clhmnalloData(Request $request){
        $reference = $request->get('reference');
        $doc_type = $request->get('doc_type');
        $clhmnallo = Clhmnallo::where('doc_type',$doc_type)
                                ->where('reference',$reference)
                                ->get();

        return Datatables::of($clhmnallo)
        ->editColumn('reference',function($ref){
            return formatReference($ref->reference);
        })
        ->editColumn('claim_no',function($clm){
            return formatPolicyOrClaim($clm->claim_no);
        })
        ->editColumn('credit_account',function($cr){
            if(!empty($cr->credit_account)){
                return formatGlhead($cr->credit_account);
            }
            else{
                return $cr->credit_account;
            }
        })
        ->editColumn('debit_account',function($dr){
            if(!empty($dr->debit_account)){
                return formatGlhead($dr->debit_account);
            }
            else{
                return $cr->debit_account;
            }
        })
        ->editColumn('amount',function($amt){
            return number_format($amt->amount,2);
        })
        ->make(true);
    }
}
