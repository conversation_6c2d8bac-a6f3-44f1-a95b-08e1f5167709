<?php

namespace App\Http\Controllers\gl\po;

use Illuminate\Support\Facades\Gate;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\Datatables\Datatables;

use View;
use Session;
use Auth;
use DB;
use DateTime;
use Carbon\Carbon;
use Response;
use App\Currency;
use App\PoReqHeader;
use App\Nlparams;
use App\Apvendors;
use App\ApTransTypes;
use App\Nlctrl;
use App\Aimsuser_web;
use Illuminate\Support\Facades\Validator;
use App\Im_Inventoryitems;
use App\Apunits_of_measure;
use App\PO_reqdetails;
use App\Im_Additionalcosts;
use App\PO_reqadditional_costs;
use App\poReqTax;
use App\PO_reqhead_costs;
use App\PO_reqheadtaxes;
use App\PO_reqitem_addcosts_taxes;
use App\Aims_process;
use App\Process_approval_dtl;
use App\Approval_level;
use App\Approval_matrix;
use App\Approver_matrix;
use App\User;
use App\Approvals;
use App\Approval_flow;
use App\Classes\Approvals\ApprovalsMgt;
use App\Classes\Approvals\ApprovalsPo;
use App\POheader;
use App\POdetails;
use App\POtaxes;
use App\POheader_taxes;
use App\POhead_additionalcosts;
use App\POitem_additionalcosts;
use App\POitem_taxes_on_costs;
use App\Gltaxgroups;
use App\Gltaxgroupdt;
use App\Gltaxtypes;
use App\Cbseqno;
use App\Doctype;
use App\PORec_header;
use App\PORec_details;
use App\POReturns_header;
use App\POReturns_dtl;
use App\POInvoice_header;
use App\POInvoice_dtls;
use App\POInvoice_taxes;
use App\FAHeader;

class POController extends Controller
{
    //!---------------------------PO REQUISITION HEADER---------------------------------------------
    public function poReqHeader(){
        $dat = array(
			'main' => 'PO', 
            'module' => 'Requisitions',
		);

        return view::make('gl.po.po_req_header', compact('dat'));

    }

    public function getVendorids(Request $request)
	{
		$vendors = Apvendors::all();
		return $vendors;
    }

    public function getAdditionalCostId(Request $request)
	{
		$additional_costs = Po_Additionalcosts::all();
		return $additional_costs;
    }

    public function  getVendordetails(Request $request)
    {
		$req_date = $request->req_date;

        $vendor_id = trim($request->vendor_id);

        $vendor = Apvendors::where('vendor_id', $vendor_id)->first();

        $control_acc_code = trim($vendor->control_account);
        $control_account = Nlparams::where('prid', 'GLH')
                                       ->where('prsno', $control_acc_code)->first();
        $control_accounts = Nlparams::where('prid', 'GLH')->whereNotIn('prsno', [$control_acc_code])
                           ->where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

        $default_currency_code = $vendor->base_currency;
        $default_currency =  Currency::where('currency_code', $default_currency_code)->first();
		$currencies = Currency::whereNotIn('currency_code', [ $default_currency_code])->get();

        $tax_grp = $vendor->tax_group;
        $old_tax_group = DB::table('gltaxgroups')->whereRaw("trim(group_code) = '" . $tax_grp . "' ")->first();

		$tax_groups = DB::table('gltaxgroups')->whereNotIn('group_code', [ $tax_grp])->get();
		

        if($default_currency->base_currency == "Y")
        {
            $currency_rate = 1;
        }

        if($default_currency->base_currency == "N")
        {
            $curr_rate = DB::table('currrate')->where('currency_code', $default_currency_code)
												-> where('rate_date', $req_date)->first();
			if($curr_rate)
			{
				$currency_rate = $curr_rate->currency_rate;
			}

			else
			{
				//return response()->json(['errors'=>$validator->errors()]);
				$err = "No rate";
			}
		}

        return ['control_accounts' =>  $control_accounts, 
                'tax_groups' => $tax_groups,
                 'old_tax_group' => $old_tax_group ,
                 'err' => $err, 
                 'currency_rate' => $currency_rate, 
                 'currencies' => $currencies, 
                 'default_currency' => $default_currency,
                  'control_account' => $control_account
                ];
    }

    public function poReqHeaderDataTb(Request $request)
    {
        $req_headers = PoReqHeader::all();

        return Datatables::Of($req_headers)
            ->addColumn('action', function ($req_headers) {
                return '<a class="btn btn-xs" id="edit_req_header_btn"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('req_date', function ($req_headers){
                $date = new DateTime($req_headers->req_date);
                $format_date =  $date->format('d/m/Y');
                return $format_date;
            })
            ->editColumn('stock_fa_flag', function ($req_headers){
              if (trim($req_headers->stock_fa_flag) == trim('FA')) {
                  $descr = 'Fixed Asset';
                  return $descr;
              }

              if (trim($req_headers->stock_fa_flag) == trim('STK')) {
                $descr = 'Stock';

                return $descr;
            }
            
            })

            ->make(true);
    }

    public function addReqHeader(Request $request)
    {
        // dd($request->all());
        $this -> validate($request, [
            'req_date' => 'required',
            'req_type' => 'required',
            'description' => 'required',
            'vendor_id' => 'required',
            'tax_group' => 'required',
            'currency_code' => 'required',
            'currency_rate' => 'required',
            'apply_discount' => 'required',
            'additional_cost' => 'required'
        ]);

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        $today = Carbon::today();
        $year = $today->format('Y');

        try 
        {
            $doctype = 'REQ';
            $req_serial = $this->genReqNo();
            $req_no = $doctype.STR_PAD($req_serial, 6, '0', STR_PAD_LEFT) . $year;
            
            $vendor_id = $request->vendor_id;

            //!create record
            $new_req = new PoReqHeader;
            $new_req->req_no = $req_no;
            $new_req->req_date = $request->req_date;
            $new_req->stock_fa_flag = $request->req_type;
            $new_req->description = $request->description;
            $new_req->vendor_id = $request->vendor_id;
            $new_req->tax_group = $request->tax_group;
            $new_req->currency_code = $request->currency_code;
            $new_req->currency_rate = $request->currency_rate;
            $new_req->control_account = $request->control_account;
            $new_req->cancelled = 'N';
            $new_req->status = 'P';
            $new_req->process_code = '1';
            $new_req->no_of_items = 0;
            $new_req->created_by = auth()->id();
            $new_req->created_at = Carbon::now();
            $new_req->discount_flag =  $request->apply_discount;
            $new_req->status =  '001';
            $new_req->save();

            $additional_cost_flag = 'N';
            $percentage_dicount_flag = 'N';
            $dicount_level = 'I';

            $discount = 0;

            #discount
            if($request->apply_discount == 'Y')
            {
                if($request->discount == 'A' && $request->disc_amt != null)
                {
                    $discount = str_replace(',', '', $request->disc_amt);
                    $dicount_level = 'R';
                }
    
                else if($request->discount == 'P' && $request->percentage_disc != null)
                {
                    $percentage_disc = floatval($request->percentage_disc);
                    $percentage_dicount_flag = 'Y';
                    $dicount_level = 'R';
                }
            }

            ##additional costs
            if($request->additional_cost == 'Y')
            {
                if(!empty($request->cost_code && $request->cost))
                {
                    for($i = 0; $i < count($request->cost_code); $i++) 
                    { 
                        $cost_type = Im_Additionalcosts::where('cost_code', $request->cost_code[$i])->get()[0];
                      
                        if($cost_type->taxable_flag == 'Y' && $cost_type->tax_group != null)
                        {
                            $tax_group_code = $cost_type->tax_group;
                
                            $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
                        
                            foreach ($tax_types as $type) 
                            {
                                $tax_type = $type->tax_type;

                                $tax_code = $type->tax_code;
                                
                                $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                                
                                $rate = $gltax->tax_rate;

                                $taxable_amnt = str_replace(',', '', $request->cost[$i]);
                                //trans type
                                $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
                
                                if($transtype)
                                {
                                    $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                                
                                    $add_deduct = $add_deduct_check->add_deduct;
                        
                                    if($add_deduct == 'A')
                                    {
                                        $tax_amount1 = ($taxable_amnt * $rate)/100;
                                    }
                
                                    if($add_deduct == 'D')
                                    {
                                        $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                                    }
                
                                    $save_tax = DB::table('po_reqheadtaxes')->insert([
                                        'vendor_id' => $vendor_id,
                                        'req_no' => $req_no,
                                        'tax_rate' => $rate,
                                        'taxable_amt' => $taxable_amnt,
                                        'add_deduct' => $add_deduct,
                                        'tax_code' => $tax_code,
                                        'tax_amt' => $tax_amount1,
                                        'created_by' => trim(auth()->id()),
                                        'created_at' => Carbon::now(),
                                        'cost_code' => $request->cost_code[$i]
                                    ]);
                                }
                            }
                        }

                        $new_cost = new PO_reqhead_costs;
                        $new_cost->req_no = $req_no;
                        $new_cost->cost_code = $request->cost_code[$i];
                        $new_cost->cost = str_replace(',', '', $request->cost[$i]);
                        $new_cost->created_by = trim(auth()->id());
                        $new_cost->created_at = Carbon::now();
                        $new_cost->save();
                    }

                    $additional_cost_flag = 'Y';
                }
            }

            $items_discount = PO_reqdetails::where('req_no', $req_no)->sum('discount_amt');

            $total_req_discount = $items_discount +  $discount;
            
            ##upd reqhead
            $total_tax_req = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req1 = PO_reqheadtaxes::where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req2 = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->sum('tax_amt');

            $taxes_on_add_costs = $total_tax_req1 + $total_tax_req2;
            //dd($total_req_discount,$total_tax_req2, $total_tax_req1, $taxes_on_add_costs);

            $total_req_taxes = $total_tax_req + $total_tax_req1 + $total_tax_req2;

            $additional_costs_req = PO_reqadditional_costs::where('req_no', $req_no)->sum('cost');
            $additional_costs_req1 = PO_reqhead_costs::where('req_no', $req_no)->sum('cost');

            $total_req_add_costs = $additional_costs_req + $additional_costs_req1;

            $req_net = PO_reqdetails::where('req_no', $req_no)->sum('net_amt');
            $total_req_net = $req_net + $total_tax_req1 + $additional_costs_req1;
            //dd($total_req_add_costs,$total_req_taxes,$total_req_net, $total_req_discount, $percentage_disc, $undistributed ,$req_amt);
            $upd_req = DB::table('po_req_header')->where('req_no', $req_no)
            ->update([
                'additional_cost' => $total_req_add_costs,
                'total_tax' => $total_req_taxes,
                'net_amt' => $total_req_net,
                'additional_costs_flag' => $additional_cost_flag,
                'additional_cost_tax' => $taxes_on_add_costs,
                'discount_amt' => $discount,
                'percentage_discount' => floatval($percentage_disc),
                'tax_on_items' => $total_tax_req,
                'percentage_discount_flag'=>$percentage_dicount_flag,
                'discount_level' => $dicount_level,
                'TOTAL_DISCOUNT' => $total_req_discount
            ]);

            $this->updDoctypeReqSerial();
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'New Requisition Added Successfully');
        } 

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Error occurred, try again');
        }
    }

    public function updateReqHeader(Request $request)
    {
        $req_no = trim($request->req_no);
        $vendor_id = trim($request->vendor_id);

        $this -> validate($request, [
            'req_date' => 'required',
            'description' => 'required',
            'vendor_id' => 'required',
            'tax_group' => 'required',
            'currency_code' => 'required',
            'currency_rate' => 'required',
        ]);

        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $updhead = PoReqHeader::where('vendor_id', $vendor_id)->where('req_no', $req_no)
            ->update([
                'currency_code' => $request->currency_code,
                'control_account' => trim($request->control_account),
                'tax_group' => trim($request->tax_group),
                'currency_rate' => $request->currency_rate,
                'req_date' => $request->req_date,
                'description' => $request->description,
                'updated_by' => trim(auth()->id()),
                'dola' => Carbon::now(),
            ]);  

            $percentage_dicount_flag = 'N';
            $discount = 0;
            $discount_flag = 'N';

            #discount
            if($request->edit_apply_discount == 'Y')
            {
                if($request->edit_discount == 'A' && $request->disc_amt != null)
                {
                    $discount = str_replace(',', '', $request->disc_amt);
                    $dicount_level = 'R';
                    $discount_flag = 'Y';

                    ##update req items
                    foreach ($items as $item) {
                        $upd_item = Po_reqdetails::where('req_no', $req_no)->where('item_no', $item->item_no)
                        ->update([
                            'discount_amt' =>  0,
                            'percentage_discount_flag' => 'N',
                            'percentage_discount' => null
                        ]);
                    }
                }
    
                else if($request->edit_discount == 'P' && $request->percentage_disc != null)
                {
                    $percentage_disc = floatval($request->percentage_disc);
                    $percentage_dicount_flag = 'Y';
                    $dicount_level = 'R';

                    ##update req items
                    $items = Po_reqdetails::where('req_no', $req_no)->get();

                    foreach ($items as $item) {
                        $discount = ($percentage_disc * $item->total_cost) / 100; 

                        $upd_item = Po_reqdetails::where('req_no', $req_no)->where('item_no', $item->item_no)
                        ->update([
                            'discount_amt' =>  $discount,
                            'percentage_discount_flag' => 'N',
                            'percentage_discount' => null
                        ]);
                    }
                }
            }

            ##upd reqhead
            $items_discount = PO_reqdetails::where('req_no', $req_no)->sum('discount_amt');
            $req_net = PO_reqdetails::where('req_no', $req_no)->sum('net_amt');

            $total_tax_req = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req1 = PO_reqheadtaxes::where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req2 = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->sum('tax_amt');

            $taxes_on_add_costs = $total_tax_req1 + $total_tax_req2;

            $total_req_taxes = $total_tax_req + $total_tax_req1 + $total_tax_req2;

            $additional_costs_req = PO_reqadditional_costs::where('req_no', $req_no)->sum('cost');
            $additional_costs_req1 = PO_reqhead_costs::where('req_no', $req_no)->sum('cost');

            $total_req_add_costs = $additional_costs_req + $additional_costs_req1;

            $req_amt = $req_net + $total_tax_req1 + $additional_costs_req1;

            if($req->discount_flag == 'Y' && $req->percentage_discount_flag == 'Y')
            {
                $total_req_discount = $items_discount;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
            }

            else if($req->discount_flag == 'Y' && ($req->percentage_discount_flag == 'N' || $req->percentage_discount_flag == null))
            {
                $total_req_discount = $req->discount_amt;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
            }

            else
            {
                $total_req_discount = $items_discount;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1);
            }
            //dd($total_req_add_costs,$total_req_taxes,$total_req_net, $total_req_discount, $percentage_disc, $undistributed,$req_amt);
            $upd_req = PoReqHeader::where('req_no', $req_no)
            ->update([
                'additional_cost' => $total_req_add_costs,
                'total_tax' => $total_req_taxes,
                'net_amt' => $total_req_net,
                'additional_cost_tax' => $taxes_on_add_costs,
                'percentage_discount' => floatval($percentage_disc),
                'tax_on_items' => $total_tax_req,
                'percentage_discount_flag'=>$percentage_dicount_flag,
                'discount_level' => $dicount_level,
                'TOTAL_DISCOUNT' => $total_req_discount
            ]);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Requisition Updated Successfully');
        } 

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Error occurred, try again');
        }
    }

    public function fetchAllReqHeaderDropDwnAndView(Request $request, PoReqHeader $req_header){
        // dd($req_header);
        $req_no = trim($request->edit_req_no);
        $vendor_id = trim($request->edit_vendor_id);
        $grp_code = trim($request->edit_tax_group);
        $currency_code = trim($request->edit_currency_code);
        $control_account = trim($request->edit_control_account);
      

        if ($req_no && $vendor_id) {

            $selected_control_account = Nlparams::where('prid', 'GLH')
                                           ->where('prsno', $control_account)->first();
            $other_control_accounts = Nlparams::where('prid', 'GLH')->whereNotIn('prsno', [$control_account]) 
                                        ->where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

            $selected_currency = Currency::where('currency_code', $currency_code)->first();
            $other_currencies = Currency::whereNotIn('currency_code', [$currency_code])->get();

            $selected_tax_group = DB::table('gltaxgroups')->whereRaw("trim(group_code) = '" . $grp_code. "' ")->first();
            $other_tax_groups = DB::table('gltaxgroups')->whereNotIn('group_code', [$grp_code])->get();
            
            return[
                'selected_control_account' => $selected_control_account,
                'other_control_accounts' => $other_control_accounts,
                'selected_currency' => $selected_currency,
                'other_currencies' => $other_currencies,
                'selected_tax_group' => $selected_tax_group,
                'other_tax_groups' => $other_tax_groups,
            ];
        }

        // $view_req_no = trim($request->view_req_no);
        // $view_vendor_id = trim($request->view_vendor_id);
        // // dd( $view_req_no, $view_vendor_id);

        // $dat = array(
		// 	'main' => 'PO', 
        //     'module' => 'Requisition Header',
        //     'submodule' => 'Requisition Header Details',
		// );
		
        
        // $control_accounts = Nlparams::where('prid', 'GLH') 
        //            ->where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

        // // $req_header = PoReqHeader::where('req_no',  trim($view_req_no))->first();
        //                            //->whereRaw("trim(vendor_id) = '" . $view_vendor_id . "' ")->first();
        // $tax_group = DB::table('gltaxgroups')->whereRaw("trim(group_code) = '" . $req_header->tax_group . "' ")->first();

        // $currency =  Currency::where('currency_code', $req_header->currency_code)->first();
		
		// $req_date1 = new DateTime($req_header->req_date);
		// $req_date =  $req_date1->format('d/m/Y');

        // $cost_arr = PO_reqhead_costs::where('req_no', $view_req_no)->pluck('cost_code')->toArray();
        
        // $all_costs = Im_Additionalcosts::all();

        // $costs = [];
        
        // foreach ($all_costs as $cost) 
        // {
        //     if(!in_array($cost->cost_code, $cost_arr))
        //     {
        //         array_push( $costs, $cost);
        //     }
        // }

        // $additional_costs = collect($costs);
        // //!approval 
        // $user_id = trim(Auth::user()->user_id);
        // $process = Aims_process::where('process_code',trim($req_header->process_code))->first();
        // $process_code= trim($req_header->process_code);
  
        // $process = Aims_process::with(['process_dtl',
		// 		'approval_levels'
		// 	])
		// 	->where('process_code',trim($process_code))
        //     ->first();
            
		// // fetch approvals if any
		// $approval_dtl = Approvals::with('approval_flow')
        //                             ->where('req_no',$view_req_no)
        //                             ->orderBy('date_created','DESC')
        //                             ->first();
        // $check_approval = Approvals::where('req_no',$view_req_no)
        //                             ->orderBy('date_created','DESC')
        //                             ->first();

        // if(isset($check_approval)){
        //     // $status = 'N';
        //     if($check_approval->status == 'A'){
        //         $status = 'A';
                
        //     }
        //     elseif($check_approval->status == 'R'){
        //         $status = 'R';
        //         $msg = 'Requisition Approval was rejected';
        //     }
        //     elseif($check_approval->status == 'P'){
        //         $status = 'P';
        //         $msg = 'Requisition has a Pending approval';
        //     }
        // }

        // $approval_status = $status ;
        // $approval_msg = $msg;
        // // dd($approval_status,$approval_msg);


        // return view::make('gl.po.po_reqheader_details', compact('approval_status','approval_msg','approval_dtl','cnt_appr_check','cnt_check_approval','cnt_user_declined','cnt_approvers','user_approvals', 'count_user_approvals','show_approve_btn','approvers','nxt_hierachy','process','control_accounts','tax_group', 'req_date', 'dat', 'req_header', 'vendor','currency', 'additional_costs'));
    }
    

    //!---------------------------/.PO REQUISITION HEADER---------------------------------------------

    public function req_details(Request $request, PoReqHeader $req_header)
    {
        $dat = array(
			'main' => 'PO Requisitions', 
            'module' => 'Requisition Details',
		);
        $view_req_no = trim($request->view_req_no);
        $view_vendor_id = trim($request->view_vendor_id);
        // dd( $view_req_no, $view_vendor_id);

        $dat = array(
			'main' => 'PO', 
            'module' => 'Requisition Header',
            'submodule' => 'Requisition Header Details',
		);
		
        
        $control_accounts = Nlparams::where('prid', 'GLH') 
                   ->where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

        // $req_header = PoReqHeader::where('req_no',  trim($view_req_no))->first();
                                   //->whereRaw("trim(vendor_id) = '" . $view_vendor_id . "' ")->first();
        $tax_group = DB::table('gltaxgroups')->whereRaw("trim(group_code) = '" . $req_header->tax_group . "' ")->first();

        $currency =  Currency::where('currency_code', $req_header->currency_code)->first();
		
		$req_date1 = new DateTime($req_header->req_date);
		$req_date =  $req_date1->format('d/m/Y');

        $cost_arr = PO_reqhead_costs::where('req_no', $view_req_no)->pluck('cost_code')->toArray();
        
        $all_costs = Im_Additionalcosts::all();

        $costs = [];
        
        foreach ($all_costs as $cost) 
        {
            if(!in_array($cost->cost_code, $cost_arr))
            {
                array_push( $costs, $cost);
            }
        }

        $additional_costs = collect($costs);
        //!approval 
        $user_id = trim(Auth::user()->user_id);
        $process = Aims_process::where('process_code',trim($req_header->process_code))->first();
        $process_code= trim($req_header->process_code);
        $view_req_no = $req_header->req_no;
  
        $process = Aims_process::with(['process_dtl',
				'approval_levels'
			])
			->where('process_code',trim($process_code))
            ->first();
            
		// fetch approvals if any
		$approval_dtl = Approvals::with('approval_flow')
                                    ->where('req_no',$view_req_no)
                                    ->orderBy('date_created','DESC')
                                    ->first();
        $check_approval = Approvals::where('req_no',$view_req_no)
                                    ->orderBy('date_created','DESC')
                                    ->first();

        if(isset($check_approval)){
            // $status = 'N';
            if($check_approval->status == 'A'){
                $status = 'A';
                
            }
            elseif($check_approval->status == 'R'){
                $status = 'R';
                $msg = 'Requisition Approval was rejected';
            }
            elseif($check_approval->status == 'P'){
                $status = 'P';
                $msg = 'Requisition has a Pending approval';
            }
        }

        $approval_status = $status ;
        $approval_msg = $msg;


        return view::make('gl.po.po_reqheader_details', compact('approval_status','approval_msg','approval_dtl','cnt_appr_check','cnt_check_approval','cnt_user_declined','cnt_approvers','user_approvals', 'count_user_approvals','show_approve_btn','approvers','nxt_hierachy','process','control_accounts','tax_group', 'req_date', 'dat', 'req_header', 'vendor','currency', 'additional_costs'));
		
        // return view::make('gl.po.po_reqheader_details', compact('dat'));
    }
    public function item_details(Request $request)
    {
        $dat = array(
			'main' => 'PO', 
            'module' => 'Requsition Details',
            'submodule' => 'Item Details',
		);

		$req_no = trim($request->req_no);
		$vendor_id = trim($request->vendor_id);
        $item_no = trim($request->item_no);
        $item_code = trim($request->item_code);
        $req = PoReqHeader::where('req_no', $req_no)->get()[0];
        
        $item = PO_reqdetails::where('item_no', $item_no)->where('req_no', $req_no)
        ->where('item_code', $item_code)->first();

        $account = Nlparams::where('prid', 'GLH')->where('prsno', $item->control_account)->first();
		
        $unit = DB::table('unit_of_measure')->where('unit_code', trim($item->unit_of_measure))->first();

        $cost_arr = PO_reqadditional_costs::where('item_no', $item_no)->where('req_no', $req_no)
        ->where('item_code', $item_code)->pluck('cost_code')->toArray();
        
        $all_costs = Im_Additionalcosts::all();

        $costs = [];
        
        foreach ($all_costs as $cost) 
        {
            if(!in_array($cost->cost_code, $cost_arr))
            {
                array_push( $costs, $cost);
            }
        }

        $additional_costs = collect($costs);
        
        $tax_types = DB::table('gltaxtypes')->where('transtype', 'INV')->get();
		
        return view::make('gl.po.po_reqitem_details', compact('account', 'tax_types', 'item', 'dat', 'unit', 'additional_costs', 'req'));
    }

    public function getGlheads(Request $request)
    {
        return Nlparams::where('prid', 'GLH')-> where('bank_flag', null)->orWhere('bank_flag', 'N')->get();
    }

    public function getUnits_of_measure(Request $request)
    {
        return Apunits_of_measure::all();
    }

    public function getItem_categories(Request $request)
    {
        return DB::table('im_item_categories')->get();
    }

    public function storeItem(Request $request)
    {
        // dd($request->all());
        $this->validate($request, [
            'control_account' => 'required',
            // 'vendor_id'=> 'required',
            // 'req_no'=> 'required',
            'item_cost'=> 'required',
            'total_cost'=> 'required',
            'item_code'=> 'required',
            'quantity'=> 'required',
            'unit_of_measure'=> 'required',
            'taxable'=> 'required',
            // 'description2' => 'required',
            'apply_discount' =>'required',
            'additional_cost' =>'required'
        ]);

        $req_no =  trim($request->req_no);
        $count =  PO_reqdetails::where('req_no', $req_no)->get();
        $item_no = count($count) + 1;

        if(trim($request->req_type) == 'STK'){

            $item_code = trim($request->item_code); 
            $im_item  = Im_Inventoryitems::where('item_code', $item_code)->get()[0];
            $description = $im_item->description;
            $category = $im_item->category_code;

        }

        if(trim($request->req_type) == 'FA'){

            $item_code = trim($request->item_code);
            $im_item  = FAHeader::where('asset_code', $item_code)->get()[0] ;
            $description = $im_item->asset_name;
            $category = $im_item->category;

        }

        $req = PoReqHeader::where('req_no', $req_no)->get()[0];
        $vendor_id = $req->vendor_id;
        $total_cost = str_replace(',', '', $request->item_cost) * $request->quantity;
        $taxable_amount = str_replace(',', '', $request->taxable_amt);

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $store = PO_reqdetails::create([
                'vendor_id' => $vendor_id,
                'item_no' => $item_no,
                'req_no' =>  $req_no,
                'item_cost' => str_replace(',', '', $request->item_cost),
                'description' => $description,
                'total_cost' => $total_cost,
                'quantity' => $request->quantity,
                'unit_of_measure' => $request->unit_of_measure,
                'taxable' => $request->taxable,
                // 'taxable_amt' => $total_cost,
                'created_by' => trim(auth()->id()),
                'created_at' => Carbon::now(),
                'control_account' => $request->control_account,
                'item_code'=> $item_code,
                'category_code'=> $category,
                'description2' => $request->description2,
                'discount_on_additional_costs' => $request->discount_on_additional_cost
            ]);

            if($request->additional_cost == 'Y' && ($req->additional_costs_flag == 'N' || $req->additional_costs_flag == null))
            {
                if(!empty($request->cost_code && $request->cost))
                {
                    for($i = 0; $i < count($request->cost_code); $i++) 
                    { 
                        ###taxes on additional costs at item level
                        $cost_type = Im_Additionalcosts::where('cost_code', $request->cost_code[$i])->get()[0];
                        
                        if($cost_type->taxable_flag == 'Y')
                        {
                            $tax_group_code = $cost_type->tax_group;
                            
                            $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
                        
                            foreach ($tax_types as $type) 
                            {
                                $tax_type = $type->tax_type;

                                $tax_code = $type->tax_code;
                                
                                $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                                
                                $rate = $gltax->tax_rate;

                                $taxable_amnt = str_replace(',', '', $request->cost[$i]);
                              
                                //trans type
                                $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
                
                                if($transtype)
                                {
                                    $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                                
                                    $add_deduct = $add_deduct_check->add_deduct;
                        
                                    if($add_deduct == 'A')
                                    {
                                        $tax_amount1 = ($taxable_amnt * $rate)/100;
                                    }
                
                                    if($add_deduct == 'D')
                                    {
                                        $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                                    }
                
                                    $save_tax = PO_reqitem_addcosts_taxes::create([
                                        'vendor_id' => $vendor_id,
                                        'cost_code' => $request->cost_code[$i],
                                        'item_no' => $item_no,
                                        'req_no' => $req_no,
                                        'tax_rate' => $rate,
                                        'taxable_amt' => $taxable_amnt,
                                        'add_deduct' => $add_deduct,
                                        'tax_code' => $tax_code,
                                        'tax_amt' => $tax_amount1,
                                        'created_by' => trim(auth()->id()),
                                        'created_at' => Carbon::now(),
                                        'item_code' => $item_code
                                    ]);
                                }
                            }
                        }
                        $new_cost = new PO_reqadditional_costs;
                        $new_cost->req_no = $req_no;
                        $new_cost->item_no = $item_no;
                        $new_cost->item_code = trim($request->item_code);
                        $new_cost->cost_code = $request->cost_code[$i];
                        $new_cost->cost = str_replace(',', '', $request->cost[$i]);
                        $new_cost->created_by = trim(auth()->id());
                        $new_cost->created_at = Carbon::now();
                        $new_cost->save();
                    }
                }
            }

            $percentage_disc = null;
            $discount = 0;
            $percentage_discount_flag = 'N';
            $discount_flag = 'N';

            ##start discounts
            if($req->discount_flag == 'N' || $req->discount_flag == null)
            {
                if($request->apply_discount == 'Y')
                {
                    if($request->discount == 'A' && $request->disc_amt != null)
                    {
                        $discount = str_replace(',', '', $request->disc_amt);
                        
                        $taxable_amnt = $total_cost - $discount; 

                        $percentage_discount_flag = 'N';

                        $discount_flag = 'Y';
                    }
        
                    else if($request->discount == 'P' && $request->percentage_disc != null)
                    {
                        $percentage_disc = floatval($request->percentage_disc);
        
                        $discount = ($percentage_disc * $total_cost) / 100; 

                        $taxable_amnt = $total_cost - $discount; 

                        $percentage_discount_flag = 'Y';

                        $discount_flag = 'Y';
                        //dd( $taxable_amnt);
                    }
                }
            }

            else if($req->discount_flag == 'Y' && $req->percentage_discount_flag == 'Y')
            {
                $discount = ($req->percentage_discount * str_replace(',', '', $request->taxable_amt)) / 100;
                $taxable_amnt = $taxable_amount; 
            }

            else if($req->discount_flag == 'Y' && ($req->percentage_discount_flag == 'N' || $req->percentage_discount_flag == null))
            {
                $discount = 0;
                $taxable_amnt = $taxable_amount;
            }

            else
            {
                $taxable_amnt = $taxable_amount;
            }
            ##end discounts

            if($request->taxable == 'Y')
            {
                $tax_group_code = $req->tax_group;
    
                $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
            
                foreach ($tax_types as $type) 
                {
                    $tax_type = $type->tax_type;

                    $tax_code = $type->tax_code;
                    
                    $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                    
                    $rate = $gltax->tax_rate;

                    //$taxable_amnt = $taxable_amount;
                  
                    //trans type
                    $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
    
                    if($transtype)
                    {
                        $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                    
                        $add_deduct = $add_deduct_check->add_deduct;
            
                        if($add_deduct == 'A')
                        {
                            $tax_amount1 = ($taxable_amnt * $rate)/100;
                        }
    
                        if($add_deduct == 'D')
                        {
                            $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                        }
    
                        $save_tax = poReqTax::create([
                            'vendor_id' => $vendor_id,
                            'item_code' => $item_code,
                            'item_no' => $item_no,
                            'req_no' => $req_no,
                            'tax_rate' => $rate,
                            'taxable_amt' => $taxable_amnt,
                            'add_deduct' => $add_deduct,
                            'tax_code' => $tax_code,
                            'tax_amt' => $tax_amount1,
                            'created_by' => trim(auth()->id()),
                            'created_at' => Carbon::now(),
                        ]);
                    }
                }
            }

            ##upd item
            $i_taxes = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->where('item_no', $item_no)->sum('tax_amt');
            $i_tax_on_cost = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->where('item_no', $item_no)->sum('tax_amt');
            $i_total_tax =  $i_taxes + $i_tax_on_cost;

            $i_add_costs = PO_reqadditional_costs::where('req_no', $req_no)->where('item_no', $item_no)->sum('cost');

            $item_net =  $i_total_tax  + $i_add_costs +  $taxable_amnt;

            //dd($taxable_amnt, $discount , $i_tax_on_cost,$i_total_tax, $i_add_costs, $taxable_amnt, $item_net );

            $upd_item = PO_reqdetails::where('req_no', $req_no)->where('item_no', $item_no)->where('item_code', $item_code)
            ->update([
                'additional_cost' => $i_add_costs,
                'total_tax' => $i_total_tax,
                'net_amt' => $item_net,
                'additional_cost_tax' => $i_tax_on_cost,
                'percentage_discount' =>  $percentage_disc,
                'taxable_amt' =>  $taxable_amnt,
                'discount_amt' =>  $discount,
                'tax_on_item' =>  $i_taxes,
                'percentage_discount_flag' => $percentage_discount_flag, 
                'discount_flag' => $discount_flag
            ]);
            ##end upd item

            ##upd reqhead start
            $items_discount = PO_reqdetails::where('req_no', $req_no)->sum('discount_amt');

            
            //$items_discount = PO_reqdetails::where('req_no', $req_no)->sum('discount_amt');
            $req_discount = $req->discount_amt;
            //$total_req_discount = $items_discount +  $req_discount;

            $total_tax_req = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req1 = PO_reqheadtaxes::where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req2 = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->sum('tax_amt');

            $taxes_on_add_costs = $total_tax_req1 + $total_tax_req2;
            $total_req_taxes = $total_tax_req + $total_tax_req1 + $total_tax_req2;
           
            $additional_costs_req = PO_reqadditional_costs::where('req_no', $req_no)->sum('cost');
            $additional_costs_req1 = PO_reqhead_costs::where('req_no', $req_no)->sum('cost');

            $total_req_add_costs = $additional_costs_req + $additional_costs_req1;

            $req_net = PO_reqdetails::where('req_no', $req_no)->sum('net_amt');

            if($req->discount_flag == 'Y' && $req->percentage_discount_flag == 'Y')
            {
                $total_req_discount = $items_discount;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
            }

            else if($req->discount_flag == 'Y' && ($req->percentage_discount_flag == 'N' || $req->percentage_discount_flag == null))
            {
                $total_req_discount = $req->discount_amt;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
            }

            else
            {
                $total_req_discount = $items_discount;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1);
            }

            $req_amt = $req_net + $total_tax_req1 + $additional_costs_req1;
            //dd($item_net,$req_net, $req_amt, $total_req_discount, $taxes_on_add_costs, $total_req_add_costs , $total_req_taxes,  $total_tax_req,$total_req_net);

            $items_count = PO_reqdetails::where('req_no', $req_no)->count();

            $upd_req = DB::table('po_req_header')->where('req_no', $req_no)
            ->update([
                'additional_cost' => $total_req_add_costs,
                'total_tax' => $total_req_taxes,
                'net_amt' => $total_req_net,
                'additional_cost_tax' => $taxes_on_add_costs,
                'total_discount' => $total_req_discount,
                'tax_on_items' => $total_tax_req,
                'no_of_items' => $items_count,
                'req_amt' => $req_amt
            ]);
            ##end

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully Added');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            //dd($e);
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function updReqitem(Request $request)
    {
        // dd($request->all());
        $this->validate($request, [
            'control_account' => 'required',
            'req_no'=> 'required',
            'item_cost'=> 'required',
            'total_cost'=> 'required',
            'item_code'=> 'required',
            'quantity'=> 'required',
            'unit_of_measure'=> 'required',
            'taxable'=> 'required',
            'description2' => 'required',
            //'edit_apply_discount' => 'required'
        ]);

        $req_no =  trim($request->req_no);
        $item_code = trim($request->item_code);
        $item_no = trim($request->item_no);
        $im_item = Im_Inventoryitems::where('item_code', $item_code)->get()[0];
        $req = PoReqHeader::where('req_no', $req_no)->get()[0];
        $vendor_id = $req->vendor_id;

        $item = PO_reqdetails::where('req_no', $req_no)->where('item_no', $item_no)->get()[0];
        // dd($item);
        $total_cost = str_replace(',', '', $request->item_cost) * $request->quantity;

        $taxable_amount = str_replace(',', '', $request->edit_taxable_amt);
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $upd = PO_reqdetails::where('req_no', $req_no)->where('item_no', $item_no)->where('item_code', $item_code)
            ->update([
                'item_cost' => str_replace(',', '', $request->item_cost),
                'description' => $im_item->description,
                'total_cost' => $total_cost,
                'quantity' => $request->quantity,
                'unit_of_measure' => $request->unit_of_measure,
                'taxable' => $request->taxable,
                'taxable_amt' => $total_cost,
                'updated_by' => trim(auth()->id()),
                'dola' => Carbon::now(),
                'control_account' => $request->control_account,
                'description2' => $request->description2,

            ]);

            if($request->taxable == 'Y')
            {
                ##delete taxes then save
                $del_item_tax = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->where('item_no', $item_no)
                ->where('item_code', $item_code)->delete();
                  
                $tax_group_code = $req->tax_group;
    
                $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
               
                foreach ($tax_types as $type) 
                {
                    $tax_type = $type->tax_type;

                    $tax_code = $type->tax_code;
                    
                    $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                    
                    $rate = $gltax->tax_rate;

                    $taxable_amnt = $taxable_amount;
                    //trans type
                    $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
    
                    if($transtype)
                    {
                        $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                    
                        $add_deduct = $add_deduct_check->add_deduct;
            
                        if($add_deduct == 'A')
                        {
                            $tax_amount1 = ($taxable_amnt * $rate)/100;
                        }
    
                        if($add_deduct == 'D')
                        {
                            $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                        }

                        $save_tax = DB::table('PO_REQ_TAXES')->insert([
                            'vendor_id' => $vendor_id,
                            'item_code' => $item_code,
                            'item_no' => $item_no,
                            'req_no' => $req_no,
                            'tax_rate' => $rate,
                            'taxable_amt' => $taxable_amnt,
                            'add_deduct' => $add_deduct,
                            'tax_code' => $tax_code,
                            'tax_amt' => $tax_amount1,
                            'created_by' => trim(auth()->id()),
                            'created_at' => Carbon::now(),
                        ]);
                    }
                }
            }

            else if($request->taxable == 'N')
            {
                ##delete taxes 
                $del_item_tax = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->where('item_no', $item_no)
                ->where('item_code', $item_code)->delete();
            }

            $discount_flag = $item->discount_flag;
            $percentage_discount_flag = $item->percentage_discount_flag;
            
            ##start discounts
            if($req->discount_flag == 'N' || $req->discount_flag == null)
            {
                if($request->edit_apply_discount == 'Y')
                {
                    if($request->edit_discount == 'A' && $request->disc_amt != null)
                    {
                        $discount = str_replace(',', '', $request->disc_amt);
                        
                        $taxable_amnt = $total_cost - $discount; 

                        $percentage_discount_flag = 'N';

                        $discount_flag = 'Y';
                    }
        
                    else if($request->edit_discount == 'P' && $request->percentage_disc != null)
                    {
                        $percentage_disc = floatval($request->percentage_disc);
        
                        $discount = ($percentage_disc * $total_cost) / 100; 

                        $taxable_amnt = $total_cost - $discount; 

                        $percentage_discount_flag = 'Y';

                        $discount_flag = 'Y';
                    }
                }
            }

            else
            {
                $taxable_amnt = $taxable_amount;
            }
           
            ##end discounts

            ##new upd
            ##upd item
            $i_taxes = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->where('item_no', $item_no)->sum('tax_amt');
            $i_tax_on_cost = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->where('item_no', $item_no)->sum('tax_amt');
            $i_total_tax =  $i_taxes + $i_tax_on_cost;

            $i_add_costs = PO_reqadditional_costs::where('req_no', $req_no)->where('item_no', $item_no)->sum('cost');

            $item_net =  $i_total_tax  + $i_add_costs +  $taxable_amnt;

            $upd_item = PO_reqdetails::where('req_no', $req_no)->where('item_no', $item_no)->where('item_code', $item_code)
            ->update([
                'additional_cost' => $i_add_costs,
                'total_tax' => $i_total_tax,
                'net_amt' => $item_net,
                'additional_cost_tax' => $i_tax_on_cost,
                'percentage_discount' =>  $percentage_disc,
                'taxable_amt' =>  $taxable_amnt,
                'discount_amt' =>  $discount,
                'tax_on_item' =>  $i_taxes,
                'percentage_discount_flag' => $percentage_discount_flag, 
                'discount_flag' => $discount_flag
            ]);
            ##end upd item

            ##upd reqhead start
            $items_discount = PO_reqdetails::where('req_no', $req_no)->sum('discount_amt');
            $req_discount = $req->discount_amt;
            $total_req_discount = $items_discount +  $req_discount;

            $total_tax_req = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req1 = PO_reqheadtaxes::where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req2 = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->sum('tax_amt');

            $taxes_on_add_costs = $total_tax_req1 + $total_tax_req2;
            $total_req_taxes = $total_tax_req + $total_tax_req1 + $total_tax_req2;
           
            $additional_costs_req = PO_reqadditional_costs::where('req_no', $req_no)->sum('cost');
            $additional_costs_req1 = PO_reqhead_costs::where('req_no', $req_no)->sum('cost');

            $total_req_add_costs = $additional_costs_req + $additional_costs_req1;

            $req_net = PO_reqdetails::where('req_no', $req_no)->sum('net_amt');

            $total_req_net = $req_net + $total_tax_req1 + $additional_costs_req1;

            $items_count = PO_reqdetails::where('req_no', $req_no)->count();

            $distributed = PO_reqdetails::where('req_no', $req_no)->sum('taxable_amt');
           
            $upd_req = DB::table('po_req_header')->where('req_no', $req_no)
            ->update([
                'additional_cost' => $total_req_add_costs,
                'total_tax' => $total_req_taxes,
                'net_amt' => $total_req_net,
                'additional_cost_tax' => $taxes_on_add_costs,
                'total_discount' => $total_req_discount,
                'undistributed_amt' => $req->amount - $distributed,
                'tax_on_items' => $total_tax_req,
                'distributed_amt' =>  $distributed
            ]);
            ##end
            ##end new upd

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully updated');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            //dd( $e);
            Session::Flash('error', 'Failed, try again');
        }
    }
   
    public function categItems(Request $request)
    {
        return Im_Inventoryitems::where('item_category', trim($request->category))->get();
    }

    public function itemProps(Request $request)
    {
        if($request->has('req_no'))
        {
            $details = PO_reqdetails::where('req_no', $request->req_no)->where('item_no', $request->item_no)
            ->where('item_code', $request->item_code)->get()[0];

            $im_item = Im_Inventoryitems::where('item_code', trim($request->item_code))->get()[0];

            $categ = DB::table('im_item_categories')->where('category_code', $im_item->item_category)->get()[0];

            $glhs = Nlparams::where('prid', 'GLH')-> where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

            $units = Apunits_of_measure::all();

            return ['units'=>$units, 'glhs'=>$glhs, 'details'=>$details, 'categ'=>$categ, 'im_item' => $im_item];
        }

        else if($request->inv_flag == 'Y')
        {
            
            $details = POdetails::where('po_number', $request->po)->where('item_code', $request->item)->get()[0];
            // $rec = PORec_header::where('rec_no', $request->rec_no)->where('po_number', $request->po)->get()[0];
            // $rec_item = PORec_details::where('po_number', $request->po)->where('vendor_id', $rec->vendor_id)
            // ->where('item_code', $request->item)->where('rec_no', $rec->rec_no)->get()[0];
           
            // $categ = DB::table('im_item_categories')->where('category_code', $details->category_code)->get()[0];
           
            $glhs = Nlparams::where('prid', 'GLH')-> where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

            $units = Apunits_of_measure::all();

            return ['units'=>$units, 'glhs'=>$glhs, 'details'=>$details];
        }

        else if($request->has('po_number'))
        {
            $details = POdetails::where('po_number', $request->po_number)->where('item_no', $request->item_no)->get()[0];

            $categ = DB::table('im_item_categories')->where('category_code', $details->category_code)->get()[0];
           
            $glhs = Nlparams::where('prid', 'GLH')-> where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

            $units = Apunits_of_measure::all();

            return ['units'=>$units, 'glhs'=>$glhs, 'details'=>$details, 'categ'=>$categ, 'im_item' => $im_item];
        }

        else
        {
            $details = Im_Inventoryitems::where('item_code', trim($request->item))->get()[0];

            $glhs = Nlparams::where('prid', 'GLH')-> where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

            $units = Apunits_of_measure::all();

            return ['units'=>$units, 'glhs'=>$glhs, 'details'=>$details];
        }
    }

    public function faItemsProps(Request $request)
    {
        if($request->has('req_no'))
        {
            $details = PO_reqdetails::where('req_no', $request->req_no)->where('item_no', $request->item_no)
            ->where('item_code', $request->item_code)->get()[0];

            $im_item = FAHeader::where('asset_code', trim($request->item_code))->get()[0];

            $categ = DB::table('im_item_categories')->where('category_code', $im_item->item_category)->get()[0];

            $glhs = Nlparams::where('prid', 'GLH')-> where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

            $units = Apunits_of_measure::all();

            return ['units'=>$units, 'glhs'=>$glhs, 'details'=>$details, 'categ'=>$categ, 'im_item' => $im_item];
        }

        else if($request->inv_flag == 'Y')
        {
            $details = POdetails::where('po_number', $request->po)->where('item_code', $request->item)->get()[0];

            $rec = PORec_header::where('rec_no', $request->rec_no)->where('po_number', $request->po)->get()[0];

            $rec_item = PORec_details::where('po_number', $request->po)->where('vendor_id', $rec->vendor_id)
            ->where('item_code', $request->item)->where('rec_no', $rec->rec_no)->get()[0];
           
            $categ = DB::table('im_item_categories')->where('category_code', $details->category_code)->get()[0];
           
            $glhs = Nlparams::where('prid', 'GLH')-> where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

            $units = Apunits_of_measure::all();

            return ['units'=>$units, 'glhs'=>$glhs, 'details'=>$details, 'categ'=>$categ, 'im_item' => $im_item, 'rec_item' =>$rec_item];
        }

        else if($request->has('po_number'))
        {
            $details = POdetails::where('po_number', $request->po_number)->where('item_no', $request->item_no)->get()[0];

            $categ = DB::table('im_item_categories')->where('category_code', $details->category_code)->get()[0];
           
            $glhs = Nlparams::where('prid', 'GLH')-> where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

            $units = Apunits_of_measure::all();

            return ['units'=>$units, 'glhs'=>$glhs, 'details'=>$details, 'categ'=>$categ, 'im_item' => $im_item];
        }

        else
        {
            $details = FAHeader::where('asset_code', trim($request->item))->get()[0];

            $glhs = Nlparams::where('prid', 'GLH')-> where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

            $units = Apunits_of_measure::all();

            return ['units'=>$units, 'glhs'=>$glhs, 'details'=>$details];
        }
    }

    public function reqItems_dat(Request $request)
    {
        $all_items = PO_reqdetails::where('req_no',  trim($request->req_no))->get();
       
        return Datatables::Of($all_items)
           ->addColumn('action', function ($item) {
                return '<a class="btn btn-xs" id="btn-view"><i class="glyphicon glyphicon-eye-open"></i></a><a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
			})

            ->addColumn('discount_applicable', function($item)
            {
                $req = PoReqHeader::where('req_no', $item->req_no)->get()[0];
                if($req->discount_flag == 'Y'  || $item->discount_flag == 'Y'){
                    return 'Yes';
                }

                else
                {
                    return 'No';
                }
            })

            ->editColumn('discount_level', function($item)
            {
                $req = PoReqHeader::where('req_no', $item->req_no)->get()[0];
               
                if($req->discount_level == 'R'  && $req->discount_flag == 'Y'){
                    return 'Requisition';
                }

                else if($item->discount_flag == 'Y' )
                {
                    return 'Item';
                }
            })
           ->make(true);
	}

    public function deleteReqitem(Request $request)
    {
        $req_no = trim($request->req_no);
        $item_no = trim($request->item_no);
        $item_code = trim($request->item_code);
        $req = PoReqHeader::where('req_no', $req_no)->get()[0];
        $item = PO_reqdetails::where('req_no', $req_no)->where('item_no', $item_no)->get()[0];
        $item_taxable = $item->taxable_amt;
        // dd($req_no,  $item_no, $item_code);
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            ##delete additional costs
            $delete_costs = PO_reqadditional_costs::where('req_no', $req_no)->where('item_no', $item_no)
            ->where('item_code', $item_code)->delete();

            #delete tax on additional costs
            $delete_tax_costs = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->where('item_no', $item_no)->delete();
            
            ##delete taxes costs
            $delete_taxes = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->where('item_no', $item_no)
            ->where('item_code', $item_code)->delete();

            $del_item = PO_reqdetails::where('req_no', $req_no)->where('item_no', $item_no)
            ->where('item_code', $item_code)->delete();

            ##reorder item numbers
            $items = PO_reqdetails::whereRaw("req_no = '".$req_no."' and  item_no >= '". $item_no."'")->get();
            
            foreach ($items as $key => $value) 
            {
                $items = PO_reqdetails::whereRaw("req_no = '".$req_no."' and  item_no = '". $value->item_no."'")
                ->update([
                    'item_no' => $value->item_no - 1
                ]);
                
                $upd_cost =  PO_reqadditional_costs::whereRaw("req_no = '".$req_no."' and  item_no = '". $value->item_no."'")
                ->update([
                    'item_no' => $value->item_no - 1
                ]);

                $upd_taxes =   DB::table('PO_REQ_TAXES')->whereRaw("req_no = '".$req_no."' and  item_no = '". $value->item_no."'")
                ->update([
                    'item_no' => $value->item_no - 1
                ]);

                $upd_taxes =   PO_reqitem_addcosts_taxes::whereRaw("req_no = '".$req_no."' and  item_no = '". $value->item_no."'")
                ->update([
                    'item_no' => $value->item_no - 1
                ]);
            }

            ##upd reqhead start
            $items_discount = PO_reqdetails::where('req_no', $req_no)->sum('discount_amt');
            $req_discount = $req->discount_amt;
            $total_req_discount = $items_discount +  $req_discount;

            $total_tax_req = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req1 = PO_reqheadtaxes::where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req2 = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->sum('tax_amt');

            $taxes_on_add_costs = $total_tax_req1 + $total_tax_req2;
            $total_req_taxes = $total_tax_req + $total_tax_req1 + $total_tax_req2;
           
            $additional_costs_req = PO_reqadditional_costs::where('req_no', $req_no)->sum('cost');
            $additional_costs_req1 = PO_reqhead_costs::where('req_no', $req_no)->sum('cost');

            $total_req_add_costs = $additional_costs_req + $additional_costs_req1;

            $req_net = PO_reqdetails::where('req_no', $req_no)->sum('net_amt');

            $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;

            $req_amt = $req_net + $total_tax_req1 + $additional_costs_req1;
            //dd($item_net,$req_net, $req_amt, $total_req_discount, $taxes_on_add_costs, $total_req_add_costs , $total_req_taxes,  $total_tax_req,$total_req_net);

            $items_count = PO_reqdetails::where('req_no', $req_no)->count();

            $upd_req = DB::table('po_req_header')->where('req_no', $req_no)
            ->update([
                'additional_cost' => $total_req_add_costs,
                'total_tax' => $total_req_taxes,
                'net_amt' => $total_req_net,
                'additional_cost_tax' => $taxes_on_add_costs,
                'total_discount' => $total_req_discount,
                'tax_on_items' => $total_tax_req,
                'no_of_items' => $items_count,
                'req_amt' => $req_amt
            ]);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully deleted');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function getAddititionalcosts(Request $request)
    {
        if($request->has('additional_cost') && $request->has('cost_code'))
        {
            $cost_arr = $request->cost_code;

            $all_costs =Im_Additionalcosts::all();

            $costs = [];
            
            foreach ($all_costs as $cost) 
            {
                if(!in_array($cost->cost_code, $cost_arr))
                {
                    array_push( $costs, $cost);
                }
            }     
            return $costs;
        }

        else
        {
            return Im_Additionalcosts::all();
        }
	}

    public function additional_cost_dat(Request $request)
    {
        $item_no = trim($request->item_no);
        $item_code = trim($request->item_code);

        $costs = PO_reqadditional_costs::where('req_no', trim($request->req_no))->whereRaw("trim(item_no) = '" . $item_no . "' ")
        ->whereRaw("trim(item_code) = '" . $item_code . "' ")->get();
       
        return Datatables::Of($costs)
           ->addColumn('action', function ($cost) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
			})
            ->addColumn('descr', function ($cost_code) {
                $cost = Im_Additionalcosts::where('cost_code', $cost_code->cost_code)->get()[0];
                return $cost->cost_name;
            })
           ->make(true);
	}

    public function delAdditional_cost(Request $request)
    {
        $req_no = trim($request->req_no);
        $cost_code = trim($request->cost_code);
        $item_no = trim($request->item_no);
        $item_code = trim($request->item_code);
        $item = PO_reqdetails::where('req_no', $req_no)->where('item_no', $item_no)->where('item_code', $item_code)->get()[0];
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            ##delete additional costs
            $delete_cost = PO_reqadditional_costs::where('req_no', $req_no)->where('item_no', $item_no)
            ->where('cost_code', $cost_code)->delete();

            ##delete taxes on additional cost
            $delete_tax_costs = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->where('item_no', $item_no)
            ->where('cost_code', $cost_code)->delete();

            $this->updReq($req_no,  $item_no);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully deleted');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function updAdditional_cost(Request $request)
    {
        $this->validate($request, [
            'cost_code' => 'required',
            'cost'=> 'required'
        ]);

        $req_no = trim($request->req_no);
        $cost_code = trim($request->cost_code);
        $item_no = trim($request->item_no);
        $item_code = trim($request->item_code);
        $item = PO_reqdetails::where('req_no', $req_no)->where('item_no', $item_no)->get()[0];
      
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        
        try 
        {
            $upd_cost = PO_reqadditional_costs::where('req_no', $req_no)->where('item_no', $item_no)->where('cost_code', $cost_code)
            ->update([
                'cost' => str_replace(',', '', $request->cost)
            ]);

            ##upd taxable amount and tax amnt 
            $taxes = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->where('cost_code', $cost_code)
            ->where('item_no', $item_no)->get();

            if($taxes->count() > 0)
            {
               foreach ($taxes as $tax) 
               {
                    if($tax->add_deduct == 'A')
                    {
                        $tax_amount = (str_replace(',', '', $request->cost) * $tax->tax_rate)/100;
                    }
        
                    else
                    {
                        $tax_amount = ((str_replace(',', '', $request->cost) * $tax->tax_rate)/100) * -1;
                    }

                    $upd_tax = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->where('cost_code', $cost_code)
                    ->update([
                        'taxable_amt' => str_replace(',', '', $request->cost),
                        'tax_amt' => $tax_amount
                    ]);
               }
            }

            $this->updReq($req_no,  $item_no);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully updated');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function storeAdditional_cost(Request $request)
    {
        $this->validate($request, [
            'cost_code' => 'required',
            'cost'=> 'required'
        ]);

        $req_no = trim($request->req_no);
        $cost_code = trim($request->cost_code);
        $item_no = trim($request->item_no);
        $item_code = trim($request->item_code);
        $item = PO_reqdetails::where('req_no', $req_no)->where('item_no', $item_no)->where('item_code', $item_code)->get()[0];
        $req = PoReqHeader::where('req_no', $req_no)->get()[0];
        $vendor_id =  $req->vendor_id;
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $new_cost = new PO_reqadditional_costs;
            $new_cost->req_no = $req_no;
            $new_cost->item_no = $item_no;
            $new_cost->item_code = trim($request->item_code);
            $new_cost->cost_code =  $cost_code;
            $new_cost->cost = str_replace(',', '', $request->cost);
            $new_cost->created_by = trim(auth()->id());
            $new_cost->created_at = Carbon::now();
            $new_cost->save();
           
            ###taxes on additional costs 
            $cost_type = Im_Additionalcosts::where('cost_code', $cost_code)->get()[0];
            
            if($cost_type->taxable_flag == 'Y')
            {
                $tax_group_code = $cost_type->tax_group;
                $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
            
                foreach ($tax_types as $type) 
                {
                    $tax_type = $type->tax_type;

                    $tax_code = $type->tax_code;
                    
                    $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                    
                    $rate = $gltax->tax_rate;

                    $taxable_amnt = str_replace(',', '', $request->cost);
                    
                    //trans type
                    $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
                    
                    if($transtype)
                    {
                        $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                    
                        $add_deduct = $add_deduct_check->add_deduct;
            
                        if($add_deduct == 'A')
                        {
                            $tax_amount1 = ($taxable_amnt * $rate)/100;
                        }
    
                        if($add_deduct == 'D')
                        {
                            $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                        }
                      
                        $save_tax = PO_reqitem_addcosts_taxes::create([
                            'vendor_id' => $vendor_id,
                            'cost_code' => $cost_code,
                            'item_no' => $item_no,
                            'req_no' => $req_no,
                            'tax_rate' => $rate,
                            'taxable_amt' => $taxable_amnt,
                            'add_deduct' => $add_deduct,
                            'tax_code' => $tax_code,
                            'tax_amt' => $tax_amount1,
                            'created_by' => trim(auth()->id()),
                            'created_at' => Carbon::now(),
                            'item_code' =>  $item_code 
                        ]);
                    }
                }
            }

            $this->updReq($req_no, $item_no);
         
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully added');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    //!----------------------------PO REQ TAXES-------------------------------------------------
    public function poReqTaxes(Request $request)
    {
        $req_no = trim($request->req_no);
        $item_code = trim($request->item_code);
        $item_no = trim($request->item_no);

        $all_tax_items = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)
                                    ->where('item_no', $item_no)->get();

        return Datatables::Of($all_tax_items)
           ->addColumn('action', function ($item) {
                return '<a class="btn btn-xs" id="btn-edit-tx"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove-tx"><i class="glyphicon glyphicon-trash"></i></a>';
			})
			
			-> editColumn('tax_amt', function ($item){
                $tax_amt = number_format($item->tax_amt, 2);
                return $tax_amt;
            })

           ->editColumn('taxable_amt', function ($item){
                $taxable_amount = number_format($item->taxable_amt, 2);
                return $taxable_amount;
            })

            ->addColumn('tax_code_descr', function ($tax){
                $tax_code = Gltaxgroupdt::where('tax_code', $tax->tax_code)->first();
                return $tax_code->tax_type;
            })

           ->addColumn('tax_rate', function ($item){
                return $item->tax_rate .'%';
            })

           ->make(true);
    }
    
    
    public function checkItemTaxable(Request $request){
        $item_no = trim($request->item_no);
        $item_code = trim($request->item_code);
        $req_no = trim($request->req_no);

        $item = PO_reqdetails::select('taxable')->where('item_no', $item_no)->where('req_no', $req_no)
        ->where('item_code', $item_code)->first();
        return $item;
    }

    public function postReqItemTax(Request $request)
    {
        $this->validate($request, [
            'vendor_id'=> 'required',
            'item_no'=> 'required',
            'req_no'=> 'required',
            'tax_code'=> 'required',
            'tax_type'=> 'required',
            'taxable_amount'=> 'required',
            'tax_rate'=> 'required',
        ]);
        
        $req_no = trim($request->req_no);
        $vendor_id = trim($request->vendor_id);
        $item_no = trim($request->item_no);
        $item_code = trim($request->item_code);
        $tax_code = trim($request->tax_code);
        $tax_type = trim($request->tax_type);
        $taxable_amount = str_replace(',', '', $request->taxable_amount);
        $tax_rate = $request->tax_rate;
        $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
        $add_deduct = $add_deduct_check->add_deduct;

        if($add_deduct == 'A')
        {
            $tax_amount = ($taxable_amount * $tax_rate)/100;
        }

        if($add_deduct == 'D')
        {
            $tax_amount = (($taxable_amount * $tax_rate)/100) * (-1);
        }

        
            
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $req_item_tax = DB::table('PO_REQ_TAXES')->insert([
                'vendor_id' => $vendor_id,
                'item_no' => $item_no,
                'req_no' => $req_no,
                'item_code' => $item_code,
                'tax_code' => $tax_code,
                'tax_amt' => $tax_amount,
                'tax_rate' => $tax_rate,
                'taxable_amt' => $taxable_amount,
                'add_deduct' => $add_deduct,
                'created_by' => trim(auth()->id()),
                'created_at' => Carbon::now()
            ]);
    
            $req_header =  DB::table('po_req_header')->where('req_no', $req_no)
                                ->where('vendor_id', $vendor_id)->first();

            $currency_rate = $req_header->currency_rate;
            //update item
            $req_item = PO_reqdetails::where('req_no', $req_no)->where('vendor_id', $vendor_id)
            ->where('item_no', $item_no)->first();

            $this->updReq($req_no,  $item_no);
                
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully added');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }

       
    }  
            
    public function head_Additionalcost_dat(Request $request)
    {
        $req_no = trim($request->req_no);

        $costs = PO_reqhead_costs::where('req_no', trim($request->req_no))->get();

        return Datatables::Of($costs)
           ->addColumn('action', function ($cost) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
			})

            ->addColumn('descr', function ($cost_code) {
                $cost = Im_Additionalcosts::where('cost_code', $cost_code->cost_code)->get()[0];
                return $cost->cost_name;
            })

           ->make(true);
	}

    public function reqHeadtax_dat(Request $request)
    {
        $req_no = trim($request->req_no);

        $taxes = PO_reqheadtaxes::where('req_no', $req_no)->get();

        return Datatables::Of($taxes)
            ->addColumn('tax_code_descr', function ($tax){
                $tax_code = Gltaxgroupdt::where('tax_code', $tax->tax_code)->first();
                return $tax_code->tax_type;
            })

           ->addColumn('tax_rate', function ($tax){
                return $tax->tax_rate .'%';
            })

            ->addColumn('cost_descr', function ($cost){
                $cost = Im_Additionalcosts::where('cost_code', $cost->cost_code)->get()[0];
                return $cost->cost_name;
            })

            ->editColumn('add_deduct', function ($tax){
                if ( $tax->add_deduct == 'A') 
                {
                    return 'ADDITION';
                }

                else if
                ( $tax->add_deduct == 'D') {
                    return 'DEDUCTION';
                }

                else
                {
                    return $tax->add_deduct;
                }
            })

           ->make(true);
    }

    public function storeHeadAdditional_cost(Request $request)
    {
        // dd($request->all());
        $this->validate($request, [
            'cost_code' => 'required',
            'cost'=> 'required'
        ]);

        $req_no = trim($request->req_no);
        $cost_code = trim($request->cost_code);
       
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $new_cost = new PO_reqhead_costs;
            $new_cost->req_no = $req_no;
            $new_cost->cost_code =  $cost_code;
            $new_cost->cost = str_replace(',', '', $request->cost);
            $new_cost->created_by = trim(auth()->id());
            $new_cost->created_at = Carbon::now();
            $new_cost->save();

            $cost_type = Im_Additionalcosts::where('cost_code', $cost_code)->get()[0];
            
            if($cost_type->taxable_flag == 'Y' && $cost_type->tax_group != null)

            $tax_group_code = $cost_type->tax_group;

            $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
        
            foreach ($tax_types as $type) 
            {
                $tax_type = $type->tax_type;

                $tax_code = $type->tax_code;
                
                $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                
                $rate = $gltax->tax_rate;

                $taxable_amnt = str_replace(',', '', $request->cost);
                //trans type
                $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();

                if($transtype)
                {
                    $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                
                    $add_deduct = $add_deduct_check->add_deduct;
        
                    if($add_deduct == 'A')
                    {
                        $tax_amount1 = ($taxable_amnt * $rate)/100;
                    }

                    if($add_deduct == 'D')
                    {
                        $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                    }

                    $save_tax = PO_reqheadtaxes::create([
                        // 'vendor_id' => $vendor_id,
                        'req_no' => $req_no,
                        'tax_rate' => $rate,
                        'taxable_amt' => $taxable_amnt,
                        'add_deduct' => $add_deduct,
                        'tax_code' => $tax_code,
                        'tax_amt' => $tax_amount1,
                        'created_by' => trim(auth()->id()),
                        'created_at' => Carbon::now(),
                        'cost_code' => $request->cost_code
                    ]);
                }
            }

            ##upd reqhead
            $total_tax_req = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req1 = PO_reqheadtaxes::where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req2 = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->sum('tax_amt');

            $taxes_on_add_costs = $total_tax_req1 + $total_tax_req2;
            $total_req_taxes = $total_tax_req + $total_tax_req1 + $total_tax_req2;
           
            $additional_costs_req = PO_reqadditional_costs::where('req_no', $req_no)->sum('cost');
            $additional_costs_req1 = PO_reqhead_costs::where('req_no', $req_no)->sum('cost');

            $total_req_add_costs = $additional_costs_req + $additional_costs_req1;

            $req_net = PO_reqdetails::where('req_no', $req_no)->sum('net_amt');

            $req_amt = $req_net + $total_tax_req1 + $additional_costs_req1;
            // dd($item_net,$req_net, $req_amt, $total_req_discount, $taxes_on_add_costs, $total_req_add_costs , $total_req_taxes,  $total_tax_req,$total_req_net);

            $items_discount = PO_reqdetails::where('req_no', $req_no)->sum('discount_amt');
            if($req->discount_flag == 'Y' && $req->percentage_discount_flag == 'Y')
            {
                $total_req_discount = $items_discount;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
            }

            else if($req->discount_flag == 'Y' && ($req->percentage_discount_flag == 'N' || $req->percentage_discount_flag == null))
            {
                $total_req_discount = $req->discount_amt;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
            }

            else
            {
                $total_req_discount = $items_discount;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1);
            }

            $upd_req = DB::table('po_req_header')->where('req_no', $req_no)
            ->update([
                'additional_cost' => $total_req_add_costs,
                'total_tax' => $total_req_taxes,
                'net_amt' => $total_req_net,
                'additional_cost_tax' => $taxes_on_add_costs,
                'total_discount' => $total_req_discount,
                'req_amt' => $req_amt
            ]);
           
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully added');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function getRqTaxtypes(Request $request)
    {
        $tax_types = DB::table('gltaxtypes')->get();
        return $tax_types;
    }

    public function updReqItemTax(Request $request)
    {
        $this->validate($request, [
            
            'edit_tax_rate'=> 'required',
        ]);
        
        $req_no = trim($request->edit_req_no);
        $vendor_id = trim($request->edit_vendor_id);
        $item_no = trim($request->edit_item_no);
        $item_code = trim($request->edit_item_code);
        $tax_code = trim($request->edit_tax_code);
        $tax_type = trim($request->tax_type);
        $taxable_amount = str_replace(',', '', $request->edit_taxable_amount);
        $tax_rate = $request->edit_tax_rate;
        $add_deduct =trim($request->edit_add_deduct);

        if($add_deduct == 'A')
        {
            $tax_amount = ($taxable_amount * $tax_rate)/100;
        }

        if($add_deduct == 'D')
        {
            $tax_amount = (($taxable_amount * $tax_rate)/100) * (-1);
        }
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {

            $req_item_tax = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)
                            ->where('vendor_id', $vendor_id)
                            ->where('tax_code', $tax_code)
                            ->where('item_no', $item_no)->update([
                            'tax_amt' => $tax_amount,
                            'tax_rate' => $tax_rate,
                            'updated_by' => trim(auth()->id()),
                            'dola' => Carbon::now()
                            ]);
    
            $req_header =  PoReqHeader::where('req_no', $req_no)
                                ->where('vendor_id', $vendor_id)->first();
            //update item
            $req_item = PO_reqdetails::where('req_no', $req_no)->where('vendor_id', $vendor_id)
            ->where('item_no', $item_no)->first();
            
            $this->updReq($req_no, $item_no);
            
            
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully Updated');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }

       
    }

    public function delHeadAdditional_cost(Request $request)
    {
        $req_no = trim($request->req_no);
        $cost_code = trim($request->cost_code);
        $req = PoReqHeader::where('req_no', $req_no)->get()[0];
       
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            ##delete additional costs
            $delete_cost = PO_reqhead_costs::where('req_no', $req_no)->where('cost_code', $cost_code)->delete();

            ##delete additional costs taxes
            $delete_taxes = PO_reqheadtaxes::where('req_no', $req_no)->where('cost_code', $cost_code)->delete();

            ##upd reqhead start
            $items_discount = PO_reqdetails::where('req_no', $req_no)->sum('discount_amt');
            $req_discount = $req->discount_amt;

            $total_tax_req = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req1 = PO_reqheadtaxes::where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req2 = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->sum('tax_amt');

            $taxes_on_add_costs = $total_tax_req1 + $total_tax_req2;
            $total_req_taxes = $total_tax_req + $total_tax_req1 + $total_tax_req2;
           
            $additional_costs_req = PO_reqadditional_costs::where('req_no', $req_no)->sum('cost');
            $additional_costs_req1 = PO_reqhead_costs::where('req_no', $req_no)->sum('cost');

            $total_req_add_costs = $additional_costs_req + $additional_costs_req1;

            $req_net = PO_reqdetails::where('req_no', $req_no)->sum('net_amt');

            if($req->discount_flag == 'Y' && $req->percentage_discount_flag == 'Y')
            {
                $total_req_discount = $items_discount;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
            }

            else if($req->discount_flag == 'Y' && ($req->percentage_discount_flag == 'N' || $req->percentage_discount_flag == null))
            {
                $total_req_discount = $req->discount_amt;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
            }

            else
            {
                $total_req_discount = $items_discount;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1);
            }

            // $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;

            $req_amt = $req_net + $total_tax_req1 + $additional_costs_req1;
            // dd($item_net,$req_net, $req_amt, $total_req_discount, $taxes_on_add_costs, $total_req_add_costs , $total_req_taxes,  $total_tax_req,$total_req_net);

            $items_count = PO_reqdetails::where('req_no', $req_no)->count();

            $upd_req = DB::table('po_req_header')->where('req_no', $req_no)
            ->update([
                'additional_cost' => $total_req_add_costs,
                'total_tax' => $total_req_taxes,
                'net_amt' => $total_req_net,
                'additional_cost_tax' => $taxes_on_add_costs,
                'total_discount' => $total_req_discount,
                'tax_on_items' => $total_tax_req,
                'no_of_items' => $items_count,
                'req_amt' => $req_amt
            ]);

            
            // ##upd reqhead
            // $total_tax_req = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->sum('tax_amt');
            // $total_tax_req1 = PO_reqheadtaxes::where('req_no', $req_no)->sum('tax_amt');
            // $total_tax_req2 = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->sum('tax_amt');

            // $taxes_on_add_costs = $total_tax_req1 + $total_tax_req2;

            // $total_req_taxes = $total_tax_req + $total_tax_req1 + $total_tax_req2;

            // $additional_costs_req = PO_reqadditional_costs::where('req_no', $req_no)->sum('cost');
            // $additional_costs_req1 = PO_reqhead_costs::where('req_no', $req_no)->sum('cost');

            // $total_req_add_costs = $additional_costs_req + $additional_costs_req1;

            // $req_net = PO_reqdetails::where('req_no', $req_no)->sum('net_amt');

            // $total_req_net = $req_net + $total_tax_req1 + $additional_costs_req1;
            
            // $upd_req = DB::table('po_req_header')->where('req_no', $req_no)
            // ->update([
            //     'additional_cost' => $total_req_add_costs,
            //     'total_tax' => $total_req_taxes,
            //     'net_amt' => $total_req_net,
            //     'additional_cost_tax' => $taxes_on_add_costs,
            // ]);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully deleted');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function removeReqItemTax(Request $request)
    {
        
        $req_no = trim($request->delete_req_no);
        $item_no = trim($request->delete_item_no);
        $item_code = trim($request->delete_item_code);
        $tax_code = trim($request->delete_tax_code);
        $vendor_id = trim($request->delete_vendor_id);

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {

            $req_item_tax = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)
                            ->where('item_code', $item_code)
                            ->where('tax_code', $tax_code)
                            ->where('item_no', $item_no)
                            ->delete();
    
            $req_header =  PoReqHeader::where('req_no', $req_no)
                                ->where('vendor_id', $vendor_id)->first();
            //update item
            $req_item = PO_reqdetails::where('req_no', $req_no)->where('vendor_id', $vendor_id)
            ->where('item_no', $item_no)->first();

            $this->updReq($req_no,  $item_no);
         
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully Removed');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }

       
    }

    public function updHeadAdditional_cost(Request $request)
    {
        $this->validate($request, [
            'cost_code' => 'required',
            'cost'=> 'required'
        ]);

        $req_no = trim($request->req_no);
        $cost_code = trim($request->cost_code);
       
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        
        try 
        {
            $upd_cost = PO_reqhead_costs::where('req_no', $req_no)->where('cost_code', $cost_code)
            ->update([
                'cost' => str_replace(',', '', $request->cost)
            ]);

            ##upd taxable amount and tax amnt 
            $taxes = PO_reqheadtaxes::where('req_no', $req_no)->where('cost_code', $cost_code)->get();
           
            if($taxes->count() > 0)
            {
                foreach ($taxes as $tax) 
                {
                    if($tax->add_deduct == 'A')
                    {
                        $tax_amount = (str_replace(',', '', $request->cost) * $tax->tax_rate)/100;
                    }
        
                    else
                    {
                        $tax_amount = ((str_replace(',', '', $request->cost) * $tax->tax_rate)/100) * -1;
                    }

                    $upd_tax = PO_reqheadtaxes::where('req_no', $req_no)->where('cost_code', $cost_code)
                    ->update([
                        'taxable_amt' => str_replace(',', '', $request->cost),
                        'tax_amt' => $tax_amount
                    ]);
                }
            }

            ##upd reqhead
            $total_tax_req = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req1 = PO_reqheadtaxes::where('req_no', $req_no)->sum('tax_amt');
            $total_tax_req2 = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->sum('tax_amt');

            $taxes_on_add_costs = $total_tax_req1 + $total_tax_req2;
            $total_req_taxes = $total_tax_req + $total_tax_req1 + $total_tax_req2;
        
            $additional_costs_req = PO_reqadditional_costs::where('req_no', $req_no)->sum('cost');
            $additional_costs_req1 = PO_reqhead_costs::where('req_no', $req_no)->sum('cost');

            $total_req_add_costs = $additional_costs_req + $additional_costs_req1;

            $req_net = PO_reqdetails::where('req_no', $req_no)->sum('net_amt');

            $req_amt = $req_net + $total_tax_req1 + $additional_costs_req1;
            // dd($item_net,$req_net, $req_amt, $total_req_discount, $taxes_on_add_costs, $total_req_add_costs , $total_req_taxes,  $total_tax_req,$total_req_net);

            $items_discount = PO_reqdetails::where('req_no', $req_no)->sum('discount_amt');
            if($req->discount_flag == 'Y' && $req->percentage_discount_flag == 'Y')
            {
                $total_req_discount = $items_discount;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
            }

            else if($req->discount_flag == 'Y' && ($req->percentage_discount_flag == 'N' || $req->percentage_discount_flag == null))
            {
                $total_req_discount = $req->discount_amt;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
            }

            else
            {
                $total_req_discount = $items_discount;
                $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1);
            }

            $upd_req = DB::table('po_req_header')->where('req_no', $req_no)
            ->update([
                'additional_cost' => $total_req_add_costs,
                'total_tax' => $total_req_taxes,
                'net_amt' => $total_req_net,
                'additional_cost_tax' => $taxes_on_add_costs,
                'total_discount' => $total_req_discount,
                'req_amt' => $req_amt
            ]);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully updated');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function taxOnAddcosts_dat(Request $request)
    {
        $req_no = trim($request->req_no);
        
        $item_no =  trim($request->item_no);

        $taxes = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->where('item_no',  $item_no)->get();


        return Datatables::Of($taxes)
            ->addColumn('tax_code_descr', function ($tax){
                $tax_code = Gltaxgroupdt::where('tax_code', $tax->tax_code)->first();
                return $tax_code->tax_type;
            })

           ->addColumn('tax_rate', function ($tax){
                return $tax->tax_rate .'%';
            })

            ->addColumn('cost_descr', function ($cost){
                $cost = Im_Additionalcosts::where('cost_code', $cost->cost_code)->get()[0];
                return $cost->cost_name;
            })

            ->editColumn('add_deduct', function ($tax){
                if ( $tax->add_deduct == 'A') 
                {
                    return 'ADDITION';
                }

                else if
                ( $tax->add_deduct == 'D') {
                    return 'DEDUCTION';
                }

                else
                {
                    return $tax->add_deduct;
                }
            })

           ->make(true);
    }

    public function updReq($req_no, $item_no)
    {
        ##upd item
        $item = PO_reqdetails::where('req_no', $req_no)->where('item_no', $item_no)->get()[0];
        $req = PoReqHeader::where('req_no', $req_no)->get()[0];
        $taxable_amnt = $item->taxable_amt;
      
        $i_taxes = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->where('item_no', $item_no)->sum('tax_amt');
        $i_tax_on_cost = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->where('item_no', $item_no)->sum('tax_amt');
        $i_total_tax =  $i_taxes + $i_tax_on_cost;

        $i_add_costs = PO_reqadditional_costs::where('req_no', $req_no)->where('item_no', $item_no)->sum('cost');
       
        $item_net =  $i_total_tax  + $i_add_costs +  $taxable_amnt;
        // dd($i_add_costs, $i_taxes, $i_tax_on_cost, $i_total_tax, $item_net);
        $upd_item = PO_reqdetails::where('req_no', $req_no)->where('item_no', $item_no)
        ->update([
            'additional_cost' => $i_add_costs,
            'total_tax' => $i_total_tax,
            'net_amt' => $item_net,
            'additional_cost_tax' => $i_tax_on_cost,
            'taxable_amt' =>  $taxable_amnt,
            'tax_on_item' =>  $i_taxes
        ]);
        ##end upd item

        ##upd reqhead start
        $items_discount = PO_reqdetails::where('req_no', $req_no)->sum('discount_amt');
        $req_discount = $req->discount_amt;
        
        $total_req_discount = $items_discount +  $req_discount;

        $total_tax_req = DB::table('PO_REQ_TAXES')->where('req_no', $req_no)->sum('tax_amt');
        $total_tax_req1 = PO_reqheadtaxes::where('req_no', $req_no)->sum('tax_amt');
        $total_tax_req2 = PO_reqitem_addcosts_taxes::where('req_no', $req_no)->sum('tax_amt');

        $taxes_on_add_costs = $total_tax_req1 + $total_tax_req2;
        $total_req_taxes = $total_tax_req + $total_tax_req1 + $total_tax_req2;
       
        $additional_costs_req = PO_reqadditional_costs::where('req_no', $req_no)->sum('cost');
        $additional_costs_req1 = PO_reqhead_costs::where('req_no', $req_no)->sum('cost');

        $total_req_add_costs = $additional_costs_req + $additional_costs_req1;

        $req_net = PO_reqdetails::where('req_no', $req_no)->sum('net_amt');

        //$total_req_net = $req_net + $total_tax_req1 + $additional_costs_req1;

        $req_amt = ($req_net + $total_tax_req1 + $additional_costs_req1);
        //dd( $req_amt);
        
        $items_discount = PO_reqdetails::where('req_no', $req_no)->sum('discount_amt');

        if($req->discount_flag == 'Y' && $req->percentage_discount_flag == 'Y')
        {
            $total_req_discount = $items_discount;
            $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
        }

        else if($req->discount_flag == 'Y' && ($req->percentage_discount_flag == 'N' || $req->percentage_discount_flag == null))
        {
            $total_req_discount = $req->discount_amt;
            $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1) - $total_req_discount;
        }

        else
        {
            $total_req_discount = $items_discount;
            $total_req_net = ($req_net + $total_tax_req1 + $additional_costs_req1);
        }

        //dd($total_req_discount, $total_req_net, $req_net, $total_req_add_costs, $total_req_taxes);

        $upd_req = DB::table('po_req_header')->where('req_no', $req_no)
        ->update([
            'additional_cost' => $total_req_add_costs,
            'total_tax' => $total_req_taxes,
            'net_amt' => $total_req_net,
            'additional_cost_tax' => $taxes_on_add_costs,
            'tax_on_items' => $total_tax_req,
            'req_amt'=>$req_amt
        ]);
        ##end
    }


    public function cancelReq(Request $request)
    {
        $this->validate($request, [
            'reason_cancelled' => 'required|min:5'
        ]);

        $req_no = trim($request->req_no);
        $req = PoReqHeader::where('req_no', $req_no)->get()[0];
       
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        
        try 
        {
            if($req->cancelled == 'Y')
            {
                Session::Flash('error', 'Failed, requisition has already been cancelled');
                $result = array("status" => -1);
                return $result;
            }

            else if($req->approved == 'Y')
            {
                Session::Flash('error', 'Failed, requisition has already been approved');
                $result = array("status" => -2);
                return $result;
            }

            else
            {
                $upd_req = PoReqHeader::where('req_no', $req_no)
                ->update([
                    'cancelled' => 'Y',
                    'cancelled_by' => auth()->id(),
                    'cancelled_date' => Carbon::now(),
                    'reason_cancelled' => $request->reason_cancelled
                ]);
            }
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully cancelled');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function checkReqCancellation(Request $request)
    {
        $allowed = Gate::allows('cancel-po-req') ? "Y" : "N";

		// Return the result as a JSON response
		return response()->json(["status" => $allowed]);
    }

    public function purchase_orders(){
        $dat = array(
			'main' => 'PO', 
            'module' => 'Purchase Orders',
		);
        return view::make('gl.po.po_headers', compact('dat'));
    }

    public function po_datatb(Request $request)
    {
        $headers = POheader::all();

        return Datatables::Of($headers)
            ->addColumn('action', function ($headers) {
                return '<a class="btn btn-xs" id="edit_btn"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('po_date', function ($header){
                $date = new DateTime($header->po_date);
                $format_date =  $date->format('d/m/Y');
                return $format_date;
            })
            ->make(true);
    }

    public function po_details(Request $request, POheader $po)
    {
        if($request->has('po_number'))
        {
            $po = POheader::where('po_number', trim($request->po_number))->get()[0];

            $glhs = Nlparams::where('prid', 'GLH')->where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

            $tax_groups = Gltaxgroups::all();

            $currencies = Currency::all();

            return ['po'=> $po, 'glhs' => $glhs, 'tax_groups'=> $tax_groups, 'currencies' => $currencies];
        }
        
        $dat = array(
			'main' => 'PO', 
            'module' => 'Purchase Order Details',
		);

        $vendor = Apvendors::where('vendor_id', $po->vendor_id)->first();

        $status_code = DB::table('po_status')->where('status_code', $po->status)->first();

        $currency =  Currency::where('currency_code', $po->currency_code)->get()[0];

        $cost_arr = POhead_additionalcosts::where('po_number', $po->po_number)->pluck('cost_code')->toArray();
        
        $all_costs = Im_Additionalcosts::all();

        $costs = [];
        
        foreach ($all_costs as $cost) 
        {
            if(!in_array($cost->cost_code, $cost_arr))
            {
                array_push( $costs, $cost);
            }
        }

        $additional_costs = collect($costs);

        //!approval 
        $user_id = trim(Auth::user()->user_id);
        $process = Aims_process::where('process_code',trim($po->process_code))->first();
        $process_code= trim($po->process_code);
    
        $process = Aims_process::with(['process_dtl',
                'approval_levels'
            ])
            ->where('process_code',trim($process_code))
            ->first();
            
        // fetch approvals if any
        $approval_dtl = Approvals::with('approval_flow')
                                    ->where('po_number',trim($po->po_number))
                                    ->where('type', 'PO')
                                    ->orderBy('date_created','DESC')
                                    ->first();
        $check_approval = Approvals::where('po_number',trim($po->po_number))->where('type', 'PO')
                                    ->orderBy('date_created','DESC')
                                    ->first();


        if(isset($check_approval)){
            // $status = 'N';
            if($check_approval->status == 'A'){
                $status = 'A';
                
            }
            elseif($check_approval->status == 'R'){
                $status = 'R';
                $msg = 'Requisition Approval was rejected';
            }
            elseif($check_approval->status == 'P'){
                $status = 'P';
                $msg = 'Requisition has a Pending approval';
            }
        }

        $approval_status = $status ;
        $approval_msg = $msg;
                //!../approval
        return view::make('gl.po.po_header_details', compact('dat', 'po', 'currency', 'additional_costs',
         'approval_status','approval_msg','approval_dtl','process', 'status_code', 'vendor'));
    }

    public function getvendor_reqs(Request $request)
    {
        $reqs = PoReqHeader::where('vendor_id', trim($request->vendor_id))
        ->where('approved', 'Y')->where('cancelled', '!=', 'Y')->where(function ($q) {
            $q->where('po_created_flag', null)->orWhere('po_created_flag', 'N');
        })->get();

        return $reqs;
    }

    public function po_reqdetails(Request $request)
    {
        $req = PoReqHeader::where('req_no', trim($request->req_no))->get()[0];

        $glhs = Nlparams::where('prid', 'GLH')->where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

        $tax_groups = Gltaxgroups::all();

        $currencies = Currency::all();

        return ['req'=> $req, 'glhs' => $glhs, 'tax_groups'=> $tax_groups, 'currencies' => $currencies];
    }

    public function storePO(Request $request)
    {
        // dd($request->all());
        $this -> validate($request, [
            'po_date' => 'required',
            'description' => 'required',
            'vendor_id' => 'required',
            'tax_group' => 'required',
            'currency_code' => 'required',
            'currency_rate' => 'required',
            'po_from_req_flag' => 'required',
            'control_account' => 'required',
        ]);

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        $today = Carbon::today();
        $year = $today->format('Y');

        try 
        {
            $doctype = 'POR';
            $po_serial = $this->genPoNumber();
            $po_number = $doctype.STR_PAD($po_serial, 6, '0', STR_PAD_LEFT) . $year;
            
            $vendor_id = $request->vendor_id;
            
            if($request->po_from_req_flag == 'Y')
            {
                $req = PoReqHeader::where('req_no', $request->req_no)->get()[0];
                $this->createPoFromReq($req, $po_number, $request);
            }

            else
            {
                $additional_cost_flag = 'N';
                $percentage_dicount_flag = 'N';
                $dicount_level = 'I';
                $discount = 0;
    
                #discount
                if($request->apply_discount == 'Y')
                {
                    if($request->discount == 'A' && $request->disc_amt != null)
                    {
                        $discount = str_replace(',', '', $request->disc_amt);
                        $dicount_level = 'P';
                    }
        
                    else if($request->discount == 'P' && $request->percentage_disc != null)
                    {
                        $percentage_disc = floatval($request->percentage_disc);
                        $percentage_dicount_flag = 'Y';
                        $dicount_level = 'P';
                    }
                }
    
                ##additional costs
                if($request->additional_cost == 'Y')
                {
                    if(!empty($request->cost_code && $request->cost))
                    {
                        for($i = 0; $i < count($request->cost_code); $i++) 
                        { 
                            $cost_type = Im_Additionalcosts::where('cost_code', $request->cost_code[$i])->get()[0];
                          
                            if($cost_type->taxable_flag == 'Y' && $cost_type->tax_group != null)
                            {
                                $tax_group_code = $cost_type->tax_group;
                    
                                $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
                               
                                foreach ($tax_types as $type) 
                                {
                                    $tax_type = $type->tax_type;
    
                                    $tax_code = $type->tax_code;
                                    
                                    $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                                    
                                    $rate = $gltax->tax_rate;
    
                                    $taxable_amnt = str_replace(',', '', $request->cost[$i]);
                                    
                                    //trans type
                                    $transtype = Gltaxtypes::where('tax_type', $tax_type)->where('transtype', 'INV')->first();
                    
                                    if($transtype)
                                    {
                                        $add_deduct_check = Gltaxtypes::where('tax_type', $tax_type)->first();
                                        
                                        $add_deduct = $add_deduct_check->add_deduct;
                            
                                        if($add_deduct == 'A')
                                        {
                                            $tax_amount1 = ($taxable_amnt * $rate)/100;
                                        }
                    
                                        if($add_deduct == 'D')
                                        {
                                            $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                                        }
                    
                                        $save_tax = POheader_taxes::create([
                                            'vendor_id' => $vendor_id,
                                            'po_number' => $po_number,
                                            'tax_rate' => $rate,
                                            'taxable_amt' => $taxable_amnt,
                                            'add_deduct' => $add_deduct,
                                            'tax_code' => $tax_code,
                                            'tax_amt' => $tax_amount1,
                                            'created_by' => trim(auth()->id()),
                                            'created_at' => Carbon::now(),
                                            'cost_code' => $request->cost_code[$i],
                                        ]);
                                    }
                                }
                            }

                            $new_cost =  POhead_additionalcosts::create([
                                'po_number' => $po_number,
                                'cost_code' => $request->cost_code[$i],
                                'cost' => str_replace(',', '', $request->cost[$i]),
                                'created_by' => trim(auth()->id()),
                                'created_at' => Carbon::now(),
                            ]);
                        }
                        $additional_cost_flag = 'Y';
                    }
                }
    
                $items_discount = POdetails::where('po_number', $po_number)->sum('discount_amt');
                
                $total_po_discount = $items_discount + $discount;
               
                ##upd poheader
                $total_tax_po = POtaxes::where('po_number', $po_number)->sum('tax_amt');
                $total_tax_po1 = POheader_taxes::where('po_number', $po_number)->sum('tax_amt');
                $total_tax_po2 = POitem_taxes_on_costs::where('po_number', $po_number)->sum('tax_amt');
    
                $taxes_on_add_costs = $total_tax_po1 + $total_tax_po2;
                //dd($taxes_on_add_costs,$total_po_discount, $total_tax_po, $total_tax_po1, $total_tax_po2);
    
                $total_po_taxes = $total_tax_po + $total_tax_po1 + $total_tax_po2;
    
                $additional_costs_po1 = POhead_additionalcosts::where('po_number', $po_number)->sum('cost');
                $additional_costs_po = POitem_additionalcosts::where('po_number', $po_number)->sum('cost');
    
                $total_po_add_costs = $additional_costs_po + $additional_costs_po1;
    
                $po_net = POdetails::where('po_number', $po_number)->sum('net_amt');

                $total_po_net = $po_net + $total_tax_po1 + $additional_costs_po1;

                $po_amt = $po_net + $total_tax_po1 + $additional_costs_po1;
                // dd($po_net, $total_po_add_costs,$total_po_taxes,$total_po_net, $total_po_discount, $percentage_disc ,$po_amt);

                $total_items_quantity = POdetails::where('po_number', $po_number)->sum('quantity');

                $create_po = POheader::create([
                    'po_number' => $po_number,
                    'req_no' => $request->req_no,
                    'po_date' => $request->po_date,
                    'description' => $request->description,
                    'vendor_id' => $request->vendor_id,
                    'tax_group' => $request->tax_group,
                    'currency_code' => $request->currency_code,
                    'currency_rate' => $request->currency_rate,
                    'control_account' => $request->control_account,
                    'cancelled' => 'N',
                    'process_code' => '3',
                    'no_of_items' => 0,
                    'created_by' => auth()->id(),
                    'created_at' => Carbon::now(),
                    'discount_flag' =>  $request->apply_discount,
                    'additional_cost' => $total_po_add_costs,
                    'total_tax' => $total_po_taxes,
                    'net_amt' => $total_po_net,
                    'additional_costs_flag' => $additional_cost_flag,
                    'tax_on_add_costs' => $taxes_on_add_costs,
                    'discount_amt' => $discount,
                    'percentage_discount' => floatval($percentage_disc),
                    'tax_on_items' => $total_tax_po,
                    'percentage_discount_flag'=>$percentage_dicount_flag,
                    'discount_level' => $dicount_level,
                    'TOTAL_DISCOUNT' => $total_po_discount,
                    'po_amt' => $po_amt,
                    'doc_type' => $doctype,
                    'TOTAL_ITEMS_QUANTITY' => $total_items_quantity,
                    'status' =>  '001'
                ]);
            }

            ##upd requisition
            $update_req = PoReqHeader::where('req_no', $request->req_no)
            ->update([
                'po_created_flag' => 'Y',
                'po_number' => $po_number
            ]);

            $new_serial = $this->updDoctypeSerial();

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            return response()->json(['status' => 1, 'po_number' => $po_number]);
            Session::Flash('success', 'Purchase order succesfully created');
        } 

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            // dd($th);
            Session::Flash('error', 'Error occurred, try again');
        }
    }

    public function updPO(Request $request)
    {
       // dd($request->all());
        $po_number = trim($request->po_number);
       
        $vendor_id = trim($request->vendor_id);

        $this -> validate($request, [
            'po_date' => 'required',
            'description' => 'required',
            'vendor_id' => 'required',
            'tax_group' => 'required',
            'currency_code' => 'required',
            'currency_rate' => 'required',
        ]);
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $percentage_dicount_flag = 'N';
            $discount = 0;
            $discount_flag = 'N';

            #discount
            if($request->edit_apply_discount == 'Y')
            {
                if($request->edit_discount == 'A' && $request->disc_amt != null)
                {
                    $discount = str_replace(',', '', $request->disc_amt);
                    $dicount_level = 'P';
                    $discount_flag = 'Y';

                    $items = POdetails::where('po_number', $po_number)->get();
                    ##update po items
                    foreach ($items as $item) {
                        $upd_item = POdetails::where('po_number', $po_number)->where('item_no', $item->item_no)
                        ->update([
                            'discount_amt' =>  0,
                            'percentage_discount_flag' => 'N',
                            'percentage_discount' => null, 
                            'taxable_amt' => $item->total_cost
                        ]);

                        $item_taxes = POtaxes::where('po_number', $po_number)->where('item_no', $item->item_no)->get();
                        if(count($item_taxes) > 0)
                        {
                            foreach ($item_taxes as $tx) 
                            {
                                $upd_tx = POtaxes::where('po_number', $po_number)->where('item_no', $item->item_no)
                                ->where('tax_code', $tx->tax_code)->update([
                                    'taxable_amt' =>  $item->total_cost,
                                    'tax_amt' => ($item->total_cost * $tx->tax_rate)/100,
                                ]);
                            }
                        }
                    }
                }
    
                else if($request->edit_discount == 'P' && $request->percentage_disc != null)
                {
                    $percentage_disc = floatval($request->percentage_disc);
                    $percentage_dicount_flag = 'Y';
                    $dicount_level = 'P';
                    $discount_flag = 'Y';
                   
                    ##update po items
                    $items = POdetails::where('po_number', $po_number)->get();
                    
                    foreach ($items as $item) {
                        $discount = ($percentage_disc * $item->total_cost) / 100; 

                        $upd_item = POdetails::where('po_number', $po_number)->where('item_no', $item->item_no)
                        ->update([
                            'discount_amt' =>  $discount,
                            'percentage_discount_flag' => 'N',
                            'percentage_discount' => null
                        ]);
                    }
                }
            }
          
            ##upd pohead
            $items_discount = POdetails::where('po_number', $po_number)->sum('discount_amt');
            
            $po_net = POdetails::where('po_number', $po_number)->sum('net_amt');
         
            $total_tax_po = POtaxes::where('po_number', $po_number)->sum('tax_amt');
            
            $total_tax_po1 = POheader_taxes::where('po_number', $po_number)->sum('tax_amt');
           
            $total_tax_po2 = POitem_taxes_on_costs::where('po_number', $po_number)->sum('tax_amt');
            
            $taxes_on_add_costs = $total_tax_po1 + $total_tax_po2;

            $total_po_taxes = $total_tax_po + $total_tax_po1 + $total_tax_po2;

            $additional_costs_po = POitem_additionalcosts::where('po_number', $po_number)->sum('cost');
            $additional_costs_po1 = POhead_additionalcosts::where('po_number', $po_number)->sum('cost');

            $total_po_add_costs = $additional_costs_po + $additional_costs_po1;

            $po_amt = $po_net + $total_tax_po1 + $additional_costs_po1;

            #upd head discount flag
            $upd = POheader::where('po_number', $po_number)
            ->update([
                'discount_flag' => $discount_flag,
                'percentage_discount' => floatval($percentage_disc),
                'tax_on_items' => $total_tax_po,
                'percentage_discount_flag'=>$percentage_dicount_flag,
                'discount_level' => $dicount_level,
                // 'TOTAL_DISCOUNT' => $total_po_discount,
                'currency_code' => $request->currency_code,
                'control_account' => trim($request->control_account),
                'tax_group' => trim($request->tax_group),
                'currency_rate' => $request->currency_rate,
                'po_date' => $request->po_date,
                'description' => $request->description,
                'updated_by' => trim(auth()->id()),
                'dola' => Carbon::now(),
                'tax_on_add_costs' => $taxes_on_add_costs,
                'additional_cost' => $total_po_add_costs,
                'total_tax' => $total_po_taxes
            ]);

            $po = POheader::where('po_number', $po_number)->get()[0];

            if($po->discount_flag == 'Y' && $po->percentage_discount_flag == 'Y')
            {
                $total_po_discount = $items_discount;
                $total_po_net = ($po_net + $total_tax_po1 + $additional_costs_po1) - $total_po_discount;
            }

            else if($po->discount_flag == 'Y' && ($po->percentage_discount_flag == 'N' || $po->percentage_discount_flag == null))
            {
                $total_po_discount = str_replace(',', '', $request->disc_amt);
                $total_po_net = ($po_net + $total_tax_po1 + $additional_costs_po1) - $total_po_discount;
            }

            else
            {
                $total_po_discount = $items_discount;
                $total_po_net = ($po_net + $total_tax_po1 + $additional_costs_po1);
            }
           
            $upd_po = POheader::where('po_number', $po_number)
            ->update([
                'net_amt' => $total_po_net,
                'TOTAL_DISCOUNT' => $total_po_discount
            ]);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            return response()->json(['status' => 1, 'po_number' => $po_number]);
            Session::Flash('success', 'Purchase order succesfully updated');
        } 

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Error occurred, try again');
        }
    }

    public function po_itemsdat(Request $request)
    {
        $items = POdetails::where('po_number',  trim($request->po_number))->get();
       
        return Datatables::Of($items)
           ->addColumn('action', function ($item) {
                return '<a class="btn btn-xs" id="btn-view"><i class="glyphicon glyphicon-eye-open"></i></a><a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
			})

            ->addColumn('discount_applicable', function($item)
            {
                $po = POheader::where('po_number', $item->po_number)->get()[0];

                if($po->discount_flag == 'Y'  || $item->discount_flag == 'Y')
                {
                    return 'Yes';
                }

                else
                {
                    return 'No';
                }
            })

            ->editColumn('discount_level', function($item)
            {
                $po = POheader::where('po_number', $item->po_number)->get()[0];
               
                if($po->discount_level == 'P'  && $po->discount_flag == 'Y'){
                    return 'Purchase Order';
                }

                else if($item->discount_flag == 'Y' )
                {
                    return 'Item';
                }
            })
           ->make(true);
	}

    public function pohead_Additionalcosts_dat(Request $request)
    {
        $po_number = trim($request->po_number);

        $costs = POhead_additionalcosts::where('po_number', trim($request->po_number))->get();

        return Datatables::Of($costs)
           ->addColumn('action', function ($cost) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
			})

            ->addColumn('descr', function ($cost_code) {
                $cost = Im_Additionalcosts::where('cost_code', $cost_code->cost_code)->get()[0];
                return $cost->cost_name;
            })
           ->make(true);
	}

    public function poHeadtax_dat(Request $request)
    {
        $po_number = trim($request->po_number);

        $taxes = POheader_taxes::where('po_number', $po_number)->get();
       
        return Datatables::Of($taxes)
           ->addColumn('tax_code_descr', function ($tax){
                $tax_code = Gltaxgroupdt::where('tax_code', $tax->tax_code)->first();
                return $tax_code->tax_type;
            })

           ->addColumn('tax_rate', function ($tax){
                return $tax->tax_rate .'%';
            })

            ->addColumn('cost_descr', function ($cost){
                $cost = Im_Additionalcosts::where('cost_code', $cost->cost_code)->get()[0];
                return $cost->cost_name;
            })

            ->editColumn('add_deduct', function ($tax){
                if ( $tax->add_deduct == 'A') 
                {
                    return 'ADDITION';
                }

                else if
                ( $tax->add_deduct == 'D') {
                    return 'DEDUCTION';
                }

                else
                {
                    return $tax->add_deduct;
                }
            })

           ->make(true);
    }

    public function po_Itemdetails(Request $request)
    {
        // dd($request->all());
        $dat = array(
			'main' => 'PO', 
            'module' => 'Purchase Order Details',
            'module' => 'Purchase Order Item Details',
		);

		$po_number = trim($request->po_number);
        $item_no = trim($request->item_no);
        $po = POheader::where('po_number', $po_number)->get()[0];

        $item = POdetails::where('item_no', $item_no)->where('po_number', $po_number)->first();

        $account = Nlparams::where('prid', 'GLH')->where('prsno', $item->control_account)->first();
		
        $unit = DB::table('unit_of_measure')->where('unit_code', trim($item->unit_of_measure))->first();

        $cost_arr = POitem_additionalcosts::where('item_no', $item_no)->where('po_number', $po->po_number)->pluck('cost_code')->toArray();
        
        $all_costs = Im_Additionalcosts::all();

        $costs = [];
        
        foreach ($all_costs as $cost) 
        {
            if(!in_array($cost->cost_code, $cost_arr))
            {
                array_push( $costs, $cost);
            }
        }

        $additional_costs = collect($costs);

        $tax_types = DB::table('gltaxtypes')->where('transtype', 'INV')->get();
		
        return view::make('gl.po.po_item_details', compact('account', 'tax_types', 'item', 'dat', 'unit', 'additional_costs', 'po'));
    }

    public function poItemTaxes_dat(Request $request)
    {
        $taxes = POtaxes::where('po_number', trim($request->po_number))->where('item_no', trim($request->item_no))->get();

        return Datatables::Of($taxes)
            ->addColumn('action', function ($cost) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
            })

            ->addColumn('tax_code_descr', function ($tax){
                $tax_code = Gltaxgroupdt::where('tax_code', $tax->tax_code)->first();
                return $tax_code->tax_type;
            })

           ->addColumn('tax_rate', function ($tax){
                return $tax->tax_rate .'%';
            })

            ->editColumn('add_deduct', function ($tax){
                if ( $tax->add_deduct == 'A') 
                {
                    return 'ADDITION';
                }

                else if
                ( $tax->add_deduct == 'D') {
                    return 'DEDUCTION';
                }

                else
                {
                    return $tax->add_deduct;
                }
            })
           ->make(true);
    }

    public function poItemAddCosts_dat(Request $request)
    {
        $costs = POitem_additionalcosts::where('po_number', trim($request->po_number))
        ->where('item_no', trim($request->item_no))->get();
        
        return Datatables::Of($costs)
           ->addColumn('action', function ($cost) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
			})

            ->addColumn('descr', function ($cost_code) {
                $cost = Im_Additionalcosts::where('cost_code', $cost_code->cost_code)->get()[0];
                return $cost->cost_name;
            })

           ->make(true);

    }

    public function poItemAddCoststax_dat(Request $request)
    {
        $taxes = POitem_taxes_on_costs::where('po_number', trim($request->po_number))
        ->where('item_no', trim($request->item_no))->get();
    
        return Datatables::Of($taxes)
            ->addColumn('tax_code_descr', function ($tax){
                $tax_code = Gltaxgroupdt::where('tax_code', $tax->tax_code)->first();
                return $tax_code->tax_type;
            })

           ->addColumn('tax_rate', function ($tax){
                return $tax->tax_rate .'%';
            })

            ->addColumn('cost_descr', function ($cost){
                $cost = Im_Additionalcosts::where('cost_code', $cost->cost_code)->get()[0];
                return $cost->cost_name;
            })

            ->editColumn('add_deduct', function ($tax){
                if ( $tax->add_deduct == 'A') 
                {
                    return 'ADDITION';
                }

                else if
                ( $tax->add_deduct == 'D') {
                    return 'DEDUCTION';
                }

                else
                {
                    return $tax->add_deduct;
                }
            })

           ->make(true);
    }

    public function storePOHeadAdditional_cost(Request $request)
    {
        $this->validate($request, [
            'cost_code' => 'required',
            'cost'=> 'required'
        ]);

        $po_number = trim($request->po_number);
        $cost_code = trim($request->cost_code);
       
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $new_cost = new POhead_additionalcosts;
            $new_cost->po_number = $po_number;
            $new_cost->cost_code =  $cost_code;
            $new_cost->cost = str_replace(',', '', $request->cost);
            $new_cost->created_by = trim(auth()->id());
            $new_cost->created_at = Carbon::now();
            $new_cost->save();

            $cost_type = Im_Additionalcosts::where('cost_code', $cost_code)->get()[0];
            
            if($cost_type->taxable_flag == 'Y' && $cost_type->tax_group != null)
            {
                $tax_group_code = $cost_type->tax_group;
                
                $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
                
                foreach ($tax_types as $type) 
                {
                    $tax_type = $type->tax_type;

                    $tax_code = $type->tax_code;
                    
                    $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                    
                    $rate = $gltax->tax_rate;

                    $taxable_amnt = str_replace(',', '', $request->cost);
                    //trans type
                    $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
                   
                    if($transtype)
                    {
                        $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                    
                        $add_deduct = $add_deduct_check->add_deduct;
            
                        if($add_deduct == 'A')
                        {
                            $tax_amount1 = ($taxable_amnt * $rate)/100;
                        }

                        if($add_deduct == 'D')
                        {
                            $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                        }

                        $save_tax = POheader_taxes::create([
                            // 'vendor_id' => $vendor_id,
                            'po_number' => $po_number,
                            'tax_rate' => $rate,
                            'taxable_amt' => $taxable_amnt,
                            'add_deduct' => $add_deduct,
                            'tax_code' => $tax_code,
                            'tax_amt' => $tax_amount1,
                            'created_by' => trim(auth()->id()),
                            'created_at' => Carbon::now(),
                            'cost_code' => $request->cost_code
                        ]);
                    }
                }
            }

            $this->updPurchaseOrder($po_number);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully added');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function delPOHeadAdditional_cost(Request $request)
    {
        $po_number = trim($request->po_number);
        $cost_code = trim($request->cost_code);
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            ##delete additional costs
            $delete_cost = POhead_additionalcosts::where('po_number', $po_number)->where('cost_code', $cost_code)->delete();
           
            ##delete additional costs taxes
            $delete_taxes = POheader_taxes::where('po_number', $po_number)->where('cost_code', $cost_code)->delete();
            
            $this->updPurchaseOrder($po_number);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully deleted');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function updPOHeadAdditional_cost(Request $request)
    {
        $this->validate($request, [
            'cost_code' => 'required',
            'cost'=> 'required'
        ]);

        $po_number = trim($request->po_number);
        $cost_code = trim($request->cost_code);
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        
        try 
        {
            $upd_cost = POhead_additionalcosts::where('po_number', $po_number)->where('cost_code', $cost_code)
            ->update([
                'cost' => str_replace(',', '', $request->cost)
            ]);

            ##upd taxable amount and tax amnt 
            $taxes = POheader_taxes::where('po_number', $po_number)->where('cost_code', $cost_code)->get();
           
            if($taxes->count() > 0)
            {
                foreach ($taxes as $tax) 
                {
                    if($tax->add_deduct == 'A')
                    {
                        $tax_amount = (str_replace(',', '', $request->cost) * $tax->tax_rate)/100;
                    }
        
                    else
                    {
                        $tax_amount = ((str_replace(',', '', $request->cost) * $tax->tax_rate)/100) * -1;
                    }

                    $upd_tax = POheader_taxes::where('po_number', $po_number)->where('cost_code', $cost_code)
                    ->update([
                        'taxable_amt' => str_replace(',', '', $request->cost),
                        'tax_amt' => $tax_amount
                    ]);
                }
            }

            $this->updPurchaseOrder($po_number);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully updated');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function storePOItemAdditional_cost(Request $request)
    {
        $this->validate($request, [
            'cost_code' => 'required',
            'cost'=> 'required'
        ]);

        $po_number = trim($request->po_number);
        $cost_code = trim($request->cost_code);
        $item_no = trim($request->item_no);
       
        $item = POdetails::where('po_number', $po_number)->where('item_no', $item_no)->get()[0];
        $po = POheader::where('po_number', $po_number)->get()[0];
        $vendor_id =  $po->vendor_id;
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $new_cost = POitem_additionalcosts::create([
                'po_number'=> $po_number,
                'item_no'=> $item_no,
                'cost_code'=>  $cost_code,
                'cost'=> str_replace(',', '', $request->cost),
                'created_by'=> trim(auth()->id()),
                'created_at'=> Carbon::now()
            ]);

            ###taxes on additional costs 
            $cost_type = Im_Additionalcosts::where('cost_code', $cost_code)->get()[0];
            
            if($cost_type->taxable_flag == 'Y')
            {
                $tax_group_code = $cost_type->tax_group;
                
                $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
            
                foreach ($tax_types as $type) 
                {
                    $tax_type = $type->tax_type;

                    $tax_code = $type->tax_code;
                    
                    $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                    
                    $rate = $gltax->tax_rate;

                    $taxable_amnt = str_replace(',', '', $request->cost);
                    
                    //trans type
                    $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
                    
                    if($transtype)
                    {
                        $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                    
                        $add_deduct = $add_deduct_check->add_deduct;
            
                        if($add_deduct == 'A')
                        {
                            $tax_amount1 = ($taxable_amnt * $rate)/100;
                        }
    
                        if($add_deduct == 'D')
                        {
                            $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                        }
                      
                        $save_tax = POitem_taxes_on_costs::create([
                            'vendor_id'=>$vendor_id,
                            'cost_code' => $cost_code,
                            'item_no' => $item_no,
                            'po_number' => $po_number,
                            'tax_rate' => $rate,
                            'taxable_amt' => $taxable_amnt,
                            'add_deduct' => $add_deduct,
                            'tax_code' => $tax_code,
                            'tax_amt' => $tax_amount1,
                            'created_by' => trim(auth()->id()),
                            'created_at' => Carbon::now(),
                        ]);
                    }
                }
            }

            $this->updPurchaseOrderItem($po_number, $item_no);
            $this->updPurchaseOrder($po_number);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully added');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function delPOItemAdditional_cost(Request $request)
    {
        $po_number = trim($request->po_number);
        $cost_code = trim($request->cost_code);
        $item_no = trim($request->item_no);
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            ##delete additional cost
            $delete_cost = POitem_additionalcosts::where('po_number', $po_number)->where('item_no', $item_no)
            ->where('cost_code', $cost_code)->delete();
           
            ##delete taxes on additional cost
            $delete_tax_on_costs = POitem_taxes_on_costs::where('po_number', $po_number)->where('item_no', $item_no)
            ->where('cost_code', $cost_code)->delete();

            $this->updPurchaseOrderItem($po_number, $item_no);
            $this->updPurchaseOrder($po_number);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully deleted');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function updPOItemAdditional_cost(Request $request)
    {
        $this->validate($request, [
            'cost_code' => 'required',
            'cost'=> 'required'
        ]);

        $po_number = trim($request->po_number);
        $cost_code = trim($request->cost_code);
        $item_no = trim($request->item_no);
      
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        
        try 
        {
            $upd_cost = POitem_additionalcosts::where('po_number', $po_number)->where('item_no', $item_no)->where('cost_code', $cost_code)
            ->update([
                'cost' => str_replace(',', '', $request->cost)
            ]);

            ##upd taxable amount and tax amnt 
            $taxes = POitem_taxes_on_costs::where('po_number', $po_number)->where('cost_code', $cost_code)
            ->where('item_no', $item_no)->get();

            if($taxes->count() > 0)
            {
               foreach ($taxes as $tax) 
               {
                    if($tax->add_deduct == 'A')
                    {
                        $tax_amount = (str_replace(',', '', $request->cost) * $tax->tax_rate)/100;
                    }
        
                    else
                    {
                        $tax_amount = ((str_replace(',', '', $request->cost) * $tax->tax_rate)/100) * -1;
                    }

                    $upd_tax = POitem_taxes_on_costs::where('po_number', $po_number)->where('cost_code', $cost_code)
                    ->update([
                        'taxable_amt' => str_replace(',', '', $request->cost),
                        'tax_amt' => $tax_amount
                    ]);
               }
            }

            $this->updPurchaseOrderItem($po_number, $item_no);
            $this->updPurchaseOrder($po_number);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully updated');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function deletePOItem(Request $request)
    {
        $po_number = trim($request->po_number);
        $item_no = trim($request->item_no);
       
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            ##delete additional costs
            $delete_costs = POitem_additionalcosts::where('po_number', $po_number)->where('item_no', $item_no)->delete();
           
            #delete tax on additional costs
            $delete_tax_costs = POitem_taxes_on_costs::where('po_number', $po_number)->where('item_no', $item_no)->delete();
            
            ##delete taxes costs
            $delete_taxes = POtaxes::where('po_number', $po_number)->where('item_no', $item_no)->delete();
           
            $del_item = POdetails::where('po_number', $po_number)->where('item_no', $item_no)->delete();
            
            ##reorder item numbers
            $items = POdetails::whereRaw("po_number = '".$po_number."' and  item_no >= '". $item_no."'")->get();
            
            foreach ($items as $key => $value) 
            {
                $items = POdetails::whereRaw("po_number = '".$po_number."' and  item_no = '". $value->item_no."'")
                ->update([
                    'item_no' => $value->item_no - 1
                ]);
                
                $upd_cost =  POitem_additionalcosts::whereRaw("po_number = '".$po_number."' and  item_no = '". $value->item_no."'")
                ->update([
                    'item_no' => $value->item_no - 1
                ]);

                $upd_taxes =   POtaxes::whereRaw("po_number = '".$po_number."' and  item_no = '". $value->item_no."'")
                ->update([
                    'item_no' => $value->item_no - 1
                ]);

                $upd_taxes =   POitem_taxes_on_costs::whereRaw("po_number = '".$po_number."' and  item_no = '". $value->item_no."'")
                ->update([
                    'item_no' => $value->item_no - 1
                ]);
            }
            $this->updPurchaseOrder($po_number);
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully deleted');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function storePOItem(Request $request)
    {
        // dd($request->all());
        $this->validate($request, [
            'control_account' => 'required',
            'item_cost'=> 'required',
            'total_cost'=> 'required',
            'item_code'=> 'required',
            'quantity'=> 'required',
            'unit_of_measure'=> 'required',
            'taxable'=> 'required',
            // 'description2' => 'required',
            'apply_discount' =>'required',
            'additional_cost' =>'required'
        ]);

        $po_number =  trim($request->po_number);
        $count =  POdetails::where('po_number', $po_number)->get();
        $item_no = count($count) + 1;
        $item_code = trim($request->item_code);
        $im_item = Im_Inventoryitems::where('item_code', $item_code)->get()[0];

        $po = POheader::where('po_number', $po_number)->get()[0];
        $vendor_id = $po->vendor_id;
        $total_cost = str_replace(',', '', $request->item_cost) * $request->quantity;
        $taxable_amount = str_replace(',', '', $request->taxable_amt);

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            if($request->additional_cost == 'Y' && ($po->additional_costs_flag == 'N' || $po->additional_costs_flag == null))
            {
                if(!empty($request->cost_code && $request->cost))
                {
                    for($i = 0; $i < count($request->cost_code); $i++) 
                    { 
                        ###taxes on additional costs at item level
                        $cost_type = Im_Additionalcosts::where('cost_code', $request->cost_code[$i])->get()[0];
                        
                        if($cost_type->taxable_flag == 'Y')
                        {
                            $tax_group_code = $cost_type->tax_group;
                            
                            $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();

                            foreach ($tax_types as $type) 
                            {
                                $tax_type = $type->tax_type;

                                $tax_code = $type->tax_code;
                                
                                $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                                
                                $rate = $gltax->tax_rate;

                                $taxable_amnt = str_replace(',', '', $request->cost[$i]);
                              
                                //trans type
                                $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
                
                                if($transtype)
                                {
                                    $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                                
                                    $add_deduct = $add_deduct_check->add_deduct;
                        
                                    if($add_deduct == 'A')
                                    {
                                        $tax_amount1 = ($taxable_amnt * $rate)/100;
                                    }
                
                                    if($add_deduct == 'D')
                                    {
                                        $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                                    }
                
                                    $save_tax = POitem_taxes_on_costs::create([
                                        'vendor_id' => $vendor_id,
                                        'cost_code' => $request->cost_code[$i],
                                        'item_no' => $item_no,
                                        'po_number' => $po_number,
                                        'tax_rate' => $rate,
                                        'taxable_amt' => $taxable_amnt,
                                        'add_deduct' => $add_deduct,
                                        'tax_code' => $tax_code,
                                        'tax_amt' => $tax_amount1,
                                        'created_by' => trim(auth()->id()),
                                        'created_at' => Carbon::now(),
                                        'item_code' => $item_code,
                                    ]);
                                }
                            }
                        }

                        $new_cost = POitem_additionalcosts::create([
                            'po_number' => $po_number,
                            'item_no' => $item_no,
                            'cost_code' => $request->cost_code[$i],
                            'cost' => str_replace(',', '', $request->cost[$i]),
                            'created_by' => trim(auth()->id()),
                            'created_at' => Carbon::now(),
                            'item_code' => $item_code,
                        ]);
                    }
                }
            }

            $percentage_disc = null;
            $discount = 0;
            $percentage_discount_flag = 'N';
            $discount_flag = 'N';

            ##start discounts
            if($po->discount_flag == 'N' || $po->discount_flag == null)
            {
                if($request->apply_discount == 'Y')
                {
                    if($request->discount == 'A' && $request->disc_amt != null)
                    {
                        $discount = str_replace(',', '', $request->disc_amt);
                        
                        $taxable_amnt = $total_cost - $discount; 

                        $percentage_discount_flag = 'N';

                        $discount_flag = 'Y';
                    }
        
                    else if($request->discount == 'P' && $request->percentage_disc != null)
                    {
                        $percentage_disc = floatval($request->percentage_disc);
        
                        $discount = ($percentage_disc * $total_cost) / 100; 

                        $taxable_amnt = $total_cost - $discount; 

                        $percentage_discount_flag = 'Y';

                        $discount_flag = 'Y';
                    }
                }
            }

            else if($po->discount_flag == 'Y' && $po->percentage_discount_flag == 'Y')
            {
                $discount = ($po->percentage_discount * str_replace(',', '', $request->taxable_amt)) / 100;
                $taxable_amnt = $taxable_amount; 
            }

            else if($po->discount_flag == 'Y' && ($po->percentage_discount_flag == 'N' || $po->percentage_discount_flag == null))
            {
                $discount = 0;
                $taxable_amnt = $taxable_amount;
            }

            else
            {
                $taxable_amnt = $taxable_amount;
            }
            ##end discounts

            if($request->taxable == 'Y')
            {
                $tax_group_code = $po->tax_group;
    
                $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
            
                foreach ($tax_types as $type) 
                {
                    $tax_type = $type->tax_type;

                    $tax_code = $type->tax_code;
                    
                    $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                    
                    $rate = $gltax->tax_rate;

                    //trans type
                    $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
    
                    if($transtype)
                    {
                        $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                    
                        $add_deduct = $add_deduct_check->add_deduct;
            
                        if($add_deduct == 'A')
                        {
                            $tax_amount1 = ($taxable_amnt * $rate)/100;
                        }
    
                        if($add_deduct == 'D')
                        {
                            $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                        }
    
                        $save_tax = POtaxes::create([
                            'vendor_id' => $vendor_id,
                            'item_no' => $item_no,
                            'po_number' => $po_number,
                            'tax_rate' => $rate,
                            'taxable_amt' => $taxable_amnt,
                            'add_deduct' => $add_deduct,
                            'tax_code' => $tax_code,
                            'tax_amt' => $tax_amount1,
                            'created_by' => trim(auth()->id()),
                            'created_at' => Carbon::now(),
                            'item_code' => $item_code,
                        ]);
                    }
                }
            }

            $i_taxes = POtaxes::where('po_number', $po_number)->where('item_no', $item_no)->sum('tax_amt');
            $i_tax_on_cost = POitem_taxes_on_costs::where('po_number', $po_number)->where('item_no', $item_no)->sum('tax_amt');
            $i_total_tax =  $i_taxes + $i_tax_on_cost;
    
            $i_add_costs = POitem_additionalcosts::where('po_number', $po_number)->where('item_no', $item_no)->sum('cost');
           
            $item_net =  $i_total_tax  + $i_add_costs +  $taxable_amnt;

            $create_item = POdetails::create([
                'vendor_id' => $vendor_id,
                'item_no' => $item_no,
                'po_number' =>  $po_number,
                'item_cost' => str_replace(',', '', $request->item_cost),
                'description' => $im_item->description,
                'total_cost' => $total_cost,
                'quantity' => $request->quantity,
                'unit_of_measure' => $request->unit_of_measure,
                'taxable' => $request->taxable,
                'created_by' => trim(auth()->id()),
                'created_at' => Carbon::now(),
                'control_account' => $request->control_account,
                'category_code'=> $request->category_code,
                'description2' => $request->description2,
                // 'discount_on_additional_costs' => dd($request->discount_on_additional_cost),

                'additional_cost' => $i_add_costs,
                'total_tax' => $i_total_tax,
                'net_amt' => $item_net,
                'TAX_ON_ADD_COSTS' => $i_tax_on_cost,
                'percentage_discount' =>  $percentage_disc,
                'taxable_amt' =>  $taxable_amnt,
                'discount_amt' =>  $discount,
                'tax_on_item' =>  $i_taxes,
                'percentage_discount_flag' => $percentage_discount_flag, 
                'discount_flag' => $discount_flag,
                'item_code' => $item_code,
            ]);

            ##upd PO
           $this->updPurchaseOrder($po_number);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully Added');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function getpo_head_details(Request $request)
    {
        $po = POheader::where('po_number', trim($request->po_number))->get()[0];

        $glhs = Nlparams::where('prid', 'GLH')->where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

        $tax_groups = Gltaxgroups::all();

        $currencies = Currency::all();

        return ['po'=> $po, 'glhs' => $glhs, 'tax_groups'=> $tax_groups, 'currencies' => $currencies];
    }

    public function updPOItem(Request $request)
    {
        // dd($request->all());
        $this->validate($request, [
            'control_account' => 'required',
            'po_number'=> 'required',
            'item_cost'=> 'required',
            'total_cost'=> 'required',
            // 'item_code'=> 'required',
            'quantity'=> 'required',
            'unit_of_measure'=> 'required',
            'taxable'=> 'required',
            // 'description2' => 'required',
            //'edit_apply_discount' => 'required'
        ]);

        $po_number =  trim($request->po_number);
        $item_no = trim($request->item_no);
        // $im_item = Im_Inventoryitems::where('item_code', $item_code)->get()[0];
        $po = POheader::where('po_number', $po_number)->get()[0];
        $vendor_id = $po->vendor_id;

        $item = POdetails::where('po_number', $po_number)->where('item_no', $item_no)->get()[0];
       
        $total_cost = str_replace(',', '', $request->item_cost) * $request->quantity;

        $taxable_amount = str_replace(',', '', $request->edit_taxable_amt);
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            if($request->taxable == 'Y')
            {
                ##delete taxes then save
                $del_item_tax = POtaxes::where('po_number', $po_number)->where('item_no', $item_no)->delete();
                  
                $tax_group_code = $po->tax_group;
    
                $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
               
                foreach ($tax_types as $type) 
                {
                    $tax_type = $type->tax_type;

                    $tax_code = $type->tax_code;
                    
                    $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                    
                    $rate = $gltax->tax_rate;

                    $taxable_amnt = $taxable_amount;
                    //trans type
                    $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
    
                    if($transtype)
                    {
                        $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                    
                        $add_deduct = $add_deduct_check->add_deduct;
            
                        if($add_deduct == 'A')
                        {
                            $tax_amount1 = ($taxable_amnt * $rate)/100;
                        }
    
                        if($add_deduct == 'D')
                        {
                            $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                        }

                        $save_tax =POtaxes::create([
                            'vendor_id' => $vendor_id,
                            'item_no' => $item_no,
                            'po_number' => $po_number,
                            'tax_rate' => $rate,
                            'taxable_amt' => $taxable_amnt,
                            'add_deduct' => $add_deduct,
                            'tax_code' => $tax_code,
                            'tax_amt' => $tax_amount1,
                            'created_by' => trim(auth()->id()),
                            'created_at' => Carbon::now(),
                        ]);
                    }
                }
            }

            else if($request->taxable == 'N')
            {
                ##delete taxes 
                $del_item_tax = POtaxes::where('po_number', $po_number)->where('item_no', $item_no)->delete();
            }

            $discount_flag = $item->discount_flag;
            $percentage_discount_flag = $item->percentage_discount_flag;
            
            ##start discounts
            if($po->discount_flag == 'N' || $po->discount_flag == null)
            {
                if($request->edit_apply_discount == 'Y')
                {
                    if($request->edit_discount == 'A' && $request->disc_amt != null)
                    {
                        $discount = str_replace(',', '', $request->disc_amt);
                        
                        $taxable_amnt = $total_cost - $discount; 

                        $percentage_discount_flag = 'N';

                        $discount_flag = 'Y';
                    }
        
                    else if($request->edit_discount == 'P' && $request->percentage_disc != null)
                    {
                        $percentage_disc = floatval($request->percentage_disc);
        
                        $discount = ($percentage_disc * $total_cost) / 100; 

                        $taxable_amnt = $total_cost - $discount; 

                        $percentage_discount_flag = 'Y';

                        $discount_flag = 'Y';
                    }
                }
            }

            else
            {
                $taxable_amnt = $taxable_amount;
            }
           
            ##end discounts

            ##upd item
            $i_taxes = POtaxes::where('po_number', $po_number)->where('item_no', $item_no)->sum('tax_amt');
            $i_tax_on_cost = POitem_taxes_on_costs::where('po_number', $po_number)->where('item_no', $item_no)->sum('tax_amt');
            $i_total_tax =  $i_taxes + $i_tax_on_cost;
    
            $i_add_costs = POitem_additionalcosts::where('po_number', $po_number)->where('item_no', $item_no)->sum('cost');
           
            $item_net =  $i_total_tax  + $i_add_costs +  $taxable_amnt;

            $upd_item = POdetails::where('po_number', $po_number)->where('item_no', $item_no)
            ->update([
                'item_cost' => str_replace(',', '', $request->item_cost),
                // 'description' => $im_item->description,
                'total_cost' => $total_cost,
                'quantity' => $request->quantity,
                'unit_of_measure' => $request->unit_of_measure,
                'taxable' => $request->taxable,
                'taxable_amt' => $total_cost,
                'updated_by' => trim(auth()->id()),
                'dola' => Carbon::now(),
                'control_account' => $request->control_account,
                'description2' => $request->description2,
                'additional_cost' => $i_add_costs,
                'total_tax' => $i_total_tax,
                'net_amt' => $item_net,
                'TAX_ON_ADD_COSTS' => $i_tax_on_cost,
                'percentage_discount' =>  $percentage_disc,
                'taxable_amt' =>  $taxable_amnt,
                'discount_amt' =>  $discount,
                'tax_on_item' =>  $i_taxes,
                'percentage_discount_flag' => $percentage_discount_flag, 
                'discount_flag' => $discount_flag,
                'QUANTITY_RECEIPTED' => 0,
                'QUANTITY_UNRECEIPTED' => $request->quantity,
            ]);

            $this->updPurchaseOrder($po_number);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully updated');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function cancelPO(Request $request)
    {
        $this->validate($request, [
            'reason_cancelled' => 'required|min:5'
        ]);

        $po_number = trim($request->po_number);
        $po = POheader::where('po_number', $po_number)->get()[0];
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        
        try 
        {
            if($po->cancelled == 'Y')
            {
                Session::Flash('error', 'Failed, purchase order has already been cancelled');
                $result = array("status" => -1);
                return $result;
            }

            // else if($po->approved == 'Y')
            // {
            //     Session::Flash('error', 'Failed,  purchase order has already has already been approved');
            //     $result = array("status" => -2);
            //     return $result;
            // }

            else
            {
                $upd_po = POheader::where('po_number', $po_number)
                ->update([
                    'cancelled' => 'Y',
                    'cancelled_by' => auth()->id(),
                    'cancelled_date' => Carbon::now(),
                    'reason_cancelled' => $request->reason_cancelled
                ]);

                $update_req = PoReqHeader::where('req_no', $po->req_no)
                ->update([
                    'po_created_flag' => 'N',
                    'po_number' => null
                ]);
            }

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Purchase order successfully cancelled');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function updPurchaseOrderItem($po_number, $item_no)
    {
        $item = POdetails::where('po_number', $po_number)->where('item_no', $item_no)->get()[0];
        $po = POheader::where('po_number', $po_number)->get()[0];

        $taxable_amnt = $item->taxable_amt;
      
        $i_taxes = POtaxes::where('po_number', $po_number)->where('item_no', $item_no)->sum('tax_amt');
        $i_tax_on_cost = POitem_taxes_on_costs::where('po_number', $po_number)->where('item_no', $item_no)->sum('tax_amt');
        $i_total_tax =  $i_taxes + $i_tax_on_cost;

        $i_add_costs = POitem_additionalcosts::where('po_number', $po_number)->where('item_no', $item_no)->sum('cost');
       
        $item_net =  $i_total_tax  + $i_add_costs +  $taxable_amnt;
        // dd($i_add_costs, $i_taxes, $i_tax_on_cost, $i_total_tax, $item_net);
        $upd_item = POdetails::where('po_number', $po_number)->where('item_no', $item_no)
        ->update([
            'additional_cost' => $i_add_costs,
            'total_tax' => $i_total_tax,
            'net_amt' => $item_net,
            'TAX_ON_ADD_COSTS' => $i_tax_on_cost,
            'tax_on_item' =>  $i_taxes
        ]);
    }

    public function updPurchaseOrder($po_number)
    {
        $po = POheader::where('po_number', $po_number)->get()[0];
        ##upd PurchaseOrder
        $total_tax_po = POtaxes::where('po_number', $po_number)->sum('tax_amt');
        $total_tax_po1 = POheader_taxes::where('po_number', $po_number)->sum('tax_amt');
        $total_tax_po2 = POitem_taxes_on_costs::where('po_number', $po_number)->sum('tax_amt');

        $taxes_on_add_costs = $total_tax_po1 + $total_tax_po2;

        $total_po_taxes = $total_tax_po + $total_tax_po1 + $total_tax_po2;

        $additional_costs_po1 = POhead_additionalcosts::where('po_number', $po_number)->sum('cost');
        $additional_costs_po = POitem_additionalcosts::where('po_number', $po_number)->sum('cost');

        $total_po_add_costs = $additional_costs_po + $additional_costs_po1;

        $po_net = POdetails::where('po_number', $po_number)->sum('net_amt');

        $po_amt = $po_net + $total_tax_po1 + $additional_costs_po1;

        $items_discount = POdetails::where('po_number', $po_number)->sum('discount_amt');


        if($po->discount_flag == 'Y' && $po->percentage_discount_flag == 'Y')
        {
            $total_po_discount = $items_discount;
            
            $total_po_net = ($po_net + $total_tax_po1 + $additional_costs_po1) - $total_po_discount;
        }

        else if($po->discount_flag == 'Y' && ($po->percentage_discount_flag == 'N' || $po->percentage_discount_flag == null))
        {
            $total_po_discount = $po->discount_amt;
            $total_po_net = ($po_net + $total_tax_po1 + $additional_costs_po1) - $total_po_discount;
        }

        else
        {
            $total_po_discount = $items_discount;
            $total_po_net = ($po_net + $total_tax_po1 + $additional_costs_po1);
        }
        // dd($total_po_add_costs, $total_po_taxes, $total_po_net,$taxes_on_add_costs, $total_po_discount,$po_amt, $total_tax_po );

        $upd_po = POheader::where('po_number', $po_number)
        ->update([
            'additional_cost' => $total_po_add_costs,
            'total_tax' => $total_po_taxes,
            'net_amt' => $total_po_net,
            'TAX_ON_ADD_COSTS' => $taxes_on_add_costs,
            'total_discount' => $total_po_discount,
            'po_amt' => $po_amt,
            'tax_on_items' => $total_tax_po
        ]);
    }

    public function createPoFromReq($req, $po_number, $request)
    {
        // dd($request);
        $total_items_quantity = PO_reqdetails::where('req_no', $req->req_no)->sum('quantity');

        $new_po = POheader::create([
            'po_number' => $po_number,
            'vendor_id'=>$req->vendor_id,
            'req_no' => $req->req_no,
            'po_date' => $request->po_date,
            'description' => $request->description,
            'tax_group' => $req->tax_group,
            'currency_code' => $req->currency_code,
            'currency_rate' => $req->currency_rate,
            'control_account' => $req->control_account,
            'cancelled' => 'N',
            //'status' => 'P',
            'process_code' => '3',
            'no_of_items' => $req->no_of_items,
            'created_by' => auth()->id(),
            'created_at' => Carbon::now(),
            'discount_flag' =>  $req->discount_flag,
            'additional_cost' => $req->additional_cost,
            'total_tax' => $req->total_tax,
            'net_amt' => $req->net_amt,
            'additional_costs_flag' => $req->additional_costs_flag,
            'TAX_ON_ADD_COSTS' => $req->additional_cost_tax,
            'discount_amt' => $req->discount_amt,
            'percentage_discount' => $req->percentage_discount,
            'tax_on_items' => $req->tax_on_items,
            'percentage_discount_flag'=>$req->percentage_discount_flag,
            'discount_level' => ($req->discount_level == 'R') ? ('P') : ('I'),
            'TOTAL_DISCOUNT' => $req->total_discount,
            'po_amt'=> $req->req_amt,
            'po_from_req_flag' => $request->po_from_req_flag,
            'doc_type' => $doctype,
            'TOTAL_ITEMS_QUANTITY' => $total_items_quantity,
            'status' =>  '001'
        ]);
        
        ##additional costs on po header
        if($req->additional_costs_flag == 'Y')
        {
            $header_costs = PO_reqhead_costs::where('req_no', $req->req_no)->get();
            if(count($header_costs) > 0)
            {
                foreach ($header_costs as $cost) 
                {
                    $taxes_on_costs = PO_reqheadtaxes::where('cost_code', $cost->cost_code)
                    ->where('req_no', $req->req_no)->get();
                    if(count($taxes_on_costs) > 0)
                    {
                        foreach ($taxes_on_costs as $tax) 
                        {
                            $save_tax = POheader_taxes::create([
                                'vendor_id' => $vendor_id,
                                'po_number' => $po_number,
                                'tax_rate' => $tax->tax_rate,
                                'taxable_amt' => $tax->taxable_amt,
                                'add_deduct' => $tax->add_deduct,
                                'tax_code' => $tax->tax_code,
                                'tax_amt' => $tax->tax_amt,
                                'created_by' => trim(auth()->id()),
                                'created_at' => Carbon::now(),
                                'cost_code' => $tax->cost_code
                            ]);
                        }
                    }

                    $new_cost = new POhead_additionalcosts;
                    $new_cost->po_number = $po_number;
                    $new_cost->cost_code = $cost->cost_code;
                    $new_cost->cost = $cost->cost;
                    $new_cost->created_by = trim(auth()->id());
                    $new_cost->created_at = Carbon::now();
                    $new_cost->save();
                }
            }
        }
        ##end additional costs

        ##items
        $items = PO_reqdetails::where('req_no', $req->req_no)->get();
        if(count($items) > 0)
        {
            foreach ($items as $item) 
            {
                ##additional costs on items
                $item_costs = PO_reqadditional_costs::where('req_no', $item->req_no)
                ->where('item_code', $item->item_code)->get();
               
                if(count($item_costs) > 0)
                {
                    foreach ($item_costs as $i_cost) 
                    {
                        ##taxes on additional costs
                        $taxes_on_i_costs = PO_reqitem_addcosts_taxes::where('req_no', $i_cost->req_no)
                        ->where('item_code', $i_cost->item_code)->where('cost_code',  $i_cost->cost_code)->get();
                       
                        if(count($taxes_on_i_costs) > 0)
                        {
                            foreach ($taxes_on_i_costs as $taxes_on_i) 
                            {
                                $new_tx = POitem_taxes_on_costs::create([
                                    'vendor_id' => $taxes_on_i->vendor_id,
                                    'cost_code' => $taxes_on_i->cost_code,
                                    'item_no' => $taxes_on_i->item_no,
                                    'po_number' => $po_number,
                                    'tax_rate' => $taxes_on_i->tax_rate,
                                    'taxable_amt' => $taxes_on_i->taxable_amt,
                                    'add_deduct' => $taxes_on_i->add_deduct,
                                    'tax_code' => $taxes_on_i->tax_code,
                                    'tax_amt' => $taxes_on_i->tax_amt,
                                    'created_by' => trim(auth()->id()),
                                    'created_at' => Carbon::now(),
                                    'item_code' => $i_cost->item_code
                                ]);
                            }
                        }

                        $new_cost1 = new POitem_additionalcosts;
                        $new_cost1->po_number = $po_number;
                        $new_cost1->item_no = $i_cost->item_no;
                        $new_cost1->cost_code = $i_cost->cost_code;
                        $new_cost1->cost = $i_cost->cost;
                        $new_cost1->created_by = trim(auth()->id());
                        $new_cost1->created_at = Carbon::now();
                        $new_cost1->item_code = $item->item_code;
                        $new_cost1->save();
                    }
                }
                ##end additional costs on items

                ##taxes on item
                $item_taxes = poReqTax::where('req_no', $item->req_no)
                ->where('item_code', $item->item_code)->get();
               
                if(count($item_taxes) > 0)
                {
                    foreach ($item_taxes as $tx) 
                    {
                        $save = POtaxes::create([
                            'vendor_id' => $tx->vendor_id,
                            'item_no' => $tx->item_no,
                            'po_number' => $po_number,
                            'tax_rate' => $tx->tax_rate,
                            'taxable_amt' => $tx->taxable_amt,
                            'add_deduct' => $tx->add_deduct,
                            'tax_code' => $tx->tax_code,
                            'tax_amt' => $tx->tax_amt,
                            'created_by' => trim(auth()->id()),
                            'created_at' => Carbon::now(),
                            'item_code' => $item->item_code
                        ]);

                    }
                }

                ##save item
                $store_item = POdetails::create([
                    'vendor_id' => $item->vendor_id,
                    'item_no' => $item->item_no,
                    'po_number' =>  $po_number,
                    'item_cost' =>  $item->item_cost,
                    'description' => $item->description,
                    'total_cost' => $item->total_cost,
                    'quantity' => $item->quantity,
                    'unit_of_measure' => $item->unit_of_measure,
                    'taxable' => $item->taxable,
                    'taxable_amt' => $item->taxable_amt,
                    'created_by' => trim(auth()->id()),
                    'created_at' => Carbon::now(),
                    'control_account' => $item->control_account,
                    'description2' => $item->description2,
                    'additional_cost' => $item->additional_cost,
                    'total_tax' => $item->total_tax,
                    'net_amt' => $item->net_amt,
                    'TAX_ON_ADD_COSTS' => $item->additional_cost_tax,
                    'percentage_discount' =>  $item->percentage_discount,
                    'taxable_amt' =>  $item->taxable_amt,
                    'discount_amt' =>  $item->discount_amt,
                    'tax_on_item' => $item->tax_on_item,
                    'percentage_discount_flag' => $item->percentage_discount_flag, 
                    'discount_flag' => $item->discount_flag,
                    'category_code'=> $item->category_code,
                    'QUANTITY_RECEIPTED' => 0,
                    'QUANTITY_UNRECEIPTED' => $item->quantity,
                    'item_code' => $item->item_code
                ]);
                // dd($store_item);
            }
        }
        ##end items
    }

    //!----------   requisistion approval----------------
    public function reqApproval(Request $request){
        // dd($request->all());
        $new_approval = $request->appr;
        $req_no = trim($request->req_no);
        $approval_process = trim($request->approval_process);
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

		try{
            $apprObj = new ApprovalsMgt();
            $total_records = Approvals::count() + 1;
            $formatted_code = sprintf("%02d", $total_records);
			
			if($request->re_escalate == 'Y'){
				$apprObj->re_escalate_approval($request);
				Session::flash('success','Successfully re-escalated approval');
			}
			else{
				
                if($approval_process == 'REQ'){
                    $type = 'REQ';
                }
                if($approval_process == 'ITEMREQ'){
                    $type = 'IRE';
                }

                if($approval_process == 'POINV'){
                    $type = 'INV';
                    $invoice_no = $request->invoice_no;
                    $vendor_id = $request->vendor_id;
                    $po_number = $request->po_number;
                }

				$approval = new Approvals;
				$approval->approval_id = $formatted_code;
				$approval->process_code = $request->process_code;
				$approval->req_no = $req_no;
				$approval->type = $type;
				$approval->description = $request->appr_notes;
				$approval->user_id = Auth::user()->user_id;
				$approval->dola = Carbon::now();
				$approval->date_created = Carbon::now();
                $approval->invoice_no = $invoice_no;
                $approval->vendor_id = $vendor_id;
                $approval->po_number = $po_number;
				$approval->save();
               

				for($i=0;$i < count($new_approval);$i++){

					$flow = new Approval_flow;
					$flow->approval_id = $approval->approval_id;
					$flow->approval_level = $new_approval[$i]['hierarchy_id'];
					$flow->approver_id = $new_approval[$i]['approver'];
					$flow->status = 'P';
					$flow->dola = Carbon::now();

					//set next level details
					$start = null;
					$final = null;

                    if($type = 'INV')
                    {
                        if($i == 0){
                            $start = 'Y';
    
                            // notify the first approver
                            $category = 'PURCHASE ORDER INVOICE APPROVAL';
                            $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                            $email = $approver->email;
                            $name = Auth::user()->name;
                            $message = "Kindly Approve this invoice: ".$invoice_no."";
    
                            $apprObj->send_email($category,$email,$message,$name);
                        }
                    }

                    else
                    {
                        if($i == 0){
                            $start = 'Y';
    
                            // notify the first approver
                            $category = 'REQUISITION APPROVAL';
                            $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                            $email = $approver->email;
                            $name = Auth::user()->name;
                            $message = "Kindly Approve this Requisition: ".$req_no."";
    
                            $apprObj->send_email($category,$email,$message,$name);
                        }
                    }
					
					
					
					if(($i +1) == count($new_approval)){
						$final = 'Y';
						$next_level = null;
						$next_approver = null;
					}
					else{
						$next_level = $new_approval[$i+1]['hierarchy_id'];
						$next_approver = $new_approval[$i+1]['approver'];
					}
					$flow->start_approval = $start;
					$flow->end_approval = $final;
					$flow->next_approval_level = $next_level;
					$flow->next_approver_id = $next_approver;

					$flow->save();
					
				}

				Session::flash('success','Successfully raised approval');
			}
			DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
			

			return redirect()->back();
		}
		catch(\Throwable $e){
			DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
			Session::flash('error','Failed to raise an approval');

			return redirect()->back();
		}
    }

    public function fetch_approval_dtl(Request $request){
		$appr_id = $request->approval_id;
		$curr_user = Auth::user()->user_name;
		$approvers = array();
      
		try{
	
			$appr_dtl = Approvals::with('approval_flow')->where('approval_id',$appr_id)->first();

			foreach($appr_dtl->approval_flow as $approver){
				if($approver->start_approval == 'Y'){

					$resp = [
						'msg' => 'Success',
						'prev_approver' => null,
						'approval_status' => $approver->status,
						'code' => 200,
					];
				}
				else{
					$prev_approver = $approver->where('next_approval_level',$approver->approval_level)->first();
	
					if($prev_approver->status == 'R'){
						throw new \Exception("Approval was Rejected");
					}
					else{
						$resp = [
							'msg' => 'Success',
							'prev_approver' => $prev_approver,
							'approval_status' => $approver->status,
							'code' => 200,
						];	
					}
				}
			}

			return response()->json($resp, 200);

		}
		catch(\Throwable $e){
			$resp = [
				'msg' => $e->getMessage(),
				'prev_status' => null,
				'approval_status' => null,
				'code' => 400,
			];

			return response()->json($resp, 400);
		}

    }
    
    public function update_approval(Request $request){
        $req_no = trim($request->req_no);
        $approval_process = trim($request->approval_process);
		$data = [
			'approval_id' => $request->approval_id,
			'level_id' => $request->level_id,
			'status' => $request->status,
			'approval_process' => $request->approval_process,
		];
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

		try{

			$apprObj = new ApprovalsMgt();
			$resp = $apprObj->change_approval_status($data);
	
			DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
			return redirect()->back()->with('success','Approval has been Succesfully processed');
		}
		catch(\Throwable $e){
			DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            dd($e);
			return redirect()->back()->with('error','Failed to process Approval');
		}

    }
    //!----------   requisistion approval----------------

    //!----------po Approval ----------------------------
    public function poApproval(Request $request){
        $new_approval = $request->appr;
        $po_number = trim($request->po_number);
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

		try{
            $apprObj = new ApprovalsPo();
            $total_records = Approvals::count() + 1;
            $formatted_code = sprintf("%02d", $total_records);
			
			if($request->re_escalate == 'Y'){
				$apprObj->re_escalate_approval($request);
				Session::flash('success','Successfully re-escalated approval');
			}
			else{
				

				$approval = new Approvals;
				$approval->approval_id = $formatted_code;
				$approval->process_code = $request->process_code;
				$approval->po_number = $po_number;
				$approval->type = 'PO';
				$approval->description = $request->appr_notes;
				$approval->user_id = Auth::user()->user_id;
				$approval->dola = Carbon::now();
				$approval->date_created = Carbon::now();
				$approval->save();

				for($i=0;$i < count($new_approval);$i++){

					$flow = new Approval_flow;
					$flow->approval_id = $approval->approval_id;
					$flow->approval_level = $new_approval[$i]['hierarchy_id'];
					$flow->approver_id = $new_approval[$i]['approver'];
					$flow->status = 'P';
					$flow->dola = Carbon::now();

					//set next level details
					$start = null;
					$final = null;
					
					if($i == 0){
						$start = 'Y';

						// notify the first approver
						$category = 'PURCHASE ORDER APPROVAL';
						$approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
						$email = $approver->email;
						$name = Auth::user()->name;
						$message = "Kindly Approve this Purchase Order: ".$po_number."";

						$apprObj->send_email($category,$email,$message,$name);
					}
					
					if(($i +1) == count($new_approval)){
						$final = 'Y';
						$next_level = null;
						$next_approver = null;
					}
					else{
						$next_level = $new_approval[$i+1]['hierarchy_id'];
						$next_approver = $new_approval[$i+1]['approver'];
					}
					$flow->start_approval = $start;
					$flow->end_approval = $final;
					$flow->next_approval_level = $next_level;
					$flow->next_approver_id = $next_approver;

					$flow->save();
					
				}

				Session::flash('success','Successfully raised approval');
			}
			DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
			

			return redirect()->back();
		}
		catch(\Throwable $e){
			DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
			Session::flash('error','Failed to raise an approval');
			return redirect()->back();
		}
    }

    public function updatePoApproval(Request $request){
		$data = [
			'approval_id' => $request->approval_id,
			'level_id' => $request->level_id,
			'status' => $request->status,
		];
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

		try{

			$apprObj = new ApprovalsPo();
			$resp = $apprObj->change_approval_status($data);
	
			DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
			return redirect()->back()->with('success','Approval has been Succesfully processed');
		}
		catch(\Throwable $e){
			// dd($e);
			DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
			return redirect()->back()->with('error','Failed to process Approval');
		}

    }
    //!----------po Approval ----------------------------
    


    public function removePOItemTax(Request $request)
    {
        $po_number = trim($request->po_number);
        $item_no = trim($request->item_no);
        $tax_code = trim($request->tax_code);
    
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $del_tax = POtaxes::where('po_number', $po_number)->where('item_no', $item_no)
            ->where('tax_code', $tax_code)->delete();
            
            $this->updPurchaseOrderItem($po_number, $item_no);
            $this->updPurchaseOrder($po_number);
           
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully removed');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function updPOItemTax(Request $request)
    {
        $this->validate($request, [
            'tax_rate'=> 'required',
        ]);

        $po_number = trim($request->po_number);
        $item_no = trim($request->item_no);
        $tax_code = trim($request->tax_code);
        
        $taxable_amount = str_replace(',', '', $request->taxable_amount);
        $tax_rate = $request->tax_rate;

        if($request->add_deduct == 'ADDITION')
        {
            $tax_amount = ($taxable_amount * $tax_rate)/100;
        }

        else
        {
            $tax_amount = (($taxable_amount * $tax_rate)/100) * -1;
        }

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $upd_tax = POtaxes::where('po_number', $po_number)->where('item_no', $item_no)
            ->where('tax_code', $tax_code)->update([
                'tax_amt' => $tax_amount,
                'tax_rate' => $tax_rate,
                'updated_by' => trim(auth()->id()),
                'dola' => Carbon::now()
            ]);
            
            $this->updPurchaseOrderItem($po_number, $item_no);
            $this->updPurchaseOrder($po_number);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Tax successfully updated');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function storePOItemTax(Request $request)
    {
        // dd($request->all());
        $this->validate($request, [
            'tax_code'=> 'required',
            'tax_type'=> 'required',
            'taxable_amount'=> 'required',
            'tax_rate'=> 'required',
        ]);

        $po_number = trim($request->po_number);
        $item_no = trim($request->item_no);
        $tax_code = trim($request->tax_code);

        $po = POheader::where('po_number', $po_number)->get()[0];

        $tax_type = trim($request->tax_type);
        $taxable_amount = str_replace(',', '', $request->taxable_amount);
        $tax_rate = $request->tax_rate;
        $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->get()[0];
       
        if($add_deduct_check->add_deduct == 'A')
        {
            $tax_amount = ($taxable_amount * $tax_rate)/100;
        }

        if($add_deduct_check->add_deduct == 'D')
        {
            $tax_amount = (($taxable_amount * $tax_rate)/100) * (-1);
        }
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $save_tax =POtaxes::create([
                'vendor_id' => $po->vendor_id,
                'item_no' => $item_no,
                'po_number' => $po_number,
                'tax_code' => $tax_code,
                'tax_amt' => $tax_amount,
                'tax_rate' => $tax_rate,
                'taxable_amt' => $taxable_amount,
                'add_deduct' => $add_deduct_check->add_deduct,
                'created_by' => trim(auth()->id()),
                'created_at' => Carbon::now()
            ]);

            $this->updPurchaseOrderItem($po_number, $item_no);
            $this->updPurchaseOrder($po_number);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Tax successfully updated');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    function genPoNumber()
    {
        $purchase_order = Doctype::where('doc_type', 'POR')->get()[0];
        $dtrans = $purchase_order->serial_no;
        return $dtrans;
    }

    function genReqNo()
    {
        $req = Doctype::where('doc_type', 'REQ')->get()[0];
        $dtrans = $req->serial_no;
        return $dtrans;
    }

   ##increment dtrans number
   public function updDoctypeSerial()
   {
       $new_serial = Doctype::where('doc_type', 'POR')->increment('serial_no', 1);
       return $new_serial;

   }

   ##increment dtrans number
   public function updDoctypeReqSerial()
   {
       $new_serial = Doctype::where('doc_type', 'REQ')->increment('serial_no', 1);
       return $new_serial;

   }

    public function po_recs()
    {
        $dat = array(
            'main' => 'PO', 
            'module' => 'Purchase Order Receipts',
        );
        return view::make('gl.po.po_rec_headers', compact('dat'));
    }

    public function recsDat(Request $request)
    {
        $headers = PORec_header::all();

        return Datatables::Of($headers)
            ->addColumn('action', function ($headers) {
                return '<a class="btn btn-xs" id="edit_btn"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('rec_date', function ($header){
                $date = new DateTime($header->rec_date);
                $format_date =  $date->format('d/m/Y');
                return $format_date;
            })
            ->make(true);
    }

    public function getvendor_pos(Request $request)
    {
        return POheader::where('vendor_id', trim($request->vendor_id))->where('approved', 'Y')->where('cancelled', '!=', 'Y')
        ->whereNotIn('status', ['006', '001', '002', '005'])->get();
    }

    public function get_purchaseOrder_details(Request $request)
    {
        if($request->has('po_invoice_flag') == 'Y')
        {
            return POheader::where('po_number', trim($request->po_number))->get()[0];
        }

        else
        {
            $po = POheader::where('po_number', trim($request->po_number))->where('vendor_id', $request->vendor_id)->get()[0];

            $currencies = Currency::all(); 

            $currency =  Currency::where('currency_code', $po->currency_code)->get()[0];
        
            $rate_flag = 'Y';

            if($currency->base_currency == 'Y')
            {
                $currency_rate = 1;
            }

            else
            {
                $curr_rate = DB::table('currrate')->where('currency_code', 1)
                ->where('rate_date', $request->rec_date)->get()[0];

                if($curr_rate)
                {
                    $currency_rate = $curr_rate->currency_rate;
                }

                else
                {
                    $rate_flag = 'N';
                }
            }

            return ['po'=> $po, 'currency_rate' => $currency_rate, 'rate_flag'=> $rate_flag, 'currencies' => $currencies];
        }
    }

    public function storePORec(Request $request)
    {
        // dd($request->all());
        $this -> validate($request, [
            'rec_date' => 'required',
            'ponumber' => 'required',
            'currency_code' => 'required',
            'currency_rate' => 'required',
            'delivered_by' => 'required',
            'vendorid'  => 'required',
            'offcd' => 'required'
        ]);

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        $today = Carbon::today();
        $year = $today->format('Y');

        try 
        {
            $date = Carbon::now();
            $year = $date->year;
    
            $initials = 'POREC';
    
            $total_records = PORec_header::count() + 1;
    
            $formatted = sprintf("%04d", $total_records);
    
            $rec_number = strtoupper($initials.$year.$formatted);

            $vendor_id = $request->vendorid;
            
            if($request->receipt_all == 'Y')
            {
                $po = POheader::where('po_number', $request->ponumber)->get()[0];
                if (count($po) > 0) 
                {
                    ##items
                    $items = POdetails::where('po_number', $po->po_number)->get();
                    if (count($items) > 0) 
                    {
                        foreach ($items as $item) 
                        {
                            if($item->quantity_receipted != $item->quantity)
                            {
                                $rec_item = PORec_details::create([
                                    'vendor_id' => $item->vendor_id,
                                    'item_no' => $item->item_no,
                                    'rec_no' =>  $rec_number,
                                    'item_cost' =>  $item->item_cost,
                                    'description' => $item->description,
                                    'total_cost' => $item->total_cost,
                                    'unit_of_measure' => $item->unit_of_measure,
                                    'created_by' => trim(auth()->id()),
                                    'created_at' => Carbon::now(),
                                    'control_account' => $item->control_account,
                                    'description2' => $item->description2,
                                    'quantity_received' => $item->quantity_unreceipted,
                                    'po_number' => $po->po_number,
                                    'item_code' => $item->item_code
                                ]);
                                $this->updInventoryItem($item->item_code, $po->po_number, $vendor_id, null, null);
                            }
                        }
                    }
                }

                $rec_amt = PORec_details::where('rec_no', $rec_number)->sum('total_cost');

                $no_of_items = PORec_details::where('rec_no', $rec_number)->count();

                $new_rec = PORec_header::create([
                    'rec_no' =>  $rec_number,
                    'po_number' => $request->ponumber,
                    'vendor_id'=>$vendor_id,
                    'rec_date' => $request->rec_date,
                    'currency_code' => $request->currency_code,
                    'currency_rate' => $request->currency_rate,
                    'control_account' => $req->control_account,
                    'no_of_items' => $no_of_items,
                    'created_by' => auth()->id(),
                    'created_at' => Carbon::now(),
                    'rec_amt'=> $rec_amt,
                    // 'rec_net'=> $rec_amt,
                    'doc_type' => $doctype,
                    'delivered_by' => $request->delivered_by,
                    'delivery_note' => $request->delivery_note,
                    'packing_slip_no' => $request->packing_slip_no,
                    'offcd' => $request->offcd
                ]);
            }

            else
            {
                if($request->has('item_code'))
                {
                    for($i = 0; $i < count($request->item_code); $i++)
                    {
                        $find_item = POdetails::where('po_number', $request->ponumber)->where('vendor_id', $vendor_id)
                        ->where('item_code', $request->item_code[$i])->get()[0];

                        if($find_item)
                        {
                            $rec_item = PORec_details::create([
                                'vendor_id' => $find_item->vendor_id,
                                'item_no' => $request->item_no[$i],
                                'rec_no' =>  $rec_number,
                                'item_cost' =>  str_replace(',', '', $request->item_cost[$i]),
                                'description' => $find_item->description,
                                'total_cost' => str_replace(',', '', $request->item_cost[$i]) * $request->quantity_received[$i],
                                // 'quantity' => $find_item->quantity,
                                'unit_of_measure' => $find_item->unit_of_measure,
                                'created_by' => trim(auth()->id()),
                                'created_at' => Carbon::now(),
                                'control_account' => $find_item->control_account,
                                'description2' => $find_item->description2,
                                'quantity_received' => $request->quantity_received[$i],
                                'po_number' => $request->ponumber,
                                'item_code' => $request->item_code[$i]
                            ]);
                        }

                        ##upd po item
                        $quantity_receipted = PORec_details::where('po_number', $request->ponumber)->where('vendor_id', $vendor_id)
                        ->where('item_code', $request->item_code[$i])->sum('quantity_received');

                        $upd_po_item = POdetails::where('po_number', $request->ponumber)->where('vendor_id', $vendor_id)
                        ->where('item_code', $request->item_code[$i])->update([
                            'quantity_unreceipted' => $find_item->quantity - $quantity_receipted,
                            'quantity_receipted' => $quantity_receipted
                        ]);


                        $im_item = Im_Inventoryitems::where('item_code', $request->item_code[$i])->get()[0];
                        
                        $upd_im_item = Im_Inventoryitems::where('item_code', $request->item_code[$i])->update([
                            'received_qty' => $im_item->received_qty + $quantity_receipted,
                            'total_in_stock' => $im_item->total_in_stock + $quantity_receipted
                        ]);
                    }
                }

                $no_of_items = PORec_details::where('rec_no', $rec_number)->count();

                $rec_amt = PORec_details::where('rec_no', $rec_number)->sum('total_cost');
            
                $new_rec = PORec_header::create([
                    'rec_no' =>  $rec_number,
                    'po_number' => $request->ponumber,
                    'vendor_id'=>$request->vendor_id,
                    'rec_date' => $request->po_date,
                    'currency_code' => $request->currency_code,
                    'currency_rate' => $request->currency_rate,
                    'control_account' => $req->control_account,
                    'no_of_items' => $no_of_items,
                    'created_by' => auth()->id(),
                    'created_at' => Carbon::now(),
                    'rec_amt'=> $rec_amt,
                    // 'rec_net'=> $rec_amt,
                    'doc_type' => $doctype,
                    'delivered_by' => $request->delivered_by,
                    'delivery_note' => $request->delivery_note,
                    'packing_slip_no' => $request->packing_slip_no,
                    'offcd' => $request->offcd
                ]);
            }
            
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            return response()->json(['status' => 1, 'rec_no' => $rec_number]);
            Session::Flash('success', 'Purchase order receipt created');
        } 

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            // dd($th);
            Session::Flash('error', 'Error occurred, try again');
        }
    }

    public function po_rec_details(Request $request, PORec_header $rec)
    {
        if($request->has('rec_no'))
        {
            $po = POheader::where('po_number', trim($request->po_number))->get()[0];

            $glhs = Nlparams::where('prid', 'GLH')->where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

            $tax_groups = Gltaxgroups::all();

            $currencies = Currency::all();

            return ['po'=> $po, 'glhs' => $glhs, 'tax_groups'=> $tax_groups, 'currencies' => $currencies];
        }
        
        $dat = array(
			'main' => 'PO', 
            'module' => 'Purchase Order Receipt Details',
		);

        $vendor = Apvendors::where('vendor_id', $rec->vendor_id)->get()[0];

        $currency =  Currency::where('currency_code', $rec->currency_code)->get()[0];

        $offcd = Nlparams::where('prid', 'OFF')->whereRaw("trim(prsno) = '" . $rec->offcd . "' ")->get()[0];

        $item_nos = PORec_details::where('rec_no', $rec->rec_no)->where('po_number', $rec->po_number)->pluck('item_no')->toArray();

        $all_items = POdetails::where('po_number',  trim($rec->po_number))
        ->where('vendor_id',  trim($rec->vendor_id))->where('quantity_unreceipted', '>', 0)->get();

        $po = POheader::where('po_number', trim($rec->po_number))->get()[0];
       
        $items_arr = [];
        
        foreach ($all_items as $item) 
        {
            if(!in_array($item->item_no, $item_nos))
            {
                array_push( $items_arr, $item);
            }
        }

        $items = collect($items_arr);

        return view::make('gl.po.po_rec_headerdetails', compact('dat', 'rec', 'currency', 'vendor', 'offcd', 'items', 'po'));
    }

    public function po_rec_itemsdat(Request $request)
    {
        $items = PORec_details::where('rec_no',  trim($request->rec_no))->get();
       
        return Datatables::Of($items)
           ->addColumn('action', function ($item) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a><a class="btn btn-xs btn-primary" id="btn-moredtl">details</a>';
			})
           ->make(true);
	}

    public function getPOItems(Request $request)
    {
        // dd($request->all());
        if($request->has('item_no'))
        {
            $po_number = $request->get('po_number');

            $vendor_id = $request->get('vendor_id');

            $item_nos = $request->item_no;
            
            $all_items = POdetails::where('po_number',  trim($request->po_number))
            ->where('vendor_id',  trim($request->vendor_id))->get();
            
            $items = [];
           
            foreach ($all_items as $item) 
            {
                if(!in_array($item->item_no, $item_nos))
                {
                    array_push( $items, $item);
                }
            }     
            return $items;
        }

        else
        {
            return POdetails::where('po_number',  trim($request->po_number))->where('vendor_id',  trim($request->vendor_id))->get();
        }
    }

    public function getPOItemdtls(Request $request)
    {
        // dd( $request->all());
        if($request->has('rec_no'))
        {
            return PORec_details::where('rec_no',  trim($request->rec_no))->where('vendor_id',  trim($request->vendor_id))
            ->where('item_code',  trim($request->item_code))->get()[0];
        }

        else if($request->has('flag') == 'Ret')
        {
            $return_header = POReturns_header::where('po_number', $request->po_number)->where('vendor_id', $request->vendor_id)
            ->where('return_code', $request->return_code)->get()[0];

           return PORec_details::where('po_number', $request->po_number)->where('vendor_id', $request->vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('item_code', $request->item_code)->get()[0];
        }

        else
        {
            return POdetails::where('po_number',  trim($request->po_number))->where('vendor_id',  trim($request->vendor_id))
            ->where('item_code',  trim($request->item_code))->get()[0];
        }
	}

    public function getPORecs_dat(Request $request)
    {
        $headers = PORec_header::where('po_number', $request->po_number);

        return Datatables::Of($headers)
            ->editColumn('rec_date', function ($header){
                $date = new DateTime($header->rec_date);
                $format_date =  $date->format('d/m/Y');
                return $format_date;
            })
            ->make(true);
    }

    public function storePORec_item(Request $request)
    {
        if($request->has('more_dtls'))
        {
            $this->validate($request, [
                'more_dtls' => 'required|min:5'
            ]);
        }

        else
        {
            $this->validate($request, [
                'vendor_id' => 'required',
                'item_cost'=> 'required',
                'total_cost'=> 'required',
                'po_number'=> 'required',
                'quantity_requested'=> 'required',
                'quantity_received' =>'required',
                'rec_no' =>'required',
            ]);
        }

        $rec_no =  trim($request->rec_no);
        $item_code = $request->item_code;
        $item_no = $request->item_no;
        $vendor_id =  $request->vendor_id;
        $po_number = $request->po_number;

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            if($request->has('more_dtls'))
            {
                $upd_item = PORec_details::where('rec_no', $rec_no)->where('item_code', $item_code)
                ->where('vendor_id', $vendor_id)->update([
                    'more_details' => $request->more_dtls
                ]);
            }

            else
            {
                $find_item = POdetails::where('po_number', $po_number)->where('vendor_id', $vendor_id)
                ->where('item_code', $item_code)->get()[0];
               
                if($find_item && ($find_item->quantity_receipted < $find_item->quantity))
                {
                    $rec_item = PORec_details::create([
                        'vendor_id' => $vendor_id,
                        'item_no' => $request->item_no,
                        'rec_no' =>  $rec_no,
                        'item_cost' =>  str_replace(',', '', $request->item_cost),
                        'description' => $find_item->description,
                        'total_cost' => str_replace(',', '', $request->item_cost) * $request->quantity_received,
                        'quantity' => $find_item->quantity_unreceipted,
                        'unit_of_measure' => $find_item->unit_of_measure,
                        'created_by' => trim(auth()->id()),
                        'created_at' => Carbon::now(),
                        'control_account' => $find_item->control_account,
                        'description2' => $find_item->description2,
                        'quantity_received' => $request->quantity_received,
                        'po_number' => $po_number,
                        'item_code' => $item_code
                    ]);
                }

                $flag = 'REC';
                $this->updRec_PO($po_number, $vendor_id, $item_no, $rec_no);
                $this->updInventoryItem($item_code, $po_number, $vendor_id,  $flag, null);
            }
           
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully Added');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function updPORec_item(Request $request)
    {
        //dd($request->all());
        $this->validate($request, [
            'vendor_id' => 'required',
            'item_cost'=> 'required',
            'total_cost'=> 'required',
            'po_number'=> 'required',
            // 'quantity_requested'=> 'required',
            'quantity_received' =>'required',
            'rec_no' =>'required',
        ]);

        $rec_no =  trim($request->rec_no);
        $item_no = $request->item_no;
        $vendor_id =  $request->vendor_id;
        $po_number = $request->po_number;
        $item_code = $request->item_code;
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $po_item = POdetails::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->get()[0];

            $rec_item1 = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->get()[0];
         
            if($po_item && ($po_item->quantity_receipted < $po_item->quantity))
            {
                $rec_item = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
                ->where('item_code', $item_code)->where('rec_no', $rec_no)->update([
                    'item_cost' =>  str_replace(',', '', $request->item_cost),
                    'total_cost' => str_replace(',', '', $request->item_cost) * $request->quantity_received,
                    'unit_of_measure' => $po_item->unit_of_measure,
                    'updated_by' => trim(auth()->id()),
                    'dola' => Carbon::now(),
                    'quantity_received' => $request->quantity_received,
                ]);
            }

            $flag = 'REC';
            $this->updRec_PO($po_number, $vendor_id, $item_code, $rec_no);

            $this->updInventoryItem($item_code, $po_number, $vendor_id, $flag, null);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully Added');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function deletePORec_item(Request $request)
    {
        // dd( $request->all());
        $rec_no = trim($request->rec_no);
        $vendor_id = trim($request->vendor_id);
        $item_code = trim($request->item_code);

        $rec = PORec_header::where('rec_no', $rec_no)->get()[0];
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $item = POdetails::where('po_number', $rec->po_number)->where('item_code', $item_code)->where('vendor_id', $vendor_id)->get()[0];
            
            $del_item = PORec_details::where('rec_no', $rec_no)->where('item_code', $item_code)
            ->where('vendor_id', $vendor_id)->delete();

            $flag = 'REC';

            $this->updRec_PO($rec->po_number, $vendor_id, $item_no, $rec_no);
            
            $this->updInventoryItem($item_code, $rec->po_number,$vendor_id, $flag, null);
           
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully deleted');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function getOffcd(Request $request)
    {
        return Nlparams::where('prid', 'OFF')->get();
    }

    public function updRec_PO($po_number, $vendor_id, $item_code, $rec_no)
    {
        $quantity_receipted = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
        ->where('item_code', $item_code)->sum('quantity_received');

        $item = POdetails::where('po_number', $po_number)->where('item_code', $item_code)->where('vendor_id', $vendor_id)->get()[0];
       
        $upd_po_item = POdetails::where('po_number', $po_number)->where('vendor_id', $vendor_id)
        ->where('item_code', $item_code)->update([
            'quantity_unreceipted' => $item->quantity - $quantity_receipted,
            'quantity_receipted' => $quantity_receipted
        ]);

        $items_count = PORec_details::where('rec_no', $rec_no)->count();
            
        $rec_amt = PORec_details::where('rec_no', $rec_no)->sum('total_cost');

        $upd_po_rec = PORec_header::where('rec_no', $rec_no)
        ->update([
            'no_of_items' => $items_count,
            'rec_amt' => $rec_amt
        ]);

        $total_quantity_po = POdetails::where('po_number', $po_number)->sum('quantity');
        $total_quantity_rec = PORec_details::where('po_number', $po_number)->sum('quantity_received');
       
        if($total_quantity_rec != $total_quantity_po)
        {
            ##upd po to partially receipted
            $update_po = POheader::where('po_number', $po_number)
            ->update([
                'status' => '004'
            ]);
        }

        else if($total_quantity_rec == $total_quantity_po)
        {
            ##upd po to fully receipted
            $update_po = POheader::where('po_number', $po_number)
            ->update([
                'status' => '005'
            ]);
        }
    }

    public function getPORecItem_dtls(Request $request)
    {
        // dd($request->all());
        if($request->has('return_code'))
        {
            $return_header = POReturns_header::where('po_number', $request->po_number)->where('vendor_id', $request->vendor_id)
            ->where('return_code', $request->return_code)->get()[0];

            $rec_item = PORec_details::where('rec_no', $return_header->rec_no)->where('po_number', $return_header->po_number)
            ->where('item_code', $request->item_code)->where('vendor_id', $return_header->vendor_id)->get()[0];
    
            $po_item = POdetails::where('po_number', $request->po_number)->where('vendor_id', $request->vendor_id)
            ->where('item_code', $request->item_code)->get()[0];

            $return_item = POReturns_dtl::where('return_code', $request->return_code)->where('po_number', $request->po_number)
            ->where('item_code', $request->item_code)->where('vendor_id', $request->vendor_id)->get()[0];

            return ['rec_item'=>$rec_item, 'po_item'=>$po_item, 'return_item'=>$return_item];
        }

        else
        {
            $rec = PORec_header::where('rec_no', $request->rec_no)->get()[0];

            $po = POheader::where('po_number', $rec->po_number)->get()[0]; 

            $rec_item = PORec_details::where('rec_no', $request->rec_no)->where('po_number', $rec->po_number)
            ->where('item_no', $request->item_no)->where('vendor_id', $rec->vendor_id)->get()[0];

            $po_item = POdetails::where('po_number', $po->po_number)->where('vendor_id', $rec->vendor_id)
            ->where('item_no', $request->item_no)->get()[0];
        
            return ['po_item'=>$po_item, 'rec_item'=>$rec_item];
        }
    }

    public function po_returns()
    {
        $dat = array(
            'main' => 'PO', 
            'module' => 'Purchase Order Returns',
        );
        //return view::make('gl.po.po_rec_headers', compact('dat'));
    }

    public function returnsDat(Request $request)
    {
        if($request->has('flag') == 'R')
        {
            $headers = POReturns_header::where('vendor_id', $request->vendor_id)->where('po_number', $request->po_number)
            ->where('rec_no', $request->rec_no)->get();
        }

        else
        {
            $headers = POReturns_header::where('vendor_id', $request->vendor_id)->where('po_number', $request->po_number)->get();
        }

        return Datatables::Of($headers)
            ->addColumn('action', function ($headers) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
            ->editColumn('return_date', function ($header){
                $date = new DateTime($header->return_date);
                $format_date =  $date->format('d/m/Y');
                return $format_date;
            })
            ->make(true);
    }

    public function storePOReturn(Request $request)
    {
        $this -> validate($request, [
            'return_date' => 'required',
            'po_number' => 'required',
            'additional_comments' => 'required',
            'vendor_id'  => 'required',
            'rec_no' => 'required'
        ]);

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        $today = Carbon::today();
        $year = $today->format('Y');

        try 
        {
            $date = Carbon::now();
            $year = $date->year;
    
            $initials = 'PORET';
    
            $total_records = POReturns_header::count() + 1;
    
            $formatted = sprintf("%04d", $total_records);
    
            $return_code = strtoupper($initials.$year.$formatted);

            $vendor_id = $request->vendor_id;
            $po_number = $request->po_number;

            $po = POheader::where('po_number', $po_number)->where('vendor_id', $vendor_id)->get()[0];
           
            $new_head = POReturns_header::create([
                'return_code' =>  $return_code,
                'po_number' => $request->po_number,
                'vendor_id'=>$vendor_id,
                'return_date' => $request->return_date,
                'currency_code' => $po->currency_code,
                'currency_rate' => $po->currency_rate,
                'created_by' => auth()->id(),
                'created_at' => Carbon::now(),
                'additional_comments' => $request->additional_comments,
                'rec_no' => $request->rec_no
            ]);
            
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            return response()->json(['status' => 1, 'return_code' => $return_code]);
            Session::Flash('success', 'Successfully created');
        } 

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Error occurred, try again');
        }
    }

    public function returnhead_dtls(Request $request, POReturns_header $return)
    {
        $dat = array(
			'main' => 'PO', 
            'module' => 'PO Return Details',
		);

        $po = POheader::where('po_number', $return->po_number)->get()[0];

        $vendor = Apvendors::where('vendor_id', $return->vendor_id)->first();

        $status_code = DB::table('po_status')->where('status_code', $return->status)->first();

        $currency =  Currency::where('currency_code', $return->currency_code)->get()[0];

        $codes = POReturns_dtl::where('po_number', $return->po_number)->where('vendor_id', $return->vendor_id)
        ->where('rec_no', $return->rec_no)->pluck('item_code')->toArray();
       
        $items = PORec_details::where('po_number', $return->po_number)->where('vendor_id', $return->vendor_id)
        ->where('rec_no', $return->rec_no)->where('quantity_received', '>', 0)->whereNotIn('item_code', $codes)->get();
        
        return view::make('gl.po.po_returnhead_dtls', compact('dat', 'return', 'currency', 'items', 'status_code', 'vendor', 'po'));
    }

    public function storePOReturn_item(Request $request)
    {
        $this->validate($request, [
            'vendor_id' => 'required',
            'item_cost'=> 'required',
            'total_cost'=> 'required',
            'po_number'=> 'required',
            // 'quantity_requested'=> 'required',
            'quantity_received' =>'required',
            'qty_returned'=> 'required',
            'item_code' =>'required',
        ]);

        $item_code = $request->item_code;
        $vendor_id =  $request->vendor_id;
        $po_number = $request->po_number;
        $return_code = $request->return_code;
        $return_header = POReturns_header::where('po_number', $po_number)->where('vendor_id', $vendor_id)
        ->where('return_code', $return_code)->get()[0];

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $rec_item = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('item_code', $item_code)->get()[0];
            //dd($rec_item, $rec_item->quantity_received);
            if($rec_item && ($rec_item->quantity_received > $request->qty_returned))
            {
                $return_item = POReturns_dtl::create([
                    'vendor_id' => $vendor_id,
                    'item_code' => $item_code,
                    'item_cost' =>  str_replace(',', '', $request->item_cost),
                    'description' => $rec_item->description,
                    'total_cost' => str_replace(',', '', $request->item_cost) * $request->qty_returned,
                    'unit_of_measure' => $rec_item->unit_of_measure,
                    'created_by' => trim(auth()->id()),
                    'created_at' => Carbon::now(),
                    'control_account' => $rec_item->control_account,
                    'quantity_returned' => $request->qty_returned,
                    'po_number' => $po_number,
                    'reason_returned' => $request->reason_returned,
                    'return_code' => $request->return_code, 
                    'item_no' => $request->item_no,
                    'rec_no' => $return_header->rec_no
                ]);
            }

            ##upd return 
            $items_count = POReturns_dtl::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('return_code', $return_code)->where('rec_no', $return_header->rec_no)->count();
            
            $upd_return_header = POReturns_header::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('return_code', $return_code)->update([
                'no_of_items' => $items_count,
            ]);

            $flag = 'RETURN';

            ##upd po_details table
            $this->updInventoryItem($item_code, $po_number,$vendor_id, $flag, $return_code);
           
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully Added');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function po_return_itemsdat(Request $request)
    {
        $items = POReturns_dtl::where('return_code',  trim($request->return_code))->where('vendor_id',  trim($request->vendor_id))
        ->where('po_number',  trim($request->po_number))->get();
       
        return Datatables::Of($items)
           ->addColumn('action', function ($item) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
			})
           ->make(true);
	}

    public function updPOReturn_item(Request $request)
    {
        // dd($request->all());
        $this->validate($request, [
            'vendor_id' => 'required',
            'item_cost'=> 'required',
            'total_cost'=> 'required',
            'po_number'=> 'required',
            // 'quantity_requested'=> 'required',
            'quantity_received' =>'required',
            'qty_returned'=> 'required',
            'item_code' =>'required',
        ]);

    
        $item_code = $request->item_code;
        $vendor_id =  $request->vendor_id;
        $po_number = $request->po_number;
        $return_code = $request->return_code;

        $return_header = POReturns_header::where('po_number', $po_number)->where('vendor_id', $vendor_id)
        ->where('return_code', $return_code)->get()[0];

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $find_item = POdetails::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->get()[0];

            $return_item1 = POReturns_dtl::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->where('return_code', $return_code)->get()[0];

            $return_item = POReturns_dtl::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->where('return_code', $return_code)
            ->update([
                'total_cost' => str_replace(',', '', $request->item_cost) * $request->qty_returned,
                'updated_by' => trim(auth()->id()),
                'dola' => Carbon::now(),
                'quantity_returned' => $request->qty_returned,
                'reason_returned' => $request->reason_returned
            ]);

            ##upd receipted item
            $receipted_item = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('item_code', $item_code)->get()[0];

            $rec_quantity_returned = POReturns_dtl::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('item_code', $item_code)->sum('quantity_returned');
           
            $upd_receipted_item = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('item_code', $item_code)
            ->update([
                'QUANTITY_RETURNED' => $rec_quantity_returned,
                // 'QUANTITY_RECEIVED' => 
            ]);

            ##upd po_details table
            $quantity_receipted = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->sum('quantity_received');

            $total_quantity_returned = POReturns_dtl::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->sum('quantity_returned');
            // dd( $quantity_receipted, $total_quantity_returned);
            $item = POdetails::where('po_number', $po_number)->where('item_code', $item_code)->where('vendor_id', $vendor_id)->get()[0];
       
            $upd_po_item = POdetails::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->update([
                'quantity_unreceipted' => $find_item->quantity_receipted + $request->qty_returned,
                'quantity_receipted' => $quantity_receipted - $request->qty_returned
            ]);
            
            ##upd stock
            $stock_item = Im_Inventoryitems::where('item_code', $item_code)->get()[0];
            $new_qty = $request->qty_returned - $return_item1->quantity_returned ;
            // dd($new_qty ,$return_item1->quantity_returned, $request->qty_returned);
           
            $upd_stock_item = Im_Inventoryitems::where('item_code', $item_code)->update([
                'received_qty' => $stock_item->received_qty + $new_qty
            ]);

            // dd('end here');

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully updated');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function deletePOReturn_item(Request $request)
    {
        $item_code = $request->item_code;
        $vendor_id =  $request->vendor_id;
        $po_number = $request->po_number;
        $return_code = $request->return_code;

        $return_header =  POReturns_header::where('po_number', $po_number)->where('vendor_id', $vendor_id)
        ->where('return_code', $return_code)->get()[0];
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $return_item1 = POReturns_dtl::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('item_code', $item_code)->where('return_code', $return_code)->get()[0];

            $delete_return_item = POReturns_dtl::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->where('return_code', $return_code)->delete();

            $items_count = POReturns_dtl::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('return_code', $return_code)->count();
            
            $upd_return_header = POReturns_header::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('return_code', $return_code)->update([
                'no_of_items' => $items_count,
            ]);

            ##upd receipted item
            $receipted_item = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('item_code', $item_code)->get()[0];
            

            $upd_receipted_item = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('item_code', $item_code)
            ->update([
                'QUANTITY_RETURNED' => $receipted_item->quantity_returned -  $return_item1->quantity_returned,
                'QUANTITY_RECEIVED' => $receipted_item->quantity_received + $return_item1->quantity_returned
            ]);
            
            ##upd po_details table
            $quantity_receipted = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->sum('quantity_received');
           
            $quantity_returned = POReturns_dtl::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->sum('quantity_returned');
            
            $po_item = POdetails::where('po_number', $po_number)->where('item_code', $item_code)->where('vendor_id', $vendor_id)->get()[0];
            
            $upd_po_item = POdetails::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->update([
                'quantity_unreceipted' => $po_item->quantity - $quantity_receipted,
                'quantity_receipted' => $quantity_receipted,
                'quantity_returned' => $quantity_returned
            ]);
 
            ##upd stock
            $im_item  = Im_Inventoryitems::where('item_code', $item_code)->get()[0];
          
            $upd_stock_item = Im_Inventoryitems::where('item_code', $item_code)->update([
                'received_qty' => $quantity_receipted,
                'total_in_stock' => $im_item->original_stock + $quantity_receipted
            ]);
           
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            Session::Flash('success', 'Successfully deleted');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function updInventoryItem($item_code, $po_number,$vendor_id, $flag, $return_code)
    {
        // dd($item_code, $po_number,$vendor_id, $flag, $return_code);
        $po_item = POdetails::where('po_number', $po_number)->where('vendor_id', $vendor_id)
        ->where('item_code', $item_code)->get()[0];
       
        $quantity_receipted = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
        ->where('item_code', $item_code)->sum('quantity_received');
        
        if($flag == 'RETURN')
        {
            $return_header = POReturns_header::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('return_code', $return_code)->get()[0];
           
            $receipted_item = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('item_code', $item_code)->get()[0];
    
            $quantity_receipted = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->sum('quantity_received');

            $return_item1 = POReturns_dtl::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->where('return_code', $return_code)->where('rec_no', $return_header->rec_no)->get()[0];

            $upd_receipted_item = PORec_details::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('rec_no', $return_header->rec_no)->where('item_code', $item_code)
            ->update([
                'QUANTITY_RETURNED' => $return_item1->quantity_returned,
                'QUANTITY_RECEIVED' => $receipted_item->quantity_received - $return_item1->quantity_returned
            ]);

            $quantity_returned = POReturns_dtl::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->sum('quantity_returned');
           
            $upd_po_item = POdetails::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->update([
                'quantity_unreceipted' => $po_item->quantity_unreceipted + $return_item1->quantity_returned,
                'quantity_receipted' => $po_item->quantity_receipted - $return_item1->quantity_returned,
                'quantity_returned' => $quantity_returned
            ]);

            ##upd stock
            $im_item  = Im_Inventoryitems::where('item_code', $item_code)->get()[0];
           
            $upd_stock_item = Im_Inventoryitems::where('item_code', $item_code)->update([
                'received_qty' => $im_item->received_qty - $return_item1->quantity_returned,
                'total_in_stock' => $im_item->total_in_stock - $return_item1->quantity_returned
            ]);
        }

        else
        {
            $upd_po_item = POdetails::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('item_code', $item_code)->update([
                'quantity_unreceipted' => $po_item->quantity -  $quantity_receipted,
                'quantity_receipted' => $quantity_receipted
            ]);
    
            $im_item = Im_Inventoryitems::where('item_code', $item_code)->get()[0];
            
            $upd_im_item = Im_Inventoryitems::where('item_code', $item_code)->update([
                'RECEIVED_QTY' => $quantity_receipted,
                'TOTAL_IN_STOCK' => $im_item->original_stock + $quantity_receipted
            ]);
        }
    }

    public function getReturnhead_dtls(Request $request)
    {
       return POReturns_header::where('po_number', $request->po_number)->where('vendor_id', $request->vendor_id)
        ->where('return_code', $request->return_code)->get()[0];
    }

    public function updPOReturn(Request $request)
    {
        $this -> validate($request, [
            'return_date' => 'required',
            'additional_comments' => 'required',
            'vendor_id'  => 'required'
        ]);

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            $upd_head = POReturns_header::where('return_code', $request->return_code)->where('po_number', $request->po_number)
            ->where('vendor_id', $request->vendor_id)->update([
                'return_date' =>  $request->return_date,
                'updated_by' => auth()->id(),
                'dola' => Carbon::now(),
                'additional_comments' => $request->additional_comments,
            ]);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            return response()->json(['status' => 1, 'return_code' => $request->return_code]);
            Session::Flash('success', 'Successfully updated');
        } 

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Error occurred, try again');
        }
    }

    public function getRecDetails(Request $request)
    {
        if($request->has('rec_no'))
        {
            $offcd = Nlparams::where('prid', 'OFF')->get();

            $head = PORec_header::where('rec_no', $request->rec_no)->where('po_number', $request->po_number)->get()[0];

            $po = POheader::where('po_number', $request->po_number)->get()[0];

            $vendor = Apvendors::where('vendor_id', $head->vendor_id)->get()[0];

            return ['offcd'=>$offcd, 'head'=>$head, 'po'=>$po, 'vendor'=>$vendor];
        }
    }

    public function updPORec(Request $request)
    {
        $this -> validate($request, [
            'rec_date' => 'required',
            'po_number' => 'required',
            'delivered_by' => 'required',
            'vendor_id'  => 'required',
            'offcd' => 'required',
        ]);

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        $today = Carbon::today();
        $year = $today->format('Y');

        try 
        {
            $upd_rec = PORec_header::where('po_number', $request->po_number)->where('rec_no', $request->rec_no)
            ->where('vendor_id', $request->vendor_id)->update([
                'rec_date' => $request->rec_date,
                'delivered_by' => $request->delivered_by,
                'packing_slip_no' => $request->packing_slip_no,
                'offcd' => $request->offcd,
                'delivery_note' => $request->delivery_note,
                'updated_by' => trim(auth()->id()),
                'dola' => Carbon::now(),
            ]);
            
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            return response()->json(['status' => 1, 'rec_no' => $request->rec_no]);
            Session::Flash('success', 'Purchase order receipt updated');
        } 

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Error occurred, try again');
        }
    }

    public function getDept(Request $request)
    {
        return Nlparams::where('prid', 'DEP')->get();
    }

    public function storeInvoice(Request $request)
    {
        // dd($request->all());
        $this->validate($request, [
            'invoice_date' => 'required',
            'vendor_id'=> 'required',
            'po_number'=> 'required',
            'invoice_descr'=> 'required',
            'offcd'=> 'required',
            'dept'=> 'required',
            'invoice_no'=> 'required',
            'apply_discount' => 'required',
            'additional_cost' => 'required',
            'process_code' => 'required'
        ]);
       
        $po_number =  trim($request->po_number);

        $vendor_id =  trim($request->vendor_id);
        $invoice_no = trim($request->invoice_no);
        $po = POheader::where('po_number',$po_number)->get()[0];

        $currency =  Currency::where('currency_code', $po->currency_code)->get()[0];

        $doctype = ApTransTypes::where('doc_type', 'INV')->get()[0];

        $acc_period = Nlctrl::get()[0];
        
        if($currency->base_currency == 'Y')
        {
            $currency_rate = 1;
        }

        else
        {
            $currency_rate = $po->currency_rate;
        }

        $invoice_amt = str_replace(',', '', $request->foreign_invoice_amount);
        $local_invoice_amt =  $invoice_amt * $currency_rate;
        $additional_cost_flag = 'N';

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            #discount
            $percentage_dicount_flag = 'N';
            
            if($request->apply_discount == 'Y')
            {
                if($request->discount == 'A' && $request->disc_amt != null)
                {
                    $discount = str_replace(',', '', $request->disc_amt);
                    // $dicount_level = 'P';
                }
    
                else if($request->discount == 'P' && $request->percentage_disc != null)
                {
                    $percentage_disc = floatval($request->percentage_disc);
                    $percentage_dicount_flag = 'Y';
                }
            }

            if(!empty($request->cost_code && $request->cost))
            {
                for($i = 0; $i < count($request->cost_code); $i++) 
                { 
                    $item_no =  POInvoice_dtls::where('po_number', $po_number)->where('vendor_id', $vendor_id)
                    ->where('invoice_no', $invoice_no)->count() + 1;
                    
                    ##will store additional cost as an invoice item
                    $cost_type = Im_Additionalcosts::where('cost_code', $request->cost_code[$i])->get()[0];

                    if($cost_type)
                    {
                        $nlparam = Nlparams::where('prid', 'GLH')->whereRaw("trim(prsno)='" . $cost_type->control_account . "'")->get()[0];
                        
                        if($nlparam)
                        {
                            if($cost_type->taxable_flag == 'Y' && $cost_type->tax_group != null)
                            {
                                $tax_group_code = $cost_type->tax_group;
                    
                                $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
                            
                                foreach ($tax_types as $type) 
                                {
                                    $tax_type = $type->tax_type;

                                    $tax_code = $type->tax_code;
                                    
                                    $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                                    
                                    $rate = $gltax->tax_rate;

                                    $taxable_amnt = str_replace(',', '', $request->cost[$i]);
                                    
                                    $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
                    
                                    if($transtype)
                                    {
                                        $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                                    
                                        $add_deduct = $add_deduct_check->add_deduct;
                            
                                        if($add_deduct == 'A')
                                        {
                                            $tax_amount1 = ($taxable_amnt * $rate)/100;
                                        }
                    
                                        if($add_deduct == 'D')
                                        {
                                            $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                                        }
                                        
                                        ##save taxes on additional costs
                                        $tax = POInvoice_taxes::create([
                                            'po_number' => $po_number,
                                            'item_code' => $cost_type->cost_code,
                                            'vendor_id' => $vendor_id,
                                            'item_no' => $item_no,
                                            'invoice_no' => $invoice_no,
                                            'tax_code' => $tax_code,
                                            'tax_amt' => $tax_amount1,
                                            'tax_rate' => $rate,
                                            'taxable_amt' => $taxable_amnt,
                                            'add_deduct' => $add_deduct,
                                            'created_by' => trim(auth()->id()),
                                            'created_at' => Carbon::now()
                                        ]);
                                    }
                                }
                            }
                        }

                        // else
                        // {
                        //     return 'Invalid control account for additional cost';
                        // }

                        $unit_of_measure = Apunits_of_measure::where('default_flag', 'Y')->get()[0];

                        ##upd item
                        $item_taxes = POInvoice_taxes::where('po_number', $po_number)->where('item_no', $item_no)
                        ->where('invoice_no', $invoice_no)->sum('tax_amt');
                        
                        $item_net =  $item_taxes  + $taxable_amnt;
                        
                        ##create additional cost as item
                        $item = POInvoice_dtls::create([
                            'item_no' => $item_no,
                            'vendor_id' => $vendor_id,
                            'item_no' => $item_no,
                            'item_cost' => str_replace(',', '', $request->cost[$i]),
                            'invoice_no' => $invoice_no,
                            'local_item_cost' => str_replace(',', '', $request->cost[$i]) * $currency_rate,
                            'item_descr' => $cost_type->cost_name,
                            'item_name' => $cost_type->cost_name,
                            'total_cost' => str_replace(',', '', $request->cost[$i]),
                            'local_total_cost' => str_replace(',', '', $request->cost[$i]) * $currency_rate,
                            'quantity' => 1,
                            'unit_of_measure' => $unit_of_measure->unit_code,
                            'taxable' => $cost_type->taxable_flag,
                            'taxable_amt' => str_replace(',', '', $request->cost[$i]),
                            'local_taxable_amt' => str_replace(',', '', $request->cost[$i]) * $currency_rate,
                            'created_by' => trim(auth()->id()),
                            'created_at' => Carbon::now(),
                            'control_account' => $cost_type->control_account,
                            'item_code' => $cost_type->cost_code,
                            'tax_amt' => $item_taxes,
                            'local_tax_amt' => $item_taxes * $currency_rate,
                            'amt_payable' => $item_net,
                            'local_amt_payable' => $item_net * $currency_rate,
                            'po_number' => $po_number,
                            'cost_flag' =>'Y'
                        ]);

                        // $additional_cost_flag = 'Y';
                    }
                }
            }
                
            $no_of_items = POInvoice_dtls::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
            ->where('vendor_id', $vendor_id)->count();

            $total_taxes =  POInvoice_taxes::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
            ->where('vendor_id', $vendor_id)->sum('tax_amt');

            $distributed_amt = POInvoice_dtls::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
            ->whereRaw("trim(vendor_id) = '" . $vendor_id . "' ")->where('cost_flag', '<>', 'Y')->sum('total_cost');

            $amount_payable = POInvoice_dtls::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
            ->whereRaw("trim(vendor_id) = '" . $vendor_id . "' ")->where('rec_no',  $rec_no)->sum('amt_payable');

            $invoice = POInvoice_header::create([
                'vendor_id' => $vendor_id,
                'currency_code' => $po->currency_code,
                'invoice_no' => $request->invoice_no,
                'invoice_descr' => $request->invoice_descr,
                'control_account' => trim($po->control_account),
                'tax_group' => trim($po->tax_group),
                'currency_rate' => $currency_rate,
                'invoice_date' => $request->invoice_date,
                'invoice_amt' => $invoice_amt,
                'local_invoice_amt' => $local_invoice_amt,
                'undistributed_amt' => $invoice_amt,
                'local_undistributed_amt' => $local_invoice_amt,
                'status' => '001',
                'created_by' => trim(auth()->id()),
                'created_at' => Carbon::now(),
                'offcd' => $request->offcd,
                'account_month' => $acc_period->account_month,
                'account_year' => $acc_period->account_year,
                'doc_type' =>  $doctype->doc_type,
                'po_number' => $po_number,
                'dept_code' => trim($request->dept),
                'total_taxes' => $total_taxes,
                'local_total_taxes' => $total_taxes * $currency_rate,
                'amt_payable' => $amount_payable,
                'local_amt_payable' => $amount_payable * $currency_rate,
                'additional_costs_flag' => $request->additional_cost, 
                'discount_flag' => $request->apply_discount,
                'percentage_discount' => floatval($percentage_disc),
                'percentage_discount_flag' => $percentage_dicount_flag,
                'disc_amt' => str_replace(',', '', $request->disc_amt),
                'process_code' =>  trim($request->process_code)
            ]);
           
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            return response()->json(['status' => 1, 'po_number' => $po_number, 'vendor_id' => $vendor_id, 'invoice_no' => $invoice_no]);
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function invoice_dtls(Request $request, $invoice_no, $vendor_id, $po_number)
    {
        $dat = array(
			'main' => 'PO', 
            'module' => 'PO Invoices',
            'submodule' => 'Invoice Header Details',
		);
    
        $invoice_header = POInvoice_header::where('po_number',  $po_number)
        ->where('invoice_no',  $invoice_no)->where('vendor_id', $vendor_id)->get()[0];

        $po_items = POdetails::where('po_number',  $po_number)->get();

        $invoice_items_code = POInvoice_dtls::where('invoice_no',  $invoice_no)->where('po_number',  $po_number)
        ->where('vendor_id', $vendor_id)->where('cost_flag', '<>', 'Y')->pluck('item_code')->toArray();
              
        $items1 = [];

        foreach ($po_items as $item) 
        {
            if(!in_array($item->item_code, $invoice_items_code))
            {
                array_push( $items1, $item);
            }
        } 

        $items = collect($items1);
       
        $unit_of_measure = DB::table('unit_of_measure')->get();
        
        $item_control_accs = Nlparams::where('prid', 'GLH') 
        ->where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

        $vendor = Apvendors::where('vendor_id',  $invoice_header->vendor_id)->first();
          
        $tax_group = DB::table('gltaxgroups')->whereRaw("trim(group_code) = '" . $invoice_header->tax_group . "' ")->first();

        $currency =  Currency::where('currency_code', $invoice_header->currency_code)->first();
		
		$invoice_date1 = new DateTime($invoice_header->invoice_date);
		$invoice_date =  $invoice_date1->format('d/m/Y');

        $status_code = DB::table('po_status')->where('status_code', $invoice_header->status)->first();

        ##approvals
        $dept = DB::table('NLPARAMS')->where('prid', 'DEP')
        ->whereRaw("trim(prsno) = '" . trim($invoice_header->dept_code) . "'")->first();
       
        $user_id = trim(Auth::user()->user_id);

        $process = Aims_process::where('process_code',trim($invoice_header->process_code))->first();

        $process_code= trim($invoice_header->process_code);
    
        $process = Aims_process::with(['process_dtl','approval_levels'])->where('process_code',trim($process_code))->first();
       
        #fetch approvals if any
        $approval_dtl = Approvals::with('approval_flow')->where('po_number',trim($invoice_header->po_number))
        ->where('vendor_id',trim($invoice_header->vendor_id))->where('invoice_no',trim($invoice_header->invoice_no))
        ->orderBy('date_created','DESC')->first();
        // dd(->where('type', 'POINV'));
       
        $check_approval = Approvals::where('po_number',trim($invoice_header->po_number))
        ->where('vendor_id',trim($invoice_header->vendor_id))->where('invoice_no',trim($invoice_header->invoice_no))
        ->orderBy('date_created','DESC')->first();
        // dd( $check_approval);
        if(isset($check_approval)){
            if($check_approval->status == 'A'){
                $status = 'A';
            }

            elseif($check_approval->status == 'R'){
                $status = 'R';
                $msg = 'Approval was rejected';
            }
            elseif($check_approval->status == 'P'){
                $status = 'P';
                $msg = 'There is a pending approval';
            }
        }

        $approval_status = $status ;
       
        $approval_msg = $msg;
        // dd($approval_msg, $approval_status, $approval_dtl,$process );
        return view::make('gl.po.po_invheader_dtls', compact('office', 'item_control_accs', 
        'invoice_status','tax_group', 'invoice_date', 'dat', 'invoice_header', 'vendor', 'unit_of_measure',
        'currency', 'items', 'approval_status', 'approval_msg', 'approval_dtl', 'process', 'status_code'));
    }

    public function invoice_dtls1(Request $request)
    {
        $dat = array(
			'main' => 'PO', 
            'module' => 'PO Invoices',
            'submodule' => 'Invoice Header Details',
		);

        $invoice_no = $request->invoice_no;
        $vendor_id = $request->vendor_id;
        $po_number = $request->po_number;
        $invoice_header = POInvoice_header::where('po_number', $po_number)
        ->where('invoice_no',  $invoice_no)->where('vendor_id', $vendor_id)->get()[0];
       
        $po_items = POdetails::where('po_number',  $po_number)->get();

        $invoice_items_code = POInvoice_dtls::where('invoice_no',  $invoice_no)->where('po_number',  $po_number)
        ->where('vendor_id', $vendor_id)->where('cost_flag', '<>', 'Y')->pluck('item_code')->toArray();
              
        $items1 = [];

        foreach ($po_items as $item) 
        {
            if(!in_array($item->item_code, $invoice_items_code))
            {
                array_push( $items1, $item);
            }
        } 

        $items = collect($items1);

        // $recs =  PORec_header::where('po_number', $invoice_header->po_number)->where('vendor_id', $invoice_header->vendor_id)
        // ->where('rec_no',  $invoice_header->rec_no)->get();
       
        $unit_of_measure = DB::table('unit_of_measure')->get();
        
        $item_control_accs = Nlparams::where('prid', 'GLH') 
        ->where('bank_flag', null)->orWhere('bank_flag', 'N')->get();

        $vendor = Apvendors::where('vendor_id',  $invoice_header->vendor_id)->first();
          
        $tax_group = DB::table('gltaxgroups')->whereRaw("trim(group_code) = '" . $invoice_header->tax_group . "' ")->first();

        $currency =  Currency::where('currency_code', $invoice_header->currency_code)->first();
		
		$invoice_date1 = new DateTime($invoice_header->invoice_date);
		$invoice_date =  $invoice_date1->format('d/m/Y');

        $status_code = DB::table('po_status')->where('status_code', $invoice_header->status)->first();

        ##approvals
        $dept = DB::table('NLPARAMS')->where('prid', 'DEP')
        ->whereRaw("trim(prsno) = '" . trim($invoice_header->dept_code) . "'")->first();
       
        $user_id = trim(Auth::user()->user_id);

        $process = Aims_process::where('process_code',trim($invoice_header->process_code))->first();

        $process_code= trim($invoice_header->process_code);
    
        $process = Aims_process::with(['process_dtl','approval_levels'])->where('process_code',trim($process_code))->first();
       
        #fetch approvals if any
        $approval_dtl = Approvals::with('approval_flow')->where('po_number',trim($invoice_header->po_number))
        ->where('vendor_id',trim($invoice_header->vendor_id))->where('invoice_no',trim($invoice_header->invoice_no))
        ->orderBy('date_created','DESC')->first();
        // dd(->where('type', 'POINV'));
       
        $check_approval = Approvals::where('po_number',trim($invoice_header->po_number))
        ->where('vendor_id',trim($invoice_header->vendor_id))->where('invoice_no',trim($invoice_header->invoice_no))
        ->orderBy('date_created','DESC')->first();
        // dd( $check_approval);
        if(isset($check_approval)){
            if($check_approval->status == 'A'){
                $status = 'A';
            }

            elseif($check_approval->status == 'R'){
                $status = 'R';
                $msg = 'Approval was rejected';
            }
            elseif($check_approval->status == 'P'){
                $status = 'P';
                $msg = 'There is a pending approval';
            }
        }

        $approval_status = $status ;
        
        $approval_msg = $msg;
        
        return view::make('gl.po.po_invheader_dtls', compact('office', 'item_control_accs', 
        'invoice_status','tax_group', 'invoice_date', 'dat', 'invoice_header', 'vendor', 'unit_of_measure', 'currency',
        'items', 'approval_status', 'approval_msg', 'approval_dtl', 'process', 'status_code'));
    }

    public function storeInvoice_item(Request $request)
    {
        // dd($request->all());
        $this->validate($request, [
            'control_account' => 'required',
            'vendor_id'=> 'required',
            'invoice_no'=> 'required',
            'item_cost'=> 'required',
            'total_cost'=> 'required',
            // 'description'=> 'required',
            'quantity'=> 'required',
            'unit_of_measure'=> 'required',
            'taxable'=> 'required',
            'apply_discount' =>'required',
            'additional_cost' =>'required'
        ]);

        $po_number =  trim($request->po_number);
        $vendor_id =  trim($request->vendor_id);
        $invoice_no =  trim($request->invoice_no);
        $rec_no =  trim($request->rec_no);

        $invoice = POInvoice_header::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
        ->whereRaw("trim(vendor_id) = '" . $vendor_id . "' ")->get()[0];

        $po_item = POdetails::where('po_number', $po_number)->where('vendor_id', $vendor_id)
        ->where('item_code', $request->item_code)->get()[0];

        $po = POheader::where('po_number',$po_number)->get()[0];

        $currency_rate = $invoice->currency_rate;

        $item_cost = str_replace(',', '', $request->item_cost);
        $total_cost = $item_cost *  $request->quantity;
        $taxable_amnt = $total_cost; 
        
        $currency =  Currency::where('currency_code', $invoice->currency_code)->get()[0];

        if($currency->base_currency == 'Y')
        {
            $currency_rate = 1;
        }

        else
        {
            $currency_rate = $invoice->currency_rate;
        }

        $percentage_discount_flag = 'N';
        $discount = 0;

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        try 
        {
            ##discount at invoice level
            if($invoice->discount_flag == 'Y')
            {
                if($invoice->percentage_discount_flag == 'Y' && $invoice->percentage_discount > 0)
                {
                    $discount = ($invoice->percentage_discount * $total_cost) / 100;
                    // $taxable_amnt = $total_cost - $discount;
                }
                
                else if( $invoice->disc_amt > 0)
                {
                    $discount = ($invoice->disc_amt * $total_cost)/$invoice->invoice_amt;
                    // $taxable_amnt = $total_cost - $discount;
                }
            }

            else if($request->apply_discount == 'Y' && ($invoice->discount_flag == 'N' || $invoice->discount_flag == null))
            {
                if($request->discount == 'A' && $request->disc_amt > 0)
                {
                    $discount = str_replace(',', '', $request->disc_amt);
                    // $taxable_amnt = $total_cost - $discount; 
                }
        
                else if($request->discount == 'P' && $request->percentage_disc > 0)
                {
                    $percentage_disc = floatval($request->percentage_disc);
                    $discount = ($percentage_disc * $total_cost) / 100; 
                    // $taxable_amnt = $total_cost - $discount; 
                    $percentage_discount_flag = 'Y';
                }
            }

            ##store additional costs on item as an item
            if($request->has('additional_cost') == 'Y' && ($invoice->additional_cost_flag == 'N' || $invoice->additional_cost_flag == null) )
            {
                for($i = 0; $i < count($request->cost_code); $i++) 
                { 
                    $cost_type = Im_Additionalcosts::where('cost_code', $request->cost_code[$i])->get()[0];
                  
                    $taxable_amnt1 = str_replace(',', '', $request->cost[$i]);
                    
                    
                    $item_no =  POInvoice_dtls::where('po_number', $po_number)->where('vendor_id', $vendor_id)
                    ->where('invoice_no', $invoice_no)->count() + 1;
                  
                    ##check if cost exists as an item on invoice
                    $check_item =  POInvoice_dtls::where('po_number', $po_number)->where('vendor_id', $vendor_id)
                    ->where('invoice_no', $invoice_no)->where('item_code', trim($cost_type->cost_code))->get()[0];
                  
                    if($check_item && $cost_type)
                    {
                        $taxable_amnt = $check_item->item_cost + $taxable_amnt1;
                        $item_cost = $check_item->item_cost + $taxable_amnt1;
                        $total_cost = $item_cost; 
                       
                        ##upd taxes
                        $cost_taxes =  POInvoice_taxes::where('po_number', $po_number)->where('vendor_id', $vendor_id)
                        ->where('invoice_no', $invoice_no)->where('item_code', $request->cost_code[$i])->get();

                        if($cost_taxes->count() > 0)
                        {
                            foreach ($cost_taxes as $tax) 
                            {
                                $taxable_amount = $tax->taxable_amt +  str_replace(',', '', $request->cost[$i]);
                                if($tax->add_deduct == 'A')
                                {
                                    $tax_amount1 = ($taxable_amount * $tax->tax_rate)/100;
                                }
            
                                if($tax->add_deduct == 'D')
                                {
                                    $tax_amount1 = (($taxable_amount * $tax->tax_rate)/100) * (-1);
                                }

                                $upd_tax =  POInvoice_taxes::where('po_number', $po_number)->where('vendor_id', $vendor_id)
                                ->where('invoice_no', $invoice_no)->where('item_code', $request->cost_code[$i])
                                ->update([
                                    'taxable_amt' => $taxable_amount,
                                    'tax_amt' => $tax_amount1,
                                    'updated_by' => trim(auth()->id()),
                                    'dola' =>  Carbon::now()
                                ]);
                            }
                        }

                        ##upd additional cost which is an item
                        $total_taxes = POInvoice_taxes::where('po_number', $po_number)->where('vendor_id', $vendor_id)
                        ->where('invoice_no', $invoice_no)->where('item_code', $request->cost_code[$i])->sum('tax_amt');
                        
                        $upd_item = POInvoice_dtls::where('po_number', $po_number)->where('vendor_id', $vendor_id)
                        ->where('invoice_no', $invoice_no)->where('item_code', $request->cost_code[$i])
                        ->update([
                            'taxable_amt' => $taxable_amnt,
                            'item_cost' => $item_cost,
                            'total_cost' => $total_cost,
                            'amt_payable' => $taxable_amnt + $total_taxes, 
                            'tax_amt' => $total_taxes
                        ]);
                    }

                    else
                    {
                        $taxable_amnt = str_replace(',', '', $request->cost[$i]);
                        $item_cost = $taxable_amnt;
                        $total_cost = $taxable_amnt; 
                      
                        $tax_group_code = $cost_type->tax_group;                    
                        $tax_types = Gltaxgroupdt::where('group_code', $tax_group_code)->get();
                        foreach ($tax_types as $type) 
                        {
                            $tax_type = $type->tax_type;
                            $tax_code = $type->tax_code;
                            $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                            $rate = $gltax->tax_rate;
                            
                            $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();
                            
                            if($transtype)
                            {
                                $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                            
                                $add_deduct = $add_deduct_check->add_deduct;
                    
                                if($add_deduct == 'A')
                                {
                                    $tax_amount1 = ($taxable_amnt * $rate)/100;
                                }
            
                                if($add_deduct == 'D')
                                {
                                    $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                                }
                                
                                ##save taxes on additional costs as item taxes
                                $tax = POInvoice_taxes::create([
                                    'po_number' => $po_number,
                                    'item_code' => $cost_type->cost_code,
                                    'vendor_id' => $vendor_id,
                                    'item_no' => $item_no,
                                    'invoice_no' => $invoice_no,
                                    'tax_code' => $tax_code,
                                    'tax_amt' => $tax_amount1,
                                    'tax_rate' => $rate,
                                    'taxable_amt' => $taxable_amnt,
                                    'add_deduct' => $add_deduct,
                                    'created_by' => trim(auth()->id()),
                                    'created_at' => Carbon::now()
                                ]);
                            }
                        }

                        $unit_of_measure = Apunits_of_measure::where('default_flag', 'Y')->get()[0];

                        $item_taxes = POInvoice_taxes::where('po_number', $po_number)->where('item_no', $item_no)
                        ->where('invoice_no', $invoice_no)->where('item_code', $cost_type->cost_code)->sum('tax_amt');
                      
                        $item_net =  $item_taxes  + $taxable_amnt;
                       
                        ##create additional cost as item
                        $item = POInvoice_dtls::create([
                            'item_no' => $item_no,
                            'vendor_id' => $vendor_id,
                            'item_no' => $item_no,
                            'item_cost' => str_replace(',', '', $request->cost[$i]),
                            'invoice_no' => $invoice_no,
                            'local_item_cost' => str_replace(',', '', $request->cost[$i]) * $currency_rate,
                            'item_descr' => $cost_type->cost_name,
                            'item_name' => $cost_type->cost_name,
                            'total_cost' => str_replace(',', '', $request->cost[$i]),
                            'local_total_cost' => str_replace(',', '', $request->cost[$i]) * $currency_rate,
                            'quantity' => 1,
                            'unit_of_measure' => $unit_of_measure->unit_code,
                            'taxable' => $cost_type->taxable_flag,
                            'taxable_amt' => str_replace(',', '', $request->cost[$i]),
                            'local_taxable_amt' => str_replace(',', '', $request->cost[$i]) * $currency_rate,
                            'created_by' => trim(auth()->id()),
                            'created_at' => Carbon::now(),
                            'control_account' => $cost_type->control_account,
                            'item_code' => $cost_type->cost_code,
                            'tax_amt' => $item_taxes,
                            'local_tax_amt' => $item_taxes * $currency_rate,
                            'amt_payable' => $item_net,
                            'local_amt_payable' => $item_net * $currency_rate,
                            'po_number' => $po_number,
                            'cost_flag' =>'Y'
                        ]); 
                    }
                }
            }

            $item_no =  POInvoice_dtls::where('po_number', $po_number)->where('vendor_id', $vendor_id)
            ->where('invoice_no', $invoice_no)->count() + 1;

            $item_cost = str_replace(',', '', $request->item_cost);
            $total_cost = $item_cost *  $request->quantity;
            $taxable_amnt = $total_cost; 

            if($request->taxable == 'Y')
            {
                $tax_group_code = $invoice->tax_group;
                $tax_types = DB::table('gltaxgroupdtl')->where('group_code', $tax_group_code)->get();
                foreach ($tax_types as $type) 
                {
                    $tax_type = $type->tax_type;
                    $tax_code = $type->tax_code;
                
                    $gltax = DB::table('gltaxes')->where('tax_type', $tax_type)->where('tax_code', $tax_code)-> first();
                    
                    $rate = $gltax->tax_rate;
                    
                    //trans type
                    $transtype = DB::table('gltaxtypes')->where('tax_type', $tax_type)->where('transtype', 'INV')->first();

                    if($transtype)
                    {
                        if($rate > 0)
                        {
                            $add_deduct_check = DB::table('gltaxtypes')->where('tax_type', $tax_type)->first();
                           
                            $add_deduct = $add_deduct_check->add_deduct;
                
                            if($add_deduct == 'A')
                            {
                                $tax_amount1 = ($taxable_amnt * $rate)/100;
                            }
    
                            if($add_deduct == 'D')
                            {
                                $tax_amount1 = (($taxable_amnt * $rate)/100) * (-1);
                            }
                            
                            //save into tax table
                            $item_taxable_amt = $taxable_amnt;
                            $tax = POInvoice_taxes::create([
                                'po_number' => $po_number,
                                'item_code' => $request->item_code,
                                'vendor_id' => $vendor_id,
                                'item_no' => $item_no,
                                'invoice_no' => $invoice_no,
                                'tax_code' => $tax_code,
                                'tax_amt' => $tax_amount1,
                                'tax_rate' => $rate,
                                'taxable_amt' => $taxable_amnt,
                                'add_deduct' => $add_deduct,
                                'created_by' => trim(auth()->id()),
                                'created_at' => Carbon::now()
                            ]);
                        }
                    }
                }
            }

            $total_tax_amount = POInvoice_taxes::where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)
            ->where('po_number', $po_number)->get();
            
            ##store item
            $total_tax_amount = POInvoice_taxes::where('invoice_no', $invoice_no)->where('vendor_id', $vendor_id)
            ->where('item_code', $po_item->item_code)->where('po_number', $po_number)->sum('tax_amt');
            
            $item_amt_payable = $total_tax_amount + $item_taxable_amt;
            
            $store_item = POInvoice_dtls::create([
                // 'rec_no' => $rec_no,
                'vendor_id' => $vendor_id,
                'item_no' => $item_no,
                'invoice_no' => $invoice_no,
                'item_cost' =>  str_replace(',', '', $request->item_cost),
                'local_item_cost' => str_replace(',', '', $request->item_cost) * $currency_rate,
                'item_descr' => $po_item->description,
                'item_name' => $po_item->description,
                'total_cost' => str_replace(',', '', $request->item_cost) *  $request->quantity,
                'local_total_cost' => (str_replace(',', '', $request->item_cost) *  $request->quantity) * $currency_rate,
                'quantity' => $request->quantity,
                'unit_of_measure' => $po_item->unit_of_measure,
                'taxable' => $request->taxable,
                'taxable_amt' => $item_taxable_amt,
                'local_taxable_amt' => $item_taxable_amt * $currency_rate,
                'created_by' => trim(auth()->id()),
                'created_at' => Carbon::now(),
                'control_account' => $po_item->control_account,
                'item_code' => $po_item->item_code,
                'tax_amt' => $total_tax_amount,
                'local_tax_amt' => $total_tax_amount * $currency_rate,
                'amt_payable' => $item_amt_payable,
                'local_amt_payable' => $item_amt_payable * $currency_rate,
                'po_number' => $po_number,
                'discount_flag' => $request->apply_discount,
                'percentage_discount' => floatval($percentage_disc),
                'percentage_discount_flag' => $percentage_discount_flag,
                'disc_amt' => $discount,
                'cost_flag' => 'N'
            ]);
            
            $no_of_items = POInvoice_dtls::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
            ->where('vendor_id', $vendor_id)->count();

            $total_taxes =  POInvoice_taxes::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
            ->where('vendor_id', $vendor_id)->sum('tax_amt');
           
            $distributed_amt = POInvoice_dtls::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
            ->whereRaw("trim(vendor_id) = '" . $vendor_id . "' ")->where('cost_flag', '<>', 'Y')->sum('total_cost');

            $amount_payable = POInvoice_dtls::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
            ->whereRaw("trim(vendor_id) = '" . $vendor_id . "' ")->sum('amt_payable');
           
            if($invoice->discount_flag == 'Y')
            {
                $invoice_discount = $invoice->disc_amt; 
            }

            else if($request->apply_discount == 'Y')
            {
                $invoice_discount =  POInvoice_dtls::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
                ->whereRaw("trim(vendor_id) = '" . $vendor_id . "' ")->sum('disc_amt');
            }

            $total_additional_costs =  POInvoice_dtls::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
            ->whereRaw("trim(vendor_id) = '" . $vendor_id . "' ")->where('cost_flag', 'Y')->sum('total_cost');
          
            $upd_invoice =  POInvoice_header::where('po_number',  $po_number)->where('invoice_no',  $invoice_no)
            ->whereRaw("trim(vendor_id) = '" . $vendor_id . "' ")
            ->update([
                'undistributed_amt' => $invoice->invoice_amt - $distributed_amt,
                'local_undistributed_amt' => ($invoice->invoice_amt - $distributed_amt) * $currency_rate,
                'total_taxes' => $total_taxes,
                'local_total_taxes' => $total_taxes * $currency_rate,
                'amt_payable' => $amount_payable,
                'local_amt_payable' => $amount_payable * $currency_rate,
                'no_of_items' => $no_of_items, 
                'disc_amt' => $invoice_discount, 
                'additional_cost' => $total_additional_costs, 
                'local_additional_cost' => $total_additional_costs * $currency_rate
            ]);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();

            Session::Flash('success', 'Item successfully added');
        }

        catch(\Throwable $e)
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            Session::Flash('error', 'Failed, try again');
        }
    }

    public function invoices_dat(Request $request)
    {
        $invoices = POInvoice_header::get();
       
        return Datatables::Of($invoices)
           ->addColumn('action', function ($inv) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
			})

            ->editColumn('invoice_date', function ($inv){
                $date = new DateTime($inv->req_date);
                $format_date =  $date->format('d/m/Y');
                return $format_date;
            })
           ->make(true);
	}

    public function invoices()
    {
        $dat = array(
            'main' => 'PO', 
            'module' => 'Purchase Order Invoices',
        );
        return view::make('gl.po.po_invoices', compact('dat'));
    }

    public function invoice_additionalcosts(Request $request)
    {
        
        if($request->has('item_flag') == 'Y' )
        {
            $item_additional_costs = POitem_additionalcosts::where('item_code', $request->item_code)
            ->where('po_number', $request->po_number)->get();

            $costs = [];

            foreach ($item_additional_costs as $cost) 
            {
                $cost1 = Im_Additionalcosts::where('cost_code', $cost->cost_code)->get()[0];
                    
                array_push( $costs, $cost1);
            }
            return $costs;
        }

        else if($request->has('item_additional_costs') == 'Y')
        {
            $item_additional_costs = POitem_additionalcosts::where('item_code', $request->item_code)
            ->where('po_number', $request->po_number)->get();
            
            $cost_arr = $request->cost_code;

            $costs = [];
            
            foreach ($item_additional_costs as $cost) 
            {
                if(!in_array($cost->cost_code, $cost_arr))
                {
                    $cost1 = Im_Additionalcosts::where('cost_code', $cost->cost_code)->get()[0];
                    array_push( $costs, $cost1);
                }
            } 
            return $costs;
        }

        else
        {
            $po_additional_costs = POhead_additionalcosts::where('po_number', $request->po_number)->get();

            $all_costs = Im_Additionalcosts::all();
            
            if($request->has('additional_cost') == 'Y' && $request->has('cost_code'))
            {
                $cost_arr = $request->cost_code;

                $costs = [];
                
                foreach ($po_additional_costs as $cost) 
                {
                    if(!in_array($cost->cost_code, $cost_arr))
                    {
                        $cost1 = Im_Additionalcosts::where('cost_code', $cost->cost_code)->get()[0];
                        array_push( $costs, $cost1);
                    }
                }     
                return $costs;
            }

            else
            {   
                $costs = [];

                foreach ($po_additional_costs as $cost) 
                {
                    $cost1 = Im_Additionalcosts::where('cost_code', $cost->cost_code)->get()[0];
                    
                    array_push( $costs, $cost1);
                }  
                
                return $costs;
            }
        }
	}

    public function invoice_items_dat(Request $request)
    {
        $items = POInvoice_dtls::where('invoice_no',  trim($request->invoice_no))->where('vendor_id',  trim($request->vendor_id))
        ->where('po_number',  trim($request->po_number))->get();
       
        return Datatables::Of($items)
           ->addColumn('action', function ($item) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
			})

            ->editColumn('control_account', function ($item) {
                $nlparam = Nlparams::where('prid', 'GLH')->whereRaw("trim(control_account) = '".$item->control_account."' ") 
                ->where('bank_flag', null)->orWhere('bank_flag', 'N')->get()[0];
                return $nlparam->prsno.'-'. $nlparam->prdesc;
            }) 

            ->editColumn('unit_of_measure', function ($item) {
                $unit = Apunits_of_measure::where('unit_code', $item->unit_of_measure)->get()[0];
                return $unit->description;
            }) 
           ->make(true);
	}

    public function invitem_taxes_dat(Request $request)
    {
        $taxes = POInvoice_taxes::where('po_number', trim($request->po_number))->where('item_no', trim($request->item_no))
        ->where('invoice_no', trim($request->invoice_no))->where('vendor_id', trim($request->vendor_id))->get();
       
        return Datatables::Of($taxes)
            ->addColumn('action', function ($cost) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a><a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
            })

            ->addColumn('tax_code_descr', function ($tax){
                $tax_code = Gltaxgroupdt::where('tax_code', $tax->tax_code)->first();
                return $tax_code->tax_type;
            })

           ->addColumn('tax_rate', function ($tax){
                return $tax->tax_rate .'%';
            })

            ->editColumn('add_deduct', function ($tax){
                if ( $tax->add_deduct == 'A') 
                {
                    return 'ADDITION';
                }

                else if
                ( $tax->add_deduct == 'D') {
                    return 'DEDUCTION';
                }

                else
                {
                    return $tax->add_deduct;
                }
            })
           ->make(true);
    }

    public function po_invitem_dtls(Request $request)
    {
        // dd($request->all());
        $dat = array(
			'main' => 'PO', 
            'module' => 'Purchase Order Invoice',
            'submodule' => 'Invoice Item Details',
		);

		$po_number = trim($request->view_po_number);
        $item_no = trim($request->view_item_no);
        $vendor_id = trim($request->view_vendor_id);
        $invoice_no = trim($request->view_invoice_no);

        $item = POInvoice_dtls::where('po_number', $po_number)->where('vendor_id', $vendor_id)
        ->where('invoice_no', $invoice_no)->where('item_no', $item_no)->get()[0];
      
        $account = Nlparams::where('prid', 'GLH')->where('prsno', $item->control_account)->first();
		
        $unit = DB::table('unit_of_measure')->where('unit_code', trim($item->unit_of_measure))->first();

        $additional_costs = collect($costs);

        $tax_types = DB::table('gltaxtypes')->where('transtype', 'INV')->get();
		
        return view::make('gl.po.po_invitem_dtls', compact('account', 'tax_types', 'item', 'dat', 'unit', 'additional_costs', 'po'));
    }

    public function getAims_processes(Request $request)
	{
		return Aims_process::all();
    }

    public function check_invoice_props(Request $request)
	{
        $invoice_header = POInvoice_header::where('po_number',  $request->po_number)
        ->where('invoice_no',  $request->invoice_no)->where('vendor_id', $request->vendor_id)->get()[0];

        $receipts = PORec_header::where('po_number', $invoice_header->po_number)->get();

        $invoice_items_quantity = POInvoice_dtls::where('po_number',  $request->po_number)
        ->where('invoice_no',  $request->invoice_no)->where('vendor_id', $request->vendor_id)
        ->where('cost_flag', '<>', 'Y')->sum('quantity');

        $count_receipted_items = PORec_details::where('po_number',  $request->po_number)
        ->where('vendor_id', $request->vendor_id)->sum('quantity_received');
        
        #$receipts
        return ['invoice_header' => $invoice_header,
            'invoice_items_quantity' => $invoice_items_quantity, 
            'count_receipted_items' => $count_receipted_items, 
        ];
    }
}
