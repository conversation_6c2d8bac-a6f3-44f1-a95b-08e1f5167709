<?php

namespace App\Http\Controllers\gl\fa;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\Datatables\Datatables;
use Illuminate\Support\Facades\Gate;

use View;
use Session;
use Auth;
use DB;
use DateTime;
use Carbon\Carbon;
use Response;
use App\Currency;
use App\Nlparams;
use App\Apstatus;
use App\ApTransTypes;
use App\Nlctrl;
use App\Fa_disposal_dtls;
use App\Fa_disposal_header;
use App\Fa_disposal_dtls_items;
use App\PoDocType;
use App\Doctype;
use App\FAHeaderDtl;
use App\FAHeader;
use App\FADisposalBuyers;
use App\FADisposalMethods;
use App\Glbank;
use App\Batch_details;
use App\Batch_entry;
use App\Batch_list;
use App\Classes\gl\GenerateBatch;
use App\Glsource;
use App\Http\Controllers\gl\ApBatchController;

use Illuminate\Support\Facades\Validator;

use App\Aims_process;
use App\Process_approval_dtl;
use App\Approval_level;
use App\Approval_matrix;
use App\Approver_matrix;
use App\User;
use App\Approvals;
use App\Approval_flow;
use App\Classes\Approvals\ApprovalsMgt;
use App\Classes\Approvals\ApprovalsPo;
use App\Aimsuser_web;
use App\Cbmast;
use App\Http\Controllers\gl\BankingController; 

class AssetDisposalController extends Controller
{
    public function disposals()
    {

        $acc_period = Nlctrl::first();

        $account_month = $acc_period->account_month;

        $account_year = $acc_period->account_year;
 
        return view::make('gl.fa.disposals.disposals', compact('doctype', 'account_month', 'account_year'));
    }
    
    public function disposal_datatable(Request $request)
    {
        $disposals = Fa_disposal_header::all();
    
        return Datatables::of($disposals)
            ->addColumn('batch_no', function ($item) {
                $batchEntry = DB::table('GLBATCHENTRY')
                    ->select('GLBATCHENTRY.BATCH_NO')
                    ->join('FA_DISPOSAL_HEADER', 'GLBATCHENTRY.ENTRY_HEADER', '=', 'FA_DISPOSAL_HEADER.DISPOSAL_NO')
                    ->where('FA_DISPOSAL_HEADER.disposal_no', $item->disposal_no)
                    ->first();
            
                return $batchEntry ? $batchEntry->batch_no : '';
            })
    
            ->addColumn('action', function ($disposal) {
                return '<a class="btn btn-xs" id="btn-edit"><i class="glyphicon glyphicon-edit"></i></a>';
            })
    
            ->editColumn('disposal_date', function ($disposal) {
                $date = new DateTime($disposal->disposal_date);
                $format_date = $date->format('d/m/Y');
                return $format_date;
            })
            ->make(true);
    }
    

    public function get_departments()
    {
        return Nlparams::where('prid', 'DEP')->get();
    }

    public function get_offcd()
    {
        return Nlparams::where('prid', 'OFF')->get();
    }

    public function fetch_currencies()
	{
       return Currency::where('base_currency', 'Y')->get()[0];
    }

    public function generate_diposal_no()
    {   
        $disposal = PoDocType::where('doc_type', 'FAD')->get()[0];
        return $disposal->serial_no;
    }

    public function upd_serial()
    {
        $new_serial = PoDocType::where('doc_type', 'FAD')->increment('serial_no', 1);
        return $new_serial;
    } 

    public function store_disposal(Request $request)
    {
        $attributes = $request->validate([
            'offcd' => 'required',
            'currency_code' => 'required',
            'description' => 'required',
            'currency_rate' => 'required',
            'disposal_date' => 'required',
            'account_month' => 'required',
            'account_year' => 'required',
            'dep_code' => 'required',
            'bank_acc_code' => 'required',
            'payment_mode' => 'required'
        ]);

        $doc_type = Doctype::where('doc_type','DSP')->first();
                            
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try 
        {
            $today = Carbon::today();
            $year = $today->format('Y');
            $serial_no = $this->generate_diposal_no();
            $disposal_no = 'FAD'.STR_PAD($serial_no, 6, '0', STR_PAD_LEFT).$year;
            $attributes['amount'] = 0;
            $attributes['local_amount'] = 0;
            $attributes['status'] = '001';
            $attributes['created_by'] = trim(auth()->id());
            $attributes['created_at'] = Carbon::now();
            $attributes['disposal_no'] = $disposal_no;
            $attributes['no_of_items'] = 0;
            $attributes['process_code'] = $doc_type->serial_no;
            $attributes['payment_reference'] = $request->payment_reference;

            $create = Fa_disposal_header::create($attributes);

            $this->upd_serial();

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            return response()->json(['status' => 1, 'disposal_no' => $disposal_no]);
        } 
        
        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            return response()->json(['status' => 0]);
        }
    }

    public function disposal_header_dtls(Request $request)
    {
        $disposal = Fa_disposal_header::where('disposal_no', trim($request->disposal_no))->get()[0];

        $batchentry = Batch_entry::where('entry_header', trim($request->disposal_no))->first();

        $batch = Batch_list::where('batch_no', $batchentry->batch_no)->first();

        $status = Apstatus::where('status_code', $disposal->status)->first();

        $currency =  Currency::where('currency_code', $disposal->currency_code)->first();

        $date = new DateTime($disposal->disposal_date);

		$disposal_date = $date->format('d/m/Y');

        
        //!approval 
        $user_id = trim(Auth::user()->user_id);
        $process = Aims_process::where('process_code',trim($disposal->process_code))->first();
        $process_code= trim($disposal->process_code);
    
        $process = Aims_process::with(['process_dtl',
                'approval_levels'
            ])
            ->where('process_code',trim($process_code))
            ->first();
       
        // fetch approvals if any
        $approval_dtl = Approvals::with('approval_flow')
                                    ->where('req_no',trim($disposal->disposal_no))
                                    ->where('type', 'DISP')
                                    ->orderBy('date_created','DESC')
                                    ->first();

        $check_approval = Approvals::where('req_no',trim($disposal->disposal_no))
                                    ->where('type', 'DISP')
                                    ->orderBy('date_created','DESC')
                                    ->first();


        if(isset($check_approval)){
            // $status = 'N';
            if($check_approval->status == 'A'){
                $status = 'A';
                
            }
            elseif($check_approval->status == 'R'){
                $status = 'R';
                $msg = 'Requisition Approval was rejected';
            }
            elseif($check_approval->status == 'P'){
                $status = 'P';
                $msg = 'Requisition has a Pending approval';
            }
        }

        $approval_status = $status ;
        $approval_msg = $msg;

        return view::make('gl.fa.disposals.disposal_dtls', compact( 'disposal', 'status', 'currency', 'disposal_date','batch','process','approval_status','approval_msg','approval_dtl'));
    }

    public function disposal_asset_datatab(Request $request)
    {
        $assets = Fa_disposal_dtls::where('disposal_no', $request->disposal_no)->get();
        return Datatables::Of($assets)
           ->addColumn('action', function ($asset) {
                return '<a class="btn btn-xs" id="btn-remove"><i class="glyphicon glyphicon-trash"></i></a>';
            })
           ->make(true);
    }

    public function fetch_active_asset(Request $request)
    {
       return FAHeaderDtl::whereNotIn('status', ['D', 'S'])->get();
    }

    public function get_asset_item_dtl(Request $request)
    {
        $item_code = trim($request->item_code);

        $item_exist = Fa_disposal_dtls::where('disposal_no', $request->disposal_no)
        ->where('item_code', $request->item_code)->get();
        
        $item_exist_on_another_disposal = Fa_disposal_dtls::where('item_code', $item_code)->get();

        $asset = FAHeaderDtl::where('item_code',  $item_code)->first();
        
        if(count($item_exist) > 0){
            return 0;
        }

        else if(count($item_exist_on_another_disposal) > 0){
            foreach ($item_exist_on_another_disposal as $record) {
                $header = Fa_disposal_header::where('disposal_no', $record->disposal_no)->get()[0];
               
                if ($header->status == '001' || $header->status == '006') {
                    return -1;
                }

                else{
                    return FAHeaderDtl::where('item_code',  $item_code)->whereIn('status', ['A', 'I'])->first(); 
                }
            }
        }

        else if($asset->status == 'I'){
            return 2;
        }

        else
        {
            return FAHeaderDtl::where('item_code',  $item_code)->whereIn('status', ['A'])->first();
        }
    }

    public function store_disposal_asset(Request $request)
    {
        $attributes = $request->validate([
            'asset_code' => 'required',
            'item_code' => 'required',
            'disposal_no' => 'required',
            'curr_cost' => 'required',
            'disposal_amt' => 'required',
        ]);

        if ($request->disposal_method !== 'SEL') {
            if ($request->curr_cost > 0) {
                
            }
        }else{

            if ($request->disposal_amt < 1) {
                $request->validate([
                    'curr_cost' => 'lt:1'
                ], [
                    'curr_cost.lt' => 'You are selling an asset at a value less than 1.'
                ]);
            }            

        }

        $asset_code = trim($request->asset_code);

        $disposal_no = trim($request->disposal_no);

        $item_code = trim($request->item_code);

        $asset_item = FAHeaderDtl::where('asset_code', $asset_code)->where('item_code', $item_code)->get()[0];

        $asset = FAHeader::where('asset_code', $asset_code)->get()[0];

        $disposal = Fa_disposal_header::where('disposal_no', $disposal_no)->get()[0];

        $currency_rate = $disposal->currency_rate;
       
        $disposal_amount = str_replace(',', '', $request->disposal_amt);

        $profit = str_replace(',', '', $request->profit);

        $loss = str_replace(',', '', $request->loss);

        $disposal_amount = str_replace(',', '', $request->disposal_amt);

        $name = trim($request->buyers_name);
        $address = trim($request->physical_address);
        $mobile_no = trim($request->mobile_no);
        $email = trim($request->email);

        $deduct_salary = trim($request->deduct_salary);
        if($deduct_salary =='Y'){

            $request->validate([
                'dr_account' => 'required'
            ]);

        }
        $dr_account = trim($request->dr_account);
        $slhead = trim($request->subaccount);


        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try 
        {
            ##create asset item disposal details
            $create = Fa_disposal_dtls::create([
                'asset_code' => $asset_code,
                'item_code' => $item_code,
                'disposal_no' => $disposal_no,
                'created_at' => Carbon::now(),
                'created_by' => trim(auth()->id()),
                'asset_name' =>  $asset_item->item_name,
                'asset_description' =>  $asset_item->item_descr,
                'profit_amount' =>  $profit,
                'loss_amount' =>  $loss,
                'disposal_method' =>  $request->disposal_method,
            ]);

            $accounts = DB::table('nllinkmst')->whereIn('trans_type', ['FADEPRE5','FADEPRE3','FADEPRE4'])->get();

            if(count($accounts) > 0){
                foreach ($accounts as $account) {
                    $items_count = Fa_disposal_dtls_items::where('disposal_no', $disposal_no)
                    ->where('asset_code', $asset_code)->where('item_code', $item_code)->count();
                    #asset control acc
                    if($account->trans_type == 'FADEPRE4'){
                        //$analysed_field = $account->analysed_field;
                        $amount =  $asset_item->original_cost;
                        $control_account = $asset->control_ac;
                    }

                    #profit/loss on disposal
                    else if($account->trans_type == 'FADEPRE3'){

                        if ($request->disposal_method !== 'SEL') {
                            $amount =  intval(str_replace(",", "", $request->curr_cost));
                            $control_account = $asset->disposal_ac;
                        }else{

                            $amount =  $asset_item->original_cost - ($asset_item->prior_depr + $asset_item->depr_curr_year);
                            $control_account = $asset->disposal_ac;
                        }
                    }

                    #accum depre on disposal
                    else if($account->trans_type == 'FADEPRE5'){
                        if ($request->disposal_method !== 'SEL') {
                            $amount = $asset_item->original_cost - intval(str_replace(",", "", $request->curr_cost));
                            $control_account = $asset->depr_ac;
                        }else{
                            $amount = $asset_item->prior_depr + $asset_item->depr_curr_year;
                            $control_account = $asset->depr_ac;
                        }
                    }

                    if(abs($amount) > 0){

                        
                        $create_record = Fa_disposal_dtls_items::create([
                            'asset_code' => $asset_code,
                            'item_no' => $items_count + 1,
                            'item_code' => $item_code,
                            'disposal_no' => $disposal_no,
                            'foreign_amount' => $amount,
                            'dr_cr' => $account->dr_cr,
                            'local_amount' => $amount *  $currency_rate,
                            'created_at' => Carbon::now(),
                            'created_by' => trim(auth()->id()),
                            'acc_description' => $account->description,
                            'control_account' => $control_account
                        ]);

                    }

                }
            }

            if($request->disposal_method == 'SEL'){
                $create_buyer = FADisposalBuyers::create([
                    'name'=>$name,
                    'physical_address'=>$address,
                    'mobile_no'=>$mobile_no,
                    'email'=>$email,
                    'asset_code'=>$asset_code,
                    'item_code'=> $item_code,
                    'disposal_no' => $disposal_no,
                ]);

                ##debit bank
                $get_gl_bnk_acc = Glbank::where('bank_acc_code', $disposal->bank_acc_code)->get()[0];

                if($deduct_salary == 'Y'){
                    $nlparams = Nlparams::where('prid', 'GLH')->whereRaw("trim(prsno) = '" . $dr_account . "'")->get()[0];
                }else{
                    $nlparams = Nlparams::where('prid', 'GLH')->whereRaw("trim(prsno) = '" . $get_gl_bnk_acc->prsno . "'")->get()[0];
                }
                
                
                $create_record = Fa_disposal_dtls_items::create([
                    'asset_code' => $asset_code,
                    'item_no' => Fa_disposal_dtls_items::where('disposal_no', $disposal_no)->where('asset_code', $asset_code)->where('item_code', $item_code)->count() + 1,
                    'item_code' => $item_code,
                    'disposal_no' => $disposal_no,
                    'foreign_amount' => $disposal_amount,
                    'dr_cr' => $nlparams->normal_dr_cr == 'C' ? 'CR' : 'DR',
                    'local_amount' => $disposal_amount *  $currency_rate,
                    'created_at' => Carbon::now(),
                    'created_by' => trim(auth()->id()),
                    'acc_description' => $nlparams->prdesc,
                    'control_account' => $deduct_salary == 'Y' ? $dr_account : $nlparams->prsno,
                    'slhead' => $deduct_salary == 'Y' ? $slhead : null,
                    'slhead_flag' => $deduct_salary
                ]);

                ##credit the disposal account
                $account = DB::table('nllinkmst')->where('trans_type', 'FADEPRE3')->get()[0];

                $create_disposal_cr_record = Fa_disposal_dtls_items::create([
                    'asset_code' => $asset_code,
                    'item_no' => Fa_disposal_dtls_items::where('disposal_no', $disposal_no)->where('asset_code', $asset_code)->where('item_code', $item_code)->count() + 1,
                    'item_code' => $item_code,
                    'disposal_no' => $disposal_no,
                    'foreign_amount' => $disposal_amount,
                    'dr_cr' => 'CR',
                    'local_amount' => $disposal_amount *  $currency_rate,
                    'created_at' => Carbon::now(),
                    'created_by' => trim(auth()->id()),
                    'acc_description' => $account->description,
                    'control_account' => $asset->disposal_ac
                ]);
            }

            $dr_amount = Fa_disposal_dtls_items::where('disposal_no', $disposal_no)->where('asset_code', $asset_code)
            ->where('item_code', $item_code)->where('dr_cr', 'DR')->sum('foreign_amount');

            $cr_amount = Fa_disposal_dtls_items::where('disposal_no', $disposal_no)->where('asset_code', $asset_code)
            ->where('item_code', $item_code)->where('dr_cr', 'CR')->sum('foreign_amount');

            $upd_diposal_dtls = Fa_disposal_dtls::where('disposal_no', $disposal_no)->where('asset_code', $asset_code)
            ->where('item_code', $item_code)->update([
                'amount' => $dr_amount,
                'local_amount' => $dr_amount * $currency_rate,
                'foreign_dr_amount' => $dr_amount,
                'local_dr_amount' => $dr_amount * $currency_rate,
                'foreign_cr_amount' => $cr_amount,
                'local_cr_amount' => $cr_amount * $currency_rate,
            ]);

            $this->upd_disposal_header($disposal_no);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            return response()->json(['status' => 1, 'disposal_no' => $disposal_no, 'item_code' => $item_code, 'asset_code' => $asset_code]);
        } 
        
        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            return response()->json(['status' => 0]);
        }
    }

    public function disposal_asset_dtls(Request $request)
    {
        $disposal = Fa_disposal_header::where('disposal_no', trim($request->disposal_no))->get()[0];

        $asset = Fa_disposal_dtls::where('disposal_no', trim($request->disposal_no))
        ->where('asset_code', trim($request->asset_code))->where('item_code', trim($request->item_code))->first();

        $buyer = FADisposalBuyers::where('disposal_no', trim($request->disposal_no))->where('asset_code', trim($request->asset_code))
        ->where('item_code', trim($request->item_code))->first();

        return view::make('gl.fa.disposals.asset_dtls', compact('asset', 'disposal', 'buyer'));
    }

    public function disposal_asset_items_datatab(Request $request)
    {
        $items = Fa_disposal_dtls_items::where('disposal_no', trim($request->disposal_no))
        ->where('asset_code', trim($request->asset_code))->where('item_code', trim($request->item_code))->get();

        return Datatables::Of($items)->make(true);
    }

    public function ready_to_post_disposal(Request $request)
    {
        $disposal_no = trim($request->disposal_no);
       
        $disposal = Fa_disposal_header::where('disposal_no', $disposal_no)->get()[0];

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try 
        {
            if($disposal->status == '001'  && $disposal->amount > 0)
            {
                $upd = Fa_disposal_header::where('disposal_no', $disposal_no)->update([
                    'status' => '006',
                    'updated_by' => trim(auth()->id()),
                    'dola' => Carbon::now()
                ]);

                Session::Flash('success', 'Disposal header is ready to be posted'); 
                DB::connection(env('DB_CONNECTION'))->commit();
                DB::connection(env('DB_CONNECTION1'))->commit();
            }
        }

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            return response()->json(['status' => 0]);
        }
    }

    public function reopen_disposal_header(Request $request)
    {
        $disposal_no = trim($request->disposal_no);
       
        $disposal = Fa_disposal_header::where('disposal_no', $disposal_no)->get()[0];

        $batch_no = $disposal->batch_no;

        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try 
        {
            if($disposal->status == '006' && !$batch_no)
            {
                $upd = Fa_disposal_header::where('disposal_no', $disposal_no)->update([
                    'status' => '001',
                    'updated_by' => trim(auth()->id()),
                    'dola' => Carbon::now()
                ]);

                Session::Flash('success', 'Disposal header successfully reopened'); 
                DB::connection(env('DB_CONNECTION'))->commit();
                DB::connection(env('DB_CONNECTION1'))->commit();
            }
        }

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            return response()->json(['status' => 0]);
        }
    
        
    }

    public function cancel_disposal_header(Request $request)
    {
        $disposal_no = trim($request->disposal_no);
       
        $disposal = Fa_disposal_header::where('disposal_no', $disposal_no)->get()[0];

        $batch_no = $disposal->batch_no;
       
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try 
        {
            if($disposal->status == '005'){
                return -1;
            }

            else if($disposal->status == '006'){
                return 2;
            }

            else if($disposal->status == '004'){
                return 3;
            }

            else if($disposal->status == '001' && !$batch_no){
                $upd = Fa_disposal_header::where('disposal_no', $disposal_no)->update([
                    'status' => '005',
                    'updated_by' => trim(auth()->id()),
                    'dola' => Carbon::now(),
                    'cancelled_by' => trim(auth()->id()),
                    'reason_cancelled' => trim($request->reason_cancelled),
                    'cancelled_date' => Carbon::now(),
                ]);

                //Session::Flash('success', 'Disposal header successfully cancelled'); 
                DB::connection(env('DB_CONNECTION'))->commit();
                DB::connection(env('DB_CONNECTION1'))->commit();
                return 1;
            }
        }

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
            //dd($th);
            return response()->json(['status' => 0]);
        }
    
        
    }

    public function delete_disposal_asset(Request $request)
    {
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try 
        {
            $asset_code = trim($request->asset_code);

            $disposal_no = trim($request->disposal_no);

            $item_code = trim($request->item_code);

            $delete_asset_dtls = Fa_disposal_dtls_items::where('disposal_no', $disposal_no)->where('asset_code', $asset_code)
            ->where('item_code', $item_code)->delete();

            $delete_asset = Fa_disposal_dtls::where('disposal_no', $disposal_no)->where('asset_code', $asset_code)
            ->where('item_code', $item_code)->delete();

            $delete_buyer = FADisposalBuyers::where('disposal_no', $disposal_no)->where('asset_code', $asset_code)
            ->where('item_code', $item_code)->delete();
       
            ##update disposal header
            $this->upd_disposal_header($disposal_no);

            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
        }

        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();
        }
    }

    public function upd_disposal_header($disposal_no)
    {
        $no_of_items = Fa_disposal_dtls::where('disposal_no', $disposal_no)->count();

        $header_dr_amount = Fa_disposal_dtls::where('disposal_no', $disposal_no)->sum('foreign_dr_amount');

        $header_local_dr_amount = Fa_disposal_dtls::where('disposal_no', $disposal_no)->sum('local_dr_amount');

        $header_cr_amount = Fa_disposal_dtls::where('disposal_no', $disposal_no)->sum('foreign_cr_amount');

        $header_local_cr_amount = Fa_disposal_dtls::where('disposal_no', $disposal_no)->sum('local_cr_amount');

        $upd_disposal_header = Fa_disposal_header::where('disposal_no', $disposal_no)->update([
            'amount' => $header_dr_amount,
            'local_amount' => $header_local_dr_amount,
            'foreign_dr_amount' => $header_dr_amount,
            'local_dr_amount' => $header_local_dr_amount,
            'foreign_cr_amount' => $header_cr_amount,
            'local_cr_amount' => $header_local_cr_amount,
            'no_of_items' => $no_of_items
        ]);
    }

    public function get_disposal_methods(){
        return FADisposalMethods::all();
    }

    public function get_buyers(Request $request){
        $driver = DB::getDriverName();
        $searchTerm = strtoupper($request->term);
        if ($driver === 'pgsql') {
            $buyers = FADisposalBuyers::whereRaw("TRIM(name) ILIKE ?", ['%' . $searchTerm . '%'])->get();
        } else {
            $buyers = FADisposalBuyers::whereRaw("UPPER(TRIM(name)) LIKE ?", ['%' . $searchTerm . '%'])->get();
        }

        foreach($buyers as $buyer){

            $results[] = [
                'label' => $buyer->name,'value'=>$buyer->name,'address'=>$buyer->physical_address,'mobile'=>$buyer->mobile_no,'email'=>$buyer->email];
        }

        return Response::Json($results);

    }

    public function getdisposaldrAcct(Request $request)
    {

        if($request->deduct <> 'Y'){

            $dr_accounts = Nlparams::where('prid', 'GLH')
            ->where('status', '!=', 'D')->where('bank_flag','Y')->get();

        }else{

            $dr_accounts = Nlparams::where('prid', 'GLH')
            ->where('status', '!=', 'D')->where('bank_flag','N')->get();

        }

        echo  $dr_accounts;
    }

    
    public function approve_disposal(Request $request)
    {


        $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];

        $today = Carbon::today();

        $period_year = $today->format('Y');
        $period_month = $today->format('m');
        $doc_type = Doctype::where('doc_type','DSP')->first();
        $doc_type2 = Doctype::where('doc_type','DSPREV')->first();

        $batch_src = 'AP';

        $getsources = Glsource::where('source_code', $batch_src)->first();

        $new_batch = new GenerateBatch($period_year, $period_month, $batch_src);
        $batch_no = $new_batch->generate(); 
                            
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try 
        {

            $new_batch = Batch_list::create([
                'batch_no' => $batch_no,
                'batch_source' => "AP",
                'batch_title' => "ASSET DISPOSAL - ".$period_month."/".$period_year,
                'batch_description' => "ASSET DISPOSAL - ".$period_month."/".$period_year,
                'account_year' => $period_year,
                'account_month' => $period_month,
                'batch_type' => $request->batch_type,
                'created_date' => Carbon::now(),
                'created_by' => Auth::user()->user_name,
                'updated_date' => Carbon::now(),
                'updated_by' => Auth::user()->user_name,
                'no_entries' => 0,
                'process_code' => $doc_type->serial_no,
                'process_code_2' => $doc_type2->serial_no,
                'local_batch_amount' => 0,
                'expected_batch_total' => 0,
                'batch_status' => '002',
                'doc_type' => $request->batch_type == 'REV' ? 'PAY' : $request->batch_type
            ]);


            $upd_glsrc = Glsource::where('source_code', $batch_src)->update([
                'batch_serial' => $getsources->batch_serial + 1,
            ]);

            $disposal = Fa_disposal_header::where('disposal_no', trim($request->disposal_no))->get()[0];

            $params = [
                "batch_no" => $batch_no,
                "batch_source" => $batch_src,
                "batch_type" => $request->batch_type,
                "office_c" => $request->offcd,
                "entry_header" => $request->disposal_no,
                "currency" => $disposal->currency_code,
                "curr_rate" => intval($request->curr_rate),
                "t_date" => Carbon::now(),
                "entry_desc" => "ASSET DISPOSAL - ".$period_month."/".$period_year
            ];
    
            $request = new Request($params);

            $apBatchController = new ApBatchController();
            $response = $apBatchController->AddEntrydtl($request);

            $bindings = [
                "batch_no" => $batch_no,
                "period_month" => $period_month,
                "period_year" => $period_year
            ];

            $request = new Request($bindings);
            $apBatchController = new ApBatchController();
            $postbatch = $apBatchController->PostBatch($request);
                                                                          
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            return response()->json(['status' => 1]);
        } 
        
        catch (\Throwable $th) 
        {
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();

            return response()->json(['status' => 0]);
        }
    }

        //!----------   requisistion approval----------------
        public function reqApproval(Request $request){
            

            $new_approval = $request->appr;
            $req_no = trim($request->disp_no);
            $approval_process = trim($request->approval_process);
            DB::connection(env('DB_CONNECTION'))->beginTransaction();
            DB::connection(env('DB_CONNECTION1'))->beginTransaction();
            
            try{
                $apprObj = new ApprovalsMgt();
                $total_records = Approvals::count() + 1;
                $formatted_code = sprintf("%02d", $total_records);
                
                if($request->re_escalate == 'Y'){
                    $apprObj->re_escalate_approval($request);
                    Session::flash('success','Successfully re-escalated approval');
                }
                else{
                    
                    if($approval_process == 'REQ'){
                        $type = 'REQ';
                        $claim_no =  $req_no;
                    }
                    if($approval_process == 'ITEMREQ'){
                        $type = 'SIR';
                        $claim_no =  $req_no;
                    }
                    if($approval_process == 'REQAP'){
                        $type = 'REQAP';
                        $claim_no =  $request->req_no;

                    }
                    if($approval_process == 'APAPR'){
                        $type = 'APAPR';
                        $claim_no = $request->invoice_no;

                    }
                    if($approval_process == 'PYQAP'){
                        $type = 'PYQAP';
                        $claim_no =  $request->req_no;

                    }
                    if($approval_process == 'POINV'){
                        $type = 'INV';
                        $invoice_no = $request->invoice_no;
                        $vendor_id = $request->vendor_id;
                        $po_number = $request->po_number;
                        $claim_no =  $invoice_no;
                    }
    
                    if($approval_process == 'PO'){
                        $claim_no =  $request->po_number;;
                    }
    
                    if($approval_process == 'ITEMDISP'){
                        $claim_no =  $request->disp_no;
                        $type = 'DISP';
                    }
                    if($approval_process == 'RECREV'){
                        $claim_no =  $request->disp_no;
                        $type = 'RECREV';
                    }
                    if($approval_process == 'ASTREQ'){
                        $claim_no =  $request->disp_no;
                        $type = 'ASTREQ';
                    }
                    if($approval_process == 'REPREQ'){
                        $claim_no =  $request->disp_no;
                        $type = 'REPREQ';
                    }
                    if($approval_process == 'BGTAPR'){
                        $claim_no =  $request->disp_no;
                        $type = 'BGTAPR';
                    }
                    if($approval_process == 'BGTREF'){
                        $claim_no =  $request->disp_no;
                        $type = 'BGTREF';
                    }
                    if($approval_process == 'APRGLR'){
                        $claim_no =  $request->disp_no;
                        $po_number = substr($request->disp_no, 0, -5);
                        $type = 'APRGLR';
                    }
                    if($approval_process == 'APRGLB'){
                        $claim_no =  $request->disp_no;
                        $po_number = substr($request->disp_no, 0, -5);
                        $type = 'APRGLB';
                    }
                    if($approval_process == 'REVPV'){

                        $dtrans_no =  substr($request->disp_no, 0, 6);
                        $account_year =  substr($request->disp_no, 6, 10);

                        $claim_no =  $dtrans_no;
                        $type = 'REVPV';
                    }
                    if($approval_process == 'BNKTR'){
                        
                        $claim_no =  $request->disp_no;
                        
                        $req_no =  $request->disp_no;
                        
                        $type = 'BNKTR';
                    }

                    if($approval_process == 'REQAP' || $approval_process == 'PYQAP'){
                        $approval = new Approvals;
                        $approval->approval_id = $formatted_code;
                        $approval->process_code = $request->process_code;
                        $approval->req_no = $request->req_no;
                        $approval->type = $type;
                        $approval->description = $request->appr_notes;
                        $approval->user_id = Auth::user()->user_id;
                        $approval->dola = Carbon::now();
                        $approval->date_created = Carbon::now();
                        $approval->claim_no = $request->claim_no ?? $request->req_no;

                    }else if($approval_process == 'APAPR'){

                        $approval = new Approvals;
                        $approval->approval_id = $formatted_code;
                        $approval->process_code = $request->process_code;
                        $approval->req_no = $request->invoice_no;
                        $approval->type = $type;
                        $approval->description = $request->appr_notes;
                        $approval->user_id = Auth::user()->user_id;
                        $approval->dola = Carbon::now();
                        $approval->date_created = Carbon::now();
                        $approval->claim_no = $claim_no;

                    }
                    else{
                        $approval = new Approvals;
                        $approval->approval_id = $formatted_code;
                        $approval->process_code = $request->process_code;
                        $approval->req_no = $req_no;
                        $approval->type = $type;
                        $approval->description = $request->appr_notes;
                        $approval->user_id = Auth::user()->user_id;
                        $approval->dola = Carbon::now();
                        $approval->date_created = Carbon::now();
                        $approval->invoice_no = $invoice_no;
                        $approval->vendor_id = $vendor_id;
                        $approval->po_number = $po_number;
                        $approval->claim_no = $claim_no;
                    } 

                    if($approval_process == 'RECREV'){

                        $dtrans_no =  substr($request->disp_no, 0, 6);
                        $account_year =  substr($request->disp_no, 6, 10);
                        $cbmast = Cbmast::where('doc_type','REC')->where('dtrans_no',$dtrans_no)->where('account_year',$account_year)->first();

                        $approval->account_year =  $cbmast->account_year;
                        $approval->account_month =  $cbmast->account_month;
                        $approval->invoice_no =  $cbmast->offcd;
                        $approval->po_number =  $cbmast->entry_type_descr;
                        $approval->claim_no = $dtrans_no;

                    }

                    if($approval_process == 'REVPV'){

                        $dtrans_no =  substr($request->disp_no, 0, 6);
                        $account_year =  substr($request->disp_no, 6, 10);
                        $cbmast = Cbmast::where('doc_type','PAY')->where('dtrans_no',$dtrans_no)->where('account_year',$account_year)->first();

                        $approval->account_year =  $cbmast->account_year;
                        $approval->account_month =  $cbmast->account_month;
                        $approval->invoice_no =  $cbmast->offcd;
                        $approval->po_number =  $cbmast->entry_type_descr;
                        $approval->claim_no = $dtrans_no;

                    }

                    $account_month=$cbmast->account_month;
                    $offcd = $cbmast->offcd;
                    $entry_type_descr = $cbmast->entry_type_descr;

                    $approval->save();

                    for($i=0;$i < count($new_approval);$i++){

                        $flow = new Approval_flow;
                        $flow->approval_id = $approval->approval_id;
                        $flow->approval_level = $new_approval[$i]['level_id'];
                        $flow->approver_id = $new_approval[$i]['approver'];
                        $flow->status = 'P';
                        $flow->dola = Carbon::now();
    
                        //set next level details
                        $start = null;
                        $final = null;
    
                        if($type = 'INV')
                        {
                            if($i == 0){
                                $start = 'Y';
        
                                // notify the first approver
                                $category = 'PURCHASE ORDER INVOICE APPROVAL';
                                $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                                $email = $approver->email;
                                $name = Auth::user()->name;
                                $message = "Kindly Approve this invoice: ".$invoice_no."";
        
                                $apprObj->send_email($category,$email,$message,$name);
                            }
                        }
                        if($type = 'REQAP')
                        {
                            if($i == 0){
                                $start = 'Y';
                                // notify the first approver
                                $category = 'CLAIM REQUISITION APPROVAL';
                                $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                                $email = $approver->email;
                                $name = Auth::user()->name;
                                $message = "Kindly Approve this claim requisition: ".$request->req_no."";
        
                                $apprObj->send_email($category,$email,$message,$name);
                            }
                        }
                        if($type = 'APAPR'){
  
                            if($i == 0){
                                $start = 'Y';
                                // notify the first approver
                                $category = 'AP PAYMENT APPROVAL';
                                $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                                $email = $approver->email;
                                $name = Auth::user()->name;
                                $message = "Kindly Approve this AP payment: ".$request->invoice_no."";
        
                                $apprObj->send_email($category,$email,$message,$name);
                            }

                        }
                        if($type = 'PYQAP')
                        {
                            if($i == 0){
                                $start = 'Y';
                                // notify the first approver
                                $category = 'CLAIM PAYMENT APPROVAL';
                                $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                                $email = $approver->email;
                                $name = Auth::user()->name;
                                $message = "Kindly Approve this claim payment requisition: ".$request->req_no."";
        
                                $apprObj->send_email($category,$email,$message,$name);
                            }
                        }
                        elseif($type = 'RECREV')
                        {
                            if($i == 0){
                                $start = 'Y';
        
                                // notify the first approver
                                $category = 'RECEIPT REVERSAL APPROVAL';
                                $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                                $email = $approver->email;
                                $name = Auth::user()->name;
                                $message = "Kindly Approve this Receipt: ".$claim_no." For reversal";
        
                                $apprObj->send_email($category,$email,$message,$name);
                            }
                        }
                        elseif($type = 'BGTAPR')
                        {
                            if($i == 0){
                                $start = 'Y';
        
                                // notify the first approver
                                $category = 'BUDGET APPROVAL';
                                $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                                $email = $approver->email;
                                $name = Auth::user()->name;
                                $message = "Kindly Approve this Budget: ".$claim_no." ";
        
                                $apprObj->send_email($category,$email,$message,$name);
                            }
                        }
                        elseif($type = 'BNKTR')
                        {
                            if($i == 0){
                                $start = 'Y';
        
                                // notify the first approver
                                $category = 'BATCH APPROVAL';
                                $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                                $email = $approver->email;
                                $name = Auth::user()->name;
                                $message = "Kindly Approve this BATCH: ".$claim_no." ";
        
                                $apprObj->send_email($category,$email,$message,$name);
                            }
                        }
                        elseif($type = 'BGTREF')
                        {
                            if($i == 0){
                                $start = 'Y';
        
                                // notify the first approver
                                $category = 'BUDGET REFOCUS APPROVAL';
                                $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                                $email = $approver->email;
                                $name = Auth::user()->name;
                                $message = "Kindly Approve this Budget: ".$claim_no." For refocusing ";
        
                                $apprObj->send_email($category,$email,$message,$name);
                            }
                        }
                        elseif($type = 'ASTREQ')
                        {
                            if($i == 0){
                                $start = 'Y';
        
                                // notify the first approver
                                $category = 'ASSET REQUEST APPROVAL';
                                $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                                $email = $approver->email;
                                $name = Auth::user()->name;
                                $message = "Kindly Approve this Asset request: ".$claim_no."";
        
                                $apprObj->send_email($category,$email,$message,$name);
                            }
                        }
                        else
                        {
                            if($i == 0){
                                $start = 'Y';
        
                                // notify the first approver
                                $category = 'DISPOSAL APPROVAL';
                                $approver = Aimsuser_web::where('user_id',$new_approval[$i]['approver'])->first();
                                $email = $approver->email;
                                $name = Auth::user()->name;
                                $message = "Kindly Approve this Disposal: ".$req_no."";
        
                                $apprObj->send_email($category,$email,$message,$name);
                            }
                        }
                        
                        
                        
                        if(($i +1) == count($new_approval)){
                            $final = 'Y';
                            $next_level = null;
                            $next_approver = null;
                        }
                        else{
                            $next_level = $new_approval[$i+1]['level_id'];
                            $next_approver = $new_approval[$i+1]['approver'];
                        }
                        $flow->start_approval = $start;
                        $flow->end_approval = $final;
                        $flow->next_approval_level = $next_level;
                        $flow->next_approver_id = $next_approver;

                        $flow->save();
                        
                    }
    
                    
                }

                if($approval_process == 'RECREV'){

                    $dtrans_no =  substr($req_no, 0, 6);
                    $account_year =  substr($req_no, 6, 10);
                    $cbmast = Cbmast::where('doc_type','REC')->where('dtrans_no',$dtrans_no)->where('account_year',$account_year)->first();
                    $account_month=$cbmast->account_month;
                    $offcd = $cbmast->offcd;
                    $entry_type_descr = $cbmast->entry_type_descr;

                    Session::flash('success','Successfully raised approval');
                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();

                    return redirect()->route('receipts.details', [
                        'dtrans_no' => $dtrans_no,
                        'account_year' => $account_year,
                        'account_month' => $account_month,
                        'offcd' => $offcd,
                        'entry_type_descr' => $entry_type_descr
                    ]);
                    
                }
                elseif($approval_process == 'REVPV'){

                    $dtrans_no =  substr($req_no, 0, 6);
                    $account_year =  substr($req_no, 6, 10);
                    $cbmast = Cbmast::where('doc_type','PAY')->where('entry_type_descr','!=','PAY')->where('dtrans_no',$dtrans_no)->where('account_year',$account_year)->first();
                    $voucher_no=$cbmast->dtrans_no;
                    $doc_type = $cbmast->doc_type;
                    $account_year = $cbmast->account_year;

                    Session::flash('success','Successfully raised approval');
                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();

                    return redirect()->route('payments.details', [
                        'voucher_no' => $voucher_no,
                        'doc_type' => $doc_type,
                        'account_year' => $account_year
                    ]);
                    
                }
                elseif($approval_process == 'APRGLB' || $approval_process == 'APRGLR' ){

                    $budget_id =  $req_no;

                    Session::flash('success','Successfully raised approval');
                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();

                    return redirect()->route('gl.budget', [
                        'budget_id' => $budget_id
                    ]);
                    
                }
                else if($request->approval_process == 'REQAP'){

                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();

                    Session::flash('success','Approval has been Succesfully processed');
                    return redirect()->route('claims.req_process', ['req_no' => $request->req_no]);   

                }
                else if($request->approval_process == 'PYQAP'){

                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();

                    Session::flash('success','Approval has been Succesfully processed');
                    return redirect()->route('payments.requisitions.details', ['requisition_no' => $request->req_no]);   

                }
                else{

                    Session::flash('success','Successfully raised approval');
                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();
                    return redirect()->back();
                }

                Session::flash('success','Successfully raised approval');
                DB::connection(env('DB_CONNECTION'))->commit();
                DB::connection(env('DB_CONNECTION1'))->commit();
    
                
            }
            catch(\Throwable $e){
                // dd($e);
                DB::connection(env('DB_CONNECTION'))->rollback();
                DB::connection(env('DB_CONNECTION1'))->rollback();
                Session::flash('error','Failed to raise an approval');
    
                return redirect()->back();
            }
        }

        public function update_approval(Request $request){

            $req_no = trim($request->req_no);
            $approval_process = trim($request->approval_process);
            $data = [
                'approval_id' => $request->approval_id,
                'level_id' => $request->level_id,
                'status' => $request->status,
                'approval_process' => $request->approval_process,
                'approval_comment' => $request->comm,
                'payment_date'=>$request->actual_payment_date,

            ];

            $approval = Approvals::where('approval_id',$request->approval_id)->first();
            $disp = null;

            if (!in_array($request->approval_process, ['RECREV', 'ASTREQ','BGTAPR','BGTREF','REPREQ','REVPV','SIR','APRGLB','APRGLR'])) {
                $disp = Fa_disposal_header::where('disposal_no', $approval->req_no)->first();
            }

            DB::connection(env('DB_CONNECTION'))->beginTransaction();
            DB::connection(env('DB_CONNECTION1'))->beginTransaction();

            try{

                $apprObj = new ApprovalsMgt();
  
                $resp = $apprObj->change_approval_status($data);

                if($request->approval_process == 'REQAP'){

                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();

                    Session::flash('success','Approval has been Succesfully processed');
                    return redirect()->route('claims.req_process', ['req_no' => $approval->req_no]);   

                }
                if($request->approval_process == 'PYQAP'){

                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();

                    Session::flash('success','Approval has been Succesfully processed');
                    
                    return redirect()->route('payments.requisitions.details', ['requisition_no' => $approval->req_no]);   

                }
                if($request->approval_process == 'APAPR'){

                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();


                    Session::flash('success','Approval has been Succesfully processed');
                    
                    return redirect()->route('pyheaderdetails',['payment_header_no'=>$approval->req_no]);   

                }

                if($request->approval_process == 'RECREV'){

                    $dtrans_no =  substr($approval->req_no, 0, 6);
                    $account_year =  substr($approval->req_no, 6, 10);
                    $cbmast = Cbmast::where('doc_type','REC')
                                    ->where('entry_type_descr','!=','REC')
                                    ->where('dtrans_no',intval($dtrans_no))
                                    ->where('account_year',intval($account_year))
                                    ->first();
                    $account_month=$cbmast->account_month;
                    $offcd = $cbmast->offcd;
                    $entry_type_descr = $cbmast->entry_type_descr;

                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();

                    return redirect()->route('receipts.details', [
                        'dtrans_no' => $dtrans_no,
                        'account_year' => $account_year,
                        'account_month' => $account_month,
                        'offcd' => $offcd,
                        'entry_type_descr' => $entry_type_descr
                    ])->with('success','Approval has been Succesfully processed');

                }elseif($request->approval_process == 'ASTREQ' || $request->approval_process == 'REPREQ'){
                    $approval = Approvals::where('approval_id',$request->approval_id)->first();

                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();

                    return redirect()->route('fa.faacquisitiondtl', [
                        'id' => $approval->req_no
                    ])->with('success','Approval has been Succesfully processed');

                }
                elseif($request->approval_process == 'BGTAPR' || $request->approval_process == 'BGTREF'){
                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();
                    return redirect()->back()->with('success','Approval has been Succesfully processed');

                }
                elseif($request->approval_process == 'APRGLB' || $request->approval_process == 'APRGLR'){
                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();
                    
                    return redirect()->route('gl.budget');

                }
                elseif($request->approval_process == 'REVPV'){
                 
                    $dtrans_no =  substr($approval->req_no, 0, 6);
                    $account_year =  substr($approval->req_no, 6, 10);
                    $cbmast = Cbmast::where('doc_type','PAY')->where('entry_type_descr','!=','PAY')->where('dtrans_no',$dtrans_no)->where('account_year',$account_year)->first();
             
                    $voucher_no=$cbmast->dtrans_no;
                    $doc_type = $cbmast->doc_type;
                    $account_year = $cbmast->account_year;

                    Session::flash('success','Approval has been Succesfully processed');
                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();

                    return redirect()->route('payments.details', [
                        'voucher_no' => $voucher_no,
                        'doc_type' => $doc_type,
                        'account_year' => $account_year
                    ]);

          
                    

                }
                else if($request->approval_process == 'BNKTR'){
                    
                    if($request->status == 'A'){

                        $batchTypeMapping = [
                                'BANK TRANSFER' => 'BNF',
                                'BANK CHARGES' => 'BNC',
                                'BANK TRANSACTION' => 'BNT',
                            ];
                            
                        if (isset($request->batch_type)) {
                            $shortBatchType = $batchTypeMapping[$request->batch_type] ?? $request->batch_type; 
                            $request->merge(['batch_type' => $shortBatchType]);
                        }
                
                        $transaction_no = $approval->req_no;
                        
                        $period = Nlctrl::where('nlctrl', 0)->first();
                        
                        $request->period_year = $period->account_year;
                        $request->period_month = $period->account_month;

                        $new_bnk_batch = new BankingController;

                        $new_bnk_batch->BnkCreateBatch($request);
                    }  
                    $category = 'TRANSACTION REQUEST APPROVAL';

                    $message = "TRANSACTION REQUEST : ".$approval->req_no." has been approved.";

                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();
                
                    return redirect()->back()->with('success','Approval successfully processed.');

                }
                else{

                    $params = [
                        "batch_type" => 'FAD',
                        "disposal_no" => $disp->disposal_no,
                        "currency" => 0,
                        "curr_rate" => 1,
                        "offcd" => $disp->offcd
                    ];
    
                    $check_appr = Fa_disposal_header::where('disposal_no',$disp->disposal_no)->first();
    
                    if($check_appr->status == '004'){
    
                        $request = new Request($params);
                        $post = $this->approve_disposal($request);
    
                    }

                    DB::connection(env('DB_CONNECTION'))->commit();
                    DB::connection(env('DB_CONNECTION1'))->commit();
                    return redirect()->back()->with('success','Approval has been Succesfully processed');

                }


            }
            catch(\Throwable $e){

                DB::connection(env('DB_CONNECTION'))->rollback();
                DB::connection(env('DB_CONNECTION1'))->rollback();
               
            }

        }

    

}
