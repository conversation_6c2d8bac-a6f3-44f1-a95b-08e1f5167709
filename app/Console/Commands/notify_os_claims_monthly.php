<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\Aimsuser;
use App\ProcessEmailNotification;
use App\Events\DispatchNotificationEvent;
class notify_os_claims_monthly extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notify_os_claims_monthly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notifies about claims that have been OS for more than six months';

    /**
     * Execute the console command.
     */
    public function handle()
    {
       $notifyOsClaims = DB::table('new_claims_parameters')->first();	
     


       $claims = DB::table('clhmn as c')
       ->select('c.claim_no', 'c.date_reg','c.acc_date','c.date_notified', 'c.curr_total_estimate', 'c.policy_no', 'c.policyholder', 'c.class','c.description as cause_narration','b.description as cause_of_loss','d.currency')
       ->join('cause as b', 'b.cause_code', '=', 'c.cause_code')
       ->join('currency as d', 'd.currency_code','=', 'c.currency_code')
       ->where(function($query) {
        $query->where('c.rejected', '<>', 'Y')
              ->orWhereNull('c.rejected');
        })
        ->where(function($query) {
          $query->where('c.closed', '=', 'N')
                ->orWhereNull('c.closed');
          })
       ->get();
        $osClaims = [];
        $date_Reg_Array = [];
        foreach ($claims as $claim) {
        $dateReg = Carbon::parse($claim->date_reg);
        $diffMonths = $dateReg->diffInMonths(Carbon::now());
        $date_reg = $dateReg->toDateString();

            if ($diffMonths >= $notifyOsClaims->notify_os_claims) {
                $osClaims[] = $claim;
            }


        }
        if (!empty($osClaims)) {
            try {
                $process_email = ProcessEmailNotification::where('process_slug', 'outstanding-claim-notification')->first();
                $notify_user = Aimsuser::where('email', $process_email->process_email)->value('name');
                
                if ($process_email) {

                    $notificationData = [
                        'osClaims' => $osClaims,
                        'notify_user' => $notify_user,
                        'months'=>  $notifyOsClaims->notify_os_claims

                    ];
                 
                    DispatchNotificationEvent::dispatch('notify-outstanding-claims', $notificationData);
                }
            } catch (\Exception $e) {
                
            }
        }
    }
}

