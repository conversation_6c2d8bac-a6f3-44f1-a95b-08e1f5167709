<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Events\DispatchNotificationEvent;
use app\Models\TatEscalationEmails;
use App\Models\TatActivities;
use App\Models\TatProcesses;
use App\Models\TatEscalations;
use App\Models\TatProcessActivities;
use App\Models\TatProcessTable;
use Illuminate\Support\Facades\Route;
use Carbon\Carbon;
use DB;

class SLANotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:sla-notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        ## query TAT process tables
        $tatprocesses = TatProcesses::all();

        foreach ($tatprocesses as $p) {

            #fetch process tables 
            $process_table = TatProcessTable::where('process_id',$p->id)->first();
            $unique_items = $process_table->table_columns;
            $table = $process_table->table_name;
                
            $query = DB::table($table);
            foreach ($unique_items as $field => $value) {
                $query->where($field, $value);
            }
            $query->select(json_decode($unique_items));
            $query->groupBy(json_decode($unique_items));
            $results = $query->get();

            foreach ($results as $k) {
                # code...
                $record = DB::table($table);
                foreach ($k as $k => $value) {
                    $record->where($k, $value);
                }
                $record->orderBy('id','desc');
                $record->orderBy('updated_on','desc');
                $r = $record->first();

                if(empty($r->next_activity)){


                    ##check for next activity
                    $next_action = TatEscalations::where('current_activity',$r->current_activity)
                                ->where('process_id',$p->id)
                                ->first();

                    $getdays = TatEscalations::where('current_activity',$next_action->next_activity)
                                ->where('process_id',$p->id)
                                ->first();
                    $date = Carbon::parse($r->updated_on);
                    // Get today's date
                    $today = Carbon::now();
                    // Calculate the difference in days
                    $differenceInDays = $today->diffInDays($date);
                    if(($differenceInDays > (int)$getdays->days )&& ((int)$getdays->days > 0)){

                        ##send notification to people to take action
                        ##fetch emails
                        $getmail = DB::table('tat_escalation_email')
                                    ->where('process_id',$p->id)
                                    ->where('activity_id',$getdays->current_activity)
                                    ->get();
                        
                        $get_action = TatProcessActivities::where('id',$getdays->id)->first();
                        
                        foreach ($getmail as $mail) {

                            switch($p->process_slug){

                                case  'claim-payment-process':
                                    ##fetch action and process
                                    $claim_no = $r->claim_no; 
                                    $action =  $get_action->activity_description;
                                    
                                    $notificationData = [
                                        'reference'=> formatPolicyOrClaim($claim_no),
                                        'process'=> $p->process_description,
                                        'action' => $action,
                                        'days'=>$differenceInDays, 
                                        'recipient'=>$mail->email   
                                    ];
                                    DispatchNotificationEvent::dispatch($slug = 'tat-escalation', $notificationData);

                                    $this->info($mail->email);
                                break;
                                default:
                                # code...
                                break;
                            }
                        }
                    }else{

                        $this->info('No Record to be Sent');
                    }

                }
            }

        }
    }
}
