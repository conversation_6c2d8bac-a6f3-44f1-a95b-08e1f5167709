<?php
 
namespace App\Console\Commands;
 
use App\Pipcnam;
use App\SendEmail;
use Carbon\Carbon;
use App\Renewal_notice;
use App\Mail\SystemMail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;


class SendingEmail extends Command
{
    /** 
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:emails';
 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'send all the emails stored in the sendemail table';
 
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
 
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    
    public function handle()
    {
      $today = Carbon::now()->toDateString();

      try {
        $pipcnam = Pipcnam::first();

        $emails = SendEmail::whereDate('createdate', $today)
          ->where('status', 'SEND')
          ->whereNull('sentdate')
          ->get();
        
        if ($emails->isEmpty()) {
            $this->info('No emails to send today.');
            return;
        }

        foreach ($emails as $email) {
            if(!empty($email->receiver)){
              
              if(!App::environment('production'))
              {
                  $companyEmailDomain = explode('@', $pipcnam->email)[1];
                  $allowedDomains = [
                      'aimsoft.co.ke',
                      $companyEmailDomain
                  ];

                  $emailDomain = explode('@', $email->receiver)[1];
                  
                  if(!in_array($emailDomain,$allowedDomains))
                  {
                      continue;
                  }
              } 
              
              $data = [
                'receiver_name' => $email->receiver_name,
                'message' => $email->message, 
              ];
              $clientEmail = trim($email->receiver); 
              $copyReceivers = !is_null($email->copy_receiver) ? explode(',', $email->copy_receiver) : [];             
              $bccReceivers = !is_null($email->bcc_receiver) ? explode(',', $email->bcc_receiver) : [];
              $title = trim($email->category);
              $subject = "REF: $email->category";
              $createDate = Carbon::parse($email->createdate)->diffForhumans();
              

              $mailAttachments = DB::table('mail_attachments')->where('email_id', $email->emailid)->get();
              $attachmentDocs = [];
              foreach($mailAttachments as $attachment){
                $attachmentDocs[] = [
                  'document_name' => $attachment->document_name,
                  'document_path' => $attachment->document_path
                ];
              };
              
              $mail = Mail::to($clientEmail);

              if (!empty($copyReceivers)) {
                  $mail->cc(array_map('trim', $copyReceivers));
              }

              if (!empty($bccReceivers)) {
                  $mail->bcc(array_map('trim', $bccReceivers));
              }

              // Send the email
              $mail->send(new SystemMail($data, $title, $createDate, $subject, $attachmentDocs));

              SendEmail::where('emailid', $email->emailid)
                ->update([
                  'status' => 'SENT',
                  'sentdate' => Carbon::now(),
                ]);
              
              if (in_array($email->category, ['Renewal Notice', 'Renewal Reminder', 'Decline Letter', 'Decline Reminder'])) {

                
                $notificationRecords = SendEmail::whereIn('emailid', (array)$email->emailid)->get();
                
                foreach ($notificationRecords as $record) {
                  
                    Renewal_notice::where('email_id', trim($record->emailid))
                        ->update([
                            'renewal_notice_status' => 'SENT',
                            'sent_date' => Carbon::now()->format('Y-m-d'),
                        ]);
                
                }
              }

              if($email->category == 'Policy Schedule Document Notification'){
                $sentMails = SendEmail::where('category', $email->category)->get();
                foreach($sentMails as $mail){
                  DB::table('debited_pol_dispatch')
                      ->where('email_id',$mail->emailid)
                      ->update(['status'=> 'SENT', 'updated_at'=> Carbon::now()->format('Y-m-d H:i:s')]);
                }
              }

            }
        }
        
        $this->info('Today\'s email(s) have been sent successfully.');
      } catch (\Throwable $th) {
          $error_msg = json_encode($th->getMessage());
          $reference = "System notifications";
          $module = __METHOD__;
          $route_name = '';// Route::getCurrentRoute()->getActionName();
          log_error_details($route_name,$error_msg,$reference,$module);
          $this->error("Error: " . $th->getMessage());
      }

    }

}
