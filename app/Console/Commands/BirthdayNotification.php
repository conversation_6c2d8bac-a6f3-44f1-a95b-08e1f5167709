<?php

namespace App\Console\Commands;

use App\Client;
use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Events\DispatchNotificationEvent;

class BirthdayNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'birthday:notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send birthday notifications to clients';

    /**
     * Execute the console command.
     */
    public function handle()
    {
      $todayDate = Carbon::now()->format('m-d');

      $clients = Client::select('client_number', 'name', 'e_mail', 'mobile_no')
                  ->whereRaw("TO_CHAR(dob, 'MM-DD') = ?", [$todayDate])
                  ->get();

      try {
        foreach ($clients as $client) {
          $notificationData = ['client_data' => $client];
          DispatchNotificationEvent::dispatch($slug = 'client-birthday-notification', $notificationData);
        }
        $this->info('Birthday Notifications dispatched successfully.');
      } catch (\Throwable $th) {
        //throw $th;
        $this->info($th->getMessage());
      }
      
    }
}
