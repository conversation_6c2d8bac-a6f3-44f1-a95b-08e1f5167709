<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\User;
use App\PasswordPolicy;
use Carbon\Carbon;

class Deactivate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deactivate:user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'deactivate user over period of time';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $parameter = PasswordPolicy::select('inactive_days')->first();
        $inactive_days = (int) $parameter->inactive_days;
        $UserActivities = User::select('last_login')->get();

        foreach ($UserActivities as $UserActivity) {
            $last_login = $UserActivity->last_login;

            $last_login_date = Carbon::parse($last_login);
            $date_now =  Carbon::today();

            $interval = $last_login_date->diffInDays($date_now);

            if ($inactive_days <= $interval) {
                User::where('last_login', $last_login)->update(['left_company' => 'Y']);
            }
        }
    }
}
