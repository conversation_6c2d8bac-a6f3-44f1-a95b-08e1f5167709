<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\PeriodParamater;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\parameters\gl\GLParamsController;

use Log;

class AutoCloseGLMonth extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto:closeglmonth';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatic GL Period Closure based on set parameters';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $glperiod = PeriodParamater::where('record_type', 1)->get()[0];

        ##check if GL autoclose is Y

        if($glperiod->auto_close_gl == 'Y'){
            try{
                $auto_close = new GLParamsController();

                $val = $auto_close->autoclose_period();

                Log::alert($val);

                if($val == 1){
                    session(
                        [
                            'automonth_msg' => "END MONTH CLOSURE SUCCESSFUL.",
                            'automonth_showmsg' => true
                        ]
                    );    
                }
            }
            
            catch (Exception $e) {
                Log::alert($e);
            }

        }
         
    }
}
