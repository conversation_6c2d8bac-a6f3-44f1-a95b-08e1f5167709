<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Notification;
use App\Notifications\ErrorNotification;
use Illuminate\Support\Str;
use File;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use App\Edms_staging;
use App\Classes\Approvals\EdmsReuploadClass;

class EdmsDocumentUpload extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'edms:upload';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Upload documents to Edms on trigger';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {

            //count docs where integrated is not equal to Y(if its N),with error comments
            $pending_unsent_docs_count = Edms_staging:: where('integrated','<>','Y')->whereNull('error_on_resend')->count();
            
            if($pending_unsent_docs_count > 0 ){
                
                $pending_unsent_docs = Edms_staging::where('integrated','<>','Y')->whereNull('error_on_resend')->get();
              
                foreach ($pending_unsent_docs as $pending_unsent_doc){
                        $edmsUpload = new EdmsReuploadClass();
                        $edmsUpload->fetch_docs_from_staging($pending_unsent_doc->document_id,$pending_unsent_doc->document_code);
                    
                    }
         
            }
        } catch (\Throwable $e) {
            throw $th;

            
        }
          

    }


}
