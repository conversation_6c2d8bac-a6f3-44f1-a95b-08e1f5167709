<?php

namespace App\Console\Commands;

use App\Client;
use App\Pipcnam;
use App\Debitmast;
use App\Polmaster;
use App\SendEmail;
use Carbon\Carbon;
use App\Classes\AddToSendEmail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\SendPaymentReminder;
use App\Models\PaymentReminderControl;
use App\Classes\AddToNotificationsCron;

class SendPremPaymentReminder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:prem-payment-reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert premium payment reminders for clients into notifications_mail_cron table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $todayDate = Carbon::now()->toDateString();
        $reminderControlParam = PaymentReminderControl::first();
        $premReminderParam = SendPaymentReminder::first();

        if(!$premReminderParam || $reminderControlParam->send_reminder == 'N'){
            return $this->info('Parameter is not set');
        }

        $daysBeforeDueDate = $premReminderParam->days_before_due_date;
        $frequency = $premReminderParam->intervals;

        $interval = $daysBeforeDueDate / $frequency;

        $policies = DB::select('
            SELECT ppp.*, pm.cov_period_from, pm.cov_period_to
            FROM PREMIUM_PAYMENT_PLANS ppp
            JOIN polmaster pm ON pm.endorse_no = ppp.endt_renewal_no
            LEFT JOIN acdet ON ppp.endt_renewal_no = acdet.endt_renewal_no
            WHERE pm.status = \'N\'
            AND acdet.unallocated != 0');
        // dd(count($policies));
        DB::beginTransaction();
        
        try {
            $recordInserted = false;
            if(count($policies) > 0){
                foreach ($policies as $policy) {

                    // base transaction
                    $periodFrom = $policy->cov_period_from;
                    $periodTo = $policy->cov_period_to;
                    
                    $baseTransaction = Debitmast::select('policy_no','endt_renewal_no', 'entry_type_descr', 'gross_amount')
                                                  ->whereIn('entry_type_descr',['POL','REN','RNS'])
                                                  ->where('policy_no',$policy->policy_no)
                                                  ->where('period_from', '>=', $periodFrom)
                                                  ->where('period_to', '<=', $periodTo)
                                                  ->orderBy('account_year', 'DESC')
                                                  ->orderBy('dtrans_no', 'DESC')
                                                  ->first();

                    $policyRecord = Polmaster::where('endorse_no', $policy->endt_renewal_no)->first();
                    // dd($policyRecord->renewal_date);
                    $renewalDate = $policyRecord->renewal_date;
                    $polRen = $baseTransaction->endt_renewal_no;
                    $clientNo = Polmaster::where('policy_no', $policy->policy_no)->value('client_number');
                    $clientEmail = Client::where('client_number', $clientNo)->value('e_mail');
                    $clientName = Client::where('client_number', $clientNo)->value('name');
                    $category = 'PREMIUM PAYMENT REMINDER';
                    $company_name = Pipcnam::pluck('company_name')->first();
                    $dueDate = Carbon::parse($policy->due_date)->format('Y-m-d');
                    $message = "<html>
                      <body>
                          <p>Dear $clientName,</p>
                          <p>We hope this message finds you well.</p>
                          <p>This is a payment reminder regarding your policy $policyRecord->policy_no.</p>
                          <p>Your next installment is due on $dueDate.</p>
                          <p>Kindly make necessary arrangements.</p>
                          <p>Kind Regards,</p>
                          <p>$company_name</p>
                      </body>
                    </html>";
                    $policyNo = $policyRecord->policy_no;
                    if(is_null($clientEmail)){
                      $clientEmail = 'missing';
                    }
                    // $addEmail = new AddToNotificationsCron();

                    // delete if same record exists, then add afresh
                    DB::table('NOTIFICATIONS_MAIL_CRON')
                      ->where('policy_no', $policyNo)
                      ->where('category', $category)
                      ->where('dispatch_date',$policy->dispatch_date)
                      ->delete();
                    
                    // Calculate sending dates based on frequency
                    for ($i = 0; $i < $frequency; $i++) {
                      $dispatchDate = Carbon::parse($policy->due_date)->subDays($daysBeforeDueDate - ($i * $interval))->toDateString();
                      
                      // $emailRecord = $addEmail->createNotificationMailRecord($policyNo, $polRen, $category, $clientEmail, $message, $dispatchDate, 'premium-payment-reminder',$renewalDate);
                      $notificationCronRecord = new AddToNotificationsCron($category,$clientEmail,$message,$dispatchDate,'premium-payment-reminder');
                      $emailRecord = $notificationCronRecord->setPolicyNo($policyNo)
                                                            ->setPolren($polRen)
                                                            ->setRenewalDate($renewalDate)
                                                            ->setCreator('system')
                                                            ->setIsRenewed('N')
                                                            ->setIsCancelled('N')
                                                            ->createNotificationMailRecord();
                    
                      if ($emailRecord) {
                          $recordInserted = true;
                      }
                    }
                }
            }
            DB::commit();
            if($recordInserted){
                $this->info('Premium payment reminders added to notifications_mail_cron table successfully');
            } else {
                $this->info("There are no record(s) to insert into notifications_mail_cron table");
            }
        } catch (\Throwable $th) {
          // dd($th);
            DB::rollBack();
            $this->error('Error: ' . $th->getMessage());
        }
    }
}
