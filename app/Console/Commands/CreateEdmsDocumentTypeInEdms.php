<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Edmsdocument;
use App\Classes\Approvals\EdmsDocumentTypesClass;
use GuzzleHttp\Exception\RequestException;

class CreateEdmsDocumentTypeInEdms extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    // create edms document types as in aims to edms
    protected $signature = 'edms:doctype';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create document types as in aims to edms and attach metadata types';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Check if there are document types in aims that have not been created in edms
       

        $pending_unset_doctypes_count = Edmsdocument::where('set_in_edms','N')->count();
        

        if($pending_unset_doctypes_count > 0 ){
            $edmsCreateDocType = new EdmsDocumentTypesClass();
            
            $pending_unset_doctypes = Edmsdocument::where('set_in_edms','N')->get();
            
            foreach ($pending_unset_doctypes as $pending_unset_doctype){
                $document_description = $pending_unset_doctype->document_description;
                $document_code = $pending_unset_doctype->document_code;
                
                
                try {
                    $edmsCreateDocType = new EdmsDocumentTypesClass();
                    $edmsCreateDocType->create_edms_document_type($document_description,$document_code);
                } catch (\Throwable $e) {
                    $message = json_encode($e->getMessage());
                    $statusCode =  json_encode($e->getCode());
                    
                    if($message != null){
                        
                         Edmsdocument::where('document_code', $document_code)
                            ->update([
                                'error_comments'=> $message
                            ]);
                    }else{
                        throw $e;
                    }

                    
                }   
                
           }


        }
        return 0;
    }
}
