<?php

namespace App\Console\Commands;

use App\Clhmn;
use App\Client;
use App\Polmaster;
use Carbon\Carbon;
use App\Classes\AddToSendEmail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Classes\AddToNotificationsMailCron;

class SendOutstandingDocs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'request:outstanding-docs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'System request client to send outstanding documents';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::beginTransaction();

        try {
          $allContextIds = DB::table('EDMS_STAGING')
            ->pluck('context_id')
            ->toArray();

          $anyEmailsSent = false;

          foreach ($allContextIds as $number) {
              // Check if it's a policy number
              $policy = Polmaster::where('policy_no', $number)->first();

              if ($policy) {
                  $policyNumber = $policy->policy_no;
                  $clientNumber = $policy->client_number;
              } else {
                  // $number is not a policy_no, try to find it as a client_number
                  $client = Client::where('client_number', $number)->first();

                  if ($client) {
                      $clientNumber = $client->client_number;
                      $policy = Polmaster::where('client_number', $clientNumber)->first();
                      if (!$policy) {
                          continue;
                      }
                      $policyNumber = $policy->policy_no;
                  } else {
                      // $number is neither a policy_no nor a client_number
                      continue;
                  }
              }

              $name = $client->name;
              $email = $client->e_mail;
              if(is_null($email)){
                $email = 'missing';
              }

              // Check if the client has claims
              $policy_has_claim = Clhmn::where('policyholder', $name)->exists();

              // Determine process descriptions based on claims status
              $processDescriptions = $this->getProcessDescriptions($number, $policy_has_claim);
              
              // Fetch mandatory document codes for these processes
              $documentCodes = $this->getMandatoryDocumentCodes($processDescriptions);

              // Fetch provided document codes for the client
              $providedDocs = DB::table('EDMS_STAGING')
                  ->where('context_id', $number)
                  ->pluck('document_code')
                  ->toArray();

              // Determine which mandatory documents were not provided
              $missingDocs = array_diff($documentCodes, $providedDocs);

              // If there are missing documents, generate and send email
              if (!empty($missingDocs)) {
                $recordInserted = $this->sendEmailForMissingDocs($email, $missingDocs, $client->name, $policyNumber);
                  if ($recordInserted) {
                    $anyEmailsSent = true;
                  }
              }
          }

          DB::commit();

          $this->info($anyEmailsSent ? 'Outstanding documents processed successfully.' : 'No new records to add to sendemail');
        } catch (\Throwable $th) {
          DB::rollBack();
          $this->error('Error: ' . $th->getMessage());
          //dd($th);
        }

    }

    /**
     * Get the process descriptions based on claims status.
     */
    private function getProcessDescriptions($contextId, $policy_has_claim)
    {
        $applicableProcesses = DB::table('EDMS_STAGING')
            ->where('context_id', $contextId)
            ->pluck('process_code')
            ->toArray();

        $baseQuery = DB::table('EDMS_SYSTEM_PROCESSES')
            ->whereIn('process_code', $applicableProcesses);

        // Include claim processing only if the policy has a claim
        if ($policy_has_claim) {
            $baseQuery->orWhereRaw('UPPER(description) LIKE ?', ['%CLAIM PROCESSING%']);
        }

        return $baseQuery->pluck('description')->toArray();
    }

    /**
     * Fetch mandatory document codes for the given process codes.
     */
    private function getMandatoryDocumentCodes(array $processDescriptions)
    {
      $processCodes = DB::table('EDMS_SYSTEM_PROCESSES')
            ->whereIn('description', $processDescriptions)
            ->pluck('process_code')
            ->toArray();

        return DB::table('EDMS_DOC_PROCESS_MAP')
            ->whereIn('process_code', $processCodes)
            ->where('mandatory_flag', 'Y')
            ->distinct()
            ->pluck('document_code')
            ->toArray();
    }

    /**
     * Send email for missing documents.
     */
    private function sendEmailForMissingDocs($email, array $missingDocs, $clientName, $policyNumber)
    {
        // $addEmail = new AddToSendEmail();
        $addEmail = new AddToNotificationsMailCron();
        $missingDocsDescriptions = $this->getDocumentDescriptions($missingDocs);
        $message = "The following mandatory documents are missing: " . implode(', ', $missingDocsDescriptions);
        $category = 'Outstanding Documents';
        $dispatchDate = Carbon::now()->toDateString();

        $notificationCronRecord = new AddToNotificationsCron($category,$email,$message,$dispatchDate,'outstanding-documents');
        $emailRecord = $notificationCronRecord->setPolicyNo($policyNumber)
                                              ->setCreator('system')
                                              ->setIsRenewed('N')
                                              ->setIsCancelled('N')
                                              ->createNotificationMailRecord();
        
        return $emailRecord ? true : false;
    }

    /**
     * Get the descriptions of the documents based on their codes.
     */
    private function getDocumentDescriptions(array $documentCodes)
    {
        return DB::table('EDMS_DOCUMENTS')
            ->whereIn('document_code', $documentCodes)
            ->pluck('document_description')
            ->toArray();
    }
}
