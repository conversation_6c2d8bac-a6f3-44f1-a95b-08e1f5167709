<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\gb\underwriting\Policy;
use Illuminate\Support\Facades\Log;

class LapsePolicies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto:lapsepolicy';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Lapse policies where expiry date are past the lapse days';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $policy = new Policy(); // Replace with the actual controller
        $policy->lapsePoliciesPastLapseDays();

        $this->info('Auto Lapse Policy has been ran successfully.');
        Log::info('Auto Lapse Policy command ran at ' . now());
    }
}
