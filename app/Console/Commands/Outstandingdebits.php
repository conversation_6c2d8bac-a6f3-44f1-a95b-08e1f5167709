<?php

namespace App\Console\Commands;

use DB;
use Auth;
use Mail;
use App\Acdet;
use App\Agmnf;
use App\Client;
use App\Currency;
use App\Dcontrol;
use App\SendEmail;
use Carbon\Carbon;
use App\Mail\DebitMail;
use Illuminate\Support\Str;
use Illuminate\Console\Command;
use App\ProcessEmailNotification;
use App\Constants\NotificationConstants;
use App\Services\IntermediaryQueryService;
use App\ValueObjects\IntermediaryQueryParams;




class Outstandingdebits extends Command
{
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'push:debits';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This Command Pushes Notification to client on Outstanding debitnotes ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
      
        $days1 =NotificationConstants::DAYS_DIFFERENCE_VALUES[0];
        $days2 =NotificationConstants::DAYS_DIFFERENCE_VALUES[1];
        $days3 =NotificationConstants::DAYS_DIFFERENCE_VALUES[2];
        $notification_date1 =Carbon::now()->subDays($days1)->toDateString();
        $notification_date2 =Carbon::now()->subDays($days2)->toDateString();
        $notification_date3 =Carbon::now()->subDays($days3)->toDateString();

      
       

        try {
         $records =Acdet::where('source','U/W')
            ->where('doc_type','DRN')
            ->where('allocated',0)
            ->whereNotNull('date_effective')
            ->whereIn(DB::raw('date_effective'), [$notification_date1, $notification_date2, $notification_date3])
            ->get();
           
            foreach($records as $record){

                $date_effective=$record->date_effective;
                 $daysDifference = now()->diffInDays($date_effective);
                 $dcontrol = Dcontrol::whereRaw("trim(endt_renewal_no) = '" . $record->endt_renewal_no . "'")->first();



                if (in_array($daysDifference,  NotificationConstants::DAYS_DIFFERENCE_VALUES)) {
                    $mailsend=DB::table("notification_log")->where("endt_renewal_no",$record->endt_renewal_no)
                    ->where("interval",$daysDifference)
                    ->where("notification_type","outstanding-debit-notes")
                    ->whereDate("processsed_date", Carbon::now()->toDateString())
                    ->exists();
                    
                    if(!$mailsend){
                        $client_data = Client::whereRaw("trim(client_number) = '" . $record->client_number . "'")->first();
                        $currency = Currency::whereRaw("trim(currency_code) = '".trim($record->currency_code) ."'")->first()->currency;
                        $policy_number=formatPolicyOrClaim( $record->endt_renewal_no);
                        $date_started = Carbon::parse($date_effective)->format('jS F Y');
                        $payamt=number_format($record->unallocated);
                        

                        $endorse_no=formatPolicyOrClaim( $record->endt_renewal_no);
                    
                        $message="This is a Polite reminder that your  Policy $policy_number and Endorsement number $endorse_no has been Outstanding for $daysDifference days since $date_started,
                            Kindly make arrangement to settle this amount of $currency $payamt";
                        $clientemail=$client_data->e_mail;
                     
                          
                        
                        $details = [
                            'title' => "Debit notes outstanding payment",
                            'content' => $message,
                            'client'=>$client_data->name
                        ];
                    #broker mail
                    // $agentemail=  Agmnf::where('agent',$dcontrol->agent)
                    // ->where('branch',$dcontrol->branch)
                    // ->first()->email;
                    $intermediaryParams = new IntermediaryQueryParams([
                        'agentNo' => $dcontrol->agent,
                    ]);
                    $intermediary_details  =  IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();

                    $agentemail  = $intermediary_details->email;

                    #underwriting mail
                    $undermail = ProcessEmailNotification::where('process_slug','installment-due-notification')->first()->process_email;
                    $undermail=trim($undermail);

                    if (isset($clientemail) && $intermediary_details->acc_type == 1) {
                        // If client email is set and account type is 1, use client email
                        $email = $clientemail;
                    } elseif (isset($agentemail) && $intermediary_details->acc_type != 1) {
                        // If intermediary email is set and account type is not 1, use intermediary email
                        $email = $agentemail;
                    } 
                        $data =Mail::to($email)
                        ->cc([$undermail])
                        ->send(new DebitMail($details));
                        //dd(count(Mail::failures()));
                        if( count(Mail::failures()) < 1 ) {
                        
                            $slug = preg_replace("/[^a-zA-Z0-9 ]/", '', $title);

                            $slug = strtolower($slug);
                            $slug =  Str::slug($slug,'-');
         
                        
                                DB::table("notification_log")->insert([
                                'endt_renewal_no'=>$record->endt_renewal_no,
                                'notification_type'=>"outstanding-debit-notes",
                                'interval'=>$daysDifference,
                                'message'=>$message,
                                'processsed_date'=>Carbon::now(),
                                'receipients'=>$email
                                ]);

                            
                        }
                    }

                    
                


                }
            


            }
        } catch (\Throwable $th) {
            //dd($th);
            throw $th;
        }
    }
}
