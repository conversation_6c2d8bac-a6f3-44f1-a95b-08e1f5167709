<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\User;
use App\Clhmn;
use App\Laclmreq;
use App\Clmreq;
use App\Dcontrol;
use App\Client;
use App\Aimsgroup;
use App\Aimsuser_web;
use App\SendEmail;
use App\Pipcnam;
use App\PasswordPolicy;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use PDF;
use Mail;

class NotifyClaimDocsManager extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'claimdocs:notifymanager';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'send notification for claim documents not provided within 3 months of claim intimation to claims analyst';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        
        $pipcnam = Pipcnam::first();
        $notice_days = (int)$pipcnam->claim_docs_notice_days;
        $date = Carbon::now()->subDays($notice_days)->format('Y-m-d');

        $claim = Clhmn::where('date_reg', $date)->get();
        $claimlist = array();
        foreach ($claim as $clm) {
            $docexists = Laclmreq::where('claim_no', $clm->claim_no)->exists();
            $policy = Dcontrol::where('policy_no', $clm->policy_no)->first();
            $unploaded = array();
            if ($docexists) {
                $notreceived = Laclmreq::select('clmreq_name')->where('claim_no', $clm->claim_no)
                                            ->where('received', 'N')->groupBy('clmreq_name')->get();
                                            
                if (count($notreceived > 0)) {
                    foreach ($notreceived as $doc) {
                        array_push($unploaded, Str::title(trim($doc->clmreq_name)));
                    }
                }
            } else {
                $docs = Clmreq::where('class', $policy->class)->get();
                foreach ($docs as $doc) {
                    array_push($unploaded, Str::title(trim($doc->clmreq_name)));
                }
            }
            $documents = implode(", ", $unploaded);

            
            $claimdetails = [
                'claim_number' => formatPolicyOrClaim($clm->claim_no),
                'client_name' => $client_name,
                'date_registered' => $clm->date_reg,
                'claim_amount' => $clm->curr_total_estimate,
                'documents_not_uploaded' => $documents,
                'created_by' => $clm->created_by,
            ];

            array_push($claimlist, $claimdetails);
   
        }

        if(count($claimlist) > 0){
            $claim_manager = Aimsgroup::where('group_description','LIKE', '%CLAIMS MANAGER%')->first();
            $manager_group = $claim_manager->aims_group;
            $manager_email = Aimsuser_web::where('aims_group', $manager_group)->first();
            $manager_email = $manager_email->email;

            $data["email"] = $manager_email;
            $data["title"] = "Claim Documents";
            $data["doc_name"] = "claim_documents_not_received.pdf";
            $claims = $claimlist;
            $pipcnam = $pipcnam->toArray();
            $pdf = PDF::loadView('pdf.claim_doc_attach', ['claims'=>$claims, 'pipcnam'=>$pipcnam]);
            Mail::send('pdf.claim_documents',[$claims, $pipcnam], function($message)use($data, $pdf) {
                $message->to($data["email"])
                        ->subject($data["title"])
                        ->attachData($pdf->output(), $data["doc_name"]);
            });
        }

    }
}
