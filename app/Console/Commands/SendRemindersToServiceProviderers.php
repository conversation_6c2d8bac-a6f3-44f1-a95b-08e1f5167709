<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Claimant;
use App\Events\DispatchNotificationEvent;
use Illuminate\Support\Facades\Log;

class SendRemindersToServiceProviderers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-reminders-to-service-providers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //
        $claimants = Claimant::join('clmcorr', function ($join) {
                $join->on('claimant.rec_ind', '=', 'clmcorr.code')
                    ->where('clmcorr.expected_report', '=', 'Y');
            })
            ->select(
                'claimant.claim_no',
                'claimant.policy_no',
                'claimant.claimant_name',
                'claimant.claimant_code',
                'claimant.email',
                'claimant.created_by',
                'claimant.mobile_no',
                'claimant.exp_pre_report',
                'claimant.rec_pre_report',
                'claimant.exp_final_report',
                'claimant.rec_final_report',
                'clmcorr.*'
            )
            ->whereNull('claimant.REC_FINAL_REPORT')
            ->whereRaw('TRUNC(sysdate) - TRUNC(claimant.dola) = 2') // Ensure accurate date comparison
            ->orderBy('claimant.rec_ind', 'desc')
            ->get();

			$today = Carbon::now();
			$claimants_array = [];
			foreach ($claimants as $claimant) {

				if(!$claimant->rec_final_report) {
                    $processor_email = Aimsuser_web::where('user_name', $claimant->created_by)->first();
                    $processor_email = $processor_email->email;

                    $claim_no = $claimant->claim_no;
                    $policy_no = $claimant->policy_no;
					$sp_name = $claimant->claimant_name;
					$sp_email = $claimant->email;
                    $sp_mobile_no = $claimant->mobile_no;
					$sp_type = trim(strtoupper($claimant->name));
					$claimants_array[] = "$claimant->claimant_name - $sp_type";

					$timestamp = strtotime($claimant->exp_final_report);
					$date_expected = date('F j, Y', $timestamp);

					$subject = "REMINDER: $sp_type REPORT";
					$message_body = "This is a reminder that a report for the claim - $claim_no in policy - $policy_no is expected.";
                    $SMSMessage = "Reminder - A report for the claim - $claim_no in policy - $policy_no is expected."; //insert company name later

					$reminder_data = [
						'name' => $sp_name,
						'receiver' => array_filter([$sp_email, $processor_email]),
						'claimant_type' => $sp_type,
						'subject' => $subject,
						'message' => $message_body,
                        'SMSMessage' => $SMSMessage,
                        'phone' => array_filter([$sp_mobile_no]),
					];

					$notificationData = ['reminder_data'  =>  $reminder_data];
					try {
						DispatchNotificationEvent::dispatch($slug = 'sp-report-reminder',$notificationData);
					} catch (\Throwable $th) {
						Log::error("Failed to dispatch notification for claimant {$claimant->claimant_name}: " . $th->getMessage());
					}
      				
				}
			}
			
    }
}
