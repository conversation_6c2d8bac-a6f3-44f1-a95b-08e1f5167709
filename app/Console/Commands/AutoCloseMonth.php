<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Dtran0;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\parameters\gb\Mainparam_gb;
use App\Http\Controllers\gl\NlBatchController;

use Log;

class AutoCloseMonth extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto:closemonth';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatic Period Closure based on set parameters';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $dtran = Dtran0::where('rec_no',0)->get(['account_year', 'account_month', 'auto_month_cls']);

        $dtran = $dtran[0];

        if($dtran->auto_month_cls == 'Y'){
            
            try{

                $auto_close = new Mainparam_gb();

                Log::channel('month_closure')->alert("/************** INITIATING END MONTH CLOSURE ..... ******************/");
                Log::channel('month_closure')->alert("Period ".$dtran->account_year."/".$dtran->account_month);
                $val = $auto_close->auto_process_monthcls();

                log::channel('month_closure')->alert("GB END MONTH CLOSURE EXITING....");
                Log::channel('month_closure')->alert($val);

                if($val == 1){
                    log::channel('month_closure')->alert("GB END MONTH CLOSURE SUCCESSFULL....");
                    $request = new Request([
                        $batch_year = $dtran->account_year,
                        $batch_month = $dtran->account_month,
                        $batch_date = now()->format('Y-m-d'),
                        $frequency = 'M',
                    ]);

                    // Call the genGbBatches function
                  $nlbatch = new NlBatchController();
                  $nlbatch->genGbBatches($request);
        
                    session(
                        [
                            'automonth_msg' => "END MONTH CLOSURE SUCCESSFUL.",
                            'automonth_showmsg' => true
                        ]
                    );    
                }
                

            }
            catch (Exception $e) {
                Log::alert($e);
            }

        }
         
    }
}
