<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\User;
use App\Clhmn;
use App\Laclmreq;
use App\Clmreq;
use App\Dcontrol;
use App\Aimsuser_web;
use App\Aimsgroup;
use App\SendEmail;
use App\Pipcnam;
use App\PasswordPolicy;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use PDF;
use Mail;

class ClaimdocsNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'claimdocs:notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'send notification for claim documents not provided within 3 months of claim intimation';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $pipcnam = Pipcnam::first();
        $notice_days = (int)$pipcnam->claim_docs_notice_days;
        $date = Carbon::now()->subDays($notice_days)->format('Y-m-d');

        $claim = Clhmn::where('date_reg', $date)->get();
        foreach ($claim as $clm) {
            $docexists = Laclmreq::where('claim_no', $clm->claim_no)->exists();
            
            $policy = Dcontrol::where('policy_no', $clm->policy_no)->first();
            $claim_no = formatPolicyOrClaim($clm->claim_no);
            $unploaded = array();
            if ($docexists == true) {
                $notreceived = Laclmreq::select('clmreq_name')->where('claim_no', $clm->claim_no)
                                            ->where('received', 'N')->groupBy('clmreq_name')->get();
                
                if (!$notreceived->isEmpty()) { 
                    foreach ($notreceived as $doc) {
                        array_push($unploaded, Str::title(trim($doc->clmreq_name)));
                    }
                }  
            } else {
                $docs = Clmreq::where('class', $policy->class)->get();
                
                foreach ($docs as $doc) {
                    array_push($unploaded, Str::title(trim($doc->clmreq_name)));
                }
            }

            if(count($unploaded) > 0){
                $unploaded = array_map(function($value){
                    return "<li> {$value}</li>";
                },$unploaded);

                $documents = implode("", $unploaded); 

                $message = "Kindly note that the following claim documents for <b>CLAIM NO: $claim_no </b>
                            have not been received after $notice_days day(s) of claim intimation.<br><ul><b>$documents</b></ul>
                            Please submit the documents as soon as possible for claim to be processed";
                
                
                $user = Aimsuser_web::where('user_name', $clm->reg_by)->first();
                $email = trim($user->email); 

                $sendemail = new Sendemail;
                $sendemail->category = "Claim Documents";
                $sendemail->receiver =$email;
                $sendemail->message = $message;
                $sendemail->creator = 'System generated';
                $save = $sendemail->save();
            }


            
        }

    }
}
