<?php

namespace App\Console\Commands;

use App\Client;
use App\Pipcnam;
use App\Dcontrol;
use App\Debitmast;
use App\Polmaster;
use Carbon\Carbon;
use App\Polmasterend;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\NotificationsMailCron;
use App\Classes\AddToNotificationsCron;



class LapsedPoliciesNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lapsed:policies-notifications';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send notifications for policies about to lapse';

    /**
     * Execute the console command.
     */
    public function handle()
    {
      DB::beginTransaction();
      try {

        // UPDATE EXISTING CRON RECORDS FOR LAPSE POLICY CATEGORY

        $lapseCronRecords = DB::table('NOTIFICATIONS_MAIL_CRON')
                                ->where('notification_slug', 'lapse-policy')
                                ->get();

        foreach ($lapseCronRecords as $record) {
          $policy = Polmaster::select('policy_no', 'renewal_date', 'cov_period_to', 'cov_period_from')->where('policy_no', $record->policy_no)->first();

          $periodFrom = $policy->cov_period_from;
          $periodTo = $policy->cov_period_to;

          $lastCommittedDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type', 'dcon_no')
              ->where('dcontrol.policy_no',$policy->policy_no)
              ->whereRaw("(delete_str is null or delete_str = 'N')")
              ->whereRaw("cancelled = 'N' and (committed is null or committed = 'Y')")
              ->where('dcontrol.period_from', '>=', $periodFrom)
              ->where('dcontrol.period_to', '<=', $periodTo)
              ->orderBy('dcon_no', 'DESC')
              ->first();

          if($lastCommittedDebited->trans_type != 'NIL'){
            $lastDebited = Debitmast::where('endt_renewal_no', $lastCommittedDebited->endt_renewal_no)->first();
              
            if(is_null($lastDebited)){
              
              $lastCommittedDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type', 'dcon_no')
                ->where('dcontrol.policy_no',$policy->policy_no)
                ->whereRaw("(delete_str is null or delete_str = 'N')")
                ->whereRaw("cancelled = 'N' and (committed is null or committed = 'Y')")
                ->where('dcontrol.period_from', '>=', $periodFrom)
                ->where('dcontrol.period_to', '<=', $periodTo)
                ->where('endt_renewal_no', '<>', $lastCommittedDebited->endt_renewal_no)
                ->orderBy('dcon_no', 'DESC')
                ->first();

            }
          }

          $lastDebitedDconNo = Dcontrol::where('dcontrol.policy_no',$policy->policy_no)
                                      ->join('debitmast', function($query){
                                          $query->on('dcontrol.endt_renewal_no','=' ,'debitmast.endt_renewal_no');
                                      })
                                      ->whereNull('dcontrol.delete_str')
                                      ->where('dcontrol.trans_type', '<>', 'NIL')
                                      ->where('debitmast.period_from', '>=', $periodFrom)
                                      ->where('debitmast.period_to', '<=', $periodTo)
                                      ->orderBy('dcontrol.dcon_no', 'DESC')
                                      ->max('dcontrol.dcon_no');

          $lastDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type')
                                  ->where('policy_no',$policy->policy_no)
                                  ->where('dcon_no', $lastDebitedDconNo)
                                  ->first();

          if (!is_null($lastDebited)) {
            $query = DB::table('NOTIFICATIONS_MAIL_CRON')
                      ->where('policy_no', $record->policy_no)
                      ->where('notification_slug', 'lapse-policy')
                      ->whereNull('email_id');
        
            switch ($lastCommittedDebited->trans_type) {
              case 'CNC':
                $query->update([
                    'is_cancelled' => 'Y',
                    'is_renewed' => 'N',
                    'updated_at' => Carbon::now()->toDateString()
                ]);
                break;
      
              case 'RNS':
                $query->update([
                    'is_cancelled' => 'N',
                    'is_renewed' => 'N',
                    'updated_at' => Carbon::now()->toDateString()
                ]);
                break;
      
              case 'REN':
                $query->update([
                    'is_renewed' => 'Y',
                    'is_cancelled' => 'N',
                    'updated_at' => Carbon::now()->toDateString()
                ]);
                break;
            }
          }  
        }
        // END OF UPDATE

        $todayDate = Carbon::now()->toDateString();

        $pipcnam = Pipcnam::first();
        $auto_lapse_days = $pipcnam->auto_lapse_days;
        $beforeEndAutoLapseDays = $pipcnam->before_end_auto_lapse_days;
        $intervals = $pipcnam->before_end_auto_lapse_days_intervals;
        $frequency = (int) ceil(($beforeEndAutoLapseDays)/$intervals);
        $company = $pipcnam->company_name;
        $daysSinceExp = $auto_lapse_days - $beforeEndAutoLapseDays;
        $dateThreshold = Carbon::now()->subDays($daysSinceExp)->toDateString(); // since start of exp to today

        $policies = Polmaster::select('polmaster.policy_no', 'polmaster.expiry_date', 'polmaster.client_number', 'cov_period_from', 'cov_period_to', 'agent_no')
                      ->whereDate('polmaster.expiry_date', '<=', $todayDate) // expired policies
                      ->whereDate('polmaster.expiry_date', '=', $dateThreshold) // expired recently i.e within auto_lapse_days
                      ->whereNull('polmaster.lapse_date') // not yet marked as lapsed
                      ->get();
        // dd(count($policies));
        
        $recordInserted = false;
        
        if(count($policies) > 0){
          foreach($policies as $policy){

            $periodFrom = $policy->cov_period_from;
            $periodTo = $policy->cov_period_to;

            if($policy->agent_no){
              $intermediaryParams = new IntermediaryQueryParams([
                'agentNo' => $policy->agent_no,
              ]);
          
              $intermediary_details  =  IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
            }

            $lastCommittedDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type', 'dcon_no')
                ->where('dcontrol.policy_no',$policy->policy_no)
                ->whereRaw("(delete_str is null or delete_str = 'N')")
                ->whereRaw("cancelled = 'N' and (committed is null or committed = 'Y')")
                ->where('dcontrol.period_from', '>=', $periodFrom)
                ->where('dcontrol.period_to', '<=', $periodTo)
                ->orderBy('dcon_no', 'DESC')
                ->first();
            
            if($lastCommittedDebited->trans_type != 'NIL'){
              $lastDebited = Debitmast::where('endt_renewal_no', $lastCommittedDebited->endt_renewal_no)->first();
              
              if(is_null($lastDebited)){
                
                $lastCommittedDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type', 'dcon_no')
                  ->where('dcontrol.policy_no',$policy->policy_no)
                  ->whereRaw("(delete_str is null or delete_str = 'N')")
                  ->whereRaw("cancelled = 'N' and (committed is null or committed = 'Y')")
                  ->where('dcontrol.period_from', '>=', $periodFrom)
                  ->where('dcontrol.period_to', '<=', $periodTo)
                  ->where('endt_renewal_no', '<>', $lastCommittedDebited->endt_renewal_no)
                  ->orderBy('dcon_no', 'DESC')
                  ->first();
  
              }
              if($lastDebited->entry_type_descr == 'CNC' && $lastCommittedDebited->trans_type == 'CNC'){
                continue;
              }
            }

            if(is_null($lastCommittedDebited)){
              continue;
            }

            $lastDebitedDconNo = Dcontrol::where('dcontrol.policy_no',$policy->policy_no)
            ->join('debitmast', function($query){
                $query->on('dcontrol.endt_renewal_no','=' ,'debitmast.endt_renewal_no');
            })
            ->whereNull('dcontrol.delete_str')
            ->where('dcontrol.trans_type', '<>', 'NIL')
            ->where('debitmast.period_from', '>=', $periodFrom)
            ->where('debitmast.period_to', '<=', $periodTo)
            ->orderBy('dcontrol.dcon_no', 'DESC')
            ->max('dcontrol.dcon_no');
            
            $lastDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type')
              ->where('policy_no',$policy->policy_no)
              ->where('dcon_no', $lastDebitedDconNo)
              ->first();
            
            $lastDebitedPolmasternd = Polmasterend::where('endorse_no', $lastDebited->endt_renewal_no)->first();
            
            $baseTransaction = Debitmast::select('policy_no','endt_renewal_no', 'entry_type_descr', 'gross_amount')
                ->whereIn('entry_type_descr',['POL','REN','RNS'])
                ->where('policy_no',$policy->policy_no)
                ->where('period_from', '>=', $periodFrom)
                ->where('period_to', '<=', $periodTo)
                ->orderBy('account_year', 'DESC')
                ->orderBy('dtrans_no', 'DESC')
                ->first();
            
            // check if the policy records are already in the cron table and delete
            DB::table('NOTIFICATIONS_MAIL_CRON')
                ->where('policy_no', $policy->policy_no)
                ->where('pol_ren', $baseTransaction->endt_renewal_no)
                ->where('notification_slug', 'lapse-policy')
                ->where('is_renewed', 'N')
                ->where('is_cancelled', 'N')
                ->whereNull('email_id')
                ->delete();

            $polRen = $baseTransaction->endt_renewal_no;
            $policyNo = $lastDebitedPolmasternd->policy_no;
            $renewalDate = $lastDebitedPolmasternd->renewal_date;
            $clientRecord = Client::where('client_number', $lastDebitedPolmasternd->client_number)->first();
            if (isset($client->e_mail) && $intermediary_details->acc_type == 1) {
              // If client email is set and account type is 1, use client email
                $receiver = $clientRecord->e_mail;
            } elseif (isset($intermediary_details->email) && $intermediary_details->acc_type != 1) {
                // If intermediary email is set and account type is not 1, use intermediary email
                $receiver = $intermediary_details->email;
            } else {
                // Default case (optional)
                $receiver = [];
            }

            $lapseDte = Carbon::parse($lastDebitedPolmasternd->expiry_date)->addDays(60)->toDateString();

            $category = 'Lapse Policy';
            $message = "<html>
              <body>
                  <p>Dear $clientRecord->name,</p>
                  <p>We hope this message finds you well.</p>
                  <p>This is a friendly reminder regarding your policy $policyNo that has expired and you have not yet renewed with us.</p>
                  <p>Kindly make necessary arrangements before the lapse period is over. Your policy will lapse on $lapseDte</p>
                  <p>Kind Regards,</p>
                  <p>$company</p>
              </body>
            </html>";
            $notificationSlug = 'lapse-policy';
            
            for($i=1; $i<=$intervals; $i++){
              $lapseDate = Carbon::parse($lastDebitedPolmasternd->expiry_date)->addDays(60);
              $days = $i * $frequency; 
              $dispatchDate = $lapseDate->subDays($days);

              $notificationMail = new AddToNotificationsCron($category,$receiver,$message,$dispatchDate,$notificationSlug);
              $newRecord = $notificationMail->setPolicyNo($policyNo)
                                            ->setPolren($polRen)
                                            ->setRenewalDate($renewalDate)
                                            ->setCreator('system')
                                            ->setIsRenewed('N')
                                            ->setIsCancelled('N')
                                            ->createNotificationMailRecord();

              if ($newRecord) {
                $recordInserted = true;
              }
            }
          }
        }

        

        DB::commit();
        if($recordInserted){
          $this->info("Record(s) have been inserted into notifications_mail_cron table");
        }else {
          $this->info("There are no record(s) to insert into notifications_mail_cron table");
        }
      } catch (\Throwable $th) {
        DB::rollback();
        $this->info("An error occured. Unable to insert record(s) into notifications_mail_cron table");
        throw $th;
      }
      

    }
}
