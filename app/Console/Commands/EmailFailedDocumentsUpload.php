<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

//use App\Edms_documents_dictionary;
use App\Mail\DocumentsUploadMail;
use App\Edms_staging;
use App\ProcessEmailNotification;
use App\Pipcnam;
use App\EdmsProcesses;

use DB;

class EmailFailedDocumentsUpload extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'failed:uploads';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $pipcnam = Pipcnam::first();

        $system_processes = EdmsProcesses::join('edms_staging', function($query){
                                                $query->on('edms_system_processes.process_code','=','edms_staging.process_code');
                                        })
                                        ->where('edms_staging.integrated', 'N')
                                        ->selectRaw("edms_system_processes.process_code")
                                        ->selectRaw("edms_system_processes.description as proc_descr")
                                        ->selectRaw("count(*) as doc_count")
                                        ->groupBy('edms_system_processes.process_code')
                                        ->groupBy('edms_system_processes.description')
                                        ->get();

        $documents = Edms_staging::where('integrated','N')
                                 ->leftjoin('edms_documents', function($query){
                                     $query->on('edms_staging.document_code','=' ,'edms_documents.document_code');
                                 })
                                 ->leftjoin('aimsusers', function($query){
                                    $query->on('edms_staging.created_by','=','aimsusers.user_id');
                                 })
                                 ->get(['edms_staging.process_code','edms_staging.context_id','edms_staging.document_description as doc_name','edms_staging.document_id','edms_documents.document_description as doc_type','edms_staging.upload_timestamp','aimsusers.name as uploaded_by']);

        $send_to = ProcessEmailNotification::where('process_slug','edms-failed-integration')->pluck('process_email'); //->toArray();
        
        if(!$documents->isEmpty()){

            //$email = $send_to; 
            $email = $send_to;
            $subject = 'DOCUMENTS PENDING TO INTEGRATE '; 
            $title = "PENDING INTEGRATION";
            $createdate = Carbon::parse(Carbon::now())->diffForhumans();
            $user = 'EDMS Integration';
          
            Mail::to($email)->send(new DocumentsUploadMail($documents, $system_processes, $title, $createdate, $subject, $user));
            
            if( count(Mail::failures()) < 1 ) {

                $this->info("all mails have been sent");

            }
 
        }else{
            $this->info("No documents found");
        }
    }
}