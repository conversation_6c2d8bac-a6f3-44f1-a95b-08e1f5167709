<?php

namespace App\Console\Commands\RenewalNotice;

use App\Agmnf;
use App\Clhmn;
use App\Pipcnam;
use App\Pipstmp;
use App\Dcontrol;
use App\VatSetup;
use App\Debitmast;
use App\Polmaster;
use Carbon\Carbon;
use App\ClassModel;
use App\Polmasterend;
use App\Renewal_notice;
use App\Models\Modtlmast;
use App\Models\Motcvrdet;
use App\Models\Modtlpivot;
use Illuminate\Console\Command;
use App\Models\RenewalNoticeSetup;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\DiscountLoadingExtExc;
use App\Models\LoadPremLossRatioBand;
use App\Services\IntermediaryQueryService;
use App\Models\AutoLoadRenewalNoticeControl;
use App\ValueObjects\IntermediaryQueryParams;

class RenewalNotices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'renewal:notices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Pull Renewal Notices';

    /**
     * Execute the console command.
     */
    public function handle()
    {
      $todayDate = Carbon::now()->toDateString();
      $renewalNotSetup = RenewalNoticeSetup::first();
      $daysBeforeExp = $renewalNotSetup->days_before_expiry;
      $pipcnam = Pipcnam::select('country_code', 'minlossratio')->first();
      // dd($this->computePremiumTaxes('256',200000,40));
      // Define the date range for the query
      $startDate = Carbon::now()->addDays($daysBeforeExp - 5)->startOfDay();
      $endDate = Carbon::now()->addDays($daysBeforeExp)->startOfDay();
      // dd(Carbon::now()->addDays(46));
      
      // Fetch policies
      $policies = Polmaster::whereBetween('renewal_date', [$startDate, $endDate])
                            ->where('status_code', 'ACT')
                            ->get();
      // dd(count($policies));
      $createdRenewalNotice = false;
      
      DB::beginTransaction();
      try {
        foreach ($policies as $policy) {
           // Check if the record already exists in renewal_notice table
           $renewal_date = Carbon::parse($policy->renewal_date)->format('Y-m-d');
           $renewalNoticeModel = Renewal_notice::where('policy_no', $policy->policy_no)
           ->where('renewal_date', $renewal_date)
           ->where('uw_year', $policy->uw_year);
           
          if ($renewalNoticeModel->exists()) {
            $renewalNotice = $renewalNoticeModel->first();
            if($renewalNotice->renewal_notice_status == 'PENDING' || $renewalNotice->renewal_notice_status == 'APPROVED' && $renewalNotice->approved_by == 'AUTO'){
              //delete the renewal notice
              Renewal_notice::where('policy_no', $policy->policy_no)
                                ->where('renewal_date', $renewal_date)
                                ->where('uw_year', $policy->uw_year)
                                ->delete();

              DB::table('NOTIFICATIONS_MAIL_CRON')
                  ->where('policy_no', $policy->policy_no)
                  ->where(function ($query) {
                    $query->where('notification_slug', 'renewal-notice')
                          ->orWhere('notification_slug', 'renewal-reminder');
                  })
                  ->delete();
            }
        }
          $periodFrom = $policy->cov_period_from;
          $periodTo = $policy->cov_period_to;

          $lastCommittedDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type', 'dcon_no')
                ->where('dcontrol.policy_no',$policy->policy_no)
                ->whereRaw("(delete_str is null or delete_str = 'N')")
                ->whereRaw("cancelled = 'N' and (committed is null or committed = 'Y')")
                ->where('dcontrol.period_from', '>=', $periodFrom)
                ->where('dcontrol.period_to', '<=', $periodTo)
                ->orderBy('dcon_no', 'DESC')
                ->first();

          // $lastDebitedTransaction = DB::select("select policy_no, trans_type, committed, endt_renewal_no from dcontrol 
          //       where policy_no = '$policy->policy_no' and cancelled = 'N' and (committed is null or committed = 'Y')
          //       AND dcontrol.period_from >= '$periodFrom' AND dcontrol.period_to <= '$periodTo'
          //       order by dola desc FETCH FIRST 1 ROWS ONLY");

          if($lastCommittedDebited->trans_type != 'NIL'){
              $lastDebited = Debitmast::where('endt_renewal_no', $lastCommittedDebited->endt_renewal_no)->first();
              
            if(is_null($lastDebited)){
              
              $lastCommittedDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type', 'dcon_no')
                ->where('dcontrol.policy_no',$policy->policy_no)
                ->whereRaw("(delete_str is null or delete_str = 'N')")
                ->whereRaw("cancelled = 'N' and (committed is null or committed = 'Y')")
                ->where('dcontrol.period_from', '>=', $periodFrom)
                ->where('dcontrol.period_to', '<=', $periodTo)
                ->where('endt_renewal_no', '<>', $lastCommittedDebited->endt_renewal_no)
                ->orderBy('dcon_no', 'DESC')
                ->first();

            }
          }

          $lastDebitedDconNo = Dcontrol::where('dcontrol.policy_no',$policy->policy_no)
            ->join('debitmast', function($query){
                $query->on('dcontrol.endt_renewal_no','=' ,'debitmast.endt_renewal_no');
            })
            ->whereNull('dcontrol.delete_str')
            ->where('dcontrol.trans_type', '<>', 'NIL')
            ->where('debitmast.period_from', '>=', $periodFrom)
            ->where('debitmast.period_to', '<=', $periodTo)
            ->orderBy('dcontrol.dcon_no', 'DESC')
            ->max('dcontrol.dcon_no');

          $lastDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type')
            ->where('policy_no',$policy->policy_no)
            ->where('dcon_no', $lastDebitedDconNo)
            ->first();
          $lastDebitedPolmasternd = Polmasterend::where('endorse_no', $lastDebited->endt_renewal_no)->first();
   
         
  
          $baseTransaction = Debitmast::select('policy_no','endt_renewal_no', 'entry_type_descr', 'gross_amount')
                ->whereIn('entry_type_descr',['POL','REN','RNS'])
                ->where('policy_no',$policy->policy_no)
                ->where('period_from', '>=', $periodFrom)
                ->where('period_to', '<=', $periodTo)
                ->orderBy('account_year', 'DESC')
                ->orderBy('dtrans_no', 'DESC')
                ->first();
          
          
          
          // dd($policy->policy_no);
          // renewable and debited
          $class = ClassModel::where('class', $policy->class)->first();
          $renewable = $class->renewable;
          $debited = Debitmast::where('endt_renewal_no',$lastDebited->endt_renewal_no)->exists();
      
          $modtls = Modtlpivot::where("endt_renewal_no" , $lastCommittedDebited->endt_renewal_no)
                ->where("status" , 'ACT')
                ->get();
          
          $autoLoadControl = AutoLoadRenewalNoticeControl::first()->auto_load_rn;

          $count_risk = ($modtls->count() < 1) ? 1: $modtls->count();

            if ($renewable == "Y" && !is_null($lastCommittedDebited)) { 
              
              $is_motor=$class->motor_policy;
              $renewal_premium= (float) $lastDebitedPolmasternd->renewal_premium;

              $sum_insured=$policy->sum_insured;
              $dep_renewal_premium=0;
              $pvt_endorse_amount=0;
              $dep_pvt_endorse_amount=0;
              $dep_endorse_amount= $renewal_premium;
              
              if($is_motor=="Y"){
                $depreciated_values = [];
                
                if($renewalNotSetup->set_depreciation_percentage == 'Y'){
                  $depreciated_values = $this->computeDepreciation($lastDebited,$renewalNotSetup->depreciation_val);
                }else{
                  $depreciated_values = $this->computeDepreciation($lastDebited,0);
                }
                
                $depreciated_collection=collect($depreciated_values);
                $annual_premium =$depreciated_collection->sum('annual_premium');
                $endorse_amount=$renewal_premium =$depreciated_collection->sum('endorse_amount');
                $dep_endorse_amount=$dep_renewal_premium =$depreciated_collection->sum('dep_endorse_amount');
                $sum_insured = $depreciated_collection->where('grp_code', 'BSP')->sum('sum_insured');
                $dep_sum_insured =$depreciated_collection->where('grp_code', 'BSP')->sum('dep_sum_insured');
                $pvt_endorse_amount =$depreciated_collection->sum('pvt_endorse_amount');
                $dep_pvt_endorse_amount =$depreciated_collection->sum('dep_pvt_endorse_amount');
              }
  
              #tax computations depreciation
              $pipstmp = Pipstmp::first();

              /***TAXES***/
              if ($is_motor == "Y") {
                $dep_levy = $this->fetchPremiumTax($dep_endorse_amount, 'training-levy',$lastDebitedPolmasternd->endorse_no);
                $dep_stamp_duty = $this->fetchPremiumTax($dep_endorse_amount, 'stamp-duty',$lastDebitedPolmasternd->endorse_no);
                $dep_sticker_fees = $this->fetchPremiumTax($dep_endorse_amount, 'sticker-fees',$lastDebitedPolmasternd->endorse_no);
                $dep_vatamount = $this->fetchPremiumTax($dep_endorse_amount, 'vat',$lastDebitedPolmasternd->endorse_no);
                $dep_policy_fund = $this->fetchPremiumTax($dep_endorse_amount, 'phcf',$lastDebitedPolmasternd->endorse_no);
                $dep_admin_fees = $this->fetchPremiumTax($dep_endorse_amount, 'admin-fees',$lastDebitedPolmasternd->endorse_no);
                $dep_fund_guarantee = $this->fetchPremiumTax($dep_endorse_amount, 'fund-guarantee',$lastDebitedPolmasternd->endorse_no);
              } else { 
                $dep_levy = 0;
                $dep_stamp_duty = 0;
                $dep_sticker_fees = 0;
                $dep_vatamount = 0;
                $dep_policy_fund = 0;
                $dep_admin_fees = 0;
                $dep_fund_guarantee = 0;
              }
              
              $levy = $this->fetchPremiumTax($renewal_premium,'training-levy', $lastDebitedPolmasternd->endorse_no);
              $stamp_duty = $this->fetchPremiumTax($renewal_premium,'stamp-duty', $lastDebitedPolmasternd->endorse_no);
              $sticker_fees = $this->fetchPremiumTax($renewal_premium,'sticker-fees', $lastDebitedPolmasternd->endorse_no);
              $vatamount = $this->fetchPremiumTax($renewal_premium,'vat', $lastDebitedPolmasternd->endorse_no);
              $policy_fund = $this->fetchPremiumTax($renewal_premium,'phcf', $lastDebitedPolmasternd->endorse_no);
              $admin_fees = $this->fetchPremiumTax($renewal_premium,'admin-fees', $lastDebitedPolmasternd->endorse_no);
              $fund_guarantee = $this->fetchPremiumTax($renewal_premium,'fund-guarantee', $lastDebitedPolmasternd->endorse_no);
              /***TAXES***/
              
                $total = round($renewal_premium + $stamp_duty + $sticker_fees + $levy + $admin_fees + $fund_guarantee + $vatamount + $pvt_endorse_amount + $policy_fund,2);
                $dep_total = round($dep_renewal_premium + $dep_stamp_duty + $dep_sticker_fees + $dep_levy + $dep_admin_fees  + $dep_fund_guarantee + $dep_vatamount + $dep_pvt_endorse_amount + $dep_policy_fund,2);
                
                // determine approval fields based on loss ratio
                $loss_ratio = 0;
                
                $policy_has_claim = Clhmn::where('policy_no', $policy->policy_no)->exists();
                
                // if policy doesnt have claim, loss ratio is 0
                if(!$policy_has_claim){
                    $loss_ratio = 0;
                }else {
                    $claim_paid = Clhmn::where('policy_no',$policy->policy_no)->sum('cost_todate'); 
                    $claim_curr_est = Clhmn::where('policy_no',$policy->policy_no)->sum('curr_total_estimate');
                    $gross_premium = Debitmast::where('policy_no',$policy->policy_no)->sum('gross_amount');
  
                    $loss_ratio = $this->calcLossRatio($claim_paid, $claim_curr_est, $gross_premium);
                }
                
                // default values
                $renewal_notice_status="PENDING";
                $approved_by=null;
                $approved_date=null;
                $approved=null;
                
                // get the set min loss ratio
                $min_loss_ratio = $pipcnam->minlossratio;
                if($renewalNotSetup->auto_approve == 'Y' && $loss_ratio < $min_loss_ratio){
                  $approved_by="AUTO";
                  $approved_date=Carbon::now();
                  $approved = "Y";
                  $renewal_notice_status="APPROVED";                              
                }

                if($loss_ratio < $min_loss_ratio && $renewalNotSetup->auto_discount == 'Y'){
                  // auto apply discount
                  $applicableDiscounts = DB::table('DISCOUNTS_LOADINGS_PARAMS')
                                  ->where('auto_load_renewal_notice', 'Y')
                                  ->where('type', 'D')
                                  ->get();

                  $amount = 0;
                  foreach ($applicableDiscounts as $disc) {
                    if($disc->basis == 'A'){
                      $amount = $disc->rate_amount;
                    }else{
                      if($disc->principle == 'C'){
                        $amount = $renewal_premium * ($disc->rate_amount/100);
                      }elseif($disc->principle == 'A'){
                        $amount = $sum_insured * ($disc->rate_amount/100);
                      }
                    } 

                    $existingRecord = DiscountLoadingExtExc::where('type', $matchedBand->code)
                                      ->where('renewal_notice_id', $policy->endorse_no)
                                      ->where('description', $loadTypeDesc)
                                      ->first();
                          
                    if(!$existingRecord){
                     
                      // Insert new record
                      $newRecord = new DiscountLoadingExtExc();
                      $newRecord->type = 'DISC';
                      $newRecord->description = $disc->description;
                      $newRecord->amount = $amount;
                      $newRecord->created_by = 'system';
                      $newRecord->renewal_notice_id = $policy->endorse_no;
                      $newRecord->code_id = DB::table('renewalnotice_loading_discount')->where('code', 'DISC')->first()->id;
                      $newRecord->param_id = DB::table('DISCOUNTS_LOADINGS_PARAMS')
                                              ->where('description', $disc->description)
                                              ->where('type', 'D')
                                              ->first()->id;
                      $newRecord->add_deduct = 'D';
      
                      $newRecord->save();
                    }
                  } 
                }
                
                if($loss_ratio > $min_loss_ratio &&  $renewalNotSetup->auto_load == 'Y' && $autoLoadControl == 'Y'){
                  
                    // Get all loading bands
                    $loadingBands = LoadPremLossRatioBand::all();
    
                    $matchedBand = null;
    
                    foreach ($loadingBands as $band) {
                        $range = $band->range;
                        list($min, $max) = explode('-', $range);
    
                        if ($loss_ratio >= $min && $loss_ratio <= $max) {
                            $matchedBand = $band;
                            break;
                        }
                    }
                    
                    // auto load notice based on loss ratio
                    if($matchedBand){
                      // find the load type in loading_discounts_param
                      $loadType = DB::table('DISCOUNTS_LOADINGS_PARAMS')
                                  ->where('auto_load_renewal_notice', 'Y')
                                  ->where('slug', 'high-loss-ratio-loading')
                                  ->first();
                      $loadTypeDesc = $loadType->description;
                      $amount = 0;
                      if($loadType->basis == 'A'){
                        $amount = $loadType->rate_amount;
                      }else{
                        if($loadType->principle == 'C'){
                          $amount = $renewal_premium * ($loadType->rate_amount/100);
                        }elseif($loadType->principle == 'A'){
                          $amount = $sum_insured * ($loadType->rate_amount/100);
                        }
                      }

                      $existingRecord = DiscountLoadingExtExc::where('type', $matchedBand->code)
                                      ->where('renewal_notice_id', $policy->endorse_no)
                                      ->where('description', $loadTypeDesc)
                                      ->first();
                      
                      if (!$existingRecord) {
                          
                          // Insert new record
                          $newRecord = new DiscountLoadingExtExc();
                          $newRecord->type = $matchedBand->code;
                          $newRecord->description = $loadTypeDesc;
                          $newRecord->amount = $amount;
                          $newRecord->created_by = 'system';
                          $newRecord->renewal_notice_id = $policy->endorse_no;
                          $newRecord->code_id = DB::table('renewalnotice_loading_discount')->where('type', $matchedBand->type)->value('id');
                          // $newRecord->code_id = DB::table('renewalnotice_loading_discount')->where('code', 'D')->first()->id;
                          $newRecord->param_id = DB::table('DISCOUNTS_LOADINGS_PARAMS')
                                                  ->where('description', $loadTypeDesc)
                                                  ->where('type', 'L')
                                                  ->first()->id;
          
                          // Set add_deduct based on type
                          if ($matchedBand->type === 'LOADING') {
                            $newRecord->add_deduct = 'A';
                          } else {
                            $newRecord->add_deduct = 'D';
                          }
          
                          $newRecord->save();
                          
                      }
                    
                  }
                }  
                
                
  
              // $agent = Agmnf::where("branch" , $policy->branch)->where("agent" , $policy->agent_no)->first();
              $intermediaryParams = new IntermediaryQueryParams([
								'agentNo' => $policy->agent_no,
							]);
							$agent  =IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();

              
              $renewal_notice = new Renewal_notice;
              
              // dd($lastCommittedDebited);
              $renewal_notice->policy_no = $policy->policy_no;
              $renewal_notice->endt_renewal_no = $lastCommittedDebited->endt_renewal_no;
              $renewal_notice->pol_ren = $baseTransaction->endt_renewal_no;
              $renewal_notice->name = $policy->name;
              $renewal_notice->agent_name = $agent->name;
              $renewal_notice->client_number = $policy->client_number;
              $renewal_notice->branch = $policy->branch;
              $renewal_notice->agent_no = $policy->agent_no;
              $renewal_notice->class = $policy->class;
              $renewal_notice->sum_insured = abs($sum_insured);
              $renewal_notice->dep_sum_insured = abs($dep_sum_insured); //abs($sum_insured-$dep_sum_insured);
              $renewal_notice->endorse_amount = abs($policy->endorse_amount);
              $renewal_notice->dep_endorse_amount = abs($dep_endorse_amount); // abs($renewal_premium - $dep_endorse_amount);
              $renewal_notice->pvt_endorse_amount = abs($pvt_endorse_amount);
              $renewal_notice->dep_pvt_endorse_amount = abs($dep_pvt_endorse_amount); //abs($pvt_endorse_amount-$dep_pvt_endorse_amount);
              $renewal_notice->dep_levy = abs ($dep_levy); //abs($levy - $dep_levy);
              $renewal_notice->dep_sticker_fees = abs($dep_sticker_fees);
              $renewal_notice->dep_stamp_duty = abs($dep_stamp_duty);
              $renewal_notice->dep_vatamount = abs($dep_vatamount);
              $renewal_notice->renewal_premium = abs($renewal_premium);
              $renewal_notice->period_from = $policy->period_from;
              $renewal_notice->uw_year = $policy->uw_year;
              $renewal_notice->period_to = $policy->period_to;
              $renewal_notice->expiary_date = Carbon::parse($policy->expiry_date)->format('Y-m-d');
              $renewal_notice->renewal_date = Carbon::parse($policy->renewal_date)->format('Y-m-d');
              $renewal_notice->dola = Carbon::parse($lastCommittedDebited->dola)->format('Y-m-d');
              $renewal_notice->stamp_duty = abs((int)$stamp_duty);
              $renewal_notice->sticker_fee = abs((int)$sticker_fees);
              $renewal_notice->levy = abs((float)$levy);
              $renewal_notice->vat_amount = abs((float)$vatamount);
              $renewal_notice->phcf = abs((float)$policy_fund);
              $renewal_notice->dep_phcf = abs((float)$dep_policy_fund);
              $renewal_notice->fund_guarantee = abs((float)$fund_guarantee);
              $renewal_notice->dep_fund_guarantee = abs((float)$dep_fund_guarantee);
              $renewal_notice->admin_fees = abs((float)$admin_fees);
              $renewal_notice->dep_admin_fees = abs((float)$dep_admin_fees);
              $renewal_notice->total_amout = abs((float)$total);
              $renewal_notice->dep_total_amount = abs((float)$dep_total);
              $renewal_notice->depreciation_amount = $renewalNotSetup->depreciation_val;
              $renewal_notice->renewal_notice_status = $renewal_notice_status;
              $renewal_notice->is_motor = $is_motor;
              $renewal_notice->sent_date = $sent_date ?? null;
              $renewal_notice->approved_by = $approved_by;
              $renewal_notice->approved_date = $approved_date;
              $renewal_notice->approved = $approved;
              $renewal_notice->save();
              
              // dd($renewal_notice);
              if ($renewal_notice) {
                $createdRenewalNotice = true;
              }
  
              DB::commit();
              
            }  
        }
        if ($createdRenewalNotice) {
          $this->info("New record(s) of renewal notices have been added.");
        } else {
            $this->info("There are no new renewal notices to add.");
        }
      } catch (\Throwable $th) {
        DB::rollBack();
        dd($th);
        $this->error('Failed to add renewal policies: ' . $th->getMessage());
      }
      
    }

    public function computeDepreciation($policy ,$rate){
      $dcontrol = Dcontrol::whereRaw("trim(endt_renewal_no)='" . trim($policy->endt_renewal_no) ."'")->first();
        
      #get the active vehicles in cover

      $modtls = Modtlpivot::where("endt_renewal_no" , $policy->endt_renewal_no)
                          ->where("status" , 'ACT')
                          ->get();
    

      $sections = [];

      foreach($modtls as $modtl){
          #get the premium breakdown
          
          $mottcvrdet = Motcvrdet::with('motor_group')
                                  ->leftJoin('motorsect',function($join){
                                      $join->on('motorsect.grp_code','motcvrdet.grp_code');
                                      $join->on('motorsect.item_code','motcvrdet.item_code');
                                  })
                                   ->leftJoin('motorprem_grp_categ','motorsect.grp_categ_id','=','motorprem_grp_categ.id')
                                  ->where('policy_no',$dcontrol->policy_no)
                                  ->where('endt_renewal_no',$dcontrol->endt_renewal_no)
                                  ->where('cancelled','<>','Y')
                                  ->where('reg_no',$modtl->reg_no)
                                  ->get(['motcvrdet.*','motorsect.basis','motorprem_grp_categ.slug']);

          foreach ($mottcvrdet as $risk_item) {
              $dep_risk_value = ($risk_item->risk_value * (100 - $rate))/100;
              $dep_annual_premium = 0;
              $endorse_amount=0;
              $dep_endorse_amount=0;
              $pvt_endorse_amount=0;
              $dep_pvt_endorse_amount=0;

              if($risk_item->basis == 'R')
              {
                  $dep_annual_premium = ($dep_risk_value * $risk_item->rate )/100; 
              }

              switch ($dcontrol->ast_marker) {
                  case 'S':
                      $endorse_amount = ($dcontrol->endt_days / $dcontrol->days_covered) * $risk_item->annual_premium;
                      $dep_endorse_amount = ($dcontrol->endt_days / $dcontrol->days_covered) * $dep_annual_premium;
                      if ($dcontrol->short_term_method == 'S') {
                          $endorse_amount = ($dcontrol->short_term_percent * $risk_item->annual_premium)/100;
                          $dep_endorse_amount = ($dcontrol->short_term_percent * $dep_annual_premium)/100;
                      }
                      break;
                  case 'T':
                      $endorse_amount = ($dcontrol->endt_days / $dcontrol->days_covered) * $risk_item->annual_premium;
                      $dep_endorse_amount = ($dcontrol->endt_days / $dcontrol->days_covered) * $dep_annual_premium;
                      break;
                  
                  default:
                      $endorse_amount = $risk_item->annual_premium;
                      $dep_endorse_amount = $dep_annual_premium;
                      break;
              }

              if($risk_item->slug=='political-violence-and-terrorism'){
                  $pvt_endorse_amount     =   $endorse_amount;
                  $dep_pvt_endorse_amount =   $dep_endorse_amount;
              }

              $section = [
                  'reg_no' => $risk_item->reg_no,
                  'grp_code' => $risk_item->grp_code,
                  'item_code' => $risk_item->item_code,
                  'annual_premium' => $risk_item->annual_premium,
                  'endorse_amount' => $endorse_amount,
                  'dep_endorse_amount' => $dep_endorse_amount,
                  'sum_insured' => $risk_item->risk_value,
                  'dep_sum_insured' => $dep_risk_value,
                  'pvt_endorse_amount' => $pvt_endorse_amount,
                  'dep_pvt_endorse_amount' => $dep_pvt_endorse_amount,
              ];

              array_push($sections,$section);
          }     
      }
      return $sections;

      

    }

    // calculate loss ratio
    private function calcLossRatio($claim_paid, $claim_curr_est, $gross_premium) {
        if ($gross_premium == 0) {
            $lor = 0;
        } else {
            $lor = round((($claim_paid + $claim_curr_est) / $gross_premium) * 100, 2);
        }
        return $lor;
    }

    function fetchPremiumTax($basicPremium , $slug,$endt_renewal_no){
      $policy  = Polmasterend::where('endorse_no',$endt_renewal_no)->first();

      $classCode = $policy->class;
      $riskcount = Modtlpivot::where("endt_renewal_no" , $endt_renewal_no)
        ->where("status" , 'ACT')
        ->count();

      $val = 0;
      $tax = DB::table('RENEWAL_NOTICE_PREMIUM_TAXES')->where('slug', $slug)->first();
      $cls = Classmodel::select('stamp_duty','sticker_fees','admin_fees', 'motor_policy')->where('class',$classCode)->first();
      $pip = Pipstmp::select('levy_rate','fund_guarantee_rate','policy_fund_rate')->first();
      $pipcnam = Pipcnam::select('country_code', 'calculate_per_fleet')->first();

      if($tax->active != 'Y'){
         return $val;
      }

      switch ($tax->slug) {
        case 'training-levy':
          
          $val = $basicPremium * (floatval($pip->levy_rate) / 100);

          break;
        case 'phcf':
            
          $val = $basicPremium * (floatval($pip->policy_fund_rate) / 100);

          break;
        case 'stamp-duty':

            $val = ($pipcnam->calculate_per_fleet == 'Y') ? $riskcount * floatval($cls->stamp_duty) : floatval($cls->stamp_duty);
            
            break;
        case 'sticker-fees':
            if($cls->motor_policy == 'Y'){
            
              $val = ($pipcnam->calculate_per_fleet == 'Y') ? $riskcount * floatval($cls->sticker_fees) : floatval($cls->sticker_fees);
            }
         
            break;  
        case 'vat':
          // dd($basicPremium);
          $val = $this->computeVat($pipcnam->country_code, $basicPremium, $classCode, $endt_renewal_no);

          break;
        case 'admin-fees':

          $val = ($pipcnam->calculate_per_fleet == 'Y') ? $riskcount * floatval($cls->admin_fees) : floatval($cls->admin_fees);

          break;
        case 'fund-guarantee':

          $val = $basicPremium * (floatval($pip->fund_guarantee_rate) / 100);

          break;  
        
      }
       
       return $val;

    }

    public function computeVat(string $countryCode, int $basicPremium, int $classCode, $endt_renewal_no){

        $val = 0;
        //$vatRate = floatval(DB::table('VAT_SETUP')->where('vat_type', 'Standard')->value('vat_rate'));

        $vatcode = Dcontrol::where("endt_renewal_no",$endt_renewal_no)->first()->vat_code;
              
        $vatRate = VatSetup::where('vat_code',$vatcode)->first()->vat_rate;

        switch ($countryCode) {
          case '254': 
            
            $val = $basicPremium * (floatval($vatRate)/100);

            break;
          case '256':
             //basic+sticker_fees+traininglevy
             $amt = $basicPremium + $this->fetchPremiumTax($basicPremium, 'training-levy',$endt_renewal_no) + $this->fetchPremiumTax($basicPremium,'sticker-fees',$endt_renewal_no);
             $val = $amt * (floatval($vatRate)/100);

            break;
          case '250':
            //basic+admin fees
            $amt = $basicPremium + $this->fetchPremiumTax($basicPremium, 'admin-fees',$endt_renewal_no);
            $val = $amt * (floatval($vatRate)/100);

            break;
          default:
            
            $val = $basicPremium * (floatval($vatRate)/100);

            break;
        }
        

        return $val;
    }


    }

    


  

