<?php

namespace App\Console\Commands\RenewalNotice;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Events\DispatchNotificationEvent;

class DispatchNotificationsCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:send-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Notification from notifications_mail_cron to sendemail table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
      $today = Carbon::now()->toDateString();

      $emailRecords = DB::table('NOTIFICATIONS_MAIL_CRON')
                          ->whereDate('dispatch_date', $today)
                          ->whereNull('email_id')
                          ->where(function($query) {
                              $query->where('is_renewed', 'N')
                                    ->orWhere('is_cancelled', 'N');
                          })
                          ->get();
      //  dd(count($emailRecords));     
      
      try {
        foreach ($emailRecords as $record) {
          $notificationData = ['cron_data' => $record];
          DispatchNotificationEvent::dispatch($slug = $record->notification_slug, $notificationData);
        }
      } catch (\Throwable $th) {
        throw $th;
      }

      $this->info('Notifications dispatched successfully.');
    }
}
