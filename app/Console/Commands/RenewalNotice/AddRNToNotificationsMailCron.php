<?php

namespace App\Console\Commands\RenewalNotice;

use DB;
use App\Agmnf;
use App\Client;
use App\Pipcnam;
use App\Dcontrol;
use App\Debitmast;
use App\Polmaster;
use App\SendEmail;
use Carbon\Carbon;
use App\Renewal_notice;
use Illuminate\Console\Command;
use App\Models\RenewalNoticeSetup;
use App\Classes\RenewalNotification;
use Illuminate\Support\Facades\Mail;
use App\Models\RenewalNoticeInterval;
use App\Mail\RenewalNotificationEmail;
use Aimsoft\UserManagement\Models\Role;
use App\Classes\AddToNotificationsCron;
use App\Services\IntermediaryQueryService;
use Aimsoft\UserManagement\Models\Permission;
use App\ValueObjects\IntermediaryQueryParams;
use App\Models\Intermediary;

class AddRNToNotificationsMailCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:mail-cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add Renewal Notification to NOTIFICATIONS_MAIL_CRON table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
      DB::beginTransaction();
        
      try {
          $renewalNotSetup = RenewalNoticeSetup::first();
          $daysBeforeExp = $renewalNotSetup->days_before_expiry; 
          $renewalNoticeIntervals = RenewalNoticeInterval::orderBy('sequence', 'asc')->get();

          $intervalDays = [];

          foreach ($renewalNoticeIntervals as $interval) {
              $intervalDays[] = $interval->days;
          }

          // UPDATE EXISTING CRON RECORDS FOR RENEWAL NOTICE/REMINDER
          $mailCrons = DB::table('NOTIFICATIONS_MAIL_CRON')
                          ->whereIn('notification_slug', ['renewal-notice', 'renewal-reminder'])
                          ->get();

          foreach ($mailCrons as $cron) {
            $policy = Polmaster::select('policy_no', 'renewal_date', 'cov_period_to', 'cov_period_from')->where('policy_no', $cron->policy_no)->first();

            $periodFrom = $policy->cov_period_from;
            $periodTo = $policy->cov_period_to;

            $lastCommittedDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type', 'dcon_no')
                                              ->where('dcontrol.policy_no',$policy->policy_no)
                                              ->whereRaw("(delete_str is null or delete_str = 'N')")
                                              ->whereRaw("cancelled = 'N' and (committed is null or committed = 'Y')")
                                              ->where('dcontrol.period_from', '>=', $periodFrom)
                                              ->where('dcontrol.period_to', '<=', $periodTo)
                                              ->orderBy('dcon_no', 'DESC')
                                              ->first();

            if($lastCommittedDebited->trans_type != 'NIL'){
              $lastDebited = Debitmast::where('endt_renewal_no', $lastCommittedDebited->endt_renewal_no)->first();
                
              if(is_null($lastDebited)){
                
                $lastCommittedDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type', 'dcon_no')
                  ->where('dcontrol.policy_no',$policy->policy_no)
                  ->whereRaw("(delete_str is null or delete_str = 'N')")
                  ->whereRaw("cancelled = 'N' and (committed is null or committed = 'Y')")
                  ->where('dcontrol.period_from', '>=', $periodFrom)
                  ->where('dcontrol.period_to', '<=', $periodTo)
                  ->where('endt_renewal_no', '<>', $lastCommittedDebited->endt_renewal_no)
                  ->orderBy('dcon_no', 'DESC')
                  ->first();
  
              }
            }
  
            $lastDebitedDconNo = Dcontrol::where('dcontrol.policy_no',$policy->policy_no)
                                        ->join('debitmast', function($query){
                                            $query->on('dcontrol.endt_renewal_no','=' ,'debitmast.endt_renewal_no');
                                        })
                                        ->whereNull('dcontrol.delete_str')
                                        ->where('dcontrol.trans_type', '<>', 'NIL')
                                        ->where('debitmast.period_from', '>=', $periodFrom)
                                        ->where('debitmast.period_to', '<=', $periodTo)
                                        ->orderBy('dcontrol.dcon_no', 'DESC')
                                        ->max('dcontrol.dcon_no');
  
            $lastDebited = Dcontrol::select('policy_no','endt_renewal_no', 'trans_type')
                                    ->where('policy_no',$policy->policy_no)
                                    ->where('dcon_no', $lastDebitedDconNo)
                                    ->first();
                                        
            if (!is_null($lastDebited)) {
              $query = DB::table('NOTIFICATIONS_MAIL_CRON')
                        ->where('policy_no', $cron->policy_no)
                        ->whereIn('notification_slug', ['renewal-notice', 'renewal-reminder'])
                        ->whereNull('email_id');
          
              switch ($lastCommittedDebited->trans_type) {
                case 'CNC':
                  $query->update([
                      'is_cancelled' => 'Y',
                      'is_renewed' => 'N',
                      'updated_at' => Carbon::now()->toDateString()
                  ]);
                  break;
        
                case 'RNS':
                  $query->update([
                      'is_cancelled' => 'N',
                      'is_renewed' => 'N',
                      'updated_at' => Carbon::now()->toDateString()
                  ]);
                  break;
        
                case 'REN':
                  $query->update([
                      'is_renewed' => 'Y',
                      'is_cancelled' => 'N',
                      'updated_at' => Carbon::now()->toDateString()
                  ]);
                  break;
              }
            } 
          }

          $todayDate = Carbon::now()->toDateString();
          $renewalNotices = Renewal_notice::whereIn('renewal_notice_status', ['APPROVED', 'DECLINED'])
              ->whereNull('sent_date')
              ->get();
          // dd(count($renewalNotices));

          $company_name = Pipcnam::pluck('company_name')->first();

          $recordInserted = false;
          if(count($renewalNotices) > 0){
              foreach ($renewalNotices as $notice) {
                  
                  
                  $existingRecord = DB::table('NOTIFICATIONS_MAIL_CRON')
                    ->where('policy_no', $notice->policy_no)
                    ->whereIn('category', ['RENEWAL NOTICE', 'RENEWAL REMINDER', 'DECLINATURE LETTER', 'DECLINATURE REMINDER'])
                    ->where(function($query) {
                      $query->where('is_renewed', 'N')
                            ->orWhere('is_cancelled', 'N');
                    })
                    ->exists();
  
                  // Skip adding if the record already exists
                  if ($existingRecord) {
                      continue;
                  }

                  $intermediaryParams = new IntermediaryQueryParams([
                      'agentNo' => $notice->agent_no,
                  ]);

                  $direct = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)
                              ->whereHas('acctype', function ($query) {
                                  $query->where('direct', 'Y');
                              })->first();

                  if ($direct) {
                    
                  $client_data = Client::whereRaw("trim(client_number) = ?", [$notice->client_number])->first();
                  } else {

                    $client_data = IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();

                    if ($client_data) {
                        $client_data->telephone = $client_data->cell_phone;
                        $client_data->mobile_no = $client_data->cell_phone;
                        $client_data->e_mail = $client_data->email;
                    }
                  }     
                  // $paramSetupEmail = trim($renparam->cc_email);
                  $policyNo = $notice->policy_no;
                  $polren = $notice->pol_ren;
                  $renewalDate = $notice->renewal_date;
                 
                  // copy person who approved
                  $permission = Permission::where('slug', 'approve-renewal-notices')->first();
                  $uwManager = $permission->users->first();
                  $role = Role::where('id',$uwManager->role_id)->first();
                  
                  $managerEmail = $uwManager->email;
                  $reviewedBy = $notice->approved_by;

                  $reviewerName = DB::table('aimsusers')->where('user_name', $reviewedBy)->value('email');

                  if($notice->approved_by != 'AUTO'){
                    $managerEmail = $reviewerName;
                  }
                  
                  // $copyReceivers = [$agentEmail, $managerEmail];
                  $copyReceivers = [$managerEmail];

                  $expiryDate = Carbon::parse($notice->expiary_date);
                  
                  $renewalTemplate = 'renewal_notice_templates.renewal_notice_letter';
                  $declineTemplate = 'emails.decline_letter';
                  
                  $telephone = $client_data->mobile_no;

                  if(is_null($telephone)){
                    $telephone = 'missing';
                  }

                  // decline case
                  if ($notice->renewal_notice_status == 'DECLINED') {
                    if (count($intervalDays) > 0) {
                        $highestDays = max($intervalDays);

                        foreach ($intervalDays as $days) {
                            $dispatchDate = Carbon::now()->addDays($days)->format('Y-m-d');
                            
                            
                            if ($days == $highestDays) {
                                // decline letter
                                $message = "<html>
                                  <body>
                                      <p>Dear $client_data->name,</p>
                                      <p>We thank you for being our valued customer and taking your insurance with $company_name.</p>
                                      <p>Please find attached your Declinature Letter for policy $policyNo.</p>
                                      <p>Thank you for your understanding in this matter.</p>
                                      <p>Kind Regards</p>
                                      <p>$company_name</p>
                                  </body>
                                </html>";
                                
                                $category = "DECLINATURE LETTER";
                                $notificationCronRecord = new AddToNotificationsCron($category,$client_data->e_mail,$message,$dispatchDate,'declinature-letter');
                                $emailRecord = $notificationCronRecord->setPolicyNo($policyNo)
                                                              ->setPolren($polren)
                                                              ->setRenewalDate($renewalDate)
                                                              ->setCreator('system')
                                                              ->setIsRenewed('N')
                                                              ->setIsCancelled('N')
                                                              ->createNotificationMailRecord();

                            } else {
                                // decline email reminder
                                $message = "<html>
                                  <body>
                                      <p>Dear $client_data->name,</p>
                                      <p>We would like to kindly remind you of the attached Declinature Letter that was previously sent regarding your policy $policyNo with $company_name.</p>
                                      <p>Thank you for your attention to this matter.</p>
                                      <p>Kind Regards,</p>
                                      <p>$company_name</p>
                                  </body>
                                </html>";
                                $category = "DECLINATURE REMINDER";
                                $notificationCronRecord = new AddToNotificationsCron($category,$client_data->e_mail,$message,$dispatchDate,'declinature-reminder');
                                $emailRecord = $notificationCronRecord->setPolicyNo($policyNo)
                                                              ->setPolren($polren)
                                                              ->setRenewalDate($renewalDate)
                                                              ->setCreator('system')
                                                              ->setIsRenewed('N')
                                                              ->setIsCancelled('N')
                                                              ->createNotificationMailRecord();

                            }
                            $smsMessage = "Dear $client_data->name, we will not be renewing your policy $policyNo. Please check your email for more details. Thank you.";
                            $smsRecord = $notificationCronRecord->createNotificationSmsRecord($policyNo,$category,$telephone,$smsMessage,$dispatchDate);
                            
                            if ($emailRecord && $smsRecord) {
                                $recordInserted = true;
                            }
                        }
                    } else {
                        $dispatchDate = Carbon::now()->addDays($daysBeforeExp)->format('Y-m-d');
                        // $company_name = Pipcnam::pluck('company_name')->first();
                        $message = "<html>
                          <body>
                              <p>Dear $client_data->name,</p>
                              <p>We thank you for being our valued customer and taking your insurance with $company_name.</p>
                              <p>Please find attached your Declinature Letter for policy $policyNo.</p>
                              <p>Thank you for your understanding in this matter.</p>
                              <p>Kind Regards</p>
                              <p>$company_name</p>
                          </body>
                        </html>";
                        $category = "DECLINATURE LETTER";

                        $notificationCronRecord = new AddToNotificationsCron($category,$client_data->e_mail,$message,$dispatchDate,'declinature-letter');
                        $emailRecord = $notificationCronRecord->setPolicyNo($policyNo)
                                                      ->setPolren($polren)
                                                      ->setRenewalDate($renewalDate)
                                                      ->setCreator('system')
                                                      ->setIsRenewed('N')
                                                      ->setIsCancelled('N')
                                                      ->createNotificationMailRecord();

                        $smsMessage = "Dear $client_data->name, this is a reminder regarding the declinature of your policy $policyNo with $company_name. Please refer to your email for details.";
                        $smsRecord = $notificationCronRecord->createNotificationSmsRecord($policyNo,$category,$telephone,$smsMessage,$dispatchDate);
                        
                        if ($emailRecord && $smsRecord) {
                            $recordInserted = true;
                        }
                    }
                  }

                  // approved case
                  if ($notice->renewal_notice_status == 'APPROVED') {
                    if (count($intervalDays) > 0) {
                        $highestDays = max($intervalDays);

                        foreach ($intervalDays as $days) {
                            $dispatchDate = $expiryDate->copy()->subDays($days)->format('Y-m-d');
                            
                            $smsMessage = "Dear $client_data->name, your policy $policyNo with $company_name is due for renewal. Please review the renewal notice sent to your email. Thank you.";
                            if ($days == $highestDays) {
                                // Send renewal notice
                                $message = "<html>
                                  <body>
                                      <p>Dear $client_data->name,</p>
                                      <p>We thank you for being our valued customer and taking your insurance with $company_name.</p>
                                      <p>Please find attached your Renewal notice for policy $policyNo.</p>
                                      <p>Thank you for your continued business support.</p>
                                      <p>Kind Regards</p>
                                      <p>$company_name</p>
                                  </body>
                                </html>";
                                
                                $category = "RENEWAL NOTICE";
                                $notificationCronRecord = new AddToNotificationsCron($category,$client_data->e_mail,$message,$dispatchDate,'renewal-notice');
                                $emailRecord = $notificationCronRecord->setPolicyNo($policyNo)
                                                              ->setPolren($polren)
                                                              ->setRenewalDate($renewalDate)
                                                              ->setCreator('system')
                                                              ->setIsRenewed('N')
                                                              ->setIsCancelled('N')
                                                              ->setTemplate($renewalTemplate)
                                                              ->setCcReceiver($copyReceivers)
                                                              ->createNotificationMailRecord();

                            } else {
                                // send renewal email reminder
                                $message = "<html>
                                  <body>
                                      <p>Dear $client_data->name,</p>
                                      <p>We hope this message finds you well.</p>
                                      <p>This is a friendly reminder regarding the renewal of your insurance policy $policyNo with $company_name.</p>
                                      <p>Thank you for your continued business support.</p>
                                      <p>Kind Regards,</p>
                                      <p>$company_name</p>
                                  </body>
                                </html>";
                                $category = "RENEWAL REMINDER";
                                $notificationCronRecord = new AddToNotificationsCron($category,$client_data->e_mail,$message,$dispatchDate,'renewal-reminder');
                                $emailRecord = $notificationCronRecord->setPolicyNo($policyNo)
                                                              ->setPolren($polren)
                                                              ->setRenewalDate($renewalDate)
                                                              ->setCreator('system')
                                                              ->setIsRenewed('N')
                                                              ->setIsCancelled('N')
                                                              ->setCcReceiver($copyReceivers)
                                                              ->createNotificationMailRecord();
                            }

                            $smsRecord = $notificationCronRecord->createNotificationSmsRecord($policyNo,$category,$telephone,$smsMessage,$dispatchDate);
                            
                            if ($emailRecord && $smsRecord) {
                                $recordInserted = true;
                            }
                        }
                    } else {
                        $dispatchDate = $expiryDate->copy()->subDays($daysBeforeExp)->format('Y-m-d');
                        $message = "<html>
                          <body>
                              <p>Dear $client_data->name,</p>
                              <p>We hope this message finds you well</p>
                              <p>This is a friendly reminder regarding your policy $policyNo which is scheduled to expire on $notice->expiary_date.</p>
                              <p>We kindly advise you to make the necessary arrangements for renewal prior to the expiration date to ensure continuous coverage.</p>
                              <p>If you have already renewed this policy, please disregard this notice.</p>

                              <p>Kind Regards</p>
                              <p>$company_name</p>
                          </body>
                        </html>";
                        $smsMessage = "Dear $client_data->name, your policy $policyNo with $company_name is due for renewal. Please review the renewal notice sent to your email. Thank you.";
                        $category = "RENEWAL NOTICE";
                
                        $notificationCronRecord = new AddToNotificationsCron($category,$client_data->e_mail,$message,$dispatchDate,'renewal-notice');
                        $emailRecord = $notificationCronRecord->setPolicyNo($policyNo)
                                                      ->setPolren($polren)
                                                      ->setRenewalDate($renewalDate)
                                                      ->setCreator('system')
                                                      ->setIsRenewed('N')
                                                      ->setIsCancelled('N')
                                                      ->setTemplate($renewalTemplate)
                                                      ->setCcReceiver($copyReceivers)
                                                      ->createNotificationMailRecord();

                        $smsRecord = $notificationCronRecord->createNotificationSmsRecord($policyNo,$category,$telephone,$smsMessage,$dispatchDate);
                        
                        if ($emailRecord && $smsRecord) {
                            $recordInserted = true;
                        }
                    }
                  }
              }
          }

          DB::commit();
        
          if($recordInserted){
            $this->info("Record(s) have been inserted into notifications_mail_cron table");
          }else {
            $this->info("There are no record(s) to insert into notifications_mail_cron table");
          }

          
      } catch (\Throwable $th) {
          DB::rollback();
          $this->info("An error occured. Unable to insert record(s) into sendemail table");
          dd($th->getMessage());
      }
    }
}
