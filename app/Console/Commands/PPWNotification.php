<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Events\DispatchNotificationEvent;
use Carbon\Carbon;
use DB;
use App\Pipcnam;

class PPWNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ppw:notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'PPW breach notification to reinsurance group email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $pipcnam = Pipcnam::where('record_type',0) ->first();
        $ppw_breach_days = $pipcnam->ppw_breach_days;
    
        if( !is_null($ppw_breach_days)){
            $sql = "
            SELECT 
                b.POLICY_NO,
                b.ENDT_RENEWAL_NO,
                b.DOLA,
                a.PPW_CODE,
                a.PPW_DAYS,
                (b.DOLA + a.PPW_DAYS) AS PPW_DATE,
                ((b.DOLA + a.PPW_DAYS) - $ppw_breach_days) AS PPW_BREACH_DATE
            FROM creditmast a
            INNER JOIN acdet b 
                ON b.endt_renewal_no = a.endt_renewal_no
                AND b.SOURCE = 'U/W'
                AND b.UNALLOCATED > 0
            WHERE a.ENDT_RENEWAL_NO IN (
                SELECT c.ENDT_RENEWAL_NO 
                FROM debitmast c
            )
            AND a.PPW_DAYS IS NOT NULL
            AND ((b.DOLA + a.PPW_DAYS) - $ppw_breach_days) = TRUNC(SYSDATE)
            GROUP BY 
                b.POLICY_NO,
                b.ENDT_RENEWAL_NO,
                b.DOLA,
                a.PPW_CODE,
                a.PPW_DAYS
            ";
        
            try {
            
                $results = DB::select($sql);

                foreach ($results as $result) {
                    $notificationData = [
                        'ppw_data' => $result,
                    ];
                    $slug  = 'ppw-breach-alert';
                    DispatchNotificationEvent::dispatch($slug,$notificationData);
                    echo $result->policy_no . ' - ' . $result->ppw_date . ' - ' . $result->ppw_breach_date . '<br>';
                }
            } catch (\Exception $e) {
            
                echo 'Error: ' . $e->getMessage();
            }
        }else{
            echo 'PPW breach days is not set in the configuration';
        }
        
    }
    

}
