<?php

namespace App\Console\Commands;

use DB;
use Auth;
use Mail;
use App\Agmnf;
use App\Client;
use App\Pipcnam;
use App\Currency;
use App\Dcontrol;
use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Mail\InstallmentMail;
use Illuminate\Console\Command;
use App\ProcessEmailNotification;
use App\Models\PremiumPaymentPlan;
use App\Constants\NotificationConstants;
use App\Services\IntermediaryQueryService;
use App\ValueObjects\IntermediaryQueryParams;
use App\Http\Controllers\gb\underwriting\PremiumPaymentPlanController;





class Installmentnotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'push:installment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Push Installment Notifications';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $todaysdate=Carbon::now();
        $pipcnam = Pipcnam::first();
        // $notification_date =$todaysdate->addDays(NotificationConstants::DAYS_INSTALLMENT_VALUE);
        $notification_date =$todaysdate->addDays($pipcnam->payment_plan_notif_days);
        $pre_plan = $pipcnam->predebit_plan;
        
        $records = PremiumPaymentPlan::whereDate('due_date',$notification_date)->get();

        
      
        foreach ($records as $key => $value) {
            try {
                $dcontrol = Dcontrol::where('endt_renewal_no', $value->endt_renewal_no)->first()->apply_payment_plan;

                if ($pre_plan == 'Y' && $dcontrol == 'Y' && is_null($value->predebit_ext_endt)) {
                    $inst = new PremiumPaymentPlanController;
                    
                    $inst->extendPolicyInstallment($value->policy_no, $value->installment_no);
                }

                if ($pre_plan == 'Y' && $dcontrol == 'Y' && !is_null($value->predebit_ext_endt)) {
                    continue;
                }
                
                $mailsend=DB::table("notification_log")->where("endt_renewal_no",$value->endt_renewal_no)
                    ->where("interval",$value->installment_no)
                    ->where("notification_type","installment-due")
                    ->whereDate("processsed_date", Carbon::now()->toDateString())
                    ->exists();
               
                
                    if(!$mailsend){
                        $dcontrol = Dcontrol::whereRaw("trim(endt_renewal_no) = '" . $value->endt_renewal_no . "'")->first();
                        $client_data = Client::whereRaw("trim(client_number) = '" . $dcontrol->client_number . "'")->first();
                        $currency = Currency::whereRaw("trim(currency_code) = '".trim($dcontrol->currency) ."'")->first()->currency; 
                        $amtvalue=number_format($value->amount_due, 2);
                        $policy_number=formatPolicyOrClaim( $dcontrol->endt_renewal_no);
                        $endorse_no=formatPolicyOrClaim( $dcontrol->endt_renewal_no);
                       
                        $due_date=$notification_date->format('jS F Y');
                        $title_date = date('d/m/Y', $notification_date->timestamp); 
                                                    
                        $message="This is a Polite reminder that your Installment Payment of $currency  $amtvalue  for Policy $policy_number and Endorsement number $endorse_no is due on $due_date,
                            Kindly make arrangement to settle this amount ";
                        #client mail
                        $clientemail=$client_data->e_mail;
                        $clientemail=trim($clientemail);

                        #broker mail
                        // $agentemail=  Agmnf::where('agent',$dcontrol->agent)
                        // ->where('branch',$dcontrol->branch)
                        // ->first()->email;
                        $intermediaryParams = new IntermediaryQueryParams([
                            'agentNo' => $dcontrol->agent,
                        ]);
                        $intermediary_details  =  IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();

                        $agentemail  = $intermediary_details->email;

                        $agentemail=trim($agentemail);

                        #underwriting mail
                        $undermail = ProcessEmailNotification::where('process_slug','installment-due-notification')->first()->process_email;
                        $undermail=trim($undermail);
                       
                        if (isset($clientemail) && $intermediary_details->acc_type == 1) {
                            // If client email is set and account type is 1, use client email
                            $email = $clientemail;
                        } elseif (isset($agentemail) && $intermediary_details->acc_type != 1) {
                            // If intermediary email is set and account type is not 1, use intermediary email
                            $email = $agentemail;
                        } 
              

                        
                        $details = [
                            'title' => "Premium payment installment due( $title_date )",
                            'content' => $message,
                            'client'=>$client_data->name
                        ];
                    
                        $data =Mail::to($email)
                        ->cc([$undermail])
                        ->send(new InstallmentMail($details));
                        // dd(count(Mail::failures()));
                        if( count(Mail::failures()) < 1 ) {
                        
                            $slug = preg_replace("/[^a-zA-Z0-9 ]/", '', $title);

                            $slug = strtolower($slug);
                            $slug =  Str::slug($slug,'-');
                            //dd($slug);
                        
                                DB::table("notification_log")->insert([
                                'endt_renewal_no'=>$dcontrol->endt_renewal_no,
                                'notification_type'=>"installment-due",
                                'interval'=>$value->installment_no,
                                'message'=>$message,
                                'processsed_date'=>Carbon::now(),
                                'receipients'=>json_encode([$email,$uderwritingmail])
                                ]);

                            
                        }

                    }

            } catch (\Throwable $th) {
                continue;
            } 

        }
    }
}
