<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use DB;
use App\Http\Controllers\gb\underwriting\EnfrisController;

class EfrisApprovals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:approval';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Query Credit Note approval status in URA';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $pending_recs = DB::table("efris_approval_records")->where('efris_intergrated','Y')
                                                            -> where(function ($query) {
                                                                    $query->whereNull('approval_status')
                                                                        ->orWhereNotIn('approval_status', [101, 103, 104]);
                                                                })
                                                           ->get();
    
        foreach ($pending_recs as $key => $value) {
            $NewReq =New EnfrisController;
           $updaterow =  $NewReq->checkApproval($value->efris_reference_no);
          

        }
    }
}
