<?php
 
namespace App\Console\Commands;
 
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use App\SendEmail;
use App\Aimsuser_web;
use App\Mail\ActivityEmail;
 
class SendingEmail extends Command
{
    /** 
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:emails';
 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'send all the emails stored in the sendemail table';
 
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
 
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $datas = SendEmail::whereRaw("trim(status) = 'SEND'")->get();

        if(!empty($datas)){

            foreach ($datas as $data) {
                if(!empty($data->receiver)){
                    $email = trim($data->receiver); 
                    $subject = trim($data->category); 
                    $title = trim($data->category);
                    $createdate = Carbon::parse($data->createdate)->diffForhumans();
                    $user = $data->creator;
                    $user = Aimsuser_web::WhereRaw("trim(user_name) = '".trim($user)."' ")->first();
                    // update sent email
                   // $update_model = SendEmail::where('emailid', trim($data->emailid))->first();

                    Mail::to($email)->send(new ActivityEmail($data, $title, $createdate,$subject,$user));

                    //if(!empty($update_model)){
                    if( count(Mail::failures()) < 1 ) {
                        $update_model = SendEmail::where('emailid', trim($data->emailid))->update([
                            'status' => 'SENT',
                            'sentdate' => Carbon::now()
                            ]);

                    }else{
                        $this->info($data);    
                    }
                    // !empty() if.end
                }
            }
            
            if( count(Mail::failures()) < 1 ) {

                $this->info("all mails have been sent");
            }

            
        }

        //$this->info("all mails have been sent");

    }

}
