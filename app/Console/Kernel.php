<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use \App\Dtran0;
use App\PeriodParamater;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        \App\Console\Commands\Deactivate::class,
        \App\Console\Commands\SendingEmail::class,
        \App\Console\Commands\AutoCloseMonth::class,
        \App\Console\Commands\ClaimdocsNotification::class,
        \App\Console\Commands\EdmsDocumentUpload::class,
        \App\Console\Commands\CreateEdmsDocumentTypeInEdms::class,
        \App\Console\Commands\NotifyClaimDocsManager::class,
        \App\Console\Commands\EmailFailedDocumentsUpload::class,
        \App\Console\Commands\EfrisApprovals::class,
        \App\Console\Commands\Outstandingdebits::class,
        \App\Console\Commands\Installmentnotifications::class,
        \App\Console\Commands\LapsePolicies::class,
        \App\Console\Commands\LapsedPoliciesNotifications::class,
        \App\Console\Commands\SendOutstandingDocs::class,
        \App\Console\Commands\SendPremPaymentReminder::class,
        \App\Console\Commands\RenewalNotice\RenewalNotices::class,
        \App\Console\Commands\RenewalNotice\AddRNToNotificationsMailCron::class,
        \App\Console\Commands\RenewalNotice\DispatchNotificationsCron::class,
        \App\Console\Commands\notify_os_claims_monthly::class,
        \App\Console\Commands\SLANotification::class,
        \App\Console\Commands\BirthdayNotification::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('deactivate:user')->daily();

        $schedule->command('failed:uploads')->daily();
        $schedule->command('renewal:notices')->daily();
        $schedule->command('notification:mail-cron')->daily();
        $schedule->command('notification:send-email')->daily();
        $schedule->command('lapsed:policies-notifications')->daily();
        $schedule->command('birthday:notification')->daily();
        // $schedule->command('claimdocs:notify')->daily();
        // $schedule->command('claimdocs:notifymanager')->weeklyOn(0, '8:00');

        $schedule->command('app:send-decline-letters')->daily();
        $schedule->command('request:outstanding-docs')->weekly();

        $schedule->command('send:emails')->everyFiveMinutes();
        $schedule->command('app:send-notifications')->daily();
        $schedule->command('app:insert-into-send-email')->daily();
        $schedule->command('app:insert-into-send-sms')->daily();
        
        $schedule->command('check:approval')->everyFiveMinutes();

        $schedule->command('auto:lapsepolicy')->dailyAt('00:00');

        $schedule->command('edms:upload')->twiceDaily(13,20);
        $schedule->command('edms:doctype')->daily();
        $rtime = Dtran0::where('rec_no',0)->get(['auto_month_cls','cls_date_type','set_closure_day','set_closure_time']);
        $rtime = $rtime[0];

        if($rtime->auto_month_cls == 'Y'){
            
            if($rtime->cls_date_type == 'S'){
                $schedule->command('auto:closemonth')->monthlyOn($rtime->set_closure_day, $rtime->set_closure_time);    
            }

            else if($rtime->cls_date_type == 'C'){ 
                
                $schedule->command('auto:closemonth')->monthlyOn(date('t'),$rtime->set_closure_time);
            }

        }

        $schedule->command('auth:clear-resets')->daily();

        $schedule->command('push:debits')->daily();
        $schedule->command('push:installment')->daily();

        ##autoclose gl period
        $glperiod = PeriodParamater::where('record_type', 1)->get()[0];

        $schedule->command('auto:closeglmonth')->dailyAt($glperiod->closure_time);
        $schedule->command('legal_notification')->daily(); 
        $schedule->command('notify_os_claims_monthly')->monthly();
        $schedule->command('send:sla-notification')->daily();

        $schedule->command('app:send-reminders-to-service-providers')->daily();

        /**
        $glperiod = PeriodParamater::where('record_type', 1)->get()[0];
        
        if($glperiod->auto_close_gl == 'Y'){
            if($glperiod->end_month_based_on == 'S'){
                $schedule->command('auto:closeglmonth')->monthlyOn($glperiod->set_closure_day, $glperiod->closure_time);    
            }

            if($glperiod->end_month_based_on == 'C'){ 
                $schedule->command('auto:closeglmonth')->monthlyOn(date('t'), $glperiod->closure_time);
            }
        }
        **/
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
