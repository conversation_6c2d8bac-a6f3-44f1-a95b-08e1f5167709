<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AuditActionEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $tableName;
    public $conditionColumns;
    public string $action_type;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($tableName,$conditionColumns,$action_type ='UPDATE')
    {
        $this->tableName = $tableName;
        $this->conditionColumns = $conditionColumns;
        $this->action_type = $action_type;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
