<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CreatePolicyCabinetInEdms
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public $client_number,$pol_clm_number,$parent_cabinet_name;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($client_number,$pol_clm_number,$parent_cabinet_name)
    {
        //
      $this->client_number = $client_number;
      $this->pol_clm_number = $pol_clm_number;
      $this->parent_cabinet_name = $parent_cabinet_name;
    //   $this->trans_type = $trans_type;

      
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
