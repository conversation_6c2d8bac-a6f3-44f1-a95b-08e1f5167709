<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CreateCabinetInEdms
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public $client_intermediary_no;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($client_intermediary_no)
    {
      
      $this->client_intermediary_no = $client_intermediary_no;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
