<?php

namespace App\Exports;

use Illuminate\Contracts\Support\Responsable;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;

class SalvageMemoExport implements Responsable
{        
    private $fileName = 'SalvageMemo.xlsx';
    private $salvageDocs;

    public function __construct($salvageDocs)
    {
        $this->salvageDocs = $salvageDocs;
    }

    public function toResponse($request)//export()
    {
        // Load the template
        // $spreadsheet = IOFactory::load(storage_path('public/downloads/Memo_template.xlsx'));
        $spreadsheet = IOFactory::load(public_path('downloads/Memo_template.xlsx'));
        $sheet = $spreadsheet->getActiveSheet();

        $row = 26; // Starting row for data
        foreach ($this->salvageDocs as $doc) {
            $memoItem = $doc->memo_items->first();
            $sheet->setCellValue('A' . $row, $doc->id);
            $sheet->setCellValue('B' . $row, $doc->description);
            $sheet->setCellValue('C' . $row, $memoItem && $memoItem->received == 'Y' ? 'Yes' : '');
            $sheet->setCellValue('D' . $row, $memoItem && $memoItem->received == 'N' ? 'No' : '');
            $sheet->setCellValue('E' . $row, $memoItem->comments ?? '');
            $row++;
        }

        // Update the updated_by and updated_at fields
        $sheet->setCellValue('D' . ($row + 5), ($memoItem->updated_by ?? ''));
        $sheet->setCellValue('D' . ($row + 6), ($memoItem->updated_at ?? ''));

    
        //save in a temporary path
        $tempFilePath = public_path('temp/'. $this->fileName);

        if (!is_writable(public_path('temp'))) {
            return response()->json(['error' => 'Directory is not writable: ' . public_path('temp/')], 500);
        }
    
        try {
            $writer = new Xlsx($spreadsheet);
            $writer->save($tempFilePath);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }

        return response()->download($tempFilePath)->deleteFileAfterSend(true);
    }
    
}
