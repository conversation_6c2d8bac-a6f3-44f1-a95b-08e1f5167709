<?php
namespace App\Classes\gl;

use App\Batch_details;
use App\Batch_list;
use App\Batch_entry;
use App\Prepay_details;

class GenerateEntryId{
    public $batch_no;
    public $entry;
    public $entry_id;

    public function __construct($batch_no, $entry_id)
    {
        $this->batch_no = $batch_no;
        $this->entry_id = $entry_id;
    }

    public function get_next_entry($batch_no)
    {
        return Batch_entry::where('batch_no', $batch_no)->max('entry_id') + 1;
    }
    public function next_entry($batch_no)
    {
        return Prepay_details::where('batch_no', $batch_no)->max('entry_id') + 1;
    }

    public function get_next_item_no($batch_no, $entry_id)
    {
        return Batch_details::where('batch_no', $batch_no)->where('entry_id', $entry_id)->max('item_no') + 1;
    }

    public function next_item_no($batch_no, $entry_id)
    {
        return Prepay_details::where('batch_no', $batch_no)->where('entry_id', $entry_id)->max('item_no') + 1;
    }

    
}

?>