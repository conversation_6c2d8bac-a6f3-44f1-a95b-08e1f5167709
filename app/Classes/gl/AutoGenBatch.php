<?php
namespace App\Classes\gl;
use App\Classes\gl\GenerateBatch;
use App\Classes\gl\GenerateEntryId;

use App\Glsource;
use App\ApInvoiceHeader;
use App\Batch_list;
use App\ApCrnInvItem;
use App\Apcrnotes;
use App\ApCrnTax;
use App\ApInvoiceDetails;
use App\Appaymentheader;
use App\Appyinvoices;
use App\Appytaxes;
use App\Apstatus;
use App\ApTax;
use App\ApTransTypes;
use App\Apvendors;
use App\ApWhtCertalloc;
use App\ApWhtCertMast;
use App\ArRecHeader;
use App\BankTrans;
use App\Batch_details;
use App\Batch_entry;
use App\Cbdeduct;
use App\Cbmast;
use App\Cbmastana;
use App\Cbmastchq;
use App\PeriodModule;
use App\Clpmn;
use App\Currency;
use App\Curr_ate;
use App\Glbank;
use App\Glbatch_status;
use App\Gltaxes;
use App\Gltaxtypes;
use App\Gltaxtypesana;

use Auth;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;

class AutoGenBatch 
{
    private $period_year, $period_month, $batch_src, $reference, $vendor_id, $doc_type;

    function __construct($period_year, $period_month, $batch_src, $reference, $vendor_id = null, $doc_type) {
        $this->period_year = $period_year;
        $this->period_month = $period_month;
        $this->batch_src = $batch_src;
        $this->vendor_id = $vendor_id;
        $this->reference = $reference;
        $this->doc_type = $doc_type;
    }
    
    private function upd_batch($batch_no, $batch_source) {
        $total_local_batch_amount = Batch_entry::whereRaw("trim(batch_no) = '" . $batch_no . "'")->whereRaw("deleted != 'Y' or deleted is null")->sum('local_dr_amount');
        $total_foreign_batch_amount = Batch_entry::whereRaw("trim(batch_no) = '" . $batch_no . "'")->whereRaw("deleted != 'Y' or deleted is null")->sum('foreign_dr_amount');
        $entries_count = Batch_entry::whereRaw("trim(batch_no) = '" . $batch_no . "'")->count();
        
        $batch_upd = Batch_list::whereRaw("trim(batch_no) = '" . $batch_no . "'")->update([
            'no_entries' => $entries_count,
            'local_batch_amount' => $total_local_batch_amount,
            'changed_by' => Auth::user()->user_name,
            'changed_date' => Carbon::now(),
            'expected_batch_total' => $total_foreign_batch_amount,
            'foreign_batch_amount' => $total_foreign_batch_amount
        ]);
    }

    private function gain_loss_transaction($transaction, $batch_source, $period_year, $period_month) {
        ##create a batch for posting gain loss
        $getsources = Glsource::where('source_code', $batch_source)->first();

        $new_batch = new GenerateBatch($period_year, str_pad($period_month, 2, 0, STR_PAD_LEFT), $batch_source);

        $batch_no = $new_batch->generate();

        $get_all_trans = Batch_list::where('batch_no', $batch_no)->first();

        $doc_type = Doctype::where('doc_type', 'MJV')->first();
        $doc_type2 = Doctype::where('doc_type', 'MJVREV')->first();
        $batch_title = 'Gain/loss on USD payment for voucher ' . $transaction->payment_header_no;

        $batch = Batch_list::create([
            'batch_no' => $batch_no,
            'batch_source' => $batch_source,
            'batch_title' =>  $batch_title,
            'batch_description' => $batch_title,
            'account_year' => $period_year,
            'account_month' => $period_month,
            'batch_type' => 'JV',
            'created_date' => Carbon::now(),
            'created_by' => Auth::user()->user_name,
            'batch_status' => '002',
            'doc_type' => 'JV',
            'process_code' => $doc_type->serial_no,
            'process_code_2' => $doc_type2->serial_no,
            'batch_date' => Carbon::now()
        ]);
       
        $upd_glsrc = Glsource::where('source_code', $batch_source)->update([
            'batch_serial' => $getsources->batch_serial + 1,
        ]);
        
        ##create entry and entry details for the gain/loss
        ##generate entry no
        $entry_id_class = new GenerateEntryId($batch_no, 0);

        $next_entry_id = $entry_id_class->get_next_entry($batch_no);

        $currency_code = Currency::where('base_currency', 'Y')->get()[0]['currency_code'];
        $currency_rate = 1;

        $gain_loss = Appyinvoices::where('payment_header_no', $transaction->payment_header_no)->sum('gain_loss');
      
        ##create entry
        $entry = Batch_entry::create([
            'batch_no' => $batch_no,
            'entry_id' =>  $next_entry_id,
            'entry_description' => 'Gain/loss on USD invoice payment',
            'batch_source' => $batch_source,
            'batch_type' => 'JV',
            'offcd' => $transaction->office_code,
            'currency_code' => $currency_code,
            'currency_rate' => $currency_rate,
            'foreign_dr_amount' => abs($gain_loss),
            'foreign_cr_amount' => abs($gain_loss),
            'local_dr_amount' => abs($gain_loss),
            'local_cr_amount' => abs($gain_loss),
            // 'foreign_out_of_balance' => 0,
            // 'local_out_of_balance' => 0,
            'entry_header' => $transaction->payment_header_no,
            'vendor_no' => $transaction->vendor_id,
            'status' => "002",
            'batch_date' => Carbon::now(),
            'transaction_date' => $transaction->effective_date,
            'created_date' => Carbon::now(),
            'created_by' => Auth::user()->user_name,
            'changed_by' => Auth::user()->user_name,
            'changed_date' => Carbon::now(),
            'payee_name' => $transaction->payee,
            'entry_type_descr' => 'JV',
        ]);
       
        ##get item no 
        $count_entry = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();
        $entry_id_clss = new GenerateEntryId($batch_no, $next_entry_id);
        $next_item_no = $entry_id_clss->get_next_item_no($batch_no, $next_entry_id);
        $count_details = Appyinvoices::where('payment_header_no', $entry->entry_header)->distinct()->select('control_account', 'slhead')->first();
        
        ##create entry_details
        if ($gain_loss < 0) {
            $gain_loss = abs($gain_loss);
            /**
             * Sundry creditors will be debited
             * gain_loss will be credited
            */
            $normal_dr_cr = 'D';
           
            $foreign_dr_amount = $gain_loss;
            $foreign_cr_amount = 0;
            $local_dr_amount = $gain_loss;
            $local_cr_amount = 0;
            $description = 'Debit vendor account with the gain/loss amount';
            $description2 = 'Credit gain/loss account with the gain/loss amount';
            $normal_dr_cr2 = 'C';
            $foreign_dr_amount2 = 0;
            $foreign_cr_amount2 = $gain_loss;
            $local_dr_amount2 = 0;
            $local_cr_amount2 = $gain_loss;
        }

        else{
            $gain_loss = abs($gain_loss);
            /**
             * Sundry creditors will be credited
             * gain_loss will be debited
            */
            $normal_dr_cr = 'C';
            $foreign_dr_amount = 0;
            $foreign_cr_amount = $gain_loss;
            $local_dr_amount = 0;
            $local_cr_amount = $gain_loss;
            $description = 'Credit vendor account with the gain/loss amount';
            $description2 = 'Debit gain/loss account with the gain/loss amount';
            $normal_dr_cr2 = 'C';
            $foreign_dr_amount2 = $gain_loss;
            $foreign_cr_amount2 = 0;
            $local_dr_amount2 = $gain_loss;
            $local_cr_amount2 = 0;
        }

        $glhead = Nlctrl::where('nlctrl', 0)->get()[0]['gain_loss_account'];
       
        $new_detail = Batch_details::create([
            'batch_no' => $batch_no,
            'entry_id' => $entry->entry_id,
            'item_no' => $next_item_no,
            'batch_source' => $batch_source,
            'batch_type' => 'JV',
            'offcd' => $entry->offcd,
            'item_description' => $description,
            'entry_type_descr' => 'JV',
            'glhead' => $count_details->control_account,
            'slhead'=>$count_details->slhead,
            'subledger'=>$count_details->slhead ? 'Y' : 'N',
            'entry_header' => $entry->entry_header,
            'payee_name' => $transaction->payee,
            'cheque_no'=>$transaction->payment_reference,
            'n_balance' => $normal_dr_cr ,
            'foreign_cr_amount' => $foreign_cr_amount,
            'foreign_dr_amount' => $foreign_dr_amount,
            'local_dr_amount' => $local_dr_amount,
            'local_cr_amount' => $local_cr_amount,
            'currency_code' => $currency_code,
            'currency_rate' => $currency_rate,
            'transaction_date' => $entry->transaction_date,
            'created_date' => Carbon::now(),
            'created_by' => Auth::user()->user_name,
            'changed_by' => Auth::user()->user_name,
            'changed_date' => Carbon::now(),
            'vendor_no' => $transaction->vendor_id,
        ]);
       
        ##create an entrydtl for gain/loss account
        $new_detail2 = Batch_details::create([
            'batch_no' => $batch_no,
            'entry_id' => $entry->entry_id,
            'item_no' => $entry_id_clss->get_next_item_no($batch_no, $next_entry_id),
            'batch_source' => $batch_source,
            'batch_type' => 'JV',
            'offcd' => $entry->offcd,
            'item_description' => $description2,
            'entry_type_descr' => 'JV',
            'glhead' => $glhead,
            'entry_header' => $entry->entry_header,
            'payee_name' => $transaction->payee,
            'cheque_no'=>$transaction->payment_reference,
            'n_balance' => $normal_dr_cr2,
            'foreign_cr_amount' => $foreign_cr_amount2,
            'foreign_dr_amount' => $foreign_dr_amount2,
            'local_dr_amount' => $local_dr_amount2,
            'local_cr_amount' => $local_cr_amount2,
            'currency_code' => $currency_code,
            'currency_rate' => $currency_rate,
            'transaction_date' => $entry->transaction_date,
            'created_date' => Carbon::now(),
            'created_by' => Auth::user()->user_name,
            'changed_by' => Auth::user()->user_name,
            'changed_date' => Carbon::now(),
            'vendor_no' => $transaction->vendor_id
        ]);
        
        $get_batch_entry_sum = Batch_details::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as local_cr_amount,sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, entry_id")->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->get()[0];
        
        $local_out_of_bal = round(abs($get_batch_entry_sum->local_dr_amount - $get_batch_entry_sum->local_cr_amount), 2);
        $foreign_out_of_bal = round(abs($get_batch_entry_sum->f_dr_amount - $get_batch_entry_sum->f_cr_amount), 2);
        
        Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->update([
            'foreign_out_of_balance' => $foreign_out_of_bal,
            'local_out_of_balance' => $local_out_of_bal,
            'changed_by' => Auth::user()->user_name,
            'changed_date' => Carbon::now()
        ]);

        return $batch_no;
    }

    public function process_batch()
    {
        $getsources = Glsource::where('source_code', $this->batch_src)->first();

        $new_batch = new GenerateBatch($this->period_year, str_pad($this->period_month, 2, 0, STR_PAD_LEFT), $this->batch_src);

        $batch_no = $new_batch->generate();
        
        if ($this->batch_src == 'AP') {
            if ($this->doc_type == 'INV') {
                $invoice = ApInvoiceHeader::where('invoice_no', $this->reference)->where('vendor_id', $this->vendor_id)->get()[0];
                
                $vendor_name = Apvendors::where('vendor_id', $invoice->vendor_id)->get()[0]['vendor_name'];
                
                $batch_title = 'BATCH ' . $invoice->invoice_no . ' - ' . $invoice->invoice_description;
                
                $batch_description = $batch_title . ' - ' . $vendor_name;
            }

            if ($this->doc_type == 'PAY') {
                $transaction = Appaymentheader::where('payment_header_no', $this->reference)->where('vendor_id', $this->vendor_id)->get()[0];
                $vendor_name = Apvendors::where('vendor_id', $transaction->vendor_id)->get()[0]['vendor_name'];
                $batch_title = strtoupper('PAYMENT BATCH FOR PAYMENT VOUCHER ' . $this->reference);
                $batch_description = strtoupper($batch_title . ' - ' . $transaction->narration);
                $entry_description = strtoupper($transaction->narration);
                $transaction_date =  $transaction->effective_date;

            }
           
            $batch = Batch_list::create([
                'batch_no' => $batch_no,
                'batch_source' => $this->batch_src,
                'batch_title' =>  $batch_title,
                'batch_description' => $batch_description,
                'account_year' => $this->period_year,
                'account_month' => $this->period_month,
                'batch_type' => $this->doc_type,
                'created_date' => Carbon::now(),
                'created_by' => Auth::user()->user_name,
                'no_entries' => 0,
                // 'local_batch_amount' => 0,
                //'expected_batch_total' => $request->exp_amount,
                'batch_status' => '002',
                'doc_type' => $this->doc_type,
                'process_code' => 'ap-batch-approvals',
                'process_code_2' => 'ap-batch-reversal-approvals'
            ]);

            $upd_glsrc = Glsource::where('source_code', $this->batch_src)->update([
                'batch_serial' => $getsources->batch_serial + 1,
            ]);

            ##create entry and entry details for the invoice
            $count = Batch_entry::where('batch_no', $batch_no)->count();
            
            ##generate entry no
            $entry_id_class = new GenerateEntryId($batch_no, 0);

            $next_entry_id = $entry_id_class->get_next_entry($batch_no);

            ##create entry
            $entry = Batch_entry::create([
                'batch_no' => $batch_no,
                'entry_id' =>  $next_entry_id,
                'entry_description' => $invoice->invoice_description,
                'batch_source' => $this->batch_src,
                'batch_type' => $this->doc_type,
                'offcd' => $invoice->office_code,
                'currency_code' => $invoice->currency_code,
                'currency_rate' => $invoice->currency_rate,
                'foreign_dr_amount' => 0,
                'foreign_cr_amount' => 0,
                'local_dr_amount' => 0,
                'local_cr_amount' => 0,
                'foreign_out_of_balance' => 0,
                'local_out_of_balance' => 0,
                'entry_header' => $this->reference,
                'vendor_no' => $this->reference,
                'status' => "002",
                'batch_date' => $batch->created_date,
                'transaction_date' => $invoice->invoice_date,
                'created_date' => Carbon::now(),
                'created_by' => Auth::user()->user_name,
                'changed_by' => Auth::user()->user_name,
                'changed_date' => Carbon::now(),
                'payee_name' => $vendor_name,
                'entry_type_descr' => $this->doc_type,
            ]);

            ##get item no 
            $count_entry = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();
            
            $entry_id_clss = new GenerateEntryId($batch_no, $next_entry_id);
            
            $next_item_no = $entry_id_clss->get_next_item_no($request->batch_no, $next_entry_id);
            
            switch ($this->doc_type) {
                case 'INV':
                    $first_detail = Batch_details::create([
                        'batch_no' => $batch_no,
                        'entry_id' => $entry->entry_id,
                        'item_no' => $next_item_no,
                        'batch_source' => $entry->batch_source,
                        'batch_type' => $entry->batch_type,
                        'offcd' => $entry->offcd,
                        'item_description' => $invoice->invoice_description,
                        'entry_type_descr' => $entry->batch_type,
                        'glhead' => $invoice->control_account,
                        'slhead'=>$invoice->slhead,
                        'subledger'=>$invoice->slhead_flag,
                        'entry_header' => $entry->entry_header,
                        'payee_name' => $vendor_name,
                        'n_balance' => 'C',
                        'foreign_cr_amount' => $invoice->foreign_amount_payable,
                        'foreign_dr_amount' => 0,
                        'local_dr_amount' => 0,
                        'local_cr_amount' => $invoice->amount_payable,
                        'currency_code' => $entry->currency_code,
                        'currency_rate' => $entry->currency_rate,
                        'transaction_date' => $entry->transaction_date,
                        'created_date' => Carbon::now(),
                        'created_by' => Auth::user()->user_name,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    $src = 'GL';

                    $transtype = 'INV';
                
                    // get credit
                    $invoice_details = ApInvoiceDetails::where('invoice_no', $entry->entry_header)
                        ->where('vendor_id', $invoice->vendor_id)->get();
                    
                    foreach ($invoice_details as $key => $value) {
                        // insert retained amount / deductions
                        if ($value->retained_amount != 0) {
                            $count_item = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();
                            
                            $btc_dtl = Batch_details::create([
                                'batch_no' => $batch_no,
                                'entry_id' => $entry->entry_id,
                                'item_no' => $entry_id_clss->get_next_item_no($entry->batch_no, $next_entry_id),
                                'batch_source' => $entry->batch_source,
                                'batch_type' => $entry->batch_type,
                                'offcd' => $entry->offcd,
                                'item_description' => 'ITEM TOTAL RETAINED AMOUNT',
                                'entry_type_descr' => $entry->batch_type,
                                'glhead' => $value->item_control_account,
                                'slhead'=>$value->slhead,
                                'subledger'=>$value->slhead_flag,
                                'payee_name' => $vendor_name,
                                'entry_header' => $entry->entry_header,
                                'n_balance' => 'D',
                                'foreign_cr_amount' => 0,
                                'foreign_dr_amount' => $value->retained_amount,
                                'local_dr_amount' => $value->retained_amount * $invoice->currency_rate,
                                'local_cr_amount' => 0,
                                'currency_code' => $entry->currency_code,
                                'currency_rate' => $entry->currency_rate,
                                'transaction_date' => $entry->transaction_date,
                                'created_date' => Carbon::now(),
                                'created_by' => Auth::user()->user_name,
                                'changed_by' => Auth::user()->user_name,
                                'changed_date' => Carbon::now(),
                            ]);
                        }

                        // insert nett amount /
                        $count_item_nett_amount = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();
                        
                        $btc_dtl2 = Batch_details::create([
                            'batch_no' => $batch_no,
                            'entry_id' => $entry->entry_id,
                            'item_no' => $entry_id_clss->get_next_item_no($batch_no, $next_entry_id),
                            'batch_source' => $entry->batch_source,
                            'batch_type' => $entry->batch_type,
                            'offcd' => $entry->offcd,
                            'item_description' => $value->item_description,
                            'entry_type_descr' => $entry->batch_type,
                            'glhead' => $value->item_control_account,
                            'slhead'=>$value->slhead,
                            'subledger'=>$value->slhead_flag,
                            'payee_name' => $vendor_name,
                            'entry_header' => $entry->entry_header,
                            'n_balance' => 'D',
                            'foreign_cr_amount' => 0,
                            'foreign_dr_amount' => $value->net_of_retained_amount,
                            'local_dr_amount' => $value->net_of_retained_amount * $invoice->currency_rate,
                            'local_cr_amount' => 0,
                            'currency_code' => $entry->currency_code,
                            'currency_rate' => $entry->currency_rate,
                            'transaction_date' => $entry->transaction_date,
                            'created_date' => Carbon::now(),
                            'created_by' => Auth::user()->user_name,
                            'changed_by' => Auth::user()->user_name,
                            'changed_date' => Carbon::now(),
                        ]);

                        // insert taxes
                        if ($value->taxable == 'Y') {
                            $get_taxes = ApTax::where('invoice_no', $entry->entry_header)->where('vendor_id', $invoice->vendor_id)->where('item_no', $value->item_no)->get();
                            
                            foreach ($get_taxes as $key => $tax) {
                                $taxes = Gltaxes::where('tax_code', $tax->tax_code)->get()[0];
                                $tax_types = Gltaxtypes::where('tax_type', $taxes->tax_type)->get()[0];

                                if ($tax_types->analyse == 'Y') {
                                    $taxtypeana = Gltaxtypesana::whereRaw("trim(tax_type) = '" . $tax_types->tax_type . "' and
                                                    entry_type_description='" . $transtype . "' and tax_code='" . $tax->tax_code . "' ")->get()[0];
                                
                                    $glh = $taxtypeana->glhead;
                                } 
                                
                                else {
                                    $glh = $tax_types->glhead;
                                }

                                if ($tax->add_deduct == 'A') {
                                    $normal_dr_cr = 'D';
                                    $tax_foreign_cr_amount = 0;
                                    $tax_foreign_dr_amount = $tax->tax_amount;
                                } else {
                                    $normal_dr_cr = 'C';
                                    $tax_foreign_cr_amount = $tax->tax_amount * -1;
                                    $tax_foreign_dr_amount = 0;
                                }

                                $count_tax_items = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();
                                
                                $btc_dtl3 = Batch_details::create([
                                    'batch_no' => $batch_no,
                                    'entry_id' => $entry->entry_id,
                                    'item_no' =>$entry_id_clss->get_next_item_no($batch_no, $next_entry_id),
                                    'batch_source' => $entry->batch_source,
                                    'batch_type' => $entry->batch_type,
                                    'offcd' => $entry->offcd,
                                    'item_description' => $taxes->tax_description,
                                    'entry_type_descr' => $entry->batch_type,
                                    'glhead' => $glh,
                                    'entry_header' => $entry->entry_header,
                                    'payee_name' => $vendor_name,
                                    'n_balance' => $normal_dr_cr,
                                    'foreign_cr_amount' => $tax_foreign_cr_amount,
                                    'foreign_dr_amount' => $tax_foreign_dr_amount,
                                    'local_dr_amount' => $tax_foreign_dr_amount * $entry->currency_rate,
                                    'local_cr_amount' => $tax_foreign_cr_amount * $entry->currency_rate,
                                    'currency_code' => $entry->currency_code,
                                    'currency_rate' => $entry->currency_rate,
                                    'transaction_date' => $entry->transaction_date,
                                    'created_date' => Carbon::now(),
                                    'created_by' => Auth::user()->user_name,
                                    'changed_by' => Auth::user()->user_name,
                                    'changed_date' => Carbon::now(),
                                ]);
                            }
                        }
                    
                    }

                    $get_batch_entry_sum = Batch_details::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as local_cr_amount,sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, entry_id")->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->get()[0];
                    
                    Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->update([
                        'foreign_cr_amount' => $get_batch_entry_sum->f_cr_amount,
                        'foreign_dr_amount' => $get_batch_entry_sum->f_dr_amount,
                        'local_cr_amount' => $get_batch_entry_sum->local_cr_amount,
                        'local_dr_amount' => $get_batch_entry_sum->local_dr_amount,
                        'foreign_out_of_balance' => abs($get_batch_entry_sum->f_dr_amount - $get_batch_entry_sum->f_cr_amount),
                        'local_out_of_balance' => abs($get_batch_entry_sum->local_dr_amount - $get_batch_entry_sum->local_cr_amount),
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                    ]);

                    $total_local_batch_amount = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->whereRaw("deleted != 'Y' or deleted is null")->sum('local_dr_amount');
                    $total_foreign_batch_amount = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->whereRaw("deleted != 'Y' or deleted is null")->sum('foreign_dr_amount');
                    $entries_count = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->count();

                    $batch_upd = Batch_list::whereRaw("trim(batch_no) = '" . $entry->batch_no . "'")->update([
                        'no_entries' => $entries_count,
                        'local_batch_amount' => $total_local_batch_amount,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                        'expected_batch_total' => $total_local_batch_amount,
                        'foreign_batch_amount' => $total_foreign_batch_amount
                    ]);

                    $update_apheader = ApInvoiceHeader::where('invoice_no', $entry->entry_header)->where('vendor_id', $invoice->vendor_id)->update([
                        'batch_no' => $batch->batch_no,
                        'invoice_status' => '006'
                    ]);
                    
                    ##post batch
                    $schem = schemaName();

                    $gb = $schem['gb'];
                    
                    $gl = $schem['gl'];

                    $common = $schem['common'];

                    $procedureName = '' . $gl . '.post_gl_batch';

                    $user = Auth::user()->user_name;

                    $bindings = [
                        'w_batch_no' => $batch_no,
                        'w_user' => $user,
                    ];

                    $resp = DB::executeProcedure($procedureName, $bindings);
                break;
                case 'PAY':
                     $schem = schemaName();

                     $gb = $schem['gb'];
                     
                     $gl = $schem['gl'];
 
                    $get_gl_bnk_acc = Glbank::where('bank_acc_code', $transaction->bank_acc_code)->get()[0];
                    $procedureName = '' . $gl . '.post_gl_batch';

                   
                    $new_detail = Batch_details::create([
                        'batch_no' => $batch_no,
                        'entry_id' => $entry->entry_id,
                        'item_no' => $next_item_no,
                        'batch_source' => $this->batch_src,
                        'batch_type' => $this->doc_type,
                        'offcd' => $entry->offcd,
                        'item_description' => $transaction->narration,
                        'entry_type_descr' => $this->doc_type,
                        'bank_acc_code' => $transaction->bank_acc_code,
                        'glhead' => $get_gl_bnk_acc->prsno,
                        'slhead'=>$transaction->slhead,
                        'subledger'=>$transaction->slhead_flag,
                        'entry_header' => $entry->entry_header,
                        'payee_name' => $vendor_name,
                        'cheque_no'=>$transaction->payment_reference,
                        'n_balance' => 'C',
                        'foreign_cr_amount' => $transaction->foreign_nett_amount,
                        'foreign_dr_amount' => 0,
                        'local_dr_amount' => 0,
                        'local_cr_amount' => $transaction->local_nett_amount,
                        'currency_code' => $transaction->currency_code,
                        'currency_rate' => $transaction->currency_rate,
                        'transaction_date' => $entry->transaction_date,
                        'created_date' => Carbon::now(),
                        'created_by' => Auth::user()->user_name,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now(),
                        'vendor_no' => $transaction->vendor_id,
                    ]);

                  
                    $payment_invoice = Appyinvoices::where('payment_header_no', $entry->entry_header)->get();
                   
                    foreach ($payment_invoice as $key => $value) {
                        $invoice_count = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();
                        $get_invoice_description = APInvoiceHeader::where('vendor_id', $value->vendor_id)->where("invoice_no", $value->invoice_no)->get()[0];
                        $invoice_description = $get_invoice_description->invoice_description;

                        $payment_debit_entry_details = Batch_details::create([
                            'batch_no' => $batch_no,
                            'entry_id' => $entry->entry_id,
                            'item_no' => $entry_id_clss->get_next_item_no($batch_no, $next_entry_id),
                            'batch_source' => $entry->batch_source,
                            'batch_type' => $entry->batch_type,
                            'offcd' => $entry->offcd,
                            'item_description' => $invoice_description,
                            'entry_type_descr' => $entry->batch_type,
                            'glhead' => $value->control_account,
                            'slhead'=>$value->slhead,
                            'subledger'=>$value->slhead_flag,
                            'entry_header' => $entry->entry_header,
                            'payee_name' => $vendor_name,
                            'n_balance' => 'D',
                            'foreign_cr_amount' => 0,
                            'foreign_dr_amount' => $value->foreign_allocated_amount,
                            'local_dr_amount' => $value->local_allocated_amount,
                            'local_cr_amount' => 0,
                            'currency_code' => $transaction->currency_code,
                            'currency_rate' => $transaction->currency_rate,
                            'transaction_date' => $entry->transaction_date,
                            'created_date' => Carbon::now(),
                            'created_by' => Auth::user()->user_name,
                            'changed_by' => Auth::user()->user_name,
                            'changed_date' => Carbon::now(),
                            'invoice_no' => $value->invoice_no,
                            'vendor_no' => $transaction->vendor_id,
                        ]);

                        $count_payment_taxes = Appytaxes::where('payment_header_no', $transaction->payment_header_no)->where('invoice_no', $value->invoice_no)->count();

                        if ($count_payment_taxes > 0) {
                            $get_payment_taxes = Appytaxes::where('payment_header_no', $transaction->payment_header_no)->where('invoice_no', $value->invoice_no)->get();
                            
                            foreach ($get_payment_taxes as $key => $tax) {
                                $taxes = Gltaxes::where('tax_code', $tax->tax_code)->get()[0];
                                $tax_types = Gltaxtypes::where('tax_type', $taxes->tax_type)->get()[0];
                                $cert_no = '';

                                $src = 'GL';

                                $transtype = 'PAY';

                                if ($tax_types->analyse == 'Y') {
                                    $taxtypeana = Gltaxtypesana::whereRaw("trim(tax_type) = '" . $tax_types->tax_type . "' and
                                                    entry_type_description='" . $transtype . "' and tax_code='" . $tax->tax_code . "' ")->get()[0];
                                    $glh = $taxtypeana->glhead;
                                } 

                                else {
                                    $glh = $tax_types->glhead;
                                }

                                if($tax_types->tax_type == 'WHT')
                                {
                                    $cert_no = $value->wht_cert_no;
                                }

                                if ($tax->add_deduct == 'A') {
                                    $normal_dr_cr = 'D';
                                    $tax_foreign_dr_amount = $tax->foreign_applied_amount;
                                    $tax_foreign_cr_amount = 0;
                                } 
                                
                                else {
                                    $normal_dr_cr = 'C';
                                    $tax_foreign_cr_amount = $tax->foreign_applied_amount * -1;
                                    $tax_foreign_dr_amount = 0;
                                }

                                $count_payment_items = Batch_details::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->count();

                                $test = Batch_details::create([
                                    'batch_no' => $batch_no,
                                    'entry_id' => $entry->entry_id,
                                    'item_no' => $entry_id_clss->get_next_item_no($batch_no, $next_entry_id),
                                    'batch_source' => $entry->batch_source,
                                    'batch_type' => $entry->batch_type,
                                    'offcd' => $entry->offcd,
                                    'item_description' => $taxes->tax_description,
                                    'entry_type_descr' => $entry->batch_type,
                                    'glhead' => $glh,
                                    'entry_header' => $entry->entry_header,
                                    'payee_name' => $vendor_name,
                                    'n_balance' => $normal_dr_cr,
                                    'foreign_cr_amount' => $tax_foreign_cr_amount,
                                    'foreign_dr_amount' => $tax_foreign_dr_amount,
                                    'local_dr_amount' => $tax_foreign_dr_amount * $transaction->currency_rate,
                                    'local_cr_amount' => $tax_foreign_cr_amount * $transaction->currency_rate,
                                    'currency_code' => $transaction->currency_code,
                                    'currency_rate' => $transaction->currency_rate,
                                    'transaction_date' => $entry->transaction_date,
                                    'created_date' => Carbon::now(),
                                    'created_by' => Auth::user()->user_name,
                                    'changed_by' => Auth::user()->user_name,
                                    'changed_date' => Carbon::now(),
                                    'wht_cert_no' => $cert_no,
                                    'invoice_no' => $value->invoice_no,
                                    'vendor_no' => $transaction->vendor_id,
                                ]);

                            }
                        }

                    }

                    $get_batch_entry_sum = Batch_details::selectRaw("sum(local_dr_amount) as local_dr_amount,sum(local_cr_amount) as local_cr_amount,sum(foreign_dr_amount) as f_dr_amount,sum(foreign_cr_amount) as f_cr_amount, batch_no, entry_id")->groupBy('batch_no', 'entry_id')->whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->get()[0];
                   
                    $local_out_of_bal = round(abs($get_batch_entry_sum->local_dr_amount - $get_batch_entry_sum->local_cr_amount), 2);
                    $foreign_out_of_bal = round(abs($get_batch_entry_sum->f_dr_amount - $get_batch_entry_sum->f_cr_amount), 2);
                    
                    if ( abs($local_out_of_bal)  > 0 || abs($foreign_out_of_bal) > 0) {
                        return [
                            'msg' => 'Out balance',
                            'code' =>404
                        ];
                    }
                    
                    $batch_ent = Batch_entry::whereRaw("trim(batch_no) = '" . $entry->batch_no . "' and entry_id='" . $entry->entry_id . "'")->update([
                        'foreign_cr_amount' => $get_batch_entry_sum->f_cr_amount,
                        'foreign_dr_amount' => $get_batch_entry_sum->f_dr_amount,
                        'local_cr_amount' => $get_batch_entry_sum->local_cr_amount,
                        'local_dr_amount' => $get_batch_entry_sum->local_dr_amount,
                        'foreign_out_of_balance' => $foreign_out_of_bal,
                        'local_out_of_balance' => $local_out_of_bal,
                        'changed_by' => Auth::user()->user_name,
                        'changed_date' => Carbon::now()
                    ]);

                    $this->upd_batch($batch_no, $this->batch_src);

                    $upd_pay = Appaymentheader::where('payment_header_no', $this->reference)->update([
                        'batch_no' => $batch_no
                    ]);

                    ##post batch
                    $bindings = [
                        'w_batch_no' => $batch_no,
                        'w_user' => Auth::user()->user_name,
                    ];

                    $resp = DB::executeProcedure($procedureName, $bindings);


                    if (abs($transaction->gain_loss) > 0) {
                        ##create an entry for gain_loss
                        $gain_loss_batch = $this->gain_loss_transaction($transaction, $this->batch_src, $this->period_year, $this->period_month);

                        $this->upd_batch($gain_loss_batch, $this->batch_src);

                        $bindings = [
                            'w_batch_no' => $gain_loss_batch,
                            'w_user' => Auth::user()->user_name,
                        ];

                        $resp = DB::executeProcedure($procedureName, $bindings);
                    }
                break;
                default:
                    # code...
                    break;
            }
        }
        
    }
}
