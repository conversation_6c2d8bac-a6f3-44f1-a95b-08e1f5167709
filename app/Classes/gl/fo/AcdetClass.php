<?php

namespace App\Classes\gl\fo;

use Carbon\Carbon;
use Auth;
use App\Reinacdetallo;
use App\Cbmast;
use App\Acdet;
use App\Acdetallo;
use App\Cbtrans;
use App\Polana;
use App\Polmaster;
use App\Commdet;
use App\Payreqst;
use App\Acdetallonew;
use App\Rein_acdetallonew;
use App\Pipcnam;

use App\Http\Controllers\gl\allocations\Allocation;
use DB;
use Session;
use Symfony\Component\HttpFoundation\Response as ResponseCode;

class AcdetClass
{

    public function createAcdet($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();
        //dd($cbmast);

        if ($cbmast->source_code == 'FAC') {
            $source = 'CRD';
        } else {
            $source = $cbmast->source_code;
        }

        if ($cbmast->analyse_policy != 'Y' || $cbmast->analyse_policy == null) {
            if ($cbmast->doc_type == 'REC') {
                $doc = 'DRN';
            } else {
                $doc = 'CRN';
            }
            //dd($cbmast->claim_no);

            $drn = Acdet::where('endt_renewal_no', $cbmast->claim_no)
                ->where('doc_type', $doc)
                ->where('source', $source)->first();
            //dd($drn);

            if ($entry == 'RRF' || $cbmast->entry_type_descr1 == 'RRF') {
                $doc = 'REC';
                $source = 'CB';

                if($cbmast->on_account=='A'){
                    $drn = Acdet::where('doc_type', $doc)
                        ->where('reference', 'LIKE', '%' . $cbmast->credit_note_no. '%')
                        ->whereRaw("trim(source) = '" . $source . "' ")
                        ->whereNull('endt_renewal_no')
                        ->first();
                    
                }else{
                    $drn = Acdet::where('endt_renewal_no', $cbmast->claim_no)
                        ->where('doc_type', $doc)
                        ->whereRaw("trim(source) = '" . $source . "' ")->first();
                }
            }
            //dd($drn);
            $rec_amt = $this->receiptLocalAmount($cbmast, $drn);
            $cbmast_amount = $rec_amt['amount'];
            $cbmast_amountf = $rec_amt['foreign_amount'];

            if ($entry == $doc_type) {
                $reference = $cbmast->cancelled_reference;
                $acdet3 = Acdet::where('reference', $reference)
                    ->whereRaw("trim(source) = 'CB'")
                    ->where('doc_type', $doc_type)
                    ->where('entry_type_descr', $cbmast->entry_type_descr1)
                    ->first();
                //dd($acdet3);
            }

            $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;
            $g_total = 0;
            $g_foreign_total = 0;
            $g_alocateddx = 0;
            $g_allocatedd = 0;
            $g_allocatedf = 0;


            $acdet_data = [
                'ln_no' => $cbmast->ln_no,
                'line_no' => $cbmast->line_no,
                'branch' => $cbmast->branch,
                'agent' => $cbmast->agent_no,
                'class' => $cbmast->class,
                'account_year' => $cbmast->account_year,
                'account_month' => $cbmast->account_month,
                'doc_type' => $cbmast->doc_type,
                'reference' => $reference,
                'date_effective' => $cbmast->effective_date,
                'date_processed' => $cbmast->effective_date,
                'client_number' => $cbmast->client_number,
                'policyholder' => trim($cbmast->name),
                'currency_code' => $cbmast->currency_code,
                'currency_rate' => $cbmast->currency_rate,
                'entry_type_descr' => $entry,
                'policy_no' => $cbmast->policy_no,
                'endt_renewal_no' => $cbmast->claim_no,
                'cheque_no' => trim($cbmast->cheque_no),
                'ref_doc' => $cbmast->ref_doc,
                'ref_doc_type' => $cbmast->ref_doc_type,
                'orig_entry_type_descr' => $cbmast->orig_entry_type_descr,
                'unit_leader_id' => $drn->unit_leader_id,
                'region_id' => $drn->region_id,
                'unit_id' => $drn->unit_id,
                'channel' => $drn->channel,
                'override_rate' => $drn->override_rate,
                'calculate_on' => $drn->calculate_on,
                'expense_on' => $drn->expense_on,
                'narration' => $cbmast->narration,
                /*'override_payable'=>$drn->override_payable*-1,
                'gross_comm_amt'=>$drn->gross_comm_amt*-1*/
            ];
            $cbtrans = Cbtrans::where('doc_type', $cbmast->doc_type)
                ->where('descr', $cbmast->entry_type_descr)
                ->first();

            if (trim($cbmast->slhead) == '01') {
                if ($cbmast->entry_type_descr == "REC") {
                    $acdet_data['nett'] = $cbmast->gross_amount;
                    $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount;
                } else {
                    $acdet_data['nett'] = $cbmast->gross_amount * -1;
                    $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount * -1;
                }
            }
            if (trim($cbmast->slhead) == '02') {
                if ($cbmast->pay_method == 4 && $entry != $doc_type) {
                    $acdet_data['nett'] = $cbmast->gross_amount * -1;
                    $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount * -1;
                    if ($cbtrans->commission_flag == "Y") {
                        $acdet_data['nett'] = $cbmast->gross_amount * -1;
                        $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount * -1;
                    }
                } else if ($cbmast->pay_method == 4 && $entry == $doc_type) {
                    $acdet_data['nett'] = $cbmast->gross_amount;
                    $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount;
                    if ($cbtrans->commission_flag == "Y") {
                        $acdet_data['nett'] = $cbmast->gross_amount;
                        $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount;
                    }
                }
                if ($cbmast->pay_method != 4 && $entry != $doc_type) {
                    $acdet_data['nett'] = $cbmast->gross_amount;
                    $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount;
                    if ($cbtrans->commission_flag == "Y") {
                        $acdet_data['nett'] = $cbmast->gross_amount;
                        $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount;
                    }
                } else if ($cbmast->pay_method != 4 && $entry == $doc_type) {
                    $acdet_data['nett'] = $cbmast->gross_amount * -1;
                    $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount * -1;
                    if ($cbtrans->commission_flag == "Y") {
                        $acdet_data['nett'] = $cbmast->gross_amount * -1;
                        $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount * -1;
                    }
                }
            }

            if ($doc_type == $entry) {
                $g_allocatedd = $acdet_data['nett']* -1;
                $g_allocatedf = $acdet_data['foreign_net'] * -1;
                $g_total = $acdet_data['nett'];
                $g_foreign_total = $acdet_data['foreign_net'];
                $g_alocateddx = $acdet_data['allocated'];
            } else {
                $g_allocatedd = $drn->unallocated;
                $g_allocatedf = $drn->foreign_unallocated;
            }
            if ($doc_type == "REC") {
                if ($entry == "REC") {
                    $acdet_data['allocated'] = 0;
                    $acdet_data['foreign_allocated'] = 0;
                    $acdet_data['comm_earned'] = $acdet3->comm_earned * -1;

                    $allocation_amt = $acdet_data['nett'];
                } else {
                    if ($g_allocatedd > $cbmast_amount) {
                        $acdet_data['allocated'] = 0;
                        $acdet_data['foreign_allocated'] = 0;
                        $allocation_amt = $cbmast_amount*-1;
                    } else {
                        $acdet_data['allocated'] = 0;
                        $acdet_data['foreign_allocated'] = 0;
                        $allocation_amt = $g_allocatedd*-1;
                    }
                    //commission earned
                    $comm_remaining = $drn->comm_amt - $drn->comm_earned;
                    $w_comm_earned = ($cbmast->gross_amount / $drn->nett) * $drn->comm_amt;
                    if ($comm_remaining < $w_comm_earned && $comm_remaining > 0) {
                        $acdet_data['comm_earned'] = $comm_remaining * -1;
                    } else if ($comm_remaining == 0) {
                        $acdet_data['comm_earned'] = 0;
                    } else {
                        $acdet_data['comm_earned'] = $w_comm_earned * -1;
                    }

                    //override earned
                    $override_remaining = $drn->override_payable - $drn->override_earned;
                    if ($drn->calculate_on == 'C') {
                        $w_override_earned = ($drn->override_rate / 100) * $acdet_data['comm_earned'];
                    } else {
                        $w_override_earned = ($drn->override_rate / 100) * $acdet_data['nett'];
                    }

                    if ($override_remaining < $override_earned && $override_remaining > 0) {
                        $acdet_data['override_earned'] = $override_remaining * -1;
                    } else if ($override_remaining == 0) {
                        $acdet_data['override_earned'] = 0;
                    } else {
                        $acdet_data['override_earned'] = $w_override_earned;
                    }
                }

                // unallocated to be computed after inserting into acdetallonew
                $acdet_data['unallocated'] = 0;
                $acdet_data['foreign_unallocated'] = 0;
            } else if ($doc_type == "PAY") {

                if ($entry == 'PAY') {
                    $acdet_data['allocated'] =0;
                    $acdet_data['foreign_allocated'] = 0;
                    $allocation_amt = $cbmast_amount*-1;
                    $acdet_data['comm_earned'] = $acdet3->comm_earned * -1;
                } else {
                    $acdet_data['allocated'] = $cbmast->gross_amount;
                    $acdet_data['foreign_allocated'] = $cbmast->foreign_gross_amount;
                    $allocation_amt = $cbmast_amount;
                    $acdet_data['comm_earned'] = ($allocation_amt / $drn->nett * $drn->comm_amt);
                }


                $acdet_data['unallocated'] = 0;
                $acdet_data['foreign_unallocated'] = 0;
                $acdet_data['override_earned'] = 0;
            }

            $acdet_data['uw_year'] = $year;
            $acdet_data['agency_no'] = $cbmast->agent_no;
            $acdet_data['orig_entry_type_descr'] = $cbmast->orig_entry_type_descr;
            $acdet_data['unallocatedi'] = $acdet_data['unallocated'];
            $acdet_data['cr_account'] = $cbmast->credit_account;
            $acdet_data['dr_account'] = $cbmast->debit_account;
            $acdet_data['dola'] = $cbmast->effective_date;
            $acdet_data['time'] = $cbmast->created_time;
            $acdet_data['type'] = 20;
            $acdet_data['balance_code'] = 'Z';
            $acdet_data['trans_number'] = $dtrans_no;
            $acdet_data['source'] = 'CB';
            $acdet_data['debit_nos'] = $cbmast->debit_note_no;
            $acdet_data['user_1'] = $cbmast->created_by;
            if ($acdet_data['nett'] > 0) {
                $acdet_data['dr_cr'] = 'D';
            } else {
                $acdet_data['dr_cr'] = 'C';
            }

            //save acdet
            //dd($acdet_data);
            $acdet = Acdet::insert($acdet_data);
            //dd($acdet_data);

            //allocate transactions
            if (trim($cbmast->source_code) == "U/W" || $cbmast->source_code == 'FAC') {

                if ($cbmast->doc_type == "REC") {
                    $r_reference = $cbmast->debit_note_no;
                } else {
                    $r_reference = $cbmast->credit_note_no;
                }

                if ($cbmast->source_code == 'U/W') {
                    $new_acdetallo = $this->insertAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $allocation_amt,$acdet_data['foreign_allocated'], $g_allocatedd, $g_allocatedf, $acdet_data['endt_renewal_no'], $cbmast->source_code);
                } else {
                    //reinacdet allo function
                    $this->createReinAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $allocation_amt, $r_reference, $acdet_data['endt_renewal_no']);
                }

                $new_acdetallo = $this->updateAcdet($cbmast,$doc_type,$year,$entry,$acdet_data['endt_renewal_no'],$dtrans_no,$cbmast->source_code,$r_reference, $acdet_data['comm_earned'], $acdet_data['override_earned'], $acdet_data['allocated']);

            }
        } //end of unaanalysed transactions

    } //end of function

    //create acdet for on account receipt
    public function createAcdetAccount($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->get()[0];
        
        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;

        $acdet_data = new Acdet;
        $acdet_data->ln_no = $cbmast->ln_no;
        $acdet_data->line_no = $cbmast->line_no;
        $acdet_data->branch = $cbmast->branch;
        $acdet_data->agent = $cbmast->agent_no;
        $acdet_data->class = $cbmast->class;
        $acdet_data->account_year = $cbmast->account_year;
        $acdet_data->account_month = $cbmast->account_month;
        $acdet_data->doc_type = $cbmast->doc_type;
        $acdet_data->reference = $reference;
        $acdet_data->date_effective = $cbmast->effective_date;
        $acdet_data->date_processed = $cbmast->effective_date;
        $acdet_data->policyholder = trim($cbmast->name);
        $acdet_data->currency_code = $cbmast->currency_code;
        $acdet_data->currency_rate = $cbmast->currency_rate;
        $acdet_data->entry_type_descr = $entry;
        $acdet_data->cheque_no = trim($cbmast->cheque_no);
        $acdet_data->ref_doc = $cbmast->ref_doc;
        $acdet_data->ref_doc_type = $cbmast->ref_doc_type;
        $acdet_data->orig_entry_type_descr = $cbmast->orig_entry_type_descr;
        $acdet_data->time = $cbmast->created_time;

        if ($cbmast->doc_type == $cbmast->entry_type_descr) {
            $acdet_data->nett = $cbmast->gross_amount;
            $acdet_data->foreign_net = $cbmast->foreign_gross_amount;
            $acdet_data->unallocated = $cbmast->gross_amount;
            $acdet_data->foreign_unallocated = $cbmast->foreign_gross_amount;
            $acdet_data->unallocatedi = $cbmast->gross_amount;
        } else {
            $acdet_data->nett = $cbmast->gross_amount * -1;
            $acdet_data->foreign_net = $cbmast->foreign_gross_amount * -1;
            $acdet_data->unallocated = $cbmast->gross_amount * -1;
            $acdet_data->foreign_unallocated = $cbmast->foreign_gross_amount * -1;
            $acdet_data->unallocatedi = $cbmast->gross_amount * -1;
        }
        $acdet_data->allocated = 0;
        $acdet_data->foreign_allocated = 0;
        $acdet_data->comm_earned = 0;
        $acdet_data->override_earned = 0;
        $acdet_data->uw_year = $year;
        $acdet_data->agency_no = $cbmast->agent_no;
        $acdet_data->cr_account = $cbmast->credit_account;
        $acdet_data->dr_account = $cbmast->debit_account;
        $acdet_data->dola = $cbmast->effective_date;
        $acdet_data->type = 20;
        $acdet_data->balance_code = 'Z';
        $acdet_data->trans_number = $dtrans_no;
        $acdet_data->source = 'CB';
        $acdet_data->user_1 = $cbmast->created_by;
        if ($acdet_data->nett > 0) {
            $acdet_data->dr_cr = 'D';
        } else {
            $acdet_data->dr_cr = 'C';
        }

        $acdet_data->client_number = $cbmast->client_number;
       
        $acdet_create = $acdet_data->save();
        
        
        if ($acdet_create) {
            if ($cbmast->doc_type == $cbmast->entry_type_descr) {
                $acdetallo = $this->createAcdetalloAccount($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $cbmast->source_code, $reference);
            }
        }
    }

    //reverse polana transactions function
    public function reversePolana($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();

        $r_reference = $cbmast->cancelled_reference;
        $polana_rec = Polana::where('doc_type', $doc_type)
            ->where('offcd', $offcd)
            ->where('entry_type_descr', $cbmast->entry_type_descr1)
            ->where('reference', $r_reference)
            ->get();
        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;

        foreach ($polana_rec as $polana) {
            $item_no = Polana::where('doc_type', $doc_type)
                ->where('offcd', $offcd)
                ->where('entry_type_descr', $entry)
                ->where('reference', $reference)
                ->count();
            $item_no = $item_no + 1;
            if ($polana->dr_cr == "D") {
                $w_dr_cr = "C";
            } else {
                $w_dr_cr = "D";
            }

            $create_polana = Polana::create([
                'offcd' => $polana->offcd,
                'doc_type' => $polana->doc_type,
                'entry_type_descr' => $cbmast->entry_type_descr,
                'entry_type_descr1' => $cbmast->entry_type_descr1,
                'reference' => $reference,
                'ln_no' => $cbmast->ln_no,
                'line_no' => $cbmast->line_no,
                'policy_no' => $polana->policy_no,
                'endt_renewal_no' => $polana->endt_renewal_no,
                'item_no' => $item_no,
                'dr_cr' => $w_dr_cr,
                'total_amount' => $cbmast->gross_amount,
                'policy_amount' => $polana->policy_amount * -1,
                'unallocated_amount' => $polana->unallocated_amount * -1,
                'allocated_amount' => 0,
                'branch' => $polana->branch,
                'agent' => $polana->agent,
                'debitnote_no' => $polana->debitnote_no,
                'ref_dtrans_no' => $polana->ref_dtrans_no,
                'ref_doc_type' => $polana->ref_doc_type,
                'ref_account_year' => $year,
                'client_number' => $polana->client_number
            ]);
        }

        //deallocate receipt
        if ($create_polana) {
            // $polanaAcdet = $this->polanaAcdet($offcd, $doc_type, $dtrans_no, $year, $month, $entry);
            //check if original receipt exists in acdet
            $acdet_rec = Acdet::where("doc_type", 'REC')
                                ->where("reference", $polana->reference)
                                ->where('entry_type_descr', $polana->entry_type_descr)
                                ->where('endt_renewal_no', $polana->endt_renewal_no)
                                ->count();
            if($acdet_rec > 0){
                $polanaAcdet = $this->polanaAcdet($offcd, $doc_type, $dtrans_no, $year, $month, $entry);
            }

        }
    }

    //analysed policy function
    public function polanaAcdet($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;
        // generate same allocation number for all polana records
        $allocation_no = Acdetallonew::next_serial();

        $polana_rec = Polana::where('doc_type', $doc_type)
            ->where('offcd', $offcd)
            ->where('entry_type_descr', $entry)
            ->where('reference', $reference)
            /*->where('line_no',$ln_no)
                        ->where('item_no',$item_no)*/
            ->get();

        //dd($polana_rec);

        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();


        foreach ($polana_rec as $polana) {
            $ln_no = Acdet::where('reference', $reference)
                ->where('doc_type', $doc_type)
                ->count();
            $ln_no = $ln_no + 1;
            $polmaster = Polmaster::where('policy_no', $polana->policy_no)
                ->first();
            if ($cbmast->source_code == 'FAC') {
                $source_code = 'CRD';
            } 
            else if($cbmast->source_code == 'COR'){
                $source_code = 'U/W';
            }
            else {
                $source_code = $cbmast->source_code;
            }
            $acdet = Acdet::where('doc_type', 'DRN')
                ->where('endt_renewal_no', $polana->endt_renewal_no)
                ->where('source', $source_code)
                ->first();
            if ($entry == "REC") {
                $old_reference = $cbmast->cancelled_reference;
                $acdet3 = Acdet::where('reference', $old_reference)
                    ->where('doc_type', $doc_type)
                    ->where('endt_renewal_no', $polana->endt_renewal_no)
                    ->first();
            }
            $g_allocatedd = $acdet->unallocated;
            $g_allocatedf = $acdet->foreign_unallocated;
            $g_total = $acdet->nett;
            $g_foreign_total = $acdet->foreign_net;
            $g_alocateddx = $acdet->allocated;

            $acdet_data = [
                'ln_no' => $ln_no,
                'line_no' => $ln_no,
                'branch' => $polana->branch,
                'agent' => $polana->agent,
                'class' => $acdet->class,
                'account_year' => $cbmast->account_year,
                'account_month' => $cbmast->account_month,
                'doc_type' => $cbmast->doc_type,
                'reference' => $reference,
                'date_effective' => $cbmast->effective_date,
                'date_processed' => $cbmast->effective_date,
                'client_number' => $acdet->client_number,
                'policyholder' => trim($polmaster->name),
                'currency_code' => $cbmast->currency_code,
                'currency_rate' => $cbmast->currency_rate,
                'entry_type_descr' => $polana->entry_type_descr,
                'entry_type_descr1' => $polana->entry_type_descr1,
                'policy_no' => $polana->policy_no,
                'endt_renewal_no' => $polana->endt_renewal_no,
                'cheque_no' => trim($cbmast->cheque_no),
                'ref_doc' => $cbmast->ref_doc,
                'ref_doc_type' => $cbmast->ref_doc_type,
                'orig_entry_type_descr' => $cbmast->orig_entry_type_descr,
                'nett' => $polana->policy_amount,
                'foreign_net' => $polana->policy_amount / $cbmast->currency_rate,
                'unit_leader_id' => $acdet->unit_leader_id,
                'region_id' => $acdet->region_id,
                'unit_id' => $acdet->unit_id,
                'channel' => $acdet->channel,
                'override_rate' => $acdet->override_rate,
                'calculate_on' => $acdet->calculate_on,
                'expense_on' => $acdet->expense_on,
                /*'override_payable'=>$acdet->override_payable,
                'gross_comm_amt'=>$acdet->gross_comm_amt*/
            ];
            $cbtrans = Cbtrans::where('doc_type', $doc_type)
                ->where('descr', $entry)
                ->first();
            if ($doc_type == "REC") {
                if ($doc_type == $entry) { //receipt reversal
                    $acdet_data['nett'] = $acdet3->nett * -1;
                    $acdet_data['foreign_net'] = $acdet3->foreign_net * -1;
                    $acdet_data['allocated'] = $acdet3->allocated * -1;
                    $acdet_data['foreign_allocated'] = $acdet3->foreign_allocated * -1;
                    $acdet_data['comm_earned'] = $acdet3->comm_earned * -1;
                    $acdet_data['override_earned'] = $acdet3->override_earned * -1;
                } //end of receipt reversals
                else { //new receipt
                    if ($g_allocatedd > abs($polana->policy_amount) || $g_allocatedd == abs($polana->policy_amount)) {
                        $acdet_data['allocated'] = $acdet_data['nett'];
                        $acdet_data['foreign_allocated'] = $acdet_data['foreign_net'];
                    } else {
                        $acdet_data['allocated'] = $g_allocatedd * -1;
                        $acdet_data['foreign_allocated'] = $g_allocatedf * -1;
                    }
                    //commission earned
                    $comm_remaining = $acdet->comm_amt - $acdet->comm_earned;
                    $w_comm_earned = ($polana->policy_amount / $acdet->nett) * $acdet->comm_amt * -1;
                    if ($comm_remaining < $w_comm_earned && $comm_remaining > 0) {
                        $acdet_data['comm_earned'] = $comm_remaining * -1;
                    } else if ($comm_remaining == 0) {
                        $acdet_data['comm_earned'] = 0;
                    } else {
                        $acdet_data['comm_earned'] = $w_comm_earned * -1;
                    }

                    //override earned
                    $override_remaining = $drn->override_payable - $drn->override_earned;
                    if ($acdet3->calculate_on == 'C') {
                        $w_override_earned = ($acdet->override_rate / 100) * $acdet_data['comm_earned'];
                    } else {
                        $w_override_earned = ($acdet->override_rate / 100) * $acdet_data['nett'];
                    }

                    if ($override_remaining < $override_earned && $override_remaining > 0) {
                        $acdet_data['override_earned'] = $override_remaining * -1;
                    } else if ($override_remaining == 0) {
                        $acdet_data['override_earned'] = 0;
                    } else {
                        $acdet_data['override_earned'] = $w_override_earned;
                    }
                } //end of new receipt
                $acdet_data['unallocated'] = 0;
                $acdet_data['foreign_unallocated'] = 0;
            } //end of receipts
            else if ($doc_type == "PAY") { //payments
                $acdet_data['allocated'] = $polana->policy_amount;
                $acdet_data['foreign_allocated'] = $polana->policy_amount / $cbmast->currency_rate;
                $acdet_data['unallocated'] = 0;
                $acdet_data['foreign_unallocated'] = 0;
            } //end of payments

            //other fields
            $acdet_data['uw_year'] = $cbmast->account_year;
            $acdet_data['agency_no'] = $cbmast->agent_no;
            $acdet_data['orig_entry_type_descr'] = $cbmast->orig_entry_type_descr;
            $acdet_data['unallocatedi'] = $acdet_data['unallocated'];
            $acdet_data['cr_account'] = $cbmast->credit_account;
            $acdet_data['dr_account'] = $cbmast->debit_account;
            $acdet_data['dola'] = $cbmast->effective_date;
            $acdet_data['time'] = $cbmast->created_time;
            $acdet_data['type'] = 20;
            $acdet_data['balance_code'] = 'Z';
            $acdet_data['trans_number'] = $dtrans_no;
            $acdet_data['source'] = 'CB';
            $acdet_data['debit_nos'] = $cbmast->debit_note_no;
            $acdet_data['user_1'] = $cbmast->created_by;
            $acdet_data['dr_cr'] = $polana->dr_cr;

            $acdet_create = Acdet::insert($acdet_data);

            if (trim($cbmast->source_code) == "U/W" || trim($cbmast->source_code) == "FAC" || $cbmast->source_code == "COR") {
                $r_reference;
                if ($cbmast->doc_type == "REC") {
                    $r_reference = $cbmast->debit_note_no;
                } else {
                    $r_reference = $cbmast->credit_note_no;
                }

                $new_acdetallo = $this->insertAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $acdet_data['allocated'],$acdet_data['foreign_allocated'], $g_allocatedd, $g_allocatedf, $acdet_data['endt_renewal_no'], $cbmast->source_code,$allocation_no);
				$updated_acdet = $this->updateAcdet($cbmast,$doc_type,$year,$entry,$acdet_data['endt_renewal_no'],$dtrans_no,$source_code,$r_reference, $acdet_data['comm_earned'], $acdet_data['override_earned'], $acdet_data['allocated']);

            }
        } //end foreach polana
    }

    //receipt allocation function
    public function allocateRec($endt_no, $reference, $allocated_amount, $foreign_amount, $s_comm_earned, $amount, $s_override_earned, $source, $r_reference,$cbmast)
    {

        $entry=$cbmast->entry_type_descr;
        $doc_type=$cbmast->doc_type;



        if ($source == 'FAC') {
            $source = 'CRD';
        }
        else if($source == 'COR'){
            $source = 'U/W';
        }

        if ($doc_type == "REC") {
            $ref_doc = 'DRN';
            $acdet = Acdet::where('doc_type', 'DRN')
                ->where('source', $source)
                ->where('endt_renewal_no', $endt_no)->first();
        } else if ($doc_type == "PAY") {
            $ref_doc = 'CRN';

           
                $acdet = Acdet::where('doc_type', 'CRN')
                // ->whereRaw("reference like '%.$reference.%'")
                ->where('reference', 'LIKE', '%' . $r_reference . '%')
                ->where('source', $source)
                ->where('endt_renewal_no', $endt_no)->first();
            


           
        }

        if ($entry == 'RRF' || $cbmast->entry_type_descr1=='RRF') { //receipt refund
            $ref_doc = 'REC';
            $source = 'CB';

            
            if($cbmast->on_account=='A'){
                $acdet = Acdet::where('doc_type', 'REC')
                ->where('reference', 'LIKE', '%' . $r_reference . '%')
                ->whereNull('endt_renewal_no')
                ->whereRaw("trim(source) = '" . $source . "' ")
                ->first();
                
            }else{

            $acdet = Acdet::where('doc_type', 'REC')
                ->where('reference', 'LIKE', '%' . $r_reference . '%')
                ->whereRaw("trim(source) = '" . $source . "' ")
                ->where('endt_renewal_no', $endt_no)->first();
            }
        }

        //dd($doc_type);

        if ($doc_type == 'REC') {
            $amount = $allocated_amount * -1;
            $foreign_amount = $foreign_amount * -1;
            $allocated = 0;
            $foreign_allocated = 0;
            $unallocated = 0;
            $foreign_unallocated = 0;
            $comm_earned = 0;
            $override_earned = 0;
        }

        if (($acdet->unallocated > 0 && $entry != "REC") || $entry == "REC") {
            $allocated = $acdet->allocated + $amount;
            $foreign_allocated = $acdet->foreign_allocated + $foreign_amount;
            $unallocated = $acdet->unallocated - $amount;
            $foreign_unallocated = $acdet->foreign_unallocated - $foreign_amount;
            $comm_earned = $acdet->comm_earned - $s_comm_earned;
            $override_earned = $acdet->override_earned - $s_override_earned;
        }

        if ($doc_type == "PAY") {
            if (($acdet->unallocated < 0 && $entry != 'PAY') || $entry == 'PAY') {
                $allocated = $acdet->allocated - $allocated_amount;
                $foreign_allocated = $acdet->foreign_allocated - $foreign_amount;
                $unallocated = $acdet->unallocated + $allocated_amount;
                $foreign_unallocated = $acdet->foreign_unallocated + $foreign_amount;
                $comm_earned = $acdet->comm_earned - $s_comm_earned;
                $override_earned = 0;
            } else {
                $allocated = $acdet->allocated;
                $foreign_allocated = $acdet->foreign_allocated;
                $unallocated = $acdet->unallocated;
                $foreign_unallocated = $acdet->foreign_unallocated;
                $comm_earned = $acdet->comm_earned;
                $override_earned = 0;
            }

            //dd($acdet->unallocated); 
        }

      
        //dd($allocated.' '.$unallocated.' '.$comm_earned);


        // $acdet1 = Acdet::whereRaw("endt_renewal_no = '" . $endt_no . "' and 
        //     doc_type = '" . $ref_doc . "' and trim(source) = '" . $source . "' ")
        //     ->where('reference', 'LIKE', '%' . $r_reference . '%')
            $acdet1=$acdet->update([
                'allocated' => $allocated,
                'foreign_allocated' => $foreign_allocated,
                'unallocated' => $unallocated,
                'foreign_unallocated' => $foreign_unallocated,
                'comm_earned' => $comm_earned,
                'receipt_number' => $reference,
                'override_earned' => $override_earned
            ]);

        //update commissions table
        if ($acdet1 && $source == 'U/W') {
            $commdet = Commdet::where('reference', $acdet->reference)
                ->where('doc_type', $ref_doc)
                ->update([
                    'allocated_amount' => $allocated,
                    'unallocated_amount' => $unallocated,
                    'comm_earned' => $comm_earned,
                    'foreign_comm_earned' => $comm_earned/$acdet->currency_rate,
                    'req' => 'N'
                ]);

            if ($doc_type == 'REC' && $entry == 'REC') {
                //for claw back commission generation
                $commdet1 = Commdet::where('reference', $acdet->reference)
                    ->where('doc_type', $ref_doc)
                    ->update([
                        'requisition_no' => '',
                        'req' => 'N'
                    ]);
            }
        }
        //dd($acdet1);

        return $acdet1;
    }



    function createAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $allocated_amount, $g_allocatedd, $g_allocatedf, $endt_no, $source)
    {
        //create acdetallo record
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();

        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;

        if ($cbmast->analyse_policy == "Y") {
            $polana = Polana::where('doc_type', $doc_type)
                ->where('offcd', $offcd)
                ->where('entry_type_descr', $entry)
                ->where('reference', $reference)
                ->where('endt_renewal_no', $endt_no)
                ->first();
            //$endt_no = $polana->endt_renewal_no;
            $amount = $polana->policy_amount;
            $foreign_amount = $polana->policy_amount / $cbmast->currency_rate;
            $client_number = $polana->client_number;
        } else {
            //$endt_no = $cbmast->claim_no;
            if ($doc_type == "REC") {
                $amount = $cbmast->gross_amount * -1;
                $foreign_amount = $cbmast->foreign_gross_amount * -1;
            } else {
                $amount = $cbmast->gross_amount;
                $foreign_amount = $cbmast->foreign_gross_amount;
            }
            $client_number = $cbmast->client_number;
        }

        if ($source == 'FAC') {
            $source = 'CRD';
        }
        else if($source == 'COR'){
            $source = 'U/W';
        }

        if ($doc_type == "REC") {
            $acdet = Acdet::where('endt_renewal_no', $endt_no)
                ->where('doc_type', "DRN")
                ->where('source', $source)->first();

            $dr_cr = 'C';

        } else if ($doc_type == "PAY") {
            $acdet = Acdet::where('doc_type', 'CRN')
                ->where('endt_renewal_no', $endt_no)
                ->where('source', $source)->first();

            $dr_cr = 'D';

            if ($entry == 'RRF' || $cbmast->entry_type_descr1 == 'RRF') {
                $source = 'CB';

                if($cbmast->on_account=='A'){
                    $acdet = Acdet::where('doc_type', 'REC')
                    ->where('reference', 'LIKE', '%' . $cbmast->credit_note_no . '%')
                    ->whereNull('endt_renewal_no')
                    ->whereRaw("trim(source) = '" . $source . "' ")->first();
                }else{
                    $acdet = Acdet::where('doc_type', 'REC')
                    ->where('reference', 'LIKE', '%' . $cbmast->credit_note_no . '%')
                    ->where('endt_renewal_no', $endt_no)
                    ->whereRaw("trim(source) = '" . $source . "' ")->first();
                }
              
            }
        }


        $records = Acdetallo::where('reference', $reference)
            ->count();
        if ($doc_type != $entry) {
            $acdetallo = Acdetallo::create([
                'branch' => $cbmast->branch,
                'agent' => $cbmast->agent_no,
                'client_number' => $acdet->client_number,
                'doc_type' => $cbmast->doc_type,
                'reference' => $reference,
                'ln_no' => $cbmast->ln_no,
                'item_no' => $records,
                'endt_renewal_no' => $acdet->endt_renewal_no,
                'policy_no' => $acdet->policy_no,
                'ref_doc_type' => $acdet->doc_type,
                'ref_reference' => $acdet->reference,
                'ref_ln_no' => $acdet->ln_no,
                'ref_endt_renewal_no' => $acdet->endt_renewal_no,
                'ref_policy_no' => $acdet->policy_no,
                'ref_client_number' => $acdet->client_number,
                'dr_cr' => $dr_cr,
                'amount' => $allocated_amount,
                'balance_before' => $amount,
                'balance_after' => ($amount) - $allocated_amount,
                'ref_balance_before' => $g_allocatedd,
                'ref_balance_after' => $acdet->unallocated,
                'account_year' => $cbmast->account_year,
                'account_month' => $cbmast->account_month,
                'dola' => $cbmast->effective_date,
                'amount_orig' => $amount,
                'foreign_amount' => $allocated_amount/$cbmast->currency_rate,
                'foreign_amount_orig' => $foreign_amount,
                'foreign_balance_before' => $foreign_amount,
                'foreign_balance_after' => ($foreign_amount) - $allocated_amount,
                'foreign_ref_balance_before' => $g_allocatedf,
                'foreign_ref_balance_after' => $acdet->foreign_unallocated,
                'currency_code' => $cbmast->currency_code,
                'currency_rate' => $cbmast->currency_rate,
                'allocation_date' => Carbon::now(),
                'user_str' => trim($cbmast->created_by),
            ]);
        } else {
            $old_reference = $cbmast->cancelled_reference;
            $acdetallo_rec = Acdetallo::where('reference', $old_reference)
                ->where('endt_renewal_no', $endt_no)
                ->get();

            foreach ($acdetallo_rec as $revs_allo) {
                $records = Acdetallo::where('reference', $reference)
                    ->count();

                if($revs_allo->endt_renewal_no == $revs_allo->ref_endt_renewal_no){
                    if($cbmast->analyse_policy == 'Y'){
                        $amount = $revs_allo->amount;
                        $foreign_amount = $revs_allo->foreign_amount;
                    }
                    else{
                        $amount = $cbmast->gross_amount * -1;
                        $foreign_amount = $cbmast->foreign_gross_amount * -1;
                    }

                    $acdetallo = Acdetallo::create([
                        'branch' => $revs_allo->branch,
                        'agent' => $revs_allo->agent,
                        'client_number' => $revs_allo->client_number,
                        'doc_type' => $revs_allo->doc_type,
                        'reference' => $reference,
                        'ln_no' => $cbmast->ln_no,
                        'item_no' => $records,
                        'endt_renewal_no' => $revs_allo->endt_renewal_no,
                        'policy_no' => $revs_allo->policy_no,
                        'ref_policy_no' => $revs_allo->ref_policy_no,
                        'ref_doc_type' => $doc_type,
                        'ref_reference' => $old_reference,
                        'ref_ln_no' => $revs_allo->ln_no,
                        'ref_endt_renewal_no' => $revs_allo->endt_renewal_no,
                        'dr_cr' => $dr_cr,
                        'amount' => $amount,
                        'balance_before' => $amount,
                        'balance_after' => 0,
                        'ref_balance_before' => $amount * -1,
                        'ref_balance_after' => 0,
                        'account_year' => $cbmast->account_year,
                        'account_month' => $cbmast->account_month,
                        'dola' => $cbmast->effective_date,
                        'amount_orig' => $amount,
                        'foreign_amount' => $foreign_amount,
                        'foreign_amount_orig' => $foreign_amount,
                        'foreign_balance_before' => $foreign_amount,
                        'foreign_balance_after' => 0,
                        'foreign_ref_balance_before' => $foreign_amount * -1,
                        'foreign_ref_balance_after' => 0,
                        'currency_code' => $cbmast->currency_code,
                        'currency_rate' => $cbmast->currency_rate,
                        'allocation_date' => Carbon::now(),
                        'user_str' => trim($cbmast->created_by),
                    ]);

                    //update acdet original receipt balances
                $acdet_rec = Acdet::where('doc_type','REC')
                                    ->where('endt_renewal_no',$endt_no)
                                    ->where('reference',$old_reference)
                                    ->where('entry_type_descr',$cbmast->entry_type_descr1)
                                    ->update([
                                        'allocated' => $amount,
                                        'unallocated' => 0,
                                        'foreign_allocated' => $foreign_amount,
                                        'foreign_unallocated' => 0
                                    ]);

                //update acdet reversal balances
                $acdet_rev = Acdet::where('doc_type','REC')
                                    ->where('endt_renewal_no',$endt_no)
                                    ->where('reference',$reference)
                                    ->where('entry_type_descr',$cbmast->entry_type_descr)
                                    ->update([
                                        'allocated' => $amount * -1,
                                        'unallocated' => 0,
                                        'foreign_allocated' => $foreign_amount * -1,
                                        'foreign_unallocated' => 0
                                    ]);
                }
                //deallocate acdetallo
                $acdetallo_upd = Acdetallo::where('reference',$old_reference)
                                ->where('ref_doc_type',$revs_allo->ref_doc_type)
                                ->where('ref_reference',$revs_allo->ref_reference)
                                ->update([
                                    'amount'=>0,
                                    'balance_before'=>$revs_allo->balance_before-$revs_allo->amount,
                                    'balance_after'=>$revs_allo->balance_after+$revs_allo->amount,
                                    'ref_balance_before'=>$revs_allo->ref_balance_before+$revs_allo->amount,
                                    'ref_balance_after'=>$revs_allo->ref_balance_after-$revs_allo->amount,
                                    'foreign_amount' => 0,
                                    'foreign_balance_before' =>$revs_allo->foreign_balance_before-$revs_allo->foreign_amount,
                                    'balance_after'=>$revs_allo->foreign_balance_after+$revs_allo->foreign_amount,
                                    'ref_balance_before'=>$revs_allo->foreign_ref_balance_before+$revs_allo->foreign_amount,
                                    'ref_balance_after'=>$revs_allo->foreign_ref_balance_after-$revs_allo->foreign_amount, 
                                    'deallocated_date'=>Carbon::now()
                                ]);

                if($revs_allo->ref_endt_renewal_no == null){
                    $endt_no = $revs_allo->endt_renewal_no;
                }
                else{
                    $endt_no = $revs_allo->ref_endt_renewal_no;
                }

                //get acdet drn/crn record record
                $acdet = Acdet::where('doc_type',$revs_allo->ref_doc_type)
                                ->where('reference',$revs_allo->ref_reference)
                                ->where('endt_renewal_no',$endt_no)
                                ->where('source','U/W')
                                ->get()[0];
                //update acdet debit notes balance
                $comm_earned = $revs_allo->amount / $acdet->nett * $acdet->comm_amt;

                $acdet_upd = Acdet::where('doc_type',$revs_allo->ref_doc_type)
                                ->where('reference',$revs_allo->ref_reference)
                                ->where('endt_renewal_no',$endt_no)
                                ->where('source','U/W')
                                ->update([
                                    'allocated' => $acdet->allocated + $revs_allo->amount,
                                    'unallocated' => $acdet->unallocated - $revs_allo->amount,
                                    'foreign_allocated' => $acdet->foreign_allocated + ($revs_allo->amount/$acdet->currency_rate),
                                    'foreign_unallocated' => $acdet->foreign_unallocated - ($revs_allo->amount/$acdet->currency_rate),
                                    'comm_earned' => $acdet->comm_earned + $comm_earned
                                ]);

                //update commdet
                $commdet_upd = Commdet::where('doc_type',$revs_allo->ref_doc_type)
                                ->where('reference',$revs_allo->ref_reference)
                                ->where('endt_renewal_no',$endt_no)
                                ->update([
                                    'allocated_amount' => $acdet->allocated + $revs_allo->amount,
                                    'unallocated_amount' => $acdet->unallocated - $revs_allo->amount,
                                    'comm_earned' => $acdet->comm_earned + $comm_earned,
                                    'foreign_comm_earned' => ($acdet->comm_earned + $comm_earned)/$acdet->currency_rate,
                                    'req' => 'N',
                                    'requisition_no' => ''
                                ]);

            }
        }


        return $acdetallo;
    }

    //START OF REINACDETALLOC FUNCTION
    
    function createReinAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $allocated_amount,$r_reference, $endt_no,$allocation_no=null)
    {
        //create acdetallo record
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();

        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;

        if($cbmast->analyse_policy=='Y'){
            $amount=$allocated_amount;
            $foreign_amount=$allocated_amount/$cbmast->currency_rate;
            $allocated_amount=$allocated_amount;
    
        }else{
            $amount = $cbmast->gross_amount;
            $foreign_amount = $cbmast->foreign_gross_amount;
        }

        $source ='CRD';
        
            
        $acdet = Acdet::where('doc_type', 'CRN')
                ->where('endt_renewal_no', $endt_no)
                ->where('reference', 'LIKE', '%' . $r_reference . '%')
                ->where('source', $source)
                // ->where('branch',$cbmast->branch)
                // ->where('agent',$agent)
                ->first();
       
        $agent = $acdet->agent;
        // payment entry
        $pay_acdet = Acdet::where('doc_type', 'PAY')
                ->where('reference', $reference)
                ->where('endt_renewal_no', $endt_no)
                ->where('entry_type_descr', $entry)
                ->whereRaw("trim(source)='CB'")
                ->where('branch', $acdet->branch)
                ->where('agent',$agent)
                ->first();
              
        // reversal entry
        $rev_acdet = Acdet::where('doc_type', 'PAY')
                ->where('reference', 'LIKE', '%' . $dtrans_no . '%')
                ->where('entry_type_descr', $cbmast->entry_type_descr1)
                ->where('endt_renewal_no', $endt_no)
                ->whereRaw("trim(source)='CB'")
                ->where('branch', $acdet->branch)
                ->where('agent', $agent)
                ->first();
    //   dd($rev_acdet,$dtrans_no,$cbmast->entry_type_descr1,$endt_no);
        $allocation_no = $allocation_no ?? Rein_acdetallonew::next_serial();
        $ref_allocation_no = null;
        $ref_item_no = null;
        $acdetallo_recs = array();
        $fac_allo = array();
        $pay_allo = array();
        // INSTANTIATE ALLOCATION CONTROLLER CLASS
        $allo_class = new Allocation;
                  
        //reversal
        if ($doc_type != $entry) {
            // PAYMENT
            $allo_class->insertReinAcdetallo($pay_acdet,$cbmast,$allocated_amount,$allocation_no,$ref_allocation_no,$ref_item_no, $acdet);
            //  credit note
            $crn_amount = $allocated_amount *-1;
            $allo_class->insertReinAcdetallo($acdet,$cbmast,$crn_amount,$allocation_no,$ref_allocation_no,$ref_item_no, $acdet);
    
        }else{

            $old_reference = $cbmast->cancelled_reference;
            $pay_allo = Rein_acdetallonew::where('reference', $old_reference)
                        ->where('endt_renewal_no', $endt_no)
                        ->where('doc_type',$cbmast->doc_type)
                        ->where('branch', $acdet->branch)
                        ->where('agent', $agent)
                        ->first();
            $prev_acdetallo_rec = Rein_acdetallonew::where('allocation_no',$pay_allo->allocation_no)
                                    ->where('endt_renewal_no',$endt_no)
                                    ->get();
                                                         
            $ref_allocation_no = $pay_allo->allocation_no;
            
            foreach ($prev_acdetallo_rec as $revs_allo) {

                $item_no = (int)Rein_acdetallonew::where('allocation_no', $allocation_no)->count() + 1;
                $amount = $revs_allo->amount * -1;
                $amountf = $revs_allo->foreign_amount * -1;
                $dr_cr = $amount < 0 ? 'C' : 'D';
                $ref_item_no = $revs_allo->item_no;
                $amountf = $revs_allo->foreign_amount * -1;
                $allocatedf = $revs_allo->foreign_allocated * -1;

                $acdetallo = Rein_acdetallonew::create([
                    'ALLOCATION_NO' => $allocation_no,
                    'REF_ALLOCATION_NO' => $ref_allocation_no,
                    'REF_ITEM_NO' => $revs_allo->item_no,
                    'POLICY_NO' => $revs_allo->policy_no,
                    'ENDT_RENEWAL_NO' => $revs_allo->endt_renewal_no,
                    'DOC_TYPE' => $revs_allo->doc_type,
                    'REFERENCE' => $revs_allo->reference,
                    'ENTRY_TYPE_DESCR' => $revs_allo->entry_type_descr,
                    'ITEM_NO' => $item_no,
                    'AMOUNT' => $amount,
                    'FOREIGN_AMOUNT' => $amountf,
                    'DR_CR' => $dr_cr,
                    'BRANCH' => $revs_allo->branch,
                    'AGENT' => $revs_allo->agent,
                    'CLIENT_NUMBER' => $revs_allo->client_number,
                    'CURRENCY_CODE' => $revs_allo->currency_code,
                    'CURRENCY_RATE' => $revs_allo->currency_rate,
                    'ACCOUNT_MONTH' => $revs_allo->account_month,
                    'ACCOUNT_YEAR' => $revs_allo->account_year,
                    'USER_STR' => trim(Auth::user()->user_name),
                    'ALLOCATION_DATE' => Carbon::now(),
                    'ALLOCATION_RATE' => $revs_allo->allocation_rate,
			        'EXCHANGE_DIFF' => $revs_allo->exchange_diff,
			        'GAIN_LOSS' => $revs_allo->gain_loss*-1,
                    'FOREIGN_ALLOCATED' => $allocatedf
                ]);
            }
            // if($cbmast->analyse_policy == 'Y'){
            //     return $acdetallo;
            // }
           
            $ref_allocation_no = null;
            $ref_item_no = null;
            // Reversal 
            $allo_class->insertReinAcdetallo($pay_acdet,$cbmast,$allocated_amount,$allocation_no,$ref_allocation_no,$ref_item_no, $acdet);
            // Original payment
            $rev_amount = $allocated_amount *-1;
            $allo_class->insertReinAcdetallo($rev_acdet,$cbmast,$rev_amount,$allocation_no,$ref_allocation_no,$ref_item_no, $acdet);
    
        }

        // INSERT  NEW ENTRIES ( FOR BOTH REVERSAL AND NEW ALLOCATION)
        $pay_allo = [
            'DOC_TYPE' => $cbmast->doc_type,
            'REFERENCE' => $reference,
            'ENTRY_TYPE_DESCR' => $cbmast->entry_type_descr,
            'AMOUNT' => $allocated_amount,
            'FOREIGN_AMOUNT' => $foreign_amount,
        ];

        //  credit note 
        $fac_allo = [
            'DOC_TYPE' => $acdet->doc_type,
            'REFERENCE' => $acdet->reference,
            'ENTRY_TYPE_DESCR' => $acdet->entry_type_descr,
            'AMOUNT' => $allocated_amount * -1,
            'FOREIGN_AMOUNT' => $foreign_amount * -1,
        ];
        $common_allo_data = [
            'ALLOCATION_NO' => $allocation_no,
            'REF_ALLOCATION_NO' => $ref_allocation_no,
            'POLICY_NO' => $acdet->policy_no,
            'ENDT_RENEWAL_NO' => $acdet->endt_renewal_no,
            'BRANCH' => $acdet->branch,
            'AGENT' => $acdet->agent,
            'CURRENCY_CODE' => $cbmast->currency_code,
            'CURRENCY_RATE' => $cbmast->currency_rate,
            'ACCOUNT_MONTH' => $cbmast->account_month,
            'ACCOUNT_YEAR' => $cbmast->account_year,
            'CLIENT_NUMBER' => $acdet->client_number,
            'USER_STR' => trim(Auth::user()->user_name),
            'ALLOCATION_DATE' => Carbon::now(),
        ];

        $fac_allo = array_merge($common_allo_data,$fac_allo);
        $pay_allo = array_merge($common_allo_data,$pay_allo);
        array_push($acdetallo_recs, $fac_allo,$pay_allo);

        foreach($acdetallo_recs as $allo){
            $item_no = (int)Rein_acdetallonew::where('allocation_no', $allocation_no)->count() + 1;
            $allo['ITEM_NO'] = $item_no;
            $allo['ALLOCATION_DATE'] = Carbon::now();
            $allo['DR_CR'] = $allo['AMOUNT'] < 0 ? 'C' : 'D';

            // $acdetallo = Rein_acdetallonew::create($allo);
        }
    
        return $acdetallo;
    }
    //END OF REINACDETALLOC FUNCTION

    function createAcdetalloAccount($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $source, $reference)
    {
        //create acdetallo record
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();

        if ($doc_type == $entry) {
            $old_reference = $cbmast->cancelled_reference;
            $acdet_rec = Acdet::where('reference', $old_reference)
                ->where('doc_type', $cbmast->doc_type)
                ->where('entry_type_descr', $cbmast->entry_type_descr1)
                ->first();
                
            // get debits not yet deallocated
            $acdetallo_recs =  Acdetallonew::allocated_entry($acdet_rec->reference);
            $allocation_no = Acdetallonew::next_serial();

            // INSTANTIATE ALLOCATION CONTROLLER CLASS
            $allo_class = new Allocation;
            $total_comm_earned = 0;
            $pipcnam = Pipcnam::where('record_type',0)->first();
            
            foreach ($acdetallo_recs as $acdet_allo) {
                $prev_allo = $acdet_allo;

                $amount = $prev_allo->amount * -1;
                $amountf = $prev_allo->foreign_amount * -1;
                $ref_allocation_no = $prev_allo->allocation_no;
                $ref_item_no = $prev_allo->item_no;
                  
                $acdet_drn = Acdet::where('doc_type', 'DRN')
                                ->where('source', 'U/W')
                                ->where('endt_renewal_no', $acdet_allo->endt_renewal_no)
                                ->get()[0];

                
                $allocation_date = Carbon::parse($acdet_allo_drn->allocation_date);
                $new_debtors_cutoff = Carbon::parse($pipcnam->new_debtors_cut_off);

                if($allocation_date >= $new_debtors_cutoff){
                    $with_held = Commdet::where('doc_type','WHA')
                        ->where('entry_type_descr','CRN')
                        ->whereRaw("(reversed ='N' OR reversed IS NULL)")
                        ->where('reference',$acdet_rec->reference)
                        ->where('endt_renewal_no',$acdet_drn->endt_renewal_no)
                        ->where('allocation_no',$acdet_allo->allocation_no)
                        ->first();
                }
                else{
                    $with_held = Commdet::where('doc_type','WHA')
                        ->where('entry_type_descr','CRN')
                        ->whereRaw("(reversed ='N' OR reversed IS NULL)")
                        ->where('reference',$acdet_rec->reference)
                        ->where('endt_renewal_no',$acdet_drn->endt_renewal_no)
                        ->first();
                }

                $acdetallo =  Acdetallonew::where('allocation_no', $acdet_allo->allocation_no)
                    ->where('endt_renewal_no', $acdet_allo->endt_renewal_no)
                    ->get();

                foreach ($acdetallo as $allo_rec) {
                    $amount = $allo_rec->amount*-1;
                    $ref_allocation_no = $allo_rec->allocation_no;
                    $ref_item_no = $allo_rec->item_no;

                    $acdet = Acdet::where('doc_type', $allo_rec->doc_type)
                        ->where('reference', $allo_rec->reference)
                        ->where('entry_type_descr',$allo_rec->entry_type_descr)
                        ->first();
                    $allocated = $allo_class->insertAcdetallo($acdet,$cbmast,$amount,$allocation_no,$ref_allocation_no,$ref_item_no,'REC',$acdet_drn);
                }


                $amount = $acdet_allo->amount *-1;
                $s_comm_earned = $amount/$acdet_drn->nett * $acdet_drn->comm_amt;
                $comm_on_withheld = $with_held->policy_amount/$acdet_drn->nett * $acdet_drn->comm_amt;
                $total_comm_earned = $s_comm_earned + $comm_on_withheld;
                $amt_witheld = $with_held->policy_amount;
                $update_acdet = $allo_class->updateOnAccountAcdet($acdet_drn,$total_comm_earned,$amt_witheld);

                // create commission details for withheld

                if(isset($with_held)){
							
                    if($allocation_date >= $new_debtors_cutoff){
                        Commdet::where('doc_type','WHA')
                            ->where('entry_type_descr','CRN')
                            ->whereRaw("(reversed ='N' OR reversed IS NULL)")
                            ->where('reference',$acdet_rec->reference)
                            ->where('endt_renewal_no',$acdet_drn->endt_renewal_no)
                            ->where('allocation_no',$acdet_allo->allocation_no)
                            ->update([
                                'reversed' => 'Y'
                            ]);
                    }
                    else{
                        Commdet::where('doc_type','WHA')
                            ->where('entry_type_descr','CRN')
                            ->whereRaw("(reversed ='N' OR reversed IS NULL)")
                            ->where('reference',$acdet_rec->reference)
                            ->where('endt_renewal_no',$acdet_drn->endt_renewal_no)
                            ->update([
                                'reversed' => 'Y'
                            ]);
                    }

                    $count = Commdet::where('reference',$acdet_rec->reference)
                        ->where('doc_type','WHA')
                        ->count();
                    $item_no = $count+1;

                    Commdet::create([
                        // "requisition_no" => $request->,
                        "allocation_no" => $allocation_no,
                        "item_no" => $item_no,
                        "reference" => $acdet_rec->reference,
                        "entry_type_descr" => 'DRN',
                        "doc_type" => 'WHA',
                        "policy_no" => $acdet_drn->policy_no,
                        "endt_renewal_no" => $acdet_drn->endt_renewal_no,
                        "branch" => $acdet_drn->branch,
                        "agent" => $acdet_drn->agent,
                        "client_number" => $acdet_drn->client_number,
                        "policy_amount" => abs($with_held->policy_amount),
                        "unallocated_amount" => 0,
                        "allocated_amount" => abs($with_held->policy_amount),
                        "witheld_amount" => abs($with_held->policy_amount),
                        "foreign_witheld_amount" => abs($with_held->foreign_witheld_amount),
                        "date_effective" => Carbon::today(),
                        // "comm_rate" => $request->,
                        "comm_amt" => abs($with_held->comm_amt),
                        "tax_rate" => abs($with_held->tax_rate),
                        "tax_amt" => abs($with_held->tax_amt),
                        "foreign_tax_amt" => abs($with_held->foreign_tax_amt),
                        // "comm_payable" => $request->,
                        "comm_earned" => abs($comm_on_withheld),
                        "foreign_comm_earned" => abs($comm_on_withheld)/$acdet_drn->currency_rate,
                        "comm_paid" => $with_held->comm_paid,
                        "foreign_comm_paid" => $with_held->foreign_comm_paid
                    ]);
                }
            } // end foreach

            // allocate receipt to reversal
            $amount = $acdet_rec->nett;
            $rev_amount = $acdet_rec->nett * -1;
            $ref_allocation_no = null;
            $ref_item_no = null;
            // receipt
            $allo_class->insertAcdetallo($acdet_rec,$cbmast,$amount,$allocation_no,$ref_allocation_no,$ref_item_no,'REC',$acdet_rec);
            $claw_back_comm = $total_comm_earned *-1;
            $update_acdet = $allo_class->updateOnAccountAcdet($acdet_rec,$claw_back_comm);

            // reversal
            $rev_acdet = Acdet::where('reference', $reference)
                ->where('doc_type', $cbmast->doc_type)
                ->where('entry_type_descr', $cbmast->entry_type_descr)
                ->get()[0];
            $allo_class->insertAcdetallo($rev_acdet,$cbmast,$rev_amount,$allocation_no,$ref_allocation_no,$ref_item_no);
            $update_acdet = $allo_class->updateOnAccountAcdet($rev_acdet,$total_comm_earned);

            return $update_acdet;

        }


        return $update_acdet;
    }
    public function allocateFac($offcd, $doc_type, $dtrans_no, $year, $month, $entry){
        
        //GET CBMAST
        $cbmast = Cbmast::where('offcd', $offcd)
                        ->where('doc_type', $doc_type)
                        ->where('dtrans_no', $dtrans_no)
                        ->where('account_year', $year)
                        ->where('account_month', $month)
                        ->where('entry_type_descr', $entry)                      
                        ->first(); 
                      
        $reference=str_pad($cbmast->dtrans_no,6,0,STR_PAD_LEFT).$cbmast->account_year;         
           
        $req_no=$cbmast->dept_code.$cbmast->payrqst_no;

       
        $payreqst=Payreqst::where('req_no',$req_no)
                            ->get()[0];


        //GET POLANA RECORDS
        if($doc_type==$entry){
            //REVERSAL
            $polana=Polana::where('doc_type',$doc_type)
                    ->where('entry_type_descr',$cbmast->entry_type_descr1)
                    ->where('req_no',$req_no)
                    ->where('offcd',$offcd)
                    ->get();
            $dr_cr='C';

           
            
        }else{
            //ORIGINAL PAYMENT
            $polana=Polana::where('doc_type',$payreqst->doc_type)
                        ->where('entry_type_descr',$payreqst->entry_type_descr)
                        ->where('req_no',$req_no)
                        ->where('offcd',$offcd)
                        ->get();
            $dr_cr='D';
        }
                        
        //LOOP THROUGH POLANA
        foreach($polana as $pol){
            //ADD TO POLANA WITH CBMAST DETAILS
            $count=Polana::where('doc_type',$cbmast->doc_type)
                        ->where('entry_type_descr',$cbmast->entry_type_descr)
                        ->where('reference',$reference)
                        ->where('offcd',$offcd)
                        ->count();

         
                 
            $create_polana=[
                'offcd'=>$cbmast['offcd'],
                'req_no'=>$req_no,
                'doc_type'=>$cbmast->doc_type,
                'entry_type_descr'=>$cbmast['entry_type_descr'],
                'entry_type_descr1'=>$cbmast['entry_type_descr1'],
                'reference'=>$reference,
                'ln_no'=>$cbmast['ln_no'],
                'line_no'=>$cbmast['line_no'],
                'policy_no'=>$pol['policy_no'],
                'endt_renewal_no'=>$pol['endt_renewal_no'],
                'item_no'=>$count+1,
                'dr_cr'=>$dr_cr,                  
                'unallocated_amount'=>0,                   
                // 'branch'=>$cbmast['branch'],
                // 'agent'=>$cbmast['agent_no'],
                'branch'=>$pol->branch,
                'agent'=>$pol->agent,
                'client_number'=>$pol['client_number'],
                'broker_branch'=>$pol->broker_branch,
                'broker_agent'=>$pol->broker_agent,
            ];

            
            if($doc_type==$entry){
                $create_polana['ref_dtrans_no']=substr($cbmast->cancelled_reference,0,6);
                $create_polana['ref_account_year']= substr($cbmast->cancelled_reference,6,4);
                $create_polana['ref_doc_type']=$cbmast->doc_type;

                $create_polana['total_amount']=$cbmast['foreign_gross_amount']*-1;
                $create_polana['policy_amount']=$pol['policy_amount'] *-1;
                $create_polana['allocated_amount']=$pol['policy_amount'] *-1;

            }
            
            else{
                $create_polana['ref_dtrans_no']=$pol->ref_dtrans_no;
                $create_polana['ref_account_year']=$pol->ref_account_year;
                $create_polana['ref_doc_type']=$pol->ref_doc_type;

                $create_polana['total_amount']=$cbmast['foreign_gross_amount'];
                $create_polana['policy_amount']=$pol['policy_amount'] ;
                $create_polana['allocated_amount']=$pol['policy_amount'] ;
            }

            $insert_polana=Polana::insert($create_polana);
                
        }
    }

    public function addFacToAcdet($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;
        $polana_pay = Polana::where('doc_type', $doc_type)
                    ->where('offcd', $offcd)
                    ->where('entry_type_descr', $entry)
                    ->where('reference', $reference)
                    ->get();

        $cbmast = Cbmast::where('offcd', $offcd)
                ->where('doc_type', $doc_type)
                ->where('dtrans_no', $dtrans_no)
                ->where('account_year', $year)
                ->where('account_month', $month)
                ->where('entry_type_descr', $entry)
                ->first();

        $allocation_no = Rein_acdetallonew::next_serial();

        foreach ($polana_pay as $polana) {
        
            $ln_no = Acdet::where('reference', $reference)
                    ->where('doc_type', $doc_type)
                    ->where('entry_type_descr', $entry)
                    ->count();
            $ln_no = $ln_no + 1;


            $polmaster = Polmaster::where('policy_no', $polana->policy_no)
                        ->first();

            if ($cbmast->entry_type_descr== $cbmast->doc_type) {
                $source_code = 'CB';
            } else {
                $source_code ='CRD';
            }

            $ref_reference=str_pad($polana->ref_dtrans_no,6,'0',STR_PAD_LEFT).$polana->ref_account_year;
            $acdet = Acdet::where('doc_type', $polana->ref_doc_type)
                            ->whereRaw("trim(reference)='$ref_reference'")
                            ->where('endt_renewal_no', $polana->endt_renewal_no)
                            ->whereRaw("trim(source)='".$source_code."'")
                            ->first();

            $acdet_data = [
                'ln_no' => $ln_no,
                'line_no' => $cbmast->line_no,
                'branch' => $polana->branch,
                'agent' => $polana->agent,
                'class' => $acdet->class,
                'account_year' => $cbmast->account_year,
                'account_month' => $cbmast->account_month,
                'doc_type' => $cbmast->doc_type,
                'reference' => $reference,
                'date_effective' => $cbmast->effective_date,
                'date_processed' => $cbmast->effective_date,
                'client_number' => $acdet->client_number,
                'policyholder' => trim($polmaster->name),
                'currency_code' => $cbmast->currency_code,
                'currency_rate' => $cbmast->currency_rate,
                'entry_type_descr' => $polana->entry_type_descr,
                'entry_type_descr1' => $polana->entry_type_descr1,
                'policy_no' => $polana->policy_no,
                'endt_renewal_no' => $polana->endt_renewal_no,
                'cheque_no' => trim($cbmast->cheque_no),
                'ref_doc' => $cbmast->ref_doc,
                'ref_doc_type' => $polana->ref_doc_type,
                'orig_entry_type_descr' => $cbmast->orig_entry_type_descr,
                'nett' => $polana->policy_amount,
                'foreign_net' => $polana->policy_amount / $cbmast->currency_rate,
                'unit_leader_id' => $acdet->unit_leader_id,
                'region_id' => $acdet->region_id,
                'unit_id' => $acdet->unit_id,
                'channel' => $acdet->channel,
                'override_rate' => $acdet->override_rate,
                'calculate_on' => $acdet->calculate_on,
                'expense_on' => $acdet->expense_on,
                /*'override_payable'=>$acdet->override_payable,
                'gross_comm_amt'=>$acdet->gross_comm_amt*/
            ];

            $cbtrans = Cbtrans::where('doc_type', $doc_type)
                ->where('descr', $entry)
                ->first();

            $acdet_data['allocated'] = $polana->policy_amount ;
            $acdet_data['foreign_allocated'] = $polana->policy_amount/$cbmast->currency_rate;
            $acdet_data['unallocated'] = $polana->unallocated_amount;
            $acdet_data['foreign_unallocated'] = $polana->unallocated_amount/$cbmast->currency_rate;
           
            //other fields
            $acdet_data['uw_year'] = $cbmast->account_year;
            $acdet_data['agency_no'] = $cbmast->agent_no;
            $acdet_data['orig_entry_type_descr'] = $cbmast->orig_entry_type_descr;
            $acdet_data['unallocatedi'] = $acdet['unallocated'];
            $acdet_data['cr_account'] = $cbmast->credit_account;
            $acdet_data['dr_account'] = $cbmast->debit_account;
            $acdet_data['dola'] = $cbmast->effective_date;
            $acdet_data['time'] = $cbmast->created_time;
            $acdet_data['type'] = 10;
            $acdet_data['balance_code'] = 'Z';
            $acdet_data['trans_number'] = $dtrans_no;
            $acdet_data['source'] = 'CB';
            $acdet_data['debit_nos'] = $polana->ref_dtrans_no;
            $acdet_data['user_1'] = $cbmast->created_by;
            $acdet_data['dr_cr'] = $polana->dr_cr;
            
            $acdet_create = Acdet::insert($acdet_data);
           
            if ($acdet_create) {

                if($doc_type == $entry){
                    $crn_polana = Polana::where('doc_type', $doc_type)
                        ->where('offcd', $offcd)
                        ->where('entry_type_descr', $cbmast->entry_type_descr1)
                        ->where('reference', $ref_reference)
                        ->where('endt_renewal_no', $polana->endt_renewal_no)
                        ->first();

                    $r_reference = str_pad($crn_polana->ref_dtrans_no,6,'0',STR_PAD_LEFT).$crn_polana->ref_account_year;
                }
                else{
                    $r_reference = $ref_reference;
                }

                //UPDATE ACDET MANUALLY
                $ref_doc = 'CRN';
                $acdet = Acdet::where('doc_type', 'CRN')
                    ->where('reference', 'LIKE', '%' . $r_reference . '%')
                    ->where('source', 'CRD')
                    ->where('endt_renewal_no', $acdet_data['endt_renewal_no'])
                    ->where('branch', $acdet_data['branch'])
                    ->where('agent', $acdet_data['agent'])
                    ->first();

                $reacdetallo = $this->createReinAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $acdet_data['allocated'],$r_reference, $acdet_data['endt_renewal_no'],$allocation_no);
                $new_acdetallo = $this->updateAcdet($cbmast,$doc_type,$year,$entry,$acdet_data['endt_renewal_no'],$dtrans_no,$cbmast->source_code,$r_reference, $acdet_data['comm_earned'], $acdet_data['override_earned'], $acdet_data['allocated']);

            }
        } //end foreach polana
    }

    // NEW ALLOCATION LOGIC
    
    function insertAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $allocated_amount,$allocated_amountf, $g_allocatedd, $g_allocatedf, $endt_no, $source,$allocation_no = null)
    {

        $acdetallo_recs = array();
        $drn_crn_allo = array();

        $allocation_no = $allocation_no ?? Acdetallonew::next_serial();
        // INSTANTIATE ALLOCATION CONTROLLER CLASS
        $allo_class = new Allocation;

        //create acdetallo record
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();

        if ($cbmast->doc_type == "REC") {
            $r_reference = $cbmast->debit_note_no;
        } else {
            $r_reference = $cbmast->credit_note_no;
        }

        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;
        $old_reference = $cbmast->cancelled_reference;

        if ($source == 'FAC') {
            $source = 'CRD';
        }
        else if($source == 'COR'){
            $source = 'U/W';
        }

        if ($doc_type == "REC") {
            $acdet = Acdet::where('endt_renewal_no', $endt_no)
                ->where('doc_type', "DRN")
                ->where('source', $source)
                ->first();

            $ref_allocation_no = null;

            if($doc_type == $entry){
                $ref_doc = 'REC';
                $source = 'CB';
                
                $acdet = Acdet::where('doc_type', $ref_doc)
                    ->where('reference', 'LIKE', '%' . $old_reference . '%')
                    ->where('entry_type_descr', $cbmast->entry_type_descr1)
                    ->whereRaw("trim(source) = '" . $source . "' ")
                    ->where('endt_renewal_no', $endt_no)->first();

            }   
        } else if ($doc_type == "PAY") {
            $acdet_drn = $acdet = Acdet::where('doc_type', 'CRN')
                ->where('endt_renewal_no', $endt_no)
                ->where('source', $source)
                ->first();

            if ($entry == 'RRF' || $cbmast->entry_type_descr1=='RRF') { //receipt refund
                $ref_doc = 'REC';
                $source = 'CB';

                if($cbmast->on_account=='A'){
                    $acdet_drn = $acdet = Acdet::where('doc_type', 'REC')
                                ->where('reference', 'LIKE', '%' . $r_reference . '%')
                                ->whereNull('endt_renewal_no')
                                ->whereRaw("trim(source) = '" . $source . "' ")
                                ->first();                        
                }else{
                    $acdet_drn = $acdet = Acdet::where('doc_type', 'REC')
                                ->where('reference', 'LIKE', '%' . $r_reference . '%')
                                ->whereRaw("trim(source) = '" . $source . "' ")
                                ->where('endt_renewal_no', $endt_no)
                                ->first();
                }
            }

            if($doc_type == $entry){
                $source = 'CB';
                $acdet = Acdet::where('doc_type', $cbmast->doc_type)
                    ->where('reference', 'LIKE', '%' . $old_reference . '%')
                    ->where('entry_type_descr', $cbmast->entry_type_descr1)
                    ->whereRaw("trim(source) = '" . $source . "' ")
                    ->where('endt_renewal_no', $endt_no)->first(); 
                    
            }
        }

        // reverse previous allocations
        if ($doc_type == $entry) {
            // dd($acdetallo_rec);
            $receipt_amt = 0;
            if($doc_type == 'PAY'){
                $prev_acdetallo_rec = Acdetallonew::where('reference', $old_reference)
                    ->where('doc_type', $doc_type)
                    ->where('entry_type_descr', $cbmast->entry_type_descr1)
                    ->first();

                $ref_allocation_no = $prev_acdetallo_rec->allocation_no;

                $acdetallo_rec = Acdetallonew::where('allocation_no', $ref_allocation_no)
                        ->get();

                foreach($acdetallo_rec as $allo_rec){

                    $prev_allo = $allo_rec;
                    $amount = $prev_allo->amount * -1;
                    $ref_allocation_no = $prev_allo->allocation_no;
                    $ref_item_no = $prev_allo->item_no;

                    if($cbmast->on_account == 'A' and empty($allo_rec->endt_renewal_no)){
                        $curr_acdet = Acdet::where('doc_type', $allo_rec->doc_type)
                            ->where('entry_type_descr', $allo_rec->entry_type_descr)
                            ->where('reference', $allo_rec->reference)
                            ->first();

                    }
                    else{
                        $curr_acdet = Acdet::where('doc_type', $allo_rec->doc_type)
                            ->where('entry_type_descr', $allo_rec->entry_type_descr)
                            ->where('reference', $allo_rec->reference)
                            ->where('endt_renewal_no', $allo_rec->endt_renewal_no)
                            ->first();
                    }

                    $allo_class->insertAcdetallo($curr_acdet,$cbmast,$amount,$allocation_no,$ref_allocation_no,$ref_item_no,'PAY',$acdet_drn);
                }
            }
            else{
                $acdetallo_rec =  Acdetallonew::allocated_entry($acdet->reference);
            
                foreach ($acdetallo_rec as $acdet_allo) {

                    $acdet_drn = Acdet::where('doc_type', 'DRN')
                                ->where('source', 'U/W')
                                ->where('endt_renewal_no', $acdet_allo->endt_renewal_no)
                                ->first();

                    $acdetallo =  Acdetallonew::where('allocation_no', $acdet_allo->allocation_no)
                                ->where('endt_renewal_no', $acdet_allo->endt_renewal_no)
                                ->get();

                    foreach($acdetallo as $allo_rec){

                        $prev_allo = $allo_rec;
                        $amount = $prev_allo->amount * -1;
                        $amountf = $prev_allo->foreign_amount * -1;
                        $ref_allocation_no = $prev_allo->allocation_no;
                        $ref_item_no = $prev_allo->item_no;
                        $manual_allocation = $allo_rec->manual_allocation;

                        if($manual_allocation == 'Y' and $allo_rec->doc_type == 'REC'){
                            $rec_endt_no = $allo_rec->rec_endt_renewal_no;
                        }
                        else{
                            $rec_endt_no = $allo_rec->endt_renewal_no;
                        }

                        $curr_acdet = Acdet::where('doc_type', $allo_rec->doc_type)
                            ->where('entry_type_descr', $allo_rec->entry_type_descr)
                            ->where('reference', $allo_rec->reference)
                            ->where('endt_renewal_no', $rec_endt_no)
                            ->first();

                        $allo_class->insertAcdetallo($curr_acdet,$cbmast,$amount,$allocation_no,$ref_allocation_no,$ref_item_no,'REC',$acdet_drn,$manual_allocation);
                    }

                    $amount = $acdet_allo->amount *-1;
                    $s_comm_earned = $amount/$acdet_drn->nett * $acdet_drn->comm_amt;

                    if($cbmast->claim_no != $acdet_drn->endt_renewal_no){
                        $update_acdet = $allo_class->updateAcdet($acdet_drn,$s_comm_earned);
                    }
                }
            }
        }

        // if($doc_type == $entry and $cbmast->analyse_policy == 'Y'){
        //     return $acdetallo;
        // }

        // insert new allocations

        // receipt
        $rec_allo_rec = [
            'ENDT_RENEWAL_NO' => $cbmast->claim_no,
            'DOC_TYPE' => $cbmast->doc_type,
            'REFERENCE' => $reference,
            'ENTRY_TYPE_DESCR' => $cbmast->entry_type_descr,
            'AMOUNT' => $allocated_amount,
            'FOREIGN_AMOUNT' => $allocated_amountf,
        ];

        // debit note || credit note || Original receipt(RRF)
        $drn_crn_allo = [
            'ENDT_RENEWAL_NO' => $acdet->endt_renewal_no,
            'DOC_TYPE' => $acdet->doc_type,
            'REFERENCE' => $acdet->reference,
            'ENTRY_TYPE_DESCR' => $acdet->entry_type_descr,
            'AMOUNT' => $allocated_amount * -1,
            'FOREIGN_AMOUNT' => $allocated_amountf * -1,
        ];

        if($cbmast->analyse_policy == 'Y'){
            $rec_allo_rec['ENDT_RENEWAL_NO'] = $acdet->endt_renewal_no;
            $drn_crn_allo['ENDT_RENEWAL_NO'] = $acdet->endt_renewal_no;
        }

        array_push($acdetallo_recs, $drn_crn_allo,$rec_allo_rec);

        $ref_allocation_no = null;
        $ref_item_no = null;

        // not a receipt reversal
        //dd($acdetallo_recs);
        foreach ($acdetallo_recs as $allo_rec) {
            $amount = $allo_rec['AMOUNT'];
            $curr_acdet = Acdet::where('endt_renewal_no',$allo_rec['ENDT_RENEWAL_NO'])
                ->where('doc_type',$allo_rec['DOC_TYPE'])
                ->where('entry_type_descr',$allo_rec['ENTRY_TYPE_DESCR'])
                ->where('reference',$allo_rec['REFERENCE'])
                ->first();

            $allo_class->insertAcdetallo($curr_acdet,$cbmast,$amount,$allocation_no,$ref_allocation_no,$ref_item_no,'REC',$acdet);
        }
       
        return $acdetallo;
    }

    public function updateAcdet($cbmast,$doc_type,$year,$entry,$endt_no, $dtrans_no,$source,$r_reference,$s_comm_earned,$s_override_earned,$new_allo)
    {
        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;
        $table = 'acdetallonew';

        if ($doc_type == "REC") {
            $ref_doc = 'DRN';
            $table = 'acdetallonew';
        }
        else if ($doc_type == "PAY" ) {
            $ref_doc = 'CRN';
        }

        if ($entry == 'RRF' || $cbmast->entry_type_descr1 == 'RRF') { //receipt refund
            $ref_doc = 'REC';
            $source = 'CB';
        }

        if ($cbmast->source_code == 'FAC') {
            $source = 'CRD';
            $table = 'rein_acdetallonew';
            
        }

        if($cbmast->on_account == 'A'){
            $rec_acdetallo = DB::select("SELECT sum(amount) as amount,sum(nvl(foreign_allocated,0)) as foreign_amount,sum(nvl(gain_loss,0)) as gain_loss 
                        FROM $table
                        where reference ='$reference' AND doc_type='$doc_type' AND entry_type_descr='$cbmast->entry_type_descr'")[0];
            
            $drn_crn_acdet_entry = Acdet::where('doc_type', $ref_doc)
                ->where('reference', 'LIKE', '%' . $cbmast->credit_note_no. '%')
                ->whereRaw("trim(source) = '" . $source . "' ")
                ->whereNull('endt_renewal_no')
                ->first();

            $drn_crn_acdetallo = DB::select("SELECT sum(amount) as amount,sum(nvl(foreign_allocated,0)) as foreign_amount,sum(nvl(gain_loss,0)) as gain_loss 
                                FROM $table 
                                WHERE entry_type_descr='PRM' AND doc_type='$drn_crn_acdet_entry->doc_type' 
                                AND reference='$drn_crn_acdet_entry->reference' ")[0];
        }
        else{
        
            $rec_acdetallo = DB::select("SELECT sum(amount) as amount,sum(nvl(foreign_allocated,0)) as foreign_amount,sum(nvl(gain_loss,0)) as gain_loss 
                        FROM $table
                        where reference ='$reference' AND doc_type='$doc_type' AND entry_type_descr='$cbmast->entry_type_descr'
                        AND '$endt_no' = CASE 
                            WHEN DOC_TYPE = 'REC' AND MANUAL_ALLOCATION = 'Y' THEN 
                                REC_ENDT_RENEWAL_NO 
                            ELSE 
                                ENDT_RENEWAL_NO 
                            END")[0];
        
            $drn_crn_acdetallo = DB::select("SELECT sum(amount) as amount,sum(nvl(foreign_allocated,0)) as foreign_amount,sum(nvl(gain_loss,0)) as gain_loss 
                                FROM $table 
                                WHERE '$endt_no' = CASE 
                                    WHEN DOC_TYPE = 'REC' AND MANUAL_ALLOCATION = 'Y' THEN 
                                        REC_ENDT_RENEWAL_NO 
                                    ELSE 
                                        ENDT_RENEWAL_NO 
                                    END AND doc_type='$ref_doc' 
                                AND reference LIKE  '%$r_reference%'")[0];


            $drn_crn_acdet_entry = Acdet::whereRaw("endt_renewal_no = '" . $endt_no . "' and 
                doc_type = '" . $ref_doc . "' and trim(source) = '" . $source . "' ")
                ->where('reference', 'LIKE', '%' . $r_reference . '%')
                ->first();
        }

        if($cbmast->analyse_policy == 'Y'){
            $rec_acdet_entry = Acdet::whereRaw("doc_type = '" . $doc_type . "' AND entry_type_descr='".$cbmast->entry_type_descr."'")
                    ->where('reference', $reference)
                    ->where('endt_renewal_no', $endt_no)
                    ->first();
        }
        else{
            $rec_acdet_entry = Acdet::whereRaw("doc_type = '" . $doc_type . "' AND entry_type_descr='".$cbmast->entry_type_descr."'")
                    ->where('reference', $reference)
                    ->first();
        }

        $rec_acdet_entry = Acdet::whereRaw("doc_type = '" . $doc_type . "' AND entry_type_descr='".$cbmast->entry_type_descr."'")
            ->where('reference', $reference)
            ->where('endt_renewal_no', $endt_no)
            ->first();

        // validate receipt before update 
        $validated = $this->validateAcdet($rec_acdet_entry,'N');

        // validate acdet before
        $validated = $this->validateAcdet($drn_crn_acdet_entry,'N');

        // debit note(DRN) || CREDIT NOTE || REVERSAL
        // dd($rec_acdetallo,$reference,$doc_type,$cbmast->entry_type_descr);
        $amt_witheld_sum = $drn_crn_acdet_entry->witheld_amount;
        $amt_witheld_sumf = $amt_witheld_sum/$drn_crn_acdet_entry->currency_rate;

        $gain_loss_sum = $drn_crn_acdetallo->gain_loss;
        $allocated_sum = $drn_crn_acdetallo->amount + $amt_witheld_sum + $gain_loss_sum;
        $f_allocated_sum = $drn_crn_acdetallo->foreign_amount + $amt_witheld_sumf;

        $allocated_sum = round($allocated_sum,10);
        $f_allocated_sum = round($f_allocated_sum,10);

        $unallocated_sum = $drn_crn_acdet_entry->nett - $allocated_sum;
        $f_unallocated_sum = $drn_crn_acdet_entry->foreign_net - $f_allocated_sum;

        // receipt
        $rec_gain_loss_sum = $rec_acdetallo->gain_loss;
        $rec_allocated_sum = $rec_acdetallo->amount + $rec_gain_loss_sum;
        $rec_f_allocated_sum = $rec_acdetallo->foreign_amount;

        $rec_allocated_sum = round($rec_allocated_sum,10);
        $rec_f_allocated_sum = round($rec_f_allocated_sum,10);

        $rec_unallocated_sum = $rec_acdet_entry->nett - $rec_allocated_sum;
        $rec_f_unallocated_sum = $rec_acdet_entry->foreign_net - $rec_f_allocated_sum;

        // dd($rec_acdetallo,$rec_unallocated_sum,$rec_f_allocated_sum);
        $comm_earned = $drn_crn_acdet_entry->comm_earned - $s_comm_earned;
        $override_earned = $drn_crn_acdet_entry->override_earned - $s_override_earned;

        // update acdet
        $drn_crn_acdet_entry->gain_loss =  $gain_loss_sum;
        $drn_crn_acdet_entry->allocated = $allocated_sum;
        $drn_crn_acdet_entry->foreign_allocated = $f_allocated_sum;
        $drn_crn_acdet_entry->unallocated = $unallocated_sum;
        $drn_crn_acdet_entry->foreign_unallocated = $f_unallocated_sum;
        $drn_crn_acdet_entry->comm_earned = $comm_earned;
        $drn_crn_acdet_entry->receipt_number = $reference;
        $drn_crn_acdet_entry->override_earned = $override_earned ;
        $drn_crn_acdet_entry->save();
        
        $rec_acdet_entry->gain_loss = $rec_gain_loss_sum;
        $rec_acdet_entry->allocated = $rec_allocated_sum;
        $rec_acdet_entry->foreign_allocated = $rec_f_allocated_sum;
        $rec_acdet_entry->unallocated = $rec_unallocated_sum;
        $rec_acdet_entry->foreign_unallocated = $rec_f_unallocated_sum;
        $rec_acdet_entry->save();

        // validate acdet after
        $validated = $this->validateAcdet($rec_acdet_entry,'Y');
        // validate acdet after
        $validated = $this->validateAcdet($drn_crn_acdet_entry,'Y');
        
        // update receipt/Payment

        if($doc_type == $entry){
            $ref = $cbmast->cancelled_reference;
            if($cbmast->on_account == 'A')
            {
                $rev_acdetallo = DB::select("SELECT sum(amount) as amount,sum(nvl(foreign_allocated,0)) as foreign_amount,sum(nvl(gain_loss,0)) as gain_loss 
                FROM $table
                where reference ='$ref' AND doc_type='$doc_type' AND entry_type_descr='$cbmast->entry_type_descr1'")[0];
            }

            else
            {
                $rev_acdetallo = DB::select("SELECT sum(amount) as amount,sum(nvl(foreign_allocated,0)) as foreign_amount,sum(nvl(gain_loss,0)) as gain_loss 
                                FROM $table
                                where reference ='$ref' AND doc_type='$doc_type' AND entry_type_descr='$cbmast->entry_type_descr1'
                                AND '$endt_no' = CASE 
                                    WHEN DOC_TYPE = 'REC' AND MANUAL_ALLOCATION = 'Y' THEN 
                                        REC_ENDT_RENEWAL_NO 
                                    ELSE 
                                        ENDT_RENEWAL_NO 
                                    END")[0];
            }
            
            
                                
            $rev_acdet_entry = Acdet::whereRaw("doc_type = '" . $doc_type . "' AND entry_type_descr='".$cbmast->entry_type_descr1."'")
                    ->where('reference', $ref)
                    ->where('endt_renewal_no',$endt_no)
                    ->first();
            //validate acdet before update
            $validated = $this->validateAcdet($rev_acdet_entry,'N');
            
            $rev_allocated_sum = $rev_acdetallo->amount + $rev_acdetallo->gain_loss;
            $rev_f_allocated_sum = $rev_acdetallo->foreign_amount;

            $rev_allocated_sum = round($rev_allocated_sum,10);
            $rev_f_allocated_sum = round($rev_f_allocated_sum,10);

            $rev_unallocated_sum = $rev_acdet_entry->nett - $rev_allocated_sum;
            $rev_f_unallocated_sum = $rev_acdet_entry->foreign_net - $rev_f_allocated_sum;
            
            $rev_acdet_entry->allocated = $rev_allocated_sum;
            $rev_acdet_entry->foreign_allocated = $rev_f_allocated_sum;
            $rev_acdet_entry->unallocated = $rev_unallocated_sum;
            $rev_acdet_entry->foreign_unallocated = $rev_f_unallocated_sum;
            $rev_acdet_entry->save();
            
            // validate acdet after
            $validated = $this->validateAcdet($rev_acdet_entry,'Y');
        }
        // }

        //dd($allocated.' '.$unallocated.' '.$comm_earned);

        //update commissions table
        if ($source == 'U/W') {
            $commdet = Commdet::where('reference', $drn_crn_acdet_entry->reference)
                ->where('doc_type', $ref_doc)
                ->update([
                    'allocated_amount' => $allocated_sum,
                    'unallocated_amount' => $unallocated_sum,
                    'comm_earned' => $comm_earned,
                    'foreign_comm_earned' => $comm_earned/$drn_crn_acdet_entry->currency_rate,
                    'req' => 'N'
                ]);

            if ($doc_type == 'REC' && $entry == 'REC') {
                //for claw back commission generation
                $commdet1 = Commdet::where('reference', $drn_crn_acdet_entry->reference)
                    ->where('doc_type', $ref_doc)
                    ->update([
                        'requisition_no' => '',
                        'req' => 'N'
                    ]);
            }
        }
        //dd($acdet1);

        return $drn_crn_acdet_entry;
    }

    // validate acdet
    public function validateAcdet($acdet,$after_update='N'){

        $valid = 'Y';
        $err_msg = null;
        $net_amt = (float)$acdet->nett;
        $allocated = (float)$acdet->allocated;
        $unallocated = (float)$acdet->unallocated;
        $net_amtf = (float)$acdet->foreign_net;
        $allocatedf = (float)$acdet->foreign_allocated;
        $unallocatedf = (float)$acdet->foreign_unallocated;
        
        if(($net_amt > 0 && $unallocated <0) || ($net_amt > 0 && $allocated <0)){
            $valid ='N';
            $err_msg = 'Signage for nett amount and allocated/unallocated does not match.'.
                'Reference :'.$acdet->reference. ' Doc_type: '.$acdet->doc_type.
                ' Net: '.$net_amt.' Allocated: '.$allocated.' Unallocated: '.$unallocated;;
        }
        elseif(($net_amt < 0 && $unallocated >0) || ($net_amt < 0 && $allocated >0)){
            $valid ='N';
            $err_msg = 'Signage for nett amount and allocated/unallocated does not match.'.
                'Reference :'.$acdet->reference. ' Doc_type: '.$acdet->doc_type.
                ' Net: '.$net_amt.' Allocated: '.$allocated.' Unallocated: '.$unallocated;
        }
        elseif((abs($unallocated) > abs($net_amt)) || abs($allocated) > abs($net_amt)){
            $valid ='N';
            $err_msg = 'Allocated/unallocated is greater than net amount.'.
                'Reference :'.$acdet->reference. ' Doc_type: '.$acdet->doc_type.
                ' Net: '.$net_amt.' Allocated: '.$allocated.' Unallocated: '.$unallocated;
        }
        // elseif((round($unallocated,0) + round($allocated,0) != round($net_amt,0))){
        //     $valid ='N';
        //     $err_msg = 'Sum of allocated and unallocated amount not equal to net amount.'.
        //         'Reference :'.$acdet->reference. ' Doc_type: '.$acdet->doc_type.
        //         ' Net: '.$net_amt.' Allocated: '.$allocated.' Unallocated: '.$unallocated;
        // }
        /*elseif((abs($unallocatedf) > abs($net_amtf)) || abs($allocatedf) > abs($net_amtf)){
            $valid ='N';
            $err_msg = 'Foreign Allocated/unallocated is greater than Foreign net amount.'.
                'Reference :'.$acdet->reference. ' Doc_type: '.$acdet->doc_type.
                ' Net: '.$net_amtf.' Allocated: '.$allocatedf.' Allocated: '.$unallocatedf;
        }
        */

		if($after_update == 'Y'){
			$state = 'After allocation. ';
		}
		else{
			$state = 'Before allocation. ';
		}
        
		if($valid == 'N'){
			Session::Flash('error',"Overallocation detected $state. Please check statements");
            throw new \Exception($state.$err_msg,ResponseCode::HTTP_NOT_ACCEPTABLE);
        }
        else{
            return true;
        }
    }

    public function receiptLocalAmount($cbmast,$debit_note)
    {
        $foreign_amount = $cbmast->foreign_gross_amount;
        $allo_rate = $cbmast->currency_rate;

        switch ($cbmast->currency_code) {
            case $debit_note->currency_code:
                $cbmast_amount = $cbmast->foreign_gross_amount * $debit_note->currency_rate;
                $allo_rate = $debit_note->currency_rate;
                break;
            
            default:
                $cbmast_amount = $cbmast->gross_amount;
                $allo_rate = $cbmast->currency_rate;
                break;
        }

        return [
            'amount' =>$cbmast_amount,
            'foreign_amount' =>$cbmast_amount/$allo_rate,
        ];
    }
} //end of class
