<?php
namespace App\Classes\gl\fo;
use Auth;
use App\Acdet;

use App\Agmnf;
use App\Cbmast;
use App\Crmast;
use App\Polana;
use App\Cbtrans;
use App\Gltaxes;
use App\Cbdeduct;
use App\Payreqst;
use App\Cbmastana;
use App\Clhmnallo;
use Carbon\Carbon;
use App\ClassModel;
use App\Gltaxtypes;
use App\Nlslparams;
use App\Payreqstana;
use App\Payreqtaxes;
use App\Cbdeductions;
use App\Gltaxtypesana;
use App\Models\TreatyPolana;
use App\Requisition_deductions;
use App\Services\IntermediaryQueryService;
use App\ValueObjects\IntermediaryQueryParams;

Class AnalysePaymentsClass {
	public function analysePayment($payreqst,$cbmast,$entry_type,$dr_account,$cr_account)
	{
		$today = carbon::today();
        $time = carbon::now();
        $user = Auth::user()->surname;

        $reference =  STR_PAD($cbmast->dtrans_no,6,'0',STR_PAD_LEFT).$cbmast->account_year;
        ///dd($cbmast);

		$delete = Cbmastana::where('offcd',$cbmast->offcd)
                            ->where('doc_type',$cbmast->doc_type)
                            ->where('entry_type_descr',$entry_type)
                            ->where('reference',$reference)
                            ->delete();
        //insert cbmastana
        $count = Cbmastana::where('offcd',$cbmast->offcd)
                            ->where('doc_type',$cbmast->doc_type)
                            ->where('entry_type_descr',$entry_type)
                            ->where('reference',$reference)
                            ->count();
        $count = $count+1;
        if($cbmast->doc_type == $cbmast->entry_type_descr){
        	$dr_cr = 'D';
        }
        else{
        	$dr_cr = 'C';
        }

        //adding nett amount to cbmastana
        $cbmastana_nett = Cbmastana::create([
            'offcd'=>$cbmast->offcd,
            'inter_branch_offcd'=>$cbmast->offcd,
            'doc_type'=>$cbmast->doc_type,
            'entry_type_descr'=>$entry_type,
            'trans_date'=>$today,
            'reference'=>$reference,
            'item_no'=>$count,
            'entry_type_descr1'=>$payreqst->entry_type_descr,
            'source_code'=>$payreqst->source_code,  
            'narration'=>'NET PAYMENT '.trim($cbmast->name),
            'glhead'=>$cbmast->credit_account,
            'currency_code'=>$payreqst->currency_code,
            'currency_rate'=>$payreqst->currency_rate,
            'amount'=>$cbmast->nett_amount,
            'foreign_amount'=>$cbmast->foreign_nett_amount,
            'dr_cr'=>$dr_cr,
        ]);

        //dd($cbmastana_nett);
        //add gross to cbmastana
        if($payreqst->break_down == 'Y'){
            $payreqstana = Payreqstana::where('requisition_no',$payreqst->req_no)->get();
            foreach($payreqstana as $payana){
                $count = Cbmastana::where('offcd',$cbmast->offcd)
                                ->where('doc_type',$cbmast->doc_type)
                                ->where('entry_type_descr',$entry_type)
                                ->where('reference',$reference)
                                ->count();
                $count = $count+1;
                if($cbmast->doc_type == $cbmast->entry_type_descr){
                    if($payana->dr_cr == 'D'){
                        $dr_cr = 'C';
                    }
                    else{
                        $dr_cr = 'D';
                    }
                }
                else{
                    $dr_cr = $payana->dr_cr;
                }

                $cbmastana = Cbmastana::create([
                    'offcd'=>$payana->offcd,
                    'inter_branch_offcd'=>$payana->offcd,
                    'doc_type'=>$cbmast->doc_type,
                    'entry_type_descr'=>$entry_type,
                    'trans_date'=>$today,
                    'reference'=>$reference,
                    'item_no'=>$count,
                    'entry_type_descr1'=>$payreqst->entry_type_descr,
                    'source_code'=>$payreqst->source_code,
                    'narration'=>substr(trim($payana->narration),0,50),
                    'glhead'=>$payana->glhead,
                    'amount'=>$payana->amount,
                    'foreign_amount'=>$payana->foreign_amount,
                    'currency_code'=>$payreqst->currency_code,
                    'currency_rate'=>$payreqst->currency_rate,
                    'dr_cr'=>$dr_cr,
                ]);
            }
        }

        else{
            $cbtrans = Cbtrans::where('doc_type',$cbmast->doc_type)
                                ->where('descr',$entry_type)
                                ->first();
            
            $glhead = $cbmast->debit_account;
            $count = Cbmastana::where('offcd',$cbmast->offcd)
                                ->where('doc_type',$cbmast->doc_type)
                                ->where('entry_type_descr',$entry_type)
                                ->where('reference',$reference)
                                ->count();
            $count = $count+1;
            $gross_line = $count;
            if($cbmast->doc_type == $cbmast->entry_type_descr){
            	$dr_cr = 'C';
            }
            else{
            	$dr_cr = 'D';
            }

            $cbmastana_gross = Cbmastana::create([
                'offcd'=>$cbmast->offcd,
                'inter_branch_offcd'=>$cbmast->offcd,
                'doc_type'=>$cbmast->doc_type,
                'entry_type_descr'=>$entry_type,
                'trans_date'=>$today,
                'reference'=>$reference,
                'item_no'=>$count,
                'entry_type_descr1'=>$payreqst->entry_type_descr,
                'source_code'=>$payreqst->source_code,
                'narration'=>substr(trim($cbtrans->description),0,50),
                'glhead'=>$glhead,
                'amount'=>$payreqst->local_gross_amount,
                'foreign_amount'=>$payreqst->gross_amount,
                'currency_code'=>$payreqst->currency_code,
                'currency_rate'=>$payreqst->currency_rate,
                'dr_cr'=>$dr_cr,
            ]);
        }
        //dd($cbmastana_gross);

        //add additions/deductions to cbmastana
        if($payreqst->analyse == 'Y'){
            for($i=1; $i<11; $i++){
                $code = 'deduction_code_'.$i;
                $deduction_amount = 'deduction_amount_'.$i;
                $w_deduction_code = $payreqst->$code;
                if(!empty($w_deduction_code) && $payreqst->$deduction_amount <> 0){
                $cbdeduct = Cbdeduct::where('code','=',$w_deduction_code)
                                    ->first();
                // dd($cbdeduct, $w_deduction_code);
                if($cbdeduct->affect_gl == "Y"){
                $count = Cbmastana::where('offcd',$cbmast->offcd)
                            	->where('doc_type',$cbmast->doc_type)
                            	->where('entry_type_descr',$entry_type)
                            	->where('reference',$reference)
                                ->count();
                $count = $count+1;
                
                $glhead = $cbdeduct->glhead;

                if($cbdeduct->add_deduct == 'A'){
                    $dr_cr = 'D';
                }
                else{
                    $dr_cr = 'C';
                }
                if($cbmast->doc_type == $cbmast->entry_type_descr){
                	if($dr_cr == 'D'){
						$dr_cr = 'C';
					}
					else{
						$dr_cr = 'D';
					}
                }
                $cbmastana_deduct = Cbmastana::create([
                    'offcd'=>$cbmast->offcd,
                    'inter_branch_offcd'=>$cbmast->offcd,
                    'doc_type'=>$cbmast->doc_type,
                    'entry_type_descr'=>$entry_type,
                    'trans_date'=>$today,
                    'reference'=>$reference,
                    'item_no'=>$count,
                    'entry_type_descr1'=>$payreqst->$code,
                    'source_code'=>$cbtrans->source_code,
                    'narration'=>substr($cbdeduct->description,0,50),
                    'glhead'=>$glhead,
                    'currency_code'=>$payreqst->currency_code,
                    'currency_rate'=>$payreqst->currency_rate,
                    'amount'=>$payreqst->$deduction_amount*$payreqst->currency_rate,
                    'foreign_amount'=>$payreqst->$deduction_amount,
                    'dr_cr'=>$dr_cr,
                ]);
                //dd($cbmastana_deduct);
                } 
                else{
                    $cbmastana = Cbmastana::where('offcd', $cbmast->offcd)
                                                ->where('doc_type', $cbmast->doc_type)
                                                ->where('reference', $reference)
                                                ->where('entry_type_descr',$cbmast->entry_type_descr)
                                                ->where('item_no', $gross_line)
                                                ->first();

                    if($cbdeduct->add_deduct == 'A'){
                        $amount = $cbmastana->amount + ($payreqst->$deduction_amount*$payreqst->currency_rate);
                        $foreign_amount = $cbmastana->amount + $payreqst->$deduction_amount;
                    }
                    else{
                        $amount = $cbmastana->amount - ($payreqst->$deduction_amount*$payreqst->currency_rate);
                        $foreign_amount = $cbmastana->amount - $payreqst->$deduction_amount;
                    }

                    $cbmastana_upd = Cbmastana::where('offcd', $cbmast->offcd)
                                                ->where('doc_type', $cbmast->doc_type)
                                                ->where('reference', $reference)
                                                ->where('entry_type_descr',$cbmast->entry_type_descr)
                                                ->where('item_no', $gross_line)
                                                ->update([
                                                    'amount' => $amount,
                                                    'foreign_amount' => $foreign_amount
                                                ]);
                }
                } //end if not empty deduction_code
            } //end for loop
        }

        //requisition taxes
        if($payreqst->total_taxes <> 0){
            $payreq_taxes = Payreqtaxes::where('requisition_no', $payreqst->requisition_no)->get();
            foreach($payreq_taxes as $taxes){
                $count = Cbmastana::where('offcd',$cbmast->offcd)
                                ->where('doc_type',$cbmast->doc_type)
                                ->where('entry_type_descr',$entry_type)
                                ->where('reference',$reference)
                                ->count();

                $count = $count+1;

                $gltaxes = Gltaxes::where('tax_code', $taxes->tax_code)
                                    ->where('tax_type', $taxes->tax_type)
                                    ->get()[0];

                $gltaxtypes  = Gltaxtypes::where('tax_type', $taxes->tax_type)->get()[0];

                if($gltaxtypes->analyse == 'Y'){
                    $taxtypesana = Gltaxtypesana::where('tax_type',$taxes->tax_type)
                                    ->where('tax_code', $taxes->tax_code)->get()[0];

                    $glhead = $taxtypesana->glhead;
                }
                else{
                    $glhead = $taxtypes->glhead;
                }

                if($taxes->add_deduct == 'A'){
                    $dr_cr = 'D';
                }
                else{
                    $dr_cr = 'C';
                }

                $cbmastana_taxes = Cbmastana::create([
                    'offcd'=>$cbmast->offcd,
                    'inter_branch_offcd'=>$cbmast->offcd,
                    'doc_type'=>$cbmast->doc_type,
                    'entry_type_descr'=>$entry_type,
                    'trans_date'=>$today,
                    'reference'=>$reference,
                    'item_no'=>$count,
                    'entry_type_descr1'=>$payreqst->entry_type_descr,
                    'source_code'=>$cbmast->source_code,
                    'narration'=>substr($gltaxes->tax_description,0,50),
                    'glhead'=>$glhead,
                    'currency_code'=>$payreqst->currency_code,
                    'currency_rate'=>$payreqst->currency_rate,
                    'amount'=>abs($taxes->$tax_amount)*$cbmast->tax_rate,
                    'foreign_amount'=>abs($taxes->$tax_amount),
                    'dr_cr'=>$dr_cr,
                ]);
            }
        }

        //premium deductions
        if($payreqst->offset_premium == 'Y'){
            $req_policies = Requisition_deductions::where('req_no', $payreqst->req_no)->get();
            foreach ($req_policies as $pol) {
                $acdet_rec = Acdet::where('doc_type', 'DRN')
                                    ->where('source', 'U/W')
                                    ->where('endt_renewal_no', $pol->endt_renewal_no)
                                    ->get()[0];
                                    //dd($acdet_rec);

                $comm = ($pol->amount/$acdet_rec->nett) * $acdet_rec->comm_amt;
                $prem = $pol->amount - $comm;

                // $agmnf = Agmnf::where('branch', $acdet_rec->branch)
                //                 ->where('agent', $acdet_rec->agent)
                //                 ->get()[0];
                $intermediaryParams = new IntermediaryQueryParams([
                    'agentNo' => $acdet_rec->agent,
                    'conditions' => function($query){
                        $query->where('slug','premium-glhead');
                    }
                ]);
                $agmnf  =IntermediaryQueryService::getIntermediaryWithAttributeDetails($intermediaryParams)->first();

                //insert premium debtors 
                $count = Cbmastana::where('offcd',$cbmast->offcd)
                                ->where('doc_type',$cbmast->doc_type)
                                ->where('entry_type_descr',$entry_type)
                                ->where('reference',$reference)
                                ->count();
                $count = $count+1;

                $cbmastana = Cbmastana::create([
                    'offcd'=>$cbmast->offcd,
                    'inter_branch_offcd'=>$cbmast->offcd,
                    'doc_type'=>$cbmast->doc_type,
                    'entry_type_descr'=>$cbmast->entry_type_descr,
                    'trans_date'=>$today,
                    'reference'=>$reference,
                    'item_no'=>$count,
                    'entry_type_descr1'=>$payreqst->entry_type_descr,
                    'source_code'=>$payreqst->source_code,
                    'narration'=>"PREMIUM DEBTORS",
                    'glhead'=>$agmnf->value,
                    'amount'=>$prem,
                    'foreign_amount'=>$prem/$payreqst->currency_rate,
                    'currency_code'=>$payreqst->currency_code,
                    'currency_rate'=>$payreqst->currency_rate,
                    'dr_cr'=>"C",
                ]); 
                
                //insert comm debtors
                if($comm <> 0) { 
                    $count = Cbmastana::where('offcd',$cbmast->offcd)
                                ->where('doc_type',$cbmast->doc_type)
                                ->where('entry_type_descr',$entry_type)
                                ->where('reference',$reference)
                                ->count();
                    $count = $count+1;
                    $cbmastana = Cbmastana::create([
                        'offcd'=>$cbmast->offcd,
                        'inter_branch_offcd'=>$cbmast->offcd,
                        'doc_type'=>$cbmast->doc_type,
                        'entry_type_descr'=>$cbmast->entry_type_descr,
                        'trans_date'=>$today,
                        'reference'=>$reference,
                        'item_no'=>$count,
                        'entry_type_descr1'=>$payreqst->entry_type_descr,
                        'source_code'=>$payreqst->source_code,
                        'narration'=>"COMMISSION DEBTORS",
                        'glhead'=>$agmnf->gl_comm_debtors,
                        'amount'=>$comm,
                        'foreign_amount'=>$comm/$payreqst->currency_rate,
                        'currency_code'=>$payreqst->currency_code,
                        'currency_rate'=>$payreqst->currency_rate,
                        'dr_cr'=>'C',
                    ]);
                }          
            }
        }

        //enter taxes
        $count_taxes = Payreqtaxes::where('requisition_no', $payreqst->req_no)->count();
        //dd($count_taxes);

        if($count_taxes > 0){
            $payreqtaxes = Payreqtaxes::where('requisition_no', $payreqst->req_no)->get();
          
            foreach($payreqtaxes as $taxes){
                $count = Cbmastana::where('offcd',$cbmast->offcd)
                                    ->where('doc_type',$cbmast->doc_type)
                                    ->where('entry_type_descr',$entry_type)
                                    ->where('reference',$reference)
                                    ->count();
                $count = $count+1;

                $gltaxes = Gltaxes::where('tax_code',$taxes->tax_code)->get()[0];
                $taxtypes = Gltaxtypes::where('tax_type',$gltaxes->tax_type)->get()[0];
               
                if($taxtypes->analyse == 'Y'){
                    $gltaxtypesana = Gltaxtypesana::where('tax_type', $gltaxes->tax_type)
                    ->where('tax_code', $taxes->tax_code)->get()[0];
                    
                    $glhead = $gltaxtypesana->glhead;
                }
                else{
                    $glhead = $taxtypes->glhead;
                }

                if($cbmast->doc_type == $cbmat->entry_type_descr){
                    if($taxtypes->add_deduct == 'A'){
                        $dr_cr = 'C';
                    }
                    else{
                        $dr_cr = 'D';
                    }
                }
                else{
                    if($taxtypes->add_deduct == 'A'){
                        $dr_cr = 'D';
                    }
                    else{
                        $dr_cr = 'C';
                    }
                }
                $cbmastana_taxes = Cbmastana::create([
                    'offcd'=>$cbmast->offcd,
                    'inter_branch_offcd'=>$cbmast->offcd,
                    'doc_type'=>$cbmast->doc_type,
                    'entry_type_descr'=>$entry_type,
                    'trans_date'=>$today,
                    'reference'=>$reference,
                    'item_no'=>$count,
                    'entry_type_descr1'=>$payreqst->entry_type_descr,
                    'source_code'=>$cbmast->source_code,
                    'narration'=>substr($taxtypes->type_description,0,20),
                    'glhead'=>$glhead,
                    'currency_code'=>$payreqst->currency_code,
                    'currency_rate'=>$payreqst->currency_rate,
                    'amount'=>abs($taxes->tax_amount)*$payreqst->currency_rate,
                    'foreign_amount'=>abs($taxes->tax_amount),
                    'dr_cr'=>$dr_cr,
                ]);
            }
        }


            $cbmastana = Cbmastana::where('offcd',$cbmast->offcd)
                            	->where('doc_type',$cbmast->doc_type)
                            	->where('entry_type_descr',$entry_type)
                            	->where('reference',$reference)
                            	->get();
                //dd($cbmastana);
            return $cbmastana;
	} //end of function

	public function auto_analyse_pay($cbmast,$vat_glhead){
    $total_premium_amount = 0;
    $total_vat_amount = 0;

    $reference = str_pad($cbmast->dtrans_no, 6,'0',STR_PAD_LEFT).$cbmast->account_year;
   // dd($reference);

    if($cbmast->analyse_policy == "Y"){
        $polana_rec = Polana::where('doc_type',$cbmast->doc_type)
                        ->where('offcd',$cbmast->offcd)
                        ->where('entry_type_descr',$cbmast->entry_type_descr)
                        ->where('reference',$reference)
                        ->get();
                        //dd($polana_rec);
        foreach($polana_rec as $polana){
            $acdet = Acdet::where('doc_type',$polana->ref_doc_type)
                            ->where('endt_renewal_no',$polana->endt_renewal_no)
                            ->where('source','CRD')
                            ->where('reference', 'like', '%'.$polana->ref_dtrans_no.'%')
                            ->where('branch', $polana->branch)
                            ->where('agent', $polana->agent)
                            ->first();

            if($acdet->vat_amount_gross <> 0){
                $vat_amount = $polana->policy_amount / $acdet->nett * $acdet->vat_amount_gross;
                $premium_amount = $polana->policy_amount - $vat_amount;   
            }
            else{
                $vat_amount = 0;
                $premium_amount = $polana->policy_amount;
            }

            $total_vat_amount = $total_vat_amount + $vat_amount;
            $total_premium_amount = $total_premium_amount + $premium_amount;
        }
    }
    else{
        $acdet = Acdet::where('doc_type','CRN')
                            ->where('endt_renewal_no',$cbmast->claim_no)
                            ->where('source','CRD')
                            ->where('reference', 'like', '%'.$cbmast->credit_note_no.'%')
                            ->where('branch', $cbmast->branch)
                            ->where('agent', (int)$cbmast->agent_no)
                            ->get()[0];
                            //dd($acdet);
        if($acdet->vat_amount_gross <> 0){
                $vat_amount = $cbmast->amount / $acdet->nett * $acdet->vat_amount_gross;
                $premium_amount = $cbmast->amount - $vat_amount;   
            }
            else{
                $vat_amount = 0;
                $premium_amount = $cbmast->amount;
            }

            $total_vat_amount = $total_vat_amount + $vat_amount;
            $total_premium_amount = $total_premium_amount + $premium_amount;
    }
    //dd($total_premium_amount);

    //create record for total amount (bank transaction)
    $cbmastana_net = Cbmastana::create([
                        'offcd'=>$cbmast->offcd,
                        'doc_type'=>$cbmast->doc_type,
                        'entry_type_descr'=>$cbmast->entry_type_descr,
                        'reference'=>$reference,
                        'source_code'=>$cbmast->source_code,
                        'dr_cr'=>'c',
                        'item_no'=>1, 
                        'currency_code'=>$cbmast->currency_code,
                        'currency_rate'=>$cbmast->currency_rate,
                        'inter_branch_offcd'=>$cbmast->offcd,
                        'entry_type_descr1'=>$cbmast->entry_type_descr,
                        'narration'=>'TOTAL PAYMENT AMOUNT',
                        'glhead'=>$cbmast->credit_account,
                        //'sub_account'=>$request->sub_account[$i],
                        'foreign_amount'=>$cbmast->foreign_amount,
                        'amount'=>$cbmast->amount,
                    ]);

    //create premium debtors record
    $cbtrans = Cbtrans::where('doc_type',$cbmast->doc_type)
                        ->where('descr',$cbmast->entry_type_descr)
                        ->first();

    $narration = trim($cbtrans->description);
    $source = $cbtrans->source_code;

    $cbmastana_prm = Cbmastana::create([
                        'offcd'=>$cbmast->offcd,
                        'doc_type'=>$cbmast->doc_type,
                        'entry_type_descr'=>$cbmast->entry_type_descr,
                        'reference'=>$reference,
                        'source_code'=>$source,
                        'dr_cr'=>'D',
                        'item_no'=>2, 
                        'currency_code'=>$cbmast->currency_code,
                        'currency_rate'=>$cbmast->currency_rate,
                        'inter_branch_offcd'=>$cbmast->offcd,
                        'entry_type_descr1'=>$cbmast->entry_type_descr,
                        'narration'=>$narration,
                        'glhead'=>$cbmast->debit_account,
                        //'sub_account'=>$request->sub_account[$i],
                        'foreign_amount'=>$total_premium_amount/$cbmast->currency_rate,
                        'amount'=>$total_premium_amount,
                    ]);

    //vat record
    if($total_vat_amount > 0){
    	$cbmastana_vat = Cbmastana::create([
                        'offcd'=>$cbmast->offcd,
                        'doc_type'=>$cbmast->doc_type,
                        'entry_type_descr'=>$cbmast->entry_type_descr,
                        'reference'=>$reference,
                        'source_code'=>$source,
                        'dr_cr'=>'D',
                        'item_no'=>3, 
                        'currency_code'=>$cbmast->currency_code,
                        'currency_rate'=>$cbmast->currency_rate,
                        'inter_branch_offcd'=>$cbmast->offcd,
                        'entry_type_descr1'=>$cbmast->entry_type_descr,
                        'narration'=>'VAT ON REINSURANCE PREMIUM',
                        'glhead'=>$vat_glhead,
                        //'sub_account'=>$request->sub_account[$i],
                        'foreign_amount'=>$total_vat_amount/$cbmast->currency_rate,
                        'amount'=>$total_vat_amount,
                    ]);
    }

} //end of function

    //apply receipt deductions
    public function analyse_receipt($cbmast){
        $reference = STR_PAD($cbmast->dtrans_no,6,'0',STR_PAD_LEFT).$cbmast->account_year;
        //dd($reference);

        //insert cbmast nett
        $cbmastana_net = Cbmastana::create([
            'offcd'=>$cbmast->offcd,
            'doc_type'=>$cbmast->doc_type,
            'entry_type_descr'=>$cbmast->entry_type_descr,
            'reference'=>$reference,
            'source_code'=>$cbmast->source_code,
            'dr_cr'=>'D',
            'item_no'=>1, 
            'currency_code'=>$cbmast->currency_code,
            'currency_rate'=>$cbmast->currency_rate,
            'inter_branch_offcd'=>$cbmast->offcd,
            'entry_type_descr1'=>$cbmast->entry_type_descr,
            'narration'=>substr($cbmast->narration,0,50),
            'glhead'=>$cbmast->debit_account,
            'foreign_amount'=>$cbmast->foreign_amount,
            'amount'=>$cbmast->amount,
        ]);

        //insert cbmastana gross
        $cbtrans = Cbtrans::where('doc_type',$cbmast->doc_type)
        ->where('descr',$cbmast->entry_type_descr)
        ->first();

        $narration = substr($cbtrans->description,0,50);
        $source = $cbtrans->source_code;

        $cbmastana_gross = Cbmastana::create([
                'offcd'=>$cbmast->offcd,
                'doc_type'=>$cbmast->doc_type,
                'entry_type_descr'=>$cbmast->entry_type_descr,
                'reference'=>$reference,
                'source_code'=>$cbmast->source_code,
                'dr_cr'=>'C',
                'item_no'=>2, 
                'currency_code'=>$cbmast->currency_code,
                'currency_rate'=>$cbmast->currency_rate,
                'inter_branch_offcd'=>$cbmast->offcd,
                'entry_type_descr1'=>$cbmast->entry_type_descr,
                'narration'=>$narration,
                'glhead'=>$cbmast->credit_account,
                'foreign_amount'=>$cbmast->foreign_gross_amount,
                'amount'=>$cbmast->gross_amount,
            ]);

        //insert deductions
        $cbdeductions = Cbdeductions::where('reference', $reference)
                                    ->where('doc_type', $cbmast->doc_type)
                                    ->where('entry_type_descr',$cbmast->entry_type_descr)
                                    ->where('offcd', $cbmast->offcd)
                                    ->get();

        foreach ($cbdeductions as $deduction) {
            $item_no = Cbmastana::where('reference', $reference)
                                ->where('doc_type', $cbmast->doc_type)
                                ->where('entry_type_descr',$cbmast->entry_type_descr)
                                ->where('offcd', $cbmast->offcd)
                                ->count();

            $cbdeduct = Cbdeduct::where('code', $deduction->deduction_code)->first();
            if($cbdeduct->add_deduct == 'A'){
                $dr_cr = 'D';
            }
            else{
                $dr_cr = 'C';
            }
            
            $cbmastana_deduction = Cbmastana::create([
                'offcd'=>$cbmast->offcd,
                'doc_type'=>$cbmast->doc_type,
                'entry_type_descr'=>$cbmast->entry_type_descr,
                'reference'=>$reference,
                'source_code'=>$cbmast->source_code,
                'dr_cr'=> $dr_cr,
                'item_no'=>$item_no + 1, 
                'currency_code'=>$cbmast->currency_code,
                'currency_rate'=>$cbmast->currency_rate,
                'inter_branch_offcd'=>$cbmast->offcd,
                'entry_type_descr1'=>$deduction->deduction_code,
                'narration'=>substr($cbdeduct->description,0,50),
                'glhead'=>$deduction->glhead,
                'foreign_amount'=>$deduction->foreign_amount,
                'amount'=>$deduction->local_amount
            ]);
        }
    }

    public function analyseMultiClaimsRec($cbmast){
        $reference = STR_PAD($cbmast->dtrans_no,6,'0',STR_PAD_LEFT).$cbmast->account_year;
        //dd($reference);

        //insert cbmast nett
        $cbmastana_net = Cbmastana::create([
            'offcd'=>$cbmast->offcd,
            'doc_type'=>$cbmast->doc_type,
            'entry_type_descr'=>$cbmast->entry_type_descr,
            'reference'=>$reference,
            'source_code'=>$cbmast->source_code,
            'dr_cr'=>'D',
            'item_no'=>1, 
            'currency_code'=>$cbmast->currency_code,
            'currency_rate'=>$cbmast->currency_rate,
            'inter_branch_offcd'=>$cbmast->offcd,
            'entry_type_descr1'=>$cbmast->entry_type_descr,
            'narration'=>substr($cbmast->narration,0,50),
            'glhead'=>$cbmast->debit_account,
            'foreign_amount'=>$cbmast->foreign_amount,
            'amount'=>$cbmast->amount,
        ]);

        //insert cbmastana gross
        $cbtrans = Cbtrans::where('doc_type',$cbmast->doc_type)
        ->where('descr',$cbmast->entry_type_descr)
        ->first();

        $narration = substr($cbtrans->description,0,50);
        $source = $cbtrans->source_code;

        $clhmnallo = Clhmnallo::selectRaw("sum(foreign_amount) as foreign_amount, sum(amount) as amount, credit_account")
                                ->where('reference', $reference)
                                ->where('doc_type', $cbmast->doc_type)
                                ->where('offcd', $cbmast->offcd)
                                ->where('entry_type_descr', $cbmast->entry_type_descr)
                                ->groupBy('credit_account')
                                ->get();
                                // dd($clhmnallo);

        foreach($clhmnallo as $rec){
            $item_no = Cbmastana::where('reference', $reference)
                                ->where('doc_type', $cbmast->doc_type)
                                ->where('entry_type_descr',$cbmast->entry_type_descr)
                                ->where('offcd', $cbmast->offcd)
                                ->count();

            $cbmastana_gross = Cbmastana::create([
                'offcd'=>$cbmast->offcd,
                'doc_type'=>$cbmast->doc_type,
                'entry_type_descr'=>$cbmast->entry_type_descr,
                'reference'=>$reference,
                'source_code'=>$cbmast->source_code,
                'dr_cr'=>'C',
                'item_no'=>$item_no + 1, 
                'currency_code'=>$cbmast->currency_code,
                'currency_rate'=>$cbmast->currency_rate,
                'inter_branch_offcd'=>$cbmast->offcd,
                'entry_type_descr1'=>$cbmast->entry_type_descr,
                'narration'=>$narration,
                'glhead'=>$rec->credit_account,
                'foreign_amount'=>$rec->foreign_amount,
                'amount'=>$rec->amount,
            ]);
        }
    }

    public function analyseTreatyTransaction($cbmast){
        $reference = STR_PAD($cbmast->dtrans_no, 6, '0', STR_PAD_LEFT).$cbmast->account_year;
        
        //insert cbmast bank
        $cbmastana_net = Cbmastana::create([
            'offcd'=>$cbmast->offcd,
            'doc_type'=>$cbmast->doc_type,
            'entry_type_descr'=>$cbmast->entry_type_descr,
            'reference'=>$reference,
            'source_code'=>$cbmast->source_code,
            'dr_cr'=>'D',
            'item_no'=>1, 
            'currency_code'=>$cbmast->currency_code,
            'currency_rate'=>$cbmast->currency_rate,
            'inter_branch_offcd'=>$cbmast->offcd,
            'entry_type_descr1'=>$cbmast->entry_type_descr,
            'narration'=> 'NET RECEIPT AMOUNT',
            'glhead'=>$cbmast->debit_account,
            'foreign_amount'=>$cbmast->foreign_amount,
            'amount'=>$cbmast->amount,
        ]);

        $cbtrans = Cbtrans::where('doc_type', $cbmast->doc_type)->where('descr', $cbmast->entry_type_descr)->first();

        $narration = substr($cbtrans->description, 0, 50);

        $source = $cbtrans->source_code;

        $polana = TreatyPolana::where('reference', $reference)->where('doc_type', 'REC')
            ->where('entry_type_descr', $cbmast->entry_type_descr)->get();
       
        foreach($polana as $pol){
            $item_no = Cbmastana::where('reference', $reference)->where('doc_type', $cbmast->doc_type)
                ->where('entry_type_descr', $cbmast->entry_type_descr)->where('offcd', $cbmast->offcd)->count();
            
            ##get slhead
            $crmast_slhead = Crmast::where('branch', $pol->branch)->where('agent', $pol->agent)->value('profit_comm_slhead');
            
            $slhead = Nlslparams::where('glhead', $cbmast->credit_account )->where('slhead', $crmast_slhead)->value('slhead');

            if ($slhead) {
                $cbmastana = Cbmastana::create([
                    'offcd'=>$cbmast->offcd,
                    'doc_type'=>$cbmast->doc_type,
                    'entry_type_descr'=>$cbmast->entry_type_descr,
                    'reference'=>$reference,
                    'source_code'=>$cbmast->source_code,
                    'dr_cr'=>'C',
                    'item_no'=>$item_no + 1, 
                    'currency_code'=>$cbmast->currency_code,
                    'currency_rate'=>$cbmast->currency_rate,
                    'inter_branch_offcd'=>$cbmast->offcd,
                    'entry_type_descr1'=>$cbmast->entry_type_descr,
                    'narration'=>$narration,
                    'glhead'=>$cbmast->credit_account,
                    'foreign_amount'=>$pol->foreign_amount,
                    'amount'=>$pol->amount,
                    'slhead' =>  $crmast_slhead
                ]);
            }
        }
    }
}
