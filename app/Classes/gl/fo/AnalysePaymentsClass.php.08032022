<?php
namespace App\Classes\gl\fo;
use Carbon\Carbon;
use Auth;

use App\Cbmast;
use App\Cbmastana;
use App\Payreqst;
use App\ClassModel;
use App\Cbtrans;
use App\Cbdeduct;

Class AnalysePaymentsClass {
	public function analysePayment($payreqst,$cbmast,$entry_type,$dr_account,$cr_account)
	{
		$today = carbon::today();
        $time = carbon::now();
        $user = Auth::user()->surname;

        $reference =  STR_PAD($cbmast->dtrans_no,6,'0',STR_PAD_LEFT).$cbmast->account_year;
        ///dd($cbmast);

		$delete = Cbmastana::where('offcd',$cbmast->offcd)
                            ->where('doc_type',$cbmast->doc_type)
                            ->where('entry_type_descr',$entry_type)
                            ->where('reference',$reference)
                            ->delete();
        //insert cbmastana
        $count = Cbmastana::where('offcd',$cbmast->offcd)
                            ->where('doc_type',$cbmast->doc_type)
                            ->where('entry_type_descr',$entry_type)
                            ->where('reference',$reference)
                            ->count();
        $count = $count+1;
        if($cbmast->doc_type == $cbmast->entry_type_descr){
        	$dr_cr = 'D';
        }
        else{
        	$dr_cr = 'C';
        }

        //adding nett amount to cbmastana
        $cbmastana_nett = Cbmastana::create([
            'offcd'=>$cbmast->offcd,
            'inter_branch_offcd'=>$cbmast->offcd,
            'doc_type'=>$cbmast->doc_type,
            'entry_type_descr'=>$entry_type,
            'trans_date'=>$today,
            'reference'=>$reference,
            'item_no'=>$count,
            'entry_type_descr1'=>$payreqst->entry_type_descr,
            'source_code'=>$payreqst->source_code,  
            'narration'=>'NET PAYMENT',
            'glhead'=>$cr_account,
            'currency_code'=>$payreqst->currency_code,
            'currency_rate'=>$payreqst->currency_rate,
            'amount'=>$payreqst->local_amount,
            'foreign_amount'=>$payreqst->amount,
            'dr_cr'=>$dr_cr,
        ]);

        //dd($cbmastana_nett);
        //add nett 2 to cbmastana
        $cbtrans = Cbtrans::where('doc_type',$cbmast->doc_type)
                            ->where('descr',$entry_type)
                            ->first();
        /*if($payreqst->source_code == 'CLM' && $payreqst->class != 0)
        {
            $class = ClassModel::where('class',$payreqst->class)
                                ->first();

            $glhead1 = substr($dr_account,1,5);
            $glhead2 = STR_PAD($class->gl_class,3,'0',STR_PAD_LEFT);
            $glhead = $glhead1.$glhead2;
        }
        else{ */
            $glhead = $dr_account;
        //}
        $count = Cbmastana::where('offcd',$cbmast->offcd)
                            ->where('doc_type',$cbmast->doc_type)
                            ->where('entry_type_descr',$entry_type)
                            ->where('reference',$reference)
                            ->count();
        $count = $count+1;
        if($cbmast->doc_type == $cbmast->entry_type_descr){
        	$dr_cr = 'C';
        }
        else{
        	$dr_cr = 'D';
        }

        $cbmastana_gross = Cbmastana::create([
            'offcd'=>$cbmast->offcd,
            'inter_branch_offcd'=>$cbmast->offcd,
            'doc_type'=>$cbmast->doc_type,
            'entry_type_descr'=>$entry_type,
            'trans_date'=>$today,
            'reference'=>$reference,
            'item_no'=>$count,
            'entry_type_descr1'=>$payreqst->entry_type_descr,
            'source_code'=>$payreqst->source_code,
            'narration'=>substr(trim($cbtrans->description),0,20),
            'glhead'=>$glhead,
            'amount'=>$payreqst->local_gross_amount,
            'foreign_amount'=>$payreqst->gross_amount,
            'currency_code'=>$payreqst->currency_code,
            'currency_rate'=>$payreqst->currency_rate,
            'dr_cr'=>$dr_cr,
        ]);
        //dd($cbmastana_gross);

        //add additions/deductions to cbmastana
            for($i=1; $i<11; $i++){
                $code = 'deduction_code_'.$i;
                $deduction_amount = 'deduction_amount_'.$i;
                $w_deduction_code = $payreqst->$code;
                 //dd($w_deduction_code);
                if(!empty($w_deduction_code)){
                $cbdeduct = Cbdeduct::where('code','=',$w_deduction_code)
                                    ->first();
                    //$affect_gl = dd($cbdeduct->affect_gl);
                if($cbdeduct->affect_gl == "Y"){
                $count = Cbmastana::where('offcd',$cbmast->offcd)
                            	->where('doc_type',$cbmast->doc_type)
                            	->where('entry_type_descr',$entry_type)
                            	->where('reference',$reference)
                                ->count();
                $count = $count+1;
                
                $glhead = $cbdeduct->glhead;

                if($cbdeduct->add_deduct == 'A'){
                    $dr_cr = 'D';
                }
                else{
                    $dr_cr = 'C';
                }
                if($cbmast->doc_type == $cbmast->entry_type_descr){
                	if($dr_cr == 'D'){
						$dr_cr = 'C';
					}
					else{
						$dr_cr = 'D';
					}
                }
                $cbmastana_deduct = Cbmastana::create([
                    'offcd'=>$cbmast->offcd,
                    'inter_branch_offcd'=>$cbmast->offcd,
                    'doc_type'=>$cbmast->doc_type,
                    'entry_type_descr'=>$entry_type,
                    'trans_date'=>$today,
                    'reference'=>$reference,
                    'item_no'=>$count,
                    'entry_type_descr1'=>$payreqst->entry_type_descr,
                    'source_code'=>$cbtrans->source_code,
                    'narration'=>substr($cbdeduct->description,0,20),
                    'affect_sl'=>$cbdeduct->affect_sl,
                    'sltype'=>$cbdeduct->sltype,
                    'glhead'=>$glhead,
                    'currency_code'=>$payreqst->currency_code,
                    'currency_rate'=>$payreqst->currency_rate,
                    'amount'=>$payreqst->$deduction_amount*$payreqst->currency_rate,
                    'foreign_amount'=>$payreqst->$deduction_amount,
                    'dr_cr'=>$dr_cr,
                ]);
                //dd($cbmastana_deduct);
                } 
                } //end if not empty deduction_code
            } //end for loop
            $cbmastana = Cbmastana::where('offcd',$cbmast->offcd)
                            	->where('doc_type',$cbmast->doc_type)
                            	->where('entry_type_descr',$entry_type)
                            	->where('reference',$reference)
                            	->get();
                //dd($cbmastana);
            return $cbmastana;
	} //end of function
}
