<?php
namespace App\Classes\gl\fo;

use Carbon\Carbon;
use Auth;

use App\Payreqst;
use App\Cbseqno;


Class RequisitionClass {
	//generate requisition number
    function genReqno($dept_code,$year)
    {
        $payreqst_no = Cbseqno::where('account_year',$year)
                                ->where('dept_code',$dept_code)->first();
        $dtrans_no = $payreqst_no->dtrans_no;

        return $dtrans_no;
    }

    //increment docytpe serial number
    function updateDoctype($dept_code,$year)
    {
        $next_serial = Cbseqno::where('account_year',$year)
                        ->where('dept_code',$dept_code)->increment('dtrans_no',1);
        return $next_serial;
    }

    //insert payreqst
	public function createPayreqst($payreqst_data)
	{
		$today = Carbon::today();
        $time = Carbon::now();
        $year = $today->format('Y');
        $month = $today->format('m');
        $user = Auth::user()->user_name;
        $dept_code = $payreqst_data['dept_code'];
        $offcd = $payreqst_data['offcd'];
		$dtrans_no = $this->genReqno($dept_code,$year);
		$ref = STR_PAD($dtrans_no,6,'0',STR_PAD_LEFT);
        $req_no = $dept_code.$ref.$year.$month;

		//other payreqst fields
        $payreqst_data['doc_type'] = 'PRQ';
		$payreqst_data['dtrans_no'] = $dtrans_no;
		$payreqst_data['account_year'] = $year;
		$payreqst_data['account_month'] = $month;
		$payreqst_data['effective_date'] = $today;
		//$payreqst_data['multiclaims'] = 'N';
        $payreqst_data['premium_amount'] = 0;
        $payreqst_data['excess_amount'] = 0;
        $payreqst_data['payments_todate'] = 0;
        $payreqst_data['created_by'] = trim($user);
        $payreqst_data['created_date'] = $today;
        $payreqst_data['dola'] = $today;
        $payreqst_data['created_time'] = $time;
        $payreqst_data['req_no'] = $req_no; 
		

		$payreqst = Payreqst::insert($payreqst_data);
		if($payreqst){
			$new_serial = $this->updateDoctype($dept_code,$year);
		}

		return $payreqst_data;
    }

}