<?php
namespace App\Classes\gl\fo;
use Carbon\Carbon;
use Auth;

use App\Classes\gl\fo\ClaimsClass;
use App\Classes\gl\fo\AcdetClass;

use App\Cbmast;
use App\Acdet;
use App\Acdetallo;
use App\Cbtrans;
use App\Polana;
use App\Polmaster;
use App\Cbmastana;
use App\Clpmn;
use App\Clhmn;

Class AnalysedTransClass {
//claims for analysed receipts
public function updateClaims($offcd,$doc_type,$dtrans_no,$year,$month,$entry){
	$today = Carbon::today();
    $user = Auth::user()->user_name;
    $time = Carbon::now();

    $cbmast = Cbmast::where('offcd',$offcd)
                    ->where('doc_type',$doc_type)
                    ->where('dtrans_no',$dtrans_no)
                    ->where('account_year',$year)
                    ->where('account_month',$month)
                    ->where('entry_type_descr',$entry)
                    ->first();

    $reference = STR_PAD($dtrans_no,6,'0',STR_PAD_LEFT).$year;

    $cbmastanalyse = Cbmastana::where('doc_type',$doc_type)
    						->where('offcd',$offcd)
    						->where('entry_type_descr',$entry)
    						->where('reference',$reference)
    						->get();
                    
    
    foreach($cbmastanalyse as $cbmastana){
    	$cbtrans = Cbtrans::where('doc_type',$doc_type)
    						->where('descr',$cbmastana->entry_type_descr1)
    						->first();
        if($cbtrans){
            $flag = $cbtrans->excess_flag;
            //dd($cbmast->slhead);
            if($flag == "Y" && trim($cbmast->slhead) == 01){
                $entry_type = 'EXC';
               // dd("here");
            }
            else{
                $entry_type = $cbmastana->entry_type_descr1;
            }
        }
        else{
            $entry_type = $cbmastana->entry_type_descr1;
        }

    	if($cbtrans->source_code == 'CLM' && ((trim($cbmast->slhead == "02") && $cbmast->source_code == 'CLM' && $cbmastana->glhead != $cbmast->credit_account && trim($cbmastana->claim_no) == null) || 
    		(trim($cbmast->slhead) == "01" && $cbmast->source_code == "CLM" && $cbmastana->glhead != $cbmast->debit_account))){
    		$g_doc_type = $cbmast->doc_type;
    		if(trim($cbmast->slhead == "02") && $cbmast->doc_type == $cbmast->entry_type_descr){
    			$g_doc_type = 'REV';
    		}

    		$ln_no = Clpmn::where('dtrans_no',$cbmast->dtrans_no)
                    	->where('account_year',$cbmast->account_year)
                    	->where('doc_type',$cbmast->doc_type)
                    	->count();
            $ln_no = $ln_no+1;

            $cbmastana_upd = Cbmastana::where('doc_type',$doc_type)
    						->where('offcd',$offcd)
    						->where('entry_type_descr',$entry)
    						->where('reference',$reference)
    						->where('item_no',$cbmastana->item_no)
    						->update([
    							'ln_no' => $ln_no
    						]);

    		$clhmn = Clhmn::where('claim_no',$cbmast->claim_no)->first();

    	$clpmn_data = [
    		'ln_no' => $ln_no,
		    'claim_no' => $cbmast->claim_no,
		    'branch' => $clhmn->branch,
		    'agent' => $clhmn->agent_no,
		    'policy_no' => $clhmn->policy_no,
		    'class' => $clhmn->class,
		    'uw_year' => $clhmn->uw_year,
		    'pay_date' => $today,
		    'effective_date' => $today,
		    'entry_type_descr' => $entry_type,
		    'dtrans_no' => $cbmast->dtrans_no,
		    'tran_no' => $cbmast->dtrans_no,
		    'doc_type' => $g_doc_type,
		    'doc_ref' => trim($cbmast->cheque_no),
		    'currency_code' => $cbmast->currency_code,
		    'currency_rate' => $cbmast->currency_rate,
		    'claimant_code' => $cbmast->claimant_code,
		    'payee' => $cbmast->name,
		    'cheque_no' => trim($cbmast->cheque_no),
		    'cheque_date' => $cbmast->cheque_date,
		];
    		
    		switch(trim($cbmast->slhead)){
    			case "01": //receipts
    				if($cbmastana->dr_cr == "C"){
    					$clpmn_data['pay_type'] = 20;
    					if($cbmastana->currency_code == $clhmn->currency_code){
    						$clpmn_data['pay_amount'] = $cbmastana->foreign_amount*-1;
    						$clpmn_data['amount'] = $cbmastana->foreign_amount*-1;
    						$clpmn_data['local_amount'] = $cbmastana->amount*-1;
    					}
    					else{
    						$clpmn_data['currency_rate'] = 1;
    						$clpmn_data['pay_amount'] = $cbmastana->amount*-1;
    						$clpmn_data['amount'] = $cbmastana->amount*-1;
    						$clpmn_data['local_amount'] = $cbmastana->amount*-1;
    					}
    				}
    				else{
    					$clpmn_data['pay_type'] = 20;
    					if($cbmastana->currency_code == $clhmn->currency_code){
    						$clpmn_data['pay_amount'] = $cbmastana->foreign_amount;
    						$clpmn_data['amount'] = $cbmastana->foreign_amount;
    						$clpmn_data['local_amount'] = $cbmastana->amount;
    					}
    					else{
    						$clpmn_data['currency_rate'] = 1;
    						$clpmn_data['pay_amount'] = $cbmastana->amount;
    						$clpmn_data['amount'] = $cbmastana->amount;
    						$clpmn_data['local_amount'] = $cbmastana->amount;
    					}
    				}
    			break;

    			case "02": //payments
    				if($cbmastana->dr_cr == "D"){
    					$clpmn_data['pay_type'] = 10;
    					if($cbmastana->currency_code == $clhmn->currency_code){
    						$clpmn_data['pay_amount'] = $cbmastana->foreign_amount;
    						$clpmn_data['amount'] = $cbmastana->foreign_amount;
    						$clpmn_data['local_amount'] = $cbmastana->amount;
    					}
    					else{
    						$clpmn_data['currency_rate'] = 1;
    						$clpmn_data['pay_amount'] = $cbmastana->amount;
    						$clpmn_data['amount'] = $cbmastana->amount;
    						$clpmn_data['local_amount'] = $cbmastana->amount;
    					}
    				}
    				else{
    					$clpmn_data['pay_type'] = 10;
    					if($cbmastana->currency_code == $clhmn->currency_code){
    						$clpmn_data['pay_amount'] = $cbmastana->foreign_amount*-1;
    						$clpmn_data['amount'] = $cbmastana->foreign_amount*-1;
    						$clpmn_data['local_amount'] = $cbmastana->amount*-1;
    					}
    					else{
    						$clpmn_data['currency_rate'] = 1;
    						$clpmn_data['pay_amount'] = $cbmastana->amount*-1;
    						$clpmn_data['amount'] = $cbmastana->amount*-1;
    						$clpmn_data['local_amount'] = $cbmastana->amount*-1;
    					}
    				}
    			break;
    		}
    		$clpmn_data['peril'] = $cbmast->peril;
    		$clpmn_data['account_month'] = $cbmast->account_month;
    		$clpmn_data['account_year'] = $cbmast->account_year;
    		$clpmn_data['user_str'] = $user;
    		$clpmn_data['pay_time'] = $time;
    		$clpmn_data['dola'] = $today;
    		$clpmn_data['reinsured'] = 'N';
    		$clpmn_data['gl_updated'] = 'Y';
    		$clpmn_data['orig_from'] = 99;
    		$clpmn_data['source'] = 'CB';
    		$clpmn_data['final_settle'] = $cbmast->final_settle;

    		$claimsClass = new ClaimsClass();
    		$clpmn_data = $claimsClass->getClaimBalances($clpmn_data);

    		return $clpmn_data;
    	} //end if
    } //end foreach

}//end function

//update underwriting 
public function updateAcdet($offcd,$doc_type,$dtrans_no,$year,$month,$entry)
{
	$cbmast = Cbmast::where('offcd',$offcd)
                    	->where('doc_type',$doc_type)
                    	->where('dtrans_no',$dtrans_no)
                    	->where('account_year',$year)
                    	->where('account_month',$month)
                    	->where('entry_type_descr',$entry)
                    	->first();

    $reference = STR_PAD($dtrans_no,6,'0',STR_PAD_LEFT).$year;
    $g_total = 0;
	$g_foreign_total = 0;
	$g_alocateddx = 0;
    $g_allocatedd = 0;
    $g_allocatedf = 0;

    $cbmastanalyse = Cbmastana::where('doc_type',$doc_type)
    						->where('offcd',$offcd)
    						->where('entry_type_descr',$entry)
    						->where('reference',$reference)
    						->get();


    $w_ln_no = 0;
    foreach($cbmastanalyse as $cbmastana){
    	$count = Acdet::where('doc_type',$cbmast->doc_type)
    					->where('reference',$reference)
    					//->where('ln_no',$w_ln_no)
    					->count();
    	$w_ln_no = $count+1;
        //dd($w_ln_no);

    	$drn = Acdet::where('endt_renewal_no',$cbmast->claim_no)
						->where('doc_type','DRN')->first();
						
		if($entry == "REC"){
			$reference = $cbmast->cancelled_reference;
			$acdet3 = Acdet::where('reference',$reference)
							->where('doc_type',$doc_type)
							->first();
		}

    	if($cbmast->doc_type == "REC" && (($cbmast->source_code == "CLM" && $cbmast->doc_type != $cbmast->entry_type_descr && $cbmast->pay_method == 4 && $cbmast->debit_account == $cbmastana->glhead) 
    			|| 
    		($cbmast->source_code == "CLM" && $cbmast->doc_type == $cbmast->entry_type_descr && $cbmast->pay_method == 4 && $cbmast->debit_account == $cbmastana->glhead) 
    			||
    		(($cbmast->source_code == "U/W" || $cbmast->source_code == "FAC") && (($cbmast->doc_type != $cbmast->entry_type_descr && ($cbmastana->source_code == "U/W" || $cbmastana->source_code == "FAC") && $cbmastana->glhead != $cbmast->debit_account) 
    			|| 
    		($cbmast->doc_type == $cbmast->entry_type_descr && ($cbmastana->source_code == "U/W" || $cbmastana->source_code == "FAC") && $cbmastana->glhead != $cbmast->debit_account)))
    	)){
    		$acdet_data = [
    			'ln_no'=>$w_ln_no,
				'line_no'=>$w_ln_no,
                'branch'=>$cbmast->branch,
                'agent'=>$cbmast->agent_no,
                'class'=>$cbmast->class,
                'account_year'=>$cbmast->account_year,
                'account_month'=>$cbmast->account_month,
                'doc_type'=>$cbmast->doc_type,
                'reference'=>$reference,
                'date_effective'=>$cbmast->effective_date,
                'date_processed'=>$cbmast->effective_date,
                'client_number'=>$cbmast->client_number,
                'policyholder'=>trim($cbmast->name),
                'currency_code'=>$cbmast->currency_code,
                'currency_rate'=>$cbmast->currency_rate,
                'entry_type_descr'=>$cbmastana->entry_type_descr1,
                'entry_type_descr1'=>$cbmast->entry_type_descr1,
                'policy_no'=>$cbmast->policy_no,
                'endt_renewal_no'=>$cbmast->claim_no,
                'cheque_no'=>trim($cbmast->cheque_no),
                'ref_doc'=>$cbmast->ref_doc,
                'ref_doc_type'=>$cbmast->ref_doc_type,
                'orig_entry_type_descr'=>$cbmast->orig_entry_type_descr,
    		];

    		$cbtrans = Cbtrans::where('doc_type',$cbmast->doc_type)
                           		->where('descr',$cbmast->entry_type_descr)
                            	->first();

            switch(trim($cbmast->slhead)){
            	case "01": //receipts
            		if($cbmast->entry_type_descr == "REC"){
	            		$acdet_data['nett'] = $cbmastana->amount;
	            		$acdet_data['foreign_net'] = $cbmastana->foreign_amount;
	            	}
	            	else{
	            		$acdet_data['nett'] = $cbmastana->amount*-1;
	            		$acdet_data['foreign_net'] = $cbmastana->foreign_amount*-1;
	            	}
            	break;

            	case "02": //payments
            	if($cbmast->pay_method == 4 && $entry != $doc_type){
            		if($cbmast->source_code == 'CLM'){
            			$acdet_data['nett'] = $cbmastana->amount*-1;
            			$acdet_data['foreign_net'] = $cbmastana->foreign_amount*-1;
            		}
            		else{
            			$acdet_data['nett'] = $cbmastana->amount;
            			$acdet_data['foreign_net'] = $cbmastana->foreign_amount;
            		}
            	}
            	else if($cbmast->pay_method == 4 && $entry == $doc_type){
            		if($cbmast->source_code == 'CLM'){
            			$acdet_data['nett'] = $cbmastana->amount;
            			$acdet_data['foreign_net'] = $cbmastana->foreign_amount;
            		}
            		else{
            			$acdet_data['nett'] = $cbmastana->amount*-1;
            			$acdet_data['foreign_net'] = $cbmastana->foreign_amount*-1;
            		}
            	}
            	if($cbmast->pay_method != 4 && $entry != $doc_type){
            		if($cbmast->source_code == 'CLM'){
            			$acdet_data['nett'] = $cbmastana->amount;
            			$acdet_data['foreign_net'] = $cbmastana->foreign_amount;
            		}
            		else{
            			$acdet_data['nett'] = $cbmastana->amount*-1;
            			$acdet_data['foreign_net'] = $cbmastana->foreign_amount*-1;
            		}
            	}
            	else if($cbmast->pay_method != 4 && $entry == $doc_type){
            		if($cbmast->source_code == 'CLM'){
            			$acdet_data['nett'] = $cbmastana->amount*-1;
            			$acdet_data['foreign_net'] = $cbmastana->foreign_amount*-1;
            		}
            		else{
            			$acdet_data['nett'] = $cbmastana->amount*-1;
            			$acdet_data['foreign_net'] = $cbmastana->foreign_amount*-1;
            		}
            	}

            	break;
            } //end switch

            //allocations
            if($doc_type == "REC"){
            	if($entry == "REC"){
            		$acdet_data['allocated'] = $acdet3->allocated*-1;
            		$acdet_data['foreign_allocated'] = $acdet3->foreign_allocated*-1;
            		$acdet_data['unallocated'] = $acdet3->unallocated*-1;
            		$acdet_data['foreign_unallocated'] = $acdet3->foreign_unallocated*-1;
            		$acdet_data['comm_earned'] = $acdet3->comm_earned*-1;

            		$g_allocatedd = $acdet_data['allocated'];
            		$g_allocatedf = $acdet_data['foreign_allocated'];
            		$g_total = $acdet_data['nett'];
            		$g_foreign_total = $acdet_data['foreign_net'];
            		$g_alocateddx = $acdet_data['allocated'];
            	}
            	else{
            		$g_allocatedd = $drn->unallocated;
            		$g_allocatedf = $drn->foreign_unallocated;
            		$g_total = $drn->nett;
            		$g_foreign_total = $drn->foreign_net;
            		$g_alocateddx = $drn->allocated;

            		if($g_allocatedd > $cbmast->amount){
            			$acdet_data['allocated'] = $acdet_data['nett'];
            			$acdet_data['foreign_allocated'] = $acdet_data['foreign_net'];
            			$acdet_data['unallocated'] = 0;
            			$acdet_data['foreign_unallocated'] = 0;
            		}
            		else{
            			$acdet_data['allocated'] = $g_allocatedd*-1;
            			$acdet_data['foreign_allocated'] = $g_allocatedf*-1;
            			$acdet_data['unallocated'] = $g_allocatedd+$acdet_data['nett'];
            			$acdet_data['foreign_unallocated'] = $g_allocatedf+$acdet_data['foreign_net'];
            		}
            		//commission earned
            		$comm_remaining = $drn->comm_amt - $drn->comm_earned;
            		$w_comm_earned = ($cbmast->amount/$drn->nett)*$drn->comm_amt;
            		if($comm_remaining < $w_comm_earned && $comm_remaining > 0){
            			$acdet_data['comm_earned'] = $comm_remaining*-1;
            		}
            		else if($comm_remaining == 0){
            			$acdet_data['comm_earned'] = 0;
            		}
            		else{
            			$acdet_data['comm_earned'] = $w_comm_earned*-1;
            		}
            	}
            }
            else if($doc_type == "PAY"){
            	$acdet_data['allocated'] = $cbmast->amount;
            	$acdet_data['foreign_allocated'] = $cbmast->foreign_amount;
            	$acdet_data['unallocated'] = 0;
            	$acdet_data['foreign_unallocated'] = 0;
                $acdet_data['comm_earned'] = 0;
            } 
            //end of allocations

            $acdet_data['uw_year'] = $year;
            $acdet_data['agency_no'] = $cbmast->agent_no;
            $acdet_data['orig_entry_type_descr'] = $cbmast->orig_entry_type_descr;
            $acdet_data['unallocatedi'] = $acdet_data['unallocated'];
            $acdet_data['cr_account'] = $cbmast->credit_account;
            $acdet_data['dr_account'] = $cbmast->debit_account;
            $acdet_data['dola'] = $cbmast->effective_date;
            $acdet_data['time'] = $cbmast->created_time;
            $acdet_data['type'] = 20;
            $acdet_data['balance_code'] = 'Z';
            $acdet_data['trans_number'] = $dtrans_no;
            $acdet_data['source'] = 'CB';
            $acdet_data['debit_nos'] = $cbmast->debit_note_no;
            $acdet_data['user_1'] = $cbmast->created_by;
            if($acdet_data['nett'] > 0){
            	$acdet_data['dr_cr'] = 'D';
            }
            else{
            	$acdet_data['dr_cr'] = 'C';
            }

            //dd($acdet_data);

            $acdet_create = Acdet::insert($acdet_data);

            //allocate transactions
            if(trim($cbmast->source_code) == "U/W"){
                $acdetClass = new AcdetClass();
                $acdet = $acdetClass->allocateRec($acdet_data['endt_renewal_no'],$doc_type,$reference,$acdet_data['allocated'],$acdet_data['foreign_allocated'],$acdet_data['comm_earned'],$cbmast->amount,$entry);


                $acdetallo = $acdetClass->createAcdetallo($offcd,$doc_type,$dtrans_no,$year,$month,$entry,$acdet_data['allocated'],$g_allocatedd,$g_allocatedf,$acdet_data['endt_renewal_no']);
                //dd($acdetallo);
            }

            return $acdet_create;

    	}//end if
    }//end foreach
}//end function
}//end class
