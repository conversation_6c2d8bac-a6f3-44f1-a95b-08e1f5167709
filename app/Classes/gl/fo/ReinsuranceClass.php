<?php
namespace App\Classes\gl\fo;

use App\Cbmast;
use App\Cbtrans;
use App\Clhmn;
use App\Clmreinalloc;
use App\Clhmnallo;
use App\Debitmast;
use App\Models\TreatyPolana;
use App\Reiacdet;
use App\XolUploadBatch;
use App\XolUploads;
use App\XolFacUploadBatch;
use App\XolFacUploads;
use Auth;
use Carbon\Carbon;

class ReinsuranceClass
{
    public function updateReinsurance($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {

        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();
           
        // tretypolana
        $new_reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;
       
        $polana_treaty = TreatyPolana::where('reference', $new_reference)
            ->where('doc_type', $doc_type)
            ->where('entry_type_descr', $entry)
            ->get();
           
        $clhmn = Clhmn::where('claim_no', $cbmast->claim_no)->first();
        $debitmast = Debitmast::where('endt_renewal_no', $clhmn->endt_renewal_no)->first();

        $today = Carbon::today();
        $user = Auth::user()->user_name;
        $time = Carbon::now();

        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;
        $month = STR_PAD($month, 2, '0', STR_PAD_LEFT);

        if ($month == '01' || $month == '02' || $month == '03') {
            $quarter = 1;
        } else if ($month == '04' || $month == '05' || $month == '06') {
            $quarter = 2;
        } else if ($month == '07' || $month == '08' || $month == '09') {
            $quarter = 3;
        } else if ($month == '10' || $month == '11' || $month == '12') {
            $quarter = 4;
        }

        if ($cbmast->entry_type_descr == 'TRT') {
            $quarter = $cbmast->quarter;
        }
        //treaty code not found for cal
        //add reiacdet
        if ($cbmast->treaty_code != null) {
            $reiacdet_data = [
                'branch' => $cbmast->branch,
                'agent' => $cbmast->agent_no,
                'class' => $cbmast->class,
                'quarter' => $quarter,
                'ln_no' => $cbmast->line_no,
                'dtrans_no' => $cbmast->dtrans_no,
                'account_year' => $cbmast->account_year,
                'account_month' => $cbmast->account_month,
                'doc_type' => $cbmast->doc_type,
                'date_effective' => $cbmast->effective_date,
                'date_processed' => $today,
                'currency_code' => $cbmast->currency_code,
                'currency_rate' => $cbmast->currency_rate,
                'col_affect' => 16,
            ];

            $cbtrans = Cbtrans::where('doc_type', $cbmast->doc_type)
                ->where('descr', $cbmast->entry_type_descr)
                ->first();

            if (trim($cbmast->slhead) == "01") {
                if ($cbmast->entry_type_descr == "REC") {
                    $reiacdet_data['nett'] = $cbmast->amount;
                    $reiacdet_data['unallocated'] = $cbmast->amount;
                } 
                
                else {
                    $reiacdet_data['nett'] = $cbmast->amount * -1;
                    $reiacdet_data['unallocated'] = $cbmast->amount * -1;
                }
                $reiacdet_data['type'] = 10;
            } 
            
            else if (trim($cbmast->slhead) == "02") {
                if (($cbmast->pay_method == 4 && $cbmast->entry_type_descr != 'PAY') || ($cbmast->pay_method != 4 && $cbmast->entry_type_descr == 'PAY')) {
                    $reiacdet_data['nett'] = $cbmast->amount * -1;
                    $reiacdet_data['unallocated'] = $cbmast->amount * -1;
                  
                    if ($cbtrans->commission_flag == "Y") {
                        $reiacdet_data['nett'] = $cbmast->gross_amount * -1;
                        $reiacdet_data['unallocated'] = $cbmast->gross_amount * -1;
                    }
                } 
                
                else if (($cbmast->pay_method == 4 && $cbmast->entry_type_descr == 'PAY') || ($cbmast->pay_method != 4 && $cbmast->entry_type_descr != 'PAY')) {
                    $reiacdet_data['nett'] = $cbmast->amount;
                    $reiacdet_data['gross'] = $cbmast->amount;
                    $reiacdet_data['unallocated'] = $cbmast->amount;
                    if ($cbtrans->commission_flag == "Y") {
                        $reiacdet_data['nett'] = $cbmast->gross_amount;
                        $reiacdet_data['gross'] = $cbmast->gross_amount;
                        $reiacdet_data['unallocated'] = $cbmast->gross_amount;
                    }
                }

                $reiacdet_data['type'] = 20;
            }
            $reiacdet_data['gross'] = 0;
            $reiacdet_data['entry_type_descr'] = $cbmast->entry_type_descr;
            $reiacdet_data['endt_renewal_no'] = $cbmast->claim_no;
            $reiacdet_data['cheque_no'] = trim($cbmast->cheque_no);
            $reiacdet_data['ref_doc'] = $cbmast->ref_doc;
            $reiacdet_data['ref_doc_type'] = $cbmast->ref_doc_type;
            $reiacdet_data['ref_ln_no'] = 0;
            // $reiacdet_data['prid'] = ' ';
            // $reiacdet_data['prsno'] = ' ';
            // $reiacdet_data['force_change'] = ' ';
            // $reiacdet_data['manual_match'] = ' ';
            $reiacdet_data['allocated'] = 0;
            // $reiacdet_data['unallocated'] = $cbmast->amount * -1;
            $reiacdet_data['uw_year'] = $cbmast->uw_year;
            $reiacdet_data['comm_rate'] = 0;
            $reiacdet_data['comm_amt'] = 0;
            $reiacdet_data['cr_account'] = $cbmast->credit_account;
            //$reiacdet_data['cr_subaccount'] = ' ';
            $reiacdet_data['dr_account'] = $cbmast->debit_account;
            //$reiacdet_data['dr_subaccount'] = ' ';
            $reiacdet_data['dola'] = $today;
            $reiacdet_data['time'] = $time;
            $reiacdet_data['entry_type'] = 0;
            //$reiacdet_data['type'] = 20;
            $reiacdet_data['payee'] = trim($cbmast->name);
            $reiacdet_data['treaty_code'] = $cbmast->treaty_code;
            $reiacdet_data['balance_code'] = 'Z';
            $reiacdet_data['trans_number'] = $cbmast->dtrans_no;
            $reiacdet_data['orig_system'] = 44;
            $reiacdet_data['orig'] = $cbmast->source_code;
            $reiacdet_data['gl_update_flag'] = ' ';
            $reiacdet_data['reins_updated'] = ' ';
            $reiacdet_data['sub_updated'] = ' ';
            $reiacdet_data['user_id'] = trim($user);
            if ($reiacdet_data['nett'] > 0) {
                $reiacdet_data['dr_cr'] = 'D';
            } else {
                $reiacdet_data['dr_cr'] = 'C';
            }

            $reiacdet_data['type_of_bus'] = 0;
            //$reiacdet_data['user_filler'] = ' ';
            // dd($reiacdet_data);
            $reiacdet_create = Reiacdet::insert($reiacdet_data);
        
            return (object)$reiacdet_data;
        } //end of if cbmast

    } //end of function

    public function createClmreinalloc($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $month = (int) $month;

        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->get()[0];

            // dd($cbmast);

        $agent = $cbmast->agent_no;
        $reference = STR_PAD($cbmast->dtrans_no, 6, '0', STR_PAD_LEFT) . $cbmast->account_year;

        if ($cbmast->doc_type == $cbmast->entry_type_descr) {
            $entry_type = $cbmast->entry_type_descr1;
        } else {
            $entry_type = $cbmast->entry_type_descr;
        }

        $clmreinalloc = Clmreinalloc::where('claim_no', $cbmast->claim_no)
            ->where('branch', $cbmast->branch)
            ->where('agent', $agent)
            ->where('dtrans_no', $cbmast->debit_note_no)
            ->where('entry_type_descr', $entry_type)
            ->where('treaty_code', $cbmast->treaty_code)
            ->get()[0];

        $count = Clmreinalloc::where('claim_no', $cbmast->claim_no)
            ->where('branch', $cbmast->branch)
            ->where('agent', $agent)
        //->where('entry_type_descr',$entry_type)
            ->count();
        $ln_no = $count + 1;

        $saveclmreinalloc = new Clmreinalloc();

        $saveclmreinalloc->trans_date = $cbmast->effective_date;
        $saveclmreinalloc->claim_no = $cbmast->claim_no;
        $saveclmreinalloc->policy_no = $cbmast->policy_no;

        $cbtrans = Cbtrans::where('doc_type', $cbmast->doc_type)
            ->where('descr', $cbmast->entry_type_descr)
            ->get()[0];

        $saveclmreinalloc->account_year = $cbmast->account_year;
        $saveclmreinalloc->account_month = $cbmast->account_month;
        $saveclmreinalloc->uw_year = $clmreinalloc->uw_year;
        $saveclmreinalloc->line_no1 = $ln_no;

        $saveclmreinalloc->analysiscode = $clmreinalloc->analysiscode;
        $saveclmreinalloc->entry_type_descr = trim($cbmast->entry_type_descr);
        $saveclmreinalloc->endt_renewal_no = $clmreinalloc->endt_renewal_no;

        $saveclmreinalloc->name_str = trim($cbtrans->description);

        $saveclmreinalloc->dtrans_no = $cbmast->dtrans_no;
        $saveclmreinalloc->invoicenumber = $clmreinalloc->invoicenumber;

        $saveclmreinalloc->currency_code = $cbmast->currency_code;
        $saveclmreinalloc->currency_rate = $cbmast->currency_rate;
        $saveclmreinalloc->class = $clmreinalloc->class;
        $saveclmreinalloc->broker_branch = $clmreinalloc->broker_branch;
        $saveclmreinalloc->broker_agent = $clmreinalloc->broker_agent;
        $saveclmreinalloc->reinclass = $clmreinalloc->reinsclass;
        $saveclmreinalloc->branch = $cbmast->branch;
        $saveclmreinalloc->doc_type = $cbmast->doc_type;
        // $saveclmreinalloc->per_treaty=$clmreinalloc->per_treaty;
        $saveclmreinalloc->treaty_code = $clmreinalloc->treaty_code;

        if ($cbmast->doc_type == $cbmast->entry_type_descr) {
            $receipt_amount = $cbmast->amount;
        } else {
            $receipt_amount = $cbmast->amount * -1;
        }

        $saveclmreinalloc->curr_pay_amount = $receipt_amount;
        $saveclmreinalloc->total_pay_amount = $receipt_amount;
        $saveclmreinalloc->amount = $receipt_amount;
        // $saveclmreinalloc->analysis_code=$clmreinalloc->analysis_code;
        $saveclmreinalloc->total_receipts = $receipt_amount;

        $saveclmreinalloc->agent = $cbmast->agent_no;

        $saveclmreinalloc->save();

        $clmreinalloc = Clmreinalloc::where('claim_no', $cbmast->claim_no)
            ->where('branch', $cbmast->branch)
            ->where('agent', $agent)
            ->where('dtrans_no', $cbmast->debit_note_no)
            ->where('entry_type_descr', $entry_type)
            ->where('treaty_code', $cbmast->treaty_code)
            ->update([
                'total_receipts' => $clmreinalloc->total_receipts - $receipt_amount,

            ]);

    }

    public function createMultiClmreinalloc($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $month = (int) $month;

        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->get()[0];

        // $agent = $cbmast->agent_no;
        $reference = STR_PAD($cbmast->dtrans_no, 6, '0', STR_PAD_LEFT) . $cbmast->account_year;
        $batch_no = trim($cbmast->batch_no);

        if($cbmast->entry_type_descr == 'XOL'){
            $clhmnallo = XolUploads::where('batch_no',$batch_no)
                                    ->get();
        } else if ($cbmast->entry_type_descr == 'FXR'){
            $clhmnallo = XolFacUploads::where('batch_no',$batch_no)
                                    ->get();
        } else {
            $clhmnallo = Clhmnallo::where('offcd',$cbmast->offcd)
                                ->where('doc_type',$cbmast->doc_type)
                                ->where('reference',$reference)
                                ->where('entry_type_descr',$cbmast->entry_type_descr)
                                ->get();
        }

        if ($cbmast->doc_type == $cbmast->entry_type_descr) {
            $entry_type = $cbmast->entry_type_descr1;
        } else {
            $entry_type = $cbmast->entry_type_descr;
        }

        foreach ($clhmnallo as $alloc) {
            $clmreinalloc = Clmreinalloc::where('claim_no', $alloc->claim_no)
                // ->where('branch', $cbmast->branch)
                ->where('agent', $alloc->agent)
                ->where('invoicenumber', $alloc->debit_note_no)
                // ->where('entry_type_descr', $alloc->entry_type)
                ->get()[0];

            $count = Clmreinalloc::where('claim_no', $alloc->claim_no)
                ->where('branch', $clmreinalloc->branch)
                ->where('agent', $clmreinalloc->agent)
            //->where('entry_type_descr',$entry_type)
                ->count();
            $ln_no = $count + 1;

            $saveclmreinalloc = new Clmreinalloc();

            $saveclmreinalloc->trans_date = $cbmast->effective_date;
            $saveclmreinalloc->claim_no = $alloc->claim_no;
            $saveclmreinalloc->policy_no = $alloc->policy_no;

            $cbtrans = Cbtrans::where('doc_type', $cbmast->doc_type)
                ->where('descr', $cbmast->entry_type_descr)
                ->get()[0];

            $saveclmreinalloc->account_year = $cbmast->account_year;
            $saveclmreinalloc->account_month = $cbmast->account_month;
            $saveclmreinalloc->uw_year = $clmreinalloc->uw_year;
            $saveclmreinalloc->line_no1 = $ln_no;

            $saveclmreinalloc->analysiscode = $clmreinalloc->analysiscode;
            $saveclmreinalloc->entry_type_descr = trim($cbmast->entry_type_descr);

            $saveclmreinalloc->endt_renewal_no = $clmreinalloc->endt_renewal_no;

            $saveclmreinalloc->name_str = trim($cbtrans->description);

            $saveclmreinalloc->dtrans_no = $cbmast->dtrans_no;
            $saveclmreinalloc->invoicenumber = $clmreinalloc->invoicenumber;

            $saveclmreinalloc->currency_code = $cbmast->currency_code;
            $saveclmreinalloc->currency_rate = $cbmast->currency_rate;
            $saveclmreinalloc->class = $clmreinalloc->class;
            $saveclmreinalloc->broker_branch = $clmreinalloc->broker_branch;
            $saveclmreinalloc->broker_agent = $clmreinalloc->broker_agent;
            $saveclmreinalloc->reinclass = $clmreinalloc->reinsclass;
            $saveclmreinalloc->branch = $clmreinalloc->branch;
            $saveclmreinalloc->doc_type = $cbmast->doc_type;
            // $saveclmreinalloc->per_treaty=$clmreinalloc->per_treaty;
            $saveclmreinalloc->treaty_code = $clmreinalloc->treaty_code;

            if ($cbmast->doc_type == $cbmast->entry_type_descr) {
                $receipt_amount = $alloc->amount;
            } else {
                $receipt_amount = $alloc->amount * -1;
            }

            $saveclmreinalloc->curr_pay_amount = $receipt_amount;
            $saveclmreinalloc->total_pay_amount = $receipt_amount;
            $saveclmreinalloc->amount = $receipt_amount;
            // $saveclmreinalloc->analysis_code=$clmreinalloc->analysis_code;
            $saveclmreinalloc->total_receipts = $receipt_amount;

            $saveclmreinalloc->agent = $clmreinalloc->agent;

            $saveclmreinalloc->save();

            $clmreinalloc = Clmreinalloc::where('claim_no', $alloc->claim_no)
                // ->where('branch', $cbmast->branch)
                ->where('agent', $agent)
                ->where('invoicenumber', $alloc->debit_note_no)
                // ->where('entry_type_descr', $entry_type)
                // ->where('treaty_code', $cbmast->treaty_code)
                ->update([
                    'total_receipts' => $clmreinalloc->total_receipts - $receipt_amount,

                ]);
        }

        if ($cbmast->entry_type_descr == $cbmast->doc_type) {
            $batch_amount = $cbmast->amount;
        } else {
            $batch_amount = $cbmast->amount * -1;
        }
        
        if($cbmast->entry_type_descr == $cbmast->doc_type){

            $batch = $xolbatch = XolUploadBatch::where('batch_no', $batch_no)
            ->get()[0];

            $xolbatch = XolUploadBatch::where('batch_no', $batch_no)
                ->update([
                    'cancelled' => 'Y',
                ]);

        }else{

            if($cbmast->entry_type_descr == 'FXR'){
                $batch = $xolfacbatch = XolFacUploadBatch::where('batch_no', $batch_no)
                ->get()[0];

                $xolfacbatch = XolFacUploadBatch::where('batch_no', $batch_no)
                ->update([
                    'amount_paid' => $batch->amount_paid - $batch_amount,
                ]);
            } else {
                
                $batch = $xolbatch = XolUploadBatch::where('batch_no', $batch_no)
                ->get()[0];

                $xolbatch = XolUploadBatch::where('batch_no', $batch_no)
                ->update([
                    'amount_paid' => $batch->amount_paid - $batch_amount,
                ]);
            }
        }
    }

    public function clhmnalloClmreinalloc($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $month = (int) $month;

        $cbmast = Cbmast::where('offcd', $offcd)
                        ->where('doc_type', $doc_type)
                        ->where('dtrans_no', $dtrans_no)
                        ->where('account_year', $year)
                        ->where('account_month', $month)
                        ->where('entry_type_descr', $entry)
                        ->get()[0];
                        // dd($cbmast);

        
        $agent = $cbmast->agent_no;
        $reference = STR_PAD($cbmast->dtrans_no, 6, '0', STR_PAD_LEFT) . $cbmast->account_year;

        $clhmnallo = Clhmnallo::where('offcd',$cbmast->offcd)
                                ->where('doc_type',$cbmast->doc_type)
                                ->where('reference',$reference)
                                ->where('entry_type_descr',$cbmast->entry_type_descr)
                                ->get();

        if ($cbmast->doc_type == $cbmast->entry_type_descr) {
            $entry_type = $cbmast->entry_type_descr1;
        } else {
            $entry_type = $cbmast->entry_type_descr;
        }
        foreach($clhmnallo as $allo){
            $clmreinalloc = Clmreinalloc::where('claim_no', $allo->claim_no)
                                        ->where('branch', $cbmast->branch)
                                        ->where('agent', $agent)
                                        ->where('dtrans_no', $allo->debit_note_no)
                                        ->where('entry_type_descr', $entry_type)
                                       ->get()[0];

            $count = Clmreinalloc::where('claim_no', $allo->claim_no)
                                    ->where('branch', $cbmast->branch)
                                    ->where('agent', $agent)
                                    ->count();
            $ln_no = $count + 1;

            $saveclmreinalloc = new Clmreinalloc();

            $saveclmreinalloc->trans_date = $cbmast->effective_date;
            $saveclmreinalloc->claim_no = $allo->claim_no;
            $saveclmreinalloc->policy_no = $clmreinalloc->policy_no;

            $cbtrans = Cbtrans::where('doc_type', $cbmast->doc_type)
                ->where('descr', $cbmast->entry_type_descr)
                ->get()[0];

            $saveclmreinalloc->account_year = $cbmast->account_year;
            $saveclmreinalloc->account_month = $cbmast->account_month;
            $saveclmreinalloc->uw_year = $clmreinalloc->uw_year;
            $saveclmreinalloc->line_no1 = $ln_no;

            $saveclmreinalloc->analysiscode = $clmreinalloc->analysiscode;
            $saveclmreinalloc->entry_type_descr = trim($cbmast->entry_type_descr);
            $saveclmreinalloc->endt_renewal_no = $clmreinalloc->endt_renewal_no;

            $saveclmreinalloc->name_str = trim($cbtrans->description);

            $saveclmreinalloc->dtrans_no = $cbmast->dtrans_no;
            $saveclmreinalloc->invoicenumber = $clmreinalloc->invoicenumber;

            $saveclmreinalloc->currency_code = $cbmast->currency_code;
            $saveclmreinalloc->currency_rate = $cbmast->currency_rate;
            $saveclmreinalloc->class = $clmreinalloc->class;
            $saveclmreinalloc->broker_branch = $clmreinalloc->broker_branch;
            $saveclmreinalloc->broker_agent = $clmreinalloc->broker_agent;
            $saveclmreinalloc->reinclass = $clmreinalloc->reinsclass;
            $saveclmreinalloc->branch = $cbmast->branch;
            $saveclmreinalloc->doc_type = $cbmast->doc_type;
            // $saveclmreinalloc->per_treaty=$clmreinalloc->per_treaty;
            $saveclmreinalloc->treaty_code = $clmreinalloc->treaty_code;

            if ($cbmast->doc_type == $cbmast->entry_type_descr) {
                $receipt_amount = $allo->amount;
            } else {
                $receipt_amount = $allo->amount * -1;
            }

            $saveclmreinalloc->curr_pay_amount = $receipt_amount;
            $saveclmreinalloc->total_pay_amount = $receipt_amount;
            $saveclmreinalloc->amount = $receipt_amount;
            $saveclmreinalloc->allocated = $receipt_amount;
            $saveclmreinalloc->unallocated = 0;
            // $saveclmreinalloc->analysis_code=$clmreinalloc->analysis_code;
            $saveclmreinalloc->total_receipts = $receipt_amount;

            $saveclmreinalloc->agent = $cbmast->agent_no;
            // dd($saveclmreinalloc);

            $saveclmreinalloc->save();

            $clmreinalloc = Clmreinalloc::where('claim_no', $allo->claim_no)
                ->where('branch', $cbmast->branch)
                ->where('agent', $agent)
                ->where('dtrans_no', $allo->debit_note_no)
                ->where('entry_type_descr', $entry_type)
                ->update([
                    'total_receipts' => $clmreinalloc->total_receipts - $receipt_amount,
                    'allocated' => $clmreinalloc->allocated - $receipt_amount,
                    'unallocated' => $clmreinalloc->unallocated + $receipt_amount
                ]);
        }

    }

    public function insertReiacdet($offcd, $doc_type, $dtrans_no, $year, $month, $entry,$item)
    {
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();
       
        $new_reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;

        $polana_treaty = TreatyPolana::where('reference', $new_reference)
            ->where('doc_type', $doc_type)
            ->where('entry_type_descr', $entry)
            ->get();
        
        $clhmn = Clhmn::where('claim_no', $cbmast->claim_no)
            ->first();
        $debitmast = Debitmast::where('endt_renewal_no', $clhmn->endt_renewal_no)
            ->first();
       
        $today = Carbon::today();
        $user = Auth::user()->user_name;
        $time = Carbon::now();

        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;

        $month = STR_PAD($month, 2, '0', STR_PAD_LEFT);

        if($cbmast->quarter == '' || $cbmast->quarter == null){
            if ($month == '01' || $month == '02' || $month == '03') {
                $quarter = 1;
            } else if ($month == '04' || $month == '05' || $month == '06') {
                $quarter = 2;
            } else if ($month == '07' || $month == '08' || $month == '09') {
                $quarter = 3;
            } else if ($month == '10' || $month == '11' || $month == '12') {
                $quarter = 4;
            }
        }

        else{
            $quarter = $cbmast->quarter;
        }

        if ($cbmast->treaty_code != null) {
            foreach ($polana_treaty as $tpolana) {
                $reiacdet_data = [
                    'branch' => $tpolana->branch,
                    'agent' => $tpolana->agent,
                    'class' => $cbmast->class,
                    'quarter' => $quarter,
                    'ln_no' => $tpolana->line_no,
                    'dtrans_no' => $cbmast->dtrans_no,
                    'account_year' => $cbmast->account_year,
                    'account_month' => $cbmast->account_month,
                    'doc_type' => $tpolana->doc_type,
                    'date_effective' => $cbmast->effective_date,
                    'date_processed' => $today,
                    'currency_code' => $tpolana->currency_code,
                    'currency_rate' => $tpolana->currency_rate,
                    'col_affect' => 16,
					'item_no'=>$tpolana->item_no,
                    'broker_agent' => $tpolana->broker_agent,
                    'broker_branch' => $tpolana->broker_branch,
                    'uw_year' => $year,
                    'ref_dtrans' => $item->dtrans_no,
                    'ref_doc_type' => $item->doc_type
                ];
               
                $cbtrans = Cbtrans::where('doc_type', $tpolana->doc_type)
                    ->where('descr', $tpolana->entry_type_descr)
                    ->first();

                if (trim($tpolana->doc_type) == "REC") {
                    $reiacdet_data['nett'] = $tpolana->amount;
                    $reiacdet_data['unallocated'] = 0;
                    $reiacdet_data['allocated'] = $tpolana->amount;
                    $reiacdet_data['type'] = 10;
                } 

                if (trim($tpolana->doc_type) == "PAY") {
                    $reiacdet_data['nett'] = $tpolana->amount;
                    $reiacdet_data['unallocated'] = 0;
                    $reiacdet_data['allocated'] = $tpolana->amount;
                    $reiacdet_data['type'] = 20;
                } 
                
                elseif (trim($cbmast->slhead) == "02") {
                    if (($cbmast->pay_method == 4 && $cbmast->entry_type_descr != 'PAY') || ($cbmast->pay_method != 4 && $cbmast->entry_type_descr == 'PAY')) {
                        $reiacdet_data['nett'] = $cbmast->amount * -1;
                        $reiacdet_data['unallocated'] = 0;
                        $reiacdet_data['allocated'] = $cbmast->amount * -1;
                        if ($cbtrans->commission_flag == "Y") {
                            $reiacdet_data['nett'] = $cbmast->gross_amount * -1;
                            $reiacdet_data['unallocated'] = 0;
                            $reiacdet_data['allocated'] = $cbmast->gross_amount * -1;
                        }
                    } elseif (($cbmast->pay_method == 4 && $cbmast->entry_type_descr == 'PAY') || ($cbmast->pay_method != 4 && $cbmast->entry_type_descr != 'PAY')) {
                        $reiacdet_data['nett'] = $cbmast->amount;
                        $reiacdet_data['gross'] = $cbmast->amount;
                        $reiacdet_data['unallocated'] = 0;
                        $reiacdet_data['allocated'] = $cbmast->amount;
                        if ($cbtrans->commission_flag == "Y") {
                            $reiacdet_data['nett'] = $cbmast->gross_amount;
                            $reiacdet_data['gross'] = $cbmast->gross_amount;
                            $reiacdet_data['allocated'] = $cbmast->gross_amount;
                            $reiacdet_data['unallocated'] = 0;
                        }
                    }

                    $reiacdet_data['type'] = 20;
                }

                $reiacdet_data['gross'] = 0;
                $reiacdet_data['entry_type_descr'] = $tpolana->entry_type_descr;
                $reiacdet_data['endt_renewal_no'] = $tpolana->claim_no;
                $reiacdet_data['cheque_no'] = trim($cbmast->cheque_no);
                $reiacdet_data['ref_doc'] = $cbmast->ref_doc;
                $reiacdet_data['ref_doc_type'] = $cbmast->ref_doc_type;
                $reiacdet_data['ref_ln_no'] = 0;
                //$reiacdet_data['allocated'] = 0;
                // $reiacdet_data['unallocated'] = $cbmast->amount * -1;
                $reiacdet_data['uw_year'] = $cbmast->uw_year == null ? $year : $cbmast->uw_year;
                $reiacdet_data['comm_rate'] = 0;
                $reiacdet_data['comm_amt'] = 0;
                $reiacdet_data['cr_account'] = $cbmast->credit_account;
                $reiacdet_data['dr_account'] = $cbmast->debit_account;
                $reiacdet_data['dola'] = $today;
                $reiacdet_data['time'] = $time;
                $reiacdet_data['entry_type'] = 0;
                $reiacdet_data['payee'] = trim($cbmast->name);
                $reiacdet_data['treaty_code'] = $cbmast->treaty_code;
                $reiacdet_data['balance_code'] = 'Z';
                $reiacdet_data['trans_number'] = $cbmast->dtrans_no;
                $reiacdet_data['orig_system'] = 44;
                $reiacdet_data['orig'] = $cbmast->source_code;
                $reiacdet_data['gl_update_flag'] = ' ';
                $reiacdet_data['reins_updated'] = ' ';
                $reiacdet_data['sub_updated'] = ' ';
                $reiacdet_data['user_id'] = trim($user);
                if ($reiacdet_data['nett'] > 0) {
                    $reiacdet_data['dr_cr'] = 'D';
                } else {
                    $reiacdet_data['dr_cr'] = 'C';
                }

                $reiacdet_data['type_of_bus'] = 0;
                
                $reiacdet_create = Reiacdet::insert($reiacdet_data);
              
                if($item){
                    if($reiacdet_data['doc_type'] == 'REC' && $reiacdet_data['entry_type_descr'] == 'REC'){
                        $upd = Reiacdet::where('doc_type', 'DRN')
                        ->where('dtrans_no', $item->trans_number)
                        ->where('broker_branch', $item->broker_branch)
                        ->where('broker_agent', $item->broker_agent)
                        ->where('branch', $item->branch)
                        ->where('agent', $item->agent)
                        ->update([
                            'allocated' => $item->allocated - $tpolana->amount,
                            'unallocated' => $item->unallocated +  $tpolana->amount
                        ]);

                    }
                    else if($reiacdet_data['doc_type'] == 'REC' && $reiacdet_data['entry_type_descr'] !== 'REC'){
                        $upd = Reiacdet::where('doc_type', 'DRN')
                        ->where('dtrans_no', $item->trans_number)
                        ->where('broker_branch', $item->broker_branch)
                        ->where('broker_agent', $item->broker_agent)
                        ->where('branch', $item->branch)
                        ->where('agent', $item->agent)
                        ->update([
                            'allocated' => $item->allocated + ($tpolana->amount * -1),
                            'unallocated' => $item->unallocated - ($tpolana->amount * -1)
                        ]);

                    }

                    if($reiacdet_data['doc_type'] == 'PAY' && $reiacdet_data['entry_type_descr'] !== 'PAY'){
                        $upd = Reiacdet::where('doc_type', 'CRN')
                        ->where('dtrans_no', $item->trans_number)
                        ->where('broker_branch', $item->broker_branch)
                        ->where('broker_agent', $item->broker_agent)
                        ->where('branch', $item->branch)
                        ->where('agent', $item->agent)
                        ->update([
                            'allocated' => $item->allocated + ($tpolana->amount * -1),
                            'unallocated' => $item->unallocated - ($tpolana->amount * -1)
                        ]);

                    }
                    else if($reiacdet_data['doc_type'] == 'PAY' && $reiacdet_data['entry_type_descr'] == 'PAY'){
                        $upd = Reiacdet::where('doc_type', 'CRN')
                        ->where('dtrans_no', $item->trans_number)
                        ->where('broker_branch', $item->broker_branch)
                        ->where('broker_agent', $item->broker_agent)
                        ->where('branch', $item->branch)
                        ->where('agent', $item->agent)
                        ->update([
                            'allocated' => $item->allocated - $tpolana->amount,
                            'unallocated' => $item->unallocated + $tpolana->amount                         
                        ]);
                    }
                }
            }
            
            return (object)$reiacdet_data;
        }
    }
} //end of class
