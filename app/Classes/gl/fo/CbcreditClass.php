<?php
namespace App\Classes\gl\fo;
use Carbon\Carbon;
use Auth;

use App\Classes\gl\fo\AcdetClass;

use App\Cbmast;
use App\Cbcredit;
use App\Userlimits;
use App\Client;
use App\Branch;
use App\Agmnf;

Class CbcreditClass {
	public function updateCbcredit($cbmast)
	{
		//if($cbmast->doc_type != $cbmast->entry_type_descr){
		$cbcredit = Cbcredit::where('offcd',$cbmast->offcd)
												->where('doc_type','CRA')
												->where('entry_type_descr',$cbmast->entry_type_descr1)
												->where('credit_reference',$cbmast->credit_reference)
												->first();


		if($cbmast->doc_type != $cbmast->entry_type_descr){
			$outs_amount = $cbcredit->out_cred_amt - $cbmast->amount;
		}
		else{
			$outs_amount = $cbcredit->out_cred_amt + $cbmast->amount;
		}

		$cbcredit1 = Cbcredit::where('offcd',$cbmast->offcd)
												->where('doc_type','CRA')
												->where('entry_type_descr',$cbmast->entry_type_descr1)
												->where('credit_reference',$cbmast->credit_reference)
												->update([
													'out_cred_amt' => $outs_amount,
												]);

			$user = trim($cbcredit->created_by);

			$userlimits = Userlimits::whereRaw("trim(user_name) = '".$user."'")
															->first();
			if($userlimits){
				if($cbmast->doc_type != $cbmast->entry_type_descr){
					$credit_amount = $userlimits->user_credit_amt - $cbmast->amount;
				}
				else{
					$credit_amount = $userlimits->user_credit_amt + $cbmast->amount;
				}

				$userlimits1 = Userlimits::whereRaw("trim(user_name) = '".$user."'")
																	->update([
																		'user_credit_amt'=>$credit_amount
																	]);
			}

			$client = Client::where('client_number',$cbcredit->client_number)
											->first();
			if($client){
				if($cbmast->doc_type != $cbmast->entry_type_descr){
					$credit_amount = $client->client_credit_amt - $cbmast->amount;
				}
				else{
					$credit_amount = $client->client_credit_amt + $cbmast->amount;
				}

				$client1 = Client::where('client_number',$cbcredit->client_number)
													->update([
														'client_credit_amt'=>$credit_amount
													]);
			}

			$branch = Branch::where('branch',$cbcredit->branch)
												->first();
			if($branch){
				if($cbmast->doc_type != $cbmast->entry_type_descr){
					$credit_amount = $branch->branch_credit_amt - $cbmast->amount;
				}
				else{
					$credit_amount = $branch->branch_credit_amt + $cbmast->amount;
				}

				$branch1 = Branch::where('branch',$cbcredit->branch)
													->update([
														'branch_credit_amt'=>$credit_amount
													]);
			}

			$agmnf = Agmnf::where('branch',$cbcredit->branch)
											->where('agent',(int)$cbcredit->agent_no)
											->first();
			if($agmnf){
				if($cbmast->doc_type != $cbmast->entry_type_descr){
					$credit_amount = $agmnf->credit_auth_amt - $cbmast->amount;
				}
				else{
					$credit_amount = $agmnf->credit_auth_amt + $cbmast->amount;
				}

				$agmnf1 = Agmnf::where('branch',$cbcredit->branch)
												->where('agent',(int)$cbcredit->agent_no)
												->update([
													'credit_auth_amt'=>$credit_amount
												]);
			}

			//do allocations
			if($cbmast->break_down != 'Y'){
				$acdetClass = new AcdetClass();
				$createAcdet = $acdetClass->createAcdet($cbmast->offcd,$cbmast->doc_type,$cbmast->dtrans_no,$cbmast->account_year,$cbmast->account_month,$cbmast->entry_type_descr);
			}

			else if($cbmast->break_down == 'Y'){
				$analysedClass = new AnalysedTransClass();
				$updateAcdet = $analysedClass->updateAcdet($cbmast->offcd,$cbmast->doc_type,$cbmast->dtrans_no,$cbmast->account_year,$cbmast->account_month,$cbmast->entry_type_descr);
			}

		return $cbcredit;
	}
}
