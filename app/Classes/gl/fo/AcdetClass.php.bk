<?php

namespace App\Classes\gl\fo;

use Carbon\Carbon;
use Auth;
use App\Reinacdetallo;
use App\Cbmast;
use App\Acdet;
use App\Acdetallo;
use App\Cbtrans;
use App\Polana;
use App\Polmaster;
use App\Commdet;
use App\Payreqst;


class AcdetClass
{

    public function createAcdet($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();
        //dd($cbmast);
        if ($cbmast->source_code == 'FAC') {
            $source = 'CRD';
        } else {
            $source = $cbmast->source_code;
        }

        if ($cbmast->analyse_policy != 'Y' || $cbmast->analyse_policy == null) {
            if ($cbmast->doc_type == 'REC') {
                $doc = 'DRN';
            } else {
                $doc = 'CRN';
            }
            //dd($cbmast->claim_no);

            $drn = Acdet::where('endt_renewal_no', $cbmast->claim_no)
                ->where('doc_type', $doc)
                ->where('source', $source)->first();
            //dd($drn);

            if ($entry == 'RRF' || $cbmast->entry_type_descr1 == 'RRF') {
                $doc = 'REC';
                $source = 'CB';
                $drn = Acdet::where('endt_renewal_no', $cbmast->claim_no)
                    ->where('doc_type', $doc)
                    ->whereRaw("trim(source) = '" . $source . "' ")->first();
            }
            //dd($drn);

            if ($entry == $doc_type) {
                $reference = $cbmast->cancelled_reference;
                $acdet3 = Acdet::where('reference', $reference)
                    ->where('doc_type', $doc_type)
                    ->where('entry_type_descr', $cbmast->entry_type_descr1)
                    ->first();
                //dd($acdet3);
            }

            $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;
            $g_total = 0;
            $g_foreign_total = 0;
            $g_alocateddx = 0;
            $g_allocatedd = 0;
            $g_allocatedf = 0;


            $acdet_data = [
                'ln_no' => $cbmast->ln_no,
                'line_no' => $cbmast->line_no,
                'branch' => $cbmast->branch,
                'agent' => $cbmast->agent_no,
                'class' => $cbmast->class,
                'account_year' => $cbmast->account_year,
                'account_month' => $cbmast->account_month,
                'doc_type' => $cbmast->doc_type,
                'reference' => $reference,
                'date_effective' => $cbmast->effective_date,
                'date_processed' => $cbmast->effective_date,
                'client_number' => $cbmast->client_number,
                'policyholder' => trim($cbmast->name),
                'currency_code' => $cbmast->currency_code,
                'currency_rate' => $cbmast->currency_rate,
                'entry_type_descr' => $entry,
                'policy_no' => $cbmast->policy_no,
                'endt_renewal_no' => $cbmast->claim_no,
                'cheque_no' => trim($cbmast->cheque_no),
                'ref_doc' => $cbmast->ref_doc,
                'ref_doc_type' => $cbmast->ref_doc_type,
                'orig_entry_type_descr' => $cbmast->orig_entry_type_descr,
                'unit_leader_id' => $drn->unit_leader_id,
                'region_id' => $drn->region_id,
                'unit_id' => $drn->unit_id,
                'channel' => $drn->channel,
                'override_rate' => $drn->override_rate,
                'calculate_on' => $drn->calculate_on,
                'expense_on' => $drn->expense_on,
                /*'override_payable'=>$drn->override_payable*-1,
                'gross_comm_amt'=>$drn->gross_comm_amt*-1*/
            ];
            $cbtrans = Cbtrans::where('doc_type', $cbmast->doc_type)
                ->where('descr', $cbmast->entry_type_descr)
                ->first();

            if (trim($cbmast->slhead) == '01') {
                if ($cbmast->entry_type_descr == "REC") {
                    $acdet_data['nett'] = $cbmast->amount;
                    $acdet_data['foreign_net'] = $cbmast->foreign_amount;
                } else {
                    $acdet_data['nett'] = $cbmast->amount * -1;
                    $acdet_data['foreign_net'] = $cbmast->foreign_amount * -1;
                }
            }
            if (trim($cbmast->slhead) == '02') {
                if ($cbmast->pay_method == 4 && $entry != $doc_type) {
                    $acdet_data['nett'] = $cbmast->gross_amount * -1;
                    $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount * -1;
                    if ($cbtrans->commission_flag == "Y") {
                        $acdet_data['nett'] = $cbmast->gross_amount * -1;
                        $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount * -1;
                    }
                } else if ($cbmast->pay_method == 4 && $entry == $doc_type) {
                    $acdet_data['nett'] = $cbmast->gross_amount;
                    $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount;
                    if ($cbtrans->commission_flag == "Y") {
                        $acdet_data['nett'] = $cbmast->gross_amount;
                        $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount;
                    }
                }
                if ($cbmast->pay_method != 4 && $entry != $doc_type) {
                    $acdet_data['nett'] = $cbmast->gross_amount;
                    $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount;
                    if ($cbtrans->commission_flag == "Y") {
                        $acdet_data['nett'] = $cbmast->gross_amount;
                        $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount;
                    }
                } else if ($cbmast->pay_method != 4 && $entry == $doc_type) {
                    $acdet_data['nett'] = $cbmast->gross_amount * -1;
                    $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount * -1;
                    if ($cbtrans->commission_flag == "Y") {
                        $acdet_data['nett'] = $cbmast->gross_amount * -1;
                        $acdet_data['foreign_net'] = $cbmast->foreign_gross_amount * -1;
                    }
                }
            }

            if ($doc_type == $entry) {
                $g_allocatedd = $acdet_data['allocated'];
                $g_allocatedf = $acdet_data['foreign_allocated'];
                $g_total = $acdet_data['nett'];
                $g_foreign_total = $acdet_data['foreign_net'];
                $g_alocateddx = $acdet_data['allocated'];
            } else {
                $g_allocatedd = $drn->unallocated;
                $g_allocatedf = $drn->foreign_unallocated;
                $g_total = $drn->nett;
                $g_foreign_total = $drn->foreign_net;
                $g_alocateddx = $drn->allocated;
            }
            if ($doc_type == "REC") {
                if ($entry == "REC") {
                    $acdet_data['allocated'] = $acdet3->allocated * -1;
                    $acdet_data['foreign_allocated'] = $acdet3->foreign_allocated * -1;
                    $acdet_data['unallocated'] = $acdet3->unallocated * -1;
                    $acdet_data['foreign_unallocated'] = $acdet3->foreign_unallocated * -1;
                    $acdet_data['comm_earned'] = $acdet3->comm_earned * -1;
                    $acdet_data['override_earned'] = $acdet3->override_earned * -1;
                } else {
                    if ($g_allocatedd > $cbmast->amount) {
                        $acdet_data['allocated'] = $acdet_data['nett'];
                        $acdet_data['foreign_allocated'] = $acdet_data['foreign_net'];
                        $acdet_data['unallocated'] = 0;
                        $acdet_data['foreign_unallocated'] = 0;
                    } else {
                        $acdet_data['allocated'] = $g_allocatedd * -1;
                        $acdet_data['foreign_allocated'] = $g_allocatedf * -1;
                        $acdet_data['unallocated'] = $g_allocatedd + $acdet_data['nett'];
                        $acdet_data['foreign_unallocated'] = $g_allocatedf + $acdet_data['foreign_net'];
                    }
                    //commission earned
                    $comm_remaining = $drn->comm_amt - $drn->comm_earned;
                    $w_comm_earned = ($cbmast->amount / $drn->nett) * $drn->comm_amt;
                    if ($comm_remaining < $w_comm_earned && $comm_remaining > 0) {
                        $acdet_data['comm_earned'] = $comm_remaining * -1;
                    } else if ($comm_remaining == 0) {
                        $acdet_data['comm_earned'] = 0;
                    } else {
                        $acdet_data['comm_earned'] = $w_comm_earned * -1;
                    }

                    //override earned
                    $override_remaining = $drn->override_payable - $drn->override_earned;
                    if ($drn->calculate_on == 'C') {
                        $w_override_earned = ($drn->override_rate / 100) * $acdet_data['comm_earned'];
                    } else {
                        $w_override_earned = ($drn->override_rate / 100) * $acdet_data['nett'];
                    }

                    if ($override_remaining < $override_earned && $override_remaining > 0) {
                        $acdet_data['override_earned'] = $override_remaining * -1;
                    } else if ($override_remaining == 0) {
                        $acdet_data['override_earned'] = 0;
                    } else {
                        $acdet_data['override_earned'] = $w_override_earned;
                    }
                }
            } else if ($doc_type == "PAY") {

                if ($entry == 'PAY') {
                    $acdet_data['allocated'] = $cbmast->amount * -1;
                    $acdet_data['foreign_allocated'] = $cbmast->foreign_amount * -1;
                    $acdet_data['comm_earned'] = $acdet3->comm_earned * -1;
                } else {
                    $acdet_data['allocated'] = $cbmast->amount;
                    $acdet_data['foreign_allocated'] = $cbmast->foreign_amount;
                    $acdet_data['comm_earned'] = ($cbmast->amount / $drn->nett * $drn->comm_amt);
                }


                $acdet_data['unallocated'] = 0;
                $acdet_data['foreign_unallocated'] = 0;
                $acdet_data['override_earned'] = 0;
            }

            $acdet_data['uw_year'] = $year;
            $acdet_data['agency_no'] = $cbmast->agent_no;
            $acdet_data['orig_entry_type_descr'] = $cbmast->orig_entry_type_descr;
            $acdet_data['unallocatedi'] = $acdet_data['unallocated'];
            $acdet_data['cr_account'] = $cbmast->credit_account;
            $acdet_data['dr_account'] = $cbmast->debit_account;
            $acdet_data['dola'] = $cbmast->effective_date;
            $acdet_data['time'] = $cbmast->created_time;
            $acdet_data['type'] = 20;
            $acdet_data['balance_code'] = 'Z';
            $acdet_data['trans_number'] = $dtrans_no;
            $acdet_data['source'] = 'CB';
            $acdet_data['debit_nos'] = $cbmast->debit_note_no;
            $acdet_data['user_1'] = $cbmast->created_by;
            if ($acdet_data['nett'] > 0) {
                $acdet_data['dr_cr'] = 'D';
            } else {
                $acdet_data['dr_cr'] = 'C';
            }

            //save acdet
            //dd($acdet_data);
            $acdet = Acdet::insert($acdet_data);
            //dd($acdet_data);

            //allocate transactions
            if (trim($cbmast->source_code) == "U/W" || $cbmast->source_code == 'FAC') {

                $r_reference;
                if ($cbmast->doc_type == "REC") {
                    $r_reference = $cbmast->debit_note_no;
                } else {
                    $r_reference = $cbmast->credit_note_no;
                }



                if($doc_type != $entry){
                    $acdet = $this->allocateRec($acdet_data['endt_renewal_no'], $reference, $acdet_data['allocated'], $acdet_data['foreign_allocated'], $acdet_data['comm_earned'], $cbmast->amount, $acdet_data['override_earned'], $cbmast->source_code, $r_reference,$cbmast);
                }

                if ($cbmast->source_code == 'U/W') {
                    $acdetallo = $this->createAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $acdet_data['allocated'], $g_allocatedd, $g_allocatedf, $acdet_data['endt_renewal_no'], $cbmast->source_code);
                } else {
                    //reinacdet allo function
                    $this->createReinAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $acdet_data['allocated'], $g_allocatedd, $g_allocatedf, $acdet_data['endt_renewal_no'], $cbmast->source_code);
                }
            }
        } //end of unaanalysed transactions

    } //end of function

    //create acdet for on account receipt
    public function createAcdetAccount($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->get()[0];

        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;

        $acdet_data = new Acdet;
        $acdet_data->ln_no = $cbmast->ln_no;
        $acdet_data->line_no = $cbmast->line_no;
        $acdet_data->branch = $cbmast->branch;
        $acdet_data->agent = $cbmast->agent_no;
        $acdet_data->class = $cbmast->class;
        $acdet_data->account_year = $cbmast->account_year;
        $acdet_data->account_month = $cbmast->account_month;
        $acdet_data->doc_type = $cbmast->doc_type;
        $acdet_data->reference = $reference;
        $acdet_data->date_effective = $cbmast->effective_date;
        $acdet_data->date_processed = $cbmast->effective_date;
        $acdet_data->policyholder = trim($cbmast->name);
        $acdet_data->currency_code = $cbmast->currency_code;
        $acdet_data->currency_rate = $cbmast->currency_rate;
        $acdet_data->entry_type_descr = $entry;
        $acdet_data->cheque_no = trim($cbmast->cheque_no);
        $acdet_data->ref_doc = $cbmast->ref_doc;
        $acdet_data->ref_doc_type = $cbmast->ref_doc_type;
        $acdet_data->orig_entry_type_descr = $cbmast->orig_entry_type_descr;
        $acdet_data->time = $cbmast->created_time;

        if ($cbmast->doc_type == $cbmast->entry_type_descr) {
            $acdet_data->nett = $cbmast->amount;
            $acdet_data->foreign_net = $cbmast->foreign_amount;
            $acdet_data->unallocated = $cbmast->amount;
            $acdet_data->foreign_unallocated = $cbmast->foreign_amount;
            $acdet_data->unallocatedi = $cbmast->amount;
        } else {
            $acdet_data->nett = $cbmast->amount * -1;
            $acdet_data->foreign_net = $cbmast->foreign_amount * -1;
            $acdet_data->unallocated = $cbmast->amount * -1;
            $acdet_data->foreign_unallocated = $cbmast->foreign_amount * -1;
            $acdet_data->unallocatedi = $cbmast->amount * -1;
        }
        $acdet_data->allocated = 0;
        $acdet_data->foreign_allocated = 0;
        $acdet_data->comm_earned = 0;
        $acdet_data->override_earned = 0;
        $acdet_data->uw_year = $year;
        $acdet_data->agency_no = $cbmast->agent_no;
        $acdet_data->cr_account = $cbmast->credit_account;
        $acdet_data->dr_account = $cbmast->debit_account;
        $acdet_data->dola = $cbmast->effective_date;
        $acdet_data->type = 20;
        $acdet_data->balance_code = 'Z';
        $acdet_data->trans_number = $dtrans_no;
        $acdet_data->source = 'CB';
        $acdet_data->user_1 = $cbmast->created_by;
        if ($acdet_data->nett > 0) {
            $acdet_data->dr_cr = 'D';
        } else {
            $acdet_data->dr_cr = 'C';
        }

        $acdet_create = $acdet_data->save();

        if ($acdet_create) {
            if ($cbmast->doc_type == $cbmast->entry_type_descr) {
                $acdetallo = $this->createAcdetalloAccount($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $cbmast->source_code, $reference);
            }
        }
    }

    //reverse polana transactions function
    public function reversePolana($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();

        $r_reference = $cbmast->cancelled_reference;
        $polana_rec = Polana::where('doc_type', $doc_type)
            ->where('offcd', $offcd)
            ->where('entry_type_descr', $cbmast->entry_type_descr1)
            ->where('reference', $r_reference)
            ->get();
        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;

        foreach ($polana_rec as $polana) {
            $item_no = Polana::where('doc_type', $doc_type)
                ->where('offcd', $offcd)
                ->where('entry_type_descr', $entry)
                ->where('reference', $reference)
                ->count();
            $item_no = $item_no + 1;
            if ($polana->dr_cr == "D") {
                $w_dr_cr = "C";
            } else {
                $w_dr_cr = "D";
            }

            $create_polana = Polana::create([
                'offcd' => $polana->offcd,
                'doc_type' => $polana->doc_type,
                'entry_type_descr' => $cbmast->entry_type_descr,
                'entry_type_descr1' => $cbmast->entry_type_descr1,
                'reference' => $reference,
                'ln_no' => $cbmast->ln_no,
                'line_no' => $cbmast->line_no,
                'policy_no' => $polana->policy_no,
                'endt_renewal_no' => $polana->endt_renewal_no,
                'item_no' => $item_no,
                'dr_cr' => $w_dr_cr,
                'total_amount' => $cbmast->amount,
                'policy_amount' => $polana->policy_amount * -1,
                'unallocated_amount' => $polana->unallocated_amount * -1,
                'allocated_amount' => 0,
                'branch' => $polana->branch,
                'agent' => $polana->agent,
                'debitnote_no' => $polana->debitnote_no,
                'ref_dtrans_no' => $polana->ref_dtrans_no,
                'ref_doc_type' => $polana->ref_doc_type,
                'ref_account_year' => $year,
                'client_number' => $polana->client_number
            ]);
        }

        //deallocate receipt
        if ($create_polana) {
            $polanaAcdet = $this->polanaAcdet($offcd, $doc_type, $dtrans_no, $year, $month, $entry);
        }
    }

    //analysed policy function
    public function polanaAcdet($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;
        $polana_rec = Polana::where('doc_type', $doc_type)
            ->where('offcd', $offcd)
            ->where('entry_type_descr', $entry)
            ->where('reference', $reference)
            /*->where('line_no',$ln_no)
                        ->where('item_no',$item_no)*/
            ->get();

        //dd($polana_rec);

        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();


        foreach ($polana_rec as $polana) {
            $ln_no = Acdet::where('reference', $reference)
                ->where('doc_type', $doc_type)
                ->count();
            $ln_no = $ln_no + 1;
            $polmaster = Polmaster::where('policy_no', $polana->policy_no)
                ->first();
            if ($cbmast->source_code == 'FAC') {
                $source_code = 'CRD';
            } 
            else if($cbmast->source_code == 'COR'){
                $source_code = 'U/W';
            }
            else {
                $source_code = $cbmast->source_code;
            }
            $acdet = Acdet::where('doc_type', 'DRN')
                ->where('endt_renewal_no', $polana->endt_renewal_no)
                ->where('source', $source_code)
                ->first();
            if ($entry == "REC") {
                $old_reference = $cbmast->cancelled_reference;
                $acdet3 = Acdet::where('reference', $old_reference)
                    ->where('doc_type', $doc_type)
                    ->where('endt_renewal_no', $polana->endt_renewal_no)
                    ->first();
            }
            $g_allocatedd = $acdet->unallocated;
            $g_allocatedf = $acdet->foreign_unallocated;
            $g_total = $acdet->nett;
            $g_foreign_total = $acdet->foreign_net;
            $g_alocateddx = $acdet->allocated;

            $acdet_data = [
                'ln_no' => $ln_no,
                'line_no' => $ln_no,
                'branch' => $polana->branch,
                'agent' => $polana->agent,
                'class' => $acdet->class,
                'account_year' => $cbmast->account_year,
                'account_month' => $cbmast->account_month,
                'doc_type' => $cbmast->doc_type,
                'reference' => $reference,
                'date_effective' => $cbmast->effective_date,
                'date_processed' => $cbmast->effective_date,
                'client_number' => $acdet->client_number,
                'policyholder' => trim($polmaster->name),
                'currency_code' => $cbmast->currency_code,
                'currency_rate' => $cbmast->currency_rate,
                'entry_type_descr' => $polana->entry_type_descr,
                'entry_type_descr1' => $polana->entry_type_descr1,
                'policy_no' => $polana->policy_no,
                'endt_renewal_no' => $polana->endt_renewal_no,
                'cheque_no' => trim($cbmast->cheque_no),
                'ref_doc' => $cbmast->ref_doc,
                'ref_doc_type' => $cbmast->ref_doc_type,
                'orig_entry_type_descr' => $cbmast->orig_entry_type_descr,
                'nett' => $polana->policy_amount,
                'foreign_net' => $polana->policy_amount / $cbmast->currency_rate,
                'unit_leader_id' => $acdet->unit_leader_id,
                'region_id' => $acdet->region_id,
                'unit_id' => $acdet->unit_id,
                'channel' => $acdet->channel,
                'override_rate' => $acdet->override_rate,
                'calculate_on' => $acdet->calculate_on,
                'expense_on' => $acdet->expense_on,
                /*'override_payable'=>$acdet->override_payable,
                'gross_comm_amt'=>$acdet->gross_comm_amt*/
            ];
            $cbtrans = Cbtrans::where('doc_type', $doc_type)
                ->where('descr', $entry)
                ->first();
            if ($doc_type == "REC") {
                if ($doc_type == $entry) { //receipt reversal
                    $acdet_data['nett'] = $acdet3->nett * -1;
                    $acdet_data['foreign_net'] = $acdet3->foreign_net * -1;
                    $acdet_data['allocated'] = $acdet3->allocated * -1;
                    $acdet_data['foreign_allocated'] = $acdet3->foreign_allocated * -1;
                    $acdet_data['unallocated'] = $acdet3->unallocated * -1;
                    $acdet_data['foreign_unallocated'] = $acdet3->foreign_unallocated * -1;
                    $acdet_data['comm_earned'] = $acdet3->comm_earned * -1;
                    $acdet_data['override_earned'] = $acdet3->override_earned * -1;
                } //end of receipt reversals
                else { //new receipt
                    if ($g_allocatedd > $polana->policy_amount || $g_allocatedd == $polana->policy_amount) {
                        $acdet_data['allocated'] = $acdet_data['nett'];
                        $acdet_data['foreign_allocated'] = $acdet_data['foreign_net'];
                        $acdet_data['unallocated'] = 0;
                        $acdet_data['foreign_unallocated'] = 0;
                    } else {
                        $acdet_data['allocated'] = $g_allocatedd * -1;
                        $acdet_data['foreign_allocated'] = $g_allocatedf * -1;
                        $acdet_data['unallocated'] = $g_allocatedd + $acdet_data['nett'];
                        $acdet_data['foreign_unallocated'] = $g_allocatedf + $acdet_data['foreign_net'];
                    }
                    //commission earned
                    $comm_remaining = $acdet->comm_amt - $acdet->comm_earned;
                    $w_comm_earned = ($polana->policy_amount / $acdet->nett) * $acdet->comm_amt * -1;
                    if ($comm_remaining < $w_comm_earned && $comm_remaining > 0) {
                        $acdet_data['comm_earned'] = $comm_remaining * -1;
                    } else if ($comm_remaining == 0) {
                        $acdet_data['comm_earned'] = 0;
                    } else {
                        $acdet_data['comm_earned'] = $w_comm_earned * -1;
                    }

                    //override earned
                    $override_remaining = $drn->override_payable - $drn->override_earned;
                    if ($acdet3->calculate_on == 'C') {
                        $w_override_earned = ($acdet->override_rate / 100) * $acdet_data['comm_earned'];
                    } else {
                        $w_override_earned = ($acdet->override_rate / 100) * $acdet_data['nett'];
                    }

                    if ($override_remaining < $override_earned && $override_remaining > 0) {
                        $acdet_data['override_earned'] = $override_remaining * -1;
                    } else if ($override_remaining == 0) {
                        $acdet_data['override_earned'] = 0;
                    } else {
                        $acdet_data['override_earned'] = $w_override_earned;
                    }
                } //end of new receipt
            } //end of receipts
            else if ($doc_type == "PAY") { //payments
                $acdet_data['allocated'] = $polana->policy_amount;
                $acdet_data['foreign_allocated'] = $polana->policy_amount / $cbmast->currency_rate;
                $acdet_data['unallocated'] = 0;
                $acdet_data['foreign_unallocated'] = 0;
            } //end of payments

            //other fields
            $acdet_data['uw_year'] = $cbmast->account_year;
            $acdet_data['agency_no'] = $cbmast->agent_no;
            $acdet_data['orig_entry_type_descr'] = $cbmast->orig_entry_type_descr;
            $acdet_data['unallocatedi'] = $acdet_data['unallocated'];
            $acdet_data['cr_account'] = $cbmast->credit_account;
            $acdet_data['dr_account'] = $cbmast->debit_account;
            $acdet_data['dola'] = $cbmast->effective_date;
            $acdet_data['time'] = $cbmast->created_time;
            $acdet_data['type'] = 20;
            $acdet_data['balance_code'] = 'Z';
            $acdet_data['trans_number'] = $dtrans_no;
            $acdet_data['source'] = 'CB';
            $acdet_data['debit_nos'] = $cbmast->debit_note_no;
            $acdet_data['user_1'] = $cbmast->created_by;
            $acdet_data['dr_cr'] = $polana->dr_cr;

            //dd($acdet_data);

            $acdet_create = Acdet::insert($acdet_data);

            if (trim($cbmast->source_code) == "U/W" || trim($cbmast->source_code) == "FAC" || $cbmast->source_code == "COR") {
                $r_reference;
                if ($cbmast->doc_type == "REC") {
                    $r_reference = $cbmast->debit_note_no;
                } else {
                    $r_reference = $cbmast->credit_note_no;
                }
                if($cbmast->doc_type != $cbmast->entry_type_descr){
                    $acdet = $this->allocateRec($acdet_data['endt_renewal_no'], $reference, $acdet_data['allocated'], $acdet_data['foreign_allocated'], $acdet_data['comm_earned'], $cbmast->amount, $acdet_data['override_earned'], $cbmast->source_code, $r_reference,$cbmast);
                }

                $acdetallo = $this->createAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $acdet_data['allocated'], $g_allocatedd, $g_allocatedf, $acdet_data['endt_renewal_no'], $cbmast->source_code);

                /*$acdet_now = Acdet::where('reference',$reference)
                            //->where('endt_renewal_no',$acdet_data['endt_renewal_no'])
                            ->get();
                    dd($acdet_now); */
            }
        } //end foreach polana
    }

    //receipt allocation function
    public function allocateRec($endt_no, $reference, $allocated_amount, $foreign_amount, $s_comm_earned, $amount, $s_override_earned, $source, $r_reference,$cbmast)
    {

        $entry=$cbmast->entry_type_descr;
        $doc_type=$cbmast->doc_type;



        if ($source == 'FAC') {
            $source = 'CRD';
        }
        else if($source == 'COR'){
            $source = 'U/W';
        }

        if ($doc_type == "REC") {
            $ref_doc = 'DRN';
            $acdet = Acdet::where('doc_type', 'DRN')
                ->where('source', $source)
                ->where('endt_renewal_no', $endt_no)->first();
        } else if ($doc_type == "PAY") {
            $ref_doc = 'CRN';

           
                $acdet = Acdet::where('doc_type', 'CRN')
                // ->whereRaw("reference like '%.$reference.%'")
                ->where('reference', 'LIKE', '%' . $r_reference . '%')
                ->where('source', $source)
                ->where('endt_renewal_no', $endt_no)->first();
            


           
        }

        if ($entry == 'RRF' || $cbmast->entry_type_descr1=='RRF') { //receipt refund
            $ref_doc = 'REC';
            $source = 'CB';

            
            if($cbmast->on_account=='A'){
                $acdet = Acdet::where('doc_type', 'REC')
                ->where('reference', 'LIKE', '%' . $r_reference . '%')
                ->whereNull('endt_renewal_no')
                ->whereRaw("trim(source) = '" . $source . "' ")
                ->first();
                
            }else{

            $acdet = Acdet::where('doc_type', 'REC')
                ->where('reference', 'LIKE', '%' . $r_reference . '%')
                ->whereRaw("trim(source) = '" . $source . "' ")
                ->where('endt_renewal_no', $endt_no)->first();
            }
        }

        //dd($doc_type);

        if ($doc_type == 'REC') {
            $amount = $allocated_amount * -1;
            $foreign_amount = $foreign_amount * -1;
            $allocated = 0;
            $foreign_allocated = 0;
            $unallocated = 0;
            $foreign_unallocated = 0;
            $comm_earned = 0;
            $override_earned = 0;
        }

        if (($acdet->unallocated > 0 && $entry != "REC") || $entry == "REC") {
            $allocated = $acdet->allocated + $amount;
            $foreign_allocated = $acdet->foreign_allocated + $foreign_amount;
            $unallocated = $acdet->unallocated - $amount;
            $foreign_unallocated = $acdet->foreign_unallocated - $foreign_amount;
            $comm_earned = $acdet->comm_earned - $s_comm_earned;
            $override_earned = $acdet->override_earned - $s_override_earned;
        }

        if ($doc_type == "PAY") {
            if (($acdet->unallocated < 0 && $entry != 'PAY') || $entry == 'PAY') {
                $allocated = $acdet->allocated - $allocated_amount;
                $foreign_allocated = $acdet->foreign_allocated - $foreign_amount;
                $unallocated = $acdet->unallocated + $allocated_amount;
                $foreign_unallocated = $acdet->foreign_unallocated + $foreign_amount;
                $comm_earned = $acdet->comm_earned - $s_comm_earned;
                $override_earned = 0;
            } else {
                $allocated = $acdet->allocated;
                $foreign_allocated = $acdet->foreign_allocated;
                $unallocated = $acdet->unallocated;
                $foreign_unallocated = $acdet->foreign_unallocated;
                $comm_earned = $acdet->comm_earned;
                $override_earned = 0;
            }

            //dd($acdet->unallocated); 
        }

      
        //dd($allocated.' '.$unallocated.' '.$comm_earned);


        // $acdet1 = Acdet::whereRaw("endt_renewal_no = '" . $endt_no . "' and 
        //     doc_type = '" . $ref_doc . "' and trim(source) = '" . $source . "' ")
        //     ->where('reference', 'LIKE', '%' . $r_reference . '%')
            $acdet1=$acdet->update([
                'allocated' => $allocated,
                'foreign_allocated' => $foreign_allocated,
                'unallocated' => $unallocated,
                'foreign_unallocated' => $foreign_unallocated,
                'comm_earned' => $comm_earned,
                'receipt_number' => $reference,
                'override_earned' => $override_earned
            ]);

        //update commissions table
        if ($acdet1 && $source == 'U/W') {
            $commdet = Commdet::where('reference', $acdet->reference)
                ->where('doc_type', $ref_doc)
                ->update([
                    'allocated_amount' => $allocated,
                    'unallocated_amount' => $unallocated,
                    'comm_earned' => $comm_earned,
                    'req' => 'N'
                ]);

            if ($doc_type == 'REC' && $entry == 'REC') {
                //for claw back commission generation
                $commdet1 = Commdet::where('reference', $acdet->reference)
                    ->where('doc_type', $ref_doc)
                    ->update([
                        'requisition_no' => '',
                        'req' => 'N'
                    ]);
            }
        }
        //dd($acdet1);

        return $acdet1;
    }



    function createAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $allocated_amount, $g_allocatedd, $g_allocatedf, $endt_no, $source)
    {
        //create acdetallo record
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();

        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;

        if ($cbmast->analyse_policy == "Y") {
            $polana = Polana::where('doc_type', $doc_type)
                ->where('offcd', $offcd)
                ->where('entry_type_descr', $entry)
                ->where('reference', $reference)
                ->where('endt_renewal_no', $endt_no)
                ->first();
            //$endt_no = $polana->endt_renewal_no;
            $amount = $polana->policy_amount;
            $foreign_amount = $polana->policy_amount / $cbmast->currency_rate;
            $client_number = $polana->client_number;
        } else {
            //$endt_no = $cbmast->claim_no;
            if ($doc_type == "REC") {
                $amount = $cbmast->amount * -1;
                $foreign_amount = $cbmast->foreign_amount * -1;
            } else {
                $amount = $cbmast->amount;
                $foreign_amount = $cbmast->foreign_amount;
            }
            $client_number = $cbmast->client_number;
        }

        if ($source == 'FAC') {
            $source = 'CRD';
        }
        else if($source == 'COR'){
            $source = 'U/W';
        }

        if ($doc_type == "REC") {
            $acdet = Acdet::where('endt_renewal_no', $endt_no)
                ->where('doc_type', "DRN")
                ->where('source', $source)->first();

            $dr_cr = 'C';

        } else if ($doc_type == "PAY") {
            $acdet = Acdet::where('doc_type', 'CRN')
                ->where('endt_renewal_no', $endt_no)
                ->where('source', $source)->first();

            $dr_cr = 'D';

            if ($entry == 'RRF' || $cbmast->entry_type_descr1 == 'RRF') {
                $source = 'CB';

                if($cbmast->on_account=='A'){
                    $acdet = Acdet::where('doc_type', 'REC')
                    ->where('reference', 'LIKE', '%' . $cbmast->credit_note_no . '%')
                    ->whereNull('endt_renewal_no')
                    ->whereRaw("trim(source) = '" . $source . "' ")->first();
                }else{
                    $acdet = Acdet::where('doc_type', 'REC')
                    ->where('reference', 'LIKE', '%' . $cbmast->credit_note_no . '%')
                    ->where('endt_renewal_no', $endt_no)
                    ->whereRaw("trim(source) = '" . $source . "' ")->first();
                }
              
            }
        }


        $records = Acdetallo::where('reference', $reference)
            ->count();
        if ($doc_type != $entry) {
            $acdetallo = Acdetallo::create([
                'branch' => $cbmast->branch,
                'agent' => $cbmast->agent_no,
                'client_number' => $acdet->client_number,
                'doc_type' => $cbmast->doc_type,
                'reference' => $reference,
                'ln_no' => $cbmast->ln_no,
                'item_no' => $records,
                'endt_renewal_no' => $acdet->endt_renewal_no,
                'policy_no' => $acdet->policy_no,
                'ref_doc_type' => $acdet->doc_type,
                'ref_reference' => $acdet->reference,
                'ref_ln_no' => $acdet->ln_no,
                'ref_endt_renewal_no' => $acdet->endt_renewal_no,
                'ref_policy_no' => $acdet->policy_no,
                'ref_client_number' => $acdet->client_number,
                'dr_cr' => $dr_cr,
                'amount' => $allocated_amount,
                'balance_before' => $amount,
                'balance_after' => ($amount) - $allocated_amount,
                'ref_balance_before' => $g_allocatedd,
                'ref_balance_after' => $acdet->unallocated,
                'account_year' => $cbmast->account_year,
                'account_month' => $cbmast->account_month,
                'dola' => $cbmast->effective_date,
                'amount_orig' => $amount,
                'foreign_amount' => $allocated_amount/$cbmast->currency_rate,
                'foreign_amount_orig' => $foreign_amount,
                'foreign_balance_before' => $foreign_amount,
                'foreign_balance_after' => ($foreign_amount) - $allocated_amount,
                'foreign_ref_balance_before' => $g_allocatedf,
                'foreign_ref_balance_after' => $acdet->foreign_unallocated,
                'currency_code' => $cbmast->currency_code,
                'currency_rate' => $cbmast->currency_rate,
                'allocation_date' => Carbon::now(),
                'user_str' => trim($cbmast->created_by),
            ]);
        } else {
            $old_reference = $cbmast->cancelled_reference;
            $acdetallo_rec = Acdetallo::where('reference', $old_reference)
                ->where('endt_renewal_no', $endt_no)
                ->get();

            foreach ($acdetallo_rec as $revs_allo) {
                $records = Acdetallo::where('reference', $reference)
                    ->count();

                if($revs_allo->endt_renewal_no == $revs_allo->ref_endt_renewal_no){
                    if($cbmast->analyse_policy == 'Y'){
                        $amount = $revs_allo->amount;
                        $foreign_amount = $revs_allo->foreign_amount;
                    }
                    else{
                        $amount = $cbmast->amount * -1;
                        $foreign_amount = $cbmast->foreign_amount * -1;
                    }

                    $acdetallo = Acdetallo::create([
                        'branch' => $revs_allo->branch,
                        'agent' => $revs_allo->agent,
                        'client_number' => $revs_allo->client_number,
                        'doc_type' => $revs_allo->doc_type,
                        'reference' => $reference,
                        'ln_no' => $cbmast->ln_no,
                        'item_no' => $records,
                        'endt_renewal_no' => $revs_allo->endt_renewal_no,
                        'policy_no' => $revs_allo->policy_no,
                        'ref_policy_no' => $revs_allo->ref_policy_no,
                        'ref_doc_type' => $doc_type,
                        'ref_reference' => $old_reference,
                        'ref_ln_no' => $revs_allo->ln_no,
                        'ref_endt_renewal_no' => $revs_allo->endt_renewal_no,
                        'dr_cr' => $dr_cr,
                        'amount' => $amount,
                        'balance_before' => $amount,
                        'balance_after' => 0,
                        'ref_balance_before' => $amount * -1,
                        'ref_balance_after' => 0,
                        'account_year' => $cbmast->account_year,
                        'account_month' => $cbmast->account_month,
                        'dola' => $cbmast->effective_date,
                        'amount_orig' => $amount,
                        'foreign_amount' => $foreign_amount,
                        'foreign_amount_orig' => $foreign_amount,
                        'foreign_balance_before' => $foreign_amount,
                        'foreign_balance_after' => 0,
                        'foreign_ref_balance_before' => $foreign_amount * -1,
                        'foreign_ref_balance_after' => 0,
                        'currency_code' => $cbmast->currency_code,
                        'currency_rate' => $cbmast->currency_rate,
                        'allocation_date' => Carbon::now(),
                        'user_str' => trim($cbmast->created_by),
                    ]);

                    //update acdet original receipt balances
                $acdet_rec = Acdet::where('doc_type','REC')
                                    ->where('endt_renewal_no',$endt_no)
                                    ->where('reference',$old_reference)
                                    ->where('entry_type_descr',$cbmast->entry_type_descr1)
                                    ->update([
                                        'allocated' => $amount,
                                        'unallocated' => 0,
                                        'foreign_allocated' => $foreign_amount,
                                        'foreign_unallocated' => 0
                                    ]);

                //update acdet reversal balances
                $acdet_rev = Acdet::where('doc_type','REC')
                                    ->where('endt_renewal_no',$endt_no)
                                    ->where('reference',$reference)
                                    ->where('entry_type_descr',$cbmast->entry_type_descr)
                                    ->update([
                                        'allocated' => $amount * -1,
                                        'unallocated' => 0,
                                        'foreign_allocated' => $foreign_amount * -1,
                                        'foreign_unallocated' => 0
                                    ]);
                }
                //deallocate acdetallo
                $acdetallo_upd = Acdetallo::where('reference',$old_reference)
                                ->where('ref_doc_type',$revs_allo->ref_doc_type)
                                ->where('ref_reference',$revs_allo->ref_reference)
                                ->update([
                                    'amount'=>0,
                                    'balance_before'=>$revs_allo->balance_before-$revs_allo->amount,
                                    'balance_after'=>$revs_allo->balance_after+$revs_allo->amount,
                                    'ref_balance_before'=>$revs_allo->ref_balance_before+$revs_allo->amount,
                                    'ref_balance_after'=>$revs_allo->ref_balance_after-$revs_allo->amount,
                                    'foreign_amount' => 0,
                                    'foreign_balance_before' =>$revs_allo->foreign_balance_before-$revs_allo->foreign_amount,
                                    'balance_after'=>$revs_allo->foreign_balance_after+$revs_allo->foreign_amount,
                                    'ref_balance_before'=>$revs_allo->foreign_ref_balance_before+$revs_allo->foreign_amount,
                                    'ref_balance_after'=>$revs_allo->foreign_ref_balance_after-$revs_allo->foreign_amount, 
                                    'deallocated_date'=>Carbon::now()
                                ]);

                if($revs_allo->ref_endt_renewal_no == null){
                    $endt_no = $revs_allo->endt_renewal_no;
                }
                else{
                    $endt_no = $revs_allo->ref_endt_renewal_no;
                }

                //get acdet drn/crn record record
                $acdet = Acdet::where('doc_type',$revs_allo->ref_doc_type)
                                ->where('reference',$revs_allo->ref_reference)
                                ->where('endt_renewal_no',$endt_no)
                                ->where('source','U/W')
                                ->get()[0];
                //update acdet debit notes balance
                $comm_earned = $revs_allo->amount / $acdet->nett * $acdet->comm_amt;

                $acdet_upd = Acdet::where('doc_type',$revs_allo->ref_doc_type)
                                ->where('reference',$revs_allo->ref_reference)
                                ->where('endt_renewal_no',$endt_no)
                                ->where('source','U/W')
                                ->update([
                                    'allocated' => $acdet->allocated + $revs_allo->amount,
                                    'unallocated' => $acdet->unallocated - $revs_allo->amount,
                                    'foreign_allocated' => $acdet->foreign_allocated + ($revs_allo->amount/$acdet->currency_rate),
                                    'foreign_unallocated' => $acdet->foreign_unallocated - ($revs_allo->amount/$acdet->currency_rate),
                                    'comm_earned' => $acdet->comm_earned + $comm_earned
                                ]);

                //update commdet
                $commdet_upd = Commdet::where('doc_type',$revs_allo->ref_doc_type)
                                ->where('reference',$revs_allo->ref_reference)
                                ->where('endt_renewal_no',$endt_no)
                                ->update([
                                    'allocated_amount' => $acdet->allocated + $revs_allo->amount,
                                    'unallocated_amount' => $acdet->unallocated - $revs_allo->amount,
                                    'comm_earned' => $acdet->comm_earned + $comm_earned,
                                    'req' => 'N',
                                    'requisition_no' => ''
                                ]);

            }
        }


        return $acdetallo;
    }

    //START OF REINACDETALLOC FUNCTION
    
    function createReinAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $allocated_amount, $g_allocatedd, $g_allocatedf, $endt_no, $source)
    {
        //create acdetallo record
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();



        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;

       

        if($cbmast->analyse_policy=='Y'){
            $amount=abs($allocated_amount);
            $foreign_amount=abs($allocated_amount)/$cbmast->currency_rate;
            $allocated_amount=abs($allocated_amount);
    
        }else{
            $amount = $cbmast->amount;
            $foreign_amount = $cbmast->foreign_amount;
        }


       
        $client_number = $cbmast->client_number;
        if($doc_type == $entry){
            $source = 'CB';
            $ref = $cbmast->cancelled_reference;
            $ref_doc = $cbmast->doc_type;
        }
        else{
            $source = 'CRD';
            $ref = $cbmast->credit_note_no;
            $ref_doc = 'CRN';
        }

        $agent=(int)$cbmast->agent_no;
            
           $acdet = Acdet::where('doc_type', $ref_doc)
                ->where('endt_renewal_no', $endt_no)
                ->where('source', $source)
                ->where('branch',$cbmast->branch)
                ->where('agent',$agent)
                ->where('reference', 'LIKE', '%' . $ref . '%')
                ->first();
      
        

        $records = Reinacdetallo::where('reference', $reference)
                                ->where('doc_type', $cbmast->doc_type)
                                ->get();

        $foreign_amount=$allocated_amount/$cbmast->currency_rate;
        
                                
        if ($doc_type != $entry) {
            $acdetallo =[
                'branch' => $cbmast->branch,
                'agent' => $cbmast->agent_no,
                'client_number' => $acdet->client_number,
                'doc_type' => $cbmast->doc_type,
                'reference' => $reference,
                'ref_reference'=>$acdet->reference,
                'user_str'=>Auth::user()->user_name,
                'time_processed'=>Carbon::now(),
                'allocated_orig'=>$allocated_amount,
                'ref_item_no'=>$acdet->item_no,
                'ln_no' => $cbmast->line_no,
                'item_no' => count($records)+1,
                'endt_renewal_no' => $acdet->endt_renewal_no,
                'policy_no' => $acdet->policy_no,
                'ref_doc_type' => $acdet->doc_type,
                'ref_ln_no' => $acdet->ln_no,
                'ref_endt_renewal_no' => $acdet->endt_renewal_no,
                'ref_policy_no' => $acdet->policy_no,
                'ref_client_number' => $acdet->client_number,
                'dr_cr' => 'D',
                'amount' => $amount,
                'balance_before' => $amount,
                'balance_after' => ($amount) - $allocated_amount,
                'ref_balance_before' => $acdet->unallocated-$allocated_amount,
                'ref_balance_after' => $acdet->unallocated,
                'account_year' => $cbmast->account_year,
                'account_month' => $cbmast->account_month,
                'dola' => $cbmast->effective_date,
                'amount_orig' => $allocated_amount,
                'foreign_amount' => $foreign_amount,
                'foreign_amount_orig' => $foreign_amount,
                'foreign_balance_before' => $foreign_amount,
                'foreign_balance_after' => ($foreign_amount) - $foreign_amount,
                'foreign_ref_balance_before' => $acdet->foreign_unallocated-$foreign_amount,
                'foreign_ref_balance_after' => $acdet->foreign_unallocated,
                'currency_code' => $cbmast->currency_code,
                'currency_rate' => $cbmast->currency_rate,
                'allocation_date' => Carbon::now()
            ];

            
          

        
          
            Reinacdetallo::insert($acdetallo);

            
        } else {

            //reversal
            $old_reference = $cbmast->cancelled_reference;
            $acdetallo_rec = Reinacdetallo::where('reference', $old_reference)
                        ->where('endt_renewal_no', $endt_no)
                        ->where('doc_type',$cbmast->doc_type)
                        ->where('branch',$cbmast->branch)
                        ->where('agent',$agent)
                        ->get();

            $acdet = Acdet::where('doc_type', $cbmast->doc_type)
                        ->where('endt_renewal_no', $endt_no)
                        ->whereRaw("trim(source) = 'CB' ")
                        ->where('branch',$cbmast->branch)
                        ->where('entry_type_descr',$cbmast->entry_type_descr1)
                        ->where('agent',$agent)
                        ->where('reference',$old_reference)
                        ->first();

            foreach ($acdetallo_rec as $revs_allo) {
                $records = Reinacdetallo::where('reference', $acdet->reference)
                            ->where('doc_type', $cbmast->doc_type)
                                ->get();

                $acdetallo = Reinacdetallo::create([
                    'branch' => $revs_allo->branch,
                    'agent' => $revs_allo->agent,
                    'client_number' => $revs_allo->client_number,
                    'doc_type' => $revs_allo->doc_type,
                    'reference' => $acdet->reference,
                    'ln_no' => $acdet->line_no,
                    'item_no' => count($records)+1,
                    'endt_renewal_no' => $revs_allo->endt_renewal_no,
                    'policy_no' => $revs_allo->policy_no,
                    'ref_doc_type' => $cbmast->doc_type,
                    'ref_reference' => $reference,
                    'ref_ln_no' => $cbmast->line_no,
                    'ref_client_number' => $revs_allo->ref_client_number,
                    'ref_endt_renewal_no' => $revs_allo->endt_renewal_no,
                    'ref_policy_no' => $revs_allo->ref_policy_no,
                    'dr_cr' => 'C',

                    'amount' => $acdet->nett,
                    'balance_before' => $acdet->nett,

                    'balance_after' => 0,
                    'ref_balance_before' => $acdet->nett * -1,
                    'ref_balance_after' => 0,
                    'account_year' => $cbmast->account_year,
                    'account_month' => $cbmast->account_month,
                    'dola' => $cbmast->effective_date,
                    'amount_orig' => $acdet->nett,

                    'foreign_amount' => $acdet->foreign_net,
                    'foreign_amount_orig' =>$acdet->foreign_net,
                    'foreign_balance_before' => $acdet->foreign_net,
                    'foreign_balance_after' => 0,

                    'foreign_ref_balance_before' => $acdet->foreign_net*-1,
                    'foreign_ref_balance_after' => 0,

                    'user_str' => Auth::user()->user_name,
                    'currency_code' => $cbmast->currency_code,
                    'currency_rate' => $cbmast->currency_rate,
                    'allocation_date' => Carbon::now(),
                    'time_processed'=>Carbon::now()
                ]);

                //deallocate acdetallo
                $acdetallo_upd = Reinacdetallo::where('reference',  $revs_allo->reference)
                    ->where('doc_type', $revs_allo->doc_type)
                    ->where('ref_reference', $revs_allo->ref_reference)
                    ->where('ref_doc_type',$revs_allo->ref_doc_type)
                    ->where('branch',$revs_allo->branch)
                    ->where('agent',$revs_allo->agent)
                    ->update([
                        'amount' => 0,
                        'balance_before' => $revs_allo->balance_after,
                        'balance_after' => $revs_allo->balance_before,
                        'ref_balance_before' => $revs_allo->ref_balance_after,
                        'ref_balance_after' => $revs_allo->ref_balance_before,

                        'foreign_amount' => 0,
                        'foreign_balance_before' => $revs_allo->foreign_balance_after,
                        'foreign_balance_after' => $revs_allo->foreign_balance_before,
                        'foreign_ref_balance_before' => $revs_allo->foreign_ref_balance_after,
                        'foreign_ref_balance_after' => $revs_allo->foreign_ref_balance_before,

                        'deallocated_date' => Carbon::now()
                    ]);
            }
        }


        return $acdetallo;
    }
    //END OF REINACDETALLOC FUNCTION

    function createAcdetalloAccount($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $source, $reference)
    {
        //create acdetallo record
        $cbmast = Cbmast::where('offcd', $offcd)
            ->where('doc_type', $doc_type)
            ->where('dtrans_no', $dtrans_no)
            ->where('account_year', $year)
            ->where('account_month', $month)
            ->where('entry_type_descr', $entry)
            ->first();

        $records = Acdetallo::where('reference', $reference)
            ->count();
        $count = $records + 1;

        if ($doc_type == $entry) {
            $old_reference = $cbmast->cancelled_reference;
            $acdet_rec = Acdet::where('reference', $old_reference)
                ->where('doc_type', $cbmast->doc_type)
                ->where('entry_type_descr', $cbmast->orig_entry_type_descr)
                ->get()[0];

            //deallocate debits allocated to the receipt
            $acdetallo_rec = Acdetallo::where('reference', $acdet_rec->reference)
                ->where('doc_type', $doc_type)
                //->where('amount','<>',0)
                ->get();

            foreach ($acdetallo_rec as $revs_allo) {

                $acdetallo_upd = Acdetallo::where('reference', $acdet_rec->reference)
                    ->where('doc_type', $acdet_rec->doc_type)
                    ->where('item_no', $revs_allo->item_no)
                    ->where('endt_renewal_no', $revs_allo->endt_renewal_no)
                    ->update([
                        'amount' => 0,
                        'balance_before' => $revs_allo->balance_before - $revs_allo->amount,
                        'balance_after' => $revs_allo->balance_after + $revs_allo->amount,
                        'ref_balance_before' => $revs_allo->ref_balance_before + $revs_allo->amount,
                        'ref_balance_after' => $revs_allo->ref_balance_after - $revs_allo->amount,
                        'foreign_amount' => 0,
                        'foreign_balance_before' => $revs_allo->foreign_balance_before - $revs_allo->foreign_amount,
                        'foreign_balance_after' => $revs_allo->foreign_balance_after + $revs_allo->foreign_amount,
                        'foreign_ref_balance_before' => $revs_allo->foreign_ref_balance_before + $revs_allo->foreign_amount,
                        'foreign_ref_balance_after' => $revs_allo->foreign_ref_balance_after - $revs_allo->foreign_amount,
                        'deallocated_date' => Carbon::now()
                    ]);

                //deallocate debit notes in acdet and commdet
                $acdet_drn = Acdet::where('doc_type', $revs_allo->ref_doc_type)
                    ->where('source', 'U/W')
                    ->where('endt_renewal_no', $revs_allo->endt_renewal_no)
                    ->get()[0];

                $comm_earned = $revs_allo->amount / $acdet_drn->nett * $acdet_drn->comm_amt;

                if (abs($comm_earned) > abs($acdet_drn->comm_earned)) {
                    $s_comm_earned = $acdet_drn->comm_earned * -1;
                } else {
                    $s_comm_earned = $comm_earned;
                }

                //update acdet
                $acdet_drn_upd = Acdet::where('doc_type', $revs_allo->ref_doc_type)
                    ->where('source', 'U/W')
                    ->where('endt_renewal_no', $revs_allo->endt_renewal_no)
                    ->update([
                        'allocated' => $acdet_drn->allocated + $revs_allo->amount,
                        'foreign_allocated' => $acdet_rec->foreign_allocated + ($revs_allo->amount / $acdet_drn->currency_rate),
                        'unallocated' => $acdet_drn->unallocated - $revs_allo->amount,
                        'foreign_unallocated' => $acdet_drn->foreign_unallocated - ($revs_allo->amount / $acdet_drn->currency_rate),
                        'comm_earned' => $acdet_drn->comm_earned + $s_comm_earned
                    ]);

                //update commdet
                $commdet_upd = Commdet::where('doc_type', $revs_allo->ref_doc_type)
                    ->where('endt_renewal_no', $revs_allo->endt_renewal_no)
                    ->update([
                        'allocated_amount' => $acdet_drn->allocated + $revs_allo->amount,
                        'unallocated_amount' => $acdet_drn->unallocated - $revs_allo->amount,
                        'comm_earned' => $acdet_drn->comm_earned + $s_comm_earned,
                        'req' => 'N'
                    ]);
            }

            //allocate receipt to reversal
            //update acdet rec and reversal records record
            $acdet_rec_upd = Acdet::where('reference', $old_reference)
                ->where('doc_type', $cbmast->doc_type)
                ->update([
                    'allocated' => $acdet_rec->nett,
                    'foreign_allocated' => $acdet_rec->foreign_net,
                    'unallocated' => 0,
                    'foreign_unallocated' => 0,
                    'comm_earned' => 0,
                ]);

            $records = Acdetallo::where('reference', $reference)
                ->count();

            $acdetallo = Acdetallo::create([
                'branch' => $acdet_rec->branch,
                'agent' => $acdet_rec->agent,
                'doc_type' => $doc_type,
                'reference' => $acdet_rec->reference,
                'ln_no' => $acdet_rec->ln_no,
                'item_no' => $count,
                'ref_doc_type' => $cbmast->doc_type,
                'ref_reference' => $reference,
                'ref_ln_no' => $cbmast->ln_no,
                'dr_cr' => 'C',
                'amount' => $acdet_rec->nett,
                'balance_before' => $acdet_rec->nett,
                'balance_after' => 0,
                'ref_balance_before' => $cbmast->amount,
                'ref_balance_after' => 0,
                'account_year' => $cbmast->account_year,
                'account_month' => $cbmast->account_month,
                'dola' => $cbmast->effective_date,
                'amount_orig' => $acdet_rec->nett,
                'foreign_amount' => $acdet_rec->foreign_net,
                'foreign_amount_orig' => $acdet_rec->foreign_net,
                'foreign_balance_before' => $acdet_rec->foreign_net,
                'foreign_balance_after' => 0,
                'foreign_ref_balance_before' => $cbmast->foreign_amount,
                'foreign_ref_balance_after' => 0,
                'currency_code' => $cbmast->currency_code,
                'currency_rate' => $cbmast->currency_rate,
                'allocation_date' => Carbon::now()
            ]);
        }


        return $acdetallo;
    }
    public function allocateFac($offcd, $doc_type, $dtrans_no, $year, $month, $entry){

     
        
        
        //GET CBMAST
        $cbmast = Cbmast::where('offcd', $offcd)
                        ->where('doc_type', $doc_type)
                        ->where('dtrans_no', $dtrans_no)
                        ->where('account_year', $year)
                        ->where('account_month', $month)
                        ->where('entry_type_descr', $entry)                      
                        ->first(); 

    
                      
        $reference=str_pad($cbmast->dtrans_no,6,0,STR_PAD_LEFT).$cbmast->account_year;         
           
        $req_no=$cbmast->dept_code.$cbmast->payrqst_no;

       
        $payreqst=Payreqst::where('req_no',$req_no)
                            ->get()[0];


        //GET POLANA RECORDS
        if($doc_type==$entry){
            //REVERSAL
            $polana=Polana::where('doc_type',$doc_type)
                    ->where('entry_type_descr',$cbmast->entry_type_descr1)
                    ->where('req_no',$req_no)
                    ->where('offcd',$offcd)
                    ->get();
            $dr_cr='C';

           
            
        }else{
            //ORIGINAL PAYMENT
            $polana=Polana::where('doc_type',$payreqst->doc_type)
                        ->where('entry_type_descr',$payreqst->entry_type_descr)
                        ->where('req_no',$req_no)
                        ->where('offcd',$offcd)
                        ->get();
            $dr_cr='D';
        }
       

                        
        //LOOP THROUGH POLANA
            foreach($polana as $pol){
            //ADD TO POLANA WITH CBMAST DETAILS
            $count=Polana::where('doc_type',$cbmast->doc_type)
                        ->where('entry_type_descr',$cbmast->entry_type_descr)
                        ->where('reference',$reference)
                        ->where('offcd',$offcd)
                        ->count();

         
                 
                 $create_polana=[
                    'offcd'=>$cbmast['offcd'],
                    'req_no'=>$req_no,
                    'doc_type'=>$cbmast->doc_type,
                    'entry_type_descr'=>$cbmast['entry_type_descr'],
                    'entry_type_descr1'=>$cbmast['entry_type_descr1'],
                    'reference'=>$reference,
                    'ln_no'=>$cbmast['ln_no'],
                    'line_no'=>$cbmast['line_no'],
                    'policy_no'=>$pol['policy_no'],
                    'endt_renewal_no'=>$pol['endt_renewal_no'],
                    'item_no'=>$count+1,
                    'dr_cr'=>$dr_cr,                  
                    'unallocated_amount'=>0,                   
                    'branch'=>$cbmast['branch'],
                    'agent'=>$cbmast['agent_no'],
                    'client_number'=>$pol['client_number']
                ];
                if($doc_type==$entry){
                    $create_polana['ref_dtrans_no']=substr($cbmast->cancelled_reference,0,6);
                    $create_polana['ref_account_year']= substr($cbmast->cancelled_reference,6,4);
                    $create_polana['ref_doc_type']=$cbmast->doc_type;

                    $create_polana['total_amount']=$cbmast['foreign_gross_amount']*-1;
                    $create_polana['policy_amount']=$pol['policy_amount'] *-1;
                    $create_polana['allocated_amount']=$pol['policy_amount'] *-1;

                }else{
                    $create_polana['ref_dtrans_no']=$pol->ref_dtrans_no;
                    $create_polana['ref_account_year']=$pol->ref_account_year;
                    $create_polana['ref_doc_type']=$pol->ref_doc_type;

                    $create_polana['total_amount']=$cbmast['foreign_gross_amount'];
                    $create_polana['policy_amount']=$pol['policy_amount'] ;
                    $create_polana['allocated_amount']=$pol['policy_amount'] ;
                    

                }

                $insert_polana=Polana::insert($create_polana);
                
              
        }
       



    }
    public function addFacToAcdet($offcd, $doc_type, $dtrans_no, $year, $month, $entry)
    {
        $reference = STR_PAD($dtrans_no, 6, '0', STR_PAD_LEFT) . $year;
        $polana_pay = Polana::where('doc_type', $doc_type)
                    ->where('offcd', $offcd)
                    ->where('entry_type_descr', $entry)
                    ->where('reference', $reference)
                    ->get();
           
          

        $cbmast = Cbmast::where('offcd', $offcd)
                ->where('doc_type', $doc_type)
                ->where('dtrans_no', $dtrans_no)
                ->where('account_year', $year)
                ->where('account_month', $month)
                ->where('entry_type_descr', $entry)
                ->first();


        foreach ($polana_pay as $polana) {
        
            $ln_no = Acdet::where('reference', $reference)
                    ->where('doc_type', $doc_type)
                    ->where('entry_type_descr', $entry)
                    ->count();
            $ln_no = $ln_no + 1;


            $polmaster = Polmaster::where('policy_no', $polana->policy_no)
                        ->first();

            /*if ($cbmast->entry_type_descr== $cbmast->doc_type) {
                $source_code =trim($cbmast->source_code);
            } else {*/
                $source_code ='CRD';
            //}

            $ref_reference=str_pad($polana->ref_dtrans_no,0,6,STR_PAD_LEFT).$polana->ref_account_year;
            $acdet = Acdet::where('doc_type', $polana->ref_doc_type)
                            ->where('endt_renewal_no', $polana->endt_renewal_no)
                            ->where('reference', $ref_reference)
                            ->whereRaw("trim(source)=' ".$source_code." '")
                            ->first();

            $acdet_data = [
                'ln_no' => $ln_no,
                'line_no' => $cbmast->line_no,
                'branch' => $polana->branch,
                'agent' => $polana->agent,
                'class' => $acdet->class,
                'account_year' => $cbmast->account_year,
                'account_month' => $cbmast->account_month,
                'doc_type' => $cbmast->doc_type,
                'reference' => $reference,
                'date_effective' => $cbmast->effective_date,
                'date_processed' => $cbmast->effective_date,
                'client_number' => $acdet->client_number,
                'policyholder' => trim($polmaster->name),
                'currency_code' => $cbmast->currency_code,
                'currency_rate' => $cbmast->currency_rate,
                'entry_type_descr' => $polana->entry_type_descr,
                'entry_type_descr1' => $polana->entry_type_descr1,
                'policy_no' => $polana->policy_no,
                'endt_renewal_no' => $polana->endt_renewal_no,
                'cheque_no' => trim($cbmast->cheque_no),
                'ref_doc' => $cbmast->ref_doc,
                'ref_doc_type' => $polana->ref_doc_type,
                'orig_entry_type_descr' => $cbmast->orig_entry_type_descr,
                'nett' => $polana->policy_amount,
                'foreign_net' => $polana->policy_amount / $cbmast->currency_rate,
                'unit_leader_id' => $acdet->unit_leader_id,
                'region_id' => $acdet->region_id,
                'unit_id' => $acdet->unit_id,
                'channel' => $acdet->channel,
                'override_rate' => $acdet->override_rate,
                'calculate_on' => $acdet->calculate_on,
                'expense_on' => $acdet->expense_on,
                /*'override_payable'=>$acdet->override_payable,
                'gross_comm_amt'=>$acdet->gross_comm_amt*/
            ];

            $cbtrans = Cbtrans::where('doc_type', $doc_type)
                ->where('descr', $entry)
                ->first();

            $acdet_data['allocated'] = $polana->policy_amount ;
            $acdet_data['foreign_allocated'] = $polana->policy_amount/$cbmast->currency_rate;
            $acdet_data['unallocated'] = $polana->unallocated_amount;
            $acdet_data['foreign_unallocated'] = $polana->unallocated_amount/$cbmast->currency_rate;
           
            //other fields
            $acdet_data['uw_year'] = $cbmast->account_year;
            $acdet_data['agency_no'] = $cbmast->agent_no;
            $acdet_data['orig_entry_type_descr'] = $cbmast->orig_entry_type_descr;
            $acdet_data['unallocatedi'] = $acdet['unallocated'];
            $acdet_data['cr_account'] = $cbmast->credit_account;
            $acdet_data['dr_account'] = $cbmast->debit_account;
            $acdet_data['dola'] = $cbmast->effective_date;
            $acdet_data['time'] = $cbmast->created_time;
            $acdet_data['type'] = 10;
            $acdet_data['balance_code'] = 'Z';
            $acdet_data['trans_number'] = $dtrans_no;
            $acdet_data['source'] = 'CB';
            $acdet_data['debit_nos'] = $polana->ref_dtrans_no;
            $acdet_data['user_1'] = $cbmast->created_by;
            $acdet_data['dr_cr'] = $polana->dr_cr;

            //dd($acdet_data);

            $acdet_create = Acdet::insert($acdet_data);

            if ($acdet_create) {
                $r_reference;
                if ($cbmast->doc_type == "REC") {
                    $r_reference = $cbmast->debit_note_no;
                } else {
                    $r_reference = $cbmast->credit_note_no;
                }
               // $acdet = $this->allocateRec($acdet_data['endt_renewal_no'], $doc_type, $reference, $acdet_data['allocated'], $acdet_data['foreign_allocated'], $acdet_data['comm_earned'], $cbmast->amount, $entry, $acdet_data['override_earned'], $cbmast->source_code, $r_reference);

                //UPDATE ACDET MANUALLY
                $ref_doc = 'CRN';
                $acdet = Acdet::where('doc_type', 'CRN')
                    ->where('reference', 'LIKE', '%' . $r_reference . '%')
                    ->where('source', 'CRD')
                    ->where('endt_renewal_no', $acdet_data['endt_renewal_no'])
                    ->where('branch', $acdet_data['branch'])
                    ->where('agent', $acdet_data['agent'])
                    ->first();
            
                // $foreign_allocated = $acdet->foreign_allocated;
                // $unallocated = $acdet->unallocated;
                // $foreign_unallocated = $acdet->foreign_unallocated;
                
              
                $allocated = $acdet->allocated - $acdet_data['allocated'];
                $foreign_allocated = $acdet->foreign_allocated - $acdet_data['foreign_allocated']; 
                $unallocated = $acdet->unallocated + $acdet_data['allocated'];

                $foreign_unallocated = $acdet->foreign_unallocated+ $acdet_data['foreign_allocated']; 

               
                
                Acdet::where('endt_renewal_no', $acdet_data['endt_renewal_no'])
                                ->where('reference', 'LIKE', '%' . $r_reference . '%')
                                ->where('source', 'CRD')
                                ->where('doc_type', 'CRN')
                                ->where('branch', $acdet_data['branch'])
                                ->where('agent', $acdet_data['agent'])
                                ->update([
                                    'allocated' => $allocated,
                                    'foreign_allocated' => $foreign_allocated,
                                    'unallocated' => $unallocated,
                                    'foreign_unallocated' => $foreign_unallocated,
                                  
                                ]);

                //END OF UPDATE ACDET MANUALY


                $reacdetallo = $this->createReinAcdetallo($offcd, $doc_type, $dtrans_no, $year, $month, $entry, $acdet_data['allocated'], $g_allocatedd, $g_allocatedf, $acdet_data['endt_renewal_no'], $cbmast->source_code);
              // 
                /*$acdet_now = Acdet::where('reference',$reference)
                            //->where('endt_renewal_no',$acdet_data['endt_renewal_no'])
                            ->get();
                    dd($acdet_now); */
            }
        } //end foreach polana
    }
} //end of class
