<?php
namespace App\Classes\gl\fo;
use Carbon\Carbon;
use Auth;
use DB;

use App\Clpmn;
use App\Clhmn;
use App\Cbmast;
use App\Clhmnallo;
use App\Cbtrans;
use App\Clremast;
use App\Claimant;
use App\Clmhist;
use App\Clstatus;
use App\Creditclm;
use App\Pipcnam;
use App\Reinsetup;
use App\ClassModel;
use App\Classyear;
use App\Polremast;
use App\Reinclass;
use App\Bustype;
use App\Cbmastmult;
use App\Payreqst;
use App\Salvages;

use App\Perilsitems;
use App\Debitmast;
use App\Polmaster;
use App\Reintray;
use App\Clpmnperildtl;
use App\Clestdtl;
use App\Dtran0;
use App\Events\ClaimProcessedEvent;
use App\Events\BlacklistVehicles;
use App\Http\Controllers\gb\claims\ClaimsController;

Class ClaimsClass {

public function updateClaimPayment($offcd,$doc_type,$dtrans_no,$year,$month,$entry)
{
    $today = Carbon::today();
    $user = Auth::user()->user_name;
    $time = Carbon::now();

    $cbmast = Cbmast::where('offcd',$offcd)
                    ->where('doc_type',$doc_type)
                    ->where('dtrans_no',$dtrans_no)
                    ->where('account_year',$year)
                    ->where('account_month',$month)
                    ->where('entry_type_descr',$entry)
                    ->first();
                    
    $clhmn = Clhmn::where('claim_no',$cbmast->claim_no)->first();

    $rein = $this->reinsEnquire($clhmn);

    $ln_no = Clpmn::where('dtrans_no',$cbmast->dtrans_no)
                    ->where('account_year',$cbmast->account_year)
                    ->where('doc_type',$cbmast->doc_type)
                    ->count();

    $cbtrans = Cbtrans::where('doc_type',$cbmast->doc_type)
                            ->where('descr',$cbmast->entry_type_descr)
                            ->first();
        if($cbtrans){
            $flag = $cbtrans->excess_flag;
            //dd($cbmast->slhead);
            if($flag == "Y" && trim($cbmast->slhead) == 01){
                $entry_type = 'EXC';
               // dd("here");
            }
            else{
                $entry_type = $cbmast->entry_type_descr;
            }
        }
        else{
            $entry_type = $cbmast->entry_type_descr;
        }
        //dd($entry_type);

        if($cbmast->final_settle == 'Y'){
            $status = 'SE';
        }
        else{ $status = ''; }

    if(trim($cbmast->slhead) == "01"){ //receipts
        $pay_type = 20;

        if($clhmn->currency_code == $cbmast->currency_code){
            $currency_rate = $cbmast->currency_rate;
            $pay_amount = $cbmast->foreign_gross_amount*-1;
            $amount = $cbmast->gross_amount*-1;
            $local_amount = $cbmast->gross_amount*-1;
        }
        else{
            $currency_rate = 1;
            $pay_amount = $cbmast->foreign_gross_amount*-1;
            $amount = $cbmast->gross_amount*-1;
            $local_amount = $cbmast->gross_amount*-1;
        }

        if($entry == "REC"){ //receipt reversal
            if($clhmn->currency_code == $cbmast->currency_code){
                $currency_rate = $cbmast->currency_rate;
                $pay_amount = $cbmast->foreign_gross_amount;
                $amount = $cbmast->gross_amount;
                $local_amount = $cbmast->gross_amount;
            }
            else{
                $currency_rate = 1;
                $pay_amount = $cbmast->foreign_gross_amount;
                $amount = $cbmast->gross_amount;
                $local_amount = $cbmast->gross_amount;
            }
        }
        // dd($pay_amount);
    }
    else if(trim($cbmast->slhead) == "02"){
       $pay_type = 10; 
        if($clhmn->currency_code == $cbmast->currency_code){
            $currency_rate = $cbmast->currency_rate;
            $pay_amount = $cbmast->foreign_gross_amount;
            $amount = $cbmast->gross_amount;
            $local_amount = $cbmast->gross_amount;
        }
        else{
            $currency_rate = 1;
            $pay_amount = $cbmast->foreign_gross_amount;
            $amount = $cbmast->gross_amount;
            $local_amount = $cbmast->gross_amount;
        }
        if($entry == "PAY"){ //payment reversal
           if($clhmn->currency_code == $cbmast->currency_code){
                $currency_rate = $cbmast->currency_rate;
                $pay_amount = $cbmast->foreign_gross_amount*-1;
                $amount = $cbmast->gross_amount*-1;
                $local_amount = $cbmast->gross_amount*-1;
            }
            else{
                $currency_rate = 1;
                $pay_amount = $cbmast->foreign_gross_amount*-1;
                $amount = $cbmast->gross_amount*-1;
                $local_amount = $cbmast->gross_amount*-1;
            } 
        }
    }
    if($entry == $doc_type and trim($cbmast->slhead) == "02"){
        $g_doc_type = 'REV';
    }
    else {
        $g_doc_type = $doc_type;
    }
    //dd($g_doc_type);

    //clhmn details
    $clhmn1 = Clhmn::where('claim_no',$cbmast->claim_no)->first();
    if($clhmn1){
            $local_amount_prev = $clhmn1->local_curr_total_estimate;
            $temp_amount_prev = $clhmn1->curr_total_estimate;
            $temp_amount = $clhmn1->curr_total_estimate;
            $temp_payment = $clhmn1->total_payment;
            $temp_recovery = $clhmn1->total_recovery;
            $local_curr_total_estimate = $clhmn1->local_curr_total_estimate;
            $local_cost_todate = $clhmn1->local_cost_todate;
            $local_total_payment = $clhmn1->local_total_payment;
            $paid_excess = $clhmn1->paid_excess;
            $local_paid_excess = $clhmn1->local_paid_excess;
            $status = $clhmn1->status;
            $date_settled = $clhmn1->date_settled;
            $local_total_reins_recovery = $clhmn1->local_total_reins_recovery;

            if($pay_type == 10){
                $temp_amount = $temp_amount-$pay_amount;
                $temp_payment = $temp_payment+$pay_amount;
                $local_total_payment = $clhmn1->local_total_payment+$local_amount;
                $local_curr_total_estimate = $local_amount_prev-$local_amount;
                $local_cost_todate = $clhmn1->local_cost_todate+$local_amount;
            }
            elseif ($pay_type == 20) {
                $temp_recovery = $clhmn1->total_recovery+$pay_amount;
                $local_cost_todate = $clhmn1->local_cost_todate+$local_amount;
              
                if($entry_type == "EXC"){
                    $paid_excess = $paid_excess+$pay_amount;
                    $local_paid_excess = $local_paid_excess+$local_amount;
                }
            }
            elseif ($pay_type == 30) {
                $temp_amount = $pay_amount;
                $local_curr_total_estimate = $local_amount;
            }

            if($temp_amount < 0 ){
                $temp_amount = 0;
            }
            if($local_curr_total_estimate < 0){
                $local_curr_total_estimate = 0;
            }
            if($cbmast->final_settle == 'Y'){
                $status = 'SE';
                $date_settled = $clpmn->dola;
            }

            $estimate = $clhmn1->curr_total_estimate;
        } //end of clhmn details
    if($cbmast){
    $clpmn_data = [
        'ln_no' =>$ln_no,
        'claim_no'=>$cbmast->claim_no,
        'branch'=>$cbmast->branch,
        'agent'=>$cbmast->agent_no,
        'policy_no'=>$cbmast->policy_no,
        'class'=>$cbmast->class,
        'uw_year'=>$clhmn1->uw_year,
        'pay_date'=>$today,
        'effective_date'=>$today,
        'entry_type_descr'=>$entry_type,
        'dtrans_no'=>$cbmast->dtrans_no,
        'tran_no'=>$cbmast->dtrans_no,
        'doc_type'=>$g_doc_type,
        'doc_ref'=>trim($cbmast->cheque_no),
        'currency_code'=>$cbmast->currency_code,
        'currency_rate'=>$cbmast->currency_rate,
        'claimant_code'=>$cbmast->claimant_code,
        'payee'=>$cbmast->name,
        'cheque_no'=>trim($cbmast->cheque_no),
        'cheque_date'=>$cbmast->cheque_date,
        'pay_amount'=>$pay_amount,
        'amount'=>$amount,
        'local_amount'=>$local_amount,
        'pay_type'=>$pay_type,
        'peril'=>$cbmast->peril,
        'account_month'=>$cbmast->account_month,
        'account_year'=>$cbmast->account_year,
        'user_str'=>$user,
        'pay_time'=>$time,
        'dola'=>$today,
        'reinsured'=>'N',
        'gl_updated'=>'Y',
        'orig_from'=>99,
        'source'=>'CB',
        'final_settle'=>$cbmast->final_settle,
        'user_str'=>$cbmast->created_by,
    ];

    $clpmn_data = $this->getClaimBalances($clpmn_data,$cbmast->payrqst_no);

    //allocate receipt to claim debit notes
    if($cbmast->doc_type == 'REC'){
        //dd($cbmast)
        $allocate_rec = $this->allocateReceipt($cbmast->dtrans_no,$cbmast->entry_type_descr,$cbmast->account_year,$cbmast->account_month,$cbmast->offcd);
    }

}//end if cbmast
    return $clpmn_data;
} //end of updateClaimPayment

    public function getClaimBalances($clpmn_data,$payrqst_no)
    {
         //clhmn details
    $today = Carbon::today();
    $user = Auth::user()->user_name;
    $time = Carbon::now();

    $req_dtrans = (int)substr($payrqst_no, 0,6);
    $req_year = (int)substr($payrqst_no, 6,4);
    $req_month = (int)substr($payrqst_no, 10,2);

    $payreqst = Payreqst::where('dtrans_no',$req_dtrans)
                        ->where('account_year',$req_year)
                        ->where('account_month',$req_month)
                        ->get();

    $payreqst = $payreqst[0];


    $clhmn1 = Clhmn::where('claim_no',$clpmn_data['claim_no'])->first();
    $rein = $this->reinsEnquire($clhmn1);

    if($clhmn1){
            $local_amount_prev = $clhmn1->local_curr_total_estimate;
            $temp_amount_prev = $clhmn1->curr_total_estimate;
            $temp_amount = $clhmn1->curr_total_estimate;
            $temp_payment = $clhmn1->total_payment;
            $temp_recovery = $clhmn1->total_recovery;
            $local_curr_total_estimate = $clhmn1->local_curr_total_estimate;
            $local_cost_todate = $clhmn1->local_cost_todate;
            $local_total_payment = $clhmn1->local_total_payment;
            $paid_excess = $clhmn1->paid_excess;
            $local_paid_excess = $clhmn1->local_paid_excess;
            $status = $clhmn1->status;
            $date_settled = $clhmn1->date_settled;
            $local_total_reins_recovery = $clhmn1->local_total_reins_recovery;

            if($clpmn_data['pay_type'] == 10){
                $temp_amount = $temp_amount-$clpmn_data['pay_amount'];
                $temp_payment = $temp_payment+$clpmn_data['pay_amount'];
                $local_total_payment = $clhmn1->local_total_payment+$clpmn_data['local_amount'];
                $local_curr_total_estimate = $local_amount_prev-$clpmn_data['local_amount'];
                $local_cost_todate = $clhmn1->local_cost_todate+$clpmn_data['local_amount'];
            }
            elseif ($clpmn_data['pay_type'] == 20) {
                $temp_recovery = $clhmn1->total_recovery+$clpmn_data['pay_amount'];
                $local_cost_todate = $clhmn1->local_cost_todate+$clpmn_data['local_amount'];
              
                if($clpmn_data['entry_type_descr'] == "EXC"){
                    $paid_excess = $paid_excess+$clpmn_data['pay_amount'];
                    $local_paid_excess = $local_paid_excess+$clpmn_data['local_amount'];
                }
            }
            elseif ($clpmn_data['pay_type'] == 30) {
                $temp_amount = $clpmn_data['pay_amount'];
                $local_curr_total_estimate = $clpmn_data['local_amount'];
            }

            if($temp_amount < 0 ){
                $temp_amount = 0;
            }
            if($local_curr_total_estimate < 0){
                $local_curr_total_estimate = 0;
            }
            if($clpmn_data['final_settle'] == 'Y'){
                $status = 'SE';
                $date_settled = $clpmn_data['dola'];
            }

            $estimate = $clhmn1->curr_total_estimate;
        } //end of clhmn details

        //autoclose claim file
            if(($temp_amount == 0)&& (($temp_payment+$temp_recovery)>0) ){

            $autocloseclaim =  Clhmn::where('claim_no',$clpmn_data['claim_no'])
                    ->update([
                        'closed'=>'Y',
                        'closed_by'=> trim(Auth::user()->user_name),
                        'closed_date'=> Carbon::now()
                    ]);
            }
        //end of autoclose file

        //update clhmn
        $clhmn = Clhmn::where('claim_no',$clpmn_data['claim_no'])
                ->update([
            'currency_rate'=>$clhmn1->currency_rate,
            'local_amount_prev'=>$local_amount_prev,
            'temp_amount_prev'=>$temp_amount_prev,
            'temp_amount'=>$temp_amount,
            'temp_payment'=>$temp_payment,
            'temp_recovery'=>$temp_recovery,
            'local_cost_todate'=>$local_cost_todate,
            'local_total_payment'=>$local_total_payment,
            'local_curr_total_estimate'=>$local_curr_total_estimate,
            'total_payment'=>$temp_payment,
            'curr_total_estimate'=>$temp_amount,
            'total_recovery'=>$temp_recovery,
            'cost_todate'=>$temp_payment+$temp_recovery,
            'paid_excess'=>$paid_excess,
            'local_paid_excess'=>$local_paid_excess,
            'amend_mkr'=>1,
            'dola'=>$clpmn_data['dola'],
            // 'tran_no'=>$clpmn_data['dtrans_no'],
            'status'=>$status,
            'date_settled'=>$date_settled,
        ]);


            $clpmn_data['status'] = $status;
            $clpmn_data['curr_total_estimate'] = $temp_amount;
            $clpmn_data['new_total_estimate'] = $temp_amount;
            $clpmn_data['prev_total_estimate'] = $estimate;
            $clpmn_data['prev_estimate_amount'] = $estimate;
            $clpmn_data['movt_total_estimate'] = $estimate-$temp_amount;
            $clpmn_data['balance'] = $temp_amount;
            $clpmn_data['effective_claim'] = $temp_amount-$temp_amount_prev;
            $clpmn_data['temp_recovery'] = $temp_recovery;
            $clpmn_data['temp_amount'] = $temp_amount;
            $clpmn_data['temp_payment'] = $temp_payment;
            $clpmn_data['payments_todate'] = $temp_payment;
            $clpmn_data['cost_todate'] = $temp_payment+$temp_recovery;
            $clpmn_data['local_amount'] = $clpmn_data['pay_amount']*$clpmn_data['currency_rate'];
            $clpmn_data['local_effective_claim'] = $local_curr_total_estimate-$local_amount_prev;

              /*Reice*/
            $clpmn_data['mandatory_reice'] = $rein['polremast']->mandatory_reice;
            $clpmn_data['company_reice'] = $rein['polremast']->company_retention_reice;
            $clpmn_data['quota_share_reice'] = $rein['polremast']->quota_share_reice;
            $clpmn_data['surplus_reice_1'] = $rein['polremast']->surplus_reice_1;
            $clpmn_data['surplus_reice_2'] = $rein['polremast']->surplus_reice_2;
            $clpmn_data['surplus_reice_3'] = $rein['polremast']->surplus_reice_3;
            $clpmn_data['group_reice'] = $rein['polremast']->group_reice;
            $clpmn_data['facob_reice'] = $rein['polremast']->facob_reice;
            $clpmn_data['facult_n_reice'] = $rein['polremast']->facult_n_reice;
            $clpmn_data['facult_p_reice'] = $rein['polremast']->facult_p_reice;

            /*Amounts*/ 
            $clpmn_data['mandatory_recovery'] = 0;
            $clpmn_data['company_recovery_1'] = 0;
            $clpmn_data['xloss_1_recovery_1'] = 0;
            $clpmn_data['xloss_1_recovery_2'] = 0;
            $clpmn_data['xloss_1_recovery_3'] = 0;
            $clpmn_data['xloss_2_recovery_1'] = 0;
            $clpmn_data['xloss_2_recovery_2'] = 0;
            $clpmn_data['xloss_2_recovery_3'] = 0;
            $clpmn_data['xloss_3_recovery_1'] = 0;
            $clpmn_data['xloss_3_recovery_2'] = 0;
            $clpmn_data['xloss_3_recovery_3'] = 0;
            $clpmn_data['quota_share_recovery'] = 0;
            $clpmn_data['surplus_1st_recovery'] = 0;
            $clpmn_data['surplus_2nd_recovery'] = 0;
            $clpmn_data['surplus_3rd_recovery'] = 0;
            $clpmn_data['group_recovery'] = 0;
            $clpmn_data['facob_recovery'] = 0;
            $clpmn_data['facult_n_recovery'] = 0;
            $clpmn_data['facult_p_recovery'] = 0;

            //dd($clpmn_data);

        //update claimant
        $claimant = Claimant::where('claim_no',$clpmn_data['claim_no'])
                                ->where('claimant_code',$clpmn_data['claimant_code'])
                                ->where('line_no',$payreqst->line_no)
                                ->get();
                                
        if($claimant){
            //dd('here');
            foreach ($claimant as $key) {
                if($clpmn_data['pay_type'] == 10 && $clpmn_data['claimant_code'] == $key->claimant_code && $key->claim_no == $clpmn_data['claim_no'] && $key->policy_no == $clpmn_data['policy_no'])
                {
                    //dd($claimant);
                    if($clpmn_data['doc_type'] == 'REV'){
                        $status = 0;
                    }
                    else{
                        $status = 1;
                    }

                    $claimant_upd = Claimant::where('claim_no',$clpmn_data['claim_no'])
                                    ->where('claimant_code',$clpmn_data['claimant_code'])
                                    ->where('line_no',$payreqst->line_no)
                                    ->update([
                                        'last_date_paid'=>$clpmn_data['pay_date'],
                                        'paid_todate'=>$key->paid_todate+$clpmn_data['pay_amount'],
                                        'status'=>$status
                                    ]);
                    //dd($claimant_upd);
                }

            }
                
        } //end of claimant

        //dd($clpmn_data);

            if($clpmn_data['pay_type'] == 10 || $clpmn_data['pay_type'] == 20){
                $clestdtl = Clestdtl::where('claim_no',$clhmn1->claim_no)
                                    ->where('peril',$payreqst->peril)
                                    ->where('perilitem',$payreqst->perilitem)
                                    ->get();

                $clestdtl = $clestdtl[0];
                $amount = $clpmn_data['amount'];

                if(isset($clestdtl)){
                    $amount = ($clpmn_data['amount']*$clpmn_data['currency_rate'])/$clestdtl->currency_rate;
                }
            if($clestdtl){
                if($clpmn_data['pay_type'] == 10){

                $clestdtl_upd = Clestdtl::where('claim_no',$clhmn1->claim_no)
                                    ->where('peril',$payreqst->peril)
                                    ->where('perilitem',$payreqst->perilitem)
                                    ->update([
                                        'curr_estimate'=>$clestdtl->curr_estimate-$amount,
                                        'payments_todate'=>$clestdtl->payments_todate+$clpmn_data['local_amount'],
                                        'currency_code'=>$clhmn1->currency_code,
                                        'currency_rate'=>$clhmn1->currency_rate,
                                        'local_curr_estimate'=>$clestdtl->local_curr_estimate-$clpmn_data['local_amount'],
                                    ]);
                }
                else{
                    $clestdtl_upd = Clestdtl::where('claim_no',$clhmn1->claim_no)
                                    ->where('peril',$payreqst->peril)
                                    ->where('perilitem',$payreqst->perilitem)
                                    ->update([
                                        'recovery_todate'=>$clestdtl->recovery_todate-$clpmn_data['local_amount']
                                    ]);
                }
                (new ClaimsController)->UpdateCreateClestIRA($clhmn1->claim_no,$payreqst->peril,$payreqst->doc_type,$clpmn_data['dtrans_no']);

                $clestdtl = Clestdtl::where('claim_no',$clhmn1->claim_no)
                                    ->where('peril',$payreqst->peril)
                                    ->where('perilitem',$payreqst->perilitem)
                                    ->get();

                $clestdtl = $clestdtl[0];

                //update clpmnperildtl
                $insert_clpmnperildtl = new Clpmnperildtl;
                $insert_clpmnperildtl->claim_no = $clhmn1->claim_no;
                $insert_clpmnperildtl->policy_no = $clhmn1->policy_no;
                $insert_clpmnperildtl->doc_type = $clpmn_data['doc_type'];
                $insert_clpmnperildtl->entry_type_descr = $clpmn_data['entry_type_descr'];
                $insert_clpmnperildtl->pay_type = $clpmn_data['pay_type'];
                $insert_clpmnperildtl->class = $clhmn1->class;
                $insert_clpmnperildtl->pay_amount = $clpmn_data['pay_amount'];
                $insert_clpmnperildtl->amount = $clpmn_data['amount'];
                $insert_clpmnperildtl->pay_date = Carbon::today();
                $insert_clpmnperildtl->dola = Carbon::today();
                $insert_clpmnperildtl->payee = trim($clpmn_data['payee']);
                $insert_clpmnperildtl->dtrans_no = $clpmn_data['dtrans_no'];
                $insert_clpmnperildtl->account_year = $clpmn_data['account_year'];
                $insert_clpmnperildtl->account_month = $clpmn_data['account_month'];
                $insert_clpmnperildtl->peril = $payreqst->peril;
                $insert_clpmnperildtl->user_str = trim($clpmn_data['user_str']);
                $insert_clpmnperildtl->pay_time = Carbon::now();
                $insert_clpmnperildtl->branch = $clhmn1->branch;
                $insert_clpmnperildtl->agent = $clhmn1->agent_no;
                $insert_clpmnperildtl->effective_date = $clhmn1->date_reg;
                $insert_clpmnperildtl->balance = $clestdtl->local_curr_estimate;
                $insert_clpmnperildtl->payments_todate = $clestdtl->payments_todate;
                $insert_clpmnperildtl->uw_year = $clhmn1->uw_year;
                $insert_clpmnperildtl->location =$clhmn1->location;
                $insert_clpmnperildtl->section = $clhmn1->section;
                $insert_clpmnperildtl->curr_total_estimate = $clestdtl->curr_estimate;
                $insert_clpmnperildtl->closing_balance = $clestdtl->local_curr_estimate;
                $insert_clpmnperildtl->perilitem = $payreqst->perilitem;
                $insert_clpmnperildtl->ln_no = $clpmn_data['line_no'];
                $insert_clpmnperildtl->currency_code = $clpmn_data['currency_code'];
                $insert_clpmnperildtl->currency_rate = $clpmn_data['currency_rate'];
                $insert_clpmnperildtl->local_amount = $clpmn_data['local_amount'];
                $insert_clpmnperildtl->local_curr_estimate = $clestdtl->local_curr_estimate;
                $insert_clpmnperildtl->claimant_code = $clpmn_data['claimant_code'];
                $insert_clpmnperildtl->cheque_no = trim($clpmn_data['cheque_no']);
                $insert_clpmnperildtl->cheque_date = $clpmn_data['cheque_date'];
                $insert_clpmnperildtl->final_settle = $clpmn_data['final_settle'];
                $insert_clpmnperildtl->source = trim($clpmn_data['source']);
                $insert_clpmnperildtl->ln_no = trim($clpmn_data['ln_no']);
                $insert_clpmnperildtl->save();
            }
            }

        //create clmhist
        if($clpmn_data['final_settle'] == 'Y' && $clpmn_data['doc_type'] != 'REV'){

            ##update claim history
			$clmhistData = [
				'claim_no' =>$clpmn_data['claim_no'],
				'slug' => 'claim-settled',
				'overide_status_desc' => 'N',
				'additional_comment' => null
			];
			ClaimProcessedEvent::dispatch((object)$clmhistData);

        }
        else{
            if($clpmn_data['pay_type'] == 10){
                if($clpmn_data['doc_type'] != 'REV'){
                    $status_code = 910;
                } 
                else{
                    $status_code = 911;
                }
            }
            else if($clpmn_data['pay_type'] == 20){
                $status_code = 920;
            }
            else if($clpmn_data['pay_type'] == 30){
                $status_code = 930;
            }
            $clstatus = Clstatus::where('status_code',$status_code)->first();

            ##update claim history
			$clmhistData = [
				'claim_no' =>$clpmn_data['claim_no'],
				'slug' => 'claim-revision-made',
				'overide_status_desc' => 'N',
				'additional_comment' => 'Claim Adjusted Made After Payment'
			];
			ClaimProcessedEvent::dispatch((object)$clmhistData);
        } //end of clmhist

        //update clremast
        $clremast =  Clremast::where('claim_no',$clpmn_data['claim_no'])->first();
        if($clremast){
            if($clpmn_data['balance'] < 0){
                $clremast_estimate = 0;
            }
            else{
                $clremast_estimate = $clpmn_data['balance'];
            }
            $clremast_upd = Clremast::where('claim_no',$clpmn_data['claim_no'])->update([
                'payments_todate'=>$clpmn_data['temp_payment'],
                'curr_total_estimate'=>$clremast_estimate,
                'prev_outstanding'=>$clpmn_data['balance']-$clpmn_data['effective_claim'],
            ]);
        } //end of clremast


        //create clpmn
        $clpmn = Clpmn::insert($clpmn_data);

        ##check for motor class 
        $class = Classmodel::where('class',$clhmn1->class)->first();
     
        $perilitem = Perilsitems::where('peril',$payreqst->peril)
                    ->where('item_no',$payreqst->perilitem)
                    ->where('own_damage','Y')
                    ->count();
        ##blacklist vehicle
        if($class->motor_policy == 'Y' && $clpmn_data['pay_type'] == 10 && $perilitem > 0 ){

            ## sum all payments under OD
            // $amount = DB::select(DB::raw("
            // SELECT sum(AMOUNT) as amount  FROM PAYREQST p
            // left JOIN PERILSITEMS d ON p.PERIL  = d.PERIL AND p.PERILITEM  = d.ITEM_NO
            // WHERE d.OWN_DAMAGE  = 'Y' and p.claim_no = '$clhmn1->claim_no'
            // "));

            $amount = Payreqst::leftJoin('PERILSITEMS as d', function($join) {
                $join->on('payreqst.PERIL', '=', 'd.PERIL')
                    ->on('payreqst.PERILITEM', '=', 'd.ITEM_NO');
            })
            ->selectRaw('sum(AMOUNT) as amount')
            ->where('d.OWN_DAMAGE', 'Y')
            ->where('payreqst.claim_no', $clhmn1->claim_no)
            ->get();
            
            ## compute percentage payment for OD

            #total payment
            $percent = ((int)$amount[0]->amount/$clhmn1->sum_insured)*100;

            if( $percent >= 60 ){

                $blacklist = [
                    'policy_no' => $clhmn1->policy_no,
                    'endt_renewal_no' => $clhmn1->endt_renewal_no,
                    'claim_no' => $clhmn1->claim_no,
                    'reg_no' => $clhmn1->reg_no,
                ];
                BlacklistVehicles::dispatch((object)$blacklist);
                
            }


        }
        


        return $clpmn;

            //dd($clpmn_data);
    }

    public function reinsEnquire($clhmn_item){
        $rein = [];
        $rein['debitmast'] = Debitmast::Where('endt_renewal_no','=',$clhmn_item['endt_renewal_no'])->first();
        $rein['pipcnam'] = Pipcnam::All()->first();
        $rein['polmaster'] = Polmaster::Where('policy_no','=',$clhmn_item['policy_no'])->first();
        $rein['classyear'] = classyear::Where([
            ['class','=',$clhmn_item['class']],
            ['uw_year','=',$rein['debitmast']->uw_year]
        ])->first();
        $rein['polremast'] = Polremast::Where([
            ['endt_renewal_no','=',$rein['debitmast']->endt_renewal_no]
            //['location','=',$clhmn_item['location']]
        ])->first();

        $rein['reinsetup'] = Reinsetup::Where([
            ['uw_year','=',$rein['debitmast']->uw_year],
            ['class','=',$rein['classyear']->reinclass]
        ])->first();
        $rein['reintray'] = Reintray::Where([
            ['location','=',$clhmn_item['location']],
            ['endt_renewal_no','=',$rein['debitmast']->endt_renewal_no]
            ])->first();
        $rein['clremast'] = Clremast::Where('claim_no','=',$clhmn_item['claim_no'])->first();
        return $rein;
    }

    public function clpmnClhmnallo($offcd,$doc_type,$dtrans_no,$year,$month,$entry,$reference,$item_no=0)
    {
        $today = Carbon::today();
        $user = Auth::user()->user_name;
        $time = Carbon::now();

        $cbmast = Cbmast::where('offcd',$offcd)
                    ->where('doc_type',$doc_type)
                    ->where('dtrans_no',$dtrans_no)
                    ->where('account_year',$year)
                    ->where('account_month',$month)
                    ->where('entry_type_descr',$entry)
                    ->first();

        $clhmnallo_recs = Clhmnallo::where('doc_type',$doc_type)
                                ->where('reference',$reference)
                                ->where('entry_type_descr',$entry)
                                ->where('line_no',$cbmast->line_no)
                                // ->where('item_no',$item_no)
                                ->get();
                                
        $g_doc_type = $cbmast->doc_type;
        if(trim($cbmast->slhead) == "02" && $doc_type == $entry){
            $g_doc_type = "REV";
        }
       
        foreach($clhmnallo_recs as $clhmnallo){
            $clhmnallo_change = Clhmnallo::where('doc_type',$doc_type)
                                    ->where('reference',$reference)
                                    ->where('entry_type_descr',$entry)
                                    ->where('line_no',$cbmast->line_no) 
                                    ->update(['ln_no'=>$w_ln_no]);

            $clhmn = Clhmn::where('claim_no',$clhmnallo->claim_no)->first();
            $count = Clpmn::where('dtrans_no',$cbmast->dtrans_no)
            ->where('doc_type',$g_doc_type)
            ->where('account_year',$cbmast->account_year)
            ->count();
            $w_ln_no = $count+1;

            $clpmn_data = [
                'ln_no' =>$w_ln_no ,
                'claim_no'=>$clhmnallo->claim_no,
                'branch'=>$clhmn->branch,
                'agent'=>$clhmn->agent_no,
                'policy_no'=>$clhmn->policy_no,
                'class'=>$clhmn->class,
                'uw_year'=>$clhmn->uw_year,
                'pay_date'=>$today,
                'effective_date'=>$today,
                'entry_type_descr'=>$entry,
                'dtrans_no'=>$cbmast->dtrans_no,
                'tran_no'=>$cbmast->dtrans_no,
                'doc_type'=>$g_doc_type,
                'doc_ref'=>trim($cbmast->cheque_no),
                'currency_code'=>$cbmast->currency_code,
                'currency_rate'=>$cbmast->currency_rate,
                'claimant_code'=>$clhmnallo->claimant_code,
                'payee'=>$cbmast->name,
                'cheque_no'=>trim($cbmast->cheque_no),
                'cheque_date'=>$cbmast->cheque_date,
                'peril'=>$cbmast->peril,
                'account_month'=>$cbmast->account_month,
                'account_year'=>$cbmast->account_year,
                'user_str'=>$user,
                'pay_time'=>$time,
                'dola'=>$today,
                'reinsured'=>'N',
                'gl_updated'=>'Y',
                'orig_from'=>99,
                'source'=>'CB',
                'final_settle'=>$cbmast->final_settle,
            ];
            if(trim($cbmast->slhead) == "01"){ //receipts
                $cbtrans = Cbtrans::where('doc_type',$cbmast->doc_type)
                                ->where('descr',$cbmast->entry_type_descr)
                                ->first();
                if($cbtrans->excess_flag == "Y"){
                $clpmn_data['entry_type_descr'] = 'EXC';  
                }
                else{
                $clpmn_data['entry_type_descr'] = $cbmast->entry_type_descr;  
                }

                if($cbmast->entry_type_descr == "REC"){
                    if($clhmnallo->currency_code == $clhmn->currency_code){
                        $clpmn_data['pay_amount'] = $clhmnallo->foreign_amount;
                        $clpmn_data['amount'] = $clhmnallo->foreign_amount;
                        $clpmn_data['local_amount'] = $clhmnallo->amount;
                    }
                    else{
                        $clpmn_data['currency_rate'] = 1;
                        $clpmn_data['pay_amount'] = $clhmnallo->amount;
                        $clpmn_data['amount'] = $clhmnallo->amount;
                        $clpmn_data['local_amount'] = $clhmnallo->amount;
                    }
                }
                else{
                    if($clhmnallo->currency_code == $clhmn->currency_code){
                        $clpmn_data['pay_amount'] = $clhmnallo->foreign_amount*-1;
                        $clpmn_data['amount'] = $clhmnallo->foreign_amount*-1;
                        $clpmn_data['local_amount'] = $clhmnallo->amount*-1;
                    }
                    else{
                        $clpmn_data['currency_rate'] = 1;
                        $clpmn_data['pay_amount'] = $clhmnallo->amount*-1;
                        $clpmn_data['amount'] = $clhmnallo->amount*-1;
                        $clpmn_data['local_amount'] = $clhmnallo->amount*-1;
                    }
                }
                $clpmn_data['pay_type'] = 20;
            } //end of receipts
            else if(trim($cbmast->slhead) == "02"){ //payments
                if($clhmnallo->currency_code == $clhmn->currency_code){
                    $clpmn_data['pay_amount'] = $clhmnallo->foreign_amount;
                    $clpmn_data['amount'] = $clhmnallo->foreign_amount;
                    $clpmn_data['local_amount'] = $clhmnallo->amount;
                }
                else{
                    $clpmn_data['currency_rate'] = 1;
                    $clpmn_data['pay_amount'] = $clhmnallo->amount;
                    $clpmn_data['amount'] = $clhmnallo->amount;
                    $clpmn_data['local_amount'] = $clhmnallo->amount;
                }
                if($cbmast->entry_type_descr == "PAY"){
                    if($clhmnallo->currency_code == $clhmn->currency_code){
                        $clpmn_data['pay_amount'] = $clhmnallo->foreign_amount*-1;
                        $clpmn_data['amount'] = $clhmnallo->foreign_amount*-1;
                        $clpmn_data['local_amount'] = $clhmnallo->amount*-1;
                    }
                    else{
                        $clpmn_data['currency_rate'] = 1;
                        $clpmn_data['pay_amount'] = $clhmnallo->amount*-1;
                        $clpmn_data['amount'] = $clhmnallo->amount*-1;
                        $clpmn_data['local_amount'] = $clhmnallo->amount*-1;
                    } 
                }
                $clpmn_data['pay_type'] = 10;
            }

            // return($clpmn_data);
            $clpmn = $this->getClaimBalances($clpmn_data,$cbmastmult1->payrqst_no);

            //allocate multiclaim receipts
            if($cbmast->doc_type == 'REC'){
                //dd($cbmast)
                $allocate_rec = $this->allocateMultiReceipt($cbmast, $clhmnallo);
            }
            } //end of forech clhmnallo

            return $clpmn;


    } //end of function

    public function cancelClhmnallo($cbmast,$entry)
    {
        //dd($cbmast);
        $clhmnallo_data = Clhmnallo::where('offcd',$cbmast->offcd)
                                ->where('doc_type',$cbmast->doc_type)
                                ->where('reference',$cbmast->cancelled_reference)
                                ->where('entry_type_descr',$entry)
                                ->get();
                        //dd($clhmnallo_data);
            $reference = STR_PAD($cbmast->dtrans_no,6,'0',STR_PAD_LEFT).$cbmast->account_year;
           //dd($reference);
            
        foreach($clhmnallo_data as $clhmnallo){
            $count = Clhmnallo::where('offcd',$cbmast->offcd)
                                ->where('doc_type',$cbmast->doc_type)
                                ->where('reference',$reference)
                                ->where('entry_type_descr',$cbmast->entry_type_descr)
                                ->where('line_no',$cbmast->line_no)
                                ->count();
            //dd($count);

            $item_no = $count+1;
            //dd($clhmnallo);
            $create_clhmnallo = [
                'doc_type'=>$cbmast->doc_type,
                'offcd'=>$cbmast->offcd,
                'entry_type_descr'=>$cbmast->entry_type_descr,
                'reference'=>$reference,
                'ln_no'=>$cbmast->ln_no,
                'total_amount'=>$cbmast->amount,
                'foreign_total_amount'=>$cbmast->foreign_amount,
                'debit_account'=>$cbmast->debit_account,
                'credit_account'=>$clhmnallo->credit_account,
                'currency_code'=>$cbmast->currency_code,
                'currency_rate'=>$cbmast->currency_rate,
                'account_year'=>$cbmast->account_year,
                'dtrans_no'=>$cbmast->dtrans_no,
                'account_month'=>$cbmast->account_month,
                'claim_no'=>$clhmnallo->claim_no,
                'item_no'=>$clhmnallo->item_no,
                'class'=>$clhmnallo->class,
                'foreign_amount'=>$clhmnallo->foreign_amount,
                'amount'=>$clhmnallo->amount,
                'claimant_code'=>$clhmnallo->claimant_code,
                'line_no'=>$cbmast->line_no,
                'dtrans_no'=>$clhmnallo->dtrans_no,
                'debit_note_no'=>$clhmnallo->debit_note_no,
                'source_code'=>$cbmast->source_code
            ];

            $insert_clhmnallo = Clhmnallo::insert($create_clhmnallo);
            // dd($insert_clhmnallo);

            //update clhmn orig
            $clhmnallo_cnc = Clhmnallo::where('offcd',$cbmast->offcd)
                                ->where('doc_type',$cbmast->doc_type)
                                ->where('reference',$cbmast->cancelled_reference)
                                ->where('entry_type_descr',$entry)
                                ->where('claim_no',$clhmnallo->claim_no)
                                ->where('dtrans_no',$clhmnallo->debit_note_no)
                                ->update(['cancelled'=>'Y']); 
        }
    }

    public function clpmnCbmastmult($cbmast,$item_no)
    {
        $today = Carbon::today();
        $user = Auth::user()->user_name;

        $reference = STR_PAD($cbmast->dtrans_no,6,'0',STR_PAD_LEFT).$cbmast->account_year;

        $cbmast_mult = Cbmastmult::where('doc_type',$cbmast->doc_type)
                                    ->where('entry_type_descr',$cbmast->entry_type_descr)
                                    ->where('reference',$reference)
                                    ->where('item_no',$item_no)
                                    ->get();

        $g_doc_type = $cbmast->doc_type;
        if($cbmast->doc_type == $cbmast->entry_type_descr){
            $g_doc_type = "REV";
        }

        foreach($cbmast_mult as $cbmastmult1){
            $payreqst = Payreqst::where('req_no',$cbmastmult1->requisition_no)->first();
            //dd($payreqst);
            $clhmn = Clhmn::where('claim_no',$payreqst->claim_no)->first();
            $clpmn_data = [
                    'ln_no' =>$cbmastmult1->item_no,
                    'claim_no'=>$payreqst->claim_no,
                    'branch'=>$cbmastmult1->branch,
                    'agent'=>$cbmastmult1->agent_no,
                    'policy_no'=>$payreqst->policy_no,
                    'class'=>$cbmastmult1->class,
                    'uw_year'=>$payreqst->uw_year,
                    'pay_date'=>$today,
                    'effective_date'=>$today,
                    'entry_type_descr'=>$cbmast->entry_type_descr,
                    'dtrans_no'=>$cbmastmult1->dtrans_no,
                    'tran_no'=>$cbmastmult1->dtrans_no,
                    'doc_type'=>$g_doc_type,
                    'doc_ref'=>trim($cbmastmult1->cheque_no),
                    'currency_code'=>$cbmastmult1->currency_code,
                    'currency_rate'=>$cbmastmult1->currency_rate,
                    'claimant_code'=>$cbmastmult1->claimant_code,
                    'payee'=>$cbmastmult1->name,
                    'cheque_no'=>trim($cbmastmult1->cheque_no),
                    'cheque_date'=>$cbmastmult1->cheque_date,
                    'peril'=>$cbmastmult1->peril,
                    'account_month'=>$cbmastmult1->account_month,
                    'account_year'=>$cbmastmult1->account_year,
                    'user_str'=>$user,
                    'pay_time'=>$cbmastmult1->created_time,
                    'dola'=>$today,
                    'reinsured'=>'N',
                    'gl_updated'=>'Y',
                    'orig_from'=>99,
                    'source'=>'CB',
                    'final_settle'=>$cbmastmult1->final_settle,
                    'status'=>'',
            ];
            //dd(trim($cbmast->slhead));
            if($cbmast->doc_type == "PAY"){
            //dd("here");
                if($cbmast->doc_type == $cbmast->entry_type_descr){
                    if($clhmn->currency_code == $cbmastmult1->currency_code){
                        $clpmn_data['currency_rate'] = $cbmastmult1->currency_rate;
                        $clpmn_data['pay_amount'] = $cbmastmult1->foreign_amount*-1;
                        $clpmn_data['amount'] = $cbmastmult1->foreign_amount*-1;
                        $clpmn_data['local_amount'] = $cbmastmult1->amount*-1;
                    }
                    else{
                        $clpmn_data['currency_rate'] = 1;
                        $clpmn_data['pay_amount'] = $cbmastmult1->amount*-1;
                        $clpmn_data['amount'] = $cbmastmult1->amount*-1;
                        $clpmn_data['local_amount'] = $cbmastmult1->amount*-1;
                    }  
                }
                else{
                    //dd("else");
                    if($clhmn->currency_code == $cbmastmult1->currency_code){
                        $clpmn_data['currency_rate'] = $cbmastmult1->currency_rate;
                        $clpmn_data['pay_amount'] = $cbmastmult1->foreign_amount;
                        $clpmn_data['amount'] = $cbmastmult1->foreign_amount;
                        $clpmn_data['local_amount'] = $cbmastmult1->amount;
                    }
                    else{
                        $clpmn_data['currency_rate'] = 1;
                        $clpmn_data['pay_amount'] = $cbmastmult1->amount;
                        $clpmn_data['amount'] = $cbmastmult1->amount;
                        $clpmn_data['local_amount'] = $cbmastmult1->amount;
                    }  
                }

                $clpmn_data['pay_type'] = 10;
            }
            if($cbmastmult1->final_settle == "Y"){
                $clpmn_data['status'] = "SE";
            }
            //dd($clpmn_data);

            $clpmn = $this->getClaimBalances($clpmn_data);
              
            return $clpmn;
        }
    }

    public function multiclaimPayment($cbmast,$payreqst)
    {
        $clhmnallo = Clhmnallo::where('requisition_no',$payreqst->req_no)
                                ->where('offcd',$payreqst->offcd)
                                ->where('doc_type',$payreqst->doc_type)
                                ->get();
        $reference = STR_PAD($cbmast->dtrans_no,6,'0',STR_PAD_LEFT).$cbmast->account_year;

        foreach($clhmnallo as $clhmnallo1){
            $item = Clhmnallo::where('offcd',$cbmast->offcd)
                            ->where('doc_type',$cbmast->doc_type)
                            ->where('reference',$reference)
                            ->where('entry_type_descr',$cbmast->entry_type_descr)
                            ->count();
            $item = $item+1;

            $clhmnallo_data = [
                'doc_type'=>$cbmast->doc_type,
                'offcd'=>$clhmnallo1->offcd,
                'entry_type_descr'=>$clhmnallo1->entry_type_descr,
                'reference'=>$reference,
                'ln_no'=>$cbmast->ln_no,
                'total_amount'=>$cbmast->amount,
                'foreign_total_amount'=>$cbmast->amount,
                'debit_account'=>$clhmnallo1->debit_account,
                'credit_account'=>$cbmast->credit_account,
                'currency_code'=>$clhmnallo1->currency_code,
                'currency_rate'=>$clhmnallo1->currency_rate,
                'account_year'=>$cbmast->account_year,
                'dtrans_no'=>$cbmast->dtrans_no,
                'account_month'=>$cbmast->account_month,
                'claim_no'=>$clhmnallo1->claim_no,
                'item_no'=>$item,
                'class'=>$clhmnallo1->class,
                'foreign_amount'=>$clhmnallo1->foreign_amount,
                'amount'=>$clhmnallo1->amount,
                'claimant_code'=>$clhmnallo1->claimant_code,
                'peril'=>$clhmnallo1->peril,
                'perilitem'=>$clhmnallo1->perilitem,
                'requisition_nox'=>$clhmnallo1->req_no,
                'line_no'=>$cbmast->line_no,
            ];

            $clhmnallo_create = Clhmnallo::insert($clhmnallo_data);

            if($clhmnallo_create){
                $createClpmn = $this->clpmnClhmnallo($cbmast->offcd,$cbmast->doc_type,$cbmast->dtrans_no,$cbmast->account_year,$cbmast->account_month,$cbmast->entry_type_descr,$reference);
            }
        }
    }


    public function  allocateReceipt($dtrans_no,$entry_type,$year,$month,$offcd){
        $cbmast=Cbmast::where('dtrans_no',$dtrans_no)
                ->where('entry_type_descr',$entry_type)
                ->where('account_year',$year)
                ->where('account_month',$month)
                ->where('doc_type','REC')
                ->where('offcd',$offcd)
                ->get()[0];
        
        $creditNote=Creditclm::where('dtrans_no',$cbmast->debit_note_no)
                    ->where('claim_no',$cbmast->claim_no)
                    ->get()[0];
        
        $clhmn=Clhmn::where('claim_no',$cbmast->claim_no)
                    ->get()[0];

        $ln_no = Creditclm::where('dtrans_no',$cbmast->dtrans_no)
                    ->where('account_year',$cbmast->account_year)
                    ->where('doc_type',$cbmast->doc_type)
                    ->count();
                    //dd($ln_no);
        $ln_no = $ln_no + 1;
    //dd($ln_no);

        $creditClm=new Creditclm();
        
        
        $creditClm->doc_type=$cbmast->doc_type;
        $creditClm->dtrans_no=$cbmast->dtrans_no;
        $creditClm->account_year=$cbmast->account_year;
        $creditClm->account_month=$cbmast->account_month;
        $creditClm->endt_renewal_no=$clhmn->endt_renewal_no;
        $creditClm->treaty_code=$cbmast->treaty_code;
     
        $creditClm->uw_year=$clhmn->uw_year;
        $creditClm->class=$clhmn->class;
        $creditClm->branch=$clhmn->branch;
        $creditClm->agent_no=$clhmn->agent_no;
        $creditClm->policy_no=$clhmn->policy_no;
        $creditClm->dola=Carbon::today();
        $creditClm->tran_no=$clhmn->$tran_no;
        $creditClm->effective_date=$cbmast->$effective_date;
        
        //return dd($clhmn->trans_type);
        //$creditClm->trans_type=$clhmn->trans_type;
        $creditClm->despatch_date=$cbmast->effective_date;
        $creditClm->debit_credit_no=$cbmast->debit_note_no;
        $creditClm->sum_insured=$clhmn->sum_insured;

        $creditClm->client_number=$clhmn->client_number;

        if($cbmast->doc_type == $cbmast->entry_type_descr){
            $receipt_amount = $cbmast->amount;
            $source = $cbmast->entry_type_descr1;
        }
        else{
            $receipt_amount = $cbmast->amount * -1;
            $source = $cbmast->entry_type_descr1;
        }

        $creditClm->gross_amount=$receipt_amount;
        $creditClm->reice=$creditNote->reice;
        $creditClm->stamp_duty=$creditNote->stamp_duty;
       

        $creditClm->comm_amount=$creditNote->comm_amount;
        
        $creditClm->nett_amount=$receipt_amount;
        $creditClm->quake_premium=$creditNote->quake_premium;
        $creditClm->quake_comm=$creditNote->quake_comm;



        $creditClm->user_str=trim(Auth::user()->user_name);
        $creditClm->period_to=$creditNote->period_to;
        $creditClm->period_from=$creditNote->period_from;
        $creditClm->entry_type_descr=$cbmast->entry_type_descr;
        $creditClm->entry_type=$cbmast->entry_type;
        $creditClm->currency_code=$cbmast->currency_code;
        $creditClm->ln_no=$ln_no;
        $creditClm->details=$cbmast->narration;

        $creditClm->claimant_code=$cbmast->claimant_code;
        $creditClm->payee=$cbmast->name;        
        $creditClm->unallocated=0;
        $creditClm->allocated=$receipt_amount;
        $creditClm->claim_no=$cbmast->claim_no;
        $creditClm->source=$source;

        if($cbmast->doc_type != $cbmast->entry_type_descr){
            $creditClm->dr_cr='C';
        }
        else{
            $creditClm->dr_cr='D';
        }


        $creditClm->save();

        //update allocations
        $newunallocated=$creditNote->unallocated+$cbmast->amount;
        $newallocated=$creditNote->allocated-$cbmast->amount;

        $credit_upd = Creditclm::where('dtrans_no',$cbmast->debit_note_no)
                            ->where('claim_no',$cbmast->claim_no)
                            ->update([
                                'allocated' => $creditNote->allocated-$receipt_amount,
                                'unallocated'=> $creditNote->unallocated+$receipt_amount
                            ]);

        //update salvages
        $debit_note = Creditclm::where('dtrans_no',$cbmast->debit_note_no)
                                ->where('claim_no',$cbmast->claim_no)
                                ->get()[0];

        if($debit_note->entry_type_descr = 'SLV' && $debit_note->unallocated == 0){
            $salvages = Salvages::where('claim_no',$cbmast->claim_no)
                                ->whereRaw("trim(serial_no) = '".trim($debit_note->item_desc)."'")
                                ->update([
                                    'paid'=>'Y',
                                    'receipt_no'=>STR_PAD($cbmast->dtrans_no,6,'0',STR_PAD_LEFT).$cbmast->account_year,
                                    'receipt_date'=>$cbmast->receipt_date
                                ]);
        }



        return $creditClm;
    }

    public function  allocateMultiReceipt($cbmast,$clhmnallo){
        // dd($cbmast, $clhmnallo);
        $creditNote=Creditclm::where('dtrans_no',$clhmnallo->debit_note_no)
                    ->where('claim_no',$clhmnallo->claim_no)
                    ->get()[0];
        
        $clhmn=Clhmn::where('claim_no',$clhmnallo->claim_no)
                    ->get()[0];

        $ln_no = Creditclm::where('dtrans_no',$cbmast->dtrans_no)
                    ->where('account_year',$cbmast->account_year)
                    ->where('doc_type',$cbmast->doc_type)
                    ->count();
                    //dd($ln_no);
        $ln_no = $ln_no + 1;
    //dd($ln_no);

        $creditClm=new Creditclm();
        
        
        $creditClm->doc_type=$cbmast->doc_type;
        $creditClm->dtrans_no=$cbmast->dtrans_no;
        $creditClm->account_year=$cbmast->account_year;
        $creditClm->account_month=$cbmast->account_month;
        $creditClm->endt_renewal_no=$clhmn->endt_renewal_no;
        $creditClm->treaty_code=$cbmast->treaty_code;
     
        $creditClm->uw_year=$clhmn->uw_year;
        $creditClm->class=$clhmn->class;
        $creditClm->branch=$clhmn->branch;
        $creditClm->agent_no=$clhmn->agent_no;
        $creditClm->policy_no=$clhmn->policy_no;
        $creditClm->dola=Carbon::today();
        $creditClm->tran_no=$clhmn->$tran_no;
        $creditClm->effective_date=$cbmast->$effective_date;
        
        //return dd($clhmn->trans_type);
        //$creditClm->trans_type=$clhmn->trans_type;
        $creditClm->despatch_date=$cbmast->effective_date;
        $creditClm->debit_credit_no=$clhmnallo->debit_note_no;
        $creditClm->sum_insured=$clhmn->sum_insured;

        $creditClm->client_number=$clhmn->client_number;

        if($cbmast->doc_type == $cbmast->entry_type_descr){
            $receipt_amount = $clhmnallo->amount;
            $source = $cbmast->entry_type_descr1;
        }
        else{
            $receipt_amount = $clhmnallo->amount * -1;
            $source = $cbmast->entry_type_descr1;
        }

        $creditClm->gross_amount=$receipt_amount;
        $creditClm->reice=$creditNote->reice;
        $creditClm->stamp_duty=$creditNote->stamp_duty;
       

        $creditClm->comm_amount=$creditNote->comm_amount;
        
        $creditClm->nett_amount=$receipt_amount;
        $creditClm->quake_premium=$creditNote->quake_premium;
        $creditClm->quake_comm=$creditNote->quake_comm;



        $creditClm->user_str=trim(Auth::user()->user_name);
        $creditClm->period_to=$creditNote->period_to;
        $creditClm->period_from=$creditNote->period_from;
        $creditClm->entry_type_descr=$cbmast->entry_type_descr;
        $creditClm->entry_type=$cbmast->entry_type;
        $creditClm->currency_code=$cbmast->currency_code;
        $creditClm->ln_no=$ln_no;
        $creditClm->details=$cbmast->narration;

        $creditClm->claimant_code=$clhmnallo->claimant_code;
        $creditClm->payee=$cbmast->name;        
        $creditClm->unallocated=0;
        $creditClm->allocated=$receipt_amount;
        $creditClm->claim_no=$clhmn->claim_no;
        $creditClm->source=$source;

        if($cbmast->doc_type != $cbmast->entry_type_descr){
            $creditClm->dr_cr='C';
        }
        else{
            $creditClm->dr_cr='D';
        }
        // dd($creditClm);

        $creditClm->save();

        //update allocations
        $newunallocated=$creditNote->unallocated+$cbmast->amount;
        $newallocated=$creditNote->allocated-$cbmast->amount;

        $credit_upd = Creditclm::where('dtrans_no',$clhmnallo->debit_note_no)
                            ->where('claim_no',$clhmnallo->claim_no)
                            ->update([
                                'allocated' => $creditNote->allocated-$receipt_amount,
                                'unallocated'=> $creditNote->unallocated+$receipt_amount
                            ]);

        //update salvages
        $debit_note = Creditclm::where('dtrans_no',$clhmnallo->debit_note_no)
                                ->where('claim_no',$clhmnallo->claim_no)
                                ->get()[0];

        if($debit_note->entry_type_descr == 'SLV' && $debit_note->unallocated == 0){
            $salvages = Salvages::where('claim_no',$clhmnallo->claim_no)
                                ->whereRaw("trim(reg_no) = '".trim($debit_note->item_desc)."'")
                                ->update([
                                    'paid'=>'Y',
                                    'receipt_no'=>STR_PAD($cbmast->dtrans_no,6,'0',STR_PAD_LEFT).$cbmast->account_year,
                                    'receipt_date'=>$cbmast->receipt_date
                                ]);
        }

        // return $creditClm;
    }

    public function reopenClaimFile($claim_no){
     
        $claim = Clhmn::Where('claim_no', '=', $claim_no)->first();
        if($claim->closed=='Y'){
            $update_clhmn = Clhmn::Where('claim_no', '=', $claim_no)
            ->update([
                'revived' => 'Y',
                'revived_period' => $this->getDtran0()->uw_period,
                'revived_date' => Carbon::now(),
                'closed' => '',
                ##change rejected status if previously rejected
                'rejected'=>'N',
                'rejected_by'=>'',
    
                //Set attributes to null when claim file is reopened.
                'closed_by' => '',
                'closed_date' => '',
                'closed_year' => '',
                'closed_month' => '',
            ]);
        }

       


    }

    public function getDtran0() {
		$dtran0 = Dtran0::All()->first();
		return $dtran0;
	}

} //end of claimsClass
