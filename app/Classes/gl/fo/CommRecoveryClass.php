<?php

namespace App\Classes\gl\fo;

use Carbon\Carbon;
use Auth;
use DB;

use App\Cbmast;
use App\Acdet;
use App\Acdetallo;
use App\Cbtrans;
use App\Polana;
use App\Polmaster;
use App\Commdet;
use App\Commana;
use App\Commanaend;
use App\Commbatch;
use App\Payreqst;


class CommRecoveryClass
{
    //create cbmast
    public function createCbmast($cbmast){
        $today = Carbon::today();
        $time = Carbon::now();
        $user = trim(Auth::user()->user_name); 
        $year = $today->year;
        $month = $today->month;

        if($cbmast->doc_type == 'REC'){
            $doc_type = 'PAY';
        }
        else{
            $doc_type = 'REC';
        }

        $cbmast_old = Cbmast::where('doc_type',$doc_type)
                            ->where('offcd',$cbmast->offcd)
                            ->where('batchno',$cbmast->batchno)
                            ->where('batch_no',$cbmast->batch_no)
                            ->where('account_year',$cbmast->account_year)
                            ->where('source_code',$cbmast->source_code)
                            ->get()[0];

        $req_no = $cbmast_old->dept_code.$cbmast_old->payrqst_no;

        $payreq = Payreqst::where('req_no',$req_no)->get()[0];

        //create payment reversal record
        $cbmast_cnc = Cbmast::create([
            'dtrans_no'=>$cbmast_old->dtrans_no,
            'account_year'=>$year,
            'r_reference'=>$cbmast_old->r_reference,
            'account_month'=>$month,
            'effective_date'=>$today,
            'cheque_no'=>$cbmast_old->cheque_no,
            'cheque_date'=>$cbmast_old->cheque_date,
            'receipt_date'=>$today,
            'dola'=>$today,
            'line_no'=>$cbmast_old->line_no+1,
            'doc_type'=>$cbmast_old->doc_type,
            'payrqst_no'=>$cbmast_old->payrqst_no,
            'offcd'=>$cbmast_old->offcd,
            'agent_no'=>$cbmast_old->agent_no,
            'branch'=>$cbmast_old->branch,
            'currency_code'=>$cbmast_old->currency_code,
            'currency_rate'=>$cbmast_old->currency_rate,
            'break_down'=>$cbmast_old->break_down,
            'analyse_comm'=>$cbmast_old->analyse_comm,
            'dept_code'=>$cbmast_old->dept_code,
            'debit_account'=>$cbmast_old->debit_account,
            'credit_account'=>$cbmast_old->credit_account,
            'bank_account'=>$cbmast_old->credit_account,
            'pay_method'=>$cbmast_old->pay_method,
            'batchno'=>$cbmast_old->batchno,
            'batch_no'=>$cbmast_old->batch_no,
            'entry_type_descr'=>$cbmast_old->doc_type,
            'gross_amount'=>$cbmast_old->gross_amount,
            'amount'=>$cbmast_old->amount,
            'foreign_amount'=>$cbmast_old->foreign_amount,
            'part_pay_reqnt'=>$cbmast_old->part_pay_reqnt,
            'foreign_gross_amount'=>$cbmast_old->foreign_gross_amount,
            'name'=>$cbmast_old->name,
            'narration'=>$cbmast->narration,
            'premium_amount'=>$cbmast_old->premium_amount,
            'source_code'=>$cbmast_old->source_code,
            'subledger_code'=>$cbmast_old->subledger_code,
            'slhead'=>$cbmast_old->slhead,
            'slhead1'=>$cbmast_old->slhead1,
            'subledger'=>$cbmast_old->subledger,
            'type_of_bus'=>$cbmast_old->type_of_bus,
            'uw_year'=>$cbmast_old->uw_year,
            'multiclaims'=>$cbmast_old->multiclaims,
            'pin_no'=>$cbmast_old->pin_no,
            'created_by'=>$user,
            'created_date'=>$today,
            'created_time'=>$time,
            'changed_by'=>$user,
            'changed_time'=>$time,
            'cancelled_reference'=>STR_PAD($cbmast_old->dtrans_no,6,'0',STR_PAD_LEFT).$cbmast_old->account_year,
            'entry_type_descr1'=>$cbmast_old->entry_type_descr,
            'foreign_nett_amount'=>$cbmast_old->foreign_nett_amount,
            'nett_amount'=>$cbmast_old->nett_amount,
            'ln_no'=>$cbmast_old->ln_no,
            'analyse_policy'=>$cbmast_old->analyse_policy,
            'on_account'=>$cbmast_old->on_account,
        ]);

        //update cbmast original payment
        $cbmast_old = Cbmast::where('doc_type',$doc_type)
                            ->where('offcd',$cbmast->offcd)
                            ->where('batchno',$cbmast->batchno)
                            ->where('batch_no',$cbmast->batch_no)
                            ->where('account_year',$cbmast->account_year)
                            ->where('source_code',$cbmast->source_code)
                            ->update([
                                'cancelled' => 'Y' 
                            ]);

        $new_reference = STR_PAD($cbmast_old->dtrans_no,6,'0',STR_PAD_LEFT).$year;

        if($doc_type == 'PAY'){
        if($cbmast->break_down == 'Y'){
            //$new_reference = STR_PAD($cbmast->dtrans_no,6,'0').$year;
            $new_entry = $doc_type;
            $old_reference = STR_PAD($cbmast_old->dtrans_no,6,'0',STR_PAD_LEFT).$cbmast_old->account_year;

            $cbmastana_rev = Cbmastana::where('offcd',$cbmast_old->offcd)
                                    ->where('doc_type',$cbmast_old->doc_type)
                                    ->where('entry_type_descr',$cbmast_old->entry_type_descr)
                                    ->where('reference',$old_reference)
                                    ->get();

            foreach($cbmastana_rev as $cbmastana1){
                if($cbmastana1->dr_cr == 'D'){
                    $dr_cr = 'C';
                }
                else{
                    $dr_cr = 'D';
                }
                $create_cbmastana = Cbmastana::create([
                    'offcd'=>$cbmastana1->offcd,
                    'inter_branch_offcd'=>$cbmastana1->offcd,
                    'doc_type'=>$cbmastana1->doc_type,
                    'entry_type_descr'=>$new_entry,
                    'trans_date'=>$today,
                    'reference'=>$new_reference,
                    'item_no'=>$cbmastana1->item_no,
                    'entry_type_descr1'=>$cbmastana1->entry_type_descr1,
                    'source_code'=>$cbmastana1->source_code,  
                    'narration'=>$cbmastana1->narration,
                    'glhead'=>$cbmastana1->glhead,
                    'currency_code'=>$cbmastana1->currency_code,
                    'currency_rate'=>$cbmastana1->currency_rate,
                    'amount'=>$cbmastana1->amount,
                    'foreign_amount'=>$cbmastana1->foreign_amount,
                    'dr_cr'=>$dr_cr,
                ]);
            }
        } //end of analyse payment

        //update payreqst
        $payreqst_cnc = Payreqst::where('req_no',$req_no)->update([
                                'payments_todate'=>0,
                                'cancelled'=>'Y',
                                'cancelled_by'=>$user,
                                'cancel_date'=>$today,
                                'cancel_time'=>$time,
                                'dola'=>$today,
                            ]);

        //update commissions tables
            $commbatch = Commbatch::where('gbatch',$payreq->batchno)->update([
                            'cancelled'=>'Y',
                            'cancelled_by'=>$user,
                        ]);
                $commana = Commana::where('batchno',$payreq->batchno)->get();
                foreach($commana as $comm){
                $commana1 = Commana::where('batchno',$payreq->batchno)
                            ->update([
                                'requisition_no'=>'',
                                'batchno'=>0,
                                'paid'=>0,
                                'unpaid'=>$comm->policy_amount-$comm->tax_amount-$comm->witheld_amount,
                            ]);
                }

                $commdet1 = Commdet::where('requisition_no',$payreq->req_no)
                                    ->update([
                                        'requisition_no'=>'',
                                        'req'=>'N',
                                        'comm_paid'=>0
                                    ]);
        }
        

    }

    
} //end of class
