<?php
namespace App\Classes\gl;
use App\Glsource;

class GenerateBatch
{
    function __construct($period_year, $period_month, $batch_src) {
        $this->period_year = $period_year;
        $this->period_month = $period_month;
        $this->batch_src = $batch_src;
      }

      public function generate()
      {
        $batch_set = $this->batch_src . $this->period_year . $this->period_month;
        // dd($batch_set);
        $glsources = Glsource::where('source_code', $this->batch_src)->first();

        $get_serial = $glsources->batch_serial;
        $serial_no = str_pad($get_serial, 5, 0, STR_PAD_LEFT);
        $batch_no = $batch_set . $serial_no;
        return $batch_no;
      }

}
