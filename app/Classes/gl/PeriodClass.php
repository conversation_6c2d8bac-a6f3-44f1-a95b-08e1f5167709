<?php
namespace App\Classes\gl;
use App\Period;
use App\PeriodModule;


class PeriodClass{
    public function __construct($year, $month, $module){
        $this->year = $year;
        $this->month = $month;
        $this->module = $module;
    }

    public function validate(){
        $status = null;
        $get_period = PeriodModule::where('account_year', $this->year)
                                    ->where('account_month', $this->month)
                                    ->where('module_id', $this->module)
                                    ->first();
        // check period status if its closed

        if ($get_period->status == 002 || $get_period->status == '002') {
            $status = 1;
        }elseif($get_period->status == 003 || $get_period->status == '003') {
            $status = 2;     
        }elseif($get_period->status == 001 || $get_period->status == '001') {
            $status = 3;
        }
        else {

            $status = 0;
        }

        return $status;
        


    }
}





?>