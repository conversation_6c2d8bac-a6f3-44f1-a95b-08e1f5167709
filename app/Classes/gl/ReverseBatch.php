<?php
namespace App\Classes\gl;
use App\Period;
use App\EscalateReverseBatch;


class ReverseBatch{
    public function __construct($batch_no,$year, $month){
        $this->year = $year;
        $this->month = $month;
        $this->batch_no = $batch_no;
    }

    public function get_values()
    {
     $batch = EscalateReverseBatch::where('batch_no', $this->batch_no)->where('account_year', $this->year)->where('account_month', $this->month)->first();
     return  $batch;  
    }
}

?>