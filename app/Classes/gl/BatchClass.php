<?php
namespace App\Classes\gl;

use App\BankReconSum;
use App\Batch_details;
use App\Batch_list;
use App\Glbank;
use DB;
use Illuminate\Support\Facades\Auth;

class BatchClass{
    public $batch_no;
    public $batch;
    public $account_year;
    public $account_month;
    public $message;
    public $user;
    public function __construct($batch_no)
    {
        $this->batch_no = $batch_no;
        $this->batch = Batch_list::where('batch_no', $this->batch_no)->first();
        $this->account_year = $this->batch->account_year;
        $this->account_month = $this->batch->account_month;
        $this->message = "";
        $this->user = Auth::user()->user_name;
    }

    public function post_batch($period_month,$period_year){
        #get all entries for the batch
        $entries = Batch_details::where('batch_no', $this->batch_no)->get();

        $schem = schemaName();

        $gb = $schem['gb'];
        $gl = $schem['gl'];
        $common = $schem['common'];
        $batch_no = $this->batch_no;
        
        // DB::connection(env('DB_CONNECTION'))->beginTransaction();
        // DB::connection(env('DB_CONNECTION1'))->beginTransaction();

        #go through all items in the batch

        // try {
            
            Batch_list::where('batch_no', $batch_no)->update([
                'account_month' => $period_month,
                'account_year' => $period_year
            ]);

            foreach ($entries as $entry) {
                $bank = Glbank::where('prsno', $entry->glhead)->first();
                # check if item account, year and month are in a closed recon period
                $closed_status = BankReconSum::where('rec_year', $this->account_year)
                ->where('bank_code', $bank->bank_acc_code)
                ->where('rec_month', (int) $this->account_month)
                ->where('trans_type', 'OBL')
                ->first();
                
    
                #check if reconcillion was closed
                if ($closed_status->reconcilliation_closed == 'Y') {
                    $data =['status'=>0, 'message'=>$this->message = "one of the transaction has been been closed in the bank reconcilliation"];
                    return $data;
                }
            }
           
            $procedureName = '' . $gl . '.post_gl_batch';

            $bindings = [
                'w_batch_no' => $batch_no,
                'w_user' => $this->user,
            ];
           
            $resp = DB::executeProcedure($procedureName, $bindings);

            $glbatch = Batch_list::where('batch_no', $batch_no)->get()[0];
           
            return ['batch_no' => $glbatch->batch_no, 'period_year' => $glbatch->account_year, 
            'period_month' => $glbatch->account_month, 'batch_type' => $glbatch->batch_type,'status' => 1];
     
        // } 
        
        // catch (\Throwable $e) {

            // DB::connection(env('DB_CONNECTION'))->rollback();
            // DB::connection(env('DB_CONNECTION1'))->rollback();
            
            // $glbatch = Batch_list::where('batch_no', $batch_no)->get()[0];
     
            
            // return ['batch_no' => $glbatch->batch_no, 'period_year' => $glbatch->account_year, 
            // 'period_month' => $glbatch->account_month, 'batch_type' => $glbatch->batch_type,'status' => 0];
        // }
    }
}




?>