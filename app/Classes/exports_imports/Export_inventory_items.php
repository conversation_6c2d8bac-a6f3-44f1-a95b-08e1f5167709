<?php

namespace App\Classes\exports_imports;

use App\Im_Inventoryitems;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class Export_inventory_items implements FromCollection,WithHeadings
{
    public function collection()
    {
        return Im_Inventoryitems::select('item_code', 'description', 'total_in_stock', 'counted_qty')->get();
    }

    public function headings(): array
    {
        return [
            'item_code', 'description', 'total_in_stock', 'counted_qty'
        ];
    }
}