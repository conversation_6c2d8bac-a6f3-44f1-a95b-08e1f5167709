<?php

namespace App\Classes;

use App;


Class TumaSmsNotification 
{
    protected $data;
    protected $base_url = 'https://portal.paradox.co.ke/api/v1/send-sms';
    protected $client;

    public function __construct($data)
    {
       $this->data = $data;
       $this->client = new \GuzzleHttp\Client();
    }

    public function sendSMS()
    {
       $token = env('SMS_TOKEN');
           

        $response = $this->client->request('POST',$this->base_url,  [
            'headers' => [
                'Authorization' => 'Bearer ' .$token,
                'Accept'     => 'application/json',
                'Content-type'=>'application/json'
            ],

            'json'=> $this->data
            ]
        );
        $body = $response->getBody()->getContents();

  

       return $response;
    }



}