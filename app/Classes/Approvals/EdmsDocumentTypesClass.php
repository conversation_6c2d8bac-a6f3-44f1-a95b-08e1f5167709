<?php

namespace App\Classes\Approvals;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

use Illuminate\Support\Facades\Notification;
use App\Notifications\ErrorNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use File;

use GuzzleHttp\Client;
use App\Edmsdocument;
use App\Apvendors;



class EdmsDocumentTypesClass{
    private $toUser ; 
    private $edms_apiurl ; 
    private $edms_apiuser ; 
    private $edms_apipassword ;

    public function __construct(){

        $this->toUser =config('edms.toUser');
        $this->edms_apiurl =env('EDMS_URL');
        $this->edms_apiuser =env('EDMS_USER');
        $this->edms_apipassword =env('EDMS_PASSWORD');
    }

    private function sendRequest($url)  {
        $client = new \GuzzleHttp\Client();

        $username = $this->edms_apiuser;
        $password = $this->edms_apipassword;

        $auth = [ $username,$password];
        $headers = ['Content-Type' => 'application/json'];

        return $client->get($url, ['auth' => $auth, 'headers' => $headers]);

        
    }

    private function postRequest($url,$data){
        $client = new \GuzzleHttp\Client();

        $username = $this->edms_apiuser;
        $password = $this->edms_apipassword;

        $auth = [ $username,$password];
        $headers = ['Content-Type' => 'application/json'];

        return $client->post($url, ['auth' => $auth, 'headers' => $headers,'json'=>$data]);

        
    }


    public function fetch_doc_types_from_edms($document_description)
    {

        $document_label = $document_description;
        
        $url = $this->edms_apiurl. '/api/v4/document_types/';
        $response = '';
        $status = '';

        $response = $this->sendRequest($url);
    
        $contents = json_decode($response->getBody());
        
        $results = $contents->results;  

        $next_page_count = $contents->next;

        foreach($results as $key => $value) {
            if($value->label == $document_label) {
                $document_type_id = $value->id;
            
            }
        }

        $page = 1;
        $document_upload = $this->process_doc_for_upload($document_type_id,$document_label,$next_page_count,$page);
        
        return $document_upload;
    }

    public function process_doc_for_upload($document_type_id, $document_label, $next_page_count, $page)
    {
        // Check if document type id is null
        if ($document_type_id == null) {
            // Check if there is the next page to load
            if ($next_page_count == null) {
                return $document_type_id;
            } else {
                // Go to the next page to check document label
                $page = $page + 1;
                $url = $this->edms_apiurl . '/api/v4/document_types/?page=' . $page;
                $response = $this->sendRequest($url);
                $contents = json_decode($response->getBody()->getContents());
                $results = $contents->results;
                $next_page_count = $contents->next;

                foreach ($results as $key => $value) {
                    if ($value->label == $document_label) {
                        $document_type_id = $value->id;
                    }
                }

                return $this->process_doc_for_upload($document_type_id, $document_label, $next_page_count, $page);
            }
        } else {
            // Upload document
            return $document_type_id;
        }
    }




    public function create_edms_document_type($document_label,$document_code){
        // Check if the document type is present in edms;
        $document_type_id = $this->fetch_doc_types_from_edms($document_label);

        // If document type is already in edms update set_in_edms field to 'Y'
        if($document_type_id != null){
            Edmsdocument::where('document_code', $document_code)
                            ->update([
                                'set_in_edms'=>'Y',
                                'error_comments'=> null
                            ]);

            $status = 1;
            $doc_type_id = $document_type_id;

            return [
                'status' => $status,
                'doc_type_id'=> $doc_type_id
            ];
        }else{

            // create the document type in edms
            try
            {
                
                $url = $this->edms_apiurl. '/api/v4/document_types/';
                
                $data = array(
                            'label' => $document_label,
                        );
                
                $response = '';
                $status="";

                $response = $this->postRequest($url,$data);
                
                $contents = json_decode($response->getBody());
                $doc_type_id = $contents->id;

                $results = $response->getStatusCode();
                
                if($results == 201){
                    
                    $result = $this->set_metadata_types($doc_type_id,$document_label,$document_code);

                    Edmsdocument::where('document_code', $document_code)
                            ->update([
                                'set_in_edms'=>'Y',
                                'error_comments'=> null
                            ]);
                    $status = 1;
                    $doc_type_id = $doc_type_id;

                }else{
                    $status = 0;
                    $doc_type_id = null;
                }

                return [
                    'status' => $status,
                    'doc_type_id'=> $doc_type_id
                ];
            
            }catch (Throwable $th) {

                throw $th;
                
            }
        }
    }

    // get metadata types
    public function get_metadata_types(){

        try {
        
            $url = $this->edms_apiurl. '/api/v4/metadata_types/';
            $resp = $this->sendRequest($url);
    
            $response = $resp->getBody()->getContents();
            $data = json_decode($response, true);

            $count = $data['count'];
            $results = $data['results'];

            return [
                    'count'=> $count,
                    'results' => $results

            ];

        
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    // set metadata types for the document type created.
    public function set_metadata_types($doc_type_id,$document_description,$document_code){

        $metadata_types = $this->get_metadata_types();
        $results = $metadata_types['results'];
        $count = $metadata_types['count'];
       

        
        for ($i = 0; $i <= $count-1; $i++) {
            
           $metadata = $metadata_types['results'][$i];

           $status = $this->set_meta($metadata,$document_description,$document_code,$doc_type_id);
           
           if($status == 0){
                Edmsdocument::where('document_code', $document_code)
                        ->update([
                            'set_in_edms'=>'N',
                            'error_comments'=>'Failed to set metadata types'
                        ]);

                return $status;
           }
           
        }
        
        
        return $status;
          
        
    }

    // Attach each metadata type to the document type using the api
    public function set_meta($metadata,$document_description,$document_code,$doc_type_id){
        try
            {

                $url = $this->edms_apiurl. '/api/v4/document_types/'.$doc_type_id.'/metadata_types/';
                
                $metadata_name = $metadata['name'];

                $required = true;

                if($metadata_name == 'document_metadata' || $metadata_name == 'document_description' ){
                    $required = false;
                }

                $data = array(
                            'metadata_type_id' => $metadata['id'],
                            'label'=> $document_description,
                            'required'=> $required
                        );
              
                $response = '';
                $status="";

                $response = $this->postRequest($url,$data);

                if($response->getStatusCode() == 201){
                    $status = 1;
                }else{
                    $status = 0;
                }

                return $status;

            


        }catch(\Throwable $th){
            
            throw $th;

            return $status = 0;
        }

    }

    

}