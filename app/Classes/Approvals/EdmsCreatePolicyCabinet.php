<?php

namespace App\Classes\Approvals;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

use Illuminate\Support\Facades\Notification;
use App\Notifications\ErrorNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Edms_cabinets_dtl;
use App\Client as Customer;
use App\Dcontrol;
use File;

use GuzzleHttp\Client;
use App\Edms_staging;
use App\Edmsdocument;



class EdmsCreatePolicyCabinet{
   
    public function create_policy_claims_cabinet($client_number,$pol_clm_number,$parent_cabinet_name){
        $EdmsUploadClass = new EdmsUploadClass();
        $cabinet_name = 'Clients'; 

        $client =  Customer::where('client_number',$client_number)->first();
        $client_cabinet_name = $client_number.'-'.trim($client->name);
        
        $cabinet = Edms_cabinets_dtl::where('cabinet_name',$cabinet_name)->first();
        $edms_parent_id = $cabinet->api_id;
        $aims_cabinet_id = $cabinet->id;

        $edms_parent_id = $EdmsUploadClass->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);
       

        // check if client cabinet exists
        if($edms_parent_id){
            $child_cabinet_name = $pol_clm_number;
            
            $response = $this->get_policyclm_cabinet_id($parent_cabinet_name,$edms_parent_id,$child_cabinet_name);
            return $response;
        }else{

            $status = -1;
            $message = "Client cabinet does not exist";

           return [
               'status'=> $status,
               'message' => $message
           ];
       }

        
    }

    

    
    public function get_policyclm_cabinet_id($parent_cabinet_name,$edms_parent_id,$child_cabinet_name){
        $EdmsUploadClass = new EdmsUploadClass();
        $EdmsCabinetsClass = new EdmsCabinetsClass();

        $client_cabinet_name = $parent_cabinet_name;
        $policy_cabinet = Edms_cabinets_dtl::where('cabinet_name',$client_cabinet_name)->first();
        $aims_cabinet_id = $policy_cabinet->id;
        

        $policy_cabinet_id = $EdmsUploadClass->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);
        
        $edms_cabinet_parent_id = $policy_cabinet_id;
    

        if($parent_cabinet_name == 'Policies'){
            // dd($child_cabinet_name,$client_cabinet_name,$aims_cabinet_id,$edms_cabinet_parent_id);
            $response = $this->get_pol_cabinet_id($child_cabinet_name,$client_cabinet_name,$aims_cabinet_id,$edms_cabinet_parent_id);
            return $response;
        }else{
            try{
    
                $cabinet_name = $child_cabinet_name;
                $trans_type = null; 
    
                $response = $EdmsCabinetsClass->create_cabinet_to_edms($edms_cabinet_parent_id,$cabinet_name,$trans_type);
                

                if($response['status'] == 1){
    
                    // if policy number cabinet creation is successful, get the edms generated cabinet id and pass it as the new parent id
                    $edms_parent_id  = $response['data']['id'];
    
                    $cabinet = Edms_cabinets_dtl::where('cabinet_name',$parent_cabinet_name)->first();
    
                    $cabinet_children_parent_id= $cabinet->id;
    
                    // create sub-cabinets for the policy cabinet 
                    $EdmsCabinetsClass->create_client_children_cabinets($edms_parent_id,$cabinet_children_parent_id,$trans_type);
    
                    $status = 1;
                    $message = "Cabinet successfully created";
    
                    return [
                        'status'=> $status,
                        'message' => $message,
                        'edms_parent_id'=>$edms_parent_id
                    ];
                }else{
                    $status = 0;
                    // $message = "Failed to create the policy cabinet";
                    $message = "Failed to create the ".$parent_cabinet_name." cabinet";
                    
                    return [
                        'status'=> $status,
                        'message' => $message
                    ];
    
                }
               
    
                
            }catch(\Throwable $th){
                throw $th;
            }
        }
        
        
    }


    public function get_pol_cabinet_id($child_cabinet_name,$client_cabinet_name,$aims_cabinet_id,$edms_cabinet_parent_id){

        // dd($child_cabinet_name,$client_cabinet_name,$aims_cabinet_id,$edms_cabinet_parent_id);
        // get the cabinet id for the policy cabinet for a specific client cabinet
        $EdmsUploadClass = new EdmsUploadClass();
        $EdmsCabinetsClass = new EdmsCabinetsClass();

        $policy_dtl = Dcontrol::where('endt_renewal_no',$child_cabinet_name)->first();
        $trans_type = $policy_dtl ->trans_type;

       
        if($trans_type == 'POL'){

            $cabinet_name = $child_cabinet_name; //new policy number
            $this->create_ext_parent_cabinet($edms_cabinet_parent_id,$cabinet_name,$trans_type);
          
           
        }else{
            // check if the policy number cabinet has been created. Then find cabinet id
            $policy = Dcontrol::where('endt_renewal_no',$child_cabinet_name)->first();
            $cabinet_name = $policy -> policy_no;

           
            // get the id of the Policies cabinet of that client
            $edms_policy_id = $EdmsUploadClass->fetch_cabinets($aims_cabinet_id,$edms_cabinet_parent_id,$cabinet_name);
            
            
            // get the id of the parent policy number cabinet
            $edms_endt_id = $EdmsUploadClass->fetch_cabinets($aims_cabinet_id,$edms_policy_id,$child_cabinet_name);

            $edms_parent_id = $edms_endt_id;

            // if the cabinet is not present then generate it first with a sub cabinet with the same name
            if($edms_policy_id == null){

                $response = $this->create_ext_parent_cabinet($edms_cabinet_parent_id,$cabinet_name,$trans_type);
                
                $edms_parent_id = $response['edms_parent_id'];
                
                
            }
            
            $EdmsCabinetsClass->create_client_children_cabinets($edms_parent_id,$child_cabinet_name,$trans_type);

            $status = 1;
            $message = "Cabinet successfully created";

            return [
                'status'=> $status,
                'message' => $message,
                'edms_parent_id'=>$edms_parent_id
            ];


        }
        
    }


    public function create_ext_parent_cabinet($edms_parent_id,$cabinet_name,$trans_type){

        $EdmsUploadClass = new EdmsUploadClass();
        $EdmsCabinetsClass = new EdmsCabinetsClass();  


            try{
    
                $response = $EdmsCabinetsClass->create_cabinet_to_edms($edms_parent_id,$cabinet_name,$trans_type);
                
                
                if($response['status'] == 1){
    
                    // if policy number cabinet creation is successful, get the edms generated cabinet id and pass it as the new parent id
                    $edms_parent_id  = $response['data']['id']; //policy number cabinet id

                    $cabinet_children_parent_id = $cabinet_name;
                    
    
                    // create sub-cabinets for the policy cabinet 
                    $EdmsCabinetsClass->create_client_children_cabinets($edms_parent_id,$cabinet_children_parent_id,$trans_type);

                    
                    
                    $status = 1;
                    $message = "Cabinet successfully created";
                   
                    return [
                        'status'=> $status,
                        'message' => $message,
                        'edms_parent_id'=>$edms_parent_id
                    ];
                }else{
                    $status = 0;
                    // $message = "Failed to create the policy cabinet";
                    $message = "Failed to create the ".$parent_cabinet_name." cabinet";
                    
                    return [
                        'status'=> $status,
                        'message' => $message
                    ];
    
                }
               
    
                
            }catch(\Throwable $th){
                throw $th;
            }

        


    }


    

 




 


}