<?php

namespace App\Classes\Approvals;

// approvals
use App\Approvals;
use App\Approval_flow;

use App\SendEmail;
use App\Aimsuser_web;
use App\POheader;

use Session;
use Auth;
use Carbon\Carbon;

class ApprovalsPo{
     
    public function __construct(){

    }

    public function approval_flow_datamap($data = null){
        
        $mapped_array = array();

        if(isset($data)){
            foreach($data as $item){
                $map_item = [
                    'level_id' => (int)$item['level_id'],
                    'approver' => (int)$item['approver'],
                ];

                array_push($mapped_array,$map_item);
            }

            // sort array
            $resp = asort($mapped_array);

            return $resp;
        }
        else{
            Session::flash('error','Approval level data is missing');
        }

    }

    public function re_escalate_approval($request){
        $approvers = $request->appr;

        $sendMail = false;
        $approval = Approvals::where('approval_id',$request->approval_id)->first();
        $$po = POheader::where('po_number',$approval->po_number)->first();

        foreach($approvers as $approval){

            $old_approver = Approval_flow::where('approval_id',$request->approval_id)
                ->where('approval_level',$approval['level_id'])
                ->first();

            // update previous approver entry
            $delete_entry = Approval_flow::where('approval_id',$request->approval_id)
                ->where('approval_level',$approval['level_id'])
                ->delete();

            if($old_approver->start_approval == 'Y'){
                $sendMail = true;
            }
            else{
                $prev_approver = Approval_flow::where('approval_id',$approval->approval_id)
                    ->where('next_approval_level',$old_approver->approval_level)
                    ->first();
        
                if($prev_approver->status == 'A'){
                    $sendMail = true;
                }
            }

            // check if next approver was changed
            $approval_collect = collect($approval);
            $nxt_appr_changed = $approval_collect->contains('level_id',$old_approver->next_approval_level);
            if($nxt_appr_changed){
                $nxt_approver_rec = $approval_collect->firstWhere('level_id',$old_approver->next_approval_level);
                $nxt_approver = $nxt_approver_rec->approver;
            }
            else{
                $nxt_approver = $old_approver->next_approver_id;
            }

            $flow = $old_approver->replicate();
            $flow->approver_id = $approval['approver'];
            $flow->next_approver_id = $nxt_approver;
            $flow->status = 'P';
            $flow->re_escalated = 'Y';
            $flow->dola = Carbon::now();
            $flow->save();

            if($sendMail){
            
                $new_approver = Aimsuser_web::where('user_id',$approval->user_id)->first();
                $category = 'PURCHASE ORDER APPROVAL';
                $message = "Kindly Approve this PURCHASE ORDER: ".$approval->req_no."";
                $email = $new_approver->email;
                $name = $new_approver->name;
    
                $this->send_email($category,$email,$message,$name);
            }
        }
    }

    public function change_approval_status($data){
        // unpack data
        $approval_id = $data['approval_id'];
        $level_id = $data['level_id'];
        $status = $data['status'];

        $approval = Approvals::where('approval_id',$approval_id)->first();
        $po = POheader::where('po_number',$approval->po_number)->first();

        if(isset($approval)){
            $level = Approval_flow::where('approval_id',$approval_id)
                ->where('approval_level',$level_id)
                ->first();

            Approval_flow::where('approval_id',$approval_id)
                ->where('approval_level',$level_id)
                ->update([
                    'status' => (string)$status,
                    'dola' => Carbon::now(),
                ]);

            // notify the first approver
            switch($status){
                case 'A':
                    if($level->end_approval != 'Y'){
                        $approver = Aimsuser_web::where('user_id',$level->next_approver_id)->first();
                        $message = "Kindly Approve this Purchase Order: ".$approval->req_no."";
                    }
                    else{
                        $approver = Aimsuser_web::where('user_id',$approval->user_id)->first();
                        $message = "Purchase Order : ".$approval->req_no." 
                            has been approved. Kindly proceed.";
                        
                        Approvals::where('approval_id',$approval_id)
                            ->update([
                                'status' => 'A',
                                'dola' => Carbon::now(),
                            ]);

                        POheader::where('po_number',$approval->po_number)
                            ->update([
                                'approved' => 'Y',
                                'status' => '003',
                            ]);
                    }

                    break;
                case 'R':

                    Approvals::where('approval_id',$approval_id)
                        ->update([
                            'status' => 'R',
                            'dola' => Carbon::now(),
                        ]);

                    $approver = Aimsuser_web::where('user_id',$approval->user_id)->first();
                    $message = "Purchase Order : ".$approval->reference." 
                        has been Rejected. Kindly find more information from the system";

                    break;
                default:

                    break;
            }
            
            $category = 'Purchase Order Approval';
            $email = $approver->email;
            $name = $approver->name;

            $this->send_email($category,$email,$message,$name);

            return [
                'msg' => 'Success',
                'code' =>200

            ];
        }
        else{
            throw new \Exception("Approval ID not found");
            
        }
    }


    /**
     * Send email function
     * @param $category subject of the email
     * @param $receiver receipient of mail
     * @param $message 
     * @param $name name of the recipient
     */
    public function send_email($category,$receiver,$message,$name){
        $sendemail = new SendEmail;

        $sendemail->category = $category ;
        $sendemail->receiver =$receiver;
        $sendemail->message =$message;
        $sendemail->creator = $name;
        $sendemail->save();
    }
}