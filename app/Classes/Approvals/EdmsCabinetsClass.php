<?php

namespace App\Classes\Approvals;

use DB;
use File;

use App\Agmnf;
use App\Crmast;
use App\Glbank;
use App\Claimant;
use function env;
use App\Apvendors;

use GuzzleHttp\Client;
use App\Edms_cabinets_dtl;
use App\Client as Customer;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Notifications\ErrorNotification;
use App\Services\IntermediaryQueryService;
use Illuminate\Support\Facades\Notification;
use App\ValueObjects\IntermediaryQueryParams;


class EdmsCabinetsClass{

    public function fetch_cabinets($aims_cabinet_id){
        $client = new \GuzzleHttp\Client();

        $edms_apiurl = env('EDMS_URL');
        $url = $edms_apiurl. '/api/v4/cabinets/';
        $username = env('EDMS_USER');
        $password = env('EDMS_PASSWORD');

        $response = '';
        $status = '';

            $response = $client->get($url,[
                'auth' => [
                    $username,
                    $password
                ],
                'headers' => [
                    'Content-Type' => 'application/json'
                ]
                ]);
   
                
                $aims_cabinet_idp = (int)$aims_cabinet_id;

                $cabinet_parents_rec= Edms_cabinets_dtl::where('id',$aims_cabinet_idp)->first();
                // dd($cabinet_parents_rec);
                
                    $cabinet_label = $cabinet_parents_rec->cabinet_name;
                    $cabinet_id = (int)$cabinet_parents_rec->id;
                    $edms_parent_id = null;  //this will be static for first parent from edms 

                $contents = json_decode($response->getBody());
                $results = $contents->results;

                $next_page_count = $contents->next;
                foreach($results as $key => $value) {
                    if($value->label==$cabinet_label && $value->parent_id==$edms_parent_id) {
                        $edms_cabinet_id = $value->id;
                      
                    }
                }
                $page=1;
                $this->check_cabinet_id($edms_cabinet_id,$cabinet_label,$next_page_count,$page,$cabinet_id,$edms_parent_id); 
                
         
    }

    public function check_cabinet_id($edms_cabinet_id,$cabinet_label,$next_page_count,$page,$cabinet_id,$edms_parent_id){
    
        $client = new \GuzzleHttp\Client();
        $edms_apiurl = env('EDMS_URL');
        $url = $edms_apiurl. '/api/v4/cabinets/';
        $user = env('EDMS_USER');
        $passwd = env('EDMS_PASSWORD');
        
        
        

        if($edms_cabinet_id == null){

            //check if there is next page to load
            
            if($next_page_count==null){
                $error = response()->json(['error'=>'Page Limit has reached and no cabinet  '.$cabinet_label], 401);
                $error_message = $error->getData()->error;
                $error_code = $error->getStatusCode();
                $error_comment = $error_code. ': '.$error_message.'.';
                
                return  $error;
            }else{
                // go to next page to check cabinet label
                $page=$page + 1;
            
                
                $this->list_cabinets_next_page($cabinet_id,$cabinet_label,$page,$edms_parent_id);
            }
            
            
        }else{
            ///update cabinet
            $this->update_cabinet_id_to_aims($edms_cabinet_id,$cabinet_id,$cabinet_label,$next_page_count,$page,$edms_parent_id);
                    
        }

    }

    public function list_cabinets_next_page($cabinet_id,$cabinet_label,$page,$edms_parent_id)
    {
        $client = new \GuzzleHttp\Client();
        $edms_apiurl = env('EDMS_URL');
        $url = $edms_apiurl. '/api/v4/cabinets/?page='.$page;
        $user = env('EDMS_USER');
        $passwd = env('EDMS_PASSWORD');

        $response = '';
        $status="";

        $response = $client->get($url, [
            'auth' => [
                $user, 
                $passwd
            ],
            'headers' => [
                    'Content-Type'  => 'application/json'
                ], 

        ]);
        $contents = json_decode($response->getBody()->getContents());
        
        $results=$contents->results;
        $next_page_count = $contents->next;
    
    
        foreach($results as $key => $value) {
            if($value->label==$cabinet_label && $value->parent_id==$edms_parent_id) {
                $edms_cabinet_id = $value->id;
            
            }
        }
        
        $this->check_cabinet_id($edms_cabinet_id,$cabinet_label,$next_page_count,$page,$cabinet_id,$edms_parent_id);
        

    }

    public function update_cabinet_id_to_aims($edms_cabinet_id,$cabinet_id,$cabinet_label,$next_page_count,$page,$edms_parent_id){
    
        
        DB::connection(env('DB_CONNECTION'))->beginTransaction();
        DB::connection(env('DB_CONNECTION1'))->beginTransaction();
        try{
            $cabinet_rec_update = Edms_cabinets_dtl::where('id',$cabinet_id)
            ->update([
                'api_id'=>$edms_cabinet_id
            ]);
            // dd($cabinet_rec_update,Edms_cabinets_dtl::where('id',1)->get(['api_id']));
            DB::connection(env('DB_CONNECTION'))->commit();
            DB::connection(env('DB_CONNECTION1'))->commit();
            // echo $payment_details; 
        }

        catch(\Throwable $e){
            DB::connection(env('DB_CONNECTION'))->rollback();
            DB::connection(env('DB_CONNECTION1'))->rollback();

            throw $e;
        }
    }

    public function create_client_cabinet_from_aims_to_edms($client_intermediary_no,$cabinet_label){
    
        $cabinet_parent_id = 0; //this is static for parent cabinet in aims

        $cabinet_client_parent = Edms_cabinets_dtl::where('parent_id',$cabinet_parent_id)
                                    ->where('cabinet_name',$cabinet_label)->first();
        
        

        $cabinet = Edms_cabinets_dtl::where('parent_id',$cabinet_parent_id)
                    ->where('id',$cabinet_client_parent->id)->first();

        $parent_id = $cabinet->api_id;
        
        if($cabinet_label == 'Clients'){

            $client = Customer::where('client_number',$client_intermediary_no)->first();

            $cabinet_name = $client_intermediary_no.'-'.trim($client->name);

        }else if($cabinet_label == 'Intermediaries'){

            $branch = substr($client_intermediary_no, 0, 3);
            $agent = substr($client_intermediary_no, 3);
            
            // $intermediary = Agmnf::where('branch',$branch)->where('agent',$agent)->first();
            $intermediaryParams = new IntermediaryQueryParams([
                'agentNo' => $agent,
            ]);
            $intermediary  =IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();
            $cabinet_name = $client_intermediary_no.'-'.trim($intermediary->name);  
            
        }
        else if($cabinet_label == 'Service Providers'){

            $claimant_number = $client_intermediary_no;

            $claimant = Claimant::where('claimant_code',$claimant_number)->first();
            $cabinet_name = $claimant_number.'-'.trim($claimant->claimant_name);

        }else if($cabinet_label == 'Reinsurer'){

            $agent_number = $client_intermediary_no;

            $branch = substr($agent_number, 0, 3);
            $agent = substr($agent_number, 3);

            $intermediary = Crmast::where('branch',$branch)->where('agent',$agent)->first();

            $cabinet_name = $agent_number.'-'.trim($intermediary->name);

        }else if($cabinet_label == 'Accounts Payable'){
            $EdmsUploadClass = new EdmsUploadClass();
            $vendor_id = $client_intermediary_no;

            $vendor = Apvendors::where('vendor_id',$vendor_id)->first();
            
            $vendor_cabinet_name = $vendor_id.'-'.trim($vendor->vendor_name);
            $parent_cabinet_name = 'Financials';

            // get aims cabinet id for 'Financials'
            $cabinet = Edms_cabinets_dtl::where('parent_id',$cabinet_parent_id)
                    ->where('cabinet_name',$parent_cabinet_name)
                    ->first();
            
            $aims_parent_id = $cabinet->id;

            $edms_parent_id = $cabinet->api_id;

            $child_cabinet = Edms_cabinets_dtl::where('parent_id',$aims_parent_id)->where('cabinet_name',$cabinet_label)->first();
            $aims_cabinet_id = $child_cabinet->id;
            
            $parent_id = $EdmsUploadClass->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$cabinet_label);
            

            $cabinet_name = $vendor_cabinet_name;

        }else if($cabinet_label == 'Bank'){

            $EdmsUploadClass = new EdmsUploadClass();
            $bank_code = $client_intermediary_no;

            $bank = Glbank::where('prsno',$bank_code)->first();
            
            $bank_cabinet_name = $bank_code.'-'.trim($bank->account_name);
            $parent_cabinet_name = 'Financials';

            // get aims cabinet id for 'Financials'
            $cabinet = Edms_cabinets_dtl::where('parent_id',$cabinet_parent_id)
                    ->where('cabinet_name',$parent_cabinet_name)
                    ->first();
            
            $aims_parent_id = $cabinet->id;

            $edms_parent_id = $cabinet->api_id;

            $child_cabinet = Edms_cabinets_dtl::where('parent_id',$aims_parent_id)->where('cabinet_name',$cabinet_label)->first();
            $aims_cabinet_id = $child_cabinet->id;
            
            $parent_id = $EdmsUploadClass->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$cabinet_label);
            

            $cabinet_name = $bank_cabinet_name;
        }
        

        $response = $this->create_cabinet_to_edms($parent_id,$cabinet_name);
        

        // $data = $response->getData();
        
        $status = $response['status'];

        if($status == 1){
            $data = $response['data'];
            $id = $data['id'];
    
            $resp = $response['response'];
            $statusCode = $resp->getStatusCode();
    
            if ($data !== null) {
                $edms_parent_id = $data['id'];
                $message = "Cabinet created successfully";
                
                $cabinet_children_parent_id = $cabinet->id;
                $trans_type = null;

                if($cabinet_label == 'Accounts Payable'|| $cabinet_label == 'Bank'){
                    $parent_cabinet = Edms_cabinets_dtl::where('cabinet_name',$cabinet_label)->where('parent_id',$cabinet_children_parent_id)->first();
                    $cabinet_children_parent_id = $parent_cabinet->id;
                }

                $this->create_client_children_cabinets($edms_parent_id,$cabinet_children_parent_id,$trans_type);
                
                ///update client edms_cabinet_created to Yes
                try{
                    $update_client_details = Customer::where('client_number',$client_intermediary_no)
                        ->update([
                            'edms_cabinet_created'=>'Y'
                        ]);

                    return [
                        'status'=> $statusCode,
                        'message' => $message,
                        'edms_parent_id'=> $edms_parent_id
                    ];
                }
                
                    catch(\Throwable $e){
                        throw $e; 
                }
                
            } else {
                // Handle the case when the response body is not valid JSON
                // Handle the error accordingly
                return [
                    'status'=> 500
                ];
            }
        } else if($status == 0){

            $update_client_details = Customer::where('client_number',$client_intermediary_no)
                                    ->update([
                                        'edms_cabinet_created'=>'Y'
            ]);

            return [
                'status'=> 400,
                'message' => $response['message'],
            ];

        }else{
            return [
                'status'=> 500
            ];
        }

        
        
        
    }

    public function create_client_children_cabinets($edms_parent_id,$cabinet_children_parent_id,$trans_type){
        if($trans_type == null){
            $cabinets = Edms_cabinets_dtl::where('parent_id',$cabinet_children_parent_id)->get();
            foreach($cabinets as $cabinet) {
                $this->create_cabinet_to_edms($edms_parent_id,$cabinet->cabinet_name);
            }
        }else{

            $this->create_cabinet_to_edms($edms_parent_id,$cabinet_children_parent_id);

        }
        
    }

    public function create_cabinet_to_edms($parent_id,$cabinet_name){

        try {
            
            $client = new \GuzzleHttp\Client();
        
            $edms_apiurl = env('EDMS_URL');
            $url = $edms_apiurl. '/api/v4/cabinets/';
            $username = env('EDMS_USER');
            $password = env('EDMS_PASSWORD');
    
            
    
            $response = '';
            $status = '';
            // dd($parent_id,$cabinet_name);

            $response = $client->post($url, [
                'auth' => [
                    $username, 
                    $password
                ],
                'multipart' => [
                    [ 
                        'name' => 'parent',
                        'contents' =>$parent_id
                    ],
                    [ 
                        'name' => 'label',
                        'contents' =>$cabinet_name
                    ]
                ]
            ]);

            $responseBody = $response->getBody()->getContents();
            
            $data = json_decode($responseBody, true);

            return [
                'status'=> 1,
                'data' => $data,
                'response' => $response
            ];


        }catch( \Exception $e){
            
            // Get the exception message which contains the full response information
            $exceptionMessage = $e->getMessage();

            // Parse the JSON part of the response message
            $responseJson = substr($exceptionMessage, strpos($exceptionMessage, '{'));
            
            // Decode the JSON into an associative array
            $responseData = json_decode($responseJson, true);
            

            // Access the specific parts of the response data
            $statusCode = $e->getCode(); // HTTP status code (e.g., 400)

            $statusText = $e->getResponse()->getReasonPhrase(); // HTTP status text (e.g., "Bad Request")
            $nonFieldErrors = $responseData['non_field_errors'];
            

            if($statusCode == 400 ){
                if($nonFieldErrors[0] == "The fields parent, label must make a unique set." ){

                    $statusText = "The cabinet has already been created in EDMS";
  
                }
                
            }else{
                $statusText = "Cabinet creation failed";
            }

            return [
                'status'=> 0,
                'message' => $statusText
            ];
            
        }

    }

    // create parent cabinets and update their cabinet ids to the cabinets_dtl table
    public function create_initial_cabinet_to_edms($aims_cabinet_id){

        $cabinet_parent_id = 0; //this is static for parent cabinet in aims

        $edms_parent_id = null;

        $parent_cabinet = Edms_cabinets_dtl::where('id',$aims_cabinet_id)->first();

        $result = $this->create_cabinet_to_edms($edms_parent_id,$parent_cabinet->cabinet_name);
        
        $cabinet_id = $result['data']['id'];
        
        

        $update_api_details = Edms_cabinets_dtl::where('id',$aims_cabinet_id)
                        ->update([
                            'api_id'=>$cabinet_id
                        ]);


        return $result;
                                    

    }



}
   