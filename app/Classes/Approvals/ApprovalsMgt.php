<?php

namespace App\Classes\Approvals;

// approvals
use App\Approvals;
use App\Approval_flow;

use App\SendEmail;
use App\Aimsuser_web;
use App\Clhmn;
use App\Acdet;

use Session;
use Auth;
use Carbon\Carbon;

use App\ImItemReq;
use App\ImItemReqDtl;
use App\PoReqHeader;
use App\POInvoice_header;
use DB;
use App\Apvendorinvoices;
use App\POInvoice_dtls;
use App\POInvoice_taxes;
use App\StockTakeHeader;
use App\Im_Inventoryitems;
use App\StockTakeDtl;
use App\Fa_disposal_dtls;
use App\Fa_disposal_header;
use App\Fa_disposal_dtls_items;
use App\Classes\gl\AutoGenBatch;
use App\Batch_list;
use App\ApInvoiceHeader;
use App\Cbmast;
use App\PoDocType;
use App\FAacquisitions;
use App\Appaymentheader;
use App\FAHeaderDtl;
use App\Clmhist;
use App\FAHeader;
use App\GlMasterbudget;
use App\Http\Controllers\gl\fo\ReceiptDetails;
use Illuminate\Http\Request;
use App\Http\Controllers\gl\im\IMController;
use App\Payreqst;
use App\Events\ClaimProcessedEvent;
use App\Http\Controllers\gl\fo\PAYController;

class ApprovalsMgt{
     
    public function __construct(){

    }

    public function approval_flow_datamap($data = null){
        
        $mapped_array = array();

        if(isset($data)){
            foreach($data as $item){
                $map_item = [
                    'level_id' => (int)$item['level_id'],
                    'approver' => (int)$item['approver'],
                ];

                array_push($mapped_array,$map_item);
            }

            // sort array
            $resp = asort($mapped_array);

            return $resp;
        }
        else{
            Session::flash('error','Approval level data is missing');
        }

    }
    public function raise_approval($data){

    }

    public function re_escalate_approval($request){
       
        $filtered_appr = array_filter($request->appr, function ($item) {
            return isset($item["level_descr"], $item["level_id"], $item["hierarchy_id"], $item["approver"]);
        });

        $approvers = array_values($filtered_appr);
        

        $sendMail = false;
        $approval = Approvals::where('approval_id',$request->approval_id)->first();
        $clhmn = Clhmn::where('claim_no',$approval->claim_no)->first();
        $acdet = Acdet::where('endt_renewal_no',$clhmn->endt_renewal_no)->where("source",'U/W')->first();

        foreach($approvers as $approval){
            
            $old_approver = Approval_flow::where('approval_id',$request->approval_id)
                ->where('approval_level',$approval['level_id'])
                ->first();
                
            $apr_status = Approval_flow::where('approval_id',$request->approval_id)
                        ->where('next_approval_level',$approval['level_id'])
                        ->first();

            if($apr_status->status == 'A'){
                $status = 'Y';
            }elseif($apr_status->status == null){
                $status = 'Y';
            }else{
                $status = null;
            }

            // update previous approver entry
            $delete_entry = Approval_flow::where('approval_id',$request->approval_id)
                ->where('approval_level',$approval['level_id'])
                ->delete();

            if($old_approver->start_approval == 'Y'){
                $sendMail = true;
            }
            else{
                $prev_approver = Approval_flow::where('approval_id',$approval->approval_id)
                    ->where('next_approval_level',$old_approver->approval_level)
                    ->first();
        
                if($prev_approver->status == 'A'){
                    $sendMail = true;
                }
            }

            // check if next approver was changed
            $approval_collect = collect($approval);
            $nxt_appr_changed = $approval_collect->contains('level_id',$old_approver->next_approval_level);
            if($nxt_appr_changed){
                $nxt_approver_rec = $approval_collect->firstWhere('level_id',$old_approver->next_approval_level);
                $nxt_approver = $nxt_approver_rec->approver;
            }
            else{
                $nxt_approver = $old_approver->next_approver_id;
            }

            $flow = new Approval_flow(); // Create a new instance instead of using replicate()
            $flow->approval_id = $request->approval_id;
            $flow->approval_level = $approval['level_id'];
            $flow->approver_id = $approval['approver'];
            $flow->next_approver_id = $nxt_approver;
            $flow->status = 'P';
            $flow->start_approval = $status;
            $flow->re_escalated = 'Y';
            $flow->dola = Carbon::now();
            $flow->end_approval = $old_approver->end_approval;
            $flow->save();

            if($sendMail){
            
                $new_approver = Aimsuser_web::where('user_id',$approval->user_id)->first();
                if($request->approval_process == 'MJV'){
                    $category = 'BATCH APPROVAL';
                    $message = "Kindly Approve this Batch: ".$request->disp_no.".";

                }
                else if($request->approval_process == 'MJVREV'){
                    $category = 'BATCH REVERSAL APPROVAL';
                    $message = "Kindly Approve this Batch Reversal: ".$request->disp_no.".";

                }else if($request->approval_process == 'REQAP'){
                    $category = 'CLAIM REQUISITION APPROVAL';
                    $message = "Kindly Approve this Claim Requisition: ".$request->req_no.".";

                }
                else if($request->approval_process == 'PYQAP'){
                    $category = 'CLAIM PAYMENT APPROVAL';
                    $message = "Kindly Approve this Claim Payment Requisition: ".$request->req_no.".";

                }
                else if(in_array($request->approval_process, ['RECREV', 'ASTREQ','BGTAPR','BGTREF','REPREQ','APRGLB','APRGLR','SIR'])){
                    $category = 'APPROVAL';
                    $message = "Kindly Approve this : ".$request->disp_no.".";

                }
                else{
                    $category = 'OUTSTANDING PREMIUM APPROVAL';
                    $message = "Kindly Approve this claim: ".formatPolicyOrClaim($request->appr_claim_no).".
                        It has an outstanding premium of ".number_format($acdet->unallocated).".";

                }

                $email = $new_approver->email;
                $name = $new_approver->name;
    
                $this->send_email($category,$email,$message,$name);
            }
        }
    }

    public function change_approval_status($data){

        // unpack data
        $approval_id = $data['approval_id'];
        $level_id = $data['level_id'];
        $status = $data['status'];
        $approval_comment = $data['approval_comment'];
        $payment_date = $data['payment_date'];

        
        $approval = Approvals::where('approval_id',$approval_id)->first();
        $clhmn = Clhmn::where('claim_no',$approval->claim_no)->first();
        $acdet = Acdet::where('endt_renewal_no',$clhmn->endt_renewal_no)->where("source",'U/W')->first();
      
        $approval_process = $data['approval_process'];
        $continue = 'Y';
   
        if(isset($approval)){
            $level = Approval_flow::where('approval_id',$approval_id)
                ->where('approval_level',$level_id)
                ->first();

            $ap_flow = Approval_flow::where('approval_id',$approval_id)
                ->where('approval_level',$level_id)
                ->update([
                    'status' => (string)$status,
                    'comments'=>$approval_comment,
                    'dola' => Carbon::now(),
                ]);
            // notify the first approver
            switch($status){
                case 'A':
                    if($level->end_approval != 'Y'){
                        $update_nxt = Approval_flow::where('approval_id',$approval_id)
                        ->where('approver_id',$level->next_approver_id)
                        ->where('approval_level',$level->next_approval_level)
                        ->update([
                            'start_approval' => 'Y'
                        ]);

                        if($approval_process == 'POINV')
                        {
                            $category = 'PO INVOICE APPROVAL';
                            $message = "Kindly approve this invoice ".$approval->invoice_no."";
                        }
                        elseif($approval_process == 'REQAP'){

                            $category = 'REQUISITION APPROVAL';
                            $message = "Kindly approve this Requisition ".$approval->req_no."";
                            Payreqst::where('req_no',$approval->req_no)
                                ->update([
                                    'checked_date' =>Carbon::now(),
                                    'checked_by' => trim(auth()->id()),
                                ]);

                            $clmhistData = [
                                'claim_no' => $clhmn->claim_no,
                                'status_code' => 909,
                                'slug' => 'requisition-checked',
                                'overide_status_desc' => 'Y',
                                'additional_comment' => 'checked Req no '. formatRequisitionNo($approval->req_no) ,
                                'user_str'=> Auth::user()->user_name,
                                'status_date' => Carbon::now(),
                            ];

                            ClaimProcessedEvent::dispatch((object)$clmhistData);
                            

                        }
                        else if ($approval_process == 'PYQAP') {
                            $category = 'REQUISITION PAYMENT APPROVAL';
                            $message = "REQUISITION : ".$approval->req_no." has been approved.";
                            Payreqst::where('req_no',$approval->req_no)
                                    ->update([
                                        'checked_date' =>Carbon::now(),
                                        'checked_by' => trim(auth()->id()),
                                    ]);

                        }

                        else if($approval_process == 'APAPR')
                        {
                            $message = "AP Payment ".$approval->req_no." has been approved.";

                            $category = 'AP PAYMENT APPROVAL';
                            
                        }
                        elseif($approval_process == 'APINV')
                        {
                            $category = 'AP INVOICE APPROVAL';
                            $message = "Kindly approve this invoice ".$approval->invoice_no."";
                        }
                        
                        elseif($approval_process == 'SCT'){
                            $category = 'STOCK ADJUSTMENT APPROVAL';

                            $message = "Kindly approve this Stock Take Count ".$approval->stk_take_code."";
                        }
                        
                        else if($approval_process == 'REQ' || $approval_process == 'ITEMREQ')
                        {
                            $category = 'REQUISITION APPROVAL';

                            $message = "Kindly approve this requisition: ".$approval->req_no."";
                        }

                        elseif($approval_process == 'ITEMDISP'){
                            $category = 'DISPOSAL APPROVAL';

                            $message = "Kindly approve this Disposal ".$approval->req_no."";
                        }

                        elseif($approval_process == 'MJV'){
                            $category = 'DISPOSAL APPROVAL';

                            $message = "Kindly approve this Disposal ".$approval->req_no."";

                            Batch_list::where('batch_no',$approval->req_no)
                            ->update([
                                'authorized_by' => Auth::user()->user_name
                            ]);

                        }

                        elseif($approval_process == 'MJVREV'){
                            $category = 'DISPOSAL APPROVAL';

                            $message = "Kindly approve this Disposal ".$approval->req_no."";
                        }

                        elseif($approval_process == 'RECREV'){
                            $category = 'RECEIPT REVERSAL APPROVAL';

                            $message = "Kindly approve this Receipt Reversal ".$approval->req_no."";
                        }
                        elseif($approval_process == 'ASTREQ'){
                            $category = 'HARDWARE REQUEST APPROVAL';

                            $message = "Kindly approve this Request ".$approval->req_no."";
                        }
                        elseif($approval_process == 'REPREQ'){
                            $category = 'REPLACEMENT REQUEST APPROVAL';

                            $message = "Kindly approve this Request ".$approval->req_no."";
                        }
                        elseif($approval_process == 'BGTAPR'){
                            $category = 'BUDGET APPROVAL REQUEST';

                            $message = "Kindly approve this BUDGET Approval Request ".$approval->req_no."";
                        }
                        elseif($approval_process == 'BGTREF'){

                            $category = 'BUDGET APPROVAL REQUEST';

                            $message = "Kindly approve this BUDGET Refocus Request ".$approval->req_no."";
                        }
                        elseif($approval_process == 'REVPV'){

                            $category = 'PV CANCELLATION APPROVAL REQUEST';

                            $message = "Kindly approve this Request ".$approval->req_no."";
                        }
                        elseif($approval_process == 'APRGLB' || $approval_process == 'APRGLR'){

                            $category = 'BUDGET APPROVAL REQUEST';

                            $message = "Kindly approve this Request ".$approval->req_no."";
                        }
                        elseif($approval_process == 'BNKTR'){

                            $category = 'BANK TRANSCATION APPROVAL REQUEST';

                            $message = "Kindly approve this Request ".$approval->req_no."";
                        }

                        else{
                            $message = "Kindly approve this claim: ".formatPolicyOrClaim($approval->claim_no).".
                            It has an outstanding premium of ".number_format($acdet->unallocated).".";

                            $category = 'OUTSTANDING PREMIUM APPROVAL';
                        }

                        $approver = Aimsuser_web::where('user_id',$level->next_approver_id)->first();
                    }else{

                        if($approval_process == 'POINV')
                        {
                            $message = "Invoice ".$approval->req_no." has been approved.";

                            $category = 'PO INVOICE APPROVAL';

                            $invoice =  POInvoice_header::where('po_number',  $approval->po_number)
                            ->where('invoice_no',  $approval->invoice_no)->where('vendor_id', $approval->vendor_id)->get()[0];
                
                            $this->integratePOinv($invoice);
                            ##update po invoice
                            $upd_invoice = POInvoice_header::where('po_number',  $approval->po_number)
                            ->where('invoice_no',  $approval->invoice_no)->where('vendor_id', $approval->vendor_id)
                            ->update([
                                'approved' => 'Y',
                                'dola' =>Carbon::now(),
                                'approved_by' => trim(auth()->id()),
                                'status' => '003',
                                'INTEGRATED_TO_AP' => 'Y'
                            ]);
                            
                            ##generate batch class 
                            $autogenbatch = new AutoGenBatch($invoice->account_year, $invoice->account_month, 'AP', $invoice->invoice_no,$invoice->vendor_id, 'INV');
                            $autogenbatch->process_batch();
                        }

                        else if($approval_process == 'APINV')
                        {
                            $message = "Invoice ".$approval->req_no." has been approved.";

                            $category = 'AP INVOICE APPROVAL';

                            $invoice = ApInvoiceHeader::where('invoice_no',  $approval->invoice_no)->where('vendor_id', $approval->vendor_id)->get()[0];
                            
                            ##update ao invoice
                            $upd_invoice = ApInvoiceHeader::where('invoice_no',  $approval->invoice_no)->where('vendor_id', $approval->vendor_id)
                                ->update([
                                    'approved' => 'Y',
                                    'dola' =>Carbon::now(),
                                    'approved_by' => trim(auth()->id()),
                                    'approval_status' => '003',
                                ]);
                               
                            ##generate batch class 
                            $autogenbatch = new AutoGenBatch($invoice->account_year, $invoice->account_month, 'AP', $invoice->invoice_no,$invoice->vendor_id, 'INV');

                            $autogenbatch->process_batch();
                            if ($autogenbatch['code'] == 404) {
                                $continue = 'N';
                             }
                            
                        }else if($approval_process == 'APAPR')
                        {
                            $transaction_no = $approval->req_no;
                            $category = 'TRANSACTION REQUEST APPROVAL';
                            $message = "TRANSACTION REQUEST : ".$approval->req_no." has been approved.";

                            $payment = Appaymentheader::where('payment_header_no', $approval->req_no)
                                        ->get(['vendor_id', 'payment_header_no', 'account_year', 'account_month', 'batch_no'])[0];
                           
                            $autogenbatch = new AutoGenBatch($payment->account_year, $payment->account_month, 'AP', $payment->payment_header_no, $payment->vendor_id, 'PAY');
                            
                            $batch_result = $autogenbatch->process_batch();

                            if ($batch_result['code'] == 404) {
                               $continue = 'N';
                            }            
                        }
                        
                        else if ($approval_process == 'SCT') {
                            $category = 'STOCK ADJUSTMENT APPROVAL';

                            $message = "Stock take : ".$approval->stk_take_code." has been approved.";

                            $this->updateInvoiceStkQty($approval->stk_take_code);
                            
                            StockTakeHeader::where('stk_take_code',$approval->stk_take_code)
                                ->update([
                                    'approved' => 'Y',
                                    'dola' =>Carbon::now(),
                                    'approved_by' => trim(auth()->id()),
                                ]);

                        }
                                
                        else if ($approval_process == 'REQAP') {
                            $category = 'REQUISITION APPROVAL';
                            $message = "REQUISITION : ".$approval->req_no." has been approved.";
                            $updated = Payreqst::where('req_no',$approval->req_no)
                                ->update([
                                    'clauthorized_date' =>Carbon::now(),
                                    'clauthorized_by' => trim(auth()->id()),
                                    'escalate_id'=>'50',
                            ]);

                        }
                        else if ($approval_process == 'PYQAP') {
                            $category = 'REQUISITION PAYMENT APPROVAL';
                            $message = "REQUISITION : ".$approval->req_no." has been approved.";
                            Payreqst::where('req_no',$approval->req_no)
                                ->update([
                                    'approved_date' =>Carbon::now(),
                                    'approved_by' => trim(auth()->id()),
                                    'escalate_id'=>'40',
                                ]);

                            // post payments
                            $pay_ctl = new PAYController;

                            $requestData = new Request([
                                'req_no' => $approval->req_no,
                                'payment_date' => $payment_date,
                            ]);

                            $pay_ctl->storePayment($requestData);

                        }

                        else if($approval_process == 'ITEMREQ'){
                            $category = 'REQUISITION APPROVAL';

                            $message = "Requisition : ".$approval->req_no." has been approved.";

                            ImItemReq::where('req_no',$approval->req_no)
                                ->update([
                                    'approved' => 'Y',
                                    'dola' =>Carbon::now(),
                                    'approved_by' => trim(auth()->id())
                                ]);
                        }
                        
                        else if($approval_process == 'REQ'){
                            $category = 'REQUISITION APPROVAL';

                            $message = "Requisition : ".$approval->req_no." has been approved.";

                            PoReqHeader::where('req_no',$approval->req_no)
                            ->update([
                                'approved' => 'Y',
                                'dola' =>Carbon::now(),
                                'approved_by' => trim(auth()->id()),
                                'approved_date' =>Carbon::now()
                            ]);
                        }
                        
                        else if($approval_process == 'ITEMDISP'){

                            $category = 'DISP APPROVAL';

                            $message = "DISPOSAL : ".$approval->req_no." has been approved.";

                            $updt_fa = Fa_disposal_header::where('disposal_no',$approval->req_no)
                                                            ->update([
                                                                'status' => '004'
                                                            ]);

                                         
                        }
                        
                        else if($approval_process == 'MJV'){

                            $category = 'BATCH APPROVAL';

                            $message = "BATCH : ".$approval->req_no." has been approved.";

                            Batch_list::where('batch_no',$approval->req_no)
                            ->update([
                                'status' => '004',
                                'approved' => 1,
                                'approved_by' => Auth::user()->user_name
                            ]);
                        }
                        
                        else if($approval_process == 'MJVREV'){

                            $category = 'BATCH REVERSAL APPROVAL';

                            $message = "BATCH REVERSAL : ".$approval->req_no." has been approved.";

                            Batch_list::where('batch_no',$approval->req_no)
                            ->update([
                                'status' => '005'
                            ]);
                        }
                        
                        else if($approval_process == 'RECREV'){

                            $dtrans_no =  substr($approval->req_no, 0, 6);
                            $account_year =  substr($approval->req_no, 6, 10);

                            $cbmast = Cbmast::where('doc_type', 'REC')
                            ->where('dtrans_no', intval($dtrans_no))
                            ->where('account_year', intval($account_year))
                            ->first();

                            $bindings = [
                                "dtran" => $cbmast->dtrans_no,
                                "year" => $cbmast->account_year,
                                "offcd" => $cbmast->offcd,
                                "entry_type" => $cbmast->entry_type_descr,
                                "doc_type" => $cbmast->doc_type,
                                "reason" => ''
                            ];
                            
                            $request = new Request($bindings);
                            
                            $receiptdtl = new ReceiptDetails();
                            $reverse = $receiptdtl->storeReversal($request);                            
                            

                            $category = 'RECEIPT REVERSAL APPROVAL';

                            $message = "RECEIPT REVERSAL : ".$approval->req_no." has been approved.";


                        }
                        
                        else if($approval_process == 'BGTAPR'){
                                    
                            $budget_id = $approval->req_no;
                            $parts = explode('/', $budget_id);
                            $budgetid = $parts[0];

                            DB::table('budget_company')
                                ->where('budget_id','<>', $budgetid)
                                ->update(['running_budget' => 'N']);

                            DB::table('budget_company')
                                ->where('budget_id', $budgetid)
                                ->update(['running_budget' => 'Y','edit'=> 'Y','locked'=> 'N']);
                
                            DB::table('budget_company')
                                ->where('budget_id', $budgetid)
                                ->update(['status' => 'ACTIVE']);
                
                            DB::table('budget_uw')
                                ->where('budget_id','<>', $budgetid)
                                ->update(['running_budget' => 'N']);
                
                            DB::table('budget_uw')
                                ->where('budget_id', $budgetid)
                                ->update(['running_budget' => 'Y']);


                            $category = 'BUDGET APPROVAL';

                            $message = "BUDGET : ".$approval->req_no." has been approved.";


                        }
                        
                        else if($approval_process == 'BGTREF'){
                                    
                            $budget_id = $approval->req_no;
                            $parts = explode('/', $budget_id);
                            $budgetid = $parts[0];
             
                            DB::table('budget_company')
                                ->where('budget_id', $budgetid)
                                ->where('running_budget', 'Y')
                                ->update([
                                    'edit'=>'Y',
                                    'locked'=>'N'
                                ]);

                            DB::table('budget_uw_forecast')
                                ->where('budget_id', $budgetid)
                                ->where('running_budget', 'N')
                                ->update(['running_budget' => 'Y']);


                            $category = 'BUDGET APPROVAL';

                            $message = "BUDGET REFOCUS : ".$approval->req_no." has been approved.";


                        }
                        else if($approval_process == 'ASTREQ'){
                            
                            $today = Carbon::today();
                            $year = $today->format('Y');

                            $doctype = 'REQ';
                            $req_serial = $this->genReqNo();
                            $req_no = $doctype.STR_PAD($req_serial, 6, '0', STR_PAD_LEFT) . $year;

                            $request_item = FAacquisitions::where('id',$approval->req_no)->first();

        
                            $bindings = [
                                "req_date" => Carbon::now(),
                                "requisitioned_by" => Auth::user()->user_name,
                                "description" => $request_item->asset_description,
                                "dept" => $request_item->department
                            ];

  
                
                            $request = new Request($bindings);
                            $new_req = new ImItemReq;
                
                            $new_req->req_no = $req_no;
                            $new_req->req_date = $request->req_date;
                            $new_req->requisitioned_by = $request->requisitioned_by;
                            $new_req->description = $request->description;
                            $new_req->prsno = $request->dept;
                            $new_req->cancelled = 'N';
                            $new_req->status = '001';
                            $new_req->approved = 'N';
                            $new_req->process_code = 6;
                            $new_req->no_of_items = 0;
                            $new_req->created_by = auth()->id();
                            $new_req->created_at = Carbon::now();
                            $new_req->save();

                            for($i=0;$i < $request_item->quantity;$i++){

                                $random = FAHeaderDtl::where('asset_code', $request_item->asset_code)
                                                    ->where('status', 'A')
                                                    ->orderByRaw('DBMS_RANDOM.VALUE')
                                                    ->first();


                                $bindings = [
                                    "fa_asset" => $request_item->asset_code,
                                    "req_no" => $req_no,
                                    "process_code" => "6",
                                    "asset_item" => $random->item_code,
                                    "description" => $request_item->asset_description,
                                    "quantity" => "1",
                                    "destination" => 'I',
                                    "issued_to" => $request_item->firstname,
                                    "offcd" => $request_item->offcd
                                ];
        
        
                                $request = new Request($bindings);
                                $addReqitem = (new IMController)->addReqItem($request);
        
        
                                
                            }
                
                            $this->updDoctypeReqSerial();

                            $request_item->update(['status'=>'approved']);

                            $category = 'ASSET REQUEST APPROVAL';

                            $message = "ASSET REQUEST : ".$approval->req_no." has been approved.";


                        }
                        else if($approval_process == 'REPREQ'){
                            
                            $today = Carbon::today();
                            $year = $today->format('Y');

                            $doctype = 'REQ';
                            $req_serial = $this->genReqNo();
                            $req_no = $doctype.STR_PAD($req_serial, 6, '0', STR_PAD_LEFT) . $year;

                            $request_item = FAacquisitions::where('id',trim($approval->req_no))->first();

                            $cancel_issuance = ImItemReqDtl::where('item_code',trim($request_item->item_code))
                                                        ->where('cancelled','N')
                                                        ->where('complete_issue','Y')
                                                        ->update([
                                                            'cancelled' => 'Y',
                                                            'complete_issue' => 'N'
                                                        ]);

                             FAHeaderDtl::where('item_code', trim($request_item->item_code))
                                            ->update([
                                                'status' => 'A'
                                            ]);

                            FAHeader::where('asset_code', trim($request_item->asset_code))
                                            ->update([
                                                'issued_qty' => \DB::raw('issued_qty - 1'),
                                                'qty_in_stk' => \DB::raw('qty_in_stk + 1'),
                                            ]);
                                        

        
                            $bindings = [
                                "req_date" => Carbon::now(),
                                "requisitioned_by" => Auth::user()->user_name,
                                "description" => $request_item->asset_description,
                                "dept" => $request_item->department
                            ];

  
                
                            $request = new Request($bindings);
                            $new_req = new ImItemReq;
                
                            $new_req->req_no = $req_no;
                            $new_req->req_date = $request->req_date;
                            $new_req->requisitioned_by = $request->requisitioned_by;
                            $new_req->description = $request->description;
                            $new_req->prsno = $request->dept;
                            $new_req->cancelled = 'N';
                            $new_req->status = '001';
                            $new_req->approved = 'N';
                            $new_req->process_code = 6;
                            $new_req->no_of_items = 0;
                            $new_req->created_by = auth()->id();
                            $new_req->created_at = Carbon::now();
                            $new_req->save();

                            for($i=0;$i < $request_item->quantity;$i++){

                                $random = FAHeaderDtl::where('asset_code', $request_item->asset_code)
                                                    ->where('item_code','!=', trim($request_item->item_code))
                                                    ->where('status', 'A')
                                                    ->orderByRaw('DBMS_RANDOM.VALUE')
                                                    ->first();
                                        


                                if($request_item->requested_for == 'C'){
                                    $destination = 'C';
                                    $issued_to = $request_item->offcd;
                                }else{
                                    $destination = 'I';
                                    $issued_to = Aimsuser::where('user_name',trim($request_item->user))->get()[0]['user_id'];
                                }
                   
                                $bindings = [
                                    "fa_asset" => $request_item->asset_code,
                                    "req_no" => $req_no,
                                    "process_code" => "6",
                                    "asset_item" => $random->item_code,
                                    "description" => $request_item->asset_description,
                                    "quantity" => "1",
                                    "destination" => $destination,
                                    "issued_to" =>  $issued_to,
                                    "offcd" => $request_item->offcd
                                ];
        
        
                                $request = new Request($bindings);
                                $addReqitem = (new IMController)->addReqItem($request);
        
        
                                
                            }
                
                            $this->updDoctypeReqSerial();

                            $request_item->update(['status'=>'approved']);

                            $category = 'ASSET REQUEST APPROVAL';

                            $message = "ASSET REQUEST : ".$approval->req_no." has been approved.";


                        }
                        else if($approval_process == 'REVPV'){
                            

                            $cbmast = Cbmast::where('doc_type','PAY')
                                            ->where('entry_type_descr','!=','PAY')
                                            ->where('dtrans_no',intval($approval->claim_no))
                                            ->where('account_year',intval($approval->account_year))
                                            ->update([
                                                'reversal_approved' => 'Y'
                                            ]);


                            $category = 'REVERSAL REQUEST APPROVAL';

                            $message = "REVERSAL REQUEST : ".$approval->req_no." has been approved.";


                        }
                        else if($approval_process == 'APRGLB'){
                            

                            $budget_id = $approval->req_no;
                            $parts = explode('/', $budget_id);
                            $budgetid = $parts[0];

                            $cbmast = GlMasterbudget::where('budget_id', $budgetid)
                                            ->update([
                                                'approved' => 'Y',
                                                'is_active' => 'Y',
                                                'status' => 'A',
                                                'approved_by' =>Auth::user()->user_name
                                            ]);


                            $category = 'BUDGET REQUEST APPROVAL';

                            $message = "BUDGET REQUEST : ".$approval->req_no." has been approved.";


                        }

                        else if($approval_process == 'APRGLR'){

                            $budget_id = $approval->req_no;
                            $parts = explode('/', $budget_id);
                            $budgetid = $parts[0];

                            $cbmast = GlMasterbudget::where('budget_id', $budgetid)
                                            ->update([
                                                'approved' => 'Y',
                                                'is_active' => 'N',
                                                'lock_budget' => 'N',
                                                'status' => 'I',
                                                'approved_by' =>Auth::user()->user_name
                                            ]);


                            $category = 'BUDGET REQUEST APPROVAL';

                            $message = "BUDGET REQUEST : ".$approval->req_no." has been approved.";


                        }
                        else if($approval_process == 'BNKTR'){

                            $transaction_no = $approval->req_no;


                            $category = 'TRANSACTION REQUEST APPROVAL';

                            $message = "TRANSACTION REQUEST : ".$approval->req_no." has been approved.";

                        }  

                        else{
                            $category = 'OUTSTANDING PREMIUM APPROVAL';

                            $message = "Claim : ".formatPolicyOrClaim($approval->claim_no)." 
                                has been approved. Kindly proceed.";
                        } 
                         
                        Approvals::where('approval_id',$approval_id)
                        ->update([
                            'status' => 'A',
                            'dola' => Carbon::now(),
                        ]);  
                          
                    }


                    break;
                case 'R':

                    Approvals::where('approval_id',$approval_id)
                        ->update([
                            'status' => 'R',
                            'dola' => Carbon::now(),
                        ]);

                    $approver = Aimsuser_web::where('user_id',$approval->user_id)->first();

                    if($approval_process == 'POINV')
                    {
                        $message = "Invoice : ".$approval->invoice_no." 
                        has been Rejected!";

                        $category = 'PO INVOICE APPROVAL';
                    }
                    
                    elseif ($approval_process == 'SCT') {

                        $message = "Stock Take : ".$approval->stk_take_code." 
                        has been Rejected!";

                        $category = 'STOCK ADJUSTMENT APPROVAL';
                    }

                    else if($approval_process == 'REQ' || $approval_process == 'ITEMREQ')
                    {
                        $message = "Requisition : ".$approval->reference." 
                        has been Rejected!";

                        $category = 'REQUISITION APPROVAL';
                    }
                      
                    else if($approval_process == 'ITEMDISP'){

                        $category = 'DISP APPROVAL';

                        $message = "DISPOSAL : ".$approval->req_no." has been rejected.";

                        Fa_disposal_header::where('disposal_no',$approval->req_no)
                        ->update([
                            'status' => '006'
                        ]);
                    }
                      
                    else if($approval_process == 'MJV'){

                        $category = 'BATCH APPROVAL';

                        $message = "BATCH : ".$approval->req_no." has been rejected.";

                        $update_batch = Batch_list::where('batch_no',$approval->req_no)
                                                    ->update([
                                                        'declined' => 1,
                                                        'approved_by' =>Auth::user()->user_name
                                                    ]);

       
                    }
                      
                    else if($approval_process == 'RECREV'){

                        $category = 'RECEIPT REVERSASL APPROVAL';

                        $message = "RECEIPT : ".$approval->req_no." has been rejected.";
       
                    }
                      
                    else if($approval_process == 'ASTREQ'){

                        $request_item = FAacquisitions::where('id',$approval->req_no)->update(['status'=>'rejected']);

                        $category = 'ASSET REQUEST APPROVAL';

                        $message = "REQUEST : ".$approval->req_no." has been rejected.";
       
                    }

                    else{
                        $message = "Claim : ".formatPolicyOrClaim($approval->reference)." 
                            has been Rejected. Kindly find more information from the system";

                        $category = 'OUTSTANDING PREMIUM APPROVAL';
                    }

                    break;
                default:

                    break;
            }
 
            $email = $approver->email;
            $name = $approver->name;
            
            $this->send_email($category,$email,$message,$name);

            if ($continue == 'N') {
                return [
                    'msg' => 'Out balance',
                    'code' =>404
                ];
            }else{
                return [
                    'msg' => 'Success',
                    'code' =>200
                ];

            }

          
        }
        else{
            throw new \Exception("Approval ID not found");
            
        }
    }


    /**
     * Send email function
     * @param $category subject of the email
     * @param $receiver receipient of mail
     * @param $message 
     * @param $name name of the recipient
     */
    public function send_email($category,$receiver,$message,$name){
        $sendemail = new SendEmail;
        $sendemail->category = $category ;
        $sendemail->receiver =$receiver;
        $sendemail->message =$message;
        $sendemail->creator = $name;
        $sendemail->save();
    }

    public function integratePOinv($invoice_header)
    {
        ##create invoice
        $currency_rate = $invoice_header->currency_rate;
        $unallocated_gross = POInvoice_dtls::where('po_number',  $invoice_header->po_number)
        ->where('invoice_no',  $invoice_header->invoice_no)->where('vendor_id', $invoice_header->vendor_id)->sum('taxable_amt');
        
        $create_invoice =  DB::table('ap_invoice_header')->insert([
            'vendor_id' => $invoice_header->vendor_id,
            'currency_code' => $invoice_header->currency_code,
            'invoice_no' => $invoice_header->invoice_no,
            'invoice_description' => $invoice_header->invoice_descr,
            'control_account' =>  $invoice_header->control_account,
            'tax_group' =>  $invoice_header->tax_group,
            'currency_rate' => $currency_rate,
            'foreign_amount_payable' => $invoice_header->amt_payable,
            'amount_payable' => $invoice_header->local_amt_payable,
            'invoice_date' => $invoice_header->invoice_date,
            'foreign_invoice_amount' => $invoice_header->invoice_amt,
            'local_invoice_amount' => $invoice_header->local_invoice_amt,
            'foreign_undistributed_amount' => $invoice_header->undistributed_amt,
            'local_undistributed_amount' => $invoice_header->local_undistributed_amt,
            'invoice_status' => '006',
            'created_by' =>  $invoice_header->created_by,
            'created_date' =>  $invoice_header->created_at,
            'office_code' => $invoice_header->offcd,
            'account_month' => $invoice_header->account_month,
            'account_year' => $invoice_header->account_year,
            'doc_type' => $invoice_header->doc_type,
            'po_number' => $invoice_header->po_number,
            'foreign_allocated' => 0,
            'local_allocated' => 0,
            'foreign_unallocated_gross' => $unallocated_gross,
            'local_unallocated_gross' => $unallocated_gross * $currency_rate,
            'dep_code' => $invoice_header->dept_code,
            'total_taxes' => $invoice_header->local_total_taxes,
            'foreign_total_taxes' => $invoice_header->total_taxes,
            'nett_amount' =>  $invoice_header->local_amt_payable,
            'foreign_total_deductions' => 0,
            'total_deductions' => 0,
            'foreign_nett_amount' => $invoice_header->amt_payable,
            'no_of_items' => $invoice_header->no_of_items,
            'foreign_unallocated' =>  $invoice_header->amt_payable,
            'local_unallocated' =>  $invoice_header->local_amt_payable,
            'dola' =>  $invoice_header->dola,
            'changed_by' => $invoice_header->updated_by,
            'FROM_PO_FLAG' => 'Y',
        ]);
      
        ##insert items
        $inv_items = POInvoice_dtls::where('po_number',  $invoice_header->po_number)
        ->where('invoice_no',  $invoice_header->invoice_no)->where('vendor_id', $invoice_header->vendor_id)->get();

        if($inv_items->count() > 0)
        {
            foreach ($inv_items as $item) 
            {
                $invoice_item = DB::table('ap_invoice_details')->insert([
                    'vendor_id' => $item->vendor_id,
                    'item_no' => $item->item_no,
                    'invoice_no' => $item->invoice_no,
                    'item_cost' => $item->item_cost,
                    'item_description' =>$item->item_descr,
                    'total_cost' => $item->total_cost,
                    'total_amount' => $item->local_total_cost,
                    'no_of_items' => $item->quantity,
                    'unit_of_measure' => $item->unit_of_measure,
                    'taxable' => $item->taxable,
                    'retained_amount' => 0,
                    'net_of_retained_amount' => $item->taxable_amt,
                    'taxable_amount' => $item->taxable_amt,
                    'created_by' => $item->created_by,
                    'created_date' => $item->created_at,
                    'item_control_account' => $item->control_account,
                    'foreign_allocated' => 0,
                    'local_allocated' => 0,
                    'net_of_tax' => $item->amt_payable,
                    'foreign_tax_amount' => $item->tax_amt,
                    'local_tax_amount' => $item->local_tax_amt,
                    'foreign_amount_payable' => $item->amt_payable,
                    'local_amount_payable' => $item->local_amt_payable,
                    'foreign_unallocated' =>  $item->amt_payable,
                    'local_unallocated' => $item->local_amt_payable,
                    'foreign_item_bal' => $item->taxable_amt,
                    'local_item_bal' => $item->taxable_amt * $currency_rate
                ]);
                
                ##invoice taxes
                $taxes = POInvoice_taxes::where('po_number',  $item->po_number)
                ->where('invoice_no',  $item->invoice_no)->where('vendor_id', $item->vendor_id)
                ->where('item_no',  $item->item_no)->get();

                if($taxes->count() > 0)
                {
                    foreach ($taxes as $tax) 
                    {
                        $invoice_item_tax = DB::table('ap_taxes')->insert([
                            'vendor_id' => $tax->vendor_id,
                            'item_no' => $tax->item_no,
                            'invoice_no' => $tax->invoice_no,
                            'tax_code' => $tax->tax_code,
                            'tax_amount' => $tax->tax_amt,
                            'tax_rate' => $tax->tax_rate,
                            'taxable_amount' => $tax->taxable_amt,
                            'add_deduct' => $tax->add_deduct,
                            'created_by' => $tax->created_by,
                            'created_date' => $tax->created_at,
                            'changed_by' => $tax->updated_by,
                            'dola' => $tax->dola
                        ]);
                    }
                }
                
            }
        }
    }

    public function updateInvoiceStkQty($stock_take_code){

        $stk_take_items = StockTakeDtl::where('stk_take_code', $stock_take_code)->get();

        foreach ($stk_take_items as $stk_take_item) {
                $upd_inv_item = Im_Inventoryitems::where(trim('item_code'), trim($stk_take_item->item_code))
                ->update([
                    'total_in_stock' => $stk_take_item->counted_qty
                ]);
        }

    }

    function genReqNo()
    {
        $req = PoDoctype::where('doc_type', 'SIR')->get()[0];
        $dtrans = $req->serial_no;
        return $dtrans;
    }
   ##increment dtrans number
   public function updDoctypeReqSerial()
   {
       $new_serial = PoDoctype::where('doc_type', 'SIR')->increment('serial_no', 1);
       return $new_serial;

   }
}