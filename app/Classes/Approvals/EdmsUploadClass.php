<?php

namespace App\Classes\Approvals;

use File;
use App\Agmnf;

use App\Crmast;
use App\Glbank;
use App\Claimant;
use App\Dcontrol;
use App\Apvendors;

use App\Edms_staging;
use App\Edmsdocument;
use GuzzleHttp\Client;
use App\Edms_cabinets_dtl;
use App\Client as Customer;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Notifications\ErrorNotification;
use App\Services\IntermediaryQueryService;
use Illuminate\Support\Facades\Notification;
use App\ValueObjects\IntermediaryQueryParams;



class EdmsUploadClass{
    private $toUser ; 
    private $edms_apiurl ; 
    private $edms_apiuser ; 
    private $edms_apipassword ;

    public function __construct(){

        $this->toUser =config('edms.toUser');
        $this->edms_apiurl =env('EDMS_URL');
        $this->edms_apiuser =env('EDMS_USER');
        $this->edms_apipassword =env('EDMS_PASSWORD');
    }
   
    public function fetch_docs_from_staging(){
        
       
        try{
            // count docs where integrated != Y(if its N),with no error comments
            $pending_docs_count = Edms_staging::where('integrated','<>','Y')->count();
        
            if($pending_docs_count > 0 ){
            
                $pending_docs_to_upload = Edms_staging::where('integrated','<>','Y');
                foreach ($pending_docs_to_upload as $pending_doc_to_upload){
                    $contextid = $pending_doc_to_upload->context_id;
                    $entityName= $pending_doc_to_upload->entity_name;
                    $entityid= $pending_doc_to_upload->entity_id;
                    $document= Edmsdocument::where("document_code",$pending_doc_to_upload->document_code)->first();

                    $document_description = $document->document_description;

                    if($contextid != null && $entityName != null && $entityid != null && $document_description!= null){
                        $this->fetch_doc_types_from_edms($pending_doc_to_upload,$document_description);
                        
                    }else{
                        $error = response()->json(['error'=>'Metadata Field is empty'], 401);
                        $error_message = $error->getData()->error;
                        $error_code = $error->getStatusCode();
                        $error_comment = $error_code. ': '.$error_message.'.';
                        
                        
                        $pending_docs_rec_update = Edms_staging::where('context_id',$pending_doc_to_upload->context_id)
                        ->where('document_description',$pending_doc_to_upload->document_description)
                        ->where('entity_id',$pending_doc_to_upload->entity_id)
                        ->update([
                            'integrated'=>'N',
                            'metadata_type_set'=>'N',
                            // 'error_comments' => $error_comment,
                            // 'error_comments'=>$error_comment,
                        ])
                        ->when(is_null($pending_doc_to_upload->error_comments), function ($query) use ($error_comment) {
                            $query->update([
                                'error_comments' => $error_comment, // Update integrated when the condition is true
                            ]);
                        })
                         ->when(!is_null($pending_doc_to_upload->error_comments), function ($query) use ($error_comment) {
                            $query->update([
                                'error_on_resend' => $error_comment, // Update metadata_type_set when the condition is false
                            ]);
                        });
                        Notification::route('mail', $toUser)->notify(new ErrorNotification($error_comment));

                        return response()->json(['status'=> 0,'message'=>'Metadata Field is empty']);
                       
                    }
                }
                

            }else {
                dd('No document pending to integrate to EDMS');
            } 
        }catch(\Throwable $th){
            
            throw $th;
        }

    }

    function fetch_doc_types_from_edms($pending_doc_to_upload,$document_description)
    {

        $document_label = $document_description;
        $document_code = $pending_doc_to_upload->document_code;

        $EdmsDocumentTypesClass = new EdmsDocumentTypesClass();
        $results = $EdmsDocumentTypesClass-> create_edms_document_type($document_label,$document_code);

        
        $document_type_id = $results['doc_type_id'];
        $status = $results['status'];

        if($status == 1 ){
            $upload_doc = $this->upload_document_to_edms($pending_doc_to_upload,$document_type_id,$document_label);
            return $upload_doc;

        }else{
            $error = response()->json(['error'=>'Page Limit has reached and no such document label as '.$document_label], 401);
                $error_message = $error->getData()->error;
                $error_code = $error->getStatusCode();
                $error_comment = $error_code. ': '.$error_message.'.';
                
                
                

                $pending_docs_rec_update = Edms_staging::where('context_id',$pending_doc_to_upload->context_id)
                        ->where('document_description',$pending_doc_to_upload->document_description)
                        ->where('entity_id',$pending_doc_to_upload->entity_id)
                        
                        ->when(is_null($pending_doc_to_upload->error_comments), function ($query) use ($error_comment) {
                            $query->update([
                                'error_comments' => $error_comment, // Update integrated when the condition is true
                            ]);
                        })
                         ->when(!is_null($pending_doc_to_upload->error_comments), function ($query) use ($error_comment) {
                            $query->update([
                                'error_on_resend' => $error_comment, // Update metadata_type_set when the condition is false
                            ]);
                        });

                Notification::route('mail', $toUser)->notify(new ErrorNotification($error_comment));
                

                // return  $error;
                return response()->json(['status'=> 0,'message'=>"Page Limit has reached and no such document label as $document_label"]);
        }

    }

    

    public function upload_document_to_edms($pending_doc_to_upload,$document_type_id,$document_label)
        {

            $base64_string = $pending_doc_to_upload->base64_string;
            $entityName = $pending_doc_to_upload->entity_name;
            $filename = $entityName.'_'.$document_label;
            $fileType = $pending_doc_to_upload->file_type;
            $extension_type = $pending_doc_to_upload->extension_type;
            $extension ='';
            
            

            // Use basename to get the filename from the path
            $extension = $extension_type ? $extension_type : basename($fileType);
            
            $file = $filename.'.'.$extension; 
            $base64_string = str_replace(' ', '+', $base64_string);
            
            $data = base64_decode($base64_string);
            
          
            Storage::disk('public')->put($file,$data);     
            // $storagePath = Storage::disk('public')->getDriver()->getAdapter()->getPathPrefix();
            $storagePath = Storage::disk('public')->path('');

            
        
           

            $client = new \GuzzleHttp\Client();
            $url = $this->edms_apiurl .'/api/v4/documents/upload/';
            $user = $this->edms_apiuser;
            $passwd = $this->edms_apipassword;

           

            $response = '';
            $status="";

            $response = $client->post($url, [
                'auth' => [
                    $user, 
                    $passwd
                ],
                    'multipart' => [
                        [ 
                            'Content-type' => 'multipart/form-data',
                            'name' => 'file',
                            'contents' => fopen(storage_path('app/public/'.$file), 'r'),
                            'label' => $document_label,
                        ],
                        [ 'name' => 'document_type_id', 'contents' => $document_type_id ]
                    ]
                    
            ]);
        



            $contents = json_decode($response->getBody()->getContents());
            
            $status = $response->getStatusCode();
           
            
            
            $upload_document_id = $contents->id;
           
            if ($status == 201){
                $upload = $this->get_metadata_types($pending_doc_to_upload,$document_type_id,$document_label,$upload_document_id,$file);
                return $upload;
            }else{
                $error = response()->json(['error'=>'Document upload integration has failed'], $status);
                $error_message = $error->getData()->error;
                $error_code = $error->getStatusCode();
                $error_comment = $error_code. ': '.$error_message.'.';

               
                

                $pending_docs_rec_update = Edms_staging::where('context_id',$pending_doc_to_upload->context_id)
                        ->where('document_description',$pending_doc_to_upload->document_description)
                        ->where('entity_id',$pending_doc_to_upload->entity_id)
                        ->update([
                            'integrated'=>'N',
                            'metadata_type_set'=>'N',
                            // 'error_comments' => $error_comment,
                            // 'error_comments'=>$error_comment,
                        ])
                        ->when(is_null($pending_doc_to_upload->error_comments), function ($query) use ($error_comment) {
                            $query->update([
                                'error_comments' => $error_comment, // Update integrated when the condition is true
                            ]);
                        })
                         ->when(!is_null($pending_doc_to_upload->error_comments), function ($query) use ($error_comment) {
                            $query->update([
                                'error_on_resend' => $error_comment, // Update metadata_type_set when the condition is false
                            ]);
                        });
                
                Notification::route('mail', $toUser)->notify(new ErrorNotification($error_comment));

                return response()->json(['status'=> 0,'message'=>'Document upload integration has failed']);
            }
             File::delete(public_path('storage/'.$file));
               
            

        
    }

    public function get_metadata_types($pending_doc_to_upload,$document_type_id,$document_label,$upload_document_id,$file){
        
        try{
            $client = new \GuzzleHttp\Client();
            $url = $this->edms_apiurl.'/api/v4/document_types/'.$document_type_id.'/metadata_types/';
            $user = $this->edms_apiuser ;
            $passwd = $this->edms_apipassword;

            $response = '';
            $status="";
    
            $response = $client->get($url, [
                'auth' => [
                    $user, 
                    $passwd
                ],
            ]);


            $contents = json_decode($response->getBody()->getContents());
            $results = $contents->results;
            $meta_types_count = $contents->count;
            $statusCode=null;

            foreach ($results as $key => $value) {

                $meta_name = $value->metadata_type->name;
            
                $meta_id = $value->metadata_type->id;

                $data = array(
                    'metadata_type_id' => $meta_id,
                    'value' => $pending_doc_to_upload->{$meta_name}

                );

                
                $url = $this->edms_apiurl.'/api/v4/documents/'.$upload_document_id.'/metadata/';

                    $response = $client->post($url, [
                        'auth' => [
                            $user, 
                            $passwd
                        ],
                        'headers' => [
                            'Content-Type'  => 'application/json'
                        ],
                        'json'=>$data
                    ]);

                    $contents = json_decode($response->getBody()->getContents());
                    $statusCode = $response->getStatusCode();
                    
                   
                    
            }
            
            if($statusCode==201){
                $doc_code = $pending_doc_to_upload->document_code;
                $document=Edmsdocument::where('document_code',$doc_code)->first();
                $aims_cabinet_path = $document->cabinet_path;
                $response = $this->get_document_cabinet($upload_document_id,$aims_cabinet_path,$pending_doc_to_upload);
                
                
                $status = $response['status'];
                
                
                if($status== 1){
                    $pending_docs_rec_update = Edms_staging::where('context_id',$pending_doc_to_upload->context_id)
                    ->where('document_description',$pending_doc_to_upload->document_description)
                    ->where('entity_id',$pending_doc_to_upload->entity_id)
                    ->where('document_id',$pending_doc_to_upload->document_id)
                    ->update([
                        'integrated'=>'Y',
                        'metadata_type_set'=>'Y',
                        'document_type_id'=>$document_type_id,
                        'uploaded_doc_id'=>$upload_document_id
                    ]);
                    return response()->json(['status'=> 1,'message'=>'Document upload integration successfully']);
                    // return ['status'=> 1,'message'=>'Document upload integration successfully'];
                    // return response()->json(['success'=>'Document upload integration successfully']);
                }

            }else {
                    $error = response()->json(['error'=>'Document upload integration has failed'], 401);
                    $error_message = $error->getData()->error;
                    $error_code = $error->getStatusCode();
                    $error_comment = $error_code. ': '.$error_message.'.';
                    
                    $pending_docs_query = Edms_staging::where('context_id', $pending_doc_to_upload->context_id)
                                                        ->where('document_description', $pending_doc_to_upload->document_description)
                                                        ->where('entity_id', $pending_doc_to_upload->entity_id)
                                                        ->where('document_id', $pending_doc_to_upload->document_id);
                    // Update common fields first
                    $pending_docs_rec_update = $pending_docs_query->update([
                        'integrated' => 'N',
                        'metadata_type_set' => 'N',
                    ]);

                    // Perform additional updates based on conditions
                    $pending_docs_query->when(is_null($pending_doc_to_upload->error_comments), function ($query) use ($error_comment) {
                        $query->update([
                            'error_comments' => $error_comment,
                        ]);
                    })->when(!is_null($pending_doc_to_upload->error_comments), function ($query) use ($error_comment) {
                        $query->update([
                            'error_on_resend' => $error_comment,
                        ]);
                    });

                    // $pending_docs_rec_update = Edms_staging::where('context_id',$pending_doc_to_upload->context_id)
                    //     ->where('document_description',$pending_doc_to_upload->document_description)
                    //     ->where('entity_id',$pending_doc_to_upload->entity_id)
                    //     ->where('document_id',$pending_doc_to_upload->document_id)
                    //     ->update([
                    //         'integrated'=>'N',
                    //         'metadata_type_set'=>'N',
                    //         // 'error_comments' => $error_comment,
                    //         // 'error_comments'=>$error_comment,
                    //     ])
                    //     ->when(is_null($pending_doc_to_upload->error_comments), function ($query) use ($error_comment) {
                    //         $query->update([
                    //             'error_comments' => $error_comment, // Update integrated when the condition is true
                    //         ]);
                    //     })
                    //      ->when(!is_null($pending_doc_to_upload->error_comments), function ($query) use ($error_comment) {
                    //         $query->update([
                    //             'error_on_resend' => $error_comment, // Update metadata_type_set when the condition is false
                    //         ]);
                    //     });
                    // Notification::route('mail', $toUser)->notify(new ErrorNotification($error_comment));

                return response()->json(['status'=> 0,'message'=>'Document upload integration has failed']);
            }

           


        }catch(Throwable $th){
            throw $th;
        }
    }

    public function get_document_cabinet($upload_document_id,$aims_cabinet_path,$pending_doc_to_upload){
       
        $client_number = $pending_doc_to_upload->entity_id;
        $client =  Customer::where('client_number',$client_number)->first();
        
        

        // Explode the input string using the '/' delimiter
        
        $explodedValues = explode('/', $aims_cabinet_path);
        

        // Get the count of exploded values
        $count = count($explodedValues);
        $EdmsCreatePolicyCabinet = new EdmsCreatePolicyCabinet();
        $EdmsCabinetsClass = new EdmsCabinetsClass();


        // Loop through the exploded values and create variables $level1, $level2, and so on
        $master_cabinet = '';
        for ($i = 0; $i < $count; $i++) {
            $aims_cabinet_id = $explodedValues[$i];
            $cabinet = Edms_cabinets_dtl::where('id',$aims_cabinet_id)->first();
            $cabinet_name = $cabinet->cabinet_name;
            

            
                if($i == 0){
                    $master_cabinet = $cabinet_name;
                    $edms_parent_idd = null;
                    $client_cabinet_name = null;
                    $edms_parent_id = $this->fetch_parent_cabinet($aims_cabinet_id,$edms_parent_idd,$client_cabinet_name);
                    

                    if($edms_parent_id == null){
                        $result = $EdmsCabinetsClass->create_initial_cabinet_to_edms($aims_cabinet_id);
                        $edms_parent_id =$result['data']['id'];
                        
                    }

                    if($cabinet_name == 'Clients'){

                        $client_cabinet_name = $client_number.'-'.trim($client->name);
                        $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);
                        

                        
                    }else if($cabinet_name == 'Intermediaries'){

                        $client_intermediary_no = $client_number;
                        $branch = substr($client_intermediary_no, 0, 3);
                        $agent = substr($client_intermediary_no, 3);
                        // $intermediary = Agmnf::where('branch',$branch)->where('agent',$agent)->first();
                        $intermediaryParams = new IntermediaryQueryParams([
                            'agentNo' => $agent,
                        ]);
                        $intermediary  =IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();


                        $client_cabinet_name = $client_intermediary_no.'-'.trim($intermediary->name);

                        $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);

                        

                    }else if($cabinet_name == 'Service Providers'){
                        $claimant_number = $client_number;
                        

                        $claimant = Claimant::where('claimant_code',$claimant_number)->first();
                        $client_cabinet_name = $claimant_number.'-'.trim($claimant->claimant_name);
                        $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);
                        
                        $edms_sp_parent_id = $this->get_sp_cabinet_id($edms_parent_id,$aims_cabinet_path);
                        
                        
                    }else if($cabinet_name == 'Reinsurer'){
                       
                        $agent_number = $client_number;

                        $branch = substr($agent_number, 0, 3);
                        $agent = substr($agent_number, 3);

                        $intermediary = Crmast::where('branch',$branch)->where('agent',$agent)->first();
                        
                        $client_cabinet_name = $agent_number.'-'.trim($intermediary->name);
                        $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);
                        $edms_rein_parent_id = $this->get_sp_cabinet_id($edms_parent_id,$aims_cabinet_path);
                        
                        
                        
                    }
                    
                    // Create new client on edms if the client is not present.
                    if($edms_parent_id == null){
                        
                        $EdmsCabinetsClass = new EdmsCabinetsClass();
                        
                        $result = $EdmsCabinetsClass->create_client_cabinet_from_aims_to_edms($client_number,$cabinet_name);
                        
                        $edms_parent_id = $result['edms_parent_id'];
                        
                        if($cabinet_name == 'Reinsurer'){
                            $edms_rein_parent_id = $this->get_sp_cabinet_id($edms_parent_id,$aims_cabinet_path);
                            
                        }

                        if($cabinet_name == 'Service Providers'){
                            $edms_sp_parent_id = $this->get_sp_cabinet_id($edms_parent_id,$aims_cabinet_path);

                        }
                    }
                    
                }else if($i == 1 && $cabinet_name == 'Policies' ){
                    
                    $client_cabinet_name = 'Policies';
                    $edms_policies_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);
                    

                    $client_cabinet_name = $pending_doc_to_upload->context_id;

                    // get the parent cabinet id first that is the policy number cabinet
                    $polcy_dtl = Dcontrol::where('endt_renewal_no',$client_cabinet_name)->first();
                    $parent_cabinet_name = $polcy_dtl->policy_no; 
                    
                    
                    $edms_policy_id = $this->fetch_cabinets($aims_cabinet_id,$edms_policies_id,$parent_cabinet_name);
                    
                    
                    // Specific EXT / REN / POL cabinet id
                    $edms_endt_id = $this->fetch_cabinets($aims_cabinet_id,$edms_policy_id,$client_cabinet_name);
              
                    if($edms_endt_id == null){
                        $pol_clm_number = $client_cabinet_name;
                        $parent_cabinet_name = 'Policies';
                        
                        $result = $EdmsCreatePolicyCabinet->create_policy_claims_cabinet($client_number,$pol_clm_number,$parent_cabinet_name);

                        $parent_id =$result['edms_parent_id'];
                        
                        $edms_endt_id = $this->fetch_cabinets($aims_cabinet_id,$parent_id,$client_cabinet_name);
                        
                    }
                }
                else if($i == 1 && $cabinet_name == 'Claims' ){
                    
                    $client_cabinet_name = 'Claims';
                    $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);
                    

                    $client_cabinet_name = $pending_doc_to_upload->context_id;
                    $edms_parent_clm_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);
                    
                    // create claims cabinet if it is not present
                    if($edms_parent_clm_id == null){
                        $pol_clm_number = $client_cabinet_name;
                        $parent_cabinet_name = 'Claims';
                        $result = $EdmsCreatePolicyCabinet->create_policy_claims_cabinet($client_number,$pol_clm_number,$parent_cabinet_name);

                        $edms_parent_clm_id = $result['edms_parent_id'];
                    }

                }
                else if($i == 1 && $cabinet_name == 'Financials' && $master_cabinet == "Clients"){
                    $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$cabinet_name);
                    $trans_type = null;
                    $EdmsCabinetsClass->create_client_children_cabinets($edms_parent_id,$aims_cabinet_id,$trans_type);
                    
                }
                else if($i == 2 && $master_cabinet == "Clients"){

                    $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$cabinet_name);
                    
                }else if(($i == 1 && $cabinet_name == 'Receipts') ||  ($i == 1 && $cabinet_name == 'Payments')){
                    if ($master_cabinet == "Intermediaries") {
                        $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$cabinet_name);
                    }
                }else if($i == 1 && $master_cabinet == 'Financials'){

                    // create the immediate sub cabinets if they are not present
                    $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$cabinet_name);
                    
                   
                    if($edms_parent_id == null){

                        $EdmsCabinetsClass = new EdmsCabinetsClass();
                        $trans_type = null;
                        $aims_parent_cabinet = Edms_cabinets_dtl::where('parent_id', 0)->where('cabinet_name',$master_cabinet)->first();
                        $cabinet_children_parent_id = $aims_parent_cabinet->id;
                        $parent_id = $aims_parent_cabinet->api_id;
                        
                        $EdmsCabinetsClass->create_client_children_cabinets($parent_id,$cabinet_children_parent_id,$trans_type);
                        
                        
                        $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$cabinet_name);
                        
                    }
                    if($cabinet_name == 'Accounts Payable'){
                        // entity_id in this case will be the vendor number
                        $vendor_id = $client_number;
                        $vendor = Apvendors::where('vendor_id',$vendor_id)->first();
                        $vendor_cabinet_name = $vendor_id.'-'.trim($vendor->vendor_name);

                        $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$vendor_cabinet_name);

                        
                        // if specific vendor cabinet does not exist, create the cabinet
                        if($edms_parent_id == null){

                            $EdmsCabinetsClass = new EdmsCabinetsClass();
                            
                            $result = $EdmsCabinetsClass->create_client_cabinet_from_aims_to_edms($vendor_id,$cabinet_name);
                            
                            $edms_parent_id = $result['edms_parent_id'];
                            
                        }

                        $edms_ap_parent_id = $this->get_sp_cabinet_id($edms_parent_id,$aims_cabinet_path);
                    }
                    if($cabinet_name == 'Bank'){
                        // entity_id in this case will be the bank id
                        $bank_code = $client_number;
                        

                        $bank = Glbank::where('prsno',$bank_code)->first();
                        $bank_cabinet_name = $bank_code.'-'.trim($bank->account_name);

                        $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$bank_cabinet_name);

                        // if specific vendor cabinet does not exist, create the cabinet
                        if($edms_parent_id == null){

                            $EdmsCabinetsClass = new EdmsCabinetsClass();
                            
                            $result = $EdmsCabinetsClass->create_client_cabinet_from_aims_to_edms($bank_code,$cabinet_name);
                            
                            
                            $edms_parent_id = $result['edms_parent_id'];
                            
                        }
                        
                        $edms_bnk_parent_id = $this->get_sp_cabinet_id($edms_parent_id,$aims_cabinet_path);
                        

                    }
                }
                else {
                    $client_cabinet_name = null;

                    $edms_parent_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);
                }
                
               
        }

         
        if($edms_endt_id){
            $edms_cabinet_id = $edms_endt_id;
        }else if($edms_parent_clm_id){
            $edms_cabinet_id = $edms_parent_clm_id;
        }else if($edms_sp_parent_id){
            $edms_cabinet_id = $edms_sp_parent_id;
        }else if($edms_rein_parent_id){
            $edms_cabinet_id = $edms_rein_parent_id;
        }else if($edms_ap_parent_id){
            $edms_cabinet_id = $edms_ap_parent_id;
        }else if($edms_bnk_parent_id){
            $edms_cabinet_id = $edms_bnk_parent_id;
        }
        else{
            $edms_cabinet_id = $edms_parent_id;
        }

        // dd($edms_cabinet_id); 
        
        $result = $this->upload_doc_to_cabinet($edms_cabinet_id,$upload_document_id);
        
        return $result;
        
    }

    // get the sub cabinet id for the service provider specific cabinet
    public function get_sp_cabinet_id($edms_parent_id,$aims_cabinet_path){
        
        $explodedValues = explode('/', $aims_cabinet_path);
        $count = count($explodedValues);
        
        
        
        for ($i = 0; $i < $count; $i++) {
            $aims_cabinet_id = $explodedValues[$i];
            
            $cabinet = Edms_cabinets_dtl::where('id',$aims_cabinet_id)->first();
            $cabinet_name = $cabinet->cabinet_name;
            
            if($count == 2 && $i == 1){
                $cabinet_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);
                return  $cabinet_id;
            
                
            }else if ($count == 3 && $i == 2){
                $cabinet_id = $this->fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name);
                return  $cabinet_id;
            }
            
            
            
            
        }

        
    }

    
    public function upload_doc_to_cabinet($edms_cabinet_id,$upload_document_id){
          ///Tie document uploaded to cabinet
        if($edms_cabinet_id){

          $client = new \GuzzleHttp\Client();
          
            try 
            {
            $url = $this->edms_apiurl. '/api/v4/cabinets/'.$edms_cabinet_id.'/documents/add/';
            $response = '';
            $status="";
            $data = array(
                'document' => $upload_document_id

            );
            $response = $client->post($url, [
                'auth' => [
                    $this->edms_apiuser, 
                    $this->edms_apipassword
                ],
                'headers' => [
                        'Content-Type'  => 'application/json'
                    ],
                            'json'=>$data
    
            ]);
            $contents = json_decode($response->getBody());
            
            $results=$response->getStatusCode();

            
            
            if($results == 200){
                $status = 1;
            }else{
                $status = 0;
            }

            return [
                'status' => $status
            ];
            }catch (Exception $e) {
                throw $e->getMessage();
            }
        }else{
            
            return [
                'status' => 0
            ];
        }
         
    }

    //get cabinet from edms apis
    public function fetch_parent_cabinet($aims_cabinet_id,$edms_parent_id,$client_cabinet_name){
    
                // dd(json_decode($response->getBody()));
                $aims_cabinet_idp = (int)$aims_cabinet_id;
                $edms_cabinet_id = null;
                

                $cabinet_parents_rec= Edms_cabinets_dtl::where('id',$aims_cabinet_idp)->first();
                
                if($client_cabinet_name==null){
                    $cabinet_label = $cabinet_parents_rec->cabinet_name;
                    $cabinet_id = (int)$cabinet_parents_rec->id;
                }else{
                    $cabinet_label = $client_cabinet_name;
                    $cabinet_id = null;
                }
                
                $page =1;
                $final_resp = [];
                $resp = $this->list_cabinets_next_page($cabinet_id,$cabinet_label,$page,$edms_parent_id);

                    $total_records = $resp['total_records'];
                    $total_pages = ceil(((int)$resp['total_records']/10));
                    
                    for($i = $page;$i<=$total_pages;$i++){
                        $resp  = $this->list_cabinets_next_page($cabinet_id,$cabinet_label,$i,$edms_parent_id);
                        
                        if ($resp['edms_cabinet_id'] != null) {
                            $final_resp = [
                                'status' =>1,
                                'edms_cabinet_id' => $resp['edms_cabinet_id'],
                                'msg' =>'Cabinet found'
                            ];
                            break;
                        }
                    }

                    if(count($final_resp) ==0){
                        $final_resp = [
                            'status' =>0,
                            'edms_cabinet_id' => $edms_cabinet_id,
                            'msg' =>'No cabinet found '.$cabinet_label
                        ];
                    }
                    return $final_resp['edms_cabinet_id'];
         
    }
    //get cabinet from edms apis

    public function fetch_cabinets($aims_cabinet_id,$edms_parent_id,$client_cabinet_name){
        $client = new \GuzzleHttp\Client();
       
        $url = $this->edms_apiurl. '/api/v4/cabinets/'.$edms_parent_id;

        $response = '';
        $status = '';

            $response = $client->get($url,[
                'auth' => [
                    $this->edms_apiuser,
                    $this->edms_apipassword
                ],
                'headers' => [
                    'Content-Type' => 'application/json'
                ]
                ]);
   
                // dd(json_decode($response->getBody()));
                $aims_cabinet_idp = (int)$aims_cabinet_id;
                

                $cabinet_parents_rec= Edms_cabinets_dtl::where('id',$aims_cabinet_idp)->first();
                
                if($client_cabinet_name==null){
                    $cabinet_label = $cabinet_parents_rec->cabinet_name;
                    $cabinet_id = (int)$cabinet_parents_rec->id;
                }else{
                    $cabinet_label = $client_cabinet_name;
                    $cabinet_id = null;
                }

                    // $edms_parent_id = null;  //this will be static for first parent from edms 

                $contents = json_decode($response->getBody());
                $results = $contents->children;

                $next_page_count = $contents->next;
              
                foreach($results as $key => $value) {
                    // echo $value->label."------".$value->parent_id;
                    if($value->label==$cabinet_label ) {
                        $edms_cabinet_id = $value->id;
                      
                    }
                }
                
                return $edms_cabinet_id;
         
    }
    


    public function list_cabinets_next_page($cabinet_id,$cabinet_label,$page,$edms_parent_id)
    {
        $client = new \GuzzleHttp\Client();
        $url = $this->edms_apiurl.'/api/v4/cabinets/?page='.$page;
        $user = $this->edms_apiuser ;
        $passwd = $this->edms_apipassword;

        $response = '';
        $status="";

        $response = $client->get($url, [
            'auth' => [
                $user, 
                $passwd
            ],
            'headers' => [
                    'Content-Type'  => 'application/json'
                ], 

        ]);
        $contents = json_decode($response->getBody()->getContents());
        $results=$contents->results;
        $next_page_count = $contents->next;
        foreach($results as $key => $value) {
            
            if($value->label==$cabinet_label && $value->parent_id==$edms_parent_id) {
                $edms_cabinet_id = $value->id;
            
            }
        }

        return [
            'edms_cabinet_id' => $edms_cabinet_id,
            'next_page_count' => $next_page_count,
            'total_records' => $contents->count
        ];

    }
}
