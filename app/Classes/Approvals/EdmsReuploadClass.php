<?php

namespace App\Classes\Approvals;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

use Illuminate\Support\Facades\Notification;
use App\Notifications\ErrorNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use File;

use GuzzleHttp\Client;
use App\Edms_staging;
use App\Edmsdocument;



class EdmsReuploadClass{
   
    public function fetch_docs_from_staging($document_id,$document_code){
        
        
        $toUser = config('edms.toUser'); 
        $edms_apiurl = env('EDMS_URL');
        $edms_apiuser = env('EDMS_USER');
        $edms_apipassword = env('EDMS_PASSWORD');
        
       
        try{
            // Use the value passed from the event to retrieve the document to be reintegrated
            $pending_doc_to_upload = Edms_staging::where('document_id',$document_id)->where('document_code',$document_code)->first();
            $cabinet_created = Edmsdocument::where('document_code',$document_code)->first();
            $cabinet_path = $cabinet_created -> cabinet_path;

            
           
           
            // Check if there is a cabinet path for this document type
            if($cabinet_path !== null){

                if($pending_doc_to_upload){

                    $contextid = $pending_doc_to_upload->context_id;
                    $entityName= $pending_doc_to_upload->entity_name;
                    $entityid= $pending_doc_to_upload->entity_id;
                    $document_commentary= $pending_doc_to_upload->document_description;


                    
                    $document= Edmsdocument::where("document_code",$pending_doc_to_upload->document_code)->first();

                    $document_description = $document->document_description;
                    
                    if($contextid != null && $entityName != null && $entityid != null && $document_description!= null &&
                    $document_commentary != null )
                    {
                        
                        $EdmsUploadClass = new EdmsUploadClass();
                        $response = $EdmsUploadClass->fetch_doc_types_from_edms($pending_doc_to_upload,$document_description);

                        return $response;
                       
                        
                        
                        
                    }else{
                        $error = response()->json(['error'=>'Metadata Field is empty'], 401);
                        $error_message = $error->getData()->error;
                        $error_code = $error->getStatusCode();
                        $error_comment = $error_code. ': '.$error_message.'.';
                        
                        
                        $pending_docs_rec_update = Edms_staging::where('context_id',$pending_doc_to_upload->context_id)
                        ->where('document_description',$pending_doc_to_upload->document_description)
                        ->where('entity_id',$pending_doc_to_upload->entity_id)
                        ->update([
                            'integrated'=>'N',
                            'doc_uploaded'=>'Y',
                            'metadata_type_set'=>'N',
                            'error_comments'=>$error_comment,
                        ]);

                        
                        Notification::route('mail', $toUser)->notify(new ErrorNotification($error_comment));
                        
                        return response()->json(['status'=> 0,'message'=>'Metadata Field is empty']);
                    }

                }else {
                    
                    return response()->json(['status'=> -1,'message'=>'No document pending to integrate to EDMS.']);
               
                } 
            }else if($cabinet_path == null){
                $error = response()->json(['error'=>'Cabinet path for this document type has not been set up!'], 401);
                $error_message = $error->getData()->error;
                $error_code = $error->getStatusCode();
                $error_comment = $error_code. ': '.$error_message.'.';
                
                
                $pending_docs_rec_update = Edms_staging::where('context_id',$pending_doc_to_upload->context_id)
                ->where('document_description',$pending_doc_to_upload->document_description)
                ->where('entity_id',$pending_doc_to_upload->entity_id)
                ->update([
                    'integrated'=>'N',
                    'doc_uploaded'=>'Y',
                    'metadata_type_set'=>'N',
                    'error_comments'=>$error_comment,
                ]);

                

                return response()->json(['status'=> 0,'message'=>'Kindly set up the cabinet path on the edms documents parameters and try again.']);
                
            }
            
        }catch(\Throwable $th){
            throw $th;

        }

    }

 


}