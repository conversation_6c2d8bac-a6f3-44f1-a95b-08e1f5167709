<?php

namespace App\Classes\Approvals;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

use Illuminate\Support\Facades\Notification;
use App\Notifications\ErrorNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use File;

use GuzzleHttp\Client;
use App\Edms_staging;
use App\Edmsdocument;
use App\Agmnf;
use App\Crmast;
use App\Claimant;
use App\Glbank;
use App\Dcontrol;
use App\Apvendors;
use App\Edms_cabinets_dtl;
use App\Client as Customer;



class EdmsDocumentRetrievalClass{
    private $toUser ; 
    private $edms_apiurl ; 
    private $edms_apiuser ; 
    private $edms_apipassword ;

    public function __construct(){

        $this->toUser =config('edms.toUser');
        $this->edms_apiurl =env('EDMS_URL');
        $this->edms_apiuser =env('EDMS_USER');
        $this->edms_apipassword =env('EDMS_PASSWORD');
    
    }

    public function get_document_from_edms( $document_id, $document_code){
        
        $doc_to_view = Edms_staging::where('document_id',$document_id)->where('document_code',$document_code)->first();
        $edms_doc_id = $doc_to_view->uploaded_doc_id;
       
            try {
                $client = new \GuzzleHttp\Client();

                $url = $this->edms_apiurl. '/api/v4/documents/'.$edms_doc_id;
                $username = $this->edms_apiuser;
                $password = $this->edms_apipassword;
                $response = '';
                $status = '';
                
       
             
                $response = $client->get($url,[
                    'auth' => [
                        $username,
                        $password
                    ],
                    'headers' => [
                        'Content-Type' => 'application/json'
                    ]
                ]);
        

                $contents = json_decode($response->getBody());
                // dd($contents);
                $status = $response->getStatusCode();
                $document_id = $contents->file_latest->document_id;
                $document_file_id = $contents->file_latest->id;
                $document_page_id = $contents->file_latest->pages_first->id;
                

                $count = $this->get_document_pages($document_id,$document_file_id);
                
                
                if($status != 200){

                    $message = 'There is a problem with retrieving the document. Kindly try again later.';
                    $image_url = null;
                    $download_url = null;
                    $document_file_id = null;
                    $document_id = null;
                    $document_page_id = null;
                    $count = null;

                }else{
                    
                    $image_url = $contents->file_latest->pages_first->image_url;
                    $download_url = $contents->file_latest->download_url;
                    $message = 'Document retrieved successfully';
                    $count = $count;
                    $document_file_id =$document_file_id;
                    $document_id = $document_id;
                    $document_page_id = $document_page_id;
                }

                return [
                    'status'=> $status,
                    'image_url' => $image_url,
                    'download_url'=> $download_url,
                    'no_of_pages' => $count,
                    'document_file_id'=> $document_file_id,
                    'document_id'=> $document_id,
                    'document_page_id'=>$document_page_id,
                    'message'=>$message

                ];

               
            } catch (\Throwable $th) {
                throw $th;
            }
        

    }


    public function get_document_pages($document_id,$document_file_id){
        
        
       
            $client = new \GuzzleHttp\Client();

            $url = $this->edms_apiurl. '/api/v4/documents/'.$document_id.'/files/'.$document_file_id.'/pages/';
            $username = $this->edms_apiuser;
            $password = $this->edms_apipassword;
            $response = '';
            $status = '';

         
            $response = $client->get($url,[
                'auth' => [
                    $username,
                    $password
                ],
                'headers' => [
                    'Content-Type' => 'application/json'
                ]
            ]);

            $contents = json_decode($response->getBody());
            $count = $contents->count;

            return $count;

            
        
    }


}