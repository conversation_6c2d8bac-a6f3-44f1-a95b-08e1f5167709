<?php

namespace App\Classes;

use App\SendEmail;

class AddToSendEmail
{
  public function createEmailRecord($category, $receiver, $message, $sendingdate, $clientName, $status = "SEND", $creator = "system", $copyReceiver = null, $bccReceiver = null, $attachmentIds = [], $template = null)
  {
    // Check if record is already in sendemail
    $existingRecord = SendEmail::where('category', $category)
    ->where('receiver', $receiver)
    ->where('createdate', $sendingdate)
    ->first();

    if($existingRecord){
      return false; // Record already exists, so do not insert again
    }

    $sendemail = new SendEmail();
    $sendemail->category = $category;
    $sendemail->receiver = $receiver; // client email
    $sendemail->message = $message;
    $sendemail->createdate = $sendingdate; // date email should be sent
    $sendemail->client_name = $clientName;
    $sendemail->copy_receiver = $copyReceiver;
    $sendemail->bcc_receiver = $bccReceiver;
    $sendemail->sentdate = null; // updated when record is sent
    $sendemail->status = $status;
    $sendemail->creator = $creator;
    $sendemail->attachment_ids = !empty($attachmentIds) ? json_encode($attachmentIds) : null;
    $sendemail->template = $template;
    $sendemail->save();
    
    return true;
    
  }
}