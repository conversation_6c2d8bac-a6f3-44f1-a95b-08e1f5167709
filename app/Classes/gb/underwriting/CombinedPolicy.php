<?php

namespace App\Classes\gb\underwriting;

use Carbon\Carbon;

use DB;
use Session;

use App\Dcontrol;
use App\Polcmb;
use App\Polmaster;
use App\Polsec;
use App\Polsect;
use App\Polsectend;
use App\Bustype;
use App\Models\Modtlmast;
use App\ClassModel;
use App\Models\Modtlsumm;

use App\Http\Controllers\gb\underwriting\MotorProcessing;


class CombinedPolicy{

    // input variables
    private $policy_no;
    private $curr_endorse_no;
    private $curr_cls;
    private $curr_dcon;
    private $prev_dcon;
    private $initial_policies;



    public function __construct($pol_no = null, $curr_endt_renewal_no = null, $curr_cls = null)
    {
        
        $this->curr_endorse_no      =  $curr_endt_renewal_no;
        $this->policy_no            =  $pol_no;
        $this->curr_cls            =  $curr_cls;

        $this->current_dcon();

        $this->previous_dcon();
        
    }

    public function current_dcon(){

        $this->curr_dcon = Dcontrol::where('policy_no',$this->policy_no)->where('endt_renewal_no',$this->curr_endorse_no)->first();

    }

    //Get previous endorsement number
    public function previous_dcon(){

        if(trim($this->policy_no) == trim($this->curr_endorse_no)){
            
            $this->prev_dcon = null;

        }else{
            
            $this->prev_dcon = Dcontrol::where('policy_no',$this->policy_no)
                                        ->where('dcon_no','<',$this->curr_dcon->dcon_no)
                                        ->where(function($query){
                                            $query->whereNotIn('cancelled',['Y','y'])
                                                ->orWhereNull('cancelled');
                                        })
                                        ->where(function($query){
                                                $query->whereNotIn('delete_str',['Y','y'])
                                                    ->orWhereNull('delete_str');
                                            })
                                        ->orderBy('dcon_no','desc')
                                        ->first();
                               
        }  

    }

    private function check_exists_polcmb(){

       return Polcmb::where('policy_no',$this->policy_no)
                    ->where('endt_renewal_no',$this->curr_endorse_no)
                    ->count();

    }

    private function trans_before_enhancement(){

        // $this->initial_policies = Polcmb::groupBy('endt_renewal_no')
        //                                 ->select('endt_renewal_no', DB::raw('count(*) as x'))
        //                                 //->distinct()
        //                                 ->where('policy_no',$this->policy_no)
        //                                 ->where(function($query){
        //                                     $query->whereNotIn('cancel',['Y','y'])
        //                                           ->orWhereNull('cancel');
        //                                 })
                                        
                                        
        //                                 ->count();
        
        //  $this->initial_policies = (int) DB::select(DB::raw("select count(*) as cnt from (select endt_renewal_no,count(*) from polcmb where policy_no='".$this->policy_no."' and (cancel<>'Y' or cancel is null) group by endt_renewal_no)"))[0]->cnt;
        $this->initial_policies = (int) 0;
    }


    private function fetch_current_polcmb(){

        $this->trans_before_enhancement();

        if($this->initial_policies == 0){

            return null;

        }
        else if($this->initial_policies > 1){

            return Polcmb::where('policy_no',$this->policy_no)
                          ->where('endt_renewal_no',$this->prev_dcon->endt_renewal_no)
                          ->where(function($query){
                                $query->whereNotIn('cancel',['Y','y'])
                                    ->orWhereNull('cancel');
                            })
                          ->get();
        }
        else{

            return Polcmb::where('policy_no',$this->policy_no)
                          //->where('endt_renewal_no',$this->prev_dcon->policy_no)
                          ->where(function($query){
                                $query->whereNotIn('cancel',['Y','y'])
                                    ->orWhereNull('cancel');
                            })
                          ->get();

        }

        
    }

    //replicate polcmb records
    public function replicate_polcmb(){

        $this->previous_dcon();

        $cmb_trans = $this->fetch_current_polcmb();

        try {

            if($cmb_trans != null){

                //dd(count($cmb_trans), $this->initial_policies, $this->prev_dcon->policy_no, $this->curr_dcon, $cmb_trans);
    
                foreach($cmb_trans as $cmb_tnx){
                    
                    $rep = $cmb_tnx->replicate();
                    
                    $rep->endt_renewal_no = $this->curr_endorse_no;
                    $rep->endorse_amount = 0;
                    
                    $rep->save();
                }
    
            }

        } catch (Exception $e) {

            Session::flash('error', 'Failed to replicate.');
        }

          

    }

    public function update_current_polcmb(){

        $curr_endorse_amount = 0;
        $curr_annual_premium = 0;
        $dcontrol = Dcontrol::where('endt_renewal_no', $this->curr_endorse_no)->get();
        $dcontrol = $dcontrol[0];

        $type_of_bus=Bustype::where('type_of_bus',$dcontrol->type_of_bus)->first();

        $polsect = Polsect::where('policy_no', $this->policy_no)
                            ->where('endt_renewal_no', $this->curr_endorse_no)
                            ->where(function($query){
                                $query->whereNotIn('cancelled',['Y','y'])
                                    ->orWhereNull('cancelled');
                            })
                            ->selectRaw("class")
                            ->selectRaw("SUM(total_sum_insured) as sum_insured")
                            ->selectRaw("SUM(total_premium) as annual_prem")
                            ->selectRaw("SUM(endorse_amount) as endorse_amount")
                            ->groupBy('class')
                            ->get();                  

        foreach ($polsect as $psect) {
            $polendt = Polcmb::where('policy_no', $this->policy_no)
                            ->where('endt_renewal_no', $this->curr_endorse_no)
                            ->where('class', $psect->class)->first();

            if($polendt->company_share != 0){

                $total_sum_insured = 100 * ($psect->sum_insured / $polendt->company_share);
                $total_premium = 100 * ($psect->annual_prem / $polendt->company_share);

            }

            $comm_amount = ($polendt->facin_comm_rate * $psect->endorse_amount ) / 100;

          if($type_of_bus->facult_in =='Y'){  
            $upd = Polcmb::where('policy_no', $this->policy_no)
                ->where('endt_renewal_no', $this->curr_endorse_no)
                ->where('class', $psect->class)
                ->update([
                            'total_sum_insured'       => $total_sum_insured,
                            'sum_insured'       => $psect->sum_insured,
                            'total_premium'           => $total_premium,
                            'premium'           => $psect->annual_prem,
                            'effective_premium' => $psect->endorse_amount,
                            'endorse_amount'    => $psect->endorse_amount,
                            'facin_comm_amount' => $comm_amount
                         ]);
            }
            else{
                $upd = Polcmb::where('policy_no', $this->policy_no)
                ->where('endt_renewal_no', $this->curr_endorse_no)
                ->where('class', $psect->class)
                ->update([
                            'sum_insured'       => $psect->sum_insured,
                            'total_sum_insured'       => $psect->sum_insured,
                            'premium'           => $psect->annual_prem,
                            'total_premium'           => $psect->annual_prem,
                            'effective_premium' => $psect->endorse_amount,
                            'endorse_amount'    => $psect->endorse_amount,
                            'facin_comm_amount'    => 0

                         ]);
            }
        }

        $this->update_cmb_motor();
        $this->update_polmaster_facultin_cmb_without_risk($this->curr_endorse_no);
    }

    public function update_cmb_motor(){

        $check_for_motor = Modtlmast::where('endt_renewal_no', $this->curr_endorse_no)->where('deleted','<>','Y')->count();

        if($check_for_motor > 0 || $class->combined=='Y'){

            $motorCtrl = new MotorProcessing();

            $motorCtrl->update_polmaster($this->curr_endorse_no);

        }

    }
    function update_polmaster_facultin_cmb_without_risk($endt_renewal_no){
        
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol = $dcontrol[0];
        $checkCombined = ClassModel::where('class',$dcontrol->class)->first()->combined;
        $facult_in=Bustype::where('type_of_bus',$dcontrol->type_of_bus)->first()->facult_in;
        $non_motor_count = Polsect::where('endt_renewal_no',$endt_renewal_no)->count() ?? 0;
        $motor_count = Modtlsumm::where('endt_renewal_no',$endt_renewal_no)->count() ?? 0;

        if(($motor_count + $non_motor_count)==0 && $checkCombined =='Y' && $facult_in=='Y'){
            $total_tot_sum = Polcmb::where('endt_renewal_no',$dcontrol->endt_renewal_no)->sum('total_sum_insured') ?? 0;
            $total_sum = Polcmb::where('endt_renewal_no',$dcontrol->endt_renewal_no)->sum('sum_insured') ?? 0;
            $total_prem = Polcmb::where('endt_renewal_no',$dcontrol->endt_renewal_no)->sum('total_premium') ?? 0;
            $total_endorse = Polcmb::where('endt_renewal_no',$dcontrol->endt_renewal_no)->sum('endorse_amount') ?? 0;
            Polmaster::where('endorse_no',$endt_renewal_no)
            ->update([
                'total_sum_insured' => $total_tot_sum,
                'sum_insured' => $total_sum,
                'annual_premium' => $total_prem,
                'renewal_premium' => $total_prem,
                'fire_premium' => $total_prem,
                'endorse_amount' => $total_endorse,
                'sys_endorse_amount' => $total_endorse,
            ]);
            
        }
    }

    public function update_singleclass_polcmb(){

        try{
            $dcontrol = Dcontrol::where('endt_renewal_no', $this->curr_endorse_no)->get();
            $dcontrol = $dcontrol[0];
    
            $type_of_bus=Bustype::where('type_of_bus',$dcontrol->type_of_bus)->first();



            $psect = Polsect::where('policy_no', $this->policy_no)
                                ->where('endt_renewal_no', $this->curr_endorse_no)
                                ->where('class', $this->curr_cls)
                                ->selectRaw("class")
                                ->selectRaw("SUM(total_sum_insured) as sum_insured")
                                ->selectRaw("SUM(total_premium) as annual_prem")
                                ->selectRaw("SUM(endorse_amount) as endorse_amount")
                                ->selectRaw("SUM(quake_endorse_amt) as quake_endorse_amt")
                                ->groupBy('class')
                                ->first(); 

                $polendt = Polcmb::where('policy_no', $this->policy_no)
                                ->where('endt_renewal_no', $this->curr_endorse_no)
                                ->where('class', $psect->class)->first();

        // Check if the rate is not zero to perform the division
        if(!empty($polendt->company_share)){
            $total_sum_insured = 100 * ($psect->sum_insured / $polendt->company_share);
            $total_premium = 100 * ($psect->annual_prem / $polendt->company_share);
        }
        
        if ($type_of_bus->facult_in == 'Y'){
            $comm_amount = ($polendt->facin_comm_rate * $psect->endorse_amount) / 100;
        }

        if($type_of_bus->facult_in =='Y'){
        $upd = Polcmb::where('policy_no', $this->policy_no)
                    ->where('endt_renewal_no', $this->curr_endorse_no)
                    ->where('class', $psect->class)
                    ->update([
                            'total_sum_insured'       => $total_sum_insured,
                            'sum_insured'       => $psect->sum_insured,
                            'total_premium'           => $total_premium,
                            'premium'           => $psect->annual_prem,
                            'effective_premium' => $psect->endorse_amount,
                            'endorse_amount'    => $psect->endorse_amount,
                            'quake_endorse_amt'    => $psect->quake_endorse_amt,
                            'facin_comm_amount' => $comm_amount
                         ]);
            }else {
        $upd = Polcmb::where('policy_no', $this->policy_no)
            ->where('endt_renewal_no', $this->curr_endorse_no)
            ->where('class', $psect->class)
            ->update([
                   'total_sum_insured'       => $psect->sum_insured,
                   'sum_insured'       => $psect->sum_insured,
                   'total_premium'           => $psect->annual_prem,
                   'premium'           => $psect->annual_prem,
                   'effective_premium' => $psect->endorse_amount,
                   'endorse_amount'    => $psect->endorse_amount,
                   'quake_endorse_amt'    => $psect->quake_endorse_amt,
                   'facin_comm_amount' =>0
                ]);   

            }


        }catch(Exception $e){

            report($e);

        }
                             
    }


    public function distribute_refund_amt(){

        $psect_endorse = Polsect::where('policy_no', $this->policy_no)
                                                ->where('endt_renewal_no', $this->curr_endorse_no)
                                                // ->where(function($query){
                                                //     $query->whereNotIn('cancelled',['Y','y'])
                                                //           ->orWhereNull('cancelled');
                                                // })
                                                ->selectRaw("SUM(endorse_amount) as endorse_amt,SUM(first_premium) as annual_prem")
                                                ->first();

        $pmaster = Polmaster::where('policy_no', $this->policy_no)->first();

        if($psect_endorse->endorse_amt != $pmaster->endorse_amount){

            $polsec = Polsec::where('policy_no', $this->policy_no)
                             ->where('endt_renewal_no', $this->curr_endorse_no)
                            //  ->where(function($query){
                            //     $query->whereNotIn('deleted',['Y','y'])
                            //         ->orWhereNull('deleted');
                            //  })
                             ->selectRaw("class,location")
                             //->selectRaw("SUM(total_sum_insured) as sum_insured")
                             ->selectRaw("SUM(premium) as annual_prem")
                             //->selectRaw("SUM(endorse_amount) as endorse_amount")
                             ->groupBy('class')
                             ->groupBy('location')
                             ->get();

            DB::beginTransaction();               

            foreach ($polsec as $psec) {
                
                $upd = Polsect::where('policy_no', $this->policy_no)
                         ->where('endt_renewal_no', $this->curr_endorse_no)
                         ->where('class', $psec->class)
                         ->where('location', $psec->location)
                         ->update([
                            'endorse_amount'    => ($psec->annual_prem / $psect_endorse->annual_prem) * $pmaster->endorse_amount
                         ]);
                
                $upd = Polsectend::where('policy_no', $this->policy_no)
                          ->where('endt_renewal_no', $this->curr_endorse_no)
                          ->where('class', $psec->class)
                          ->where('location', $psec->location)
                          ->update([
                              'endorse_amount'    => ($psec->annual_prem / $psect_endorse->annual_prem) * $pmaster->endorse_amount
                           ]);
                
            }

            $polsect = Polsect::where('policy_no', $this->policy_no)
                             ->where('endt_renewal_no', $this->curr_endorse_no)
                             ->selectRaw("class")
                             ->selectRaw("SUM(endorse_amount) as endorse_amount")
                             ->groupBy('class')
                             ->get();

                             

            foreach ($polsect as $psect) {
                
                $upd = Polcmb::where('policy_no', $this->policy_no)
                            ->where('endt_renewal_no', $this->curr_endorse_no)
                            ->where('class', $psect->class)
                            ->update([
                                'endorse_amount'    => $psect->endorse_amount
                            ]);
            }

            DB::commit();

        }



    }

    public function insert_missing_polcmb(){

        $polcmb_check = $this->check_exists_polcmb();

        

        if($polcmb_check == 0){

            $polsect = Polsectend::where('policy_no', $this->policy_no)
                             ->where('endt_renewal_no', $this->curr_endorse_no)
                             ->selectRaw("class")
                             ->selectRaw("count(location) as items_total, SUM(endorse_amount) as endorse_amount, SUM(total_premium) as premium, SUM(total_sum_insured) as sum_insured")
                             ->groupBy('class')
                             ->get();

            DB::beginTransaction(); 

            try {

                foreach($polsect as $psect){

                    $cmb = new Polcmb();

                    $cmb->policy_no = $this->policy_no;
                    $cmb->class = $psect->class;
                    $cmb->class_cmb = $this->curr_dcon->class;
                    $cmb->items_total = $psect->items_total;
                    $cmb->cancel = 'N';
                    $cmb->sum_insured = $psect->sum_insured;
                    $cmb->premium = $psect->premium;
                    $cmb->effective_premium = $psect->endorse_amount;
                    $cmb->endt_renewal_no = $this->curr_endorse_no;
                    $cmb->endorse_amount = $psect->endorse_amount;

                    $cmb->save();

                }

                DB::commit();
                
            } catch (\Throwable $th) {
               
                DB::rollback();

            }  
            
        }
        
    }

    
    

}