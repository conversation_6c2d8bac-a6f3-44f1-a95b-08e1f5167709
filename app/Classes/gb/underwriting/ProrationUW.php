<?php

namespace App\Classes\gb\underwriting;

use Carbon\Carbon;

use DB;
use Session;

use App\Dcontrol;
use App\Instalparam;
use App\Polmaster;
use App\ClassModel;


class ProrationUW{

    // input variables
    private $curr_endorse_no;
    private $curr_dcon;
    private $class_rec;
    private float $numerator;
    private float $denominator;



    public function __construct($curr_endt_renewal_no = null)
    {

        $this->curr_dcon = Dcontrol::where('endt_renewal_no', $curr_endt_renewal_no)->first();
        $this->numerator = 1;
        $this->denominator = 1;
        $this->class_rec = ClassModel::where('class', $this->curr_dcon->class)->first();

    }
/*
    public function prorate($endt_renewal_no, $amount)
    {
        //get endorsement details
        $polmaster = Polmaster::where('endorse_no', $endt_renewal_no)->first();
        //get number of days in the
        $yearLength = $this->yearLength($polmaster->uw_year, $endt_renewal_no);
        $dcontrol = Dcontrol::where('endt_renewal_no', $endt_renewal_no)->get();
        $dcontrol = $dcontrol[0];

        //get policy details
        $dcontrolfirst = Dcontrol::where('endt_renewal_no', $dcontrol->policy_no)->get();
        $dcontrolfirst = $dcontrolfirst[0];
        
        $policy_effective_date = Carbon::parse($dcontrolfirst->effective_date);
        $policy_period_to = Carbon::parse($dcontrolfirst->period_to);
        $class = ClassModel::where('class', $dcontrol->class)->first();
        
        //get the difference in days between todays date and end of cover
        $effective_date = Carbon::parse($dcontrol->effective_date);
        $period_to = Carbon::parse($dcontrol->period_to);

        if ($class->motor == 'Y' || $class->motor == 'y') {
            # Motor Proration
            if($class->exem == "Y"){
                $policy_days = $policy_effective_date->diffInDays($policy_period_to) + 1;
                $no_of_days = $effective_date->diffInDays($period_to) + 1;
            }
            else{
                $policy_days = $policy_effective_date->diffInDays($policy_period_to) + 2;
                $no_of_days = $effective_date->diffInDays($period_to) + 2;
            }
        } else {
            # Non Motor Proration
            if($class->exem == "Y"){
                $no_of_days = $effective_date->diffInDays($period_to) + 1;
                $policy_days = $policy_effective_date->diffInDays($policy_period_to) + 1;
            }
            else{
                $policy_days = $policy_effective_date->diffInDays($policy_period_to);
                $no_of_days = $effective_date->diffInDays($period_to);
            }
        }
        //dd($no_of_days,$policy_days);
        $prorated_amt = ((int)$no_of_days / (int)$yearLength) * $amount;
        $prorated_amt = round($prorated_amt, 3);
        //dd($no_of_days,$policy_days,$prorated_amt,$dcontrol->ast_marker,$amount);

        return $prorated_amt;
    }

    public function numerator_denominator($endorse_no){

        $prorate_numerator = 1;
        $prorate_denominator = 1;
    
        $dcontrol = Dcontrol::where('endt_renewal_no', $endorse_no)->first();
    
        $polmasteryear = Polmaster::where('endorse_no', $this->curr_dcon->endt_renewal_no)->first();
        $yearLength = $this->yearLength($polmasteryear->uw_year, $this->curr_dcon->endt_renewal_no);
    
        $check_cov_days = 'N';
    
        $date1 = Carbon::parse($this->curr_dcon->cov_period_from);
        $date2 = Carbon::parse($this->curr_dcon->cov_period_to);
    
        $day1 = $date1->day;
        $month1 = $date1->month;
    
        $day2 = $date2->day;
        $month2 = $date2->month;
        
        #check trans type
        if($this->curr_dcon->ast_marker == 'T'){
    
            $prorate_numerator = $dcontrol->endt_days;
            $prorate_denominator = $yearLength;
    
            $check_cov_days = 'Y';
    
        }
        else if($this->curr_dcon->ast_marker == 'S'){
          switch($this->curr_dcon->short_term_method){
            case 'P':
    
                $prorate_numerator = $this->curr_dcon->endt_days;
                $prorate_denominator = $yearLength;
    
                $check_cov_days = 'Y';
    
                break;
            case 'S':
    
                $prorate_numerator = $this->curr_dcon->short_term_percent;
                $prorate_denominator = 100;
    
                break;
            default:
    
                $prorate_numerator = 1;
                $prorate_denominator = 1;
    
                break;
          }
    
        }
    
        if($day1 == $day2 && $month1 == $month2 && $dcontrol->period_to == $dcontrol->cov_period_to && $check_cov_days == 'Y'){
    
            $prorate_numerator = $prorate_numerator - 1;
        }
      
        return [
                  'numerator'=> $prorate_numerator,
                  'denominator'=>$prorate_denominator
               ];
    
    }
*/
    
    public function multiplier(){

        if($this->curr_dcon->ast_marker == 'A' || $this->curr_dcon->ast_marker == 'T' || ($this->curr_dcon->ast_marker == 'S' && $this->curr_dcon->short_term_method == 'P') ){
            
            $this->prorate_days_method();

        }
        else if($this->curr_dcon->ast_marker == 'S' && $this->curr_dcon->short_term_method == 'S'){

            $this->prorate_percent_method();

        }
        else if($this->curr_dcon->ast_marker == 'I'){

            $this->prorate_installment();

        }

        return (float) ($this->numerator / $this->denominator);

    }

    private function prorate_percent_method(){

        
        if($this->curr_dcon->short_term_percent > 0){

            $this->numerator = $this->curr_dcon->short_term_percent;

        }
        else{

            $this->numerator = 100;

        }

        $this->denominator = 100;

    }

    private function prorate_days_method(){

        //GET ENDORSEMENT DAYS
        if($this->curr_dcon->endt_days > 0 && $this->curr_dcon->endt_days == $this->calculate_endt_days()){
            //dd('IF', $this->curr_dcon->endt_days, $this->calculate_endt_days(), $this->curr_dcon->days_covered, $this->calculate_cover_days());

            $this->numerator = $this->curr_dcon->endt_days;

        }
        else{
            //dd('ELSE', $this->curr_dcon->endt_days, $this->calculate_endt_days(), $this->curr_dcon->days_covered, $this->calculate_cover_days());
            $this->numerator = $this->calculate_endt_days();

        }

        //GET COVER DAYS
        if($this->curr_dcon->days_covered > 0 && (int)$this->curr_dcon->days_covered == (int)$this->calculate_cover_days()){

            $this->denominator = $this->curr_dcon->days_covered;

        }
        else{

            $this->denominator = $this->calculate_cover_days();

        }
    }

    private function prorate_installment(){

        $this->numerator = Instalparam::where('plan', $this->curr_dcon->plan)
                                  ->where('plan_categ', $this->curr_dcon->plan_categ)
                                  ->where('instal_categ', $this->curr_dcon->instal_categ)
                                  ->first()->instal_rate;

        if($this->numerator==null || $this->numerator == 0 || $this->numerator < 0){

            $this->numerator = 100;

        }

        $this->denominator = 100;
    }

    private function calculate_endt_days(){

        $period_from = Carbon::parse($this->curr_dcon->effective_date);
        $period_to = Carbon::parse($this->curr_dcon->period_to);

        $date1 = Carbon::parse($this->curr_dcon->cov_period_from);
        $date2 = Carbon::parse($this->curr_dcon->cov_period_to);

        $day1 = $date1->day;
        $month1 = $date1->month;
    
        $day2 = $date2->day;
        $month2 = $date2->month;
        
        //if($day1 == $day2 && $month1 == $month2 && $this->curr_dcon->period_to == $this->curr_dcon->cov_period_to){
    
        //    return $period_from->diffInDays($period_to);
        //}
       // else{
            return $period_from->diffInDays($period_to) + 1;
        //}

    }

    private function calculate_cover_days(){

        $period_from = Carbon::parse($this->curr_dcon->cov_period_from);
        $period_to = Carbon::parse($this->curr_dcon->cov_period_to);
 
        if(($this->class_rec->exem == 'Y' || $this->class_rec->exem == 'y')){
            //dd($period_from->diffInDays($period_to) + 1);
            return $period_from->diffInDays($period_to) + 1;

        }else{

            return $period_from->diffInDays($period_to);

        }

    }
    

}