<?php

namespace App\Classes\gb\underwriting;

use Carbon\Carbon;

use DB;
use Session;

use App\Prosched;
use App\Dcontrol;

class GPAEndorsement_premium{

    // input variables
    public $endorse_amt = 0;
    public $prev_endorse_no;
    public $curr_endorse_no;
    public $curr_period_from;
    public $curr_period_to;
    public $curr_annual_premium;
    public $emp_id;
    public $location_id; 

    // function variables
    public $curr_dcon;
    public $prev_prosched;
    public $calendar_days = 365;
    public $prev_cover_days;
    public $prev_utilized_days;
    public $curr_cover_days;

    public function __construct($curr_endt_renewal_no,$curr_period_from,$curr_period_to,$annual_premium,$emp_id,$location)
    {
        //$this->prev_endorse_no      =  $prev_endt_renewal_no;
        $this->curr_endorse_no      =  $curr_endt_renewal_no;
        $this->curr_period_from     =  $curr_period_from;
        $this->curr_period_to       =  $curr_period_to;
        $this->curr_annual_premium  =  $annual_premium;
        $this->emp_id               =  $emp_id;
        $this->location_id          =  $location;

    }


    public function curr_dcontrol()
    {
        $curr_dcon = Dcontrol::where("endt_renewal_no",$this->curr_endorse_no)->first();

        return $curr_dcon;
    }

    public function get_prev_endorse_no(){

        $get_pol = $this->curr_dcontrol();
        
        $prev_dcontrol   =   Dcontrol::whereRaw("policy_no = '".$get_pol->policy_no."'")
            ->whereRaw("cancelled = 'N'")
            ->whereRaw("trans_type IN ('POL','EXT','REN','RFN','RNS')")
            ->orderBy('dtrans_no', 'DESC')
            ->get();

            $this->prev_endorse_no = $prev_dcontrol[1]->endt_renewal_no;
    }

    public function prev_prosched()
    {
        $this->get_prev_endorse_no();

        $prosched   =   Prosched::whereRaw("endorse_no = '".$this->prev_endorse_no."'")
                                ->whereRaw("trim(head1) = '".$this->emp_id."'")
                                ->whereRaw("location = '".$this->location_id."'")
                                ->orderBy('dola', 'DESC')
                                ->get();

        // $prosched   = DB::select(DB::raw('select * from UGAIMSDATA.PROSCHED where endorse_no='$this->prev_endorse_no' and head1='$this->emp_id' and location='$this->location';'));
        if ($prosched) {
            return $prosched[0];
        }else{
            Session::flash("error", "Could not find employee with employee no".$this->emp_id." in the previous transaction");
            return redirect()->back();
        }
    }

    public function prev_cover_days(){
        $prev_prosched    = $this->prev_prosched();
        $prev_period_to   = Carbon::parse($prev_prosched->period_to);
        $prev_period_from = Carbon::parse($prev_prosched->period_from);
        $cover_days = $prev_period_from->diffInDays($prev_period_to)+1;
        
        if ($cover_days) {
           return $cover_days;
        }else{
            return $this->calendar_days;
        }
      
    }

    public function prev_utilized_days(){
        $curr_period_from   = $this->curr_period_from;
        $curr_period_to   = $this->curr_period_to;
        $prev_prosched    = $this->prev_prosched();
       
        $prev_period_to = Carbon::createFromFormat("Y-m-d",Carbon::parse($prev_prosched->period_to)->format("Y-m-d"));
        $prev_period_from = Carbon::createFromFormat("Y-m-d",Carbon::parse($prev_prosched->period_from)->format("Y-m-d"));

        $check          =   $curr_period_from->between($prev_period_from,$prev_period_to);


        if ($check) {

            // if current period_from is same as prev period_from
            // this is a refund if period to is less than period to for previous policy
            if($curr_period_from->eq($prev_period_from) && $prev_period_to->gte($curr_period_to)) {
               
                $cover_days = 0; // we reset initial amount to zero but subtract to get a refund from what was paid

            }else{
               //todo
               $cover_days = $curr_period_from->diffInDays($prev_period_from)+1;
               
            }
           

        }else {

            $cover_days = $prev_period_to->diffInDays($prev_period_from)+1;

        }
       
       
        return $cover_days;
        // }else{

        //     Session::flash("error", "Error while computing employee no".$this->emp_id." used days.");
        //     return redirect()->back();
        // }
      
    }

    public function curr_cover_days()
    {
        $cover_days = $this->curr_period_from->diffInDays($this->curr_period_to);

        if($cover_days == 0){
            $cover_days = 0;
        }
        else{
            $cover_days = $cover_days + 1;
        }
    
        return $cover_days;
    }

    public function prev_endorse_amount(){

        $prev_endorse_amount = 0;

        $prev_prosched  = $this->prev_prosched();

        if ($prev_prosched && ($prev_prosched->endorse_amount == null || $prev_prosched->endorse_amount == 0)) {
            
            $prev_endorse_amount = ($this->prev_cover_days()/$this->calendar_days) * (int)$prev_prosched->annual_premium;
        
        }else{

            $prev_endorse_amount = $prev_prosched->endorse_amount;
        }
        
        return $prev_endorse_amount;
    }

    public function endorse_amt(){

        $prev_utilized_prem   = ($this->prev_utilized_days()/$this->prev_cover_days())*$this->prev_endorse_amount();
       
        $prev_unutilized_prem = $this->prev_endorse_amount() - $prev_utilized_prem;
        
        $curr_endorse_amount  = ($this->curr_cover_days() / $this->calendar_days) * $this->curr_annual_premium;
        
        $curr_actual_endorse_amount = $curr_endorse_amount - $prev_unutilized_prem;
        
        if ($curr_actual_endorse_amount) {

            return $curr_actual_endorse_amount;

        }else{

            return 0;
        }
        
        // return $this->endorse_amt = $curr_actual_endorse_amount;
    }



}