<?php
namespace App\Classes\gb\underwriting;

use App\Nlslparams;
use App\Nlparams;

use Auth;
use Carbon\Carbon;
use DB;

class GenerateSlHead 
{
    private $glhead, $branch, $agent, $name, $edit_flag;

    function __construct($glhead, $agent, $branch, $name, $edit_flag = null) {
        $this->glhead = $glhead;
        $this->branch = $branch;
        $this->agent = $agent;
        $this->name = $name;
        $this->edit_flag = $edit_flag;
    }

    function generate_slhead()
    {
        $gl_acc = Nlparams::where('PRID', 'GLH')->where('prsno', $this->glhead)->get(['normal_dr_cr', 'slhead_flag'])->first();
       
        $sub_account_code = str_pad($this->branch, 3, '0',STR_PAD_LEFT).str_pad($this->agent, 5, '0', STR_PAD_LEFT);

        $slhead = $this->glhead .'-'. $sub_account_code;

        ##check nlsparams if the account exists
        $count_sl = Nlslparams::where('glhead', $this->glhead)->where('slhead', $slhead)->get()[0];
       
        if ($count_sl && $this->edit_flag == null) {
            Session::flash('error', 'Subledger for the participant already exist');

            return redirect()->route('deftreatpart');
        }
       
        if ($count_sl && $this->edit_flag == 'Y') {
            Nlslparams::where('glhead', $this->glhead)->where('slhead', $slhead)->update([
                'name' => $this->name
            ]);

            return $count_sl->slhead;
        }

        if (!$count_sl && $gl_acc->slhead_flag == 'Y') {
            $create_sl = Nlslparams::Create([
                'created_by' => trim(auth()->id()),
                'created_at' =>  Carbon::now(),
                'normal_dr_cr' => $gl_acc->normal_dr_cr,
                'glhead' => $this->glhead,
                'sub_account_code' =>  $sub_account_code,
                'name' => $this->name,
                'slhead' => $slhead
            ]);
            // dd($create_sl, $this->glhead);
            $schem = schemaName();
    
            $gl = $schem['gl'];
    
            $procedureName = ''.$gl.'.generate_subledger_accounts';
    
            $resp= DB::executeProcedure($procedureName);
    
            return $create_sl->slhead;
        }

        else{
            return null;
        }
    }
}
