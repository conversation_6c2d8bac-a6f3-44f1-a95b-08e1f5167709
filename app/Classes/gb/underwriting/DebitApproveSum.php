<?php

namespace App\Classes\gb\underwriting;

use Carbon\Carbon;

use DB;
use Session;

use App\Dcontrol;
use App\Debitmast;
use App\Polmaster;
use App\Polmasterend;
use App\Pipcnam;

class DebitApproveSum{

    // input variables
    private $policy_no;
    private $curr_endorse_no;
    private $previous_sum = 0;
    private $current_sum = 0;
    private $curr_poldet;
    private $escalate_basis;  //To Take P for Policy Sum OR E for Endorsement Sum
    private $curr_dcon;



    public function __construct($pol_no = null, $curr_endt_renewal_no = null)
    {
        $this->curr_endorse_no      =  $curr_endt_renewal_no;
        $this->policy_no            =  $pol_no;
        $this->curr_poldet          =  Polmasterend::where('policy_no',$this->policy_no)->where('endorse_no',$this->curr_endorse_no)->first();
        $this->curr_dcon            =  Dcontrol::where('policy_no',$this->policy_no)->where('endt_renewal_no',$this->curr_endorse_no)->first();

    }


    //Get previous endorsement number
    private function previous_endorse_no(){

        if(trim($this->policy_no) == trim($this->curr_endorse_no)){
            
            return $this->policy_no;

        }else{
            $prev_dcon = Dcontrol::where('policy_no',$this->policy_no)
                              ->where('dcon_no','<',$this->curr_dcon->dcon_no)
                              ->where('trans_type','<>','NIL')
                              ->where('cancelled','<>','Y')
                              ->where(function($query){
                                    $query->whereNotIn('delete_str',['Y','y'])
                                        ->orWhereNull('delete_str');
                                })
                               ->orderBy('dcon_no','desc')
                               ->first();

            return $prev_dcon->endt_renewal_no;

        }

        

    }

    //Set previous sum insured
    private function prev_suminsured(){

        //if(trim($this->policy_no) == trim($this->curr_endorse_no)){
        if($this->curr_poldet->trans_type == 'POL' || $this->curr_poldet->trans_type == 'REN' || $this->curr_poldet->trans_type == 'RNS' || $this->curr_poldet->trans_type == 'CXT'){
            
            $this->previous_sum = 0;

        }else{
            
            $prev_debited_endt = Debitmast::where('endt_renewal_no',$this->previous_endorse_no())
                                        ->first();


            $this->previous_sum = $prev_debited_endt->foreign_sum;

        }

    }

    //Set current sum insured
    private function curr_suminsured(){

        $curr_endt = Polmasterend::where('policy_no',$this->policy_no)
                                ->where('endorse_no',$this->curr_endorse_no)
                                ->first();
        
        $this->current_sum = $curr_endt->sum_insured;
    }

    private function get_escalate_basis(){

        $basis = Pipcnam::where('record_type', 0)->first();

        $this->escalate_basis = $basis->debit_escalate_basis;
    }

    public function get_escalate_sum(){

        $this->get_escalate_basis();
        $this->prev_suminsured();
        $this->curr_suminsured();


        //IF BASIS E THEN GET ENDORSEMENT SUM
        //ELSE GET POLICY SUM (POLICY SUM IS P)
        if($this->escalate_basis == 'E'){
            return abs($this->current_sum - $this->previous_sum);
        }
        else{

            return abs($this->current_sum);
        }     

    }

    public function get_prev_sum(){

        $this->prev_suminsured();

        return $this->previous_sum;

    }




   


}