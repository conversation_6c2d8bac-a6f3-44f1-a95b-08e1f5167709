<?php

namespace App\Classes;

use Auth;
use View;
use App\User;
use App\Agmnf;
use App\Clhmn;
use App\Client;
use App\Polsec;
use App\Pipcnam;
use App\Pipstmp;
use App\Polsect;
use App\Currency;

use App\Dcontrol;

// models
use App\Debitmast;
use App\Polmaster;
use Carbon\Carbon;
use Dompdf\Dompdf;
use App\ClassModel;

use App\Polsectend;

use App\Polmasterend;
use App\Renewal_notice;
use App\Models\Modtlmast;
use App\Models\Modtlpivot;
use Illuminate\Support\Facades\DB;
use Aimsoft\UserManagement\Models\Role;
use App\Http\Controllers\pdfController;
use Illuminate\Support\Facades\Response;
use App\Services\IntermediaryQueryService;
use Aimsoft\UserManagement\Models\Permission;
use App\ValueObjects\IntermediaryQueryParams;


Class RenewalNotification 
{
    public function generateClientPDF($endt_renewal_no, $view_path, $open_file=null)
    {
        $policy = Polmasterend::where("endorse_no" , $endt_renewal_no)->first();
        $modtls = Modtlpivot::where("endt_renewal_no" , $endt_renewal_no)
                            ->where("status" , 'ACT')
                            ->get();
        
        $polsect = Polsectend::where("endt_renewal_no" , $policy->endorse_no)->orderBy('location','asc')->get();
        $polsec = Polsec::where("endt_renewal_no" , $policy->endorse_no)->get();
        $debitmast = Debitmast::whereRaw("trim(endt_renewal_no)='" . trim($endt_renewal_no) ."'")->first();
        $dcontrol = Dcontrol::whereRaw("trim(endt_renewal_no)='" . trim($endt_renewal_no) ."'")->first();
        $rennotice = Renewal_notice::whereRaw("trim(endt_renewal_no)='" . trim($endt_renewal_no) ."'")->first();
   


        $class = ClassModel::where('class', $dcontrol->class)->first();
        $classDesc = $class->description;
        $currency = Currency::where('currency_code', $dcontrol->currency)->first();
        // $agent = Agmnf::where("branch" , $policy->branch)->where("agent" , $policy->agent_no)->first();
        $intermediaryParams = new IntermediaryQueryParams([
            'agentNo' => $policy->agent_no,
        ]);
        $agent  =IntermediaryQueryService::getIntermediaryByAgentNo($intermediaryParams)->first();

        $pipcnam = Pipcnam::where('record_type',0) ->first();

        $client = Client::where('client_number',trim($dcontrol->client_number))->first();

        $date = formatDate(Carbon::now()->format('d-m-Y'));
        $dateToday = Carbon::now()->format('F j, Y');
        $periodFrom = Carbon::parse($rennotice->period_from);
        $periodTo = Carbon::parse($rennotice->period_to)->startOfDay();
        $customPeriodTo = $periodTo->format('d/m/Y');
        $years = $periodFrom->diffInYears($periodTo);
        $lor = $this->lossRatio($policy);
        $companyName = Pipcnam::select('company_name')->first()->value('company_name');
        $permission = Permission::where('slug', 'approve-renewal-notices')->first();
        $uwManager = $permission->users->first();
        $role = Role::where('id',$uwManager->role_id)->first();
        $address = $client->address1;
        $managerSignature = $uwManager->signature;
        // dd($client->address1);
        $managerName = $uwManager->name;
        // $managerSignature = DB::table('aimsusers')->where('user_id', 11)->value('signature');
        $reviewedBy = $rennotice->approved_by;
        $reviewerName = DB::table('aimsusers')->where('user_name', $reviewedBy)->value('name');

        if($rennotice->approved_by != 'AUTO'){
          $managerName = $reviewerName;
        }

        //custom format date for period to
        $formattedDate = $periodTo->format('j F, Y');
        $dayOfMonth = $periodTo->format('j');

        if (in_array($dayOfMonth, ['11', '12', '13'])) {
            $formattedDate = str_replace($dayOfMonth, $dayOfMonth . 'th', $formattedDate);
        } elseif ($dayOfMonth % 10 == 1) {
            $formattedDate = str_replace($dayOfMonth, $dayOfMonth . 'st', $formattedDate);
        } elseif ($dayOfMonth % 10 == 2) {
            $formattedDate = str_replace($dayOfMonth, $dayOfMonth . 'nd', $formattedDate);
        } elseif ($dayOfMonth % 10 == 3) {
            $formattedDate = str_replace($dayOfMonth, $dayOfMonth . 'rd', $formattedDate);
        } else {
            $formattedDate = str_replace($dayOfMonth, $dayOfMonth . 'th', $formattedDate);
        }
        
        // 
        // $logoPath = public_path('logo/download.png');
        $logoPath = public_path($pipcnam->company_logo_path);
        $logoData = file_get_contents($logoPath);
        $logoBase64 = base64_encode($logoData);
        $logoMimeType = 'image/png'; 
        // $htmlContent .= '<img src="data:'..';base64,'.$logoBase64.'" style="width: 100px;">';
          
         $file = View::make($view_path,compact('logoBase64','logoMimeType','client','policy','dcontrol','debitmast','date','user','currency','class','agent','vatamount','stamp_duty','sticker_fees','levy','pipcnam','modtls','rennotice','polsect','polsec', 'dateToday', 'years', 'formattedDate', 'customPeriodTo', 'lor', 'classDesc', 'managerSignature', 'companyName', 'intermediary', 'role', 'managerName'))->render();
         $dompdf = new Dompdf();
        $dompdf->loadHtml($file);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();
        if($open_file == "Y"){
            header('Content-Type: application/pdf');
            header('Content-Disposition: inline; filename="document.pdf"');
            header('Cache-Control: private, must-revalidate, post-check=0, pre-check=0');
            header('Expires: 0');
            echo $dompdf->output();
          




        }else{
            return $dompdf->output();
        }
        

    

    }

    public function generateAgentPdf($agent_details,$agent_policies)
    {
        $date = formatDate(Carbon::now()->format('d-m-Y'));

        $user = Auth::user()->name;
        
        $file = View::make('emails.agent_renewal_notice_letter',compact('agent_details','agent_policies','date','user'))->render();
        $dompdf = new Dompdf();
        $dompdf->loadHtml($file);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        return $dompdf->output();
    }

    //calc loss ratio
    private function lossRatio ($policy) {
      $clampaid = Clhmn::where('policy_no', $policy->policy_no)->sum('cost_todate');
      $clamest = Clhmn::where('policy_no', $policy->policy_no)->sum('curr_total_estimate');
      $gross_premium = Debitmast::where('policy_no', $policy->policy_no)->sum('gross_amount');


      if ($gross_premium == 0) {
              $lor = 0;
      } else {

              $lor = round((($clampaid + $clamest) / $gross_premium) * 100, 2);
      }
      return "$lor%";

    }


}