<?php

namespace App\Classes;

use Carbon\Carbon;
use App\Models\NotificationsSmsCron;
use App\Models\NotificationsMailCron;

class AddToNotificationsCron
{
  protected $policyNo;
  protected $polren;
  protected $category;
  protected $receiver;
  protected $message;
  protected $dispatchDate;
  protected $notificationSlug;
  protected $renewalDate;
  protected $creator;
  protected $template;
  protected $ccReceiver;
  protected $bccReceiver;
  protected $isRenewed;
  protected $isCancelled;

  public function __construct($category,$receiver,$message,$dispatchDate,$notificationSlug)
  {
    $this->category = $category;
    $this->receiver = $receiver;
    $this->message = $message;
    $this->dispatchDate = $dispatchDate;
    $this->notificationSlug = $notificationSlug;
  }

  public function setPolicyNo($policyNo)
  {
    $this->policyNo = $policyNo;
    return $this;
  }

  public function setPolren($polren)
  {
    $this->polren = $polren;
    return $this;
  }

  public function setRenewalDate($renewalDate)
  {
    $this->renewalDate = $renewalDate;
    return $this;
  }

  public function setCreator($creator)
  {
    $this->creator = $creator;
    return $this;
  }

  public function setTemplate($template)
  {
    $this->template = $template;
    return $this;
  }

  public function setCcReceiver($ccReceiver)
  {
    $this->ccReceiver = $ccReceiver;
    return $this;
  }

  public function setBccReceiver($bccReceiver)
  {
    $this->bccReceiver = $bccReceiver;
    return $this;
  }
  
  public function setIsRenewed($isRenewed)
  {
    $this->isRenewed = $isRenewed;
    return $this;
  }

  public function setIsCancelled($isCancelled)
  {
    $this->isCancelled = $isCancelled;
    return $this;
  }

  public function createNotificationMailRecord()
  {
    $existingRecord = NotificationsMailCron::where('policy_no', $this->policyNo)
    ->where('category', $this->category)
    ->where('receiver', $this->receiver)
    ->where('dispatch_date', $this->dispatchDate)
    ->first();

    if($existingRecord){
      return false; // Record already exists, so do not insert again
    }
    
    $notificationMail = new NotificationsMailCron();
    $notificationMail->policy_no = $this->policyNo;
    $notificationMail->pol_ren = $this->polren;
    $notificationMail->category = $this->category;
    $notificationMail->receiver = $this->receiver;
    $notificationMail->message = $this->message;
    $notificationMail->dispatch_date = $this->dispatchDate;
    $notificationMail->is_renewed = $this->isRenewed;
    $notificationMail->is_cancelled = $this->isCancelled;
    $notificationMail->renewal_date = Carbon::parse($this->renewalDate)->format('Y-m-d H:i:s');
    $notificationMail->notification_slug = $this->notificationSlug;
    $notificationMail->cc_receiver = !empty($this->ccReceiver) ? json_encode($this->ccReceiver) : null;
    $notificationMail->bcc_receiver = !empty($this->bccReceiver) ? json_encode($this->bccReceiver) : null;
    $notificationMail->creator = $this->creator;
    $notificationMail->template = $this->template;
    $notificationMail->save();
    
    return true;
  }

  public function createNotificationSmsRecord($policyNo,$category,$telephone,$message,$dispatchDate,$creator='system')
  {
    $existingRecord = NotificationsSmsCron::where('policy_no', $policyNo)
    ->where('category', $category)
    ->where('receiver', $telephone)
    ->where('dispatch_date', $dispatchDate)
    ->first();

    if($existingRecord){
      return false;
    }else{
      $sendSms = new NotificationsSmsCron();
      $sendSms->policy_no = $policyNo;
      $sendSms->category = $category;
      $sendSms->receiver = $telephone; 
      $sendSms->message = $message;
      $sendSms->dispatch_date = $dispatchDate;
      $sendSms->creator = $creator;
      $sendSms->save();

      return true;
    } 
  }
}