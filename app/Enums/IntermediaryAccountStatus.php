<?php

namespace App\Enums;

use InvalidArgumentException;

class IntermediaryAccountStatus
{
  public const Active = 'active';
  public const Disabled = 'disabled';
  public const Blacklisted = 'blacklisted';
  public const Stopped = 'stopped';

  public static function getStatusString(int $statusId): string
  {
    return match ($statusId) {
      self::Active => 'active',
      self::Disabled => 'disabled',
      self::Blacklisted => 'blacklisted',
      default => throw new InvalidArgumentException('Invalid status ID'),
    };
  }
}
